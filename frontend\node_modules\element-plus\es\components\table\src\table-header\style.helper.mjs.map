{"version": 3, "file": "style.helper.mjs", "sources": ["../../../../../../../packages/components/table/src/table-header/style.helper.ts"], "sourcesContent": ["import { inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isString } from '@element-plus/utils'\n\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableHeaderProps } from '.'\n\nfunction useStyle<T>(props: TableHeaderProps<T>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n\n  const getHeaderRowStyle = (rowIndex: number) => {\n    const headerRowStyle = parent?.props.headerRowStyle\n    if (isFunction(headerRowStyle)) {\n      return headerRowStyle.call(null, { rowIndex })\n    }\n    return headerRowStyle\n  }\n\n  const getHeaderRowClass = (rowIndex: number): string => {\n    const classes: string[] = []\n    const headerRowClassName = parent?.props.headerRowClassName\n    if (isString(headerRowClassName)) {\n      classes.push(headerRowClassName)\n    } else if (isFunction(headerRowClassName)) {\n      classes.push(headerRowClassName.call(null, { rowIndex }))\n    }\n\n    return classes.join(' ')\n  }\n\n  const getHeaderCellStyle = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    let headerCellStyles = parent?.props.headerCellStyle ?? {}\n    if (isFunction(headerCellStyles)) {\n      headerCellStyles = headerCellStyles.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column,\n      })\n    }\n    const fixedStyle = getFixedColumnOffset<T>(\n      columnIndex,\n      column.fixed,\n      props.store,\n      row as unknown as TableColumnCtx<T>[]\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return Object.assign({}, headerCellStyles, fixedStyle)\n  }\n\n  const getHeaderCellClass = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    const fixedClasses = getFixedColumnsClass<T>(\n      ns.b(),\n      columnIndex,\n      column.fixed,\n      props.store,\n      row as unknown as TableColumnCtx<T>[]\n    )\n    const classes = [\n      column.id,\n      column.order,\n      column.headerAlign,\n      column.className,\n      column.labelClassName,\n      ...fixedClasses,\n    ]\n\n    if (!column.children) {\n      classes.push('is-leaf')\n    }\n\n    if (column.sortable) {\n      classes.push('is-sortable')\n    }\n\n    const headerCellClassName = parent?.props.headerCellClassName\n    if (isString(headerCellClassName)) {\n      classes.push(headerCellClassName)\n    } else if (isFunction(headerCellClassName)) {\n      classes.push(\n        headerCellClassName.call(null, {\n          rowIndex,\n          columnIndex,\n          row,\n          column,\n        })\n      )\n    }\n\n    classes.push(ns.e('cell'))\n\n    return classes.filter((className) => Boolean(className)).join(' ')\n  }\n\n  return {\n    getHeaderRowStyle,\n    getHeaderRowClass,\n    getHeaderCellStyle,\n    getHeaderCellClass,\n  }\n}\n\nexport default useStyle\n"], "names": [], "mappings": ";;;;;;AASA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACnC,EAAE,MAAM,iBAAiB,GAAG,CAAC,QAAQ,KAAK;AAC1C,IAAI,MAAM,cAAc,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC;AACjF,IAAI,IAAI,UAAU,CAAC,cAAc,CAAC,EAAE;AACpC,MAAM,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,QAAQ,KAAK;AAC1C,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,kBAAkB,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC;AACzF,IAAI,IAAI,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACtC,MAAM,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACvC,KAAK,MAAM,IAAI,UAAU,CAAC,kBAAkB,CAAC,EAAE;AAC/C,MAAM,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;AAChE,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,KAAK;AACrE,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,gBAAgB,GAAG,CAAC,EAAE,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3G,IAAI,IAAI,UAAU,CAAC,gBAAgB,CAAC,EAAE;AACtC,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE;AACrD,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,QAAQ,GAAG;AACX,QAAQ,MAAM;AACd,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,oBAAoB,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACzF,IAAI,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACvC,IAAI,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACxC,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC;AAC3D,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,KAAK;AACrE,IAAI,MAAM,YAAY,GAAG,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACnG,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,MAAM,CAAC,EAAE;AACf,MAAM,MAAM,CAAC,KAAK;AAClB,MAAM,MAAM,CAAC,WAAW;AACxB,MAAM,MAAM,CAAC,SAAS;AACtB,MAAM,MAAM,CAAC,cAAc;AAC3B,MAAM,GAAG,YAAY;AACrB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC1B,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACzB,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,MAAM,mBAAmB,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;AAC3F,IAAI,IAAI,QAAQ,CAAC,mBAAmB,CAAC,EAAE;AACvC,MAAM,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACxC,KAAK,MAAM,IAAI,UAAU,CAAC,mBAAmB,CAAC,EAAE;AAChD,MAAM,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE;AAClD,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,QAAQ,GAAG;AACX,QAAQ,MAAM;AACd,OAAO,CAAC,CAAC,CAAC;AACV,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/B,IAAI,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvE,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,GAAG,CAAC;AACJ;;;;"}