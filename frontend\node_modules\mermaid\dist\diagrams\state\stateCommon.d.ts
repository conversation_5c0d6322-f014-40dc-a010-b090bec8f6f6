/**
 * Constants common to all State Diagram code
 */
export declare const DEFAULT_DIAGRAM_DIRECTION = "TB";
export declare const DEFAULT_NESTED_DOC_DIR = "TB";
export declare const STMT_DIRECTION = "dir";
export declare const STMT_STATE = "state";
export declare const STMT_ROOT = "root";
export declare const STMT_RELATION = "relation";
export declare const STMT_CLASSDEF = "classDef";
export declare const STMT_STYLEDEF = "style";
export declare const STMT_APPLYCLASS = "applyClass";
export declare const DEFAULT_STATE_TYPE = "default";
export declare const DIVIDER_TYPE = "divider";
export declare const G_EDGE_STYLE = "fill:none";
export declare const G_EDGE_ARROWHEADSTYLE = "fill: #333";
export declare const G_EDGE_LABELPOS = "c";
export declare const G_EDGE_LABELTYPE = "text";
export declare const G_EDGE_THICKNESS = "normal";
export declare const SHAPE_STATE = "rect";
export declare const SHAPE_STATE_WITH_DESC = "rectWithTitle";
export declare const SHAPE_START = "stateStart";
export declare const SHAPE_END = "stateEnd";
export declare const SHAPE_DIVIDER = "divider";
export declare const SHAPE_GROUP = "roundedWithTitle";
export declare const SHAPE_NOTE = "note";
export declare const SHAPE_NOTEGROUP = "noteGroup";
export declare const CSS_DIAGRAM = "statediagram";
export declare const CSS_STATE = "state";
export declare const CSS_DIAGRAM_STATE = "statediagram-state";
export declare const CSS_EDGE = "transition";
export declare const CSS_NOTE = "note";
export declare const CSS_NOTE_EDGE = "note-edge";
export declare const CSS_EDGE_NOTE_EDGE = "transition note-edge";
export declare const CSS_DIAGRAM_NOTE = "statediagram-note";
export declare const CSS_CLUSTER = "cluster";
export declare const CSS_DIAGRAM_CLUSTER = "statediagram-cluster";
export declare const CSS_CLUSTER_ALT = "cluster-alt";
export declare const CSS_DIAGRAM_CLUSTER_ALT = "statediagram-cluster-alt";
export declare const PARENT = "parent";
export declare const NOTE = "note";
export declare const DOMID_STATE = "state";
export declare const DOMID_TYPE_SPACER = "----";
export declare const NOTE_ID = "----note";
export declare const PARENT_ID = "----parent";
declare const _default: {
    DEFAULT_DIAGRAM_DIRECTION: string;
    DEFAULT_NESTED_DOC_DIR: string;
    STMT_STATE: string;
    STMT_RELATION: string;
    STMT_CLASSDEF: string;
    STMT_STYLEDEF: string;
    STMT_APPLYCLASS: string;
    DEFAULT_STATE_TYPE: string;
    DIVIDER_TYPE: string;
    G_EDGE_STYLE: string;
    G_EDGE_ARROWHEADSTYLE: string;
    G_EDGE_LABELPOS: string;
    G_EDGE_LABELTYPE: string;
    G_EDGE_THICKNESS: string;
    CSS_EDGE: string;
    CSS_DIAGRAM: string;
    SHAPE_STATE: string;
    SHAPE_STATE_WITH_DESC: string;
    SHAPE_START: string;
    SHAPE_END: string;
    SHAPE_DIVIDER: string;
    SHAPE_GROUP: string;
    SHAPE_NOTE: string;
    SHAPE_NOTEGROUP: string;
    CSS_STATE: string;
    CSS_DIAGRAM_STATE: string;
    CSS_NOTE: string;
    CSS_NOTE_EDGE: string;
    CSS_EDGE_NOTE_EDGE: string;
    CSS_DIAGRAM_NOTE: string;
    CSS_CLUSTER: string;
    CSS_DIAGRAM_CLUSTER: string;
    CSS_CLUSTER_ALT: string;
    CSS_DIAGRAM_CLUSTER_ALT: string;
    PARENT: string;
    NOTE: string;
    DOMID_STATE: string;
    DOMID_TYPE_SPACER: string;
    NOTE_ID: string;
    PARENT_ID: string;
};
export default _default;
