{"version": 3, "file": "virtual-tree.mjs", "sources": ["../../../../../../packages/components/tree-v2/src/virtual-tree.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isBoolean,\n  mutable,\n} from '@element-plus/utils'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { InjectionKey } from 'vue'\nimport type { TreeNodeData } from '@element-plus/components/tree/src/tree.type'\nimport type {\n  CheckedInfo,\n  FilterMethod,\n  TreeContext,\n  TreeData,\n  TreeKey,\n  TreeNode,\n  TreeOptionProps,\n} from './types'\n\n// constants\nexport const ROOT_TREE_INJECTION_KEY: InjectionKey<TreeContext> = Symbol()\nconst EMPTY_NODE = {\n  key: -1,\n  level: -1,\n  data: {},\n} as const\n\n// enums\nexport enum TreeOptionsEnum {\n  KEY = 'id',\n  LABEL = 'label',\n  CHILDREN = 'children',\n  DISABLED = 'disabled',\n  CLASS = '',\n}\n\nexport const enum SetOperationEnum {\n  ADD = 'add',\n  DELETE = 'delete',\n}\n\nconst itemSize = {\n  type: Number,\n  default: 26,\n}\n\n// props\nexport const treeProps = buildProps({\n  data: {\n    type: definePropType<TreeData>(Array),\n    default: () => mutable([] as const),\n  },\n  emptyText: {\n    type: String,\n  },\n  height: {\n    type: Number,\n    default: 200,\n  },\n  props: {\n    type: definePropType<TreeOptionProps>(Object),\n    default: () =>\n      mutable({\n        children: TreeOptionsEnum.CHILDREN,\n        label: TreeOptionsEnum.LABEL,\n        disabled: TreeOptionsEnum.DISABLED,\n        value: TreeOptionsEnum.KEY,\n        class: TreeOptionsEnum.CLASS,\n      } as const),\n  },\n  highlightCurrent: {\n    type: Boolean,\n    default: false,\n  },\n  showCheckbox: {\n    type: Boolean,\n    default: false,\n  },\n  defaultCheckedKeys: {\n    type: definePropType<TreeKey[]>(Array),\n    default: () => mutable([] as const),\n  },\n  // Whether checked state of a node not affects its father and\n  // child nodes when show-checkbox is true\n  checkStrictly: {\n    type: Boolean,\n    default: false,\n  },\n  defaultExpandedKeys: {\n    type: definePropType<TreeKey[]>(Array),\n    default: () => mutable([] as const),\n  },\n  indent: {\n    type: Number,\n    default: 16,\n  },\n  itemSize,\n  icon: {\n    type: iconPropType,\n  },\n  expandOnClickNode: {\n    type: Boolean,\n    default: true,\n  },\n  checkOnClickNode: {\n    type: Boolean,\n    default: false,\n  },\n  checkOnClickLeaf: {\n    type: Boolean,\n    default: true,\n  },\n  currentNodeKey: {\n    type: definePropType<TreeKey>([String, Number]),\n  },\n  // TODO need to optimization\n  accordion: {\n    type: Boolean,\n    default: false,\n  },\n  filterMethod: {\n    type: definePropType<FilterMethod>(Function),\n  },\n  // Performance mode will increase memory usage, but scrolling will be smoother\n  perfMode: {\n    type: Boolean,\n    default: true,\n  },\n} as const)\n\nexport const treeNodeProps = buildProps({\n  node: {\n    type: definePropType<TreeNode>(Object),\n    default: () => mutable(EMPTY_NODE),\n  },\n  expanded: {\n    type: Boolean,\n    default: false,\n  },\n  checked: {\n    type: Boolean,\n    default: false,\n  },\n  indeterminate: {\n    type: Boolean,\n    default: false,\n  },\n  showCheckbox: {\n    type: Boolean,\n    default: false,\n  },\n  disabled: {\n    type: Boolean,\n    default: false,\n  },\n  current: {\n    type: Boolean,\n    default: false,\n  },\n  hiddenExpandIcon: {\n    type: Boolean,\n    default: false,\n  },\n  itemSize,\n} as const)\n\nexport const treeNodeContentProps = buildProps({\n  node: {\n    type: definePropType<TreeNode>(Object),\n    required: true,\n  },\n} as const)\n\n// emits\nexport const NODE_CLICK = 'node-click'\nexport const NODE_DROP = 'node-drop'\nexport const NODE_EXPAND = 'node-expand'\nexport const NODE_COLLAPSE = 'node-collapse'\nexport const CURRENT_CHANGE = 'current-change'\nexport const NODE_CHECK = 'check'\nexport const NODE_CHECK_CHANGE = 'check-change'\nexport const NODE_CONTEXTMENU = 'node-contextmenu'\n\nexport const treeEmits = {\n  [NODE_CLICK]: (data: TreeNodeData, node: TreeNode, e: MouseEvent) =>\n    data && node && e,\n  [NODE_DROP]: (data: TreeNodeData, node: TreeNode, e: DragEvent) =>\n    data && node && e,\n  [NODE_EXPAND]: (data: TreeNodeData, node: TreeNode) => data && node,\n  [NODE_COLLAPSE]: (data: TreeNodeData, node: TreeNode) => data && node,\n  [CURRENT_CHANGE]: (data: TreeNodeData, node: TreeNode) => data && node,\n  [NODE_CHECK]: (data: TreeNodeData, checkedInfo: CheckedInfo) =>\n    data && checkedInfo,\n  [NODE_CHECK_CHANGE]: (data: TreeNodeData, checked: boolean) =>\n    data && isBoolean(checked),\n  [NODE_CONTEXTMENU]: (evt: Event, data: TreeNodeData, node: TreeNode) =>\n    evt && data && node,\n}\n\nexport const treeNodeEmits = {\n  click: (node: TreeNode, e: MouseEvent) => !!(node && e),\n  drop: (node: TreeNode, e: DragEvent) => !!(node && e),\n  toggle: (node: TreeNode) => !!node,\n  check: (node: TreeNode, checked: CheckboxValueType) =>\n    node && isBoolean(checked),\n}\n"], "names": [], "mappings": ";;;;;AAOY,MAAC,uBAAuB,GAAG,MAAM,GAAG;AAChD,MAAM,UAAU,GAAG;AACnB,EAAE,GAAG,EAAE,CAAC,CAAC;AACT,EAAE,KAAK,EAAE,CAAC,CAAC;AACX,EAAE,IAAI,EAAE,EAAE;AACV,CAAC,CAAC;AACQ,IAAC,eAAe,mBAAmB,CAAC,CAAC,gBAAgB,KAAK;AACpE,EAAE,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACjC,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AACtC,EAAE,gBAAgB,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;AAC5C,EAAE,gBAAgB,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;AAC5C,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AACjC,EAAE,OAAO,gBAAgB,CAAC;AAC1B,CAAC,EAAE,eAAe,IAAI,EAAE,EAAE;AAChB,IAAC,gBAAgB,mBAAmB,CAAC,CAAC,iBAAiB,KAAK;AACtE,EAAE,iBAAiB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACnC,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACzC,EAAE,OAAO,iBAAiB,CAAC;AAC3B,CAAC,EAAE,gBAAgB,IAAI,EAAE,EAAE;AAC3B,MAAM,QAAQ,GAAG;AACjB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE,EAAE;AACb,CAAC,CAAC;AACU,MAAC,SAAS,GAAG,UAAU,CAAC;AACpC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC;AAC3B,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,KAAK,EAAE,EAAE;AACf,KAAK,CAAC;AACN,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ;AACV,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,UAAU,CAAC;AACtC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,QAAQ;AACV,CAAC,EAAE;AACS,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,UAAU,GAAG,aAAa;AAC3B,MAAC,SAAS,GAAG,YAAY;AACzB,MAAC,WAAW,GAAG,cAAc;AAC7B,MAAC,aAAa,GAAG,gBAAgB;AACjC,MAAC,cAAc,GAAG,iBAAiB;AACnC,MAAC,UAAU,GAAG,QAAQ;AACtB,MAAC,iBAAiB,GAAG,eAAe;AACpC,MAAC,gBAAgB,GAAG,mBAAmB;AACvC,MAAC,SAAS,GAAG;AACzB,EAAE,CAAC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC;AACpD,EAAE,CAAC,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC;AACnD,EAAE,CAAC,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI;AAC7C,EAAE,CAAC,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI;AAC/C,EAAE,CAAC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI;AAChD,EAAE,CAAC,UAAU,GAAG,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,WAAW;AAC1D,EAAE,CAAC,iBAAiB,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC;AACpE,EAAE,CAAC,gBAAgB,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,IAAI;AAC9D,EAAE;AACU,MAAC,aAAa,GAAG;AAC7B,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;AACnC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;AAClC,EAAE,MAAM,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;AAC1B,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC;AACtD;;;;"}