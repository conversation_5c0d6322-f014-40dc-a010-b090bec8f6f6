import{_ as S,r as v,i as u,j as d,k as r,n as s,L as z,p as t,w as i,M as o,F as m,x as f,N as l,O as C,P as h,Q as L,f as B,m as P,D as w,R as E,B as y,C as k,z as c,H}from"./index-b6a2842e.js";const N={class:"enterprise-homepage"},D={class:"enterprise-header"},V={class:"header-container"},M={class:"nav-menu"},T={class:"header-actions"},j={class:"hero-section"},G={class:"hero-container"},O={class:"hero-content"},Q={class:"hero-text"},R={class:"hero-actions"},q={class:"hero-visual"},J={class:"floating-cards"},K={class:"feature-card ai-card"},U={class:"feature-card voice-card"},W={class:"feature-card data-card"},X={class:"products-section"},Y={class:"section-container"},Z={class:"products-grid"},$={class:"card-header"},ss={class:"product-title"},es={class:"product-description"},ts={class:"feature-list"},is={class:"card-footer"},as={class:"advantages-section"},os={class:"section-container"},ns={class:"advantages-grid"},ds={class:"advantage-icon"},ls={class:"advantage-title"},rs={class:"advantage-description"},cs={class:"advantage-metrics"},_s={class:"metric-value"},us={class:"metric-label"},ps={__name:"EnterpriseHomePage",setup(vs){const b=v("dashboard"),x=v([{id:1,title:"AI智能面试官",description:"基于iFlytek Spark大模型的智能面试官，支持多轮对话、实时评估和个性化问题生成",icon:"Cpu",gradient:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",features:["自然语言理解与生成","实时语音交互分析","智能问题动态调整","多维度能力评估"]},{id:2,title:"多模态分析引擎",description:"集成语音识别、情感分析和行为评估的综合分析系统",icon:"Grid",gradient:"linear-gradient(135deg, #1890ff 0%, #0066cc 100%)",features:["语音质量智能评估","情感状态实时监测","表达能力量化分析","综合评分自动生成"]},{id:3,title:"智能招聘管理",description:"企业级招聘流程管理，从职位发布到人才录用的全链路智能化",icon:"TrendCharts",gradient:"linear-gradient(135deg, #52c41a 0%, #389e0d 100%)",features:["职位智能匹配推荐","候选人批量筛选","面试流程自动化","数据驱动决策支持"]}]),A=v([{id:1,title:"领先AI技术",description:"基于iFlytek Spark大模型，具备强大的自然语言理解和生成能力",icon:"Cpu",metric:"95%+",metricLabel:"语音识别准确率"},{id:2,title:"实时处理能力",description:"毫秒级响应速度，支持大规模并发面试场景",icon:"Clock",metric:"<100ms",metricLabel:"平均响应时间"},{id:3,title:"数据安全保障",description:"企业级安全防护，确保面试数据和候选人隐私安全",icon:"Lock",metric:"99.9%",metricLabel:"系统可用性"}]);return(ms,e)=>{const _=u("el-menu-item"),I=u("el-menu"),n=u("el-icon"),p=u("el-button"),F=u("el-avatar");return d(),r("div",N,[s("header",D,[s("div",V,[e[6]||(e[6]=s("div",{class:"logo-section"},[s("img",{src:z,alt:"iFlytek Spark",class:"logo"}),s("span",{class:"brand-text"},"iFlytek Spark AI面试系统")],-1)),s("nav",M,[t(I,{mode:"horizontal","default-active":b.value,class:"enterprise-nav"},{default:i(()=>[t(_,{index:"dashboard"},{default:i(()=>e[0]||(e[0]=[o("智能仪表板")])),_:1,__:[0]}),t(_,{index:"interviews"},{default:i(()=>e[1]||(e[1]=[o("AI面试管理")])),_:1,__:[1]}),t(_,{index:"candidates"},{default:i(()=>e[2]||(e[2]=[o("候选人中心")])),_:1,__:[2]}),t(_,{index:"analytics"},{default:i(()=>e[3]||(e[3]=[o("数据洞察")])),_:1,__:[3]}),t(_,{index:"settings"},{default:i(()=>e[4]||(e[4]=[o("系统配置")])),_:1,__:[4]})]),_:1},8,["default-active"])]),s("div",T,[t(p,{type:"primary",class:"cta-button"},{default:i(()=>[t(n,null,{default:i(()=>[t(l(C))]),_:1}),e[5]||(e[5]=o(" 创建面试 "))]),_:1,__:[5]}),t(F,{size:40,src:"/images/candidate-avatar.svg"})])])]),s("section",j,[s("div",G,[s("div",O,[s("div",Q,[e[9]||(e[9]=s("h1",{class:"hero-title"},[s("span",{class:"gradient-text"},"AI+招聘"),s("br"),o(" iFlytek Spark智能面试系统 ")],-1)),e[10]||(e[10]=s("p",{class:"hero-subtitle"}," 基于讯飞星火大模型的多模态AI面试解决方案， 让招聘更智能、更高效、更精准 ",-1)),s("div",R,[t(p,{type:"primary",size:"large",class:"primary-cta"},{default:i(()=>[e[7]||(e[7]=o(" 立即体验 ")),t(n,null,{default:i(()=>[t(l(h))]),_:1})]),_:1,__:[7]}),t(p,{size:"large",class:"secondary-cta"},{default:i(()=>[e[8]||(e[8]=o(" 观看演示 ")),t(n,null,{default:i(()=>[t(l(L))]),_:1})]),_:1,__:[8]})])]),s("div",q,[s("div",J,[s("div",K,[t(n,{class:"card-icon"},{default:i(()=>[t(l(B))]),_:1}),e[11]||(e[11]=s("span",null,"AI智能分析",-1))]),s("div",U,[t(n,{class:"card-icon"},{default:i(()=>[t(l(P))]),_:1}),e[12]||(e[12]=s("span",null,"语音识别",-1))]),s("div",W,[t(n,{class:"card-icon"},{default:i(()=>[t(l(w))]),_:1}),e[13]||(e[13]=s("span",null,"数据洞察",-1))])])])])])]),s("section",X,[s("div",Y,[e[15]||(e[15]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"核心AI面试解决方案"),s("p",{class:"section-subtitle"}," 基于iFlytek Spark大模型，提供全方位智能面试体验 ")],-1)),s("div",Z,[(d(!0),r(m,null,f(x.value,a=>(d(),r("div",{class:"product-card primary-product",key:a.id},[s("div",$,[s("div",{class:"product-icon",style:E({background:a.gradient})},[t(n,{size:32},{default:i(()=>[(d(),y(k(a.icon)))]),_:2},1024)],4),s("h3",ss,c(a.title),1)]),s("p",es,c(a.description),1),s("ul",ts,[(d(!0),r(m,null,f(a.features,g=>(d(),r("li",{key:g},[t(n,{class:"check-icon"},{default:i(()=>[t(l(H))]),_:1}),o(" "+c(g),1)]))),128))]),s("div",is,[t(p,{type:"primary",class:"learn-more-btn"},{default:i(()=>[e[14]||(e[14]=o(" 了解更多 ")),t(n,null,{default:i(()=>[t(l(h))]),_:1})]),_:1,__:[14]})])]))),128))])])]),s("section",as,[s("div",os,[e[16]||(e[16]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"iFlytek Spark技术优势"),s("p",{class:"section-subtitle"},"领先的多模态AI技术，重新定义智能面试")],-1)),s("div",ns,[(d(!0),r(m,null,f(A.value,a=>(d(),r("div",{class:"advantage-item",key:a.id},[s("div",ds,[t(n,{size:48},{default:i(()=>[(d(),y(k(a.icon)))]),_:2},1024)]),s("h4",ls,c(a.title),1),s("p",rs,c(a.description),1),s("div",cs,[s("span",_s,c(a.metric),1),s("span",us,c(a.metricLabel),1)])]))),128))])])])])}}},gs=S(ps,[["__scopeId","data-v-1779add7"]]);export{gs as default};
