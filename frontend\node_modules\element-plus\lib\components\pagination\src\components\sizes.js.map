{"version": 3, "file": "sizes.js", "sources": ["../../../../../../../packages/components/pagination/src/components/sizes.ts"], "sourcesContent": ["import { buildProps, definePropType, mutable } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type Sizes from './sizes.vue'\n\nexport const paginationSizesProps = buildProps({\n  pageSize: {\n    type: Number,\n    required: true,\n  },\n  pageSizes: {\n    type: definePropType<number[]>(Array),\n    default: () => mutable([10, 20, 30, 40, 50, 100] as const),\n  },\n  popperClass: {\n    type: String,\n  },\n  disabled: Boolean,\n  teleported: Boolean,\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n  appendSizeTo: String,\n} as const)\n\nexport type PaginationSizesProps = ExtractPropTypes<typeof paginationSizesProps>\n\nexport type SizesInstance = InstanceType<typeof Sizes> & unknown\n"], "names": ["buildProps", "definePropType", "mutable", "componentSizes"], "mappings": ";;;;;;;;AAEY,MAAC,oBAAoB,GAAGA,kBAAU,CAAC;AAC/C,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAEC,mBAAc;AAC1B,GAAG;AACH,EAAE,YAAY,EAAE,MAAM;AACtB,CAAC;;;;"}