<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示图片占位符生成器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .image-item:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .placeholder-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .image-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .image-desc {
            font-size: 12px;
            color: #666;
        }
        .generate-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px auto;
            display: block;
            transition: transform 0.2s ease;
        }
        .generate-btn:hover {
            transform: translateY(-2px);
        }
        .status {
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background: #e8f5e8;
            color: #2d5a2d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 多模态面试系统演示图片</h1>
        <p style="text-align: center; color: #666;">为演示系统生成占位符图片</p>
        
        <button class="generate-btn" onclick="generateImages()">🚀 生成所有演示图片</button>
        
        <div class="image-grid">
            <div class="image-item">
                <div class="placeholder-image">视频封面 - AI</div>
                <div class="image-title">video-poster-ai.jpg</div>
                <div class="image-desc">人工智能领域面试演示封面</div>
            </div>
            
            <div class="image-item">
                <div class="placeholder-image">视频封面 - 大数据</div>
                <div class="image-title">video-poster-bigdata.jpg</div>
                <div class="image-desc">大数据领域面试演示封面</div>
            </div>
            
            <div class="image-item">
                <div class="placeholder-image">视频封面 - IoT</div>
                <div class="image-title">video-poster-iot.jpg</div>
                <div class="image-desc">物联网领域面试演示封面</div>
            </div>
            
            <div class="image-item">
                <div class="placeholder-image">章节缩略图 1</div>
                <div class="image-title">chapter-1.jpg</div>
                <div class="image-desc">第一章节缩略图</div>
            </div>
            
            <div class="image-item">
                <div class="placeholder-image">章节缩略图 2</div>
                <div class="image-title">chapter-2.jpg</div>
                <div class="image-desc">第二章节缩略图</div>
            </div>
            
            <div class="image-item">
                <div class="placeholder-image">章节缩略图 3</div>
                <div class="image-title">chapter-3.jpg</div>
                <div class="image-desc">第三章节缩略图</div>
            </div>
            
            <div class="image-item">
                <div class="placeholder-image">章节缩略图 4</div>
                <div class="image-title">chapter-4.jpg</div>
                <div class="image-desc">第四章节缩略图</div>
            </div>
            
            <div class="image-item">
                <div class="placeholder-image">主视频封面 1</div>
                <div class="image-title">video-poster-1.jpg</div>
                <div class="image-desc">主要演示视频封面</div>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;">
            ✅ 图片生成完成！请刷新页面查看效果。
        </div>
    </div>

    <script>
        function generateImages() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 400;
            canvas.height = 225; // 16:9 比例
            
            const images = [
                { name: 'video-poster-ai.jpg', title: '人工智能面试演示', color: '#4CAF50' },
                { name: 'video-poster-bigdata.jpg', title: '大数据面试演示', color: '#2196F3' },
                { name: 'video-poster-iot.jpg', title: '物联网面试演示', color: '#FF9800' },
                { name: 'video-poster-1.jpg', title: '系统演示视频', color: '#9C27B0' },
                { name: 'video-poster-2.jpg', title: '功能展示视频', color: '#F44336' },
                { name: 'video-poster-3.jpg', title: '技术架构视频', color: '#607D8B' },
                { name: 'chapter-1.jpg', title: '第一章节', color: '#795548' },
                { name: 'chapter-2.jpg', title: '第二章节', color: '#3F51B5' },
                { name: 'chapter-3.jpg', title: '第三章节', color: '#009688' },
                { name: 'chapter-4.jpg', title: '第四章节', color: '#E91E63' }
            ];
            
            images.forEach((img, index) => {
                setTimeout(() => {
                    // 创建渐变背景
                    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                    gradient.addColorStop(0, img.color);
                    gradient.addColorStop(1, adjustBrightness(img.color, -30));
                    
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // 添加文字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 24px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(img.title, canvas.width / 2, canvas.height / 2 - 10);
                    
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText('多模态面试评估系统', canvas.width / 2, canvas.height / 2 + 20);
                    
                    // 添加装饰元素
                    ctx.strokeStyle = 'rgba(255,255,255,0.3)';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(20, 20, canvas.width - 40, canvas.height - 40);
                    
                    // 下载图片
                    const link = document.createElement('a');
                    link.download = img.name;
                    link.href = canvas.toDataURL('image/jpeg', 0.9);
                    link.click();
                }, index * 200);
            });
            
            setTimeout(() => {
                document.getElementById('status').style.display = 'block';
            }, images.length * 200 + 500);
        }
        
        function adjustBrightness(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }
    </script>
</body>
</html>
