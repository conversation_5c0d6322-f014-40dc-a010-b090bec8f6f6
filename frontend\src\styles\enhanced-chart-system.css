/* 增强图表系统样式 - iFlytek 多模态面试评估系统 */

/* 图表容器基础样式 */
.chart-container {
  background: var(--iflytek-bg-primary);
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--iflytek-shadow-md);
  border: 1px solid var(--iflytek-border-secondary);
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--iflytek-border-secondary);
}

.chart-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--iflytek-text-primary);
  margin: 0;
  font-family: var(--font-family-chinese-heading);
}

.chart-subtitle {
  font-size: var(--font-size-sm);
  color: var(--iflytek-text-secondary);
  margin: 4px 0 0 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-control-btn {
  background: var(--iflytek-bg-secondary);
  border: 1px solid var(--iflytek-border-primary);
  color: var(--iflytek-text-primary);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-control-btn:hover {
  background: var(--iflytek-primary);
  color: white;
  border-color: var(--iflytek-primary);
}

.chart-control-btn.active {
  background: var(--iflytek-primary);
  color: white;
  border-color: var(--iflytek-primary);
}

/* 图表内容区域 */
.chart-content {
  position: relative;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-canvas {
  width: 100%;
  height: 100%;
  max-height: 400px;
}

/* 图表图例 */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--iflytek-border-secondary);
}

.chart-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: var(--font-size-sm);
  color: var(--iflytek-text-primary);
}

.chart-legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

/* 技术领域图表主题 */
.chart-ai-theme {
  --chart-primary: var(--ai-theme-primary);
  --chart-secondary: var(--ai-theme-secondary);
  --chart-accent: var(--ai-theme-accent);
  --chart-gradient: var(--ai-theme-bg);
}

.chart-bigdata-theme {
  --chart-primary: var(--bigdata-theme-primary);
  --chart-secondary: var(--bigdata-theme-secondary);
  --chart-accent: var(--bigdata-theme-accent);
  --chart-gradient: var(--bigdata-theme-bg);
}

.chart-iot-theme {
  --chart-primary: var(--iot-theme-primary);
  --chart-secondary: var(--iot-theme-secondary);
  --chart-accent: var(--iot-theme-accent);
  --chart-gradient: var(--iot-theme-bg);
}

.chart-cloud-theme {
  --chart-primary: var(--cloud-theme-primary);
  --chart-secondary: var(--cloud-theme-secondary);
  --chart-accent: var(--cloud-theme-accent);
  --chart-gradient: var(--cloud-theme-bg);
}

/* 图表主题应用 */
.chart-themed .chart-title {
  background: var(--chart-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chart-themed .chart-header {
  border-bottom-color: var(--chart-primary);
}

/* 雷达图样式 */
.radar-chart-container {
  position: relative;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 70%);
}

.radar-chart-labels {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.radar-label {
  position: absolute;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--iflytek-text-primary);
  background: var(--iflytek-bg-primary);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: var(--iflytek-shadow-sm);
  transform: translate(-50%, -50%);
}

/* 柱状图样式 */
.bar-chart-container {
  background: linear-gradient(180deg, rgba(24, 144, 255, 0.02) 0%, transparent 100%);
}

.bar-chart-axis {
  stroke: var(--iflytek-border-primary);
  stroke-width: 1;
}

.bar-chart-grid {
  stroke: var(--iflytek-border-secondary);
  stroke-width: 0.5;
  stroke-dasharray: 2,2;
}

.bar-chart-bar {
  fill: var(--chart-primary);
  transition: all 0.3s ease;
}

.bar-chart-bar:hover {
  fill: var(--chart-secondary);
  filter: brightness(1.1);
}

/* 折线图样式 */
.line-chart-container {
  background: linear-gradient(45deg, rgba(24, 144, 255, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);
}

.line-chart-line {
  stroke: var(--chart-primary);
  stroke-width: 3;
  fill: none;
  filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.3));
}

.line-chart-area {
  fill: url(#lineGradient);
  opacity: 0.3;
}

.line-chart-point {
  fill: var(--chart-primary);
  stroke: white;
  stroke-width: 2;
  r: 4;
  transition: all 0.3s ease;
}

.line-chart-point:hover {
  r: 6;
  fill: var(--chart-secondary);
}

/* 饼图样式 */
.pie-chart-container {
  background: radial-gradient(circle, rgba(24, 144, 255, 0.03) 0%, transparent 60%);
}

.pie-chart-slice {
  transition: all 0.3s ease;
  cursor: pointer;
}

.pie-chart-slice:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.pie-chart-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  fill: var(--iflytek-text-primary);
  text-anchor: middle;
  dominant-baseline: middle;
}

.pie-chart-percentage {
  font-size: var(--font-size-xs);
  fill: var(--iflytek-text-secondary);
}

/* 环形图样式 */
.donut-chart-container {
  position: relative;
}

.donut-chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.donut-chart-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--iflytek-primary);
  margin: 0;
}

.donut-chart-label {
  font-size: var(--font-size-sm);
  color: var(--iflytek-text-secondary);
  margin: 4px 0 0 0;
}

/* 热力图样式 */
.heatmap-container {
  background: var(--iflytek-bg-secondary);
  border-radius: 8px;
  padding: 16px;
}

.heatmap-cell {
  transition: all 0.3s ease;
  cursor: pointer;
}

.heatmap-cell:hover {
  stroke: var(--iflytek-primary);
  stroke-width: 2;
}

.heatmap-tooltip {
  position: absolute;
  background: var(--iflytek-text-primary);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: var(--font-size-sm);
  pointer-events: none;
  z-index: 1000;
  box-shadow: var(--iflytek-shadow-lg);
}

/* 仪表盘样式 */
.gauge-chart-container {
  position: relative;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 80%);
}

.gauge-chart-arc {
  fill: none;
  stroke-width: 20;
  stroke-linecap: round;
}

.gauge-chart-background {
  stroke: var(--iflytek-bg-tertiary);
}

.gauge-chart-progress {
  stroke: var(--chart-primary);
  transition: stroke-dasharray 1s ease;
}

.gauge-chart-needle {
  fill: var(--iflytek-text-primary);
  transform-origin: center;
  transition: transform 1s ease;
}

.gauge-chart-center {
  position: absolute;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.gauge-chart-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--chart-primary);
  margin: 0;
}

.gauge-chart-unit {
  font-size: var(--font-size-sm);
  color: var(--iflytek-text-secondary);
  margin: 4px 0 0 0;
}

/* 图表加载状态 */
.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--iflytek-text-secondary);
}

.chart-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--iflytek-bg-tertiary);
  border-top: 4px solid var(--iflytek-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.chart-loading-text {
  font-size: var(--font-size-base);
  color: var(--iflytek-text-secondary);
}

/* 图表错误状态 */
.chart-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--iflytek-error);
  text-align: center;
}

.chart-error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.chart-error-message {
  font-size: var(--font-size-base);
  margin-bottom: 16px;
}

.chart-error-retry {
  background: var(--iflytek-primary);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: all 0.3s ease;
}

.chart-error-retry:hover {
  background: var(--iflytek-primary-dark);
}

/* 图表工具提示 */
.chart-tooltip {
  position: absolute;
  background: var(--iflytek-text-primary);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: var(--font-size-sm);
  pointer-events: none;
  z-index: 1000;
  box-shadow: var(--iflytek-shadow-lg);
  max-width: 200px;
}

.chart-tooltip::before {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--iflytek-text-primary);
}

.chart-tooltip-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: 4px;
}

.chart-tooltip-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--iflytek-primary-light);
}

/* 响应式图表 */
@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .chart-controls {
    width: 100%;
    justify-content: flex-start;
    overflow-x: auto;
  }
  
  .chart-legend {
    flex-direction: column;
    gap: 8px;
  }
  
  .chart-content {
    min-height: 250px;
  }
  
  .chart-tooltip {
    font-size: var(--font-size-xs);
    padding: 8px 12px;
    max-width: 150px;
  }
}

/* 图表动画 */
@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-animate-in {
  animation: chartFadeIn 0.6s ease-out;
}

/* 图表交互增强 */
.chart-interactive {
  cursor: crosshair;
}

.chart-interactive:hover {
  background: rgba(24, 144, 255, 0.02);
}

/* 图表导出按钮 */
.chart-export-btn {
  background: var(--iflytek-bg-secondary);
  border: 1px solid var(--iflytek-border-primary);
  color: var(--iflytek-text-primary);
  padding: 6px 12px;
  border-radius: 4px;
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-export-btn:hover {
  background: var(--iflytek-primary);
  color: white;
}
