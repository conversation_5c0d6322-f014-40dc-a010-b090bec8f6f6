import {
  __name
} from "./chunk-DLQEHMXD.mjs";

// src/diagrams/globalStyles.ts
var getIconStyles = /* @__PURE__ */ __name(() => `
  /* Font Awesome icon styling - consolidated */
  .label-icon {
    display: inline-block;
    height: 1em;
    overflow: visible;
    vertical-align: -0.125em;
  }
  
  .node .label-icon path {
    fill: currentColor;
    stroke: revert;
    stroke-width: revert;
  }
`, "getIconStyles");

export {
  getIconStyles
};
