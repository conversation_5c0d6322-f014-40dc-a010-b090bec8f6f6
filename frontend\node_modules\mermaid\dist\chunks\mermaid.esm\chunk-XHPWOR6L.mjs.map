{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ms@2.1.3/node_modules/ms/index.js", "../../../../../node_modules/.pnpm/debug@4.4.0_supports-color@8.1.1/node_modules/debug/src/common.js", "../../../../../node_modules/.pnpm/debug@4.4.0_supports-color@8.1.1/node_modules/debug/src/browser.js", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/defaults.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/customisations/defaults.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/name.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/transformations.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/merge.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon-set/tree.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon-set/get-icon.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/size.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/defs.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/build.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/id.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/html.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/index.mjs", "../../../src/rendering-util/icons.ts", "../../../../../node_modules/.pnpm/ts-dedent@2.2.0/node_modules/ts-dedent/src/index.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/defaults.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/rules.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/helpers.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Tokenizer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Lexer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Renderer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/TextRenderer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Parser.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Hooks.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Instance.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/marked.ts", "../../../src/rendering-util/handle-markdown-text.ts", "../../../src/rendering-util/splitText.ts", "../../../src/rendering-util/createText.ts"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(' ', ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "const defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations };\n", "import { defaultIconTransformations } from '../icon/defaults.mjs';\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nexport { defaultIconCustomisations, defaultIconSizeCustomisations };\n", "const matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\nexport { matchIconName, stringToIcon, validateIconName };\n", "function mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nexport { mergeIconTransformations };\n", "import { defaultExtendedIconProps, defaultIconTransformations } from './defaults.mjs';\nimport { mergeIconTransformations } from './transformations.mjs';\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nexport { mergeIconData };\n", "function getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nexport { getIconsTree };\n", "import { mergeIconData } from '../icon/merge.mjs';\nimport { getIconsTree } from './tree.mjs';\nimport '../icon/defaults.mjs';\nimport '../icon/transformations.mjs';\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\nfunction getIconData(data, name) {\n  if (data.icons[name]) {\n    return internalGetIconData(data, name, []);\n  }\n  const tree = getIconsTree(data, [name])[name];\n  return tree ? internalGetIconData(data, name, tree) : null;\n}\n\nexport { getIconData, internalGetIconData };\n", "const unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nexport { calculateSize };\n", "function splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent };\n", "import { defaultIconProps } from '../icon/defaults.mjs';\nimport { defaultIconCustomisations } from '../customisations/defaults.mjs';\nimport { calculateSize } from './size.mjs';\nimport { wrapSVGContent } from './defs.mjs';\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = wrapSVGContent(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\nexport { iconToSVG, isUnsetKeyword };\n", "const regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nexport { replaceIDs };\n", "function iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nexport { iconToHTML };\n", "export { defaultIconCustomisations, defaultIconSizeCustomisations } from './customisations/defaults.mjs';\nexport { mergeCustomisations } from './customisations/merge.mjs';\nexport { toBoolean } from './customisations/bool.mjs';\nexport { flipFromString } from './customisations/flip.mjs';\nexport { rotateFromString } from './customisations/rotate.mjs';\nexport { matchIconName, stringToIcon, validateIconName } from './icon/name.mjs';\nexport { mergeIconData } from './icon/merge.mjs';\nexport { mergeIconTransformations } from './icon/transformations.mjs';\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations } from './icon/defaults.mjs';\nexport { makeIconSquare } from './icon/square.mjs';\nexport { getIconsTree } from './icon-set/tree.mjs';\nexport { parseIconSet, parseIconSetAsync } from './icon-set/parse.mjs';\nexport { validateIconSet } from './icon-set/validate.mjs';\nexport { quicklyValidateIconSet } from './icon-set/validate-basic.mjs';\nexport { expandIconSet } from './icon-set/expand.mjs';\nexport { minifyIconSet } from './icon-set/minify.mjs';\nexport { getIcons } from './icon-set/get-icons.mjs';\nexport { getIconData } from './icon-set/get-icon.mjs';\nexport { convertIconSetInfo } from './icon-set/convert-info.mjs';\nexport { iconToSVG } from './svg/build.mjs';\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent } from './svg/defs.mjs';\nexport { replaceIDs } from './svg/id.mjs';\nexport { calculateSize } from './svg/size.mjs';\nexport { encodeSvgForCss } from './svg/encode-svg-for-css.mjs';\nexport { trimSVG } from './svg/trim.mjs';\nexport { prettifySVG } from './svg/pretty.mjs';\nexport { iconToHTML } from './svg/html.mjs';\nexport { svgToData, svgToURL } from './svg/url.mjs';\nexport { cleanUpInnerHTML } from './svg/inner-html.mjs';\nexport { getSVGViewBox } from './svg/viewbox.mjs';\nexport { buildParsedSVG, convertParsedSVG, parseSVGContent } from './svg/parse.mjs';\nexport { colorKeywords } from './colors/keywords.mjs';\nexport { colorToString, compareColors, stringToColor } from './colors/index.mjs';\nexport { getIconCSS, getIconContentCSS } from './css/icon.mjs';\nexport { getIconsCSS, getIconsContentCSS } from './css/icons.mjs';\nexport { mergeIconProps } from './loader/utils.mjs';\nexport { getCustomIcon } from './loader/custom.mjs';\nexport { searchForIcon } from './loader/modern.mjs';\nexport { loadIcon } from './loader/loader.mjs';\nexport { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from './emoji/cleanup.mjs';\nexport { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number } from './emoji/convert.mjs';\nexport { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString } from './emoji/format.mjs';\nexport { parseEmojiTestFile } from './emoji/test/parse.mjs';\nexport { getQualifiedEmojiVariations } from './emoji/test/variations.mjs';\nexport { findMissingEmojis } from './emoji/test/missing.mjs';\nexport { createOptimisedRegex, createOptimisedRegexForEmojiSequences } from './emoji/regex/create.mjs';\nexport { prepareEmojiForIconSet, prepareEmojiForIconsList } from './emoji/parse.mjs';\nexport { findAndReplaceEmojisInText } from './emoji/replace/replace.mjs';\nexport { camelToKebab, camelize, pascalize, snakelize } from './misc/strings.mjs';\nexport { commonObjectProps, compareObjects, unmergeObjects } from './misc/objects.mjs';\nexport { sanitiseTitleAttribute } from './misc/title.mjs';\nimport './css/common.mjs';\nimport './css/format.mjs';\nimport 'debug';\nimport './emoji/data.mjs';\nimport './emoji/test/components.mjs';\nimport './emoji/regex/tree.mjs';\nimport './emoji/regex/base.mjs';\nimport './emoji/regex/numbers.mjs';\nimport './emoji/regex/similar.mjs';\nimport './emoji/test/similar.mjs';\nimport './emoji/test/name.mjs';\nimport './emoji/test/tree.mjs';\nimport './emoji/replace/find.mjs';\n", "import { log } from '../logger.js';\nimport type { ExtendedIconifyIcon, IconifyIcon, IconifyJSON } from '@iconify/types';\nimport type { IconifyIconCustomisations } from '@iconify/utils';\nimport { getIconData, iconToHTML, iconToSVG, replaceIDs, stringToIcon } from '@iconify/utils';\n\ninterface AsyncIconLoader {\n  name: string;\n  loader: () => Promise<IconifyJSON>;\n}\n\ninterface SyncIconLoader {\n  name: string;\n  icons: IconifyJSON;\n}\n\nexport type IconLoader = AsyncIconLoader | SyncIconLoader;\n\nexport const unknownIcon: IconifyIcon = {\n  body: '<g><rect width=\"80\" height=\"80\" style=\"fill: #087ebf; stroke-width: 0px;\"/><text transform=\"translate(21.16 64.67)\" style=\"fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;\"><tspan x=\"0\" y=\"0\">?</tspan></text></g>',\n  height: 80,\n  width: 80,\n};\n\nconst iconsStore = new Map<string, IconifyJSON>();\nconst loaderStore = new Map<string, AsyncIconLoader['loader']>();\n\nexport const registerIconPacks = (iconLoaders: IconLoader[]) => {\n  for (const iconLoader of iconLoaders) {\n    if (!iconLoader.name) {\n      throw new Error(\n        'Invalid icon loader. Must have a \"name\" property with non-empty string value.'\n      );\n    }\n    log.debug('Registering icon pack:', iconLoader.name);\n    if ('loader' in iconLoader) {\n      loaderStore.set(iconLoader.name, iconLoader.loader);\n    } else if ('icons' in iconLoader) {\n      iconsStore.set(iconLoader.name, iconLoader.icons);\n    } else {\n      log.error('Invalid icon loader:', iconLoader);\n      throw new Error('Invalid icon loader. Must have either \"icons\" or \"loader\" property.');\n    }\n  }\n};\n\nconst getRegisteredIconData = async (iconName: string, fallbackPrefix?: string) => {\n  const data = stringToIcon(iconName, true, fallbackPrefix !== undefined);\n  if (!data) {\n    throw new Error(`Invalid icon name: ${iconName}`);\n  }\n  const prefix = data.prefix || fallbackPrefix;\n  if (!prefix) {\n    throw new Error(`Icon name must contain a prefix: ${iconName}`);\n  }\n  let icons = iconsStore.get(prefix);\n  if (!icons) {\n    const loader = loaderStore.get(prefix);\n    if (!loader) {\n      throw new Error(`Icon set not found: ${data.prefix}`);\n    }\n    try {\n      const loaded = await loader();\n      icons = { ...loaded, prefix };\n      iconsStore.set(prefix, icons);\n    } catch (e) {\n      log.error(e);\n      throw new Error(`Failed to load icon set: ${data.prefix}`);\n    }\n  }\n  const iconData = getIconData(icons, data.name);\n  if (!iconData) {\n    throw new Error(`Icon not found: ${iconName}`);\n  }\n  return iconData;\n};\n\nexport const isIconAvailable = async (iconName: string) => {\n  try {\n    await getRegisteredIconData(iconName);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const getIconSVG = async (\n  iconName: string,\n  customisations?: IconifyIconCustomisations & { fallbackPrefix?: string },\n  extraAttributes?: Record<string, string>\n) => {\n  let iconData: ExtendedIconifyIcon;\n  try {\n    iconData = await getRegisteredIconData(iconName, customisations?.fallbackPrefix);\n  } catch (e) {\n    log.error(e);\n    iconData = unknownIcon;\n  }\n  const renderData = iconToSVG(iconData, customisations);\n  const svg = iconToHTML(replaceIDs(renderData.body), {\n    ...renderData.attributes,\n    ...extraAttributes,\n  });\n  return svg;\n};\n", "export function dedent(\n  templ: TemplateStringsArray | string,\n  ...values: unknown[]\n): string {\n  let strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n\n  // 1. Remove trailing whitespace.\n  strings[strings.length - 1] = strings[strings.length - 1].replace(\n    /\\r?\\n([\\t ]*)$/,\n    '',\n  );\n\n  // 2. Find all line breaks to determine the highest common indentation level.\n  const indentLengths = strings.reduce((arr, str) => {\n    const matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n    if (matches) {\n      return arr.concat(\n        matches.map((match) => match.match(/[\\t ]/g)?.length ?? 0),\n      );\n    }\n    return arr;\n  }, <number[]>[]);\n\n  // 3. Remove the common indentation from all strings.\n  if (indentLengths.length) {\n    const pattern = new RegExp(`\\n[\\t ]{${Math.min(...indentLengths)}}`, 'g');\n\n    strings = strings.map((str) => str.replace(pattern, '\\n'));\n  }\n\n  // 4. Remove leading whitespace.\n  strings[0] = strings[0].replace(/^\\r?\\n/, '');\n\n  // 5. Perform interpolation.\n  let string = strings[0];\n\n  values.forEach((value, i) => {\n    // 5.1 Read current indentation level\n    const endentations = string.match(/(?:^|\\n)( *)$/)\n    const endentation = endentations ? endentations[1] : ''\n    let indentedValue = value\n    // 5.2 Add indentation to values with multiline strings\n    if (typeof value === 'string' && value.includes('\\n')) {\n      indentedValue = String(value)\n        .split('\\n')\n        .map((str, i) => {\n          return i === 0 ? str : `${endentation}${str}`\n        })\n        .join('\\n');\n    }\n\n    string += indentedValue + strings[i + 1];\n  });\n\n  return string;\n}\n\nexport default dedent;\n", "/**\n * Gets the original marked default options.\n */\nexport function _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null,\n    };\n}\nexport let _defaults = _getDefaults();\nexport function changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null };\nfunction edit(regex, opt = '') {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(other.caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        },\n    };\n    return obj;\n}\nexport const other = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n    htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/\\|table/g, '') // table not in commonmark\n    .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText,\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    lheading: lheadingGfm,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex(),\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex(),\n};\n/**\n * Inline-Level Grammar\n */\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n    .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\nconst emStrongRDelimAstCore = '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n    .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n    + '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest,\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    emStrongRDelimAst: emStrongRDelimAstGfm,\n    emStrongLDelim: emStrongLDelimGfm,\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex(),\n};\n/**\n * exports\n */\nexport const block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic,\n};\nexport const inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic,\n};\n", "import { other } from './rules.ts';\n/**\n * Helpers\n */\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nexport function escape(html, encode) {\n    if (encode) {\n        if (other.escapeTest.test(html)) {\n            return html.replace(other.escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (other.escapeTestNoEncode.test(html)) {\n            return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nexport function unescape(html) {\n    // explicitly match decimal, hex, and named HTML entities\n    return html.replace(other.unescapeTest, (_, n) => {\n        n = n.toLowerCase();\n        if (n === 'colon')\n            return ':';\n        if (n.charAt(0) === '#') {\n            return n.charAt(1) === 'x'\n                ? String.fromCharCode(parseInt(n.substring(2), 16))\n                : String.fromCharCode(+n.substring(1));\n        }\n        return '';\n    });\n}\nexport function cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(other.percentDecode, '%');\n    }\n    catch {\n        return null;\n    }\n    return href;\n}\nexport function splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(other.splitPipe);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells.at(-1)?.trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n            suffLen++;\n        }\n        else if (currChar !== c && invert) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nexport function findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport { rtrim, splitCells, findClosingBracket, } from './helpers.ts';\nfunction outputLink(cap, link, raw, lexer, rules) {\n    const href = link.href;\n    const title = link.title || null;\n    const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text),\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text,\n    };\n}\nfunction indentCodeCompensation(raw, text, rules) {\n    const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(rules.other.beginningSpace);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nexport class _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0],\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text,\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text,\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (this.rules.other.endingHash.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: rtrim(cap[0], '\\n'),\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            let lines = rtrim(cap[0], '\\n').split('\\n');\n            let raw = '';\n            let text = '';\n            const tokens = [];\n            while (lines.length > 0) {\n                let inBlockquote = false;\n                const currentLines = [];\n                let i;\n                for (i = 0; i < lines.length; i++) {\n                    // get lines up to a continuation\n                    if (this.rules.other.blockquoteStart.test(lines[i])) {\n                        currentLines.push(lines[i]);\n                        inBlockquote = true;\n                    }\n                    else if (!inBlockquote) {\n                        currentLines.push(lines[i]);\n                    }\n                    else {\n                        break;\n                    }\n                }\n                lines = lines.slice(i);\n                const currentRaw = currentLines.join('\\n');\n                const currentText = currentRaw\n                    // precede setext continuation with 4 spaces so it isn't a setext\n                    .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n                    .replace(this.rules.other.blockquoteSetextReplace2, '');\n                raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n                text = text ? `${text}\\n${currentText}` : currentText;\n                // parse blockquote lines as top level tokens\n                // merge paragraphs if this is a continuation\n                const top = this.lexer.state.top;\n                this.lexer.state.top = true;\n                this.lexer.blockTokens(currentText, tokens, true);\n                this.lexer.state.top = top;\n                // if there is no continuation then we are done\n                if (lines.length === 0) {\n                    break;\n                }\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'code') {\n                    // blockquote continuation cannot be preceded by a code block\n                    break;\n                }\n                else if (lastToken?.type === 'blockquote') {\n                    // include continuation in nested blockquote\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.blockquote(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n                    break;\n                }\n                else if (lastToken?.type === 'list') {\n                    // include continuation in nested list\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.list(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n                    lines = newText.substring(tokens.at(-1).raw.length).split('\\n');\n                    continue;\n                }\n            }\n            return {\n                type: 'blockquote',\n                raw,\n                tokens,\n                text,\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: [],\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = this.rules.other.listItemRegex(bull);\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                let raw = '';\n                let itemContents = '';\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let blankLine = !line.trim();\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else if (blankLine) {\n                    indent = cap[1].length + 1;\n                }\n                else {\n                    indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n                    const hrRegex = this.rules.other.hrRegex(indent);\n                    const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n                    const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n                    const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        let nextLineWithoutTabs;\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n                            nextLineWithoutTabs = nextLine;\n                        }\n                        else {\n                            nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of html block\n                        if (htmlBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(nextLine)) {\n                            break;\n                        }\n                        if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLineWithoutTabs.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (this.rules.other.doubleBlankLine.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = this.rules.other.listIsTask.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: [],\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            const lastItem = list.items.at(-1);\n            if (lastItem) {\n                lastItem.raw = lastItem.raw.trimEnd();\n                lastItem.text = lastItem.text.trimEnd();\n            }\n            else {\n                // not a list since there were no items\n                return;\n            }\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0],\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title,\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!this.rules.other.tableDelimiter.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n        const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: [],\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (this.rules.other.tableAlignRight.test(align)) {\n                item.align.push('right');\n            }\n            else if (this.rules.other.tableAlignCenter.test(align)) {\n                item.align.push('center');\n            }\n            else if (this.rules.other.tableAlignLeft.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (let i = 0; i < headers.length; i++) {\n            item.header.push({\n                text: headers[i],\n                tokens: this.lexer.inline(headers[i]),\n                header: true,\n                align: item.align[i],\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell),\n                    header: false,\n                    align: item.align[i],\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1]),\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0]),\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: cap[1],\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0],\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = this.rules.other.pedanticHrefTitle.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (this.rules.other.startAngleBracket.test(href)) {\n                if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n            }, cap[0], this.lexer, this.rules);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text,\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer, this.rules);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text),\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text),\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n            const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n            const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0],\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2]),\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[1];\n                href = 'mailto:' + text;\n            }\n            else {\n                text = cap[1];\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[0];\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = cap[0];\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            const escaped = this.lexer.state.inRawBlock;\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                escaped,\n            };\n        }\n    }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\n/**\n * Block Lexer\n */\nexport class _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true,\n        };\n        const rules = {\n            other,\n            block: block.normal,\n            inline: inline.normal,\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline,\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src.replace(other.carriageReturn, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n            src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n        }\n        while (src) {\n            let token;\n            if (this.options.extensions?.block?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.raw.length === 1 && lastToken !== undefined) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    lastToken.raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title,\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                const lastToken = tokens.at(-1);\n                if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = cutSrc.length !== src.length;\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match = null;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index)\n                            + '[' + 'a'.repeat(match[0].length - 2) + ']'\n                            + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        let keepPrevChar = false;\n        let prevChar = '';\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            let token;\n            // extensions\n            if (this.options.extensions?.inline?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.type === 'text' && lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { cleanUrl, escape, } from './helpers.ts';\nimport { other } from './rules.ts';\n/**\n * Renderer\n */\nexport class _Renderer {\n    options;\n    parser; // set by the parser\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(token) {\n        return '';\n    }\n    code({ text, lang, escaped }) {\n        const langString = (lang || '').match(other.notSpaceStart)?.[0];\n        const code = text.replace(other.endingNewline, '') + '\\n';\n        if (!langString) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(langString)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote({ tokens }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n    }\n    html({ text }) {\n        return text;\n    }\n    heading({ tokens, depth }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n    }\n    hr(token) {\n        return '<hr>\\n';\n    }\n    list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n            const item = token.items[j];\n            body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n            const checkbox = this.checkbox({ checked: !!item.checked });\n            if (item.loose) {\n                if (item.tokens[0]?.type === 'paragraph') {\n                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                        item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n                        item.tokens[0].tokens[0].escaped = true;\n                    }\n                }\n                else {\n                    item.tokens.unshift({\n                        type: 'text',\n                        raw: checkbox + ' ',\n                        text: checkbox + ' ',\n                        escaped: true,\n                    });\n                }\n            }\n            else {\n                itemBody += checkbox + ' ';\n            }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n    }\n    checkbox({ checked }) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n    }\n    table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n            cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({ text: cell });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n            const row = token.rows[j];\n            cell = '';\n            for (let k = 0; k < row.length; k++) {\n                cell += this.tablecell(row[k]);\n            }\n            body += this.tablerow({ text: cell });\n        }\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow({ text }) {\n        return `<tr>\\n${text}</tr>\\n`;\n    }\n    tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align\n            ? `<${type} align=\"${token.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong({ tokens }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n    }\n    em({ tokens }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n    }\n    codespan({ text }) {\n        return `<code>${escape(text, true)}</code>`;\n    }\n    br(token) {\n        return '<br>';\n    }\n    del({ tokens }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n    }\n    link({ href, title, tokens }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + (escape(title)) + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image({ href, title, text }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return escape(text);\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${escape(title)}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(token) {\n        return 'tokens' in token && token.tokens\n            ? this.parser.parseInline(token.tokens)\n            : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n    }\n}\n", "/**\n * Text<PERSON><PERSON>er\n * returns only the textual part of the token\n */\nexport class _TextRenderer {\n    // no need for block level renderers\n    strong({ text }) {\n        return text;\n    }\n    em({ text }) {\n        return text;\n    }\n    codespan({ text }) {\n        return text;\n    }\n    del({ text }) {\n        return text;\n    }\n    html({ text }) {\n        return text;\n    }\n    text({ text }) {\n        return text;\n    }\n    link({ text }) {\n        return '' + text;\n    }\n    image({ text }) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\n/**\n * Parsing & Compiling\n */\nexport class _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const genericToken = anyToken;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'space': {\n                    out += this.renderer.space(token);\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr(token);\n                    continue;\n                }\n                case 'heading': {\n                    out += this.renderer.heading(token);\n                    continue;\n                }\n                case 'code': {\n                    out += this.renderer.code(token);\n                    continue;\n                }\n                case 'table': {\n                    out += this.renderer.table(token);\n                    continue;\n                }\n                case 'blockquote': {\n                    out += this.renderer.blockquote(token);\n                    continue;\n                }\n                case 'list': {\n                    out += this.renderer.list(token);\n                    continue;\n                }\n                case 'html': {\n                    out += this.renderer.html(token);\n                    continue;\n                }\n                case 'paragraph': {\n                    out += this.renderer.paragraph(token);\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = this.renderer.text(textToken);\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + this.renderer.text(textToken);\n                    }\n                    if (top) {\n                        out += this.renderer.paragraph({\n                            type: 'paragraph',\n                            raw: body,\n                            text: body,\n                            tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n                        });\n                    }\n                    else {\n                        out += body;\n                    }\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer = this.renderer) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'escape': {\n                    out += renderer.text(token);\n                    break;\n                }\n                case 'html': {\n                    out += renderer.html(token);\n                    break;\n                }\n                case 'link': {\n                    out += renderer.link(token);\n                    break;\n                }\n                case 'image': {\n                    out += renderer.image(token);\n                    break;\n                }\n                case 'strong': {\n                    out += renderer.strong(token);\n                    break;\n                }\n                case 'em': {\n                    out += renderer.em(token);\n                    break;\n                }\n                case 'codespan': {\n                    out += renderer.codespan(token);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br(token);\n                    break;\n                }\n                case 'del': {\n                    out += renderer.del(token);\n                    break;\n                }\n                case 'text': {\n                    out += renderer.text(token);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nexport class _Hooks {\n    options;\n    block;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens',\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n    /**\n     * Provide function to tokenize markdown\n     */\n    provideLexer() {\n        return this.block ? _Lexer.lex : _Lexer.lexInline;\n    }\n    /**\n     * Provide function to parse tokens\n     */\n    provideParser() {\n        return this.block ? _Parser.parse : _Parser.parseInline;\n    }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nexport class Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.parseMarkdown(true);\n    parseInline = this.parseMarkdown(false);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (['options', 'parser'].includes(prop)) {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    const rendererFunc = pack.renderer[rendererProp];\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (['options', 'block'].includes(prop)) {\n                        // ignore options and block properties\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    parseMarkdown(blockType) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const parse = (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            const throwError = this.onError(!!opt.silent, !!opt.async);\n            // throw error if an extension set async to true but parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n            }\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n                opt.hooks.block = blockType;\n            }\n            const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n            const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n        return parse;\n    }\n    onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n", "import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport { _getDefaults, changeDefaults, _defaults, } from './defaults.ts';\nconst markedInstance = new Marked();\nexport function marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\n", "import type { MarkedToken, Token } from 'marked';\nimport { marked } from 'marked';\nimport { dedent } from 'ts-dedent';\nimport type { MarkdownLine, MarkdownWordType } from './types.js';\nimport type { MermaidConfig } from '../config.type.js';\n\n/**\n * @param markdown - markdown to process\n * @returns processed markdown\n */\nfunction preprocessMarkdown(markdown: string, { markdownAutoWrap }: MermaidConfig): string {\n  //Replace <br/>with \\n\n  const withoutBR = markdown.replace(/<br\\/>/g, '\\n');\n  // Replace multiple newlines with a single newline\n  const withoutMultipleNewlines = withoutBR.replace(/\\n{2,}/g, '\\n');\n  // Remove extra spaces at the beginning of each line\n  const withoutExtraSpaces = dedent(withoutMultipleNewlines);\n  if (markdownAutoWrap === false) {\n    return withoutExtraSpaces.replace(/ /g, '&nbsp;');\n  }\n  return withoutExtraSpaces;\n}\n\n/**\n * @param markdown - markdown to split into lines\n */\nexport function markdownToLines(markdown: string, config: MermaidConfig = {}): MarkdownLine[] {\n  const preprocessedMarkdown = preprocessMarkdown(markdown, config);\n  const nodes = marked.lexer(preprocessedMarkdown);\n  const lines: MarkdownLine[] = [[]];\n  let currentLine = 0;\n\n  function processNode(node: MarkedToken, parentType: MarkdownWordType = 'normal') {\n    if (node.type === 'text') {\n      const textLines = node.text.split('\\n');\n      textLines.forEach((textLine, index) => {\n        if (index !== 0) {\n          currentLine++;\n          lines.push([]);\n        }\n        textLine.split(' ').forEach((word) => {\n          word = word.replace(/&#39;/g, `'`);\n          if (word) {\n            lines[currentLine].push({ content: word, type: parentType });\n          }\n        });\n      });\n    } else if (node.type === 'strong' || node.type === 'em') {\n      node.tokens.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken, node.type);\n      });\n    } else if (node.type === 'html') {\n      lines[currentLine].push({ content: node.text, type: 'normal' });\n    }\n  }\n\n  nodes.forEach((treeNode) => {\n    if (treeNode.type === 'paragraph') {\n      treeNode.tokens?.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken);\n      });\n    } else if (treeNode.type === 'html') {\n      lines[currentLine].push({ content: treeNode.text, type: 'normal' });\n    }\n  });\n\n  return lines;\n}\n\nexport function markdownToHTML(markdown: string, { markdownAutoWrap }: MermaidConfig = {}) {\n  const nodes = marked.lexer(markdown);\n\n  function output(node: Token): string {\n    if (node.type === 'text') {\n      if (markdownAutoWrap === false) {\n        return node.text.replace(/\\n */g, '<br/>').replace(/ /g, '&nbsp;');\n      }\n      return node.text.replace(/\\n */g, '<br/>');\n    } else if (node.type === 'strong') {\n      return `<strong>${node.tokens?.map(output).join('')}</strong>`;\n    } else if (node.type === 'em') {\n      return `<em>${node.tokens?.map(output).join('')}</em>`;\n    } else if (node.type === 'paragraph') {\n      return `<p>${node.tokens?.map(output).join('')}</p>`;\n    } else if (node.type === 'space') {\n      return '';\n    } else if (node.type === 'html') {\n      return `${node.text}`;\n    } else if (node.type === 'escape') {\n      return node.text;\n    }\n    return `Unsupported markdown: ${node.type}`;\n  }\n\n  return nodes.map(output).join('');\n}\n", "import type { CheckFitFunction, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MarkdownWordType } from './types.js';\n\n/**\n * Splits a string into graphemes if available, otherwise characters.\n */\nexport function splitTextToChars(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter().segment(text)].map((s) => s.segment);\n  }\n  return [...text];\n}\n\n/**\n * Splits a string into words by using `Intl.Segmenter` if available, or splitting by ' '.\n * `Intl.Segmenter` uses the default locale, which might be different across browsers.\n */\nexport function splitLineToWords(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter(undefined, { granularity: 'word' }).segment(text)].map(\n      (s) => s.segment\n    );\n  }\n  // Split by ' ' removes the ' 's from the result.\n  const words = text.split(' ');\n  // Add the ' 's back to the result.\n  const wordsWithSpaces = words.flatMap((s) => [s, ' ']).filter((s) => s);\n  // Remove last space.\n  wordsWithSpaces.pop();\n  return wordsWithSpaces;\n}\n\n/**\n * Splits a word into two parts, the first part fits the width and the remaining part.\n * @param checkFit - Function to check if word fits\n * @param word - Word to split\n * @returns [first part of word that fits, rest of word]\n */\nexport function splitWordToFitWidth(\n  checkFit: CheckFitFunction,\n  word: MarkdownWord\n): [MarkdownWord, MarkdownWord] {\n  const characters = splitTextToChars(word.content);\n  return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);\n}\n\nfunction splitWordToFitWidthRecursion(\n  checkFit: CheckFitFunction,\n  usedChars: string[],\n  remainingChars: string[],\n  type: MarkdownWordType\n): [MarkdownWord, MarkdownWord] {\n  if (remainingChars.length === 0) {\n    return [\n      { content: usedChars.join(''), type },\n      { content: '', type },\n    ];\n  }\n  const [nextChar, ...rest] = remainingChars;\n  const newWord = [...usedChars, nextChar];\n  if (checkFit([{ content: newWord.join(''), type }])) {\n    return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);\n  }\n  if (usedChars.length === 0 && nextChar) {\n    // If the first character does not fit, split it anyway\n    usedChars.push(nextChar);\n    remainingChars.shift();\n  }\n  return [\n    { content: usedChars.join(''), type },\n    { content: remainingChars.join(''), type },\n  ];\n}\n\n/**\n * Splits a line into multiple lines that satisfy the checkFit function.\n * @param line - Line to split\n * @param checkFit - Function to check if line fits\n * @returns Array of lines that fit\n */\nexport function splitLineToFitWidth(\n  line: MarkdownLine,\n  checkFit: CheckFitFunction\n): MarkdownLine[] {\n  if (line.some(({ content }) => content.includes('\\n'))) {\n    throw new Error('splitLineToFitWidth does not support newlines in the line');\n  }\n  return splitLineToFitWidthRecursion(line, checkFit);\n}\n\nfunction splitLineToFitWidthRecursion(\n  words: MarkdownWord[],\n  checkFit: CheckFitFunction,\n  lines: MarkdownLine[] = [],\n  newLine: MarkdownLine = []\n): MarkdownLine[] {\n  // Return if there is nothing left to split\n  if (words.length === 0) {\n    // If there is a new line, add it to the lines\n    if (newLine.length > 0) {\n      lines.push(newLine);\n    }\n    return lines.length > 0 ? lines : [];\n  }\n  let joiner = '';\n  if (words[0].content === ' ') {\n    joiner = ' ';\n    words.shift();\n  }\n  const nextWord: MarkdownWord = words.shift() ?? { content: ' ', type: 'normal' };\n  const lineWithNextWord: MarkdownLine = [...newLine];\n  if (joiner !== '') {\n    lineWithNextWord.push({ content: joiner, type: 'normal' });\n  }\n  lineWithNextWord.push(nextWord);\n\n  if (checkFit(lineWithNextWord)) {\n    // nextWord fits, so we can add it to the new line and continue\n    return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);\n  }\n\n  // nextWord doesn't fit, so we need to split it\n  if (newLine.length > 0) {\n    // There was text in newLine, so add it to lines and push nextWord back into words.\n    lines.push(newLine);\n    words.unshift(nextWord);\n  } else if (nextWord.content) {\n    // There was no text in newLine, so we need to split nextWord\n    const [line, rest] = splitWordToFitWidth(checkFit, nextWord);\n    lines.push([line]);\n    if (rest.content) {\n      words.unshift(rest);\n    }\n  }\n  return splitLineToFitWidthRecursion(words, checkFit, lines);\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n// @ts-nocheck TODO: Fix types\nimport { select } from 'd3';\nimport type { MermaidConfig } from '../config.type.js';\nimport { getConfig, sanitizeText } from '../diagram-api/diagramAPI.js';\nimport type { SVGGroup } from '../diagram-api/types.js';\nimport common, { hasKatex, renderKatex } from '../diagrams/common/common.js';\nimport type { D3TSpanElement, D3TextElement } from '../diagrams/common/commonTypes.js';\nimport { log } from '../logger.js';\nimport { markdownToHTML, markdownToLines } from '../rendering-util/handle-markdown-text.js';\nimport { decodeEntities } from '../utils.js';\nimport { getIconSVG, isIconAvailable } from './icons.js';\nimport { splitLineToFitWidth } from './splitText.js';\nimport type { MarkdownLine, MarkdownWord } from './types.js';\n\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\nasync function addHtmlSpan(element, node, width, classes, addBackground = false) {\n  const fo = element.append('foreignObject');\n  // This is not the final width but used in order to make sure the foreign\n  // object in firefox gets a width at all. The final width is fetched from the div\n  fo.attr('width', `${10 * width}px`);\n  fo.attr('height', `${10 * width}px`);\n\n  const div = fo.append('xhtml:div');\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common.lineBreakRegex, '\\n'), getConfig());\n  }\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  const span = div.append('span');\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr('class', `${labelClass} ${classes}`);\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'table-cell');\n  div.style('white-space', 'nowrap');\n  div.style('line-height', '1.5');\n  div.style('max-width', width + 'px');\n  div.style('text-align', 'center');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  if (addBackground) {\n    div.attr('class', 'labelBkg');\n  }\n\n  let bbox = div.node().getBoundingClientRect();\n  if (bbox.width === width) {\n    div.style('display', 'table');\n    div.style('white-space', 'break-spaces');\n    div.style('width', width + 'px');\n    bbox = div.node().getBoundingClientRect();\n  }\n\n  // fo.style('width', bbox.width);\n  // fo.style('height', bbox.height);\n\n  return fo.node();\n}\n\n/**\n * Creates a tspan element with the specified attributes for text positioning.\n *\n * @param textElement - The parent text element to append the tspan element.\n * @param lineIndex - The index of the current line in the structuredText array.\n * @param lineHeight - The line height value for the text.\n * @returns The created tspan element.\n */\nfunction createTspan(textElement: any, lineIndex: number, lineHeight: number) {\n  return textElement\n    .append('tspan')\n    .attr('class', 'text-outer-tspan')\n    .attr('x', 0)\n    .attr('y', lineIndex * lineHeight - 0.1 + 'em')\n    .attr('dy', lineHeight + 'em');\n}\n\nfunction computeWidthOfText(parentNode: any, lineHeight: number, line: MarkdownLine): number {\n  const testElement = parentNode.append('text');\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, line);\n  const textLength = testSpan.node().getComputedTextLength();\n  testElement.remove();\n  return textLength;\n}\n\nexport function computeDimensionOfText(\n  parentNode: SVGGroup,\n  lineHeight: number,\n  text: string\n): DOMRect | undefined {\n  const testElement: D3TextElement = parentNode.append('text');\n  const testSpan: D3TSpanElement = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, [{ content: text, type: 'normal' }]);\n  const textDimension: DOMRect | undefined = testSpan.node()?.getBoundingClientRect();\n  if (textDimension) {\n    testElement.remove();\n  }\n  return textDimension;\n}\n\n/**\n * Creates a formatted text element by breaking lines and applying styles based on\n * the given structuredText.\n *\n * @param width - The maximum allowed width of the text.\n * @param g - The parent group element to append the formatted text.\n * @param structuredText - The structured text data to format.\n * @param addBackground - Whether to add a background to the text.\n */\nfunction createFormattedText(\n  width: number,\n  g: any,\n  structuredText: MarkdownWord[][],\n  addBackground = false\n) {\n  const lineHeight = 1.1;\n  const labelGroup = g.append('g');\n  const bkg = labelGroup.insert('rect').attr('class', 'background').attr('style', 'stroke: none');\n  const textElement = labelGroup.append('text').attr('y', '-10.1');\n  let lineIndex = 0;\n  for (const line of structuredText) {\n    /**\n     * Preprocess raw string content of line data\n     * Creating an array of strings pre-split to satisfy width limit\n     */\n    const checkWidth = (line: MarkdownLine) =>\n      computeWidthOfText(labelGroup, lineHeight, line) <= width;\n    const linesUnderWidth = checkWidth(line) ? [line] : splitLineToFitWidth(line, checkWidth);\n    /** Add each prepared line as a tspan to the parent node */\n    for (const preparedLine of linesUnderWidth) {\n      const tspan = createTspan(textElement, lineIndex, lineHeight);\n      updateTextContentAndStyles(tspan, preparedLine);\n      lineIndex++;\n    }\n  }\n  if (addBackground) {\n    const bbox = textElement.node().getBBox();\n    const padding = 2;\n    bkg\n      .attr('x', bbox.x - padding)\n      .attr('y', bbox.y - padding)\n      .attr('width', bbox.width + 2 * padding)\n      .attr('height', bbox.height + 2 * padding);\n\n    return labelGroup.node();\n  } else {\n    return textElement.node();\n  }\n}\n\n/**\n * Updates the text content and styles of the given tspan element based on the\n * provided wrappedLine data.\n *\n * @param tspan - The tspan element to update.\n * @param wrappedLine - The line data to apply to the tspan element.\n */\nfunction updateTextContentAndStyles(tspan: any, wrappedLine: MarkdownWord[]) {\n  tspan.text('');\n\n  wrappedLine.forEach((word, index) => {\n    const innerTspan = tspan\n      .append('tspan')\n      .attr('font-style', word.type === 'em' ? 'italic' : 'normal')\n      .attr('class', 'text-inner-tspan')\n      .attr('font-weight', word.type === 'strong' ? 'bold' : 'normal');\n    if (index === 0) {\n      innerTspan.text(word.content);\n    } else {\n      // TODO: check what joiner to use.\n      innerTspan.text(' ' + word.content);\n    }\n  });\n}\n\n/**\n * Convert fontawesome labels into fontawesome icons by using a regex pattern\n * @param text - The raw string to convert\n * @returns string with fontawesome icons as svg if the icon is registered otherwise as i tags\n */\nexport async function replaceIconSubstring(text: string) {\n  const pendingReplacements: Promise<string>[] = [];\n  // cspell: disable-next-line\n  text.replace(/(fa[bklrs]?):fa-([\\w-]+)/g, (fullMatch, prefix, iconName) => {\n    pendingReplacements.push(\n      (async () => {\n        const registeredIconName = `${prefix}:${iconName}`;\n        if (await isIconAvailable(registeredIconName)) {\n          return await getIconSVG(registeredIconName, undefined, { class: 'label-icon' });\n        } else {\n          return `<i class='${sanitizeText(fullMatch).replace(':', ' ')}'></i>`;\n        }\n      })()\n    );\n    return fullMatch;\n  });\n\n  const replacements = await Promise.all(pendingReplacements);\n  // cspell: disable-next-line\n  return text.replace(/(fa[bklrs]?):fa-([\\w-]+)/g, () => replacements.shift() ?? '');\n}\n\n// Note when using from flowcharts converting the API isNode means classes should be set accordingly. When using htmlLabels => to set classes to 'nodeLabel' when isNode=true otherwise 'edgeLabel'\n// When not using htmlLabels => to set classes to 'title-row' when isTitle=true otherwise 'title-row'\nexport const createText = async (\n  el,\n  text = '',\n  {\n    style = '',\n    isTitle = false,\n    classes = '',\n    useHtmlLabels = true,\n    isNode = true,\n    width = 200,\n    addSvgBackground = false,\n  } = {},\n  config?: MermaidConfig\n) => {\n  log.debug(\n    'XYZ createText',\n    text,\n    style,\n    isTitle,\n    classes,\n    useHtmlLabels,\n    isNode,\n    'addSvgBackground: ',\n    addSvgBackground\n  );\n  if (useHtmlLabels) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n\n    const htmlText = markdownToHTML(text, config);\n    const decodedReplacedText = await replaceIconSubstring(decodeEntities(htmlText));\n\n    //for Katex the text could contain escaped characters, \\\\relax that should be transformed to \\relax\n    const inputForKatex = text.replace(/\\\\\\\\/g, '\\\\');\n\n    const node = {\n      isNode,\n      label: hasKatex(text) ? inputForKatex : decodedReplacedText,\n      labelStyle: style.replace('fill:', 'color:'),\n    };\n    const vertexNode = await addHtmlSpan(el, node, width, classes, addSvgBackground);\n    return vertexNode;\n  } else {\n    //sometimes the user might add br tags with 1 or more spaces in between, so we need to replace them with <br/>\n    const sanitizeBR = text.replace(/<br\\s*\\/?>/g, '<br/>');\n    const structuredText = markdownToLines(sanitizeBR.replace('<br>', '<br/>'), config);\n    const svgLabel = createFormattedText(\n      width,\n      el,\n      structuredText,\n      text ? addSvgBackground : false\n    );\n    if (isNode) {\n      if (/stroke:/.exec(style)) {\n        style = style.replace('stroke:', 'lineColor:');\n      }\n\n      const nodeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).attr('style', nodeLabelTextStyle);\n      // svgLabel.setAttribute('style', style);\n    } else {\n      //On style, assume `stroke`, `stroke-width` are used for edge path, so remove them\n      // remove `fill`\n      //  use  `background` as `fill` for label rect,\n\n      const edgeLabelRectStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/background:/g, 'fill:');\n      select(svgLabel)\n        .select('rect')\n        .attr('style', edgeLabelRectStyle.replace(/background:/g, 'fill:'));\n\n      // for text, update fill color with `color`\n      const edgeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).select('text').attr('style', edgeLabelTextStyle);\n    }\n    return svgLabel;\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAIA,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AAgBZ,WAAO,UAAU,SAAU,KAAKA,UAAS;AACvC,MAAAA,WAAUA,YAAW,CAAC;AACtB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,YAAY,IAAI,SAAS,GAAG;AACvC,eAAO,MAAM,GAAG;AAAA,MAClB,WAAW,SAAS,YAAY,SAAS,GAAG,GAAG;AAC7C,eAAOA,SAAQ,OAAO,QAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,MACnD;AACA,YAAM,IAAI;AAAA,QACR,0DACE,KAAK,UAAU,GAAG;AAAA,MACtB;AAAA,IACF;AAUA,aAAS,MAAM,KAAK;AAClB,YAAM,OAAO,GAAG;AAChB,UAAI,IAAI,SAAS,KAAK;AACpB;AAAA,MACF;AACA,UAAI,QAAQ,mIAAmI;AAAA,QAC7I;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,IAAI,WAAW,MAAM,CAAC,CAAC;AAC3B,UAAI,QAAQ,MAAM,CAAC,KAAK,MAAM,YAAY;AAC1C,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAvDS;AAiET,aAAS,SAAS,IAAI;AACpB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK;AAAA,IACd;AAfS;AAyBT,aAAS,QAAQ,IAAI;AACnB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,KAAK;AAAA,MACnC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,MAAM;AAAA,MACpC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,aAAO,KAAK;AAAA,IACd;AAfS;AAqBT,aAAS,OAAO,IAAI,OAAO,GAAG,MAAM;AAClC,UAAI,WAAW,SAAS,IAAI;AAC5B,aAAO,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,QAAQ,WAAW,MAAM;AAAA,IAC7D;AAHS;AAAA;AAAA;;;AC9JT;AAAA;AAAA;AAMA,aAAS,MAAM,KAAK;AACnB,kBAAY,QAAQ;AACpB,kBAAY,UAAU;AACtB,kBAAY,SAAS;AACrB,kBAAY,UAAU;AACtB,kBAAY,SAAS;AACrB,kBAAY,UAAU;AACtB,kBAAY,WAAW;AACvB,kBAAY,UAAU;AAEtB,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAO;AAC/B,oBAAY,GAAG,IAAI,IAAI,GAAG;AAAA,MAC3B,CAAC;AAMD,kBAAY,QAAQ,CAAC;AACrB,kBAAY,QAAQ,CAAC;AAOrB,kBAAY,aAAa,CAAC;AAQ1B,eAAS,YAAY,WAAW;AAC/B,YAAI,OAAO;AAEX,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,kBAAS,QAAQ,KAAK,OAAQ,UAAU,WAAW,CAAC;AACpD,kBAAQ;AAAA,QACT;AAEA,eAAO,YAAY,OAAO,KAAK,IAAI,IAAI,IAAI,YAAY,OAAO,MAAM;AAAA,MACrE;AATS;AAUT,kBAAY,cAAc;AAS1B,eAAS,YAAY,WAAW;AAC/B,YAAI;AACJ,YAAI,iBAAiB;AACrB,YAAI;AACJ,YAAI;AAEJ,iBAAS,SAAS,MAAM;AAEvB,cAAI,CAAC,MAAM,SAAS;AACnB;AAAA,UACD;AAEA,gBAAM,OAAO;AAGb,gBAAM,OAAO,OAAO,oBAAI,KAAK,CAAC;AAC9B,gBAAM,KAAK,QAAQ,YAAY;AAC/B,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,qBAAW;AAEX,eAAK,CAAC,IAAI,YAAY,OAAO,KAAK,CAAC,CAAC;AAEpC,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAEhC,iBAAK,QAAQ,IAAI;AAAA,UAClB;AAGA,cAAI,QAAQ;AACZ,eAAK,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,iBAAiB,CAAC,OAAO,WAAW;AAE7D,gBAAI,UAAU,MAAM;AACnB,qBAAO;AAAA,YACR;AACA;AACA,kBAAM,YAAY,YAAY,WAAW,MAAM;AAC/C,gBAAI,OAAO,cAAc,YAAY;AACpC,oBAAM,MAAM,KAAK,KAAK;AACtB,sBAAQ,UAAU,KAAK,MAAM,GAAG;AAGhC,mBAAK,OAAO,OAAO,CAAC;AACpB;AAAA,YACD;AACA,mBAAO;AAAA,UACR,CAAC;AAGD,sBAAY,WAAW,KAAK,MAAM,IAAI;AAEtC,gBAAM,QAAQ,KAAK,OAAO,YAAY;AACtC,gBAAM,MAAM,MAAM,IAAI;AAAA,QACvB;AAhDS;AAkDT,cAAM,YAAY;AAClB,cAAM,YAAY,YAAY,UAAU;AACxC,cAAM,QAAQ,YAAY,YAAY,SAAS;AAC/C,cAAM,SAAS;AACf,cAAM,UAAU,YAAY;AAE5B,eAAO,eAAe,OAAO,WAAW;AAAA,UACvC,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,KAAK,6BAAM;AACV,gBAAI,mBAAmB,MAAM;AAC5B,qBAAO;AAAA,YACR;AACA,gBAAI,oBAAoB,YAAY,YAAY;AAC/C,gCAAkB,YAAY;AAC9B,6BAAe,YAAY,QAAQ,SAAS;AAAA,YAC7C;AAEA,mBAAO;AAAA,UACR,GAVK;AAAA,UAWL,KAAK,8BAAK;AACT,6BAAiB;AAAA,UAClB,GAFK;AAAA,QAGN,CAAC;AAGD,YAAI,OAAO,YAAY,SAAS,YAAY;AAC3C,sBAAY,KAAK,KAAK;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAvFS;AAyFT,eAAS,OAAO,WAAW,WAAW;AACrC,cAAM,WAAW,YAAY,KAAK,aAAa,OAAO,cAAc,cAAc,MAAM,aAAa,SAAS;AAC9G,iBAAS,MAAM,KAAK;AACpB,eAAO;AAAA,MACR;AAJS;AAaT,eAAS,OAAO,YAAY;AAC3B,oBAAY,KAAK,UAAU;AAC3B,oBAAY,aAAa;AAEzB,oBAAY,QAAQ,CAAC;AACrB,oBAAY,QAAQ,CAAC;AAErB,cAAM,SAAS,OAAO,eAAe,WAAW,aAAa,IAC3D,KAAK,EACL,QAAQ,KAAK,GAAG,EAChB,MAAM,GAAG,EACT,OAAO,OAAO;AAEhB,mBAAW,MAAM,OAAO;AACvB,cAAI,GAAG,CAAC,MAAM,KAAK;AAClB,wBAAY,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC;AAAA,UACnC,OAAO;AACN,wBAAY,MAAM,KAAK,EAAE;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AApBS;AA8BT,eAAS,gBAAgB,QAAQ,UAAU;AAC1C,YAAI,cAAc;AAClB,YAAI,gBAAgB;AACpB,YAAI,YAAY;AAChB,YAAI,aAAa;AAEjB,eAAO,cAAc,OAAO,QAAQ;AACnC,cAAI,gBAAgB,SAAS,WAAW,SAAS,aAAa,MAAM,OAAO,WAAW,KAAK,SAAS,aAAa,MAAM,MAAM;AAE5H,gBAAI,SAAS,aAAa,MAAM,KAAK;AACpC,0BAAY;AACZ,2BAAa;AACb;AAAA,YACD,OAAO;AACN;AACA;AAAA,YACD;AAAA,UACD,WAAW,cAAc,IAAI;AAE5B,4BAAgB,YAAY;AAC5B;AACA,0BAAc;AAAA,UACf,OAAO;AACN,mBAAO;AAAA,UACR;AAAA,QACD;AAGA,eAAO,gBAAgB,SAAS,UAAU,SAAS,aAAa,MAAM,KAAK;AAC1E;AAAA,QACD;AAEA,eAAO,kBAAkB,SAAS;AAAA,MACnC;AAjCS;AAyCT,eAAS,UAAU;AAClB,cAAM,aAAa;AAAA,UAClB,GAAG,YAAY;AAAA,UACf,GAAG,YAAY,MAAM,IAAI,eAAa,MAAM,SAAS;AAAA,QACtD,EAAE,KAAK,GAAG;AACV,oBAAY,OAAO,EAAE;AACrB,eAAO;AAAA,MACR;AAPS;AAgBT,eAAS,QAAQ,MAAM;AACtB,mBAAW,QAAQ,YAAY,OAAO;AACrC,cAAI,gBAAgB,MAAM,IAAI,GAAG;AAChC,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,mBAAW,MAAM,YAAY,OAAO;AACnC,cAAI,gBAAgB,MAAM,EAAE,GAAG;AAC9B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAdS;AAuBT,eAAS,OAAO,KAAK;AACpB,YAAI,eAAe,OAAO;AACzB,iBAAO,IAAI,SAAS,IAAI;AAAA,QACzB;AACA,eAAO;AAAA,MACR;AALS;AAWT,eAAS,UAAU;AAClB,gBAAQ,KAAK,uIAAuI;AAAA,MACrJ;AAFS;AAIT,kBAAY,OAAO,YAAY,KAAK,CAAC;AAErC,aAAO;AAAA,IACR;AA3RS;AA6RT,WAAO,UAAU;AAAA;AAAA;;;ACnSjB;AAAA;AAAA;AAMA,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,YAAY;AACpB,YAAQ,UAAU,aAAa;AAC/B,YAAQ,UAAW,uBAAM;AACxB,UAAI,SAAS;AAEb,aAAO,MAAM;AACZ,YAAI,CAAC,QAAQ;AACZ,mBAAS;AACT,kBAAQ,KAAK,uIAAuI;AAAA,QACrJ;AAAA,MACD;AAAA,IACD,GAAG;AAMH,YAAQ,SAAS;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAWA,aAAS,YAAY;AAIpB,UAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,SAAS,cAAc,OAAO,QAAQ,SAAS;AACrH,eAAO;AAAA,MACR;AAGA,UAAI,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,uBAAuB,GAAG;AAChI,eAAO;AAAA,MACR;AAEA,UAAI;AAKJ,aAAQ,OAAO,aAAa,eAAe,SAAS,mBAAmB,SAAS,gBAAgB,SAAS,SAAS,gBAAgB,MAAM;AAAA,MAEtI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,WAAY,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA;AAAA,MAG1H,OAAO,cAAc,eAAe,UAAU,cAAc,IAAI,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK;AAAA,MAEpJ,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,IAC1H;AA1BS;AAkCT,aAAS,WAAW,MAAM;AACzB,WAAK,CAAC,KAAK,KAAK,YAAY,OAAO,MAClC,KAAK,aACJ,KAAK,YAAY,QAAQ,OAC1B,KAAK,CAAC,KACL,KAAK,YAAY,QAAQ,OAC1B,MAAM,OAAO,QAAQ,SAAS,KAAK,IAAI;AAExC,UAAI,CAAC,KAAK,WAAW;AACpB;AAAA,MACD;AAEA,YAAM,IAAI,YAAY,KAAK;AAC3B,WAAK,OAAO,GAAG,GAAG,GAAG,gBAAgB;AAKrC,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,WAAK,CAAC,EAAE,QAAQ,eAAe,WAAS;AACvC,YAAI,UAAU,MAAM;AACnB;AAAA,QACD;AACA;AACA,YAAI,UAAU,MAAM;AAGnB,kBAAQ;AAAA,QACT;AAAA,MACD,CAAC;AAED,WAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IACxB;AAjCS;AA2CT,YAAQ,MAAM,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,IAAC;AAQtD,aAAS,KAAK,YAAY;AACzB,UAAI;AACH,YAAI,YAAY;AACf,kBAAQ,QAAQ,QAAQ,SAAS,UAAU;AAAA,QAC5C,OAAO;AACN,kBAAQ,QAAQ,WAAW,OAAO;AAAA,QACnC;AAAA,MACD,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AAXS;AAmBT,aAAS,OAAO;AACf,UAAI;AACJ,UAAI;AACH,YAAI,QAAQ,QAAQ,QAAQ,OAAO;AAAA,MACpC,SAAS,OAAO;AAAA,MAGhB;AAGA,UAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;AAC7D,YAAI,QAAQ,IAAI;AAAA,MACjB;AAEA,aAAO;AAAA,IACR;AAfS;AA4BT,aAAS,eAAe;AACvB,UAAI;AAGH,eAAO;AAAA,MACR,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AATS;AAWT,WAAO,UAAU,iBAAoB,OAAO;AAE5C,QAAM,EAAC,WAAU,IAAI,OAAO;AAM5B,eAAW,IAAI,SAAU,GAAG;AAC3B,UAAI;AACH,eAAO,KAAK,UAAU,CAAC;AAAA,MACxB,SAAS,OAAO;AACf,eAAO,iCAAiC,MAAM;AAAA,MAC/C;AAAA,IACD;AAAA;AAAA;;;AC/QA,IAAM,wBAAwB,OAAO;AAAA,EACnC;AAAA,IACE,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AACA,IAAM,6BAA6B,OAAO,OAAO;AAAA,EAC/C,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT,CAAC;AACD,IAAM,mBAAmB,OAAO,OAAO;AAAA,EACrC,GAAG;AAAA,EACH,GAAG;AACL,CAAC;AACD,IAAM,2BAA2B,OAAO,OAAO;AAAA,EAC7C,GAAG;AAAA,EACH,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;;;ACnBD,IAAM,gCAAgC,OAAO,OAAO;AAAA,EAClD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,IAAM,4BAA4B,OAAO,OAAO;AAAA;AAAA,EAE9C,GAAG;AAAA;AAAA,EAEH,GAAG;AACL,CAAC;;;ACVD,IAAM,eAAe,wBAAC,OAAO,UAAU,iBAAiB,WAAW,OAAO;AACxE,QAAM,iBAAiB,MAAM,MAAM,GAAG;AACtC,MAAI,MAAM,MAAM,GAAG,CAAC,MAAM,KAAK;AAC7B,QAAI,eAAe,SAAS,KAAK,eAAe,SAAS,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,eAAW,eAAe,MAAM,EAAE,MAAM,CAAC;AAAA,EAC3C;AACA,MAAI,eAAe,SAAS,KAAK,CAAC,eAAe,QAAQ;AACvD,WAAO;AAAA,EACT;AACA,MAAI,eAAe,SAAS,GAAG;AAC7B,UAAM,QAAQ,eAAe,IAAI;AACjC,UAAM,SAAS,eAAe,IAAI;AAClC,UAAM,SAAS;AAAA;AAAA,MAEb,UAAU,eAAe,SAAS,IAAI,eAAe,CAAC,IAAI;AAAA,MAC1D;AAAA,MACA,MAAM;AAAA,IACR;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,QAAM,OAAO,eAAe,CAAC;AAC7B,QAAM,gBAAgB,KAAK,MAAM,GAAG;AACpC,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ,cAAc,MAAM;AAAA,MAC5B,MAAM,cAAc,KAAK,GAAG;AAAA,IAC9B;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,MAAI,mBAAmB,aAAa,IAAI;AACtC,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF;AACA,WAAO,YAAY,CAAC,iBAAiB,QAAQ,eAAe,IAAI,OAAO;AAAA,EACzE;AACA,SAAO;AACT,GAzCqB;AA0CrB,IAAM,mBAAmB,wBAAC,MAAM,oBAAoB;AAClD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AAAA;AAAA,IAEN,mBAAmB,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,KAAK;AACtE,GAPyB;;;AC3CzB,SAAS,yBAAyB,MAAM,MAAM;AAC5C,QAAM,SAAS,CAAC;AAChB,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,WAAW,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM;AAC3D,MAAI,QAAQ;AACV,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AAbS;;;ACGT,SAAS,cAAc,QAAQ,OAAO;AACpC,QAAM,SAAS,yBAAyB,QAAQ,KAAK;AACrD,aAAW,OAAO,0BAA0B;AAC1C,QAAI,OAAO,4BAA4B;AACrC,UAAI,OAAO,UAAU,EAAE,OAAO,SAAS;AACrC,eAAO,GAAG,IAAI,2BAA2B,GAAG;AAAA,MAC9C;AAAA,IACF,WAAW,OAAO,OAAO;AACvB,aAAO,GAAG,IAAI,MAAM,GAAG;AAAA,IACzB,WAAW,OAAO,QAAQ;AACxB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAdS;;;ACHT,SAAS,aAAa,MAAM,OAAO;AACjC,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,QAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAS,QAAQ,MAAM;AACrB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,SAAS,IAAI,IAAI,CAAC;AAAA,IAC3B;AACA,QAAI,EAAE,QAAQ,WAAW;AACvB,eAAS,IAAI,IAAI;AACjB,YAAM,SAAS,QAAQ,IAAI,KAAK,QAAQ,IAAI,EAAE;AAC9C,YAAM,QAAQ,UAAU,QAAQ,MAAM;AACtC,UAAI,OAAO;AACT,iBAAS,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK;AAAA,MACxC;AAAA,IACF;AACA,WAAO,SAAS,IAAI;AAAA,EACtB;AAbS;AAcT,GAAC,SAAS,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO,KAAK,OAAO,CAAC,GAAG,QAAQ,OAAO;AAC1E,SAAO;AACT;AApBS;;;ACKT,SAAS,oBAAoB,MAAM,MAAM,MAAM;AAC7C,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,MAAI,eAAe,CAAC;AACpB,WAAS,MAAM,OAAO;AACpB,mBAAe;AAAA,MACb,MAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AALS;AAMT,QAAM,IAAI;AACV,OAAK,QAAQ,KAAK;AAClB,SAAO,cAAc,MAAM,YAAY;AACzC;AAbS;AAcT,SAAS,YAAY,MAAM,MAAM;AAC/B,MAAI,KAAK,MAAM,IAAI,GAAG;AACpB,WAAO,oBAAoB,MAAM,MAAM,CAAC,CAAC;AAAA,EAC3C;AACA,QAAM,OAAO,aAAa,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI;AAC5C,SAAO,OAAO,oBAAoB,MAAM,MAAM,IAAI,IAAI;AACxD;AANS;;;ACnBT,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,SAAS,cAAc,MAAM,OAAO,WAAW;AAC7C,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,cAAY,aAAa;AACzB,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK,KAAK,OAAO,QAAQ,SAAS,IAAI;AAAA,EAC/C;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,UAAU;AACtC,MAAI,aAAa,QAAQ,CAAC,SAAS,QAAQ;AACzC,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC;AAClB,MAAI,OAAO,SAAS,MAAM;AAC1B,MAAI,WAAW,UAAU,KAAK,IAAI;AAClC,SAAO,MAAM;AACX,QAAI,UAAU;AACZ,YAAM,MAAM,WAAW,IAAI;AAC3B,UAAI,MAAM,GAAG,GAAG;AACd,iBAAS,KAAK,IAAI;AAAA,MACpB,OAAO;AACL,iBAAS,KAAK,KAAK,KAAK,MAAM,QAAQ,SAAS,IAAI,SAAS;AAAA,MAC9D;AAAA,IACF,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,WAAO,SAAS,MAAM;AACtB,QAAI,SAAS,QAAQ;AACnB,aAAO,SAAS,KAAK,EAAE;AAAA,IACzB;AACA,eAAW,CAAC;AAAA,EACd;AACF;AAnCS;;;ACFT,SAAS,aAAa,SAASC,OAAM,QAAQ;AAC3C,MAAI,OAAO;AACX,QAAM,QAAQ,QAAQ,QAAQ,MAAMA,IAAG;AACvC,SAAO,SAAS,GAAG;AACjB,UAAM,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACxC,UAAM,MAAM,QAAQ,QAAQ,OAAOA,IAAG;AACtC,QAAI,UAAU,MAAM,QAAQ,IAAI;AAC9B;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,QAAQ,KAAK,GAAG;AACvC,QAAI,WAAW,IAAI;AACjB;AAAA,IACF;AACA,YAAQ,QAAQ,MAAM,QAAQ,GAAG,GAAG,EAAE,KAAK;AAC3C,cAAU,QAAQ,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,QAAQ,MAAM,SAAS,CAAC;AAAA,EACrE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AApBS;AAqBT,SAAS,oBAAoB,MAAM,SAAS;AAC1C,SAAO,OAAO,WAAW,OAAO,YAAY,UAAU;AACxD;AAFS;AAGT,SAAS,eAAe,MAAM,OAAO,KAAK;AACxC,QAAM,QAAQ,aAAa,IAAI;AAC/B,SAAO,oBAAoB,MAAM,MAAM,QAAQ,MAAM,UAAU,GAAG;AACpE;AAHS;;;ACnBT,IAAM,iBAAiB,wBAAC,UAAU,UAAU,WAAW,UAAU,eAAe,UAAU,QAAnE;AACvB,SAAS,UAAU,MAAM,gBAAgB;AACvC,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,qBAAqB;AAAA,IACzB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,MAAM;AAAA,IACV,MAAM,SAAS;AAAA,IACf,KAAK,SAAS;AAAA,IACd,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,EACnB;AACA,MAAI,OAAO,SAAS;AACpB,GAAC,UAAU,kBAAkB,EAAE,QAAQ,CAAC,UAAU;AAChD,UAAM,kBAAkB,CAAC;AACzB,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,MAAM;AACpB,QAAI,WAAW,MAAM;AACrB,QAAI,OAAO;AACT,UAAI,OAAO;AACT,oBAAY;AAAA,MACd,OAAO;AACL,wBAAgB;AAAA,UACd,gBAAgB,IAAI,QAAQ,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACtF;AACA,wBAAgB,KAAK,aAAa;AAClC,YAAI,MAAM,IAAI,OAAO;AAAA,MACvB;AAAA,IACF,WAAW,OAAO;AAChB,sBAAgB;AAAA,QACd,gBAAgB,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI;AAAA,MACvF;AACA,sBAAgB,KAAK,aAAa;AAClC,UAAI,MAAM,IAAI,OAAO;AAAA,IACvB;AACA,QAAI;AACJ,QAAI,WAAW,GAAG;AAChB,kBAAY,KAAK,MAAM,WAAW,CAAC,IAAI;AAAA,IACzC;AACA,eAAW,WAAW;AACtB,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,oBAAY,IAAI,SAAS,IAAI,IAAI;AACjC,wBAAgB;AAAA,UACd,eAAe,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACrE;AACA;AAAA,MACF,KAAK;AACH,wBAAgB;AAAA,UACd,iBAAiB,IAAI,QAAQ,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACxG;AACA;AAAA,MACF,KAAK;AACH,oBAAY,IAAI,QAAQ,IAAI,IAAI;AAChC,wBAAgB;AAAA,UACd,gBAAgB,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACtE;AACA;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,GAAG;AACtB,UAAI,IAAI,SAAS,IAAI,KAAK;AACxB,oBAAY,IAAI;AAChB,YAAI,OAAO,IAAI;AACf,YAAI,MAAM;AAAA,MACZ;AACA,UAAI,IAAI,UAAU,IAAI,QAAQ;AAC5B,oBAAY,IAAI;AAChB,YAAI,QAAQ,IAAI;AAChB,YAAI,SAAS;AAAA,MACf;AAAA,IACF;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO;AAAA,QACL;AAAA,QACA,mBAAmB,gBAAgB,KAAK,GAAG,IAAI;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,mBAAmB;AAC/C,QAAM,uBAAuB,mBAAmB;AAChD,QAAM,WAAW,IAAI;AACrB,QAAM,YAAY,IAAI;AACtB,MAAI;AACJ,MAAI;AACJ,MAAI,wBAAwB,MAAM;AAChC,aAAS,yBAAyB,OAAO,QAAQ,yBAAyB,SAAS,YAAY;AAC/F,YAAQ,cAAc,QAAQ,WAAW,SAAS;AAAA,EACpD,OAAO;AACL,YAAQ,wBAAwB,SAAS,WAAW;AACpD,aAAS,yBAAyB,OAAO,cAAc,OAAO,YAAY,QAAQ,IAAI,yBAAyB,SAAS,YAAY;AAAA,EACtI;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,wBAAC,MAAM,UAAU;AAC/B,QAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,iBAAW,IAAI,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,EACF,GAJgB;AAKhB,UAAQ,SAAS,KAAK;AACtB,UAAQ,UAAU,MAAM;AACxB,QAAM,UAAU,CAAC,IAAI,MAAM,IAAI,KAAK,UAAU,SAAS;AACvD,aAAW,UAAU,QAAQ,KAAK,GAAG;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AA9GS;;;ACNT,IAAM,QAAQ;AACd,IAAM,eAAe,cAAc,KAAK,IAAI,EAAE,SAAS,EAAE,KAAK,KAAK,OAAO,IAAI,WAAW,GAAG,SAAS,EAAE;AACvG,IAAI,UAAU;AACd,SAAS,WAAW,MAAM,SAAS,cAAc;AAC/C,QAAM,MAAM,CAAC;AACb,MAAI;AACJ,SAAO,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC/B,QAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EACnB;AACA,MAAI,CAAC,IAAI,QAAQ;AACf,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,KAAK,OAAO,IAAI,WAAW,KAAK,IAAI,GAAG,SAAS,EAAE;AAC7E,MAAI,QAAQ,CAAC,OAAO;AAClB,UAAM,QAAQ,OAAO,WAAW,aAAa,OAAO,EAAE,IAAI,UAAU,WAAW,SAAS;AACxF,UAAM,YAAY,GAAG,QAAQ,uBAAuB,MAAM;AAC1D,WAAO,KAAK;AAAA;AAAA;AAAA,MAGV,IAAI,OAAO,aAAa,YAAY,oBAAoB,GAAG;AAAA,MAC3D,OAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,SAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG,EAAE;AAC/C,SAAO;AACT;AAtBS;;;ACHT,SAAS,WAAW,MAAM,YAAY;AACpC,MAAI,oBAAoB,KAAK,QAAQ,QAAQ,MAAM,KAAK,KAAK;AAC7D,aAAW,QAAQ,YAAY;AAC7B,yBAAqB,MAAM,OAAO,OAAO,WAAW,IAAI,IAAI;AAAA,EAC9D;AACA,SAAO,4CAA4C,oBAAoB,MAAM,OAAO;AACtF;AANS;;;ACqDT,mBAAO;;;ACpCA,IAAM,cAA2B;AAAA,EACtC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,aAAa,oBAAI,IAAyB;AAChD,IAAM,cAAc,oBAAI,IAAuC;AAExD,IAAM,oBAAoB,wBAAC,gBAA8B;AAC9D,aAAW,cAAc,aAAa;AACpC,QAAI,CAAC,WAAW,MAAM;AACpB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,0BAA0B,WAAW,IAAI;AACnD,QAAI,YAAY,YAAY;AAC1B,kBAAY,IAAI,WAAW,MAAM,WAAW,MAAM;AAAA,IACpD,WAAW,WAAW,YAAY;AAChC,iBAAW,IAAI,WAAW,MAAM,WAAW,KAAK;AAAA,IAClD,OAAO;AACL,UAAI,MAAM,wBAAwB,UAAU;AAC5C,YAAM,IAAI,MAAM,qEAAqE;AAAA,IACvF;AAAA,EACF;AACF,GAjBiC;AAmBjC,IAAM,wBAAwB,8BAAO,UAAkB,mBAA4B;AACjF,QAAM,OAAO,aAAa,UAAU,MAAM,mBAAmB,MAAS;AACtE,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,sBAAsB,QAAQ,EAAE;AAAA,EAClD;AACA,QAAM,SAAS,KAAK,UAAU;AAC9B,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,oCAAoC,QAAQ,EAAE;AAAA,EAChE;AACA,MAAI,QAAQ,WAAW,IAAI,MAAM;AACjC,MAAI,CAAC,OAAO;AACV,UAAM,SAAS,YAAY,IAAI,MAAM;AACrC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,uBAAuB,KAAK,MAAM,EAAE;AAAA,IACtD;AACA,QAAI;AACF,YAAM,SAAS,MAAM,OAAO;AAC5B,cAAQ,EAAE,GAAG,QAAQ,OAAO;AAC5B,iBAAW,IAAI,QAAQ,KAAK;AAAA,IAC9B,SAAS,GAAG;AACV,UAAI,MAAM,CAAC;AACX,YAAM,IAAI,MAAM,4BAA4B,KAAK,MAAM,EAAE;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,WAAW,YAAY,OAAO,KAAK,IAAI;AAC7C,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,mBAAmB,QAAQ,EAAE;AAAA,EAC/C;AACA,SAAO;AACT,GA7B8B;AA+BvB,IAAM,kBAAkB,8BAAO,aAAqB;AACzD,MAAI;AACF,UAAM,sBAAsB,QAAQ;AACpC,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF,GAP+B;AASxB,IAAM,aAAa,8BACxB,UACA,gBACA,oBACG;AACH,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,sBAAsB,UAAU,gBAAgB,cAAc;AAAA,EACjF,SAAS,GAAG;AACV,QAAI,MAAM,CAAC;AACX,eAAW;AAAA,EACb;AACA,QAAM,aAAa,UAAU,UAAU,cAAc;AACrD,QAAM,MAAM,WAAW,WAAW,WAAW,IAAI,GAAG;AAAA,IAClD,GAAG,WAAW;AAAA,IACd,GAAG;AAAA,EACL,CAAC;AACD,SAAO;AACT,GAlB0B;;;ACrFpB,SAAU,OACd,OAAoC;AACpC,MAAA,SAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAoB;AAApB,WAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAEA,MAAI,UAAU,MAAM,KAAK,OAAO,UAAU,WAAW,CAAC,KAAK,IAAI,KAAK;AAGpE,UAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,QAAQ,SAAS,CAAC,EAAE,QACxD,kBACA,EAAE;AAIJ,MAAM,gBAAgB,QAAQ,OAAO,SAAC,KAAK,KAAG;AAC5C,QAAM,UAAU,IAAI,MAAM,qBAAqB;AAC/C,QAAI,SAAS;AACX,aAAO,IAAI,OACT,QAAQ,IAAI,SAAC,OAAK;AAAA,YAAA,IAAA;AAAK,gBAAA,MAAA,KAAA,MAAM,MAAM,QAAQ,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,KAAI;MAAC,CAAA,CAAC;;AAG9D,WAAO;EACT,GAAa,CAAA,CAAE;AAGf,MAAI,cAAc,QAAQ;AACxB,QAAM,YAAU,IAAI,OAAO,YAAW,KAAK,IAAG,MAAR,MAAY,aAAa,IAAA,KAAM,GAAG;AAExE,cAAU,QAAQ,IAAI,SAAC,KAAG;AAAK,aAAA,IAAI,QAAQ,WAAS,IAAI;IAAzB,CAA0B;;AAI3D,UAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE;AAG5C,MAAI,SAAS,QAAQ,CAAC;AAEtB,SAAO,QAAQ,SAAC,OAAO,GAAC;AAEtB,QAAM,eAAe,OAAO,MAAM,eAAe;AACjD,QAAM,cAAc,eAAe,aAAa,CAAC,IAAI;AACrD,QAAI,gBAAgB;AAEpB,QAAI,OAAO,UAAU,YAAY,MAAM,SAAS,IAAI,GAAG;AACrD,sBAAgB,OAAO,KAAK,EACzB,MAAM,IAAI,EACV,IAAI,SAAC,KAAKC,IAAC;AACV,eAAOA,OAAM,IAAI,MAAM,KAAG,cAAc;MAC1C,CAAC,EACA,KAAK,IAAI;;AAGd,cAAU,gBAAgB,QAAQ,IAAI,CAAC;EACzC,CAAC;AAED,SAAO;AACT;AAvDgB;;;ACGT,SAAS,eAAe;AAC3B,SAAO;IACH,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,KAAK;IACL,OAAO;IACP,UAAU;IACV,UAAU;IACV,QAAQ;IACR,WAAW;IACX,YAAY;EACpB;AACA;AAbgB;AAcN,IAAC,YAAY,aAAY;AAC5B,SAAS,eAAe,aAAa;AACxC,cAAY;AAChB;AAFgB;AClBhB,IAAM,WAAW,EAAE,MAAM,6BAAM,MAAN,QAAU;AACnC,SAAS,KAAKC,QAAO,MAAM,IAAI;AAC3B,MAAI,SAAS,OAAOA,WAAU,WAAWA,SAAQA,OAAM;AACvD,QAAM,MAAM;IACR,SAAS,wBAAC,MAAM,QAAQ;AACpB,UAAI,YAAY,OAAO,QAAQ,WAAW,MAAM,IAAI;AACpD,kBAAY,UAAU,QAAQ,MAAM,OAAO,IAAI;AAC/C,eAAS,OAAO,QAAQ,MAAM,SAAS;AACvC,aAAO;IACnB,GALiB;IAMT,UAAU,6BAAM;AACZ,aAAO,IAAI,OAAO,QAAQ,GAAG;IACzC,GAFkB;EAGlB;AACI,SAAO;AACX;AAdS;AAeF,IAAM,QAAQ;EACjB,kBAAkB;EAClB,mBAAmB;EACnB,wBAAwB;EACxB,gBAAgB;EAChB,YAAY;EACZ,mBAAmB;EACnB,iBAAiB;EACjB,cAAc;EACd,mBAAmB;EACnB,eAAe;EACf,qBAAqB;EACrB,WAAW;EACX,iBAAiB;EACjB,iBAAiB;EACjB,yBAAyB;EACzB,0BAA0B;EAC1B,iBAAiB;EACjB,oBAAoB;EACpB,YAAY;EACZ,iBAAiB;EACjB,SAAS;EACT,cAAc;EACd,gBAAgB;EAChB,iBAAiB;EACjB,mBAAmB;EACnB,iBAAiB;EACjB,kBAAkB;EAClB,gBAAgB;EAChB,WAAW;EACX,SAAS;EACT,mBAAmB;EACnB,iBAAiB;EACjB,mBAAmB;EACnB,iBAAiB;EACjB,mBAAmB;EACnB,qBAAqB;EACrB,YAAY;EACZ,eAAe;EACf,oBAAoB;EACpB,uBAAuB;EACvB,cAAc;EACd,OAAO;EACP,eAAe;EACf,UAAU;EACV,WAAW;EACX,WAAW;EACX,gBAAgB;EAChB,WAAW;EACX,eAAe;EACf,eAAe;EACf,eAAe,wBAAC,SAAS,IAAI,OAAO,WAAW,IAAI,8BAA+B,GAAnE;EACf,iBAAiB,wBAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAqD,GAA3G;EACjB,SAAS,wBAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD,GAA1G;EACT,kBAAkB,wBAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,iBAAiB,GAAvE;EAClB,mBAAmB,wBAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI,GAA1D;EACnB,gBAAgB,wBAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,sBAAsB,GAAG,GAA/E;AACpB;AAIA,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,SAAS;AACf,IAAM,KAAK;AACX,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,WAAW,KAAK,YAAY,EAC7B,QAAQ,SAAS,MAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,YAAY,EAAE,EACtB,SAAQ;AACb,IAAM,cAAc,KAAK,YAAY,EAChC,QAAQ,SAAS,MAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,UAAU,mCAAmC,EACrD,SAAQ;AACb,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,MAAM,KAAK,6GAA6G,EACzH,QAAQ,SAAS,WAAW,EAC5B,QAAQ,SAAS,8DAA8D,EAC/E,SAAQ;AACb,IAAM,OAAO,KAAK,sCAAsC,EACnD,QAAQ,SAAS,MAAM,EACvB,SAAQ;AACb,IAAM,OAAO;AAMb,IAAM,WAAW;AACjB,IAAM,OAAO,KAAK,6dASP,GAAG,EACT,QAAQ,WAAW,QAAQ,EAC3B,QAAQ,OAAO,IAAI,EACnB,QAAQ,aAAa,0EAA0E,EAC/F,SAAQ;AACb,IAAM,YAAY,KAAK,UAAU,EAC5B,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAQ;AACb,IAAM,aAAa,KAAK,yCAAyC,EAC5D,QAAQ,aAAa,SAAS,EAC9B,SAAQ;AAIb,IAAM,cAAc;EAChB;EACA,MAAM;EACN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO;EACP,MAAM;AACV;AAIA,IAAM,WAAW,KAAK,6JAEsE,EACvF,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,cAAc,SAAS,EAC/B,QAAQ,QAAQ,wBAAyB,EACzC,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAQ;AACb,IAAM,WAAW;EACb,GAAG;EACH,UAAU;EACV,OAAO;EACP,WAAW,KAAK,UAAU,EACrB,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,SAAS,QAAQ,EACzB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAQ;AACjB;AAIA,IAAM,gBAAgB;EAClB,GAAG;EACH,MAAM,KAAK,wIAEiE,EACvE,QAAQ,WAAW,QAAQ,EAC3B,QAAQ,QAAQ,mKAGgB,EAChC,SAAQ;EACb,KAAK;EACL,SAAS;EACT,QAAQ;;EACR,UAAU;EACV,WAAW,KAAK,UAAU,EACrB,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,iBAAiB,EACpC,QAAQ,YAAY,QAAQ,EAC5B,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,WAAW,EAAE,EACrB,QAAQ,SAAS,EAAE,EACnB,QAAQ,SAAS,EAAE,EACnB,QAAQ,QAAQ,EAAE,EAClB,SAAQ;AACjB;AAIA,IAAMC,WAAS;AACf,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,aAAa;AAEnB,IAAM,eAAe;AACrB,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,cAAc,KAAK,yBAAyB,GAAG,EAChD,QAAQ,eAAe,mBAAmB,EAAE,SAAQ;AAEzD,IAAM,0BAA0B;AAChC,IAAM,iCAAiC;AACvC,IAAM,oCAAoC;AAE1C,IAAM,YAAY;AAClB,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB,KAAK,oBAAoB,GAAG,EAC9C,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,oBAAoB,KAAK,oBAAoB,GAAG,EACjD,QAAQ,UAAU,uBAAuB,EACzC,SAAQ;AACb,IAAM,wBAAwB;AAQ9B,IAAM,oBAAoB,KAAK,uBAAuB,IAAI,EACrD,QAAQ,kBAAkB,sBAAsB,EAChD,QAAQ,eAAe,mBAAmB,EAC1C,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,uBAAuB,KAAK,uBAAuB,IAAI,EACxD,QAAQ,kBAAkB,iCAAiC,EAC3D,QAAQ,eAAe,8BAA8B,EACrD,QAAQ,UAAU,uBAAuB,EACzC,SAAQ;AAEb,IAAM,oBAAoB,KAAK,oNAMQ,IAAI,EACtC,QAAQ,kBAAkB,sBAAsB,EAChD,QAAQ,eAAe,mBAAmB,EAC1C,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,iBAAiB,KAAK,aAAa,IAAI,EACxC,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,WAAW,KAAK,qCAAqC,EACtD,QAAQ,UAAU,8BAA8B,EAChD,QAAQ,SAAS,8IAA8I,EAC/J,SAAQ;AACb,IAAM,iBAAiB,KAAK,QAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAQ;AAC1E,IAAM,MAAM,KAAK,0JAKuB,EACnC,QAAQ,WAAW,cAAc,EACjC,QAAQ,aAAa,6EAA6E,EAClG,SAAQ;AACb,IAAM,eAAe;AACrB,IAAM,OAAO,KAAK,+CAA+C,EAC5D,QAAQ,SAAS,YAAY,EAC7B,QAAQ,QAAQ,sCAAsC,EACtD,QAAQ,SAAS,6DAA6D,EAC9E,SAAQ;AACb,IAAM,UAAU,KAAK,yBAAyB,EACzC,QAAQ,SAAS,YAAY,EAC7B,QAAQ,OAAO,WAAW,EAC1B,SAAQ;AACb,IAAM,SAAS,KAAK,uBAAuB,EACtC,QAAQ,OAAO,WAAW,EAC1B,SAAQ;AACb,IAAM,gBAAgB,KAAK,yBAAyB,GAAG,EAClD,QAAQ,WAAW,OAAO,EAC1B,QAAQ,UAAU,MAAM,EACxB,SAAQ;AAIb,IAAM,eAAe;EACjB,YAAY;;EACZ;EACA;EACA;EACA;EACA,MAAM;EACN,KAAK;EACL;EACA;EACA;EACJ,QAAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM;EACN,KAAK;AACT;AAIA,IAAM,iBAAiB;EACnB,GAAG;EACH,MAAM,KAAK,yBAAyB,EAC/B,QAAQ,SAAS,YAAY,EAC7B,SAAQ;EACb,SAAS,KAAK,+BAA+B,EACxC,QAAQ,SAAS,YAAY,EAC7B,SAAQ;AACjB;AAIA,IAAM,YAAY;EACd,GAAG;EACH,mBAAmB;EACnB,gBAAgB;EAChB,KAAK,KAAK,oEAAoE,GAAG,EAC5E,QAAQ,SAAS,2EAA2E,EAC5F,SAAQ;EACb,YAAY;EACZ,KAAK;EACL,MAAM;AACV;AAIA,IAAM,eAAe;EACjB,GAAG;EACH,IAAI,KAAK,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAQ;EAC1C,MAAM,KAAK,UAAU,IAAI,EACpB,QAAQ,QAAQ,eAAe,EAC/B,QAAQ,WAAW,GAAG,EACtB,SAAQ;AACjB;AAIO,IAAM,QAAQ;EACjB,QAAQ;EACR,KAAK;EACL,UAAU;AACd;AACO,IAAM,SAAS;EAClB,QAAQ;EACR,KAAK;EACL,QAAQ;EACR,UAAU;AACd;AClYA,IAAM,qBAAqB;EACvB,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACT;AACA,IAAM,uBAAuB,wBAAC,OAAO,mBAAmB,EAAE,GAA7B;AACtB,SAAS,OAAOC,OAAM,QAAQ;AACjC,MAAI,QAAQ;AACR,QAAI,MAAM,WAAW,KAAKA,KAAI,GAAG;AAC7B,aAAOA,MAAK,QAAQ,MAAM,eAAe,oBAAoB;IACzE;EACA,OACS;AACD,QAAI,MAAM,mBAAmB,KAAKA,KAAI,GAAG;AACrC,aAAOA,MAAK,QAAQ,MAAM,uBAAuB,oBAAoB;IACjF;EACA;AACI,SAAOA;AACX;AAZgB;AA2BT,SAAS,SAAS,MAAM;AAC3B,MAAI;AACA,WAAO,UAAU,IAAI,EAAE,QAAQ,MAAM,eAAe,GAAG;EAC/D,QACU;AACF,WAAO;EACf;AACI,SAAO;AACX;AARgB;AAST,SAAS,WAAW,UAAU,OAAO;AAGxC,QAAM,MAAM,SAAS,QAAQ,MAAM,UAAU,CAAC,OAAO,QAAQ,QAAQ;AACjE,QAAI,UAAU;AACd,QAAI,OAAO;AACX,WAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM;AAChC,gBAAU,CAAC;AACf,QAAI,SAAS;AAGT,aAAO;IACnB,OACa;AAED,aAAO;IACnB;EACA,CAAK,GAAG,QAAQ,IAAI,MAAM,MAAM,SAAS;AACrC,MAAI,IAAI;AAER,MAAI,CAAC,MAAM,CAAC,EAAE,KAAI,GAAI;AAClB,UAAM,MAAK;EACnB;AACI,MAAI,MAAM,SAAS,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,KAAI,GAAI;AAC3C,UAAM,IAAG;EACjB;AACI,MAAI,OAAO;AACP,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,OAAO,KAAK;IAC9B,OACa;AACD,aAAO,MAAM,SAAS;AAClB,cAAM,KAAK,EAAE;IAC7B;EACA;AACI,SAAO,IAAI,MAAM,QAAQ,KAAK;AAE1B,UAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAI,EAAG,QAAQ,MAAM,WAAW,GAAG;EAC/D;AACI,SAAO;AACX;AAxCgB;AAiDT,SAAS,MAAM,KAAK,GAAG,QAAQ;AAClC,QAAM,IAAI,IAAI;AACd,MAAI,MAAM,GAAG;AACT,WAAO;EACf;AAEI,MAAI,UAAU;AAEd,SAAO,UAAU,GAAG;AAChB,UAAM,WAAW,IAAI,OAAO,IAAI,UAAU,CAAC;AAC3C,QAAI,aAAa,KAAK,MAAS;AAC3B;IACZ,OAIa;AACD;IACZ;EACA;AACI,SAAO,IAAI,MAAM,GAAG,IAAI,OAAO;AACnC;AArBgB;AAsBT,SAAS,mBAAmB,KAAK,GAAG;AACvC,MAAI,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,IAAI;AAC1B,WAAO;EACf;AACI,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,IAAI,CAAC,MAAM,MAAM;AACjB;IACZ,WACiB,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AACtB;IACZ,WACiB,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AACtB;AACA,UAAI,QAAQ,GAAG;AACX,eAAO;MACvB;IACA;EACA;AACI,SAAO;AACX;AApBgB;ACrHhB,SAAS,WAAW,KAAKC,OAAM,KAAKC,QAAO,OAAO;AAC9C,QAAM,OAAOD,MAAK;AAClB,QAAM,QAAQA,MAAK,SAAS;AAC5B,QAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,MAAM,MAAM,mBAAmB,IAAI;AAC/D,MAAI,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK;AAC1B,IAAAC,OAAM,MAAM,SAAS;AACrB,UAAM,QAAQ;MACV,MAAM;MACN;MACA;MACA;MACA;MACA,QAAQA,OAAM,aAAa,IAAI;IAC3C;AACQ,IAAAA,OAAM,MAAM,SAAS;AACrB,WAAO;EACf;AACI,SAAO;IACH,MAAM;IACN;IACA;IACA;IACA;EACR;AACA;AAxBS;AAyBT,SAAS,uBAAuB,KAAK,MAAM,OAAO;AAC9C,QAAM,oBAAoB,IAAI,MAAM,MAAM,MAAM,sBAAsB;AACtE,MAAI,sBAAsB,MAAM;AAC5B,WAAO;EACf;AACI,QAAM,eAAe,kBAAkB,CAAC;AACxC,SAAO,KACF,MAAM,IAAI,EACV,IAAI,UAAQ;AACb,UAAM,oBAAoB,KAAK,MAAM,MAAM,MAAM,cAAc;AAC/D,QAAI,sBAAsB,MAAM;AAC5B,aAAO;IACnB;AACQ,UAAM,CAAC,YAAY,IAAI;AACvB,QAAI,aAAa,UAAU,aAAa,QAAQ;AAC5C,aAAO,KAAK,MAAM,aAAa,MAAM;IACjD;AACQ,WAAO;EACf,CAAK,EACI,KAAK,IAAI;AAClB;AApBS;AAwBF,IAAM,aAAN,MAAiB;SAAA;;;EACpB;EACA;;EACA;;EACA,YAAYC,UAAS;AACjB,SAAK,UAAUA,YAAW;EAClC;EACI,MAAM,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,IAAI,CAAC,EAAE,SAAS,GAAG;AAC1B,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;MAC1B;IACA;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAkB,EAAE;AACjE,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,gBAAgB;QAChB,MAAM,CAAC,KAAK,QAAQ,WACd,MAAM,MAAM,IAAI,IAChB;MACtB;IACA;EACA;EACI,OAAO,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC5C,QAAI,KAAK;AACL,YAAM,MAAM,IAAI,CAAC;AACjB,YAAM,OAAO,uBAAuB,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK;AACjE,aAAO;QACH,MAAM;QACN;QACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAI,EAAG,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;QACpF;MAChB;IACA;EACA;EACI,QAAQ,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,KAAI;AAEtB,UAAI,KAAK,MAAM,MAAM,WAAW,KAAK,IAAI,GAAG;AACxC,cAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,YAAI,KAAK,QAAQ,UAAU;AACvB,iBAAO,QAAQ,KAAI;QACvC,WACyB,CAAC,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,OAAO,GAAG;AAEjE,iBAAO,QAAQ,KAAI;QACvC;MACA;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,OAAO,IAAI,CAAC,EAAE;QACd;QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;MAC9C;IACA;EACA;EACI,GAAG,KAAK;AACJ,UAAM,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;AACxC,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,MAAM,IAAI,CAAC,GAAG,IAAI;MACvC;IACA;EACA;EACI,WAAW,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,MAAM,WAAW,KAAK,GAAG;AAChD,QAAI,KAAK;AACL,UAAI,QAAQ,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,IAAI;AAC1C,UAAI,MAAM;AACV,UAAI,OAAO;AACX,YAAM,SAAS,CAAA;AACf,aAAO,MAAM,SAAS,GAAG;AACrB,YAAI,eAAe;AACnB,cAAM,eAAe,CAAA;AACrB,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAE/B,cAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,GAAG;AACjD,yBAAa,KAAK,MAAM,CAAC,CAAC;AAC1B,2BAAe;UACvC,WAC6B,CAAC,cAAc;AACpB,yBAAa,KAAK,MAAM,CAAC,CAAC;UAClD,OACyB;AACD;UACxB;QACA;AACgB,gBAAQ,MAAM,MAAM,CAAC;AACrB,cAAM,aAAa,aAAa,KAAK,IAAI;AACzC,cAAM,cAAc,WAEf,QAAQ,KAAK,MAAM,MAAM,yBAAyB,UAAU,EAC5D,QAAQ,KAAK,MAAM,MAAM,0BAA0B,EAAE;AAC1D,cAAM,MAAM,GAAG,GAAG;EAAK,UAAU,KAAK;AACtC,eAAO,OAAO,GAAG,IAAI;EAAK,WAAW,KAAK;AAG1C,cAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,aAAK,MAAM,MAAM,MAAM;AACvB,aAAK,MAAM,YAAY,aAAa,QAAQ,IAAI;AAChD,aAAK,MAAM,MAAM,MAAM;AAEvB,YAAI,MAAM,WAAW,GAAG;AACpB;QACpB;AACgB,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAE5B;QACpB,WACyB,WAAW,SAAS,cAAc;AAEvC,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,WAAW,OAAO;AACxC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACpE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,KAAK,MAAM,IAAI,SAAS;AACxE;QACpB,WACyB,WAAW,SAAS,QAAQ;AAEjC,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,KAAK,OAAO;AAClC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,UAAU,IAAI,MAAM,IAAI,SAAS;AACrE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACvE,kBAAQ,QAAQ,UAAU,OAAO,GAAG,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,IAAI;AAC9D;QACpB;MACA;AACY,aAAO;QACH,MAAM;QACN;QACA;QACA;MAChB;IACA;EACA;EACI,KAAK,KAAK;AACN,QAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AACxC,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,KAAI;AACtB,YAAM,YAAY,KAAK,SAAS;AAChC,YAAMC,QAAO;QACT,MAAM;QACN,KAAK;QACL,SAAS;QACT,OAAO,YAAY,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;QACxC,OAAO;QACP,OAAO,CAAA;MACvB;AACY,aAAO,YAAY,aAAa,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI;AAC5D,UAAI,KAAK,QAAQ,UAAU;AACvB,eAAO,YAAY,OAAO;MAC1C;AAEY,YAAM,YAAY,KAAK,MAAM,MAAM,cAAc,IAAI;AACrD,UAAI,oBAAoB;AAExB,aAAO,KAAK;AACR,YAAI,WAAW;AACf,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,YAAI,EAAE,MAAM,UAAU,KAAK,GAAG,IAAI;AAC9B;QACpB;AACgB,YAAI,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;AAC/B;QACpB;AACgB,cAAM,IAAI,CAAC;AACX,cAAM,IAAI,UAAU,IAAI,MAAM;AAC9B,YAAI,OAAO,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,CAAC,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC;AAC7G,YAAI,WAAW,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACnC,YAAI,YAAY,CAAC,KAAK,KAAI;AAC1B,YAAI,SAAS;AACb,YAAI,KAAK,QAAQ,UAAU;AACvB,mBAAS;AACT,yBAAe,KAAK,UAAS;QACjD,WACyB,WAAW;AAChB,mBAAS,IAAI,CAAC,EAAE,SAAS;QAC7C,OACqB;AACD,mBAAS,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY;AACpD,mBAAS,SAAS,IAAI,IAAI;AAC1B,yBAAe,KAAK,MAAM,MAAM;AAChC,oBAAU,IAAI,CAAC,EAAE;QACrC;AACgB,YAAI,aAAa,KAAK,MAAM,MAAM,UAAU,KAAK,QAAQ,GAAG;AACxD,iBAAO,WAAW;AAClB,gBAAM,IAAI,UAAU,SAAS,SAAS,CAAC;AACvC,qBAAW;QAC/B;AACgB,YAAI,CAAC,UAAU;AACX,gBAAM,kBAAkB,KAAK,MAAM,MAAM,gBAAgB,MAAM;AAC/D,gBAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/C,gBAAM,mBAAmB,KAAK,MAAM,MAAM,iBAAiB,MAAM;AACjE,gBAAM,oBAAoB,KAAK,MAAM,MAAM,kBAAkB,MAAM;AACnE,gBAAM,iBAAiB,KAAK,MAAM,MAAM,eAAe,MAAM;AAE7D,iBAAO,KAAK;AACR,kBAAM,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACpC,gBAAI;AACJ,uBAAW;AAEX,gBAAI,KAAK,QAAQ,UAAU;AACvB,yBAAW,SAAS,QAAQ,KAAK,MAAM,MAAM,oBAAoB,IAAI;AACrE,oCAAsB;YAClD,OAC6B;AACD,oCAAsB,SAAS,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM;YACzG;AAEwB,gBAAI,iBAAiB,KAAK,QAAQ,GAAG;AACjC;YAC5B;AAEwB,gBAAI,kBAAkB,KAAK,QAAQ,GAAG;AAClC;YAC5B;AAEwB,gBAAI,eAAe,KAAK,QAAQ,GAAG;AAC/B;YAC5B;AAEwB,gBAAI,gBAAgB,KAAK,QAAQ,GAAG;AAChC;YAC5B;AAEwB,gBAAI,QAAQ,KAAK,QAAQ,GAAG;AACxB;YAC5B;AACwB,gBAAI,oBAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,UAAU,CAAC,SAAS,KAAI,GAAI;AACzF,8BAAgB,OAAO,oBAAoB,MAAM,MAAM;YACnF,OAC6B;AAED,kBAAI,WAAW;AACX;cAChC;AAE4B,kBAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AACjG;cAChC;AAC4B,kBAAI,iBAAiB,KAAK,IAAI,GAAG;AAC7B;cAChC;AAC4B,kBAAI,kBAAkB,KAAK,IAAI,GAAG;AAC9B;cAChC;AAC4B,kBAAI,QAAQ,KAAK,IAAI,GAAG;AACpB;cAChC;AAC4B,8BAAgB,OAAO;YACnD;AACwB,gBAAI,CAAC,aAAa,CAAC,SAAS,KAAI,GAAI;AAChC,0BAAY;YACxC;AACwB,mBAAO,UAAU;AACjB,kBAAM,IAAI,UAAU,QAAQ,SAAS,CAAC;AACtC,mBAAO,oBAAoB,MAAM,MAAM;UAC/D;QACA;AACgB,YAAI,CAACA,MAAK,OAAO;AAEb,cAAI,mBAAmB;AACnB,YAAAA,MAAK,QAAQ;UACrC,WAC6B,KAAK,MAAM,MAAM,gBAAgB,KAAK,GAAG,GAAG;AACjD,gCAAoB;UAC5C;QACA;AACgB,YAAI,SAAS;AACb,YAAI;AAEJ,YAAI,KAAK,QAAQ,KAAK;AAClB,mBAAS,KAAK,MAAM,MAAM,WAAW,KAAK,YAAY;AACtD,cAAI,QAAQ;AACR,wBAAY,OAAO,CAAC,MAAM;AAC1B,2BAAe,aAAa,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE;UAChG;QACA;AACgB,QAAAA,MAAK,MAAM,KAAK;UACZ,MAAM;UACN;UACA,MAAM,CAAC,CAAC;UACR,SAAS;UACT,OAAO;UACP,MAAM;UACN,QAAQ,CAAA;QAC5B,CAAiB;AACD,QAAAA,MAAK,OAAO;MAC5B;AAEY,YAAM,WAAWA,MAAK,MAAM,GAAG,EAAE;AACjC,UAAI,UAAU;AACV,iBAAS,MAAM,SAAS,IAAI,QAAO;AACnC,iBAAS,OAAO,SAAS,KAAK,QAAO;MACrD,OACiB;AAED;MAChB;AACY,MAAAA,MAAK,MAAMA,MAAK,IAAI,QAAO;AAE3B,eAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AACxC,aAAK,MAAM,MAAM,MAAM;AACvB,QAAAA,MAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,YAAYA,MAAK,MAAM,CAAC,EAAE,MAAM,CAAA,CAAE;AACpE,YAAI,CAACA,MAAK,OAAO;AAEb,gBAAM,UAAUA,MAAK,MAAM,CAAC,EAAE,OAAO,OAAO,OAAK,EAAE,SAAS,OAAO;AACnE,gBAAM,wBAAwB,QAAQ,SAAS,KAAK,QAAQ,KAAK,OAAK,KAAK,MAAM,MAAM,QAAQ,KAAK,EAAE,GAAG,CAAC;AAC1G,UAAAA,MAAK,QAAQ;QACjC;MACA;AAEY,UAAIA,MAAK,OAAO;AACZ,iBAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AACxC,UAAAA,MAAK,MAAM,CAAC,EAAE,QAAQ;QAC1C;MACA;AACY,aAAOA;IACnB;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,YAAM,QAAQ;QACV,MAAM;QACN,OAAO;QACP,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM;QAC3D,MAAM,IAAI,CAAC;MAC3B;AACY,aAAO;IACnB;EACA;EACI,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG;AACzC,QAAI,KAAK;AACL,YAAMC,OAAM,IAAI,CAAC,EAAE,YAAW,EAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AAClF,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,cAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAC5H,YAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AACrH,aAAO;QACH,MAAM;QACN,KAAAA;QACA,KAAK,IAAI,CAAC;QACV;QACA;MAChB;IACA;EACA;EACI,MAAM,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC3C,QAAI,CAAC,KAAK;AACN;IACZ;AACQ,QAAI,CAAC,KAAK,MAAM,MAAM,eAAe,KAAK,IAAI,CAAC,CAAC,GAAG;AAE/C;IACZ;AACQ,UAAM,UAAU,WAAW,IAAI,CAAC,CAAC;AACjC,UAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,EAAE,MAAM,GAAG;AAC7E,UAAM,OAAO,IAAI,CAAC,GAAG,KAAI,IAAK,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,EAAE,EAAE,MAAM,IAAI,IAAI,CAAA;AACnG,UAAM,OAAO;MACT,MAAM;MACN,KAAK,IAAI,CAAC;MACV,QAAQ,CAAA;MACR,OAAO,CAAA;MACP,MAAM,CAAA;IAClB;AACQ,QAAI,QAAQ,WAAW,OAAO,QAAQ;AAElC;IACZ;AACQ,eAAW,SAAS,QAAQ;AACxB,UAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,KAAK,GAAG;AAC9C,aAAK,MAAM,KAAK,OAAO;MACvC,WACqB,KAAK,MAAM,MAAM,iBAAiB,KAAK,KAAK,GAAG;AACpD,aAAK,MAAM,KAAK,QAAQ;MACxC,WACqB,KAAK,MAAM,MAAM,eAAe,KAAK,KAAK,GAAG;AAClD,aAAK,MAAM,KAAK,MAAM;MACtC,OACiB;AACD,aAAK,MAAM,KAAK,IAAI;MACpC;IACA;AACQ,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,WAAK,OAAO,KAAK;QACb,MAAM,QAAQ,CAAC;QACf,QAAQ,KAAK,MAAM,OAAO,QAAQ,CAAC,CAAC;QACpC,QAAQ;QACR,OAAO,KAAK,MAAM,CAAC;MACnC,CAAa;IACb;AACQ,eAAW,OAAO,MAAM;AACpB,WAAK,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,MAAM,MAAM;AAChE,eAAO;UACH,MAAM;UACN,QAAQ,KAAK,MAAM,OAAO,IAAI;UAC9B,QAAQ;UACR,OAAO,KAAK,MAAM,CAAC;QACvC;MACA,CAAa,CAAC;IACd;AACQ,WAAO;EACf;EACI,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AAC9C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI;QACtC,MAAM,IAAI,CAAC;QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;MAChD;IACA;EACA;EACI,UAAU,KAAK;AACX,UAAM,MAAM,KAAK,MAAM,MAAM,UAAU,KAAK,GAAG;AAC/C,QAAI,KAAK;AACL,YAAM,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,OAC5C,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAClB,IAAI,CAAC;AACX,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;MAC9C;IACA;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;MAChD;IACA;EACA;EACI,OAAO,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG;AAC7C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;MAC3B;IACA;EACA;EACI,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,UAAI,CAAC,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,UAAU,KAAK,IAAI,CAAC,CAAC,GAAG;AACrE,aAAK,MAAM,MAAM,SAAS;MAC1C,WACqB,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAG;AACvE,aAAK,MAAM,MAAM,SAAS;MAC1C;AACY,UAAI,CAAC,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,GAAG;AACjF,aAAK,MAAM,MAAM,aAAa;MAC9C,WACqB,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAC,GAAG;AACnF,aAAK,MAAM,MAAM,aAAa;MAC9C;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,QAAQ,KAAK,MAAM,MAAM;QACzB,YAAY,KAAK,MAAM,MAAM;QAC7B,OAAO;QACP,MAAM,IAAI,CAAC;MAC3B;IACA;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,YAAM,aAAa,IAAI,CAAC,EAAE,KAAI;AAC9B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,MAAM,MAAM,kBAAkB,KAAK,UAAU,GAAG;AAE/E,YAAI,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAI;AACtD;QACpB;AAEgB,cAAM,aAAa,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAK,WAAW,SAAS,WAAW,UAAU,MAAM,GAAG;AACnD;QACpB;MACA,OACiB;AAED,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,IAAI;AACtD,YAAI,iBAAiB,IAAI;AACrB,gBAAM,QAAQ,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI;AAC9C,gBAAM,UAAU,QAAQ,IAAI,CAAC,EAAE,SAAS;AACxC,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAC3C,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,KAAI;AAC1C,cAAI,CAAC,IAAI;QAC7B;MACA;AACY,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AAEvB,cAAMJ,QAAO,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI;AACzD,YAAIA,OAAM;AACN,iBAAOA,MAAK,CAAC;AACb,kBAAQA,MAAK,CAAC;QAClC;MACA,OACiB;AACD,gBAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;MACvD;AACY,aAAO,KAAK,KAAI;AAChB,UAAI,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,GAAG;AAC/C,YAAI,KAAK,QAAQ,YAAY,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAI;AAE/E,iBAAO,KAAK,MAAM,CAAC;QACvC,OACqB;AACD,iBAAO,KAAK,MAAM,GAAG,EAAE;QAC3C;MACA;AACY,aAAO,WAAW,KAAK;QACnB,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;QACpE,OAAO,QAAQ,MAAM,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;MACvF,GAAe,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;IAC7C;EACA;EACI,QAAQ,KAAK,OAAO;AAChB,QAAI;AACJ,SAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,GAAG,OACrC,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG,IAAI;AAC/C,YAAM,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AACvF,YAAMA,QAAO,MAAM,WAAW,YAAW,CAAE;AAC3C,UAAI,CAACA,OAAM;AACP,cAAM,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO;UACH,MAAM;UACN,KAAK;UACL;QACpB;MACA;AACY,aAAO,WAAW,KAAKA,OAAM,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;IACvE;EACA;EACI,SAAS,KAAK,WAAW,WAAW,IAAI;AACpC,QAAI,QAAQ,KAAK,MAAM,OAAO,eAAe,KAAK,GAAG;AACrD,QAAI,CAAC;AACD;AAEJ,QAAI,MAAM,CAAC,KAAK,SAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB;AAC/D;AACJ,UAAM,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AACzC,QAAI,CAAC,YAAY,CAAC,YAAY,KAAK,MAAM,OAAO,YAAY,KAAK,QAAQ,GAAG;AAExE,YAAM,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS;AACvC,UAAI,QAAQ,SAAS,aAAa,SAAS,gBAAgB;AAC3D,YAAM,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,oBAAoB,KAAK,MAAM,OAAO;AAC7F,aAAO,YAAY;AAEnB,kBAAY,UAAU,MAAM,KAAK,IAAI,SAAS,OAAO;AACrD,cAAQ,QAAQ,OAAO,KAAK,SAAS,MAAM,MAAM;AAC7C,iBAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC;AAC5E,YAAI,CAAC;AACD;AACJ,kBAAU,CAAC,GAAG,MAAM,EAAE;AACtB,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACtB,wBAAc;AACd;QACpB,WACyB,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC3B,cAAI,UAAU,KAAK,GAAG,UAAU,WAAW,IAAI;AAC3C,6BAAiB;AACjB;UACxB;QACA;AACgB,sBAAc;AACd,YAAI,aAAa;AACb;AAEJ,kBAAU,KAAK,IAAI,SAAS,UAAU,aAAa,aAAa;AAEhE,cAAM,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;AACxC,cAAM,MAAM,IAAI,MAAM,GAAG,UAAU,MAAM,QAAQ,iBAAiB,OAAO;AAEzE,YAAI,KAAK,IAAI,SAAS,OAAO,IAAI,GAAG;AAChC,gBAAMK,QAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,iBAAO;YACH,MAAM;YACN;YACA,MAAAA;YACA,QAAQ,KAAK,MAAM,aAAaA,KAAI;UAC5D;QACA;AAEgB,cAAM,OAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,eAAO;UACH,MAAM;UACN;UACA;UACA,QAAQ,KAAK,MAAM,aAAa,IAAI;QACxD;MACA;IACA;EACA;EACI,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,GAAG;AACjE,YAAM,mBAAmB,KAAK,MAAM,MAAM,aAAa,KAAK,IAAI;AAChE,YAAM,0BAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,KAAK,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI;AAC3H,UAAI,oBAAoB,yBAAyB;AAC7C,eAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;MACxD;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;MAChB;IACA;EACA;EACI,GAAG,KAAK;AACJ,UAAM,MAAM,KAAK,MAAM,OAAO,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;MAC1B;IACA;EACA;EACI,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;QACX,QAAQ,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;MACtD;IACA;EACA;EACI,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG;AAC/C,QAAI,KAAK;AACL,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAChB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;MACnC,OACiB;AACD,eAAO,IAAI,CAAC;AACZ,eAAO;MACvB;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;QACA;QACA,QAAQ;UACJ;YACI,MAAM;YACN,KAAK;YACL;UACxB;QACA;MACA;IACA;EACA;EACI,IAAI,KAAK;AACL,QAAI;AACJ,QAAI,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG;AACvC,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAChB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;MACnC,OACiB;AAED,YAAI;AACJ,WAAG;AACC,wBAAc,IAAI,CAAC;AACnB,cAAI,CAAC,IAAI,KAAK,MAAM,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;QAC/E,SAAyB,gBAAgB,IAAI,CAAC;AAC9B,eAAO,IAAI,CAAC;AACZ,YAAI,IAAI,CAAC,MAAM,QAAQ;AACnB,iBAAO,YAAY,IAAI,CAAC;QAC5C,OACqB;AACD,iBAAO,IAAI,CAAC;QAChC;MACA;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;QACA;QACA,QAAQ;UACJ;YACI,MAAM;YACN,KAAK;YACL;UACxB;QACA;MACA;IACA;EACA;EACI,WAAW,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,YAAM,UAAU,KAAK,MAAM,MAAM;AACjC,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;QACX;MAChB;IACA;EACA;AACA;AClxBO,IAAM,SAAN,MAAM,QAAO;SAAA;;;EAChB;EACA;EACA;EACA;EACA;EACA,YAAYH,UAAS;AAEjB,SAAK,SAAS,CAAA;AACd,SAAK,OAAO,QAAQ,uBAAO,OAAO,IAAI;AACtC,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAI,WAAU;AACjE,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAc,CAAA;AACnB,SAAK,QAAQ;MACT,QAAQ;MACR,YAAY;MACZ,KAAK;IACjB;AACQ,UAAM,QAAQ;MACV;MACA,OAAO,MAAM;MACb,QAAQ,OAAO;IAC3B;AACQ,QAAI,KAAK,QAAQ,UAAU;AACvB,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS,OAAO;IAClC,WACiB,KAAK,QAAQ,KAAK;AACvB,YAAM,QAAQ,MAAM;AACpB,UAAI,KAAK,QAAQ,QAAQ;AACrB,cAAM,SAAS,OAAO;MACtC,OACiB;AACD,cAAM,SAAS,OAAO;MACtC;IACA;AACQ,SAAK,UAAU,QAAQ;EAC/B;;;;EAII,WAAW,QAAQ;AACf,WAAO;MACH;MACA;IACZ;EACA;;;;EAII,OAAO,IAAI,KAAKA,UAAS;AACrB,UAAMD,SAAQ,IAAI,QAAOC,QAAO;AAChC,WAAOD,OAAM,IAAI,GAAG;EAC5B;;;;EAII,OAAO,UAAU,KAAKC,UAAS;AAC3B,UAAMD,SAAQ,IAAI,QAAOC,QAAO;AAChC,WAAOD,OAAM,aAAa,GAAG;EACrC;;;;EAII,IAAI,KAAK;AACL,UAAM,IAAI,QAAQ,MAAM,gBAAgB,IAAI;AAC5C,SAAK,YAAY,KAAK,KAAK,MAAM;AACjC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,YAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,WAAK,aAAa,KAAK,KAAK,KAAK,MAAM;IACnD;AACQ,SAAK,cAAc,CAAA;AACnB,WAAO,KAAK;EACpB;EACI,YAAY,KAAK,SAAS,CAAA,GAAI,uBAAuB,OAAO;AACxD,QAAI,KAAK,QAAQ,UAAU;AACvB,YAAM,IAAI,QAAQ,MAAM,eAAe,MAAM,EAAE,QAAQ,MAAM,WAAW,EAAE;IACtF;AACQ,WAAO,KAAK;AACR,UAAI;AACJ,UAAI,KAAK,QAAQ,YAAY,OAAO,KAAK,CAAC,iBAAiB;AACvD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAI,GAAI,KAAK,MAAM,GAAG;AACzD,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;QAC3B;AACgB,eAAO;MACvB,CAAa,GAAG;AACA;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,IAAI,WAAW,KAAK,cAAc,QAAW;AAGnD,oBAAU,OAAO;QACrC,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAE9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AAC/D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,QAAQ,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAChC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,WAAW,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AAC/D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,WACyB,CAAC,KAAK,OAAO,MAAM,MAAM,GAAG,GAAG;AACpC,eAAK,OAAO,MAAM,MAAM,GAAG,IAAI;YAC3B,MAAM,MAAM;YACZ,OAAO,MAAM;UACrC;QACA;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAGY,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,YAAY;AACrC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,WAAW,QAAQ,CAAC,kBAAkB;AAC1D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAI,GAAI,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACjD,yBAAa,KAAK,IAAI,YAAY,SAAS;UACnE;QACA,CAAiB;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC1C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;QAC5D;MACA;AACY,UAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,UAAU,UAAU,MAAM,IAAI;AAC9D,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,wBAAwB,WAAW,SAAS,aAAa;AACzD,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAG;AACpB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB,+BAAuB,OAAO,WAAW,IAAI;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC5B,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAG;AACpB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AACY,UAAI,KAAK;AACL,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACrB,kBAAQ,MAAM,MAAM;AACpB;QACpB,OACqB;AACD,gBAAM,IAAI,MAAM,MAAM;QAC1C;MACA;IACA;AACQ,SAAK,MAAM,MAAM;AACjB,WAAO;EACf;EACI,OAAO,KAAK,SAAS,CAAA,GAAI;AACrB,SAAK,YAAY,KAAK,EAAE,KAAK,OAAM,CAAE;AACrC,WAAO;EACf;;;;EAII,aAAa,KAAK,SAAS,CAAA,GAAI;AAE3B,QAAI,YAAY;AAChB,QAAI,QAAQ;AAEZ,QAAI,KAAK,OAAO,OAAO;AACnB,YAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAI,MAAM,SAAS,GAAG;AAClB,gBAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK,SAAS,MAAM,MAAM;AAChF,cAAI,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG;AACnE,wBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IACpC,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MACxC,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;UACjG;QACA;MACA;IACA;AAEQ,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK,SAAS,MAAM,MAAM;AAC5E,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;IACvK;AAEQ,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK,SAAS,MAAM,MAAM;AACjF,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS;IACrI;AACQ,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,WAAO,KAAK;AACR,UAAI,CAAC,cAAc;AACf,mBAAW;MAC3B;AACY,qBAAe;AACf,UAAI;AAEJ,UAAI,KAAK,QAAQ,YAAY,QAAQ,KAAK,CAAC,iBAAiB;AACxD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAI,GAAI,KAAK,MAAM,GAAG;AACzD,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;QAC3B;AACgB,eAAO;MACvB,CAAa,GAAG;AACA;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AACxD,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,SAAS,UAAU,WAAW,SAAS,QAAQ;AACrD,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;QAC5C,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,WAAW,QAAQ,GAAG;AAC3D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAChC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,CAAC,KAAK,MAAM,WAAW,QAAQ,KAAK,UAAU,IAAI,GAAG,IAAI;AACzD,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAGY,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,aAAa;AACtC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,YAAY,QAAQ,CAAC,kBAAkB;AAC3D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAI,GAAI,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACjD,yBAAa,KAAK,IAAI,YAAY,SAAS;UACnE;QACA,CAAiB;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC1C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;QAC5D;MACA;AACY,UAAI,QAAQ,KAAK,UAAU,WAAW,MAAM,GAAG;AAC3C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,MAAM,EAAE,MAAM,KAAK;AAC7B,qBAAW,MAAM,IAAI,MAAM,EAAE;QACjD;AACgB,uBAAe;AACf,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC5B,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;QAC5C,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AACY,UAAI,KAAK;AACL,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACrB,kBAAQ,MAAM,MAAM;AACpB;QACpB,OACqB;AACD,gBAAM,IAAI,MAAM,MAAM;QAC1C;MACA;IACA;AACQ,WAAO;EACf;AACA;AC5ZO,IAAM,YAAN,MAAgB;SAAA;;;EACnB;EACA;;EACA,YAAYC,UAAS;AACjB,SAAK,UAAUA,YAAW;EAClC;EACI,MAAM,OAAO;AACT,WAAO;EACf;EACI,KAAK,EAAE,MAAM,MAAM,QAAO,GAAI;AAC1B,UAAM,cAAc,QAAQ,IAAI,MAAM,MAAM,aAAa,IAAI,CAAC;AAC9D,UAAM,OAAO,KAAK,QAAQ,MAAM,eAAe,EAAE,IAAI;AACrD,QAAI,CAAC,YAAY;AACb,aAAO,iBACA,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;IAClB;AACQ,WAAO,gCACD,OAAO,UAAU,IACjB,QACC,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;EACd;EACI,WAAW,EAAE,OAAM,GAAI;AACnB,UAAM,OAAO,KAAK,OAAO,MAAM,MAAM;AACrC,WAAO;EAAiB,IAAI;;EACpC;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO;EACf;EACI,QAAQ,EAAE,QAAQ,MAAK,GAAI;AACvB,WAAO,KAAK,KAAK,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,MAAM,KAAK;;EACvE;EACI,GAAG,OAAO;AACN,WAAO;EACf;EACI,KAAK,OAAO;AACR,UAAM,UAAU,MAAM;AACtB,UAAM,QAAQ,MAAM;AACpB,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AACzC,YAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,cAAQ,KAAK,SAAS,IAAI;IACtC;AACQ,UAAM,OAAO,UAAU,OAAO;AAC9B,UAAM,YAAa,WAAW,UAAU,IAAM,aAAa,QAAQ,MAAO;AAC1E,WAAO,MAAM,OAAO,YAAY,QAAQ,OAAO,OAAO,OAAO;EACrE;EACI,SAAS,MAAM;AACX,QAAI,WAAW;AACf,QAAI,KAAK,MAAM;AACX,YAAM,WAAW,KAAK,SAAS,EAAE,SAAS,CAAC,CAAC,KAAK,QAAO,CAAE;AAC1D,UAAI,KAAK,OAAO;AACZ,YAAI,KAAK,OAAO,CAAC,GAAG,SAAS,aAAa;AACtC,eAAK,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE;AACtD,cAAI,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,QAAQ;AACvG,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,OAAO,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI;AACrF,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU;UAC3D;QACA,OACqB;AACD,eAAK,OAAO,QAAQ;YAChB,MAAM;YACN,KAAK,WAAW;YAChB,MAAM,WAAW;YACjB,SAAS;UACjC,CAAqB;QACrB;MACA,OACiB;AACD,oBAAY,WAAW;MACvC;IACA;AACQ,gBAAY,KAAK,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,KAAK,KAAK;AACvD,WAAO,OAAO,QAAQ;;EAC9B;EACI,SAAS,EAAE,QAAO,GAAI;AAClB,WAAO,aACA,UAAU,gBAAgB,MAC3B;EACd;EACI,UAAU,EAAE,OAAM,GAAI;AAClB,WAAO,MAAM,KAAK,OAAO,YAAY,MAAM,CAAC;;EACpD;EACI,MAAM,OAAO;AACT,QAAI,SAAS;AAEb,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC1C,cAAQ,KAAK,UAAU,MAAM,OAAO,CAAC,CAAC;IAClD;AACQ,cAAU,KAAK,SAAS,EAAE,MAAM,KAAI,CAAE;AACtC,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAM,MAAM,MAAM,KAAK,CAAC;AACxB,aAAO;AACP,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,gBAAQ,KAAK,UAAU,IAAI,CAAC,CAAC;MAC7C;AACY,cAAQ,KAAK,SAAS,EAAE,MAAM,KAAI,CAAE;IAChD;AACQ,QAAI;AACA,aAAO,UAAU,IAAI;AACzB,WAAO,uBAED,SACA,eACA,OACA;EACd;EACI,SAAS,EAAE,KAAI,GAAI;AACf,WAAO;EAAS,IAAI;;EAC5B;EACI,UAAU,OAAO;AACb,UAAM,UAAU,KAAK,OAAO,YAAY,MAAM,MAAM;AACpD,UAAM,OAAO,MAAM,SAAS,OAAO;AACnC,UAAME,OAAM,MAAM,QACZ,IAAI,IAAI,WAAW,MAAM,KAAK,OAC9B,IAAI,IAAI;AACd,WAAOA,OAAM,UAAU,KAAK,IAAI;;EACxC;;;;EAII,OAAO,EAAE,OAAM,GAAI;AACf,WAAO,WAAW,KAAK,OAAO,YAAY,MAAM,CAAC;EACzD;EACI,GAAG,EAAE,OAAM,GAAI;AACX,WAAO,OAAO,KAAK,OAAO,YAAY,MAAM,CAAC;EACrD;EACI,SAAS,EAAE,KAAI,GAAI;AACf,WAAO,SAAS,OAAO,MAAM,IAAI,CAAC;EAC1C;EACI,GAAG,OAAO;AACN,WAAO;EACf;EACI,IAAI,EAAE,OAAM,GAAI;AACZ,WAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC;EACtD;EACI,KAAK,EAAE,MAAM,OAAO,OAAM,GAAI;AAC1B,UAAM,OAAO,KAAK,OAAO,YAAY,MAAM;AAC3C,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO;IACnB;AACQ,WAAO;AACP,QAAI,MAAM,cAAc,OAAO;AAC/B,QAAI,OAAO;AACP,aAAO,aAAc,OAAO,KAAK,IAAK;IAClD;AACQ,WAAO,MAAM,OAAO;AACpB,WAAO;EACf;EACI,MAAM,EAAE,MAAM,OAAO,KAAI,GAAI;AACzB,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO,OAAO,IAAI;IAC9B;AACQ,WAAO;AACP,QAAI,MAAM,aAAa,IAAI,UAAU,IAAI;AACzC,QAAI,OAAO;AACP,aAAO,WAAW,OAAO,KAAK,CAAC;IAC3C;AACQ,WAAO;AACP,WAAO;EACf;EACI,KAAK,OAAO;AACR,WAAO,YAAY,SAAS,MAAM,SAC5B,KAAK,OAAO,YAAY,MAAM,MAAM,IACnC,aAAa,SAAS,MAAM,UAAU,MAAM,OAAO,OAAO,MAAM,IAAI;EACnF;AACA;AC7KO,IAAM,gBAAN,MAAoB;SAAA;;;;EAEvB,OAAO,EAAE,KAAI,GAAI;AACb,WAAO;EACf;EACI,GAAG,EAAE,KAAI,GAAI;AACT,WAAO;EACf;EACI,SAAS,EAAE,KAAI,GAAI;AACf,WAAO;EACf;EACI,IAAI,EAAE,KAAI,GAAI;AACV,WAAO;EACf;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO;EACf;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO;EACf;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO,KAAK;EACpB;EACI,MAAM,EAAE,KAAI,GAAI;AACZ,WAAO,KAAK;EACpB;EACI,KAAK;AACD,WAAO;EACf;AACA;AC3BO,IAAM,UAAN,MAAM,SAAQ;SAAA;;;EACjB;EACA;EACA;EACA,YAAYF,UAAS;AACjB,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAI,UAAS;AAC9D,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,SAAS,SAAS;AACvB,SAAK,eAAe,IAAI,cAAa;EAC7C;;;;EAII,OAAO,MAAM,QAAQA,UAAS;AAC1B,UAAMI,UAAS,IAAI,SAAQJ,QAAO;AAClC,WAAOI,QAAO,MAAM,MAAM;EAClC;;;;EAII,OAAO,YAAY,QAAQJ,UAAS;AAChC,UAAMI,UAAS,IAAI,SAAQJ,QAAO;AAClC,WAAOI,QAAO,YAAY,MAAM;EACxC;;;;EAII,MAAM,QAAQ,MAAM,MAAM;AACtB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,WAAW,OAAO,CAAC;AAEzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACrD,cAAM,eAAe;AACrB,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,aAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAI,GAAI,YAAY;AACpG,YAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAAS,aAAa,IAAI,GAAG;AAC9I,iBAAO,OAAO;AACd;QACpB;MACA;AACY,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAI;QACd,KAAK,SAAS;AACV,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;QACpB;QACgB,KAAK,MAAM;AACP,iBAAO,KAAK,SAAS,GAAG,KAAK;AAC7B;QACpB;QACgB,KAAK,WAAW;AACZ,iBAAO,KAAK,SAAS,QAAQ,KAAK;AAClC;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;QACpB;QACgB,KAAK,SAAS;AACV,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;QACpB;QACgB,KAAK,cAAc;AACf,iBAAO,KAAK,SAAS,WAAW,KAAK;AACrC;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;QACpB;QACgB,KAAK,aAAa;AACd,iBAAO,KAAK,SAAS,UAAU,KAAK;AACpC;QACpB;QACgB,KAAK,QAAQ;AACT,cAAI,YAAY;AAChB,cAAI,OAAO,KAAK,SAAS,KAAK,SAAS;AACvC,iBAAO,IAAI,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,EAAE,SAAS,QAAQ;AAC3D,wBAAY,OAAO,EAAE,CAAC;AACtB,oBAAQ,OAAO,KAAK,SAAS,KAAK,SAAS;UACnE;AACoB,cAAI,KAAK;AACL,mBAAO,KAAK,SAAS,UAAU;cAC3B,MAAM;cACN,KAAK;cACL,MAAM;cACN,QAAQ,CAAC,EAAE,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,SAAS,KAAI,CAAE;YAC3F,CAAyB;UACzB,OACyB;AACD,mBAAO;UAC/B;AACoB;QACpB;QACgB,SAAS;AACL,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACrB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;UAC/B,OACyB;AACD,kBAAM,IAAI,MAAM,MAAM;UAC9C;QACA;MACA;IACA;AACQ,WAAO;EACf;;;;EAII,YAAY,QAAQ,WAAW,KAAK,UAAU;AAC1C,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,WAAW,OAAO,CAAC;AAEzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACrD,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,SAAS,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAI,GAAI,QAAQ;AAC5F,YAAI,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAAS,SAAS,IAAI,GAAG;AAChI,iBAAO,OAAO;AACd;QACpB;MACA;AACY,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAI;QACd,KAAK,UAAU;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,KAAK,SAAS;AACV,iBAAO,SAAS,MAAM,KAAK;AAC3B;QACpB;QACgB,KAAK,UAAU;AACX,iBAAO,SAAS,OAAO,KAAK;AAC5B;QACpB;QACgB,KAAK,MAAM;AACP,iBAAO,SAAS,GAAG,KAAK;AACxB;QACpB;QACgB,KAAK,YAAY;AACb,iBAAO,SAAS,SAAS,KAAK;AAC9B;QACpB;QACgB,KAAK,MAAM;AACP,iBAAO,SAAS,GAAG,KAAK;AACxB;QACpB;QACgB,KAAK,OAAO;AACR,iBAAO,SAAS,IAAI,KAAK;AACzB;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,SAAS;AACL,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACrB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;UAC/B,OACyB;AACD,kBAAM,IAAI,MAAM,MAAM;UAC9C;QACA;MACA;IACA;AACQ,WAAO;EACf;AACA;AC5LO,IAAM,SAAN,MAAa;SAAA;;;EAChB;EACA;EACA,YAAYJ,UAAS;AACjB,SAAK,UAAUA,YAAW;EAClC;EACI,OAAO,mBAAmB,oBAAI,IAAI;IAC9B;IACA;IACA;EACR,CAAK;;;;EAID,WAAW,UAAU;AACjB,WAAO;EACf;;;;EAII,YAAYH,OAAM;AACd,WAAOA;EACf;;;;EAII,iBAAiB,QAAQ;AACrB,WAAO;EACf;;;;EAII,eAAe;AACX,WAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;EAChD;;;;EAII,gBAAgB;AACZ,WAAO,KAAK,QAAQ,QAAQ,QAAQ,QAAQ;EACpD;AACA;ACpCO,IAAM,SAAN,MAAa;SAAA;;;EAChB,WAAW,aAAY;EACvB,UAAU,KAAK;EACf,QAAQ,KAAK,cAAc,IAAI;EAC/B,cAAc,KAAK,cAAc,KAAK;EACtC,SAAS;EACT,WAAW;EACX,eAAe;EACf,QAAQ;EACR,YAAY;EACZ,QAAQ;EACR,eAAe,MAAM;AACjB,SAAK,IAAI,GAAG,IAAI;EACxB;;;;EAII,WAAW,QAAQ,UAAU;AACzB,QAAI,SAAS,CAAA;AACb,eAAW,SAAS,QAAQ;AACxB,eAAS,OAAO,OAAO,SAAS,KAAK,MAAM,KAAK,CAAC;AACjD,cAAQ,MAAM,MAAI;QACd,KAAK,SAAS;AACV,gBAAM,aAAa;AACnB,qBAAW,QAAQ,WAAW,QAAQ;AAClC,qBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;UACrF;AACoB,qBAAW,OAAO,WAAW,MAAM;AAC/B,uBAAW,QAAQ,KAAK;AACpB,uBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;YACzF;UACA;AACoB;QACpB;QACgB,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,mBAAS,OAAO,OAAO,KAAK,WAAW,UAAU,OAAO,QAAQ,CAAC;AACjE;QACpB;QACgB,SAAS;AACL,gBAAM,eAAe;AACrB,cAAI,KAAK,SAAS,YAAY,cAAc,aAAa,IAAI,GAAG;AAC5D,iBAAK,SAAS,WAAW,YAAY,aAAa,IAAI,EAAE,QAAQ,CAAC,gBAAgB;AAC7E,oBAAMQ,UAAS,aAAa,WAAW,EAAE,KAAK,QAAQ;AACtD,uBAAS,OAAO,OAAO,KAAK,WAAWA,SAAQ,QAAQ,CAAC;YACpF,CAAyB;UACzB,WAC6B,aAAa,QAAQ;AAC1B,qBAAS,OAAO,OAAO,KAAK,WAAW,aAAa,QAAQ,QAAQ,CAAC;UAC7F;QACA;MACA;IACA;AACQ,WAAO;EACf;EACI,OAAO,MAAM;AACT,UAAM,aAAa,KAAK,SAAS,cAAc,EAAE,WAAW,CAAA,GAAI,aAAa,CAAA,EAAE;AAC/E,SAAK,QAAQ,CAAC,SAAS;AAEnB,YAAM,OAAO,EAAE,GAAG,KAAI;AAEtB,WAAK,QAAQ,KAAK,SAAS,SAAS,KAAK,SAAS;AAElD,UAAI,KAAK,YAAY;AACjB,aAAK,WAAW,QAAQ,CAAC,QAAQ;AAC7B,cAAI,CAAC,IAAI,MAAM;AACX,kBAAM,IAAI,MAAM,yBAAyB;UACjE;AACoB,cAAI,cAAc,KAAK;AACnB,kBAAM,eAAe,WAAW,UAAU,IAAI,IAAI;AAClD,gBAAI,cAAc;AAEd,yBAAW,UAAU,IAAI,IAAI,IAAI,YAAaC,OAAM;AAChD,oBAAI,MAAM,IAAI,SAAS,MAAM,MAAMA,KAAI;AACvC,oBAAI,QAAQ,OAAO;AACf,wBAAM,aAAa,MAAM,MAAMA,KAAI;gBACvE;AACgC,uBAAO;cACvC;YACA,OAC6B;AACD,yBAAW,UAAU,IAAI,IAAI,IAAI,IAAI;YACjE;UACA;AACoB,cAAI,eAAe,KAAK;AACpB,gBAAI,CAAC,IAAI,SAAU,IAAI,UAAU,WAAW,IAAI,UAAU,UAAW;AACjE,oBAAM,IAAI,MAAM,6CAA6C;YACzF;AACwB,kBAAM,WAAW,WAAW,IAAI,KAAK;AACrC,gBAAI,UAAU;AACV,uBAAS,QAAQ,IAAI,SAAS;YAC1D,OAC6B;AACD,yBAAW,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS;YAClE;AACwB,gBAAI,IAAI,OAAO;AACX,kBAAI,IAAI,UAAU,SAAS;AACvB,oBAAI,WAAW,YAAY;AACvB,6BAAW,WAAW,KAAK,IAAI,KAAK;gBACxE,OACqC;AACD,6BAAW,aAAa,CAAC,IAAI,KAAK;gBACtE;cACA,WACqC,IAAI,UAAU,UAAU;AAC7B,oBAAI,WAAW,aAAa;AACxB,6BAAW,YAAY,KAAK,IAAI,KAAK;gBACzE,OACqC;AACD,6BAAW,cAAc,CAAC,IAAI,KAAK;gBACvE;cACA;YACA;UACA;AACoB,cAAI,iBAAiB,OAAO,IAAI,aAAa;AACzC,uBAAW,YAAY,IAAI,IAAI,IAAI,IAAI;UAC/D;QACA,CAAiB;AACD,aAAK,aAAa;MAClC;AAEY,UAAI,KAAK,UAAU;AACf,cAAM,WAAW,KAAK,SAAS,YAAY,IAAI,UAAU,KAAK,QAAQ;AACtE,mBAAW,QAAQ,KAAK,UAAU;AAC9B,cAAI,EAAE,QAAQ,WAAW;AACrB,kBAAM,IAAI,MAAM,aAAa,IAAI,kBAAkB;UAC3E;AACoB,cAAI,CAAC,WAAW,QAAQ,EAAE,SAAS,IAAI,GAAG;AAEtC;UACxB;AACoB,gBAAM,eAAe;AACrB,gBAAM,eAAe,KAAK,SAAS,YAAY;AAC/C,gBAAM,eAAe,SAAS,YAAY;AAE1C,mBAAS,YAAY,IAAI,IAAIA,UAAS;AAClC,gBAAI,MAAM,aAAa,MAAM,UAAUA,KAAI;AAC3C,gBAAI,QAAQ,OAAO;AACf,oBAAM,aAAa,MAAM,UAAUA,KAAI;YACnE;AACwB,mBAAO,OAAO;UACtC;QACA;AACgB,aAAK,WAAW;MAChC;AACY,UAAI,KAAK,WAAW;AAChB,cAAM,YAAY,KAAK,SAAS,aAAa,IAAI,WAAW,KAAK,QAAQ;AACzE,mBAAW,QAAQ,KAAK,WAAW;AAC/B,cAAI,EAAE,QAAQ,YAAY;AACtB,kBAAM,IAAI,MAAM,cAAc,IAAI,kBAAkB;UAC5E;AACoB,cAAI,CAAC,WAAW,SAAS,OAAO,EAAE,SAAS,IAAI,GAAG;AAE9C;UACxB;AACoB,gBAAM,gBAAgB;AACtB,gBAAM,gBAAgB,KAAK,UAAU,aAAa;AAClD,gBAAM,gBAAgB,UAAU,aAAa;AAG7C,oBAAU,aAAa,IAAI,IAAIA,UAAS;AACpC,gBAAI,MAAM,cAAc,MAAM,WAAWA,KAAI;AAC7C,gBAAI,QAAQ,OAAO;AACf,oBAAM,cAAc,MAAM,WAAWA,KAAI;YACrE;AACwB,mBAAO;UAC/B;QACA;AACgB,aAAK,YAAY;MACjC;AAEY,UAAI,KAAK,OAAO;AACZ,cAAM,QAAQ,KAAK,SAAS,SAAS,IAAI,OAAM;AAC/C,mBAAW,QAAQ,KAAK,OAAO;AAC3B,cAAI,EAAE,QAAQ,QAAQ;AAClB,kBAAM,IAAI,MAAM,SAAS,IAAI,kBAAkB;UACvE;AACoB,cAAI,CAAC,WAAW,OAAO,EAAE,SAAS,IAAI,GAAG;AAErC;UACxB;AACoB,gBAAM,YAAY;AAClB,gBAAM,YAAY,KAAK,MAAM,SAAS;AACtC,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,OAAO,iBAAiB,IAAI,IAAI,GAAG;AAEnC,kBAAM,SAAS,IAAI,CAAC,QAAQ;AACxB,kBAAI,KAAK,SAAS,OAAO;AACrB,uBAAO,QAAQ,QAAQ,UAAU,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,CAAAC,SAAO;AAC3D,yBAAO,SAAS,KAAK,OAAOA,IAAG;gBACnE,CAAiC;cACjC;AAC4B,oBAAM,MAAM,UAAU,KAAK,OAAO,GAAG;AACrC,qBAAO,SAAS,KAAK,OAAO,GAAG;YAC3D;UACA,OACyB;AAED,kBAAM,SAAS,IAAI,IAAID,UAAS;AAC5B,kBAAI,MAAM,UAAU,MAAM,OAAOA,KAAI;AACrC,kBAAI,QAAQ,OAAO;AACf,sBAAM,SAAS,MAAM,OAAOA,KAAI;cAChE;AAC4B,qBAAO;YACnC;UACA;QACA;AACgB,aAAK,QAAQ;MAC7B;AAEY,UAAI,KAAK,YAAY;AACjB,cAAME,cAAa,KAAK,SAAS;AACjC,cAAM,iBAAiB,KAAK;AAC5B,aAAK,aAAa,SAAU,OAAO;AAC/B,cAAI,SAAS,CAAA;AACb,iBAAO,KAAK,eAAe,KAAK,MAAM,KAAK,CAAC;AAC5C,cAAIA,aAAY;AACZ,qBAAS,OAAO,OAAOA,YAAW,KAAK,MAAM,KAAK,CAAC;UAC3E;AACoB,iBAAO;QAC3B;MACA;AACY,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,KAAI;IACvD,CAAS;AACD,WAAO;EACf;EACI,WAAW,KAAK;AACZ,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,IAAG;AAC1C,WAAO;EACf;EACI,MAAM,KAAKR,UAAS;AAChB,WAAO,OAAO,IAAI,KAAKA,YAAW,KAAK,QAAQ;EACvD;EACI,OAAO,QAAQA,UAAS;AACpB,WAAO,QAAQ,MAAM,QAAQA,YAAW,KAAK,QAAQ;EAC7D;EACI,cAAc,WAAW;AAErB,UAAM,QAAQ,wBAAC,KAAKA,aAAY;AAC5B,YAAM,UAAU,EAAE,GAAGA,SAAO;AAC5B,YAAM,MAAM,EAAE,GAAG,KAAK,UAAU,GAAG,QAAO;AAC1C,YAAM,aAAa,KAAK,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK;AAEzD,UAAI,KAAK,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AACzD,eAAO,WAAW,IAAI,MAAM,oIAAoI,CAAC;MACjL;AAEY,UAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;AAC5C,eAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;MAC7F;AACY,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,WAAW,IAAI,MAAM,0CACtB,OAAO,UAAU,SAAS,KAAK,GAAG,IAAI,mBAAmB,CAAC;MAChF;AACY,UAAI,IAAI,OAAO;AACX,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM,QAAQ;MAClC;AACY,YAAMD,SAAQ,IAAI,QAAQ,IAAI,MAAM,aAAY,IAAM,YAAY,OAAO,MAAM,OAAO;AACtF,YAAMK,UAAS,IAAI,QAAQ,IAAI,MAAM,cAAa,IAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC5F,UAAI,IAAI,OAAO;AACX,eAAO,QAAQ,QAAQ,IAAI,QAAQ,IAAI,MAAM,WAAW,GAAG,IAAI,GAAG,EAC7D,KAAK,CAAAK,SAAOV,OAAMU,MAAK,GAAG,CAAC,EAC3B,KAAK,YAAU,IAAI,QAAQ,IAAI,MAAM,iBAAiB,MAAM,IAAI,MAAM,EACtE,KAAK,YAAU,IAAI,aAAa,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,MAAM,EAChH,KAAK,YAAUL,QAAO,QAAQ,GAAG,CAAC,EAClC,KAAK,CAAAP,UAAQ,IAAI,QAAQ,IAAI,MAAM,YAAYA,KAAI,IAAIA,KAAI,EAC3D,MAAM,UAAU;MACrC;AACY,UAAI;AACA,YAAI,IAAI,OAAO;AACX,gBAAM,IAAI,MAAM,WAAW,GAAG;QAClD;AACgB,YAAI,SAASE,OAAM,KAAK,GAAG;AAC3B,YAAI,IAAI,OAAO;AACX,mBAAS,IAAI,MAAM,iBAAiB,MAAM;QAC9D;AACgB,YAAI,IAAI,YAAY;AAChB,eAAK,WAAW,QAAQ,IAAI,UAAU;QAC1D;AACgB,YAAIF,QAAOO,QAAO,QAAQ,GAAG;AAC7B,YAAI,IAAI,OAAO;AACX,UAAAP,QAAO,IAAI,MAAM,YAAYA,KAAI;QACrD;AACgB,eAAOA;MACvB,SACmB,GAAG;AACN,eAAO,WAAW,CAAC;MACnC;IACA,GAnDsB;AAoDd,WAAO;EACf;EACI,QAAQ,QAAQ,OAAO;AACnB,WAAO,CAAC,MAAM;AACV,QAAE,WAAW;AACb,UAAI,QAAQ;AACR,cAAM,MAAM,mCACN,OAAO,EAAE,UAAU,IAAI,IAAI,IAC3B;AACN,YAAI,OAAO;AACP,iBAAO,QAAQ,QAAQ,GAAG;QAC9C;AACgB,eAAO;MACvB;AACY,UAAI,OAAO;AACP,eAAO,QAAQ,OAAO,CAAC;MACvC;AACY,YAAM;IAClB;EACA;AACA;ACtTA,IAAM,iBAAiB,IAAI,OAAM;AAC1B,SAAS,OAAO,KAAK,KAAK;AAC7B,SAAO,eAAe,MAAM,KAAK,GAAG;AACxC;AAFgB;AAQhB,OAAO,UACH,OAAO,aAAa,SAAUG,UAAS;AACnC,iBAAe,WAAWA,QAAO;AACjC,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACf;AAIA,OAAO,cAAc;AACrB,OAAO,WAAW;AAIlB,OAAO,MAAM,YAAa,MAAM;AAC5B,iBAAe,IAAI,GAAG,IAAI;AAC1B,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACX;AAIA,OAAO,aAAa,SAAU,QAAQ,UAAU;AAC5C,SAAO,eAAe,WAAW,QAAQ,QAAQ;AACrD;AAQA,OAAO,cAAc,eAAe;AAIpC,OAAO,SAAS;AAChB,OAAO,SAAS,QAAQ;AACxB,OAAO,WAAW;AAClB,OAAO,eAAe;AACtB,OAAO,QAAQ;AACf,OAAO,QAAQ,OAAO;AACtB,OAAO,YAAY;AACnB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACH,IAAC,UAAU,OAAO;AAClB,IAAC,aAAa,OAAO;AACrB,IAAC,MAAM,OAAO;AACd,IAAC,aAAa,OAAO;AACrB,IAAC,cAAc,OAAO;AAEtB,IAAC,SAAS,QAAQ;AAClB,IAAC,QAAQ,OAAO;;;AC7D5B,SAAS,mBAAmB,UAAkB,EAAE,iBAAiB,GAA0B;AAEzF,QAAM,YAAY,SAAS,QAAQ,WAAW,IAAI;AAElD,QAAM,0BAA0B,UAAU,QAAQ,WAAW,IAAI;AAEjE,QAAM,qBAAqB,OAAO,uBAAuB;AACzD,MAAI,qBAAqB,OAAO;AAC9B,WAAO,mBAAmB,QAAQ,MAAM,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;AAXS;AAgBF,SAAS,gBAAgB,UAAkB,SAAwB,CAAC,GAAmB;AAC5F,QAAM,uBAAuB,mBAAmB,UAAU,MAAM;AAChE,QAAM,QAAQ,OAAO,MAAM,oBAAoB;AAC/C,QAAM,QAAwB,CAAC,CAAC,CAAC;AACjC,MAAI,cAAc;AAElB,WAAS,YAAY,MAAmB,aAA+B,UAAU;AAC/E,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,YAAY,KAAK,KAAK,MAAM,IAAI;AACtC,gBAAU,QAAQ,CAAC,UAAU,UAAU;AACrC,YAAI,UAAU,GAAG;AACf;AACA,gBAAM,KAAK,CAAC,CAAC;AAAA,QACf;AACA,iBAAS,MAAM,GAAG,EAAE,QAAQ,CAAC,SAAS;AACpC,iBAAO,KAAK,QAAQ,UAAU,GAAG;AACjC,cAAI,MAAM;AACR,kBAAM,WAAW,EAAE,KAAK,EAAE,SAAS,MAAM,MAAM,WAAW,CAAC;AAAA,UAC7D;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,YAAY,KAAK,SAAS,MAAM;AACvD,WAAK,OAAO,QAAQ,CAAC,gBAAgB;AACnC,oBAAY,aAA4B,KAAK,IAAI;AAAA,MACnD,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,QAAQ;AAC/B,YAAM,WAAW,EAAE,KAAK,EAAE,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC;AAAA,IAChE;AAAA,EACF;AAtBS;AAwBT,QAAM,QAAQ,CAAC,aAAa;AAC1B,QAAI,SAAS,SAAS,aAAa;AACjC,eAAS,QAAQ,QAAQ,CAAC,gBAAgB;AACxC,oBAAY,WAA0B;AAAA,MACxC,CAAC;AAAA,IACH,WAAW,SAAS,SAAS,QAAQ;AACnC,YAAM,WAAW,EAAE,KAAK,EAAE,SAAS,SAAS,MAAM,MAAM,SAAS,CAAC;AAAA,IACpE;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAzCgB;AA2CT,SAAS,eAAe,UAAkB,EAAE,iBAAiB,IAAmB,CAAC,GAAG;AACzF,QAAM,QAAQ,OAAO,MAAM,QAAQ;AAEnC,WAAS,OAAO,MAAqB;AACnC,QAAI,KAAK,SAAS,QAAQ;AACxB,UAAI,qBAAqB,OAAO;AAC9B,eAAO,KAAK,KAAK,QAAQ,SAAS,OAAO,EAAE,QAAQ,MAAM,QAAQ;AAAA,MACnE;AACA,aAAO,KAAK,KAAK,QAAQ,SAAS,OAAO;AAAA,IAC3C,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,WAAW,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACrD,WAAW,KAAK,SAAS,MAAM;AAC7B,aAAO,OAAO,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACjD,WAAW,KAAK,SAAS,aAAa;AACpC,aAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IAChD,WAAW,KAAK,SAAS,SAAS;AAChC,aAAO;AAAA,IACT,WAAW,KAAK,SAAS,QAAQ;AAC/B,aAAO,GAAG,KAAK,IAAI;AAAA,IACrB,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,KAAK;AAAA,IACd;AACA,WAAO,yBAAyB,KAAK,IAAI;AAAA,EAC3C;AApBS;AAsBT,SAAO,MAAM,IAAI,MAAM,EAAE,KAAK,EAAE;AAClC;AA1BgB;;;AChET,SAAS,iBAAiB,MAAwB;AACvD,MAAI,KAAK,WAAW;AAClB,WAAO,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;AAAA,EACrE;AACA,SAAO,CAAC,GAAG,IAAI;AACjB;AALgB;AAgCT,SAAS,oBACd,UACA,MAC8B;AAC9B,QAAM,aAAa,iBAAiB,KAAK,OAAO;AAChD,SAAO,6BAA6B,UAAU,CAAC,GAAG,YAAY,KAAK,IAAI;AACzE;AANgB;AAQhB,SAAS,6BACP,UACA,WACA,gBACA,MAC8B;AAC9B,MAAI,eAAe,WAAW,GAAG;AAC/B,WAAO;AAAA,MACL,EAAE,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK;AAAA,MACpC,EAAE,SAAS,IAAI,KAAK;AAAA,IACtB;AAAA,EACF;AACA,QAAM,CAAC,UAAU,GAAG,IAAI,IAAI;AAC5B,QAAM,UAAU,CAAC,GAAG,WAAW,QAAQ;AACvC,MAAI,SAAS,CAAC,EAAE,SAAS,QAAQ,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC,GAAG;AACnD,WAAO,6BAA6B,UAAU,SAAS,MAAM,IAAI;AAAA,EACnE;AACA,MAAI,UAAU,WAAW,KAAK,UAAU;AAEtC,cAAU,KAAK,QAAQ;AACvB,mBAAe,MAAM;AAAA,EACvB;AACA,SAAO;AAAA,IACL,EAAE,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK;AAAA,IACpC,EAAE,SAAS,eAAe,KAAK,EAAE,GAAG,KAAK;AAAA,EAC3C;AACF;AA1BS;AAkCF,SAAS,oBACd,MACA,UACgB;AAChB,MAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,MAAM,QAAQ,SAAS,IAAI,CAAC,GAAG;AACtD,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC7E;AACA,SAAO,6BAA6B,MAAM,QAAQ;AACpD;AARgB;AAUhB,SAAS,6BACP,OACA,UACA,QAAwB,CAAC,GACzB,UAAwB,CAAC,GACT;AAEhB,MAAI,MAAM,WAAW,GAAG;AAEtB,QAAI,QAAQ,SAAS,GAAG;AACtB,YAAM,KAAK,OAAO;AAAA,IACpB;AACA,WAAO,MAAM,SAAS,IAAI,QAAQ,CAAC;AAAA,EACrC;AACA,MAAI,SAAS;AACb,MAAI,MAAM,CAAC,EAAE,YAAY,KAAK;AAC5B,aAAS;AACT,UAAM,MAAM;AAAA,EACd;AACA,QAAM,WAAyB,MAAM,MAAM,KAAK,EAAE,SAAS,KAAK,MAAM,SAAS;AAC/E,QAAM,mBAAiC,CAAC,GAAG,OAAO;AAClD,MAAI,WAAW,IAAI;AACjB,qBAAiB,KAAK,EAAE,SAAS,QAAQ,MAAM,SAAS,CAAC;AAAA,EAC3D;AACA,mBAAiB,KAAK,QAAQ;AAE9B,MAAI,SAAS,gBAAgB,GAAG;AAE9B,WAAO,6BAA6B,OAAO,UAAU,OAAO,gBAAgB;AAAA,EAC9E;AAGA,MAAI,QAAQ,SAAS,GAAG;AAEtB,UAAM,KAAK,OAAO;AAClB,UAAM,QAAQ,QAAQ;AAAA,EACxB,WAAW,SAAS,SAAS;AAE3B,UAAM,CAAC,MAAM,IAAI,IAAI,oBAAoB,UAAU,QAAQ;AAC3D,UAAM,KAAK,CAAC,IAAI,CAAC;AACjB,QAAI,KAAK,SAAS;AAChB,YAAM,QAAQ,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO,6BAA6B,OAAO,UAAU,KAAK;AAC5D;AA7CS;;;AC1ET,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AAJS;AAMT,eAAe,YAAY,SAAS,MAAM,OAAO,SAAS,gBAAgB,OAAO;AAC/E,QAAM,KAAK,QAAQ,OAAO,eAAe;AAGzC,KAAG,KAAK,SAAS,GAAG,KAAK,KAAK,IAAI;AAClC,KAAG,KAAK,UAAU,GAAG,KAAK,KAAK,IAAI;AAEnC,QAAM,MAAM,GAAG,OAAO,WAAW;AACjC,MAAI,QAAQ,KAAK;AACjB,MAAI,KAAK,SAAS,SAAS,KAAK,KAAK,GAAG;AACtC,YAAQ,MAAM,YAAY,KAAK,MAAM,QAAQ,eAAO,gBAAgB,IAAI,GAAG,UAAU,CAAC;AAAA,EACxF;AACA,QAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,QAAM,OAAO,IAAI,OAAO,MAAM;AAC9B,OAAK,KAAK,KAAK;AACf,aAAW,MAAM,KAAK,UAAU;AAChC,OAAK,KAAK,SAAS,GAAG,UAAU,IAAI,OAAO,EAAE;AAE7C,aAAW,KAAK,KAAK,UAAU;AAC/B,MAAI,MAAM,WAAW,YAAY;AACjC,MAAI,MAAM,eAAe,QAAQ;AACjC,MAAI,MAAM,eAAe,KAAK;AAC9B,MAAI,MAAM,aAAa,QAAQ,IAAI;AACnC,MAAI,MAAM,cAAc,QAAQ;AAChC,MAAI,KAAK,SAAS,8BAA8B;AAChD,MAAI,eAAe;AACjB,QAAI,KAAK,SAAS,UAAU;AAAA,EAC9B;AAEA,MAAI,OAAO,IAAI,KAAK,EAAE,sBAAsB;AAC5C,MAAI,KAAK,UAAU,OAAO;AACxB,QAAI,MAAM,WAAW,OAAO;AAC5B,QAAI,MAAM,eAAe,cAAc;AACvC,QAAI,MAAM,SAAS,QAAQ,IAAI;AAC/B,WAAO,IAAI,KAAK,EAAE,sBAAsB;AAAA,EAC1C;AAKA,SAAO,GAAG,KAAK;AACjB;AAzCe;AAmDf,SAAS,YAAY,aAAkB,WAAmB,YAAoB;AAC5E,SAAO,YACJ,OAAO,OAAO,EACd,KAAK,SAAS,kBAAkB,EAChC,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,YAAY,aAAa,MAAM,IAAI,EAC7C,KAAK,MAAM,aAAa,IAAI;AACjC;AAPS;AAST,SAAS,mBAAmB,YAAiB,YAAoB,MAA4B;AAC3F,QAAM,cAAc,WAAW,OAAO,MAAM;AAC5C,QAAM,WAAW,YAAY,aAAa,GAAG,UAAU;AACvD,6BAA2B,UAAU,IAAI;AACzC,QAAM,aAAa,SAAS,KAAK,EAAE,sBAAsB;AACzD,cAAY,OAAO;AACnB,SAAO;AACT;AAPS;AASF,SAAS,uBACd,YACA,YACA,MACqB;AACrB,QAAM,cAA6B,WAAW,OAAO,MAAM;AAC3D,QAAM,WAA2B,YAAY,aAAa,GAAG,UAAU;AACvE,6BAA2B,UAAU,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,CAAC;AACxE,QAAM,gBAAqC,SAAS,KAAK,GAAG,sBAAsB;AAClF,MAAI,eAAe;AACjB,gBAAY,OAAO;AAAA,EACrB;AACA,SAAO;AACT;AAbgB;AAwBhB,SAAS,oBACP,OACA,GACA,gBACA,gBAAgB,OAChB;AACA,QAAM,aAAa;AACnB,QAAM,aAAa,EAAE,OAAO,GAAG;AAC/B,QAAM,MAAM,WAAW,OAAO,MAAM,EAAE,KAAK,SAAS,YAAY,EAAE,KAAK,SAAS,cAAc;AAC9F,QAAM,cAAc,WAAW,OAAO,MAAM,EAAE,KAAK,KAAK,OAAO;AAC/D,MAAI,YAAY;AAChB,aAAW,QAAQ,gBAAgB;AAKjC,UAAM,aAAa,wBAACU,UAClB,mBAAmB,YAAY,YAAYA,KAAI,KAAK,OADnC;AAEnB,UAAM,kBAAkB,WAAW,IAAI,IAAI,CAAC,IAAI,IAAI,oBAAoB,MAAM,UAAU;AAExF,eAAW,gBAAgB,iBAAiB;AAC1C,YAAM,QAAQ,YAAY,aAAa,WAAW,UAAU;AAC5D,iCAA2B,OAAO,YAAY;AAC9C;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe;AACjB,UAAM,OAAO,YAAY,KAAK,EAAE,QAAQ;AACxC,UAAM,UAAU;AAChB,QACG,KAAK,KAAK,KAAK,IAAI,OAAO,EAC1B,KAAK,KAAK,KAAK,IAAI,OAAO,EAC1B,KAAK,SAAS,KAAK,QAAQ,IAAI,OAAO,EACtC,KAAK,UAAU,KAAK,SAAS,IAAI,OAAO;AAE3C,WAAO,WAAW,KAAK;AAAA,EACzB,OAAO;AACL,WAAO,YAAY,KAAK;AAAA,EAC1B;AACF;AAvCS;AAgDT,SAAS,2BAA2B,OAAY,aAA6B;AAC3E,QAAM,KAAK,EAAE;AAEb,cAAY,QAAQ,CAAC,MAAM,UAAU;AACnC,UAAM,aAAa,MAChB,OAAO,OAAO,EACd,KAAK,cAAc,KAAK,SAAS,OAAO,WAAW,QAAQ,EAC3D,KAAK,SAAS,kBAAkB,EAChC,KAAK,eAAe,KAAK,SAAS,WAAW,SAAS,QAAQ;AACjE,QAAI,UAAU,GAAG;AACf,iBAAW,KAAK,KAAK,OAAO;AAAA,IAC9B,OAAO;AAEL,iBAAW,KAAK,MAAM,KAAK,OAAO;AAAA,IACpC;AAAA,EACF,CAAC;AACH;AAhBS;AAuBT,eAAsB,qBAAqB,MAAc;AACvD,QAAM,sBAAyC,CAAC;AAEhD,OAAK,QAAQ,6BAA6B,CAAC,WAAW,QAAQ,aAAa;AACzE,wBAAoB;AAAA,OACjB,YAAY;AACX,cAAM,qBAAqB,GAAG,MAAM,IAAI,QAAQ;AAChD,YAAI,MAAM,gBAAgB,kBAAkB,GAAG;AAC7C,iBAAO,MAAM,WAAW,oBAAoB,QAAW,EAAE,OAAO,aAAa,CAAC;AAAA,QAChF,OAAO;AACL,iBAAO,aAAa,aAAa,SAAS,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,QAC/D;AAAA,MACF,GAAG;AAAA,IACL;AACA,WAAO;AAAA,EACT,CAAC;AAED,QAAM,eAAe,MAAM,QAAQ,IAAI,mBAAmB;AAE1D,SAAO,KAAK,QAAQ,6BAA6B,MAAM,aAAa,MAAM,KAAK,EAAE;AACnF;AApBsB;AAwBf,IAAM,aAAa,8BACxB,IACA,OAAO,IACP;AAAA,EACE,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,mBAAmB;AACrB,IAAI,CAAC,GACL,WACG;AACH,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,eAAe;AAGjB,UAAM,WAAW,eAAe,MAAM,MAAM;AAC5C,UAAM,sBAAsB,MAAM,qBAAqB,eAAe,QAAQ,CAAC;AAG/E,UAAM,gBAAgB,KAAK,QAAQ,SAAS,IAAI;AAEhD,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO,SAAS,IAAI,IAAI,gBAAgB;AAAA,MACxC,YAAY,MAAM,QAAQ,SAAS,QAAQ;AAAA,IAC7C;AACA,UAAM,aAAa,MAAM,YAAY,IAAI,MAAM,OAAO,SAAS,gBAAgB;AAC/E,WAAO;AAAA,EACT,OAAO;AAEL,UAAM,aAAa,KAAK,QAAQ,eAAe,OAAO;AACtD,UAAM,iBAAiB,gBAAgB,WAAW,QAAQ,QAAQ,OAAO,GAAG,MAAM;AAClF,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,mBAAmB;AAAA,IAC5B;AACA,QAAI,QAAQ;AACV,UAAI,UAAU,KAAK,KAAK,GAAG;AACzB,gBAAQ,MAAM,QAAQ,WAAW,YAAY;AAAA,MAC/C;AAEA,YAAM,qBAAqB,MACxB,QAAQ,mBAAmB,EAAE,EAC7B,QAAQ,yBAAyB,EAAE,EACnC,QAAQ,iBAAiB,EAAE,EAC3B,QAAQ,WAAW,OAAO;AAC7B,qBAAO,QAAQ,EAAE,KAAK,SAAS,kBAAkB;AAAA,IAEnD,OAAO;AAKL,YAAM,qBAAqB,MACxB,QAAQ,mBAAmB,EAAE,EAC7B,QAAQ,yBAAyB,EAAE,EACnC,QAAQ,iBAAiB,EAAE,EAC3B,QAAQ,gBAAgB,OAAO;AAClC,qBAAO,QAAQ,EACZ,OAAO,MAAM,EACb,KAAK,SAAS,mBAAmB,QAAQ,gBAAgB,OAAO,CAAC;AAGpE,YAAM,qBAAqB,MACxB,QAAQ,mBAAmB,EAAE,EAC7B,QAAQ,yBAAyB,EAAE,EACnC,QAAQ,iBAAiB,EAAE,EAC3B,QAAQ,WAAW,OAAO;AAC7B,qBAAO,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,kBAAkB;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AACF,GAvF0B;", "names": ["options", "tag", "i", "regex", "escape", "html", "link", "lexer", "options", "list", "tag", "text", "parser", "tokens", "args", "ret", "walkTokens", "src", "line"]}