@echo off
chcp 65001
echo ========================================
echo iFlytek 多模态智能面试评测系统
echo ========================================
echo.
echo 正在启动系统...
echo 前端服务: http://localhost:8080
echo 后端服务: http://localhost:8000
echo.

REM 启动后端服务
echo 启动后端服务...
cd backend
start "后端服务" cmd /k "python simple_start.py"

REM 等待后端启动
timeout /t 3 /nobreak > nul

REM 启动前端服务
echo 启动前端服务...
cd ..
if exist frontend_dist (
    echo 使用构建版本...
    cd frontend_dist
    start "前端服务" cmd /k "python -m http.server 8080"
) else (
    echo 请先构建前端或使用开发模式
)

echo.
echo 系统启动完成！
echo 请访问: http://localhost:8080
pause
