// iFlytek 面试系统应急导航修复脚本
// 直接在页面上创建和修复导航功能

console.log('🚨 开始iFlytek面试系统应急导航修复...');

// 应急修复结果
const emergencyResults = {
    created: [],
    fixed: [],
    errors: []
};

// 1. 检查并创建缺失的导航结构
function createMissingNavigation() {
    console.log('🏗️ 检查并创建缺失的导航结构...');
    
    const app = document.getElementById('app');
    if (!app) {
        emergencyResults.errors.push('无法找到#app容器');
        return false;
    }
    
    // 检查是否已有enterprise-homepage结构
    let homepageContainer = document.querySelector('.enterprise-homepage');
    
    if (!homepageContainer) {
        console.log('📋 创建主页容器结构...');
        
        // 创建主页容器
        homepageContainer = document.createElement('div');
        homepageContainer.className = 'enterprise-homepage';
        
        // 创建头部导航
        const header = document.createElement('header');
        header.className = 'enterprise-header';
        header.style.cssText = `
            background: white;
            border-bottom: 1px solid #e8e8e8;
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        `;
        
        const headerContainer = document.createElement('div');
        headerContainer.className = 'header-container';
        headerContainer.style.cssText = `
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 72px;
        `;
        
        // 创建Logo区域
        const logoSection = document.createElement('div');
        logoSection.className = 'logo-section';
        logoSection.style.cssText = `
            display: flex;
            align-items: center;
            gap: 12px;
        `;
        logoSection.innerHTML = `
            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #1890ff 0%, #0066cc 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold;">iFlytek</div>
            <span style="font-size: 18px; font-weight: 600; color: #1890ff;">iFlytek Spark AI面试系统</span>
        `;
        
        // 创建导航菜单
        const navMenu = document.createElement('nav');
        navMenu.className = 'nav-menu';
        navMenu.innerHTML = `
            <div class="enterprise-nav" style="display: flex; gap: 8px;">
                <div class="el-menu-item nav-item" data-path="/" style="padding: 12px 16px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; color: #666;">首页</div>
                <div class="el-menu-item nav-item" data-path="/demo" style="padding: 12px 16px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; color: #666;">产品演示</div>
                <div class="el-menu-item nav-item" data-path="/interview-selection" style="padding: 12px 16px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; color: #666;">开始面试</div>
                <div class="el-menu-item nav-item" data-path="/reports" style="padding: 12px 16px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; color: #666;">面试报告</div>
                <div class="el-menu-item nav-item" data-path="/intelligent-dashboard" style="padding: 12px 16px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; color: #666;">数据洞察</div>
            </div>
        `;
        
        // 创建操作按钮区域
        const headerActions = document.createElement('div');
        headerActions.className = 'header-actions';
        headerActions.style.cssText = `
            display: flex;
            gap: 12px;
        `;
        headerActions.innerHTML = `
            <button class="el-button secondary-btn" data-path="/candidate-portal" style="padding: 8px 16px; border: 1px solid #1890ff; background: transparent; color: #1890ff; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;">候选人入口</button>
            <button class="el-button cta-button" data-path="/enterprise-home" style="padding: 8px 16px; background: linear-gradient(135deg, #1890ff 0%, #0066cc 100%); color: white; border: none; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;">企业版体验</button>
        `;
        
        // 组装头部
        headerContainer.appendChild(logoSection);
        headerContainer.appendChild(navMenu);
        headerContainer.appendChild(headerActions);
        header.appendChild(headerContainer);
        
        // 创建主要内容区域
        const heroSection = document.createElement('section');
        heroSection.className = 'hero-section';
        heroSection.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        `;
        heroSection.innerHTML = `
            <div class="hero-container" style="max-width: 1200px; margin: 0 auto; padding: 0 24px;">
                <h1 style="font-size: 48px; font-weight: 600; margin-bottom: 20px;">iFlytek Spark智能面试系统</h1>
                <p style="font-size: 20px; margin-bottom: 40px; opacity: 0.9;">基于讯飞星火大模型的多模态AI面试解决方案</p>
                <div style="display: flex; gap: 20px; justify-content: center;">
                    <button class="primary-cta" data-path="/interview-selection" style="padding: 15px 30px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; font-size: 16px; cursor: pointer; transition: all 0.3s ease;">立即开始面试</button>
                    <button class="secondary-cta" data-path="/demo" style="padding: 15px 30px; background: transparent; color: white; border: 2px solid rgba(255,255,255,0.5); border-radius: 8px; font-size: 16px; cursor: pointer; transition: all 0.3s ease;">观看产品演示</button>
                </div>
            </div>
        `;
        
        // 创建产品特性区域
        const productsSection = document.createElement('section');
        productsSection.className = 'products-section';
        productsSection.style.cssText = `
            padding: 80px 0;
            background: #f8f9fa;
        `;
        productsSection.innerHTML = `
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 24px;">
                <h2 style="text-align: center; font-size: 36px; margin-bottom: 20px; color: #333;">核心AI面试解决方案</h2>
                <p style="text-align: center; font-size: 18px; color: #666; margin-bottom: 60px;">基于iFlytek Spark大模型，提供全方位智能面试体验</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                    <div class="product-card" data-path="/interview-selection" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); cursor: pointer; transition: all 0.3s ease;">
                        <h3 style="font-size: 24px; margin-bottom: 15px; color: #1890ff;">AI智能面试官</h3>
                        <p style="color: #666; line-height: 1.6;">基于iFlytek Spark大模型的智能面试官，支持多轮对话、实时评估和个性化问题生成</p>
                    </div>
                    <div class="product-card" data-path="/demo" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); cursor: pointer; transition: all 0.3s ease;">
                        <h3 style="font-size: 24px; margin-bottom: 15px; color: #1890ff;">多模态分析引擎</h3>
                        <p style="color: #666; line-height: 1.6;">集成语音识别、情感分析和行为评估的综合分析系统</p>
                    </div>
                    <div class="product-card" data-path="/intelligent-dashboard" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); cursor: pointer; transition: all 0.3s ease;">
                        <h3 style="font-size: 24px; margin-bottom: 15px; color: #1890ff;">智能招聘管理</h3>
                        <p style="color: #666; line-height: 1.6;">企业级招聘流程管理，从职位发布到人才录用的全链路智能化</p>
                    </div>
                </div>
            </div>
        `;
        
        // 组装完整页面
        homepageContainer.appendChild(header);
        homepageContainer.appendChild(heroSection);
        homepageContainer.appendChild(productsSection);
        
        // 清空app容器并添加新内容
        app.innerHTML = '';
        app.appendChild(homepageContainer);
        
        emergencyResults.created.push('主页导航结构');
        console.log('✅ 主页导航结构创建完成');
    }
    
    return true;
}

// 2. 绑定导航事件
function bindNavigationEvents() {
    console.log('🔗 绑定导航事件...');
    
    // 绑定所有导航元素的点击事件
    const navElements = document.querySelectorAll('[data-path]');
    
    navElements.forEach(element => {
        const path = element.getAttribute('data-path');
        
        element.addEventListener('click', (e) => {
            e.preventDefault();
            console.log(`🖱️ 导航到: ${path}`);
            
            // 多重导航策略
            try {
                // 方式1: History API
                window.history.pushState({}, '', path);
                
                // 方式2: 触发popstate事件
                window.dispatchEvent(new PopStateEvent('popstate', { state: {} }));
                
                // 方式3: 延迟检查并重新加载
                setTimeout(() => {
                    if (window.location.pathname !== path) {
                        window.location.href = path;
                    }
                }, 200);
                
            } catch (error) {
                console.error(`导航失败: ${error.message}`);
                window.location.href = path;
            }
        });
        
        // 添加悬停效果
        element.addEventListener('mouseenter', () => {
            if (element.classList.contains('nav-item')) {
                element.style.backgroundColor = 'rgba(24, 144, 255, 0.1)';
            } else if (element.classList.contains('product-card')) {
                element.style.transform = 'translateY(-4px)';
                element.style.boxShadow = '0 8px 24px rgba(0,0,0,0.15)';
            } else {
                element.style.transform = 'translateY(-2px)';
            }
        });
        
        element.addEventListener('mouseleave', () => {
            if (element.classList.contains('nav-item')) {
                element.style.backgroundColor = '';
            } else if (element.classList.contains('product-card')) {
                element.style.transform = 'translateY(0)';
                element.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
            } else {
                element.style.transform = 'translateY(0)';
            }
        });
        
        emergencyResults.fixed.push(`导航元素: ${element.textContent.trim()}`);
    });
    
    console.log(`✅ 绑定了 ${navElements.length} 个导航元素的事件`);
    return navElements.length;
}

// 3. 主应急修复函数
function runEmergencyNavigationFix() {
    console.log('🚨 开始应急导航修复...\n');
    
    // 重置结果
    emergencyResults.created = [];
    emergencyResults.fixed = [];
    emergencyResults.errors = [];
    
    try {
        // 创建导航结构
        const structureCreated = createMissingNavigation();
        
        // 绑定事件
        const eventsCount = bindNavigationEvents();
        
        // 生成报告
        const report = {
            timestamp: new Date().toISOString(),
            success: structureCreated && eventsCount > 0,
            summary: {
                structuresCreated: emergencyResults.created.length,
                eventsFixed: emergencyResults.fixed.length,
                errors: emergencyResults.errors.length
            },
            details: emergencyResults
        };
        
        console.log('\n📊 应急修复结果:');
        console.log(`  创建结构: ${report.summary.structuresCreated} 个`);
        console.log(`  修复事件: ${report.summary.eventsFixed} 个`);
        console.log(`  错误数量: ${report.summary.errors} 个`);
        
        if (report.summary.errors > 0) {
            console.log('\n❌ 修复过程中的错误:');
            emergencyResults.errors.forEach(error => console.error(`  - ${error}`));
        }
        
        // 保存报告
        window.iflytekEmergencyFixReport = report;
        
        if (report.success) {
            console.log('\n✅ 应急导航修复完成!');
            console.log('🎯 现在可以测试导航功能');
        } else {
            console.log('\n❌ 应急修复失败，请检查错误信息');
        }
        
        return report;
        
    } catch (error) {
        console.error('应急修复失败:', error);
        emergencyResults.errors.push(`应急修复: ${error.message}`);
        return null;
    }
}

// 导出到全局作用域
window.iflytekEmergencyFix = {
    runEmergencyNavigationFix,
    createMissingNavigation,
    bindNavigationEvents,
    getResults: () => emergencyResults
};

console.log('✅ 应急导航修复脚本已加载');
console.log('💡 使用 iflytekEmergencyFix.runEmergencyNavigationFix() 开始应急修复');
console.log('💡 使用 window.iflytekEmergencyFixReport 查看修复报告');
