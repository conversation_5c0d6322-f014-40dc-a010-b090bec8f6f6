import{_ as D,S as F,r as v,i as p,j as c,k as r,n as s,L as P,z as n,p as e,w as i,F as f,x as h,M as d,N as l,E as N,P as y,T as R,D as T,t as V,U as M,V as j,W as k,B as b,f as E,A as L,C as q,X as U}from"./index-b6a2842e.js";const W={class:"candidate-portal"},X={class:"candidate-header"},G={class:"header-container"},H={class:"user-section"},J={class:"user-info"},K={class:"user-name"},O={class:"portal-main"},Q={class:"main-container"},Y={class:"quick-actions-section"},Z={class:"actions-grid"},$={class:"card-icon"},ss={class:"card-footer"},ts={class:"card-icon practice-icon"},es={class:"card-footer"},as={class:"card-icon report-icon"},is={class:"card-footer"},os={class:"status-section"},ns={class:"status-grid"},ls={class:"status-card"},ds={class:"status-header"},cs={class:"status-content"},rs={class:"progress-info"},_s={class:"progress-text"},us={class:"status-card"},ps={class:"status-header"},vs={class:"status-content"},gs={class:"score-display"},ms={class:"score-value"},fs={class:"score-trend"},hs={class:"status-card"},ys={class:"status-header"},ks={class:"status-content"},bs={class:"skill-tags"},Cs={class:"ai-suggestions-section"},ws={class:"suggestions-header"},xs={class:"suggestions-title"},zs={class:"suggestions-grid"},Ss={class:"suggestion-content"},Is={class:"suggestion-title"},As={class:"suggestion-description"},Bs={class:"suggestion-action"},Ds={class:"learning-section"},Fs={class:"learning-grid"},Ps={class:"course-image"},Ns=["src","alt"],Rs={class:"course-badge"},Ts={class:"course-content"},Vs={class:"course-title"},Ms={class:"course-description"},js={class:"course-meta"},Es={class:"course-duration"},Ls={class:"course-rating"},qs={__name:"CandidatePortal",setup(Us){const g=F(),m=v({name:"张三",avatar:"/images/candidate-avatar.svg",topSkills:[{name:"Python",level:"high"},{name:"机器学习",level:"high"},{name:"深度学习",level:"medium"},{name:"TensorFlow",level:"medium"}]}),_=v({completed:3,total:5,averageScore:87}),C=v([{id:1,type:"voice",icon:"Microphone",title:"语音表达优化",description:"建议在回答技术问题时语速稍微放慢，增加停顿以便思考"},{id:2,type:"content",icon:"ChatDotRound",title:"内容结构改进",description:"可以采用STAR法则（情境-任务-行动-结果）来组织回答"},{id:3,type:"knowledge",icon:"Document",title:"知识点补强",description:"建议加强对深度学习优化算法的理解和实践经验"}]),w=v([{id:1,title:"深度学习进阶实战",description:"从理论到实践，掌握深度学习核心算法",image:"/images/ai-chapter-1.jpg",difficulty:"中级",duration:"8小时",rating:"4.8"},{id:2,title:"面试技巧与沟通艺术",description:"提升面试表现，掌握专业沟通技巧",image:"/images/placeholder-demo.svg",difficulty:"初级",duration:"4小时",rating:"4.9"}]),x=()=>{g.push("/interview-selection")},z=()=>{g.push("/practice-mode")},S=()=>{g.push("/reports")};return(Ws,t)=>{const I=p("el-avatar"),o=p("el-icon"),u=p("el-button"),A=p("el-progress"),B=p("el-tag");return c(),r("div",W,[s("header",X,[s("div",G,[t[1]||(t[1]=s("div",{class:"logo-section"},[s("img",{src:P,alt:"iFlytek Spark",class:"logo"}),s("span",{class:"portal-title"},"AI面试助手")],-1)),s("div",H,[s("div",J,[t[0]||(t[0]=s("span",{class:"welcome-text"},"欢迎回来，",-1)),s("span",K,n(m.value.name),1)]),e(I,{size:40,src:m.value.avatar},null,8,["src"])])])]),s("main",O,[s("div",Q,[s("section",Y,[t[11]||(t[11]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"开始您的AI面试之旅"),s("p",{class:"section-subtitle"},"智能化面试体验，助您展现最佳状态")],-1)),s("div",Z,[s("div",{class:"action-card primary-action",onClick:x},[s("div",$,[e(o,{size:32},{default:i(()=>[e(l(N))]),_:1})]),t[3]||(t[3]=s("h3",{class:"card-title"},"开始AI面试",-1)),t[4]||(t[4]=s("p",{class:"card-description"},"与iFlytek Spark AI面试官进行实时对话",-1)),s("div",ss,[e(u,{type:"primary",size:"large",class:"action-btn"},{default:i(()=>[t[2]||(t[2]=d(" 立即开始 ")),e(o,null,{default:i(()=>[e(l(y))]),_:1})]),_:1,__:[2]})])]),s("div",{class:"action-card",onClick:z},[s("div",ts,[e(o,{size:32},{default:i(()=>[e(l(R))]),_:1})]),t[6]||(t[6]=s("h3",{class:"card-title"},"面试练习",-1)),t[7]||(t[7]=s("p",{class:"card-description"},"模拟面试场景，提升表达能力",-1)),s("div",es,[e(u,{size:"large",class:"secondary-btn"},{default:i(()=>t[5]||(t[5]=[d(" 开始练习 ")])),_:1,__:[5]})])]),s("div",{class:"action-card",onClick:S},[s("div",as,[e(o,{size:32},{default:i(()=>[e(l(T))]),_:1})]),t[9]||(t[9]=s("h3",{class:"card-title"},"面试报告",-1)),t[10]||(t[10]=s("p",{class:"card-description"},"查看详细的AI分析和改进建议",-1)),s("div",is,[e(u,{size:"large",class:"secondary-btn"},{default:i(()=>t[8]||(t[8]=[d(" 查看报告 ")])),_:1,__:[8]})])])])]),s("section",os,[s("div",ns,[s("div",ls,[s("div",ds,[t[12]||(t[12]=s("h4",{class:"status-title"},"面试进度",-1)),e(o,{class:"status-icon"},{default:i(()=>[e(l(V))]),_:1})]),s("div",cs,[s("div",rs,[s("span",_s,"已完成 "+n(_.value.completed)+" / "+n(_.value.total)+" 场面试",1),e(A,{percentage:_.value.completed/_.value.total*100,"stroke-width":8,color:"#1890ff"},null,8,["percentage"])])])]),s("div",us,[s("div",ps,[t[13]||(t[13]=s("h4",{class:"status-title"},"综合评分",-1)),e(o,{class:"status-icon"},{default:i(()=>[e(l(M))]),_:1})]),s("div",vs,[s("div",gs,[s("span",ms,n(_.value.averageScore),1),t[14]||(t[14]=s("span",{class:"score-label"},"平均分",-1))]),s("div",fs,[e(o,{class:"trend-up"},{default:i(()=>[e(l(j))]),_:1}),t[15]||(t[15]=s("span",{class:"trend-text"},"较上次提升 5分",-1))])])]),s("div",hs,[s("div",ys,[t[16]||(t[16]=s("h4",{class:"status-title"},"技能匹配度",-1)),e(o,{class:"status-icon"},{default:i(()=>[e(l(k))]),_:1})]),s("div",ks,[s("div",bs,[(c(!0),r(f,null,h(m.value.topSkills,a=>(c(),b(B,{key:a.name,type:a.level==="high"?"success":a.level==="medium"?"warning":"info",class:"skill-tag"},{default:i(()=>[d(n(a.name),1)]),_:2},1032,["type"]))),128))])])])])]),s("section",Cs,[s("div",ws,[s("h3",xs,[e(o,null,{default:i(()=>[e(l(E))]),_:1}),t[17]||(t[17]=d(" AI智能建议 "))]),t[18]||(t[18]=s("p",{class:"suggestions-subtitle"},"基于您的面试表现，AI为您提供个性化改进建议",-1))]),s("div",zs,[(c(!0),r(f,null,h(C.value,a=>(c(),r("div",{class:"suggestion-card",key:a.id},[s("div",{class:L(["suggestion-icon",a.type])},[e(o,null,{default:i(()=>[(c(),b(q(a.icon)))]),_:2},1024)],2),s("div",Ss,[s("h4",Is,n(a.title),1),s("p",As,n(a.description),1),s("div",Bs,[e(u,{size:"small",type:"primary",text:""},{default:i(()=>[t[19]||(t[19]=d(" 查看详情 ")),e(o,null,{default:i(()=>[e(l(y))]),_:1})]),_:1,__:[19]})])])]))),128))])]),s("section",Ds,[t[21]||(t[21]=s("div",{class:"section-header"},[s("h3",{class:"section-title"},"个性化学习路径"),s("p",{class:"section-subtitle"},"根据您的技能评估结果，推荐最适合的学习内容")],-1)),s("div",Fs,[(c(!0),r(f,null,h(w.value,a=>(c(),r("div",{class:"learning-card",key:a.id},[s("div",Ps,[s("img",{src:a.image,alt:a.title},null,8,Ns),s("div",Rs,n(a.difficulty),1)]),s("div",Ts,[s("h4",Vs,n(a.title),1),s("p",Ms,n(a.description),1),s("div",js,[s("span",Es,[e(o,null,{default:i(()=>[e(l(U))]),_:1}),d(" "+n(a.duration),1)]),s("span",Ls,[e(o,null,{default:i(()=>[e(l(k))]),_:1}),d(" "+n(a.rating),1)])]),e(u,{type:"primary",size:"small",class:"course-btn"},{default:i(()=>t[20]||(t[20]=[d(" 开始学习 ")])),_:1,__:[20]})])]))),128))])])])])])}}},Gs=D(qs,[["__scopeId","data-v-51b73f54"]]);export{Gs as default};
