{"version": 3, "file": "gast_resolver_public.js", "sourceRoot": "", "sources": ["../../../../../src/parse/grammar/gast/gast_resolver_public.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,EAAE,cAAc,IAAI,iBAAiB,EAAE,MAAM,gBAAgB,CAAC;AACrE,OAAO,EAAE,eAAe,IAAI,kBAAkB,EAAE,MAAM,cAAc,CAAC;AACrE,OAAO,EACL,mCAAmC,EACnC,oCAAoC,GACrC,MAAM,wBAAwB,CAAC;AAYhC,MAAM,UAAU,cAAc,CAC5B,OAA2B;IAE3B,MAAM,aAAa,GAAiC,QAAQ,CAAC,OAAO,EAAE;QACpE,cAAc,EAAE,mCAAmC;KACpD,CAAC,CAAC;IAEH,MAAM,aAAa,GAAiC,EAAE,CAAC;IACvD,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE;QAC9B,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAClC,CAAC,CAAC,CAAC;IACH,OAAO,iBAAiB,CAAC,aAAa,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC;AACxE,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,OAK/B;IACC,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE;QAC1B,cAAc,EAAE,oCAAoC;KACrD,CAAC,CAAC;IAEH,OAAO,kBAAkB,CACvB,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,WAAW,CACpB,CAAC;AACJ,CAAC"}