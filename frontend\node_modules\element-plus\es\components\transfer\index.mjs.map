{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/transfer/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Transfer from './src/transfer.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTransfer: SFCWithInstall<typeof Transfer> = withInstall(Transfer)\nexport default ElTransfer\n\nexport * from './src/transfer'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ;;;;"}