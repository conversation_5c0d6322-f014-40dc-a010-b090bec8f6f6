{"version": 3, "file": "grammar-code-actions.js", "sourceRoot": "", "sources": ["../../../src/grammar/lsp/grammar-code-actions.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAchF,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,0BAA0B,CAAC;AAChE,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,wCAAwC,CAAC;AAC3E,OAAO,KAAK,GAAG,MAAM,kCAAkC,CAAC;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AAExD,MAAM,OAAO,gCAAgC;IAKzC,YAAY,QAAyB;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QAChD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/D,CAAC;IAED,cAAc,CAAC,QAAyB,EAAE,MAAwB;QAC9D,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,CAAC,EAA0B,EAAE,EAAE,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvE,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,UAAsB,EAAE,QAAyB,EAAE,MAA4C;;QACrH,QAAQ,MAAC,UAAU,CAAC,IAAuB,0CAAE,IAAI,EAAE,CAAC;YAChD,KAAK,UAAU,CAAC,oBAAoB,CAAC;YACrC,KAAK,UAAU,CAAC,iBAAiB;gBAC7B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACjD,MAAM;YACV,KAAK,UAAU,CAAC,mBAAmB;gBAC/B,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACtD,MAAM;YACV,KAAK,UAAU,CAAC,cAAc;gBAC1B,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAClD,MAAM;YACV,KAAK,UAAU,CAAC,oBAAoB;gBAChC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,UAAU,CAAC,mBAAmB;gBAC/B,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACrD,MAAM;YACV,KAAK,UAAU,CAAC,wBAAwB;gBACpC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC/D,MAAM;YACV,KAAK,UAAU,CAAC,cAAc;gBAC1B,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACrD,MAAM;YACV,KAAK,UAAU,CAAC,aAAa,CAAC;YAC9B,KAAK,UAAU,CAAC,cAAc;gBAC1B,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC3D,MAAM;YACV,KAAK,UAAU,CAAC,YAAY;gBACxB,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,UAAU,CAAC,gBAAgB;gBAC5B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACvD,MAAM;YACV,KAAK,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC;gBAClC,MAAM,IAAI,GAAG,UAAU,CAAC,IAAwB,CAAC;gBACjD,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;oBACxE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACxD,CAAC;gBACD,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;gBACD,MAAM;YACV,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAAsB,EAAE,QAAyB;QACvE,MAAM,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,IAAI,EAAE,CAAC;YACP,OAAO;gBACH,KAAK,EAAE,4CAA4C,IAAI,EAAE;gBACzD,IAAI,EAAE,cAAc,CAAC,QAAQ;gBAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE;oBACF,OAAO,EAAE;wBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gCAC1B,KAAK,EAAE,UAAU,CAAC,KAAK;gCACvB,OAAO,EAAE,GAAG,IAAI,YAAY,IAAI,EAAE,CAAC,mCAAmC;6BACzE,CAAC;qBACL;iBACJ;aACJ,CAAC;QACN,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,uBAAuB,CAAC,UAAsB,EAAE,QAAyB;QAC7E,MAAM,IAAI,GAAG,UAAU,CAAC,IAAsB,CAAC;QAC/C,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACrE,OAAO;gBACH,KAAK,EAAE,WAAW,IAAI,QAAQ;gBAC9B,IAAI,EAAE,cAAc,CAAC,QAAQ;gBAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE;oBACF,OAAO,EAAE;wBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gCAC1B,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK;gCAC/B,OAAO,EAAE,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;6BACpD,CAAC;qBACL;iBACJ;aACJ,CAAC;QACN,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,eAAe,CAAC,UAAsB,EAAE,QAAyB;QACrE,MAAM,IAAI,GAAG,UAAU,CAAC,IAAsB,CAAC;QAC/C,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7B,OAAO;gBACH,KAAK,EAAE,uBAAuB;gBAC9B,IAAI,EAAE,cAAc,CAAC,QAAQ;gBAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE;oBACF,OAAO,EAAE;wBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gCAC1B,KAAK,EAAE;oCACH,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG;oCACnC,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG;iCACpC;gCACD,OAAO,EAAE,QAAQ;6BACpB,CAAC;qBACL;iBACJ;aACJ,CAAC;QACN,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,mBAAmB,CAAC,UAAsB,EAAE,QAAyB;QACzE,MAAM,IAAI,GAAG,UAAU,CAAC,IAAsB,CAAC;QAC/C,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3B,OAAO;gBACH,KAAK,EAAE,4BAA4B;gBACnC,IAAI,EAAE,cAAc,CAAC,QAAQ;gBAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE;oBACF,OAAO,EAAE;wBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gCAC1B,KAAK,EAAE,IAAI,CAAC,WAAW;gCACvB,OAAO,EAAE,EAAE;6BACd,CAAC;qBACL;iBACJ;aACJ,CAAC;QACN,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,2BAA2B,CAAC,UAAsB,EAAE,QAAyB;QACjF,MAAM,GAAG,qBAAO,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtC,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC;QACnB,MAAM,KAAK,qBAAO,GAAG,CAAC,CAAC;QACvB,KAAK,CAAC,SAAS,IAAI,UAAU,CAAC,MAAM,CAAC;QACrC,OAAO;YACH,KAAK,EAAE,uBAAuB;YAC9B,IAAI,EAAE,cAAc,CAAC,QAAQ;YAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE;gBACF,OAAO,EAAE;oBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;4BAC1B,KAAK,EAAE;gCACH,KAAK;gCACL,GAAG;6BACN;4BACD,OAAO,EAAE,EAAE;yBACd,CAAC;iBACL;aACJ;SACJ,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,UAAsB,EAAE,QAAyB;QACnE,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK;YAC7B,GAAG,EAAE;gBACD,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;gBACjC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC;aAClD;SACJ,CAAC;QACF,OAAO;YACH,KAAK,EAAE,4BAA4B;YACnC,IAAI,EAAE,cAAc,CAAC,QAAQ;YAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE;gBACF,OAAO,EAAE;oBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;4BAC1B,KAAK;4BACL,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE;yBAC9D,CAAC;iBACL;aACJ;SACJ,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,UAAsB,EAAE,QAAyB;QACrE,OAAO;YACH,KAAK,EAAE,mBAAmB;YAC1B,IAAI,EAAE,cAAc,CAAC,QAAQ;YAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE;gBACF,OAAO,EAAE;oBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;4BAC1B,KAAK,EAAE,EAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,EAAC;4BACnE,OAAO,EAAE,QAAQ;yBACpB,CAAC;iBACL;aACJ;SACJ,CAAC;IACN,CAAC;IAEO,cAAc,CAAC,UAAsB,EAAE,QAAyB;QACpE,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC;QACpD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,kBAAkB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC7E,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACrD,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;gBACpC,OAAO;oBACH,KAAK,EAAE,kCAAkC;oBACzC,IAAI,EAAE,cAAc,CAAC,QAAQ;oBAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;oBACzB,WAAW,EAAE,IAAI;oBACjB,IAAI,EAAE;wBACF,OAAO,EAAE;4BACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;oCAC1B,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK;oCAC/B,OAAO,EAAE,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI;iCAC9D,CAAC;yBACL;qBACJ;iBACJ,CAAC;YACN,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,iBAAiB,CAAC,UAAsB,EAAE,QAAyB;QACvE,OAAO;YACH,KAAK,EAAE,sBAAsB;YAC7B,IAAI,EAAE,cAAc,CAAC,QAAQ;YAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE;gBACF,OAAO,EAAE;oBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;4BAC1B,KAAK,EAAE,UAAU,CAAC,KAAK;4BACvB,OAAO,EAAE,GAAG;yBACf,CAAC;iBACL;aACJ;SACJ,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,UAAsB,EAAE,QAAyB;QACxE,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAoB,CAAC;QAC1D,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAC1C,MAAM,OAAO,GAAe,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAG,mBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;QAChF,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC;YACrC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACjC,MAAM,GAAG,GAAG,OAAO,CAAC,QAAS,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC;gBACT,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE;oBACH,KAAK;oBACL,GAAG,EAAE,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC;iBAC7C;aACJ,CAAC,CAAC;QACP,CAAC;QACD,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;YAClC,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;YACzB,IAAI,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBAChE,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;gBACvC,OAAO,CAAC,IAAI,CAAC;oBACT,OAAO,EAAE,SAAS;oBAClB,KAAK,EAAE;wBACH,KAAK;wBACL,GAAG,EAAE,KAAK;qBACb;iBACJ,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QACD,OAAO;YACH,KAAK,EAAE,sBAAsB;YAC7B,IAAI,EAAE,cAAc,CAAC,QAAQ;YAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE;gBACF,OAAO,EAAE;oBACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,OAAO;iBACvC;aACJ;SACJ,CAAC;IACN,CAAC;IAEO,UAAU,CAAC,UAAsB,EAAE,IAAsB,EAAE,QAAyB;QACxF,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC;QACpD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,kBAAkB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;YACzE,IAAI,SAAS,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAClC,OAAO;oBACH,KAAK,EAAE,iBAAiB,IAAI,CAAC,OAAO,GAAG;oBACvC,IAAI,EAAE,cAAc,CAAC,QAAQ;oBAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;oBACzB,WAAW,EAAE,KAAK;oBAClB,IAAI,EAAE;wBACF,OAAO,EAAE;4BACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;oCAC1B,KAAK,EAAE;wCACH,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG;wCACnC,GAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG;qCACpC;oCACD,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,0CAA0C,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI;iCACpG,CAAC;yBACL;qBACJ;iBACJ,CAAC;YACN,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,iBAAiB,CAAC,UAAsB,EAAE,IAAsB,EAAE,QAAyB;;QAC/F,MAAM,OAAO,GAAkB;YAC3B,SAAS,EAAE;gBACP,KAAK,EAAE,IAAI,CAAC,aAAa;aAC5B;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE;gBACP,QAAQ,EAAE,IAAI,CAAC,OAAO;aACZ;SACjB,CAAC;QACF,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAChE,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC;QAErG,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,IAAI,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC3B,IAAI,kBAAkB,GAAG,CAAC,CAAC,CAAC;QAC5B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACjC,IAAI,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvD,SAAS;YACb,CAAC;YACD,0DAA0D;YAC1D,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;YAC1E,IAAI,QAA8B,CAAC;YACnC,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAoB,CAAC;YAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;YAClF,IAAI,UAAU,EAAE,CAAC;gBACb,uCAAuC;gBACvC,QAAQ,GAAG,MAAA,UAAU,CAAC,QAAQ,0CAAE,KAAK,CAAC,KAAK,CAAC;YAChD,CAAC;iBAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,2CAA2C;gBAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAS,CAAC,KAAK,CAAC,GAAG,CAAC;gBACjF,IAAI,QAAQ,EAAE,CAAC;oBACX,QAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;gBACzD,CAAC;YACL,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,2CAA2C;gBAC3C,QAAQ,GAAG,MAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,KAAK,CAAC,KAAK,CAAC;gBAClD,MAAM,GAAG,IAAI,CAAC;YAClB,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACX,IAAI,iBAAiB,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,kBAAkB,EAAE,CAAC;oBAClE,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC;oBAClC,kBAAkB,GAAG,UAAU,CAAC,MAAM,CAAC;gBAC3C,CAAC;gBACD,kEAAkE;gBAClE,MAAM,CAAC,IAAI,CAAC;oBACR,KAAK,EAAE,kBAAkB,UAAU,GAAG;oBACtC,IAAI,EAAE,cAAc,CAAC,QAAQ;oBAC7B,WAAW,EAAE,CAAC,UAAU,CAAC;oBACzB,WAAW,EAAE,KAAK;oBAClB,IAAI,EAAE;wBACF,OAAO,EAAE;4BACL,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;oCAC1B,KAAK,EAAE;wCACH,KAAK,EAAE,QAAQ;wCACf,GAAG,EAAE,QAAQ;qCAChB;oCACD,OAAO,EAAE,WAAW,UAAU,MAAM,MAAM,EAAE;iCAC/C,CAAC;yBACL;qBACJ;iBACJ,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,kEAAkE;QAClE,IAAI,iBAAiB,IAAI,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,iBAAiB,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC;QACjD,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;CAEJ;AAED,SAAS,iBAAiB,CAAC,MAAW,EAAE,MAAW;IAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC3C,IAAI,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IACxD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACpE,YAAY,GAAG,IAAI,GAAG,YAAY,CAAC;IACvC,CAAC;IACD,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACpC,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;IACtF,CAAC;IACD,OAAO,YAAY,CAAC;AACxB,CAAC"}