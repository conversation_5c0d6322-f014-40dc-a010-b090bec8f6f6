import { createExpoIn, reverseEasing, mirrorEasing, createBackIn, createAnticipate, } from "./utils";
const DEFAULT_OVERSHOOT_STRENGTH = 1.525;
const BOUNCE_FIRST_THRESHOLD = 4.0 / 11.0;
const BOUNCE_SECOND_THRESHOLD = 8.0 / 11.0;
const BOUNCE_THIRD_THRESHOLD = 9.0 / 10.0;
export const linear = p => p;
export const easeIn = createExpoIn(2);
export const easeOut = reverseEasing(easeIn);
export const easeInOut = mirrorEasing(easeIn);
export const circIn = p => 1 - Math.sin(Math.acos(p));
export const circOut = reverseEasing(circIn);
export const circInOut = mirrorEasing(circOut);
export const backIn = createBackIn(DEFAULT_OVERSHOOT_STRENGTH);
export const backOut = reverseEasing(backIn);
export const backInOut = mirrorEasing(backIn);
export const anticipate = createAnticipate(DEFAULT_OVERSHOOT_STRENGTH);
const ca = 4356.0 / 361.0;
const cb = 35442.0 / 1805.0;
const cc = 16061.0 / 1805.0;
export const bounceOut = (p) => {
    if (p === 1 || p === 0)
        return p;
    const p2 = p * p;
    return p < BOUNCE_FIRST_THRESHOLD
        ? 7.5625 * p2
        : p < BOUNCE_SECOND_THRESHOLD
            ? 9.075 * p2 - 9.9 * p + 3.4
            : p < BOUNCE_THIRD_THRESHOLD
                ? ca * p2 - cb * p + cc
                : 10.8 * p * p - 20.52 * p + 10.72;
};
export const bounceIn = reverseEasing(bounceOut);
export const bounceInOut = (p) => p < 0.5
    ? 0.5 * (1.0 - bounceOut(1.0 - p * 2.0))
    : 0.5 * bounceOut(p * 2.0 - 1.0) + 0.5;
//# sourceMappingURL=index.js.map