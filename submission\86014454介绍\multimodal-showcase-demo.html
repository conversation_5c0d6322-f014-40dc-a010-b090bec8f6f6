<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek Spark 多模态AI特色展示 - 竞品优化版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }
        
        .showcase-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .showcase-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .showcase-subtitle {
            font-size: 20px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        .section {
            padding: 80px 0;
        }
        
        .section-title {
            font-size: 36px;
            text-align: center;
            margin-bottom: 16px;
            color: #2c3e50;
        }
        
        .section-subtitle {
            font-size: 18px;
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 60px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
            margin-bottom: 80px;
        }
        
        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            font-size: 24px;
            color: white;
        }
        
        .feature-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2c3e50;
        }
        
        .feature-highlights {
            list-style: none;
        }
        
        .highlight-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 16px;
            color: #5a6c7d;
        }
        
        .highlight-icon {
            color: #27ae60;
            margin-right: 12px;
            font-size: 18px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 32px;
            margin-bottom: 80px;
        }
        
        .metric-card {
            background: white;
            border-radius: 20px;
            padding: 40px 24px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .metric-value {
            font-size: 48px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 12px;
        }
        
        .metric-label {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .metric-comparison {
            font-size: 14px;
            color: #95a5a6;
            padding: 8px 16px;
            background: #f8f9fa;
            border-radius: 20px;
            display: inline-block;
        }
        
        .scenarios-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 32px;
        }
        
        .scenario-card {
            background: white;
            border-radius: 20px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .company-info {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }
        
        .company-details h4 {
            font-size: 20px;
            color: #2c3e50;
            margin-bottom: 4px;
        }
        
        .company-details p {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .result-metrics {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-top: 16px;
        }
        
        .result-item {
            background: #f8f9fa;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .result-item strong {
            color: #667eea;
            font-weight: 700;
        }
        
        .demo-animation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin: 40px 0;
        }
        
        .voice-wave {
            display: flex;
            justify-content: center;
            align-items: end;
            gap: 4px;
            height: 60px;
            margin-bottom: 20px;
        }
        
        .wave-bar {
            width: 6px;
            background: linear-gradient(to top, #667eea, #764ba2);
            border-radius: 3px;
            animation: wave 1.5s ease-in-out infinite;
        }
        
        .wave-bar:nth-child(1) { animation-delay: 0s; }
        .wave-bar:nth-child(2) { animation-delay: 0.1s; }
        .wave-bar:nth-child(3) { animation-delay: 0.2s; }
        .wave-bar:nth-child(4) { animation-delay: 0.3s; }
        .wave-bar:nth-child(5) { animation-delay: 0.4s; }
        .wave-bar:nth-child(6) { animation-delay: 0.5s; }
        .wave-bar:nth-child(7) { animation-delay: 0.6s; }
        .wave-bar:nth-child(8) { animation-delay: 0.7s; }
        
        @keyframes wave {
            0%, 100% { height: 20px; }
            50% { height: 50px; }
        }
        
        .status-text {
            font-size: 16px;
            color: #667eea;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .showcase-title {
                font-size: 36px;
            }
            
            .features-grid,
            .metrics-grid,
            .scenarios-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 特色展示头部 -->
    <div class="showcase-header">
        <div class="container">
            <h1 class="showcase-title">iFlytek Spark 全能面试AI</h1>
            <p class="showcase-subtitle">融合行业三强优势，超越传统面试边界</p>
        </div>
    </div>

    <!-- 核心功能特性 -->
    <div class="section">
        <div class="container">
            <h2 class="section-title">核心功能特性</h2>
            <p class="section-subtitle">基于三大竞品平台优势的全面整合</p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎤</div>
                    <h3 class="feature-title">实时面试辅助</h3>
                    <ul class="feature-highlights">
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>98%+ 准确率实时语音识别</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>毫秒级智能提示响应</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>多模态情绪状态分析</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>7×24小时全天候辅助</span>
                        </li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">多维度智能评估</h3>
                    <ul class="feature-highlights">
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>12维度全方位能力评估</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>iFlytek Spark深度学习分析</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>可视化评估报告生成</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>行业标准对比分析</span>
                        </li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🏢</div>
                    <h3 class="feature-title">系统化管理平台</h3>
                    <ul class="feature-highlights">
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>ATS+TRM一体化管理</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>千人级批量面试调度</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>多维度数据洞察分析</span>
                        </li>
                        <li class="highlight-item">
                            <span class="highlight-icon">✓</span>
                            <span>雇主品牌智能建设</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 技术性能指标 -->
    <div class="section" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
        <div class="container">
            <h2 class="section-title">技术性能指标</h2>
            <p class="section-subtitle">超越行业标准的技术实力证明</p>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">98.5%</div>
                    <div class="metric-label">语音识别准确率</div>
                    <div class="metric-comparison">行业平均 85%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">< 200ms</div>
                    <div class="metric-label">AI响应延迟</div>
                    <div class="metric-comparison">行业平均 1.2s</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">95.2%</div>
                    <div class="metric-label">面试评估准确率</div>
                    <div class="metric-comparison">行业平均 78%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">12+</div>
                    <div class="metric-label">评估维度</div>
                    <div class="metric-comparison">行业平均 6维</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时演示动画 -->
    <div class="section">
        <div class="container">
            <div class="demo-animation">
                <h3 style="margin-bottom: 24px; color: #2c3e50;">实时语音分析演示</h3>
                <div class="voice-wave">
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                </div>
                <div class="status-text" id="statusText">正在实时分析语音内容...</div>
            </div>
        </div>
    </div>

    <!-- 实际应用案例 -->
    <div class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">实际应用场景</h2>
            <p class="section-subtitle">真实企业案例，验证系统价值</p>
            
            <div class="scenarios-grid">
                <div class="scenario-card">
                    <div class="company-info">
                        <div class="company-logo">科技</div>
                        <div class="company-details">
                            <h4>某知名科技企业</h4>
                            <p>员工规模：10000+</p>
                        </div>
                    </div>
                    <div>
                        <h5 style="color: #2c3e50; margin-bottom: 8px;">挑战</h5>
                        <p style="color: #7f8c8d; margin-bottom: 16px;">校园招聘量大，传统面试效率低，候选人体验差</p>
                        
                        <h5 style="color: #2c3e50; margin-bottom: 8px;">解决方案</h5>
                        <p style="color: #7f8c8d; margin-bottom: 16px;">部署iFlytek Spark AI面试系统，实现批量智能筛选</p>
                        
                        <h5 style="color: #2c3e50; margin-bottom: 8px;">效果</h5>
                        <div class="result-metrics">
                            <span class="result-item">面试效率提升 <strong>300%</strong></span>
                            <span class="result-item">招聘成本降低 <strong>60%</strong></span>
                            <span class="result-item">候选人满意度 <strong>95%</strong></span>
                        </div>
                    </div>
                </div>

                <div class="scenario-card">
                    <div class="company-info">
                        <div class="company-logo">金融</div>
                        <div class="company-details">
                            <h4>某大型金融集团</h4>
                            <p>员工规模：50000+</p>
                        </div>
                    </div>
                    <div>
                        <h5 style="color: #2c3e50; margin-bottom: 8px;">挑战</h5>
                        <p style="color: #7f8c8d; margin-bottom: 16px;">多地分支机构招聘标准不统一，人才评估主观性强</p>
                        
                        <h5 style="color: #2c3e50; margin-bottom: 8px;">解决方案</h5>
                        <p style="color: #7f8c8d; margin-bottom: 16px;">统一部署多维度AI评估系统，建立标准化评估体系</p>
                        
                        <h5 style="color: #2c3e50; margin-bottom: 8px;">效果</h5>
                        <div class="result-metrics">
                            <span class="result-item">评估标准化 <strong>100%</strong></span>
                            <span class="result-item">人才质量提升 <strong>40%</strong></span>
                            <span class="result-item">管理效率提升 <strong>250%</strong></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 状态文本轮播
        const statusTexts = [
            '正在实时分析语音内容...',
            '检测到技术关键词，匹配度95%',
            '情绪状态：自信、专业',
            '语速适中，表达清晰',
            '正在生成智能建议...'
        ];
        
        let statusIndex = 0;
        const statusElement = document.getElementById('statusText');
        
        setInterval(() => {
            statusIndex = (statusIndex + 1) % statusTexts.length;
            statusElement.textContent = statusTexts[statusIndex];
        }, 3000);
    </script>
</body>
</html>
