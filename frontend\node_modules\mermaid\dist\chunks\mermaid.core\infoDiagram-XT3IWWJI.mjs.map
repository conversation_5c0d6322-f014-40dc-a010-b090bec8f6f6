{"version": 3, "sources": ["../../../src/diagrams/info/infoParser.ts", "../../../src/diagrams/info/infoDb.ts", "../../../src/diagrams/info/infoRenderer.ts", "../../../src/diagrams/info/infoDiagram.ts"], "sourcesContent": ["import type { Info } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: Info = await parse('info', input);\n    log.debug(ast);\n  },\n};\n", "import type { InfoFields, InfoDB } from './infoTypes.js';\nimport packageJson from '../../../package.json' assert { type: 'json' };\n\nexport const DEFAULT_INFO_DB: InfoFields = {\n  version: packageJson.version + (includeLargeFeatures ? '' : '-tiny'),\n} as const;\n\nexport const getVersion = (): string => DEFAULT_INFO_DB.version;\n\nexport const db: InfoDB = {\n  getVersion,\n};\n", "import type { DrawDefinition, SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\n/**\n * Draws an info picture in the tag with id: id based on the graph definition in text.\n *\n * @param text - The text of the diagram.\n * @param id - The id of the diagram which will be used as a DOM element id.\n * @param version - MermaidJS version.\n */\nconst draw: DrawDefinition = (text, id, version) => {\n  log.debug('rendering info diagram\\n' + text);\n\n  const svg: SVG = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n\n  const group: SVGGroup = svg.append('g');\n  group\n    .append('text')\n    .attr('x', 100)\n    .attr('y', 40)\n    .attr('class', 'version')\n    .attr('font-size', 32)\n    .style('text-anchor', 'middle')\n    .text(`v${version}`);\n};\n\nexport const renderer = { draw };\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { parser } from './infoParser.js';\nimport { db } from './infoDb.js';\nimport { renderer } from './infoRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n};\n"], "mappings": ";;;;;;;;;;;;;AACA,SAAS,aAAa;AAIf,IAAM,SAA2B;AAAA,EACtC,OAAO,8BAAO,UAAiC;AAC7C,UAAM,MAAY,MAAM,MAAM,QAAQ,KAAK;AAC3C,QAAI,MAAM,GAAG;AAAA,EACf,GAHO;AAIT;;;ACPO,IAAM,kBAA8B;AAAA,EACzC,SAAS,gBAAY,WAAW,OAAuB,KAAK;AAC9D;AAEO,IAAM,aAAa,6BAAc,gBAAgB,SAA9B;AAEnB,IAAM,KAAa;AAAA,EACxB;AACF;;;ACCA,IAAM,OAAuB,wBAAC,MAAM,IAAI,YAAY;AAClD,MAAI,MAAM,6BAA6B,IAAI;AAE3C,QAAM,MAAW,iBAAiB,EAAE;AACpC,mBAAiB,KAAK,KAAK,KAAK,IAAI;AAEpC,QAAM,QAAkB,IAAI,OAAO,GAAG;AACtC,QACG,OAAO,MAAM,EACb,KAAK,KAAK,GAAG,EACb,KAAK,KAAK,EAAE,EACZ,KAAK,SAAS,SAAS,EACvB,KAAK,aAAa,EAAE,EACpB,MAAM,eAAe,QAAQ,EAC7B,KAAK,IAAI,OAAO,EAAE;AACvB,GAf6B;AAiBtB,IAAM,WAAW,EAAE,KAAK;;;ACxBxB,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AACF;", "names": []}