{"version": 3, "file": "switch.mjs", "sources": ["../../../../../../packages/components/switch/src/switch.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isBoolean,\n  isNumber,\n  isString,\n  isValidComponentSize,\n} from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useAriaProps } from '@element-plus/hooks'\nimport type { ComponentSize } from '@element-plus/constants'\nimport type Switch from './switch.vue'\nimport type { ExtractPropTypes, PropType } from 'vue'\n\nexport const switchProps = buildProps({\n  /**\n   * @description binding value, it should be equivalent to either `active-value` or `inactive-value`, by default it's `boolean` type\n   */\n  modelValue: {\n    type: [Boolean, String, Number],\n    default: false,\n  },\n  /**\n   * @description whether Switch is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether Switch is in loading state\n   */\n  loading: Boolean,\n  /**\n   * @description size of Switch\n   */\n  size: {\n    type: String as PropType<ComponentSize>,\n    validator: isValidComponentSize,\n  },\n  /**\n   * @description width of Switch\n   */\n  width: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description whether icon or text is displayed inside dot, only the first character will be rendered for text\n   */\n  inlinePrompt: Boolean,\n  /**\n   * @description component of the icon displayed in action when in `off` state\n   */\n  inactiveActionIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description component of the icon displayed in action when in `on` state\n   */\n  activeActionIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description component of the icon displayed when in `on` state, overrides `active-text`\n   */\n  activeIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description component of the icon displayed when in `off` state, overrides `inactive-text`\n   */\n  inactiveIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description text displayed when in `on` state\n   */\n  activeText: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description text displayed when in `off` state\n   */\n  inactiveText: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description switch value when in `on` state\n   */\n  activeValue: {\n    type: [Boolean, String, Number],\n    default: true,\n  },\n  /**\n   * @description switch value when in `off` state\n   */\n  inactiveValue: {\n    type: [Boolean, String, Number],\n    default: false,\n  },\n  /**\n   * @description input name of Switch\n   */\n  name: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description before-change hook before the switch state changes. If `false` is returned or a `Promise` is returned and then is rejected, will stop switching\n   */\n  beforeChange: {\n    type: definePropType<() => Promise<boolean> | boolean>(Function),\n  },\n  /**\n   * @description id for input\n   */\n  id: String,\n  /**\n   * @description tabindex for input\n   */\n  tabindex: {\n    type: [String, Number],\n  },\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport type SwitchProps = ExtractPropTypes<typeof switchProps>\n\nexport const switchEmits = {\n  [UPDATE_MODEL_EVENT]: (val: boolean | string | number) =>\n    isBoolean(val) || isString(val) || isNumber(val),\n  [CHANGE_EVENT]: (val: boolean | string | number) =>\n    isBoolean(val) || isString(val) || isNumber(val),\n  [INPUT_EVENT]: (val: boolean | string | number) =>\n    isBoolean(val) || isString(val) || isNumber(val),\n}\nexport type SwitchEmits = typeof switchEmits\n\nexport type SwitchInstance = InstanceType<typeof Switch> & unknown\n"], "names": [], "mappings": ";;;;;;;;AAeY,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,SAAS,EAAE,oBAAoB;AACnC,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;AACnC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,GAAG,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;AAC3B,EAAE,CAAC,kBAAkB,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjF,EAAE,CAAC,YAAY,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC;AAC3E,EAAE,CAAC,WAAW,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC;AAC1E;;;;"}