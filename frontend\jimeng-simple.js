#!/usr/bin/env node

/**
 * 即梦AI视频生成器 - 简化版本
 * 请在下方填入您的新API密钥
 */

import fetch from 'node-fetch';
import fs from 'fs';

console.log('🎬 即梦AI视频生成器 - 简化版本\n');

// ⚠️ 请在这里填入您的新API密钥
const ACCESS_KEY_ID = "YOUR_ACCESS_KEY_ID_HERE";
const SECRET_ACCESS_KEY = "YOUR_SECRET_ACCESS_KEY_HERE";

// 检查密钥是否已填入
if (!ACCESS_KEY_ID || !SECRET_ACCESS_KEY) {
    console.log('❌ 请编辑此文件，在上方填入您的新API密钥');
    console.log('');
    console.log('📝 编辑步骤:');
    console.log('1. 打开文件: frontend/jimeng-simple.js');
    console.log('2. 找到第12-13行');
    console.log('3. 在引号中填入您的新密钥');
    console.log('4. 保存文件后重新运行');
    console.log('');
    console.log('示例:');
    console.log('const ACCESS_KEY_ID = "AKLT..."; // 您的新AccessKeyId');
    console.log('const SECRET_ACCESS_KEY = "TWpN..."; // 您的新SecretAccessKey');
    process.exit(1);
}

console.log('✅ API密钥已配置');
console.log(`Access Key ID: ${ACCESS_KEY_ID.substring(0, 10)}...`);

// 测试用的简化提示词
const testPrompt = `专业AI面试系统界面，清晰显示"科大讯飞Spark智能面试系统"中文标题，蓝紫色渐变背景，包含"开始面试""能力评估""生成报告"白色按钮，现代企业UI设计，高对比度文字，专业商务风格`;

console.log('\n🎯 测试提示词:');
console.log(testPrompt);

// 生成测试视频
async function generateTestVideo() {
    console.log('\n🚀 开始生成测试视频...');
    
    try {
        // 即梦AI API调用格式（需要根据实际文档调整）
        const requestData = {
            prompt: testPrompt,
            duration: 180, // 3分钟测试
            width: 1920,
            height: 1080,
            fps: 30
        };
        
        console.log('📡 发送API请求...');
        
        // 尝试不同的可能API端点
        const apiEndpoints = [
            'https://api.jimeng.ai/v1/video/generate',
            'https://api.jimeng.ai/api/v1/video/text2video',
            'https://jimeng.ai/api/video/generate'
        ];
        
        for (const endpoint of apiEndpoints) {
            console.log(`🔗 尝试端点: ${endpoint}`);
            
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${ACCESS_KEY_ID}`,
                        'X-API-Key': ACCESS_KEY_ID,
                        'Access-Key-Id': ACCESS_KEY_ID,
                        'Secret-Access-Key': SECRET_ACCESS_KEY
                    },
                    body: JSON.stringify({
                        ...requestData,
                        access_key_id: ACCESS_KEY_ID,
                        secret_access_key: SECRET_ACCESS_KEY
                    })
                });
                
                console.log(`📊 响应状态: ${response.status}`);
                
                if (response.status === 404) {
                    console.log('❌ 端点不存在，尝试下一个...');
                    continue;
                }
                
                const responseText = await response.text();
                console.log('📋 响应内容:', responseText);
                
                if (response.ok) {
                    try {
                        const result = JSON.parse(responseText);
                        console.log('✅ API调用成功!');
                        console.log('🎯 任务信息:', JSON.stringify(result, null, 2));
                        
                        // 保存任务信息
                        const taskInfo = {
                            endpoint: endpoint,
                            taskId: result.task_id || result.taskId || result.id,
                            prompt: testPrompt,
                            status: 'pending',
                            createdAt: new Date().toISOString(),
                            response: result
                        };
                        
                        fs.writeFileSync('jimeng-test-task.json', JSON.stringify(taskInfo, null, 2));
                        console.log('💾 任务信息已保存到 jimeng-test-task.json');
                        
                        return true;
                        
                    } catch (parseError) {
                        console.log('⚠️ 响应不是有效的JSON，但请求可能成功');
                        return true;
                    }
                } else {
                    console.log(`❌ 请求失败: ${response.status}`);
                    
                    if (response.status === 401) {
                        console.log('💡 认证失败，请检查API密钥是否正确');
                    } else if (response.status === 403) {
                        console.log('💡 权限不足，请检查账户状态');
                    } else if (response.status === 429) {
                        console.log('💡 请求过于频繁，请稍后再试');
                    }
                }
                
            } catch (fetchError) {
                console.log(`❌ 网络请求失败: ${fetchError.message}`);
            }
        }
        
        console.log('\n❌ 所有API端点都无法连接');
        console.log('💡 可能的解决方案:');
        console.log('1. 检查即梦AI官方文档获取正确的API端点');
        console.log('2. 确认API密钥格式是否正确');
        console.log('3. 检查账户是否已激活视频生成功能');
        console.log('4. 联系即梦AI技术支持');
        
        return false;
        
    } catch (error) {
        console.log(`❌ 生成失败: ${error.message}`);
        return false;
    }
}

// 检查即梦AI官方文档
function showDocumentationHelp() {
    console.log('\n📚 即梦AI官方资源:');
    console.log('🌐 官网: https://jimeng.ai');
    console.log('📖 API文档: https://docs.jimeng.ai');
    console.log('💬 技术支持: 查看官网联系方式');
    console.log('');
    console.log('📋 需要确认的信息:');
    console.log('1. 正确的API端点地址');
    console.log('2. 认证方式 (Bearer Token / API Key / 签名)');
    console.log('3. 请求参数格式');
    console.log('4. 响应数据结构');
}

// 主函数
async function main() {
    const success = await generateTestVideo();
    
    if (!success) {
        showDocumentationHelp();
    }
    
    console.log('\n🏁 测试完成');
}

// 运行
main().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
});
