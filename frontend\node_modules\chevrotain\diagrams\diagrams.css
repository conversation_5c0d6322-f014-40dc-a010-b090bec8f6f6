svg.railroad-diagram path {
    stroke-width: 3;
    stroke: black;
    fill: rgba(0, 0, 0, 0);
}

svg.railroad-diagram text {
    font: bold 14px monospace;
    text-anchor: middle;
}

svg.railroad-diagram text.label {
    text-anchor: start;
}

svg.railroad-diagram text.comment {
    font: italic 12px monospace;
}

svg.railroad-diagram g.non-terminal rect {
    fill: hsl(223, 100%, 83%);
}

svg.railroad-diagram rect {
    stroke-width: 3;
    stroke: black;
    fill: hsl(190, 100%, 83%);
}

.diagramHeader {
    display: inline-block;
    -webkit-touch-callout: default;
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    font-weight: bold;
    font-family: monospace;
    font-size: 18px;
    margin-bottom: -8px;
    text-align: center;
}

.diagramHeaderDef {
    background-color: lightgreen;
}

svg.railroad-diagram text {
    -webkit-touch-callout: default;
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

svg.railroad-diagram g.non-terminal rect.diagramRectUsage {
    color: green;
    fill: yellow;
    stroke: 5;
}

svg.railroad-diagram g.terminal rect.diagramRectUsage {
    color: green;
    fill: yellow;
    stroke: 5;
}

div {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

svg {
    width: 100%;
}

svg.railroad-diagram g.non-terminal text {
    cursor: pointer;
}