#!/usr/bin/env node

/**
 * 🎨 第一步：高质量中文界面图片生成器
 * Step 1: High-Quality Chinese Interface Image Generator
 * 
 * 专门生成企业级中文界面截图，确保字体清晰和设计专业
 * Specifically generates enterprise-level Chinese interface screenshots with clear fonts and professional design
 */

const fs = require('fs');
const path = require('path');

// 环境变量配置
const MIDJOURNEY_API_KEY = process.env.MIDJOURNEY_API_KEY;
const DALLE_API_KEY = process.env.DALLE_API_KEY || process.env.OPENAI_API_KEY;
const STABILITY_API_KEY = process.env.STABILITY_API_KEY;

console.log('🎨 第一步：高质量中文界面图片生成器');
console.log('Step 1: High-Quality Chinese Interface Image Generator\n');

// iFlytek Spark系统界面配置
const interfaceConfigs = [
    {
        filename: 'interface-complete-system.png',
        title: '系统完整演示界面',
        category: 'main_interface',
        midjourney_prompt: `Professional AI interview system main interface, Microsoft YaHei font rendering, crystal clear Chinese text "科大讯飞Spark智能面试评估系统" as prominent title, blue-purple gradient background (#667eea to #764ba2), modern corporate UI design, clean white buttons with clear Chinese labels "开始面试" "多模态分析" "能力评估" "生成报告", professional dashboard layout, high contrast text, enterprise-grade interface, photorealistic, ultra-detailed, 1920x1080 resolution --ar 16:9 --v 6 --style raw --quality 2`,
        dalle_prompt: `Create a professional AI interview system interface screenshot with Microsoft YaHei font displaying crystal clear Chinese text "科大讯飞Spark智能面试评估系统" as the main title. Blue-purple gradient background with modern corporate buttons labeled "开始面试", "多模态分析", "能力评估", "生成报告" in clean white text. Professional dashboard design, high contrast, enterprise-level quality, 1920x1080 resolution.`,
        stable_diffusion_prompt: `professional AI interview system interface, Microsoft YaHei font, Chinese text "科大讯飞Spark智能面试评估系统", blue gradient background, corporate UI design, clean buttons, high quality, detailed, 8k resolution`,
        quality_requirements: {
            text_clarity: 'ultra_high',
            font_sharpness: 'maximum',
            contrast_ratio: 'high',
            design_professionalism: 'enterprise_grade'
        }
    },
    {
        filename: 'interface-ai-architecture.png',
        title: 'AI技术架构界面',
        category: 'technical_interface',
        midjourney_prompt: `iFlytek Spark LLM technical architecture interface, Microsoft YaHei font, crystal clear Chinese title "AI技术架构", neural network visualization diagrams, technical components labeled "神经网络" "算法优化" "多模态融合" "深度学习", deep blue tech background with subtle grid pattern, white crisp text labels, professional technical visualization, modern sci-fi design aesthetic, high-tech atmosphere, detailed interface elements, glowing accents --ar 16:9 --v 6 --style raw --quality 2`,
        dalle_prompt: `Design a technical AI architecture interface for iFlytek Spark LLM with Microsoft YaHei font showing clear Chinese title "AI技术架构". Include neural network diagrams with technical labels "神经网络", "算法优化", "多模态融合", "深度学习" in white text on deep blue background. Professional technical visualization with modern design.`,
        stable_diffusion_prompt: `AI technical architecture interface, Microsoft YaHei font, Chinese technical labels, neural network diagrams, deep blue background, professional tech visualization, high quality`,
        quality_requirements: {
            technical_accuracy: 'high',
            text_clarity: 'ultra_high',
            diagram_quality: 'professional',
            color_scheme: 'tech_blue'
        }
    },
    {
        filename: 'interface-case-analysis.png',
        title: '案例分析界面',
        category: 'analysis_interface',
        midjourney_prompt: `Interview case analysis interface, Microsoft YaHei font, clear Chinese title "面试案例分析", split-screen layout showing candidate evaluation, professional position labels "AI工程师" "大数据分析师" "IoT开发者" "算法工程师", assessment metrics dashboard, evaluation charts and graphs, corporate interview environment design, professional color scheme, clean modern layout --ar 16:9 --v 6 --style raw --quality 2`,
        dalle_prompt: `Create an interview case analysis interface with Microsoft YaHei font displaying clear Chinese title "面试案例分析". Split-screen layout with position labels "AI工程师", "大数据分析师", "IoT开发者", "算法工程师". Professional assessment dashboard with evaluation metrics and charts.`,
        stable_diffusion_prompt: `interview case analysis interface, Microsoft YaHei font, Chinese labels, split-screen layout, professional dashboard, assessment metrics, corporate design`,
        quality_requirements: {
            layout_professionalism: 'high',
            text_readability: 'maximum',
            chart_clarity: 'professional',
            color_harmony: 'corporate'
        }
    },
    {
        filename: 'interface-bigdata-analysis.png',
        title: '大数据分析界面',
        category: 'data_interface',
        midjourney_prompt: `Big data analysis interface, Microsoft YaHei font, clear Chinese title "大数据分析技术", data visualization dashboard with charts and graphs, technical labels "机器学习" "数据挖掘" "实时分析" "数据仓库", blue data-themed color scheme, professional analytics interface, modern data science design, interactive elements, statistical displays --ar 16:9 --v 6 --style raw --quality 2`,
        dalle_prompt: `Design a big data analysis interface with Microsoft YaHei font showing clear Chinese title "大数据分析技术". Include data visualization charts with technical labels "机器学习", "数据挖掘", "实时分析", "数据仓库". Blue data-themed interface with professional analytics design.`,
        stable_diffusion_prompt: `big data analysis interface, Microsoft YaHei font, Chinese data labels, visualization charts, blue theme, professional analytics design`,
        quality_requirements: {
            data_visualization: 'professional',
            text_clarity: 'high',
            chart_accuracy: 'technical',
            color_scheme: 'data_blue'
        }
    },
    {
        filename: 'interface-iot-systems.png',
        title: 'IoT物联网界面',
        category: 'iot_interface',
        midjourney_prompt: `IoT Internet of Things interface, Microsoft YaHei font, clear Chinese title "物联网技术架构", IoT network topology visualization, technical components labeled "传感器网络" "嵌入式系统" "边缘计算" "智能设备", green tech-themed color scheme, connected devices illustration, network flow visualization, modern IoT design aesthetic --ar 16:9 --v 6 --style raw --quality 2`,
        dalle_prompt: `Create an IoT technology interface with Microsoft YaHei font displaying clear Chinese title "物联网技术架构". Show IoT network topology with technical labels "传感器网络", "嵌入式系统", "边缘计算", "智能设备". Green tech-themed design with connected devices visualization.`,
        stable_diffusion_prompt: `IoT technology interface, Microsoft YaHei font, Chinese IoT labels, network topology, green tech theme, connected devices, professional design`,
        quality_requirements: {
            network_visualization: 'clear',
            text_clarity: 'high',
            technical_accuracy: 'professional',
            color_scheme: 'iot_green'
        }
    }
];

// 生成单个界面图片
async function generateInterfaceImage(config, platform = 'midjourney') {
    console.log(`🎨 开始生成: ${config.title}`);
    console.log(`📱 界面类型: ${config.category}`);
    console.log(`🎯 平台: ${platform.toUpperCase()}`);
    
    try {
        let prompt, apiEndpoint, headers, requestData;
        
        switch (platform.toLowerCase()) {
            case 'midjourney':
                if (!MIDJOURNEY_API_KEY) {
                    throw new Error('Midjourney API密钥未设置');
                }
                prompt = config.midjourney_prompt;
                apiEndpoint = 'https://api.midjourney.com/v1/imagine';
                headers = {
                    'Authorization': `Bearer ${MIDJOURNEY_API_KEY}`,
                    'Content-Type': 'application/json'
                };
                requestData = {
                    prompt: prompt,
                    aspect_ratio: '16:9',
                    version: 6,
                    style: 'raw',
                    quality: 2
                };
                break;
                
            case 'dalle':
                if (!DALLE_API_KEY) {
                    throw new Error('DALL-E API密钥未设置');
                }
                prompt = config.dalle_prompt;
                apiEndpoint = 'https://api.openai.com/v1/images/generations';
                headers = {
                    'Authorization': `Bearer ${DALLE_API_KEY}`,
                    'Content-Type': 'application/json'
                };
                requestData = {
                    prompt: prompt,
                    n: 4, // 生成4张图片供选择
                    size: '1792x1024', // 接近16:9比例
                    quality: 'hd',
                    style: 'natural'
                };
                break;
                
            case 'stable_diffusion':
                if (!STABILITY_API_KEY) {
                    throw new Error('Stability AI API密钥未设置');
                }
                prompt = config.stable_diffusion_prompt;
                apiEndpoint = 'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image';
                headers = {
                    'Authorization': `Bearer ${STABILITY_API_KEY}`,
                    'Content-Type': 'application/json'
                };
                requestData = {
                    text_prompts: [
                        {
                            text: prompt,
                            weight: 1
                        },
                        {
                            text: 'blurry text, pixelated fonts, low quality, distorted characters, poor UI design',
                            weight: -1
                        }
                    ],
                    cfg_scale: 7,
                    height: 1024,
                    width: 1792,
                    samples: 4,
                    steps: 50
                };
                break;
                
            default:
                throw new Error(`不支持的平台: ${platform}`);
        }
        
        console.log(`📝 提示词: ${prompt.substring(0, 100)}...`);
        console.log('🚀 发送生成请求...');
        
        // 模拟API调用（实际使用时替换为真实API）
        const response = await simulateImageGeneration(config, platform, requestData);
        
        console.log('✅ 图片生成请求成功提交');
        console.log(`📋 任务ID: ${response.task_id}`);
        console.log(`🎯 质量要求: ${JSON.stringify(config.quality_requirements)}`);
        
        return {
            filename: config.filename,
            title: config.title,
            category: config.category,
            platform: platform,
            task_id: response.task_id,
            quality_requirements: config.quality_requirements,
            status: 'submitted',
            estimated_completion: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5分钟后
        };
        
    } catch (error) {
        console.error(`❌ 生成 ${config.title} 失败:`, error.message);
        return null;
    }
}

// 模拟图片生成API调用
async function simulateImageGeneration(config, platform, requestData) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    return {
        task_id: `img_${platform}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        status: 'submitted',
        platform: platform,
        estimated_completion: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
        images_count: platform === 'dalle' ? 4 : (platform === 'stable_diffusion' ? 4 : 1)
    };
}

// 批量生成所有界面图片
async function generateAllInterfaceImages(platforms = ['midjourney']) {
    console.log('🎨 第一步：高质量中文界面图片生成器启动\n');
    
    // 检查API密钥
    const availablePlatforms = [];
    if (MIDJOURNEY_API_KEY) availablePlatforms.push('midjourney');
    if (DALLE_API_KEY) availablePlatforms.push('dalle');
    if (STABILITY_API_KEY) availablePlatforms.push('stable_diffusion');
    
    if (availablePlatforms.length === 0) {
        console.log('⚠️  未检测到任何API密钥，将使用模拟模式');
        console.log('请设置以下环境变量之一:');
        console.log('export MIDJOURNEY_API_KEY="your_key"');
        console.log('export DALLE_API_KEY="your_key"');
        console.log('export STABILITY_API_KEY="your_key"\n');
        availablePlatforms.push('midjourney'); // 模拟模式
    }
    
    console.log(`🔧 可用平台: ${availablePlatforms.join(', ')}`);
    console.log(`📱 界面数量: ${interfaceConfigs.length} 个`);
    console.log(`🎯 目标: iFlytek Spark多模态面试评估系统界面\n`);
    
    const allTasks = [];
    
    // 为每个平台生成图片
    for (const platform of platforms.filter(p => availablePlatforms.includes(p))) {
        console.log(`\n🚀 使用 ${platform.toUpperCase()} 平台生成图片...\n`);
        
        for (const config of interfaceConfigs) {
            const task = await generateInterfaceImage(config, platform);
            if (task) {
                allTasks.push(task);
            }
            
            // 避免API限流
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log(`\n📊 生成结果: ${allTasks.length} 个图片生成任务已提交`);
    
    if (allTasks.length > 0) {
        console.log('\n📋 任务列表:');
        allTasks.forEach(task => {
            console.log(`  🎨 ${task.title} (${task.filename})`);
            console.log(`     平台: ${task.platform.toUpperCase()}`);
            console.log(`     任务ID: ${task.task_id}`);
            console.log(`     质量要求: ${Object.keys(task.quality_requirements).join(', ')}`);
        });
        
        // 保存任务信息
        const taskInfo = {
            step: 1,
            purpose: 'High-quality Chinese interface image generation',
            generated_at: new Date().toISOString(),
            total_interfaces: interfaceConfigs.length,
            platforms_used: [...new Set(allTasks.map(t => t.platform))],
            tasks: allTasks
        };
        
        fs.writeFileSync('step1-image-generation-tasks.json', JSON.stringify(taskInfo, null, 2));
        console.log('\n📄 任务信息已保存到 step1-image-generation-tasks.json');
        
        console.log('\n⏳ 图片生成中，预计5-10分钟完成');
        console.log('使用以下命令检查状态:');
        console.log('node step1-image-generator.js --check');
    }
}

// 检查图片生成状态
async function checkImageGenerationStatus() {
    console.log('🔍 检查图片生成状态...\n');
    
    if (!fs.existsSync('step1-image-generation-tasks.json')) {
        console.log('❌ 未找到任务记录文件');
        return;
    }
    
    const taskInfo = JSON.parse(fs.readFileSync('step1-image-generation-tasks.json', 'utf8'));
    
    console.log(`📅 任务创建时间: ${taskInfo.generated_at}`);
    console.log(`🎯 任务目的: ${taskInfo.purpose}`);
    console.log(`📱 界面数量: ${taskInfo.total_interfaces}`);
    console.log(`🔧 使用平台: ${taskInfo.platforms_used.join(', ')}`);
    console.log(`📝 任务总数: ${taskInfo.tasks.length}\n`);
    
    console.log('📋 详细状态:');
    taskInfo.tasks.forEach(task => {
        console.log(`\n🎨 ${task.title}`);
        console.log(`   文件: ${task.filename}`);
        console.log(`   平台: ${task.platform.toUpperCase()}`);
        console.log(`   任务ID: ${task.task_id}`);
        console.log(`   状态: 生成中... (请到对应平台查看详细状态)`);
        console.log(`   质量要求: ${Object.entries(task.quality_requirements).map(([k,v]) => `${k}:${v}`).join(', ')}`);
    });
    
    console.log('\n✅ 质量检查清单:');
    console.log('   [ ] 中文字符完整清晰，无乱码');
    console.log('   [ ] 字体边缘锐利，无模糊');
    console.log('   [ ] 界面布局专业，符合企业标准');
    console.log('   [ ] 颜色对比度充足');
    console.log('   [ ] 分辨率达到1920x1080');
    console.log('   [ ] 整体设计风格统一');
    
    console.log('\n📊 下一步操作:');
    console.log('   1. 下载生成的图片到 ./generated-images/ 目录');
    console.log('   2. 运行质量验证: node validate-image-quality.js');
    console.log('   3. 选择最佳图片: node select-best-images.js');
    console.log('   4. 进入第二步: node step2-video-generator.js');
}

// 主程序
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--check')) {
        checkImageGenerationStatus();
    } else if (args.includes('--platforms')) {
        const platformIndex = args.indexOf('--platforms');
        const platforms = args[platformIndex + 1] ? args[platformIndex + 1].split(',') : ['midjourney'];
        generateAllInterfaceImages(platforms);
    } else {
        generateAllInterfaceImages(['midjourney']); // 默认使用Midjourney
    }
}

module.exports = {
    generateInterfaceImage,
    generateAllInterfaceImages,
    checkImageGenerationStatus,
    interfaceConfigs
};
