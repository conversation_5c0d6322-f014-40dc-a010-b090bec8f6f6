<p align="center">
  <a href="https://www.npmjs.com/dagre-d3-es"><img src="https://img.shields.io/npm/v/dagre-d3-es.svg?logo=npm&logoColor=fff&label=NPM+package&color=limegreen" alt="dagre-d3-es on npm" /></a>&nbsp;
  <a href="https://www.npmjs.com/dagre-d3-es"><img src="https://img.shields.io/npm/dw/dagre-d3-es.svg?logo=npm&logoColor=fff&label=NPM+package&color=limegreen" alt="dagre-d3-es on npm" /></a>
</p>

# dagre-d3-es

[dagre-d3-es](https://www.npmjs.com/package/dagre-d3-es) is a fork of [dagre-d3](https://github.com/dagrejs) using the more modern ES6 javascript syntax.

It uses [ES](https://262.ecma-international.org/6.0/) modules, thus the name [dagre-d3-es](https://www.npmjs.com/package/dagre-d3-es).

[dagre-d3-es](https://www.npmjs.com/package/dagre-d3-es) follows [d3](https://www.npmjs.com/package/d3) versions. Ex: dagre-d3-es version 7 depends on [d3](https://www.npmjs.com/package/d3) version 7.

## Demos

[Simple graph demo](https://codesandbox.io/s/dagre-d3-es-tree-9ywg9) using react, dagre-d3-es.

Clone and run [dagre-es-example](https://github.com/tbo47/dagre-es-example) using angular.

## Install

```
npm install dagre-d3-es
```

## Code example

Coming from the legacy `dagre-d3`, the main changes in your code will be:

```
import * as dagreD3 from 'dagre-d3-es';
...
const g = new dagreD3.graphlib.Graph().setGraph({});
...
const zoom = d3.zoom().on('zoom', (zoomEvent) => {
  inner.attr('transform', zoomEvent.transform);
});

```
