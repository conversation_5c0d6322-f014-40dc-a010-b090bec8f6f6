import Skeleton from './src/skeleton2.mjs';
import SkeletonItem from './src/skeleton-item.mjs';
export { skeletonProps } from './src/skeleton.mjs';
export { skeletonItemProps } from './src/skeleton-item2.mjs';
import { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';

const ElSkeleton = withInstall(Skeleton, {
  SkeletonItem
});
const ElSkeletonItem = withNoopInstall(SkeletonItem);

export { ElSkeleton, ElSkeletonItem, ElSkeleton as default };
//# sourceMappingURL=index.mjs.map
