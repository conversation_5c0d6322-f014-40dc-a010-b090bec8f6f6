#!/usr/bin/env node

/**
 * iFlytek星火智能面试系统启动脚本
 * 跨平台启动脚本，支持Windows、macOS、Linux
 */

import { spawn, exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const execAsync = promisify(exec)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

class IflytekSystemStarter {
  constructor() {
    this.isWindows = process.platform === 'win32'
    this.projectRoot = __dirname
    this.defaultPort = 8080
  }

  /**
   * 🎯 主启动方法
   */
  async start() {
    console.log('\n========================================')
    console.log('   iFlytek星火智能面试系统启动脚本')
    console.log('========================================\n')

    try {
      await this.checkEnvironment()
      await this.checkDependencies()
      await this.checkPort()
      await this.startDevServer()
    } catch (error) {
      console.error('❌ 启动失败:', error.message)
      process.exit(1)
    }
  }

  /**
   * 🔍 检查环境
   */
  async checkEnvironment() {
    console.log('[1/4] 检查Node.js环境...')
    
    try {
      const { stdout } = await execAsync('node --version')
      const nodeVersion = stdout.trim()
      console.log(`✅ Node.js版本: ${nodeVersion}`)
      
      // 检查Node.js版本是否满足要求
      const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0])
      if (majorVersion < 16) {
        throw new Error('Node.js版本过低，请升级到16.0.0或更高版本')
      }
    } catch (error) {
      throw new Error('未检测到Node.js，请先安装Node.js (https://nodejs.org/)')
    }
  }

  /**
   * 📦 检查依赖
   */
  async checkDependencies() {
    console.log('\n[2/4] 检查项目依赖...')
    
    const nodeModulesPath = path.join(this.projectRoot, 'node_modules')
    const packageJsonPath = path.join(this.projectRoot, 'package.json')
    
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error('未找到package.json文件，请确认在正确的项目目录中运行')
    }

    if (!fs.existsSync(nodeModulesPath)) {
      console.log('📦 正在安装项目依赖...')
      await this.installDependencies()
    } else {
      console.log('✅ 项目依赖已存在')
      
      // 检查关键依赖是否存在
      const criticalDeps = ['vue', 'element-plus', 'vite']
      for (const dep of criticalDeps) {
        const depPath = path.join(nodeModulesPath, dep)
        if (!fs.existsSync(depPath)) {
          console.log(`⚠️  关键依赖 ${dep} 缺失，重新安装依赖...`)
          await this.installDependencies()
          break
        }
      }
    }
  }

  /**
   * 📦 安装依赖
   */
  async installDependencies() {
    return new Promise((resolve, reject) => {
      const npmCommand = this.isWindows ? 'npm.cmd' : 'npm'
      const installProcess = spawn(npmCommand, ['install'], {
        cwd: this.projectRoot,
        stdio: 'inherit'
      })

      installProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 依赖安装完成')
          resolve()
        } else {
          reject(new Error('依赖安装失败，请检查网络连接或尝试手动运行 npm install'))
        }
      })

      installProcess.on('error', (error) => {
        reject(new Error(`依赖安装失败: ${error.message}`))
      })
    })
  }

  /**
   * 🔌 检查端口
   */
  async checkPort() {
    console.log('\n[3/4] 检查端口占用...')
    
    try {
      const command = this.isWindows 
        ? `netstat -an | findstr :${this.defaultPort}`
        : `lsof -i :${this.defaultPort}`
      
      await execAsync(command)
      console.log(`⚠️  警告：端口${this.defaultPort}已被占用，Vite将自动选择其他可用端口`)
    } catch (error) {
      console.log(`✅ 端口${this.defaultPort}可用`)
    }
  }

  /**
   * 🚀 启动开发服务器
   */
  async startDevServer() {
    console.log('\n[4/4] 启动开发服务器...')
    console.log('🚀 正在启动iFlytek面试系统...\n')
    
    this.printAccessInfo()
    
    return new Promise((resolve, reject) => {
      const npmCommand = this.isWindows ? 'npm.cmd' : 'npm'
      const devProcess = spawn(npmCommand, ['run', 'dev'], {
        cwd: this.projectRoot,
        stdio: 'inherit'
      })

      devProcess.on('close', (code) => {
        console.log('\n服务器已停止')
        resolve()
      })

      devProcess.on('error', (error) => {
        reject(new Error(`启动失败: ${error.message}`))
      })

      // 处理Ctrl+C信号
      process.on('SIGINT', () => {
        console.log('\n正在停止服务器...')
        devProcess.kill('SIGINT')
      })
    })
  }

  /**
   * 📍 打印访问信息
   */
  printAccessInfo() {
    console.log('📍 访问地址：')
    console.log(`   - 主页：http://localhost:${this.defaultPort}`)
    console.log(`   - 系统测试：http://localhost:${this.defaultPort}/system-test`)
    console.log(`   - 面试页面：http://localhost:${this.defaultPort}/text-interview`)
    console.log(`   - 报告中心：http://localhost:${this.defaultPort}/report-center`)
    console.log(`   - 报告详情：http://localhost:${this.defaultPort}/report/1`)
    console.log('\n💡 提示：')
    console.log('   - 按 Ctrl+C 停止服务器')
    console.log('   - 修改代码后会自动热重载')
    console.log('   - 如遇问题请查看控制台错误信息')
    console.log('   - 首次启动可能需要较长时间编译')
    console.log('\n🔧 新功能测试：')
    console.log('   - AI智能提示：在面试页面点击"AI提示"按钮')
    console.log('   - 报告导出：在报告中心点击"下载"或"批量导出"')
    console.log('   - 报告分享：在报告详情页点击"分享报告"')
    console.log('   - 系统测试：访问/system-test页面运行完整测试')
    console.log('\n')
  }

  /**
   * 🆘 显示帮助信息
   */
  static showHelp() {
    console.log('\niFlytek星火智能面试系统启动脚本')
    console.log('\n使用方法：')
    console.log('  node start-iflytek-system.js     # 启动开发服务器')
    console.log('  npm run dev                      # 使用npm启动')
    console.log('\n故障排除：')
    console.log('  1. 确保已安装Node.js 16+')
    console.log('  2. 删除node_modules文件夹后重新运行')
    console.log('  3. 检查网络连接是否正常')
    console.log('  4. 确认在frontend目录中运行脚本')
    console.log('\n更多帮助：')
    console.log('  - 查看FEATURE_VALIDATION.md了解功能详情')
    console.log('  - 访问/system-test页面进行功能测试')
  }
}

// 处理命令行参数
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  IflytekSystemStarter.showHelp()
  process.exit(0)
}

// 启动系统
const starter = new IflytekSystemStarter()
starter.start().catch((error) => {
  console.error('\n❌ 启动失败:', error.message)
  console.log('\n💡 尝试以下解决方案：')
  console.log('   1. 删除node_modules文件夹后重新运行')
  console.log('   2. 运行 npm install 手动安装依赖')
  console.log('   3. 检查Node.js版本是否为16+')
  console.log('   4. 使用 --help 参数查看更多帮助')
  process.exit(1)
})
