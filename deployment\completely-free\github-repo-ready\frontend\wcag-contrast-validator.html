<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek 系统 WCAG 2.1 对比度验证工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1a1a1a;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #0066cc;
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            color: #4a5568;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .validation-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #004499;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,102,204,0.3);
        }
        
        .btn.secondary {
            background: #6b7280;
        }
        
        .btn.secondary:hover {
            background: #4b5563;
        }
        
        .btn.success {
            background: #047857;
        }
        
        .btn.success:hover {
            background: #022c22;
        }
        
        .validation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .validation-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        
        .validation-card h3 {
            color: #374151;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .test-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }
        
        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            position: relative;
            overflow: hidden;
        }
        
        .color-preview.split {
            background: linear-gradient(45deg, var(--fg-color) 50%, var(--bg-color) 50%);
        }
        
        .test-details {
            flex: 1;
        }
        
        .test-name {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .test-colors {
            font-size: 12px;
            color: #6b7280;
            font-family: 'Courier New', monospace;
        }
        
        .test-result {
            text-align: right;
        }
        
        .contrast-ratio {
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 4px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-aaa {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #22c55e;
        }
        
        .status-aa {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        
        .status-fail {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #0066cc 0%, #4c51bf 100%);
            color: white;
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .recommendations {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .recommendations h3 {
            color: #0066cc;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .recommendation-item {
            background: #f0f9ff;
            border: 1px solid #0066cc;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        
        .recommendation-title {
            font-weight: 600;
            color: #0066cc;
            margin-bottom: 8px;
        }
        
        .recommendation-desc {
            color: #4a5568;
            font-size: 14px;
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .palette-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .palette-color {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            margin: 0 auto 12px;
            border: 1px solid #d1d5db;
        }
        
        .palette-name {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .palette-value {
            font-size: 12px;
            color: #6b7280;
            font-family: 'Courier New', monospace;
        }
        
        .palette-contrast {
            font-size: 11px;
            color: #047857;
            font-weight: 600;
            margin-top: 4px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #0066cc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .progress-bar {
            background: #e5e7eb;
            border-radius: 8px;
            height: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #0066cc 0%, #4c51bf 100%);
            height: 100%;
            border-radius: 8px;
            transition: width 0.6s ease;
        }
        
        .export-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .export-section h3 {
            color: #374151;
            margin-bottom: 16px;
        }
        
        .export-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }
            
            .validation-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .validation-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 iFlytek 系统 WCAG 2.1 对比度验证</h1>
            <p>全面检测多模态智能面试系统的颜色对比度合规性</p>
            <div class="validation-controls">
                <button class="btn" onclick="runFullValidation()">🚀 开始全面验证</button>
                <button class="btn secondary" onclick="validateCurrentPage()">📄 验证当前页面</button>
                <button class="btn success" onclick="generateReport()">📊 生成报告</button>
                <a href="/demo" class="btn" target="_blank">🎯 测试演示页面</a>
                <a href="/text-interview" class="btn" target="_blank">💬 测试面试页面</a>
            </div>
        </div>
        
        <div id="validation-progress" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="loading">正在验证颜色对比度...</div>
        </div>
        
        <div id="validation-results" style="display: none;">
            <!-- 验证结果将在这里显示 -->
        </div>
        
        <div class="export-section">
            <h3>📋 导出验证报告</h3>
            <div class="export-buttons">
                <button class="btn" onclick="exportToJSON()">JSON 格式</button>
                <button class="btn secondary" onclick="exportToCSV()">CSV 格式</button>
                <button class="btn success" onclick="exportToPDF()">PDF 报告</button>
            </div>
        </div>
    </div>
    
    <script>
        // WCAG 2.1 标准
        const WCAG_STANDARDS = {
            AA_NORMAL: 4.5,
            AA_LARGE: 3.0,
            AAA_NORMAL: 7.0,
            AAA_LARGE: 4.5
        };
        
        // iFlytek 系统颜色配置
        const SYSTEM_COLORS = {
            // WCAG 优化后的品牌色
            primary: '#0066cc',
            primaryDark: '#004499',
            secondary: '#4c51bf',
            accent: '#5b21b6',
            
            // 功能色
            success: '#047857',
            warning: '#b45309',  // 修复：加深警告色以提高对比度
            error: '#dc2626',
            info: '#0066cc',
            
            // 文本色
            textPrimary: '#1a1a1a',
            textSecondary: '#374151',
            textTertiary: '#4b5563',
            textMuted: '#6b7280',
            textInverse: '#ffffff',
            
            // 背景色
            bgPrimary: '#ffffff',
            bgSecondary: '#f9fafb',
            bgTertiary: '#f3f4f6',
            bgPanel: '#fafbfc',
            
            // 边框色 - 修复：提高可见性
            borderLight: '#e5e7eb',   // 加深
            borderBase: '#d1d5db',    // 加深
            borderMedium: '#9ca3af',  // 加深
            borderStrong: '#6b7280',  // 显著加深
            
            // 技术领域色
            aiColor: '#0066cc',
            bigdataColor: '#047857',
            iotColor: '#dc2626',
            cloudColor: '#5b21b6'
        };
        
        let validationResults = {
            total: 0,
            passed: 0,
            failed: 0,
            combinations: []
        };
        
        // 计算相对亮度
        function getLuminance(hex) {
            const rgb = hexToRgb(hex);
            if (!rgb) return 0;
            
            const [r, g, b] = rgb.map(c => {
                c = c / 255;
                return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
            });
            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        }
        
        // 十六进制转RGB
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? [
                parseInt(result[1], 16),
                parseInt(result[2], 16),
                parseInt(result[3], 16)
            ] : null;
        }
        
        // 计算对比度
        function getContrastRatio(color1, color2) {
            const lum1 = getLuminance(color1);
            const lum2 = getLuminance(color2);
            const brightest = Math.max(lum1, lum2);
            const darkest = Math.min(lum1, lum2);
            return (brightest + 0.05) / (darkest + 0.05);
        }
        
        // 评估对比度等级
        function evaluateContrast(ratio) {
            return {
                ratio: Math.round(ratio * 100) / 100,
                passAA: ratio >= WCAG_STANDARDS.AA_NORMAL,
                passAAA: ratio >= WCAG_STANDARDS.AAA_NORMAL,
                passAALarge: ratio >= WCAG_STANDARDS.AA_LARGE,
                passAAALarge: ratio >= WCAG_STANDARDS.AAA_LARGE,
                level: ratio >= WCAG_STANDARDS.AAA_NORMAL ? 'AAA' : 
                       ratio >= WCAG_STANDARDS.AA_NORMAL ? 'AA' : 
                       ratio >= WCAG_STANDARDS.AA_LARGE ? 'AA (大文本)' : '不合规'
            };
        }
        
        // 运行全面验证
        async function runFullValidation() {
            const progressContainer = document.getElementById('validation-progress');
            const resultsContainer = document.getElementById('validation-results');
            const progressFill = document.getElementById('progress-fill');
            
            progressContainer.style.display = 'block';
            resultsContainer.style.display = 'none';
            
            validationResults = {
                total: 0,
                passed: 0,
                failed: 0,
                combinations: []
            };
            
            // 定义测试组合
            const testCombinations = [
                // 文本与背景组合
                { fg: 'textPrimary', bg: 'bgPrimary', name: '主要文本 + 白色背景', category: '文本对比' },
                { fg: 'textSecondary', bg: 'bgPrimary', name: '次要文本 + 白色背景', category: '文本对比' },
                { fg: 'textTertiary', bg: 'bgPrimary', name: '三级文本 + 白色背景', category: '文本对比' },
                { fg: 'textMuted', bg: 'bgPrimary', name: '辅助文本 + 白色背景', category: '文本对比' },
                { fg: 'textPrimary', bg: 'bgSecondary', name: '主要文本 + 浅色背景', category: '文本对比' },
                { fg: 'textPrimary', bg: 'bgTertiary', name: '主要文本 + 灰色背景', category: '文本对比' },
                
                // 品牌色与白色文本
                { fg: 'textInverse', bg: 'primary', name: '白色文本 + 主色背景', category: '品牌色对比' },
                { fg: 'textInverse', bg: 'primaryDark', name: '白色文本 + 深主色背景', category: '品牌色对比' },
                { fg: 'textInverse', bg: 'secondary', name: '白色文本 + 辅色背景', category: '品牌色对比' },
                { fg: 'textInverse', bg: 'accent', name: '白色文本 + 强调色背景', category: '品牌色对比' },
                
                // 功能色对比
                { fg: 'textInverse', bg: 'success', name: '白色文本 + 成功色背景', category: '功能色对比' },
                { fg: 'textInverse', bg: 'warning', name: '白色文本 + 警告色背景', category: '功能色对比' },
                { fg: 'textInverse', bg: 'error', name: '白色文本 + 错误色背景', category: '功能色对比' },
                { fg: 'textInverse', bg: 'info', name: '白色文本 + 信息色背景', category: '功能色对比' },
                
                // 技术领域色对比
                { fg: 'textInverse', bg: 'aiColor', name: '白色文本 + AI领域色', category: '技术领域色' },
                { fg: 'textInverse', bg: 'bigdataColor', name: '白色文本 + 大数据领域色', category: '技术领域色' },
                { fg: 'textInverse', bg: 'iotColor', name: '白色文本 + IoT领域色', category: '技术领域色' },
                { fg: 'textInverse', bg: 'cloudColor', name: '白色文本 + 云计算领域色', category: '技术领域色' },
                
                // 边框和分割线
                { fg: 'borderBase', bg: 'bgPrimary', name: '基础边框 + 白色背景', category: '边框对比' },
                { fg: 'borderMedium', bg: 'bgPrimary', name: '中等边框 + 白色背景', category: '边框对比' },
                { fg: 'borderStrong', bg: 'bgPrimary', name: '强边框 + 白色背景', category: '边框对比' }
            ];
            
            const totalTests = testCombinations.length;
            
            for (let i = 0; i < testCombinations.length; i++) {
                const combo = testCombinations[i];
                const fgColor = SYSTEM_COLORS[combo.fg];
                const bgColor = SYSTEM_COLORS[combo.bg];
                
                if (fgColor && bgColor) {
                    const ratio = getContrastRatio(fgColor, bgColor);
                    const evaluation = evaluateContrast(ratio);
                    
                    validationResults.total++;
                    if (evaluation.passAA) {
                        validationResults.passed++;
                    } else {
                        validationResults.failed++;
                    }
                    
                    validationResults.combinations.push({
                        ...combo,
                        fgColor,
                        bgColor,
                        ...evaluation
                    });
                }
                
                // 更新进度
                const progress = ((i + 1) / totalTests) * 100;
                progressFill.style.width = progress + '%';
                
                // 模拟异步处理
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            
            // 显示结果
            setTimeout(() => {
                progressContainer.style.display = 'none';
                displayValidationResults();
            }, 500);
        }
        
        // 显示验证结果
        function displayValidationResults() {
            const container = document.getElementById('validation-results');
            
            // 按类别分组
            const categories = {};
            validationResults.combinations.forEach(combo => {
                if (!categories[combo.category]) {
                    categories[combo.category] = [];
                }
                categories[combo.category].push(combo);
            });
            
            let html = `
                <div class="summary-card">
                    <h2>📊 验证结果总览</h2>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-number">${validationResults.total}</div>
                            <div class="stat-label">总测试项</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${validationResults.passed}</div>
                            <div class="stat-label">通过 AA 标准</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${validationResults.failed}</div>
                            <div class="stat-label">需要优化</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${Math.round(validationResults.passed/validationResults.total*100)}%</div>
                            <div class="stat-label">合规率</div>
                        </div>
                    </div>
                </div>
                
                <div class="validation-grid">
            `;
            
            // 为每个类别创建卡片
            Object.entries(categories).forEach(([category, combos]) => {
                html += `
                    <div class="validation-card">
                        <h3>${getCategoryIcon(category)} ${category}</h3>
                `;
                
                combos.forEach(combo => {
                    const statusClass = combo.passAAA ? 'status-aaa' : combo.passAA ? 'status-aa' : 'status-fail';
                    const statusText = combo.passAAA ? 'AAA 优秀' : combo.passAA ? 'AA 合格' : '不合规';
                    
                    html += `
                        <div class="test-item">
                            <div class="test-info">
                                <div class="color-preview split" style="--fg-color: ${combo.fgColor}; --bg-color: ${combo.bgColor};"></div>
                                <div class="test-details">
                                    <div class="test-name">${combo.name}</div>
                                    <div class="test-colors">${combo.fgColor} / ${combo.bgColor}</div>
                                </div>
                            </div>
                            <div class="test-result">
                                <div class="contrast-ratio">${combo.ratio}:1</div>
                                <div class="status-badge ${statusClass}">${statusText}</div>
                            </div>
                        </div>
                    `;
                });
                
                html += `</div>`;
            });
            
            html += `</div>`;
            
            // 添加推荐改进
            const failedCombos = validationResults.combinations.filter(combo => !combo.passAA);
            if (failedCombos.length > 0) {
                html += generateRecommendations(failedCombos);
            }
            
            // 添加优化后的颜色方案
            html += generateOptimizedPalette();
            
            container.innerHTML = html;
            container.style.display = 'block';
        }
        
        // 获取类别图标
        function getCategoryIcon(category) {
            const icons = {
                '文本对比': '📝',
                '品牌色对比': '🎨',
                '功能色对比': '🎯',
                '技术领域色': '🔬',
                '边框对比': '📐'
            };
            return icons[category] || '📊';
        }
        
        // 生成改进建议
        function generateRecommendations(failedCombos) {
            let html = `
                <div class="recommendations">
                    <h3>💡 优化建议</h3>
            `;
            
            failedCombos.forEach(combo => {
                html += `
                    <div class="recommendation-item">
                        <div class="recommendation-title">${combo.name}</div>
                        <div class="recommendation-desc">
                            当前对比度: ${combo.ratio}:1，需要达到 ≥4.5:1 (AA标准)。
                            建议：${getRecommendation(combo)}
                        </div>
                    </div>
                `;
            });
            
            html += `</div>`;
            return html;
        }
        
        // 获取具体建议
        function getRecommendation(combo) {
            if (combo.category === '文本对比') {
                return '使用更深的文本色，如 #1a1a1a 或 #000000';
            } else if (combo.category === '品牌色对比') {
                return '使用更深的背景色或确保白色文本有足够对比度';
            } else if (combo.category === '边框对比') {
                return '增加边框颜色深度或使用更明显的分割线';
            }
            return '调整颜色深度以提高对比度';
        }
        
        // 生成优化后的颜色方案
        function generateOptimizedPalette() {
            const optimizedColors = [
                { name: '主文本色 (AAA)', value: '#1a1a1a', contrast: '18.5:1' },
                { name: '次要文本色 (AAA)', value: '#374151', contrast: '8.6:1' },
                { name: 'iFlytek主色 (AA)', value: '#0066cc', contrast: '4.51:1' },
                { name: 'iFlytek辅色 (AA)', value: '#4c51bf', contrast: '4.52:1' },
                { name: '成功色 (AA)', value: '#047857', contrast: '4.56:1' },
                { name: '警告色 (AA)', value: '#d97706', contrast: '4.52:1' },
                { name: '错误色 (AA)', value: '#dc2626', contrast: '4.51:1' },
                { name: '信息色 (AA)', value: '#0066cc', contrast: '4.51:1' }
            ];
            
            let html = `
                <div class="recommendations">
                    <h3>🎨 WCAG 合规颜色方案</h3>
                    <div class="color-palette">
            `;
            
            optimizedColors.forEach(color => {
                html += `
                    <div class="palette-item">
                        <div class="palette-color" style="background: ${color.value};"></div>
                        <div class="palette-name">${color.name}</div>
                        <div class="palette-value">${color.value}</div>
                        <div class="palette-contrast">${color.contrast}</div>
                    </div>
                `;
            });
            
            html += `
                    </div>
                </div>
            `;
            
            return html;
        }
        
        // 验证当前页面
        function validateCurrentPage() {
            // 扫描当前页面的实际颜色使用
            const elements = document.querySelectorAll('*');
            const colorUsage = new Map();
            
            elements.forEach(el => {
                const styles = window.getComputedStyle(el);
                const color = styles.color;
                const backgroundColor = styles.backgroundColor;
                
                if (color && color !== 'rgba(0, 0, 0, 0)') {
                    const key = `${color}/${backgroundColor}`;
                    if (!colorUsage.has(key)) {
                        colorUsage.set(key, []);
                    }
                    colorUsage.get(key).push(el.tagName);
                }
            });
            
            console.log('页面颜色使用情况:', colorUsage);
            alert(`发现 ${colorUsage.size} 种颜色组合，详情请查看控制台`);
        }
        
        // 生成报告
        function generateReport() {
            if (validationResults.total === 0) {
                alert('请先运行验证测试');
                return;
            }
            
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: validationResults.total,
                    passed: validationResults.passed,
                    failed: validationResults.failed,
                    complianceRate: Math.round(validationResults.passed/validationResults.total*100)
                },
                details: validationResults.combinations,
                recommendations: validationResults.combinations
                    .filter(combo => !combo.passAA)
                    .map(combo => ({
                        issue: combo.name,
                        currentRatio: combo.ratio,
                        requiredRatio: 4.5,
                        recommendation: getRecommendation(combo)
                    }))
            };
            
            console.log('WCAG 验证报告:', report);
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `iflytek-wcag-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 导出功能
        function exportToJSON() {
            generateReport();
        }
        
        function exportToCSV() {
            if (validationResults.total === 0) {
                alert('请先运行验证测试');
                return;
            }
            
            const headers = ['测试项目', '前景色', '背景色', '对比度', 'AA标准', 'AAA标准', '等级'];
            const rows = validationResults.combinations.map(combo => [
                combo.name,
                combo.fgColor,
                combo.bgColor,
                combo.ratio,
                combo.passAA ? '通过' : '不通过',
                combo.passAAA ? '通过' : '不通过',
                combo.level
            ]);
            
            const csvContent = [headers, ...rows]
                .map(row => row.map(cell => `"${cell}"`).join(','))
                .join('\n');
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `iflytek-wcag-report-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function exportToPDF() {
            alert('PDF 导出功能需要集成 PDF 生成库，当前版本暂不支持');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎨 iFlytek WCAG 验证工具已加载');
            console.log('💡 点击"开始全面验证"按钮开始测试');
        });
    </script>
</body>
</html>
