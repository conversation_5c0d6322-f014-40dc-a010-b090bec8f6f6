{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@braintree+sanitize-url@7.1.0/node_modules/@braintree/sanitize-url/dist/constants.js", "../../../../../node_modules/.pnpm/@braintree+sanitize-url@7.1.0/node_modules/@braintree/sanitize-url/dist/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BLANK_URL = exports.relativeFirstCharacters = exports.whitespaceEscapeCharsRegex = exports.urlSchemeRegex = exports.ctrlCharactersRegex = exports.htmlCtrlEntityRegex = exports.htmlEntitiesRegex = exports.invalidProtocolRegex = void 0;\nexports.invalidProtocolRegex = /^([^\\w]*)(javascript|data|vbscript)/im;\nexports.htmlEntitiesRegex = /&#(\\w+)(^\\w|;)?/g;\nexports.htmlCtrlEntityRegex = /&(newline|tab);/gi;\nexports.ctrlCharactersRegex = /[\\u0000-\\u001F\\u007F-\\u009F\\u2000-\\u200D\\uFEFF]/gim;\nexports.urlSchemeRegex = /^.+(:|&colon;)/gim;\nexports.whitespaceEscapeCharsRegex = /(\\\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;\nexports.relativeFirstCharacters = [\".\", \"/\"];\nexports.BLANK_URL = \"about:blank\";\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sanitizeUrl = void 0;\nvar constants_1 = require(\"./constants\");\nfunction isRelativeUrlWithoutProtocol(url) {\n    return constants_1.relativeFirstCharacters.indexOf(url[0]) > -1;\n}\nfunction decodeHtmlCharacters(str) {\n    var removedNullByte = str.replace(constants_1.ctrlCharactersRegex, \"\");\n    return removedNullByte.replace(constants_1.htmlEntitiesRegex, function (match, dec) {\n        return String.fromCharCode(dec);\n    });\n}\nfunction isValidUrl(url) {\n    return URL.canParse(url);\n}\nfunction decodeURI(uri) {\n    try {\n        return decodeURIComponent(uri);\n    }\n    catch (e) {\n        // Ignoring error\n        // It is possible that the URI contains a `%` not associated\n        // with URI/URL-encoding.\n        return uri;\n    }\n}\nfunction sanitizeUrl(url) {\n    if (!url) {\n        return constants_1.BLANK_URL;\n    }\n    var charsToDecode;\n    var decodedUrl = decodeURI(url.trim());\n    do {\n        decodedUrl = decodeHtmlCharacters(decodedUrl)\n            .replace(constants_1.htmlCtrlEntityRegex, \"\")\n            .replace(constants_1.ctrlCharactersRegex, \"\")\n            .replace(constants_1.whitespaceEscapeCharsRegex, \"\")\n            .trim();\n        decodedUrl = decodeURI(decodedUrl);\n        charsToDecode =\n            decodedUrl.match(constants_1.ctrlCharactersRegex) ||\n                decodedUrl.match(constants_1.htmlEntitiesRegex) ||\n                decodedUrl.match(constants_1.htmlCtrlEntityRegex) ||\n                decodedUrl.match(constants_1.whitespaceEscapeCharsRegex);\n    } while (charsToDecode && charsToDecode.length > 0);\n    var sanitizedUrl = decodedUrl;\n    if (!sanitizedUrl) {\n        return constants_1.BLANK_URL;\n    }\n    if (isRelativeUrlWithoutProtocol(sanitizedUrl)) {\n        return sanitizedUrl;\n    }\n    // Remove any leading whitespace before checking the URL scheme\n    var trimmedUrl = sanitizedUrl.trimStart();\n    var urlSchemeParseResults = trimmedUrl.match(constants_1.urlSchemeRegex);\n    if (!urlSchemeParseResults) {\n        return sanitizedUrl;\n    }\n    var urlScheme = urlSchemeParseResults[0].toLowerCase().trim();\n    if (constants_1.invalidProtocolRegex.test(urlScheme)) {\n        return constants_1.BLANK_URL;\n    }\n    var backSanitized = trimmedUrl.replace(/\\\\/g, \"/\");\n    // Handle special cases for mailto: and custom deep-link protocols\n    if (urlScheme === \"mailto:\" || urlScheme.includes(\"://\")) {\n        return backSanitized;\n    }\n    // For http and https URLs, perform additional validation\n    if (urlScheme === \"http:\" || urlScheme === \"https:\") {\n        if (!isValidUrl(backSanitized)) {\n            return constants_1.BLANK_URL;\n        }\n        var url_1 = new URL(backSanitized);\n        url_1.protocol = url_1.protocol.toLowerCase();\n        url_1.hostname = url_1.hostname.toLowerCase();\n        return url_1.toString();\n    }\n    return backSanitized;\n}\nexports.sanitizeUrl = sanitizeUrl;\n"], "mappings": "gDAAA,IAAAA,EAAAC,EAAAC,GAAA,cACA,OAAO,eAAeA,EAAS,aAAc,CAAE,MAAO,EAAK,CAAC,EAC5DA,EAAQ,UAAYA,EAAQ,wBAA0BA,EAAQ,2BAA6BA,EAAQ,eAAiBA,EAAQ,oBAAsBA,EAAQ,oBAAsBA,EAAQ,kBAAoBA,EAAQ,qBAAuB,OAC3OA,EAAQ,qBAAuB,wCAC/BA,EAAQ,kBAAoB,mBAC5BA,EAAQ,oBAAsB,oBAC9BA,EAAQ,oBAAsB,qDAC9BA,EAAQ,eAAiB,oBACzBA,EAAQ,2BAA6B,uCACrCA,EAAQ,wBAA0B,CAAC,IAAK,GAAG,EAC3CA,EAAQ,UAAY,gBCVpB,IAAAC,EAAAC,EAAAC,GAAA,cACA,OAAO,eAAeA,EAAS,aAAc,CAAE,MAAO,EAAK,CAAC,EAC5DA,EAAQ,YAAc,OACtB,IAAIC,EAAc,IAClB,SAASC,EAA6BC,EAAK,CACvC,OAAOF,EAAY,wBAAwB,QAAQE,EAAI,CAAC,CAAC,EAAI,EACjE,CAFSC,EAAAF,EAAA,gCAGT,SAASG,EAAqBC,EAAK,CAC/B,IAAIC,EAAkBD,EAAI,QAAQL,EAAY,oBAAqB,EAAE,EACrE,OAAOM,EAAgB,QAAQN,EAAY,kBAAmB,SAAUO,EAAOC,EAAK,CAChF,OAAO,OAAO,aAAaA,CAAG,CAClC,CAAC,CACL,CALSL,EAAAC,EAAA,wBAMT,SAASK,EAAWP,EAAK,CACrB,OAAO,IAAI,SAASA,CAAG,CAC3B,CAFSC,EAAAM,EAAA,cAGT,SAASC,EAAUC,EAAK,CACpB,GAAI,CACA,OAAO,mBAAmBA,CAAG,CACjC,MACU,CAIN,OAAOA,CACX,CACJ,CAVSR,EAAAO,EAAA,aAWT,SAASE,EAAYV,EAAK,CACtB,GAAI,CAACA,EACD,OAAOF,EAAY,UAEvB,IAAIa,EACAC,EAAaJ,EAAUR,EAAI,KAAK,CAAC,EACrC,GACIY,EAAaV,EAAqBU,CAAU,EACvC,QAAQd,EAAY,oBAAqB,EAAE,EAC3C,QAAQA,EAAY,oBAAqB,EAAE,EAC3C,QAAQA,EAAY,2BAA4B,EAAE,EAClD,KAAK,EACVc,EAAaJ,EAAUI,CAAU,EACjCD,EACIC,EAAW,MAAMd,EAAY,mBAAmB,GAC5Cc,EAAW,MAAMd,EAAY,iBAAiB,GAC9Cc,EAAW,MAAMd,EAAY,mBAAmB,GAChDc,EAAW,MAAMd,EAAY,0BAA0B,QAC1Da,GAAiBA,EAAc,OAAS,GACjD,IAAIE,EAAeD,EACnB,GAAI,CAACC,EACD,OAAOf,EAAY,UAEvB,GAAIC,EAA6Bc,CAAY,EACzC,OAAOA,EAGX,IAAIC,EAAaD,EAAa,UAAU,EACpCE,EAAwBD,EAAW,MAAMhB,EAAY,cAAc,EACvE,GAAI,CAACiB,EACD,OAAOF,EAEX,IAAIG,EAAYD,EAAsB,CAAC,EAAE,YAAY,EAAE,KAAK,EAC5D,GAAIjB,EAAY,qBAAqB,KAAKkB,CAAS,EAC/C,OAAOlB,EAAY,UAEvB,IAAImB,EAAgBH,EAAW,QAAQ,MAAO,GAAG,EAEjD,GAAIE,IAAc,WAAaA,EAAU,SAAS,KAAK,EACnD,OAAOC,EAGX,GAAID,IAAc,SAAWA,IAAc,SAAU,CACjD,GAAI,CAACT,EAAWU,CAAa,EACzB,OAAOnB,EAAY,UAEvB,IAAIoB,EAAQ,IAAI,IAAID,CAAa,EACjC,OAAAC,EAAM,SAAWA,EAAM,SAAS,YAAY,EAC5CA,EAAM,SAAWA,EAAM,SAAS,YAAY,EACrCA,EAAM,SAAS,CAC1B,CACA,OAAOD,CACX,CApDShB,EAAAS,EAAA,eAqDTb,EAAQ,YAAca", "names": ["require_constants", "__commonJSMin", "exports", "require_dist", "__commonJSMin", "exports", "constants_1", "isRelativeUrlWithoutProtocol", "url", "__name", "decodeHtmlCharacters", "str", "removedNullByte", "match", "dec", "isValidUrl", "decodeURI", "uri", "sanitizeUrl", "charsToDecode", "decodedUrl", "sanitizedUrl", "trimmedUrl", "urlSchemeParseResults", "urlScheme", "backSanitized", "url_1"]}