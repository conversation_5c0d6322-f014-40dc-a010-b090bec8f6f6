{"version": 3, "file": "indentation-aware.d.ts", "sourceRoot": "", "sources": ["../../src/parser/indentation-aware.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EAAE,wBAAwB,EAAE,SAAS,EAAE,MAAM,EAA6B,eAAe,EAAE,MAAM,YAAY,CAAC;AAC1H,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAC3E,OAAO,KAAK,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AAC5E,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC/D,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAE1D,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAA4B,YAAY,EAAoB,MAAM,YAAY,CAAC;AAEtF,KAAK,yBAAyB,CAAC,SAAS,SAAS,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAE9F,MAAM,WAAW,8BAA8B,CAAC,YAAY,SAAS,MAAM,GAAG,MAAM,EAAE,WAAW,SAAS,MAAM,GAAG,MAAM;IACrH;;;;;;;;OAQG;IACH,eAAe,EAAE,YAAY,CAAC;IAC9B;;;;;;;;OAQG;IACH,eAAe,EAAE,YAAY,CAAC;IAC9B;;;;;;;;OAQG;IACH,mBAAmB,EAAE,YAAY,CAAC;IAClC;;;;;;;OAOG;IACH,2BAA2B,EAAE,KAAK,CAAC,yBAAyB,CAAC,YAAY,GAAG,WAAW,CAAC,CAAC,CAAA;CAC5F;AAED,eAAO,MAAM,gCAAgC,EAAE,8BAK9C,CAAC;AAEF,oBAAY,UAAU;IAClB,OAAO,0BAA0B;IACjC,kBAAkB,uBAAuB;CAC5C;AAED,MAAM,WAAW,uBAAwB,SAAQ,YAAY;IACzD,uEAAuE;IACvE,gBAAgB,EAAE,MAAM,EAAE,CAAC;CAC9B;AAED;;;;;;;;;GASG;AACH,qBAAa,4BAA4B,CAAC,SAAS,SAAS,MAAM,GAAG,MAAM,EAAE,WAAW,SAAS,MAAM,GAAG,MAAM,CAAE,SAAQ,mBAAmB;IACzI;;;OAGG;IACH,SAAS,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAO;IAE3C,QAAQ,CAAC,OAAO,EAAE,8BAA8B,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IAEzE;;OAEG;IACH,QAAQ,CAAC,eAAe,EAAE,SAAS,CAAC;IAEpC;;OAEG;IACH,QAAQ,CAAC,eAAe,EAAE,SAAS,CAAC;IAEpC;;;OAGG;IACH,SAAS,CAAC,gBAAgB,SAAa;gBAE3B,OAAO,GAAE,OAAO,CAAC,8BAA8B,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAA8F;IAoB1L,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,mBAAmB,GAAG,SAAS,GAAG,eAAe;IAkDzF,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,uBAAuB;IAQjE;;;;;;OAMG;IACH,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO;IAI9D;;;;;;;;OAQG;IAEH,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,GAAG;QAAE,eAAe,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,eAAe,GAAG,IAAI,CAAA;KAAE;IAUhM;;;;;;;;OAQG;IACH,SAAS,CAAC,8BAA8B,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;IAWnH;;;;;;OAMG;IACH,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;IAI7D;;;;;;;OAOG;IACH,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,GAAG,UAAU,CAAC,wBAAwB,CAAC;IAkB/I;;;;;;;OAOG;IACH,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,GAAG,UAAU,CAAC,wBAAwB,CAAC;cA8C5H,kBAAkB,CAAC,QAAQ,EAAE,YAAY,GAAG,SAAS;IAkBxE;;;;;OAKG;IACH,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;CAYhD;AAED;;;;;;;;;;;;GAYG;AACH,qBAAa,qBAAsB,SAAQ,YAAY;IAEnD,SAAS,CAAC,QAAQ,CAAC,uBAAuB,EAAE,4BAA4B,CAAC;gBAE7D,QAAQ,EAAE,mBAAmB;IAShC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE,eAA0C,GAAG,WAAW;CAqCpG"}