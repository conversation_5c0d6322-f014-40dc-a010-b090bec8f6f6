<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强制刷新Vue应用</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .title {
            font-size: 2rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
        }
        .button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
        }
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            display: none;
        }
        .success {
            background: rgba(67, 233, 123, 0.3);
            border: 1px solid rgba(67, 233, 123, 0.5);
        }
        .info {
            background: rgba(52, 152, 219, 0.3);
            border: 1px solid rgba(52, 152, 219, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔄 Vue应用强制刷新工具</h1>
        <p>解决样式缓存问题，确保最新的CSS修复生效</p>
        
        <div>
            <button class="button" onclick="forceRefresh()">
                🚀 强制刷新Vue应用
            </button>
            <button class="button" onclick="clearCache()">
                🗑️ 清除浏览器缓存
            </button>
            <button class="button" onclick="hardReload()">
                ⚡ 硬刷新 (Ctrl+F5)
            </button>
        </div>
        
        <div id="status" class="status"></div>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>💡 提示：如果样式仍未更新，请尝试：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>按 Ctrl+Shift+R 进行硬刷新</li>
                <li>清除浏览器缓存和Cookie</li>
                <li>在开发者工具中禁用缓存</li>
                <li>重启Vite开发服务器</li>
            </ul>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function forceRefresh() {
            showStatus('🔄 正在强制刷新Vue应用...', 'info');
            
            // 添加时间戳参数强制刷新
            const timestamp = new Date().getTime();
            const url = `http://localhost:5173/?t=${timestamp}`;
            
            setTimeout(() => {
                window.open(url, '_blank');
                showStatus('✅ Vue应用已在新窗口中打开！', 'success');
            }, 1000);
        }

        function clearCache() {
            showStatus('🗑️ 正在清除缓存...', 'info');
            
            // 清除localStorage和sessionStorage
            localStorage.clear();
            sessionStorage.clear();
            
            // 尝试清除缓存API（如果支持）
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            setTimeout(() => {
                showStatus('✅ 缓存已清除！请刷新页面。', 'success');
            }, 1000);
        }

        function hardReload() {
            showStatus('⚡ 执行硬刷新...', 'info');
            
            setTimeout(() => {
                // 模拟Ctrl+F5硬刷新
                location.reload(true);
            }, 1000);
        }

        // 自动检测Vue应用状态
        function checkVueApp() {
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        showStatus('✅ Vue应用运行正常', 'success');
                    }
                })
                .catch(error => {
                    showStatus('❌ Vue应用未运行，请先启动开发服务器', 'error');
                });
        }

        // 页面加载时检查
        window.onload = () => {
            setTimeout(checkVueApp, 1000);
        };
    </script>
</body>
</html>
