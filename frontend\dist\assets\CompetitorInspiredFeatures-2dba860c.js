import{_ as F,m as V,c as A,t as N,v as L,a as O,l as R,u as U,s as j,b as z,d as E,e as q,f as H,g as G,r as J,h as K,o as P,i as o,j as i,k as l,n as s,p as t,w as n,q as c,F as Q,x as W,y as C,z as v,A as X,B as Y,C as Z}from"./index-b6a2842e.js";const $={name:"CompetitorInspiredFeatures",components:{Microphone:V,ChatDotRound:A,TrendCharts:N,View:L,Monitor:O,Lock:R,UserFilled:U,School:j,DataAnalysis,Briefcase:z,Trophy:E,Connection:q,Cpu:H,Tools:G},setup(){const h=J("campus"),a=[{id:"campus",name:"校园招聘",icon:"School"},{id:"social",name:"社会招聘",icon:"Briefcase"},{id:"technical",name:"技术招聘",icon:"Cpu"}],f=K({interviews:0,accuracy:0,companies:0,satisfaction:0}),d={interviews:500,accuracy:98,companies:1e3,satisfaction:95},S=m=>{h.value=m},w=()=>{const y=33.333333333333336;Object.keys(d).forEach(r=>{const u=d[r],b=u/60;let _=0;const g=setInterval(()=>{_+=b,_>=u?(f[r]=u,clearInterval(g)):f[r]=Math.floor(_)},y)})};return P(()=>{setTimeout(w,500)}),{activeScenario:h,scenarios:a,animatedStats:f,switchScenario:S}}},ss={class:"competitor-inspired-features"},as={class:"feature-section"},ts={class:"features-grid"},es={class:"feature-card-dayee-style card-modern-entrance"},ns={class:"feature-icon-dayee"},os={class:"feature-card-dayee-style card-modern-entrance delay-100"},ds={class:"feature-icon-dayee"},is={class:"feature-card-dayee-style card-modern-entrance delay-200"},ls={class:"feature-icon-dayee"},cs={class:"feature-section"},rs={class:"features-grid"},us={class:"feature-card-dayee-style card-modern-entrance"},_s={class:"feature-icon-dayee"},ps={class:"feature-card-dayee-style card-modern-entrance delay-100"},vs={class:"feature-icon-dayee"},fs={class:"feature-card-dayee-style card-modern-entrance delay-200"},ms={class:"feature-icon-dayee"},hs={class:"feature-section"},ys={class:"scenario-tabs"},bs=["onClick"],gs={class:"scenario-content"},Cs={key:0,class:"scenario-details"},Ss={class:"scenario-features"},ws={class:"scenario-feature"},ks={class:"scenario-feature"},Bs={class:"scenario-feature"},Ds={key:1,class:"scenario-details"},xs={class:"scenario-features"},Is={class:"scenario-feature"},Ms={class:"scenario-feature"},Ts={class:"scenario-feature"},Fs={key:2,class:"scenario-details"},Vs={class:"scenario-features"},As={class:"scenario-feature"},Ns={class:"scenario-feature"},Ls={class:"scenario-feature"},Os={class:"feature-section"},Rs={class:"data-showcase"},Us={class:"stats-card-hina-style professional-glow"},js={class:"stats-number-hina"},zs={class:"stats-card-hina-style professional-glow delay-100"},Es={class:"stats-number-hina"},qs={class:"stats-card-hina-style professional-glow delay-200"},Hs={class:"stats-number-hina"},Gs={class:"stats-card-hina-style professional-glow delay-300"},Js={class:"stats-number-hina"};function Ks(h,a,f,d,S,w){const m=o("Microphone"),e=o("el-icon"),y=o("ChatDotRound"),r=o("TrendCharts"),u=o("View"),b=o("Monitor"),_=o("Lock"),g=o("User"),B=o("School"),D=o("DataBoard"),x=o("OfficeBuilding"),I=o("Trophy"),M=o("Connection"),T=o("Cpu"),k=o("Setting");return i(),l("div",ss,[s("section",as,[a[3]||(a[3]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"实时AI面试辅助"),s("p",{class:"section-subtitle"},"借鉴OfferMore的实时辅助理念，提供智能面试支持")],-1)),s("div",ts,[s("div",es,[s("div",ns,[t(e,null,{default:n(()=>[t(m)]),_:1})]),a[0]||(a[0]=c('<h3 class="feature-title-dayee" data-v-a970de4b>实时语音识别</h3><p class="feature-description-dayee" data-v-a970de4b>基于iFlytek Spark的实时语音转文字，准确识别候选人回答内容</p><div class="feature-tags" data-v-a970de4b><span class="tag-modern" data-v-a970de4b>语音识别</span><span class="tag-modern success" data-v-a970de4b>实时处理</span></div>',3))]),s("div",os,[s("div",ds,[t(e,null,{default:n(()=>[t(y)]),_:1})]),a[1]||(a[1]=c('<h3 class="feature-title-dayee" data-v-a970de4b>智能回答建议</h3><p class="feature-description-dayee" data-v-a970de4b>AI分析面试问题，为面试官提供专业的追问建议和评估要点</p><div class="feature-tags" data-v-a970de4b><span class="tag-modern" data-v-a970de4b>智能分析</span><span class="tag-modern warning" data-v-a970de4b>辅助决策</span></div>',3))]),s("div",is,[s("div",ls,[t(e,null,{default:n(()=>[t(r)]),_:1})]),a[2]||(a[2]=c('<h3 class="feature-title-dayee" data-v-a970de4b>实时能力评估</h3><p class="feature-description-dayee" data-v-a970de4b>面试过程中实时分析候选人表现，动态更新六维能力评分</p><div class="feature-tags" data-v-a970de4b><span class="tag-modern" data-v-a970de4b>实时评估</span><span class="tag-modern success" data-v-a970de4b>六维分析</span></div>',3))])])]),s("section",cs,[a[7]||(a[7]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"智能防作弊系统"),s("p",{class:"section-subtitle"},"参考Dayee的企业级安全机制，确保面试公平性")],-1)),s("div",rs,[s("div",us,[s("div",_s,[t(e,null,{default:n(()=>[t(u)]),_:1})]),a[4]||(a[4]=c('<h3 class="feature-title-dayee" data-v-a970de4b>人脸识别验证</h3><p class="feature-description-dayee" data-v-a970de4b>实时人脸检测和身份验证，防止替考和身份造假</p><div class="feature-status" data-v-a970de4b><span class="status-indicator active" data-v-a970de4b></span><span class="status-text" data-v-a970de4b>实时监控中</span></div>',3))]),s("div",ps,[s("div",vs,[t(e,null,{default:n(()=>[t(b)]),_:1})]),a[5]||(a[5]=c('<h3 class="feature-title-dayee" data-v-a970de4b>行为异常检测</h3><p class="feature-description-dayee" data-v-a970de4b>检测切屏、多窗口、异常操作等作弊行为，自动记录和预警</p><div class="feature-status" data-v-a970de4b><span class="status-indicator warning" data-v-a970de4b></span><span class="status-text" data-v-a970de4b>智能监测</span></div>',3))]),s("div",fs,[s("div",ms,[t(e,null,{default:n(()=>[t(_)]),_:1})]),a[6]||(a[6]=c('<h3 class="feature-title-dayee" data-v-a970de4b>环境安全检查</h3><p class="feature-description-dayee" data-v-a970de4b>检测面试环境，确保无第三方干扰，维护面试公平性</p><div class="feature-status" data-v-a970de4b><span class="status-indicator success" data-v-a970de4b></span><span class="status-text" data-v-a970de4b>环境安全</span></div>',3))])])]),s("section",hs,[a[20]||(a[20]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"多场景智能适配"),s("p",{class:"section-subtitle"},"学习Hina的场景化解决方案，满足不同招聘需求")],-1)),s("div",ys,[(i(!0),l(Q,null,W(d.scenarios,p=>(i(),l("div",{key:p.id,class:X(["scenario-tab",{active:d.activeScenario===p.id}]),onClick:Ps=>d.switchScenario(p.id)},[t(e,null,{default:n(()=>[(i(),Y(Z(p.icon)))]),_:2},1024),s("span",null,v(p.name),1)],10,bs))),128))]),s("div",gs,[d.activeScenario==="campus"?(i(),l("div",Cs,[a[11]||(a[11]=s("h3",null,"校园招聘场景",-1)),s("div",Ss,[s("div",ws,[t(e,null,{default:n(()=>[t(g)]),_:1}),a[8]||(a[8]=s("span",null,"批量面试管理",-1))]),s("div",ks,[t(e,null,{default:n(()=>[t(B)]),_:1}),a[9]||(a[9]=s("span",null,"学生群体适配",-1))]),s("div",Bs,[t(e,null,{default:n(()=>[t(D)]),_:1}),a[10]||(a[10]=s("span",null,"潜力评估模型",-1))])])])):C("",!0),d.activeScenario==="social"?(i(),l("div",Ds,[a[15]||(a[15]=s("h3",null,"社会招聘场景",-1)),s("div",xs,[s("div",Is,[t(e,null,{default:n(()=>[t(x)]),_:1}),a[12]||(a[12]=s("span",null,"经验技能评估",-1))]),s("div",Ms,[t(e,null,{default:n(()=>[t(I)]),_:1}),a[13]||(a[13]=s("span",null,"专业能力测试",-1))]),s("div",Ts,[t(e,null,{default:n(()=>[t(M)]),_:1}),a[14]||(a[14]=s("span",null,"团队协作评估",-1))])])])):C("",!0),d.activeScenario==="technical"?(i(),l("div",Fs,[a[19]||(a[19]=s("h3",null,"技术人才招聘",-1)),s("div",Vs,[s("div",As,[t(e,null,{default:n(()=>[t(T)]),_:1}),a[16]||(a[16]=s("span",null,"技术深度考察",-1))]),s("div",Ns,[t(e,null,{default:n(()=>[t(k)]),_:1}),a[17]||(a[17]=s("span",null,"编程能力测试",-1))]),s("div",Ls,[t(e,null,{default:n(()=>[t(k)]),_:1}),a[18]||(a[18]=s("span",null,"项目经验评估",-1))])])])):C("",!0)])]),s("section",Os,[a[25]||(a[25]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"数据驱动的智能决策"),s("p",{class:"section-subtitle"},"基于海量面试数据，提供科学的招聘决策支持")],-1)),s("div",Rs,[s("div",Us,[s("div",js,v(d.animatedStats.interviews),1),a[21]||(a[21]=s("div",{class:"stats-label-hina"},"累计面试场次",-1))]),s("div",zs,[s("div",Es,v(d.animatedStats.accuracy)+"%",1),a[22]||(a[22]=s("div",{class:"stats-label-hina"},"AI评估准确率",-1))]),s("div",qs,[s("div",Hs,v(d.animatedStats.companies)+"+",1),a[23]||(a[23]=s("div",{class:"stats-label-hina"},"服务企业数量",-1))]),s("div",Gs,[s("div",Js,v(d.animatedStats.satisfaction)+"%",1),a[24]||(a[24]=s("div",{class:"stats-label-hina"},"用户满意度",-1))])])])])}const Ws=F($,[["render",Ks],["__scopeId","data-v-a970de4b"]]);export{Ws as default};
