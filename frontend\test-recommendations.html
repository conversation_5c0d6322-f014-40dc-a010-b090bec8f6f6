<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能推荐功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            color: #1890ff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .recommendation {
            background: #f0f7ff;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #1890ff;
        }
        .recommendation.warning {
            background: #fff7e6;
            border-left-color: #fa8c16;
        }
        .recommendation.success {
            background: #f6ffed;
            border-left-color: #52c41a;
        }
        .recommendation.info {
            background: #f0f7ff;
            border-left-color: #1890ff;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iFlytek 智能推荐功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 职位筛选建议测试</div>
            <p>测试智能推荐是否能生成职位筛选相关的建议</p>
            <button class="test-button" onclick="testFilterRecommendations()">测试筛选建议</button>
            <div id="filter-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 招聘优化建议测试</div>
            <p>测试智能推荐是否能生成招聘优化相关的建议</p>
            <button class="test-button" onclick="testOptimizationRecommendations()">测试优化建议</button>
            <div id="optimization-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 数据洞察建议测试</div>
            <p>测试智能推荐是否能生成数据洞察相关的建议</p>
            <button class="test-button" onclick="testInsightRecommendations()">测试洞察建议</button>
            <div id="insight-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 完整推荐系统测试</div>
            <p>测试完整的智能推荐生成流程</p>
            <button class="test-button" onclick="testFullRecommendationSystem()">完整测试</button>
            <div id="full-result" class="result"></div>
        </div>
    </div>

    <script type="module">
        // 模拟 iFlytek 服务
        class MockIflytekService {
            generatePositionManagementRecommendations(currentFilters, positionData, dataScope) {
                const recommendations = []
                let idCounter = 1

                // 职位筛选建议
                if (!currentFilters.domain || currentFilters.domain === '') {
                    recommendations.push({
                        id: `filter_${idCounter++}`,
                        text: '查看紧急AI职位',
                        type: 'warning',
                        category: 'filter',
                        action: 'filter',
                        filters: { domain: 'ai', urgent: true }
                    })
                }

                if (!currentFilters.status || currentFilters.status === '') {
                    recommendations.push({
                        id: `filter_${idCounter++}`,
                        text: '筛选活跃招聘职位',
                        type: 'success',
                        category: 'filter',
                        action: 'filter',
                        filters: { status: 'active' }
                    })
                }

                // 招聘优化建议
                const urgentPositions = positionData.filter(p => p.urgent === true)
                if (urgentPositions.length > 3) {
                    recommendations.push({
                        id: `optimize_${idCounter++}`,
                        text: '优化紧急职位描述以提高吸引力',
                        type: 'warning',
                        category: 'optimization',
                        action: 'optimize_descriptions'
                    })
                }

                // 数据洞察建议
                recommendations.push({
                    id: `insight_${idCounter++}`,
                    text: '关注AI领域热门技术趋势',
                    type: 'primary',
                    category: 'insight',
                    action: 'view_trends',
                    domain: 'ai'
                })

                recommendations.push({
                    id: `management_${idCounter++}`,
                    text: '启用iFlytek智能推荐系统',
                    type: 'success',
                    category: 'management',
                    action: 'enable_smart_features'
                })

                return recommendations.slice(0, 6)
            }

            async generateDataDrivenInsights(analysisRequest) {
                const recommendations = this.generatePositionManagementRecommendations(
                    analysisRequest.currentFilters || {},
                    analysisRequest.positionData || [],
                    analysisRequest.dataScope || 'general'
                )

                return {
                    insights: ['技术岗位候选人整体水平呈上升趋势', 'AI领域人才竞争激烈'],
                    recommendations: recommendations,
                    predictions: { successRate: 0.78 },
                    trends: ['技术面试向实战化方向发展']
                }
            }
        }

        const mockService = new MockIflytekService()

        // 测试函数
        window.testFilterRecommendations = async function() {
            const result = await mockService.generateDataDrivenInsights({
                dataScope: 'position_management',
                currentFilters: {},
                positionData: []
            })
            
            const filterRecs = result.recommendations.filter(r => r.category === 'filter')
            displayRecommendations('filter-result', filterRecs)
        }

        window.testOptimizationRecommendations = async function() {
            const result = await mockService.generateDataDrivenInsights({
                dataScope: 'position_management',
                currentFilters: { domain: 'ai' },
                positionData: [
                    { urgent: true, domain: 'ai' },
                    { urgent: true, domain: 'ai' },
                    { urgent: true, domain: 'ai' },
                    { urgent: true, domain: 'ai' }
                ]
            })
            
            const optimizationRecs = result.recommendations.filter(r => r.category === 'optimization')
            displayRecommendations('optimization-result', optimizationRecs)
        }

        window.testInsightRecommendations = async function() {
            const result = await mockService.generateDataDrivenInsights({
                dataScope: 'position_management',
                currentFilters: { domain: 'ai' },
                positionData: []
            })
            
            const insightRecs = result.recommendations.filter(r => r.category === 'insight' || r.category === 'management')
            displayRecommendations('insight-result', insightRecs)
        }

        window.testFullRecommendationSystem = async function() {
            const result = await mockService.generateDataDrivenInsights({
                dataScope: 'position_management',
                currentFilters: {},
                positionData: [
                    { urgent: true, domain: 'ai', status: 'active' },
                    { urgent: true, domain: 'ai', status: 'active' },
                    { urgent: false, domain: 'bigdata', status: 'active' },
                    { urgent: false, domain: 'iot', status: 'paused' }
                ]
            })
            
            displayRecommendations('full-result', result.recommendations)
        }

        function displayRecommendations(elementId, recommendations) {
            const element = document.getElementById(elementId)
            if (recommendations.length === 0) {
                element.innerHTML = '<p style="color: #ff4d4f;">❌ 没有生成推荐内容</p>'
                return
            }

            let html = '<p style="color: #52c41a;">✅ 成功生成推荐内容：</p>'
            recommendations.forEach(rec => {
                html += `<div class="recommendation ${rec.type}">
                    <strong>${rec.text}</strong><br>
                    <small>类别: ${rec.category} | 动作: ${rec.action}</small>
                </div>`
            })
            element.innerHTML = html
        }
    </script>
</body>
</html>
