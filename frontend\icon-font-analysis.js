#!/usr/bin/env node

/**
 * 图标字体协调性分析工具
 * 分析Vue组件中图标与文字的尺寸匹配问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 分析图标字体协调性
function analyzeIconFontCoordination(filePath) {
    console.log(`🔍 分析文件: ${path.relative(__dirname, filePath)}`);
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    const issues = [];
    const iconUsages = [];
    const fontSizes = [];
    
    // 查找图标使用
    lines.forEach((line, index) => {
        const lineNum = index + 1;
        
        // 匹配 el-icon 使用
        const iconMatch = line.match(/<el-icon[^>]*>.*?<\/el-icon>/g);
        if (iconMatch) {
            iconUsages.push({
                line: lineNum,
                content: line.trim(),
                context: getContext(lines, index)
            });
        }
        
        // 匹配字体大小定义
        const fontSizeMatch = line.match(/font-size:\s*(\d+)px/g);
        if (fontSizeMatch) {
            fontSizeMatch.forEach(match => {
                const size = parseInt(match.match(/(\d+)px/)[1]);
                fontSizes.push({
                    line: lineNum,
                    size: size,
                    content: line.trim()
                });
            });
        }
    });
    
    // 分析问题
    console.log(`\n📊 统计信息:`);
    console.log(`  - 图标使用: ${iconUsages.length} 处`);
    console.log(`  - 字体大小定义: ${fontSizes.length} 处`);
    
    // 分析字体大小分布
    const sizeDistribution = {};
    fontSizes.forEach(item => {
        sizeDistribution[item.size] = (sizeDistribution[item.size] || 0) + 1;
    });
    
    console.log(`\n📏 字体大小分布:`);
    Object.entries(sizeDistribution)
        .sort(([a], [b]) => parseInt(a) - parseInt(b))
        .forEach(([size, count]) => {
            console.log(`  - ${size}px: ${count} 次使用`);
        });
    
    // 识别潜在问题
    console.log(`\n⚠️  潜在协调性问题:`);
    
    // 检查图标容器的字体大小
    iconUsages.forEach(usage => {
        const context = usage.context;
        let hasExplicitSize = false;
        let parentFontSize = null;
        
        // 检查上下文中的字体大小
        context.forEach(contextLine => {
            const fontMatch = contextLine.match(/font-size:\s*(\d+)px/);
            if (fontMatch) {
                parentFontSize = parseInt(fontMatch[1]);
                hasExplicitSize = true;
            }
        });
        
        if (!hasExplicitSize) {
            issues.push({
                type: 'missing_icon_size',
                line: usage.line,
                message: '图标未明确设置尺寸，可能与文字不协调',
                suggestion: '建议为图标容器添加明确的字体大小或使用Element Plus的size属性'
            });
        } else if (parentFontSize) {
            // 检查图标大小是否合适
            if (parentFontSize < 12) {
                issues.push({
                    type: 'icon_too_small',
                    line: usage.line,
                    message: `图标所在区域字体过小 (${parentFontSize}px)，可能影响可访问性`,
                    suggestion: '建议增大字体大小至至少12px以符合WCAG标准'
                });
            }
        }
    });
    
    return { iconUsages, fontSizes, issues, sizeDistribution };
}

// 获取上下文行
function getContext(lines, currentIndex, contextSize = 3) {
    const start = Math.max(0, currentIndex - contextSize);
    const end = Math.min(lines.length, currentIndex + contextSize + 1);
    return lines.slice(start, end);
}

// 生成修复建议
function generateFixSuggestions(analysis) {
    console.log(`\n🔧 修复建议:`);
    
    const { sizeDistribution, issues } = analysis;
    
    // 建议标准化字体大小
    console.log(`\n1. 字体大小标准化建议:`);
    console.log(`   - 主标题: 24-28px`);
    console.log(`   - 副标题: 18-20px`);
    console.log(`   - 正文: 14-16px`);
    console.log(`   - 辅助文字: 12-13px`);
    console.log(`   - 图标建议与相邻文字保持相同或略大1-2px的尺寸`);
    
    // Element Plus图标尺寸建议
    console.log(`\n2. Element Plus图标尺寸建议:`);
    console.log(`   - 使用CSS设置: .el-icon { font-size: 16px; }`);
    console.log(`   - 或使用内联样式: <el-icon style="font-size: 16px">`);
    console.log(`   - 响应式设计: 在不同断点使用不同尺寸`);
    
    // 具体问题修复
    if (issues.length > 0) {
        console.log(`\n3. 具体问题修复:`);
        issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. 第${issue.line}行: ${issue.message}`);
            console.log(`      建议: ${issue.suggestion}`);
        });
    }
    
    // WCAG可访问性建议
    console.log(`\n4. WCAG 2.1 AA可访问性建议:`);
    console.log(`   - 最小字体大小: 12px`);
    console.log(`   - 图标点击区域: 至少44x44px`);
    console.log(`   - 颜色对比度: 至少4.5:1`);
    console.log(`   - 提供替代文本或aria-label`);
}

// 主函数
function main() {
    console.log('🎯 多模态面试评估系统 - 图标字体协调性分析\n');
    
    const targetFile = path.join(__dirname, 'src/views/InterviewingPage.vue');
    
    if (!fs.existsSync(targetFile)) {
        console.error(`❌ 文件不存在: ${targetFile}`);
        return;
    }
    
    const analysis = analyzeIconFontCoordination(targetFile);
    generateFixSuggestions(analysis);
    
    console.log(`\n✅ 分析完成！`);
    console.log(`📄 详细报告已生成，请根据建议进行修复。`);
}

main();
