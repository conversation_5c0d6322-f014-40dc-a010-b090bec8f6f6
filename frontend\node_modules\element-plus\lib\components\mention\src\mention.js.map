{"version": 3, "file": "mention.js", "sources": ["../../../../../../packages/components/mention/src/mention.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isFunction,\n  isString,\n} from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { inputProps } from '@element-plus/components/input'\nimport { filterOption } from './helper'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Mention from './mention.vue'\nimport type { MentionOption } from './types'\nimport type { Options } from '@element-plus/components/popper'\n\nexport const mentionProps = buildProps({\n  ...inputProps,\n  /**\n   * @description mention options list\n   */\n  options: {\n    type: definePropType<MentionOption[]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description prefix character to trigger mentions. The string length must be exactly 1.\n   */\n  prefix: {\n    type: definePropType<string | string[]>([String, Array]),\n    default: '@',\n    validator: (val: string | string[]) => {\n      if (isString(val)) return val.length === 1\n      return val.every((v) => isString(v) && v.length === 1)\n    },\n  },\n  /**\n   * @description character to split mentions. The string length must be exactly 1.\n   */\n  split: {\n    type: String,\n    default: ' ',\n    validator: (val: string) => val.length === 1,\n  },\n  /**\n   * @description customize filter option logic.\n   */\n  filterOption: {\n    type: definePropType<false | typeof filterOption>([Boolean, Function]),\n    default: () => filterOption,\n    validator: (val) => {\n      if (val === false) return true\n      return isFunction(val)\n    },\n  },\n  /**\n   * @description set popup placement\n   */\n  placement: {\n    type: definePropType<'bottom' | 'top'>(String),\n    default: 'bottom',\n  },\n  /**\n   * @description whether the dropdown panel has an arrow\n   */\n  showArrow: Boolean,\n  /**\n   * @description offset of the dropdown panel\n   */\n  offset: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description when backspace is pressed to delete, whether the mention content is deleted as a whole\n   */\n  whole: Boolean,\n  /**\n   * @description when backspace is pressed to delete, check if the mention is a whole\n   */\n  checkIsWhole: {\n    type: definePropType<(pattern: string, prefix: string) => boolean>(\n      Function\n    ),\n  },\n  /**\n   * @description input value\n   */\n  modelValue: String,\n  /**\n   * @description whether the dropdown panel of mentions is in a loading state.\n   */\n  loading: Boolean,\n  /**\n   * @description custom class name for dropdown panel\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description [popper.js](https://popper.js.org/docs/v2/) parameters\n   */\n  popperOptions: {\n    type: definePropType<Partial<Options>>(Object),\n    default: () => ({} as Partial<Options>),\n  },\n})\n\nexport const mentionEmits = {\n  [UPDATE_MODEL_EVENT]: (value: string) => isString(value),\n  input: (value: string) => isString(value),\n  search: (pattern: string, prefix: string) =>\n    isString(pattern) && isString(prefix),\n  select: (option: MentionOption, prefix: string) =>\n    isString(option.value) && isString(prefix),\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n}\n\nexport type MentionEmits = typeof mentionEmits\nexport type MentionProps = ExtractPropTypes<typeof mentionProps>\nexport type MentionInstance = InstanceType<typeof Mention> & unknown\n\nexport type { MentionOption } from './types'\n"], "names": ["buildProps", "inputProps", "definePropType", "isString", "filterOption", "isFunction", "UPDATE_MODEL_EVENT"], "mappings": ";;;;;;;;;;AASY,MAAC,YAAY,GAAGA,kBAAU,CAAC;AACvC,EAAE,GAAGC,gBAAU;AACf,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK;AACxB,MAAM,IAAIC,eAAQ,CAAC,GAAG,CAAC;AACvB,QAAQ,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;AAChC,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAKA,eAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC;AACxC,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC7C,IAAI,OAAO,EAAE,MAAME,mBAAY;AAC/B,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK;AACxB,MAAM,IAAI,GAAG,KAAK,KAAK;AACvB,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,OAAOC,iBAAU,CAAC,GAAG,CAAC,CAAC;AAC7B,KAAK;AACL,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEH,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,CAACI,wBAAkB,GAAG,CAAC,KAAK,KAAKH,eAAQ,CAAC,KAAK,CAAC;AAClD,EAAE,KAAK,EAAE,CAAC,KAAK,KAAKA,eAAQ,CAAC,KAAK,CAAC;AACnC,EAAE,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,KAAKA,eAAQ,CAAC,OAAO,CAAC,IAAIA,eAAQ,CAAC,MAAM,CAAC;AACpE,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,KAAKA,eAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAIA,eAAQ,CAAC,MAAM,CAAC;AACxE,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C;;;;;"}