{"version": 3, "file": "normalized-text-documents.js", "sourceRoot": "", "sources": ["../../src/lsp/normalized-text-documents.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAMhF,OAAO,EAAE,oBAAoB,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAElF,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAoFjD,gBAAgB;AAChB,2IAA2I;AAE3I;;;gGAGgG;AAEhG;;GAEG;AACH,MAAM,OAAO,uBAAuB;IAahC,YAAmB,aAA4C;QAC3D,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAElC,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,EAA8B,CAAC;QACrE,IAAI,CAAC,UAAU,GAAG,IAAI,OAAO,EAA8B,CAAC;QAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,EAA8B,CAAC;QAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,OAAO,EAA8B,CAAC;QAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,EAAgC,CAAC;IACnE,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;IACjC,CAAC;IAED,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;IAC1C,CAAC;IAED,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAClC,CAAC;IAEM,mBAAmB,CAAC,OAAuE;QAC9F,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;IACtC,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;IACjC,CAAC;IAED,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAClC,CAAC;IAEM,GAAG,CAAC,GAAiB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEM,GAAG,CAAC,QAAW;QAClB,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,GAAG,KAAK,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,MAAM,CAAC,GAAqB;QAC/B,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9F,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5D,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAEM,GAAG;QACN,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IACtD,CAAC;IAEM,IAAI;QACP,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;IAEM,MAAM,CAAC,UAAsB;QAChC,2EAA2E;QAC3E,8DAA8D;QAC7D,UAAkB,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,WAAW,CAAC;QAC1E,MAAM,WAAW,GAAiB,EAAE,CAAC;QACrC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,KAAgC,EAAE,EAAE;YACnF,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY,CAAC;YAC9B,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YAErF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC;QACJ,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,KAAkC,EAAE,EAAE;YACvF,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY,CAAC;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,cAAc,CAAC;YACrC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO;YACX,CAAC;YAED,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;YACvB,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,sCAAsC,EAAE,CAAC,GAAG,mCAAmC,CAAC,CAAC;YACrG,CAAC;YACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YAEvC,IAAI,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACpD,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBAC/B,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC9E,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;gBAC/C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;YAC/E,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,KAAiC,EAAE,EAAE;YACrF,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACvD,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACtD,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;YACvE,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,KAAiC,EAAE,EAAE;YACrF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7F,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC7F,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC,KAAiC,EAAE,KAAwB,EAAE,EAAE;YACxH,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7F,IAAI,cAAc,KAAK,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1D,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YAC7G,CAAC;iBAAM,CAAC;gBACJ,OAAO,EAAE,CAAC;YACd,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,KAAgC,EAAE,EAAE;YACnF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7F,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;YACtE,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,CAAC;CACJ"}