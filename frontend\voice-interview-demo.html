<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek语音专项面试功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding-bottom: 30px;
            border-bottom: 2px solid #e6f7ff;
        }
        .header h1 {
            color: #1890ff;
            font-size: 3rem;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
        }
        .header .icon {
            font-size: 2.5rem;
        }
        .header p {
            color: #64748b;
            font-size: 1.2rem;
            margin: 0;
        }
        
        .demo-section {
            margin-bottom: 60px;
            padding: 40px;
            border: 2px solid #e6e6e6;
            border-radius: 16px;
            background: #fafbfc;
        }
        .section-title {
            color: #1890ff;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #e6e6e6;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
        }
        .feature-card h3 {
            color: #1890ff;
            margin: 0 0 16px 0;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-card p {
            color: #64748b;
            margin: 0 0 20px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 8px 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-list li:before {
            content: "✓";
            color: #52c41a;
            font-weight: 600;
        }
        
        .demo-interface {
            background: white;
            border-radius: 12px;
            padding: 30px;
            border: 1px solid #e6e6e6;
            margin-bottom: 30px;
        }
        .interface-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .interface-title {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(82, 196, 26, 0.1);
            border-radius: 20px;
            color: #52c41a;
            font-size: 14px;
            font-weight: 600;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #52c41a;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .voice-waveform {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;
            height: 80px;
            background: #f8fafc;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .wave-bar {
            width: 4px;
            background: #1890ff;
            border-radius: 2px;
            animation: wave 1s infinite ease-in-out;
        }
        .wave-bar:nth-child(1) { height: 20px; animation-delay: 0s; }
        .wave-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }
        .wave-bar:nth-child(3) { height: 50px; animation-delay: 0.2s; }
        .wave-bar:nth-child(4) { height: 30px; animation-delay: 0.3s; }
        .wave-bar:nth-child(5) { height: 45px; animation-delay: 0.4s; }
        .wave-bar:nth-child(6) { height: 25px; animation-delay: 0.5s; }
        .wave-bar:nth-child(7) { height: 40px; animation-delay: 0.6s; }
        .wave-bar:nth-child(8) { height: 55px; animation-delay: 0.7s; }
        .wave-bar:nth-child(9) { height: 35px; animation-delay: 0.8s; }
        .wave-bar:nth-child(10) { height: 20px; animation-delay: 0.9s; }
        
        @keyframes wave {
            0%, 40%, 100% { transform: scaleY(0.4); }
            20% { transform: scaleY(1); }
        }
        
        .recognition-result {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
            margin-bottom: 20px;
        }
        .recognition-text {
            font-size: 16px;
            color: #2c3e50;
            line-height: 1.6;
            margin-bottom: 12px;
        }
        .recognition-accuracy {
            font-size: 12px;
            color: #52c41a;
            font-weight: 600;
        }
        
        .analysis-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e6e6e6;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .metric-label {
            color: #64748b;
            font-size: 14px;
        }
        
        .ai-thinking {
            background: rgba(24, 144, 255, 0.05);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
            margin-bottom: 20px;
        }
        .thinking-header {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 12px;
        }
        .thinking-icon {
            animation: rotate 1s linear infinite;
        }
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .thinking-content {
            color: #2c3e50;
            line-height: 1.5;
        }
        
        .tech-specs {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 30px;
            border-radius: 12px;
            margin-top: 40px;
        }
        .tech-specs h3 {
            color: #0369a1;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
        }
        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .spec-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        .spec-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .spec-desc {
            color: #64748b;
            font-size: 14px;
        }
        
        .cta-section {
            text-align: center;
            margin-top: 50px;
            padding: 40px;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border-radius: 16px;
            color: white;
        }
        .cta-section h3 {
            margin: 0 0 16px 0;
            font-size: 1.8rem;
        }
        .cta-section p {
            margin: 0 0 24px 0;
            opacity: 0.9;
        }
        .cta-button {
            background: white;
            color: #1890ff;
            padding: 16px 32px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .header h1 {
                font-size: 2rem;
                flex-direction: column;
                gap: 8px;
            }
            .feature-grid,
            .analysis-metrics,
            .specs-grid {
                grid-template-columns: 1fr;
            }
            .demo-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <span class="icon">🎤</span>
                iFlytek语音专项面试
            </h1>
            <p>专注语音表达和沟通能力的智能面试系统</p>
        </div>

        <!-- 核心功能展示 -->
        <div class="demo-section">
            <div class="section-title">
                <span>🚀</span>
                <span>核心功能特性</span>
            </div>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎯 实时语音识别</h3>
                    <p>基于iFlytek Spark语音识别技术，实现高精度的实时语音转文字</p>
                    <ul class="feature-list">
                        <li>支持中英文混合识别</li>
                        <li>识别准确率≥95%</li>
                        <li>实时流式识别</li>
                        <li>自动标点符号</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>📊 语音特征分析</h3>
                    <p>深度分析语音特征，评估表达能力和沟通技巧</p>
                    <ul class="feature-list">
                        <li>语速分析（字/分钟）</li>
                        <li>音调变化检测</li>
                        <li>停顿频率统计</li>
                        <li>情感倾向识别</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🤖 AI智能分析</h3>
                    <p>结合语音内容和特征，提供专业的面试评估</p>
                    <ul class="feature-list">
                        <li>技术内容深度分析</li>
                        <li>表达逻辑评估</li>
                        <li>智能引导建议</li>
                        <li>实时反馈优化</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>⚡ 语音活动检测</h3>
                    <p>智能检测语音活动，自动判断回答完毕时机</p>
                    <ul class="feature-list">
                        <li>VAD自动检测</li>
                        <li>智能停顿识别</li>
                        <li>回答完整性判断</li>
                        <li>自动进入下一题</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 界面演示 -->
        <div class="demo-section">
            <div class="section-title">
                <span>💻</span>
                <span>界面演示</span>
            </div>
            
            <!-- 语音交互界面 -->
            <div class="demo-interface">
                <div class="interface-header">
                    <div class="interface-title">语音交互区域</div>
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        正在录音...
                    </div>
                </div>
                
                <div class="voice-waveform">
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                </div>
                
                <div class="recognition-result">
                    <div class="recognition-text">
                        "在我之前的机器学习项目中，我主要使用Python和TensorFlow框架。遇到的最大挑战是数据质量问题，我通过数据清洗和特征工程解决了这个问题..."
                    </div>
                    <div class="recognition-accuracy">识别准确率: 96%</div>
                </div>
            </div>
            
            <!-- 语音分析面板 -->
            <div class="demo-interface">
                <div class="interface-header">
                    <div class="interface-title">语音特征分析</div>
                </div>
                
                <div class="analysis-metrics">
                    <div class="metric-card">
                        <div class="metric-value">156</div>
                        <div class="metric-label">语速 (字/分钟)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">78%</div>
                        <div class="metric-label">音调变化</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3.2</div>
                        <div class="metric-label">停顿频率 (次/分钟)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">积极</div>
                        <div class="metric-label">情感倾向</div>
                    </div>
                </div>
            </div>
            
            <!-- AI思考过程 -->
            <div class="ai-thinking">
                <div class="thinking-header">
                    <span class="thinking-icon">🧠</span>
                    AI分析思考
                </div>
                <div class="thinking-content">
                    正在分析候选人的回答内容和语音特征... 技术描述较为准确，表达逻辑清晰，语速适中，建议进一步询问具体的技术实现细节。
                </div>
            </div>
        </div>

        <!-- 技术规格 -->
        <div class="tech-specs">
            <h3>🔧 技术实现规格</h3>
            <div class="specs-grid">
                <div class="spec-item">
                    <div class="spec-title">语音识别引擎</div>
                    <div class="spec-desc">iFlytek Spark语音识别API，支持实时流式识别</div>
                </div>
                <div class="spec-item">
                    <div class="spec-title">音频处理</div>
                    <div class="spec-desc">WebRTC + Web Audio API，实现实时音频分析</div>
                </div>
                <div class="spec-item">
                    <div class="spec-title">AI分析</div>
                    <div class="spec-desc">iFlytek Spark LLM，智能分析语音内容和特征</div>
                </div>
                <div class="spec-item">
                    <div class="spec-title">前端技术</div>
                    <div class="spec-desc">Vue.js 3 + Element Plus，响应式设计</div>
                </div>
                <div class="spec-item">
                    <div class="spec-title">浏览器支持</div>
                    <div class="spec-desc">Chrome 60+, Firefox 55+, Safari 11+</div>
                </div>
                <div class="spec-item">
                    <div class="spec-title">设备要求</div>
                    <div class="spec-desc">麦克风设备，稳定网络连接</div>
                </div>
            </div>
        </div>

        <!-- 行动号召 -->
        <div class="cta-section">
            <h3>🎯 立即体验语音专项面试</h3>
            <p>感受iFlytek AI技术带来的智能语音面试体验</p>
            <button class="cta-button" onclick="window.open('http://localhost:5173/select-interview-mode', '_blank')">
                开始语音面试
            </button>
        </div>
    </div>
</body>
</html>
