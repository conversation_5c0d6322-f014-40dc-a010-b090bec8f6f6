/**
 * iFlytek 星火大模型智能面试系统 - 品牌一致性检查工具
 * Brand Consistency Checker for iFlytek Spark Interview System
 * 
 * 功能：
 * 1. 检查图标颜色是否符合品牌规范
 * 2. 验证图标尺寸一致性
 * 3. 检查图标语义匹配度
 * 4. 生成品牌一致性报告
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// iFlytek 品牌色彩规范
const iflytekBrandColors = {
  primary: '#1890ff',
  secondary: '#667eea',
  accent: '#0066cc',
  purple: '#4c51bf',
  gradient: '#764ba2',
  
  // 技术领域色彩
  ai: '#0066cc',
  bigdata: '#059669',
  iot: '#dc2626',
  cloud: '#7c3aed',
  
  // 状态色彩
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
}

// 图标语义规范
const iconSemanticRules = {
  // 面试相关
  'interview': ['VideoPlay', 'VideoPause', 'Microphone', 'VideoCamera'],
  'analysis': ['TrendCharts', 'DataBoard', 'Cpu', 'Monitor'],
  'user': ['User', 'OfficeBuilding'],
  'document': ['Document', 'Download', 'Share'],
  'navigation': ['ArrowRight', 'ArrowLeft', 'ArrowUp', 'ArrowDown', 'House'],
  'status': ['Star', 'CircleCheck', 'Clock', 'Loading', 'WarningFilled', 'Close'],
  'control': ['Setting', 'Search', 'Menu', 'View']
}

// 图标尺寸规范
const iconSizeStandards = {
  'meta-item': '14px',
  'panel-header': '16px',
  'control-btn': '14px',
  'control-action-btn': '16px',
  'quick-btn': '18px',
  'avatar-icon': '20px',
  'hero-section': '32px'
}

// 扫描文件中的图标使用
function scanIconUsage(filePath) {
  if (!fs.existsSync(filePath)) {
    return { icons: [], colors: [], sizes: [] }
  }

  const content = fs.readFileSync(filePath, 'utf8')
  const icons = []
  const colors = []
  const sizes = []

  // 匹配图标使用
  const iconRegex = /<el-icon[^>]*>\s*<([A-Z][a-zA-Z]*)\s*\/?\s*>\s*<\/el-icon>/g
  let iconMatch
  while ((iconMatch = iconRegex.exec(content)) !== null) {
    icons.push(iconMatch[1])
  }

  // 匹配颜色使用
  const colorRegex = /color:\s*([#\w-]+)/g
  let colorMatch
  while ((colorMatch = colorRegex.exec(content)) !== null) {
    colors.push(colorMatch[1])
  }

  // 匹配尺寸使用
  const sizeRegex = /font-size:\s*([0-9]+px)/g
  let sizeMatch
  while ((sizeMatch = sizeRegex.exec(content)) !== null) {
    sizes.push(sizeMatch[1])
  }

  return { icons, colors, sizes }
}

// 检查图标语义一致性
function checkIconSemantics(icons) {
  const issues = []
  
  icons.forEach(icon => {
    let semanticMatch = false
    
    for (const [category, validIcons] of Object.entries(iconSemanticRules)) {
      if (validIcons.includes(icon)) {
        semanticMatch = true
        break
      }
    }
    
    if (!semanticMatch) {
      issues.push({
        type: 'semantic',
        icon,
        message: `图标 ${icon} 可能不符合语义规范，建议检查使用场景`
      })
    }
  })
  
  return issues
}

// 检查颜色品牌一致性
function checkColorConsistency(colors) {
  const issues = []
  const brandColorValues = Object.values(iflytekBrandColors)
  
  colors.forEach(color => {
    if (color.startsWith('#') && !brandColorValues.includes(color.toLowerCase())) {
      // 检查是否是品牌色的变体
      const isVariant = brandColorValues.some(brandColor => {
        return color.toLowerCase().includes(brandColor.substring(1, 4))
      })
      
      if (!isVariant) {
        issues.push({
          type: 'color',
          color,
          message: `颜色 ${color} 不符合 iFlytek 品牌色彩规范`,
          suggestion: '建议使用品牌色彩变量'
        })
      }
    }
  })
  
  return issues
}

// 检查尺寸一致性
function checkSizeConsistency(sizes, filePath) {
  const issues = []
  const fileName = path.basename(filePath)
  
  // 检查是否使用了非标准尺寸
  sizes.forEach(size => {
    const sizeValue = parseInt(size)
    const standardSizes = [12, 14, 16, 18, 20, 24, 32, 48]
    
    if (!standardSizes.includes(sizeValue)) {
      issues.push({
        type: 'size',
        size,
        file: fileName,
        message: `尺寸 ${size} 不符合标准规范`,
        suggestion: `建议使用标准尺寸: ${standardSizes.join('px, ')}px`
      })
    }
  })
  
  return issues
}

// 生成品牌一致性报告
function generateBrandReport(results) {
  console.log('🎨 iFlytek 星火面试系统 - 品牌一致性检查报告')
  console.log('=' .repeat(60))
  
  let totalIssues = 0
  let filesChecked = 0
  const issuesByType = {
    semantic: 0,
    color: 0,
    size: 0
  }
  
  results.forEach(({ file, issues }) => {
    if (issues.length > 0) {
      console.log(`\n📄 ${file}:`)
      issues.forEach(issue => {
        totalIssues++
        issuesByType[issue.type]++
        
        const icon = issue.type === 'semantic' ? '🔤' : 
                    issue.type === 'color' ? '🎨' : '📏'
        
        console.log(`   ${icon} ${issue.message}`)
        if (issue.suggestion) {
          console.log(`      💡 ${issue.suggestion}`)
        }
      })
    }
    filesChecked++
  })
  
  console.log('\n📊 检查统计:')
  console.log(`   检查文件数: ${filesChecked}`)
  console.log(`   发现问题数: ${totalIssues}`)
  console.log(`   语义问题: ${issuesByType.semantic}`)
  console.log(`   颜色问题: ${issuesByType.color}`)
  console.log(`   尺寸问题: ${issuesByType.size}`)
  
  console.log('\n🎯 品牌一致性评分:')
  const score = Math.max(0, 100 - (totalIssues * 5))
  console.log(`   评分: ${score}/100`)
  
  if (score >= 90) {
    console.log('   等级: 优秀 ✨')
  } else if (score >= 80) {
    console.log('   等级: 良好 👍')
  } else if (score >= 70) {
    console.log('   等级: 一般 ⚠️')
  } else {
    console.log('   等级: 需要改进 ❌')
  }
  
  console.log('\n📋 改进建议:')
  if (issuesByType.semantic > 0) {
    console.log('   1. 检查图标语义匹配度，确保图标与功能对应')
  }
  if (issuesByType.color > 0) {
    console.log('   2. 使用 iFlytek 品牌色彩变量，避免硬编码颜色')
  }
  if (issuesByType.size > 0) {
    console.log('   3. 统一图标尺寸标准，使用预定义尺寸变量')
  }
  
  console.log('\n🔗 参考资源:')
  console.log('   - iFlytek 品牌规范: /src/config/icon-system-config.js')
  console.log('   - 图标使用指南: /icon-system-guide.md')
  console.log('   - Element Plus 图标: https://element-plus.org/zh-CN/component/icon.html')
  
  return { totalIssues, filesChecked, score, issuesByType }
}

// 递归扫描目录
function scanDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.css'))) {
        const relativePath = path.relative(__dirname, fullPath)
        const usage = scanIconUsage(fullPath)
        
        const issues = [
          ...checkIconSemantics(usage.icons),
          ...checkColorConsistency(usage.colors),
          ...checkSizeConsistency(usage.sizes, fullPath)
        ]
        
        results.push({
          file: relativePath,
          usage,
          issues
        })
      }
    }
  }
  
  scan(dir)
  return results
}

// 主函数
function main() {
  console.log('🚀 启动 iFlytek 品牌一致性检查工具...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  const report = generateBrandReport(results)
  
  console.log('\n✅ 品牌一致性检查完成！')
  
  return report
}

// 运行检查工具
main()

export {
  iflytekBrandColors,
  iconSemanticRules,
  iconSizeStandards,
  scanIconUsage,
  checkIconSemantics,
  checkColorConsistency,
  checkSizeConsistency,
  generateBrandReport
}
