#!/usr/bin/env node

/**
 * 浏览器图标实际检查工具
 * 生成JavaScript代码在浏览器控制台中运行，检查图标实际显示状态
 */

console.log(`
🔍 多模态面试评估系统 - 浏览器图标实际检查工具
=====================================================

请在浏览器中打开 http://localhost:5173，然后：
1. 按F12打开开发者工具
2. 切换到Console标签页
3. 复制并运行以下JavaScript代码：

`);

console.log(`
// ===== 浏览器控制台检查代码 =====
(function() {
    console.log('🎯 开始检查图标实际显示状态...');
    
    // 检查目标图标的实际计算样式
    const iconChecks = [
        {
            name: '技术图标',
            selector: '.ai-tech-icon .el-icon',
            expectedSize: '20px',
            context: '技术展示卡片'
        },
        {
            name: '步骤图标', 
            selector: '.ai-step-icon .el-icon',
            expectedSize: '18px',
            context: '流程步骤展示'
        },
        {
            name: 'CTA选项图标',
            selector: '.ai-cta-option-icon .el-icon', 
            expectedSize: '18px',
            context: '行动号召选项'
        },
        {
            name: '特色标签图标',
            selector: '.feature-tag .el-icon',
            expectedSize: '14px',
            context: '功能特色标签'
        },
        {
            name: '按钮图标',
            selector: '.btn-primary-enhanced .el-icon',
            expectedSize: '16px',
            context: '主要操作按钮'
        },
        {
            name: '统计卡片图标',
            selector: '.stats-icon .el-icon',
            expectedSize: '24px',
            context: '数据统计展示'
        }
    ];
    
    let totalChecked = 0;
    let correctSized = 0;
    let oversized = [];
    let missing = [];
    
    iconChecks.forEach(check => {
        const elements = document.querySelectorAll(check.selector);
        
        if (elements.length === 0) {
            missing.push(check);
            console.log(\`❌ \${check.name}: 未找到元素 (\${check.selector})\`);
            return;
        }
        
        elements.forEach((element, index) => {
            totalChecked++;
            const computedStyle = window.getComputedStyle(element);
            const actualSize = computedStyle.fontSize;
            const actualSizeNum = parseInt(actualSize);
            const expectedSizeNum = parseInt(check.expectedSize);
            
            console.log(\`\${index + 1}. \${check.name} [\${check.context}]:\`);
            console.log(\`   选择器: \${check.selector}\`);
            console.log(\`   期望尺寸: \${check.expectedSize}\`);
            console.log(\`   实际尺寸: \${actualSize}\`);
            
            if (actualSize === check.expectedSize) {
                correctSized++;
                console.log(\`   ✅ 尺寸正确\`);
            } else {
                const status = actualSizeNum > expectedSizeNum ? '过大' : '过小';
                console.log(\`   ❌ 尺寸\${status} (差异: \${actualSizeNum - expectedSizeNum}px)\`);
                
                if (actualSizeNum > expectedSizeNum) {
                    oversized.push({
                        ...check,
                        actualSize,
                        difference: actualSizeNum - expectedSizeNum
                    });
                }
            }
            
            // 检查样式来源
            const styleSheets = Array.from(document.styleSheets);
            console.log(\`   样式来源检查:\`);
            
            // 检查是否有内联样式
            if (element.style.fontSize) {
                console.log(\`     - 内联样式: \${element.style.fontSize}\`);
            }
            
            // 检查计算样式的其他属性
            console.log(\`     - 计算样式: \${actualSize}\`);
            console.log(\`     - 字体族: \${computedStyle.fontFamily}\`);
            console.log(\`     - 显示方式: \${computedStyle.display}\`);
            console.log(\`     - 垂直对齐: \${computedStyle.verticalAlign}\`);
            
            console.log(\`\`);
        });
    });
    
    // 生成检查报告
    console.log(\`📊 检查报告:\`);
    console.log(\`=\`.repeat(40));
    console.log(\`总检查数: \${totalChecked}\`);
    console.log(\`正确尺寸: \${correctSized}\`);
    console.log(\`过大图标: \${oversized.length}\`);
    console.log(\`缺失元素: \${missing.length}\`);
    console.log(\`正确率: \${Math.round(correctSized/totalChecked*100)}%\`);
    
    if (oversized.length > 0) {
        console.log(\`\\n⚠️ 仍然过大的图标:\`);
        oversized.forEach(icon => {
            console.log(\`- \${icon.name}: \${icon.actualSize} (应为\${icon.expectedSize}, 超出\${icon.difference}px)\`);
        });
    }
    
    if (missing.length > 0) {
        console.log(\`\\n❌ 缺失的图标元素:\`);
        missing.forEach(icon => {
            console.log(\`- \${icon.name}: \${icon.selector}\`);
        });
    }
    
    // 检查响应式样式
    console.log(\`\\n📱 响应式样式检查:\`);
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    console.log(\`当前屏幕宽度: \${window.innerWidth}px\`);
    console.log(\`移动端媒体查询匹配: \${mediaQuery.matches}\`);
    
    if (mediaQuery.matches) {
        console.log(\`当前应用移动端样式\`);
    } else {
        console.log(\`当前应用桌面端样式\`);
    }
    
    console.log(\`\\n✅ 检查完成！\`);
    
    // 返回检查结果供进一步分析
    return {
        totalChecked,
        correctSized,
        oversized,
        missing,
        successRate: Math.round(correctSized/totalChecked*100)
    };
})();

`);

console.log(`
===== 检查完成后的操作指南 =====

1. 如果发现图标仍然过大：
   - 记录具体的选择器和实际尺寸
   - 检查是否有样式冲突或覆盖
   - 查看计算样式的来源

2. 如果发现缺失元素：
   - 确认页面是否完全加载
   - 检查选择器是否正确
   - 验证组件是否正确渲染

3. 响应式检查：
   - 调整浏览器窗口大小
   - 重新运行检查代码
   - 验证移动端样式是否生效

4. 样式冲突诊断：
   - 右键点击问题图标 → 检查元素
   - 查看Styles面板中的样式层叠
   - 识别被覆盖的样式规则

请运行上述代码并将结果反馈给我，我将根据实际情况提供针对性的修复方案。
`);
