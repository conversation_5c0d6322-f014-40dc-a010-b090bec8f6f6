{"version": 3, "file": "use-space.mjs", "sources": ["../../../../../../packages/components/space/src/use-space.ts"], "sourcesContent": ["import { computed, ref, watchEffect } from 'vue'\nimport { isArray, isNumber } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { SpaceProps } from './space'\nimport type { CSSProperties, StyleValue } from 'vue'\n\nconst SIZE_MAP = {\n  small: 8,\n  default: 12,\n  large: 16,\n} as const\n\nexport function useSpace(props: SpaceProps) {\n  const ns = useNamespace('space')\n\n  const classes = computed(() => [ns.b(), ns.m(props.direction), props.class])\n\n  const horizontalSize = ref(0)\n  const verticalSize = ref(0)\n\n  const containerStyle = computed<StyleValue>(() => {\n    const wrapKls: CSSProperties =\n      props.wrap || props.fill ? { flexWrap: 'wrap' } : {}\n    const alignment: CSSProperties = {\n      alignItems: props.alignment,\n    }\n    const gap: CSSProperties = {\n      rowGap: `${verticalSize.value}px`,\n      columnGap: `${horizontalSize.value}px`,\n    }\n    return [wrapKls, alignment, gap, props.style]\n  })\n\n  const itemStyle = computed<StyleValue>(() => {\n    return props.fill ? { flexGrow: 1, minWidth: `${props.fillRatio}%` } : {}\n  })\n\n  watchEffect(() => {\n    const { size = 'small', wrap, direction: dir, fill } = props\n\n    // when the specified size have been given\n    if (isArray(size)) {\n      const [h = 0, v = 0] = size\n      horizontalSize.value = h\n      verticalSize.value = v\n    } else {\n      let val: number\n      if (isNumber(size)) {\n        val = size\n      } else {\n        val = SIZE_MAP[size || 'small'] || SIZE_MAP.small\n      }\n\n      if ((wrap || fill) && dir === 'horizontal') {\n        horizontalSize.value = verticalSize.value = val\n      } else {\n        if (dir === 'horizontal') {\n          horizontalSize.value = val\n          verticalSize.value = 0\n        } else {\n          verticalSize.value = val\n          horizontalSize.value = 0\n        }\n      }\n    }\n  })\n\n  return {\n    classes,\n    containerStyle,\n    itemStyle,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,QAAQ,GAAG;AACjB,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,KAAK,EAAE,EAAE;AACX,CAAC,CAAC;AACK,SAAS,QAAQ,CAAC,KAAK,EAAE;AAChC,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/E,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,UAAU,EAAE,KAAK,CAAC,SAAS;AACjC,KAAK,CAAC;AACN,IAAI,MAAM,GAAG,GAAG;AAChB,MAAM,MAAM,EAAE,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;AACvC,MAAM,SAAS,EAAE,CAAC,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;AAC5C,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,OAAO,KAAK,CAAC,IAAI,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;AAC9E,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,MAAM,EAAE,IAAI,GAAG,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACjE,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAClC,MAAM,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;AAC/B,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7B,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,CAAC;AACd,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC1B,QAAQ,GAAG,GAAG,IAAI,CAAC;AACnB,OAAO,MAAM;AACb,QAAQ,GAAG,GAAG,QAAQ,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC;AAC1D,OAAO;AACP,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,YAAY,EAAE;AAClD,QAAQ,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC;AACxD,OAAO,MAAM;AACb,QAAQ,IAAI,GAAG,KAAK,YAAY,EAAE;AAClC,UAAU,cAAc,CAAC,KAAK,GAAG,GAAG,CAAC;AACrC,UAAU,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;AACjC,SAAS,MAAM;AACf,UAAU,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC;AACnC,UAAU,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;AACnC,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,cAAc;AAClB,IAAI,SAAS;AACb,GAAG,CAAC;AACJ;;;;"}