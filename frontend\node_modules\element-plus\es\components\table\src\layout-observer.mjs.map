{"version": 3, "file": "layout-observer.mjs", "sources": ["../../../../../../packages/components/table/src/layout-observer.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  computed,\n  getCurrentInstance,\n  onBeforeMount,\n  onMounted,\n  onUnmounted,\n  onUpdated,\n} from 'vue'\n\nimport type { TableHeader } from './table-header'\nimport type TableLayout from './table-layout'\nimport type { Table } from './table/defaults'\n\nfunction useLayoutObserver<T>(root: Table<T>) {\n  const instance = getCurrentInstance() as TableHeader\n  onBeforeMount(() => {\n    tableLayout.value.addObserver(instance)\n  })\n  onMounted(() => {\n    onColumnsChange(tableLayout.value)\n    onScrollableChange(tableLayout.value)\n  })\n  onUpdated(() => {\n    onColumnsChange(tableLayout.value)\n    onScrollableChange(tableLayout.value)\n  })\n  onUnmounted(() => {\n    tableLayout.value.removeObserver(instance)\n  })\n  const tableLayout = computed(() => {\n    const layout = root.layout as TableLayout<T>\n    if (!layout) {\n      throw new Error('Can not find table layout.')\n    }\n    return layout\n  })\n  const onColumnsChange = (layout: TableLayout<T>) => {\n    const cols = root.vnode.el?.querySelectorAll('colgroup > col') || []\n    if (!cols.length) return\n    const flattenColumns = layout.getFlattenColumns()\n    const columnsMap = {}\n    flattenColumns.forEach((column) => {\n      columnsMap[column.id] = column\n    })\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i]\n      const name = col.getAttribute('name')\n      const column = columnsMap[name]\n      if (column) {\n        col.setAttribute('width', column.realWidth || column.width)\n      }\n    }\n  }\n\n  const onScrollableChange = (layout: TableLayout<T>) => {\n    const cols =\n      root.vnode.el?.querySelectorAll('colgroup > col[name=gutter]') || []\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i]\n      col.setAttribute('width', layout.scrollY.value ? layout.gutterWidth : '0')\n    }\n    const ths = root.vnode.el?.querySelectorAll('th.gutter') || []\n    for (let i = 0, j = ths.length; i < j; i++) {\n      const th = ths[i]\n      th.style.width = layout.scrollY.value ? `${layout.gutterWidth}px` : '0'\n      th.style.display = layout.scrollY.value ? '' : 'none'\n    }\n  }\n\n  return {\n    tableLayout: tableLayout.value,\n    onColumnsChange,\n    onScrollableChange,\n  }\n}\n\nexport default useLayoutObserver\n"], "names": [], "mappings": ";;AAQA,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACjC,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,aAAa,CAAC,MAAM;AACtB,IAAI,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC5C,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACvC,IAAI,kBAAkB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACvC,IAAI,kBAAkB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAC/C,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,CAAC,MAAM,KAAK;AACtC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AACvG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,MAAM,OAAO;AACb,IAAI,MAAM,cAAc,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC;AACtD,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC;AAC1B,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACvC,MAAM,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;AACrC,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC5C,MAAM,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AACpE,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,MAAM,KAAK;AACzC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;AACpH,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,MAAM,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;AACjF,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;AACjG,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,MAAM,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACxB,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;AAC9E,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,GAAG,MAAM,CAAC;AAC5D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,WAAW,EAAE,WAAW,CAAC,KAAK;AAClC,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,GAAG,CAAC;AACJ;;;;"}