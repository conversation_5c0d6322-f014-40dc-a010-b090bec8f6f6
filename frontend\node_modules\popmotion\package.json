{"name": "popmotion", "version": "11.0.5", "description": "The animator's toolbox", "author": "<PERSON>", "homepage": "https://popmotion.io", "main": "dist/popmotion.cjs.js", "types": "lib/index.d.ts", "module": "dist/es/index.mjs", "jsnext:main": "dist/es/index.mjs", "unpkg": "./dist/popmotion.min.js", "exports": {".": {"types": "./lib/index.d.ts", "import": "./dist/es/index.mjs", "require": "./dist/popmotion.cjs.js", "default": "./dist/popmotion.cjs.js"}, "./package.json": "./package.json"}, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/Popmotion/popmotion/tree/master/packages/popmotion"}, "bugs": {"url": "https://github.com/Popmotion/popmotion/issues"}, "keywords": ["animation", "ux", "ui", "popmotion", "canvas animation", "dom animation", "dom", "pointer tracking", "mouse", "mouse tracking", "touch", "touch tracking", "physics", "interaction", "interface", "svg"], "analyze": true, "license": "MIT", "scripts": {"build": "tsc -p . && rollup -c && yarn measure", "postbuild": "yarn babel $npm_package_module --out-file $npm_package_module --no-babelrc --plugins annotate-pure-calls", "dev": "rollup -c -w", "prepublishOnly": "yarn test && yarn build", "measure": "gzip -c $npm_package_unpkg | wc -c", "test": "jest --coverage --maxWorkers=2", "publish-beta": "npm publish --tag beta"}, "dependencies": {"framesync": "6.1.2", "hey-listen": "^1.0.8", "style-value-types": "5.1.2", "tslib": "2.4.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^11.0.1", "tslint-circular-dependencies": "^0.1.0", "typescript": "^4.2.3"}, "prettier": {"printWidth": 80, "tabWidth": 4, "semi": false, "trailingComma": "es5"}, "jest": {"moduleFileExtensions": ["ts", "js"], "moduleNameMapper": {"style-value-types": "<rootDir>/../../style-value-types/src", "framesync": "<rootDir>/../../framesync/src"}, "transform": {"\\.(ts)$": "../../../node_modules/ts-jest/preprocessor.js"}, "testRegex": "/__tests__/.*\\.test.(ts|js)$", "rootDir": "src", "collectCoverageFrom": ["**/*.{js,jsx,ts,tsx}", "!**/node_modules/**", "!**/__tests__/**", "!**/worklet/**"], "coverageDirectory": "<rootDir>/../coverage"}}