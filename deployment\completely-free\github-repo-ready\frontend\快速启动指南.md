# iFlytek面试系统快速启动指南

## 🚀 一键启动（推荐）

### Windows用户
1. 双击运行：`启动iFlytek面试系统.bat`
2. 等待自动安装依赖和启动服务器
3. 浏览器会自动打开系统主页

### Mac/Linux用户
```bash
# 在frontend目录中运行
node start-iflytek-system.js
```

## 📋 手动启动步骤

如果一键启动失败，请按以下步骤操作：

### 1. 检查环境
```bash
# 检查Node.js版本（需要16+）
node --version

# 如果没有Node.js，请下载安装：
# https://nodejs.org/
```

### 2. 安装依赖
```bash
# 进入项目目录
cd frontend

# 安装依赖
npm install
```

### 3. 启动开发服务器
```bash
# 启动开发服务器
npm run dev
```

### 4. 访问系统
启动成功后，在浏览器中访问：
- **主页**：http://localhost:8080
- **系统测试**：http://localhost:8080/system-test

## 🎯 功能测试地址

### 核心功能页面
- **面试页面**：http://localhost:8080/text-interview
- **报告中心**：http://localhost:8080/report-center  
- **报告详情**：http://localhost:8080/report/1

### 新功能测试
- **AI智能提示**：在面试页面中点击"AI提示"按钮
- **报告导出**：在报告中心点击"下载"或"批量导出"
- **企业分享**：在报告详情页点击"分享报告"
- **系统测试**：http://localhost:8080/system-test

## ❌ 常见问题

### 问题1：ERR_CONNECTION_REFUSED
**解决**：确保开发服务器已启动，运行 `npm run dev`

### 问题2：端口被占用
**解决**：Vite会自动选择其他端口，查看控制台显示的实际地址

### 问题3：依赖安装失败
**解决**：
```bash
# 清除缓存后重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 问题4：页面白屏
**解决**：
1. 按F12打开开发者工具查看错误
2. 强制刷新：Ctrl+Shift+R
3. 清除浏览器缓存

## 💡 启动成功标志

看到以下信息说明启动成功：
```
✅ Node.js环境检查通过
✅ 项目依赖已存在  
✅ 端口8080可用
🚀 正在启动iFlytek面试系统...

VITE v4.4.5  ready in 1234 ms

➜  Local:   http://localhost:8080/
➜  Network: use --host to expose
```

## 🧪 验证新功能

### 1. AI智能提示系统
1. 访问：http://localhost:8080/text-interview
2. 输入问题："请介绍您在AI领域的项目经验"
3. 输入回答："不知道，没有接触过"
4. 点击"AI提示"按钮
5. 验证是否生成智能引导提示

### 2. 报告导出功能
1. 访问：http://localhost:8080/report-center
2. 点击任意报告的"下载"按钮
3. 选择Excel或CSV格式
4. 验证文件是否成功下载

### 3. 企业分享功能
1. 访问：http://localhost:8080/report/1
2. 点击"分享报告"按钮
3. 配置分享设置（标题、有效期、权限等）
4. 验证分享链接是否生成并复制到剪贴板

### 4. 系统集成测试
1. 访问：http://localhost:8080/system-test
2. 点击"开始完整测试"按钮
3. 观察测试进度和结果
4. 验证通过率是否≥90%

## 📞 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 阅读`启动故障排除指南.md`
3. 运行系统测试获取诊断信息
4. 查看`FEATURE_VALIDATION.md`了解功能详情

---

**🎉 恭喜！您已成功启动iFlytek星火智能面试系统！**

现在可以体验全新的AI智能提示和报告导出分享功能了。
