{"version": 3, "file": "breadcrumb-item2.js", "sources": ["../../../../../../packages/components/breadcrumb/src/breadcrumb-item.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('item')\">\n    <span\n      ref=\"link\"\n      :class=\"[ns.e('inner'), ns.is('link', !!to)]\"\n      role=\"link\"\n      @click=\"onClick\"\n    >\n      <slot />\n    </span>\n    <el-icon v-if=\"breadcrumbContext?.separatorIcon\" :class=\"ns.e('separator')\">\n      <component :is=\"breadcrumbContext.separatorIcon\" />\n    </el-icon>\n    <span v-else :class=\"ns.e('separator')\" role=\"presentation\">\n      {{ breadcrumbContext?.separator }}\n    </span>\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { getCurrentInstance, inject, ref } from 'vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { breadcrumbKey } from './constants'\nimport { breadcrumbItemProps } from './breadcrumb-item'\n\nimport type { Router } from 'vue-router'\n\ndefineOptions({\n  name: 'ElBreadcrumbItem',\n})\n\nconst props = defineProps(breadcrumbItemProps)\n\nconst instance = getCurrentInstance()!\nconst breadcrumbContext = inject(breadcrumbKey, undefined)\nconst ns = useNamespace('breadcrumb')\n\nconst router = instance.appContext.config.globalProperties.$router as Router\n\nconst link = ref<HTMLSpanElement>()\n\nconst onClick = () => {\n  if (!props.to || !router) return\n  props.replace ? router.replace(props.to) : router.push(props.to)\n}\n</script>\n"], "names": ["getCurrentInstance", "inject", "breadcrumbKey", "useNamespace", "ref"], "mappings": ";;;;;;;;;;;uCA4Bc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,WAAWA,sBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,iBAAA,GAAoBC,UAAO,CAAAC,uBAAA,EAAe,KAAS,CAAA,CAAA,CAAA;AACzD,IAAM,MAAA,EAAA,GAAKC,mBAAa,YAAY,CAAA,CAAA;AAEpC,IAAA,MAAM,MAAS,GAAA,QAAA,CAAS,UAAW,CAAA,MAAA,CAAO,gBAAiB,CAAA,OAAA,CAAA;AAE3D,IAAA,MAAM,OAAOC,OAAqB,EAAA,CAAA;AAElC,IAAA,MAAM,UAAU,MAAM;AACpB,MAAA,IAAI,CAAC,KAAA,CAAM,EAAM,IAAA,CAAC,MAAQ;AAC1B,QAAM,OAAA;AAAyD,MACjE,KAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA,CAAA,KAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}