<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Vue应用测试</h1>
        
        <div class="status">
            <strong>测试Vue应用是否正常工作</strong>
        </div>
        
        <button onclick="testMainApp()">测试主应用</button>
        <button onclick="testHomePage()">测试首页</button>
        <button onclick="testDemoPage()">测试演示页</button>
        
        <div id="test-results" style="margin-top: 20px;">
            <h3>测试结果:</h3>
            <div id="results-container"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const container = document.getElementById('results-container');
            const entry = document.createElement('div');
            entry.style.padding = '5px';
            entry.style.borderBottom = '1px solid #eee';
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
        }
        
        function testMainApp() {
            log('🔍 测试主应用...');
            try {
                // 尝试访问主应用
                fetch('/')
                    .then(response => {
                        if (response.ok) {
                            return response.text();
                        }
                        throw new Error(`HTTP ${response.status}`);
                    })
                    .then(html => {
                        if (html.includes('id="app"')) {
                            log('✅ 主应用HTML结构正常');
                            // 尝试在新窗口打开
                            window.open('/', '_blank');
                        } else {
                            log('❌ 主应用HTML结构异常');
                        }
                    })
                    .catch(error => {
                        log(`❌ 主应用测试失败: ${error.message}`);
                    });
            } catch (error) {
                log(`❌ 主应用测试异常: ${error.message}`);
            }
        }
        
        function testHomePage() {
            log('🏠 测试首页组件...');
            window.open('/#/', '_blank');
        }
        
        function testDemoPage() {
            log('🎬 测试演示页组件...');
            window.open('/#/demo', '_blank');
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 Vue测试页面已加载');
            log('🚀 开始自动测试...');
            
            setTimeout(() => {
                testMainApp();
            }, 1000);
        });
    </script>
</body>
</html>
