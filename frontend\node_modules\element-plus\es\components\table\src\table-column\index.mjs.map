{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../packages/components/table/src/table-column/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  Fragment,\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  onBeforeMount,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n} from 'vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { isArray, isString, isUndefined } from '@element-plus/utils'\nimport { cellStarts } from '../config'\nimport { compose, mergeOptions } from '../util'\nimport useWatcher from './watcher-helper'\nimport useRender from './render-helper'\nimport defaultProps from './defaults'\nimport type { TableColumn, TableColumnCtx } from './defaults'\n\nimport type { DefaultRow } from '../table/defaults'\n\nlet columnIdSeed = 1\n\nexport default defineComponent({\n  name: 'ElTableColumn',\n  components: {\n    ElCheckbox,\n  },\n  props: defaultProps,\n  setup(props, { slots }) {\n    const instance = getCurrentInstance() as TableColumn<DefaultRow>\n    const columnConfig = ref<Partial<TableColumnCtx<DefaultRow>>>({})\n    const owner = computed(() => {\n      let parent = instance.parent as any\n      while (parent && !parent.tableId) {\n        parent = parent.parent\n      }\n      return parent\n    })\n\n    const { registerNormalWatchers, registerComplexWatchers } = useWatcher(\n      owner,\n      props\n    )\n    const {\n      columnId,\n      isSubColumn,\n      realHeaderAlign,\n      columnOrTableParent,\n      setColumnWidth,\n      setColumnForcedProps,\n      setColumnRenders,\n      getPropsData,\n      getColumnElIndex,\n      realAlign,\n      updateColumnOrder,\n    } = useRender(props as unknown as TableColumnCtx<unknown>, slots, owner)\n\n    const parent = columnOrTableParent.value\n    columnId.value = `${\n      parent.tableId || parent.columnId\n    }_column_${columnIdSeed++}`\n    onBeforeMount(() => {\n      isSubColumn.value = owner.value !== parent\n\n      const type = props.type || 'default'\n      const sortable = props.sortable === '' ? true : props.sortable\n      //The selection column should not be affected by `showOverflowTooltip`.\n      const showOverflowTooltip =\n        type === 'selection'\n          ? false\n          : isUndefined(props.showOverflowTooltip)\n          ? parent.props.showOverflowTooltip\n          : props.showOverflowTooltip\n      const tooltipFormatter = isUndefined(props.tooltipFormatter)\n        ? parent.props.tooltipFormatter\n        : props.tooltipFormatter\n      const defaults = {\n        ...cellStarts[type],\n        id: columnId.value,\n        type,\n        property: props.prop || props.property,\n        align: realAlign,\n        headerAlign: realHeaderAlign,\n        showOverflowTooltip,\n        tooltipFormatter,\n        // filter 相关属性\n        filterable: props.filters || props.filterMethod,\n        filteredValue: [],\n        filterPlacement: '',\n        filterClassName: '',\n        isColumnGroup: false,\n        isSubColumn: false,\n        filterOpened: false,\n        // sort 相关属性\n        sortable,\n        // index 列\n        index: props.index,\n        // <el-table-column key=\"xxx\" />\n        rawColumnKey: instance.vnode.key,\n      }\n\n      const basicProps = [\n        'columnKey',\n        'label',\n        'className',\n        'labelClassName',\n        'type',\n        'renderHeader',\n        'formatter',\n        'fixed',\n        'resizable',\n      ]\n      const sortProps = ['sortMethod', 'sortBy', 'sortOrders']\n      const selectProps = ['selectable', 'reserveSelection']\n      const filterProps = [\n        'filterMethod',\n        'filters',\n        'filterMultiple',\n        'filterOpened',\n        'filteredValue',\n        'filterPlacement',\n        'filterClassName',\n      ]\n\n      let column = getPropsData(basicProps, sortProps, selectProps, filterProps)\n\n      column = mergeOptions(defaults, column)\n      // 注意 compose 中函数执行的顺序是从右到左\n      const chains = compose(\n        setColumnRenders,\n        setColumnWidth,\n        setColumnForcedProps\n      )\n      column = chains(column)\n      columnConfig.value = column\n\n      // 注册 watcher\n      registerNormalWatchers()\n      registerComplexWatchers()\n    })\n    onMounted(() => {\n      const parent = columnOrTableParent.value\n      const children = isSubColumn.value\n        ? parent.vnode.el.children\n        : parent.refs.hiddenColumns?.children\n      const getColumnIndex = () =>\n        getColumnElIndex(children || [], instance.vnode.el)\n      columnConfig.value.getColumnIndex = getColumnIndex\n      const columnIndex = getColumnIndex()\n      columnIndex > -1 &&\n        owner.value.store.commit(\n          'insertColumn',\n          columnConfig.value,\n          isSubColumn.value ? parent.columnConfig.value : null,\n          updateColumnOrder\n        )\n    })\n    onBeforeUnmount(() => {\n      const getColumnIndex = columnConfig.value.getColumnIndex\n      const columnIndex = getColumnIndex ? getColumnIndex() : -1\n      columnIndex > -1 &&\n        owner.value.store.commit(\n          'removeColumn',\n          columnConfig.value,\n          isSubColumn.value ? parent.columnConfig.value : null,\n          updateColumnOrder\n        )\n    })\n    instance.columnId = columnId.value\n\n    instance.columnConfig = columnConfig\n    return\n  },\n  render() {\n    try {\n      const renderDefault = this.$slots.default?.({\n        row: {},\n        column: {},\n        $index: -1,\n      })\n      const children = []\n      if (isArray(renderDefault)) {\n        for (const childNode of renderDefault) {\n          if (\n            childNode.type?.name === 'ElTableColumn' ||\n            childNode.shapeFlag & 2\n          ) {\n            children.push(childNode)\n          } else if (\n            childNode.type === Fragment &&\n            isArray(childNode.children)\n          ) {\n            childNode.children.forEach((vnode) => {\n              // No rendering when vnode is dynamic slot or text\n              if (vnode?.patchFlag !== 1024 && !isString(vnode?.children)) {\n                children.push(vnode)\n              }\n            })\n          }\n        }\n      }\n      const vnode = h('div', children)\n      return vnode\n    } catch {\n      return h('div', [])\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAkBA,IAAI,YAAY,GAAG,CAAC,CAAC;AACrB,oBAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,eAAe;AACvB,EAAE,UAAU,EAAE;AACd,IAAI,UAAU;AACd,GAAG;AACH,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE;AAC1B,IAAI,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AAC1C,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACjC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;AACjC,MAAM,IAAI,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;AACpC,MAAM,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC1C,QAAQ,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;AACjC,OAAO;AACP,MAAM,OAAO,OAAO,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,EAAE,sBAAsB,EAAE,uBAAuB,EAAE,GAAG,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACzF,IAAI,MAAM;AACV,MAAM,QAAQ;AACd,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,oBAAoB;AAC1B,MAAM,gBAAgB;AACtB,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,SAAS;AACf,MAAM,iBAAiB;AACvB,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACvC,IAAI,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,CAAC;AAC7C,IAAI,QAAQ,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AACrF,IAAI,aAAa,CAAC,MAAM;AACxB,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,MAAM,CAAC;AACjD,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;AAC3C,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,KAAK,EAAE,GAAG,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC;AACrE,MAAM,MAAM,mBAAmB,GAAG,IAAI,KAAK,WAAW,GAAG,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,mBAAmB,CAAC;AACvK,MAAM,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC;AAC5H,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;AAC3B,QAAQ,EAAE,EAAE,QAAQ,CAAC,KAAK;AAC1B,QAAQ,IAAI;AACZ,QAAQ,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ;AAC9C,QAAQ,KAAK,EAAE,SAAS;AACxB,QAAQ,WAAW,EAAE,eAAe;AACpC,QAAQ,mBAAmB;AAC3B,QAAQ,gBAAgB;AACxB,QAAQ,UAAU,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,YAAY;AACvD,QAAQ,aAAa,EAAE,EAAE;AACzB,QAAQ,eAAe,EAAE,EAAE;AAC3B,QAAQ,eAAe,EAAE,EAAE;AAC3B,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,WAAW,EAAE,KAAK;AAC1B,QAAQ,YAAY,EAAE,KAAK;AAC3B,QAAQ,QAAQ;AAChB,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG;AACxC,OAAO,CAAC;AACR,MAAM,MAAM,UAAU,GAAG;AACzB,QAAQ,WAAW;AACnB,QAAQ,OAAO;AACf,QAAQ,WAAW;AACnB,QAAQ,gBAAgB;AACxB,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,WAAW;AACnB,QAAQ,OAAO;AACf,QAAQ,WAAW;AACnB,OAAO,CAAC;AACR,MAAM,MAAM,SAAS,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AAC/D,MAAM,MAAM,WAAW,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;AAC7D,MAAM,MAAM,WAAW,GAAG;AAC1B,QAAQ,cAAc;AACtB,QAAQ,SAAS;AACjB,QAAQ,gBAAgB;AACxB,QAAQ,cAAc;AACtB,QAAQ,eAAe;AACvB,QAAQ,iBAAiB;AACzB,QAAQ,iBAAiB;AACzB,OAAO,CAAC;AACR,MAAM,IAAI,MAAM,GAAG,YAAY,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AACjF,MAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC9C,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,cAAc,EAAE,oBAAoB,CAAC,CAAC;AACrF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,MAAM,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAClC,MAAM,sBAAsB,EAAE,CAAC;AAC/B,MAAM,uBAAuB,EAAE,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,OAAO,GAAG,mBAAmB,CAAC,KAAK,CAAC;AAChD,MAAM,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;AACxI,MAAM,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC,QAAQ,IAAI,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACvF,MAAM,YAAY,CAAC,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;AACzD,MAAM,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AAC3C,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,EAAE,iBAAiB,CAAC,CAAC;AACjK,KAAK,CAAC,CAAC;AACP,IAAI,eAAe,CAAC,MAAM;AAC1B,MAAM,MAAM,cAAc,GAAG,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC;AAC/D,MAAM,MAAM,WAAW,GAAG,cAAc,GAAG,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC;AACjE,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,EAAE,iBAAiB,CAAC,CAAC;AAChK,KAAK,CAAC,CAAC;AACP,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;AACvC,IAAI,QAAQ,CAAC,YAAY,GAAG,YAAY,CAAC;AACzC,IAAI,OAAO;AACX,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAI,IAAI;AACR,MAAM,MAAM,aAAa,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE;AAC7F,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,MAAM,EAAE,EAAE;AAClB,QAAQ,MAAM,EAAE,CAAC,CAAC;AAClB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC1B,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE;AAClC,QAAQ,KAAK,MAAM,SAAS,IAAI,aAAa,EAAE;AAC/C,UAAU,IAAI,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,MAAM,eAAe,IAAI,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE;AACjH,YAAY,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,WAAW,MAAM,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;AACjF,YAAY,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACnD,cAAc,IAAI,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE;AACjI,gBAAgB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtC,eAAe;AACf,aAAa,CAAC,CAAC;AACf,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACvC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG;AACH,CAAC,CAAC;;;;"}