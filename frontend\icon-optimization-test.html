<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标优化测试 - iFlytek AI面试系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        .test-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .icon-test-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .icon-container {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25);
        }
        
        .icon-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .optimization-status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .icon-container {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
            
            .icon-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; margin-bottom: 40px;">🎯 iFlytek AI面试系统 - 图标优化测试</h1>
        
        <div class="test-section">
            <h2 class="test-title">📊 优化后的图标尺寸展示</h2>
            <p>以下展示了优化后的图标尺寸，确保与中文界面协调且不影响页面布局：</p>
            
            <div class="icon-grid">
                <div class="icon-test-item">
                    <div class="icon-container">⚙️</div>
                    <div class="icon-label">技术图标 (60px容器)</div>
                </div>
                
                <div class="icon-test-item">
                    <div class="icon-container" style="width: 50px; height: 50px; font-size: 20px;">🎯</div>
                    <div class="icon-label">CTA图标 (50px容器)</div>
                </div>
                
                <div class="icon-test-item">
                    <div class="icon-container" style="width: 40px; height: 40px; font-size: 18px;">📊</div>
                    <div class="icon-label">统计图标 (40px容器)</div>
                </div>
                
                <div class="icon-test-item">
                    <div class="icon-container" style="width: 70px; height: 70px; font-size: 22px;">🚀</div>
                    <div class="icon-label">步骤图标 (70px容器)</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">✅ 优化效果确认</h2>

            <div class="optimization-status">
                <div class="status-item">
                    <span class="status-icon">✅</span>
                    <span><strong>尺寸调整：</strong>技术图标从90px缩小到60px，减少33%</span>
                </div>

                <div class="status-item">
                    <span class="status-icon">✅</span>
                    <span><strong>场景图标：</strong>从4.5rem缩小到2.5rem，减少44%</span>
                </div>

                <div class="status-item">
                    <span class="status-icon">✅</span>
                    <span><strong>全局限制：</strong>所有图标最大尺寸限制为32px</span>
                </div>

                <div class="status-item">
                    <span class="status-icon">✅</span>
                    <span><strong>Setting图标修复：</strong>使用内联样式强制控制尺寸</span>
                </div>

                <div class="status-item">
                    <span class="status-icon">✅</span>
                    <span><strong>响应式适配：</strong>移动端自动缩小到24px</span>
                </div>

                <div class="status-item">
                    <span class="status-icon">✅</span>
                    <span><strong>性能优化：</strong>添加GPU加速和过渡动画</span>
                </div>

                <div class="status-item">
                    <span class="status-icon">✅</span>
                    <span><strong>品牌一致性：</strong>保持iFlytek渐变色彩方案</span>
                </div>

                <div class="status-item">
                    <span class="status-icon">🔧</span>
                    <span><strong>强制修复：</strong>对所有Setting图标添加内联样式控制</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">📱 响应式测试</h2>
            <p>请调整浏览器窗口大小或在移动设备上查看，确认图标在不同屏幕尺寸下的显示效果。</p>
            
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="window.open('http://localhost:5173/', '_blank')" 
                        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                               color: white; border: none; padding: 15px 30px; 
                               border-radius: 8px; font-size: 1rem; cursor: pointer;">
                    🚀 查看优化后的主页
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 简单的图标尺寸检测
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 图标优化测试页面已加载');
            console.log('📊 当前屏幕宽度:', window.innerWidth + 'px');
            
            // 检测图标容器尺寸
            const iconContainers = document.querySelectorAll('.icon-container');
            iconContainers.forEach((container, index) => {
                const rect = container.getBoundingClientRect();
                console.log(`图标 ${index + 1} 尺寸:`, rect.width + 'x' + rect.height + 'px');
            });
        });
        
        // 响应式检测
        window.addEventListener('resize', function() {
            console.log('📱 屏幕尺寸变化:', window.innerWidth + 'px');
        });
    </script>
</body>
</html>
