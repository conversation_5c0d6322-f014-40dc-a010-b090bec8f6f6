# 🚀 iFlytek 多模态面试系统 - 云服务器部署指南

## 📋 部署概述

本指南将帮助你将多模态面试系统部署到云服务器，实现永久在线访问。支持阿里云、腾讯云、华为云等主流云服务商。

## 🛠️ 部署架构

```
用户 → 域名/IP → Nginx反向代理 → Vue.js前端 + FastAPI后端 + Redis缓存
```

## 📦 部署方式选择

### 方式一：一键快速部署（推荐）
- **适用场景**：快速演示、测试环境
- **部署时间**：10-15分钟
- **技术要求**：基础Linux操作

### 方式二：完整生产部署
- **适用场景**：正式生产环境
- **部署时间**：30-60分钟
- **技术要求**：熟悉Docker、Nginx配置

## 🌟 方式一：一键快速部署

### 1. 购买云服务器

#### 阿里云ECS推荐配置
- **实例规格**：ecs.t6-c1m2.large（2核4GB）
- **操作系统**：Ubuntu 20.04 LTS
- **带宽**：5Mbps
- **存储**：40GB SSD
- **安全组**：开放80、443、22端口

#### 腾讯云CVM推荐配置
- **实例规格**：S5.MEDIUM4（2核4GB）
- **操作系统**：Ubuntu Server 20.04 LTS
- **带宽**：5Mbps
- **存储**：50GB高性能云硬盘
- **安全组**：开放80、443、22端口

### 2. 连接服务器

```bash
# 使用SSH连接服务器
ssh root@your-server-ip

# 或使用密钥连接
ssh -i your-key.pem ubuntu@your-server-ip
```

### 3. 上传项目文件

```bash
# 方法1：使用scp上传
scp -r deployment/ root@your-server-ip:/root/

# 方法2：使用rsync上传
rsync -avz deployment/ root@your-server-ip:/root/deployment/

# 方法3：在服务器上直接下载
wget https://github.com/your-repo/archive/main.zip
unzip main.zip
```

### 4. 一键部署

```bash
# 进入部署目录
cd deployment

# 给脚本执行权限
chmod +x quick-deploy.sh

# 运行一键部署脚本
./quick-deploy.sh
```

### 5. 配置iFlytek API

```bash
# 编辑环境配置文件
nano .env

# 修改以下配置
IFLYTEK_APP_ID=your_app_id_here
IFLYTEK_API_KEY=your_api_key_here
IFLYTEK_API_SECRET=your_api_secret_here
DOMAIN_NAME=your-server-ip
```

### 6. 重启服务

```bash
# 重启服务使配置生效
docker-compose restart
```

### 7. 访问系统

- **访问地址**：http://your-server-ip
- **管理后台**：http://your-server-ip/admin
- **API文档**：http://your-server-ip/api/docs

## 🏗️ 方式二：完整生产部署

### 1. 服务器准备

#### 推荐配置（生产环境）
- **CPU**：4核心以上
- **内存**：8GB以上
- **存储**：100GB SSD
- **带宽**：10Mbps以上
- **操作系统**：Ubuntu 20.04 LTS / CentOS 8

#### 安全组配置
```bash
# 开放必要端口
80    # HTTP
443   # HTTPS
22    # SSH
8000  # 后端API（可选，用于调试）
```

### 2. 域名配置

#### 购买域名
- 阿里云：https://wanwang.aliyun.com/
- 腾讯云：https://dnspod.cloud.tencent.com/
- Cloudflare：https://www.cloudflare.com/

#### DNS解析配置
```
类型    主机记录    解析值
A       @          your-server-ip
A       www        your-server-ip
```

### 3. SSL证书配置

#### 方法1：Let's Encrypt免费证书
```bash
# 运行SSL配置脚本
./deploy.sh --ssl
```

#### 方法2：上传自有证书
```bash
# 将证书文件放到指定目录
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem
```

### 4. 完整部署

```bash
# 初始化部署环境
./deploy.sh --init

# 编辑配置文件
nano .env

# 执行完整部署
./deploy.sh
```

## 🔧 部署后配置

### 1. 系统监控

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 查看系统资源
docker stats
```

### 2. 性能优化

#### 数据库优化
```bash
# 定期备份数据库
docker-compose exec backend python -c "
import sqlite3
import shutil
shutil.copy('/app/data/interview_system.db', '/app/data/backup.db')
"
```

#### 缓存优化
```bash
# 清理Redis缓存
docker-compose exec redis redis-cli FLUSHALL
```

### 3. 安全加固

#### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw enable
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

#### 定期更新
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 更新应用
./deploy.sh --update
```

## 🚨 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 查看详细日志
docker-compose logs backend
docker-compose logs frontend

# 重启服务
docker-compose restart
```

#### 2. 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# 停止占用端口的服务
sudo systemctl stop apache2
sudo systemctl stop nginx
```

#### 3. 内存不足
```bash
# 查看内存使用
free -h

# 清理Docker缓存
docker system prune -a
```

#### 4. SSL证书问题
```bash
# 检查证书有效性
openssl x509 -in nginx/ssl/cert.pem -text -noout

# 重新申请证书
./deploy.sh --ssl
```

## 📊 监控和维护

### 1. 日志管理
```bash
# 查看访问日志
docker-compose exec nginx tail -f /var/log/nginx/access.log

# 查看错误日志
docker-compose exec nginx tail -f /var/log/nginx/error.log
```

### 2. 备份策略
```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec backend tar -czf /app/data/backup_$DATE.tar.gz /app/data/
EOF

chmod +x backup.sh
```

### 3. 自动更新
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点自动备份
0 2 * * * /path/to/backup.sh

# 每周日凌晨3点自动更新
0 3 * * 0 /path/to/deploy.sh --update
```

## 🎯 部署成功验证

### 功能测试清单
- [ ] 首页正常访问
- [ ] 用户注册登录
- [ ] AI面试功能
- [ ] 文件上传下载
- [ ] 报告生成导出
- [ ] 多模态分析
- [ ] 响应速度测试

### 性能指标
- **页面加载时间**：< 3秒
- **API响应时间**：< 2秒
- **并发用户数**：100+
- **系统可用性**：99.9%

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志：`docker-compose logs`
3. 确认配置文件正确性
4. 联系技术支持

---

**恭喜！你的iFlytek多模态面试系统现已成功部署到云服务器，可以永久在线访问了！** 🎉
