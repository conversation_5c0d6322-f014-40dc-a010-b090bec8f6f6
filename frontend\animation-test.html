<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek UI动效测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #333;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            color: #1890ff;
            font-size: 32px;
            margin: 0 0 30px 0;
            font-weight: 600;
            text-align: center;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 20px 0;
            color: #333;
        }
        
        /* 用友大易风格动效测试 */
        .dayee-card-hover {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
            margin: 10px;
            display: inline-block;
            min-width: 200px;
        }
        
        .dayee-card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            border-color: #1890ff;
        }
        
        .dayee-btn-micro {
            transition: all 0.15s ease-out;
            position: relative;
            overflow: hidden;
            padding: 12px 24px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        
        .dayee-btn-micro:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
        }
        
        .dayee-progressive-load {
            opacity: 0;
            transform: translateY(20px);
            animation: dayeeProgressiveLoad 0.6s ease-out forwards;
        }
        
        @keyframes dayeeProgressiveLoad {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .dayee-progressive-load:nth-child(1) { animation-delay: 0.1s; }
        .dayee-progressive-load:nth-child(2) { animation-delay: 0.2s; }
        .dayee-progressive-load:nth-child(3) { animation-delay: 0.3s; }
        .dayee-progressive-load:nth-child(4) { animation-delay: 0.4s; }
        
        .dayee-icon-rotate {
            transition: transform 0.3s ease-out;
            display: inline-block;
            font-size: 24px;
            color: #1890ff;
        }
        
        .dayee-icon-rotate:hover {
            transform: rotate(360deg);
        }
        
        .status-indicator {
            display: inline-block;
            padding: 8px 16px;
            background: #52c41a;
            color: white;
            border-radius: 20px;
            font-size: 12px;
            margin: 5px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .test-result {
            background: #f0f9ff;
            border: 1px solid #1890ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .success {
            background: #f6ffed;
            border-color: #52c41a;
            color: #389e0d;
        }
        
        .warning {
            background: #fffbe6;
            border-color: #faad14;
            color: #d48806;
        }
        
        .error {
            background: #fff2f0;
            border-color: #ff4d4f;
            color: #cf1322;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎨 iFlytek UI动效测试页面</h1>
        
        <!-- 卡片悬停效果测试 -->
        <div class="test-section">
            <h2 class="section-title">1. 卡片悬停效果测试 (.dayee-card-hover)</h2>
            <p>将鼠标悬停在下面的卡片上，应该看到向上移动4px和阴影变化的效果：</p>
            <div class="test-grid">
                <div class="dayee-card-hover">
                    <h3>测试卡片 1</h3>
                    <p>悬停查看效果</p>
                </div>
                <div class="dayee-card-hover">
                    <h3>测试卡片 2</h3>
                    <p>悬停查看效果</p>
                </div>
                <div class="dayee-card-hover">
                    <h3>测试卡片 3</h3>
                    <p>悬停查看效果</p>
                </div>
            </div>
        </div>
        
        <!-- 按钮微动效测试 -->
        <div class="test-section">
            <h2 class="section-title">2. 按钮微动效测试 (.dayee-btn-micro)</h2>
            <p>将鼠标悬停在下面的按钮上，应该看到向上移动2px和阴影变化的效果：</p>
            <button class="dayee-btn-micro">主要按钮</button>
            <button class="dayee-btn-micro">次要按钮</button>
            <button class="dayee-btn-micro">操作按钮</button>
        </div>
        
        <!-- 渐进式加载测试 -->
        <div class="test-section">
            <h2 class="section-title">3. 渐进式加载测试 (.dayee-progressive-load)</h2>
            <p>刷新页面查看下面元素的渐进式加载效果：</p>
            <div class="test-grid">
                <div class="dayee-card-hover dayee-progressive-load">
                    <h3>加载项 1</h3>
                    <p>延迟 0.1s</p>
                </div>
                <div class="dayee-card-hover dayee-progressive-load">
                    <h3>加载项 2</h3>
                    <p>延迟 0.2s</p>
                </div>
                <div class="dayee-card-hover dayee-progressive-load">
                    <h3>加载项 3</h3>
                    <p>延迟 0.3s</p>
                </div>
                <div class="dayee-card-hover dayee-progressive-load">
                    <h3>加载项 4</h3>
                    <p>延迟 0.4s</p>
                </div>
            </div>
        </div>
        
        <!-- 图标旋转测试 -->
        <div class="test-section">
            <h2 class="section-title">4. 图标旋转测试 (.dayee-icon-rotate)</h2>
            <p>将鼠标悬停在下面的图标上，应该看到360度旋转效果：</p>
            <div style="font-size: 48px; text-align: center; padding: 20px;">
                <span class="dayee-icon-rotate">⚙️</span>
                <span class="dayee-icon-rotate">🎯</span>
                <span class="dayee-icon-rotate">📊</span>
                <span class="dayee-icon-rotate">🚀</span>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-section">
            <h2 class="section-title">5. 测试结果</h2>
            <div class="test-result success">
                <strong>✅ 如果您能看到上述所有动效正常工作，说明用友大易风格的UI优化已成功应用！</strong>
            </div>
            <div class="test-result warning">
                <strong>⚠️ 如果某些动效不工作，请检查浏览器控制台是否有CSS错误。</strong>
            </div>
            <div class="test-result">
                <strong>📝 测试说明：</strong>
                <ul>
                    <li>卡片悬停：向上移动4px + 阴影变化 + 边框变蓝</li>
                    <li>按钮微动效：向上移动2px + 蓝色阴影</li>
                    <li>渐进式加载：页面刷新时逐步显示</li>
                    <li>图标旋转：悬停时360度旋转</li>
                </ul>
            </div>
        </div>
        
        <!-- 返回主系统 -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="http://localhost:5173" class="dayee-btn-micro" style="text-decoration: none; display: inline-block;">
                🏠 返回iFlytek主系统
            </a>
        </div>
    </div>
    
    <script>
        console.log('🎨 iFlytek UI动效测试页面已加载');
        console.log('📊 测试项目：');
        console.log('   1. 卡片悬停效果 (.dayee-card-hover)');
        console.log('   2. 按钮微动效 (.dayee-btn-micro)');
        console.log('   3. 渐进式加载 (.dayee-progressive-load)');
        console.log('   4. 图标旋转效果 (.dayee-icon-rotate)');
        
        // 检测CSS动画支持
        const testElement = document.createElement('div');
        testElement.style.transition = 'transform 0.3s ease';
        if (testElement.style.transition) {
            console.log('✅ 浏览器支持CSS过渡动画');
        } else {
            console.log('❌ 浏览器不支持CSS过渡动画');
        }
        
        // 检测transform支持
        testElement.style.transform = 'translateY(-4px)';
        if (testElement.style.transform) {
            console.log('✅ 浏览器支持CSS Transform');
        } else {
            console.log('❌ 浏览器不支持CSS Transform');
        }
    </script>
</body>
</html>
