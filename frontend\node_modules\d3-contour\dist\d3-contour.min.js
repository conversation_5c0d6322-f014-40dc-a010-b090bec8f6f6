// https://d3js.org/d3-contour/ v4.0.2 Copyright 2012-2023 <PERSON>
!function(r,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("d3-array")):"function"==typeof define&&define.amd?define(["exports","d3-array"],n):n((r="undefined"!=typeof globalThis?globalThis:r||self).d3=r.d3||{},r.d3)}(this,(function(r,n){"use strict";var t=Array.prototype.slice;function e(r,n){return r-n}var o=r=>()=>r;function i(r,n){for(var t,e=-1,o=n.length;++e<o;)if(t=u(r,n[e]))return t;return 0}function u(r,n){for(var t=n[0],e=n[1],o=-1,i=0,u=r.length,f=u-1;i<u;f=i++){var c=r[i],h=c[0],s=c[1],l=r[f],d=l[0],g=l[1];if(a(c,l,n))return 0;s>e!=g>e&&t<(d-h)*(e-s)/(g-s)+h&&(o=-o)}return o}function a(r,n,t){var e,o,i,u;return function(r,n,t){return(n[0]-r[0])*(t[1]-r[1])==(t[0]-r[0])*(n[1]-r[1])}(r,n,t)&&(o=r[e=+(r[0]===n[0])],i=t[e],u=n[e],o<=i&&i<=u||u<=i&&i<=o)}function f(){}var c=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function h(){var r=1,u=1,a=n.thresholdSturges,h=w;function v(r){var t=a(r);if(Array.isArray(t))t=t.slice().sort(e);else{const e=n.extent(r,s);for(t=n.ticks(...n.nice(e[0],e[1],t),t);t[t.length-1]>=e[1];)t.pop();for(;t[1]<e[0];)t.shift()}return t.map((n=>p(r,n)))}function p(n,t){const e=null==t?NaN:+t;if(isNaN(e))throw new Error(`invalid value: ${t}`);var o=[],a=[];return function(n,t,e){var o,i,a,f,h,s,d=new Array,g=new Array;o=i=-1,f=l(n[0],t),c[f<<1].forEach(v);for(;++o<r-1;)a=f,f=l(n[o+1],t),c[a|f<<1].forEach(v);c[f<<0].forEach(v);for(;++i<u-1;){for(o=-1,f=l(n[i*r+r],t),h=l(n[i*r],t),c[f<<1|h<<2].forEach(v);++o<r-1;)a=f,f=l(n[i*r+r+o+1],t),s=h,h=l(n[i*r+o+1],t),c[a|f<<1|h<<2|s<<3].forEach(v);c[f|h<<3].forEach(v)}o=-1,h=n[i*r]>=t,c[h<<2].forEach(v);for(;++o<r-1;)s=h,h=l(n[i*r+o+1],t),c[h<<2|s<<3].forEach(v);function v(r){var n,t,u=[r[0][0]+o,r[0][1]+i],a=[r[1][0]+o,r[1][1]+i],f=y(u),c=y(a);(n=g[f])?(t=d[c])?(delete g[n.end],delete d[t.start],n===t?(n.ring.push(a),e(n.ring)):d[n.start]=g[t.end]={start:n.start,end:t.end,ring:n.ring.concat(t.ring)}):(delete g[n.end],n.ring.push(a),g[n.end=c]=n):(n=d[c])?(t=g[f])?(delete d[n.start],delete g[t.end],n===t?(n.ring.push(a),e(n.ring)):d[t.start]=g[n.end]={start:t.start,end:n.end,ring:t.ring.concat(n.ring)}):(delete d[n.start],n.ring.unshift(u),d[n.start=f]=n):d[f]=g[c]={start:f,end:c,ring:[u,a]}}c[h<<3].forEach(v)}(n,e,(function(r){h(r,n,e),function(r){for(var n=0,t=r.length,e=r[t-1][1]*r[0][0]-r[t-1][0]*r[0][1];++n<t;)e+=r[n-1][1]*r[n][0]-r[n-1][0]*r[n][1];return e}(r)>0?o.push([r]):a.push(r)})),a.forEach((function(r){for(var n,t=0,e=o.length;t<e;++t)if(-1!==i((n=o[t])[0],r))return void n.push(r)})),{type:"MultiPolygon",value:t,coordinates:o}}function y(n){return 2*n[0]+n[1]*(r+1)*4}function w(n,t,e){n.forEach((function(n){var o=n[0],i=n[1],a=0|o,f=0|i,c=d(t[f*r+a]);o>0&&o<r&&a===o&&(n[0]=g(o,d(t[f*r+a-1]),c,e)),i>0&&i<u&&f===i&&(n[1]=g(i,d(t[(f-1)*r+a]),c,e))}))}return v.contour=p,v.size=function(n){if(!arguments.length)return[r,u];var t=Math.floor(n[0]),e=Math.floor(n[1]);if(!(t>=0&&e>=0))throw new Error("invalid size");return r=t,u=e,v},v.thresholds=function(r){return arguments.length?(a="function"==typeof r?r:Array.isArray(r)?o(t.call(r)):o(r),v):a},v.smooth=function(r){return arguments.length?(h=r?w:f,v):h===w},v}function s(r){return isFinite(r)?r:NaN}function l(r,n){return null!=r&&+r>=n}function d(r){return null==r||isNaN(r=+r)?-1/0:r}function g(r,n,t,e){const o=e-n,i=t-n,u=isFinite(o)||isFinite(i)?o/i:Math.sign(o)/Math.sign(i);return isNaN(u)?r:r+u-.5}function v(r){return r[0]}function p(r){return r[1]}function y(){return 1}r.contourDensity=function(){var r=v,e=p,i=y,u=960,a=500,f=20,c=2,s=3*f,l=u+2*s>>c,d=a+2*s>>c,g=o(20);function w(t){var o=new Float32Array(l*d),u=Math.pow(2,-c),a=-1;for(const n of t){var h=(r(n,++a,t)+s)*u,g=(e(n,a,t)+s)*u,v=+i(n,a,t);if(v&&h>=0&&h<l&&g>=0&&g<d){var p=Math.floor(h),y=Math.floor(g),w=h-p-.5,E=g-y-.5;o[p+y*l]+=(1-w)*(1-E)*v,o[p+1+y*l]+=w*(1-E)*v,o[p+1+(y+1)*l]+=w*E*v,o[p+(y+1)*l]+=(1-w)*E*v}}return n.blur2({data:o,width:l,height:d},f*u),o}function E(r){var t=w(r),e=g(t),o=Math.pow(2,2*c);return Array.isArray(e)||(e=n.ticks(Number.MIN_VALUE,n.max(t)/o,e)),h().size([l,d]).thresholds(e.map((r=>r*o)))(t).map(((r,n)=>(r.value=+e[n],M(r))))}function M(r){return r.coordinates.forEach(A),r}function A(r){r.forEach(N)}function N(r){r.forEach(m)}function m(r){r[0]=r[0]*Math.pow(2,c)-s,r[1]=r[1]*Math.pow(2,c)-s}function b(){return l=u+2*(s=3*f)>>c,d=a+2*s>>c,E}return E.contours=function(r){var t=w(r),e=h().size([l,d]),o=Math.pow(2,2*c),i=r=>{r=+r;var n=M(e.contour(t,r*o));return n.value=r,n};return Object.defineProperty(i,"max",{get:()=>n.max(t)/o}),i},E.x=function(n){return arguments.length?(r="function"==typeof n?n:o(+n),E):r},E.y=function(r){return arguments.length?(e="function"==typeof r?r:o(+r),E):e},E.weight=function(r){return arguments.length?(i="function"==typeof r?r:o(+r),E):i},E.size=function(r){if(!arguments.length)return[u,a];var n=+r[0],t=+r[1];if(!(n>=0&&t>=0))throw new Error("invalid size");return u=n,a=t,b()},E.cellSize=function(r){if(!arguments.length)return 1<<c;if(!((r=+r)>=1))throw new Error("invalid cell size");return c=Math.floor(Math.log(r)/Math.LN2),b()},E.thresholds=function(r){return arguments.length?(g="function"==typeof r?r:Array.isArray(r)?o(t.call(r)):o(r),E):g},E.bandwidth=function(r){if(!arguments.length)return Math.sqrt(f*(f+1));if(!((r=+r)>=0))throw new Error("invalid bandwidth");return f=(Math.sqrt(4*r*r+1)-1)/2,b()},E},r.contours=h}));
