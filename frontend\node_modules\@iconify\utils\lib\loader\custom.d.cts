import { CustomIconLoader, InlineCollection, IconifyLoaderOptions } from './types.cjs';
import '@antfu/utils';
import '../customisations/defaults.cjs';
import '@iconify/types';

/**
 * Get custom icon from inline collection or using loader
 */
declare function getCustomIcon(custom: CustomIconLoader | InlineCollection, collection: string, icon: string, options?: IconifyLoaderOptions): Promise<string | undefined>;

export { getCustomIcon };
