#!/usr/bin/env node

/**
 * 按钮可点击性修复验证脚本
 * 验证"您的回答"输入框下方的蓝色提交按钮是否可以正常点击
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 验证按钮可点击性修复...\n')

// 检查的文件
const targetFile = 'src/views/TextPrimaryInterviewPage.vue'
const fullPath = path.join(__dirname, targetFile)

console.log(`📄 检查文件: ${targetFile}`)

if (!fs.existsSync(fullPath)) {
  console.log(`❌ 文件不存在: ${targetFile}`)
  process.exit(1)
}

const content = fs.readFileSync(fullPath, 'utf8')
console.log(`✅ 文件存在 (${content.length} 字符)\n`)

// 验证项目
const verificationChecks = [
  {
    name: '输入面板overflow修复',
    pattern: /\.answer-input-panel\s*{[^}]*overflow:\s*visible[^}]*}/s,
    description: '检查输入面板是否设置为overflow: visible，确保内容不被截断'
  },
  {
    name: '输入面板高度限制移除',
    pattern: /\.answer-input-panel\s*{[^}]*max-height:\s*none[^}]*}/s,
    description: '检查是否移除了输入面板的最大高度限制'
  },
  {
    name: '输入底部区域z-index设置',
    pattern: /\.input-footer\s*{[^}]*z-index:\s*15[^}]*}/s,
    description: '检查输入底部区域是否设置了正确的z-index'
  },
  {
    name: '按钮区域z-index设置',
    pattern: /\.input-actions\s*{[^}]*z-index:\s*20[^}]*}/s,
    description: '检查按钮区域是否设置了足够高的z-index'
  },
  {
    name: '智能建议面板z-index调整',
    pattern: /\.smart-suggestions\s*{[^}]*z-index:\s*5[^}]*}/s,
    description: '检查智能建议面板的z-index是否降低，避免遮挡按钮'
  },
  {
    name: '提交按钮存在',
    pattern: /<el-button[^>]*@click="submitAnswer"[^>]*>\s*<el-icon><Promotion \/><\/el-icon>\s*提交回答\s*<\/el-button>/s,
    description: '确认提交回答按钮仍然存在'
  }
]

let allChecksPass = true
let passedChecks = 0

console.log('🔧 执行按钮可点击性修复验证...\n')

verificationChecks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name}`)
  console.log(`   描述: ${check.description}`)
  
  const result = check.pattern.test(content)
  
  if (result) {
    console.log(`   ✅ 通过`)
    passedChecks++
  } else {
    console.log(`   ❌ 失败`)
    allChecksPass = false
  }
  
  console.log()
})

// 额外检查：分析z-index层级
console.log('📊 Z-Index层级分析:')

const zIndexMatches = content.match(/z-index:\s*(\d+)/g) || []
const zIndexValues = zIndexMatches.map(match => {
  const value = match.match(/z-index:\s*(\d+)/)[1]
  return parseInt(value)
}).sort((a, b) => a - b)

console.log(`   发现 ${zIndexMatches.length} 个z-index设置`)
console.log(`   z-index值范围: ${Math.min(...zIndexValues)} - ${Math.max(...zIndexValues)}`)

// 检查关键区域的z-index
const keyAreas = [
  { name: '智能建议面板', pattern: /\.smart-suggestions[^}]*z-index:\s*(\d+)/ },
  { name: '输入面板', pattern: /\.answer-input-panel[^}]*z-index:\s*(\d+)/ },
  { name: '输入底部', pattern: /\.input-footer[^}]*z-index:\s*(\d+)/ },
  { name: '按钮区域', pattern: /\.input-actions[^}]*z-index:\s*(\d+)/ }
]

keyAreas.forEach(area => {
  const match = content.match(area.pattern)
  if (match) {
    console.log(`   ${area.name}: z-index ${match[1]}`)
  } else {
    console.log(`   ${area.name}: 未设置z-index`)
  }
})

console.log()

// 检查布局相关设置
console.log('📋 布局设置检查:')

const layoutChecks = [
  { name: 'overflow设置', pattern: /overflow:\s*(visible|hidden|auto|scroll)/, found: [] },
  { name: 'position设置', pattern: /position:\s*(relative|absolute|fixed|static)/, found: [] },
  { name: 'max-height设置', pattern: /max-height:\s*([^;]+)/, found: [] }
]

layoutChecks.forEach(check => {
  const matches = content.match(new RegExp(check.pattern.source, 'g')) || []
  check.found = matches
  console.log(`   ${check.name}: 发现 ${matches.length} 个`)
  if (matches.length > 0 && matches.length <= 5) {
    matches.forEach((match, index) => {
      console.log(`     ${index + 1}. ${match}`)
    })
  }
})

console.log()

// 检查按钮相关功能
console.log('🔘 按钮功能检查:')

const buttonChecks = [
  { name: '提交按钮', pattern: /@click="submitAnswer"/, count: 0 },
  { name: '按钮禁用状态', pattern: /:disabled="[^"]*"/, count: 0 },
  { name: '加载状态', pattern: /:loading="[^"]*"/, count: 0 },
  { name: '按钮样式类', pattern: /type="primary"/, count: 0 }
]

buttonChecks.forEach(check => {
  const matches = content.match(new RegExp(check.pattern.source, 'g')) || []
  check.count = matches.length
  const status = check.count > 0 ? '✅' : '❌'
  console.log(`   ${status} ${check.name}: ${check.count} 个`)
})

console.log()

// 生成测试报告
console.log('📊 按钮可点击性修复验证报告')
console.log('='.repeat(50))

if (allChecksPass) {
  console.log('🎉 按钮可点击性修复成功！')
  console.log('')
  console.log('✅ 完成的修复:')
  console.log('  - 输入面板overflow: hidden → visible')
  console.log('  - 输入面板max-height: 200px → none')
  console.log('  - 输入底部区域z-index: 15')
  console.log('  - 按钮区域z-index: 20')
  console.log('  - 智能建议面板z-index: 10 → 5')
  console.log('')
  console.log('✅ 解决的问题:')
  console.log('  - 按钮区域不再被截断')
  console.log('  - z-index层级正确设置')
  console.log('  - 智能建议面板不再遮挡按钮')
  console.log('  - 输入面板高度限制移除')
  console.log('')
  console.log('✅ 预期效果:')
  console.log('  - "提交回答"按钮完全可见')
  console.log('  - 按钮可以正常点击')
  console.log('  - 所有按钮功能正常工作')
  console.log('  - 界面布局更加合理')
} else {
  console.log('⚠️  按钮可点击性修复验证中发现问题')
  console.log('')
  console.log(`📊 验证结果: ${passedChecks}/${verificationChecks.length} 项通过`)
  console.log('')
  console.log('💡 建议:')
  console.log('  - 检查上述失败的验证项目')
  console.log('  - 确保CSS修改正确应用')
  console.log('  - 检查z-index层级设置')
  console.log('  - 重新运行此验证脚本')
}

console.log('')
console.log('🔗 相关信息:')
console.log('  - 目标文件: src/views/TextPrimaryInterviewPage.vue')
console.log('  - 修复类型: 布局和z-index调整')
console.log('  - 主要问题: 按钮被遮挡或截断')
console.log('  - 解决方案: overflow + z-index + 高度限制调整')

console.log('')
console.log('📞 测试访问:')
console.log('  - 文本面试页面: http://localhost:8080/text-primary-interview')
console.log('  - 产品演示页面: http://localhost:8080/demo')

console.log('')
console.log('🎯 测试步骤:')
console.log('  1. 打开文本面试页面')
console.log('  2. 在"您的回答"输入框中输入文字')
console.log('  3. 查看输入框下方的按钮区域')
console.log('  4. 确认"提交回答"按钮完全可见')
console.log('  5. 点击"提交回答"按钮测试功能')

console.log('')
console.log('🛠️ 技术细节:')
console.log('  - 使用overflow: visible确保内容不被截断')
console.log('  - 使用z-index层级管理元素显示优先级')
console.log('  - 移除max-height限制允许内容完全显示')
console.log('  - 调整智能建议面板避免遮挡按钮')

export { allChecksPass, passedChecks, verificationChecks }
