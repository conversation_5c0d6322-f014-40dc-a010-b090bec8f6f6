{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/virtual-list/src/utils.ts"], "sourcesContent": ["import {\n  BACKWARD,\n  FORWARD,\n  HORIZONTAL,\n  LTR,\n  RTL,\n  RTL_OFFSET_NAG,\n  RTL_OFFSET_POS_ASC,\n  RTL_OFFSET_POS_DESC,\n} from './defaults'\n\nimport type { CSSProperties } from 'vue'\nimport type { Direction, RTLOffsetType } from './types'\n\nexport const getScrollDir = (prev: number, cur: number) =>\n  prev < cur ? FORWARD : BACKWARD\n\nexport const isHorizontal = (dir: string) =>\n  dir === LTR || dir === RTL || dir === HORIZONTAL\n\nexport const isRTL = (dir: Direction) => dir === RTL\n\nlet cachedRTLResult: RTLOffsetType | null = null\n\nexport function getRTLOffsetType(recalculate = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div')\n    const outerStyle = outerDiv.style\n    outerStyle.width = '50px'\n    outerStyle.height = '50px'\n    outerStyle.overflow = 'scroll'\n    outerStyle.direction = 'rtl'\n\n    const innerDiv = document.createElement('div')\n    const innerStyle = innerDiv.style\n    innerStyle.width = '100px'\n    innerStyle.height = '100px'\n\n    outerDiv.appendChild(innerDiv)\n\n    document.body.appendChild(outerDiv)\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = RTL_OFFSET_POS_DESC\n    } else {\n      outerDiv.scrollLeft = 1\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = RTL_OFFSET_NAG\n      } else {\n        cachedRTLResult = RTL_OFFSET_POS_ASC\n      }\n    }\n\n    document.body.removeChild(outerDiv)\n\n    return cachedRTLResult\n  }\n\n  return cachedRTLResult\n}\n\ntype RenderThumbStyleParams = {\n  bar: {\n    size: 'height' | 'width'\n    axis: 'X' | 'Y'\n  }\n  size: string\n  move: number\n}\n\nexport function renderThumbStyle(\n  { move, size, bar }: RenderThumbStyleParams,\n  layout: string\n) {\n  const style: CSSProperties = {}\n  const translate = `translate${bar.axis}(${move}px)`\n\n  style[bar.size] = size\n  style.transform = translate\n\n  if (layout === 'horizontal') {\n    style.height = '100%'\n  } else {\n    style.width = '100%'\n  }\n\n  return style\n}\n"], "names": [], "mappings": ";;AAUY,MAAC,YAAY,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,OAAO,GAAG,SAAS;AAC/D,MAAC,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,WAAW;AAC1E,MAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,IAAI;AAC1C,IAAI,eAAe,GAAG,IAAI,CAAC;AACpB,SAAS,gBAAgB,CAAC,WAAW,GAAG,KAAK,EAAE;AACtD,EAAE,IAAI,eAAe,KAAK,IAAI,IAAI,WAAW,EAAE;AAC/C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;AACtC,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;AAC9B,IAAI,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B,IAAI,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACnC,IAAI,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC;AACjC,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;AACtC,IAAI,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC;AAC/B,IAAI,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC,IAAI,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnC,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAI,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE;AACjC,MAAM,eAAe,GAAG,mBAAmB,CAAC;AAC5C,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;AAC9B,MAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,CAAC,EAAE;AACrC,QAAQ,eAAe,GAAG,cAAc,CAAC;AACzC,OAAO,MAAM;AACb,QAAQ,eAAe,GAAG,kBAAkB,CAAC;AAC7C,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAI,OAAO,eAAe,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,eAAe,CAAC;AACzB,CAAC;AACM,SAAS,gBAAgB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE;AAC9D,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB,EAAE,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACtD,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACzB,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAC9B,EAAE,IAAI,MAAM,KAAK,YAAY,EAAE;AAC/B,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC1B,GAAG,MAAM;AACT,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;AACzB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf;;;;"}