{"name": "@chevrotain/regexp-to-ast", "version": "11.0.3", "description": "Parses a Regular Expression and outputs an AST", "keywords": ["regExp", "parser", "regular expression"], "bugs": {"url": "https://github.com/Chevrotain/chevrotain/issues"}, "license": "Apache-2.0", "type": "module", "types": "./types.d.ts", "exports": {".": {"import": "./lib/src/api.js", "types": "./types.d.ts"}}, "files": ["lib/src/**/*.js", "lib/src/**/*.map", "src/**/*.ts", "types.d.ts"], "repository": {"type": "git", "url": "git://github.com/Chevrotain/chevrotain.git"}, "scripts": {"---------- CI FLOWS --------": "", "ci": "pnpm run build test", "build": "npm-run-all clean compile", "test": "npm-run-all coverage", "---------- BUILD STEPS --------": "", "clean": "shx rm -rf lib coverage", "compile:watch": "tsc -w", "compile": "tsc", "coverage": "c8 mocha"}, "publishConfig": {"access": "public"}, "gitHead": "ac5806631779035c2c1955744a47d8ed4f25a175"}