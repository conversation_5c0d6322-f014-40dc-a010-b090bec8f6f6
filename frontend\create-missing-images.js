#!/usr/bin/env node

/**
 * 创建EnhancedVideoDemo组件需要的所有缺失图片
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎨 创建EnhancedVideoDemo组件需要的缺失图片...\n');

// 创建SVG占位符的函数
function createSVGPlaceholder(title, color = '#667eea', width = 400, height = 225) {
    return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
                <stop offset="100%" style="stop-color:${adjustColor(color, -30)};stop-opacity:1" />
            </linearGradient>
        </defs>
        <rect width="${width}" height="${height}" fill="url(#grad)"/>
        <text x="${width/2}" y="${height/2 - 15}" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle" font-weight="bold">
            ${title}
        </text>
        <text x="${width/2}" y="${height/2 + 15}" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)" text-anchor="middle">
            多模态面试评估系统
        </text>
        <rect x="20" y="20" width="${width-40}" height="${height-40}" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="5"/>
        <circle cx="50" cy="50" r="8" fill="rgba(255,255,255,0.5)"/>
        <circle cx="70" cy="50" r="8" fill="rgba(255,255,255,0.3)"/>
        <circle cx="90" cy="50" r="8" fill="rgba(255,255,255,0.2)"/>
    </svg>`;
}

// 调整颜色亮度的函数
function adjustColor(color, amount) {
    const hex = color.replace('#', '');
    const num = parseInt(hex, 16);
    const r = Math.max(0, Math.min(255, (num >> 16) + amount));
    const g = Math.max(0, Math.min(255, (num >> 8 & 0x00FF) + amount));
    const b = Math.max(0, Math.min(255, (num & 0x0000FF) + amount));
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
}

// 需要创建的图片列表
const missingImages = [
    // AI章节图片
    { filename: 'ai-chapter-1.jpg', title: 'iFlytek Spark介绍', color: '#4CAF50' },
    { filename: 'ai-chapter-2.jpg', title: '多模态融合', color: '#2196F3' },
    { filename: 'ai-chapter-3.jpg', title: '评估算法', color: '#FF9800' },
    
    // 案例图片
    { filename: 'case-ai.jpg', title: 'AI领域案例', color: '#9C27B0' },
    { filename: 'case-bigdata.jpg', title: '大数据案例', color: '#F44336' },
    { filename: 'case-iot.jpg', title: '物联网案例', color: '#009688' },
    
    // 大数据章节图片
    { filename: 'bigdata-chapter-1.jpg', title: '数据处理能力', color: '#3F51B5' },
    { filename: 'bigdata-chapter-2.jpg', title: '算法理解', color: '#795548' },
    { filename: 'bigdata-chapter-3.jpg', title: '实战项目', color: '#607D8B' },
    
    // IoT章节图片
    { filename: 'iot-chapter-1.jpg', title: '硬件基础', color: '#E91E63' },
    { filename: 'iot-chapter-2.jpg', title: '通信协议', color: '#00BCD4' },
    { filename: 'iot-chapter-3.jpg', title: '系统集成', color: '#8BC34A' }
];

// 创建图片目录
const imageDir = path.join(__dirname, 'public/images');
if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
}

console.log('📸 创建缺失的图片文件...');

// 生成所有缺失的图片
missingImages.forEach((img, index) => {
    const imagePath = path.join(imageDir, img.filename);
    const svgContent = createSVGPlaceholder(img.title, img.color);
    
    fs.writeFileSync(imagePath, svgContent);
    console.log(`  ✅ 创建: ${img.filename} - ${img.title}`);
});

console.log('\n🎉 所有缺失图片创建完成！');
console.log(`\n📋 创建了 ${missingImages.length} 个图片文件:`);
missingImages.forEach(img => {
    console.log(`  - ${img.filename}`);
});

console.log('\n💡 使用说明:');
console.log('  1. 所有图片都是SVG格式的占位符');
console.log('  2. 可以直接在浏览器中显示');
console.log('  3. 实际部署时请替换为真实的图片文件');
console.log('  4. 刷新浏览器查看效果');
