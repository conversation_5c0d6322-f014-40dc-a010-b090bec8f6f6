{"name": "pkg-types", "version": "2.2.0", "description": "Node.js utilities and TypeScript definitions for `package.json` and `tsconfig.json`", "repository": "unjs/pkg-types", "license": "MIT", "sideEffects": false, "exports": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "types": "./dist/index.d.mts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest --typecheck", "lint": "eslint && prettier -c src test", "lint:fix": "automd && eslint --fix . && prettier -w src test", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "vitest run --typecheck --coverage"}, "dependencies": {"confbox": "^0.2.2", "exsolve": "^1.0.7", "pathe": "^2.0.3"}, "devDependencies": {"@types/node": "^24.0.7", "@vitest/coverage-v8": "^3.2.4", "automd": "^0.4.0", "changelogen": "^0.6.1", "eslint": "^9.30.0", "eslint-config-unjs": "^0.5.0", "expect-type": "^1.2.1", "jiti": "^2.4.2", "prettier": "^3.6.2", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.12.4"}