#!/usr/bin/env python3
"""
多模态面试评估系统完整功能验证脚本
测试前后端集成和所有核心功能
"""

import requests
import json
import time
from datetime import datetime

# 配置
FRONTEND_URL = "http://localhost:5175"
BACKEND_URL = "http://localhost:8000"

def test_frontend_accessibility():
    """测试前端可访问性"""
    print("🔍 测试前端可访问性...")
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200:
            # 检查页面是否包含Vue应用的基本结构
            if ('<div id="app"></div>' in response.text and
                'src="/src/main.js' in response.text and
                '智能面试评测' in response.text):
                print("✅ 前端页面正常加载")
                return True
            else:
                print(f"❌ 前端页面内容异常，缺少Vue应用结构")
                return False
        else:
            print(f"❌ 前端页面异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        return False

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    try:
        response = requests.get(f"{BACKEND_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_domains_api():
    """测试技术领域API"""
    print("🔍 测试技术领域API...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/domains", timeout=10)
        if response.status_code == 200:
            data = response.json()
            domains = data.get("domains", [])
            if "人工智能" in domains and "大数据" in domains and "物联网" in domains:
                print(f"✅ 技术领域API正常: {domains}")
                return True
            else:
                print(f"❌ 技术领域数据异常: {domains}")
                return False
        else:
            print(f"❌ 技术领域API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 技术领域API失败: {e}")
        return False

def test_positions_api():
    """测试职位API"""
    print("🔍 测试职位API...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/domains/人工智能/positions", timeout=10)
        if response.status_code == 200:
            data = response.json()
            positions = data.get("positions", [])
            if "技术岗" in positions:
                print(f"✅ 职位API正常: {positions}")
                return True
            else:
                print(f"❌ 职位数据异常: {positions}")
                return False
        else:
            print(f"❌ 职位API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 职位API失败: {e}")
        return False

def test_interview_session():
    """测试面试会话创建"""
    print("🔍 测试面试会话创建...")
    try:
        session_data = {
            "domain": "人工智能",
            "position": "技术岗",
            "difficulty": "中级"
        }
        response = requests.post(
            f"{BACKEND_URL}/api/v1/interview/session",
            json=session_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            session_id = data.get("session_id")
            if session_id:
                print(f"✅ 面试会话创建成功: Session ID {session_id}")
                return session_id
            else:
                print(f"❌ 面试会话数据异常: {data}")
                return None
        else:
            print(f"❌ 面试会话创建失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 面试会话创建异常: {e}")
        return None

def test_interview_start():
    """测试面试开始"""
    print("🔍 测试面试开始...")
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/v1/interview/start",
            json={"domain": "人工智能", "position": "技术岗"},
            headers={"Content-Type": "application/json"},
            timeout=30  # AI生成问题可能需要更长时间
        )
        if response.status_code == 200:
            data = response.json()
            question = data.get("question")
            if question and len(question) > 10:
                print(f"✅ 面试开始成功，生成问题: {question[:50]}...")
                return True
            else:
                print(f"❌ 面试问题异常: {question}")
                return False
        else:
            print(f"❌ 面试开始失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 面试开始异常: {e}")
        return False

def test_learning_paths():
    """测试学习路径功能"""
    print("🔍 测试学习路径功能...")
    try:
        # 测试获取领域学习路径
        response = requests.get(
            f"{BACKEND_URL}/api/v1/learning-paths/domains/人工智能",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and data.get("data"):
                print(f"✅ 学习路径API正常: 找到 {len(data['data'])} 个路径")
                
                # 测试个性化学习路径生成
                personalized_data = {
                    "domain": "人工智能",
                    "position": "技术岗",
                    "skill_level": "中级"
                }
                response = requests.post(
                    f"{BACKEND_URL}/api/v1/learning-paths/personalized",
                    json=personalized_data,
                    headers={"Content-Type": "application/json"},
                    timeout=15
                )
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        print("✅ 个性化学习路径生成成功")
                        return True
                    else:
                        print(f"❌ 个性化学习路径生成失败: {result.get('message')}")
                        return False
                else:
                    print(f"❌ 个性化学习路径API异常: {response.status_code}")
                    return False
            else:
                print(f"❌ 学习路径数据异常: {data}")
                return False
        else:
            print(f"❌ 学习路径API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 学习路径功能异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始多模态面试评估系统完整功能验证")
    print("=" * 60)
    
    test_results = []
    
    # 1. 前端可访问性测试
    test_results.append(("前端可访问性", test_frontend_accessibility()))
    
    # 2. 后端健康状态测试
    test_results.append(("后端健康状态", test_backend_health()))
    
    # 3. 技术领域API测试
    test_results.append(("技术领域API", test_domains_api()))
    
    # 4. 职位API测试
    test_results.append(("职位API", test_positions_api()))
    
    # 5. 面试会话测试
    session_id = test_interview_session()
    test_results.append(("面试会话创建", session_id is not None))
    
    # 6. 面试开始测试
    test_results.append(("面试开始", test_interview_start()))
    
    # 7. 学习路径功能测试
    test_results.append(("学习路径功能", test_learning_paths()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！系统运行正常！")
        return True
    else:
        print("⚠️  部分功能存在问题，请检查失败的测试项")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
