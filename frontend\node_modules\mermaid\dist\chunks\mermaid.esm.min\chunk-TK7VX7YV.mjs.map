{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ms@2.1.3/node_modules/ms/index.js", "../../../../../node_modules/.pnpm/debug@4.4.0_supports-color@8.1.1/node_modules/debug/src/common.js", "../../../../../node_modules/.pnpm/debug@4.4.0_supports-color@8.1.1/node_modules/debug/src/browser.js", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/defaults.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/customisations/defaults.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/name.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/transformations.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon/merge.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon-set/tree.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/icon-set/get-icon.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/size.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/defs.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/build.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/id.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/svg/html.mjs", "../../../../../node_modules/.pnpm/@iconify+utils@2.3.0/node_modules/@iconify/utils/lib/index.mjs", "../../../src/rendering-util/icons.ts", "../../../../../node_modules/.pnpm/ts-dedent@2.2.0/node_modules/ts-dedent/src/index.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/defaults.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/rules.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/helpers.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Tokenizer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Lexer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Renderer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/TextRenderer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Parser.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Hooks.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Instance.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/marked.ts", "../../../src/rendering-util/handle-markdown-text.ts", "../../../src/rendering-util/splitText.ts", "../../../src/rendering-util/createText.ts"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(' ', ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "const defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations };\n", "import { defaultIconTransformations } from '../icon/defaults.mjs';\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nexport { defaultIconCustomisations, defaultIconSizeCustomisations };\n", "const matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\nexport { matchIconName, stringToIcon, validateIconName };\n", "function mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nexport { mergeIconTransformations };\n", "import { defaultExtendedIconProps, defaultIconTransformations } from './defaults.mjs';\nimport { mergeIconTransformations } from './transformations.mjs';\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nexport { mergeIconData };\n", "function getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nexport { getIconsTree };\n", "import { mergeIconData } from '../icon/merge.mjs';\nimport { getIconsTree } from './tree.mjs';\nimport '../icon/defaults.mjs';\nimport '../icon/transformations.mjs';\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\nfunction getIconData(data, name) {\n  if (data.icons[name]) {\n    return internalGetIconData(data, name, []);\n  }\n  const tree = getIconsTree(data, [name])[name];\n  return tree ? internalGetIconData(data, name, tree) : null;\n}\n\nexport { getIconData, internalGetIconData };\n", "const unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nexport { calculateSize };\n", "function splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent };\n", "import { defaultIconProps } from '../icon/defaults.mjs';\nimport { defaultIconCustomisations } from '../customisations/defaults.mjs';\nimport { calculateSize } from './size.mjs';\nimport { wrapSVGContent } from './defs.mjs';\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = wrapSVGContent(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\nexport { iconToSVG, isUnsetKeyword };\n", "const regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nexport { replaceIDs };\n", "function iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nexport { iconToHTML };\n", "export { defaultIconCustomisations, defaultIconSizeCustomisations } from './customisations/defaults.mjs';\nexport { mergeCustomisations } from './customisations/merge.mjs';\nexport { toBoolean } from './customisations/bool.mjs';\nexport { flipFromString } from './customisations/flip.mjs';\nexport { rotateFromString } from './customisations/rotate.mjs';\nexport { matchIconName, stringToIcon, validateIconName } from './icon/name.mjs';\nexport { mergeIconData } from './icon/merge.mjs';\nexport { mergeIconTransformations } from './icon/transformations.mjs';\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations } from './icon/defaults.mjs';\nexport { makeIconSquare } from './icon/square.mjs';\nexport { getIconsTree } from './icon-set/tree.mjs';\nexport { parseIconSet, parseIconSetAsync } from './icon-set/parse.mjs';\nexport { validateIconSet } from './icon-set/validate.mjs';\nexport { quicklyValidateIconSet } from './icon-set/validate-basic.mjs';\nexport { expandIconSet } from './icon-set/expand.mjs';\nexport { minifyIconSet } from './icon-set/minify.mjs';\nexport { getIcons } from './icon-set/get-icons.mjs';\nexport { getIconData } from './icon-set/get-icon.mjs';\nexport { convertIconSetInfo } from './icon-set/convert-info.mjs';\nexport { iconToSVG } from './svg/build.mjs';\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent } from './svg/defs.mjs';\nexport { replaceIDs } from './svg/id.mjs';\nexport { calculateSize } from './svg/size.mjs';\nexport { encodeSvgForCss } from './svg/encode-svg-for-css.mjs';\nexport { trimSVG } from './svg/trim.mjs';\nexport { prettifySVG } from './svg/pretty.mjs';\nexport { iconToHTML } from './svg/html.mjs';\nexport { svgToData, svgToURL } from './svg/url.mjs';\nexport { cleanUpInnerHTML } from './svg/inner-html.mjs';\nexport { getSVGViewBox } from './svg/viewbox.mjs';\nexport { buildParsedSVG, convertParsedSVG, parseSVGContent } from './svg/parse.mjs';\nexport { colorKeywords } from './colors/keywords.mjs';\nexport { colorToString, compareColors, stringToColor } from './colors/index.mjs';\nexport { getIconCSS, getIconContentCSS } from './css/icon.mjs';\nexport { getIconsCSS, getIconsContentCSS } from './css/icons.mjs';\nexport { mergeIconProps } from './loader/utils.mjs';\nexport { getCustomIcon } from './loader/custom.mjs';\nexport { searchForIcon } from './loader/modern.mjs';\nexport { loadIcon } from './loader/loader.mjs';\nexport { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from './emoji/cleanup.mjs';\nexport { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number } from './emoji/convert.mjs';\nexport { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString } from './emoji/format.mjs';\nexport { parseEmojiTestFile } from './emoji/test/parse.mjs';\nexport { getQualifiedEmojiVariations } from './emoji/test/variations.mjs';\nexport { findMissingEmojis } from './emoji/test/missing.mjs';\nexport { createOptimisedRegex, createOptimisedRegexForEmojiSequences } from './emoji/regex/create.mjs';\nexport { prepareEmojiForIconSet, prepareEmojiForIconsList } from './emoji/parse.mjs';\nexport { findAndReplaceEmojisInText } from './emoji/replace/replace.mjs';\nexport { camelToKebab, camelize, pascalize, snakelize } from './misc/strings.mjs';\nexport { commonObjectProps, compareObjects, unmergeObjects } from './misc/objects.mjs';\nexport { sanitiseTitleAttribute } from './misc/title.mjs';\nimport './css/common.mjs';\nimport './css/format.mjs';\nimport 'debug';\nimport './emoji/data.mjs';\nimport './emoji/test/components.mjs';\nimport './emoji/regex/tree.mjs';\nimport './emoji/regex/base.mjs';\nimport './emoji/regex/numbers.mjs';\nimport './emoji/regex/similar.mjs';\nimport './emoji/test/similar.mjs';\nimport './emoji/test/name.mjs';\nimport './emoji/test/tree.mjs';\nimport './emoji/replace/find.mjs';\n", "import { log } from '../logger.js';\nimport type { ExtendedIconifyIcon, IconifyIcon, IconifyJSON } from '@iconify/types';\nimport type { IconifyIconCustomisations } from '@iconify/utils';\nimport { getIconData, iconToHTML, iconToSVG, replaceIDs, stringToIcon } from '@iconify/utils';\n\ninterface AsyncIconLoader {\n  name: string;\n  loader: () => Promise<IconifyJSON>;\n}\n\ninterface SyncIconLoader {\n  name: string;\n  icons: IconifyJSON;\n}\n\nexport type IconLoader = AsyncIconLoader | SyncIconLoader;\n\nexport const unknownIcon: IconifyIcon = {\n  body: '<g><rect width=\"80\" height=\"80\" style=\"fill: #087ebf; stroke-width: 0px;\"/><text transform=\"translate(21.16 64.67)\" style=\"fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;\"><tspan x=\"0\" y=\"0\">?</tspan></text></g>',\n  height: 80,\n  width: 80,\n};\n\nconst iconsStore = new Map<string, IconifyJSON>();\nconst loaderStore = new Map<string, AsyncIconLoader['loader']>();\n\nexport const registerIconPacks = (iconLoaders: IconLoader[]) => {\n  for (const iconLoader of iconLoaders) {\n    if (!iconLoader.name) {\n      throw new Error(\n        'Invalid icon loader. Must have a \"name\" property with non-empty string value.'\n      );\n    }\n    log.debug('Registering icon pack:', iconLoader.name);\n    if ('loader' in iconLoader) {\n      loaderStore.set(iconLoader.name, iconLoader.loader);\n    } else if ('icons' in iconLoader) {\n      iconsStore.set(iconLoader.name, iconLoader.icons);\n    } else {\n      log.error('Invalid icon loader:', iconLoader);\n      throw new Error('Invalid icon loader. Must have either \"icons\" or \"loader\" property.');\n    }\n  }\n};\n\nconst getRegisteredIconData = async (iconName: string, fallbackPrefix?: string) => {\n  const data = stringToIcon(iconName, true, fallbackPrefix !== undefined);\n  if (!data) {\n    throw new Error(`Invalid icon name: ${iconName}`);\n  }\n  const prefix = data.prefix || fallbackPrefix;\n  if (!prefix) {\n    throw new Error(`Icon name must contain a prefix: ${iconName}`);\n  }\n  let icons = iconsStore.get(prefix);\n  if (!icons) {\n    const loader = loaderStore.get(prefix);\n    if (!loader) {\n      throw new Error(`Icon set not found: ${data.prefix}`);\n    }\n    try {\n      const loaded = await loader();\n      icons = { ...loaded, prefix };\n      iconsStore.set(prefix, icons);\n    } catch (e) {\n      log.error(e);\n      throw new Error(`Failed to load icon set: ${data.prefix}`);\n    }\n  }\n  const iconData = getIconData(icons, data.name);\n  if (!iconData) {\n    throw new Error(`Icon not found: ${iconName}`);\n  }\n  return iconData;\n};\n\nexport const isIconAvailable = async (iconName: string) => {\n  try {\n    await getRegisteredIconData(iconName);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const getIconSVG = async (\n  iconName: string,\n  customisations?: IconifyIconCustomisations & { fallbackPrefix?: string },\n  extraAttributes?: Record<string, string>\n) => {\n  let iconData: ExtendedIconifyIcon;\n  try {\n    iconData = await getRegisteredIconData(iconName, customisations?.fallbackPrefix);\n  } catch (e) {\n    log.error(e);\n    iconData = unknownIcon;\n  }\n  const renderData = iconToSVG(iconData, customisations);\n  const svg = iconToHTML(replaceIDs(renderData.body), {\n    ...renderData.attributes,\n    ...extraAttributes,\n  });\n  return svg;\n};\n", "export function dedent(\n  templ: TemplateStringsArray | string,\n  ...values: unknown[]\n): string {\n  let strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n\n  // 1. Remove trailing whitespace.\n  strings[strings.length - 1] = strings[strings.length - 1].replace(\n    /\\r?\\n([\\t ]*)$/,\n    '',\n  );\n\n  // 2. Find all line breaks to determine the highest common indentation level.\n  const indentLengths = strings.reduce((arr, str) => {\n    const matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n    if (matches) {\n      return arr.concat(\n        matches.map((match) => match.match(/[\\t ]/g)?.length ?? 0),\n      );\n    }\n    return arr;\n  }, <number[]>[]);\n\n  // 3. Remove the common indentation from all strings.\n  if (indentLengths.length) {\n    const pattern = new RegExp(`\\n[\\t ]{${Math.min(...indentLengths)}}`, 'g');\n\n    strings = strings.map((str) => str.replace(pattern, '\\n'));\n  }\n\n  // 4. Remove leading whitespace.\n  strings[0] = strings[0].replace(/^\\r?\\n/, '');\n\n  // 5. Perform interpolation.\n  let string = strings[0];\n\n  values.forEach((value, i) => {\n    // 5.1 Read current indentation level\n    const endentations = string.match(/(?:^|\\n)( *)$/)\n    const endentation = endentations ? endentations[1] : ''\n    let indentedValue = value\n    // 5.2 Add indentation to values with multiline strings\n    if (typeof value === 'string' && value.includes('\\n')) {\n      indentedValue = String(value)\n        .split('\\n')\n        .map((str, i) => {\n          return i === 0 ? str : `${endentation}${str}`\n        })\n        .join('\\n');\n    }\n\n    string += indentedValue + strings[i + 1];\n  });\n\n  return string;\n}\n\nexport default dedent;\n", "/**\n * Gets the original marked default options.\n */\nexport function _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null,\n    };\n}\nexport let _defaults = _getDefaults();\nexport function changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null };\nfunction edit(regex, opt = '') {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(other.caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        },\n    };\n    return obj;\n}\nexport const other = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n    htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/\\|table/g, '') // table not in commonmark\n    .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText,\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    lheading: lheadingGfm,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex(),\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex(),\n};\n/**\n * Inline-Level Grammar\n */\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n    .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\nconst emStrongRDelimAstCore = '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n    .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n    + '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest,\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    emStrongRDelimAst: emStrongRDelimAstGfm,\n    emStrongLDelim: emStrongLDelimGfm,\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex(),\n};\n/**\n * exports\n */\nexport const block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic,\n};\nexport const inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic,\n};\n", "import { other } from './rules.ts';\n/**\n * Helpers\n */\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nexport function escape(html, encode) {\n    if (encode) {\n        if (other.escapeTest.test(html)) {\n            return html.replace(other.escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (other.escapeTestNoEncode.test(html)) {\n            return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nexport function unescape(html) {\n    // explicitly match decimal, hex, and named HTML entities\n    return html.replace(other.unescapeTest, (_, n) => {\n        n = n.toLowerCase();\n        if (n === 'colon')\n            return ':';\n        if (n.charAt(0) === '#') {\n            return n.charAt(1) === 'x'\n                ? String.fromCharCode(parseInt(n.substring(2), 16))\n                : String.fromCharCode(+n.substring(1));\n        }\n        return '';\n    });\n}\nexport function cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(other.percentDecode, '%');\n    }\n    catch {\n        return null;\n    }\n    return href;\n}\nexport function splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(other.splitPipe);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells.at(-1)?.trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n            suffLen++;\n        }\n        else if (currChar !== c && invert) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nexport function findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport { rtrim, splitCells, findClosingBracket, } from './helpers.ts';\nfunction outputLink(cap, link, raw, lexer, rules) {\n    const href = link.href;\n    const title = link.title || null;\n    const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text),\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text,\n    };\n}\nfunction indentCodeCompensation(raw, text, rules) {\n    const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(rules.other.beginningSpace);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nexport class _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0],\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text,\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text,\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (this.rules.other.endingHash.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: rtrim(cap[0], '\\n'),\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            let lines = rtrim(cap[0], '\\n').split('\\n');\n            let raw = '';\n            let text = '';\n            const tokens = [];\n            while (lines.length > 0) {\n                let inBlockquote = false;\n                const currentLines = [];\n                let i;\n                for (i = 0; i < lines.length; i++) {\n                    // get lines up to a continuation\n                    if (this.rules.other.blockquoteStart.test(lines[i])) {\n                        currentLines.push(lines[i]);\n                        inBlockquote = true;\n                    }\n                    else if (!inBlockquote) {\n                        currentLines.push(lines[i]);\n                    }\n                    else {\n                        break;\n                    }\n                }\n                lines = lines.slice(i);\n                const currentRaw = currentLines.join('\\n');\n                const currentText = currentRaw\n                    // precede setext continuation with 4 spaces so it isn't a setext\n                    .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n                    .replace(this.rules.other.blockquoteSetextReplace2, '');\n                raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n                text = text ? `${text}\\n${currentText}` : currentText;\n                // parse blockquote lines as top level tokens\n                // merge paragraphs if this is a continuation\n                const top = this.lexer.state.top;\n                this.lexer.state.top = true;\n                this.lexer.blockTokens(currentText, tokens, true);\n                this.lexer.state.top = top;\n                // if there is no continuation then we are done\n                if (lines.length === 0) {\n                    break;\n                }\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'code') {\n                    // blockquote continuation cannot be preceded by a code block\n                    break;\n                }\n                else if (lastToken?.type === 'blockquote') {\n                    // include continuation in nested blockquote\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.blockquote(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n                    break;\n                }\n                else if (lastToken?.type === 'list') {\n                    // include continuation in nested list\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.list(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n                    lines = newText.substring(tokens.at(-1).raw.length).split('\\n');\n                    continue;\n                }\n            }\n            return {\n                type: 'blockquote',\n                raw,\n                tokens,\n                text,\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: [],\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = this.rules.other.listItemRegex(bull);\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                let raw = '';\n                let itemContents = '';\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let blankLine = !line.trim();\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else if (blankLine) {\n                    indent = cap[1].length + 1;\n                }\n                else {\n                    indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n                    const hrRegex = this.rules.other.hrRegex(indent);\n                    const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n                    const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n                    const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        let nextLineWithoutTabs;\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n                            nextLineWithoutTabs = nextLine;\n                        }\n                        else {\n                            nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of html block\n                        if (htmlBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(nextLine)) {\n                            break;\n                        }\n                        if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLineWithoutTabs.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (this.rules.other.doubleBlankLine.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = this.rules.other.listIsTask.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: [],\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            const lastItem = list.items.at(-1);\n            if (lastItem) {\n                lastItem.raw = lastItem.raw.trimEnd();\n                lastItem.text = lastItem.text.trimEnd();\n            }\n            else {\n                // not a list since there were no items\n                return;\n            }\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0],\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title,\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!this.rules.other.tableDelimiter.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n        const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: [],\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (this.rules.other.tableAlignRight.test(align)) {\n                item.align.push('right');\n            }\n            else if (this.rules.other.tableAlignCenter.test(align)) {\n                item.align.push('center');\n            }\n            else if (this.rules.other.tableAlignLeft.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (let i = 0; i < headers.length; i++) {\n            item.header.push({\n                text: headers[i],\n                tokens: this.lexer.inline(headers[i]),\n                header: true,\n                align: item.align[i],\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell),\n                    header: false,\n                    align: item.align[i],\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1]),\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0]),\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: cap[1],\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0],\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = this.rules.other.pedanticHrefTitle.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (this.rules.other.startAngleBracket.test(href)) {\n                if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n            }, cap[0], this.lexer, this.rules);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text,\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer, this.rules);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text),\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text),\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n            const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n            const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0],\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2]),\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[1];\n                href = 'mailto:' + text;\n            }\n            else {\n                text = cap[1];\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[0];\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = cap[0];\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            const escaped = this.lexer.state.inRawBlock;\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                escaped,\n            };\n        }\n    }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\n/**\n * Block Lexer\n */\nexport class _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true,\n        };\n        const rules = {\n            other,\n            block: block.normal,\n            inline: inline.normal,\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline,\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src.replace(other.carriageReturn, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n            src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n        }\n        while (src) {\n            let token;\n            if (this.options.extensions?.block?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.raw.length === 1 && lastToken !== undefined) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    lastToken.raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title,\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                const lastToken = tokens.at(-1);\n                if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = cutSrc.length !== src.length;\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match = null;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index)\n                            + '[' + 'a'.repeat(match[0].length - 2) + ']'\n                            + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        let keepPrevChar = false;\n        let prevChar = '';\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            let token;\n            // extensions\n            if (this.options.extensions?.inline?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.type === 'text' && lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { cleanUrl, escape, } from './helpers.ts';\nimport { other } from './rules.ts';\n/**\n * Renderer\n */\nexport class _Renderer {\n    options;\n    parser; // set by the parser\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(token) {\n        return '';\n    }\n    code({ text, lang, escaped }) {\n        const langString = (lang || '').match(other.notSpaceStart)?.[0];\n        const code = text.replace(other.endingNewline, '') + '\\n';\n        if (!langString) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(langString)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote({ tokens }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n    }\n    html({ text }) {\n        return text;\n    }\n    heading({ tokens, depth }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n    }\n    hr(token) {\n        return '<hr>\\n';\n    }\n    list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n            const item = token.items[j];\n            body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n            const checkbox = this.checkbox({ checked: !!item.checked });\n            if (item.loose) {\n                if (item.tokens[0]?.type === 'paragraph') {\n                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                        item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n                        item.tokens[0].tokens[0].escaped = true;\n                    }\n                }\n                else {\n                    item.tokens.unshift({\n                        type: 'text',\n                        raw: checkbox + ' ',\n                        text: checkbox + ' ',\n                        escaped: true,\n                    });\n                }\n            }\n            else {\n                itemBody += checkbox + ' ';\n            }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n    }\n    checkbox({ checked }) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n    }\n    table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n            cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({ text: cell });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n            const row = token.rows[j];\n            cell = '';\n            for (let k = 0; k < row.length; k++) {\n                cell += this.tablecell(row[k]);\n            }\n            body += this.tablerow({ text: cell });\n        }\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow({ text }) {\n        return `<tr>\\n${text}</tr>\\n`;\n    }\n    tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align\n            ? `<${type} align=\"${token.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong({ tokens }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n    }\n    em({ tokens }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n    }\n    codespan({ text }) {\n        return `<code>${escape(text, true)}</code>`;\n    }\n    br(token) {\n        return '<br>';\n    }\n    del({ tokens }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n    }\n    link({ href, title, tokens }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + (escape(title)) + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image({ href, title, text }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return escape(text);\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${escape(title)}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(token) {\n        return 'tokens' in token && token.tokens\n            ? this.parser.parseInline(token.tokens)\n            : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n    }\n}\n", "/**\n * Text<PERSON><PERSON>er\n * returns only the textual part of the token\n */\nexport class _TextRenderer {\n    // no need for block level renderers\n    strong({ text }) {\n        return text;\n    }\n    em({ text }) {\n        return text;\n    }\n    codespan({ text }) {\n        return text;\n    }\n    del({ text }) {\n        return text;\n    }\n    html({ text }) {\n        return text;\n    }\n    text({ text }) {\n        return text;\n    }\n    link({ text }) {\n        return '' + text;\n    }\n    image({ text }) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\n/**\n * Parsing & Compiling\n */\nexport class _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const genericToken = anyToken;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'space': {\n                    out += this.renderer.space(token);\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr(token);\n                    continue;\n                }\n                case 'heading': {\n                    out += this.renderer.heading(token);\n                    continue;\n                }\n                case 'code': {\n                    out += this.renderer.code(token);\n                    continue;\n                }\n                case 'table': {\n                    out += this.renderer.table(token);\n                    continue;\n                }\n                case 'blockquote': {\n                    out += this.renderer.blockquote(token);\n                    continue;\n                }\n                case 'list': {\n                    out += this.renderer.list(token);\n                    continue;\n                }\n                case 'html': {\n                    out += this.renderer.html(token);\n                    continue;\n                }\n                case 'paragraph': {\n                    out += this.renderer.paragraph(token);\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = this.renderer.text(textToken);\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + this.renderer.text(textToken);\n                    }\n                    if (top) {\n                        out += this.renderer.paragraph({\n                            type: 'paragraph',\n                            raw: body,\n                            text: body,\n                            tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n                        });\n                    }\n                    else {\n                        out += body;\n                    }\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer = this.renderer) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'escape': {\n                    out += renderer.text(token);\n                    break;\n                }\n                case 'html': {\n                    out += renderer.html(token);\n                    break;\n                }\n                case 'link': {\n                    out += renderer.link(token);\n                    break;\n                }\n                case 'image': {\n                    out += renderer.image(token);\n                    break;\n                }\n                case 'strong': {\n                    out += renderer.strong(token);\n                    break;\n                }\n                case 'em': {\n                    out += renderer.em(token);\n                    break;\n                }\n                case 'codespan': {\n                    out += renderer.codespan(token);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br(token);\n                    break;\n                }\n                case 'del': {\n                    out += renderer.del(token);\n                    break;\n                }\n                case 'text': {\n                    out += renderer.text(token);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nexport class _Hooks {\n    options;\n    block;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens',\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n    /**\n     * Provide function to tokenize markdown\n     */\n    provideLexer() {\n        return this.block ? _Lexer.lex : _Lexer.lexInline;\n    }\n    /**\n     * Provide function to parse tokens\n     */\n    provideParser() {\n        return this.block ? _Parser.parse : _Parser.parseInline;\n    }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nexport class Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.parseMarkdown(true);\n    parseInline = this.parseMarkdown(false);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (['options', 'parser'].includes(prop)) {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    const rendererFunc = pack.renderer[rendererProp];\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (['options', 'block'].includes(prop)) {\n                        // ignore options and block properties\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    parseMarkdown(blockType) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const parse = (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            const throwError = this.onError(!!opt.silent, !!opt.async);\n            // throw error if an extension set async to true but parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n            }\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n                opt.hooks.block = blockType;\n            }\n            const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n            const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n        return parse;\n    }\n    onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n", "import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport { _getDefaults, changeDefaults, _defaults, } from './defaults.ts';\nconst markedInstance = new Marked();\nexport function marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\n", "import type { MarkedToken, Token } from 'marked';\nimport { marked } from 'marked';\nimport { dedent } from 'ts-dedent';\nimport type { MarkdownLine, MarkdownWordType } from './types.js';\nimport type { MermaidConfig } from '../config.type.js';\n\n/**\n * @param markdown - markdown to process\n * @returns processed markdown\n */\nfunction preprocessMarkdown(markdown: string, { markdownAutoWrap }: MermaidConfig): string {\n  //Replace <br/>with \\n\n  const withoutBR = markdown.replace(/<br\\/>/g, '\\n');\n  // Replace multiple newlines with a single newline\n  const withoutMultipleNewlines = withoutBR.replace(/\\n{2,}/g, '\\n');\n  // Remove extra spaces at the beginning of each line\n  const withoutExtraSpaces = dedent(withoutMultipleNewlines);\n  if (markdownAutoWrap === false) {\n    return withoutExtraSpaces.replace(/ /g, '&nbsp;');\n  }\n  return withoutExtraSpaces;\n}\n\n/**\n * @param markdown - markdown to split into lines\n */\nexport function markdownToLines(markdown: string, config: MermaidConfig = {}): MarkdownLine[] {\n  const preprocessedMarkdown = preprocessMarkdown(markdown, config);\n  const nodes = marked.lexer(preprocessedMarkdown);\n  const lines: MarkdownLine[] = [[]];\n  let currentLine = 0;\n\n  function processNode(node: MarkedToken, parentType: MarkdownWordType = 'normal') {\n    if (node.type === 'text') {\n      const textLines = node.text.split('\\n');\n      textLines.forEach((textLine, index) => {\n        if (index !== 0) {\n          currentLine++;\n          lines.push([]);\n        }\n        textLine.split(' ').forEach((word) => {\n          word = word.replace(/&#39;/g, `'`);\n          if (word) {\n            lines[currentLine].push({ content: word, type: parentType });\n          }\n        });\n      });\n    } else if (node.type === 'strong' || node.type === 'em') {\n      node.tokens.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken, node.type);\n      });\n    } else if (node.type === 'html') {\n      lines[currentLine].push({ content: node.text, type: 'normal' });\n    }\n  }\n\n  nodes.forEach((treeNode) => {\n    if (treeNode.type === 'paragraph') {\n      treeNode.tokens?.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken);\n      });\n    } else if (treeNode.type === 'html') {\n      lines[currentLine].push({ content: treeNode.text, type: 'normal' });\n    }\n  });\n\n  return lines;\n}\n\nexport function markdownToHTML(markdown: string, { markdownAutoWrap }: MermaidConfig = {}) {\n  const nodes = marked.lexer(markdown);\n\n  function output(node: Token): string {\n    if (node.type === 'text') {\n      if (markdownAutoWrap === false) {\n        return node.text.replace(/\\n */g, '<br/>').replace(/ /g, '&nbsp;');\n      }\n      return node.text.replace(/\\n */g, '<br/>');\n    } else if (node.type === 'strong') {\n      return `<strong>${node.tokens?.map(output).join('')}</strong>`;\n    } else if (node.type === 'em') {\n      return `<em>${node.tokens?.map(output).join('')}</em>`;\n    } else if (node.type === 'paragraph') {\n      return `<p>${node.tokens?.map(output).join('')}</p>`;\n    } else if (node.type === 'space') {\n      return '';\n    } else if (node.type === 'html') {\n      return `${node.text}`;\n    } else if (node.type === 'escape') {\n      return node.text;\n    }\n    return `Unsupported markdown: ${node.type}`;\n  }\n\n  return nodes.map(output).join('');\n}\n", "import type { CheckFitFunction, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MarkdownWordType } from './types.js';\n\n/**\n * Splits a string into graphemes if available, otherwise characters.\n */\nexport function splitTextToChars(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter().segment(text)].map((s) => s.segment);\n  }\n  return [...text];\n}\n\n/**\n * Splits a string into words by using `Intl.Segmenter` if available, or splitting by ' '.\n * `Intl.Segmenter` uses the default locale, which might be different across browsers.\n */\nexport function splitLineToWords(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter(undefined, { granularity: 'word' }).segment(text)].map(\n      (s) => s.segment\n    );\n  }\n  // Split by ' ' removes the ' 's from the result.\n  const words = text.split(' ');\n  // Add the ' 's back to the result.\n  const wordsWithSpaces = words.flatMap((s) => [s, ' ']).filter((s) => s);\n  // Remove last space.\n  wordsWithSpaces.pop();\n  return wordsWithSpaces;\n}\n\n/**\n * Splits a word into two parts, the first part fits the width and the remaining part.\n * @param checkFit - Function to check if word fits\n * @param word - Word to split\n * @returns [first part of word that fits, rest of word]\n */\nexport function splitWordToFitWidth(\n  checkFit: CheckFitFunction,\n  word: MarkdownWord\n): [MarkdownWord, MarkdownWord] {\n  const characters = splitTextToChars(word.content);\n  return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);\n}\n\nfunction splitWordToFitWidthRecursion(\n  checkFit: CheckFitFunction,\n  usedChars: string[],\n  remainingChars: string[],\n  type: MarkdownWordType\n): [MarkdownWord, MarkdownWord] {\n  if (remainingChars.length === 0) {\n    return [\n      { content: usedChars.join(''), type },\n      { content: '', type },\n    ];\n  }\n  const [nextChar, ...rest] = remainingChars;\n  const newWord = [...usedChars, nextChar];\n  if (checkFit([{ content: newWord.join(''), type }])) {\n    return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);\n  }\n  if (usedChars.length === 0 && nextChar) {\n    // If the first character does not fit, split it anyway\n    usedChars.push(nextChar);\n    remainingChars.shift();\n  }\n  return [\n    { content: usedChars.join(''), type },\n    { content: remainingChars.join(''), type },\n  ];\n}\n\n/**\n * Splits a line into multiple lines that satisfy the checkFit function.\n * @param line - Line to split\n * @param checkFit - Function to check if line fits\n * @returns Array of lines that fit\n */\nexport function splitLineToFitWidth(\n  line: MarkdownLine,\n  checkFit: CheckFitFunction\n): MarkdownLine[] {\n  if (line.some(({ content }) => content.includes('\\n'))) {\n    throw new Error('splitLineToFitWidth does not support newlines in the line');\n  }\n  return splitLineToFitWidthRecursion(line, checkFit);\n}\n\nfunction splitLineToFitWidthRecursion(\n  words: MarkdownWord[],\n  checkFit: CheckFitFunction,\n  lines: MarkdownLine[] = [],\n  newLine: MarkdownLine = []\n): MarkdownLine[] {\n  // Return if there is nothing left to split\n  if (words.length === 0) {\n    // If there is a new line, add it to the lines\n    if (newLine.length > 0) {\n      lines.push(newLine);\n    }\n    return lines.length > 0 ? lines : [];\n  }\n  let joiner = '';\n  if (words[0].content === ' ') {\n    joiner = ' ';\n    words.shift();\n  }\n  const nextWord: MarkdownWord = words.shift() ?? { content: ' ', type: 'normal' };\n  const lineWithNextWord: MarkdownLine = [...newLine];\n  if (joiner !== '') {\n    lineWithNextWord.push({ content: joiner, type: 'normal' });\n  }\n  lineWithNextWord.push(nextWord);\n\n  if (checkFit(lineWithNextWord)) {\n    // nextWord fits, so we can add it to the new line and continue\n    return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);\n  }\n\n  // nextWord doesn't fit, so we need to split it\n  if (newLine.length > 0) {\n    // There was text in newLine, so add it to lines and push nextWord back into words.\n    lines.push(newLine);\n    words.unshift(nextWord);\n  } else if (nextWord.content) {\n    // There was no text in newLine, so we need to split nextWord\n    const [line, rest] = splitWordToFitWidth(checkFit, nextWord);\n    lines.push([line]);\n    if (rest.content) {\n      words.unshift(rest);\n    }\n  }\n  return splitLineToFitWidthRecursion(words, checkFit, lines);\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n// @ts-nocheck TODO: Fix types\nimport { select } from 'd3';\nimport type { MermaidConfig } from '../config.type.js';\nimport { getConfig, sanitizeText } from '../diagram-api/diagramAPI.js';\nimport type { SVGGroup } from '../diagram-api/types.js';\nimport common, { hasKatex, renderKatex } from '../diagrams/common/common.js';\nimport type { D3TSpanElement, D3TextElement } from '../diagrams/common/commonTypes.js';\nimport { log } from '../logger.js';\nimport { markdownToHTML, markdownToLines } from '../rendering-util/handle-markdown-text.js';\nimport { decodeEntities } from '../utils.js';\nimport { getIconSVG, isIconAvailable } from './icons.js';\nimport { splitLineToFitWidth } from './splitText.js';\nimport type { MarkdownLine, MarkdownWord } from './types.js';\n\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\nasync function addHtmlSpan(element, node, width, classes, addBackground = false) {\n  const fo = element.append('foreignObject');\n  // This is not the final width but used in order to make sure the foreign\n  // object in firefox gets a width at all. The final width is fetched from the div\n  fo.attr('width', `${10 * width}px`);\n  fo.attr('height', `${10 * width}px`);\n\n  const div = fo.append('xhtml:div');\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common.lineBreakRegex, '\\n'), getConfig());\n  }\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  const span = div.append('span');\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr('class', `${labelClass} ${classes}`);\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'table-cell');\n  div.style('white-space', 'nowrap');\n  div.style('line-height', '1.5');\n  div.style('max-width', width + 'px');\n  div.style('text-align', 'center');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  if (addBackground) {\n    div.attr('class', 'labelBkg');\n  }\n\n  let bbox = div.node().getBoundingClientRect();\n  if (bbox.width === width) {\n    div.style('display', 'table');\n    div.style('white-space', 'break-spaces');\n    div.style('width', width + 'px');\n    bbox = div.node().getBoundingClientRect();\n  }\n\n  // fo.style('width', bbox.width);\n  // fo.style('height', bbox.height);\n\n  return fo.node();\n}\n\n/**\n * Creates a tspan element with the specified attributes for text positioning.\n *\n * @param textElement - The parent text element to append the tspan element.\n * @param lineIndex - The index of the current line in the structuredText array.\n * @param lineHeight - The line height value for the text.\n * @returns The created tspan element.\n */\nfunction createTspan(textElement: any, lineIndex: number, lineHeight: number) {\n  return textElement\n    .append('tspan')\n    .attr('class', 'text-outer-tspan')\n    .attr('x', 0)\n    .attr('y', lineIndex * lineHeight - 0.1 + 'em')\n    .attr('dy', lineHeight + 'em');\n}\n\nfunction computeWidthOfText(parentNode: any, lineHeight: number, line: MarkdownLine): number {\n  const testElement = parentNode.append('text');\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, line);\n  const textLength = testSpan.node().getComputedTextLength();\n  testElement.remove();\n  return textLength;\n}\n\nexport function computeDimensionOfText(\n  parentNode: SVGGroup,\n  lineHeight: number,\n  text: string\n): DOMRect | undefined {\n  const testElement: D3TextElement = parentNode.append('text');\n  const testSpan: D3TSpanElement = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, [{ content: text, type: 'normal' }]);\n  const textDimension: DOMRect | undefined = testSpan.node()?.getBoundingClientRect();\n  if (textDimension) {\n    testElement.remove();\n  }\n  return textDimension;\n}\n\n/**\n * Creates a formatted text element by breaking lines and applying styles based on\n * the given structuredText.\n *\n * @param width - The maximum allowed width of the text.\n * @param g - The parent group element to append the formatted text.\n * @param structuredText - The structured text data to format.\n * @param addBackground - Whether to add a background to the text.\n */\nfunction createFormattedText(\n  width: number,\n  g: any,\n  structuredText: MarkdownWord[][],\n  addBackground = false\n) {\n  const lineHeight = 1.1;\n  const labelGroup = g.append('g');\n  const bkg = labelGroup.insert('rect').attr('class', 'background').attr('style', 'stroke: none');\n  const textElement = labelGroup.append('text').attr('y', '-10.1');\n  let lineIndex = 0;\n  for (const line of structuredText) {\n    /**\n     * Preprocess raw string content of line data\n     * Creating an array of strings pre-split to satisfy width limit\n     */\n    const checkWidth = (line: MarkdownLine) =>\n      computeWidthOfText(labelGroup, lineHeight, line) <= width;\n    const linesUnderWidth = checkWidth(line) ? [line] : splitLineToFitWidth(line, checkWidth);\n    /** Add each prepared line as a tspan to the parent node */\n    for (const preparedLine of linesUnderWidth) {\n      const tspan = createTspan(textElement, lineIndex, lineHeight);\n      updateTextContentAndStyles(tspan, preparedLine);\n      lineIndex++;\n    }\n  }\n  if (addBackground) {\n    const bbox = textElement.node().getBBox();\n    const padding = 2;\n    bkg\n      .attr('x', bbox.x - padding)\n      .attr('y', bbox.y - padding)\n      .attr('width', bbox.width + 2 * padding)\n      .attr('height', bbox.height + 2 * padding);\n\n    return labelGroup.node();\n  } else {\n    return textElement.node();\n  }\n}\n\n/**\n * Updates the text content and styles of the given tspan element based on the\n * provided wrappedLine data.\n *\n * @param tspan - The tspan element to update.\n * @param wrappedLine - The line data to apply to the tspan element.\n */\nfunction updateTextContentAndStyles(tspan: any, wrappedLine: MarkdownWord[]) {\n  tspan.text('');\n\n  wrappedLine.forEach((word, index) => {\n    const innerTspan = tspan\n      .append('tspan')\n      .attr('font-style', word.type === 'em' ? 'italic' : 'normal')\n      .attr('class', 'text-inner-tspan')\n      .attr('font-weight', word.type === 'strong' ? 'bold' : 'normal');\n    if (index === 0) {\n      innerTspan.text(word.content);\n    } else {\n      // TODO: check what joiner to use.\n      innerTspan.text(' ' + word.content);\n    }\n  });\n}\n\n/**\n * Convert fontawesome labels into fontawesome icons by using a regex pattern\n * @param text - The raw string to convert\n * @returns string with fontawesome icons as svg if the icon is registered otherwise as i tags\n */\nexport async function replaceIconSubstring(text: string) {\n  const pendingReplacements: Promise<string>[] = [];\n  // cspell: disable-next-line\n  text.replace(/(fa[bklrs]?):fa-([\\w-]+)/g, (fullMatch, prefix, iconName) => {\n    pendingReplacements.push(\n      (async () => {\n        const registeredIconName = `${prefix}:${iconName}`;\n        if (await isIconAvailable(registeredIconName)) {\n          return await getIconSVG(registeredIconName, undefined, { class: 'label-icon' });\n        } else {\n          return `<i class='${sanitizeText(fullMatch).replace(':', ' ')}'></i>`;\n        }\n      })()\n    );\n    return fullMatch;\n  });\n\n  const replacements = await Promise.all(pendingReplacements);\n  // cspell: disable-next-line\n  return text.replace(/(fa[bklrs]?):fa-([\\w-]+)/g, () => replacements.shift() ?? '');\n}\n\n// Note when using from flowcharts converting the API isNode means classes should be set accordingly. When using htmlLabels => to set classes to 'nodeLabel' when isNode=true otherwise 'edgeLabel'\n// When not using htmlLabels => to set classes to 'title-row' when isTitle=true otherwise 'title-row'\nexport const createText = async (\n  el,\n  text = '',\n  {\n    style = '',\n    isTitle = false,\n    classes = '',\n    useHtmlLabels = true,\n    isNode = true,\n    width = 200,\n    addSvgBackground = false,\n  } = {},\n  config?: MermaidConfig\n) => {\n  log.debug(\n    'XYZ createText',\n    text,\n    style,\n    isTitle,\n    classes,\n    useHtmlLabels,\n    isNode,\n    'addSvgBackground: ',\n    addSvgBackground\n  );\n  if (useHtmlLabels) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n\n    const htmlText = markdownToHTML(text, config);\n    const decodedReplacedText = await replaceIconSubstring(decodeEntities(htmlText));\n\n    //for Katex the text could contain escaped characters, \\\\relax that should be transformed to \\relax\n    const inputForKatex = text.replace(/\\\\\\\\/g, '\\\\');\n\n    const node = {\n      isNode,\n      label: hasKatex(text) ? inputForKatex : decodedReplacedText,\n      labelStyle: style.replace('fill:', 'color:'),\n    };\n    const vertexNode = await addHtmlSpan(el, node, width, classes, addSvgBackground);\n    return vertexNode;\n  } else {\n    //sometimes the user might add br tags with 1 or more spaces in between, so we need to replace them with <br/>\n    const sanitizeBR = text.replace(/<br\\s*\\/?>/g, '<br/>');\n    const structuredText = markdownToLines(sanitizeBR.replace('<br>', '<br/>'), config);\n    const svgLabel = createFormattedText(\n      width,\n      el,\n      structuredText,\n      text ? addSvgBackground : false\n    );\n    if (isNode) {\n      if (/stroke:/.exec(style)) {\n        style = style.replace('stroke:', 'lineColor:');\n      }\n\n      const nodeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).attr('style', nodeLabelTextStyle);\n      // svgLabel.setAttribute('style', style);\n    } else {\n      //On style, assume `stroke`, `stroke-width` are used for edge path, so remove them\n      // remove `fill`\n      //  use  `background` as `fill` for label rect,\n\n      const edgeLabelRectStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/background:/g, 'fill:');\n      select(svgLabel)\n        .select('rect')\n        .attr('style', edgeLabelRectStyle.replace(/background:/g, 'fill:'));\n\n      // for text, update fill color with `color`\n      const edgeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).select('text').attr('style', edgeLabelTextStyle);\n    }\n    return svgLabel;\n  }\n};\n"], "mappings": "uLAAA,IAAAA,GAAAC,GAAA,CAAAC,GAAAC,KAAA,cAIA,IAAIC,EAAI,IACJC,EAAID,EAAI,GACRE,EAAID,EAAI,GACRE,EAAID,EAAI,GACRE,GAAID,EAAI,EACRE,GAAIF,EAAI,OAgBZJ,GAAO,QAAU,SAAUO,EAAKC,EAAS,CACvCA,EAAUA,GAAW,CAAC,EACtB,IAAIC,EAAO,OAAOF,EAClB,GAAIE,IAAS,UAAYF,EAAI,OAAS,EACpC,OAAOG,GAAMH,CAAG,EACX,GAAIE,IAAS,UAAY,SAASF,CAAG,EAC1C,OAAOC,EAAQ,KAAOG,GAAQJ,CAAG,EAAIK,GAASL,CAAG,EAEnD,MAAM,IAAI,MACR,wDACE,KAAK,UAAUA,CAAG,CACtB,CACF,EAUA,SAASG,GAAMG,EAAK,CAElB,GADAA,EAAM,OAAOA,CAAG,EACZ,EAAAA,EAAI,OAAS,KAGjB,KAAIC,EAAQ,mIAAmI,KAC7ID,CACF,EACA,GAAKC,EAGL,KAAIC,EAAI,WAAWD,EAAM,CAAC,CAAC,EACvBL,GAAQK,EAAM,CAAC,GAAK,MAAM,YAAY,EAC1C,OAAQL,EAAM,CACZ,IAAK,QACL,IAAK,OACL,IAAK,MACL,IAAK,KACL,IAAK,IACH,OAAOM,EAAIT,GACb,IAAK,QACL,IAAK,OACL,IAAK,IACH,OAAOS,EAAIV,GACb,IAAK,OACL,IAAK,MACL,IAAK,IACH,OAAOU,EAAIX,EACb,IAAK,QACL,IAAK,OACL,IAAK,MACL,IAAK,KACL,IAAK,IACH,OAAOW,EAAIZ,EACb,IAAK,UACL,IAAK,SACL,IAAK,OACL,IAAK,MACL,IAAK,IACH,OAAOY,EAAIb,EACb,IAAK,UACL,IAAK,SACL,IAAK,OACL,IAAK,MACL,IAAK,IACH,OAAOa,EAAId,EACb,IAAK,eACL,IAAK,cACL,IAAK,QACL,IAAK,OACL,IAAK,KACH,OAAOc,EACT,QACE,MACJ,GACF,CAvDSC,EAAAN,GAAA,SAiET,SAASE,GAASK,EAAI,CACpB,IAAIC,EAAQ,KAAK,IAAID,CAAE,EACvB,OAAIC,GAASd,EACJ,KAAK,MAAMa,EAAKb,CAAC,EAAI,IAE1Bc,GAASf,EACJ,KAAK,MAAMc,EAAKd,CAAC,EAAI,IAE1Be,GAAShB,EACJ,KAAK,MAAMe,EAAKf,CAAC,EAAI,IAE1BgB,GAASjB,EACJ,KAAK,MAAMgB,EAAKhB,CAAC,EAAI,IAEvBgB,EAAK,IACd,CAfSD,EAAAJ,GAAA,YAyBT,SAASD,GAAQM,EAAI,CACnB,IAAIC,EAAQ,KAAK,IAAID,CAAE,EACvB,OAAIC,GAASd,EACJe,EAAOF,EAAIC,EAAOd,EAAG,KAAK,EAE/Bc,GAASf,EACJgB,EAAOF,EAAIC,EAAOf,EAAG,MAAM,EAEhCe,GAAShB,EACJiB,EAAOF,EAAIC,EAAOhB,EAAG,QAAQ,EAElCgB,GAASjB,EACJkB,EAAOF,EAAIC,EAAOjB,EAAG,QAAQ,EAE/BgB,EAAK,KACd,CAfSD,EAAAL,GAAA,WAqBT,SAASQ,EAAOF,EAAIC,EAAOH,EAAGK,EAAM,CAClC,IAAIC,EAAWH,GAASH,EAAI,IAC5B,OAAO,KAAK,MAAME,EAAKF,CAAC,EAAI,IAAMK,GAAQC,EAAW,IAAM,GAC7D,CAHSL,EAAAG,EAAA,YC9JT,IAAAG,GAAAC,GAAA,CAAAC,GAAAC,KAAA,cAMA,SAASC,GAAMC,EAAK,CACnBC,EAAY,MAAQA,EACpBA,EAAY,QAAUA,EACtBA,EAAY,OAASC,EACrBD,EAAY,QAAUE,EACtBF,EAAY,OAASG,EACrBH,EAAY,QAAUI,EACtBJ,EAAY,SAAW,KACvBA,EAAY,QAAUK,EAEtB,OAAO,KAAKN,CAAG,EAAE,QAAQO,GAAO,CAC/BN,EAAYM,CAAG,EAAIP,EAAIO,CAAG,CAC3B,CAAC,EAMDN,EAAY,MAAQ,CAAC,EACrBA,EAAY,MAAQ,CAAC,EAOrBA,EAAY,WAAa,CAAC,EAQ1B,SAASO,EAAYC,EAAW,CAC/B,IAAIC,EAAO,EAEX,QAASC,EAAI,EAAGA,EAAIF,EAAU,OAAQE,IACrCD,GAASA,GAAQ,GAAKA,EAAQD,EAAU,WAAWE,CAAC,EACpDD,GAAQ,EAGT,OAAOT,EAAY,OAAO,KAAK,IAAIS,CAAI,EAAIT,EAAY,OAAO,MAAM,CACrE,CATSW,EAAAJ,EAAA,eAUTP,EAAY,YAAcO,EAS1B,SAASP,EAAYQ,EAAW,CAC/B,IAAII,EACAC,EAAiB,KACjBC,EACAC,EAEJ,SAASC,KAASC,EAAM,CAEvB,GAAI,CAACD,EAAM,QACV,OAGD,IAAME,EAAOF,EAGPG,EAAO,OAAO,IAAI,IAAM,EACxBC,EAAKD,GAAQP,GAAYO,GAC/BD,EAAK,KAAOE,EACZF,EAAK,KAAON,EACZM,EAAK,KAAOC,EACZP,EAAWO,EAEXF,EAAK,CAAC,EAAIjB,EAAY,OAAOiB,EAAK,CAAC,CAAC,EAEhC,OAAOA,EAAK,CAAC,GAAM,UAEtBA,EAAK,QAAQ,IAAI,EAIlB,IAAII,EAAQ,EACZJ,EAAK,CAAC,EAAIA,EAAK,CAAC,EAAE,QAAQ,gBAAiB,CAACK,EAAOC,IAAW,CAE7D,GAAID,IAAU,KACb,MAAO,IAERD,IACA,IAAMG,EAAYxB,EAAY,WAAWuB,CAAM,EAC/C,GAAI,OAAOC,GAAc,WAAY,CACpC,IAAMC,GAAMR,EAAKI,CAAK,EACtBC,EAAQE,EAAU,KAAKN,EAAMO,EAAG,EAGhCR,EAAK,OAAOI,EAAO,CAAC,EACpBA,GACD,CACA,OAAOC,CACR,CAAC,EAGDtB,EAAY,WAAW,KAAKkB,EAAMD,CAAI,GAExBC,EAAK,KAAOlB,EAAY,KAChC,MAAMkB,EAAMD,CAAI,CACvB,CAhDS,OAAAN,EAAAK,EAAA,SAkDTA,EAAM,UAAYR,EAClBQ,EAAM,UAAYhB,EAAY,UAAU,EACxCgB,EAAM,MAAQhB,EAAY,YAAYQ,CAAS,EAC/CQ,EAAM,OAASU,EACfV,EAAM,QAAUhB,EAAY,QAE5B,OAAO,eAAegB,EAAO,UAAW,CACvC,WAAY,GACZ,aAAc,GACd,IAAKL,EAAA,IACAE,IAAmB,KACfA,GAEJC,IAAoBd,EAAY,aACnCc,EAAkBd,EAAY,WAC9Be,EAAef,EAAY,QAAQQ,CAAS,GAGtCO,GATH,OAWL,IAAKJ,EAAAgB,GAAK,CACTd,EAAiBc,CAClB,EAFK,MAGN,CAAC,EAGG,OAAO3B,EAAY,MAAS,YAC/BA,EAAY,KAAKgB,CAAK,EAGhBA,CACR,CAvFSL,EAAAX,EAAA,eAyFT,SAAS0B,EAAOlB,EAAWoB,EAAW,CACrC,IAAMC,EAAW7B,EAAY,KAAK,WAAa,OAAO4B,EAAc,IAAc,IAAMA,GAAapB,CAAS,EAC9G,OAAAqB,EAAS,IAAM,KAAK,IACbA,CACR,CAJSlB,EAAAe,EAAA,UAaT,SAASvB,EAAO2B,EAAY,CAC3B9B,EAAY,KAAK8B,CAAU,EAC3B9B,EAAY,WAAa8B,EAEzB9B,EAAY,MAAQ,CAAC,EACrBA,EAAY,MAAQ,CAAC,EAErB,IAAM+B,GAAS,OAAOD,GAAe,SAAWA,EAAa,IAC3D,KAAK,EACL,QAAQ,IAAK,GAAG,EAChB,MAAM,GAAG,EACT,OAAO,OAAO,EAEhB,QAAWE,KAAMD,EACZC,EAAG,CAAC,IAAM,IACbhC,EAAY,MAAM,KAAKgC,EAAG,MAAM,CAAC,CAAC,EAElChC,EAAY,MAAM,KAAKgC,CAAE,CAG5B,CApBSrB,EAAAR,EAAA,UA8BT,SAAS8B,EAAgBC,EAAQC,EAAU,CAC1C,IAAIC,EAAc,EACdC,EAAgB,EAChBC,EAAY,GACZC,EAAa,EAEjB,KAAOH,EAAcF,EAAO,QAC3B,GAAIG,EAAgBF,EAAS,SAAWA,EAASE,CAAa,IAAMH,EAAOE,CAAW,GAAKD,EAASE,CAAa,IAAM,KAElHF,EAASE,CAAa,IAAM,KAC/BC,EAAYD,EACZE,EAAaH,EACbC,MAEAD,IACAC,aAESC,IAAc,GAExBD,EAAgBC,EAAY,EAC5BC,IACAH,EAAcG,MAEd,OAAO,GAKT,KAAOF,EAAgBF,EAAS,QAAUA,EAASE,CAAa,IAAM,KACrEA,IAGD,OAAOA,IAAkBF,EAAS,MACnC,CAjCSxB,EAAAsB,EAAA,mBAyCT,SAAS/B,GAAU,CAClB,IAAM4B,EAAa,CAClB,GAAG9B,EAAY,MACf,GAAGA,EAAY,MAAM,IAAIQ,GAAa,IAAMA,CAAS,CACtD,EAAE,KAAK,GAAG,EACV,OAAAR,EAAY,OAAO,EAAE,EACd8B,CACR,CAPSnB,EAAAT,EAAA,WAgBT,SAASE,EAAQoC,EAAM,CACtB,QAAWC,KAAQzC,EAAY,MAC9B,GAAIiC,EAAgBO,EAAMC,CAAI,EAC7B,MAAO,GAIT,QAAWT,KAAMhC,EAAY,MAC5B,GAAIiC,EAAgBO,EAAMR,CAAE,EAC3B,MAAO,GAIT,MAAO,EACR,CAdSrB,EAAAP,EAAA,WAuBT,SAASH,EAAOwB,EAAK,CACpB,OAAIA,aAAe,MACXA,EAAI,OAASA,EAAI,QAElBA,CACR,CALSd,EAAAV,EAAA,UAWT,SAASI,GAAU,CAClB,QAAQ,KAAK,uIAAuI,CACrJ,CAFS,OAAAM,EAAAN,EAAA,WAITL,EAAY,OAAOA,EAAY,KAAK,CAAC,EAE9BA,CACR,CA3RSW,EAAAb,GAAA,SA6RTD,GAAO,QAAUC,KCnSjB,IAAA4C,GAAAC,GAAA,CAAAC,EAAAC,IAAA,cAMAD,EAAQ,WAAaE,GACrBF,EAAQ,KAAOG,GACfH,EAAQ,KAAOI,GACfJ,EAAQ,UAAYK,GACpBL,EAAQ,QAAUM,GAAa,EAC/BN,EAAQ,SAAW,IAAM,CACxB,IAAIO,EAAS,GAEb,MAAO,IAAM,CACPA,IACJA,EAAS,GACT,QAAQ,KAAK,uIAAuI,EAEtJ,CACD,GAAG,EAMHP,EAAQ,OAAS,CAChB,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,SACD,EAWA,SAASK,IAAY,CAIpB,GAAI,OAAO,OAAW,KAAe,OAAO,UAAY,OAAO,QAAQ,OAAS,YAAc,OAAO,QAAQ,QAC5G,MAAO,GAIR,GAAI,OAAO,UAAc,KAAe,UAAU,WAAa,UAAU,UAAU,YAAY,EAAE,MAAM,uBAAuB,EAC7H,MAAO,GAGR,IAAIG,EAKJ,OAAQ,OAAO,SAAa,KAAe,SAAS,iBAAmB,SAAS,gBAAgB,OAAS,SAAS,gBAAgB,MAAM,kBAEtI,OAAO,OAAW,KAAe,OAAO,UAAY,OAAO,QAAQ,SAAY,OAAO,QAAQ,WAAa,OAAO,QAAQ,QAG1H,OAAO,UAAc,KAAe,UAAU,YAAcA,EAAI,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,IAAM,SAASA,EAAE,CAAC,EAAG,EAAE,GAAK,IAEpJ,OAAO,UAAc,KAAe,UAAU,WAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB,CAC1H,CA1BSC,EAAAJ,GAAA,aAkCT,SAASH,GAAWQ,EAAM,CAQzB,GAPAA,EAAK,CAAC,GAAK,KAAK,UAAY,KAAO,IAClC,KAAK,WACJ,KAAK,UAAY,MAAQ,KAC1BA,EAAK,CAAC,GACL,KAAK,UAAY,MAAQ,KAC1B,IAAMT,EAAO,QAAQ,SAAS,KAAK,IAAI,EAEpC,CAAC,KAAK,UACT,OAGD,IAAMU,EAAI,UAAY,KAAK,MAC3BD,EAAK,OAAO,EAAG,EAAGC,EAAG,gBAAgB,EAKrC,IAAIC,EAAQ,EACRC,EAAQ,EACZH,EAAK,CAAC,EAAE,QAAQ,cAAeI,GAAS,CACnCA,IAAU,OAGdF,IACIE,IAAU,OAGbD,EAAQD,GAEV,CAAC,EAEDF,EAAK,OAAOG,EAAO,EAAGF,CAAC,CACxB,CAjCSF,EAAAP,GAAA,cA2CTF,EAAQ,IAAM,QAAQ,OAAS,QAAQ,MAAQ,IAAM,CAAC,GAQtD,SAASG,GAAKY,EAAY,CACzB,GAAI,CACCA,EACHf,EAAQ,QAAQ,QAAQ,QAASe,CAAU,EAE3Cf,EAAQ,QAAQ,WAAW,OAAO,CAEpC,MAAgB,CAGhB,CACD,CAXSS,EAAAN,GAAA,QAmBT,SAASC,IAAO,CACf,IAAIY,EACJ,GAAI,CACHA,EAAIhB,EAAQ,QAAQ,QAAQ,OAAO,CACpC,MAAgB,CAGhB,CAGA,MAAI,CAACgB,GAAK,OAAO,QAAY,KAAe,QAAS,UACpDA,EAAI,QAAQ,IAAI,OAGVA,CACR,CAfSP,EAAAL,GAAA,QA4BT,SAASE,IAAe,CACvB,GAAI,CAGH,OAAO,YACR,MAAgB,CAGhB,CACD,CATSG,EAAAH,GAAA,gBAWTL,EAAO,QAAU,KAAoBD,CAAO,EAE5C,GAAM,CAAC,WAAAiB,EAAU,EAAIhB,EAAO,QAM5BgB,GAAW,EAAI,SAAUC,EAAG,CAC3B,GAAI,CACH,OAAO,KAAK,UAAUA,CAAC,CACxB,OAASC,EAAO,CACf,MAAO,+BAAiCA,EAAM,OAC/C,CACD,IC/QA,IAAMC,GAAwB,OAAO,OACnC,CACE,KAAM,EACN,IAAK,EACL,MAAO,GACP,OAAQ,EACV,CACF,EACMC,EAA6B,OAAO,OAAO,CAC/C,OAAQ,EACR,MAAO,GACP,MAAO,EACT,CAAC,EACKC,GAAmB,OAAO,OAAO,CACrC,GAAGF,GACH,GAAGC,CACL,CAAC,EACKE,GAA2B,OAAO,OAAO,CAC7C,GAAGD,GACH,KAAM,GACN,OAAQ,EACV,CAAC,ECnBD,IAAME,GAAgC,OAAO,OAAO,CAClD,MAAO,KACP,OAAQ,IACV,CAAC,EACKC,GAA4B,OAAO,OAAO,CAE9C,GAAGD,GAEH,GAAGE,CACL,CAAC,ECVD,IAAMC,GAAeC,EAAA,CAACC,EAAOC,EAAUC,EAAiBC,EAAW,KAAO,CACxE,IAAMC,EAAiBJ,EAAM,MAAM,GAAG,EACtC,GAAIA,EAAM,MAAM,EAAG,CAAC,IAAM,IAAK,CAC7B,GAAII,EAAe,OAAS,GAAKA,EAAe,OAAS,EACvD,OAAO,KAETD,EAAWC,EAAe,MAAM,EAAE,MAAM,CAAC,CAC3C,CACA,GAAIA,EAAe,OAAS,GAAK,CAACA,EAAe,OAC/C,OAAO,KAET,GAAIA,EAAe,OAAS,EAAG,CAC7B,IAAMC,EAAQD,EAAe,IAAI,EAC3BE,EAASF,EAAe,IAAI,EAC5BG,EAAS,CAEb,SAAUH,EAAe,OAAS,EAAIA,EAAe,CAAC,EAAID,EAC1D,OAAAG,EACA,KAAMD,CACR,EACA,OAAOJ,GAAY,CAACO,EAAiBD,CAAM,EAAI,KAAOA,CACxD,CACA,IAAME,EAAOL,EAAe,CAAC,EACvBM,EAAgBD,EAAK,MAAM,GAAG,EACpC,GAAIC,EAAc,OAAS,EAAG,CAC5B,IAAMH,EAAS,CACb,SAAAJ,EACA,OAAQO,EAAc,MAAM,EAC5B,KAAMA,EAAc,KAAK,GAAG,CAC9B,EACA,OAAOT,GAAY,CAACO,EAAiBD,CAAM,EAAI,KAAOA,CACxD,CACA,GAAIL,GAAmBC,IAAa,GAAI,CACtC,IAAMI,EAAS,CACb,SAAAJ,EACA,OAAQ,GACR,KAAAM,CACF,EACA,OAAOR,GAAY,CAACO,EAAiBD,EAAQL,CAAe,EAAI,KAAOK,CACzE,CACA,OAAO,IACT,EAzCqB,gBA0CfC,EAAmBT,EAAA,CAACY,EAAMT,IACzBS,EAGE,CAAC,GAENT,GAAmBS,EAAK,SAAW,IAAQA,EAAK,SAAaA,EAAK,MAJ3D,GAFc,oBC3CzB,SAASC,GAAyBC,EAAMC,EAAM,CAC5C,IAAMC,EAAS,CAAC,EACZ,CAACF,EAAK,OAAU,CAACC,EAAK,QACxBC,EAAO,MAAQ,IAEb,CAACF,EAAK,OAAU,CAACC,EAAK,QACxBC,EAAO,MAAQ,IAEjB,IAAMC,IAAWH,EAAK,QAAU,IAAMC,EAAK,QAAU,IAAM,EAC3D,OAAIE,IACFD,EAAO,OAASC,GAEXD,CACT,CAbSE,EAAAL,GAAA,4BCGT,SAASM,GAAcC,EAAQC,EAAO,CACpC,IAAMC,EAASC,GAAyBH,EAAQC,CAAK,EACrD,QAAWG,KAAOC,GACZD,KAAOE,EACLF,KAAOJ,GAAU,EAAEI,KAAOF,KAC5BA,EAAOE,CAAG,EAAIE,EAA2BF,CAAG,GAErCA,KAAOH,EAChBC,EAAOE,CAAG,EAAIH,EAAMG,CAAG,EACdA,KAAOJ,IAChBE,EAAOE,CAAG,EAAIJ,EAAOI,CAAG,GAG5B,OAAOF,CACT,CAdSK,EAAAR,GAAA,iBCHT,SAASS,GAAaC,EAAMC,EAAO,CACjC,IAAMC,EAAQF,EAAK,MACbG,EAAUH,EAAK,SAA2B,OAAO,OAAO,IAAI,EAC5DI,EAA2B,OAAO,OAAO,IAAI,EACnD,SAASC,EAAQC,EAAM,CACrB,GAAIJ,EAAMI,CAAI,EACZ,OAAOF,EAASE,CAAI,EAAI,CAAC,EAE3B,GAAI,EAAEA,KAAQF,GAAW,CACvBA,EAASE,CAAI,EAAI,KACjB,IAAMC,EAASJ,EAAQG,CAAI,GAAKH,EAAQG,CAAI,EAAE,OACxCE,EAAQD,GAAUF,EAAQE,CAAM,EAClCC,IACFJ,EAASE,CAAI,EAAI,CAACC,CAAM,EAAE,OAAOC,CAAK,EAE1C,CACA,OAAOJ,EAASE,CAAI,CACtB,CAbS,OAAAG,EAAAJ,EAAA,YAcRJ,GAAS,OAAO,KAAKC,CAAK,EAAE,OAAO,OAAO,KAAKC,CAAO,CAAC,GAAG,QAAQE,CAAO,EACnED,CACT,CApBSK,EAAAV,GAAA,gBCKT,SAASW,GAAoBC,EAAMC,EAAMC,EAAM,CAC7C,IAAMC,EAAQH,EAAK,MACbI,EAAUJ,EAAK,SAA2B,OAAO,OAAO,IAAI,EAC9DK,EAAe,CAAC,EACpB,SAASC,EAAMC,EAAO,CACpBF,EAAeG,GACbL,EAAMI,CAAK,GAAKH,EAAQG,CAAK,EAC7BF,CACF,CACF,CALS,OAAAI,EAAAH,EAAA,SAMTA,EAAML,CAAI,EACVC,EAAK,QAAQI,CAAK,EACXE,GAAcR,EAAMK,CAAY,CACzC,CAbSI,EAAAV,GAAA,uBAcT,SAASW,GAAYV,EAAMC,EAAM,CAC/B,GAAID,EAAK,MAAMC,CAAI,EACjB,OAAOF,GAAoBC,EAAMC,EAAM,CAAC,CAAC,EAE3C,IAAMC,EAAOS,GAAaX,EAAM,CAACC,CAAI,CAAC,EAAEA,CAAI,EAC5C,OAAOC,EAAOH,GAAoBC,EAAMC,EAAMC,CAAI,EAAI,IACxD,CANSO,EAAAC,GAAA,eCnBT,IAAME,GAAa,4BACbC,GAAY,4BAClB,SAASC,GAAcC,EAAMC,EAAOC,EAAW,CAC7C,GAAID,IAAU,EACZ,OAAOD,EAGT,GADAE,EAAYA,GAAa,IACrB,OAAOF,GAAS,SAClB,OAAO,KAAK,KAAKA,EAAOC,EAAQC,CAAS,EAAIA,EAE/C,GAAI,OAAOF,GAAS,SAClB,OAAOA,EAET,IAAMG,EAAWH,EAAK,MAAMH,EAAU,EACtC,GAAIM,IAAa,MAAQ,CAACA,EAAS,OACjC,OAAOH,EAET,IAAMI,EAAW,CAAC,EACdC,EAAOF,EAAS,MAAM,EACtBG,EAAWR,GAAU,KAAKO,CAAI,EAClC,OAAa,CACX,GAAIC,EAAU,CACZ,IAAMC,EAAM,WAAWF,CAAI,EACvB,MAAME,CAAG,EACXH,EAAS,KAAKC,CAAI,EAElBD,EAAS,KAAK,KAAK,KAAKG,EAAMN,EAAQC,CAAS,EAAIA,CAAS,CAEhE,MACEE,EAAS,KAAKC,CAAI,EAGpB,GADAA,EAAOF,EAAS,MAAM,EAClBE,IAAS,OACX,OAAOD,EAAS,KAAK,EAAE,EAEzBE,EAAW,CAACA,CACd,CACF,CAnCSE,EAAAT,GAAA,iBCFT,SAASU,GAAaC,EAASC,EAAM,OAAQ,CAC3C,IAAIC,EAAO,GACLC,EAAQH,EAAQ,QAAQ,IAAMC,CAAG,EACvC,KAAOE,GAAS,GAAG,CACjB,IAAMC,EAAQJ,EAAQ,QAAQ,IAAKG,CAAK,EAClCE,EAAML,EAAQ,QAAQ,KAAOC,CAAG,EACtC,GAAIG,IAAU,IAAMC,IAAQ,GAC1B,MAEF,IAAMC,EAASN,EAAQ,QAAQ,IAAKK,CAAG,EACvC,GAAIC,IAAW,GACb,MAEFJ,GAAQF,EAAQ,MAAMI,EAAQ,EAAGC,CAAG,EAAE,KAAK,EAC3CL,EAAUA,EAAQ,MAAM,EAAGG,CAAK,EAAE,KAAK,EAAIH,EAAQ,MAAMM,EAAS,CAAC,CACrE,CACA,MAAO,CACL,KAAAJ,EACA,QAAAF,CACF,CACF,CApBSO,EAAAR,GAAA,gBAqBT,SAASS,GAAoBN,EAAMF,EAAS,CAC1C,OAAOE,EAAO,SAAWA,EAAO,UAAYF,EAAUA,CACxD,CAFSO,EAAAC,GAAA,uBAGT,SAASC,GAAeC,EAAMN,EAAOC,EAAK,CACxC,IAAMM,EAAQZ,GAAaW,CAAI,EAC/B,OAAOF,GAAoBG,EAAM,KAAMP,EAAQO,EAAM,QAAUN,CAAG,CACpE,CAHSE,EAAAE,GAAA,kBCnBT,IAAMG,GAAiBC,EAACC,GAAUA,IAAU,SAAWA,IAAU,aAAeA,IAAU,OAAnE,kBACvB,SAASC,GAAUC,EAAMC,EAAgB,CACvC,IAAMC,EAAW,CACf,GAAGC,GACH,GAAGH,CACL,EACMI,EAAqB,CACzB,GAAGC,GACH,GAAGJ,CACL,EACMK,EAAM,CACV,KAAMJ,EAAS,KACf,IAAKA,EAAS,IACd,MAAOA,EAAS,MAChB,OAAQA,EAAS,MACnB,EACIK,EAAOL,EAAS,KACpB,CAACA,EAAUE,CAAkB,EAAE,QAASI,GAAU,CAChD,IAAMC,EAAkB,CAAC,EACnBC,EAAQF,EAAM,MACdG,EAAQH,EAAM,MAChBI,EAAWJ,EAAM,OACjBE,EACEC,EACFC,GAAY,GAEZH,EAAgB,KACd,cAAgBH,EAAI,MAAQA,EAAI,MAAM,SAAS,EAAI,KAAO,EAAIA,EAAI,KAAK,SAAS,EAAI,GACtF,EACAG,EAAgB,KAAK,aAAa,EAClCH,EAAI,IAAMA,EAAI,KAAO,GAEdK,IACTF,EAAgB,KACd,cAAgB,EAAIH,EAAI,MAAM,SAAS,EAAI,KAAOA,EAAI,OAASA,EAAI,KAAK,SAAS,EAAI,GACvF,EACAG,EAAgB,KAAK,aAAa,EAClCH,EAAI,IAAMA,EAAI,KAAO,GAEvB,IAAIO,EAKJ,OAJID,EAAW,IACbA,GAAY,KAAK,MAAMA,EAAW,CAAC,EAAI,GAEzCA,EAAWA,EAAW,EACdA,EAAU,CAChB,IAAK,GACHC,EAAYP,EAAI,OAAS,EAAIA,EAAI,IACjCG,EAAgB,QACd,aAAeI,EAAU,SAAS,EAAI,IAAMA,EAAU,SAAS,EAAI,GACrE,EACA,MACF,IAAK,GACHJ,EAAgB,QACd,eAAiBH,EAAI,MAAQ,EAAIA,EAAI,MAAM,SAAS,EAAI,KAAOA,EAAI,OAAS,EAAIA,EAAI,KAAK,SAAS,EAAI,GACxG,EACA,MACF,IAAK,GACHO,EAAYP,EAAI,MAAQ,EAAIA,EAAI,KAChCG,EAAgB,QACd,cAAgBI,EAAU,SAAS,EAAI,IAAMA,EAAU,SAAS,EAAI,GACtE,EACA,KACJ,CACID,EAAW,IAAM,IACfN,EAAI,OAASA,EAAI,MACnBO,EAAYP,EAAI,KAChBA,EAAI,KAAOA,EAAI,IACfA,EAAI,IAAMO,GAERP,EAAI,QAAUA,EAAI,SACpBO,EAAYP,EAAI,MAChBA,EAAI,MAAQA,EAAI,OAChBA,EAAI,OAASO,IAGbJ,EAAgB,SAClBF,EAAOO,GACLP,EACA,iBAAmBE,EAAgB,KAAK,GAAG,EAAI,KAC/C,MACF,EAEJ,CAAC,EACD,IAAMM,EAAsBX,EAAmB,MACzCY,EAAuBZ,EAAmB,OAC1Ca,EAAWX,EAAI,MACfY,EAAYZ,EAAI,OAClBa,EACAC,EACAL,IAAwB,MAC1BK,EAASJ,IAAyB,KAAO,MAAQA,IAAyB,OAASE,EAAYF,EAC/FG,EAAQE,GAAcD,EAAQH,EAAWC,CAAS,IAElDC,EAAQJ,IAAwB,OAASE,EAAWF,EACpDK,EAASJ,IAAyB,KAAOK,GAAcF,EAAOD,EAAYD,CAAQ,EAAID,IAAyB,OAASE,EAAYF,GAEtI,IAAMM,EAAa,CAAC,EACdC,EAAU1B,EAAA,CAAC2B,EAAM1B,IAAU,CAC1BF,GAAeE,CAAK,IACvBwB,EAAWE,CAAI,EAAI1B,EAAM,SAAS,EAEtC,EAJgB,WAKhByB,EAAQ,QAASJ,CAAK,EACtBI,EAAQ,SAAUH,CAAM,EACxB,IAAMK,EAAU,CAACnB,EAAI,KAAMA,EAAI,IAAKW,EAAUC,CAAS,EACvD,OAAAI,EAAW,QAAUG,EAAQ,KAAK,GAAG,EAC9B,CACL,WAAAH,EACA,QAAAG,EACA,KAAAlB,CACF,CACF,CA9GSV,EAAAE,GAAA,aCNT,IAAM2B,GAAQ,gBACRC,GAAe,YAAc,KAAK,IAAI,EAAE,SAAS,EAAE,GAAK,KAAK,OAAO,EAAI,SAAW,GAAG,SAAS,EAAE,EACnGC,GAAU,EACd,SAASC,GAAWC,EAAMC,EAASJ,GAAc,CAC/C,IAAMK,EAAM,CAAC,EACTC,EACJ,KAAOA,EAAQP,GAAM,KAAKI,CAAI,GAC5BE,EAAI,KAAKC,EAAM,CAAC,CAAC,EAEnB,GAAI,CAACD,EAAI,OACP,OAAOF,EAET,IAAMI,EAAS,UAAY,KAAK,OAAO,EAAI,SAAW,KAAK,IAAI,GAAG,SAAS,EAAE,EAC7E,OAAAF,EAAI,QAASG,GAAO,CAClB,IAAMC,EAAQ,OAAOL,GAAW,WAAaA,EAAOI,CAAE,EAAIJ,GAAUH,MAAW,SAAS,EAClFS,EAAYF,EAAG,QAAQ,sBAAuB,MAAM,EAC1DL,EAAOA,EAAK,QAGV,IAAI,OAAO,WAAaO,EAAY,mBAAoB,GAAG,EAC3D,KAAOD,EAAQF,EAAS,IAC1B,CACF,CAAC,EACDJ,EAAOA,EAAK,QAAQ,IAAI,OAAOI,EAAQ,GAAG,EAAG,EAAE,EACxCJ,CACT,CAtBSQ,EAAAT,GAAA,cCHT,SAASU,GAAWC,EAAMC,EAAY,CACpC,IAAIC,EAAoBF,EAAK,QAAQ,QAAQ,IAAM,GAAK,GAAK,8CAC7D,QAAWG,KAAQF,EACjBC,GAAqB,IAAMC,EAAO,KAAOF,EAAWE,CAAI,EAAI,IAE9D,MAAO,0CAA4CD,EAAoB,IAAMF,EAAO,QACtF,CANSI,EAAAL,GAAA,cCqDT,IAAAM,GAAO,WCpCA,IAAMC,GAA2B,CACtC,KAAM,mOACN,OAAQ,GACR,MAAO,EACT,EAEMC,GAAa,IAAI,IACjBC,GAAc,IAAI,IAEXC,GAAoBC,EAACC,GAA8B,CAC9D,QAAWC,KAAcD,EAAa,CACpC,GAAI,CAACC,EAAW,KACd,MAAM,IAAI,MACR,+EACF,EAGF,GADAC,EAAI,MAAM,yBAA0BD,EAAW,IAAI,EAC/C,WAAYA,EACdJ,GAAY,IAAII,EAAW,KAAMA,EAAW,MAAM,UACzC,UAAWA,EACpBL,GAAW,IAAIK,EAAW,KAAMA,EAAW,KAAK,MAEhD,OAAAC,EAAI,MAAM,uBAAwBD,CAAU,EACtC,IAAI,MAAM,qEAAqE,CAEzF,CACF,EAjBiC,qBAmB3BE,GAAwBJ,EAAA,MAAOK,EAAkBC,IAA4B,CACjF,IAAMC,EAAOC,GAAaH,EAAU,GAAMC,IAAmB,MAAS,EACtE,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,sBAAsBF,CAAQ,EAAE,EAElD,IAAMI,EAASF,EAAK,QAAUD,EAC9B,GAAI,CAACG,EACH,MAAM,IAAI,MAAM,oCAAoCJ,CAAQ,EAAE,EAEhE,IAAIK,EAAQb,GAAW,IAAIY,CAAM,EACjC,GAAI,CAACC,EAAO,CACV,IAAMC,EAASb,GAAY,IAAIW,CAAM,EACrC,GAAI,CAACE,EACH,MAAM,IAAI,MAAM,uBAAuBJ,EAAK,MAAM,EAAE,EAEtD,GAAI,CAEFG,EAAQ,CAAE,GADK,MAAMC,EAAO,EACP,OAAAF,CAAO,EAC5BZ,GAAW,IAAIY,EAAQC,CAAK,CAC9B,OAASE,EAAG,CACV,MAAAT,EAAI,MAAMS,CAAC,EACL,IAAI,MAAM,4BAA4BL,EAAK,MAAM,EAAE,CAC3D,CACF,CACA,IAAMM,EAAWC,GAAYJ,EAAOH,EAAK,IAAI,EAC7C,GAAI,CAACM,EACH,MAAM,IAAI,MAAM,mBAAmBR,CAAQ,EAAE,EAE/C,OAAOQ,CACT,EA7B8B,yBA+BjBE,GAAkBf,EAAA,MAAOK,GAAqB,CACzD,GAAI,CACF,aAAMD,GAAsBC,CAAQ,EAC7B,EACT,MAAQ,CACN,MAAO,EACT,CACF,EAP+B,mBASlBW,GAAahB,EAAA,MACxBK,EACAY,EACAC,IACG,CACH,IAAIL,EACJ,GAAI,CACFA,EAAW,MAAMT,GAAsBC,EAAUY,GAAgB,cAAc,CACjF,OAASL,EAAG,CACVT,EAAI,MAAMS,CAAC,EACXC,EAAWjB,EACb,CACA,IAAMuB,EAAaC,GAAUP,EAAUI,CAAc,EAKrD,OAJYI,GAAWC,GAAWH,EAAW,IAAI,EAAG,CAClD,GAAGA,EAAW,WACd,GAAGD,CACL,CAAC,CAEH,EAlB0B,cCrFpB,SAAUK,GACdC,EAAoC,SACpCC,EAAA,CAAA,EAAAC,EAAA,EAAAA,EAAA,UAAA,OAAAA,IAAAD,EAAAC,EAAA,CAAA,EAAA,UAAAA,CAAA,EAEA,IAAIC,EAAU,MAAM,KAAK,OAAOH,GAAU,SAAW,CAACA,CAAK,EAAIA,CAAK,EAGpEG,EAAQA,EAAQ,OAAS,CAAC,EAAIA,EAAQA,EAAQ,OAAS,CAAC,EAAE,QACxD,iBACA,EAAE,EAIJ,IAAMC,EAAgBD,EAAQ,OAAO,SAACE,EAAKC,EAAG,CAC5C,IAAMC,EAAUD,EAAI,MAAM,qBAAqB,EAC/C,OAAIC,EACKF,EAAI,OACTE,EAAQ,IAAI,SAACC,EAAK,CAAA,IAAAC,EAAAC,EAAK,OAAAA,GAAAD,EAAAD,EAAM,MAAM,QAAQ,KAAC,MAAAC,IAAA,OAAA,OAAAA,EAAE,UAAM,MAAAC,IAAA,OAAAA,EAAI,CAAC,CAAA,CAAC,EAGvDL,CACT,EAAa,CAAA,CAAE,EAGf,GAAID,EAAc,OAAQ,CACxB,IAAMO,EAAU,IAAI,OAAO;OAAW,KAAK,IAAG,MAAR,KAAYP,CAAa,EAAA,IAAM,GAAG,EAExED,EAAUA,EAAQ,IAAI,SAACG,EAAG,CAAK,OAAAA,EAAI,QAAQK,EAAS;CAAI,CAAzB,CAA0B,EAI3DR,EAAQ,CAAC,EAAIA,EAAQ,CAAC,EAAE,QAAQ,SAAU,EAAE,EAG5C,IAAIS,EAAST,EAAQ,CAAC,EAEtB,OAAAF,EAAO,QAAQ,SAACY,EAAOC,EAAC,CAEtB,IAAMC,EAAeH,EAAO,MAAM,eAAe,EAC3CI,EAAcD,EAAeA,EAAa,CAAC,EAAI,GACjDE,EAAgBJ,EAEhB,OAAOA,GAAU,UAAYA,EAAM,SAAS;CAAI,IAClDI,EAAgB,OAAOJ,CAAK,EACzB,MAAM;CAAI,EACV,IAAI,SAACP,EAAKQ,EAAC,CACV,OAAOA,IAAM,EAAIR,EAAM,GAAGU,EAAcV,CAC1C,CAAC,EACA,KAAK;CAAI,GAGdM,GAAUK,EAAgBd,EAAQW,EAAI,CAAC,CACzC,CAAC,EAEMF,CACT,CAvDgBM,EAAAnB,GAAA,UCGT,SAASoB,IAAe,CAC3B,MAAO,CACH,MAAO,GACP,OAAQ,GACR,WAAY,KACZ,IAAK,GACL,MAAO,KACP,SAAU,GACV,SAAU,KACV,OAAQ,GACR,UAAW,KACX,WAAY,IACpB,CACA,CAbgBC,EAAAD,GAAA,gBAcN,IAACE,EAAYF,GAAY,EAC5B,SAASG,GAAeC,EAAa,CACxCF,EAAYE,CAChB,CAFgBH,EAAAE,GAAA,kBClBhB,IAAME,EAAW,CAAE,KAAMJ,EAAA,IAAM,KAAN,OAAU,EACnC,SAASK,EAAKC,EAAOC,EAAM,GAAI,CAC3B,IAAIC,EAAS,OAAOF,GAAU,SAAWA,EAAQA,EAAM,OACjDG,EAAM,CACR,QAAST,EAAA,CAACU,EAAMC,IAAQ,CACpB,IAAIC,EAAY,OAAOD,GAAQ,SAAWA,EAAMA,EAAI,OACpD,OAAAC,EAAYA,EAAU,QAAQC,EAAM,MAAO,IAAI,EAC/CL,EAASA,EAAO,QAAQE,EAAME,CAAS,EAChCH,CACnB,EALiB,WAMT,SAAUT,EAAA,IACC,IAAI,OAAOQ,EAAQD,CAAG,EADvB,WAGlB,EACI,OAAOE,CACX,CAdST,EAAAK,EAAA,QAeF,IAAMQ,EAAQ,CACjB,iBAAkB,yBAClB,kBAAmB,cACnB,uBAAwB,gBACxB,eAAgB,OAChB,WAAY,KACZ,kBAAmB,KACnB,gBAAiB,KACjB,aAAc,OACd,kBAAmB,MACnB,cAAe,MACf,oBAAqB,OACrB,UAAW,WACX,gBAAiB,oBACjB,gBAAiB,WACjB,wBAAyB,iCACzB,yBAA0B,mBAC1B,gBAAiB,OACjB,mBAAoB,0BACpB,WAAY,cACZ,gBAAiB,eACjB,QAAS,SACT,aAAc,WACd,eAAgB,OAChB,gBAAiB,aACjB,kBAAmB,YACnB,gBAAiB,YACjB,iBAAkB,aAClB,eAAgB,YAChB,UAAW,QACX,QAAS,UACT,kBAAmB,iCACnB,gBAAiB,mCACjB,kBAAmB,KACnB,gBAAiB,KACjB,kBAAmB,gCACnB,oBAAqB,gBACrB,WAAY,UACZ,cAAe,WACf,mBAAoB,oDACpB,sBAAuB,qDACvB,aAAc,6CACd,MAAO,eACP,cAAe,OACf,SAAU,MACV,UAAW,MACX,UAAW,QACX,eAAgB,WAChB,UAAW,SACX,cAAe,OACf,cAAe,MACf,cAAeb,EAACc,GAAS,IAAI,OAAO,WAAWA,CAAI,8BAA+B,EAAnE,iBACf,gBAAiBd,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,oDAAqD,EAA3G,mBACjB,QAASf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,oDAAoD,EAA1G,WACT,iBAAkBf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,iBAAiB,EAAvE,oBAClB,kBAAmBf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,IAAI,EAA1D,qBACnB,eAAgBf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,qBAAsB,GAAG,EAA/E,iBACpB,EAIMC,GAAU,uBACVC,GAAY,wDACZC,GAAS,8GACTC,EAAK,qEACLC,GAAU,uCACVC,GAAS,wBACTC,GAAe,iKACfC,GAAWlB,EAAKiB,EAAY,EAC7B,QAAQ,QAASD,EAAM,EACvB,QAAQ,aAAc,mBAAmB,EACzC,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,cAAe,SAAS,EAChC,QAAQ,WAAY,cAAc,EAClC,QAAQ,QAAS,mBAAmB,EACpC,QAAQ,WAAY,EAAE,EACtB,SAAQ,EACPG,GAAcnB,EAAKiB,EAAY,EAChC,QAAQ,QAASD,EAAM,EACvB,QAAQ,aAAc,mBAAmB,EACzC,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,cAAe,SAAS,EAChC,QAAQ,WAAY,cAAc,EAClC,QAAQ,QAAS,mBAAmB,EACpC,QAAQ,SAAU,mCAAmC,EACrD,SAAQ,EACPI,GAAa,uFACbC,GAAY,UACZC,GAAc,8BACdC,GAAMvB,EAAK,6GAA6G,EACzH,QAAQ,QAASsB,EAAW,EAC5B,QAAQ,QAAS,8DAA8D,EAC/E,SAAQ,EACPE,GAAOxB,EAAK,sCAAsC,EACnD,QAAQ,QAASgB,EAAM,EACvB,SAAQ,EACPS,GAAO,gWAMPC,GAAW,gCACXC,GAAO3B,EAAK,4dASP,GAAG,EACT,QAAQ,UAAW0B,EAAQ,EAC3B,QAAQ,MAAOD,EAAI,EACnB,QAAQ,YAAa,0EAA0E,EAC/F,SAAQ,EACPG,GAAY5B,EAAKoB,EAAU,EAC5B,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOW,EAAI,EACnB,SAAQ,EACPI,GAAa7B,EAAK,yCAAyC,EAC5D,QAAQ,YAAa4B,EAAS,EAC9B,SAAQ,EAIPE,GAAc,CAChB,WAAAD,GACA,KAAMjB,GACN,IAAAW,GACA,OAAAV,GACA,QAAAE,GACA,GAAAD,EACA,KAAAa,GACA,SAAAT,GACA,KAAAM,GACA,QAAAb,GACA,UAAAiB,GACA,MAAO7B,EACP,KAAMsB,EACV,EAIMU,GAAW/B,EAAK,6JAEsE,EACvF,QAAQ,KAAMc,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,aAAc,SAAS,EAC/B,QAAQ,OAAQ,wBAAyB,EACzC,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOW,EAAI,EACnB,SAAQ,EACPO,GAAW,CACb,GAAGF,GACH,SAAUX,GACV,MAAOY,GACP,UAAW/B,EAAKoB,EAAU,EACrB,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,QAASiB,EAAQ,EACzB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAON,EAAI,EACnB,SAAQ,CACjB,EAIMQ,GAAgB,CAClB,GAAGH,GACH,KAAM9B,EAAK,wIAEiE,EACvE,QAAQ,UAAW0B,EAAQ,EAC3B,QAAQ,OAAQ,mKAGgB,EAChC,SAAQ,EACb,IAAK,oEACL,QAAS,yBACT,OAAQ3B,EACR,SAAU,mCACV,UAAWC,EAAKoB,EAAU,EACrB,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW;EAAiB,EACpC,QAAQ,WAAYI,EAAQ,EAC5B,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,UAAW,EAAE,EACrB,QAAQ,QAAS,EAAE,EACnB,QAAQ,QAAS,EAAE,EACnB,QAAQ,OAAQ,EAAE,EAClB,SAAQ,CACjB,EAIMgB,GAAS,8CACTC,GAAa,sCACbC,GAAK,wBACLC,GAAa,8EAEbC,GAAe,gBACfC,GAAsB,kBACtBC,GAAyB,mBACzBC,GAAczC,EAAK,wBAAyB,GAAG,EAChD,QAAQ,cAAeuC,EAAmB,EAAE,SAAQ,EAEnDG,GAA0B,qBAC1BC,GAAiC,uBACjCC,GAAoC,yBAEpCC,GAAY,gFACZC,GAAqB,gEACrBC,GAAiB/C,EAAK8C,GAAoB,GAAG,EAC9C,QAAQ,SAAUR,EAAY,EAC9B,SAAQ,EACPU,GAAoBhD,EAAK8C,GAAoB,GAAG,EACjD,QAAQ,SAAUJ,EAAuB,EACzC,SAAQ,EACPO,GAAwB,wQAQxBC,GAAoBlD,EAAKiD,GAAuB,IAAI,EACrD,QAAQ,iBAAkBT,EAAsB,EAChD,QAAQ,cAAeD,EAAmB,EAC1C,QAAQ,SAAUD,EAAY,EAC9B,SAAQ,EACPa,GAAuBnD,EAAKiD,GAAuB,IAAI,EACxD,QAAQ,iBAAkBL,EAAiC,EAC3D,QAAQ,cAAeD,EAA8B,EACrD,QAAQ,SAAUD,EAAuB,EACzC,SAAQ,EAEPU,GAAoBpD,EAAK,mNAMQ,IAAI,EACtC,QAAQ,iBAAkBwC,EAAsB,EAChD,QAAQ,cAAeD,EAAmB,EAC1C,QAAQ,SAAUD,EAAY,EAC9B,SAAQ,EACPe,GAAiBrD,EAAK,YAAa,IAAI,EACxC,QAAQ,SAAUsC,EAAY,EAC9B,SAAQ,EACPgB,GAAWtD,EAAK,qCAAqC,EACtD,QAAQ,SAAU,8BAA8B,EAChD,QAAQ,QAAS,8IAA8I,EAC/J,SAAQ,EACPuD,GAAiBvD,EAAK0B,EAAQ,EAAE,QAAQ,YAAa,KAAK,EAAE,SAAQ,EACpE8B,GAAMxD,EAAK,0JAKuB,EACnC,QAAQ,UAAWuD,EAAc,EACjC,QAAQ,YAAa,6EAA6E,EAClG,SAAQ,EACPE,EAAe,sDACfC,GAAO1D,EAAK,+CAA+C,EAC5D,QAAQ,QAASyD,CAAY,EAC7B,QAAQ,OAAQ,sCAAsC,EACtD,QAAQ,QAAS,6DAA6D,EAC9E,SAAQ,EACPE,GAAU3D,EAAK,yBAAyB,EACzC,QAAQ,QAASyD,CAAY,EAC7B,QAAQ,MAAOnC,EAAW,EAC1B,SAAQ,EACPsC,GAAS5D,EAAK,uBAAuB,EACtC,QAAQ,MAAOsB,EAAW,EAC1B,SAAQ,EACPuC,GAAgB7D,EAAK,wBAAyB,GAAG,EAClD,QAAQ,UAAW2D,EAAO,EAC1B,QAAQ,SAAUC,EAAM,EACxB,SAAQ,EAIPE,GAAe,CACjB,WAAY/D,EACZ,eAAAsD,GACA,SAAAC,GACA,UAAAT,GACA,GAAAT,GACA,KAAMD,GACN,IAAKpC,EACL,eAAAgD,GACA,kBAAAG,GACA,kBAAAE,GACJ,OAAIlB,GACA,KAAAwB,GACA,OAAAE,GACA,YAAAnB,GACA,QAAAkB,GACA,cAAAE,GACA,IAAAL,GACA,KAAMnB,GACN,IAAKtC,CACT,EAIMgE,GAAiB,CACnB,GAAGD,GACH,KAAM9D,EAAK,yBAAyB,EAC/B,QAAQ,QAASyD,CAAY,EAC7B,SAAQ,EACb,QAASzD,EAAK,+BAA+B,EACxC,QAAQ,QAASyD,CAAY,EAC7B,SAAQ,CACjB,EAIMO,GAAY,CACd,GAAGF,GACH,kBAAmBX,GACnB,eAAgBH,GAChB,IAAKhD,EAAK,mEAAoE,GAAG,EAC5E,QAAQ,QAAS,2EAA2E,EAC5F,SAAQ,EACb,WAAY,6EACZ,IAAK,gEACL,KAAM,4NACV,EAIMiE,GAAe,CACjB,GAAGD,GACH,GAAIhE,EAAKoC,EAAE,EAAE,QAAQ,OAAQ,GAAG,EAAE,SAAQ,EAC1C,KAAMpC,EAAKgE,GAAU,IAAI,EACpB,QAAQ,OAAQ,eAAe,EAC/B,QAAQ,UAAW,GAAG,EACtB,SAAQ,CACjB,EAIaE,EAAQ,CACjB,OAAQpC,GACR,IAAKE,GACL,SAAUC,EACd,EACakC,EAAS,CAClB,OAAQL,GACR,IAAKE,GACL,OAAQC,GACR,SAAUF,EACd,EClYMK,GAAqB,CACvB,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,OACT,EACMC,GAAuB1E,EAAC2E,GAAOF,GAAmBE,CAAE,EAA7B,wBACtB,SAASpC,EAAOP,EAAM4C,EAAQ,CACjC,GAAIA,GACA,GAAI/D,EAAM,WAAW,KAAKmB,CAAI,EAC1B,OAAOA,EAAK,QAAQnB,EAAM,cAAe6D,EAAoB,UAI7D7D,EAAM,mBAAmB,KAAKmB,CAAI,EAClC,OAAOA,EAAK,QAAQnB,EAAM,sBAAuB6D,EAAoB,EAG7E,OAAO1C,CACX,CAZgBhC,EAAAuC,EAAA,UA2BT,SAASsC,GAASC,EAAM,CAC3B,GAAI,CACAA,EAAO,UAAUA,CAAI,EAAE,QAAQjE,EAAM,cAAe,GAAG,CAC/D,MACU,CACF,OAAO,IACf,CACI,OAAOiE,CACX,CARgB9E,EAAA6E,GAAA,YAST,SAASE,GAAWC,EAAUC,EAAO,CAGxC,IAAMC,EAAMF,EAAS,QAAQnE,EAAM,SAAU,CAACsE,EAAOC,EAAQC,IAAQ,CACjE,IAAIC,EAAU,GACVC,EAAOH,EACX,KAAO,EAAEG,GAAQ,GAAKF,EAAIE,CAAI,IAAM,MAChCD,EAAU,CAACA,EACf,OAAIA,EAGO,IAIA,IAEnB,CAAK,EAAGE,EAAQN,EAAI,MAAMrE,EAAM,SAAS,EACjC4E,EAAI,EAQR,GANKD,EAAM,CAAC,EAAE,KAAI,GACdA,EAAM,MAAK,EAEXA,EAAM,OAAS,GAAK,CAACA,EAAM,GAAG,EAAE,GAAG,KAAI,GACvCA,EAAM,IAAG,EAETP,EACA,GAAIO,EAAM,OAASP,EACfO,EAAM,OAAOP,CAAK,MAGlB,MAAOO,EAAM,OAASP,GAClBO,EAAM,KAAK,EAAE,EAGzB,KAAOC,EAAID,EAAM,OAAQC,IAErBD,EAAMC,CAAC,EAAID,EAAMC,CAAC,EAAE,KAAI,EAAG,QAAQ5E,EAAM,UAAW,GAAG,EAE3D,OAAO2E,CACX,CAxCgBxF,EAAA+E,GAAA,cAiDT,SAASW,EAAML,EAAKM,EAAGC,EAAQ,CAClC,IAAMC,EAAIR,EAAI,OACd,GAAIQ,IAAM,EACN,MAAO,GAGX,IAAIC,EAAU,EAEd,KAAOA,EAAUD,GACIR,EAAI,OAAOQ,EAAIC,EAAU,CAAC,IAC1BH,GACbG,IASR,OAAOT,EAAI,MAAM,EAAGQ,EAAIC,CAAO,CACnC,CArBgB9F,EAAA0F,EAAA,SAsBT,SAASK,GAAmBV,EAAKW,EAAG,CACvC,GAAIX,EAAI,QAAQW,EAAE,CAAC,CAAC,IAAM,GACtB,MAAO,GAEX,IAAIC,EAAQ,EACZ,QAASR,EAAI,EAAGA,EAAIJ,EAAI,OAAQI,IAC5B,GAAIJ,EAAII,CAAC,IAAM,KACXA,YAEKJ,EAAII,CAAC,IAAMO,EAAE,CAAC,EACnBC,YAEKZ,EAAII,CAAC,IAAMO,EAAE,CAAC,IACnBC,IACIA,EAAQ,GACR,OAAOR,EAInB,MAAO,EACX,CApBgBzF,EAAA+F,GAAA,sBCrHhB,SAASG,GAAWC,EAAKpC,EAAMqC,EAAKC,EAAOC,EAAO,CAC9C,IAAMxB,EAAOf,EAAK,KACZwC,EAAQxC,EAAK,OAAS,KACtByC,EAAOL,EAAI,CAAC,EAAE,QAAQG,EAAM,MAAM,kBAAmB,IAAI,EAC/D,GAAIH,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAK,CAC1BE,EAAM,MAAM,OAAS,GACrB,IAAMI,EAAQ,CACV,KAAM,OACN,IAAAL,EACA,KAAAtB,EACA,MAAAyB,EACA,KAAAC,EACA,OAAQH,EAAM,aAAaG,CAAI,CAC3C,EACQ,OAAAH,EAAM,MAAM,OAAS,GACdI,CACf,CACI,MAAO,CACH,KAAM,QACN,IAAAL,EACA,KAAAtB,EACA,MAAAyB,EACA,KAAAC,CACR,CACA,CAxBSxG,EAAAkG,GAAA,cAyBT,SAASQ,GAAuBN,EAAKI,EAAMF,EAAO,CAC9C,IAAMK,EAAoBP,EAAI,MAAME,EAAM,MAAM,sBAAsB,EACtE,GAAIK,IAAsB,KACtB,OAAOH,EAEX,IAAMI,EAAeD,EAAkB,CAAC,EACxC,OAAOH,EACF,MAAM;CAAI,EACV,IAAIK,GAAQ,CACb,IAAMC,EAAoBD,EAAK,MAAMP,EAAM,MAAM,cAAc,EAC/D,GAAIQ,IAAsB,KACtB,OAAOD,EAEX,GAAM,CAACE,CAAY,EAAID,EACvB,OAAIC,EAAa,QAAUH,EAAa,OAC7BC,EAAK,MAAMD,EAAa,MAAM,EAElCC,CACf,CAAK,EACI,KAAK;CAAI,CAClB,CApBS7G,EAAA0G,GAAA,0BAwBF,IAAMM,EAAN,KAAiB,OAAA,CAAAhH,EAAA,mBACpB,QACA,MACA,MACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,CAClC,CACI,MAAMiH,EAAK,CACP,IAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,GAAOA,EAAI,CAAC,EAAE,OAAS,EACvB,MAAO,CACH,KAAM,QACN,IAAKA,EAAI,CAAC,CAC1B,CAEA,CACI,KAAKe,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAAK,CACL,IAAMK,EAAOL,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAkB,EAAE,EACjE,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,eAAgB,WAChB,KAAO,KAAK,QAAQ,SAEdK,EADAd,EAAMc,EAAM;CAAI,CAEtC,CACA,CACA,CACI,OAAOU,EAAK,CACR,IAAMf,EAAM,KAAK,MAAM,MAAM,OAAO,KAAKe,CAAG,EAC5C,GAAIf,EAAK,CACL,IAAMC,EAAMD,EAAI,CAAC,EACXK,EAAOE,GAAuBN,EAAKD,EAAI,CAAC,GAAK,GAAI,KAAK,KAAK,EACjE,MAAO,CACH,KAAM,OACN,IAAAC,EACA,KAAMD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,KAAI,EAAG,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACpF,KAAAK,CAChB,CACA,CACA,CACI,QAAQU,EAAK,CACT,IAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,KAAI,EAEtB,GAAI,KAAK,MAAM,MAAM,WAAW,KAAKK,CAAI,EAAG,CACxC,IAAMW,EAAUzB,EAAMc,EAAM,GAAG,GAC3B,KAAK,QAAQ,UAGR,CAACW,GAAW,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAO,KAE9DX,EAAOW,EAAQ,KAAI,EAEvC,CACY,MAAO,CACH,KAAM,UACN,IAAKhB,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OACd,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACA,CACA,CACI,GAAGU,EAAK,CACJ,IAAMf,EAAM,KAAK,MAAM,MAAM,GAAG,KAAKe,CAAG,EACxC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKT,EAAMS,EAAI,CAAC,EAAG;CAAI,CACvC,CAEA,CACI,WAAWe,EAAK,CACZ,IAAMf,EAAM,KAAK,MAAM,MAAM,WAAW,KAAKe,CAAG,EAChD,GAAIf,EAAK,CACL,IAAIiB,EAAQ1B,EAAMS,EAAI,CAAC,EAAG;CAAI,EAAE,MAAM;CAAI,EACtCC,EAAM,GACNI,EAAO,GACLa,EAAS,CAAA,EACf,KAAOD,EAAM,OAAS,GAAG,CACrB,IAAIE,EAAe,GACbC,EAAe,CAAA,EACjB9B,EACJ,IAAKA,EAAI,EAAGA,EAAI2B,EAAM,OAAQ3B,IAE1B,GAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK2B,EAAM3B,CAAC,CAAC,EAC9C8B,EAAa,KAAKH,EAAM3B,CAAC,CAAC,EAC1B6B,EAAe,WAEV,CAACA,EACNC,EAAa,KAAKH,EAAM3B,CAAC,CAAC,MAG1B,OAGR2B,EAAQA,EAAM,MAAM3B,CAAC,EACrB,IAAM+B,EAAaD,EAAa,KAAK;CAAI,EACnCE,EAAcD,EAEf,QAAQ,KAAK,MAAM,MAAM,wBAAyB;OAAU,EAC5D,QAAQ,KAAK,MAAM,MAAM,yBAA0B,EAAE,EAC1DpB,EAAMA,EAAM,GAAGA,CAAG;EAAKoB,CAAU,GAAKA,EACtChB,EAAOA,EAAO,GAAGA,CAAI;EAAKiB,CAAW,GAAKA,EAG1C,IAAMC,EAAM,KAAK,MAAM,MAAM,IAK7B,GAJA,KAAK,MAAM,MAAM,IAAM,GACvB,KAAK,MAAM,YAAYD,EAAaJ,EAAQ,EAAI,EAChD,KAAK,MAAM,MAAM,IAAMK,EAEnBN,EAAM,SAAW,EACjB,MAEJ,IAAMO,EAAYN,EAAO,GAAG,EAAE,EAC9B,GAAIM,GAAW,OAAS,OAEpB,MAEC,GAAIA,GAAW,OAAS,aAAc,CAEvC,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;EAAOR,EAAM,KAAK;CAAI,EAC/CU,EAAW,KAAK,WAAWD,CAAO,EACxCR,EAAOA,EAAO,OAAS,CAAC,EAAIS,EAC5B1B,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAASwB,EAAS,IAAI,MAAM,EAAIE,EAAS,IACpEtB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASoB,EAAS,KAAK,MAAM,EAAIE,EAAS,KACxE,KACpB,SACyBH,GAAW,OAAS,OAAQ,CAEjC,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;EAAOR,EAAM,KAAK;CAAI,EAC/CU,EAAW,KAAK,KAAKD,CAAO,EAClCR,EAAOA,EAAO,OAAS,CAAC,EAAIS,EAC5B1B,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAASuB,EAAU,IAAI,MAAM,EAAIG,EAAS,IACrEtB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASoB,EAAS,IAAI,MAAM,EAAIE,EAAS,IACvEV,EAAQS,EAAQ,UAAUR,EAAO,GAAG,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM;CAAI,EAC9D,QACpB,CACA,CACY,MAAO,CACH,KAAM,aACN,IAAAjB,EACA,OAAAiB,EACA,KAAAb,CAChB,CACA,CACA,CACI,KAAKU,EAAK,CACN,IAAIf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EACxC,GAAIf,EAAK,CACL,IAAIrF,EAAOqF,EAAI,CAAC,EAAE,KAAI,EAChB4B,EAAYjH,EAAK,OAAS,EAC1Be,EAAO,CACT,KAAM,OACN,IAAK,GACL,QAASkG,EACT,MAAOA,EAAY,CAACjH,EAAK,MAAM,EAAG,EAAE,EAAI,GACxC,MAAO,GACP,MAAO,CAAA,CACvB,EACYA,EAAOiH,EAAY,aAAajH,EAAK,MAAM,EAAE,CAAC,GAAK,KAAKA,CAAI,GACxD,KAAK,QAAQ,WACbA,EAAOiH,EAAYjH,EAAO,SAG9B,IAAMkH,EAAY,KAAK,MAAM,MAAM,cAAclH,CAAI,EACjDmH,EAAoB,GAExB,KAAOf,GAAK,CACR,IAAIgB,EAAW,GACX9B,EAAM,GACN+B,EAAe,GAInB,GAHI,EAAEhC,EAAM6B,EAAU,KAAKd,CAAG,IAG1B,KAAK,MAAM,MAAM,GAAG,KAAKA,CAAG,EAC5B,MAEJd,EAAMD,EAAI,CAAC,EACXe,EAAMA,EAAI,UAAUd,EAAI,MAAM,EAC9B,IAAIgC,EAAOjC,EAAI,CAAC,EAAE,MAAM;EAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,gBAAkBkC,GAAM,IAAI,OAAO,EAAIA,EAAE,MAAM,CAAC,EACzGC,EAAWpB,EAAI,MAAM;EAAM,CAAC,EAAE,CAAC,EAC/BqB,EAAY,CAACH,EAAK,KAAI,EACtBrH,EAAS,EAmBb,GAlBI,KAAK,QAAQ,UACbA,EAAS,EACToH,EAAeC,EAAK,UAAS,GAExBG,EACLxH,EAASoF,EAAI,CAAC,EAAE,OAAS,GAGzBpF,EAASoF,EAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,EACpDpF,EAASA,EAAS,EAAI,EAAIA,EAC1BoH,EAAeC,EAAK,MAAMrH,CAAM,EAChCA,GAAUoF,EAAI,CAAC,EAAE,QAEjBoC,GAAa,KAAK,MAAM,MAAM,UAAU,KAAKD,CAAQ,IACrDlC,GAAOkC,EAAW;EAClBpB,EAAMA,EAAI,UAAUoB,EAAS,OAAS,CAAC,EACvCJ,EAAW,IAEX,CAACA,EAAU,CACX,IAAMM,EAAkB,KAAK,MAAM,MAAM,gBAAgBzH,CAAM,EACzD0H,EAAU,KAAK,MAAM,MAAM,QAAQ1H,CAAM,EACzC2H,EAAmB,KAAK,MAAM,MAAM,iBAAiB3H,CAAM,EAC3D4H,GAAoB,KAAK,MAAM,MAAM,kBAAkB5H,CAAM,EAC7D6H,EAAiB,KAAK,MAAM,MAAM,eAAe7H,CAAM,EAE7D,KAAOmG,GAAK,CACR,IAAM2B,EAAU3B,EAAI,MAAM;EAAM,CAAC,EAAE,CAAC,EAChC4B,EA2BJ,GA1BAR,EAAWO,EAEP,KAAK,QAAQ,UACbP,EAAWA,EAAS,QAAQ,KAAK,MAAM,MAAM,mBAAoB,IAAI,EACrEQ,EAAsBR,GAGtBQ,EAAsBR,EAAS,QAAQ,KAAK,MAAM,MAAM,cAAe,MAAM,EAG7EI,EAAiB,KAAKJ,CAAQ,GAI9BK,GAAkB,KAAKL,CAAQ,GAI/BM,EAAe,KAAKN,CAAQ,GAI5BE,EAAgB,KAAKF,CAAQ,GAI7BG,EAAQ,KAAKH,CAAQ,EACrB,MAEJ,GAAIQ,EAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,GAAK/H,GAAU,CAACuH,EAAS,KAAI,EACrFH,GAAgB;EAAOW,EAAoB,MAAM/H,CAAM,MAEtD,CAeD,GAbIwH,GAIAH,EAAK,QAAQ,KAAK,MAAM,MAAM,cAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,GAAK,GAG9FM,EAAiB,KAAKN,CAAI,GAG1BO,GAAkB,KAAKP,CAAI,GAG3BK,EAAQ,KAAKL,CAAI,EACjB,MAEJD,GAAgB;EAAOG,CACnD,CAC4B,CAACC,GAAa,CAACD,EAAS,KAAI,IAC5BC,EAAY,IAEhBnC,GAAOyC,EAAU;EACjB3B,EAAMA,EAAI,UAAU2B,EAAQ,OAAS,CAAC,EACtCT,EAAOU,EAAoB,MAAM/H,CAAM,CAC/D,CACA,CACqBc,EAAK,QAEFoG,EACApG,EAAK,MAAQ,GAER,KAAK,MAAM,MAAM,gBAAgB,KAAKuE,CAAG,IAC9C6B,EAAoB,KAG5B,IAAIc,EAAS,KACTC,EAEA,KAAK,QAAQ,MACbD,EAAS,KAAK,MAAM,MAAM,WAAW,KAAKZ,CAAY,EAClDY,IACAC,EAAYD,EAAO,CAAC,IAAM,OAC1BZ,EAAeA,EAAa,QAAQ,KAAK,MAAM,MAAM,gBAAiB,EAAE,IAGhFtG,EAAK,MAAM,KAAK,CACZ,KAAM,YACN,IAAAuE,EACA,KAAM,CAAC,CAAC2C,EACR,QAASC,EACT,MAAO,GACP,KAAMb,EACN,OAAQ,CAAA,CAC5B,CAAiB,EACDtG,EAAK,KAAOuE,CAC5B,CAEY,IAAM6C,EAAWpH,EAAK,MAAM,GAAG,EAAE,EACjC,GAAIoH,EACAA,EAAS,IAAMA,EAAS,IAAI,QAAO,EACnCA,EAAS,KAAOA,EAAS,KAAK,QAAO,MAIrC,QAEJpH,EAAK,IAAMA,EAAK,IAAI,QAAO,EAE3B,QAAS4D,EAAI,EAAGA,EAAI5D,EAAK,MAAM,OAAQ4D,IAGnC,GAFA,KAAK,MAAM,MAAM,IAAM,GACvB5D,EAAK,MAAM4D,CAAC,EAAE,OAAS,KAAK,MAAM,YAAY5D,EAAK,MAAM4D,CAAC,EAAE,KAAM,CAAA,CAAE,EAChE,CAAC5D,EAAK,MAAO,CAEb,IAAMqH,EAAUrH,EAAK,MAAM4D,CAAC,EAAE,OAAO,OAAO4C,GAAKA,EAAE,OAAS,OAAO,EAC7Dc,EAAwBD,EAAQ,OAAS,GAAKA,EAAQ,KAAKb,GAAK,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAE,GAAG,CAAC,EAC1GxG,EAAK,MAAQsH,CACjC,CAGY,GAAItH,EAAK,MACL,QAAS4D,EAAI,EAAGA,EAAI5D,EAAK,MAAM,OAAQ4D,IACnC5D,EAAK,MAAM4D,CAAC,EAAE,MAAQ,GAG9B,OAAO5D,CACnB,CACA,CACI,KAAKqF,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAQA,MAPc,CACV,KAAM,OACN,MAAO,GACP,IAAKA,EAAI,CAAC,EACV,IAAKA,EAAI,CAAC,IAAM,OAASA,EAAI,CAAC,IAAM,UAAYA,EAAI,CAAC,IAAM,QAC3D,KAAMA,EAAI,CAAC,CAC3B,CAGA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,MAAM,IAAI,KAAKe,CAAG,EACzC,GAAIf,EAAK,CACL,IAAMtC,EAAMsC,EAAI,CAAC,EAAE,YAAW,EAAG,QAAQ,KAAK,MAAM,MAAM,oBAAqB,GAAG,EAC5ErB,EAAOqB,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,aAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAI,GACtHI,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGA,EAAI,CAAC,EAAE,OAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACrH,MAAO,CACH,KAAM,MACN,IAAAtC,EACA,IAAKsC,EAAI,CAAC,EACV,KAAArB,EACA,MAAAyB,CAChB,CACA,CACA,CACI,MAAMW,EAAK,CACP,IAAMf,EAAM,KAAK,MAAM,MAAM,MAAM,KAAKe,CAAG,EAI3C,GAHI,CAACf,GAGD,CAAC,KAAK,MAAM,MAAM,eAAe,KAAKA,EAAI,CAAC,CAAC,EAE5C,OAEJ,IAAMiD,EAAUrE,GAAWoB,EAAI,CAAC,CAAC,EAC3BkD,EAASlD,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,gBAAiB,EAAE,EAAE,MAAM,GAAG,EACvEmD,EAAOnD,EAAI,CAAC,GAAG,KAAI,EAAKA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAmB,EAAE,EAAE,MAAM;CAAI,EAAI,CAAA,EAC7FoD,EAAO,CACT,KAAM,QACN,IAAKpD,EAAI,CAAC,EACV,OAAQ,CAAA,EACR,MAAO,CAAA,EACP,KAAM,CAAA,CAClB,EACQ,GAAIiD,EAAQ,SAAWC,EAAO,OAI9B,SAAWG,KAASH,EACZ,KAAK,MAAM,MAAM,gBAAgB,KAAKG,CAAK,EAC3CD,EAAK,MAAM,KAAK,OAAO,EAElB,KAAK,MAAM,MAAM,iBAAiB,KAAKC,CAAK,EACjDD,EAAK,MAAM,KAAK,QAAQ,EAEnB,KAAK,MAAM,MAAM,eAAe,KAAKC,CAAK,EAC/CD,EAAK,MAAM,KAAK,MAAM,EAGtBA,EAAK,MAAM,KAAK,IAAI,EAG5B,QAAS9D,EAAI,EAAGA,EAAI2D,EAAQ,OAAQ3D,IAChC8D,EAAK,OAAO,KAAK,CACb,KAAMH,EAAQ3D,CAAC,EACf,OAAQ,KAAK,MAAM,OAAO2D,EAAQ3D,CAAC,CAAC,EACpC,OAAQ,GACR,MAAO8D,EAAK,MAAM9D,CAAC,CACnC,CAAa,EAEL,QAAWP,KAAOoE,EACdC,EAAK,KAAK,KAAKxE,GAAWG,EAAKqE,EAAK,OAAO,MAAM,EAAE,IAAI,CAACE,EAAMhE,KACnD,CACH,KAAMgE,EACN,OAAQ,KAAK,MAAM,OAAOA,CAAI,EAC9B,OAAQ,GACR,MAAOF,EAAK,MAAM9D,CAAC,CACvC,EACa,CAAC,EAEN,OAAO8D,EACf,CACI,SAASrC,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,MAAM,SAAS,KAAKe,CAAG,EAC9C,GAAIf,EACA,MAAO,CACH,KAAM,UACN,IAAKA,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAM,EAAI,EACtC,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEA,CACI,UAAUe,EAAK,CACX,IAAMf,EAAM,KAAK,MAAM,MAAM,UAAU,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,IAAMK,EAAOL,EAAI,CAAC,EAAE,OAAOA,EAAI,CAAC,EAAE,OAAS,CAAC,IAAM;EAC5CA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAClBA,EAAI,CAAC,EACX,MAAO,CACH,KAAM,YACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACA,CACA,CACI,KAAKU,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEA,CACI,OAAOe,EAAK,CACR,IAAMf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,EAC7C,GAAIf,EACA,MAAO,CACH,KAAM,SACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,CAC3B,CAEA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAI,CAAC,KAAK,MAAM,MAAM,QAAU,KAAK,MAAM,MAAM,UAAU,KAAKA,EAAI,CAAC,CAAC,EAClE,KAAK,MAAM,MAAM,OAAS,GAErB,KAAK,MAAM,MAAM,QAAU,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAI,CAAC,CAAC,IACpE,KAAK,MAAM,MAAM,OAAS,IAE1B,CAAC,KAAK,MAAM,MAAM,YAAc,KAAK,MAAM,MAAM,kBAAkB,KAAKA,EAAI,CAAC,CAAC,EAC9E,KAAK,MAAM,MAAM,WAAa,GAEzB,KAAK,MAAM,MAAM,YAAc,KAAK,MAAM,MAAM,gBAAgB,KAAKA,EAAI,CAAC,CAAC,IAChF,KAAK,MAAM,MAAM,WAAa,IAE3B,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,OAAQ,KAAK,MAAM,MAAM,OACzB,WAAY,KAAK,MAAM,MAAM,WAC7B,MAAO,GACP,KAAMA,EAAI,CAAC,CAC3B,CAEA,CACI,KAAKe,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAMuD,EAAavD,EAAI,CAAC,EAAE,KAAI,EAC9B,GAAI,CAAC,KAAK,QAAQ,UAAY,KAAK,MAAM,MAAM,kBAAkB,KAAKuD,CAAU,EAAG,CAE/E,GAAI,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAU,EAClD,OAGJ,IAAMC,EAAajE,EAAMgE,EAAW,MAAM,EAAG,EAAE,EAAG,IAAI,EACtD,IAAKA,EAAW,OAASC,EAAW,QAAU,IAAM,EAChD,MAEpB,KACiB,CAED,IAAMC,EAAiB7D,GAAmBI,EAAI,CAAC,EAAG,IAAI,EACtD,GAAIyD,EAAiB,GAAI,CAErB,IAAMC,GADQ1D,EAAI,CAAC,EAAE,QAAQ,GAAG,IAAM,EAAI,EAAI,GACtBA,EAAI,CAAC,EAAE,OAASyD,EACxCzD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGyD,CAAc,EAC3CzD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAG0D,CAAO,EAAE,KAAI,EAC1C1D,EAAI,CAAC,EAAI,EAC7B,CACA,CACY,IAAIrB,EAAOqB,EAAI,CAAC,EACZI,EAAQ,GACZ,GAAI,KAAK,QAAQ,SAAU,CAEvB,IAAMxC,EAAO,KAAK,MAAM,MAAM,kBAAkB,KAAKe,CAAI,EACrDf,IACAe,EAAOf,EAAK,CAAC,EACbwC,EAAQxC,EAAK,CAAC,EAElC,MAEgBwC,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAAI,GAE3C,OAAArB,EAAOA,EAAK,KAAI,EACZ,KAAK,MAAM,MAAM,kBAAkB,KAAKA,CAAI,IACxC,KAAK,QAAQ,UAAY,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK4E,CAAU,EAE3E5E,EAAOA,EAAK,MAAM,CAAC,EAGnBA,EAAOA,EAAK,MAAM,EAAG,EAAE,GAGxBoB,GAAWC,EAAK,CACnB,KAAMrB,GAAOA,EAAK,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAChE,MAAOyB,GAAQA,EAAM,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,CACnF,EAAeJ,EAAI,CAAC,EAAG,KAAK,MAAO,KAAK,KAAK,CAC7C,CACA,CACI,QAAQe,EAAK4C,EAAO,CAChB,IAAI3D,EACJ,IAAKA,EAAM,KAAK,MAAM,OAAO,QAAQ,KAAKe,CAAG,KACrCf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,GAAI,CAC/C,IAAM6C,GAAc5D,EAAI,CAAC,GAAKA,EAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,oBAAqB,GAAG,EACjFpC,EAAO+F,EAAMC,EAAW,YAAW,CAAE,EAC3C,GAAI,CAAChG,EAAM,CACP,IAAMyC,EAAOL,EAAI,CAAC,EAAE,OAAO,CAAC,EAC5B,MAAO,CACH,KAAM,OACN,IAAKK,EACL,KAAAA,CACpB,CACA,CACY,OAAON,GAAWC,EAAKpC,EAAMoC,EAAI,CAAC,EAAG,KAAK,MAAO,KAAK,KAAK,CACvE,CACA,CACI,SAASe,EAAK8C,EAAWC,EAAW,GAAI,CACpC,IAAI9E,EAAQ,KAAK,MAAM,OAAO,eAAe,KAAK+B,CAAG,EAIrD,GAHI,CAAC/B,GAGDA,EAAM,CAAC,GAAK8E,EAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB,EAC/D,OAEJ,GAAI,EADa9E,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAK,KACxB,CAAC8E,GAAY,KAAK,MAAM,OAAO,YAAY,KAAKA,CAAQ,EAAG,CAExE,IAAMC,EAAU,CAAC,GAAG/E,EAAM,CAAC,CAAC,EAAE,OAAS,EACnCgF,EAAQC,EAASC,EAAaH,EAASI,EAAgB,EACrDC,EAASpF,EAAM,CAAC,EAAE,CAAC,IAAM,IAAM,KAAK,MAAM,OAAO,kBAAoB,KAAK,MAAM,OAAO,kBAI7F,IAHAoF,EAAO,UAAY,EAEnBP,EAAYA,EAAU,MAAM,GAAK9C,EAAI,OAASgD,CAAO,GAC7C/E,EAAQoF,EAAO,KAAKP,CAAS,IAAM,MAAM,CAE7C,GADAG,EAAShF,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,EACxE,CAACgF,EACD,SAEJ,GADAC,EAAU,CAAC,GAAGD,CAAM,EAAE,OAClBhF,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAG,CACtBkF,GAAcD,EACd,QACpB,UACyBjF,EAAM,CAAC,GAAKA,EAAM,CAAC,IACpB+E,EAAU,GAAK,GAAGA,EAAUE,GAAW,GAAI,CAC3CE,GAAiBF,EACjB,QACxB,CAGgB,GADAC,GAAcD,EACVC,EAAa,EACb,SAEJD,EAAU,KAAK,IAAIA,EAASA,EAAUC,EAAaC,CAAa,EAEhE,IAAME,EAAiB,CAAC,GAAGrF,EAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAClCiB,EAAMc,EAAI,MAAM,EAAGgD,EAAU/E,EAAM,MAAQqF,EAAiBJ,CAAO,EAEzE,GAAI,KAAK,IAAIF,EAASE,CAAO,EAAI,EAAG,CAChC,IAAM5D,EAAOJ,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,KACN,IAAAA,EACA,KAAAI,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CAC5D,CACA,CAEgB,IAAMA,EAAOJ,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,SACN,IAAAA,EACA,KAAAI,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CACxD,CACA,CACA,CACA,CACI,SAASU,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAmB,GAAG,EAC3DsE,EAAmB,KAAK,MAAM,MAAM,aAAa,KAAKjE,CAAI,EAC1DkE,EAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAKlE,CAAI,GAAK,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAI,EAC3H,OAAIiE,GAAoBC,IACpBlE,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAAS,CAAC,GAErC,CACH,KAAM,WACN,IAAKL,EAAI,CAAC,EACV,KAAAK,CAChB,CACA,CACA,CACI,GAAGU,EAAK,CACJ,IAAMf,EAAM,KAAK,MAAM,OAAO,GAAG,KAAKe,CAAG,EACzC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKA,EAAI,CAAC,CAC1B,CAEA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,MACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,aAAaA,EAAI,CAAC,CAAC,CACtD,CAEA,CACI,SAASe,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,OAAO,SAAS,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,IAAIK,EAAM1B,EACV,OAAIqB,EAAI,CAAC,IAAM,KACXK,EAAOL,EAAI,CAAC,EACZrB,EAAO,UAAY0B,IAGnBA,EAAOL,EAAI,CAAC,EACZrB,EAAO0B,GAEJ,CACH,KAAM,OACN,IAAKL,EAAI,CAAC,EACV,KAAAK,EACA,KAAA1B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK0B,EACL,KAAAA,CACxB,CACA,CACA,CACA,CACA,CACI,IAAIU,EAAK,CACL,IAAIf,EACJ,GAAIA,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAAG,CACvC,IAAIV,EAAM1B,EACV,GAAIqB,EAAI,CAAC,IAAM,IACXK,EAAOL,EAAI,CAAC,EACZrB,EAAO,UAAY0B,MAElB,CAED,IAAImE,EACJ,GACIA,EAAcxE,EAAI,CAAC,EACnBA,EAAI,CAAC,EAAI,KAAK,MAAM,OAAO,WAAW,KAAKA,EAAI,CAAC,CAAC,IAAI,CAAC,GAAK,SACtDwE,IAAgBxE,EAAI,CAAC,GAC9BK,EAAOL,EAAI,CAAC,EACRA,EAAI,CAAC,IAAM,OACXrB,EAAO,UAAYqB,EAAI,CAAC,EAGxBrB,EAAOqB,EAAI,CAAC,CAEhC,CACY,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,KAAA1B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK0B,EACL,KAAAA,CACxB,CACA,CACA,CACA,CACA,CACI,WAAWU,EAAK,CACZ,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAMb,EAAU,KAAK,MAAM,MAAM,WACjC,MAAO,CACH,KAAM,OACN,IAAKa,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,QAAAb,CAChB,CACA,CACA,CACA,EClxBasF,EAAN,MAAMC,CAAO,OAAA,CAAA7K,EAAA,eAChB,OACA,QACA,MACA,UACA,YACA,YAAYiH,EAAS,CAEjB,KAAK,OAAS,CAAA,EACd,KAAK,OAAO,MAAQ,OAAO,OAAO,IAAI,EACtC,KAAK,QAAUA,GAAWhH,EAC1B,KAAK,QAAQ,UAAY,KAAK,QAAQ,WAAa,IAAI+G,EACvD,KAAK,UAAY,KAAK,QAAQ,UAC9B,KAAK,UAAU,QAAU,KAAK,QAC9B,KAAK,UAAU,MAAQ,KACvB,KAAK,YAAc,CAAA,EACnB,KAAK,MAAQ,CACT,OAAQ,GACR,WAAY,GACZ,IAAK,EACjB,EACQ,IAAMV,EAAQ,CACV,MAAAzF,EACA,MAAO0D,EAAM,OACb,OAAQC,EAAO,MAC3B,EACY,KAAK,QAAQ,UACb8B,EAAM,MAAQ/B,EAAM,SACpB+B,EAAM,OAAS9B,EAAO,UAEjB,KAAK,QAAQ,MAClB8B,EAAM,MAAQ/B,EAAM,IAChB,KAAK,QAAQ,OACb+B,EAAM,OAAS9B,EAAO,OAGtB8B,EAAM,OAAS9B,EAAO,KAG9B,KAAK,UAAU,MAAQ8B,CAC/B,CAII,WAAW,OAAQ,CACf,MAAO,CACH,MAAA/B,EACA,OAAAC,CACZ,CACA,CAII,OAAO,IAAI0C,EAAKD,EAAS,CAErB,OADc,IAAI4D,EAAO5D,CAAO,EACnB,IAAIC,CAAG,CAC5B,CAII,OAAO,UAAUA,EAAKD,EAAS,CAE3B,OADc,IAAI4D,EAAO5D,CAAO,EACnB,aAAaC,CAAG,CACrC,CAII,IAAIA,EAAK,CACLA,EAAMA,EAAI,QAAQrG,EAAM,eAAgB;CAAI,EAC5C,KAAK,YAAYqG,EAAK,KAAK,MAAM,EACjC,QAASzB,EAAI,EAAGA,EAAI,KAAK,YAAY,OAAQA,IAAK,CAC9C,IAAMqF,EAAO,KAAK,YAAYrF,CAAC,EAC/B,KAAK,aAAaqF,EAAK,IAAKA,EAAK,MAAM,CACnD,CACQ,YAAK,YAAc,CAAA,EACZ,KAAK,MACpB,CACI,YAAY5D,EAAKG,EAAS,CAAA,EAAI0D,EAAuB,GAAO,CAIxD,IAHI,KAAK,QAAQ,WACb7D,EAAMA,EAAI,QAAQrG,EAAM,cAAe,MAAM,EAAE,QAAQA,EAAM,UAAW,EAAE,GAEvEqG,GAAK,CACR,IAAIT,EACJ,GAAI,KAAK,QAAQ,YAAY,OAAO,KAAMuE,IAClCvE,EAAQuE,EAAa,KAAK,CAAE,MAAO,IAAI,EAAI9D,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,EACG,SAGJ,GAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BZ,EAAM,IAAI,SAAW,GAAKkB,IAAc,OAGxCA,EAAU,KAAO;EAGjBN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAE1BM,GAAW,OAAS,aAAeA,GAAW,OAAS,QACvDA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAGxCN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,QAAQS,CAAG,EAAG,CACrCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,WAAWS,CAAG,EAAG,CACxCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BM,GAAW,OAAS,aAAeA,GAAW,OAAS,QACvDA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,IAC/B,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAElC,KAAK,OAAO,MAAMlB,EAAM,GAAG,IACjC,KAAK,OAAO,MAAMA,EAAM,GAAG,EAAI,CAC3B,KAAMA,EAAM,KACZ,MAAOA,EAAM,KACrC,GAEgB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAGY,IAAIwE,EAAS/D,EACb,GAAI,KAAK,QAAQ,YAAY,WAAY,CACrC,IAAIgE,EAAa,IACXC,EAAUjE,EAAI,MAAM,CAAC,EACvBkE,EACJ,KAAK,QAAQ,WAAW,WAAW,QAASC,GAAkB,CAC1DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCD,EAAS/D,EAAI,UAAU,EAAGgE,EAAa,CAAC,EAE5D,CACY,GAAI,KAAK,MAAM,MAAQzE,EAAQ,KAAK,UAAU,UAAUwE,CAAM,GAAI,CAC9D,IAAMtD,EAAYN,EAAO,GAAG,EAAE,EAC1B0D,GAAwBpD,GAAW,OAAS,aAC5CA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,IAAG,EACpB,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAGxCN,EAAO,KAAKZ,CAAK,EAErBsE,EAAuBE,EAAO,SAAW/D,EAAI,OAC7CA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BM,GAAW,OAAS,QACpBA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,IAAG,EACpB,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAGxCN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CACY,GAAIS,EAAK,CACL,IAAMoE,EAAS,0BAA4BpE,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAMoE,CAAM,EACpB,KACpB,KAEoB,OAAM,IAAI,MAAMA,CAAM,CAE1C,CACA,CACQ,YAAK,MAAM,IAAM,GACVjE,CACf,CACI,OAAOH,EAAKG,EAAS,CAAA,EAAI,CACrB,YAAK,YAAY,KAAK,CAAE,IAAAH,EAAK,OAAAG,CAAM,CAAE,EAC9BA,CACf,CAII,aAAaH,EAAKG,EAAS,CAAA,EAAI,CAE3B,IAAI2C,EAAY9C,EACZ/B,EAAQ,KAEZ,GAAI,KAAK,OAAO,MAAO,CACnB,IAAM2E,EAAQ,OAAO,KAAK,KAAK,OAAO,KAAK,EAC3C,GAAIA,EAAM,OAAS,EACf,MAAQ3E,EAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK6E,CAAS,IAAM,MACtEF,EAAM,SAAS3E,EAAM,CAAC,EAAE,MAAMA,EAAM,CAAC,EAAE,YAAY,GAAG,EAAI,EAAG,EAAE,CAAC,IAChE6E,EAAYA,EAAU,MAAM,EAAG7E,EAAM,KAAK,EACpC,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IACxC6E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS,EAIjG,CAEQ,MAAQ7E,EAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK6E,CAAS,IAAM,MACtEA,EAAYA,EAAU,MAAM,EAAG7E,EAAM,KAAK,EAAI,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IAAM6E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS,EAG/J,MAAQ7E,EAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK6E,CAAS,IAAM,MAC3EA,EAAYA,EAAU,MAAM,EAAG7E,EAAM,KAAK,EAAI,KAAO6E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS,EAE7H,IAAIuB,EAAe,GACftB,EAAW,GACf,KAAO/C,GAAK,CACHqE,IACDtB,EAAW,IAEfsB,EAAe,GACf,IAAI9E,EAEJ,GAAI,KAAK,QAAQ,YAAY,QAAQ,KAAMuE,IACnCvE,EAAQuE,EAAa,KAAK,CAAE,MAAO,IAAI,EAAI9D,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,EACG,SAGJ,GAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,QAAQS,EAAK,KAAK,OAAO,KAAK,EAAG,CACxDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BZ,EAAM,OAAS,QAAUkB,GAAW,OAAS,QAC7CA,EAAU,KAAOlB,EAAM,IACvBkB,EAAU,MAAQlB,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,EAAK8C,EAAWC,CAAQ,EAAG,CAC3D/C,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAI,CAAC,KAAK,MAAM,SAAWA,EAAQ,KAAK,UAAU,IAAIS,CAAG,GAAI,CACzDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAGY,IAAIwE,EAAS/D,EACb,GAAI,KAAK,QAAQ,YAAY,YAAa,CACtC,IAAIgE,EAAa,IACXC,EAAUjE,EAAI,MAAM,CAAC,EACvBkE,EACJ,KAAK,QAAQ,WAAW,YAAY,QAASC,GAAkB,CAC3DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCD,EAAS/D,EAAI,UAAU,EAAGgE,EAAa,CAAC,EAE5D,CACY,GAAIzE,EAAQ,KAAK,UAAU,WAAWwE,CAAM,EAAG,CAC3C/D,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EAChCA,EAAM,IAAI,MAAM,EAAE,IAAM,MACxBwD,EAAWxD,EAAM,IAAI,MAAM,EAAE,GAEjC8E,EAAe,GACf,IAAM5D,EAAYN,EAAO,GAAG,EAAE,EAC1BM,GAAW,OAAS,QACpBA,EAAU,KAAOlB,EAAM,IACvBkB,EAAU,MAAQlB,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QAChB,CACY,GAAIS,EAAK,CACL,IAAMoE,EAAS,0BAA4BpE,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAMoE,CAAM,EACpB,KACpB,KAEoB,OAAM,IAAI,MAAMA,CAAM,CAE1C,CACA,CACQ,OAAOjE,CACf,CACA,EC5ZamE,EAAN,KAAgB,OAAA,CAAAxL,EAAA,kBACnB,QACA,OACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,CAClC,CACI,MAAMwG,EAAO,CACT,MAAO,EACf,CACI,KAAK,CAAE,KAAAD,EAAM,KAAAiF,EAAM,QAAAnG,CAAO,EAAI,CAC1B,IAAMoG,GAAcD,GAAQ,IAAI,MAAM5K,EAAM,aAAa,IAAI,CAAC,EACxD8K,EAAOnF,EAAK,QAAQ3F,EAAM,cAAe,EAAE,EAAI;EACrD,OAAK6K,EAKE,8BACDnJ,EAAOmJ,CAAU,EACjB,MACCpG,EAAUqG,EAAOpJ,EAAOoJ,EAAM,EAAI,GACnC;EARK,eACArG,EAAUqG,EAAOpJ,EAAOoJ,EAAM,EAAI,GACnC;CAOlB,CACI,WAAW,CAAE,OAAAtE,CAAM,EAAI,CAEnB,MAAO;EADM,KAAK,OAAO,MAAMA,CAAM,CACT;CACpC,CACI,KAAK,CAAE,KAAAb,CAAI,EAAI,CACX,OAAOA,CACf,CACI,QAAQ,CAAE,OAAAa,EAAQ,MAAAuE,CAAK,EAAI,CACvB,MAAO,KAAKA,CAAK,IAAI,KAAK,OAAO,YAAYvE,CAAM,CAAC,MAAMuE,CAAK;CACvE,CACI,GAAGnF,EAAO,CACN,MAAO;CACf,CACI,KAAKA,EAAO,CACR,IAAMoF,EAAUpF,EAAM,QAChBqF,EAAQrF,EAAM,MAChBsF,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIvF,EAAM,MAAM,OAAQuF,IAAK,CACzC,IAAMzC,EAAO9C,EAAM,MAAMuF,CAAC,EAC1BD,GAAQ,KAAK,SAASxC,CAAI,CACtC,CACQ,IAAM0C,EAAOJ,EAAU,KAAO,KACxBK,EAAaL,GAAWC,IAAU,EAAM,WAAaA,EAAQ,IAAO,GAC1E,MAAO,IAAMG,EAAOC,EAAY;EAAQH,EAAO,KAAOE,EAAO;CACrE,CACI,SAAS1C,EAAM,CACX,IAAI4C,EAAW,GACf,GAAI5C,EAAK,KAAM,CACX,IAAM6C,EAAW,KAAK,SAAS,CAAE,QAAS,CAAC,CAAC7C,EAAK,OAAO,CAAE,EACtDA,EAAK,MACDA,EAAK,OAAO,CAAC,GAAG,OAAS,aACzBA,EAAK,OAAO,CAAC,EAAE,KAAO6C,EAAW,IAAM7C,EAAK,OAAO,CAAC,EAAE,KAClDA,EAAK,OAAO,CAAC,EAAE,QAAUA,EAAK,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAS,SAC/FA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,KAAO6C,EAAW,IAAM7J,EAAOgH,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI,EACrFA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAU,KAIvCA,EAAK,OAAO,QAAQ,CAChB,KAAM,OACN,IAAK6C,EAAW,IAChB,KAAMA,EAAW,IACjB,QAAS,EACjC,CAAqB,EAILD,GAAYC,EAAW,GAEvC,CACQ,OAAAD,GAAY,KAAK,OAAO,MAAM5C,EAAK,OAAQ,CAAC,CAACA,EAAK,KAAK,EAChD,OAAO4C,CAAQ;CAC9B,CACI,SAAS,CAAE,QAAAE,CAAO,EAAI,CAClB,MAAO,WACAA,EAAU,cAAgB,IAC3B,8BACd,CACI,UAAU,CAAE,OAAAhF,CAAM,EAAI,CAClB,MAAO,MAAM,KAAK,OAAO,YAAYA,CAAM,CAAC;CACpD,CACI,MAAMZ,EAAO,CACT,IAAI6F,EAAS,GAET7C,EAAO,GACX,QAASuC,EAAI,EAAGA,EAAIvF,EAAM,OAAO,OAAQuF,IACrCvC,GAAQ,KAAK,UAAUhD,EAAM,OAAOuF,CAAC,CAAC,EAE1CM,GAAU,KAAK,SAAS,CAAE,KAAM7C,CAAI,CAAE,EACtC,IAAIsC,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIvF,EAAM,KAAK,OAAQuF,IAAK,CACxC,IAAM9G,EAAMuB,EAAM,KAAKuF,CAAC,EACxBvC,EAAO,GACP,QAAS8C,EAAI,EAAGA,EAAIrH,EAAI,OAAQqH,IAC5B9C,GAAQ,KAAK,UAAUvE,EAAIqH,CAAC,CAAC,EAEjCR,GAAQ,KAAK,SAAS,CAAE,KAAMtC,CAAI,CAAE,CAChD,CACQ,OAAIsC,IACAA,EAAO,UAAUA,CAAI,YAClB;;EAEDO,EACA;EACAP,EACA;CACd,CACI,SAAS,CAAE,KAAAvF,CAAI,EAAI,CACf,MAAO;EAASA,CAAI;CAC5B,CACI,UAAUC,EAAO,CACb,IAAM+F,EAAU,KAAK,OAAO,YAAY/F,EAAM,MAAM,EAC9CwF,EAAOxF,EAAM,OAAS,KAAO,KAInC,OAHYA,EAAM,MACZ,IAAIwF,CAAI,WAAWxF,EAAM,KAAK,KAC9B,IAAIwF,CAAI,KACDO,EAAU,KAAKP,CAAI;CACxC,CAII,OAAO,CAAE,OAAA5E,CAAM,EAAI,CACf,MAAO,WAAW,KAAK,OAAO,YAAYA,CAAM,CAAC,WACzD,CACI,GAAG,CAAE,OAAAA,CAAM,EAAI,CACX,MAAO,OAAO,KAAK,OAAO,YAAYA,CAAM,CAAC,OACrD,CACI,SAAS,CAAE,KAAAb,CAAI,EAAI,CACf,MAAO,SAASjE,EAAOiE,EAAM,EAAI,CAAC,SAC1C,CACI,GAAGC,EAAO,CACN,MAAO,MACf,CACI,IAAI,CAAE,OAAAY,CAAM,EAAI,CACZ,MAAO,QAAQ,KAAK,OAAO,YAAYA,CAAM,CAAC,QACtD,CACI,KAAK,CAAE,KAAAvC,EAAM,MAAAyB,EAAO,OAAAc,CAAM,EAAI,CAC1B,IAAMb,EAAO,KAAK,OAAO,YAAYa,CAAM,EACrCoF,EAAY5H,GAASC,CAAI,EAC/B,GAAI2H,IAAc,KACd,OAAOjG,EAEX1B,EAAO2H,EACP,IAAIC,EAAM,YAAc5H,EAAO,IAC/B,OAAIyB,IACAmG,GAAO,WAAcnK,EAAOgE,CAAK,EAAK,KAE1CmG,GAAO,IAAMlG,EAAO,OACbkG,CACf,CACI,MAAM,CAAE,KAAA5H,EAAM,MAAAyB,EAAO,KAAAC,CAAI,EAAI,CACzB,IAAMiG,EAAY5H,GAASC,CAAI,EAC/B,GAAI2H,IAAc,KACd,OAAOlK,EAAOiE,CAAI,EAEtB1B,EAAO2H,EACP,IAAIC,EAAM,aAAa5H,CAAI,UAAU0B,CAAI,IACzC,OAAID,IACAmG,GAAO,WAAWnK,EAAOgE,CAAK,CAAC,KAEnCmG,GAAO,IACAA,CACf,CACI,KAAKjG,EAAO,CACR,MAAO,WAAYA,GAASA,EAAM,OAC5B,KAAK,OAAO,YAAYA,EAAM,MAAM,EACnC,YAAaA,GAASA,EAAM,QAAUA,EAAM,KAAOlE,EAAOkE,EAAM,IAAI,CACnF,CACA,EC7KakG,EAAN,KAAoB,OAAA,CAAA3M,EAAA,sBAEvB,OAAO,CAAE,KAAAwG,CAAI,EAAI,CACb,OAAOA,CACf,CACI,GAAG,CAAE,KAAAA,CAAI,EAAI,CACT,OAAOA,CACf,CACI,SAAS,CAAE,KAAAA,CAAI,EAAI,CACf,OAAOA,CACf,CACI,IAAI,CAAE,KAAAA,CAAI,EAAI,CACV,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,MAAO,GAAKA,CACpB,CACI,MAAM,CAAE,KAAAA,CAAI,EAAI,CACZ,MAAO,GAAKA,CACpB,CACI,IAAK,CACD,MAAO,EACf,CACA,EC3BaoG,EAAN,MAAMC,CAAQ,OAAA,CAAA7M,EAAA,gBACjB,QACA,SACA,aACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,EAC1B,KAAK,QAAQ,SAAW,KAAK,QAAQ,UAAY,IAAIuL,EACrD,KAAK,SAAW,KAAK,QAAQ,SAC7B,KAAK,SAAS,QAAU,KAAK,QAC7B,KAAK,SAAS,OAAS,KACvB,KAAK,aAAe,IAAImB,CAChC,CAII,OAAO,MAAMtF,EAAQJ,EAAS,CAE1B,OADe,IAAI4F,EAAQ5F,CAAO,EACpB,MAAMI,CAAM,CAClC,CAII,OAAO,YAAYA,EAAQJ,EAAS,CAEhC,OADe,IAAI4F,EAAQ5F,CAAO,EACpB,YAAYI,CAAM,CACxC,CAII,MAAMA,EAAQK,EAAM,GAAM,CACtB,IAAIgF,EAAM,GACV,QAASjH,EAAI,EAAGA,EAAI4B,EAAO,OAAQ5B,IAAK,CACpC,IAAMqH,EAAWzF,EAAO5B,CAAC,EAEzB,GAAI,KAAK,QAAQ,YAAY,YAAYqH,EAAS,IAAI,EAAG,CACrD,IAAMC,EAAeD,EACfE,EAAM,KAAK,QAAQ,WAAW,UAAUD,EAAa,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAI,EAAIA,CAAY,EACpG,GAAIC,IAAQ,IAAS,CAAC,CAAC,QAAS,KAAM,UAAW,OAAQ,QAAS,aAAc,OAAQ,OAAQ,YAAa,MAAM,EAAE,SAASD,EAAa,IAAI,EAAG,CAC9IL,GAAOM,GAAO,GACd,QACpB,CACA,CACY,IAAMvG,EAAQqG,EACd,OAAQrG,EAAM,KAAI,CACd,IAAK,QAAS,CACViG,GAAO,KAAK,SAAS,MAAMjG,CAAK,EAChC,QACpB,CACgB,IAAK,KAAM,CACPiG,GAAO,KAAK,SAAS,GAAGjG,CAAK,EAC7B,QACpB,CACgB,IAAK,UAAW,CACZiG,GAAO,KAAK,SAAS,QAAQjG,CAAK,EAClC,QACpB,CACgB,IAAK,OAAQ,CACTiG,GAAO,KAAK,SAAS,KAAKjG,CAAK,EAC/B,QACpB,CACgB,IAAK,QAAS,CACViG,GAAO,KAAK,SAAS,MAAMjG,CAAK,EAChC,QACpB,CACgB,IAAK,aAAc,CACfiG,GAAO,KAAK,SAAS,WAAWjG,CAAK,EACrC,QACpB,CACgB,IAAK,OAAQ,CACTiG,GAAO,KAAK,SAAS,KAAKjG,CAAK,EAC/B,QACpB,CACgB,IAAK,OAAQ,CACTiG,GAAO,KAAK,SAAS,KAAKjG,CAAK,EAC/B,QACpB,CACgB,IAAK,YAAa,CACdiG,GAAO,KAAK,SAAS,UAAUjG,CAAK,EACpC,QACpB,CACgB,IAAK,OAAQ,CACT,IAAIwG,EAAYxG,EACZsF,EAAO,KAAK,SAAS,KAAKkB,CAAS,EACvC,KAAOxH,EAAI,EAAI4B,EAAO,QAAUA,EAAO5B,EAAI,CAAC,EAAE,OAAS,QACnDwH,EAAY5F,EAAO,EAAE5B,CAAC,EACtBsG,GAAQ;EAAO,KAAK,SAAS,KAAKkB,CAAS,EAE3CvF,EACAgF,GAAO,KAAK,SAAS,UAAU,CAC3B,KAAM,YACN,IAAKX,EACL,KAAMA,EACN,OAAQ,CAAC,CAAE,KAAM,OAAQ,IAAKA,EAAM,KAAMA,EAAM,QAAS,EAAI,CAAE,CAC3F,CAAyB,EAGDW,GAAOX,EAEX,QACpB,CACgB,QAAS,CACL,IAAMT,EAAS,eAAiB7E,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAM6E,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE9C,CACA,CACA,CACQ,OAAOoB,CACf,CAII,YAAYrF,EAAQ6F,EAAW,KAAK,SAAU,CAC1C,IAAIR,EAAM,GACV,QAASjH,EAAI,EAAGA,EAAI4B,EAAO,OAAQ5B,IAAK,CACpC,IAAMqH,EAAWzF,EAAO5B,CAAC,EAEzB,GAAI,KAAK,QAAQ,YAAY,YAAYqH,EAAS,IAAI,EAAG,CACrD,IAAME,EAAM,KAAK,QAAQ,WAAW,UAAUF,EAAS,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAI,EAAIA,CAAQ,EAC5F,GAAIE,IAAQ,IAAS,CAAC,CAAC,SAAU,OAAQ,OAAQ,QAAS,SAAU,KAAM,WAAY,KAAM,MAAO,MAAM,EAAE,SAASF,EAAS,IAAI,EAAG,CAChIJ,GAAOM,GAAO,GACd,QACpB,CACA,CACY,IAAMvG,EAAQqG,EACd,OAAQrG,EAAM,KAAI,CACd,IAAK,SAAU,CACXiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,IAAK,OAAQ,CACTiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,IAAK,OAAQ,CACTiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,IAAK,QAAS,CACViG,GAAOQ,EAAS,MAAMzG,CAAK,EAC3B,KACpB,CACgB,IAAK,SAAU,CACXiG,GAAOQ,EAAS,OAAOzG,CAAK,EAC5B,KACpB,CACgB,IAAK,KAAM,CACPiG,GAAOQ,EAAS,GAAGzG,CAAK,EACxB,KACpB,CACgB,IAAK,WAAY,CACbiG,GAAOQ,EAAS,SAASzG,CAAK,EAC9B,KACpB,CACgB,IAAK,KAAM,CACPiG,GAAOQ,EAAS,GAAGzG,CAAK,EACxB,KACpB,CACgB,IAAK,MAAO,CACRiG,GAAOQ,EAAS,IAAIzG,CAAK,EACzB,KACpB,CACgB,IAAK,OAAQ,CACTiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,QAAS,CACL,IAAM6E,EAAS,eAAiB7E,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAM6E,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE9C,CACA,CACA,CACQ,OAAOoB,CACf,CACA,EC5LaS,EAAN,KAAa,OAAA,CAAAnN,EAAA,eAChB,QACA,MACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,CAClC,CACI,OAAO,iBAAmB,IAAI,IAAI,CAC9B,aACA,cACA,kBACR,CAAK,EAID,WAAWmN,EAAU,CACjB,OAAOA,CACf,CAII,YAAYpL,EAAM,CACd,OAAOA,CACf,CAII,iBAAiBqF,EAAQ,CACrB,OAAOA,CACf,CAII,cAAe,CACX,OAAO,KAAK,MAAQuD,EAAO,IAAMA,EAAO,SAChD,CAII,eAAgB,CACZ,OAAO,KAAK,MAAQgC,EAAQ,MAAQA,EAAQ,WACpD,CACA,ECpCaS,GAAN,KAAa,OAAA,CAAArN,EAAA,eAChB,SAAWD,GAAY,EACvB,QAAU,KAAK,WACf,MAAQ,KAAK,cAAc,EAAI,EAC/B,YAAc,KAAK,cAAc,EAAK,EACtC,OAAS6M,EACT,SAAWpB,EACX,aAAemB,EACf,MAAQ/B,EACR,UAAY5D,EACZ,MAAQmG,EACR,eAAeG,EAAM,CACjB,KAAK,IAAI,GAAGA,CAAI,CACxB,CAII,WAAWjG,EAAQkG,EAAU,CACzB,IAAIC,EAAS,CAAA,EACb,QAAW/G,KAASY,EAEhB,OADAmG,EAASA,EAAO,OAAOD,EAAS,KAAK,KAAM9G,CAAK,CAAC,EACzCA,EAAM,KAAI,CACd,IAAK,QAAS,CACV,IAAMgH,EAAahH,EACnB,QAAWgD,KAAQgE,EAAW,OAC1BD,EAASA,EAAO,OAAO,KAAK,WAAW/D,EAAK,OAAQ8D,CAAQ,CAAC,EAEjE,QAAWrI,KAAOuI,EAAW,KACzB,QAAWhE,KAAQvE,EACfsI,EAASA,EAAO,OAAO,KAAK,WAAW/D,EAAK,OAAQ8D,CAAQ,CAAC,EAGrE,KACpB,CACgB,IAAK,OAAQ,CACT,IAAMG,EAAYjH,EAClB+G,EAASA,EAAO,OAAO,KAAK,WAAWE,EAAU,MAAOH,CAAQ,CAAC,EACjE,KACpB,CACgB,QAAS,CACL,IAAMR,EAAetG,EACjB,KAAK,SAAS,YAAY,cAAcsG,EAAa,IAAI,EACzD,KAAK,SAAS,WAAW,YAAYA,EAAa,IAAI,EAAE,QAASY,GAAgB,CAC7E,IAAMtG,EAAS0F,EAAaY,CAAW,EAAE,KAAK,GAAQ,EACtDH,EAASA,EAAO,OAAO,KAAK,WAAWnG,EAAQkG,CAAQ,CAAC,CACpF,CAAyB,EAEIR,EAAa,SAClBS,EAASA,EAAO,OAAO,KAAK,WAAWT,EAAa,OAAQQ,CAAQ,CAAC,EAE7F,CACA,CAEQ,OAAOC,CACf,CACI,OAAOF,EAAM,CACT,IAAMM,EAAa,KAAK,SAAS,YAAc,CAAE,UAAW,CAAA,EAAI,YAAa,CAAA,CAAE,EAC/E,OAAAN,EAAK,QAASO,GAAS,CAEnB,IAAMC,EAAO,CAAE,GAAGD,CAAI,EA8DtB,GA5DAC,EAAK,MAAQ,KAAK,SAAS,OAASA,EAAK,OAAS,GAE9CD,EAAK,aACLA,EAAK,WAAW,QAASE,GAAQ,CAC7B,GAAI,CAACA,EAAI,KACL,MAAM,IAAI,MAAM,yBAAyB,EAE7C,GAAI,aAAcA,EAAK,CACnB,IAAMC,EAAeJ,EAAW,UAAUG,EAAI,IAAI,EAC9CC,EAEAJ,EAAW,UAAUG,EAAI,IAAI,EAAI,YAAaT,EAAM,CAChD,IAAIN,EAAMe,EAAI,SAAS,MAAM,KAAMT,CAAI,EACvC,OAAIN,IAAQ,KACRA,EAAMgB,EAAa,MAAM,KAAMV,CAAI,GAEhCN,CACvC,EAG4BY,EAAW,UAAUG,EAAI,IAAI,EAAIA,EAAI,QAEjE,CACoB,GAAI,cAAeA,EAAK,CACpB,GAAI,CAACA,EAAI,OAAUA,EAAI,QAAU,SAAWA,EAAI,QAAU,SACtD,MAAM,IAAI,MAAM,6CAA6C,EAEjE,IAAME,EAAWL,EAAWG,EAAI,KAAK,EACjCE,EACAA,EAAS,QAAQF,EAAI,SAAS,EAG9BH,EAAWG,EAAI,KAAK,EAAI,CAACA,EAAI,SAAS,EAEtCA,EAAI,QACAA,EAAI,QAAU,QACVH,EAAW,WACXA,EAAW,WAAW,KAAKG,EAAI,KAAK,EAGpCH,EAAW,WAAa,CAACG,EAAI,KAAK,EAGjCA,EAAI,QAAU,WACfH,EAAW,YACXA,EAAW,YAAY,KAAKG,EAAI,KAAK,EAGrCH,EAAW,YAAc,CAACG,EAAI,KAAK,GAIvE,CACwB,gBAAiBA,GAAOA,EAAI,cAC5BH,EAAW,YAAYG,EAAI,IAAI,EAAIA,EAAI,YAE/D,CAAiB,EACDD,EAAK,WAAaF,GAGlBC,EAAK,SAAU,CACf,IAAMX,EAAW,KAAK,SAAS,UAAY,IAAI1B,EAAU,KAAK,QAAQ,EACtE,QAAW0C,KAAQL,EAAK,SAAU,CAC9B,GAAI,EAAEK,KAAQhB,GACV,MAAM,IAAI,MAAM,aAAagB,CAAI,kBAAkB,EAEvD,GAAI,CAAC,UAAW,QAAQ,EAAE,SAASA,CAAI,EAEnC,SAEJ,IAAMC,EAAeD,EACfE,EAAeP,EAAK,SAASM,CAAY,EACzCH,EAAed,EAASiB,CAAY,EAE1CjB,EAASiB,CAAY,EAAI,IAAIb,IAAS,CAClC,IAAIN,EAAMoB,EAAa,MAAMlB,EAAUI,CAAI,EAC3C,OAAIN,IAAQ,KACRA,EAAMgB,EAAa,MAAMd,EAAUI,CAAI,GAEpCN,GAAO,EACtC,CACA,CACgBc,EAAK,SAAWZ,CAChC,CACY,GAAIW,EAAK,UAAW,CAChB,IAAMQ,EAAY,KAAK,SAAS,WAAa,IAAIrH,EAAW,KAAK,QAAQ,EACzE,QAAWkH,KAAQL,EAAK,UAAW,CAC/B,GAAI,EAAEK,KAAQG,GACV,MAAM,IAAI,MAAM,cAAcH,CAAI,kBAAkB,EAExD,GAAI,CAAC,UAAW,QAAS,OAAO,EAAE,SAASA,CAAI,EAE3C,SAEJ,IAAMI,EAAgBJ,EAChBK,EAAgBV,EAAK,UAAUS,CAAa,EAC5CE,EAAgBH,EAAUC,CAAa,EAG7CD,EAAUC,CAAa,EAAI,IAAIhB,IAAS,CACpC,IAAIN,EAAMuB,EAAc,MAAMF,EAAWf,CAAI,EAC7C,OAAIN,IAAQ,KACRA,EAAMwB,EAAc,MAAMH,EAAWf,CAAI,GAEtCN,CAC/B,CACA,CACgBc,EAAK,UAAYO,CACjC,CAEY,GAAIR,EAAK,MAAO,CACZ,IAAMY,EAAQ,KAAK,SAAS,OAAS,IAAItB,EACzC,QAAWe,KAAQL,EAAK,MAAO,CAC3B,GAAI,EAAEK,KAAQO,GACV,MAAM,IAAI,MAAM,SAASP,CAAI,kBAAkB,EAEnD,GAAI,CAAC,UAAW,OAAO,EAAE,SAASA,CAAI,EAElC,SAEJ,IAAMQ,EAAYR,EACZS,EAAYd,EAAK,MAAMa,CAAS,EAChCE,EAAWH,EAAMC,CAAS,EAC5BvB,EAAO,iBAAiB,IAAIe,CAAI,EAEhCO,EAAMC,CAAS,EAAKG,GAAQ,CACxB,GAAI,KAAK,SAAS,MACd,OAAO,QAAQ,QAAQF,EAAU,KAAKF,EAAOI,CAAG,CAAC,EAAE,KAAK7B,GAC7C4B,EAAS,KAAKH,EAAOzB,CAAG,CAClC,EAEL,IAAMA,EAAM2B,EAAU,KAAKF,EAAOI,CAAG,EACrC,OAAOD,EAAS,KAAKH,EAAOzB,CAAG,CAC3D,EAIwByB,EAAMC,CAAS,EAAI,IAAIpB,IAAS,CAC5B,IAAIN,EAAM2B,EAAU,MAAMF,EAAOnB,CAAI,EACrC,OAAIN,IAAQ,KACRA,EAAM4B,EAAS,MAAMH,EAAOnB,CAAI,GAE7BN,CACnC,CAEA,CACgBc,EAAK,MAAQW,CAC7B,CAEY,GAAIZ,EAAK,WAAY,CACjB,IAAMiB,EAAa,KAAK,SAAS,WAC3BC,EAAiBlB,EAAK,WAC5BC,EAAK,WAAa,SAAUrH,EAAO,CAC/B,IAAI+G,EAAS,CAAA,EACb,OAAAA,EAAO,KAAKuB,EAAe,KAAK,KAAMtI,CAAK,CAAC,EACxCqI,IACAtB,EAASA,EAAO,OAAOsB,EAAW,KAAK,KAAMrI,CAAK,CAAC,GAEhD+G,CAC3B,CACA,CACY,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGM,CAAI,CACvD,CAAS,EACM,IACf,CACI,WAAWvN,EAAK,CACZ,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGA,CAAG,EACnC,IACf,CACI,MAAM2G,EAAKD,EAAS,CAChB,OAAO2D,EAAO,IAAI1D,EAAKD,GAAW,KAAK,QAAQ,CACvD,CACI,OAAOI,EAAQJ,EAAS,CACpB,OAAO2F,EAAQ,MAAMvF,EAAQJ,GAAW,KAAK,QAAQ,CAC7D,CACI,cAAc+H,EAAW,CAsDrB,OApDchP,EAAA,CAACkH,EAAKD,IAAY,CAC5B,IAAMgI,EAAU,CAAE,GAAGhI,CAAO,EACtB1G,EAAM,CAAE,GAAG,KAAK,SAAU,GAAG0O,CAAO,EACpCC,EAAa,KAAK,QAAQ,CAAC,CAAC3O,EAAI,OAAQ,CAAC,CAACA,EAAI,KAAK,EAEzD,GAAI,KAAK,SAAS,QAAU,IAAQ0O,EAAQ,QAAU,GAClD,OAAOC,EAAW,IAAI,MAAM,oIAAoI,CAAC,EAGrK,GAAI,OAAOhI,EAAQ,KAAeA,IAAQ,KACtC,OAAOgI,EAAW,IAAI,MAAM,gDAAgD,CAAC,EAEjF,GAAI,OAAOhI,GAAQ,SACf,OAAOgI,EAAW,IAAI,MAAM,wCACtB,OAAO,UAAU,SAAS,KAAKhI,CAAG,EAAI,mBAAmB,CAAC,EAEhE3G,EAAI,QACJA,EAAI,MAAM,QAAUA,EACpBA,EAAI,MAAM,MAAQyO,GAEtB,IAAM3I,EAAQ9F,EAAI,MAAQA,EAAI,MAAM,aAAY,EAAMyO,EAAYpE,EAAO,IAAMA,EAAO,UAChFuE,EAAS5O,EAAI,MAAQA,EAAI,MAAM,cAAa,EAAMyO,EAAYpC,EAAQ,MAAQA,EAAQ,YAC5F,GAAIrM,EAAI,MACJ,OAAO,QAAQ,QAAQA,EAAI,MAAQA,EAAI,MAAM,WAAW2G,CAAG,EAAIA,CAAG,EAC7D,KAAKA,GAAOb,EAAMa,EAAK3G,CAAG,CAAC,EAC3B,KAAK8G,GAAU9G,EAAI,MAAQA,EAAI,MAAM,iBAAiB8G,CAAM,EAAIA,CAAM,EACtE,KAAKA,GAAU9G,EAAI,WAAa,QAAQ,IAAI,KAAK,WAAW8G,EAAQ9G,EAAI,UAAU,CAAC,EAAE,KAAK,IAAM8G,CAAM,EAAIA,CAAM,EAChH,KAAKA,GAAU8H,EAAO9H,EAAQ9G,CAAG,CAAC,EAClC,KAAKyB,GAAQzB,EAAI,MAAQA,EAAI,MAAM,YAAYyB,CAAI,EAAIA,CAAI,EAC3D,MAAMkN,CAAU,EAEzB,GAAI,CACI3O,EAAI,QACJ2G,EAAM3G,EAAI,MAAM,WAAW2G,CAAG,GAElC,IAAIG,EAAShB,EAAMa,EAAK3G,CAAG,EACvBA,EAAI,QACJ8G,EAAS9G,EAAI,MAAM,iBAAiB8G,CAAM,GAE1C9G,EAAI,YACJ,KAAK,WAAW8G,EAAQ9G,EAAI,UAAU,EAE1C,IAAIyB,EAAOmN,EAAO9H,EAAQ9G,CAAG,EAC7B,OAAIA,EAAI,QACJyB,EAAOzB,EAAI,MAAM,YAAYyB,CAAI,GAE9BA,CACvB,OACmBoN,EAAG,CACN,OAAOF,EAAWE,CAAC,CACnC,CACA,EAnDsB,QAqDtB,CACI,QAAQC,EAAQC,EAAO,CACnB,OAAQF,GAAM,CAEV,GADAA,EAAE,SAAW;2DACTC,EAAQ,CACR,IAAME,EAAM,iCACNhN,EAAO6M,EAAE,QAAU,GAAI,EAAI,EAC3B,SACN,OAAIE,EACO,QAAQ,QAAQC,CAAG,EAEvBA,CACvB,CACY,GAAID,EACA,OAAO,QAAQ,OAAOF,CAAC,EAE3B,MAAMA,CAClB,CACA,CACA,ECtTMI,EAAiB,IAAInC,GACpB,SAASoC,EAAOvI,EAAK3G,EAAK,CAC7B,OAAOiP,EAAe,MAAMtI,EAAK3G,CAAG,CACxC,CAFgBP,EAAAyP,EAAA,UAQhBA,EAAO,QACHA,EAAO,WAAa,SAAUxI,EAAS,CACnC,OAAAuI,EAAe,WAAWvI,CAAO,EACjCwI,EAAO,SAAWD,EAAe,SACjCtP,GAAeuP,EAAO,QAAQ,EACvBA,CACf,EAIAA,EAAO,YAAc1P,GACrB0P,EAAO,SAAWxP,EAIlBwP,EAAO,IAAM,YAAanC,EAAM,CAC5B,OAAAkC,EAAe,IAAI,GAAGlC,CAAI,EAC1BmC,EAAO,SAAWD,EAAe,SACjCtP,GAAeuP,EAAO,QAAQ,EACvBA,CACX,EAIAA,EAAO,WAAa,SAAUpI,EAAQkG,EAAU,CAC5C,OAAOiC,EAAe,WAAWnI,EAAQkG,CAAQ,CACrD,EAQAkC,EAAO,YAAcD,EAAe,YAIpCC,EAAO,OAAS7C,EAChB6C,EAAO,OAAS7C,EAAQ,MACxB6C,EAAO,SAAWjE,EAClBiE,EAAO,aAAe9C,EACtB8C,EAAO,MAAQ7E,EACf6E,EAAO,MAAQ7E,EAAO,IACtB6E,EAAO,UAAYzI,EACnByI,EAAO,MAAQtC,EACfsC,EAAO,MAAQA,EACH,IAACxI,GAAUwI,EAAO,QACjBC,GAAaD,EAAO,WACpBE,GAAMF,EAAO,IACbX,GAAaW,EAAO,WACpBG,GAAcH,EAAO,YAEtB,IAACI,GAASC,EAAQ,MACjBC,GAAQC,EAAO,IC7D5B,SAASC,GAAmBC,EAAkB,CAAE,iBAAAC,CAAiB,EAA0B,CAIzF,IAAMC,EAFYF,EAAS,QAAQ,UAAW;AAAA,CAAI,EAER,QAAQ,UAAW;AAAA,CAAI,EAE3DG,EAAqBC,GAAOF,CAAuB,EACzD,OAAID,IAAqB,GAChBE,EAAmB,QAAQ,KAAM,QAAQ,EAE3CA,CACT,CAXSE,EAAAN,GAAA,sBAgBF,SAASO,GAAgBN,EAAkBO,EAAwB,CAAC,EAAmB,CAC5F,IAAMC,EAAuBT,GAAmBC,EAAUO,CAAM,EAC1DE,EAAQC,EAAO,MAAMF,CAAoB,EACzCG,EAAwB,CAAC,CAAC,CAAC,EAC7BC,EAAc,EAElB,SAASC,EAAYC,EAAmBC,EAA+B,SAAU,CAC3ED,EAAK,OAAS,OACEA,EAAK,KAAK,MAAM;AAAA,CAAI,EAC5B,QAAQ,CAACE,EAAUC,IAAU,CACjCA,IAAU,IACZL,IACAD,EAAM,KAAK,CAAC,CAAC,GAEfK,EAAS,MAAM,GAAG,EAAE,QAASE,GAAS,CACpCA,EAAOA,EAAK,QAAQ,SAAU,GAAG,EAC7BA,GACFP,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASM,EAAM,KAAMH,CAAW,CAAC,CAE/D,CAAC,CACH,CAAC,EACQD,EAAK,OAAS,UAAYA,EAAK,OAAS,KACjDA,EAAK,OAAO,QAASK,GAAgB,CACnCN,EAAYM,EAA4BL,EAAK,IAAI,CACnD,CAAC,EACQA,EAAK,OAAS,QACvBH,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASE,EAAK,KAAM,KAAM,QAAS,CAAC,CAElE,CAtBS,OAAAT,EAAAQ,EAAA,eAwBTJ,EAAM,QAASW,GAAa,CACtBA,EAAS,OAAS,YACpBA,EAAS,QAAQ,QAASD,GAAgB,CACxCN,EAAYM,CAA0B,CACxC,CAAC,EACQC,EAAS,OAAS,QAC3BT,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASQ,EAAS,KAAM,KAAM,QAAS,CAAC,CAEtE,CAAC,EAEMT,CACT,CAzCgBN,EAAAC,GAAA,mBA2CT,SAASe,GAAerB,EAAkB,CAAE,iBAAAC,CAAiB,EAAmB,CAAC,EAAG,CACzF,IAAMQ,EAAQC,EAAO,MAAMV,CAAQ,EAEnC,SAASsB,EAAOR,EAAqB,CACnC,OAAIA,EAAK,OAAS,OACZb,IAAqB,GAChBa,EAAK,KAAK,QAAQ,QAAS,OAAO,EAAE,QAAQ,KAAM,QAAQ,EAE5DA,EAAK,KAAK,QAAQ,QAAS,OAAO,EAChCA,EAAK,OAAS,SAChB,WAAWA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,YAC1CR,EAAK,OAAS,KAChB,OAAOA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,QACtCR,EAAK,OAAS,YAChB,MAAMA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,OACrCR,EAAK,OAAS,QAChB,GACEA,EAAK,OAAS,OAChB,GAAGA,EAAK,IAAI,GACVA,EAAK,OAAS,SAChBA,EAAK,KAEP,yBAAyBA,EAAK,IAAI,EAC3C,CApBS,OAAAT,EAAAiB,EAAA,UAsBFb,EAAM,IAAIa,CAAM,EAAE,KAAK,EAAE,CAClC,CA1BgBjB,EAAAgB,GAAA,kBChET,SAASE,GAAiBC,EAAwB,CACvD,OAAI,KAAK,UACA,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,QAAQA,CAAI,CAAC,EAAE,IAAKC,GAAMA,EAAE,OAAO,EAE9D,CAAC,GAAGD,CAAI,CACjB,CALgBE,EAAAH,GAAA,oBAgCT,SAASI,GACdC,EACAC,EAC8B,CAC9B,IAAMC,EAAaC,GAAiBF,EAAK,OAAO,EAChD,OAAOG,GAA6BJ,EAAU,CAAC,EAAGE,EAAYD,EAAK,IAAI,CACzE,CANgBI,EAAAN,GAAA,uBAQhB,SAASK,GACPJ,EACAM,EACAC,EACAC,EAC8B,CAC9B,GAAID,EAAe,SAAW,EAC5B,MAAO,CACL,CAAE,QAASD,EAAU,KAAK,EAAE,EAAG,KAAAE,CAAK,EACpC,CAAE,QAAS,GAAI,KAAAA,CAAK,CACtB,EAEF,GAAM,CAACC,EAAU,GAAGC,CAAI,EAAIH,EACtBI,EAAU,CAAC,GAAGL,EAAWG,CAAQ,EACvC,OAAIT,EAAS,CAAC,CAAE,QAASW,EAAQ,KAAK,EAAE,EAAG,KAAAH,CAAK,CAAC,CAAC,EACzCJ,GAA6BJ,EAAUW,EAASD,EAAMF,CAAI,GAE/DF,EAAU,SAAW,GAAKG,IAE5BH,EAAU,KAAKG,CAAQ,EACvBF,EAAe,MAAM,GAEhB,CACL,CAAE,QAASD,EAAU,KAAK,EAAE,EAAG,KAAAE,CAAK,EACpC,CAAE,QAASD,EAAe,KAAK,EAAE,EAAG,KAAAC,CAAK,CAC3C,EACF,CA1BSH,EAAAD,GAAA,gCAkCF,SAASQ,GACdC,EACAb,EACgB,CAChB,GAAIa,EAAK,KAAK,CAAC,CAAE,QAAAC,CAAQ,IAAMA,EAAQ,SAAS;AAAA,CAAI,CAAC,EACnD,MAAM,IAAI,MAAM,2DAA2D,EAE7E,OAAOC,GAA6BF,EAAMb,CAAQ,CACpD,CARgBK,EAAAO,GAAA,uBAUhB,SAASG,GACPC,EACAhB,EACAiB,EAAwB,CAAC,EACzBC,EAAwB,CAAC,EACT,CAEhB,GAAIF,EAAM,SAAW,EAEnB,OAAIE,EAAQ,OAAS,GACnBD,EAAM,KAAKC,CAAO,EAEbD,EAAM,OAAS,EAAIA,EAAQ,CAAC,EAErC,IAAIE,EAAS,GACTH,EAAM,CAAC,EAAE,UAAY,MACvBG,EAAS,IACTH,EAAM,MAAM,GAEd,IAAMI,EAAyBJ,EAAM,MAAM,GAAK,CAAE,QAAS,IAAK,KAAM,QAAS,EACzEK,EAAiC,CAAC,GAAGH,CAAO,EAMlD,GALIC,IAAW,IACbE,EAAiB,KAAK,CAAE,QAASF,EAAQ,KAAM,QAAS,CAAC,EAE3DE,EAAiB,KAAKD,CAAQ,EAE1BpB,EAASqB,CAAgB,EAE3B,OAAON,GAA6BC,EAAOhB,EAAUiB,EAAOI,CAAgB,EAI9E,GAAIH,EAAQ,OAAS,EAEnBD,EAAM,KAAKC,CAAO,EAClBF,EAAM,QAAQI,CAAQ,UACbA,EAAS,QAAS,CAE3B,GAAM,CAACP,EAAMH,CAAI,EAAIX,GAAoBC,EAAUoB,CAAQ,EAC3DH,EAAM,KAAK,CAACJ,CAAI,CAAC,EACbH,EAAK,SACPM,EAAM,QAAQN,CAAI,CAEtB,CACA,OAAOK,GAA6BC,EAAOhB,EAAUiB,CAAK,CAC5D,CA7CSZ,EAAAU,GAAA,gCC1ET,SAASO,GAAWC,EAAKC,EAAS,CAC5BA,GACFD,EAAI,KAAK,QAASC,CAAO,CAE7B,CAJSC,EAAAH,GAAA,cAMT,eAAeI,GAAYC,EAASC,EAAMC,EAAOC,EAASC,EAAgB,GAAO,CAC/E,IAAMC,EAAKL,EAAQ,OAAO,eAAe,EAGzCK,EAAG,KAAK,QAAS,GAAG,GAAKH,CAAK,IAAI,EAClCG,EAAG,KAAK,SAAU,GAAG,GAAKH,CAAK,IAAI,EAEnC,IAAMI,EAAMD,EAAG,OAAO,WAAW,EAC7BE,EAAQN,EAAK,MACbA,EAAK,OAASO,GAASP,EAAK,KAAK,IACnCM,EAAQ,MAAME,GAAYR,EAAK,MAAM,QAAQS,GAAO,eAAgB;AAAA,CAAI,EAAGC,GAAU,CAAC,GAExF,IAAMC,EAAaX,EAAK,OAAS,YAAc,YACzCY,EAAOP,EAAI,OAAO,MAAM,EAC9BO,EAAK,KAAKN,CAAK,EACfZ,GAAWkB,EAAMZ,EAAK,UAAU,EAChCY,EAAK,KAAK,QAAS,GAAGD,CAAU,IAAIT,CAAO,EAAE,EAE7CR,GAAWW,EAAKL,EAAK,UAAU,EAC/BK,EAAI,MAAM,UAAW,YAAY,EACjCA,EAAI,MAAM,cAAe,QAAQ,EACjCA,EAAI,MAAM,cAAe,KAAK,EAC9BA,EAAI,MAAM,YAAaJ,EAAQ,IAAI,EACnCI,EAAI,MAAM,aAAc,QAAQ,EAChCA,EAAI,KAAK,QAAS,8BAA8B,EAC5CF,GACFE,EAAI,KAAK,QAAS,UAAU,EAG9B,IAAIQ,EAAOR,EAAI,KAAK,EAAE,sBAAsB,EAC5C,OAAIQ,EAAK,QAAUZ,IACjBI,EAAI,MAAM,UAAW,OAAO,EAC5BA,EAAI,MAAM,cAAe,cAAc,EACvCA,EAAI,MAAM,QAASJ,EAAQ,IAAI,EAC/BY,EAAOR,EAAI,KAAK,EAAE,sBAAsB,GAMnCD,EAAG,KAAK,CACjB,CAzCeP,EAAAC,GAAA,eAmDf,SAASgB,GAAYC,EAAkBC,EAAmBC,EAAoB,CAC5E,OAAOF,EACJ,OAAO,OAAO,EACd,KAAK,QAAS,kBAAkB,EAChC,KAAK,IAAK,CAAC,EACX,KAAK,IAAKC,EAAYC,EAAa,GAAM,IAAI,EAC7C,KAAK,KAAMA,EAAa,IAAI,CACjC,CAPSpB,EAAAiB,GAAA,eAST,SAASI,GAAmBC,EAAiBF,EAAoBG,EAA4B,CAC3F,IAAMC,EAAcF,EAAW,OAAO,MAAM,EACtCG,EAAWR,GAAYO,EAAa,EAAGJ,CAAU,EACvDM,GAA2BD,EAAUF,CAAI,EACzC,IAAMI,EAAaF,EAAS,KAAK,EAAE,sBAAsB,EACzD,OAAAD,EAAY,OAAO,EACZG,CACT,CAPS3B,EAAAqB,GAAA,sBASF,SAASO,GACdN,EACAF,EACAS,EACqB,CACrB,IAAML,EAA6BF,EAAW,OAAO,MAAM,EACrDG,EAA2BR,GAAYO,EAAa,EAAGJ,CAAU,EACvEM,GAA2BD,EAAU,CAAC,CAAE,QAASI,EAAM,KAAM,QAAS,CAAC,CAAC,EACxE,IAAMC,EAAqCL,EAAS,KAAK,GAAG,sBAAsB,EAClF,OAAIK,GACFN,EAAY,OAAO,EAEdM,CACT,CAbgB9B,EAAA4B,GAAA,0BAwBhB,SAASG,GACP3B,EACA4B,EACAC,EACA3B,EAAgB,GAChB,CAEA,IAAM4B,EAAaF,EAAE,OAAO,GAAG,EACzBG,EAAMD,EAAW,OAAO,MAAM,EAAE,KAAK,QAAS,YAAY,EAAE,KAAK,QAAS,cAAc,EACxFhB,EAAcgB,EAAW,OAAO,MAAM,EAAE,KAAK,IAAK,OAAO,EAC3Df,EAAY,EAChB,QAAWI,KAAQU,EAAgB,CAKjC,IAAMG,EAAapC,EAACuB,GAClBF,GAAmBa,EAAY,IAAYX,CAAI,GAAKnB,EADnC,cAEbiC,EAAkBD,EAAWb,CAAI,EAAI,CAACA,CAAI,EAAIe,GAAoBf,EAAMa,CAAU,EAExF,QAAWG,KAAgBF,EAAiB,CAC1C,IAAMG,EAAQvB,GAAYC,EAAaC,EAAW,GAAU,EAC5DO,GAA2Bc,EAAOD,CAAY,EAC9CpB,GACF,CACF,CACA,GAAIb,EAAe,CACjB,IAAMU,EAAOE,EAAY,KAAK,EAAE,QAAQ,EAClCuB,EAAU,EAChB,OAAAN,EACG,KAAK,IAAKnB,EAAK,EAAIyB,CAAO,EAC1B,KAAK,IAAKzB,EAAK,EAAIyB,CAAO,EAC1B,KAAK,QAASzB,EAAK,MAAQ,EAAIyB,CAAO,EACtC,KAAK,SAAUzB,EAAK,OAAS,EAAIyB,CAAO,EAEpCP,EAAW,KAAK,CACzB,KACE,QAAOhB,EAAY,KAAK,CAE5B,CAvCSlB,EAAA+B,GAAA,uBAgDT,SAASL,GAA2Bc,EAAYE,EAA6B,CAC3EF,EAAM,KAAK,EAAE,EAEbE,EAAY,QAAQ,CAACC,EAAMC,IAAU,CACnC,IAAMC,EAAaL,EAChB,OAAO,OAAO,EACd,KAAK,aAAcG,EAAK,OAAS,KAAO,SAAW,QAAQ,EAC3D,KAAK,QAAS,kBAAkB,EAChC,KAAK,cAAeA,EAAK,OAAS,SAAW,OAAS,QAAQ,EAC7DC,IAAU,EACZC,EAAW,KAAKF,EAAK,OAAO,EAG5BE,EAAW,KAAK,IAAMF,EAAK,OAAO,CAEtC,CAAC,CACH,CAhBS3C,EAAA0B,GAAA,8BAuBT,eAAsBoB,GAAqBjB,EAAc,CACvD,IAAMkB,EAAyC,CAAC,EAEhDlB,EAAK,QAAQ,4BAA6B,CAACmB,EAAWC,EAAQC,KAC5DH,EAAoB,MACjB,SAAY,CACX,IAAMI,EAAqB,GAAGF,CAAM,IAAIC,CAAQ,GAChD,OAAI,MAAME,GAAgBD,CAAkB,EACnC,MAAME,GAAWF,EAAoB,OAAW,CAAE,MAAO,YAAa,CAAC,EAEvE,aAAaG,GAAaN,CAAS,EAAE,QAAQ,IAAK,GAAG,CAAC,QAEjE,GAAG,CACL,EACOA,EACR,EAED,IAAMO,EAAe,MAAM,QAAQ,IAAIR,CAAmB,EAE1D,OAAOlB,EAAK,QAAQ,4BAA6B,IAAM0B,EAAa,MAAM,GAAK,EAAE,CACnF,CApBsBvD,EAAA8C,GAAA,wBAwBf,IAAMU,GAAaxD,EAAA,MACxByD,EACA5B,EAAO,GACP,CACE,MAAA6B,EAAQ,GACR,QAAAC,EAAU,GACV,QAAAtD,EAAU,GACV,cAAAuD,EAAgB,GAChB,OAAAC,EAAS,GACT,MAAAzD,EAAQ,IACR,iBAAA0D,EAAmB,EACrB,EAAI,CAAC,EACLC,IACG,CAYH,GAXAC,EAAI,MACF,iBACAnC,EACA6B,EACAC,EACAtD,EACAuD,EACAC,EACA,qBACAC,CACF,EACIF,EAAe,CAGjB,IAAMK,EAAWC,GAAerC,EAAMkC,CAAM,EACtCI,EAAsB,MAAMrB,GAAqBsB,GAAeH,CAAQ,CAAC,EAGzEI,EAAgBxC,EAAK,QAAQ,QAAS,IAAI,EAE1C1B,EAAO,CACX,OAAA0D,EACA,MAAOnD,GAASmB,CAAI,EAAIwC,EAAgBF,EACxC,WAAYT,EAAM,QAAQ,QAAS,QAAQ,CAC7C,EAEA,OADmB,MAAMzD,GAAYwD,EAAItD,EAAMC,EAAOC,EAASyD,CAAgB,CAEjF,KAAO,CAEL,IAAMQ,EAAazC,EAAK,QAAQ,cAAe,OAAO,EAChDI,EAAiBsC,GAAgBD,EAAW,QAAQ,OAAQ,OAAO,EAAGP,CAAM,EAC5ES,EAAWzC,GACf3B,EACAqD,EACAxB,EACAJ,EAAOiC,EAAmB,EAC5B,EACA,GAAID,EAAQ,CACN,UAAU,KAAKH,CAAK,IACtBA,EAAQA,EAAM,QAAQ,UAAW,YAAY,GAG/C,IAAMe,EAAqBf,EACxB,QAAQ,kBAAmB,EAAE,EAC7B,QAAQ,wBAAyB,EAAE,EACnC,QAAQ,gBAAiB,EAAE,EAC3B,QAAQ,UAAW,OAAO,EAC7BgB,EAAOF,CAAQ,EAAE,KAAK,QAASC,CAAkB,CAEnD,KAAO,CAKL,IAAME,EAAqBjB,EACxB,QAAQ,kBAAmB,EAAE,EAC7B,QAAQ,wBAAyB,EAAE,EACnC,QAAQ,gBAAiB,EAAE,EAC3B,QAAQ,eAAgB,OAAO,EAClCgB,EAAOF,CAAQ,EACZ,OAAO,MAAM,EACb,KAAK,QAASG,EAAmB,QAAQ,eAAgB,OAAO,CAAC,EAGpE,IAAMC,EAAqBlB,EACxB,QAAQ,kBAAmB,EAAE,EAC7B,QAAQ,wBAAyB,EAAE,EACnC,QAAQ,gBAAiB,EAAE,EAC3B,QAAQ,UAAW,OAAO,EAC7BgB,EAAOF,CAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,QAASI,CAAkB,CAClE,CACA,OAAOJ,CACT,CACF,EAvF0B", "names": ["require_ms", "__commonJSMin", "exports", "module", "s", "m", "h", "d", "w", "y", "val", "options", "type", "parse", "fmtLong", "fmtShort", "str", "match", "n", "__name", "ms", "msAbs", "plural", "name", "isPlural", "require_common", "__commonJSMin", "exports", "module", "setup", "env", "createDebug", "coerce", "disable", "enable", "enabled", "destroy", "key", "selectColor", "namespace", "hash", "i", "__name", "prevTime", "enableOverride", "namespacesCache", "enabledCache", "debug", "args", "self", "curr", "ms", "index", "match", "format", "formatter", "val", "extend", "v", "delimiter", "newDebug", "namespaces", "split", "ns", "matchesTemplate", "search", "template", "searchIndex", "templateIndex", "starIndex", "matchIndex", "name", "skip", "require_browser", "__commonJSMin", "exports", "module", "formatArgs", "save", "load", "useColors", "localstorage", "warned", "m", "__name", "args", "c", "index", "lastC", "match", "namespaces", "r", "formatters", "v", "error", "defaultIconDimensions", "defaultIconTransformations", "defaultIconProps", "defaultExtendedIconProps", "defaultIconSizeCustomisations", "defaultIconCustomisations", "defaultIconTransformations", "stringToIcon", "__name", "value", "validate", "allowSimpleName", "provider", "colonSeparated", "name2", "prefix", "result", "validateIconName", "name", "dashSeparated", "icon", "mergeIconTransformations", "obj1", "obj2", "result", "rotate", "__name", "mergeIconData", "parent", "child", "result", "mergeIconTransformations", "key", "defaultExtendedIconProps", "defaultIconTransformations", "__name", "getIconsTree", "data", "names", "icons", "aliases", "resolved", "resolve", "name", "parent", "value", "__name", "internalGetIconData", "data", "name", "tree", "icons", "aliases", "currentProps", "parse", "name2", "mergeIconData", "__name", "getIconData", "getIconsTree", "unitsSplit", "unitsTest", "calculateSize", "size", "ratio", "precision", "oldParts", "newParts", "code", "isNumber", "num", "__name", "splitSVGDefs", "content", "tag", "defs", "index", "start", "end", "endEnd", "__name", "mergeDefsAndContent", "wrapSVGContent", "body", "split", "isUnsetKeyword", "__name", "value", "iconToSVG", "icon", "customisations", "fullIcon", "defaultIconProps", "fullCustomisations", "defaultIconCustomisations", "box", "body", "props", "transformations", "hFlip", "vFlip", "rotation", "tempValue", "wrapSVGContent", "customisationsWidth", "customisationsHeight", "boxWidth", "boxHeight", "width", "height", "calculateSize", "attributes", "setAttr", "prop", "viewBox", "regex", "randomPrefix", "counter", "replaceIDs", "body", "prefix", "ids", "match", "suffix", "id", "newID", "escapedID", "__name", "iconToHTML", "body", "attributes", "renderAttribsHTML", "attr", "__name", "import_debug", "unknownIcon", "iconsStore", "loaderStore", "registerIconPacks", "__name", "iconLoaders", "icon<PERSON><PERSON><PERSON>", "log", "getRegisteredIconData", "iconName", "fallbackPrefix", "data", "stringToIcon", "prefix", "icons", "loader", "e", "iconData", "getIconData", "isIconAvailable", "getIconSVG", "customisations", "extraAttributes", "renderData", "iconToSVG", "iconToHTML", "replaceIDs", "dedent", "templ", "values", "_i", "strings", "indentLengths", "arr", "str", "matches", "match", "_a", "_b", "pattern_1", "string", "value", "i", "endentations", "endentation", "indentedValue", "__name", "_getDefaults", "__name", "_defaults", "changeDefaults", "newDefaults", "noopTest", "edit", "regex", "opt", "source", "obj", "name", "val", "valSource", "other", "bull", "indent", "newline", "blockCode", "fences", "hr", "heading", "bullet", "lheadingCore", "lheading", "lheadingGfm", "_paragraph", "blockText", "_blockLabel", "def", "list", "_tag", "_comment", "html", "paragraph", "blockquote", "blockNormal", "gfmTable", "blockGfm", "blockPedantic", "escape", "inlineCode", "br", "inlineText", "_punctuation", "_punctuationOrSpace", "_notPunctuationOrSpace", "punctuation", "_punctuationGfmStrongEm", "_punctuationOrSpaceGfmStrongEm", "_notPunctuationOrSpaceGfmStrongEm", "blockSkip", "emStrongLDelimCore", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongLDelimGfm", "emStrongRDelimAstCore", "emStrongRDelim<PERSON>t", "emStrongRDelimAstGfm", "emStrongRDelimUnd", "anyPunctuation", "autolink", "_inlineComment", "tag", "_inlineLabel", "link", "reflink", "nolink", "reflinkSearch", "inlineNormal", "inlinePedantic", "inlineGfm", "inlineBreaks", "block", "inline", "escapeReplacements", "getEscapeReplacement", "ch", "encode", "cleanUrl", "href", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "i", "rtrim", "c", "invert", "l", "suffLen", "findClosingBracket", "b", "level", "outputLink", "cap", "raw", "lexer", "rules", "title", "text", "token", "indentCodeCompensation", "matchIndentToCode", "indentToCode", "node", "matchIndentInNode", "indentInNode", "_Tokenizer", "options", "src", "trimmed", "lines", "tokens", "inBlockquote", "currentLines", "currentRaw", "currentText", "top", "lastToken", "oldToken", "newText", "newToken", "isordered", "itemRegex", "endsWithBlankLine", "endEarly", "itemContents", "line", "t", "nextLine", "blankLine", "nextBulletRegex", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "htmlBeginRegex", "rawLine", "nextLineWithoutTabs", "istask", "ischecked", "lastItem", "spacers", "hasMultipleLineBreaks", "headers", "aligns", "rows", "item", "align", "cell", "trimmedUrl", "rtrimSlash", "lastParenIndex", "linkLen", "links", "linkString", "maskedSrc", "prevChar", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "prevCapZero", "_<PERSON>er", "__<PERSON><PERSON>", "next", "lastParagraphClipped", "extTokenizer", "cutSrc", "startIndex", "tempSrc", "tempStart", "getStartIndex", "errMsg", "keepPrevChar", "_Renderer", "lang", "langString", "code", "depth", "ordered", "start", "body", "j", "type", "startAttr", "itemBody", "checkbox", "checked", "header", "k", "content", "cleanHref", "out", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "__<PERSON><PERSON><PERSON>", "anyToken", "genericToken", "ret", "textToken", "renderer", "_Hooks", "markdown", "Marked", "args", "callback", "values", "tableToken", "listToken", "childTokens", "extensions", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "extLevel", "prop", "rendererProp", "rendererFunc", "tokenizer", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooks", "hooksProp", "hooksFunc", "prevHook", "arg", "walkTokens", "packWalktokens", "blockType", "origOpt", "throwError", "parser", "e", "silent", "async", "msg", "markedInstance", "marked", "setOptions", "use", "parseInline", "parser", "_<PERSON><PERSON>r", "lexer", "_<PERSON>er", "preprocessMarkdown", "markdown", "markdownAutoWrap", "withoutMultipleNewlines", "withoutExtraSpaces", "dedent", "__name", "markdownToLines", "config", "preprocessedMarkdown", "nodes", "marked", "lines", "currentLine", "processNode", "node", "parentType", "textLine", "index", "word", "contentNode", "treeNode", "markdownToHTML", "output", "splitTextToChars", "text", "s", "__name", "splitWordToFitWidth", "checkFit", "word", "characters", "splitTextToChars", "splitWordToFitWidthRecursion", "__name", "usedChars", "remainingChars", "type", "nextChar", "rest", "newWord", "splitLineToFitWidth", "line", "content", "splitLineToFitWidthRecursion", "words", "lines", "newLine", "joiner", "nextWord", "lineWithNextWord", "applyStyle", "dom", "styleFn", "__name", "addHtmlSpan", "element", "node", "width", "classes", "addBackground", "fo", "div", "label", "hasKatex", "renderKatex", "common_default", "getConfig", "labelClass", "span", "bbox", "createTspan", "textElement", "lineIndex", "lineHeight", "computeWidthOfText", "parentNode", "line", "testElement", "testSpan", "updateTextContentAndStyles", "textLength", "computeDimensionOfText", "text", "textDimension", "createFormattedText", "g", "structuredText", "labelGroup", "bkg", "checkWidth", "linesUnderWidth", "splitLineToFitWidth", "preparedLine", "tspan", "padding", "wrappedLine", "word", "index", "innerTspan", "replaceIconSubstring", "pendingReplacements", "fullMatch", "prefix", "iconName", "registeredIconName", "isIconAvailable", "getIconSVG", "sanitizeText", "replacements", "createText", "el", "style", "isTitle", "useHtmlLabels", "isNode", "addSvgBackground", "config", "log", "htmlText", "markdownToHTML", "decodedReplacedText", "decodeEntities", "inputForKatex", "sanitizeBR", "markdownToLines", "svgLabel", "nodeLabelTextStyle", "select_default", "edgeLabelRectStyle", "edgeLabelTextStyle"]}