#!/usr/bin/env node

/**
 * 色彩系统WCAG 2.1 AA合规性验证脚本
 * Color System WCAG 2.1 AA Compliance Validation Script
 */

// 简化的验证脚本，直接包含验证逻辑
import fs from 'fs'
import path from 'path'

// 内联验证函数
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

function getLuminance(rgb) {
  const { r, g, b } = rgb
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
}

function getContrastRatio(color1, color2) {
  const rgb1 = hexToRgb(color1)
  const rgb2 = hexToRgb(color2)

  if (!rgb1 || !rgb2) return 0

  const lum1 = getLuminance(rgb1)
  const lum2 = getLuminance(rgb2)

  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)

  return (brightest + 0.05) / (darkest + 0.05)
}

function validateContrast(foreground, background) {
  const ratio = getContrastRatio(foreground, background)
  return {
    passed: ratio >= 4.5,
    ratio: Math.round(ratio * 100) / 100,
    grade: ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : 'Fail'
  }
}

console.log('🎨 开始验证多模态面试评估系统色彩合规性...\n')

// 定义主题色彩
const themes = {
  ai: {
    primary: '#0066cc',
    secondary: '#4c51bf',
    text: '#1e3a8a',
    light: '#eff6ff',
    dark: '#1e40af'
  },
  data: {
    primary: '#059669',
    secondary: '#047857',
    text: '#064e3b',
    light: '#ecfdf5',
    dark: '#047857'
  },
  iot: {
    primary: '#d97706',
    secondary: '#b45309',
    text: '#92400e',
    light: '#fffbeb',
    dark: '#b45309'
  },
  interview: {
    primary: '#7c3aed',
    secondary: '#6d28d9',
    text: '#581c87',
    light: '#f5f3ff',
    dark: '#6d28d9'
  }
}

// 验证所有主题
const validationResults = {}

Object.keys(themes).forEach(themeName => {
  const theme = themes[themeName]
  const combinations = [
    { fg: theme.primary, bg: '#ffffff', name: '主色 + 白色背景' },
    { fg: '#ffffff', bg: theme.primary, name: '白色文字 + 主色背景' },
    { fg: theme.text, bg: '#ffffff', name: '文字色 + 白色背景' },
    { fg: theme.text, bg: theme.light, name: '文字色 + 浅色背景' },
    { fg: '#ffffff', bg: theme.dark, name: '白色文字 + 深色背景' }
  ]

  validationResults[themeName] = combinations.map(combo => ({
    ...validateContrast(combo.fg, combo.bg),
    name: combo.name,
    foreground: combo.fg,
    background: combo.bg
  }))
})

// 在控制台输出结果
console.group('🎨 WCAG 2.1 AA 对比度验证结果')

Object.keys(validationResults).forEach(themeName => {
  const themeResults = validationResults[themeName]
  const passedCount = themeResults.filter(r => r.passed).length
  const totalCount = themeResults.length

  console.group(`${themeName.toUpperCase()} 主题 (${passedCount}/${totalCount} 通过)`)

  themeResults.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const message = `${status} ${result.name}: ${result.ratio}:1 (${result.grade})`

    if (result.passed) {
      console.log(`%c${message}`, 'color: #10b981')
    } else {
      console.warn(`%c${message}`, 'color: #ef4444')
    }
  })

  console.groupEnd()
})

console.groupEnd()

// 生成简化的HTML报告
const reportHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>WCAG 2.1 AA 对比度验证报告</title>
  <style>
    body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
    .theme-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
    .passed { background: #f0fdf4; border-left: 4px solid #10b981; }
    .failed { background: #fef2f2; border-left: 4px solid #ef4444; }
    .combination { padding: 10px; margin: 5px 0; }
    .color-sample { padding: 8px; border-radius: 4px; display: inline-block; margin-left: 10px; }
  </style>
</head>
<body>
  <h1>WCAG 2.1 AA 对比度验证报告</h1>
  <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
  ${Object.keys(validationResults).map(themeName => {
    const themeResults = validationResults[themeName]
    const passedCount = themeResults.filter(r => r.passed).length
    return `
      <div class="theme-section">
        <h2>${themeName.toUpperCase()} 主题 (${passedCount}/${themeResults.length} 通过)</h2>
        ${themeResults.map(result => `
          <div class="combination ${result.passed ? 'passed' : 'failed'}">
            ${result.name}: ${result.ratio}:1 (${result.grade})
            <span class="color-sample" style="background: ${result.background}; color: ${result.foreground};">
              示例文字
            </span>
          </div>
        `).join('')}
      </div>
    `
  }).join('')}
</body>
</html>
`

// 保存报告到文件
const reportPath = path.join(process.cwd(), 'color-contrast-report.html')
fs.writeFileSync(reportPath, reportHtml, 'utf8')

console.log(`\n📊 详细报告已保存到: ${reportPath}`)

// 统计总体合规性
let totalTests = 0
let passedTests = 0

Object.keys(validationResults).forEach(themeName => {
  const themeResults = validationResults[themeName]
  totalTests += themeResults.length
  passedTests += themeResults.filter(r => r.passed).length
})

const overallPassRate = Math.round((passedTests / totalTests) * 100)

console.log('\n📈 总体合规性统计:')
console.log(`   总测试数: ${totalTests}`)
console.log(`   通过数: ${passedTests}`)
console.log(`   通过率: ${overallPassRate}%`)

if (overallPassRate >= 90) {
  console.log('✅ 色彩系统WCAG 2.1 AA合规性优秀!')
} else if (overallPassRate >= 80) {
  console.log('⚠️  色彩系统WCAG 2.1 AA合规性良好，建议优化部分组合')
} else {
  console.log('❌ 色彩系统需要优化以满足WCAG 2.1 AA标准')
}

// 检查每个主题的合规性
console.log('\n🎯 各主题合规性详情:')
Object.keys(validationResults).forEach(themeName => {
  const themeResults = validationResults[themeName]
  const themePassedCount = themeResults.filter(r => r.passed).length
  const themeTotalCount = themeResults.length
  const themePassRate = Math.round((themePassedCount / themeTotalCount) * 100)
  
  const status = themePassRate === 100 ? '✅' : themePassRate >= 80 ? '⚠️' : '❌'
  console.log(`   ${status} ${themeName.toUpperCase()}: ${themePassedCount}/${themeTotalCount} (${themePassRate}%)`)
  
  // 显示不合规的组合
  const failedTests = themeResults.filter(r => !r.passed)
  if (failedTests.length > 0) {
    console.log(`      不合规组合:`)
    failedTests.forEach(test => {
      console.log(`        - ${test.name}: ${test.ratio}:1 (需要 ≥4.5:1)`)
    })
  }
})

console.log('\n🔗 WCAG 2.1 指南: https://www.w3.org/WAI/WCAG21/Understanding/')
console.log('📖 对比度要求: https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html')

process.exit(overallPassRate >= 80 ? 0 : 1)
