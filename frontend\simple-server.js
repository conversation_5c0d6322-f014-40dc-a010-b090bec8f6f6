import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 5173;

// 静态文件服务
app.use(express.static(__dirname));

// SPA路由支持 - 所有路由都返回index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'enhanced-demo-static.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 服务器启动成功！`);
  console.log(`📱 访问地址: http://localhost:${PORT}`);
  console.log(`🎯 增强演示: http://localhost:${PORT}/enhanced-demo`);
  console.log(`⚡ 服务器运行在端口 ${PORT}`);
});
