<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek 导航功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .test-button:hover {
            background: #0066cc;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 iFlytek 导航功能测试</h1>
        
        <div class="test-section">
            <h3>主要导航路径测试</h3>
            <p>点击以下链接测试导航功能：</p>
            <a href="/" class="test-button">首页</a>
            <a href="/demo" class="test-button">产品演示</a>
            <a href="/interview-selection" class="test-button">开始面试</a>
            <a href="/reports" class="test-button">面试报告</a>
            <a href="/intelligent-dashboard" class="test-button">数据洞察</a>
        </div>

        <div class="test-section">
            <h3>企业功能测试</h3>
            <a href="/candidate-portal" class="test-button">候选人入口</a>
            <a href="/enterprise-home" class="test-button">企业版体验</a>
            <a href="/enterprise" class="test-button">企业管理</a>
        </div>

        <div class="test-section">
            <h3>JavaScript 导航测试</h3>
            <button class="test-button" onclick="testJSNavigation()">测试JS导航</button>
            <div id="js-nav-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>路由状态检查</h3>
            <button class="test-button" onclick="checkRouterStatus()">检查路由状态</button>
            <div id="router-status" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Vue应用检查</h3>
            <button class="test-button" onclick="checkVueApp()">检查Vue应用</button>
            <div id="vue-status" class="result"></div>
        </div>

        <div class="test-section">
            <h3>控制台错误监控</h3>
            <button class="test-button" onclick="checkConsoleErrors()">检查控制台错误</button>
            <div id="console-errors" class="result"></div>
        </div>
    </div>

    <script>
        // 错误收集
        const collectedErrors = [];
        const originalError = console.error;
        console.error = function(...args) {
            collectedErrors.push({
                message: args.join(' '),
                timestamp: new Date().toISOString()
            });
            originalError.apply(console, args);
        };

        // 测试JavaScript导航
        function testJSNavigation() {
            const result = document.getElementById('js-nav-result');
            let html = '<h4>JavaScript导航测试结果:</h4>';
            
            try {
                // 测试History API
                const currentPath = window.location.pathname;
                window.history.pushState({}, '', '/test-path');
                html += '<div class="success">✅ History.pushState 工作正常</div>';
                
                // 恢复路径
                window.history.pushState({}, '', currentPath);
                html += '<div class="success">✅ 路径恢复成功</div>';
                
                // 测试路由器是否存在
                if (window.location.pathname === '/') {
                    html += '<div class="info">📍 当前在主页</div>';
                } else {
                    html += `<div class="info">📍 当前路径: ${window.location.pathname}</div>`;
                }
                
                result.innerHTML = html;
                result.className = 'result info';
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 导航测试失败: ${error.message}</div>`;
                result.className = 'result error';
            }
        }

        // 检查路由状态
        function checkRouterStatus() {
            const result = document.getElementById('router-status');
            let html = '<h4>路由状态检查:</h4>';
            
            try {
                html += `<div class="info">🌐 当前URL: ${window.location.href}</div>`;
                html += `<div class="info">📂 路径: ${window.location.pathname}</div>`;
                html += `<div class="info">🔍 查询参数: ${window.location.search}</div>`;
                html += `<div class="info">🏷️ 哈希: ${window.location.hash}</div>`;
                
                // 检查Vue Router
                const app = document.getElementById('app');
                if (app && app.__vue_app__) {
                    html += '<div class="success">✅ Vue应用已挂载</div>';
                    
                    // 尝试获取路由信息
                    const vueApp = app.__vue_app__;
                    if (vueApp.config && vueApp.config.globalProperties) {
                        html += '<div class="success">✅ Vue全局属性可访问</div>';
                    }
                } else {
                    html += '<div class="error">❌ Vue应用未找到</div>';
                }
                
                result.innerHTML = html;
                result.className = 'result info';
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 路由检查失败: ${error.message}</div>`;
                result.className = 'result error';
            }
        }

        // 检查Vue应用
        function checkVueApp() {
            const result = document.getElementById('vue-status');
            let html = '<h4>Vue应用检查:</h4>';
            
            try {
                const app = document.getElementById('app');
                if (app) {
                    html += '<div class="success">✅ #app 容器存在</div>';
                    
                    if (app.__vue_app__) {
                        html += '<div class="success">✅ Vue应用实例已挂载</div>';
                        
                        // 检查Vue版本
                        if (window.Vue) {
                            html += `<div class="info">📦 Vue版本: ${window.Vue.version || '未知'}</div>`;
                        }
                        
                        // 检查组件
                        const components = app.querySelectorAll('[data-v-]');
                        if (components.length > 0) {
                            html += `<div class="success">✅ 找到 ${components.length} 个Vue组件</div>`;
                        }
                    } else {
                        html += '<div class="error">❌ Vue应用实例未挂载</div>';
                    }
                } else {
                    html += '<div class="error">❌ #app 容器不存在</div>';
                }
                
                result.innerHTML = html;
                result.className = 'result info';
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Vue检查失败: ${error.message}</div>`;
                result.className = 'result error';
            }
        }

        // 检查控制台错误
        function checkConsoleErrors() {
            const result = document.getElementById('console-errors');
            let html = '<h4>控制台错误检查:</h4>';
            
            if (collectedErrors.length === 0) {
                html += '<div class="success">✅ 未发现JavaScript错误</div>';
            } else {
                html += `<div class="error">❌ 发现 ${collectedErrors.length} 个错误:</div>`;
                collectedErrors.slice(-3).forEach(error => {
                    html += `<div class="error">🚫 ${error.message}</div>`;
                });
            }
            
            result.innerHTML = html;
            result.className = 'result info';
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkRouterStatus();
                checkVueApp();
                checkConsoleErrors();
            }, 1000);
        });
    </script>
</body>
</html>
