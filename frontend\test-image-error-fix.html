<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片错误处理测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-image {
            width: 80px;
            height: 45px;
            border: 2px solid #667eea;
            border-radius: 4px;
            margin: 10px;
            display: inline-block;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🖼️ 图片错误处理修复测试</h1>
    
    <div class="test-container">
        <h3>测试1: 章节缩略图错误处理</h3>
        <p>这些图片会故意加载失败，然后使用我们的错误处理函数：</p>
        
        <img class="test-image" 
             src="/non-existent-image-1.jpg" 
             data-index="1"
             onerror="handleChapterImageError(this)"
             alt="章节1">
        
        <img class="test-image" 
             src="/non-existent-image-2.jpg" 
             data-index="2"
             onerror="handleChapterImageError(this)"
             alt="章节2">
        
        <img class="test-image" 
             src="/non-existent-image-3.jpg" 
             data-index="3"
             onerror="handleChapterImageError(this)"
             alt="章节3">
        
        <div id="result1" class="result"></div>
    </div>

    <div class="test-container">
        <h3>测试2: SVG占位符生成</h3>
        <p>直接测试SVG生成函数：</p>
        <div id="svg-test"></div>
        <div id="result2" class="result"></div>
    </div>

    <script>
        // 修复后的图片错误处理函数
        function handleChapterImageError(event) {
            try {
                const chapterIndex = event.dataset.index || '1'
                const svgContent = `<svg width="80" height="45" xmlns="http://www.w3.org/2000/svg">
                    <rect width="80" height="45" fill="#667eea"/>
                    <text x="40" y="25" text-anchor="middle" fill="white" font-size="10" font-family="Arial">
                        Ch.${chapterIndex}
                    </text>
                </svg>`
                
                // 使用URL编码而不是base64来避免中文字符问题
                event.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`
                
                document.getElementById('result1').innerHTML = '✅ 图片错误处理成功！没有btoa错误。'
                document.getElementById('result1').className = 'result success'
            } catch (error) {
                document.getElementById('result1').innerHTML = `❌ 错误: ${error.message}`
                document.getElementById('result1').className = 'result error'
                console.error('图片错误处理失败:', error)
            }
        }

        // 测试SVG生成
        function testSVGGeneration() {
            try {
                const svgContent = `<svg width="80" height="45" xmlns="http://www.w3.org/2000/svg">
                    <rect width="80" height="45" fill="#667eea"/>
                    <text x="40" y="25" text-anchor="middle" fill="white" font-size="10" font-family="Arial">
                        Ch.Test
                    </text>
                </svg>`
                
                const dataUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`
                
                const img = document.createElement('img')
                img.src = dataUrl
                img.className = 'test-image'
                img.style.border = '2px solid #28a745'
                
                document.getElementById('svg-test').appendChild(img)
                document.getElementById('result2').innerHTML = '✅ SVG占位符生成成功！'
                document.getElementById('result2').className = 'result success'
                
                console.log('✅ SVG生成测试通过')
            } catch (error) {
                document.getElementById('result2').innerHTML = `❌ SVG生成错误: ${error.message}`
                document.getElementById('result2').className = 'result error'
                console.error('SVG生成失败:', error)
            }
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            console.log('🧪 开始图片错误处理测试...')
            testSVGGeneration()
            
            // 检查是否有btoa相关错误
            window.addEventListener('error', (e) => {
                if (e.message.includes('btoa') || e.message.includes('InvalidCharacterError')) {
                    console.error('❌ 检测到btoa错误:', e.message)
                    document.getElementById('result1').innerHTML = `❌ 检测到btoa错误: ${e.message}`
                    document.getElementById('result1').className = 'result error'
                }
            })
        })
    </script>
</body>
</html>
