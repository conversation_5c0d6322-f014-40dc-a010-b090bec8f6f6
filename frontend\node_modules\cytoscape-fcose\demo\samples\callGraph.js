call_graph = {
	"elements": {
		"nodes": [
			{
				"data": {
					"id": "nwtN_6edbfba5-6884-4f26-868f-18bc2481616e",
					"bbox": {
						"x": 1048.6375489786594,
						"y": 153.7561581259744,
						"w": 91.52294921875,
						"h": 50.25
					},
					"minWidth": 91.52294921875,
					"minWidthBiasLeft": 0.00005,
					"minWidthBiasRight": 0.00005,
					"minHeight": 50.25,
					"minHeightBiasTop": 0.00005,
					"minHeightBiasBottom": 0.00005,
					"originalW": 119.52294921875,
					"originalH": 78.25,
					"class": "compartment",
					"label": "__main__",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 3.25,
					"border-color": "#555555",
					"background-color": "#ffffff",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1048.6375489786594,
					"y": 153.7561581259744
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_6e27959a-ae83-4cb0-898f-9a1e6c5cafae",
					"bbox": {
						"x": 1048.6375489786594,
						"y": 153.7561581259744,
						"w": 88.27294921875,
						"h": 47
					},
					"class": "macromolecule",
					"label": "main\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6edbfba5-6884-4f26-868f-18bc2481616e",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1048.6375489786594,
					"y": 153.7561581259744
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_e872ccff-fb1a-4264-a66a-cbc72d1e9711",
					"bbox": {
						"x": 832.2602245209991,
						"y": 267.2349457952003,
						"w": 203.3974609375,
						"h": 50.25
					},
					"minWidth": 155.5302734375,
					"minWidthBiasLeft": 0.00005,
					"minWidthBiasRight": 0.00005,
					"minHeight": 50.25,
					"minHeightBiasTop": 0.00005,
					"minHeightBiasBottom": 0.00005,
					"originalW": 231.3974609375,
					"originalH": 78.25,
					"class": "compartment",
					"label": "communications",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 3.25,
					"border-color": "#555555",
					"background-color": "#ffffff",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 832.2602245209991,
					"y": 267.2349457952003
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "d2f78da5-fbd3-fe91-4046-3b5aea0648f6",
					"bbox": {
						"x": 832.2602245209991,
						"y": 267.2349457952003,
						"w": 200.1474609375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "communications.ServerSocket.__init__\ncalls: 1\ntime: 0.001029s",
					"statesandinfos": [],
					"parent": "nwtN_e872ccff-fb1a-4264-a66a-cbc72d1e9711",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 832.2602245209991,
					"y": 267.2349457952003
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "d1f240af-7520-fb1b-19f9-e5e92eab909e",
					"bbox": {
						"x": 461.06847268925367,
						"y": 406.7278918574187,
						"w": 121.4775390625,
						"h": 50.25
					},
					"minWidth": 109.640625,
					"minWidthBiasLeft": 0.00005,
					"minWidthBiasRight": 0.00005,
					"minHeight": 50.25,
					"minHeightBiasTop": 0.00005,
					"minHeightBiasBottom": 0.00005,
					"originalW": 149.4775390625,
					"originalH": 78.25,
					"class": "compartment",
					"label": "socket",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 3.25,
					"border-color": "#555555",
					"background-color": "#ffffff",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 461.06847268925367,
					"y": 406.7278918574187
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "288a2f9b-2930-701e-c68e-400307fbffd4",
					"bbox": {
						"x": 461.06847268925367,
						"y": 406.7278918574187,
						"w": 118.2275390625,
						"h": 47
					},
					"class": "macromolecule",
					"label": "socket.socket.__init__\ncalls: 1\ntime: 0.001029s",
					"statesandinfos": [],
					"parent": "d1f240af-7520-fb1b-19f9-e5e92eab909e",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 461.06847268925367,
					"y": 406.7278918574187
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"bbox": {
						"x": 973.1536617161887,
						"y": 562.1987962172685,
						"w": 808.9722624959013,
						"h": 380.90672838617974
					},
					"minWidth": 205.5387290927216,
					"minWidthBiasLeft": 0.00005,
					"minWidthBiasRight": 0.00005,
					"minHeight": 128.27786926084235,
					"minHeightBiasTop": 0.00005,
					"minHeightBiasBottom": 0.00005,
					"originalW": 836.9722624959013,
					"originalH": 408.90672838617974,
					"class": "compartment",
					"label": "threading",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 3.25,
					"border-color": "#555555",
					"background-color": "#ffffff",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 973.1536617161887,
					"y": 562.1987962172685
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_a44ad134-4c47-4cf5-b244-9d9fc0804e93",
					"bbox": {
						"x": 1186.3610894421433,
						"y": 396.8704320241787,
						"w": 149.4013671875,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.ServerSocket.start\ncalls: 1\ntime: 0.001969s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1186.3610894421433,
					"y": 396.8704320241787
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"bbox": {
						"x": 785.3451019915435,
						"y": 399.3027259948305,
						"w": 166.54052734375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.ServerSocket.__init__\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 785.3451019915435,
					"y": 399.3027259948305
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_550a66a2-1061-45cc-9f78-14570daaff94",
					"bbox": {
						"x": 1171.6884552852318,
						"y": 501.00617333218474,
						"w": 112.7275390625,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Event.isset\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1171.6884552852318,
					"y": 501.00617333218474
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "82181180-e275-b004-243e-eb4383e1e72f",
					"bbox": {
						"x": 623.602833202613,
						"y": 504.26049930822586,
						"w": 106.62060546875,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.newname\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 623.602833202613,
					"y": 504.26049930822586
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "359e920c-52a4-459e-2262-4ec90824f21e",
					"bbox": {
						"x": 774.4147292170284,
						"y": 501.6751525194074,
						"w": 124.3505859375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.currentthread\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 774.4147292170284,
					"y": 501.6751525194074
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "5a34dca5-646c-cae7-affe-ef4142c5ad73",
					"bbox": {
						"x": 883.8610766103469,
						"y": 509.431192885863,
						"w": 160.42822265625,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.MainThread.daemon\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 883.8610766103469,
					"y": 509.431192885863
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "2f560d5b-bb1b-cc3f-d5b5-a69e6fab8d47",
					"bbox": {
						"x": 1012.9044451633454,
						"y": 510.2832319234375,
						"w": 104.17138671875,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Event.init\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1012.9044451633454,
					"y": 510.2832319234375
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"bbox": {
						"x": 1321.1790996047644,
						"y": 501.0665091727607,
						"w": 109.67138671875,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Event.wait\ncalls: 1\ntime: 0.001005s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1321.1790996047644,
					"y": 501.0665091727607
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "1f64237f-dd15-2d5f-18f0-02a0d0338f44",
					"bbox": {
						"x": 810.9249008712393,
						"y": 629.4880484917272,
						"w": 146.98974609375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Condition.__init__\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 810.9249008712393,
					"y": 629.4880484917272
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"bbox": {
						"x": 976.5020676671496,
						"y": 615.3268434368138,
						"w": 128.01904296875,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Condition.wait\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 976.5020676671496,
					"y": 615.3268434368138
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "d6768356-8772-a49a-c2f7-c3b9802c954b",
					"bbox": {
						"x": 1161.3313010713127,
						"y": 621.0949598234208,
						"w": 150.0458984375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Condition.__exit__\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1161.3313010713127,
					"y": 621.0949598234208
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "05b42313-71a4-e1a9-383b-6b4be641e69d",
					"bbox": {
						"x": 1285.8699319437194,
						"y": 616.4161669025763,
						"w": 158.00048828125,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Condition.__enter__\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1285.8699319437194,
					"y": 616.4161669025763
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "3c161178-1fca-ec7e-3fc7-425ce940fe1a",
					"bbox": {
						"x": 842.5152813783538,
						"y": 727.5271604103584,
						"w": 161.05126953125,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Condition._is_owned\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 842.5152813783538,
					"y": 727.5271604103584
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "bab5c8a0-0f39-66d4-eaca-7f9160ef25fa",
					"bbox": {
						"x": 981.9486849959626,
						"y": 712.2766318896824,
						"w": 180.005859375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Condition._release_save\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 981.9486849959626,
					"y": 712.2766318896824
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "a0d92978-e6e9-6d87-9a06-567d139d15af",
					"bbox": {
						"x": 1170.401644572887,
						"y": 717.7232492184953,
						"w": 191.005859375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "threading.Condition._acquire_restore\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_6f2f14d2-4d5d-4582-8555-9ce01b4f769f",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1170.401644572887,
					"y": 717.7232492184953
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_e8ac41f5-45d0-4855-bcc9-623f16e77889",
					"bbox": {
						"x": 1473.2018438500227,
						"y": 504.2598166908556,
						"w": 135.3349609375,
						"h": 50.25
					},
					"minWidth": 91.52294921875,
					"minWidthBiasLeft": 0.00005,
					"minWidthBiasRight": 0.00005,
					"minHeight": 50.25,
					"minHeightBiasTop": 0.00005,
					"minHeightBiasBottom": 0.00005,
					"originalW": 163.3349609375,
					"originalH": 78.25,
					"class": "compartment",
					"label": "_weakrefset",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 3.25,
					"border-color": "#555555",
					"background-color": "#ffffff",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1473.2018438500227,
					"y": 504.2598166908556
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "912c0f08-87fb-c746-18cb-19909a2b41c8",
					"bbox": {
						"x": 1473.2018438500227,
						"y": 504.2598166908556,
						"w": 132.0849609375,
						"h": 47
					},
					"class": "macromolecule",
					"label": "weakrefset.WeakSet.add\ncalls: 1\ntime: 0.000000s",
					"statesandinfos": [],
					"parent": "nwtN_e8ac41f5-45d0-4855-bcc9-623f16e77889",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#9999e5",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1473.2018438500227,
					"y": 504.2598166908556
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			}
		],
		"edges": [
			{
				"data": {
					"id": "nwtE_8b2b6e86-b5c9-4d80-99d6-fba6d10c340f",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "d2f78da5-fbd3-fe91-4046-3b5aea0648f6",
					"target": "288a2f9b-2930-701e-c68e-400307fbffd4",
					"portsource": "d2f78da5-fbd3-fe91-4046-3b5aea0648f6",
					"porttarget": "288a2f9b-2930-701e-c68e-400307fbffd4"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_618ca0aa-0c62-42ea-ad5a-1c399335a913",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_6e27959a-ae83-4cb0-898f-9a1e6c5cafae",
					"target": "d2f78da5-fbd3-fe91-4046-3b5aea0648f6",
					"portsource": "nwtN_6e27959a-ae83-4cb0-898f-9a1e6c5cafae",
					"porttarget": "d2f78da5-fbd3-fe91-4046-3b5aea0648f6"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_c5861902-d6ee-4657-88e7-d57ab0de2b93",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_a44ad134-4c47-4cf5-b244-9d9fc0804e93",
					"target": "nwtN_550a66a2-1061-45cc-9f78-14570daaff94",
					"portsource": "nwtN_a44ad134-4c47-4cf5-b244-9d9fc0804e93",
					"porttarget": "nwtN_550a66a2-1061-45cc-9f78-14570daaff94"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_93cf0fe2-8c29-44f4-95af-5e687ee44c2b",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "d2f78da5-fbd3-fe91-4046-3b5aea0648f6",
					"target": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"portsource": "d2f78da5-fbd3-fe91-4046-3b5aea0648f6",
					"porttarget": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_cc55cbc0-61dc-41d3-bb2f-d6b55caeab9a",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"target": "82181180-e275-b004-243e-eb4383e1e72f",
					"portsource": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"porttarget": "82181180-e275-b004-243e-eb4383e1e72f"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_a3d7bd9b-7c21-49a8-a6bb-1c7a4ebd7eb7",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"target": "359e920c-52a4-459e-2262-4ec90824f21e",
					"portsource": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"porttarget": "359e920c-52a4-459e-2262-4ec90824f21e"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_15d4f487-636c-4374-99c1-ae754989862d",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"target": "5a34dca5-646c-cae7-affe-ef4142c5ad73",
					"portsource": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"porttarget": "5a34dca5-646c-cae7-affe-ef4142c5ad73"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_e4fc0a9f-6f88-4a20-8f21-00149e7ed87a",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"target": "2f560d5b-bb1b-cc3f-d5b5-a69e6fab8d47",
					"portsource": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"porttarget": "2f560d5b-bb1b-cc3f-d5b5-a69e6fab8d47"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_bf434941-04cf-4b18-9caf-577fac1c27f7",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"target": "912c0f08-87fb-c746-18cb-19909a2b41c8",
					"portsource": "nwtN_ac02fa71-2428-47ad-8c3c-80bb5659a066",
					"porttarget": "912c0f08-87fb-c746-18cb-19909a2b41c8"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_d2680712-4bc6-40b7-86b0-4af7052d069c",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_a44ad134-4c47-4cf5-b244-9d9fc0804e93",
					"target": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"portsource": "nwtN_a44ad134-4c47-4cf5-b244-9d9fc0804e93",
					"porttarget": "d07ee031-2818-7c9c-5589-e8a161b9946c"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_445c17cc-894b-4833-81da-9d573e6c3c25",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "2f560d5b-bb1b-cc3f-d5b5-a69e6fab8d47",
					"target": "1f64237f-dd15-2d5f-18f0-02a0d0338f44",
					"portsource": "2f560d5b-bb1b-cc3f-d5b5-a69e6fab8d47",
					"porttarget": "1f64237f-dd15-2d5f-18f0-02a0d0338f44"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_1e925488-3584-4b09-b716-ba6f14379838",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"target": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"portsource": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"porttarget": "63edffef-16cf-a216-5ace-a27a4290e3d8"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_d748d6aa-f386-4ced-bac6-d2587064d47b",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"target": "d6768356-8772-a49a-c2f7-c3b9802c954b",
					"portsource": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"porttarget": "d6768356-8772-a49a-c2f7-c3b9802c954b"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_c71d9e10-11c4-4e9a-baf7-fa043282103c",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"target": "05b42313-71a4-e1a9-383b-6b4be641e69d",
					"portsource": "d07ee031-2818-7c9c-5589-e8a161b9946c",
					"porttarget": "05b42313-71a4-e1a9-383b-6b4be641e69d"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_55f140c5-f99c-4bc5-b8ef-e7c808ae8d66",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"target": "3c161178-1fca-ec7e-3fc7-425ce940fe1a",
					"portsource": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"porttarget": "3c161178-1fca-ec7e-3fc7-425ce940fe1a"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_741a21e2-6999-4d7f-8f01-bc7fecbbc41b",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"target": "bab5c8a0-0f39-66d4-eaca-7f9160ef25fa",
					"portsource": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"porttarget": "bab5c8a0-0f39-66d4-eaca-7f9160ef25fa"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_790b1a2d-2b2c-481a-967f-e696d23e6824",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"target": "a0d92978-e6e9-6d87-9a06-567d139d15af",
					"portsource": "63edffef-16cf-a216-5ace-a27a4290e3d8",
					"porttarget": "a0d92978-e6e9-6d87-9a06-567d139d15af"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_664a54ae-b07a-4c85-a503-349ca4edf006",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"label": 1,
					"source": "nwtN_6e27959a-ae83-4cb0-898f-9a1e6c5cafae",
					"target": "nwtN_a44ad134-4c47-4cf5-b244-9d9fc0804e93",
					"portsource": "nwtN_6e27959a-ae83-4cb0-898f-9a1e6c5cafae",
					"porttarget": "nwtN_a44ad134-4c47-4cf5-b244-9d9fc0804e93"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			}
		]
	},
	"style": [
	    {
	      "selector": "node",
	      "style": {
	        "shape": "rectangle",
	        "text-halign": "center",
	        "text-valign": "center",
	        "font-weight" : "normal",
	        "background-color": "#ffffff",
	        "opacity": "1",
	        "border-color": "#555555"
	      }
	    },
	    {
	      "selector": "node[class = 'simple chemical']",
	      "style": {
	        "shape": "ellipse",
	        "font-size": 14,
	        "background-color": "#fddbc7"
	      }
	    },
	    {
	      "selector": "node[class = 'macromolecule']",
	      "style": {
	        "shape": "roundrectangle",
	        "background-color": "#9999e5"	        
	      }
	    },
	    {
	      "selector": "node[class = 'unspecified entity']",
	      "style": {
	        "shape": "ellipse",
	        "background-color": "#f7f7f7"
	      }
	    },
	    {
	      "selector": "node[class = 'nucleic acid feature']",
	      "style": {
	        "shape": "rectangle",
	        "background-color": "#f4a582",	        
	      }
	    },	    	    	   
	    {
	      "selector": ":parent",
	      "style": {
	        "background-opacity": "0.333",
	        "text-valign": "bottom",
	        "shape": "barrel",
	        "text-margin-y": "2px",
	        "font-weight" : "normal",
	        "border-color": "#555555"
	      }
	    },
	    {
	      "selector": "node:selected",
	      "style": {
	        "background-color": "#33ff00",
	        "border-color": "#22ee00"
	      }
	    },
	    {
	      "selector": "edge",
	      "style": {
	        "curve-style": "bezier",
	        "width": "2px",
	        "line-color": "rgb(58,126,207)",
	        "opacity": "1"
	      }
	    },
	    {
	      "selector": "edge:selected",
	      "style": {
	        "line-color": "#33ff00",
	        "font-size": "13px",
	        "text-opacity": "1",
	        "text-rotation": "autorotate",
	        "color": "#33ff00",
	        "font-weight": "bold",
	        "text-background-shape": "roundrectangle",
	        "text-background-opacity": "1",
	        "text-background-padding": "2px"
	      }
	    },
	    {
	      "selector": "edge[class = 'production']",
	      "style": {
	        "target-arrow-shape": "triangle",
	        "target-arrow-color": "rgb(58,126,207)"
	      }
	    },
	    {
	      "selector": "edge:selected[class = 'production']",
	      "style": {
	        "target-arrow-shape": "triangle",
	        "target-arrow-color": "#33ff00"
	      }
	    },	    
	  ],
	"zoomingEnabled": true,
	"userZoomingEnabled": true,
	"zoom": 0.9387299878673792,
	"minZoom": 0.125,
	"maxZoom": 16,
	"panningEnabled": true,
	"userPanningEnabled": true,
	"pan": {
		"x": -275.1308697557213,
		"y": -85.14353945866765
	},
	"boxSelectionEnabled": true,
	"renderer": {
		"name": "canvas"
	},
	"wheelSensitivity": 0.1,
	"motionBlur": true
}