# iFlytek多模态智能面试评测系统
## 演示视频录制详细指导

---

## 🎯 录制总体要求

### 基本规格
- **时长**：严格控制在7分钟内（6分30秒-7分钟最佳）
- **分辨率**：1920x1080 (Full HD)
- **帧率**：30fps
- **音频**：清晰的中文普通话解说
- **格式**：MP4（H.264编码）

### 质量标准
- **画面清晰**：所有文字和界面元素清晰可见
- **音频清晰**：无杂音，音量适中，语速适当
- **操作流畅**：演示操作自然流畅，无卡顿
- **内容完整**：覆盖所有要求的功能点

---

## 🛠️ 推荐录制软件

### 1. OBS Studio（免费，推荐）
**优点**：
- 完全免费，功能强大
- 支持多场景切换
- 录制质量高，文件大小合理
- 支持实时音频监控

**下载地址**：https://obsproject.com/

**基本设置**：
```
视频设置：
- 基础分辨率：1920x1080
- 输出分辨率：1920x1080
- 帧率：30fps

音频设置：
- 采样率：44.1kHz
- 声道：立体声

录制设置：
- 录制格式：MP4
- 编码器：x264
- 质量：高质量，中等文件大小
```

### 2. Camtasia（付费，专业）
**优点**：
- 录制和编辑一体化
- 操作简单，适合新手
- 内置丰富的编辑功能
- 支持添加标注和特效

**价格**：约$299（有试用版）

### 3. Bandicam（付费，轻量）
**优点**：
- 录制质量高，文件小
- 系统资源占用少
- 操作简单
- 支持实时绘制

**价格**：约$39

### 4. Windows自带录制（免费，简单）
**使用方法**：
- 按 Win + G 打开游戏栏
- 点击录制按钮
- 适合简单录制需求

---

## 📋 录制前准备工作

### 系统环境准备
1. **清理桌面**：
   - 关闭不必要的程序
   - 隐藏桌面图标
   - 设置纯色壁纸

2. **浏览器准备**：
   - 清除浏览历史和缓存
   - 关闭不相关标签页
   - 设置合适的缩放比例（100%推荐）

3. **系统设置**：
   - 关闭通知提醒
   - 设置勿扰模式
   - 确保网络连接稳定

### 演示环境准备
1. **启动服务**：
   ```bash
   # 启动前端服务
   cd frontend
   npm run dev
   
   # 启动后端服务
   cd backend
   uvicorn app.main:app --reload
   ```

2. **准备测试数据**：
   - 创建测试用户账号
   - 准备演示用的回答内容
   - 确保所有功能正常工作

3. **浏览器书签**：
   - http://localhost:5173 （主页）
   - http://localhost:5173/select-interview-mode
   - http://localhost:5173/voice-interview
   - http://localhost:5173/text-based-interview
   - http://localhost:8000/docs （API文档）

### 音频设备准备
1. **麦克风设置**：
   - 使用外置麦克风（推荐）
   - 调整麦克风音量到70-80%
   - 进行录音测试

2. **环境要求**：
   - 安静的录制环境
   - 避免回音和杂音
   - 保持稳定的说话距离

---

## 🎬 分段录制指导

### 第1段：开场介绍 (0:00-0:30)
**录制要点**：
- 从系统Logo开始
- 语速稍慢，突出重点
- 展示iFlytek品牌标识

**操作步骤**：
1. 打开主页 http://localhost:5173
2. 停留在首页3-5秒
3. 鼠标悬停在关键元素上
4. 展示iFlytek Logo和品牌色彩

**解说词**：
```
大家好！欢迎观看iFlytek多模态智能面试评测系统演示。
随着高校毕业生人数逐年攀升，面试成为求职的关键环节。
我们的系统基于科大讯飞星火大模型，通过多模态AI分析，
为高校学生提供智能化的面试训练和评估服务。
```

### 第2段：系统概览 (0:30-1:30)
**录制要点**：
- 展示技术领域选择
- 突出中文界面特色
- 强调符合比赛要求

**操作步骤**：
1. 点击"选择面试模式"
2. 展示技术领域选择界面
3. 依次点击"人工智能"、"大数据"、"物联网"
4. 展示岗位类型选择
5. 展示界面的中文化特色

**解说词**：
```
我们的系统完全符合比赛要求，支持人工智能、大数据、物联网三大技术领域，
涵盖技术岗、产品岗、运维测试岗等多种职位。
系统采用Vue.js + Element Plus构建，界面简洁美观，完全中文化。
```

### 第3段：核心功能演示 (1:30-5:30)
**录制要点**：
- 完整演示面试流程
- 展示多模态分析
- 显示六维能力评估
- 展示智能反馈

**操作步骤**：
1. **面试流程演示** (1:30-2:30)：
   - 选择"人工智能"领域
   - 选择"AI工程师"岗位
   - 点击"开始面试"
   - 展示AI面试官界面

2. **多模态分析演示** (2:30-3:30)：
   - 输入文本回答（关于机器学习）
   - 展示语音输入功能
   - 展示视频分析功能
   - 显示实时分析结果

3. **智能评估展示** (3:30-4:30)：
   - 展示六维能力评估结果
   - 显示雷达图可视化
   - 展示详细分析报告

4. **智能反馈演示** (4:30-5:30)：
   - 展示完整评估报告
   - 显示具体改进建议
   - 展示个性化学习路径

**解说词**：
```
现在演示完整的面试流程。我选择人工智能领域的AI工程师岗位，
系统会根据选择生成针对性的面试问题。

系统支持文本、语音、视频三种模态的分析，我现在回答一个技术问题，
系统会实时分析我的回答内容、语音表达和视频表现。

系统基于六项核心能力指标进行评估：专业知识水平、技能匹配度、
语言表达能力、逻辑思维能力、创新能力和应变抗压能力。
每项指标都有科学的权重分配。

系统会生成详细的反馈报告，包括能力雷达图、关键问题定位和具体改进建议。
比如"回答缺乏STAR结构"、"眼神交流不足"等具体指导。
```

### 第4段：技术亮点展示 (5:30-7:00)
**录制要点**：
- 强调iFlytek星火大模型
- 展示个性化学习路径
- 显示系统性能

**操作步骤**：
1. **iFlytek集成展示** (5:30-6:00)：
   - 展示AI分析过程
   - 演示智能引导功能
   - 显示技术知识库

2. **学习路径展示** (6:00-6:30)：
   - 展示学习路径生成
   - 显示分层学习计划
   - 展示资源推荐

3. **性能展示** (6:30-7:00)：
   - 展示系统监控
   - 显示性能指标
   - 总结系统优势

**解说词**：
```
系统深度集成iFlytek星火大模型，提供15年专家级的分析能力。
AI面试官不仅能提出专业问题，还能在候选人遇到困难时提供智能引导。

基于评估结果，系统会生成个性化的学习路径，包括短期、中期、长期的学习计划，
以及具体的学习资源推荐。

系统具有优秀的性能表现，响应时间小于2秒，支持高并发访问，
并且具备完善的监控和日志系统。

iFlytek多模态智能面试评测系统完全满足比赛要求，具有技术创新性和实用价值。
我们将继续优化系统功能，为更多高校学生提供优质的面试训练服务。感谢观看！
```

---

## 🎞️ 录制技术要点

### OBS Studio录制设置
1. **创建场景**：
   ```
   场景1：桌面录制
   - 添加显示器捕获
   - 设置音频输入捕获（麦克风）
   ```

2. **音频设置**：
   - 桌面音频：关闭（避免系统声音干扰）
   - 麦克风音频：开启，音量调至-12dB到-6dB
   - 启用噪音抑制和噪音门限

3. **录制设置**：
   ```
   输出模式：简单
   录制质量：高质量，中等文件大小
   录制格式：MP4
   音频比特率：160
   ```

### 录制操作技巧
1. **鼠标操作**：
   - 移动速度适中，不要过快
   - 点击时稍作停顿
   - 重要操作可以重复展示

2. **页面切换**：
   - 给每个页面足够的展示时间
   - 切换时使用平滑过渡
   - 避免频繁的页面跳转

3. **文字输入**：
   - 输入速度适中
   - 重要内容可以高亮显示
   - 避免输入错误

### 音频录制技巧
1. **语音要求**：
   - 语速：每分钟180-200字
   - 语调：平稳，重点词汇适当强调
   - 停顿：句间停顿0.5秒，段间停顿1秒

2. **发音要求**：
   - 标准普通话
   - 吐字清晰
   - 音量稳定

3. **内容要求**：
   - 严格按照脚本
   - 突出技术亮点
   - 强调符合比赛要求

---

## ✂️ 后期编辑指导

### 推荐编辑软件
1. **DaVinci Resolve**（免费，专业）
2. **Adobe Premiere Pro**（付费，专业）
3. **Camtasia**（付费，简单）
4. **剪映**（免费，简单）

### 编辑要点
1. **剪辑**：
   - 删除多余的停顿和错误
   - 保持内容连贯性
   - 控制总时长在7分钟内

2. **音频处理**：
   - 音量标准化
   - 降噪处理
   - 添加淡入淡出

3. **视觉效果**：
   - 添加字幕（可选）
   - 重要信息高亮
   - 添加转场效果

4. **最终输出**：
   ```
   格式：MP4
   分辨率：1920x1080
   帧率：30fps
   比特率：8-12 Mbps
   音频：AAC，128-192 kbps
   ```

---

## ✅ 录制质量检查清单

### 技术质量
- [ ] 画面清晰，无模糊
- [ ] 音频清晰，无杂音
- [ ] 时长控制在7分钟内
- [ ] 文件格式为MP4

### 内容完整性
- [ ] 展示3个技术领域
- [ ] 演示多模态分析
- [ ] 显示6项核心能力
- [ ] 展示可视化反馈
- [ ] 演示学习路径
- [ ] 强调iFlytek集成

### 演示效果
- [ ] 操作流畅自然
- [ ] 解说清晰准确
- [ ] 重点突出明确
- [ ] 整体节奏合适

### 比赛要求
- [ ] 符合所有功能要求
- [ ] 突出技术创新点
- [ ] 体现实用价值
- [ ] 展示系统优势

---

## 🚀 录制流程总结

### 录制前（30分钟）
1. 环境准备和设备调试
2. 系统启动和功能检查
3. 录制软件设置和测试
4. 脚本熟悉和练习

### 录制中（7分钟）
1. 严格按照脚本执行
2. 保持操作流畅性
3. 注意时间控制
4. 确保音频质量

### 录制后（60分钟）
1. 视频质量检查
2. 必要的后期编辑
3. 最终输出和压缩
4. 质量验收和备份

通过以上详细指导，您可以制作出高质量的演示视频，充分展示系统的功能特色和技术优势。
