import{a as te}from"./chunk-YEYRPSRY.mjs";import{a as se}from"./chunk-R4PCWW2Q.mjs";import{b as ee}from"./chunk-G4RV2GLT.mjs";import{e as Qt,m as Zt}from"./chunk-CRSA2SMT.mjs";import{M,Q as jt,R as Wt,S as zt,T as Kt,U as Xt,V as Jt,W as qt,Y as N,b as _}from"./chunk-63ZE7VZ5.mjs";import{a as c}from"./chunk-GTKDMUJJ.mjs";var Lt=function(){var t=c(function(Y,o,h,a){for(h=h||{},a=Y.length;a--;h[Y[a]]=o);return h},"o"),e=[1,2],s=[1,3],n=[1,4],r=[2,4],d=[1,9],u=[1,11],f=[1,16],S=[1,17],m=[1,18],E=[1,19],b=[1,33],L=[1,20],x=[1,21],p=[1,22],k=[1,23],O=[1,24],I=[1,26],G=[1,27],R=[1,28],P=[1,29],et=[1,30],st=[1,31],it=[1,32],rt=[1,35],nt=[1,36],at=[1,37],ot=[1,38],z=[1,34],g=[1,4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],lt=[1,4,5,14,15,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,39,40,41,45,48,51,52,53,54,57],Ut=[4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],_t={trace:c(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NL:5,SD:6,document:7,line:8,statement:9,classDefStatement:10,styleStatement:11,cssClassStatement:12,idStatement:13,DESCR:14,"-->":15,HIDE_EMPTY:16,scale:17,WIDTH:18,COMPOSIT_STATE:19,STRUCT_START:20,STRUCT_STOP:21,STATE_DESCR:22,AS:23,ID:24,FORK:25,JOIN:26,CHOICE:27,CONCURRENT:28,note:29,notePosition:30,NOTE_TEXT:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,CLICK:38,STRING:39,HREF:40,classDef:41,CLASSDEF_ID:42,CLASSDEF_STYLEOPTS:43,DEFAULT:44,style:45,STYLE_IDS:46,STYLEDEF_STYLEOPTS:47,class:48,CLASSENTITY_IDS:49,STYLECLASS:50,direction_tb:51,direction_bt:52,direction_rl:53,direction_lr:54,eol:55,";":56,EDGE_STATE:57,STYLE_SEPARATOR:58,left_of:59,right_of:60,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NL",6:"SD",14:"DESCR",15:"-->",16:"HIDE_EMPTY",17:"scale",18:"WIDTH",19:"COMPOSIT_STATE",20:"STRUCT_START",21:"STRUCT_STOP",22:"STATE_DESCR",23:"AS",24:"ID",25:"FORK",26:"JOIN",27:"CHOICE",28:"CONCURRENT",29:"note",31:"NOTE_TEXT",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",38:"CLICK",39:"STRING",40:"HREF",41:"classDef",42:"CLASSDEF_ID",43:"CLASSDEF_STYLEOPTS",44:"DEFAULT",45:"style",46:"STYLE_IDS",47:"STYLEDEF_STYLEOPTS",48:"class",49:"CLASSENTITY_IDS",50:"STYLECLASS",51:"direction_tb",52:"direction_bt",53:"direction_rl",54:"direction_lr",56:";",57:"EDGE_STATE",58:"STYLE_SEPARATOR",59:"left_of",60:"right_of"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,3],[9,4],[9,1],[9,2],[9,1],[9,4],[9,3],[9,6],[9,1],[9,1],[9,1],[9,1],[9,4],[9,4],[9,1],[9,2],[9,2],[9,1],[9,5],[9,5],[10,3],[10,3],[11,3],[12,3],[32,1],[32,1],[32,1],[32,1],[55,1],[55,1],[13,1],[13,1],[13,3],[13,3],[30,1],[30,1]],performAction:c(function(o,h,a,y,T,i,Q){var l=i.length-1;switch(T){case 3:return y.setRootDoc(i[l]),i[l];break;case 4:this.$=[];break;case 5:i[l]!="nl"&&(i[l-1].push(i[l]),this.$=i[l-1]);break;case 6:case 7:this.$=i[l];break;case 8:this.$="nl";break;case 12:this.$=i[l];break;case 13:let ct=i[l-1];ct.description=y.trimColon(i[l]),this.$=ct;break;case 14:this.$={stmt:"relation",state1:i[l-2],state2:i[l]};break;case 15:let ht=y.trimColon(i[l]);this.$={stmt:"relation",state1:i[l-3],state2:i[l-1],description:ht};break;case 19:this.$={stmt:"state",id:i[l-3],type:"default",description:"",doc:i[l-1]};break;case 20:var V=i[l],K=i[l-2].trim();if(i[l].match(":")){var Z=i[l].split(":");V=Z[0],K=[K,Z[1]]}this.$={stmt:"state",id:V,type:"default",description:K};break;case 21:this.$={stmt:"state",id:i[l-3],type:"default",description:i[l-5],doc:i[l-1]};break;case 22:this.$={stmt:"state",id:i[l],type:"fork"};break;case 23:this.$={stmt:"state",id:i[l],type:"join"};break;case 24:this.$={stmt:"state",id:i[l],type:"choice"};break;case 25:this.$={stmt:"state",id:y.getDividerId(),type:"divider"};break;case 26:this.$={stmt:"state",id:i[l-1].trim(),note:{position:i[l-2].trim(),text:i[l].trim()}};break;case 29:this.$=i[l].trim(),y.setAccTitle(this.$);break;case 30:case 31:this.$=i[l].trim(),y.setAccDescription(this.$);break;case 32:this.$={stmt:"click",id:i[l-3],url:i[l-2],tooltip:i[l-1]};break;case 33:this.$={stmt:"click",id:i[l-3],url:i[l-1],tooltip:""};break;case 34:case 35:this.$={stmt:"classDef",id:i[l-1].trim(),classes:i[l].trim()};break;case 36:this.$={stmt:"style",id:i[l-1].trim(),styleClass:i[l].trim()};break;case 37:this.$={stmt:"applyClass",id:i[l-1].trim(),styleClass:i[l].trim()};break;case 38:y.setDirection("TB"),this.$={stmt:"dir",value:"TB"};break;case 39:y.setDirection("BT"),this.$={stmt:"dir",value:"BT"};break;case 40:y.setDirection("RL"),this.$={stmt:"dir",value:"RL"};break;case 41:y.setDirection("LR"),this.$={stmt:"dir",value:"LR"};break;case 44:case 45:this.$={stmt:"state",id:i[l].trim(),type:"default",description:""};break;case 46:this.$={stmt:"state",id:i[l-2].trim(),classes:[i[l].trim()],type:"default",description:""};break;case 47:this.$={stmt:"state",id:i[l-2].trim(),classes:[i[l].trim()],type:"default",description:""};break}},"anonymous"),table:[{3:1,4:e,5:s,6:n},{1:[3]},{3:5,4:e,5:s,6:n},{3:6,4:e,5:s,6:n},t([1,4,5,16,17,19,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],r,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:d,5:u,8:8,9:10,10:12,11:13,12:14,13:15,16:f,17:S,19:m,22:E,24:b,25:L,26:x,27:p,28:k,29:O,32:25,33:I,35:G,37:R,38:P,41:et,45:st,48:it,51:rt,52:nt,53:at,54:ot,57:z},t(g,[2,5]),{9:39,10:12,11:13,12:14,13:15,16:f,17:S,19:m,22:E,24:b,25:L,26:x,27:p,28:k,29:O,32:25,33:I,35:G,37:R,38:P,41:et,45:st,48:it,51:rt,52:nt,53:at,54:ot,57:z},t(g,[2,7]),t(g,[2,8]),t(g,[2,9]),t(g,[2,10]),t(g,[2,11]),t(g,[2,12],{14:[1,40],15:[1,41]}),t(g,[2,16]),{18:[1,42]},t(g,[2,18],{20:[1,43]}),{23:[1,44]},t(g,[2,22]),t(g,[2,23]),t(g,[2,24]),t(g,[2,25]),{30:45,31:[1,46],59:[1,47],60:[1,48]},t(g,[2,28]),{34:[1,49]},{36:[1,50]},t(g,[2,31]),{13:51,24:b,57:z},{42:[1,52],44:[1,53]},{46:[1,54]},{49:[1,55]},t(lt,[2,44],{58:[1,56]}),t(lt,[2,45],{58:[1,57]}),t(g,[2,38]),t(g,[2,39]),t(g,[2,40]),t(g,[2,41]),t(g,[2,6]),t(g,[2,13]),{13:58,24:b,57:z},t(g,[2,17]),t(Ut,r,{7:59}),{24:[1,60]},{24:[1,61]},{23:[1,62]},{24:[2,48]},{24:[2,49]},t(g,[2,29]),t(g,[2,30]),{39:[1,63],40:[1,64]},{43:[1,65]},{43:[1,66]},{47:[1,67]},{50:[1,68]},{24:[1,69]},{24:[1,70]},t(g,[2,14],{14:[1,71]}),{4:d,5:u,8:8,9:10,10:12,11:13,12:14,13:15,16:f,17:S,19:m,21:[1,72],22:E,24:b,25:L,26:x,27:p,28:k,29:O,32:25,33:I,35:G,37:R,38:P,41:et,45:st,48:it,51:rt,52:nt,53:at,54:ot,57:z},t(g,[2,20],{20:[1,73]}),{31:[1,74]},{24:[1,75]},{39:[1,76]},{39:[1,77]},t(g,[2,34]),t(g,[2,35]),t(g,[2,36]),t(g,[2,37]),t(lt,[2,46]),t(lt,[2,47]),t(g,[2,15]),t(g,[2,19]),t(Ut,r,{7:78}),t(g,[2,26]),t(g,[2,27]),{5:[1,79]},{5:[1,80]},{4:d,5:u,8:8,9:10,10:12,11:13,12:14,13:15,16:f,17:S,19:m,21:[1,81],22:E,24:b,25:L,26:x,27:p,28:k,29:O,32:25,33:I,35:G,37:R,38:P,41:et,45:st,48:it,51:rt,52:nt,53:at,54:ot,57:z},t(g,[2,32]),t(g,[2,33]),t(g,[2,21])],defaultActions:{5:[2,1],6:[2,2],47:[2,48],48:[2,49]},parseError:c(function(o,h){if(h.recoverable)this.trace(o);else{var a=new Error(o);throw a.hash=h,a}},"parseError"),parse:c(function(o){var h=this,a=[0],y=[],T=[null],i=[],Q=this.table,l="",V=0,K=0,Z=0,ct=2,ht=1,ke=i.slice.call(arguments,1),D=Object.create(this.lexer),U={yy:{}};for(var Dt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Dt)&&(U.yy[Dt]=this.yy[Dt]);D.setInput(o,U.yy),U.yy.lexer=D,U.yy.parser=this,typeof D.yylloc>"u"&&(D.yylloc={});var kt=D.yylloc;i.push(kt);var xe=D.options&&D.options.ranges;typeof U.yy.parseError=="function"?this.parseError=U.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Me(v){a.length=a.length-2*v,T.length=T.length-v,i.length=i.length-v}c(Me,"popStack");function Ce(){var v;return v=y.pop()||D.lex()||ht,typeof v!="number"&&(v instanceof Array&&(y=v,v=y.pop()),v=h.symbols_[v]||v),v}c(Ce,"lex");for(var C,xt,H,w,Ye,Ct,X={},dt,$,Ht,ut;;){if(H=a[a.length-1],this.defaultActions[H]?w=this.defaultActions[H]:((C===null||typeof C>"u")&&(C=Ce()),w=Q[H]&&Q[H][C]),typeof w>"u"||!w.length||!w[0]){var At="";ut=[];for(dt in Q[H])this.terminals_[dt]&&dt>ct&&ut.push("'"+this.terminals_[dt]+"'");D.showPosition?At="Parse error on line "+(V+1)+`:
`+D.showPosition()+`
Expecting `+ut.join(", ")+", got '"+(this.terminals_[C]||C)+"'":At="Parse error on line "+(V+1)+": Unexpected "+(C==ht?"end of input":"'"+(this.terminals_[C]||C)+"'"),this.parseError(At,{text:D.match,token:this.terminals_[C]||C,line:D.yylineno,loc:kt,expected:ut})}if(w[0]instanceof Array&&w.length>1)throw new Error("Parse Error: multiple actions possible at state: "+H+", token: "+C);switch(w[0]){case 1:a.push(C),T.push(D.yytext),i.push(D.yylloc),a.push(w[1]),C=null,xt?(C=xt,xt=null):(K=D.yyleng,l=D.yytext,V=D.yylineno,kt=D.yylloc,Z>0&&Z--);break;case 2:if($=this.productions_[w[1]][1],X.$=T[T.length-$],X._$={first_line:i[i.length-($||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-($||1)].first_column,last_column:i[i.length-1].last_column},xe&&(X._$.range=[i[i.length-($||1)].range[0],i[i.length-1].range[1]]),Ct=this.performAction.apply(X,[l,K,V,U.yy,w[1],T,i].concat(ke)),typeof Ct<"u")return Ct;$&&(a=a.slice(0,-1*$*2),T=T.slice(0,-1*$),i=i.slice(0,-1*$)),a.push(this.productions_[w[1]][0]),T.push(X.$),i.push(X._$),Ht=Q[a[a.length-2]][a[a.length-1]],a.push(Ht);break;case 3:return!0}}return!0},"parse")},De=function(){var Y={EOF:1,parseError:c(function(h,a){if(this.yy.parser)this.yy.parser.parseError(h,a);else throw new Error(h)},"parseError"),setInput:c(function(o,h){return this.yy=h||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:c(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var h=o.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:c(function(o){var h=o.length,a=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var y=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var T=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===y.length?this.yylloc.first_column:0)+y[y.length-a.length].length-a[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[T[0],T[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:c(function(){return this._more=!0,this},"more"),reject:c(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:c(function(o){this.unput(this.match.slice(o))},"less"),pastInput:c(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:c(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:c(function(){var o=this.pastInput(),h=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:c(function(o,h){var a,y,T;if(this.options.backtrack_lexer&&(T={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(T.yylloc.range=this.yylloc.range.slice(0))),y=o[0].match(/(?:\r\n?|\n).*/g),y&&(this.yylineno+=y.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:y?y[y.length-1].length-y[y.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],a=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack){for(var i in T)this[i]=T[i];return!1}return!1},"test_match"),next:c(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,h,a,y;this._more||(this.yytext="",this.match="");for(var T=this._currentRules(),i=0;i<T.length;i++)if(a=this._input.match(this.rules[T[i]]),a&&(!h||a[0].length>h[0].length)){if(h=a,y=i,this.options.backtrack_lexer){if(o=this.test_match(a,T[i]),o!==!1)return o;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(o=this.test_match(h,T[y]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:c(function(){var h=this.next();return h||this.lex()},"lex"),begin:c(function(h){this.conditionStack.push(h)},"begin"),popState:c(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:c(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:c(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:c(function(h){this.begin(h)},"pushState"),stateStackSize:c(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:c(function(h,a,y,T){var i=T;switch(y){case 0:return 38;case 1:return 40;case 2:return 39;case 3:return 44;case 4:return 51;case 5:return 52;case 6:return 53;case 7:return 54;case 8:break;case 9:break;case 10:return 5;case 11:break;case 12:break;case 13:break;case 14:break;case 15:return this.pushState("SCALE"),17;break;case 16:return 18;case 17:this.popState();break;case 18:return this.begin("acc_title"),33;break;case 19:return this.popState(),"acc_title_value";break;case 20:return this.begin("acc_descr"),35;break;case 21:return this.popState(),"acc_descr_value";break;case 22:this.begin("acc_descr_multiline");break;case 23:this.popState();break;case 24:return"acc_descr_multiline_value";case 25:return this.pushState("CLASSDEF"),41;break;case 26:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";break;case 27:return this.popState(),this.pushState("CLASSDEFID"),42;break;case 28:return this.popState(),43;break;case 29:return this.pushState("CLASS"),48;break;case 30:return this.popState(),this.pushState("CLASS_STYLE"),49;break;case 31:return this.popState(),50;break;case 32:return this.pushState("STYLE"),45;break;case 33:return this.popState(),this.pushState("STYLEDEF_STYLES"),46;break;case 34:return this.popState(),47;break;case 35:return this.pushState("SCALE"),17;break;case 36:return 18;case 37:this.popState();break;case 38:this.pushState("STATE");break;case 39:return this.popState(),a.yytext=a.yytext.slice(0,-8).trim(),25;break;case 40:return this.popState(),a.yytext=a.yytext.slice(0,-8).trim(),26;break;case 41:return this.popState(),a.yytext=a.yytext.slice(0,-10).trim(),27;break;case 42:return this.popState(),a.yytext=a.yytext.slice(0,-8).trim(),25;break;case 43:return this.popState(),a.yytext=a.yytext.slice(0,-8).trim(),26;break;case 44:return this.popState(),a.yytext=a.yytext.slice(0,-10).trim(),27;break;case 45:return 51;case 46:return 52;case 47:return 53;case 48:return 54;case 49:this.pushState("STATE_STRING");break;case 50:return this.pushState("STATE_ID"),"AS";break;case 51:return this.popState(),"ID";break;case 52:this.popState();break;case 53:return"STATE_DESCR";case 54:return 19;case 55:this.popState();break;case 56:return this.popState(),this.pushState("struct"),20;break;case 57:break;case 58:return this.popState(),21;break;case 59:break;case 60:return this.begin("NOTE"),29;break;case 61:return this.popState(),this.pushState("NOTE_ID"),59;break;case 62:return this.popState(),this.pushState("NOTE_ID"),60;break;case 63:this.popState(),this.pushState("FLOATING_NOTE");break;case 64:return this.popState(),this.pushState("FLOATING_NOTE_ID"),"AS";break;case 65:break;case 66:return"NOTE_TEXT";case 67:return this.popState(),"ID";break;case 68:return this.popState(),this.pushState("NOTE_TEXT"),24;break;case 69:return this.popState(),a.yytext=a.yytext.substr(2).trim(),31;break;case 70:return this.popState(),a.yytext=a.yytext.slice(0,-8).trim(),31;break;case 71:return 6;case 72:return 6;case 73:return 16;case 74:return 57;case 75:return 24;case 76:return a.yytext=a.yytext.trim(),14;break;case 77:return 15;case 78:return 28;case 79:return 58;case 80:return 5;case 81:return"INVALID"}},"anonymous"),rules:[/^(?:click\b)/i,/^(?:href\b)/i,/^(?:"[^"]*")/i,/^(?:default\b)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:[\s]+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:scale\s+)/i,/^(?:\d+)/i,/^(?:\s+width\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:classDef\s+)/i,/^(?:DEFAULT\s+)/i,/^(?:\w+\s+)/i,/^(?:[^\n]*)/i,/^(?:class\s+)/i,/^(?:(\w+)+((,\s*\w+)*))/i,/^(?:[^\n]*)/i,/^(?:style\s+)/i,/^(?:[\w,]+\s+)/i,/^(?:[^\n]*)/i,/^(?:scale\s+)/i,/^(?:\d+)/i,/^(?:\s+width\b)/i,/^(?:state\s+)/i,/^(?:.*<<fork>>)/i,/^(?:.*<<join>>)/i,/^(?:.*<<choice>>)/i,/^(?:.*\[\[fork\]\])/i,/^(?:.*\[\[join\]\])/i,/^(?:.*\[\[choice\]\])/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:["])/i,/^(?:\s*as\s+)/i,/^(?:[^\n\{]*)/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[^\n\s\{]+)/i,/^(?:\n)/i,/^(?:\{)/i,/^(?:%%(?!\{)[^\n]*)/i,/^(?:\})/i,/^(?:[\n])/i,/^(?:note\s+)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:")/i,/^(?:\s*as\s*)/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[^\n]*)/i,/^(?:\s*[^:\n\s\-]+)/i,/^(?:\s*:[^:\n;]+)/i,/^(?:[\s\S]*?end note\b)/i,/^(?:stateDiagram\s+)/i,/^(?:stateDiagram-v2\s+)/i,/^(?:hide empty description\b)/i,/^(?:\[\*\])/i,/^(?:[^:\n\s\-\{]+)/i,/^(?:\s*:[^:\n;]+)/i,/^(?:-->)/i,/^(?:--)/i,/^(?::::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{LINE:{rules:[12,13],inclusive:!1},struct:{rules:[12,13,25,29,32,38,45,46,47,48,57,58,59,60,74,75,76,77,78],inclusive:!1},FLOATING_NOTE_ID:{rules:[67],inclusive:!1},FLOATING_NOTE:{rules:[64,65,66],inclusive:!1},NOTE_TEXT:{rules:[69,70],inclusive:!1},NOTE_ID:{rules:[68],inclusive:!1},NOTE:{rules:[61,62,63],inclusive:!1},STYLEDEF_STYLEOPTS:{rules:[],inclusive:!1},STYLEDEF_STYLES:{rules:[34],inclusive:!1},STYLE_IDS:{rules:[],inclusive:!1},STYLE:{rules:[33],inclusive:!1},CLASS_STYLE:{rules:[31],inclusive:!1},CLASS:{rules:[30],inclusive:!1},CLASSDEFID:{rules:[28],inclusive:!1},CLASSDEF:{rules:[26,27],inclusive:!1},acc_descr_multiline:{rules:[23,24],inclusive:!1},acc_descr:{rules:[21],inclusive:!1},acc_title:{rules:[19],inclusive:!1},SCALE:{rules:[16,17,36,37],inclusive:!1},ALIAS:{rules:[],inclusive:!1},STATE_ID:{rules:[51],inclusive:!1},STATE_STRING:{rules:[52,53],inclusive:!1},FORK_STATE:{rules:[],inclusive:!1},STATE:{rules:[12,13,39,40,41,42,43,44,49,50,54,55,56],inclusive:!1},ID:{rules:[12,13],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,13,14,15,18,20,22,25,29,32,35,38,56,60,71,72,73,74,75,76,77,79,80,81],inclusive:!0}}};return Y}();_t.lexer=De;function bt(){this.yy={}}return c(bt,"Parser"),bt.prototype=_t,_t.Parser=bt,new bt}();Lt.parser=Lt;var Be=Lt;var ie="TB",St="TB",It="dir",B="state",j="root",tt="relation",re="classDef",ne="style",ae="applyClass",W="default",pt="divider",Rt="fill:none",vt="fill: #333",Nt="c",Ot="text",wt="normal",ft="rect",gt="rectWithTitle",oe="stateStart",le="stateEnd",Gt="divider",Pt="roundedWithTitle",ce="note",he="noteGroup",J="statediagram",Ae="state",de=`${J}-${Ae}`,$t="transition",Le="note",Ie="note-edge",ue=`${$t} ${Ie}`,Se=`${J}-${Le}`,Re="cluster",pe=`${J}-${Re}`,ve="cluster-alt",fe=`${J}-${ve}`,Mt="parent",Yt="note",ge="state",yt="----",ye=`${yt}${Yt}`,Bt=`${yt}${Mt}`;var Ft=c((t,e=St)=>{if(!t.doc)return e;let s=e;for(let n of t.doc)n.stmt==="dir"&&(s=n.value);return s},"getDir"),Ne=c(function(t,e){return e.db.getClasses()},"getClasses"),Oe=c(async function(t,e,s,n){_.info("REF0:"),_.info("Drawing state diagram (v2)",e);let{securityLevel:r,state:d,layout:u}=N();n.db.extract(n.db.getRootDocV2());let f=n.db.getData(),S=te(e,r);f.type=n.type,f.layoutAlgorithm=u,f.nodeSpacing=d?.nodeSpacing||50,f.rankSpacing=d?.rankSpacing||50,f.markers=["barb"],f.diagramId=e,await ee(f,S);let m=8;try{(typeof n.db.getLinks=="function"?n.db.getLinks():new Map).forEach((b,L)=>{let x=typeof L=="string"?L:typeof L?.id=="string"?L.id:"";if(!x){_.warn("\u26A0\uFE0F Invalid or missing stateId from key:",JSON.stringify(L));return}let p=S.node()?.querySelectorAll("g"),k;if(p?.forEach(R=>{R.textContent?.trim()===x&&(k=R)}),!k){_.warn("\u26A0\uFE0F Could not find node matching text:",x);return}let O=k.parentNode;if(!O){_.warn("\u26A0\uFE0F Node has no parent, cannot wrap:",x);return}let I=document.createElementNS("http://www.w3.org/2000/svg","a"),G=b.url.replace(/^"+|"+$/g,"");if(I.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",G),I.setAttribute("target","_blank"),b.tooltip){let R=b.tooltip.replace(/^"+|"+$/g,"");I.setAttribute("title",R)}O.replaceChild(I,k),I.appendChild(k),_.info("\u{1F517} Wrapped node in <a> tag for:",x,b.url)})}catch(E){_.error("\u274C Error injecting clickable links:",E)}Zt.insertTitle(S,"statediagramTitleText",d?.titleTopMargin??25,n.db.getDiagramTitle()),se(S,m,J,d?.useMaxWidth??!0)},"draw"),qe={getClasses:Ne,draw:Oe,getDir:Ft};var Tt=new Map,F=0;function Vt(t="",e=0,s="",n=yt){let r=s!==null&&s.length>0?`${n}${s}`:"";return`${ge}-${t}${r}-${e}`}c(Vt,"stateDomId");var we=c((t,e,s,n,r,d,u,f)=>{_.trace("items",e),e.forEach(S=>{switch(S.stmt){case B:q(t,S,s,n,r,d,u,f);break;case W:q(t,S,s,n,r,d,u,f);break;case tt:{q(t,S.state1,s,n,r,d,u,f),q(t,S.state2,s,n,r,d,u,f);let m={id:"edge"+F,start:S.state1.id,end:S.state2.id,arrowhead:"normal",arrowTypeEnd:"arrow_barb",style:Rt,labelStyle:"",label:M.sanitizeText(S.description??"",N()),arrowheadStyle:vt,labelpos:Nt,labelType:Ot,thickness:wt,classes:$t,look:u};r.push(m),F++}break}})},"setupDoc"),me=c((t,e=St)=>{let s=e;if(t.doc)for(let n of t.doc)n.stmt==="dir"&&(s=n.value);return s},"getDir");function mt(t,e,s){if(!e.id||e.id==="</join></fork>"||e.id==="</choice>")return;e.cssClasses&&(Array.isArray(e.cssCompiledStyles)||(e.cssCompiledStyles=[]),e.cssClasses.split(" ").forEach(r=>{let d=s.get(r);d&&(e.cssCompiledStyles=[...e.cssCompiledStyles??[],...d.styles])}));let n=t.find(r=>r.id===e.id);n?Object.assign(n,e):t.push(e)}c(mt,"insertOrUpdateNode");function Ge(t){return t?.classes?.join(" ")??""}c(Ge,"getClassesFromDbInfo");function Pe(t){return t?.styles??[]}c(Pe,"getStylesFromDbInfo");var q=c((t,e,s,n,r,d,u,f)=>{let S=e.id,m=s.get(S),E=Ge(m),b=Pe(m),L=N();if(_.info("dataFetcher parsedItem",e,m,b),S!=="root"){let x=ft;e.start===!0?x=oe:e.start===!1&&(x=le),e.type!==W&&(x=e.type),Tt.get(S)||Tt.set(S,{id:S,shape:x,description:M.sanitizeText(S,L),cssClasses:`${E} ${de}`,cssStyles:b});let p=Tt.get(S);e.description&&(Array.isArray(p.description)?(p.shape=gt,p.description.push(e.description)):p.description?.length&&p.description.length>0?(p.shape=gt,p.description===S?p.description=[e.description]:p.description=[p.description,e.description]):(p.shape=ft,p.description=e.description),p.description=M.sanitizeTextOrArray(p.description,L)),p.description?.length===1&&p.shape===gt&&(p.type==="group"?p.shape=Pt:p.shape=ft),!p.type&&e.doc&&(_.info("Setting cluster for XCX",S,me(e)),p.type="group",p.isGroup=!0,p.dir=me(e),p.shape=e.type===pt?Gt:Pt,p.cssClasses=`${p.cssClasses} ${pe} ${d?fe:""}`);let k={labelStyle:"",shape:p.shape,label:p.description,cssClasses:p.cssClasses,cssCompiledStyles:[],cssStyles:p.cssStyles,id:S,dir:p.dir,domId:Vt(S,F),type:p.type,isGroup:p.type==="group",padding:8,rx:10,ry:10,look:u};if(k.shape===Gt&&(k.label=""),t&&t.id!=="root"&&(_.trace("Setting node ",S," to be child of its parent ",t.id),k.parentId=t.id),k.centerLabel=!0,e.note){let O={labelStyle:"",shape:ce,label:e.note.text,cssClasses:Se,cssStyles:[],cssCompiledStyles:[],id:S+ye+"-"+F,domId:Vt(S,F,Yt),type:p.type,isGroup:p.type==="group",padding:L.flowchart?.padding,look:u,position:e.note.position},I=S+Bt,G={labelStyle:"",shape:he,label:e.note.text,cssClasses:p.cssClasses,cssStyles:[],id:S+Bt,domId:Vt(S,F,Mt),type:"group",isGroup:!0,padding:16,look:u,position:e.note.position};F++,G.id=I,O.parentId=I,mt(n,G,f),mt(n,O,f),mt(n,k,f);let R=S,P=O.id;e.note.position==="left of"&&(R=O.id,P=S),r.push({id:R+"-"+P,start:R,end:P,arrowhead:"none",arrowTypeEnd:"",style:Rt,labelStyle:"",classes:ue,arrowheadStyle:vt,labelpos:Nt,labelType:Ot,thickness:wt,look:u})}else mt(n,k,f)}e.doc&&(_.trace("Adding nodes children "),we(e,e.doc,s,n,r,!d,u,f))},"dataFetcher"),Te=c(()=>{Tt.clear(),F=0},"reset");var A={START_NODE:"[*]",START_TYPE:"start",END_NODE:"[*]",END_TYPE:"end",COLOR_KEYWORD:"color",FILL_KEYWORD:"fill",BG_FILL:"bgFill",STYLECLASS_SEP:","},Ee=c(()=>new Map,"newClassesList"),_e=c(()=>({relations:[],states:new Map,documents:{}}),"newDoc"),Et=c(t=>JSON.parse(JSON.stringify(t)),"clone"),be=class{constructor(e){this.version=e;this.nodes=[];this.edges=[];this.rootDoc=[];this.classes=Ee();this.documents={root:_e()};this.currentDocument=this.documents.root;this.startEndCount=0;this.dividerCnt=0;this.links=new Map;this.getAccTitle=zt;this.setAccTitle=Wt;this.getAccDescription=Xt;this.setAccDescription=Kt;this.setDiagramTitle=Jt;this.getDiagramTitle=qt;this.clear(),this.setRootDoc=this.setRootDoc.bind(this),this.getDividerId=this.getDividerId.bind(this),this.setDirection=this.setDirection.bind(this),this.trimColon=this.trimColon.bind(this)}static{c(this,"StateDB")}static{this.relationType={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3}}extract(e){this.clear(!0);for(let r of Array.isArray(e)?e:e.doc)switch(r.stmt){case B:this.addState(r.id.trim(),r.type,r.doc,r.description,r.note);break;case tt:this.addRelation(r.state1,r.state2,r.description);break;case re:this.addStyleClass(r.id.trim(),r.classes);break;case ne:this.handleStyleDef(r);break;case ae:this.setCssClass(r.id.trim(),r.styleClass);break;case"click":this.addLink(r.id,r.url,r.tooltip);break}let s=this.getStates(),n=N();Te(),q(void 0,this.getRootDocV2(),s,this.nodes,this.edges,!0,n.look,this.classes);for(let r of this.nodes)if(Array.isArray(r.label)){if(r.description=r.label.slice(1),r.isGroup&&r.description.length>0)throw new Error(`Group nodes can only have label. Remove the additional description for node [${r.id}]`);r.label=r.label[0]}}handleStyleDef(e){let s=e.id.trim().split(","),n=e.styleClass.split(",");for(let r of s){let d=this.getState(r);if(!d){let u=r.trim();this.addState(u),d=this.getState(u)}d&&(d.styles=n.map(u=>u.replace(/;/g,"")?.trim()))}}setRootDoc(e){_.info("Setting root doc",e),this.rootDoc=e,this.version===1?this.extract(e):this.extract(this.getRootDocV2())}docTranslator(e,s,n){if(s.stmt===tt){this.docTranslator(e,s.state1,!0),this.docTranslator(e,s.state2,!1);return}if(s.stmt===B&&(s.id===A.START_NODE?(s.id=e.id+(n?"_start":"_end"),s.start=n):s.id=s.id.trim()),s.stmt!==j&&s.stmt!==B||!s.doc)return;let r=[],d=[];for(let u of s.doc)if(u.type===pt){let f=Et(u);f.doc=Et(d),r.push(f),d=[]}else d.push(u);if(r.length>0&&d.length>0){let u={stmt:B,id:Qt(),type:"divider",doc:Et(d)};r.push(Et(u)),s.doc=r}s.doc.forEach(u=>this.docTranslator(s,u,!0))}getRootDocV2(){return this.docTranslator({id:j,stmt:j},{id:j,stmt:j,doc:this.rootDoc},!0),{id:j,doc:this.rootDoc}}addState(e,s=W,n=void 0,r=void 0,d=void 0,u=void 0,f=void 0,S=void 0){let m=e?.trim();if(!this.currentDocument.states.has(m))_.info("Adding state ",m,r),this.currentDocument.states.set(m,{stmt:B,id:m,descriptions:[],type:s,doc:n,note:d,classes:[],styles:[],textStyles:[]});else{let E=this.currentDocument.states.get(m);if(!E)throw new Error(`State not found: ${m}`);E.doc||(E.doc=n),E.type||(E.type=s)}if(r&&(_.info("Setting state description",m,r),(Array.isArray(r)?r:[r]).forEach(b=>this.addDescription(m,b.trim()))),d){let E=this.currentDocument.states.get(m);if(!E)throw new Error(`State not found: ${m}`);E.note=d,E.note.text=M.sanitizeText(E.note.text,N())}u&&(_.info("Setting state classes",m,u),(Array.isArray(u)?u:[u]).forEach(b=>this.setCssClass(m,b.trim()))),f&&(_.info("Setting state styles",m,f),(Array.isArray(f)?f:[f]).forEach(b=>this.setStyle(m,b.trim()))),S&&(_.info("Setting state styles",m,f),(Array.isArray(S)?S:[S]).forEach(b=>this.setTextStyle(m,b.trim())))}clear(e){this.nodes=[],this.edges=[],this.documents={root:_e()},this.currentDocument=this.documents.root,this.startEndCount=0,this.classes=Ee(),e||(this.links=new Map,jt())}getState(e){return this.currentDocument.states.get(e)}getStates(){return this.currentDocument.states}logDocuments(){_.info("Documents = ",this.documents)}getRelations(){return this.currentDocument.relations}addLink(e,s,n){this.links.set(e,{url:s,tooltip:n}),_.warn("Adding link",e,s,n)}getLinks(){return this.links}startIdIfNeeded(e=""){return e===A.START_NODE?(this.startEndCount++,`${A.START_TYPE}${this.startEndCount}`):e}startTypeIfNeeded(e="",s=W){return e===A.START_NODE?A.START_TYPE:s}endIdIfNeeded(e=""){return e===A.END_NODE?(this.startEndCount++,`${A.END_TYPE}${this.startEndCount}`):e}endTypeIfNeeded(e="",s=W){return e===A.END_NODE?A.END_TYPE:s}addRelationObjs(e,s,n=""){let r=this.startIdIfNeeded(e.id.trim()),d=this.startTypeIfNeeded(e.id.trim(),e.type),u=this.startIdIfNeeded(s.id.trim()),f=this.startTypeIfNeeded(s.id.trim(),s.type);this.addState(r,d,e.doc,e.description,e.note,e.classes,e.styles,e.textStyles),this.addState(u,f,s.doc,s.description,s.note,s.classes,s.styles,s.textStyles),this.currentDocument.relations.push({id1:r,id2:u,relationTitle:M.sanitizeText(n,N())})}addRelation(e,s,n){if(typeof e=="object"&&typeof s=="object")this.addRelationObjs(e,s,n);else if(typeof e=="string"&&typeof s=="string"){let r=this.startIdIfNeeded(e.trim()),d=this.startTypeIfNeeded(e),u=this.endIdIfNeeded(s.trim()),f=this.endTypeIfNeeded(s);this.addState(r,d),this.addState(u,f),this.currentDocument.relations.push({id1:r,id2:u,relationTitle:n?M.sanitizeText(n,N()):void 0})}}addDescription(e,s){let n=this.currentDocument.states.get(e),r=s.startsWith(":")?s.replace(":","").trim():s;n?.descriptions?.push(M.sanitizeText(r,N()))}cleanupLabel(e){return e.startsWith(":")?e.slice(2).trim():e.trim()}getDividerId(){return this.dividerCnt++,`divider-id-${this.dividerCnt}`}addStyleClass(e,s=""){this.classes.has(e)||this.classes.set(e,{id:e,styles:[],textStyles:[]});let n=this.classes.get(e);s&&n&&s.split(A.STYLECLASS_SEP).forEach(r=>{let d=r.replace(/([^;]*);/,"$1").trim();if(RegExp(A.COLOR_KEYWORD).exec(r)){let f=d.replace(A.FILL_KEYWORD,A.BG_FILL).replace(A.COLOR_KEYWORD,A.FILL_KEYWORD);n.textStyles.push(f)}n.styles.push(d)})}getClasses(){return this.classes}setCssClass(e,s){e.split(",").forEach(n=>{let r=this.getState(n);if(!r){let d=n.trim();this.addState(d),r=this.getState(d)}r?.classes?.push(s)})}setStyle(e,s){this.getState(e)?.styles?.push(s)}setTextStyle(e,s){this.getState(e)?.textStyles?.push(s)}getDirectionStatement(){return this.rootDoc.find(e=>e.stmt===It)}getDirection(){return this.getDirectionStatement()?.value??ie}setDirection(e){let s=this.getDirectionStatement();s?s.value=e:this.rootDoc.unshift({stmt:It,value:e})}trimColon(e){return e.startsWith(":")?e.slice(1).trim():e.trim()}getData(){let e=N();return{nodes:this.nodes,edges:this.edges,other:{},config:e,direction:Ft(this.getRootDocV2())}}getConfig(){return N().state}};var $e=c(t=>`
defs #statediagram-barbEnd {
    fill: ${t.transitionColor};
    stroke: ${t.transitionColor};
  }
g.stateGroup text {
  fill: ${t.nodeBorder};
  stroke: none;
  font-size: 10px;
}
g.stateGroup text {
  fill: ${t.textColor};
  stroke: none;
  font-size: 10px;

}
g.stateGroup .state-title {
  font-weight: bolder;
  fill: ${t.stateLabelColor};
}

g.stateGroup rect {
  fill: ${t.mainBkg};
  stroke: ${t.nodeBorder};
}

g.stateGroup line {
  stroke: ${t.lineColor};
  stroke-width: 1;
}

.transition {
  stroke: ${t.transitionColor};
  stroke-width: 1;
  fill: none;
}

.stateGroup .composit {
  fill: ${t.background};
  border-bottom: 1px
}

.stateGroup .alt-composit {
  fill: #e0e0e0;
  border-bottom: 1px
}

.state-note {
  stroke: ${t.noteBorderColor};
  fill: ${t.noteBkgColor};

  text {
    fill: ${t.noteTextColor};
    stroke: none;
    font-size: 10px;
  }
}

.stateLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${t.mainBkg};
  opacity: 0.5;
}

.edgeLabel .label rect {
  fill: ${t.labelBackgroundColor};
  opacity: 0.5;
}
.edgeLabel {
  background-color: ${t.edgeLabelBackground};
  p {
    background-color: ${t.edgeLabelBackground};
  }
  rect {
    opacity: 0.5;
    background-color: ${t.edgeLabelBackground};
    fill: ${t.edgeLabelBackground};
  }
  text-align: center;
}
.edgeLabel .label text {
  fill: ${t.transitionLabelColor||t.tertiaryTextColor};
}
.label div .edgeLabel {
  color: ${t.transitionLabelColor||t.tertiaryTextColor};
}

.stateLabel text {
  fill: ${t.stateLabelColor};
  font-size: 10px;
  font-weight: bold;
}

.node circle.state-start {
  fill: ${t.specialStateColor};
  stroke: ${t.specialStateColor};
}

.node .fork-join {
  fill: ${t.specialStateColor};
  stroke: ${t.specialStateColor};
}

.node circle.state-end {
  fill: ${t.innerEndBackground};
  stroke: ${t.background};
  stroke-width: 1.5
}
.end-state-inner {
  fill: ${t.compositeBackground||t.background};
  // stroke: ${t.background};
  stroke-width: 1.5
}

.node rect {
  fill: ${t.stateBkg||t.mainBkg};
  stroke: ${t.stateBorder||t.nodeBorder};
  stroke-width: 1px;
}
.node polygon {
  fill: ${t.mainBkg};
  stroke: ${t.stateBorder||t.nodeBorder};;
  stroke-width: 1px;
}
#statediagram-barbEnd {
  fill: ${t.lineColor};
}

.statediagram-cluster rect {
  fill: ${t.compositeTitleBackground};
  stroke: ${t.stateBorder||t.nodeBorder};
  stroke-width: 1px;
}

.cluster-label, .nodeLabel {
  color: ${t.stateLabelColor};
  // line-height: 1;
}

.statediagram-cluster rect.outer {
  rx: 5px;
  ry: 5px;
}
.statediagram-state .divider {
  stroke: ${t.stateBorder||t.nodeBorder};
}

.statediagram-state .title-state {
  rx: 5px;
  ry: 5px;
}
.statediagram-cluster.statediagram-cluster .inner {
  fill: ${t.compositeBackground||t.background};
}
.statediagram-cluster.statediagram-cluster-alt .inner {
  fill: ${t.altBackground?t.altBackground:"#efefef"};
}

.statediagram-cluster .inner {
  rx:0;
  ry:0;
}

.statediagram-state rect.basic {
  rx: 5px;
  ry: 5px;
}
.statediagram-state rect.divider {
  stroke-dasharray: 10,10;
  fill: ${t.altBackground?t.altBackground:"#efefef"};
}

.note-edge {
  stroke-dasharray: 5;
}

.statediagram-note rect {
  fill: ${t.noteBkgColor};
  stroke: ${t.noteBorderColor};
  stroke-width: 1px;
  rx: 0;
  ry: 0;
}
.statediagram-note rect {
  fill: ${t.noteBkgColor};
  stroke: ${t.noteBorderColor};
  stroke-width: 1px;
  rx: 0;
  ry: 0;
}

.statediagram-note text {
  fill: ${t.noteTextColor};
}

.statediagram-note .nodeLabel {
  color: ${t.noteTextColor};
}
.statediagram .edgeLabel {
  color: red; // ${t.noteTextColor};
}

#dependencyStart, #dependencyEnd {
  fill: ${t.lineColor};
  stroke: ${t.lineColor};
  stroke-width: 1;
}

.statediagramTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${t.textColor};
}
`,"getStyles"),gs=$e;export{Be as a,qe as b,be as c,gs as d};
