{"version": 3, "file": "date-picker.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/props/date-picker.ts"], "sourcesContent": ["import { timePickerDefaultProps } from '@element-plus/components/time-picker'\nimport { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { IDatePickerType } from '../date-picker.type'\n\nexport const datePickerProps = buildProps({\n  ...timePickerDefaultProps,\n  /**\n   * @description type of the picker\n   */\n  type: {\n    type: definePropType<IDatePickerType>(String),\n    default: 'date',\n  },\n} as const)\n\nexport type DatePickerProps = ExtractPropTypes<typeof datePickerProps>\n"], "names": [], "mappings": ";;;AAEY,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,GAAG,sBAAsB;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}