import{E as m,H as c,M as l,O as p,a as e,b as n,c as u,d,e as s,f as S}from"./chunk-6TGVXIR7.mjs";var t=class extends p{static{e(this,"PieTokenBuilder")}constructor(){super(["pie","showData"])}};var o=class extends l{static{e(this,"PieValueConverter")}runCustomConverter(r,i,v){if(r.name==="PIE_SECTION_LABEL")return i.replace(/"/g,"").trim()}};var C={parser:{TokenBuilder:e(()=>new t,"TokenBuilder"),ValueConverter:e(()=>new o,"ValueConverter")}};function V(a=s){let r=d(u(a),m),i=d(n({shared:r}),c,C);return r.ServiceRegistry.register(i),{shared:r,Pie:i}}e(V,"createPieServices");export{C as a,V as b};
