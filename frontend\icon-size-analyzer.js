#!/usr/bin/env node

/**
 * 图标尺寸分析工具
 * 分析当前系统中图标尺寸过大的问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 推荐的图标尺寸标准（与中文文字协调）
const RECOMMENDED_SIZES = {
  'micro': { size: '12px', usage: '辅助信息、问题元数据' },
  'small': { size: '14px', usage: '导航链接、控制按钮、特色标签' },
  'medium': { size: '16px', usage: '面板标题、主要按钮、优势列表' },
  'large': { size: '18px', usage: '重要状态、星级评分' },
  'xlarge': { size: '20px', usage: '标签页、移动端菜单' },
  'xxlarge': { size: '24px', usage: '统计卡片（最大推荐）' }
};

// 分析图标尺寸使用情况
function analyzeIconSizes(filePath) {
  console.log(`\n🔍 分析文件: ${path.relative(__dirname, filePath)}`);
  
  const content = fs.readFileSync(filePath, 'utf8');
  const oversizedIcons = [];
  const appropriateIcons = [];
  
  // 查找图标样式定义
  const iconStyleRegex = /([^{]+\.el-icon[^{]*)\{[^}]*font-size:\s*(\d+)px[^}]*\}/g;
  let match;
  
  while ((match = iconStyleRegex.exec(content)) !== null) {
    const selector = match[1].trim();
    const size = parseInt(match[2]);
    
    const iconInfo = {
      selector,
      size,
      sizeCategory: getSizeCategory(size),
      recommendation: getRecommendation(size, selector)
    };
    
    if (size > 24) {
      oversizedIcons.push(iconInfo);
    } else {
      appropriateIcons.push(iconInfo);
    }
  }
  
  return { oversizedIcons, appropriateIcons };
}

// 获取尺寸分类
function getSizeCategory(size) {
  if (size <= 12) return 'micro';
  if (size <= 14) return 'small';
  if (size <= 16) return 'medium';
  if (size <= 18) return 'large';
  if (size <= 20) return 'xlarge';
  if (size <= 24) return 'xxlarge';
  return 'oversized';
}

// 获取修复建议
function getRecommendation(size, selector) {
  if (size > 32) {
    return {
      issue: '严重过大',
      suggestedSize: '20px',
      reason: '图标过大会破坏页面视觉平衡，建议缩小到20px以下'
    };
  } else if (size > 24) {
    return {
      issue: '过大',
      suggestedSize: '18px',
      reason: '与中文文字不协调，建议调整到18px或更小'
    };
  }
  
  return {
    issue: '合适',
    suggestedSize: `${size}px`,
    reason: '尺寸适中，与文字协调'
  };
}

// 生成修复建议
function generateFixSuggestions(results) {
  console.log(`\n🔧 图标尺寸修复建议:`);
  console.log(`=`.repeat(50));
  
  const allOversized = results.flatMap(r => r.oversizedIcons);
  const allAppropriate = results.flatMap(r => r.appropriateIcons);
  
  console.log(`📊 统计信息:`);
  console.log(`  - 过大图标: ${allOversized.length} 个`);
  console.log(`  - 合适图标: ${allAppropriate.length} 个`);
  console.log(`  - 修复率需求: ${Math.round(allOversized.length / (allOversized.length + allAppropriate.length) * 100)}%`);
  
  if (allOversized.length > 0) {
    console.log(`\n⚠️ 需要修复的过大图标:`);
    allOversized.forEach((icon, index) => {
      console.log(`\n${index + 1}. ${icon.selector}`);
      console.log(`   当前尺寸: ${icon.size}px (${icon.recommendation.issue})`);
      console.log(`   建议尺寸: ${icon.recommendation.suggestedSize}`);
      console.log(`   修复原因: ${icon.recommendation.reason}`);
    });
  }
  
  console.log(`\n📏 推荐的图标尺寸标准:`);
  Object.entries(RECOMMENDED_SIZES).forEach(([category, info]) => {
    console.log(`  - ${info.size}: ${info.usage}`);
  });
}

// 生成CSS修复代码
function generateFixCSS(results) {
  const allOversized = results.flatMap(r => r.oversizedIcons);
  
  if (allOversized.length === 0) {
    console.log(`\n✅ 没有发现过大的图标，无需修复！`);
    return;
  }
  
  console.log(`\n📝 CSS修复代码:`);
  console.log(`/* 图标尺寸修复 - 确保与中文文字协调 */`);
  
  allOversized.forEach(icon => {
    const cleanSelector = icon.selector.replace(/\s+/g, ' ').trim();
    console.log(`${cleanSelector} {`);
    console.log(`  font-size: ${icon.recommendation.suggestedSize} !important;`);
    console.log(`  /* 修复原因: ${icon.recommendation.reason} */`);
    console.log(`}`);
    console.log(``);
  });
  
  // 响应式修复
  console.log(`/* 响应式修复 */`);
  console.log(`@media (max-width: 768px) {`);
  allOversized.forEach(icon => {
    const cleanSelector = icon.selector.replace(/\s+/g, ' ').trim();
    const mobileSize = Math.max(12, parseInt(icon.recommendation.suggestedSize) - 2);
    console.log(`  ${cleanSelector} {`);
    console.log(`    font-size: ${mobileSize}px !important;`);
    console.log(`  }`);
  });
  console.log(`}`);
}

// 主函数
function main() {
  console.log('🎯 多模态面试评估系统 - 图标尺寸分析工具');
  console.log('=' .repeat(60));
  
  const filesToAnalyze = [
    'src/views/HomePage.vue',
    'src/views/InterviewingPage.vue',
    'src/App.vue'
  ];
  
  const results = [];
  
  filesToAnalyze.forEach(file => {
    const fullPath = path.join(__dirname, file);
    if (fs.existsSync(fullPath)) {
      const result = analyzeIconSizes(fullPath);
      results.push(result);
      
      if (result.oversizedIcons.length > 0) {
        console.log(`  ⚠️ 发现 ${result.oversizedIcons.length} 个过大图标`);
      } else {
        console.log(`  ✅ 图标尺寸合适`);
      }
    }
  });
  
  generateFixSuggestions(results);
  generateFixCSS(results);
  
  console.log(`\n✅ 分析完成！`);
}

main();
