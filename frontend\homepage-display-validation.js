#!/usr/bin/env node

/**
 * iFlytek Spark 主页显示问题修复验证脚本
 * 检查产品演示部分的显示问题修复效果
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 iFlytek Spark 主页显示修复验证');
console.log('=' .repeat(60));

// 检查主页文件
function validateHomePage() {
  console.log('\n📄 检查主页文件...');
  
  const homePagePath = path.join(__dirname, 'src/views/CleanHomePage.vue');
  
  if (!fs.existsSync(homePagePath)) {
    console.log('❌ CleanHomePage.vue 文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(homePagePath, 'utf8');
  
  // 检查关键元素
  const checks = [
    {
      name: '导航栏结构',
      pattern: /<header class="header">/,
      required: true
    },
    {
      name: 'Hero区域',
      pattern: /<section class="hero-section">/,
      required: true
    },
    {
      name: '产品功能区域',
      pattern: /<section class="features-section">/,
      required: true
    },
    {
      name: 'iFlytek logo引用',
      pattern: /iflytek-spark-logo\.svg/,
      required: true
    },
    {
      name: '渐变背景样式',
      pattern: /gradient-hero/,
      required: true
    },
    {
      name: '响应式设计',
      pattern: /@media \(max-width: 768px\)/,
      required: true
    },
    {
      name: 'Element Plus图标',
      pattern: /from '@element-plus\/icons-vue'/,
      required: true
    },
    {
      name: '中文字体配置',
      pattern: /Microsoft YaHei/,
      required: true
    }
  ];
  
  let passedChecks = 0;
  
  checks.forEach(check => {
    const found = check.pattern.test(content);
    if (found) {
      console.log(`  ✅ ${check.name}`);
      passedChecks++;
    } else {
      console.log(`  ${check.required ? '❌' : '⚠️'} ${check.name}`);
    }
  });
  
  console.log(`\n📊 检查结果: ${passedChecks}/${checks.length} 项通过`);
  return passedChecks === checks.length;
}

// 检查样式文件
function validateStyles() {
  console.log('\n🎨 检查样式文件...');
  
  const stylesPath = path.join(__dirname, 'src/styles/iflytek-simple.css');
  
  if (!fs.existsSync(stylesPath)) {
    console.log('❌ iflytek-simple.css 文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(stylesPath, 'utf8');
  
  const styleChecks = [
    {
      name: 'iFlytek主品牌色',
      pattern: /--iflytek-primary:\s*#1890ff/,
      required: true
    },
    {
      name: '渐变色定义',
      pattern: /--gradient-hero:/,
      required: true
    },
    {
      name: '间距变量',
      pattern: /--space-/,
      required: true
    },
    {
      name: '圆角变量',
      pattern: /--radius-/,
      required: true
    },
    {
      name: '阴影变量',
      pattern: /--shadow-/,
      required: true
    },
    {
      name: '中文字体定义',
      pattern: /--font-family.*Microsoft YaHei/,
      required: true
    }
  ];
  
  let passedStyleChecks = 0;
  
  styleChecks.forEach(check => {
    const found = check.pattern.test(content);
    if (found) {
      console.log(`  ✅ ${check.name}`);
      passedStyleChecks++;
    } else {
      console.log(`  ${check.required ? '❌' : '⚠️'} ${check.name}`);
    }
  });
  
  console.log(`\n📊 样式检查结果: ${passedStyleChecks}/${styleChecks.length} 项通过`);
  return passedStyleChecks === styleChecks.length;
}

// 检查图片资源
function validateImages() {
  console.log('\n🖼️ 检查图片资源...');
  
  const imagesPath = path.join(__dirname, 'public/images');
  
  if (!fs.existsSync(imagesPath)) {
    console.log('❌ images 目录不存在');
    return false;
  }
  
  const requiredImages = [
    'iflytek-spark-logo.svg',
    'iflytek-logo.svg',
    'placeholder-demo.jpg',
    'placeholder-feature.jpg'
  ];
  
  let foundImages = 0;
  
  requiredImages.forEach(image => {
    const imagePath = path.join(imagesPath, image);
    if (fs.existsSync(imagePath)) {
      console.log(`  ✅ ${image}`);
      foundImages++;
    } else {
      console.log(`  ⚠️ ${image} (可选)`);
    }
  });
  
  console.log(`\n📊 图片检查结果: ${foundImages}/${requiredImages.length} 个文件存在`);
  return foundImages >= 2; // 至少需要logo文件
}

// 检查路由配置
function validateRoutes() {
  console.log('\n🛣️ 检查路由配置...');
  
  const routesPath = path.join(__dirname, 'src/router/clean-routes.js');
  
  if (!fs.existsSync(routesPath)) {
    console.log('❌ clean-routes.js 文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(routesPath, 'utf8');
  
  const routeChecks = [
    {
      name: '主页路由',
      pattern: /path:\s*['"]\/['"]/,
      required: true
    },
    {
      name: 'CleanHomePage组件',
      pattern: /CleanHomePage/,
      required: true
    },
    {
      name: '产品演示路由',
      pattern: /path:\s*['"]\/demo['"]/,
      required: true
    },
    {
      name: '面试选择路由',
      pattern: /path:\s*['"]\/interview-selection['"]/,
      required: true
    }
  ];
  
  let passedRouteChecks = 0;
  
  routeChecks.forEach(check => {
    const found = check.pattern.test(content);
    if (found) {
      console.log(`  ✅ ${check.name}`);
      passedRouteChecks++;
    } else {
      console.log(`  ${check.required ? '❌' : '⚠️'} ${check.name}`);
    }
  });
  
  console.log(`\n📊 路由检查结果: ${passedRouteChecks}/${routeChecks.length} 项通过`);
  return passedRouteChecks === routeChecks.length;
}

// 生成修复报告
function generateReport(results) {
  console.log('\n📋 修复验证报告');
  console.log('=' .repeat(40));
  
  const totalChecks = Object.keys(results).length;
  const passedChecks = Object.values(results).filter(Boolean).length;
  
  console.log(`\n✅ 总体通过率: ${passedChecks}/${totalChecks} (${Math.round(passedChecks/totalChecks*100)}%)`);
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 所有检查项目都已通过！');
    console.log('✅ 主页显示问题已成功修复');
    console.log('✅ 产品演示部分正常显示');
    console.log('✅ iFlytek品牌一致性良好');
    console.log('✅ 响应式设计正常工作');
  } else {
    console.log('\n⚠️ 部分检查项目需要注意');
    Object.entries(results).forEach(([check, passed]) => {
      console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    });
  }
  
  console.log('\n🚀 建议操作:');
  console.log('1. 访问 http://localhost:5174/ 查看修复后的主页');
  console.log('2. 访问 http://localhost:5174/demo 查看产品演示页面');
  console.log('3. 检查浏览器开发者工具中的控制台错误');
  console.log('4. 测试不同屏幕尺寸下的响应式效果');
}

// 主函数
function main() {
  const results = {
    '主页文件': validateHomePage(),
    '样式文件': validateStyles(),
    '图片资源': validateImages(),
    '路由配置': validateRoutes()
  };
  
  generateReport(results);
}

// 运行验证
main();
