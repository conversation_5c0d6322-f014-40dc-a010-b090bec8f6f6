#!/usr/bin/env python3
"""
演示功能测试脚本
"""

import requests
import time

# 配置
FRONTEND_URL = "http://localhost:5175"

def test_demo_page_accessibility():
    """测试演示页面可访问性"""
    print("🔍 测试演示页面可访问性...")
    try:
        response = requests.get(f"{FRONTEND_URL}/demo", timeout=10)
        if response.status_code == 200:
            print("✅ 演示页面可正常访问")
            return True
        else:
            print(f"❌ 演示页面访问异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 演示页面连接失败: {e}")
        return False

def test_main_page_demo_button():
    """测试主页演示按钮"""
    print("🔍 测试主页演示按钮...")
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200 and '观看演示' in response.text:
            print("✅ 主页包含观看演示按钮")
            return True
        else:
            print("❌ 主页未找到观看演示按钮")
            return False
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
        return False

def test_navigation_menu():
    """测试导航菜单中的演示链接"""
    print("🔍 测试导航菜单...")
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200:
            # 检查是否包含演示相关的导航
            if '观看演示' in response.text or 'demo' in response.text.lower():
                print("✅ 导航菜单包含演示链接")
                return True
            else:
                print("❌ 导航菜单未找到演示链接")
                return False
        else:
            print(f"❌ 导航菜单检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 导航菜单检查异常: {e}")
        return False

def main():
    print("🚀 开始演示功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 演示页面可访问性测试
    test_results.append(("演示页面可访问性", test_demo_page_accessibility()))
    
    # 2. 主页演示按钮测试
    test_results.append(("主页演示按钮", test_main_page_demo_button()))
    
    # 3. 导航菜单测试
    test_results.append(("导航菜单演示链接", test_navigation_menu()))
    
    print("=" * 50)
    print("📊 演示功能测试结果汇总:")
    print("=" * 50)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed_tests += 1
    
    print("=" * 50)
    print(f"总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有演示功能测试通过！")
        print("💡 您可以访问以下链接体验演示功能：")
        print(f"   - 主页: {FRONTEND_URL}")
        print(f"   - 演示页面: {FRONTEND_URL}/demo")
        print("")
        print("📋 演示功能包括：")
        print("   ✅ 功能演示 - 展示系统核心功能")
        print("   ✅ 视频教程 - 分步骤学习指南")
        print("   ✅ 交互体验 - 模拟面试流程")
        print("   ✅ 技术架构 - 系统技术栈介绍")
    else:
        print("⚠️  部分演示功能存在问题，请检查失败的测试项")

if __name__ == "__main__":
    main()
