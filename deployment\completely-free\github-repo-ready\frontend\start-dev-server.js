#!/usr/bin/env node

/**
 * 开发服务器启动脚本
 * 确保MultimodalAIShowcase组件优化内容正确显示
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 启动iFlytek Spark多模态面试AI系统开发服务器');
console.log('=' .repeat(60));

// 检查必要文件
const requiredFiles = [
    'src/components/MultimodalAIShowcase.vue',
    'src/views/HomePage.vue',
    'package.json',
    'vite.config.js'
];

console.log('📋 检查必要文件...');
let allFilesExist = true;

requiredFiles.forEach(file => {
    const filePath = join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件不存在`);
        allFilesExist = false;
    }
});

if (!allFilesExist) {
    console.log('\n❌ 缺少必要文件，请检查项目结构');
    process.exit(1);
}

// 检查MultimodalAIShowcase组件内容
console.log('\n🔍 验证MultimodalAIShowcase组件优化内容...');
const showcaseFile = join(__dirname, 'src/components/MultimodalAIShowcase.vue');
const showcaseContent = fs.readFileSync(showcaseFile, 'utf8');

const requiredContent = [
    '技术性能指标',
    '98.5%',
    '12维度全方位能力评估',
    'iFlytek Spark深度学习分析',
    '实际应用场景',
    'ATS+TRM一体化管理'
];

let contentValid = true;
requiredContent.forEach(content => {
    if (showcaseContent.includes(content)) {
        console.log(`✅ 包含: ${content}`);
    } else {
        console.log(`❌ 缺少: ${content}`);
        contentValid = false;
    }
});

if (!contentValid) {
    console.log('\n⚠️  组件内容不完整，但继续启动服务器...');
}

console.log('\n🎯 优化内容验证:');
console.log('• 功能特性增强: 98%+ 准确率、毫秒级响应、12维度评估');
console.log('• 技术性能指标: 超越行业标准的技术实力证明');
console.log('• 实际应用案例: 科技企业和金融集团成功案例');
console.log('• 动画效果优化: 12条语音波形、实时数据更新');
console.log('• 竞品优势融合: Offermore.cc + Hina.com + Dayee.com');

console.log('\n🌐 启动Vite开发服务器...');
console.log('服务器地址: http://localhost:5173/');
console.log('按 Ctrl+C 停止服务器');
console.log('=' .repeat(60));

// 启动Vite开发服务器
const viteProcess = spawn('npx', ['vite', '--host', '0.0.0.0', '--port', '5173'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: process.platform === 'win32'
});

// 处理进程退出
viteProcess.on('close', (code) => {
    console.log(`\n📊 开发服务器已停止 (退出码: ${code})`);
});

viteProcess.on('error', (error) => {
    console.error(`❌ 启动失败: ${error.message}`);
    process.exit(1);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
    console.log('\n🛑 正在停止开发服务器...');
    viteProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 正在停止开发服务器...');
    viteProcess.kill('SIGTERM');
});
