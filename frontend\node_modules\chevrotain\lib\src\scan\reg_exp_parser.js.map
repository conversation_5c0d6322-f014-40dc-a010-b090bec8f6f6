{"version": 3, "file": "reg_exp_parser.js", "sourceRoot": "", "sources": ["../../../src/scan/reg_exp_parser.ts"], "names": [], "mappings": "AAAA,OAAO,EAKL,YAAY,GAEb,MAAM,2BAA2B,CAAC;AAEnC,IAAI,cAAc,GAAuC,EAAE,CAAC;AAC5D,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAUxC,MAAM,UAAU,YAAY,CAAC,MAAc;IACzC,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;IACpC,IAAI,cAAc,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;QAC5C,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC;KAClC;SAAM;QACL,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAClD,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;QACtC,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,MAAM,UAAU,sBAAsB;IACpC,cAAc,GAAG,EAAE,CAAC;AACtB,CAAC"}