'use strict';
require('../../modules/es.number.constructor');
require('../../modules/es.number.epsilon');
require('../../modules/es.number.is-finite');
require('../../modules/es.number.is-integer');
require('../../modules/es.number.is-nan');
require('../../modules/es.number.is-safe-integer');
require('../../modules/es.number.max-safe-integer');
require('../../modules/es.number.min-safe-integer');
require('../../modules/es.number.parse-float');
require('../../modules/es.number.parse-int');
require('../../modules/es.number.to-exponential');
require('../../modules/es.number.to-fixed');
require('../../modules/es.number.to-precision');
var path = require('../../internals/path');

module.exports = path.Number;
