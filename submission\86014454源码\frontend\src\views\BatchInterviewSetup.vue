<template>
  <div class="batch-interview-setup">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="brand-section">
            <router-link to="/" class="brand-link">
              <img src="/images/iflytek-spark-logo.svg" alt="iFlytek Spark" class="logo-image" />
              <span class="brand-text">iFlytek</span>
            </router-link>
          </div>
          <div class="header-divider"></div>
          <el-button @click="goBack" link class="back-btn">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <div class="page-title">
            <h1>批量创建面试</h1>
            <p>高效管理大规模招聘需求，一键创建多场面试</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="viewReports" type="info">
            <el-icon><Document /></el-icon>
            查看报表
          </el-button>
          <el-button @click="analyzeWithAI" type="warning">
            <el-icon><Setting /></el-icon>
            AI智能分析
          </el-button>
          <el-button @click="saveDraft">保存草稿</el-button>
          <el-button type="primary" @click="createBatchInterview" :loading="creating">
            <el-icon><Plus /></el-icon>
            创建批量面试
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="setup-content">
      <el-row :gutter="24">
        <!-- 左侧配置面板 -->
        <el-col :span="16">
          <div class="setup-panel">
            <!-- 基本信息 -->
            <el-card class="setup-card">
              <template #header>
                <div class="card-header">
                  <el-icon><Setting /></el-icon>
                  <span>基本信息</span>
                </div>
              </template>
              
              <el-form :model="batchForm" :rules="formRules" ref="batchFormRef" label-width="120px">
                <el-form-item label="批次名称" prop="batchName">
                  <el-input v-model="batchForm.batchName" placeholder="请输入批次名称" />
                </el-form-item>
                
                <el-form-item label="面试职位" prop="position">
                  <el-select v-model="batchForm.position" placeholder="选择面试职位" style="width: 100%">
                    <el-option label="前端工程师" value="frontend" />
                    <el-option label="后端工程师" value="backend" />
                    <el-option label="算法工程师" value="algorithm" />
                    <el-option label="产品经理" value="product" />
                    <el-option label="UI设计师" value="ui-designer" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="技术领域" prop="domain">
                  <el-select v-model="batchForm.domain" placeholder="选择技术领域" style="width: 100%">
                    <el-option label="AI技术" value="ai" />
                    <el-option label="大数据" value="bigdata" />
                    <el-option label="IoT物联网" value="iot" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="面试时长" prop="duration">
                  <el-select v-model="batchForm.duration" placeholder="选择面试时长">
                    <el-option label="30分钟" :value="30" />
                    <el-option label="45分钟" :value="45" />
                    <el-option label="60分钟" :value="60" />
                    <el-option label="90分钟" :value="90" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="面试日期" prop="interviewDate">
                  <el-date-picker
                    v-model="batchForm.interviewDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="描述信息">
                  <el-input
                    v-model="batchForm.description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入批次描述信息"
                  />
                </el-form-item>
              </el-form>
            </el-card>

            <!-- 候选人管理 -->
            <el-card class="setup-card">
              <template #header>
                <div class="card-header">
                  <el-icon><User /></el-icon>
                  <span>候选人管理</span>
                  <div class="header-actions">
                    <el-button size="small" @click="importCandidates">
                      <el-icon><Upload /></el-icon>
                      批量导入
                    </el-button>
                    <el-button size="small" type="primary" @click="addCandidate">
                      <el-icon><CirclePlus /></el-icon>
                      添加候选人
                    </el-button>
                  </div>
                </div>
              </template>
              
              <div class="candidates-section">
                <div class="candidates-stats">
                  <div class="stat-item">
                    <span class="stat-number">{{ candidates.length }}</span>
                    <span class="stat-label">总候选人</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-number">{{ candidates.filter(c => c.status === 'confirmed').length }}</span>
                    <span class="stat-label">已确认</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-number">{{ candidates.filter(c => c.status === 'pending').length }}</span>
                    <span class="stat-label">待确认</span>
                  </div>
                </div>
                
                <el-table :data="candidates" style="width: 100%" max-height="300">
                  <el-table-column prop="name" label="姓名" width="120" />
                  <el-table-column prop="email" label="邮箱" width="200" />
                  <el-table-column prop="phone" label="电话" width="130" />
                  <el-table-column prop="experience" label="经验" width="80" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === 'confirmed' ? 'success' : 'warning'">
                        {{ scope.row.status === 'confirmed' ? '已确认' : '待确认' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="editCandidate(scope.row)">编辑</el-button>
                      <el-button size="small" type="danger" @click="removeCandidate(scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
          </div>
        </el-col>

        <!-- 右侧预览面板 -->
        <el-col :span="8">
          <div class="preview-panel">
            <el-card class="preview-card">
              <template #header>
                <div class="card-header">
                  <el-icon><View /></el-icon>
                  <span>批次预览</span>
                </div>
              </template>
              
              <div class="batch-preview">
                <div class="preview-item">
                  <label>批次名称：</label>
                  <span>{{ batchForm.batchName || '未设置' }}</span>
                </div>
                <div class="preview-item">
                  <label>面试职位：</label>
                  <span>{{ getPositionLabel(batchForm.position) }}</span>
                </div>
                <div class="preview-item">
                  <label>技术领域：</label>
                  <span>{{ getDomainLabel(batchForm.domain) }}</span>
                </div>
                <div class="preview-item">
                  <label>面试时长：</label>
                  <span>{{ batchForm.duration ? batchForm.duration + '分钟' : '未设置' }}</span>
                </div>
                <div class="preview-item">
                  <label>候选人数：</label>
                  <span>{{ candidates.length }}人</span>
                </div>
                <div class="preview-item">
                  <label>预计时间：</label>
                  <span>{{ estimatedTime }}</span>
                </div>
              </div>
            </el-card>

            <!-- 快速操作 -->
            <el-card class="quick-actions-card">
              <template #header>
                <div class="card-header">
                  <el-icon><Promotion /></el-icon>
                  <span>快速操作</span>
                </div>
              </template>
              
              <div class="quick-actions">
                <el-button class="action-btn" @click="useTemplate">
                  <el-icon><Document /></el-icon>
                  使用模板
                </el-button>
                <el-button class="action-btn" @click="previewInterview">
                  <el-icon><View /></el-icon>
                  预览面试
                </el-button>
                <el-button class="action-btn" @click="scheduleInterview">
                  <el-icon><Calendar /></el-icon>
                  安排时间
                </el-button>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 添加候选人对话框 -->
    <el-dialog v-model="showAddCandidateDialog" title="添加候选人" width="500px">
      <el-form :model="candidateForm" label-width="80px">
        <el-form-item label="姓名" required>
          <el-input v-model="candidateForm.name" placeholder="请输入候选人姓名" />
        </el-form-item>
        <el-form-item label="邮箱" required>
          <el-input v-model="candidateForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        <el-form-item label="电话">
          <el-input v-model="candidateForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="工作经验">
          <el-select v-model="candidateForm.experience" placeholder="选择工作经验">
            <el-option label="应届生" value="0-1年" />
            <el-option label="1-3年" value="1-3年" />
            <el-option label="3-5年" value="3-5年" />
            <el-option label="5年以上" value="5年以上" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddCandidateDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAddCandidate">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import enhancedIflytekSparkService from '../services/enhancedIflytekSparkService.js'
import {
  ArrowLeft, Plus, Setting, User, Upload, View, Lightning,
  Document, Calendar
} from '@element-plus/icons-vue'

const router = useRouter()

// iFlytek Spark服务 (使用单例实例)
const iflytekService = enhancedIflytekSparkService

// 表单数据
const batchForm = reactive({
  batchName: '',
  position: '',
  domain: '',
  duration: 60,
  interviewDate: [],
  description: ''
})

// 表单验证规则
const formRules = {
  batchName: [
    { required: true, message: '请输入批次名称', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请选择面试职位', trigger: 'change' }
  ],
  domain: [
    { required: true, message: '请选择技术领域', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '请选择面试时长', trigger: 'change' }
  ],
  interviewDate: [
    { required: true, message: '请选择面试日期', trigger: 'change' }
  ]
}

// 候选人数据
const candidates = ref([
  {
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    experience: '3-5年',
    status: 'confirmed'
  },
  {
    name: '李四',
    email: '<EMAIL>', 
    phone: '13800138002',
    experience: '1-3年',
    status: 'pending'
  }
])

// 候选人表单
const candidateForm = reactive({
  name: '',
  email: '',
  phone: '',
  experience: ''
})

// 状态变量
const creating = ref(false)
const showAddCandidateDialog = ref(false)
const batchFormRef = ref()

// 计算属性
const estimatedTime = computed(() => {
  const totalCandidates = candidates.value.length
  const duration = batchForm.duration || 60
  const totalMinutes = totalCandidates * duration
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`
})

// 方法
const goBack = () => {
  router.go(-1)
}

const viewReports = () => {
  router.push('/enterprise-reports')
}

const analyzeWithAI = async () => {
  try {
    ElMessage.info('正在进行AI智能分析...')

    // 准备批量面试配置数据
    const batchConfig = {
      interviews: candidates.value.map(candidate => ({
        id: `interview_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        candidateName: candidate.name,
        candidateEmail: candidate.email,
        position: batchForm.position,
        domain: batchForm.domain,
        duration: batchForm.duration,
        experience: candidate.experience
      })),
      analysisDepth: 'comprehensive',
      reportingFormat: 'detailed'
    }

    // 调用AI批量分析
    const analysisResult = await iflytekService.processBatchInterviews(batchConfig)

    ElMessage.success(`AI分析完成！处理了${analysisResult.processedCount}个候选人，成功率${(analysisResult.successRate * 100).toFixed(1)}%`)

    console.log('🤖 AI批量分析结果:', analysisResult)

  } catch (error) {
    console.error('❌ AI分析失败:', error)
    ElMessage.error('AI分析暂时不可用，请稍后重试')
  }
}

// 初始化iFlytek服务
onMounted(async () => {
  try {
    console.log('✅ iFlytek Spark服务已就绪')
  } catch (error) {
    console.error('❌ iFlytek Spark服务初始化失败:', error)
  }
})

const getPositionLabel = (value) => {
  const positions = {
    'frontend': '前端工程师',
    'backend': '后端工程师', 
    'algorithm': '算法工程师',
    'product': '产品经理',
    'ui-designer': 'UI设计师'
  }
  return positions[value] || '未设置'
}

const getDomainLabel = (value) => {
  const domains = {
    'ai': 'AI技术',
    'bigdata': '大数据',
    'iot': 'IoT物联网'
  }
  return domains[value] || '未设置'
}

const saveDraft = () => {
  ElMessage.success('草稿已保存')
}

const createBatchInterview = async () => {
  if (!batchFormRef.value) return
  
  try {
    await batchFormRef.value.validate()
    
    if (candidates.value.length === 0) {
      ElMessage.warning('请至少添加一个候选人')
      return
    }
    
    creating.value = true
    
    // 模拟创建过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('批量面试创建成功！')
    router.push('/enterprise')
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    creating.value = false
  }
}

const addCandidate = () => {
  showAddCandidateDialog.value = true
}

const confirmAddCandidate = () => {
  if (!candidateForm.name || !candidateForm.email) {
    ElMessage.warning('请填写必要信息')
    return
  }
  
  candidates.value.push({
    ...candidateForm,
    status: 'pending'
  })
  
  // 重置表单
  Object.keys(candidateForm).forEach(key => {
    candidateForm[key] = ''
  })
  
  showAddCandidateDialog.value = false
  ElMessage.success('候选人添加成功')
}

const editCandidate = (candidate) => {
  ElMessage.info('编辑候选人功能开发中...')
}

const removeCandidate = (index) => {
  ElMessageBox.confirm('确定要删除这个候选人吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    candidates.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

const importCandidates = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 模板功能实现
const useTemplate = () => {
  ElMessageBox.confirm(
    '选择面试模板将自动填充相关配置，是否继续？',
    '使用面试模板',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 显示模板选择对话框
    showTemplateDialog()
  }).catch(() => {
    // 用户取消
  })
}

// 预览功能实现
const previewInterview = () => {
  if (!batchName.value.trim()) {
    ElMessage.warning('请先填写批次名称')
    return
  }

  if (selectedCandidates.value.length === 0) {
    ElMessage.warning('请先选择候选人')
    return
  }

  // 显示预览对话框
  showPreviewDialog()
}

// 时间安排功能实现
const scheduleInterview = () => {
  if (!batchName.value.trim()) {
    ElMessage.warning('请先填写批次名称')
    return
  }

  if (selectedCandidates.value.length === 0) {
    ElMessage.warning('请先选择候选人')
    return
  }

  // 显示时间安排对话框
  showScheduleDialog()
}

// 显示模板选择对话框
const showTemplateDialog = () => {
  const templates = [
    {
      id: 1,
      name: 'AI工程师标准模板',
      description: '适用于AI算法、机器学习相关职位',
      duration: 60,
      domain: 'ai',
      questions: 15
    },
    {
      id: 2,
      name: '大数据工程师模板',
      description: '适用于大数据开发、数据分析职位',
      duration: 45,
      domain: 'bigdata',
      questions: 12
    },
    {
      id: 3,
      name: '物联网工程师模板',
      description: '适用于IoT开发、嵌入式系统职位',
      duration: 50,
      domain: 'iot',
      questions: 10
    }
  ]

  const templateOptions = templates.map(t =>
    `<div style="padding: 12px; border: 1px solid #e4e7ed; border-radius: 6px; margin-bottom: 8px; cursor: pointer;" onclick="selectTemplate(${t.id})">
      <h4 style="margin: 0 0 8px 0; color: #1890ff;">${t.name}</h4>
      <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">${t.description}</p>
      <div style="font-size: 12px; color: #999;">
        <span>时长: ${t.duration}分钟</span> |
        <span>题目: ${t.questions}道</span> |
        <span>领域: ${getDomainLabel(t.domain)}</span>
      </div>
    </div>`
  ).join('')

  ElMessageBox({
    title: '选择面试模板',
    message: `<div style="max-height: 400px; overflow-y: auto;">${templateOptions}</div>`,
    showCancelButton: true,
    confirmButtonText: '自定义模板',
    cancelButtonText: '取消',
    dangerouslyUseHTMLString: true,
    customClass: 'template-selection-dialog'
  }).then(() => {
    ElMessage.info('自定义模板功能开发中，敬请期待...')
  }).catch(() => {
    // 用户取消
  })

  // 添加模板选择处理函数到全局
  window.selectTemplate = (templateId) => {
    const template = templates.find(t => t.id === templateId)
    if (template) {
      // 应用模板配置
      interviewDuration.value = template.duration
      selectedDomain.value = template.domain

      ElMessage.success(`已应用模板：${template.name}`)

      // 关闭对话框
      const dialogElement = document.querySelector('.template-selection-dialog')
      if (dialogElement) {
        const closeBtn = dialogElement.querySelector('.el-message-box__close')
        if (closeBtn) closeBtn.click()
      }
    }
  }
}

// 显示预览对话框
const showPreviewDialog = () => {
  const previewContent = `
    <div style="font-family: 'Microsoft YaHei', sans-serif;">
      <div style="margin-bottom: 20px; padding: 16px; background: #f0f7ff; border-radius: 8px;">
        <h3 style="margin: 0 0 12px 0; color: #1890ff;">批次预览</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; font-size: 14px;">
          <div><strong>批次名称:</strong> ${batchName.value}</div>
          <div><strong>面试职位:</strong> ${selectedPosition.value || '未设置'}</div>
          <div><strong>技术领域:</strong> ${getDomainLabel(selectedDomain.value)}</div>
          <div><strong>面试时长:</strong> ${interviewDuration.value}分钟</div>
          <div><strong>候选人数:</strong> ${selectedCandidates.value.length}人</div>
          <div><strong>预计时间:</strong> ${Math.ceil(selectedCandidates.value.length * interviewDuration.value / 60)}小时</div>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #333;">候选人列表</h4>
        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #e4e7ed; border-radius: 6px;">
          ${selectedCandidates.value.map((candidate, index) => `
            <div style="padding: 8px 12px; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between;">
              <span>${candidate.name}</span>
              <span style="color: #666; font-size: 12px;">${candidate.experience} | ${candidate.phone}</span>
            </div>
          `).join('')}
        </div>
      </div>

      <div style="padding: 12px; background: #fff7e6; border-radius: 6px; border-left: 4px solid #fa8c16;">
        <strong style="color: #fa8c16;">注意事项：</strong>
        <ul style="margin: 8px 0 0 0; padding-left: 20px; font-size: 14px;">
          <li>请确保所有候选人信息准确无误</li>
          <li>面试开始前会发送通知邮件</li>
          <li>建议提前15分钟进入面试间</li>
        </ul>
      </div>
    </div>
  `

  ElMessageBox({
    title: '批次预览',
    message: previewContent,
    showCancelButton: true,
    confirmButtonText: '确认创建',
    cancelButtonText: '返回修改',
    dangerouslyUseHTMLString: true,
    customClass: 'preview-dialog'
  }).then(() => {
    ElMessage.success('批次创建成功！')
    router.push('/position-management')
  }).catch(() => {
    // 用户选择返回修改
  })
}

// 显示时间安排对话框
const showScheduleDialog = () => {
  const scheduleContent = `
    <div style="font-family: 'Microsoft YaHei', sans-serif;">
      <div style="margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0;">时间安排设置</h4>
        <div style="display: grid; gap: 16px;">
          <div>
            <label style="display: block; margin-bottom: 8px; font-weight: 600;">面试开始时间</label>
            <input type="datetime-local" id="startTime" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
          </div>
          <div>
            <label style="display: block; margin-bottom: 8px; font-weight: 600;">时间间隔</label>
            <select id="timeInterval" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
              <option value="30">30分钟</option>
              <option value="45">45分钟</option>
              <option value="60" selected>60分钟</option>
              <option value="90">90分钟</option>
            </select>
          </div>
          <div>
            <label style="display: block; margin-bottom: 8px; font-weight: 600;">工作日设置</label>
            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
              <label style="display: flex; align-items: center; gap: 4px;">
                <input type="checkbox" checked /> 周一
              </label>
              <label style="display: flex; align-items: center; gap: 4px;">
                <input type="checkbox" checked /> 周二
              </label>
              <label style="display: flex; align-items: center; gap: 4px;">
                <input type="checkbox" checked /> 周三
              </label>
              <label style="display: flex; align-items: center; gap: 4px;">
                <input type="checkbox" checked /> 周四
              </label>
              <label style="display: flex; align-items: center; gap: 4px;">
                <input type="checkbox" checked /> 周五
              </label>
              <label style="display: flex; align-items: center; gap: 4px;">
                <input type="checkbox" /> 周六
              </label>
              <label style="display: flex; align-items: center; gap: 4px;">
                <input type="checkbox" /> 周日
              </label>
            </div>
          </div>
        </div>
      </div>

      <div style="padding: 12px; background: #f0f7ff; border-radius: 6px; border-left: 4px solid #1890ff;">
        <strong style="color: #1890ff;">智能建议：</strong>
        <p style="margin: 8px 0 0 0; font-size: 14px;">
          根据候选人数量(${selectedCandidates.value.length}人)和面试时长(${interviewDuration.value}分钟)，
          建议安排${Math.ceil(selectedCandidates.value.length / 8)}天完成所有面试。
        </p>
      </div>
    </div>
  `

  ElMessageBox({
    title: '时间安排',
    message: scheduleContent,
    showCancelButton: true,
    confirmButtonText: '生成时间表',
    cancelButtonText: '取消',
    dangerouslyUseHTMLString: true,
    customClass: 'schedule-dialog'
  }).then(() => {
    const startTime = document.getElementById('startTime')?.value
    const interval = document.getElementById('timeInterval')?.value

    if (!startTime) {
      ElMessage.warning('请选择开始时间')
      return
    }

    ElMessage.success(`时间安排已生成！从 ${startTime} 开始，每 ${interval} 分钟安排一场面试`)
  }).catch(() => {
    // 用户取消
  })
}

onMounted(() => {
  console.log('批量面试设置页面已加载')
})
</script>

<style scoped>
.batch-interview-setup {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.brand-section {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.brand-link:hover {
  opacity: 0.8;
}

.logo-image {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.brand-text {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.header-divider {
  width: 1px;
  height: 24px;
  background: #e4e7ed;
  margin: 0 8px;
}

.back-btn {
  color: #0066cc;
}

.page-title h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-title p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.setup-content {
  max-width: 1200px;
  margin: 24px auto;
  padding: 0 24px;
}

.setup-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.candidates-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #0066cc;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.preview-card {
  position: sticky;
  top: 24px;
}

.batch-preview {
  space-y: 12px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-item label {
  font-weight: 500;
  color: #374151;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  width: 100%;
  justify-content: flex-start;
}

.position-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .setup-content {
    padding: 0 16px;
  }
}
</style>
