#!/usr/bin/env node

/**
 * 📥 ModelsLab图片下载助手
 * ModelsLab Image Download Helper
 * 
 * 帮助您正确组织和验证从ModelsLab下载的图片
 * Helps you properly organize and validate images downloaded from ModelsLab
 */

import fs from 'fs';
import path from 'path';

console.log('📥 ModelsLab图片下载助手');
console.log('ModelsLab Image Download Helper\n');

// 预期的图片文件映射
const EXPECTED_IMAGES = {
    'modelslab_1751811835587': {
        filename: 'interface-complete-system.png',
        title: '系统完整演示界面',
        description: 'iFlytek Spark主界面，包含"科大讯飞Spark智能面试评估系统"标题'
    },
    'modelslab_1751811838043': {
        filename: 'interface-ai-architecture.png', 
        title: 'AI技术架构界面',
        description: 'AI技术架构展示，包含"AI技术架构"标题和神经网络图表'
    },
    'modelslab_1751811840452': {
        filename: 'interface-case-analysis.png',
        title: '案例分析界面', 
        description: '面试案例分析界面，包含"面试案例分析"标题'
    },
    'modelslab_1751811842869': {
        filename: 'interface-bigdata-analysis.png',
        title: '大数据分析界面',
        description: '大数据分析界面，包含"大数据分析技术"标题'
    },
    'modelslab_1751811845289': {
        filename: 'interface-iot-systems.png',
        title: 'IoT物联网界面',
        description: 'IoT物联网界面，包含"物联网技术架构"标题'
    }
};

// 显示下载指南
function showDownloadGuide() {
    console.log('📋 ModelsLab图片下载指南');
    console.log('=' .repeat(60));
    
    console.log('\n🔗 1. 访问ModelsLab控制台:');
    console.log('   https://modelslab.com/dashboard');
    
    console.log('\n🔍 2. 查找您的图片生成任务:');
    Object.entries(EXPECTED_IMAGES).forEach(([taskId, info]) => {
        console.log(`\n   📋 任务ID: ${taskId}`);
        console.log(`   📁 目标文件名: ${info.filename}`);
        console.log(`   📝 内容: ${info.description}`);
    });
    
    console.log('\n📥 3. 下载步骤:');
    console.log('   a) 在ModelsLab控制台中找到每个任务');
    console.log('   b) 点击下载生成的图片');
    console.log('   c) 将图片重命名为对应的文件名');
    console.log('   d) 保存到 ./generated-images/ 目录');
    
    console.log('\n📁 4. 正确的文件结构:');
    console.log('   ./generated-images/');
    Object.values(EXPECTED_IMAGES).forEach(info => {
        console.log(`   ├── ${info.filename}`);
    });
    
    console.log('\n✅ 5. 下载完成后运行验证:');
    console.log('   node validate-image-quality.js');
}

// 检查下载状态
function checkDownloadStatus() {
    console.log('🔍 检查图片下载状态...\n');
    
    const imageDir = './generated-images/';
    
    if (!fs.existsSync(imageDir)) {
        console.log('❌ 图片目录不存在');
        console.log('💡 请先创建目录: mkdir generated-images');
        return false;
    }
    
    const existingFiles = fs.readdirSync(imageDir).filter(file => file.endsWith('.png'));
    const expectedFiles = Object.values(EXPECTED_IMAGES).map(info => info.filename);
    
    console.log('📊 下载状态检查:');
    console.log(`   预期文件: ${expectedFiles.length} 个`);
    console.log(`   已下载: ${existingFiles.length} 个\n`);
    
    const downloadStatus = {};
    
    expectedFiles.forEach(filename => {
        const exists = existingFiles.includes(filename);
        downloadStatus[filename] = exists;
        
        const status = exists ? '✅' : '❌';
        const size = exists ? getFileSize(path.join(imageDir, filename)) : 'N/A';
        console.log(`   ${status} ${filename} ${exists ? `(${size})` : '- 未下载'}`);
    });
    
    const downloadedCount = Object.values(downloadStatus).filter(Boolean).length;
    const allDownloaded = downloadedCount === expectedFiles.length;
    
    console.log(`\n📈 完成度: ${downloadedCount}/${expectedFiles.length} (${Math.round(downloadedCount/expectedFiles.length*100)}%)`);
    
    if (allDownloaded) {
        console.log('\n🎉 所有图片已下载完成！');
        console.log('🚀 下一步: node validate-image-quality.js');
    } else {
        console.log('\n⚠️  还有图片未下载');
        console.log('💡 请继续从ModelsLab控制台下载剩余图片');
    }
    
    return allDownloaded;
}

// 获取文件大小
function getFileSize(filePath) {
    try {
        const stats = fs.statSync(filePath);
        const sizeKB = Math.round(stats.size / 1024);
        return `${sizeKB} KB`;
    } catch (error) {
        return 'Unknown';
    }
}

// 验证图片内容
function validateImageContent() {
    console.log('🔍 验证图片内容...\n');
    
    const imageDir = './generated-images/';
    const results = [];
    
    Object.values(EXPECTED_IMAGES).forEach(info => {
        const filePath = path.join(imageDir, info.filename);
        
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            const sizeKB = Math.round(stats.size / 1024);
            
            // 基本验证
            const isValidSize = stats.size > 50 * 1024; // 至少50KB
            const isPNG = info.filename.endsWith('.png');
            
            results.push({
                filename: info.filename,
                title: info.title,
                exists: true,
                size_kb: sizeKB,
                valid_size: isValidSize,
                valid_format: isPNG,
                overall_valid: isValidSize && isPNG
            });
            
            const status = isValidSize && isPNG ? '✅' : '⚠️';
            console.log(`${status} ${info.title}`);
            console.log(`   文件: ${info.filename}`);
            console.log(`   大小: ${sizeKB} KB ${isValidSize ? '✅' : '❌ 过小'}`);
            console.log(`   格式: PNG ${isPNG ? '✅' : '❌ 错误'}`);
            console.log('');
        } else {
            results.push({
                filename: info.filename,
                title: info.title,
                exists: false,
                overall_valid: false
            });
            
            console.log(`❌ ${info.title}`);
            console.log(`   文件: ${info.filename} - 不存在\n`);
        }
    });
    
    const validCount = results.filter(r => r.overall_valid).length;
    console.log(`📊 验证结果: ${validCount}/${results.length} 个文件有效`);
    
    if (validCount === results.length) {
        console.log('🎉 所有图片验证通过！');
        console.log('🚀 可以进入第二步: node step2-video-generator.js');
    } else {
        console.log('⚠️  部分图片需要重新下载或检查');
    }
    
    return results;
}

// 创建下载目录
function createDownloadDirectory() {
    const imageDir = './generated-images/';
    
    if (!fs.existsSync(imageDir)) {
        fs.mkdirSync(imageDir, { recursive: true });
        console.log(`📁 创建下载目录: ${imageDir}`);
    } else {
        console.log(`📁 下载目录已存在: ${imageDir}`);
    }
}

// 显示任务ID映射
function showTaskMapping() {
    console.log('📋 ModelsLab任务ID映射表');
    console.log('=' .repeat(60));
    
    Object.entries(EXPECTED_IMAGES).forEach(([taskId, info]) => {
        console.log(`\n🆔 ${taskId}`);
        console.log(`   ➜ ${info.filename}`);
        console.log(`   📝 ${info.title}`);
        console.log(`   💭 ${info.description}`);
    });
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--check')) {
    checkDownloadStatus();
} else if (args.includes('--validate')) {
    validateImageContent();
} else if (args.includes('--mapping')) {
    showTaskMapping();
} else if (args.includes('--setup')) {
    createDownloadDirectory();
    showDownloadGuide();
} else {
    console.log('📥 ModelsLab图片下载助手使用说明:\n');
    console.log('node download-helper.js --setup     # 显示下载指南');
    console.log('node download-helper.js --check     # 检查下载状态');
    console.log('node download-helper.js --validate  # 验证图片内容');
    console.log('node download-helper.js --mapping   # 显示任务ID映射');
    console.log('\n🚀 推荐流程:');
    console.log('1. node download-helper.js --setup');
    console.log('2. [手动从ModelsLab下载图片]');
    console.log('3. node download-helper.js --check');
    console.log('4. node download-helper.js --validate');
    console.log('5. node validate-image-quality.js');
}

export {
    showDownloadGuide,
    checkDownloadStatus,
    validateImageContent,
    createDownloadDirectory,
    EXPECTED_IMAGES
};
