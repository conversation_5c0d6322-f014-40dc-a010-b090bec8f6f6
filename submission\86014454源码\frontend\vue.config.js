const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false, // 禁用ESLint警告以减少控制台噪音

  configureWebpack: {
    resolve: {
      alias: {
        '@': require('path').resolve(__dirname, 'src')
      }
    }
  },

  chainWebpack: config => {
    // 优化Vue组件处理
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap(options => {
        // 确保组件不被意外响应式化
        options.compilerOptions = options.compilerOptions || {}
        options.compilerOptions.isCustomElement = tag => tag.startsWith('custom-')

        // 开发环境特定优化
        if (process.env.NODE_ENV === 'development') {
          // 减少不必要的警告
          options.compilerOptions.whitespace = 'preserve'
          options.compilerOptions.comments = false
        }

        return options
      })

    // 开发环境Vue配置优化
    if (process.env.NODE_ENV === 'development') {
      config.plugin('define').tap(definitions => {
        definitions[0]['__VUE_OPTIONS_API__'] = true
        definitions[0]['__VUE_PROD_DEVTOOLS__'] = false
        return definitions
      })
    }
  },

  // 开发服务器配置
  devServer: {
    port: 8080,
    open: true,
    hot: true,
    // 减少控制台输出
    client: {
      logging: 'warn',
      overlay: {
        errors: true,
        warnings: false, // 不显示警告覆盖层
      },
    },
  }
})