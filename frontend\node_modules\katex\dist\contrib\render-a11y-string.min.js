!function(e,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r(require("katex"));else if("function"==typeof define&&define.amd)define(["katex"],r);else{var t="object"==typeof exports?r(require("katex")):r(e.katex);for(var a in t)("object"==typeof exports?exports:e)[a]=t[a]}}("undefined"!=typeof self?self:this,(function(e){return function(){"use strict";var r={757:function(r){r.exports=e}},t={};function a(e){var o=t[e];if(void 0!==o)return o.exports;var n=t[e]={exports:{}};return r[e](n,n.exports,a),n.exports}a.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(r,{a:r}),r},a.d=function(e,r){for(var t in r)a.o(r,t)&&!a.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},a.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)};var o={},n=a(757),s=a.n(n);const l={"(":"left parenthesis",")":"right parenthesis","[":"open bracket","]":"close bracket","\\{":"left brace","\\}":"right brace","\\lvert":"open vertical bar","\\rvert":"close vertical bar","|":"vertical bar","\\uparrow":"up arrow","\\Uparrow":"up arrow","\\downarrow":"down arrow","\\Downarrow":"down arrow","\\updownarrow":"up down arrow","\\leftarrow":"left arrow","\\Leftarrow":"left arrow","\\rightarrow":"right arrow","\\Rightarrow":"right arrow","\\langle":"open angle","\\rangle":"close angle","\\lfloor":"open floor","\\rfloor":"close floor","\\int":"integral","\\intop":"integral","\\lim":"limit","\\ln":"natural log","\\log":"log","\\sin":"sine","\\cos":"cosine","\\tan":"tangent","\\cot":"cotangent","\\sum":"sum","/":"slash",",":"comma",".":"point","-":"negative","+":"plus","~":"tilde",":":"colon","?":"question mark","'":"apostrophe","\\%":"percent"," ":"space","\\ ":"space","\\$":"dollar sign","\\angle":"angle","\\degree":"degree","\\circ":"circle","\\vec":"vector","\\triangle":"triangle","\\pi":"pi","\\prime":"prime","\\infty":"infinity","\\alpha":"alpha","\\beta":"beta","\\gamma":"gamma","\\omega":"omega","\\theta":"theta","\\sigma":"sigma","\\lambda":"lambda","\\tau":"tau","\\Delta":"delta","\\delta":"delta","\\mu":"mu","\\rho":"rho","\\nabla":"del","\\ell":"ell","\\ldots":"dots","\\hat":"hat","\\acute":"acute"},i={prime:"prime",degree:"degrees",circle:"degrees",2:"squared",3:"cubed"},c={"|":"open vertical bar",".":""},p={"|":"close vertical bar",".":""},u={"+":"plus","-":"minus","\\pm":"plus minus","\\cdot":"dot","*":"times","/":"divided by","\\times":"times","\\div":"divided by","\\circ":"circle","\\bullet":"bullet"},d={"=":"equals","\\approx":"approximately equals","\u2260":"does not equal","\\geq":"is greater than or equal to","\\ge":"is greater than or equal to","\\leq":"is less than or equal to","\\le":"is less than or equal to",">":"is greater than","<":"is less than","\\leftarrow":"left arrow","\\Leftarrow":"left arrow","\\rightarrow":"right arrow","\\Rightarrow":"right arrow",":":"colon"},h={"\\underleftarrow":"left arrow","\\underrightarrow":"right arrow","\\underleftrightarrow":"left-right arrow","\\undergroup":"group","\\underlinesegment":"line segment","\\utilde":"tilde"},b=(e,r,t)=>{if(!e)return;let a;a="open"===r?e in c?c[e]:l[e]||e:"close"===r?e in p?p[e]:l[e]||e:"bin"===r?u[e]||e:"rel"===r?d[e]||e:l[e]||e,/^\d+$/.test(a)&&t.length>0&&/^\d+$/.test(t[t.length-1])?t[t.length-1]+=a:a&&t.push(a)},m=(e,r)=>{const t=[];e.push(t),r(t)},f=function(e,r,t){if(void 0===r&&(r=[]),e instanceof Array)for(let a=0;a<e.length;a++)f(e[a],r,t);else((e,r,t)=>{switch(e.type){case"accent":m(r,(r=>{f(e.base,r,t),r.push("with"),b(e.label,"normal",r),r.push("on top")}));break;case"accentUnder":m(r,(r=>{f(e.base,r,t),r.push("with"),b(h[e.label],"normal",r),r.push("underneath")}));break;case"accent-token":case"color-token":case"kern":case"leftright-right":case"size":case"infix":case"internal":break;case"atom":{const{text:t}=e;switch(e.family){case"bin":b(t,"bin",r);break;case"close":b(t,"close",r);break;case"inner":b(e.text,"inner",r);break;case"open":b(t,"open",r);break;case"punct":b(t,"punct",r);break;case"rel":b(t,"rel",r);break;default:throw e.family,new Error('"'+e.family+'" is not a valid atom type')}break}case"color":{const a=e.color.replace(/katex-/,"");m(r,(r=>{r.push("start color "+a),f(e.body,r,t),r.push("end color "+a)}));break}case"delimsizing":e.delim&&"."!==e.delim&&b(e.delim,"normal",r);break;case"genfrac":m(r,(r=>{const{leftDelim:a,rightDelim:o}=e;e.hasBarLine?(r.push("start fraction"),a&&b(a,"open",r),f(e.numer,r,t),r.push("divided by"),f(e.denom,r,t),o&&b(o,"close",r),r.push("end fraction")):(r.push("start binomial"),a&&b(a,"open",r),f(e.numer,r,t),r.push("over"),f(e.denom,r,t),o&&b(o,"close",r),r.push("end binomial"))}));break;case"hbox":case"lap":case"ordgroup":case"raisebox":case"sizing":case"styling":case"smash":case"vcenter":case"operatorname":case"font":case"html":f(e.body,r,t);break;case"leftright":m(r,(r=>{b(e.left,"open",r),f(e.body,r,t),b(e.right,"close",r)}));break;case"mathord":b(e.text,"normal",r);break;case"op":{const{body:a,name:o}=e;a?f(a,r,t):o&&b(o,"normal",r);break}case"op-token":case"textord":b(e.text,t,r);break;case"overline":m(r,(function(r){r.push("start overline"),f(e.body,r,t),r.push("end overline")}));break;case"pmb":r.push("bold");break;case"phantom":r.push("empty space");break;case"rule":r.push("rectangle");break;case"spacing":r.push("space");break;case"sqrt":m(r,(r=>{const{body:a,index:o}=e;if(o)return"3"===w(f(o,[],t)).join(",")?(r.push("cube root of"),f(a,r,t),void r.push("end cube root")):(r.push("root"),r.push("start index"),f(o,r,t),void r.push("end index"));r.push("square root of"),f(a,r,t),r.push("end square root")}));break;case"supsub":{const{base:a,sub:o,sup:n}=e;let s=!1;if(a&&(f(a,r,t),s="op"===a.type&&"\\log"===a.name),o){const e=s?"base":"subscript";m(r,(function(r){r.push("start "+e),f(o,r,t),r.push("end "+e)}))}n&&m(r,(function(e){const r=w(f(n,[],t)).join(",");r in i?e.push(i[r]):(e.push("start superscript"),f(n,e,t),e.push("end superscript"))}));break}case"text":if("\\textbf"===e.font){m(r,(function(r){r.push("start bold text"),f(e.body,r,t),r.push("end bold text")}));break}m(r,(function(r){r.push("start text"),f(e.body,r,t),r.push("end text")}));break;case"enclose":if(/cancel/.test(e.label)){m(r,(function(r){r.push("start cancel"),f(e.body,r,t),r.push("end cancel")}));break}if(/box/.test(e.label)){m(r,(function(r){r.push("start box"),f(e.body,r,t),r.push("end box")}));break}if(/sout/.test(e.label)){m(r,(function(r){r.push("start strikeout"),f(e.body,r,t),r.push("end strikeout")}));break}if(/phase/.test(e.label)){m(r,(function(r){r.push("start phase angle"),f(e.body,r,t),r.push("end phase angle")}));break}throw new Error("KaTeX-a11y: enclose node with "+e.label+" not supported yet");case"vphantom":throw new Error("KaTeX-a11y: vphantom not implemented yet");case"hphantom":throw new Error("KaTeX-a11y: hphantom not implemented yet");case"array":throw new Error("KaTeX-a11y: array not implemented yet");case"raw":throw new Error("KaTeX-a11y: raw not implemented yet");case"url":throw new Error("KaTeX-a11y: url not implemented yet");case"tag":throw new Error("KaTeX-a11y: tag not implemented yet");case"verb":b("start verbatim","normal",r),b(e.body,"normal",r),b("end verbatim","normal",r);break;case"environment":throw new Error("KaTeX-a11y: environment not implemented yet");case"horizBrace":b("start "+e.label.slice(1),"normal",r),f(e.base,r,t),b("end "+e.label.slice(1),"normal",r);break;case"includegraphics":throw new Error("KaTeX-a11y: includegraphics not implemented yet");case"href":throw new Error("KaTeX-a11y: href not implemented yet");case"cr":throw new Error("KaTeX-a11y: cr not implemented yet");case"underline":m(r,(function(r){r.push("start underline"),f(e.body,r,t),r.push("end underline")}));break;case"xArrow":throw new Error("KaTeX-a11y: xArrow not implemented yet");case"cdlabel":throw new Error("KaTeX-a11y: cdlabel not implemented yet");case"cdlabelparent":throw new Error("KaTeX-a11y: cdlabelparent not implemented yet");case"mclass":{const t=e.mclass.slice(1);f(e.body,r,t);break}case"mathchoice":f(e.text,r,t);break;case"htmlmathml":f(e.mathml,r,t);break;case"middle":b(e.delim,t,r);break;default:throw e.type,new Error("KaTeX a11y un-recognized type: "+e.type)}})(e,r,t);return r},w=function(e){let r=[];return e.forEach((function(e){e instanceof Array?r=r.concat(w(e)):r.push(e)})),r};return o.default=function(e,r){const t=s().__parse(e,r),a=f(t,[],"normal");return w(a).join(", ")},o=o.default}()}));