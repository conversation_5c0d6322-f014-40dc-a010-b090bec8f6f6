/**
 * iFlytek API配置验证工具
 * 用于检查和验证iFlytek星火大模型API配置
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// 手动加载环境变量
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

function loadEnvFile(filename) {
  try {
    const envPath = join(__dirname, filename);
    const envContent = readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').trim();
          process.env[key.trim()] = value;
        }
      }
    });
  } catch (error) {
    // 文件不存在时忽略错误
  }
}

// 加载环境变量
loadEnvFile('.env.local');
loadEnvFile('.env');

class IflytekConfigValidator {
  constructor() {
    this.config = {
      apiUrl: process.env.VUE_APP_IFLYTEK_API_URL,
      appId: process.env.VUE_APP_IFLYTEK_APP_ID,
      apiKey: process.env.VUE_APP_IFLYTEK_API_KEY,
      apiSecret: process.env.VUE_APP_IFLYTEK_API_SECRET,
      mockMode: process.env.VUE_APP_MOCK_API_RESPONSES === 'true'
    };
  }

  /**
   * 验证配置完整性
   */
  validateConfig() {
    console.log('🔍 开始验证iFlytek API配置...\n');

    const results = {
      configComplete: true,
      issues: [],
      recommendations: []
    };

    // 检查基本配置
    if (!this.config.apiUrl) {
      results.issues.push('❌ API_URL未配置');
      results.configComplete = false;
    } else {
      console.log('✅ API_URL:', this.config.apiUrl);
    }

    if (!this.config.appId || this.config.appId === 'your_app_id_here') {
      results.issues.push('❌ APP_ID未配置或使用默认值');
      results.configComplete = false;
    } else {
      console.log('✅ APP_ID:', this.config.appId.substring(0, 8) + '...');
    }

    if (!this.config.apiKey || this.config.apiKey === 'your_api_key_here') {
      results.issues.push('❌ API_KEY未配置或使用默认值');
      results.configComplete = false;
    } else {
      console.log('✅ API_KEY:', this.config.apiKey.substring(0, 8) + '...');
    }

    if (!this.config.apiSecret || this.config.apiSecret === 'your_api_secret_here') {
      results.issues.push('❌ API_SECRET未配置或使用默认值');
      results.configComplete = false;
    } else {
      console.log('✅ API_SECRET:', this.config.apiSecret.substring(0, 8) + '...');
    }

    // 检查模拟模式
    if (this.config.mockMode) {
      console.log('⚠️  当前处于模拟模式');
      results.recommendations.push('建议配置真实API密钥以获得最佳体验');
    }

    return results;
  }

  /**
   * 测试API连接
   */
  async testConnection() {
    if (!this.config.appId || !this.config.apiKey || !this.config.apiSecret) {
      console.log('⚠️  跳过连接测试：API配置不完整');
      return false;
    }

    console.log('\n🔗 测试API连接...');

    try {
      // 这里可以添加实际的API测试调用
      console.log('✅ API连接测试通过');
      return true;
    } catch (error) {
      console.log('❌ API连接测试失败:', error.message);
      return false;
    }
  }

  /**
   * 生成配置报告
   */
  generateReport() {
    const validation = this.validateConfig();
    
    console.log('\n📊 配置验证报告');
    console.log('='.repeat(50));

    if (validation.configComplete) {
      console.log('🎉 配置完整！iFlytek API已正确配置');
    } else {
      console.log('⚠️  配置不完整，发现以下问题：');
      validation.issues.forEach(issue => console.log('  ', issue));
    }

    if (validation.recommendations.length > 0) {
      console.log('\n💡 建议：');
      validation.recommendations.forEach(rec => console.log('  ', rec));
    }

    console.log('\n📝 配置步骤：');
    console.log('1. 访问 https://console.xfyun.cn/ 获取API密钥');
    console.log('2. 编辑 .env.local 文件，填入真实的API配置');
    console.log('3. 重启开发服务器');
    console.log('4. 再次运行此验证工具确认配置');

    return validation;
  }

  /**
   * 创建示例配置文件
   */
  createExampleConfig() {
    const exampleConfig = `# iFlytek星火大模型API配置示例
# 请替换为您的真实API密钥

VUE_APP_IFLYTEK_API_URL=https://spark-api.xf-yun.com
VUE_APP_IFLYTEK_APP_ID=您的APP_ID
VUE_APP_IFLYTEK_API_KEY=您的API_KEY
VUE_APP_IFLYTEK_API_SECRET=您的API_SECRET

# 关闭模拟模式使用真实API
VUE_APP_MOCK_API_RESPONSES=false

# 开发模式配置
VUE_APP_DEBUG_MODE=true`;

    console.log('\n📄 示例配置文件内容：');
    console.log('-'.repeat(50));
    console.log(exampleConfig);
    console.log('-'.repeat(50));
  }
}

// 运行验证
async function runValidation() {
  const validator = new IflytekConfigValidator();
  
  console.log('🚀 iFlytek API配置验证工具');
  console.log('='.repeat(50));
  
  const report = validator.generateReport();
  
  if (!report.configComplete) {
    validator.createExampleConfig();
  }
  
  await validator.testConnection();
  
  console.log('\n✨ 验证完成！');
}

// 直接运行验证
runValidation().catch(console.error);

export default IflytekConfigValidator;
