<svg width="120" height="120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="defaultAvatarBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e0e0e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bdbdbd;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="60" cy="60" r="60" fill="url(#defaultAvatarBg)"/>
  
  <!-- 头部 -->
  <circle cx="60" cy="45" r="20" fill="#ffffff" opacity="0.8"/>
  
  <!-- 身体 -->
  <path d="M 30 85 Q 60 70 90 85 L 90 120 L 30 120 Z" fill="#ffffff" opacity="0.8"/>
  
  <!-- 文字标签 -->
  <text x="60" y="105" text-anchor="middle" dominant-baseline="middle" 
        fill="#757575" font-size="10" font-family="Microsoft YaHei, Arial, sans-serif">
    默认头像
  </text>
</svg>
