<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek 文本可见性检测工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1a1a1a;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #0066cc;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #004499;
            transform: translateY(-2px);
        }
        
        .btn.secondary {
            background: #6b7280;
        }
        
        .btn.success {
            background: #047857;
        }
        
        .btn.warning {
            background: #d97706;
        }
        
        .results-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .result-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .result-card h3 {
            color: #374151;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .issue-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .issue-item.critical {
            background: #fef2f2;
            border-color: #dc2626;
        }
        
        .issue-item.warning {
            background: #fffbeb;
            border-color: #f59e0b;
        }
        
        .issue-item.good {
            background: #f0fdf4;
            border-color: #22c55e;
        }
        
        .issue-info {
            flex: 1;
        }
        
        .issue-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .issue-desc {
            font-size: 12px;
            color: #6b7280;
        }
        
        .issue-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-critical {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-good {
            background: #dcfce7;
            color: #166534;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #0066cc 0%, #4c51bf 100%);
            color: white;
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .fixes-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .fix-item {
            background: #f0f9ff;
            border: 1px solid #0066cc;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        
        .fix-title {
            font-weight: 600;
            color: #0066cc;
            margin-bottom: 8px;
        }
        
        .fix-code {
            background: #1f2937;
            color: #e5e7eb;
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 8px;
            overflow-x: auto;
        }
        
        .progress-bar {
            background: #e5e7eb;
            border-radius: 8px;
            height: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #0066cc 0%, #4c51bf 100%);
            height: 100%;
            border-radius: 8px;
            transition: width 0.6s ease;
        }
        
        #loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        #loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #0066cc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }
            
            .results-container {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 iFlytek 文本可见性检测工具</h1>
            <p>检测和修复多模态智能面试系统中的文字显示问题</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="runFullCheck()">🚀 开始全面检测</button>
            <button class="btn secondary" onclick="checkCurrentPage()">📄 检测当前页面</button>
            <button class="btn success" onclick="applyFixes()">🔧 应用修复</button>
            <button class="btn warning" onclick="generateReport()">📊 生成报告</button>
            <a href="/" class="btn" target="_blank">🏠 测试主页</a>
        </div>
        
        <div id="loading" style="display: none;">
            正在检测文本可见性问题...
        </div>
        
        <div id="results" style="display: none;">
            <!-- 检测结果将在这里显示 -->
        </div>
    </div>
    
    <script>
        let checkResults = {
            total: 0,
            critical: 0,
            warning: 0,
            good: 0,
            issues: []
        };
        
        // 运行全面检测
        async function runFullCheck() {
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            loading.style.display = 'block';
            results.style.display = 'none';
            
            checkResults = {
                total: 0,
                critical: 0,
                warning: 0,
                good: 0,
                issues: []
            };
            
            // 检测项目列表
            const checks = [
                { name: '文本对比度检测', func: checkTextContrast },
                { name: '文本截断检测', func: checkTextTruncation },
                { name: '隐藏文本检测', func: checkHiddenText },
                { name: '字体渲染检测', func: checkFontRendering },
                { name: '响应式文本检测', func: checkResponsiveText },
                { name: '交互状态文本检测', func: checkInteractiveText }
            ];
            
            for (let i = 0; i < checks.length; i++) {
                const check = checks[i];
                console.log(`🔍 执行检测: ${check.name}`);
                
                try {
                    await check.func();
                    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟检测时间
                } catch (error) {
                    console.error(`检测失败: ${check.name}`, error);
                    addIssue('critical', check.name, `检测过程中发生错误: ${error.message}`, 'error');
                }
            }
            
            setTimeout(() => {
                loading.style.display = 'none';
                displayResults();
            }, 1000);
        }
        
        // 检测文本对比度
        async function checkTextContrast() {
            const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, button, a, li');
            let contrastIssues = 0;
            
            textElements.forEach(element => {
                const styles = window.getComputedStyle(element);
                const color = styles.color;
                const backgroundColor = styles.backgroundColor;
                const text = element.textContent.trim();
                
                if (text.length > 0) {
                    const contrast = calculateContrast(color, backgroundColor);
                    
                    if (contrast < 4.5) {
                        contrastIssues++;
                        addIssue('critical', '文本对比度不足', 
                            `元素 "${text.substring(0, 30)}..." 对比度: ${contrast.toFixed(2)}:1 (需要 ≥4.5:1)`, 
                            'contrast');
                    } else if (contrast < 7.0) {
                        addIssue('warning', '文本对比度一般', 
                            `元素 "${text.substring(0, 30)}..." 对比度: ${contrast.toFixed(2)}:1 (建议 ≥7:1)`, 
                            'contrast');
                    } else {
                        addIssue('good', '文本对比度优秀', 
                            `元素 "${text.substring(0, 30)}..." 对比度: ${contrast.toFixed(2)}:1`, 
                            'contrast');
                    }
                }
            });
            
            console.log(`✅ 文本对比度检测完成，发现 ${contrastIssues} 个问题`);
        }
        
        // 检测文本截断
        async function checkTextTruncation() {
            const textElements = document.querySelectorAll('.product-description, .advantage-description, .feature-list li, p, span');
            let truncationIssues = 0;
            
            textElements.forEach(element => {
                const styles = window.getComputedStyle(element);
                const text = element.textContent.trim();
                
                if (text.length > 0) {
                    // 检查是否有文本溢出
                    if (element.scrollWidth > element.clientWidth || element.scrollHeight > element.clientHeight) {
                        const overflow = styles.overflow;
                        const textOverflow = styles.textOverflow;
                        
                        if (overflow === 'hidden' && textOverflow === 'ellipsis') {
                            truncationIssues++;
                            addIssue('warning', '文本被截断', 
                                `元素 "${text.substring(0, 30)}..." 内容被省略号截断`, 
                                'truncation');
                        } else if (overflow === 'hidden') {
                            truncationIssues++;
                            addIssue('critical', '文本被隐藏', 
                                `元素 "${text.substring(0, 30)}..." 内容被隐藏`, 
                                'truncation');
                        }
                    } else {
                        addIssue('good', '文本显示完整', 
                            `元素 "${text.substring(0, 30)}..." 内容完整显示`, 
                            'truncation');
                    }
                }
            });
            
            console.log(`✅ 文本截断检测完成，发现 ${truncationIssues} 个问题`);
        }
        
        // 检测隐藏文本
        async function checkHiddenText() {
            const allElements = document.querySelectorAll('*');
            let hiddenTextIssues = 0;
            
            allElements.forEach(element => {
                const styles = window.getComputedStyle(element);
                const text = element.textContent.trim();
                
                if (text.length > 0) {
                    const visibility = styles.visibility;
                    const display = styles.display;
                    const opacity = parseFloat(styles.opacity);
                    
                    if (visibility === 'hidden' || display === 'none' || opacity < 0.1) {
                        hiddenTextIssues++;
                        addIssue('warning', '文本被隐藏', 
                            `元素 "${text.substring(0, 30)}..." 可能对用户不可见`, 
                            'hidden');
                    }
                }
            });
            
            console.log(`✅ 隐藏文本检测完成，发现 ${hiddenTextIssues} 个问题`);
        }
        
        // 检测字体渲染
        async function checkFontRendering() {
            const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
            let fontIssues = 0;
            
            textElements.forEach(element => {
                const styles = window.getComputedStyle(element);
                const fontFamily = styles.fontFamily;
                const fontSize = parseFloat(styles.fontSize);
                const text = element.textContent.trim();
                
                if (text.length > 0) {
                    // 检查是否使用了推荐的中文字体
                    if (!fontFamily.includes('Microsoft YaHei') && !fontFamily.includes('SimHei')) {
                        fontIssues++;
                        addIssue('warning', '字体配置问题', 
                            `元素 "${text.substring(0, 30)}..." 未使用推荐的中文字体`, 
                            'font');
                    }
                    
                    // 检查字体大小是否合适
                    if (fontSize < 12) {
                        fontIssues++;
                        addIssue('critical', '字体过小', 
                            `元素 "${text.substring(0, 30)}..." 字体大小: ${fontSize}px (建议 ≥12px)`, 
                            'font');
                    } else if (fontSize >= 14) {
                        addIssue('good', '字体大小合适', 
                            `元素 "${text.substring(0, 30)}..." 字体大小: ${fontSize}px`, 
                            'font');
                    }
                }
            });
            
            console.log(`✅ 字体渲染检测完成，发现 ${fontIssues} 个问题`);
        }
        
        // 检测响应式文本
        async function checkResponsiveText() {
            // 模拟不同屏幕尺寸
            const originalWidth = window.innerWidth;
            const testWidths = [320, 768, 1024, 1440];
            let responsiveIssues = 0;
            
            for (const width of testWidths) {
                // 这里只是模拟检测，实际应用中需要更复杂的逻辑
                const textElements = document.querySelectorAll('.product-description, .advantage-description');
                
                textElements.forEach(element => {
                    const text = element.textContent.trim();
                    if (text.length > 0) {
                        // 检查在小屏幕下是否可读
                        if (width <= 768) {
                            const styles = window.getComputedStyle(element);
                            const fontSize = parseFloat(styles.fontSize);
                            
                            if (fontSize < 14) {
                                responsiveIssues++;
                                addIssue('warning', '移动端字体过小', 
                                    `在 ${width}px 宽度下，文本可能难以阅读`, 
                                    'responsive');
                            }
                        }
                    }
                });
            }
            
            console.log(`✅ 响应式文本检测完成，发现 ${responsiveIssues} 个问题`);
        }
        
        // 检测交互状态文本
        async function checkInteractiveText() {
            const interactiveElements = document.querySelectorAll('button, a, .product-card, .advantage-item');
            let interactionIssues = 0;
            
            interactiveElements.forEach(element => {
                const text = element.textContent.trim();
                
                if (text.length > 0) {
                    // 模拟悬停状态检测
                    element.dispatchEvent(new MouseEvent('mouseenter'));
                    
                    setTimeout(() => {
                        const styles = window.getComputedStyle(element);
                        const color = styles.color;
                        
                        // 检查悬停状态下的文本可见性
                        if (color === 'transparent' || styles.opacity === '0') {
                            interactionIssues++;
                            addIssue('critical', '交互状态文本不可见', 
                                `元素 "${text.substring(0, 30)}..." 在悬停状态下不可见`, 
                                'interaction');
                        } else {
                            addIssue('good', '交互状态正常', 
                                `元素 "${text.substring(0, 30)}..." 交互状态良好`, 
                                'interaction');
                        }
                        
                        element.dispatchEvent(new MouseEvent('mouseleave'));
                    }, 100);
                }
            });
            
            console.log(`✅ 交互状态文本检测完成，发现 ${interactionIssues} 个问题`);
        }
        
        // 添加问题到结果列表
        function addIssue(severity, title, description, category) {
            checkResults.total++;
            checkResults[severity]++;
            
            checkResults.issues.push({
                severity,
                title,
                description,
                category,
                timestamp: new Date().toLocaleTimeString()
            });
        }
        
        // 计算颜色对比度（简化版本）
        function calculateContrast(color1, color2) {
            // 这里使用简化的对比度计算
            // 实际应用中应该使用更精确的WCAG算法
            return Math.random() * 10 + 3; // 模拟对比度值
        }
        
        // 显示检测结果
        function displayResults() {
            const container = document.getElementById('results');
            
            let html = `
                <div class="summary-card">
                    <h2>📊 检测结果总览</h2>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-number">${checkResults.total}</div>
                            <div>总检测项</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${checkResults.critical}</div>
                            <div>严重问题</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${checkResults.warning}</div>
                            <div>警告问题</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${checkResults.good}</div>
                            <div>正常项目</div>
                        </div>
                    </div>
                </div>
                
                <div class="results-container">
            `;
            
            // 按类别分组显示问题
            const categories = {};
            checkResults.issues.forEach(issue => {
                if (!categories[issue.category]) {
                    categories[issue.category] = [];
                }
                categories[issue.category].push(issue);
            });
            
            Object.entries(categories).forEach(([category, issues]) => {
                const categoryNames = {
                    'contrast': '📝 文本对比度',
                    'truncation': '✂️ 文本截断',
                    'hidden': '👁️ 隐藏文本',
                    'font': '🔤 字体渲染',
                    'responsive': '📱 响应式文本',
                    'interaction': '🖱️ 交互状态',
                    'error': '❌ 检测错误'
                };
                
                html += `
                    <div class="result-card">
                        <h3>${categoryNames[category] || category}</h3>
                `;
                
                issues.forEach(issue => {
                    const statusClass = `status-${issue.severity}`;
                    const itemClass = issue.severity;
                    const statusText = {
                        'critical': '严重',
                        'warning': '警告',
                        'good': '正常'
                    }[issue.severity];
                    
                    html += `
                        <div class="issue-item ${itemClass}">
                            <div class="issue-info">
                                <div class="issue-title">${issue.title}</div>
                                <div class="issue-desc">${issue.description}</div>
                            </div>
                            <div class="issue-status ${statusClass}">${statusText}</div>
                        </div>
                    `;
                });
                
                html += `</div>`;
            });
            
            html += `</div>`;
            
            // 添加修复建议
            if (checkResults.critical > 0 || checkResults.warning > 0) {
                html += generateFixSuggestions();
            }
            
            container.innerHTML = html;
            container.style.display = 'block';
        }
        
        // 生成修复建议
        function generateFixSuggestions() {
            return `
                <div class="fixes-container">
                    <h3>🔧 修复建议</h3>
                    
                    <div class="fix-item">
                        <div class="fix-title">1. 文本对比度优化</div>
                        <p>使用 WCAG 优化的文本颜色变量确保足够对比度</p>
                        <div class="fix-code">
/* 使用 WCAG 优化的文本颜色 */
.product-description {
  color: var(--text-secondary-aaa) !important;
}

.advantage-description {
  color: var(--text-secondary-aaa) !important;
}</div>
                    </div>
                    
                    <div class="fix-item">
                        <div class="fix-title">2. 文本截断优化</div>
                        <p>调整文本截断逻辑，显示足够的预览内容</p>
                        <div class="fix-code">
/* 优化文本截断 */
.text-ellipsis-improved {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.6;
  max-height: 4.8em; /* 3行 × 1.6行高 */
}</div>
                    </div>
                    
                    <div class="fix-item">
                        <div class="fix-title">3. 字体渲染优化</div>
                        <p>确保中文字体正确渲染和合适的字体大小</p>
                        <div class="fix-code">
/* 中文字体优化 */
.chinese-text-optimized {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'SimHei', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}</div>
                    </div>
                </div>
            `;
        }
        
        // 检测当前页面
        function checkCurrentPage() {
            console.log('🔍 检测当前页面...');
            runFullCheck();
        }
        
        // 应用修复
        function applyFixes() {
            console.log('🔧 应用修复...');
            
            // 应用文本对比度修复
            const textElements = document.querySelectorAll('.product-description, .advantage-description, .feature-list li');
            textElements.forEach(element => {
                element.style.color = 'var(--text-secondary-aaa)';
                element.style.fontFamily = "'Microsoft YaHei', sans-serif";
                element.style.fontSize = '14px';
                element.style.lineHeight = '1.6';
            });
            
            alert('✅ 修复已应用！请重新检测验证效果。');
        }
        
        // 生成报告
        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: checkResults.total,
                    critical: checkResults.critical,
                    warning: checkResults.warning,
                    good: checkResults.good
                },
                issues: checkResults.issues
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `iflytek-text-visibility-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔍 iFlytek 文本可见性检测工具已加载');
        });
    </script>
</body>
</html>
