{"version": 3, "sources": ["../../../src/diagrams/treemap/db.ts", "../../../src/diagrams/treemap/parser.ts", "../../../src/diagrams/treemap/utils.ts", "../../../src/diagrams/treemap/renderer.ts", "../../../src/diagrams/treemap/styles.ts", "../../../src/diagrams/treemap/diagram.ts"], "sourcesContent": ["import { getConfig as commonGetConfig } from '../../config.js';\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\nimport type { DiagramStyleClassDef } from '../../diagram-api/types.js';\nimport { isLabelStyle } from '../../rendering-util/rendering-elements/shapes/handDrawnShapeStyles.js';\n\nimport { cleanAndMerge } from '../../utils.js';\nimport { ImperativeState } from '../../utils/imperativeState.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport type { TreemapDB, TreemapData, TreemapDiagramConfig, TreemapNode } from './types.js';\n\nconst defaultTreemapData: TreemapData = {\n  nodes: [],\n  levels: new Map(),\n  outerNodes: [],\n  classes: new Map(),\n};\n\nconst state = new ImperativeState<TreemapData>(() => structuredClone(defaultTreemapData));\n\nconst getConfig = (): Required<TreemapDiagramConfig> => {\n  // Use type assertion with unknown as intermediate step\n  const defaultConfig = DEFAULT_CONFIG as unknown as { treemap: Required<TreemapDiagramConfig> };\n  const userConfig = commonGetConfig() as unknown as { treemap?: Partial<TreemapDiagramConfig> };\n\n  return cleanAndMerge({\n    ...defaultConfig.treemap,\n    ...(userConfig.treemap ?? {}),\n  }) as Required<TreemapDiagramConfig>;\n};\n\nconst getNodes = (): TreemapNode[] => state.records.nodes;\n\nconst addNode = (node: TreemapNode, level: number) => {\n  const data = state.records;\n  data.nodes.push(node);\n  data.levels.set(node, level);\n\n  if (level === 0) {\n    data.outerNodes.push(node);\n  }\n\n  // Set the root node if this is a level 0 node and we don't have a root yet\n  if (level === 0 && !data.root) {\n    data.root = node;\n  }\n};\n\nconst getRoot = (): TreemapNode | undefined => ({ name: '', children: state.records.outerNodes });\n\nconst addClass = (id: string, _style: string) => {\n  const classes = state.records.classes;\n  const styleClass = classes.get(id) ?? { id, styles: [], textStyles: [] };\n  classes.set(id, styleClass);\n\n  const styles = _style.replace(/\\\\,/g, '§§§').replace(/,/g, ';').replace(/§§§/g, ',').split(';');\n\n  if (styles) {\n    styles.forEach((s) => {\n      if (isLabelStyle(s)) {\n        if (styleClass?.textStyles) {\n          styleClass.textStyles.push(s);\n        } else {\n          styleClass.textStyles = [s];\n        }\n      }\n      if (styleClass?.styles) {\n        styleClass.styles.push(s);\n      } else {\n        styleClass.styles = [s];\n      }\n    });\n  }\n\n  classes.set(id, styleClass);\n};\nconst getClasses = (): Map<string, DiagramStyleClassDef> => {\n  return state.records.classes;\n};\n\nconst getStylesForClass = (classSelector: string): string[] => {\n  return state.records.classes.get(classSelector)?.styles ?? [];\n};\n\nconst clear = () => {\n  commonClear();\n  state.reset();\n};\n\nexport const db: TreemapDB = {\n  getNodes,\n  addNode,\n  getRoot,\n  getConfig,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  addClass,\n  getClasses,\n  getStylesForClass,\n};\n", "import { parse } from '@mermaid-js/parser';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport { db } from './db.js';\nimport type { TreemapNode, TreemapAst } from './types.js';\nimport { buildHierarchy } from './utils.js';\n\n/**\n * Populates the database with data from the Treemap AST\n * @param ast - The Treemap AST\n */\nconst populate = (ast: TreemapAst) => {\n  // We need to bypass the type checking for populateCommonDb\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  populateCommonDb(ast as any, db);\n\n  const items: {\n    level: number;\n    name: string;\n    type: string;\n    value?: number;\n    classSelector?: string;\n    cssCompiledStyles?: string;\n  }[] = [];\n\n  // Extract classes and styles from the treemap\n  for (const row of ast.TreemapRows ?? []) {\n    if (row.$type === 'ClassDefStatement') {\n      db.addClass(row.className ?? '', row.styleText ?? '');\n    }\n  }\n\n  // Extract data from each row in the treemap\n  for (const row of ast.TreemapRows ?? []) {\n    const item = row.item;\n\n    if (!item) {\n      continue;\n    }\n\n    const level = row.indent ? parseInt(row.indent) : 0;\n    const name = getItemName(item);\n\n    // Get styles as a string if they exist\n    const styles = item.classSelector ? db.getStylesForClass(item.classSelector) : [];\n    const cssCompiledStyles = styles.length > 0 ? styles.join(';') : undefined;\n\n    const itemData = {\n      level,\n      name,\n      type: item.$type,\n      value: item.value,\n      classSelector: item.classSelector,\n      cssCompiledStyles,\n    };\n\n    items.push(itemData);\n  }\n\n  // Convert flat structure to hierarchical\n  const hierarchyNodes = buildHierarchy(items);\n\n  // Add all nodes to the database\n  const addNodesRecursively = (nodes: TreemapNode[], level: number) => {\n    for (const node of nodes) {\n      db.addNode(node, level);\n      if (node.children && node.children.length > 0) {\n        addNodesRecursively(node.children, level + 1);\n      }\n    }\n  };\n\n  addNodesRecursively(hierarchyNodes, 0);\n};\n\n/**\n * Gets the name of a treemap item\n * @param item - The treemap item\n * @returns The name of the item\n */\nconst getItemName = (item: { name?: string | number }): string => {\n  return item.name ? String(item.name) : '';\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (text: string): Promise<void> => {\n    try {\n      // Use a generic parse that accepts any diagram type\n\n      const parseFunc = parse as (diagramType: string, text: string) => Promise<TreemapAst>;\n      const ast = await parseFunc('treemap', text);\n      log.debug('Treemap AST:', ast);\n      populate(ast);\n    } catch (error) {\n      log.error('Error parsing treemap:', error);\n      throw error;\n    }\n  },\n};\n", "import type { TreemapNode } from './types.js';\n\n/**\n * Converts a flat array of treemap items into a hierarchical structure\n * @param items - Array of flat treemap items with level, name, type, and optional value\n * @returns A hierarchical tree structure\n */\nexport function buildHierarchy(\n  items: {\n    level: number;\n    name: string;\n    type: string;\n    value?: number;\n    classSelector?: string;\n    cssCompiledStyles?: string;\n  }[]\n): TreemapNode[] {\n  if (!items.length) {\n    return [];\n  }\n\n  const root: TreemapNode[] = [];\n  const stack: { node: TreemapNode; level: number }[] = [];\n\n  items.forEach((item) => {\n    const node: TreemapNode = {\n      name: item.name,\n      children: item.type === 'Leaf' ? undefined : [],\n    };\n    node.classSelector = item?.classSelector;\n    if (item?.cssCompiledStyles) {\n      node.cssCompiledStyles = [item.cssCompiledStyles];\n    }\n\n    if (item.type === 'Leaf' && item.value !== undefined) {\n      node.value = item.value;\n    }\n\n    // Find the right parent for this node\n    while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {\n      stack.pop();\n    }\n\n    if (stack.length === 0) {\n      // This is a root node\n      root.push(node);\n    } else {\n      // Add as child to the parent\n      const parent = stack[stack.length - 1].node;\n      if (parent.children) {\n        parent.children.push(node);\n      } else {\n        parent.children = [node];\n      }\n    }\n\n    // Only add to stack if it can have children\n    if (item.type !== 'Leaf') {\n      stack.push({ node, level: item.level });\n    }\n  });\n\n  return root;\n}\n", "import type { Diagram } from '../../Diagram.js';\nimport type {\n  Diagram<PERSON><PERSON>er,\n  DiagramStyleClassDef,\n  DrawDefinition,\n} from '../../diagram-api/types.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { setupViewPortForSVG } from '../../rendering-util/setupViewPortForSVG.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { TreemapDB, TreemapNode } from './types.js';\nimport { scaleOrdinal, treemap, hierarchy, format, select } from 'd3';\nimport { styles2String } from '../../rendering-util/rendering-elements/shapes/handDrawnShapeStyles.js';\nimport { getConfig } from '../../config.js';\nimport { log } from '../../logger.js';\nimport type { Node } from '../../rendering-util/types.js';\n\nconst DEFAULT_INNER_PADDING = 10; // Default for inner padding between cells/sections\nconst SECTION_INNER_PADDING = 10; // Default for inner padding between cells/sections\nconst SECTION_HEADER_HEIGHT = 25;\n\n/**\n * Draws the treemap diagram\n */\nconst draw: DrawDefinition = (_text, id, _version, diagram: Diagram) => {\n  const treemapDb = diagram.db as TreemapDB;\n  const config = treemapDb.getConfig();\n  const treemapInnerPadding = config.padding ?? DEFAULT_INNER_PADDING;\n  const title = treemapDb.getDiagramTitle();\n  const root = treemapDb.getRoot();\n  const { themeVariables } = getConfig();\n  if (!root) {\n    return;\n  }\n\n  // Define dimensions\n  const titleHeight = title ? 30 : 0;\n\n  const svg = selectSvgElement(id);\n  // Use config dimensions or defaults\n  const width = config.nodeWidth ? config.nodeWidth * SECTION_INNER_PADDING : 960;\n  const height = config.nodeHeight ? config.nodeHeight * SECTION_INNER_PADDING : 500;\n\n  const svgWidth = width;\n  const svgHeight = height + titleHeight;\n\n  // Set the SVG size\n  svg.attr('viewBox', `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n\n  // Format for displaying values\n  let valueFormat;\n  try {\n    // Handle special format patterns\n    const formatStr = config.valueFormat || ',';\n\n    // Handle special cases that aren't directly supported by D3 format\n    if (formatStr === '$0,0') {\n      // Currency with thousands separator\n      valueFormat = (value: number) => '$' + format(',')(value);\n    } else if (formatStr.startsWith('$') && formatStr.includes(',')) {\n      // Other dollar formats with commas\n      const precision = /\\.\\d+/.exec(formatStr);\n      const precisionStr = precision ? precision[0] : '';\n      valueFormat = (value: number) => '$' + format(',' + precisionStr)(value);\n    } else if (formatStr.startsWith('$')) {\n      // Simple dollar sign prefix\n      const restOfFormat = formatStr.substring(1);\n      valueFormat = (value: number) => '$' + format(restOfFormat || '')(value);\n    } else {\n      // Standard D3 format\n      valueFormat = format(formatStr);\n    }\n  } catch (error) {\n    log.error('Error creating format function:', error);\n    // Fallback to default format\n    valueFormat = format(',');\n  }\n\n  // Create color scale\n  const colorScale = scaleOrdinal<string>().range([\n    'transparent',\n    themeVariables.cScale0,\n    themeVariables.cScale1,\n    themeVariables.cScale2,\n    themeVariables.cScale3,\n    themeVariables.cScale4,\n    themeVariables.cScale5,\n    themeVariables.cScale6,\n    themeVariables.cScale7,\n    themeVariables.cScale8,\n    themeVariables.cScale9,\n    themeVariables.cScale10,\n    themeVariables.cScale11,\n  ]);\n  const colorScalePeer = scaleOrdinal<string>().range([\n    'transparent',\n    themeVariables.cScalePeer0,\n    themeVariables.cScalePeer1,\n    themeVariables.cScalePeer2,\n    themeVariables.cScalePeer3,\n    themeVariables.cScalePeer4,\n    themeVariables.cScalePeer5,\n    themeVariables.cScalePeer6,\n    themeVariables.cScalePeer7,\n    themeVariables.cScalePeer8,\n    themeVariables.cScalePeer9,\n    themeVariables.cScalePeer10,\n    themeVariables.cScalePeer11,\n  ]);\n  const colorScaleLabel = scaleOrdinal<string>().range([\n    themeVariables.cScaleLabel0,\n    themeVariables.cScaleLabel1,\n    themeVariables.cScaleLabel2,\n    themeVariables.cScaleLabel3,\n    themeVariables.cScaleLabel4,\n    themeVariables.cScaleLabel5,\n    themeVariables.cScaleLabel6,\n    themeVariables.cScaleLabel7,\n    themeVariables.cScaleLabel8,\n    themeVariables.cScaleLabel9,\n    themeVariables.cScaleLabel10,\n    themeVariables.cScaleLabel11,\n  ]);\n\n  // Draw the title if it exists\n  if (title) {\n    svg\n      .append('text')\n      .attr('x', svgWidth / 2)\n      .attr('y', titleHeight / 2)\n      .attr('class', 'treemapTitle')\n      .attr('text-anchor', 'middle')\n      .attr('dominant-baseline', 'middle')\n      .text(title);\n  }\n\n  // Create a main container for the treemap, translated below the title\n  const g = svg\n    .append('g')\n    .attr('transform', `translate(0, ${titleHeight})`)\n    .attr('class', 'treemapContainer');\n\n  // Create the hierarchical structure\n  const hierarchyRoot = hierarchy<TreemapNode>(root)\n    .sum((d) => d.value ?? 0)\n    .sort((a, b) => (b.value ?? 0) - (a.value ?? 0));\n\n  // Create treemap layout\n  const treemapLayout = treemap<TreemapNode>()\n    .size([width, height])\n    .paddingTop((d) =>\n      d.children && d.children.length > 0 ? SECTION_HEADER_HEIGHT + SECTION_INNER_PADDING : 0\n    )\n    .paddingInner(treemapInnerPadding)\n    .paddingLeft((d) => (d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0))\n    .paddingRight((d) => (d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0))\n    .paddingBottom((d) => (d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0))\n    .round(true);\n\n  // Apply the treemap layout to the hierarchy\n  const treemapData = treemapLayout(hierarchyRoot);\n\n  // Draw section nodes (branches - nodes with children)\n  const branchNodes = treemapData.descendants().filter((d) => d.children && d.children.length > 0);\n  const sections = g\n    .selectAll('.treemapSection')\n    .data(branchNodes)\n    .enter()\n    .append('g')\n    .attr('class', 'treemapSection')\n    .attr('transform', (d) => `translate(${d.x0},${d.y0})`);\n\n  // Add section header background\n  sections\n    .append('rect')\n    .attr('width', (d) => d.x1 - d.x0)\n    .attr('height', SECTION_HEADER_HEIGHT)\n    .attr('class', 'treemapSectionHeader')\n    .attr('fill', 'none')\n    .attr('fill-opacity', 0.6)\n    .attr('stroke-width', 0.6)\n    .attr('style', (d) => {\n      // Hide the label for the root section\n      if (d.depth === 0) {\n        return 'display: none;';\n      }\n      return '';\n    });\n\n  // Add clip paths for section headers to prevent text overflow\n  sections\n    .append('clipPath')\n    .attr('id', (_d, i) => `clip-section-${id}-${i}`)\n    .append('rect')\n    .attr('width', (d) => Math.max(0, d.x1 - d.x0 - 12)) // 6px padding on each side\n    .attr('height', SECTION_HEADER_HEIGHT);\n\n  sections\n    .append('rect')\n    .attr('width', (d) => d.x1 - d.x0)\n    .attr('height', (d) => d.y1 - d.y0)\n    .attr('class', (_d, i) => {\n      return `treemapSection section${i}`;\n    })\n    .attr('fill', (d) => colorScale(d.data.name))\n    .attr('fill-opacity', 0.6)\n    .attr('stroke', (d) => colorScalePeer(d.data.name))\n    .attr('stroke-width', 2.0)\n    .attr('stroke-opacity', 0.4)\n    .attr('style', (d) => {\n      // Hide the label for the root section\n      if (d.depth === 0) {\n        return 'display: none;';\n      }\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return styles.nodeStyles + ';' + styles.borderStyles.join(';');\n    });\n  // Add section labels\n  sections\n    .append('text')\n    .attr('class', 'treemapSectionLabel')\n    .attr('x', 6) // Keep original left padding\n    .attr('y', SECTION_HEADER_HEIGHT / 2)\n    .attr('dominant-baseline', 'middle')\n    .text((d) => (d.depth === 0 ? '' : d.data.name)) // Skip label for root section\n    .attr('font-weight', 'bold')\n    .attr('style', (d) => {\n      // Hide the label for the root section\n      if (d.depth === 0) {\n        return 'display: none;';\n      }\n      const labelStyles =\n        'dominant-baseline: middle; font-size: 12px; fill:' +\n        colorScaleLabel(d.data.name) +\n        '; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n    })\n    .each(function (d) {\n      // Skip processing for root section\n      if (d.depth === 0) {\n        return;\n      }\n      const self = select(this);\n      const originalText = d.data.name;\n      self.text(originalText);\n      const totalHeaderWidth = d.x1 - d.x0;\n      const labelXPosition = 6;\n      let spaceForTextContent;\n      if (config.showValues !== false && d.value) {\n        const valueEndsAtXRelative = totalHeaderWidth - 10;\n        const estimatedValueTextActualWidth = 30;\n        const gapBetweenLabelAndValue = 10;\n        const labelMustEndBeforeX =\n          valueEndsAtXRelative - estimatedValueTextActualWidth - gapBetweenLabelAndValue;\n        spaceForTextContent = labelMustEndBeforeX - labelXPosition;\n      } else {\n        const labelOwnRightPadding = 6;\n        spaceForTextContent = totalHeaderWidth - labelXPosition - labelOwnRightPadding;\n      }\n      const minimumWidthToDisplay = 15;\n      const actualAvailableWidth = Math.max(minimumWidthToDisplay, spaceForTextContent);\n      const textNode = self.node()!;\n      const currentTextContentLength = textNode.getComputedTextLength();\n      if (currentTextContentLength > actualAvailableWidth) {\n        const ellipsis = '...';\n        let currentTruncatedText = originalText;\n        while (currentTruncatedText.length > 0) {\n          currentTruncatedText = originalText.substring(0, currentTruncatedText.length - 1);\n          if (currentTruncatedText.length === 0) {\n            self.text(ellipsis);\n            if (textNode.getComputedTextLength() > actualAvailableWidth) {\n              self.text('');\n            }\n            break;\n          }\n          self.text(currentTruncatedText + ellipsis);\n          if (textNode.getComputedTextLength() <= actualAvailableWidth) {\n            break;\n          }\n        }\n      }\n    });\n\n  // Add section values if enabled\n  if (config.showValues !== false) {\n    sections\n      .append('text')\n      .attr('class', 'treemapSectionValue')\n      .attr('x', (d) => d.x1 - d.x0 - 10)\n      .attr('y', SECTION_HEADER_HEIGHT / 2)\n      .attr('text-anchor', 'end')\n      .attr('dominant-baseline', 'middle')\n      .text((d) => (d.value ? valueFormat(d.value) : ''))\n      .attr('font-style', 'italic')\n      .attr('style', (d) => {\n        // Hide the value for the root section\n        if (d.depth === 0) {\n          return 'display: none;';\n        }\n        const labelStyles =\n          'text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:' +\n          colorScaleLabel(d.data.name) +\n          '; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';\n        const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n        return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n      });\n  }\n\n  // Draw the leaf nodes\n  const leafNodes = treemapData.leaves();\n  const cell = g\n    .selectAll('.treemapLeafGroup')\n    .data(leafNodes)\n    .enter()\n    .append('g')\n    .attr('class', (d, i) => {\n      return `treemapNode treemapLeafGroup leaf${i}${d.data.classSelector ? ` ${d.data.classSelector}` : ''}x`;\n    })\n    .attr('transform', (d) => `translate(${d.x0},${d.y0})`);\n\n  // Add rectangle for each leaf node\n  cell\n    .append('rect')\n    .attr('width', (d) => d.x1 - d.x0)\n    .attr('height', (d) => d.y1 - d.y0)\n    .attr('class', 'treemapLeaf')\n    .attr('fill', (d) => {\n      // Leaves inherit color from their immediate parent section's name.\n      // If a leaf is the root itself (no parent), it uses its own name.\n      return d.parent ? colorScale(d.parent.data.name) : colorScale(d.data.name);\n    })\n    .attr('style', (d) => {\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return styles.nodeStyles;\n    })\n    .attr('fill-opacity', 0.3)\n    .attr('stroke', (d) => {\n      // Leaves inherit color from their immediate parent section's name.\n      // If a leaf is the root itself (no parent), it uses its own name.\n      return d.parent ? colorScale(d.parent.data.name) : colorScale(d.data.name);\n    })\n    .attr('stroke-width', 3.0);\n\n  // Add clip paths to prevent text from extending outside nodes\n  cell\n    .append('clipPath')\n    .attr('id', (_d, i) => `clip-${id}-${i}`)\n    .append('rect')\n    .attr('width', (d) => Math.max(0, d.x1 - d.x0 - 4))\n    .attr('height', (d) => Math.max(0, d.y1 - d.y0 - 4));\n\n  // Add node labels with clipping\n  const leafLabels = cell\n    .append('text')\n    .attr('class', 'treemapLabel')\n    .attr('x', (d) => (d.x1 - d.x0) / 2)\n    .attr('y', (d) => (d.y1 - d.y0) / 2)\n    // .style('fill', (d) => colorScaleLabel(d.data.name))\n    .attr('style', (d) => {\n      const labelStyles =\n        'text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:' +\n        colorScaleLabel(d.data.name) +\n        ';';\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n    })\n    .attr('clip-path', (_d, i) => `url(#clip-${id}-${i})`)\n    .text((d) => d.data.name);\n\n  leafLabels.each(function (d) {\n    const self = select(this);\n    const nodeWidth = d.x1 - d.x0;\n    const nodeHeight = d.y1 - d.y0;\n    const textNode = self.node()!;\n\n    const padding = 4;\n    const availableWidth = nodeWidth - 2 * padding;\n    const availableHeight = nodeHeight - 2 * padding;\n\n    if (availableWidth < 10 || availableHeight < 10) {\n      self.style('display', 'none');\n      return;\n    }\n\n    let currentLabelFontSize = parseInt(self.style('font-size'), 10);\n    const minLabelFontSize = 8;\n    const originalValueRelFontSize = 28; // Original font size of value, for max cap\n    const valueScaleFactor = 0.6; // Value font size as a factor of label font size\n    const minValueFontSize = 6;\n    const spacingBetweenLabelAndValue = 2;\n\n    // 1. Adjust label font size to fit width\n    while (\n      textNode.getComputedTextLength() > availableWidth &&\n      currentLabelFontSize > minLabelFontSize\n    ) {\n      currentLabelFontSize--;\n      self.style('font-size', `${currentLabelFontSize}px`);\n    }\n\n    // 2. Adjust both label and prospective value font size to fit combined height\n    let prospectiveValueFontSize = Math.max(\n      minValueFontSize,\n      Math.min(originalValueRelFontSize, Math.round(currentLabelFontSize * valueScaleFactor))\n    );\n    let combinedHeight =\n      currentLabelFontSize + spacingBetweenLabelAndValue + prospectiveValueFontSize;\n\n    while (combinedHeight > availableHeight && currentLabelFontSize > minLabelFontSize) {\n      currentLabelFontSize--;\n      prospectiveValueFontSize = Math.max(\n        minValueFontSize,\n        Math.min(originalValueRelFontSize, Math.round(currentLabelFontSize * valueScaleFactor))\n      );\n      if (\n        prospectiveValueFontSize < minValueFontSize &&\n        currentLabelFontSize === minLabelFontSize\n      ) {\n        break;\n      } // Avoid shrinking label if value is already at min\n      self.style('font-size', `${currentLabelFontSize}px`);\n      combinedHeight =\n        currentLabelFontSize + spacingBetweenLabelAndValue + prospectiveValueFontSize;\n      if (prospectiveValueFontSize <= minValueFontSize && combinedHeight > availableHeight) {\n        // If value is at min and still doesn't fit, label might need to shrink more alone\n        // This might lead to label being too small for its own text, checked next\n      }\n    }\n\n    // Update label font size based on height adjustment\n    self.style('font-size', `${currentLabelFontSize}px`);\n\n    // 3. Final visibility check for the label\n    if (\n      textNode.getComputedTextLength() > availableWidth ||\n      currentLabelFontSize < minLabelFontSize ||\n      availableHeight < currentLabelFontSize\n    ) {\n      self.style('display', 'none');\n      // If label is hidden, value will be hidden by its own .each() loop\n    }\n  });\n\n  // Add node values with clipping\n  if (config.showValues !== false) {\n    const leafValues = cell\n      .append('text')\n      .attr('class', 'treemapValue')\n      .attr('x', (d) => (d.x1 - d.x0) / 2)\n      .attr('y', function (d) {\n        // Y position calculated dynamically in leafValues.each based on final label metrics\n        return (d.y1 - d.y0) / 2; // Placeholder, will be overwritten\n      })\n      .attr('style', (d) => {\n        const labelStyles =\n          'text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:' +\n          colorScaleLabel(d.data.name) +\n          ';';\n        const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n        return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n      })\n\n      .attr('clip-path', (_d, i) => `url(#clip-${id}-${i})`)\n      .text((d) => (d.value ? valueFormat(d.value) : ''));\n\n    leafValues.each(function (d) {\n      const valueTextElement = select(this);\n      const parentCellNode = this.parentNode as SVGGElement | null;\n\n      if (!parentCellNode) {\n        valueTextElement.style('display', 'none');\n        return;\n      }\n\n      const labelElement = select(parentCellNode).select<SVGTextElement>('.treemapLabel');\n\n      if (labelElement.empty() || labelElement.style('display') === 'none') {\n        valueTextElement.style('display', 'none');\n        return;\n      }\n\n      const finalLabelFontSize = parseFloat(labelElement.style('font-size'));\n      const originalValueFontSize = 28; // From initial style setting\n      const valueScaleFactor = 0.6;\n      const minValueFontSize = 6;\n      const spacingBetweenLabelAndValue = 2;\n\n      const actualValueFontSize = Math.max(\n        minValueFontSize,\n        Math.min(originalValueFontSize, Math.round(finalLabelFontSize * valueScaleFactor))\n      );\n      valueTextElement.style('font-size', `${actualValueFontSize}px`);\n\n      const labelCenterY = (d.y1 - d.y0) / 2;\n      const valueTopActualY = labelCenterY + finalLabelFontSize / 2 + spacingBetweenLabelAndValue;\n      valueTextElement.attr('y', valueTopActualY);\n\n      const nodeWidth = d.x1 - d.x0;\n      const nodeTotalHeight = d.y1 - d.y0;\n      const cellBottomPadding = 4;\n      const maxValueBottomY = nodeTotalHeight - cellBottomPadding;\n      const availableWidthForValue = nodeWidth - 2 * 4; // padding for value text\n\n      if (\n        valueTextElement.node()!.getComputedTextLength() > availableWidthForValue ||\n        valueTopActualY + actualValueFontSize > maxValueBottomY ||\n        actualValueFontSize < minValueFontSize\n      ) {\n        valueTextElement.style('display', 'none');\n      } else {\n        valueTextElement.style('display', null);\n      }\n    });\n  }\n  const diagramPadding = config.diagramPadding ?? 8;\n  setupViewPortForSVG(svg, diagramPadding, 'flowchart', config?.useMaxWidth || false);\n};\n\nconst getClasses = function (\n  _text: string,\n  diagramObj: Pick<Diagram, 'db'>\n): Map<string, DiagramStyleClassDef> {\n  return (diagramObj.db as TreemapDB).getClasses();\n};\nexport const renderer: DiagramRenderer = { draw, getClasses };\n", "import type { DiagramStylesProvider } from '../../diagram-api/types.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport type { TreemapStyleOptions } from './types.js';\n\nconst defaultTreemapStyleOptions: TreemapStyleOptions = {\n  sectionStrokeColor: 'black',\n  sectionStrokeWidth: '1',\n  sectionFillColor: '#efefef',\n  leafStrokeColor: 'black',\n  leafStrokeWidth: '1',\n  leafFillColor: '#efefef',\n  labelColor: 'black',\n  labelFontSize: '12px',\n  valueFontSize: '10px',\n  valueColor: 'black',\n  titleColor: 'black',\n  titleFontSize: '14px',\n};\n\nexport const getStyles: DiagramStylesProvider = ({\n  treemap,\n}: { treemap?: TreemapStyleOptions } = {}) => {\n  const options = cleanAndMerge(defaultTreemapStyleOptions, treemap);\n\n  return `\n  .treemapNode.section {\n    stroke: ${options.sectionStrokeColor};\n    stroke-width: ${options.sectionStrokeWidth};\n    fill: ${options.sectionFillColor};\n  }\n  .treemapNode.leaf {\n    stroke: ${options.leafStrokeColor};\n    stroke-width: ${options.leafStrokeWidth};\n    fill: ${options.leafFillColor};\n  }\n  .treemapLabel {\n    fill: ${options.labelColor};\n    font-size: ${options.labelFontSize};\n  }\n  .treemapValue {\n    fill: ${options.valueColor};\n    font-size: ${options.valueFontSize};\n  }\n  .treemapTitle {\n    fill: ${options.titleColor};\n    font-size: ${options.titleFontSize};\n  }\n  `;\n};\n\nexport default getStyles;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { db } from './db.js';\nimport { parser } from './parser.js';\nimport { renderer } from './renderer.js';\nimport styles from './styles.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,IAAM,qBAAkC;AAAA,EACtC,OAAO,CAAC;AAAA,EACR,QAAQ,oBAAI,IAAI;AAAA,EAChB,YAAY,CAAC;AAAA,EACb,SAAS,oBAAI,IAAI;AACnB;AAEA,IAAM,QAAQ,IAAI,gBAA6B,MAAM,gBAAgB,kBAAkB,CAAC;AAExF,IAAMA,aAAY,6BAAsC;AAEtD,QAAM,gBAAgB;AACtB,QAAM,aAAa,UAAgB;AAEnC,SAAO,cAAc;AAAA,IACnB,GAAG,cAAc;AAAA,IACjB,GAAI,WAAW,WAAW,CAAC;AAAA,EAC7B,CAAC;AACH,GATkB;AAWlB,IAAM,WAAW,6BAAqB,MAAM,QAAQ,OAAnC;AAEjB,IAAM,UAAU,wBAAC,MAAmB,UAAkB;AACpD,QAAM,OAAO,MAAM;AACnB,OAAK,MAAM,KAAK,IAAI;AACpB,OAAK,OAAO,IAAI,MAAM,KAAK;AAE3B,MAAI,UAAU,GAAG;AACf,SAAK,WAAW,KAAK,IAAI;AAAA,EAC3B;AAGA,MAAI,UAAU,KAAK,CAAC,KAAK,MAAM;AAC7B,SAAK,OAAO;AAAA,EACd;AACF,GAbgB;AAehB,IAAM,UAAU,8BAAgC,EAAE,MAAM,IAAI,UAAU,MAAM,QAAQ,WAAW,IAA/E;AAEhB,IAAM,WAAW,wBAAC,IAAY,WAAmB;AAC/C,QAAM,UAAU,MAAM,QAAQ;AAC9B,QAAM,aAAa,QAAQ,IAAI,EAAE,KAAK,EAAE,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE;AACvE,UAAQ,IAAI,IAAI,UAAU;AAE1B,QAAM,SAAS,OAAO,QAAQ,QAAQ,cAAK,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,MAAM,GAAG;AAE9F,MAAI,QAAQ;AACV,WAAO,QAAQ,CAAC,MAAM;AACpB,UAAI,aAAa,CAAC,GAAG;AACnB,YAAI,YAAY,YAAY;AAC1B,qBAAW,WAAW,KAAK,CAAC;AAAA,QAC9B,OAAO;AACL,qBAAW,aAAa,CAAC,CAAC;AAAA,QAC5B;AAAA,MACF;AACA,UAAI,YAAY,QAAQ;AACtB,mBAAW,OAAO,KAAK,CAAC;AAAA,MAC1B,OAAO;AACL,mBAAW,SAAS,CAAC,CAAC;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,UAAQ,IAAI,IAAI,UAAU;AAC5B,GAzBiB;AA0BjB,IAAM,aAAa,6BAAyC;AAC1D,SAAO,MAAM,QAAQ;AACvB,GAFmB;AAInB,IAAM,oBAAoB,wBAAC,kBAAoC;AAC7D,SAAO,MAAM,QAAQ,QAAQ,IAAI,aAAa,GAAG,UAAU,CAAC;AAC9D,GAF0B;AAI1B,IAAMC,SAAQ,6BAAM;AAClB,QAAY;AACZ,QAAM,MAAM;AACd,GAHc;AAKP,IAAM,KAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAAD;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AC/GA,SAAS,aAAa;;;ACOf,SAAS,eACd,OAQe;AACf,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO,CAAC;AAAA,EACV;AAEA,QAAM,OAAsB,CAAC;AAC7B,QAAM,QAAgD,CAAC;AAEvD,QAAM,QAAQ,CAAC,SAAS;AACtB,UAAM,OAAoB;AAAA,MACxB,MAAM,KAAK;AAAA,MACX,UAAU,KAAK,SAAS,SAAS,SAAY,CAAC;AAAA,IAChD;AACA,SAAK,gBAAgB,MAAM;AAC3B,QAAI,MAAM,mBAAmB;AAC3B,WAAK,oBAAoB,CAAC,KAAK,iBAAiB;AAAA,IAClD;AAEA,QAAI,KAAK,SAAS,UAAU,KAAK,UAAU,QAAW;AACpD,WAAK,QAAQ,KAAK;AAAA,IACpB;AAGA,WAAO,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,KAAK,OAAO;AACtE,YAAM,IAAI;AAAA,IACZ;AAEA,QAAI,MAAM,WAAW,GAAG;AAEtB,WAAK,KAAK,IAAI;AAAA,IAChB,OAAO;AAEL,YAAM,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE;AACvC,UAAI,OAAO,UAAU;AACnB,eAAO,SAAS,KAAK,IAAI;AAAA,MAC3B,OAAO;AACL,eAAO,WAAW,CAAC,IAAI;AAAA,MACzB;AAAA,IACF;AAGA,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,KAAK,EAAE,MAAM,OAAO,KAAK,MAAM,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAxDgB;;;ADKhB,IAAM,WAAW,wBAAC,QAAoB;AAGpC,mBAAiB,KAAY,EAAE;AAE/B,QAAM,QAOA,CAAC;AAGP,aAAW,OAAO,IAAI,eAAe,CAAC,GAAG;AACvC,QAAI,IAAI,UAAU,qBAAqB;AACrC,SAAG,SAAS,IAAI,aAAa,IAAI,IAAI,aAAa,EAAE;AAAA,IACtD;AAAA,EACF;AAGA,aAAW,OAAO,IAAI,eAAe,CAAC,GAAG;AACvC,UAAM,OAAO,IAAI;AAEjB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AAEA,UAAM,QAAQ,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI;AAClD,UAAM,OAAO,YAAY,IAAI;AAG7B,UAAM,SAAS,KAAK,gBAAgB,GAAG,kBAAkB,KAAK,aAAa,IAAI,CAAC;AAChF,UAAM,oBAAoB,OAAO,SAAS,IAAI,OAAO,KAAK,GAAG,IAAI;AAEjE,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,eAAe,KAAK;AAAA,MACpB;AAAA,IACF;AAEA,UAAM,KAAK,QAAQ;AAAA,EACrB;AAGA,QAAM,iBAAiB,eAAe,KAAK;AAG3C,QAAM,sBAAsB,wBAAC,OAAsB,UAAkB;AACnE,eAAW,QAAQ,OAAO;AACxB,SAAG,QAAQ,MAAM,KAAK;AACtB,UAAI,KAAK,YAAY,KAAK,SAAS,SAAS,GAAG;AAC7C,4BAAoB,KAAK,UAAU,QAAQ,CAAC;AAAA,MAC9C;AAAA,IACF;AAAA,EACF,GAP4B;AAS5B,sBAAoB,gBAAgB,CAAC;AACvC,GA9DiB;AAqEjB,IAAM,cAAc,wBAAC,SAA6C;AAChE,SAAO,KAAK,OAAO,OAAO,KAAK,IAAI,IAAI;AACzC,GAFoB;AAIb,IAAM,SAA2B;AAAA,EACtC,OAAO,8BAAO,SAAgC;AAC5C,QAAI;AAGF,YAAM,YAAY;AAClB,YAAM,MAAM,MAAM,UAAU,WAAW,IAAI;AAC3C,UAAI,MAAM,gBAAgB,GAAG;AAC7B,eAAS,GAAG;AAAA,IACd,SAAS,OAAO;AACd,UAAI,MAAM,0BAA0B,KAAK;AACzC,YAAM;AAAA,IACR;AAAA,EACF,GAZO;AAaT;;;AEzFA,SAAS,cAAc,SAAS,WAAW,QAAQ,cAAc;AAMjE,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAK9B,IAAM,OAAuB,wBAAC,OAAO,IAAI,UAAUC,aAAqB;AACtE,QAAM,YAAYA,SAAQ;AAC1B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,sBAAsB,OAAO,WAAW;AAC9C,QAAM,QAAQ,UAAU,gBAAgB;AACxC,QAAM,OAAO,UAAU,QAAQ;AAC/B,QAAM,EAAE,eAAe,IAAI,UAAU;AACrC,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AAGA,QAAM,cAAc,QAAQ,KAAK;AAEjC,QAAM,MAAM,iBAAiB,EAAE;AAE/B,QAAM,QAAQ,OAAO,YAAY,OAAO,YAAY,wBAAwB;AAC5E,QAAM,SAAS,OAAO,aAAa,OAAO,aAAa,wBAAwB;AAE/E,QAAM,WAAW;AACjB,QAAM,YAAY,SAAS;AAG3B,MAAI,KAAK,WAAW,OAAO,QAAQ,IAAI,SAAS,EAAE;AAClD,mBAAiB,KAAK,WAAW,UAAU,OAAO,WAAW;AAG7D,MAAI;AACJ,MAAI;AAEF,UAAM,YAAY,OAAO,eAAe;AAGxC,QAAI,cAAc,QAAQ;AAExB,oBAAc,wBAAC,UAAkB,MAAM,OAAO,GAAG,EAAE,KAAK,GAA1C;AAAA,IAChB,WAAW,UAAU,WAAW,GAAG,KAAK,UAAU,SAAS,GAAG,GAAG;AAE/D,YAAM,YAAY,QAAQ,KAAK,SAAS;AACxC,YAAM,eAAe,YAAY,UAAU,CAAC,IAAI;AAChD,oBAAc,wBAAC,UAAkB,MAAM,OAAO,MAAM,YAAY,EAAE,KAAK,GAAzD;AAAA,IAChB,WAAW,UAAU,WAAW,GAAG,GAAG;AAEpC,YAAM,eAAe,UAAU,UAAU,CAAC;AAC1C,oBAAc,wBAAC,UAAkB,MAAM,OAAO,gBAAgB,EAAE,EAAE,KAAK,GAAzD;AAAA,IAChB,OAAO;AAEL,oBAAc,OAAO,SAAS;AAAA,IAChC;AAAA,EACF,SAAS,OAAO;AACd,QAAI,MAAM,mCAAmC,KAAK;AAElD,kBAAc,OAAO,GAAG;AAAA,EAC1B;AAGA,QAAM,aAAa,aAAqB,EAAE,MAAM;AAAA,IAC9C;AAAA,IACA,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,EACjB,CAAC;AACD,QAAM,iBAAiB,aAAqB,EAAE,MAAM;AAAA,IAClD;AAAA,IACA,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,EACjB,CAAC;AACD,QAAM,kBAAkB,aAAqB,EAAE,MAAM;AAAA,IACnD,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,EACjB,CAAC;AAGD,MAAI,OAAO;AACT,QACG,OAAO,MAAM,EACb,KAAK,KAAK,WAAW,CAAC,EACtB,KAAK,KAAK,cAAc,CAAC,EACzB,KAAK,SAAS,cAAc,EAC5B,KAAK,eAAe,QAAQ,EAC5B,KAAK,qBAAqB,QAAQ,EAClC,KAAK,KAAK;AAAA,EACf;AAGA,QAAM,IAAI,IACP,OAAO,GAAG,EACV,KAAK,aAAa,gBAAgB,WAAW,GAAG,EAChD,KAAK,SAAS,kBAAkB;AAGnC,QAAM,gBAAgB,UAAuB,IAAI,EAC9C,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EACvB,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAGjD,QAAM,gBAAgB,QAAqB,EACxC,KAAK,CAAC,OAAO,MAAM,CAAC,EACpB;AAAA,IAAW,CAAC,MACX,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,wBAAwB;AAAA,EACxF,EACC,aAAa,mBAAmB,EAChC,YAAY,CAAC,MAAO,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,CAAE,EACpF,aAAa,CAAC,MAAO,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,CAAE,EACrF,cAAc,CAAC,MAAO,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,CAAE,EACtF,MAAM,IAAI;AAGb,QAAM,cAAc,cAAc,aAAa;AAG/C,QAAM,cAAc,YAAY,YAAY,EAAE,OAAO,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,SAAS,CAAC;AAC/F,QAAM,WAAW,EACd,UAAU,iBAAiB,EAC3B,KAAK,WAAW,EAChB,MAAM,EACN,OAAO,GAAG,EACV,KAAK,SAAS,gBAAgB,EAC9B,KAAK,aAAa,CAAC,MAAM,aAAa,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG;AAGxD,WACG,OAAO,MAAM,EACb,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAChC,KAAK,UAAU,qBAAqB,EACpC,KAAK,SAAS,sBAAsB,EACpC,KAAK,QAAQ,MAAM,EACnB,KAAK,gBAAgB,GAAG,EACxB,KAAK,gBAAgB,GAAG,EACxB,KAAK,SAAS,CAAC,MAAM;AAEpB,QAAI,EAAE,UAAU,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AAGH,WACG,OAAO,UAAU,EACjB,KAAK,MAAM,CAAC,IAAI,MAAM,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAC/C,OAAO,MAAM,EACb,KAAK,SAAS,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAClD,KAAK,UAAU,qBAAqB;AAEvC,WACG,OAAO,MAAM,EACb,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAChC,KAAK,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EACjC,KAAK,SAAS,CAAC,IAAI,MAAM;AACxB,WAAO,yBAAyB,CAAC;AAAA,EACnC,CAAC,EACA,KAAK,QAAQ,CAAC,MAAM,WAAW,EAAE,KAAK,IAAI,CAAC,EAC3C,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,CAAC,MAAM,eAAe,EAAE,KAAK,IAAI,CAAC,EACjD,KAAK,gBAAgB,CAAG,EACxB,KAAK,kBAAkB,GAAG,EAC1B,KAAK,SAAS,CAAC,MAAM;AAEpB,QAAI,EAAE,UAAU,GAAG;AACjB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAkB,CAAS;AACpF,WAAO,OAAO,aAAa,MAAM,OAAO,aAAa,KAAK,GAAG;AAAA,EAC/D,CAAC;AAEH,WACG,OAAO,MAAM,EACb,KAAK,SAAS,qBAAqB,EACnC,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,wBAAwB,CAAC,EACnC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,CAAC,MAAO,EAAE,UAAU,IAAI,KAAK,EAAE,KAAK,IAAK,EAC9C,KAAK,eAAe,MAAM,EAC1B,KAAK,SAAS,CAAC,MAAM;AAEpB,QAAI,EAAE,UAAU,GAAG;AACjB,aAAO;AAAA,IACT;AACA,UAAM,cACJ,sDACA,gBAAgB,EAAE,KAAK,IAAI,IAC3B;AACF,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAkB,CAAS;AACpF,WAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;AAAA,EACnE,CAAC,EACA,KAAK,SAAU,GAAG;AAEjB,QAAI,EAAE,UAAU,GAAG;AACjB;AAAA,IACF;AACA,UAAM,OAAO,OAAO,IAAI;AACxB,UAAM,eAAe,EAAE,KAAK;AAC5B,SAAK,KAAK,YAAY;AACtB,UAAM,mBAAmB,EAAE,KAAK,EAAE;AAClC,UAAM,iBAAiB;AACvB,QAAI;AACJ,QAAI,OAAO,eAAe,SAAS,EAAE,OAAO;AAC1C,YAAM,uBAAuB,mBAAmB;AAChD,YAAM,gCAAgC;AACtC,YAAM,0BAA0B;AAChC,YAAM,sBACJ,uBAAuB,gCAAgC;AACzD,4BAAsB,sBAAsB;AAAA,IAC9C,OAAO;AACL,YAAM,uBAAuB;AAC7B,4BAAsB,mBAAmB,iBAAiB;AAAA,IAC5D;AACA,UAAM,wBAAwB;AAC9B,UAAM,uBAAuB,KAAK,IAAI,uBAAuB,mBAAmB;AAChF,UAAM,WAAW,KAAK,KAAK;AAC3B,UAAM,2BAA2B,SAAS,sBAAsB;AAChE,QAAI,2BAA2B,sBAAsB;AACnD,YAAM,WAAW;AACjB,UAAI,uBAAuB;AAC3B,aAAO,qBAAqB,SAAS,GAAG;AACtC,+BAAuB,aAAa,UAAU,GAAG,qBAAqB,SAAS,CAAC;AAChF,YAAI,qBAAqB,WAAW,GAAG;AACrC,eAAK,KAAK,QAAQ;AAClB,cAAI,SAAS,sBAAsB,IAAI,sBAAsB;AAC3D,iBAAK,KAAK,EAAE;AAAA,UACd;AACA;AAAA,QACF;AACA,aAAK,KAAK,uBAAuB,QAAQ;AACzC,YAAI,SAAS,sBAAsB,KAAK,sBAAsB;AAC5D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGH,MAAI,OAAO,eAAe,OAAO;AAC/B,aACG,OAAO,MAAM,EACb,KAAK,SAAS,qBAAqB,EACnC,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EACjC,KAAK,KAAK,wBAAwB,CAAC,EACnC,KAAK,eAAe,KAAK,EACzB,KAAK,qBAAqB,QAAQ,EAClC,KAAK,CAAC,MAAO,EAAE,QAAQ,YAAY,EAAE,KAAK,IAAI,EAAG,EACjD,KAAK,cAAc,QAAQ,EAC3B,KAAK,SAAS,CAAC,MAAM;AAEpB,UAAI,EAAE,UAAU,GAAG;AACjB,eAAO;AAAA,MACT;AACA,YAAM,cACJ,wEACA,gBAAgB,EAAE,KAAK,IAAI,IAC3B;AACF,YAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAkB,CAAS;AACpF,aAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;AAAA,IACnE,CAAC;AAAA,EACL;AAGA,QAAM,YAAY,YAAY,OAAO;AACrC,QAAM,OAAO,EACV,UAAU,mBAAmB,EAC7B,KAAK,SAAS,EACd,MAAM,EACN,OAAO,GAAG,EACV,KAAK,SAAS,CAAC,GAAG,MAAM;AACvB,WAAO,oCAAoC,CAAC,GAAG,EAAE,KAAK,gBAAgB,IAAI,EAAE,KAAK,aAAa,KAAK,EAAE;AAAA,EACvG,CAAC,EACA,KAAK,aAAa,CAAC,MAAM,aAAa,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG;AAGxD,OACG,OAAO,MAAM,EACb,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAChC,KAAK,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EACjC,KAAK,SAAS,aAAa,EAC3B,KAAK,QAAQ,CAAC,MAAM;AAGnB,WAAO,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,WAAW,EAAE,KAAK,IAAI;AAAA,EAC3E,CAAC,EACA,KAAK,SAAS,CAAC,MAAM;AACpB,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAkB,CAAS;AACpF,WAAO,OAAO;AAAA,EAChB,CAAC,EACA,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,CAAC,MAAM;AAGrB,WAAO,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,WAAW,EAAE,KAAK,IAAI;AAAA,EAC3E,CAAC,EACA,KAAK,gBAAgB,CAAG;AAG3B,OACG,OAAO,UAAU,EACjB,KAAK,MAAM,CAAC,IAAI,MAAM,QAAQ,EAAE,IAAI,CAAC,EAAE,EACvC,OAAO,MAAM,EACb,KAAK,SAAS,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EACjD,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAGrD,QAAM,aAAa,KAChB,OAAO,MAAM,EACb,KAAK,SAAS,cAAc,EAC5B,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAClC,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAElC,KAAK,SAAS,CAAC,MAAM;AACpB,UAAM,cACJ,0EACA,gBAAgB,EAAE,KAAK,IAAI,IAC3B;AACF,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAkB,CAAS;AACpF,WAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;AAAA,EACnE,CAAC,EACA,KAAK,aAAa,CAAC,IAAI,MAAM,aAAa,EAAE,IAAI,CAAC,GAAG,EACpD,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI;AAE1B,aAAW,KAAK,SAAU,GAAG;AAC3B,UAAM,OAAO,OAAO,IAAI;AACxB,UAAM,YAAY,EAAE,KAAK,EAAE;AAC3B,UAAM,aAAa,EAAE,KAAK,EAAE;AAC5B,UAAM,WAAW,KAAK,KAAK;AAE3B,UAAM,UAAU;AAChB,UAAM,iBAAiB,YAAY,IAAI;AACvC,UAAM,kBAAkB,aAAa,IAAI;AAEzC,QAAI,iBAAiB,MAAM,kBAAkB,IAAI;AAC/C,WAAK,MAAM,WAAW,MAAM;AAC5B;AAAA,IACF;AAEA,QAAI,uBAAuB,SAAS,KAAK,MAAM,WAAW,GAAG,EAAE;AAC/D,UAAM,mBAAmB;AACzB,UAAM,2BAA2B;AACjC,UAAM,mBAAmB;AACzB,UAAM,mBAAmB;AACzB,UAAM,8BAA8B;AAGpC,WACE,SAAS,sBAAsB,IAAI,kBACnC,uBAAuB,kBACvB;AACA;AACA,WAAK,MAAM,aAAa,GAAG,oBAAoB,IAAI;AAAA,IACrD;AAGA,QAAI,2BAA2B,KAAK;AAAA,MAClC;AAAA,MACA,KAAK,IAAI,0BAA0B,KAAK,MAAM,uBAAuB,gBAAgB,CAAC;AAAA,IACxF;AACA,QAAI,iBACF,uBAAuB,8BAA8B;AAEvD,WAAO,iBAAiB,mBAAmB,uBAAuB,kBAAkB;AAClF;AACA,iCAA2B,KAAK;AAAA,QAC9B;AAAA,QACA,KAAK,IAAI,0BAA0B,KAAK,MAAM,uBAAuB,gBAAgB,CAAC;AAAA,MACxF;AACA,UACE,2BAA2B,oBAC3B,yBAAyB,kBACzB;AACA;AAAA,MACF;AACA,WAAK,MAAM,aAAa,GAAG,oBAAoB,IAAI;AACnD,uBACE,uBAAuB,8BAA8B;AACvD,UAAI,4BAA4B,oBAAoB,iBAAiB,iBAAiB;AAAA,MAGtF;AAAA,IACF;AAGA,SAAK,MAAM,aAAa,GAAG,oBAAoB,IAAI;AAGnD,QACE,SAAS,sBAAsB,IAAI,kBACnC,uBAAuB,oBACvB,kBAAkB,sBAClB;AACA,WAAK,MAAM,WAAW,MAAM;AAAA,IAE9B;AAAA,EACF,CAAC;AAGD,MAAI,OAAO,eAAe,OAAO;AAC/B,UAAM,aAAa,KAChB,OAAO,MAAM,EACb,KAAK,SAAS,cAAc,EAC5B,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAClC,KAAK,KAAK,SAAU,GAAG;AAEtB,cAAQ,EAAE,KAAK,EAAE,MAAM;AAAA,IACzB,CAAC,EACA,KAAK,SAAS,CAAC,MAAM;AACpB,YAAM,cACJ,2EACA,gBAAgB,EAAE,KAAK,IAAI,IAC3B;AACF,YAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAkB,CAAS;AACpF,aAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;AAAA,IACnE,CAAC,EAEA,KAAK,aAAa,CAAC,IAAI,MAAM,aAAa,EAAE,IAAI,CAAC,GAAG,EACpD,KAAK,CAAC,MAAO,EAAE,QAAQ,YAAY,EAAE,KAAK,IAAI,EAAG;AAEpD,eAAW,KAAK,SAAU,GAAG;AAC3B,YAAM,mBAAmB,OAAO,IAAI;AACpC,YAAM,iBAAiB,KAAK;AAE5B,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,MAAM,WAAW,MAAM;AACxC;AAAA,MACF;AAEA,YAAM,eAAe,OAAO,cAAc,EAAE,OAAuB,eAAe;AAElF,UAAI,aAAa,MAAM,KAAK,aAAa,MAAM,SAAS,MAAM,QAAQ;AACpE,yBAAiB,MAAM,WAAW,MAAM;AACxC;AAAA,MACF;AAEA,YAAM,qBAAqB,WAAW,aAAa,MAAM,WAAW,CAAC;AACrE,YAAM,wBAAwB;AAC9B,YAAM,mBAAmB;AACzB,YAAM,mBAAmB;AACzB,YAAM,8BAA8B;AAEpC,YAAM,sBAAsB,KAAK;AAAA,QAC/B;AAAA,QACA,KAAK,IAAI,uBAAuB,KAAK,MAAM,qBAAqB,gBAAgB,CAAC;AAAA,MACnF;AACA,uBAAiB,MAAM,aAAa,GAAG,mBAAmB,IAAI;AAE9D,YAAM,gBAAgB,EAAE,KAAK,EAAE,MAAM;AACrC,YAAM,kBAAkB,eAAe,qBAAqB,IAAI;AAChE,uBAAiB,KAAK,KAAK,eAAe;AAE1C,YAAM,YAAY,EAAE,KAAK,EAAE;AAC3B,YAAM,kBAAkB,EAAE,KAAK,EAAE;AACjC,YAAM,oBAAoB;AAC1B,YAAM,kBAAkB,kBAAkB;AAC1C,YAAM,yBAAyB,YAAY,IAAI;AAE/C,UACE,iBAAiB,KAAK,EAAG,sBAAsB,IAAI,0BACnD,kBAAkB,sBAAsB,mBACxC,sBAAsB,kBACtB;AACA,yBAAiB,MAAM,WAAW,MAAM;AAAA,MAC1C,OAAO;AACL,yBAAiB,MAAM,WAAW,IAAI;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,iBAAiB,OAAO,kBAAkB;AAChD,sBAAoB,KAAK,gBAAgB,aAAa,QAAQ,eAAe,KAAK;AACpF,GA9e6B;AAgf7B,IAAMC,cAAa,gCACjB,OACA,YACmC;AACnC,SAAQ,WAAW,GAAiB,WAAW;AACjD,GALmB;AAMZ,IAAM,WAA4B,EAAE,MAAM,YAAAA,YAAW;;;ACzgB5D,IAAM,6BAAkD;AAAA,EACtD,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,eAAe;AACjB;AAEO,IAAM,YAAmC,wBAAC;AAAA,EAC/C,SAAAC;AACF,IAAuC,CAAC,MAAM;AAC5C,QAAM,UAAU,cAAc,4BAA4BA,QAAO;AAEjE,SAAO;AAAA;AAAA,cAEK,QAAQ,kBAAkB;AAAA,oBACpB,QAAQ,kBAAkB;AAAA,YAClC,QAAQ,gBAAgB;AAAA;AAAA;AAAA,cAGtB,QAAQ,eAAe;AAAA,oBACjB,QAAQ,eAAe;AAAA,YAC/B,QAAQ,aAAa;AAAA;AAAA;AAAA,YAGrB,QAAQ,UAAU;AAAA,iBACb,QAAQ,aAAa;AAAA;AAAA;AAAA,YAG1B,QAAQ,UAAU;AAAA,iBACb,QAAQ,aAAa;AAAA;AAAA;AAAA,YAG1B,QAAQ,UAAU;AAAA,iBACb,QAAQ,aAAa;AAAA;AAAA;AAGtC,GA7BgD;AA+BhD,IAAO,iBAAQ;;;AC5CR,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["getConfig", "clear", "diagram", "getClasses", "treemap"]}