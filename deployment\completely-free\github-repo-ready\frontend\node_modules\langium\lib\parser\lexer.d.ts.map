{"version": 3, "file": "lexer.d.ts", "sourceRoot": "", "sources": ["../../src/parser/lexer.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EAAE,0BAA0B,EAAE,YAAY,EAAE,yBAAyB,EAAE,MAAM,EAAE,SAAS,EAAE,mBAAmB,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC/J,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,EAAE,KAAK,IAAI,eAAe,EAA6B,MAAM,YAAY,CAAC;AACjF,OAAO,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAErE,qBAAa,gCAAiC,YAAW,0BAA0B;IAE/E,gCAAgC,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM;IAI/H,gCAAgC,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM;CAG1D;AAED,MAAM,WAAW,WAAW;IACxB;;;;;OAKG;IACH,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB;;OAEG;IACH,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,MAAM,EAAE,YAAY,EAAE,CAAC;IACvB,MAAM,CAAC,EAAE,YAAY,CAAC;CACzB;AAED,MAAM,MAAM,YAAY,GAAG,MAAM,GAAG,SAAS,CAAC;AAE9C,MAAM,WAAW,eAAe;IAC5B,IAAI,CAAC,EAAE,YAAY,CAAC;CACvB;AAED,eAAO,MAAM,wBAAwB,EAAE,eAAkC,CAAC;AAE1E,MAAM,WAAW,KAAK;IAClB,QAAQ,CAAC,UAAU,EAAE,mBAAmB,CAAC;IACzC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,eAAe,GAAG,WAAW,CAAC;CAClE;AAED,qBAAa,YAAa,YAAW,KAAK;IAEtC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,oBAAoB,EAAE,0BAA0B,CAAC;IACpE,SAAS,CAAC,UAAU,EAAE,mBAAmB,CAAC;IAC1C,SAAS,CAAC,eAAe,EAAE,eAAe,CAAC;gBAE/B,QAAQ,EAAE,mBAAmB;IAgBzC,IAAI,UAAU,IAAI,mBAAmB,CAEpC;IAED,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,GAAE,eAA0C,GAAG,WAAW;IAUzF,SAAS,CAAC,qBAAqB,CAAC,WAAW,EAAE,eAAe,GAAG,mBAAmB;CAOrF;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,eAAe,EAAE,eAAe,GAAG,eAAe,IAAI,SAAS,EAAE,CAEjG;AAED;;GAEG;AACH,wBAAgB,2BAA2B,CAAC,eAAe,EAAE,eAAe,GAAG,eAAe,IAAI,yBAAyB,CAE1H;AAED;;GAEG;AACH,wBAAgB,qBAAqB,CAAC,eAAe,EAAE,eAAe,GAAG,eAAe,IAAI,mBAAmB,CAE9G"}