<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek面试系统演示视频制作工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .demo-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #0066cc;
        }
        
        .demo-button.recording {
            background: #f5222d;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .timer {
            font-size: 1.5em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .script-section {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #52c41a;
            width: 0%;
            transition: width 0.3s;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 5px 0;
            cursor: pointer;
        }
        
        .checklist li:before {
            content: '☐ ';
            margin-right: 10px;
        }
        
        .checklist li.checked:before {
            content: '☑ ';
            color: #52c41a;
        }
        
        .url-input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .demo-frame {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 10px;
            background: white;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 iFlytek面试系统演示视频制作工具</h1>
        
        <div class="section">
            <h3>📋 录制准备清单</h3>
            <ul class="checklist" id="preparationChecklist">
                <li data-item="server">确保开发服务器正常运行 (http://localhost:5173)</li>
                <li data-item="data">准备演示数据和测试账号</li>
                <li data-item="functions">检查所有功能正常工作</li>
                <li data-item="recording">准备录屏软件和音频设备</li>
                <li data-item="script">熟悉演示脚本内容</li>
                <li data-item="browser">清理浏览器缓存和历史记录</li>
            </ul>
            
            <div class="progress-bar">
                <div class="progress-fill" id="preparationProgress"></div>
            </div>
        </div>
        
        <div class="section">
            <h3>🎯 演示URL快速访问</h3>
            <input type="text" class="url-input" id="demoUrl" value="http://localhost:5173" placeholder="输入演示URL">
            <br>
            <button class="demo-button" onclick="openDemoUrl()">🏠 打开主页</button>
            <button class="demo-button" onclick="openUrl('http://localhost:5173/select-interview-mode')">🎯 面试模式选择</button>
            <button class="demo-button" onclick="openUrl('http://localhost:5173/voice-interview')">🎤 语音面试</button>
            <button class="demo-button" onclick="openUrl('http://localhost:5173/text-based-interview')">💬 文字面试</button>
            <button class="demo-button" onclick="openUrl('http://localhost:8000/docs')">📚 API文档</button>
        </div>
        
        <div class="section">
            <h3>⏱️ 录制计时器</h3>
            <div class="timer" id="recordingTimer">00:00</div>
            <button class="demo-button" id="recordButton" onclick="toggleRecording()">🔴 开始录制</button>
            <button class="demo-button" onclick="resetTimer()">🔄 重置计时器</button>
            <div class="progress-bar">
                <div class="progress-fill" id="timeProgress"></div>
            </div>
            <p>目标时长：7分钟</p>
        </div>
        
        <div class="section">
            <h3>📝 演示脚本分段</h3>
            
            <h4>第1段：开场介绍 (0:00-0:30)</h4>
            <div class="script-section">
解说词：大家好！欢迎观看iFlytek多模态智能面试评测系统演示。随着高校毕业生人数逐年攀升，面试成为求职的关键环节。我们的系统基于科大讯飞星火大模型，通过多模态AI分析，为高校学生提供智能化的面试训练和评估服务。

操作：
- 展示系统启动画面
- 显示iFlytek品牌标识
            </div>
            <button class="demo-button" onclick="startSegment(1, 30)">开始第1段</button>
            
            <h4>第2段：系统概览 (0:30-1:30)</h4>
            <div class="script-section">
解说词：我们的系统完全符合比赛要求，支持人工智能、大数据、物联网三大技术领域，涵盖技术岗、产品岗、运维测试岗等多种职位。系统采用Vue.js + Element Plus构建，界面简洁美观，完全中文化。

操作：
1. 访问 http://localhost:5173
2. 展示主页界面
3. 点击"选择面试模式"
4. 展示技术领域选择界面
5. 展示岗位类型选择
            </div>
            <button class="demo-button" onclick="startSegment(2, 60)">开始第2段</button>
            
            <h4>第3段：核心功能演示 (1:30-5:30)</h4>
            <div class="script-section">
解说词：现在演示完整的面试流程。我选择人工智能领域的AI工程师岗位，系统会根据选择生成针对性的面试问题。系统支持文本、语音、视频三种模态的分析，基于六项核心能力指标进行评估。

操作：
1. 选择"人工智能"领域
2. 选择"AI工程师"岗位
3. 点击"开始面试"
4. 演示多模态分析功能
5. 展示六维能力评估
6. 显示智能反馈报告
            </div>
            <button class="demo-button" onclick="startSegment(3, 240)">开始第3段</button>
            
            <h4>第4段：技术亮点展示 (5:30-7:00)</h4>
            <div class="script-section">
解说词：系统深度集成iFlytek星火大模型，提供15年专家级的分析能力。基于评估结果，系统会生成个性化的学习路径，包括短期、中期、长期的学习计划。系统具有优秀的性能表现，响应时间小于2秒。

操作：
1. 展示AI面试官的深度分析
2. 演示个性化学习路径
3. 显示系统性能指标
4. 展示系统总览
            </div>
            <button class="demo-button" onclick="startSegment(4, 90)">开始第4段</button>
        </div>
        
        <div class="section">
            <h3>🖥️ 演示预览窗口</h3>
            <iframe id="demoFrame" class="demo-frame" src="http://localhost:5173"></iframe>
            <button class="demo-button" onclick="refreshDemo()">🔄 刷新演示</button>
            <button class="demo-button" onclick="fullscreenDemo()">🔍 全屏演示</button>
        </div>
        
        <div class="section">
            <h3>✅ 录制完成检查</h3>
            <ul class="checklist" id="completionChecklist">
                <li data-item="intro">开场介绍完整</li>
                <li data-item="domains">展示3个技术领域</li>
                <li data-item="multimodal">演示多模态分析</li>
                <li data-item="capabilities">显示6项核心能力</li>
                <li data-item="feedback">展示可视化反馈</li>
                <li data-item="learning">演示学习路径</li>
                <li data-item="iflytek">强调iFlytek集成</li>
                <li data-item="time">控制在7分钟内</li>
            </ul>
            
            <button class="demo-button" onclick="exportChecklist()">📄 导出检查清单</button>
            <button class="demo-button" onclick="generateReport()">📊 生成录制报告</button>
        </div>
    </div>

    <script>
        let recordingStartTime = null;
        let recordingInterval = null;
        let currentSegment = null;
        let segmentStartTime = null;
        
        // 准备清单管理
        function updatePreparationProgress() {
            const checklist = document.getElementById('preparationChecklist');
            const items = checklist.querySelectorAll('li');
            const checkedItems = checklist.querySelectorAll('li.checked');
            const progress = (checkedItems.length / items.length) * 100;
            
            document.getElementById('preparationProgress').style.width = progress + '%';
        }
        
        // 清单项点击事件
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'LI' && e.target.closest('.checklist')) {
                e.target.classList.toggle('checked');
                updatePreparationProgress();
            }
        });
        
        // URL操作
        function openDemoUrl() {
            const url = document.getElementById('demoUrl').value;
            openUrl(url);
        }
        
        function openUrl(url) {
            document.getElementById('demoFrame').src = url;
            window.open(url, '_blank');
        }
        
        // 录制计时器
        function toggleRecording() {
            const button = document.getElementById('recordButton');
            
            if (recordingStartTime === null) {
                // 开始录制
                recordingStartTime = Date.now();
                recordingInterval = setInterval(updateTimer, 1000);
                button.textContent = '⏹️ 停止录制';
                button.classList.add('recording');
                console.log('🔴 录制开始');
            } else {
                // 停止录制
                clearInterval(recordingInterval);
                recordingStartTime = null;
                button.textContent = '🔴 开始录制';
                button.classList.remove('recording');
                console.log('⏹️ 录制停止');
            }
        }
        
        function updateTimer() {
            if (recordingStartTime === null) return;
            
            const elapsed = Date.now() - recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('recordingTimer').textContent = timeString;
            
            // 更新进度条（7分钟为100%）
            const progress = Math.min((elapsed / (7 * 60 * 1000)) * 100, 100);
            document.getElementById('timeProgress').style.width = progress + '%';
            
            // 时间提醒
            if (elapsed >= 6.5 * 60 * 1000 && elapsed < 6.6 * 60 * 1000) {
                console.log('⚠️ 录制时间接近7分钟，请准备结束');
            }
        }
        
        function resetTimer() {
            clearInterval(recordingInterval);
            recordingStartTime = null;
            document.getElementById('recordingTimer').textContent = '00:00';
            document.getElementById('timeProgress').style.width = '0%';
            document.getElementById('recordButton').textContent = '🔴 开始录制';
            document.getElementById('recordButton').classList.remove('recording');
        }
        
        // 分段录制
        function startSegment(segmentNumber, duration) {
            currentSegment = segmentNumber;
            segmentStartTime = Date.now();
            
            console.log(`🎬 开始第${segmentNumber}段录制，预计时长：${duration}秒`);
            
            // 设置分段提醒
            setTimeout(() => {
                if (currentSegment === segmentNumber) {
                    console.log(`⏰ 第${segmentNumber}段录制时间到`);
                }
            }, duration * 1000);
        }
        
        // 演示窗口操作
        function refreshDemo() {
            const frame = document.getElementById('demoFrame');
            frame.src = frame.src;
        }
        
        function fullscreenDemo() {
            const frame = document.getElementById('demoFrame');
            if (frame.requestFullscreen) {
                frame.requestFullscreen();
            }
        }
        
        // 导出功能
        function exportChecklist() {
            const checklist = document.getElementById('completionChecklist');
            const items = checklist.querySelectorAll('li');
            
            let checklistText = 'iFlytek面试系统演示视频录制检查清单\n';
            checklistText += '=' .repeat(50) + '\n\n';
            
            items.forEach((item, index) => {
                const checked = item.classList.contains('checked') ? '✅' : '❌';
                checklistText += `${checked} ${item.textContent}\n`;
            });
            
            checklistText += '\n录制时间：' + new Date().toLocaleString();
            
            // 下载文件
            const blob = new Blob([checklistText], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'demo-video-checklist.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                preparation: getChecklistStatus('preparationChecklist'),
                completion: getChecklistStatus('completionChecklist'),
                recordingTime: document.getElementById('recordingTimer').textContent,
                segments: {
                    intro: '0:00-0:30',
                    overview: '0:30-1:30',
                    demo: '1:30-5:30',
                    highlights: '5:30-7:00'
                }
            };
            
            console.log('📊 录制报告：', report);
            
            // 显示报告
            alert(`录制报告生成完成！\n\n录制时长：${report.recordingTime}\n准备完成度：${report.preparation.percentage}%\n检查完成度：${report.completion.percentage}%`);
        }
        
        function getChecklistStatus(checklistId) {
            const checklist = document.getElementById(checklistId);
            const items = checklist.querySelectorAll('li');
            const checkedItems = checklist.querySelectorAll('li.checked');
            
            return {
                total: items.length,
                completed: checkedItems.length,
                percentage: Math.round((checkedItems.length / items.length) * 100)
            };
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('🎬 演示视频制作工具已加载');
            console.log('📋 请按照清单准备录制环境');
            
            // 检查服务器状态
            fetch('http://localhost:5173')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ 前端服务器运行正常');
                        document.querySelector('[data-item="server"]').classList.add('checked');
                        updatePreparationProgress();
                    }
                })
                .catch(() => {
                    console.log('❌ 前端服务器未运行，请启动开发服务器');
                });
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        toggleRecording();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        refreshDemo();
                        break;
                }
            }
        });
    </script>
</body>
</html>
