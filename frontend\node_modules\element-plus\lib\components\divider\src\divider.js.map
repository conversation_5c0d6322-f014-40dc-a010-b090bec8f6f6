{"version": 3, "file": "divider.js", "sources": ["../../../../../../packages/components/divider/src/divider.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Divider from './divider.vue'\n\nexport type BorderStyle = CSSStyleDeclaration['borderStyle']\n\nexport const dividerProps = buildProps({\n  /**\n   * @description Set divider's direction\n   */\n  direction: {\n    type: String,\n    values: ['horizontal', 'vertical'],\n    default: 'horizontal',\n  },\n  /**\n   * @description Set the style of divider\n   */\n  contentPosition: {\n    type: String,\n    values: ['left', 'center', 'right'],\n    default: 'center',\n  },\n  /**\n   * @description the position of the customized content on the divider line\n   */\n  borderStyle: {\n    type: definePropType<BorderStyle>(String),\n    default: 'solid',\n  },\n} as const)\nexport type DividerProps = ExtractPropTypes<typeof dividerProps>\n\nexport type DividerInstance = InstanceType<typeof Divider> & unknown\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,YAAY,GAAGA,kBAAU,CAAC;AACvC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACtC,IAAI,OAAO,EAAE,YAAY;AACzB,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;AACvC,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,CAAC;;;;"}