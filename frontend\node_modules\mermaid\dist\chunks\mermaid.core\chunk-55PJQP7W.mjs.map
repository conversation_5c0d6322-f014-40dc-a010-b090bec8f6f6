{"version": 3, "sources": ["../../../src/utils.ts"], "sourcesContent": ["import { sanitizeUrl } from '@braintree/sanitize-url';\nimport type { BaseType, CurveFactory } from 'd3';\nimport {\n  curveBasis,\n  curveBasisClosed,\n  curveBasisOpen,\n  curveBumpX,\n  curveBumpY,\n  curveBundle,\n  curveCardinalClosed,\n  curveCardinalOpen,\n  curveCardinal,\n  curveCatmullRomClosed,\n  curveCatmullRomOpen,\n  curveCatmullRom,\n  curveLinear,\n  curveLinearClosed,\n  curveMonotoneX,\n  curveMonotoneY,\n  curveNatural,\n  curveStep,\n  curveStepAfter,\n  curveStepBefore,\n  select,\n} from 'd3';\nimport common from './diagrams/common/common.js';\nimport { sanitizeDirective } from './utils/sanitizeDirective.js';\nimport { log } from './logger.js';\nimport { detectType } from './diagram-api/detectType.js';\nimport assignWithDepth from './assignWithDepth.js';\nimport type { MermaidConfig } from './config.type.js';\nimport memoize from 'lodash-es/memoize.js';\nimport merge from 'lodash-es/merge.js';\nimport { directiveRegex } from './diagram-api/regexes.js';\nimport type { D3Element, Point, TextDimensionConfig, TextDimensions } from './types.js';\n\nexport const ZERO_WIDTH_SPACE = '\\u200b';\n\n// Effectively an enum of the supported curve types, accessible by name\nconst d3CurveTypes = {\n  curveBasis: curveBasis,\n  curveBasisClosed: curveBasisClosed,\n  curveBasisOpen: curveBasisOpen,\n  curveBumpX: curveBumpX,\n  curveBumpY: curveBumpY,\n  curveBundle: curveBundle,\n  curveCardinalClosed: curveCardinalClosed,\n  curveCardinalOpen: curveCardinalOpen,\n  curveCardinal: curveCardinal,\n  curveCatmullRomClosed: curveCatmullRomClosed,\n  curveCatmullRomOpen: curveCatmullRomOpen,\n  curveCatmullRom: curveCatmullRom,\n  curveLinear: curveLinear,\n  curveLinearClosed: curveLinearClosed,\n  curveMonotoneX: curveMonotoneX,\n  curveMonotoneY: curveMonotoneY,\n  curveNatural: curveNatural,\n  curveStep: curveStep,\n  curveStepAfter: curveStepAfter,\n  curveStepBefore: curveStepBefore,\n} as const;\n\nconst directiveWithoutOpen =\n  /\\s*(?:(\\w+)(?=:):|(\\w+))\\s*(?:(\\w+)|((?:(?!}%{2}).|\\r?\\n)*))?\\s*(?:}%{2})?/gi;\n/**\n * Detects the init config object from the text\n *\n * @param text - The text defining the graph. For example:\n *\n * ```mermaid\n * %%{init: {\"theme\": \"debug\", \"logLevel\": 1 }}%%\n * graph LR\n *      a-->b\n *      b-->c\n *      c-->d\n *      d-->e\n *      e-->f\n *      f-->g\n *      g-->h\n * ```\n *\n * Or\n *\n * ```mermaid\n * %%{initialize: {\"theme\": \"dark\", logLevel: \"debug\" }}%%\n * graph LR\n *    a-->b\n *    b-->c\n *    c-->d\n *    d-->e\n *    e-->f\n *    f-->g\n *    g-->h\n * ```\n *\n * @param config - Optional mermaid configuration object.\n * @returns The json object representing the init passed to mermaid.initialize()\n */\nexport const detectInit = function (\n  text: string,\n  config?: MermaidConfig\n): MermaidConfig | undefined {\n  const inits = detectDirective(text, /(?:init\\b)|(?:initialize\\b)/);\n  let results: MermaidConfig & { config?: unknown } = {};\n\n  if (Array.isArray(inits)) {\n    const args = inits.map((init) => init.args);\n    sanitizeDirective(args);\n    results = assignWithDepth(results, [...args]);\n  } else {\n    results = inits.args as MermaidConfig;\n  }\n\n  if (!results) {\n    return;\n  }\n\n  let type = detectType(text, config);\n\n  // Move the `config` value to appropriate diagram type value\n  const prop = 'config';\n  if (results[prop] !== undefined) {\n    if (type === 'flowchart-v2') {\n      type = 'flowchart';\n    }\n    results[type as keyof MermaidConfig] = results[prop];\n    delete results[prop];\n  }\n\n  return results;\n};\n\ninterface Directive {\n  type?: string;\n  args?: unknown;\n}\n/**\n * Detects the directive from the text.\n *\n * Text can be single line or multiline. If type is null or omitted,\n * the first directive encountered in text will be returned\n *\n * ```mermaid\n * graph LR\n * %%{someDirective}%%\n *    a-->b\n *    b-->c\n *    c-->d\n *    d-->e\n *    e-->f\n *    f-->g\n *    g-->h\n * ```\n *\n * @param text - The text defining the graph\n * @param type - The directive to return (default: `null`)\n * @returns An object or Array representing the directive(s) matched by the input type.\n * If a single directive was found, that directive object will be returned.\n */\nexport const detectDirective = function (\n  text: string,\n  type: string | RegExp | null = null\n): Directive | Directive[] {\n  try {\n    const commentWithoutDirectives = new RegExp(\n      `[%]{2}(?![{]${directiveWithoutOpen.source})(?=[}][%]{2}).*\\n`,\n      'ig'\n    );\n    text = text.trim().replace(commentWithoutDirectives, '').replace(/'/gm, '\"');\n    log.debug(\n      `Detecting diagram directive${type !== null ? ' type:' + type : ''} based on the text:${text}`\n    );\n    let match: RegExpExecArray | null;\n    const result: Directive[] = [];\n    while ((match = directiveRegex.exec(text)) !== null) {\n      // This is necessary to avoid infinite loops with zero-width matches\n      if (match.index === directiveRegex.lastIndex) {\n        directiveRegex.lastIndex++;\n      }\n      if ((match && !type) || (type && match[1]?.match(type)) || (type && match[2]?.match(type))) {\n        const type = match[1] ? match[1] : match[2];\n        const args = match[3] ? match[3].trim() : match[4] ? JSON.parse(match[4].trim()) : null;\n        result.push({ type, args });\n      }\n    }\n    if (result.length === 0) {\n      return { type: text, args: null };\n    }\n\n    return result.length === 1 ? result[0] : result;\n  } catch (error) {\n    log.error(\n      `ERROR: ${\n        (error as Error).message\n      } - Unable to parse directive type: '${type}' based on the text: '${text}'`\n    );\n    return { type: undefined, args: null };\n  }\n};\n\nexport const removeDirectives = function (text: string): string {\n  return text.replace(directiveRegex, '');\n};\n\n/**\n * Detects whether a substring in present in a given array\n *\n * @param str - The substring to detect\n * @param arr - The array to search\n * @returns The array index containing the substring or -1 if not present\n */\nexport const isSubstringInArray = function (str: string, arr: string[]): number {\n  for (const [i, element] of arr.entries()) {\n    if (element.match(str)) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n/**\n * Returns a d3 curve given a curve name\n *\n * @param interpolate - The interpolation name\n * @param defaultCurve - The default curve to return\n * @returns The curve factory to use\n */\nexport function interpolateToCurve(\n  interpolate: string | undefined,\n  defaultCurve: CurveFactory\n): CurveFactory {\n  if (!interpolate) {\n    return defaultCurve;\n  }\n  const curveName = `curve${interpolate.charAt(0).toUpperCase() + interpolate.slice(1)}`;\n\n  // @ts-ignore TODO: Fix issue with curve type\n  return d3CurveTypes[curveName as keyof typeof d3CurveTypes] ?? defaultCurve;\n}\n\n/**\n * Formats a URL string\n *\n * @param linkStr - String of the URL\n * @param config - Configuration passed to MermaidJS\n * @returns The formatted URL or `undefined`.\n */\nexport function formatUrl(linkStr: string, config: MermaidConfig): string | undefined {\n  const url = linkStr.trim();\n\n  if (!url) {\n    return undefined;\n  }\n\n  if (config.securityLevel !== 'loose') {\n    return sanitizeUrl(url);\n  }\n\n  return url;\n}\n\n/**\n * Runs a function\n *\n * @param functionName - A dot separated path to the function relative to the `window`\n * @param params - Parameters to pass to the function\n */\nexport const runFunc = (functionName: string, ...params: unknown[]) => {\n  const arrPaths = functionName.split('.');\n\n  const len = arrPaths.length - 1;\n  const fnName = arrPaths[len];\n\n  let obj = window;\n  for (let i = 0; i < len; i++) {\n    obj = obj[arrPaths[i] as keyof typeof obj];\n    if (!obj) {\n      log.error(`Function name: ${functionName} not found in window`);\n      return;\n    }\n  }\n\n  obj[fnName as keyof typeof obj](...params);\n};\n\n/**\n * Finds the distance between two points using the Distance Formula\n *\n * @param p1 - The first point\n * @param p2 - The second point\n * @returns The distance between the two points.\n */\nfunction distance(p1?: Point, p2?: Point): number {\n  if (!p1 || !p2) {\n    return 0;\n  }\n  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n}\n\n/**\n * TODO: Give this a description\n *\n * @param points - List of points\n */\nfunction traverseEdge(points: Point[]): Point {\n  let prevPoint: Point | undefined;\n  let totalDistance = 0;\n\n  points.forEach((point) => {\n    totalDistance += distance(point, prevPoint);\n    prevPoint = point;\n  });\n\n  // Traverse half of total distance along points\n  const remainingDistance = totalDistance / 2;\n  return calculatePoint(points, remainingDistance);\n}\n\n/**\n * {@inheritdoc traverseEdge}\n */\nfunction calcLabelPosition(points: Point[]): Point {\n  if (points.length === 1) {\n    return points[0];\n  }\n  return traverseEdge(points);\n}\n\nexport const roundNumber = (num: number, precision = 2) => {\n  const factor = Math.pow(10, precision);\n  return Math.round(num * factor) / factor;\n};\n\nexport const calculatePoint = (points: Point[], distanceToTraverse: number): Point => {\n  let prevPoint: Point | undefined = undefined;\n  let remainingDistance = distanceToTraverse;\n  for (const point of points) {\n    if (prevPoint) {\n      const vectorDistance = distance(point, prevPoint);\n      if (vectorDistance === 0) {\n        return prevPoint;\n      }\n      if (vectorDistance < remainingDistance) {\n        remainingDistance -= vectorDistance;\n      } else {\n        // The point is remainingDistance from prevPoint in the vector between prevPoint and point\n        // Calculate the coordinates\n        const distanceRatio = remainingDistance / vectorDistance;\n        if (distanceRatio <= 0) {\n          return prevPoint;\n        }\n        if (distanceRatio >= 1) {\n          return { x: point.x, y: point.y };\n        }\n        if (distanceRatio > 0 && distanceRatio < 1) {\n          return {\n            x: roundNumber((1 - distanceRatio) * prevPoint.x + distanceRatio * point.x, 5),\n            y: roundNumber((1 - distanceRatio) * prevPoint.y + distanceRatio * point.y, 5),\n          };\n        }\n      }\n    }\n    prevPoint = point;\n  }\n  throw new Error('Could not find a suitable point for the given distance');\n};\n\nconst calcCardinalityPosition = (\n  isRelationTypePresent: boolean,\n  points: Point[],\n  initialPosition: Point\n) => {\n  log.info(`our points ${JSON.stringify(points)}`);\n  if (points[0] !== initialPosition) {\n    points = points.reverse();\n  }\n  // Traverse only 25 total distance along points to find cardinality point\n  const distanceToCardinalityPoint = 25;\n  const center = calculatePoint(points, distanceToCardinalityPoint);\n  // if relation is present (Arrows will be added), change cardinality point off-set distance (d)\n  const d = isRelationTypePresent ? 10 : 5;\n  //Calculate Angle for x and y axis\n  const angle = Math.atan2(points[0].y - center.y, points[0].x - center.x);\n  const cardinalityPosition = { x: 0, y: 0 };\n  //Calculation cardinality position using angle, center point on the line/curve but perpendicular and with offset-distance\n  cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2;\n  cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2;\n  return cardinalityPosition;\n};\n\n/**\n * Calculates the terminal label position.\n *\n * @param terminalMarkerSize - Terminal marker size.\n * @param position - Position of label relative to points.\n * @param _points - Array of points.\n * @returns - The `cardinalityPosition`.\n */\nfunction calcTerminalLabelPosition(\n  terminalMarkerSize: number,\n  position: 'start_left' | 'start_right' | 'end_left' | 'end_right',\n  _points: Point[]\n): Point {\n  const points = structuredClone(_points);\n  log.info('our points', points);\n  if (position !== 'start_left' && position !== 'start_right') {\n    points.reverse();\n  }\n\n  // Traverse only 25 total distance along points to find cardinality point\n  const distanceToCardinalityPoint = 25 + terminalMarkerSize;\n  const center = calculatePoint(points, distanceToCardinalityPoint);\n\n  // if relation is present (Arrows will be added), change cardinality point off-set distance (d)\n  const d = 10 + terminalMarkerSize * 0.5;\n  //Calculate Angle for x and y axis\n  const angle = Math.atan2(points[0].y - center.y, points[0].x - center.x);\n\n  const cardinalityPosition: Point = { x: 0, y: 0 };\n  //Calculation cardinality position using angle, center point on the line/curve but perpendicular and with offset-distance\n\n  if (position === 'start_left') {\n    cardinalityPosition.x = Math.sin(angle + Math.PI) * d + (points[0].x + center.x) / 2;\n    cardinalityPosition.y = -Math.cos(angle + Math.PI) * d + (points[0].y + center.y) / 2;\n  } else if (position === 'end_right') {\n    cardinalityPosition.x = Math.sin(angle - Math.PI) * d + (points[0].x + center.x) / 2 - 5;\n    cardinalityPosition.y = -Math.cos(angle - Math.PI) * d + (points[0].y + center.y) / 2 - 5;\n  } else if (position === 'end_left') {\n    cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2 - 5;\n    cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2 - 5;\n  } else {\n    cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2;\n    cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2;\n  }\n  return cardinalityPosition;\n}\n\n/**\n * Gets styles from an array of declarations\n *\n * @param arr - Declarations\n * @returns The styles grouped as strings\n */\nexport function getStylesFromArray(arr: string[]): { style: string; labelStyle: string } {\n  let style = '';\n  let labelStyle = '';\n\n  for (const element of arr) {\n    if (element !== undefined) {\n      // add text properties to label style definition\n      if (element.startsWith('color:') || element.startsWith('text-align:')) {\n        labelStyle = labelStyle + element + ';';\n      } else {\n        style = style + element + ';';\n      }\n    }\n  }\n\n  return { style, labelStyle };\n}\n\nlet cnt = 0;\nexport const generateId = () => {\n  cnt++;\n  return 'id-' + Math.random().toString(36).substr(2, 12) + '-' + cnt;\n};\n\n/**\n * Generates a random hexadecimal id of the given length.\n *\n * @param length - Length of string.\n * @returns The generated string.\n */\nfunction makeRandomHex(length: number): string {\n  let result = '';\n  const characters = '0123456789abcdef';\n  const charactersLength = characters.length;\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * charactersLength));\n  }\n  return result;\n}\n\nexport const random = (options: { length: number }) => {\n  return makeRandomHex(options.length);\n};\n\nexport const getTextObj = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: undefined,\n    anchor: 'start',\n    style: '#666',\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    valign: undefined,\n    text: '',\n  };\n};\n\n/**\n * Adds text to an element\n *\n * @param elem - SVG Element to add text to\n * @param textData - Text options.\n * @returns Text element with given styling and content\n */\nexport const drawSimpleText = function (\n  elem: SVGElement,\n  textData: {\n    text: string;\n    x: number;\n    y: number;\n    anchor: 'start' | 'middle' | 'end';\n    fontFamily: string;\n    fontSize: string | number;\n    fontWeight: string | number;\n    fill: string;\n    class: string | undefined;\n    textMargin: number;\n  }\n): SVGTextElement {\n  // Remove and ignore br:s\n  const nText = textData.text.replace(common.lineBreakRegex, ' ');\n\n  const [, _fontSizePx] = parseFontSize(textData.fontSize);\n\n  const textElem = elem.append('text') as any;\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.style('text-anchor', textData.anchor);\n  textElem.style('font-family', textData.fontFamily);\n  textElem.style('font-size', _fontSizePx);\n  textElem.style('font-weight', textData.fontWeight);\n  textElem.attr('fill', textData.fill);\n\n  if (textData.class !== undefined) {\n    textElem.attr('class', textData.class);\n  }\n\n  const span = textElem.append('tspan');\n  span.attr('x', textData.x + textData.textMargin * 2);\n  span.attr('fill', textData.fill);\n  span.text(nText);\n\n  return textElem;\n};\n\ninterface WrapLabelConfig {\n  fontSize: number;\n  fontFamily: string;\n  fontWeight: number;\n  joinWith: string;\n}\n\nexport const wrapLabel: (label: string, maxWidth: number, config: WrapLabelConfig) => string =\n  memoize(\n    (label: string, maxWidth: number, config: WrapLabelConfig): string => {\n      if (!label) {\n        return label;\n      }\n      config = Object.assign(\n        { fontSize: 12, fontWeight: 400, fontFamily: 'Arial', joinWith: '<br/>' },\n        config\n      );\n      if (common.lineBreakRegex.test(label)) {\n        return label;\n      }\n      const words = label.split(' ').filter(Boolean);\n      const completedLines: string[] = [];\n      let nextLine = '';\n      words.forEach((word, index) => {\n        const wordLength = calculateTextWidth(`${word} `, config);\n        const nextLineLength = calculateTextWidth(nextLine, config);\n        if (wordLength > maxWidth) {\n          const { hyphenatedStrings, remainingWord } = breakString(word, maxWidth, '-', config);\n          completedLines.push(nextLine, ...hyphenatedStrings);\n          nextLine = remainingWord;\n        } else if (nextLineLength + wordLength >= maxWidth) {\n          completedLines.push(nextLine);\n          nextLine = word;\n        } else {\n          nextLine = [nextLine, word].filter(Boolean).join(' ');\n        }\n        const currentWord = index + 1;\n        const isLastWord = currentWord === words.length;\n        if (isLastWord) {\n          completedLines.push(nextLine);\n        }\n      });\n      return completedLines.filter((line) => line !== '').join(config.joinWith);\n    },\n    (label, maxWidth, config) =>\n      `${label}${maxWidth}${config.fontSize}${config.fontWeight}${config.fontFamily}${config.joinWith}`\n  );\n\ninterface BreakStringOutput {\n  hyphenatedStrings: string[];\n  remainingWord: string;\n}\n\nconst breakString: (\n  word: string,\n  maxWidth: number,\n  hyphenCharacter: string,\n  config: WrapLabelConfig\n) => BreakStringOutput = memoize(\n  (\n    word: string,\n    maxWidth: number,\n    hyphenCharacter = '-',\n    config: WrapLabelConfig\n  ): BreakStringOutput => {\n    config = Object.assign(\n      { fontSize: 12, fontWeight: 400, fontFamily: 'Arial', margin: 0 },\n      config\n    );\n    const characters = [...word];\n    const lines: string[] = [];\n    let currentLine = '';\n    characters.forEach((character, index) => {\n      const nextLine = `${currentLine}${character}`;\n      const lineWidth = calculateTextWidth(nextLine, config);\n      if (lineWidth >= maxWidth) {\n        const currentCharacter = index + 1;\n        const isLastLine = characters.length === currentCharacter;\n        const hyphenatedNextLine = `${nextLine}${hyphenCharacter}`;\n        lines.push(isLastLine ? nextLine : hyphenatedNextLine);\n        currentLine = '';\n      } else {\n        currentLine = nextLine;\n      }\n    });\n    return { hyphenatedStrings: lines, remainingWord: currentLine };\n  },\n  (word, maxWidth, hyphenCharacter = '-', config) =>\n    `${word}${maxWidth}${hyphenCharacter}${config.fontSize}${config.fontWeight}${config.fontFamily}`\n);\n\n/**\n * This calculates the text's height, taking into account the wrap breaks and both the statically\n * configured height, width, and the length of the text (in pixels).\n *\n * If the wrapped text has greater height, we extend the height, so it's value won't overflow.\n *\n * @param text - The text to measure\n * @param config - The config for fontSize, fontFamily, and fontWeight all impacting the\n *   resulting size\n * @returns The height for the given text\n */\nexport function calculateTextHeight(\n  text: Parameters<typeof calculateTextDimensions>[0],\n  config: Parameters<typeof calculateTextDimensions>[1]\n): ReturnType<typeof calculateTextDimensions>['height'] {\n  return calculateTextDimensions(text, config).height;\n}\n\n/**\n * This calculates the width of the given text, font size and family.\n *\n * @param text - The text to calculate the width of\n * @param config - The config for fontSize, fontFamily, and fontWeight all impacting the\n *   resulting size\n * @returns The width for the given text\n */\nexport function calculateTextWidth(\n  text: Parameters<typeof calculateTextDimensions>[0],\n  config: Parameters<typeof calculateTextDimensions>[1]\n): ReturnType<typeof calculateTextDimensions>['width'] {\n  return calculateTextDimensions(text, config).width;\n}\n\n/**\n * This calculates the dimensions of the given text, font size, font family, font weight, and\n * margins.\n *\n * @param text - The text to calculate the width of\n * @param config - The config for fontSize, fontFamily, fontWeight, and margin all impacting\n *   the resulting size\n * @returns The dimensions for the given text\n */\nexport const calculateTextDimensions: (\n  text: string,\n  config: TextDimensionConfig\n) => TextDimensions = memoize(\n  (text: string, config: TextDimensionConfig): TextDimensions => {\n    const { fontSize = 12, fontFamily = 'Arial', fontWeight = 400 } = config;\n    if (!text) {\n      return { width: 0, height: 0 };\n    }\n\n    const [, _fontSizePx] = parseFontSize(fontSize);\n\n    // We can't really know if the user supplied font family will render on the user agent;\n    // thus, we'll take the max width between the user supplied font family, and a default\n    // of sans-serif.\n    const fontFamilies = ['sans-serif', fontFamily];\n    const lines = text.split(common.lineBreakRegex);\n    const dims = [];\n\n    const body = select('body');\n    // We don't want to leak DOM elements - if a removal operation isn't available\n    // for any reason, do not continue.\n    if (!body.remove) {\n      return { width: 0, height: 0, lineHeight: 0 };\n    }\n\n    const g = body.append('svg');\n\n    for (const fontFamily of fontFamilies) {\n      let cHeight = 0;\n      const dim = { width: 0, height: 0, lineHeight: 0 };\n      for (const line of lines) {\n        const textObj = getTextObj();\n        textObj.text = line || ZERO_WIDTH_SPACE;\n        // @ts-ignore TODO: Fix D3 types\n        const textElem = drawSimpleText(g, textObj)\n          // @ts-ignore TODO: Fix D3 types\n          .style('font-size', _fontSizePx)\n          .style('font-weight', fontWeight)\n          .style('font-family', fontFamily);\n\n        const bBox = (textElem._groups || textElem)[0][0].getBBox();\n        if (bBox.width === 0 && bBox.height === 0) {\n          throw new Error('svg element not in render tree');\n        }\n        dim.width = Math.round(Math.max(dim.width, bBox.width));\n        cHeight = Math.round(bBox.height);\n        dim.height += cHeight;\n        dim.lineHeight = Math.round(Math.max(dim.lineHeight, cHeight));\n      }\n      dims.push(dim);\n    }\n\n    g.remove();\n\n    const index =\n      isNaN(dims[1].height) ||\n      isNaN(dims[1].width) ||\n      isNaN(dims[1].lineHeight) ||\n      (dims[0].height > dims[1].height &&\n        dims[0].width > dims[1].width &&\n        dims[0].lineHeight > dims[1].lineHeight)\n        ? 0\n        : 1;\n    return dims[index];\n  },\n  (text, config) => `${text}${config.fontSize}${config.fontWeight}${config.fontFamily}`\n);\n\nexport class InitIDGenerator {\n  private count = 0;\n  public next: () => number;\n  constructor(deterministic = false, seed?: string) {\n    // TODO: Seed is only used for length?\n    // v11: Use the actual value of seed string to generate an initial value for count.\n    this.count = seed ? seed.length : 0;\n    this.next = deterministic ? () => this.count++ : () => Date.now();\n  }\n}\n\nlet decoder: HTMLDivElement;\n\n/**\n * Decodes HTML, source: {@link https://github.com/shrpne/entity-decode/blob/v2.0.1/browser.js}\n *\n * @param html - HTML as a string\n * @returns Unescaped HTML\n */\nexport const entityDecode = function (html: string): string {\n  decoder = decoder || document.createElement('div');\n  // Escape HTML before decoding for HTML Entities\n  html = escape(html).replace(/%26/g, '&').replace(/%23/g, '#').replace(/%3B/g, ';');\n  decoder.innerHTML = html;\n\n  return unescape(decoder.textContent!);\n};\n\nexport interface DetailedError {\n  str: string;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  hash: any;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  error?: any;\n  message?: string;\n}\n\n/** @param error - The error to check */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function isDetailedError(error: any): error is DetailedError {\n  return 'str' in error;\n}\n\n/** @param error - The error to convert to an error message */\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return String(error);\n}\n\n/**\n * Appends <text> element with the given title and css class.\n *\n * @param parent - d3 svg object to append title to\n * @param cssClass - CSS class for the <text> element containing the title\n * @param titleTopMargin - Margin in pixels between title and rest of the graph\n * @param title - The title. If empty, returns immediately.\n */\nexport const insertTitle = (\n  parent: D3Element,\n  cssClass: string,\n  titleTopMargin: number,\n  title?: string\n): void => {\n  if (!title) {\n    return;\n  }\n  const bounds = parent.node()?.getBBox();\n  if (!bounds) {\n    return;\n  }\n  parent\n    .append('text')\n    .text(title)\n    .attr('text-anchor', 'middle')\n    .attr('x', bounds.x + bounds.width / 2)\n    .attr('y', -titleTopMargin)\n    .attr('class', cssClass);\n};\n\n/**\n * Parses a raw fontSize configuration value into a number and string value.\n *\n * @param fontSize - a string or number font size configuration value\n *\n * @returns parsed number and string style font size values, or nulls if a number value can't\n * be parsed from an input string.\n */\nexport const parseFontSize = (fontSize: string | number | undefined): [number?, string?] => {\n  // if the font size is a number, assume a px string representation\n  if (typeof fontSize === 'number') {\n    return [fontSize, fontSize + 'px'];\n  }\n\n  const fontSizeNumber = parseInt(fontSize ?? '', 10);\n  if (Number.isNaN(fontSizeNumber)) {\n    // if a number value can't be parsed, return null for both values\n    return [undefined, undefined];\n  } else if (fontSize === String(fontSizeNumber)) {\n    // if a string input doesn't contain any units, assume px units\n    return [fontSizeNumber, fontSize + 'px'];\n  } else {\n    return [fontSizeNumber, fontSize];\n  }\n};\n\nexport function cleanAndMerge<T>(defaultData: T, data?: Partial<T>): T {\n  return merge({}, defaultData, data);\n}\n\nexport default {\n  assignWithDepth,\n  wrapLabel,\n  calculateTextHeight,\n  calculateTextWidth,\n  calculateTextDimensions,\n  cleanAndMerge,\n  detectInit,\n  detectDirective,\n  isSubstringInArray,\n  interpolateToCurve,\n  calcLabelPosition,\n  calcCardinalityPosition,\n  calcTerminalLabelPosition,\n  formatUrl,\n  getStylesFromArray,\n  generateId,\n  random,\n  runFunc,\n  entityDecode,\n  insertTitle,\n  parseFontSize,\n  InitIDGenerator,\n};\n\n/**\n * @param  text - text to be encoded\n * @returns\n */\nexport const encodeEntities = function (text: string): string {\n  let txt = text;\n\n  txt = txt.replace(/style.*:\\S*#.*;/g, function (s): string {\n    return s.substring(0, s.length - 1);\n  });\n  txt = txt.replace(/classDef.*:\\S*#.*;/g, function (s): string {\n    return s.substring(0, s.length - 1);\n  });\n\n  txt = txt.replace(/#\\w+;/g, function (s) {\n    const innerTxt = s.substring(1, s.length - 1);\n\n    const isInt = /^\\+?\\d+$/.test(innerTxt);\n    if (isInt) {\n      return 'ﬂ°°' + innerTxt + '¶ß';\n    } else {\n      return 'ﬂ°' + innerTxt + '¶ß';\n    }\n  });\n\n  return txt;\n};\n\n/**\n *\n * @param  text - text to be decoded\n * @returns\n */\nexport const decodeEntities = function (text: string): string {\n  return text.replace(/ﬂ°°/g, '&#').replace(/ﬂ°/g, '&').replace(/¶ß/g, ';');\n};\n\nexport const isString = (value: unknown): value is string => {\n  return typeof value === 'string';\n};\n\nexport const getEdgeId = (\n  from: string,\n  to: string,\n  {\n    counter = 0,\n    prefix,\n    suffix,\n  }: {\n    counter?: number;\n    prefix?: string;\n    suffix?: string;\n  },\n  id?: string\n) => {\n  if (id) {\n    return id;\n  }\n  return `${prefix ? `${prefix}_` : ''}${from}_${to}_${counter}${suffix ? `_${suffix}` : ''}`;\n};\n\n/**\n * D3's `selection.attr` method doesn't officially support `undefined`.\n *\n * However, it seems if you do pass `undefined`, it seems to be treated as `null`\n * (e.g. it removes the attribute).\n */\nexport function handleUndefinedAttr(\n  attrValue: Parameters<d3.Selection<BaseType, unknown, HTMLElement, any>['attr']>[1] | undefined\n) {\n  return attrValue ?? null;\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAAS,mBAAmB;AAE5B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAOP,OAAO,aAAa;AACpB,OAAO,WAAW;AAIX,IAAM,mBAAmB;AAGhC,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,uBACJ;AAmCK,IAAM,aAAa,gCACxB,MACA,QAC2B;AAC3B,QAAM,QAAQ,gBAAgB,MAAM,6BAA6B;AACjE,MAAI,UAAgD,CAAC;AAErD,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,UAAM,OAAO,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI;AAC1C,sBAAkB,IAAI;AACtB,cAAU,wBAAgB,SAAS,CAAC,GAAG,IAAI,CAAC;AAAA,EAC9C,OAAO;AACL,cAAU,MAAM;AAAA,EAClB;AAEA,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,MAAM,MAAM;AAGlC,QAAM,OAAO;AACb,MAAI,QAAQ,IAAI,MAAM,QAAW;AAC/B,QAAI,SAAS,gBAAgB;AAC3B,aAAO;AAAA,IACT;AACA,YAAQ,IAA2B,IAAI,QAAQ,IAAI;AACnD,WAAO,QAAQ,IAAI;AAAA,EACrB;AAEA,SAAO;AACT,GAhC0B;AA6DnB,IAAM,kBAAkB,gCAC7B,MACA,OAA+B,MACN;AACzB,MAAI;AACF,UAAM,2BAA2B,IAAI;AAAA,MACnC,eAAe,qBAAqB,MAAM;AAAA;AAAA,MAC1C;AAAA,IACF;AACA,WAAO,KAAK,KAAK,EAAE,QAAQ,0BAA0B,EAAE,EAAE,QAAQ,OAAO,GAAG;AAC3E,QAAI;AAAA,MACF,8BAA8B,SAAS,OAAO,WAAW,OAAO,EAAE,sBAAsB,IAAI;AAAA,IAC9F;AACA,QAAI;AACJ,UAAM,SAAsB,CAAC;AAC7B,YAAQ,QAAQ,eAAe,KAAK,IAAI,OAAO,MAAM;AAEnD,UAAI,MAAM,UAAU,eAAe,WAAW;AAC5C,uBAAe;AAAA,MACjB;AACA,UAAK,SAAS,CAAC,QAAU,QAAQ,MAAM,CAAC,GAAG,MAAM,IAAI,KAAO,QAAQ,MAAM,CAAC,GAAG,MAAM,IAAI,GAAI;AAC1F,cAAMA,QAAO,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;AAC1C,cAAM,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI;AACnF,eAAO,KAAK,EAAE,MAAAA,OAAM,KAAK,CAAC;AAAA,MAC5B;AAAA,IACF;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,EAAE,MAAM,MAAM,MAAM,KAAK;AAAA,IAClC;AAEA,WAAO,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,EAC3C,SAAS,OAAO;AACd,QAAI;AAAA,MACF,UACG,MAAgB,OACnB,uCAAuC,IAAI,yBAAyB,IAAI;AAAA,IAC1E;AACA,WAAO,EAAE,MAAM,QAAW,MAAM,KAAK;AAAA,EACvC;AACF,GAvC+B;AAyCxB,IAAM,mBAAmB,gCAAU,MAAsB;AAC9D,SAAO,KAAK,QAAQ,gBAAgB,EAAE;AACxC,GAFgC;AAWzB,IAAM,qBAAqB,gCAAU,KAAa,KAAuB;AAC9E,aAAW,CAAC,GAAG,OAAO,KAAK,IAAI,QAAQ,GAAG;AACxC,QAAI,QAAQ,MAAM,GAAG,GAAG;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT,GAPkC;AAgB3B,SAAS,mBACd,aACA,cACc;AACd,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,QAAQ,YAAY,OAAO,CAAC,EAAE,YAAY,IAAI,YAAY,MAAM,CAAC,CAAC;AAGpF,SAAO,aAAa,SAAsC,KAAK;AACjE;AAXgB;AAoBT,SAAS,UAAU,SAAiB,QAA2C;AACpF,QAAM,MAAM,QAAQ,KAAK;AAEzB,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,kBAAkB,SAAS;AACpC,WAAO,YAAY,GAAG;AAAA,EACxB;AAEA,SAAO;AACT;AAZgB;AAoBT,IAAM,UAAU,wBAAC,iBAAyB,WAAsB;AACrE,QAAM,WAAW,aAAa,MAAM,GAAG;AAEvC,QAAM,MAAM,SAAS,SAAS;AAC9B,QAAM,SAAS,SAAS,GAAG;AAE3B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,IAAI,SAAS,CAAC,CAAqB;AACzC,QAAI,CAAC,KAAK;AACR,UAAI,MAAM,kBAAkB,YAAY,sBAAsB;AAC9D;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAA0B,EAAE,GAAG,MAAM;AAC3C,GAhBuB;AAyBvB,SAAS,SAAS,IAAY,IAAoB;AAChD,MAAI,CAAC,MAAM,CAAC,IAAI;AACd,WAAO;AAAA,EACT;AACA,SAAO,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;AACtE;AALS;AAYT,SAAS,aAAa,QAAwB;AAC5C,MAAI;AACJ,MAAI,gBAAgB;AAEpB,SAAO,QAAQ,CAAC,UAAU;AACxB,qBAAiB,SAAS,OAAO,SAAS;AAC1C,gBAAY;AAAA,EACd,CAAC;AAGD,QAAM,oBAAoB,gBAAgB;AAC1C,SAAO,eAAe,QAAQ,iBAAiB;AACjD;AAZS;AAiBT,SAAS,kBAAkB,QAAwB;AACjD,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO,OAAO,CAAC;AAAA,EACjB;AACA,SAAO,aAAa,MAAM;AAC5B;AALS;AAOF,IAAM,cAAc,wBAAC,KAAa,YAAY,MAAM;AACzD,QAAM,SAAS,KAAK,IAAI,IAAI,SAAS;AACrC,SAAO,KAAK,MAAM,MAAM,MAAM,IAAI;AACpC,GAH2B;AAKpB,IAAM,iBAAiB,wBAAC,QAAiB,uBAAsC;AACpF,MAAI,YAA+B;AACnC,MAAI,oBAAoB;AACxB,aAAW,SAAS,QAAQ;AAC1B,QAAI,WAAW;AACb,YAAM,iBAAiB,SAAS,OAAO,SAAS;AAChD,UAAI,mBAAmB,GAAG;AACxB,eAAO;AAAA,MACT;AACA,UAAI,iBAAiB,mBAAmB;AACtC,6BAAqB;AAAA,MACvB,OAAO;AAGL,cAAM,gBAAgB,oBAAoB;AAC1C,YAAI,iBAAiB,GAAG;AACtB,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB,GAAG;AACtB,iBAAO,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,EAAE;AAAA,QAClC;AACA,YAAI,gBAAgB,KAAK,gBAAgB,GAAG;AAC1C,iBAAO;AAAA,YACL,GAAG,aAAa,IAAI,iBAAiB,UAAU,IAAI,gBAAgB,MAAM,GAAG,CAAC;AAAA,YAC7E,GAAG,aAAa,IAAI,iBAAiB,UAAU,IAAI,gBAAgB,MAAM,GAAG,CAAC;AAAA,UAC/E;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,gBAAY;AAAA,EACd;AACA,QAAM,IAAI,MAAM,wDAAwD;AAC1E,GAhC8B;AAkC9B,IAAM,0BAA0B,wBAC9B,uBACA,QACA,oBACG;AACH,MAAI,KAAK,cAAc,KAAK,UAAU,MAAM,CAAC,EAAE;AAC/C,MAAI,OAAO,CAAC,MAAM,iBAAiB;AACjC,aAAS,OAAO,QAAQ;AAAA,EAC1B;AAEA,QAAM,6BAA6B;AACnC,QAAM,SAAS,eAAe,QAAQ,0BAA0B;AAEhE,QAAM,IAAI,wBAAwB,KAAK;AAEvC,QAAM,QAAQ,KAAK,MAAM,OAAO,CAAC,EAAE,IAAI,OAAO,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC;AACvE,QAAM,sBAAsB,EAAE,GAAG,GAAG,GAAG,EAAE;AAEzC,sBAAoB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AACzE,sBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AAC1E,SAAO;AACT,GArBgC;AA+BhC,SAAS,0BACP,oBACA,UACA,SACO;AACP,QAAM,SAAS,gBAAgB,OAAO;AACtC,MAAI,KAAK,cAAc,MAAM;AAC7B,MAAI,aAAa,gBAAgB,aAAa,eAAe;AAC3D,WAAO,QAAQ;AAAA,EACjB;AAGA,QAAM,6BAA6B,KAAK;AACxC,QAAM,SAAS,eAAe,QAAQ,0BAA0B;AAGhE,QAAM,IAAI,KAAK,qBAAqB;AAEpC,QAAM,QAAQ,KAAK,MAAM,OAAO,CAAC,EAAE,IAAI,OAAO,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC;AAEvE,QAAM,sBAA6B,EAAE,GAAG,GAAG,GAAG,EAAE;AAGhD,MAAI,aAAa,cAAc;AAC7B,wBAAoB,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AACnF,wBAAoB,IAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AAAA,EACtF,WAAW,aAAa,aAAa;AACnC,wBAAoB,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AACvF,wBAAoB,IAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AAAA,EAC1F,WAAW,aAAa,YAAY;AAClC,wBAAoB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AAC7E,wBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AAAA,EAChF,OAAO;AACL,wBAAoB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AACzE,wBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AAAA,EAC5E;AACA,SAAO;AACT;AArCS;AA6CF,SAAS,mBAAmB,KAAsD;AACvF,MAAI,QAAQ;AACZ,MAAI,aAAa;AAEjB,aAAW,WAAW,KAAK;AACzB,QAAI,YAAY,QAAW;AAEzB,UAAI,QAAQ,WAAW,QAAQ,KAAK,QAAQ,WAAW,aAAa,GAAG;AACrE,qBAAa,aAAa,UAAU;AAAA,MACtC,OAAO;AACL,gBAAQ,QAAQ,UAAU;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAEA,SAAO,EAAE,OAAO,WAAW;AAC7B;AAhBgB;AAkBhB,IAAI,MAAM;AACH,IAAM,aAAa,6BAAM;AAC9B;AACA,SAAO,QAAQ,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,EAAE,IAAI,MAAM;AAClE,GAH0B;AAW1B,SAAS,cAAc,QAAwB;AAC7C,MAAI,SAAS;AACb,QAAM,aAAa;AACnB,QAAM,mBAAmB,WAAW;AACpC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,WAAW,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,gBAAgB,CAAC;AAAA,EAC1E;AACA,SAAO;AACT;AARS;AAUF,IAAM,SAAS,wBAAC,YAAgC;AACrD,SAAO,cAAc,QAAQ,MAAM;AACrC,GAFsB;AAIf,IAAM,aAAa,kCAAY;AACpC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF,GAf0B;AAwBnB,IAAM,iBAAiB,gCAC5B,MACA,UAYgB;AAEhB,QAAM,QAAQ,SAAS,KAAK,QAAQ,eAAO,gBAAgB,GAAG;AAE9D,QAAM,CAAC,EAAE,WAAW,IAAI,cAAc,SAAS,QAAQ;AAEvD,QAAM,WAAW,KAAK,OAAO,MAAM;AACnC,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,MAAM,eAAe,SAAS,MAAM;AAC7C,WAAS,MAAM,eAAe,SAAS,UAAU;AACjD,WAAS,MAAM,aAAa,WAAW;AACvC,WAAS,MAAM,eAAe,SAAS,UAAU;AACjD,WAAS,KAAK,QAAQ,SAAS,IAAI;AAEnC,MAAI,SAAS,UAAU,QAAW;AAChC,aAAS,KAAK,SAAS,SAAS,KAAK;AAAA,EACvC;AAEA,QAAM,OAAO,SAAS,OAAO,OAAO;AACpC,OAAK,KAAK,KAAK,SAAS,IAAI,SAAS,aAAa,CAAC;AACnD,OAAK,KAAK,QAAQ,SAAS,IAAI;AAC/B,OAAK,KAAK,KAAK;AAEf,SAAO;AACT,GAvC8B;AAgDvB,IAAM,YACX;AAAA,EACE,CAAC,OAAe,UAAkB,WAAoC;AACpE,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,aAAS,OAAO;AAAA,MACd,EAAE,UAAU,IAAI,YAAY,KAAK,YAAY,SAAS,UAAU,QAAQ;AAAA,MACxE;AAAA,IACF;AACA,QAAI,eAAO,eAAe,KAAK,KAAK,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,MAAM,MAAM,GAAG,EAAE,OAAO,OAAO;AAC7C,UAAM,iBAA2B,CAAC;AAClC,QAAI,WAAW;AACf,UAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,YAAM,aAAa,mBAAmB,GAAG,IAAI,KAAK,MAAM;AACxD,YAAM,iBAAiB,mBAAmB,UAAU,MAAM;AAC1D,UAAI,aAAa,UAAU;AACzB,cAAM,EAAE,mBAAmB,cAAc,IAAI,YAAY,MAAM,UAAU,KAAK,MAAM;AACpF,uBAAe,KAAK,UAAU,GAAG,iBAAiB;AAClD,mBAAW;AAAA,MACb,WAAW,iBAAiB,cAAc,UAAU;AAClD,uBAAe,KAAK,QAAQ;AAC5B,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW,CAAC,UAAU,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,MACtD;AACA,YAAM,cAAc,QAAQ;AAC5B,YAAM,aAAa,gBAAgB,MAAM;AACzC,UAAI,YAAY;AACd,uBAAe,KAAK,QAAQ;AAAA,MAC9B;AAAA,IACF,CAAC;AACD,WAAO,eAAe,OAAO,CAAC,SAAS,SAAS,EAAE,EAAE,KAAK,OAAO,QAAQ;AAAA,EAC1E;AAAA,EACA,CAAC,OAAO,UAAU,WAChB,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,OAAO,QAAQ;AACnG;AAOF,IAAM,cAKmB;AAAA,EACvB,CACE,MACA,UACA,kBAAkB,KAClB,WACsB;AACtB,aAAS,OAAO;AAAA,MACd,EAAE,UAAU,IAAI,YAAY,KAAK,YAAY,SAAS,QAAQ,EAAE;AAAA,MAChE;AAAA,IACF;AACA,UAAM,aAAa,CAAC,GAAG,IAAI;AAC3B,UAAM,QAAkB,CAAC;AACzB,QAAI,cAAc;AAClB,eAAW,QAAQ,CAAC,WAAW,UAAU;AACvC,YAAM,WAAW,GAAG,WAAW,GAAG,SAAS;AAC3C,YAAM,YAAY,mBAAmB,UAAU,MAAM;AACrD,UAAI,aAAa,UAAU;AACzB,cAAM,mBAAmB,QAAQ;AACjC,cAAM,aAAa,WAAW,WAAW;AACzC,cAAM,qBAAqB,GAAG,QAAQ,GAAG,eAAe;AACxD,cAAM,KAAK,aAAa,WAAW,kBAAkB;AACrD,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,EAAE,mBAAmB,OAAO,eAAe,YAAY;AAAA,EAChE;AAAA,EACA,CAAC,MAAM,UAAU,kBAAkB,KAAK,WACtC,GAAG,IAAI,GAAG,QAAQ,GAAG,eAAe,GAAG,OAAO,QAAQ,GAAG,OAAO,UAAU,GAAG,OAAO,UAAU;AAClG;AAaO,SAAS,oBACd,MACA,QACsD;AACtD,SAAO,wBAAwB,MAAM,MAAM,EAAE;AAC/C;AALgB;AAeT,SAAS,mBACd,MACA,QACqD;AACrD,SAAO,wBAAwB,MAAM,MAAM,EAAE;AAC/C;AALgB;AAgBT,IAAM,0BAGS;AAAA,EACpB,CAAC,MAAc,WAAgD;AAC7D,UAAM,EAAE,WAAW,IAAI,aAAa,SAAS,aAAa,IAAI,IAAI;AAClE,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,IAC/B;AAEA,UAAM,CAAC,EAAE,WAAW,IAAI,cAAc,QAAQ;AAK9C,UAAM,eAAe,CAAC,cAAc,UAAU;AAC9C,UAAM,QAAQ,KAAK,MAAM,eAAO,cAAc;AAC9C,UAAM,OAAO,CAAC;AAEd,UAAM,OAAO,OAAO,MAAM;AAG1B,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO,EAAE,OAAO,GAAG,QAAQ,GAAG,YAAY,EAAE;AAAA,IAC9C;AAEA,UAAM,IAAI,KAAK,OAAO,KAAK;AAE3B,eAAWC,eAAc,cAAc;AACrC,UAAI,UAAU;AACd,YAAM,MAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,YAAY,EAAE;AACjD,iBAAW,QAAQ,OAAO;AACxB,cAAM,UAAU,WAAW;AAC3B,gBAAQ,OAAO,QAAQ;AAEvB,cAAM,WAAW,eAAe,GAAG,OAAO,EAEvC,MAAM,aAAa,WAAW,EAC9B,MAAM,eAAe,UAAU,EAC/B,MAAM,eAAeA,WAAU;AAElC,cAAM,QAAQ,SAAS,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,QAAQ;AAC1D,YAAI,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG;AACzC,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AACA,YAAI,QAAQ,KAAK,MAAM,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC;AACtD,kBAAU,KAAK,MAAM,KAAK,MAAM;AAChC,YAAI,UAAU;AACd,YAAI,aAAa,KAAK,MAAM,KAAK,IAAI,IAAI,YAAY,OAAO,CAAC;AAAA,MAC/D;AACA,WAAK,KAAK,GAAG;AAAA,IACf;AAEA,MAAE,OAAO;AAET,UAAM,QACJ,MAAM,KAAK,CAAC,EAAE,MAAM,KACpB,MAAM,KAAK,CAAC,EAAE,KAAK,KACnB,MAAM,KAAK,CAAC,EAAE,UAAU,KACvB,KAAK,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,UACxB,KAAK,CAAC,EAAE,QAAQ,KAAK,CAAC,EAAE,SACxB,KAAK,CAAC,EAAE,aAAa,KAAK,CAAC,EAAE,aAC3B,IACA;AACN,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,CAAC,MAAM,WAAW,GAAG,IAAI,GAAG,OAAO,QAAQ,GAAG,OAAO,UAAU,GAAG,OAAO,UAAU;AACrF;AAEO,IAAM,kBAAN,MAAsB;AAAA,EAG3B,YAAY,gBAAgB,OAAO,MAAe;AAFlD,SAAQ,QAAQ;AAKd,SAAK,QAAQ,OAAO,KAAK,SAAS;AAClC,SAAK,OAAO,gBAAgB,MAAM,KAAK,UAAU,MAAM,KAAK,IAAI;AAAA,EAClE;AAAA,EA1vBF,OAkvB6B;AAAA;AAAA;AAS7B;AAEA,IAAI;AAQG,IAAM,eAAe,gCAAU,MAAsB;AAC1D,YAAU,WAAW,SAAS,cAAc,KAAK;AAEjD,SAAO,OAAO,IAAI,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG;AACjF,UAAQ,YAAY;AAEpB,SAAO,SAAS,QAAQ,WAAY;AACtC,GAP4B;AAoBrB,SAAS,gBAAgB,OAAoC;AAClE,SAAO,SAAS;AAClB;AAFgB;AAoBT,IAAM,cAAc,wBACzB,QACA,UACA,gBACA,UACS;AACT,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,QAAM,SAAS,OAAO,KAAK,GAAG,QAAQ;AACtC,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,SACG,OAAO,MAAM,EACb,KAAK,KAAK,EACV,KAAK,eAAe,QAAQ,EAC5B,KAAK,KAAK,OAAO,IAAI,OAAO,QAAQ,CAAC,EACrC,KAAK,KAAK,CAAC,cAAc,EACzB,KAAK,SAAS,QAAQ;AAC3B,GApB2B;AA8BpB,IAAM,gBAAgB,wBAAC,aAA8D;AAE1F,MAAI,OAAO,aAAa,UAAU;AAChC,WAAO,CAAC,UAAU,WAAW,IAAI;AAAA,EACnC;AAEA,QAAM,iBAAiB,SAAS,YAAY,IAAI,EAAE;AAClD,MAAI,OAAO,MAAM,cAAc,GAAG;AAEhC,WAAO,CAAC,QAAW,MAAS;AAAA,EAC9B,WAAW,aAAa,OAAO,cAAc,GAAG;AAE9C,WAAO,CAAC,gBAAgB,WAAW,IAAI;AAAA,EACzC,OAAO;AACL,WAAO,CAAC,gBAAgB,QAAQ;AAAA,EAClC;AACF,GAhB6B;AAkBtB,SAAS,cAAiB,aAAgB,MAAsB;AACrE,SAAO,MAAM,CAAC,GAAG,aAAa,IAAI;AACpC;AAFgB;AAIhB,IAAO,gBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAMO,IAAM,iBAAiB,gCAAU,MAAsB;AAC5D,MAAI,MAAM;AAEV,QAAM,IAAI,QAAQ,oBAAoB,SAAU,GAAW;AACzD,WAAO,EAAE,UAAU,GAAG,EAAE,SAAS,CAAC;AAAA,EACpC,CAAC;AACD,QAAM,IAAI,QAAQ,uBAAuB,SAAU,GAAW;AAC5D,WAAO,EAAE,UAAU,GAAG,EAAE,SAAS,CAAC;AAAA,EACpC,CAAC;AAED,QAAM,IAAI,QAAQ,UAAU,SAAU,GAAG;AACvC,UAAM,WAAW,EAAE,UAAU,GAAG,EAAE,SAAS,CAAC;AAE5C,UAAM,QAAQ,WAAW,KAAK,QAAQ;AACtC,QAAI,OAAO;AACT,aAAO,mBAAQ,WAAW;AAAA,IAC5B,OAAO;AACL,aAAO,eAAO,WAAW;AAAA,IAC3B;AAAA,EACF,CAAC;AAED,SAAO;AACT,GAtB8B;AA6BvB,IAAM,iBAAiB,gCAAU,MAAsB;AAC5D,SAAO,KAAK,QAAQ,QAAQ,IAAI,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC1E,GAF8B;AAQvB,IAAM,YAAY,wBACvB,MACA,IACA;AAAA,EACE,UAAU;AAAA,EACV;AAAA,EACA;AACF,GAKA,OACG;AACH,MAAI,IAAI;AACN,WAAO;AAAA,EACT;AACA,SAAO,GAAG,SAAS,GAAG,MAAM,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,IAAI,OAAO,GAAG,SAAS,IAAI,MAAM,KAAK,EAAE;AAC3F,GAlByB;AA0BlB,SAAS,oBACd,WACA;AACA,SAAO,aAAa;AACtB;AAJgB;", "names": ["type", "fontFamily"]}