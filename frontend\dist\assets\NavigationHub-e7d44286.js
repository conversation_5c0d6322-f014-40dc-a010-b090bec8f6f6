import{_,S as b,i as p,j as m,k as h,n as s,p as t,w as d,M as o,q as u,N as i,D as c,a8 as k,P as v,a9 as y,a5 as I,aa as A,Q as C,ab as $,m as N,G as w,f as x,ac as S,t as z,W as F}from"./index-b6a2842e.js";const V={class:"navigation-hub"},B={class:"hub-container"},H={class:"hub-header"},T={class:"hub-title"},j={class:"navigation-grid"},q={class:"card-header"},D={class:"card-icon enterprise-icon"},E={class:"card-footer"},G={class:"card-header"},M={class:"card-icon candidate-icon"},P={class:"card-footer"},Q={class:"card-header"},R={class:"card-icon dashboard-icon"},W={class:"card-footer"},J={class:"card-header"},K={class:"card-icon configurator-icon"},L={class:"card-footer"},O={class:"legacy-section"},U={class:"legacy-grid"},X={class:"design-highlights"},Y={class:"highlights-grid"},Z={class:"highlight-item"},aa={class:"highlight-icon"},sa={class:"highlight-item"},ta={class:"highlight-icon"},da={class:"highlight-item"},la={class:"highlight-icon"},ia={class:"highlight-item"},na={class:"highlight-icon"},ea={__name:"NavigationHub",setup(oa){const g=b(),n=f=>{g.push(f)};return(f,a)=>{const l=p("el-icon"),r=p("el-button");return m(),h("div",V,[s("div",B,[s("div",H,[s("h1",T,[t(l,null,{default:d(()=>[t(i(c))]),_:1}),a[9]||(a[9]=o(" iFlytek Spark AI面试系统导航中心 "))]),a[10]||(a[10]=s("p",{class:"hub-subtitle"},"基于用友大易平台设计理念的企业级组件展示",-1))]),s("div",j,[s("div",{class:"nav-card enterprise-card",onClick:a[0]||(a[0]=e=>n("/enterprise-home"))},[s("div",q,[s("div",D,[t(l,{size:32},{default:d(()=>[t(i(k))]),_:1})]),a[11]||(a[11]=s("div",{class:"card-badge"},"企业端",-1))]),a[13]||(a[13]=u('<div class="card-content" data-v-0b295fa7><h3 class="card-title" data-v-0b295fa7>企业级主页</h3><p class="card-description" data-v-0b295fa7> 借鉴大易平台的渐变背景和卡片式布局，突出iFlytek Spark AI技术优势 </p><div class="card-features" data-v-0b295fa7><span class="feature-tag" data-v-0b295fa7>渐变背景</span><span class="feature-tag" data-v-0b295fa7>浮动动画</span><span class="feature-tag" data-v-0b295fa7>技术展示</span></div></div>',1)),s("div",E,[t(r,{type:"primary",class:"nav-button"},{default:d(()=>[a[12]||(a[12]=o(" 访问企业主页 ")),t(l,null,{default:d(()=>[t(i(v))]),_:1})]),_:1,__:[12]})])]),s("div",{class:"nav-card candidate-card",onClick:a[1]||(a[1]=e=>n("/candidate-portal"))},[s("div",G,[s("div",M,[t(l,{size:32},{default:d(()=>[t(i(y))]),_:1})]),a[14]||(a[14]=s("div",{class:"card-badge"},"候选人端",-1))]),a[16]||(a[16]=u('<div class="card-content" data-v-0b295fa7><h3 class="card-title" data-v-0b295fa7>候选人专属门户</h3><p class="card-description" data-v-0b295fa7> 差异化的候选人体验设计，AI智能建议和个性化学习路径 </p><div class="card-features" data-v-0b295fa7><span class="feature-tag" data-v-0b295fa7>AI建议</span><span class="feature-tag" data-v-0b295fa7>学习路径</span><span class="feature-tag" data-v-0b295fa7>进度跟踪</span></div></div>',1)),s("div",P,[t(r,{type:"success",class:"nav-button"},{default:d(()=>[a[15]||(a[15]=o(" 进入候选人门户 ")),t(l,null,{default:d(()=>[t(i(v))]),_:1})]),_:1,__:[15]})])]),s("div",{class:"nav-card dashboard-card",onClick:a[2]||(a[2]=e=>n("/intelligent-dashboard"))},[s("div",Q,[s("div",R,[t(l,{size:32},{default:d(()=>[t(i(c))]),_:1})]),a[17]||(a[17]=s("div",{class:"card-badge"},"数据分析",-1))]),a[19]||(a[19]=u('<div class="card-content" data-v-0b295fa7><h3 class="card-title" data-v-0b295fa7>智能数据仪表板</h3><p class="card-description" data-v-0b295fa7> 专业级数据可视化展示，实时指标监控和AI洞察建议 </p><div class="card-features" data-v-0b295fa7><span class="feature-tag" data-v-0b295fa7>实时监控</span><span class="feature-tag" data-v-0b295fa7>数据可视化</span><span class="feature-tag" data-v-0b295fa7>AI洞察</span></div></div>',1)),s("div",W,[t(r,{type:"warning",class:"nav-button"},{default:d(()=>[a[18]||(a[18]=o(" 查看数据仪表板 ")),t(l,null,{default:d(()=>[t(i(v))]),_:1})]),_:1,__:[18]})])]),s("div",{class:"nav-card configurator-card",onClick:a[3]||(a[3]=e=>n("/ai-configurator"))},[s("div",J,[s("div",K,[t(l,{size:32},{default:d(()=>[t(i(I))]),_:1})]),a[20]||(a[20]=s("div",{class:"card-badge"},"AI配置",-1))]),a[22]||(a[22]=u('<div class="card-content" data-v-0b295fa7><h3 class="card-title" data-v-0b295fa7>AI面试配置器</h3><p class="card-description" data-v-0b295fa7> 4步式专业配置向导，多模态AI能力配置和Spark模型调优 </p><div class="card-features" data-v-0b295fa7><span class="feature-tag" data-v-0b295fa7>配置向导</span><span class="feature-tag" data-v-0b295fa7>多模态AI</span><span class="feature-tag" data-v-0b295fa7>模型调优</span></div></div>',1)),s("div",L,[t(r,{type:"info",class:"nav-button"},{default:d(()=>[a[21]||(a[21]=o(" 打开AI配置器 ")),t(l,null,{default:d(()=>[t(i(v))]),_:1})]),_:1,__:[21]})])])]),s("div",O,[a[28]||(a[28]=s("h2",{class:"section-title"},"原有系统快速访问",-1)),s("div",U,[s("div",{class:"legacy-item",onClick:a[4]||(a[4]=e=>n("/"))},[t(l,null,{default:d(()=>[t(i(A))]),_:1}),a[23]||(a[23]=s("span",null,"系统首页",-1))]),s("div",{class:"legacy-item",onClick:a[5]||(a[5]=e=>n("/demo"))},[t(l,null,{default:d(()=>[t(i(C))]),_:1}),a[24]||(a[24]=s("span",null,"演示页面",-1))]),s("div",{class:"legacy-item",onClick:a[6]||(a[6]=e=>n("/interview-selection"))},[t(l,null,{default:d(()=>[t(i($))]),_:1}),a[25]||(a[25]=s("span",null,"面试选择",-1))]),s("div",{class:"legacy-item",onClick:a[7]||(a[7]=e=>n("/interviewing"))},[t(l,null,{default:d(()=>[t(i(N))]),_:1}),a[26]||(a[26]=s("span",null,"面试页面",-1))]),s("div",{class:"legacy-item",onClick:a[8]||(a[8]=e=>n("/reports"))},[t(l,null,{default:d(()=>[t(i(w))]),_:1}),a[27]||(a[27]=s("span",null,"报告页面",-1))])])]),s("div",X,[a[37]||(a[37]=s("h2",{class:"section-title"},"设计特色亮点",-1)),s("div",Y,[s("div",Z,[s("div",aa,[t(l,null,{default:d(()=>[t(i(x))]),_:1})]),a[29]||(a[29]=s("h4",null,"iFlytek Spark AI技术",-1)),a[30]||(a[30]=s("p",null,"突出讯飞星火大模型的技术优势和多模态分析能力",-1))]),s("div",sa,[s("div",ta,[t(l,null,{default:d(()=>[t(i(S))]),_:1})]),a[31]||(a[31]=s("h4",null,"大易平台设计理念",-1)),a[32]||(a[32]=s("p",null,"借鉴渐变背景、卡片布局、现代交互等优秀设计元素",-1))]),s("div",da,[s("div",la,[t(l,null,{default:d(()=>[t(i(z))]),_:1})]),a[33]||(a[33]=s("h4",null,"企业级用户体验",-1)),a[34]||(a[34]=s("p",null,"专业的数据可视化和差异化的用户界面设计",-1))]),s("div",ia,[s("div",na,[t(l,null,{default:d(()=>[t(i(F))]),_:1})]),a[35]||(a[35]=s("h4",null,"品牌一致性",-1)),a[36]||(a[36]=s("p",null,"保持iFlytek品牌色彩和中文本地化标准",-1))])])])])])}}},ua=_(ea,[["__scopeId","data-v-0b295fa7"]]);export{ua as default};
