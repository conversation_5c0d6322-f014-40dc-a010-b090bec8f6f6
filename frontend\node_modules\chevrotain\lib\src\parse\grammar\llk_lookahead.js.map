{"version": 3, "file": "llk_lookahead.js", "sourceRoot": "", "sources": ["../../../../src/parse/grammar/llk_lookahead.ts"], "names": [], "mappings": "AAQA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAC7C,OAAO,EAAE,oCAAoC,EAAE,MAAM,qBAAqB,CAAC;AAC3E,OAAO,EAAE,qBAAqB,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,EACL,wCAAwC,EACxC,0BAA0B,EAC1B,uBAAuB,EACvB,iCAAiC,GAClC,MAAM,aAAa,CAAC;AACrB,OAAO,EACL,8BAA8B,EAC9B,iCAAiC,EACjC,uBAAuB,EACvB,uCAAuC,EACvC,WAAW,GACZ,MAAM,gBAAgB,CAAC;AAGxB,MAAM,OAAO,oBAAoB;IAG/B,YAAY,OAAmC;;QAC7C,IAAI,CAAC,YAAY;YACf,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,mCAAI,qBAAqB,CAAC,YAAY,CAAC;IAChE,CAAC;IAED,QAAQ,CAAC,OAIR;QACC,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAExE,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE;YAChC,MAAM,cAAc,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACvE,MAAM,mBAAmB,GAAG,IAAI,CAAC,wCAAwC,CACvE,OAAO,CAAC,KAAK,EACb,IAAI,CAAC,YAAY,CAClB,CAAC;YACF,MAAM,qBAAqB,GAAG,IAAI,CAAC,iCAAiC,CAClE,OAAO,CAAC,KAAK,EACb,IAAI,CAAC,YAAY,CAClB,CAAC;YACF,MAAM,SAAS,GAAG;gBAChB,GAAG,mBAAmB;gBACtB,GAAG,cAAc;gBACjB,GAAG,mBAAmB;gBACtB,GAAG,qBAAqB;aACzB,CAAC;YACF,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,uBAAuB,CAAC,KAAa;QACnC,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,EAAE,CACpC,uBAAuB,CACrB,WAAW,EACX,WAAW,EACX,oCAAoC,CACrC,CACF,CAAC;IACJ,CAAC;IAED,2BAA2B,CAAC,KAAa;QACvC,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,EAAE,CACpC,0BAA0B,CACxB,WAAW,EACX,oCAAoC,CACrC,CACF,CAAC;IACJ,CAAC;IAED,wCAAwC,CACtC,KAAa,EACb,YAAoB;QAEpB,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,EAAE,CACpC,wCAAwC,CACtC,WAAW,EACX,YAAY,EACZ,oCAAoC,CACrC,CACF,CAAC;IACJ,CAAC;IAED,iCAAiC,CAC/B,KAAa,EACb,YAAoB;QAEpB,OAAO,iCAAiC,CACtC,KAAK,EACL,YAAY,EACZ,oCAAoC,CACrC,CAAC;IACJ,CAAC;IAED,4BAA4B,CAAC,OAM5B;QACC,OAAO,uBAAuB,CAC5B,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,oBAAoB,EAC5B,8BAA8B,CAC/B,CAAC;IACJ,CAAC;IAED,yBAAyB,CAAC,OAMzB;QACC,OAAO,iCAAiC,CACtC,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,oBAAoB,EAC5B,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAC7B,uCAAuC,CACxC,CAAC;IACJ,CAAC;CACF"}