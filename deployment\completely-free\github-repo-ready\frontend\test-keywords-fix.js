/**
 * 测试关键词字段修复
 */

// 模拟iFlytek服务的关键词提取
function extractEnhancedKeywords(text, domain = 'comprehensive') {
  const domainKeywords = {
    'ai': ['算法', '机器学习', '深度学习', '神经网络', '人工智能'],
    'bigdata': ['大数据', '数据分析', '数据挖掘', '数据处理', 'Hadoop'],
    'iot': ['物联网', '传感器', '嵌入式', '边缘计算', '智能设备']
  }
  
  const keywords = domainKeywords[domain] || []
  const commonKeywords = ['技术', '项目', '经验', '解决方案', '优化', '创新']
  const allKeywords = [...keywords, ...commonKeywords]
  
  return allKeywords.filter(keyword => text.includes(keyword))
}

// 模拟分析结果
function getEnhancedFallbackAnalysis(inputData) {
  const textContent = inputData?.text || ''
  const domain = inputData?.domain || 'comprehensive'
  const keywords = extractEnhancedKeywords(textContent, domain)
  
  return {
    overallScore: 75,
    textAnalysis: {
      keywords: keywords, // 确保这是一个数组
      sentiment: 'positive',
      complexity: 'detailed'
    }
  }
}

// 测试用例
console.log('🧪 测试关键词字段修复')
console.log('='.repeat(50))

const testCases = [
  {
    text: '我有机器学习和深度学习的项目经验',
    domain: 'ai'
  },
  {
    text: '我了解大数据处理和数据分析技术',
    domain: 'bigdata'
  },
  {
    text: '不知道',
    domain: 'ai'
  },
  {
    text: '',
    domain: 'comprehensive'
  }
]

testCases.forEach((testCase, index) => {
  console.log(`\n测试用例 ${index + 1}:`)
  console.log(`输入: "${testCase.text}"`)
  console.log(`领域: ${testCase.domain}`)
  
  const analysis = getEnhancedFallbackAnalysis(testCase)
  const keywords = analysis.textAnalysis?.keywords || []
  
  console.log(`关键词类型: ${Array.isArray(keywords) ? '数组' : typeof keywords}`)
  console.log(`关键词内容: [${keywords.join(', ')}]`)
  console.log(`模板渲染测试: "${(keywords || []).join(', ') || '无'}"`)
  
  // 测试模板中的表达式
  try {
    const templateResult = (analysis.textAnalysis?.keywords || []).join(', ') || '无'
    console.log(`✅ 模板表达式正常: "${templateResult}"`)
  } catch (error) {
    console.log(`❌ 模板表达式错误: ${error.message}`)
  }
})

console.log('\n✨ 测试完成！')
console.log('\n📋 修复总结：')
console.log('- 在模板中使用 (message.analysis.keywords || []).join(\', \') || \'无\'')
console.log('- 确保即使 keywords 为 undefined 也能正常渲染')
console.log('- 提供默认值 "无" 当没有关键词时')
