'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../../utils/vue/props/runtime.js');

const disabledTimeListsProps = runtime.buildProps({
  disabledHours: {
    type: runtime.definePropType(Function)
  },
  disabledMinutes: {
    type: runtime.definePropType(Function)
  },
  disabledSeconds: {
    type: runtime.definePropType(Function)
  }
});
const timePanelSharedProps = runtime.buildProps({
  visible: Boolean,
  actualVisible: {
    type: Boolean,
    default: void 0
  },
  format: {
    type: String,
    default: ""
  }
});

exports.disabledTimeListsProps = disabledTimeListsProps;
exports.timePanelSharedProps = timePanelSharedProps;
//# sourceMappingURL=shared.js.map
