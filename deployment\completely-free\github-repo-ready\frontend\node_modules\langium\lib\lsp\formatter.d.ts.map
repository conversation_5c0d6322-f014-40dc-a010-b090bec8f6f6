{"version": 3, "file": "formatter.d.ts", "sourceRoot": "", "sources": ["../../src/lsp/formatter.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EAAE,wBAAwB,EAAE,+BAA+B,EAAE,8BAA8B,EAAE,6BAA6B,EAAE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AACnN,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AACtE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAO/E;;GAEG;AACH,MAAM,WAAW,SAAS;IACtB;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,wBAAwB,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAA;IACtI;;OAEG;IACH,mBAAmB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,6BAA6B,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAA;IAChJ;;OAEG;IACH,oBAAoB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,8BAA8B,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAA;IAClJ;;;OAGG;IACH,IAAI,mBAAmB,IAAI,+BAA+B,GAAG,SAAS,CAAA;CACzE;AAED,8BAAsB,iBAAkB,YAAW,SAAS;IAExD,SAAS,CAAC,SAAS,EAAE,mBAAmB,CAAyC;IAEjF;;;;;;;;;;;;;;;;;OAiBG;IACH,SAAS,CAAC,gBAAgB,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;IAIxE,cAAc,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,wBAAwB,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;IAWrG;;;;;;OAMG;IACH,SAAS,CAAC,sBAAsB,CAAC,QAAQ,EAAE,eAAe,EAAE,KAAK,EAAE,KAAK,GAAG,OAAO;IAgBlF,mBAAmB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,6BAA6B,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;IAQ/G,oBAAoB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,8BAA8B,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;IAiBjH,IAAI,mBAAmB,IAAI,+BAA+B,GAAG,SAAS,CAErE;IAED,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,iBAAiB,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,QAAQ,EAAE;IAmB5G,SAAS,CAAC,qBAAqB,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,QAAQ,EAAE;IAoB9F,SAAS,CAAC,oBAAoB,CAAC,QAAQ,EAAE,eAAe,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI;IAmB9E,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAE9C,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,QAAQ,GAAG,MAAM;IAI1E,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,OAAO;IAY5D,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,GAAG,OAAO;IAItE,SAAS,CAAC,oBAAoB,CAAC,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,QAAQ,EAAE;IA2D5J,SAAS,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,GAAG,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,GAAG,SAAS,EAAE,OAAO,EAAE,iBAAiB,GAAG,QAAQ,EAAE;IA2EjK,SAAS,CAAC,oCAAoC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,GAAG,MAAM;IAMhG,SAAS,CAAC,4BAA4B,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAAE,cAAc,GAAG,MAAM;IAQ3G,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,GAAG,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAAE,iBAAiB,GAAG,QAAQ,EAAE;IA0ClI,SAAS,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,uBAAuB,GAAG,QAAQ;IAYvG,SAAS,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,uBAAuB,GAAG,QAAQ;IAYjI,SAAS,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,GAAG,QAAQ;IAYrG,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,uBAAuB,GAAG,MAAM;IASnG,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,QAAQ,EAAE,iBAAiB,GAAG,cAAc,GAAG,SAAS;IAqBzH,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC;IAShG,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC;CAmBnF;AAED;;GAEG;AACH,MAAM,WAAW,aAAa,CAAC,CAAC,SAAS,OAAO;IAC5C;;OAEG;IACH,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,gBAAgB,CAAA;IACrC;;OAEG;IACH,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAA;IAC5C;;;;;OAKG;IACH,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,gBAAgB,CAAA;IACnE;;;;OAIG;IACH,UAAU,CAAC,GAAG,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAA;IACjE;;;;;OAKG;IACH,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,gBAAgB,CAAA;IAC1D;;;;OAIG;IACH,QAAQ,CAAC,GAAG,QAAQ,EAAE,MAAM,EAAE,GAAG,gBAAgB,CAAA;IACjD;;;;OAIG;IACH,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAA;IACvC;;;;;;;;;;;;;;OAcG;IACH,QAAQ,CAAC,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,gBAAgB,GAAG,gBAAgB,CAAA;CAC7E;AAED,qBAAa,oBAAoB,CAAC,CAAC,SAAS,OAAO,CAAE,YAAW,aAAa,CAAC,CAAC,CAAC;IAE5E,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9B,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,mBAAmB,CAAC;gBAEtC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,mBAAmB;IAKtD,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,gBAAgB;IAIrC,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,GAAG,gBAAgB;IAU5C,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,gBAAgB;IAKlE,UAAU,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB;IAS/D,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,gBAAgB;IAK1D,QAAQ,CAAC,GAAG,QAAQ,EAAE,MAAM,EAAE,GAAG,gBAAgB;IASjD,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,gBAAgB;IAIvC,QAAQ,CAAC,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,gBAAgB,GAAG,gBAAgB;CAiB7E;AAED,MAAM,WAAW,iBAAiB;IAC9B,QAAQ,EAAE,YAAY,CAAA;IACtB,OAAO,EAAE,iBAAiB,CAAA;IAC1B,WAAW,EAAE,MAAM,CAAA;CACtB;AAED,qBAAa,gBAAgB;IAEzB,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;IAC1B,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,mBAAmB,CAAC;gBAEtC,KAAK,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,mBAAmB;IAK5D;;OAEG;IACH,OAAO,CAAC,UAAU,EAAE,gBAAgB,GAAG,gBAAgB;IAOvD;;OAEG;IACH,MAAM,CAAC,UAAU,EAAE,gBAAgB,GAAG,gBAAgB;IAOtD;;;OAGG;IACH,QAAQ,CAAC,UAAU,EAAE,gBAAgB,GAAG,gBAAgB;IAQxD;;;;;;OAMG;IACH,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,gBAAgB;CAGxD;AAED,MAAM,WAAW,gBAAgB;IAC7B,OAAO,EAAE,uBAAuB,CAAA;IAChC,KAAK,EAAE,cAAc,EAAE,CAAA;CAC1B;AAED,MAAM,WAAW,uBAAuB;IACpC;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAA;CACtB;AAED,MAAM,WAAW,cAAc;IAC3B,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,KAAK,CAAC,EAAE,MAAM,CAAA;CACjB;AAED;;GAEG;AACH,yBAAiB,UAAU,CAAC;IAExB;;;OAGG;IACH,SAAgB,GAAG,CAAC,GAAG,WAAW,EAAE,gBAAgB,EAAE,GAAG,gBAAgB,CAKxE;IAED;;OAEG;IACH,SAAgB,OAAO,CAAC,OAAO,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,CAE3E;IAED;;OAEG;IACH,SAAgB,QAAQ,CAAC,OAAO,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,CAE5E;IAED;;;;OAIG;IACH,SAAgB,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,CAOzF;IAED;;OAEG;IACH,SAAgB,OAAO,CAAC,OAAO,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,CAE3E;IAED;;OAEG;IACH,SAAgB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,CAO3F;IAED;;OAEG;IACH,SAAgB,MAAM,CAAC,OAAO,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,CAQ1E;IAED;;OAEG;IACH,SAAgB,QAAQ,CAAC,OAAO,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,CAO5E;CAyBJ;AAED,MAAM,MAAM,mBAAmB,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,QAAQ,EAAE,UAAU,EAAE,gBAAgB,KAAK,IAAI,CAAC"}