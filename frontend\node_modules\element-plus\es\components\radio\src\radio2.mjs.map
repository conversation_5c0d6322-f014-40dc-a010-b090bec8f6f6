{"version": 3, "file": "radio2.mjs", "sources": ["../../../../../../packages/components/radio/src/radio.vue"], "sourcesContent": ["<template>\n  <label\n    :class=\"[\n      ns.b(),\n      ns.is('disabled', disabled),\n      ns.is('focus', focus),\n      ns.is('bordered', border),\n      ns.is('checked', modelValue === actualValue),\n      ns.m(size),\n    ]\"\n  >\n    <span\n      :class=\"[\n        ns.e('input'),\n        ns.is('disabled', disabled),\n        ns.is('checked', modelValue === actualValue),\n      ]\"\n    >\n      <input\n        ref=\"radioRef\"\n        v-model=\"modelValue\"\n        :class=\"ns.e('original')\"\n        :value=\"actualValue\"\n        :name=\"name || radioGroup?.name\"\n        :disabled=\"disabled\"\n        :checked=\"modelValue === actualValue\"\n        type=\"radio\"\n        @focus=\"focus = true\"\n        @blur=\"focus = false\"\n        @change=\"handleChange\"\n        @click.stop\n      />\n      <span :class=\"ns.e('inner')\" />\n    </span>\n    <span :class=\"ns.e('label')\" @keydown.stop>\n      <slot>\n        {{ label }}\n      </slot>\n    </span>\n  </label>\n</template>\n\n<script lang=\"ts\" setup>\nimport { nextTick } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { radioEmits, radioProps } from './radio'\nimport { useRadio } from './use-radio'\n\ndefineOptions({\n  name: 'ElRadio',\n})\n\nconst props = defineProps(radioProps)\nconst emit = defineEmits(radioEmits)\n\nconst ns = useNamespace('radio')\nconst { radioRef, radioGroup, focus, size, disabled, modelValue, actualValue } =\n  useRadio(props, emit)\n\nfunction handleChange() {\n  nextTick(() => emit(CHANGE_EVENT, modelValue.value))\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;mCAiDc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAM,MAAA,EAAE,QAAU,EAAA,UAAA,EAAY,KAAO,EAAA,IAAA,EAAM,QAAU,EAAA,UAAA,EAAY,WAAY,EAAA,GAC3E,QAAS,CAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AAEtB,IAAA,SAAS,YAAe,GAAA;AACtB,MAAA,QAAA,CAAS,MAAM,IAAA,CAAK,YAAc,EAAA,UAAA,CAAW,KAAK,CAAC,CAAA,CAAA;AAAA,KACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}