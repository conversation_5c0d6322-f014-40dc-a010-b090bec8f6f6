{"timestamp": "2025-07-06T15:14:56.308Z", "score": 100, "status": "🎉 优秀 - 系统完全就绪", "summary": {"total": 14, "passed": 14, "failed": 0, "components": {"total": 4, "passed": 4}, "images": {"total": 5, "passed": 5}, "videos": {"total": 5, "passed": 5}, "details": [{"path": "src/components/Demo/iFlytek SparkShowcase.vue", "name": "iFlytek Spark演示组件", "type": "component", "exists": true, "size": 19560}, {"path": "src/components/Demo/ResponsiveMediaViewer.vue", "name": "响应式媒体查看器", "type": "component", "exists": true, "size": 10867}, {"path": "src/utils/mediaIntegrationTest.js", "name": "媒体集成测试工具", "type": "utility", "exists": true, "size": 12166}, {"path": "src/views/DemoPage.vue", "name": "演示页面", "type": "view", "exists": true, "size": 38176}, {"path": "public/generated-images/interface-complete-system.png", "name": "系统主界面", "type": "image", "exists": true, "size": 819200}, {"path": "public/generated-images/interface-ai-architecture.png", "name": "AI技术架构", "type": "image", "exists": true, "size": 1024000}, {"path": "public/generated-images/interface-case-analysis.png", "name": "案例分析界面", "type": "image", "exists": true, "size": 1228800}, {"path": "public/generated-images/interface-bigdata-analysis.png", "name": "大数据分析", "type": "image", "exists": true, "size": 1433600}, {"path": "public/generated-images/interface-iot-systems.png", "name": "IoT物联网", "type": "image", "exists": true, "size": 1638400}, {"path": "public/generated-videos/demo-complete.mp4", "name": "系统完整演示", "type": "video", "exists": true, "size": 14930064}, {"path": "public/generated-videos/demo-ai-tech.mp4", "name": "AI技术解析", "type": "video", "exists": true, "size": 11197584}, {"path": "public/generated-videos/demo-cases.mp4", "name": "案例分析", "type": "video", "exists": true, "size": 9331344}, {"path": "public/generated-videos/demo-bigdata.mp4", "name": "大数据专题", "type": "video", "exists": true, "size": 13063824}, {"path": "public/generated-videos/demo-iot.mp4", "name": "IoT物联网专题", "type": "video", "exists": true, "size": 11197584}]}, "details": [{"path": "src/components/Demo/iFlytek SparkShowcase.vue", "name": "iFlytek Spark演示组件", "type": "component", "exists": true, "size": 19560}, {"path": "src/components/Demo/ResponsiveMediaViewer.vue", "name": "响应式媒体查看器", "type": "component", "exists": true, "size": 10867}, {"path": "src/utils/mediaIntegrationTest.js", "name": "媒体集成测试工具", "type": "utility", "exists": true, "size": 12166}, {"path": "src/views/DemoPage.vue", "name": "演示页面", "type": "view", "exists": true, "size": 38176}, {"path": "public/generated-images/interface-complete-system.png", "name": "系统主界面", "type": "image", "exists": true, "size": 819200}, {"path": "public/generated-images/interface-ai-architecture.png", "name": "AI技术架构", "type": "image", "exists": true, "size": 1024000}, {"path": "public/generated-images/interface-case-analysis.png", "name": "案例分析界面", "type": "image", "exists": true, "size": 1228800}, {"path": "public/generated-images/interface-bigdata-analysis.png", "name": "大数据分析", "type": "image", "exists": true, "size": 1433600}, {"path": "public/generated-images/interface-iot-systems.png", "name": "IoT物联网", "type": "image", "exists": true, "size": 1638400}, {"path": "public/generated-videos/demo-complete.mp4", "name": "系统完整演示", "type": "video", "exists": true, "size": 14930064}, {"path": "public/generated-videos/demo-ai-tech.mp4", "name": "AI技术解析", "type": "video", "exists": true, "size": 11197584}, {"path": "public/generated-videos/demo-cases.mp4", "name": "案例分析", "type": "video", "exists": true, "size": 9331344}, {"path": "public/generated-videos/demo-bigdata.mp4", "name": "大数据专题", "type": "video", "exists": true, "size": 13063824}, {"path": "public/generated-videos/demo-iot.mp4", "name": "IoT物联网专题", "type": "video", "exists": true, "size": 11197584}]}