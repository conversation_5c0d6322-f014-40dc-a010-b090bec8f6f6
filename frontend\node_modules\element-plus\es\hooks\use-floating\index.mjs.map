{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-floating/index.ts"], "sourcesContent": ["import { isRef, onMounted, ref, unref, watchEffect } from 'vue'\nimport { unrefElement } from '@vueuse/core'\nimport { isNil } from 'lodash-unified'\nimport { arrow as arrowCore, computePosition } from '@floating-ui/dom'\nimport { buildProps, isClient, keysOf } from '@element-plus/utils'\n\nimport type { Ref, ToRefs } from 'vue'\nimport type {\n  ComputePositionReturn,\n  Middleware,\n  Placement,\n  SideObject,\n  Strategy,\n  VirtualElement,\n} from '@floating-ui/dom'\n\nexport const useFloatingProps = buildProps({} as const)\n\nexport type UseFloatingProps = ToRefs<{\n  middleware: Array<Middleware>\n  placement: Placement\n  strategy: Strategy\n}>\n\ntype ElementRef = Parameters<typeof unrefElement>['0']\n\nconst unrefReference = (\n  elRef: ElementRef | Ref<VirtualElement | undefined>\n) => {\n  if (!isClient) return\n  if (!elRef) return elRef\n  const unrefEl = unrefElement(elRef as ElementRef)\n  if (unrefEl) return unrefEl\n  return isRef(elRef) ? unrefEl : (elRef as VirtualElement)\n}\n\nexport const getPositionDataWithUnit = <T extends Record<string, number>>(\n  record: T | undefined,\n  key: keyof T\n) => {\n  const value = record?.[key]\n  return isNil(value) ? '' : `${value}px`\n}\n\nexport const useFloating = ({\n  middleware,\n  placement,\n  strategy,\n}: UseFloatingProps) => {\n  const referenceRef = ref<HTMLElement | VirtualElement>()\n  const contentRef = ref<HTMLElement>()\n  const x = ref<number>()\n  const y = ref<number>()\n  const middlewareData = ref<ComputePositionReturn['middlewareData']>({})\n\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData,\n  } as const\n\n  const update = async () => {\n    if (!isClient) return\n\n    const referenceEl = unrefReference(referenceRef)\n    const contentEl = unrefElement(contentRef)\n    if (!referenceEl || !contentEl) return\n\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware),\n    })\n\n    keysOf(states).forEach((key) => {\n      states[key].value = data[key]\n    })\n  }\n\n  onMounted(() => {\n    watchEffect(() => {\n      update()\n    })\n  })\n\n  return {\n    ...states,\n    update,\n    referenceRef,\n    contentRef,\n  }\n}\n\nexport type ArrowMiddlewareProps = {\n  arrowRef: Ref<HTMLElement | null | undefined>\n  padding?: number | SideObject\n}\n\nexport const arrowMiddleware = ({\n  arrowRef,\n  padding,\n}: ArrowMiddlewareProps): Middleware => {\n  return {\n    name: 'arrow',\n    options: {\n      element: arrowRef,\n      padding,\n    },\n\n    fn(args) {\n      const arrowEl = unref(arrowRef)\n      if (!arrowEl) return {}\n\n      return arrowCore({\n        element: arrowEl,\n        padding,\n      }).fn(args)\n    },\n  }\n}\n"], "names": ["arrowCore"], "mappings": ";;;;;;;AAKY,MAAC,gBAAgB,GAAG,UAAU,CAAC,EAAE,EAAE;AAC/C,MAAM,cAAc,GAAG,CAAC,KAAK,KAAK;AAClC,EAAE,IAAI,CAAC,QAAQ;AACf,IAAI,OAAO;AACX,EAAE,IAAI,CAAC,KAAK;AACZ,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO;AACb,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC;AACxC,CAAC,CAAC;AACU,MAAC,uBAAuB,GAAG,CAAC,MAAM,EAAE,GAAG,KAAK;AACxD,EAAE,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACtD,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AAC1C,EAAE;AACU,MAAC,WAAW,GAAG,CAAC;AAC5B,EAAE,UAAU;AACZ,EAAE,SAAS;AACX,EAAE,QAAQ;AACV,CAAC,KAAK;AACN,EAAE,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,YAAY;AAC7B,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO;AACb,IAAI,MAAM,WAAW,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;AACrD,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS;AAClC,MAAM,OAAO;AACb,IAAI,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE;AAC/D,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC;AACjC,MAAM,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC;AAC/B,MAAM,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC;AACnC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACpC,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,WAAW,CAAC,MAAM;AACtB,MAAM,MAAM,EAAE,CAAC;AACf,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,GAAG,MAAM;AACb,IAAI,MAAM;AACV,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,eAAe,GAAG,CAAC;AAChC,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,CAAC,KAAK;AACN,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,EAAE,CAAC,IAAI,EAAE;AACb,MAAM,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ,OAAO,EAAE,CAAC;AAClB,MAAM,OAAOA,KAAS,CAAC;AACvB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,OAAO;AACf,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAClB,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}