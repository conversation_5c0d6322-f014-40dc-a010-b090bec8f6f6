<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片加载测试 - iFlytek Spark 面试系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #1890ff;
            margin-bottom: 40px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .test-item {
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .test-item:hover {
            border-color: #1890ff;
            transform: translateY(-2px);
        }
        
        .test-item h3 {
            color: #1890ff;
            margin-bottom: 15px;
        }
        
        .test-item img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid #1890ff;
            object-fit: cover;
            margin-bottom: 15px;
        }
        
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .status.loading {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .path-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        
        .summary {
            background: #f0f8ff;
            border: 1px solid #d4edda;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .summary h3 {
            color: #1890ff;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片资源加载测试</h1>
        
        <div class="test-grid" id="testGrid">
            <!-- 测试项将通过JavaScript动态生成 -->
        </div>
        
        <div class="summary" id="summary">
            <h3>📊 测试结果汇总</h3>
            <div id="summaryContent">正在测试中...</div>
        </div>
    </div>

    <script>
        // 测试图片列表
        const testImages = [
            {
                name: '候选人头像 (SVG)',
                path: '/images/candidate-avatar.svg',
                description: '主要候选人头像文件 - 修复后'
            },
            {
                name: '默认头像',
                path: '/images/default-avatar.png',
                description: '备用默认头像文件'
            },
            {
                name: 'iFlytek Logo',
                path: '/images/iflytek-spark-logo.svg',
                description: 'iFlytek Spark 标志'
            },
            {
                name: '占位符演示',
                path: '/images/placeholder-demo.svg',
                description: '演示占位符图片'
            },
            {
                name: '占位符案例',
                path: '/images/placeholder-case.svg',
                description: '案例占位符图片'
            }
        ];

        let loadedCount = 0;
        let errorCount = 0;
        let totalCount = testImages.length;

        function createTestItem(imageInfo, index) {
            const testItem = document.createElement('div');
            testItem.className = 'test-item';
            testItem.innerHTML = `
                <h3>${imageInfo.name}</h3>
                <img id="img-${index}" alt="${imageInfo.name}" style="display: none;">
                <div class="status loading" id="status-${index}">⏳ 加载中...</div>
                <div class="path-info">${imageInfo.path}</div>
                <p>${imageInfo.description}</p>
            `;
            return testItem;
        }

        function updateStatus(index, status, message) {
            const statusElement = document.getElementById(`status-${index}`);
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }

        function updateSummary() {
            const summaryContent = document.getElementById('summaryContent');
            const successRate = ((loadedCount / totalCount) * 100).toFixed(1);
            
            summaryContent.innerHTML = `
                <p><strong>总计:</strong> ${totalCount} 个图片文件</p>
                <p><strong>成功加载:</strong> ${loadedCount} 个</p>
                <p><strong>加载失败:</strong> ${errorCount} 个</p>
                <p><strong>成功率:</strong> ${successRate}%</p>
                ${successRate === '100.0' ? 
                    '<p style="color: #52c41a; font-weight: 600;">✅ 所有图片资源加载正常！</p>' : 
                    '<p style="color: #ff4d4f; font-weight: 600;">⚠️ 部分图片资源加载失败，请检查文件路径和格式。</p>'
                }
            `;
        }

        function testImageLoading() {
            const testGrid = document.getElementById('testGrid');
            
            testImages.forEach((imageInfo, index) => {
                const testItem = createTestItem(imageInfo, index);
                testGrid.appendChild(testItem);
                
                const img = document.getElementById(`img-${index}`);
                
                img.onload = function() {
                    loadedCount++;
                    updateStatus(index, 'success', '✅ 加载成功');
                    img.style.display = 'block';
                    updateSummary();
                };
                
                img.onerror = function() {
                    errorCount++;
                    updateStatus(index, 'error', '❌ 加载失败');
                    updateSummary();
                };
                
                // 开始加载图片
                img.src = imageInfo.path;
            });
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            testImageLoading();
        });
    </script>
</body>
</html>
