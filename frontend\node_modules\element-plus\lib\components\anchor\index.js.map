{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/anchor/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Anchor from './src/anchor.vue'\nimport AnchorLink from './src/anchor-link.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAnchor: SFCWithInstall<typeof Anchor> & {\n  AnchorLink: typeof AnchorLink\n} = withInstall(Anchor, {\n  AnchorLink,\n})\nexport const ElAnchorLink: SFCWithInstall<typeof AnchorLink> =\n  withNoopInstall(AnchorLink)\nexport default ElAnchor\n\nexport * from './src/anchor'\n"], "names": ["withInstall", "<PERSON><PERSON>", "AnchorLink", "withNoopInstall"], "mappings": ";;;;;;;;;AAGY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,mBAAM,EAAE;AAC5C,cAAEC,qBAAU;AACZ,CAAC,EAAE;AACS,MAAC,YAAY,GAAGC,uBAAe,CAACD,qBAAU;;;;;;;;"}