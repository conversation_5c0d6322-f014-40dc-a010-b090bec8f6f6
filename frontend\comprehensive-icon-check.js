#!/usr/bin/env node

/**
 * 全面的 Element Plus 图标检查脚本
 * 检查所有可能的无效图标并提供修复建议
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Element Plus 官方支持的图标列表（常用图标）
const VALID_ICONS = [
  'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
  'Plus', 'Minus', 'Check', 'Close', 'Delete', 'Edit',
  'Search', 'Refresh', 'Setting', 'Star', 'Warning', 'InfoFilled',
  'User', 'Users', 'House', 'Document', 'Folder', 'Collection',
  'VideoPlay', 'VideoPause', 'VideoCamera', 'Microphone',
  'TrendCharts', 'PieChart', 'DataBoard', 'Grid', 'Odometer',
  'Cpu', 'Monitor', 'Connection', 'Lock', 'Unlock', 'Shield',
  'Timer', 'Clock', 'Calendar', 'Location', 'OfficeBuilding',
  'Upload', 'Download', 'Share', 'Printer', 'View', 'More',
  'ChatDotRound', 'Message', 'Phone', 'Bell', 'Notification',
  'Loading', 'CircleCheck', 'CircleClose', 'CirclePlus',
  'Promotion', 'Trophy', 'Medal', 'Rank', 'Tools', 'Briefcase'
]

// 已知的无效图标及其建议替换
const INVALID_ICON_REPLACEMENTS = {
  'Brain': 'Cpu',
  'DataAnalysis': 'Grid', 
  'PlayCircle': 'VideoPlay',
  'CloudUpload': 'Upload',
  'DataChart': 'TrendCharts',
  'Analytics': 'Grid',
  'Intelligence': 'Cpu',
  'SmartAnalysis': 'Grid',
  'VoiceAnalysis': 'Microphone',
  'VideoAnalysis': 'VideoCamera'
}

// 扫描文件中的图标使用
function scanFileForIcons(filePath) {
  if (!fs.existsSync(filePath)) {
    return { imports: [], usages: [], issues: [] }
  }

  const content = fs.readFileSync(filePath, 'utf8')
  const imports = []
  const usages = []
  const issues = []

  // 匹配导入语句中的图标
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@element-plus\/icons-vue['"]/g
  let importMatch
  while ((importMatch = importRegex.exec(content)) !== null) {
    const iconList = importMatch[1]
    const iconNames = iconList
      .split(',')
      .map(name => name.trim())
      .filter(name => name && !name.includes('as'))
      .map(name => name.replace(/\s+as\s+\w+/, '').trim())
    
    imports.push(...iconNames)
  }

  // 匹配模板中的图标使用
  const templateRegex = /<el-icon[^>]*>\s*<([A-Z][a-zA-Z]*)\s*\/?\s*>\s*<\/el-icon>/g
  let templateMatch
  while ((templateMatch = templateRegex.exec(content)) !== null) {
    const iconName = templateMatch[1]
    usages.push(iconName)
  }

  // 匹配字符串中的图标引用
  const allIcons = [...new Set([...imports, ...usages])]
  allIcons.forEach(icon => {
    // 检查是否为无效图标
    if (INVALID_ICON_REPLACEMENTS[icon]) {
      issues.push({
        type: 'invalid',
        icon: icon,
        suggestion: INVALID_ICON_REPLACEMENTS[icon],
        locations: []
      })
    }
    // 检查是否为可能无效的图标（不在已知有效列表中）
    else if (!VALID_ICONS.includes(icon)) {
      issues.push({
        type: 'unknown',
        icon: icon,
        suggestion: 'Please verify this icon exists in Element Plus',
        locations: []
      })
    }
  })

  return { imports, usages, issues }
}

// 递归扫描目录
function scanDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const relativePath = path.relative(__dirname, fullPath)
        const analysis = scanFileForIcons(fullPath)
        
        if (analysis.issues.length > 0) {
          results.push({
            file: relativePath,
            ...analysis
          })
        }
      }
    }
  }
  
  scan(dir)
  return results
}

// 主函数
function main() {
  console.log('🔍 全面检查 Element Plus 图标使用情况...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  if (results.length === 0) {
    console.log('✅ 所有图标检查通过！')
    console.log('✅ 未发现无效或可疑的图标使用')
    console.log('\n🎉 图标系统状态良好！')
    return
  }
  
  console.log(`⚠️  发现 ${results.length} 个文件存在图标问题:\n`)
  
  let invalidCount = 0
  let unknownCount = 0
  
  results.forEach(({ file, issues }) => {
    console.log(`📄 ${file}:`)
    
    issues.forEach(({ type, icon, suggestion }) => {
      if (type === 'invalid') {
        console.log(`  ❌ 无效图标: ${icon} → 建议使用: ${suggestion}`)
        invalidCount++
      } else if (type === 'unknown') {
        console.log(`  ⚠️  未知图标: ${icon} → ${suggestion}`)
        unknownCount++
      }
    })
    
    console.log()
  })
  
  console.log('📊 统计信息:')
  console.log(`  ❌ 确认无效图标: ${invalidCount}`)
  console.log(`  ⚠️  需要验证图标: ${unknownCount}`)
  
  if (invalidCount > 0) {
    console.log('\n💡 修复建议:')
    console.log('  1. 运行批量修复脚本: node batch-icon-fix.js')
    console.log('  2. 手动检查并替换无效图标')
    console.log('  3. 重新运行此脚本验证修复结果')
  }
}

main()
