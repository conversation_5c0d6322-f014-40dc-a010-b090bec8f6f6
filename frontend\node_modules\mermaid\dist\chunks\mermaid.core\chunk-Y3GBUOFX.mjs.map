{"version": 3, "sources": ["../../../src/rendering-util/rendering-elements/shapes/util.ts", "../../../src/rendering-util/rendering-elements/clusters.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-rect.js", "../../../src/rendering-util/rendering-elements/createLabel.js", "../../../src/rendering-util/rendering-elements/shapes/roundedRectPath.ts", "../../../src/rendering-util/rendering-elements/intersect/intersect-node.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-ellipse.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-circle.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-line.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-polygon.js", "../../../src/rendering-util/rendering-elements/intersect/index.js", "../../../src/rendering-util/rendering-elements/shapes/anchor.ts", "../../../src/rendering-util/rendering-elements/shapes/bowTieRect.ts", "../../../src/rendering-util/rendering-elements/shapes/card.ts", "../../../src/rendering-util/rendering-elements/shapes/insertPolygonShape.ts", "../../../src/rendering-util/rendering-elements/shapes/choice.ts", "../../../src/rendering-util/rendering-elements/shapes/circle.ts", "../../../src/rendering-util/rendering-elements/shapes/crossedCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/curlyBraceLeft.ts", "../../../src/rendering-util/rendering-elements/shapes/curlyBraceRight.ts", "../../../src/rendering-util/rendering-elements/shapes/curlyBraces.ts", "../../../src/rendering-util/rendering-elements/shapes/curvedTrapezoid.ts", "../../../src/rendering-util/rendering-elements/shapes/cylinder.ts", "../../../src/rendering-util/rendering-elements/shapes/dividedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/doubleCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/filledCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/flippedTriangle.ts", "../../../src/rendering-util/rendering-elements/shapes/forkJoin.ts", "../../../src/rendering-util/rendering-elements/shapes/halfRoundedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/hexagon.ts", "../../../src/rendering-util/rendering-elements/shapes/hourglass.ts", "../../../src/rendering-util/rendering-elements/shapes/icon.ts", "../../../src/rendering-util/rendering-elements/shapes/iconCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/iconRounded.ts", "../../../src/rendering-util/rendering-elements/shapes/iconSquare.ts", "../../../src/rendering-util/rendering-elements/shapes/imageSquare.ts", "../../../src/rendering-util/rendering-elements/shapes/invertedTrapezoid.ts", "../../../src/rendering-util/rendering-elements/shapes/drawRect.ts", "../../../src/rendering-util/rendering-elements/shapes/labelRect.ts", "../../../src/rendering-util/rendering-elements/shapes/leanLeft.ts", "../../../src/rendering-util/rendering-elements/shapes/leanRight.ts", "../../../src/rendering-util/rendering-elements/shapes/lightningBolt.ts", "../../../src/rendering-util/rendering-elements/shapes/linedCylinder.ts", "../../../src/rendering-util/rendering-elements/shapes/linedWaveEdgedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/multiRect.ts", "../../../src/rendering-util/rendering-elements/shapes/multiWaveEdgedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/note.ts", "../../../src/rendering-util/rendering-elements/shapes/question.ts", "../../../src/rendering-util/rendering-elements/shapes/rectLeftInvArrow.ts", "../../../src/rendering-util/rendering-elements/shapes/rectWithTitle.ts", "../../../src/rendering-util/rendering-elements/shapes/roundedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/shadedProcess.ts", "../../../src/rendering-util/rendering-elements/shapes/slopedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/squareRect.ts", "../../../src/rendering-util/rendering-elements/shapes/stadium.ts", "../../../src/rendering-util/rendering-elements/shapes/state.ts", "../../../src/rendering-util/rendering-elements/shapes/stateEnd.ts", "../../../src/rendering-util/rendering-elements/shapes/stateStart.ts", "../../../src/rendering-util/rendering-elements/shapes/subroutine.ts", "../../../src/rendering-util/rendering-elements/shapes/taggedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/taggedWaveEdgedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/text.ts", "../../../src/rendering-util/rendering-elements/shapes/tiltedCylinder.ts", "../../../src/rendering-util/rendering-elements/shapes/trapezoid.ts", "../../../src/rendering-util/rendering-elements/shapes/trapezoidalPentagon.ts", "../../../src/rendering-util/rendering-elements/shapes/triangle.ts", "../../../src/rendering-util/rendering-elements/shapes/waveEdgedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/waveRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/windowPane.ts", "../../../src/rendering-util/rendering-elements/shapes/erBox.ts", "../../../src/rendering-util/rendering-elements/shapes/classBox.ts", "../../../src/diagrams/class/shapeUtil.ts", "../../../src/rendering-util/rendering-elements/shapes/requirementBox.ts", "../../../src/rendering-util/rendering-elements/shapes/kanbanItem.ts", "../../../src/rendering-util/rendering-elements/shapes.ts", "../../../src/rendering-util/rendering-elements/nodes.ts"], "sourcesContent": ["import { createText } from '../../createText.js';\nimport type { Node } from '../../types.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport { select } from 'd3';\nimport defaultConfig from '../../../defaultConfig.js';\nimport { evaluate, sanitizeText } from '../../../diagrams/common/common.js';\nimport { decodeEntities, handleUndefinedAttr, parseFontSize } from '../../../utils.js';\nimport type { D3Selection, Point } from '../../../types.js';\n\nexport const labelHelper = async <T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  _classes?: string\n) => {\n  let cssClasses;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(getConfig()?.htmlLabels);\n  if (!_classes) {\n    cssClasses = 'node default';\n  } else {\n    cssClasses = _classes;\n  }\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', cssClasses)\n    .attr('id', node.domId || node.id);\n\n  // Create the label and insert it after the rect\n  const labelEl = shapeSvg\n    .insert('g')\n    .attr('class', 'label')\n    .attr('style', handleUndefinedAttr(node.labelStyle));\n\n  // Replace label with default value if undefined\n  let label;\n  if (node.label === undefined) {\n    label = '';\n  } else {\n    label = typeof node.label === 'string' ? node.label : node.label[0];\n  }\n\n  const text = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig()), {\n    useHtmlLabels,\n    width: node.width || getConfig().flowchart?.wrappingWidth,\n    // @ts-expect-error -- This is currently not used. Should this be `classes` instead?\n    cssClasses: 'markdown-node-label',\n    style: node.labelStyle,\n    addSvgBackground: !!node.icon || !!node.img,\n  });\n  // Get the size of the label\n  let bbox = text.getBBox();\n  const halfPadding = (node?.padding ?? 0) / 2;\n\n  if (useHtmlLabels) {\n    const div = text.children[0];\n    const dv = select(text);\n\n    // if there are images, need to wait for them to load before getting the bounding box\n    const images = div.getElementsByTagName('img');\n    if (images) {\n      const noImgText = label.replace(/<img[^>]*>/g, '').trim() === '';\n\n      await Promise.all(\n        [...images].map(\n          (img) =>\n            new Promise((res) => {\n              /**\n               *\n               */\n              function setupImage() {\n                img.style.display = 'flex';\n                img.style.flexDirection = 'column';\n\n                if (noImgText) {\n                  // default size if no text\n                  const bodyFontSize = getConfig().fontSize\n                    ? getConfig().fontSize\n                    : window.getComputedStyle(document.body).fontSize;\n                  const enlargingFactor = 5;\n                  const [parsedBodyFontSize = defaultConfig.fontSize] = parseFontSize(bodyFontSize);\n                  const width = parsedBodyFontSize * enlargingFactor + 'px';\n                  img.style.minWidth = width;\n                  img.style.maxWidth = width;\n                } else {\n                  img.style.width = '100%';\n                }\n                res(img);\n              }\n              setTimeout(() => {\n                if (img.complete) {\n                  setupImage();\n                }\n              });\n              img.addEventListener('error', setupImage);\n              img.addEventListener('load', setupImage);\n            })\n        )\n      );\n    }\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center the label\n  if (useHtmlLabels) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  } else {\n    labelEl.attr('transform', 'translate(' + 0 + ', ' + -bbox.height / 2 + ')');\n  }\n  if (node.centerLabel) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  }\n  labelEl.insert('rect', ':first-child');\n  return { shapeSvg, bbox, halfPadding, label: labelEl };\n};\nexport const insertLabel = async <T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  label: string,\n  options: {\n    labelStyle?: string | undefined;\n    icon?: boolean | undefined;\n    img?: string | undefined;\n    useHtmlLabels?: boolean | undefined;\n    padding: number;\n    width?: number | undefined;\n    centerLabel?: boolean | undefined;\n    addSvgBackground?: boolean | undefined;\n  }\n) => {\n  const useHtmlLabels = options.useHtmlLabels || evaluate(getConfig()?.flowchart?.htmlLabels);\n\n  // Create the label and insert it after the rect\n  const labelEl = parent\n    .insert('g')\n    .attr('class', 'label')\n    .attr('style', options.labelStyle || '');\n\n  const text = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig()), {\n    useHtmlLabels,\n    width: options.width || getConfig()?.flowchart?.wrappingWidth,\n    style: options.labelStyle,\n    addSvgBackground: !!options.icon || !!options.img,\n  });\n  // Get the size of the label\n  let bbox = text.getBBox();\n  const halfPadding = options.padding / 2;\n\n  if (evaluate(getConfig()?.flowchart?.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center the label\n  if (useHtmlLabels) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  } else {\n    labelEl.attr('transform', 'translate(' + 0 + ', ' + -bbox.height / 2 + ')');\n  }\n  if (options.centerLabel) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  }\n  labelEl.insert('rect', ':first-child');\n  return { shapeSvg: parent, bbox, halfPadding, label: labelEl };\n};\nexport const updateNodeBounds = <T extends SVGGraphicsElement>(\n  node: Node,\n  // D3Selection<SVGGElement> is for the roughjs case, D3Selection<T> is for the non-roughjs case\n  element: D3Selection<SVGGElement> | D3Selection<T>\n) => {\n  const bbox = element.node()!.getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n};\n\n/**\n * @param parent - Parent element to append the polygon to\n * @param w - Width of the polygon\n * @param h - Height of the polygon\n * @param points - Array of points to create the polygon\n */\nexport function insertPolygonShape(\n  parent: D3Selection<SVGGElement>,\n  w: number,\n  h: number,\n  points: Point[]\n) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('class', 'label-container')\n    .attr('transform', 'translate(' + -w / 2 + ',' + h / 2 + ')');\n}\n\nexport const getNodeClasses = (node: Node, extra?: string) =>\n  (node.look === 'handDrawn' ? 'rough-node' : 'node') + ' ' + node.cssClasses + ' ' + (extra || '');\n\nexport function createPathFromPoints(points: Point[]) {\n  const pointStrings = points.map((p, i) => `${i === 0 ? 'M' : 'L'}${p.x},${p.y}`);\n  pointStrings.push('Z');\n  return pointStrings.join(' ');\n}\n\nexport function generateFullSineWavePoints(\n  x1: number,\n  y1: number,\n  x2: number,\n  y2: number,\n  amplitude: number,\n  numCycles: number\n) {\n  const points = [];\n  const steps = 50; // Number of segments to create a smooth curve\n  const deltaX = x2 - x1;\n  const deltaY = y2 - y1;\n  const cycleLength = deltaX / numCycles;\n\n  // Calculate frequency and phase shift\n  const frequency = (2 * Math.PI) / cycleLength;\n  const midY = y1 + deltaY / 2;\n\n  for (let i = 0; i <= steps; i++) {\n    const t = i / steps;\n    const x = x1 + t * deltaX;\n    const y = midY + amplitude * Math.sin(frequency * (x - x1));\n\n    points.push({ x, y });\n  }\n\n  return points;\n}\n\n/**\n * @param centerX - x-coordinate of center of circle\n * @param centerY - y-coordinate of center of circle\n * @param radius - radius of circle\n * @param numPoints - total points required\n * @param startAngle - angle where arc will start\n * @param endAngle - angle where arc will end\n */\nexport function generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints: number,\n  startAngle: number,\n  endAngle: number\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n\n  return points;\n}\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { evaluate } from '../../diagrams/common/common.js';\nimport { log } from '../../logger.js';\nimport { getSubGraphTitleMargins } from '../../utils/subGraphTitleMargins.js';\nimport { select } from 'd3';\nimport rough from 'roughjs';\nimport { createText } from '../createText.ts';\nimport intersectRect from '../rendering-elements/intersect/intersect-rect.js';\nimport createLabel from './createLabel.js';\nimport { createRoundedRectPathD } from './shapes/roundedRectPath.ts';\nimport { styles2String, userNodeOverrides } from './shapes/handDrawnShapeStyles.js';\n\nconst rect = async (parent, node) => {\n  log.info('Creating subgraph rect for ', node.id, node);\n  const siteConfig = getConfig();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { clusterBkg, clusterBorder } = themeVariables;\n\n  const { labelStyles, nodeStyles, borderStyles, backgroundStyles } = styles2String(node);\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'cluster ' + node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-look', node.look);\n\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n\n  // Create the label and insert it after the rect\n  const labelEl = shapeSvg.insert('g').attr('class', 'cluster-label ');\n\n  const text = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true,\n  });\n\n  // Get the size of the label\n  let bbox = text.getBBox();\n\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n\n  log.trace('Data ', node, JSON.stringify(node));\n  let rect;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 3,\n      seed: handDrawnSeed,\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, 0), options);\n    rect = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughNode;\n    }, ':first-child');\n    // Should we affect the options instead of doing this?\n    rect.select('path:nth-child(2)').attr('style', borderStyles.join(';'));\n    rect.select('path').attr('style', backgroundStyles.join(';').replace('fill', 'stroke'));\n  } else {\n    // add the rect\n    rect = shapeSvg.insert('rect', ':first-child');\n    // center the rect around its coordinate\n    rect\n      .attr('style', nodeStyles)\n      .attr('rx', node.rx)\n      .attr('ry', node.ry)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n  }\n  const { subGraphTitleTopMargin } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\n    'transform',\n    // This puts the label on top of the box instead of inside it\n    `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`\n  );\n\n  if (labelStyles) {\n    const span = labelEl.select('span');\n    if (span) {\n      span.attr('style', labelStyles);\n    }\n  }\n  // Center the label\n\n  const rectBox = rect.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = bbox.height - node.padding / 2;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: bbox };\n};\n\n/**\n * Non visible cluster where the note is group with its\n *\n * @param {any} parent\n * @param {any} node\n * @returns {any} ShapeSvg\n */\nconst noteGroup = (parent, node) => {\n  // Add outer g element\n  const shapeSvg = parent.insert('g').attr('class', 'note-cluster').attr('id', node.id);\n\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n\n  // center the rect around its coordinate\n  rect\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', node.x - node.width / 2 - halfPadding)\n    .attr('y', node.y - node.height / 2 - halfPadding)\n    .attr('width', node.width + padding)\n    .attr('height', node.height + padding)\n    .attr('fill', 'none');\n\n  const rectBox = rect.node().getBBox();\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: { width: 0, height: 0 } };\n};\n\nconst roundedWithTitle = async (parent, node) => {\n  const siteConfig = getConfig();\n\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { altBackground, compositeBackground, compositeTitleBackground, nodeBorder } =\n    themeVariables;\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-id', node.id)\n    .attr('data-look', node.look);\n\n  // add the rect\n  const outerRectG = shapeSvg.insert('g', ':first-child');\n\n  // Create the label and insert it after the rect\n  const label = shapeSvg.insert('g').attr('class', 'cluster-label');\n  let innerRect = shapeSvg.append('rect');\n\n  const text = label\n    .node()\n    .appendChild(await createLabel(node.label, node.labelStyle, undefined, true));\n\n  // Get the size of the label\n  let bbox = text.getBBox();\n\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Rounded With Title\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n\n  const width =\n    (node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width) + padding;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n\n  const height = node.height + padding;\n  // const height = node.height + padding;\n  const innerHeight = node.height + padding - bbox.height - 6;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n  const innerY = node.y - node.height / 2 - halfPadding + bbox.height + 2;\n\n  // add the rect\n  let rect;\n  if (node.look === 'handDrawn') {\n    const isAlt = node.cssClasses.includes('statediagram-cluster-alt');\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode =\n      node.rx || node.ry\n        ? rc.path(createRoundedRectPathD(x, y, width, height, 10), {\n            roughness: 0.7,\n            fill: compositeTitleBackground,\n            fillStyle: 'solid',\n            stroke: nodeBorder,\n            seed: handDrawnSeed,\n          })\n        : rc.rectangle(x, y, width, height, { seed: handDrawnSeed });\n\n    rect = shapeSvg.insert(() => roughOuterNode, ':first-child');\n    const roughInnerNode = rc.rectangle(x, innerY, width, innerHeight, {\n      fill: isAlt ? altBackground : compositeBackground,\n      fillStyle: isAlt ? 'hachure' : 'solid',\n      stroke: nodeBorder,\n      seed: handDrawnSeed,\n    });\n\n    rect = shapeSvg.insert(() => roughOuterNode, ':first-child');\n    innerRect = shapeSvg.insert(() => roughInnerNode);\n  } else {\n    rect = outerRectG.insert('rect', ':first-child');\n    const outerRectClass = 'outer';\n\n    // center the rect around its coordinate\n    rect\n      .attr('class', outerRectClass)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('data-look', node.look);\n    innerRect\n      .attr('class', 'inner')\n      .attr('x', x)\n      .attr('y', innerY)\n      .attr('width', width)\n      .attr('height', innerHeight);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${node.x - bbox.width / 2}, ${y + 1 - (evaluate(siteConfig.flowchart.htmlLabels) ? 0 : 3)})`\n  );\n\n  const rectBox = rect.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = bbox.height - node.padding / 2;\n  node.labelBBox = bbox;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: bbox };\n};\nconst kanbanSection = async (parent, node) => {\n  log.info('Creating subgraph rect for ', node.id, node);\n  const siteConfig = getConfig();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { clusterBkg, clusterBorder } = themeVariables;\n\n  const { labelStyles, nodeStyles, borderStyles, backgroundStyles } = styles2String(node);\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'cluster ' + node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-look', node.look);\n\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n\n  // Create the label and insert it after the rect\n  const labelEl = shapeSvg.insert('g').attr('class', 'cluster-label ');\n\n  const text = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true,\n    width: node.width,\n  });\n\n  // Get the size of the label\n  let bbox = text.getBBox();\n\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n\n  log.trace('Data ', node, JSON.stringify(node));\n  let rect;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 4,\n      seed: handDrawnSeed,\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, node.rx), options);\n    rect = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughNode;\n    }, ':first-child');\n    // Should we affect the options instead of doing this?\n    rect.select('path:nth-child(2)').attr('style', borderStyles.join(';'));\n    rect.select('path').attr('style', backgroundStyles.join(';').replace('fill', 'stroke'));\n  } else {\n    // add the rect\n    rect = shapeSvg.insert('rect', ':first-child');\n    // center the rect around its coordinate\n    rect\n      .attr('style', nodeStyles)\n      .attr('rx', node.rx)\n      .attr('ry', node.ry)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n  }\n  const { subGraphTitleTopMargin } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\n    'transform',\n    // This puts the label on top of the box instead of inside it\n    `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`\n  );\n\n  if (labelStyles) {\n    const span = labelEl.select('span');\n    if (span) {\n      span.attr('style', labelStyles);\n    }\n  }\n  // Center the label\n\n  const rectBox = rect.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = bbox.height - node.padding / 2;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: bbox };\n};\nconst divider = (parent, node) => {\n  const siteConfig = getConfig();\n\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { nodeBorder } = themeVariables;\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-look', node.look);\n\n  // add the rect\n  const outerRectG = shapeSvg.insert('g', ':first-child');\n\n  const padding = 0 * node.padding;\n\n  const width = node.width + padding;\n\n  node.diff = -node.padding;\n\n  const height = node.height + padding;\n  // const height = node.height + padding;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n\n  // add the rect\n  let rect;\n  if (node.look === 'handDrawn') {\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode = rc.rectangle(x, y, width, height, {\n      fill: 'lightgrey',\n      roughness: 0.5,\n      strokeLineDash: [5],\n      stroke: nodeBorder,\n      seed: handDrawnSeed,\n    });\n\n    rect = shapeSvg.insert(() => roughOuterNode, ':first-child');\n  } else {\n    rect = outerRectG.insert('rect', ':first-child');\n    const outerRectClass = 'divider';\n\n    // center the rect around its coordinate\n    rect\n      .attr('class', outerRectClass)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('data-look', node.look);\n  }\n\n  const rectBox = rect.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = 0;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: {} };\n};\n\nconst squareRect = rect;\nconst shapes = {\n  rect,\n  squareRect,\n  roundedWithTitle,\n  noteGroup,\n  divider,\n  kanbanSection,\n};\n\nlet clusterElems = new Map();\n\n/**\n * @typedef {keyof typeof shapes} ClusterShapeID\n */\n\n/**\n * @param {import('../types.js').ClusterNode} node - Shape defaults to 'rect'\n */\nexport const insertCluster = async (elem, node) => {\n  const shape = node.shape || 'rect';\n  const cluster = await shapes[shape](elem, node);\n  clusterElems.set(node.id, cluster);\n  return cluster;\n};\n\nexport const getClusterTitleWidth = (elem, node) => {\n  const label = createLabel(node.label, node.labelStyle, undefined, true);\n  elem.node().appendChild(label);\n  const width = label.getBBox().width;\n  elem.node().removeChild(label);\n  return width;\n};\n\nexport const clear = () => {\n  clusterElems = new Map();\n};\n\nexport const positionCluster = (node) => {\n  log.info(\n    'Position cluster (' +\n      node.id +\n      ', ' +\n      node.x +\n      ', ' +\n      node.y +\n      ') (' +\n      node?.width +\n      ', ' +\n      node?.height +\n      ')',\n    clusterElems.get(node.id)\n  );\n  const el = clusterElems.get(node.id);\n  el.cluster.attr('transform', 'translate(' + node.x + ', ' + node.y + ')');\n};\n", "const intersectRect = (node, point) => {\n  var x = node.x;\n  var y = node.y;\n\n  // Rectangle intersection algorithm from:\n  // https://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n};\n\nexport default intersectRect;\n", "import { select } from 'd3';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport common, { evaluate, renderKatex, hasKatex } from '../../diagrams/common/common.js';\nimport { decodeEntities } from '../../utils.js';\n\n/**\n * @param dom\n * @param styleFn\n */\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\n/**\n * @param {any} node\n * @returns {Promise<SVGForeignObjectElement>} Node\n */\nasync function addHtmlLabel(node) {\n  const fo = select(document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject'));\n  const div = fo.append('xhtml:div');\n\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common.lineBreakRegex, '\\n'), getConfig());\n  }\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  div.html(\n    '<span class=\"' +\n      labelClass +\n      '\" ' +\n      (node.labelStyle ? 'style=\"' + node.labelStyle + '\"' : '') + // codeql [js/html-constructed-from-input] : false positive\n      '>' +\n      label +\n      '</span>'\n  );\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'inline-block');\n  div.style('padding-right', '1px');\n  // Fix for firefox\n  div.style('white-space', 'nowrap');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  return fo.node();\n}\n/**\n * @param _vertexText\n * @param style\n * @param isTitle\n * @param isNode\n * @deprecated svg-util/createText instead\n */\nconst createLabel = async (_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || '';\n  if (typeof vertexText === 'object') {\n    vertexText = vertexText[0];\n  }\n\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n    vertexText = vertexText.replace(/\\\\n|\\n/g, '<br />');\n    log.info('vertexText' + vertexText);\n    const node = {\n      isNode,\n      label: decodeEntities(vertexText).replace(\n        /fa[blrs]?:fa-[\\w-]+/g,\n        (s) => `<i class='${s.replace(':', ' ')}'></i>`\n      ),\n      labelStyle: style ? style.replace('fill:', 'color:') : style,\n    };\n    let vertexNode = await addHtmlLabel(node);\n    // vertexNode.parentNode.removeChild(vertexNode);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n    svgLabel.setAttribute('style', style.replace('color:', 'fill:'));\n    let rows = [];\n    if (typeof vertexText === 'string') {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n\n    for (const row of rows) {\n      const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');\n      tspan.setAttributeNS('http://www.w3.org/XML/1998/namespace', 'xml:space', 'preserve');\n      tspan.setAttribute('dy', '1em');\n      tspan.setAttribute('x', '0');\n      if (isTitle) {\n        tspan.setAttribute('class', 'title-row');\n      } else {\n        tspan.setAttribute('class', 'row');\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n};\n\nexport default createLabel;\n", "export const createRoundedRectPathD = (\n  x: number,\n  y: number,\n  totalWidth: number,\n  totalHeight: number,\n  radius: number\n) =>\n  [\n    'M',\n    x + radius,\n    y, // Move to the first point\n    'H',\n    x + totalWidth - radius, // Draw horizontal line to the beginning of the right corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth,\n    y + radius, // Draw arc to the right top corner\n    'V',\n    y + totalHeight - radius, // Draw vertical line down to the beginning of the right bottom corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth - radius,\n    y + totalHeight, // Draw arc to the right bottom corner\n    'H',\n    x + radius, // Draw horizontal line to the beginning of the left bottom corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x,\n    y + totalHeight - radius, // Draw arc to the left bottom corner\n    'V',\n    y + radius, // Draw vertical line up to the beginning of the left top corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + radius,\n    y, // Draw arc to the left top corner\n    'Z', // Close the path\n  ].join(' ');\n", "function intersectNode(node, point) {\n  return node.intersect(point);\n}\n\nexport default intersectNode;\n", "function intersectEllipse(node, rx, ry, point) {\n  // Formulae from: https://mathworld.wolfram.com/Ellipse-LineIntersection.html\n\n  var cx = node.x;\n  var cy = node.y;\n\n  var px = cx - point.x;\n  var py = cy - point.y;\n\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n\n  var dx = Math.abs((rx * ry * px) / det);\n  if (point.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs((rx * ry * py) / det);\n  if (point.y < cy) {\n    dy = -dy;\n  }\n\n  return { x: cx + dx, y: cy + dy };\n}\n\nexport default intersectEllipse;\n", "import intersectEllipse from './intersect-ellipse.js';\n\nfunction intersectCircle(node, rx, point) {\n  return intersectEllipse(node, rx, rx, point);\n}\n\nexport default intersectCircle;\n", "/**\n * Returns the point at which two lines, p and q, intersect or returns undefined if they do not intersect.\n */\nfunction intersectLine(p1, p2, q1, q2) {\n  // Algorithm from <PERSON><PERSON>, (ed.) Graphics Gems, No 2, <PERSON>, 1994,\n  // p7 and p473.\n\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n\n  // Compute a1, b1, c1, where line joining points 1 and 2 is F(x,y) = a1 x +\n  // b1 y + c1 = 0.\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n\n  // Compute r3 and r4.\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n\n  // Check signs of r3 and r4. If both point 3 and point 4 lie on\n  // same side of line 1, the line segments do not intersect.\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Compute a2, b2, c2 where line joining points 3 and 4 is G(x,y) = a2 x + b2 y + c2 = 0\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n\n  // Compute r1 and r2\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n\n  // Check signs of r1 and r2. If both point 1 and point 2 lie\n  // on same side of second line segment, the line segments do\n  // not intersect.\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Line segments intersect: compute intersection point.\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return /*COLLINEAR*/;\n  }\n\n  offset = Math.abs(denom / 2);\n\n  // The denom/2 is to get rounding instead of truncating. It\n  // is added or subtracted to the numerator, depending upon the\n  // sign of the numerator.\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  return { x: x, y: y };\n}\n\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n\nexport default intersectLine;\n", "import intersectLine from './intersect-line.js';\n\n/**\n * Returns the point ({x, y}) at which the point argument intersects with the node argument assuming\n * that it has the shape specified by polygon.\n */\nfunction intersectPolygon(node, polyPoints, point) {\n  let x1 = node.x;\n  let y1 = node.y;\n\n  let intersections = [];\n\n  let minX = Number.POSITIVE_INFINITY;\n  let minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === 'function') {\n    polyPoints.forEach(function (entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n\n  let left = x1 - node.width / 2 - minX;\n  let top = y1 - node.height / 2 - minY;\n\n  for (let i = 0; i < polyPoints.length; i++) {\n    let p1 = polyPoints[i];\n    let p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    let intersect = intersectLine(\n      node,\n      point,\n      { x: left + p1.x, y: top + p1.y },\n      { x: left + p2.x, y: top + p2.y }\n    );\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n\n  if (!intersections.length) {\n    return node;\n  }\n\n  if (intersections.length > 1) {\n    // More intersections, find the one nearest to edge end point\n    intersections.sort(function (p, q) {\n      let pdx = p.x - point.x;\n      let pdy = p.y - point.y;\n      let distp = Math.sqrt(pdx * pdx + pdy * pdy);\n\n      let qdx = q.x - point.x;\n      let qdy = q.y - point.y;\n      let distq = Math.sqrt(qdx * qdx + qdy * qdy);\n\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n\nexport default intersectPolygon;\n", "/*\n * Borrowed with love from dagre-d3. Many thanks to c<PERSON><PERSON><PERSON>!\n */\n\nimport node from './intersect-node.js';\nimport circle from './intersect-circle.js';\nimport ellipse from './intersect-ellipse.js';\nimport polygon from './intersect-polygon.js';\nimport rect from './intersect-rect.js';\n\nexport default {\n  node,\n  circle,\n  ellipse,\n  polygon,\n  rect,\n};\n", "import { log } from '../../../logger.js';\nimport { updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { handleUndefinedAttr } from '../../../utils.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function anchor<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const classes = getNodeClasses(node);\n  let cssClasses = classes;\n  if (!classes) {\n    cssClasses = 'anchor';\n  }\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', cssClasses)\n    .attr('id', node.domId || node.id);\n\n  const radius = 1;\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'black', stroke: 'none', fillStyle: 'solid' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n  }\n  const roughNode = rc.circle(0, 0, radius * 2, options);\n  const circleElem = shapeSvg.insert(() => roughNode, ':first-child');\n  circleElem.attr('class', 'anchor').attr('style', handleUndefinedAttr(cssStyles));\n\n  updateNodeBounds(node, circleElem);\n\n  node.intersect = function (point) {\n    log.info('Circle intersect', node, radius, point);\n    return intersect.circle(node, radius, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateArcPoints(\n  x1: number,\n  y1: number,\n  x2: number,\n  y2: number,\n  rx: number,\n  ry: number,\n  clockwise: boolean\n) {\n  const numPoints = 20;\n  // Calculate midpoint\n  const midX = (x1 + x2) / 2;\n  const midY = (y1 + y2) / 2;\n\n  // Calculate the angle of the line connecting the points\n  const angle = Math.atan2(y2 - y1, x2 - x1);\n\n  // Calculate transformed coordinates for the ellipse\n  const dx = (x2 - x1) / 2;\n  const dy = (y2 - y1) / 2;\n\n  // Scale to unit circle\n  const transformedX = dx / rx;\n  const transformedY = dy / ry;\n\n  // Calculate the distance between points on the unit circle\n  const distance = Math.sqrt(transformedX ** 2 + transformedY ** 2);\n\n  // Check if the ellipse can be drawn with the given radii\n  if (distance > 1) {\n    throw new Error('The given radii are too small to create an arc between the points.');\n  }\n\n  // Calculate the distance from the midpoint to the center of the ellipse\n  const scaledCenterDistance = Math.sqrt(1 - distance ** 2);\n\n  // Calculate the center of the ellipse\n  const centerX = midX + scaledCenterDistance * ry * Math.sin(angle) * (clockwise ? -1 : 1);\n  const centerY = midY - scaledCenterDistance * rx * Math.cos(angle) * (clockwise ? -1 : 1);\n\n  // Calculate the start and end angles on the ellipse\n  const startAngle = Math.atan2((y1 - centerY) / ry, (x1 - centerX) / rx);\n  const endAngle = Math.atan2((y2 - centerY) / ry, (x2 - centerX) / rx);\n\n  // Adjust angles for clockwise/counterclockwise\n  let angleRange = endAngle - startAngle;\n  if (clockwise && angleRange < 0) {\n    angleRange += 2 * Math.PI;\n  }\n  if (!clockwise && angleRange > 0) {\n    angleRange -= 2 * Math.PI;\n  }\n\n  // Generate points\n  const points = [];\n  for (let i = 0; i < numPoints; i++) {\n    const t = i / (numPoints - 1);\n    const angle = startAngle + t * angleRange;\n    const x = centerX + rx * Math.cos(angle);\n    const y = centerY + ry * Math.sin(angle);\n    points.push({ x, y });\n  }\n\n  return points;\n}\n\nexport async function bowTieRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding + 20;\n  const h = bbox.height + node.padding;\n\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n\n  // let shape: d3.Selection<SVGPathElement | SVGGElement, unknown, null, undefined>;\n  const { cssStyles } = node;\n\n  const points = [\n    { x: w / 2, y: -h / 2 },\n    { x: -w / 2, y: -h / 2 },\n    ...generateArcPoints(-w / 2, -h / 2, -w / 2, h / 2, rx, ry, false),\n    { x: w / 2, y: h / 2 },\n    ...generateArcPoints(w / 2, h / 2, w / 2, -h / 2, rx, ry, true),\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const bowTieRectPath = createPathFromPoints(points);\n  const bowTieRectShapePath = rc.path(bowTieRectPath, options);\n  const bowTieRectShape = shapeSvg.insert(() => bowTieRectShapePath, ':first-child');\n\n  bowTieRectShape.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    bowTieRectShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    bowTieRectShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  bowTieRectShape.attr('transform', `translate(${rx / 2}, 0)`);\n\n  updateNodeBounds(node, bowTieRectShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\n\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport { createPathFromPoints } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\n// const createPathFromPoints = (points: { x: number; y: number }[]): string => {\n//   const pointStrings = points.map((p, i) => `${i === 0 ? 'M' : 'L'}${p.x},${p.y}`);\n//   pointStrings.push('Z');\n//   return pointStrings.join(' ');\n// };\n\nexport async function card<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const h = bbox.height + node.padding;\n  const padding = 12;\n  const w = bbox.width + node.padding + padding;\n  const left = 0;\n  const right = w;\n  const top = -h;\n  const bottom = 0;\n  const points = [\n    { x: left + padding, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n    { x: left, y: top + padding },\n    { x: left + padding, y: top },\n  ];\n\n  let polygon: D3Selection<SVGGElement> | Awaited<ReturnType<typeof insertPolygonShape>>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { D3Selection } from '../../../types.js';\n\nexport function insertPolygonShape<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  w: number,\n  h: number,\n  points: { x: number; y: number }[]\n) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('class', 'label-container')\n    .attr('transform', 'translate(' + -w / 2 + ',' + h / 2 + ')');\n}\n", "import intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { createPathFromPoints, getNodeClasses } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function choice<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { nodeStyles } = styles2String(node);\n  node.label = '';\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const { cssStyles } = node;\n\n  const s = Math.max(28, node.width ?? 0);\n\n  const points = [\n    { x: 0, y: s / 2 },\n    { x: s / 2, y: 0 },\n    { x: 0, y: -s / 2 },\n    { x: -s / 2, y: 0 },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const choicePath = createPathFromPoints(points);\n  const roughNode = rc.path(choicePath, options);\n  const choiceShape = shapeSvg.insert(() => roughNode, ':first-child');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    choiceShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    choiceShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  node.width = 28;\n  node.height = 28;\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function circle<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const radius = bbox.width / 2 + halfPadding;\n  let circleElem;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.circle(0, 0, radius * 2, options);\n\n    circleElem = shapeSvg.insert(() => roughNode, ':first-child');\n    circleElem.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n  } else {\n    circleElem = shapeSvg\n      .insert('circle', ':first-child')\n      .attr('class', 'basic label-container')\n      .attr('style', nodeStyles)\n      .attr('r', radius)\n      .attr('cx', 0)\n      .attr('cy', 0);\n  }\n\n  updateNodeBounds(node, circleElem);\n\n  node.intersect = function (point) {\n    log.info('Circle intersect', node, radius, point);\n    return intersect.circle(node, radius, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nfunction createLine(r: number) {\n  const xAxis45 = Math.cos(Math.PI / 4); // cosine of 45 degrees\n  const yAxis45 = Math.sin(Math.PI / 4); // sine of 45 degrees\n  const lineLength = r * 2;\n\n  const pointQ1 = { x: (lineLength / 2) * xAxis45, y: (lineLength / 2) * yAxis45 }; // Quadrant I\n  const pointQ2 = { x: -(lineLength / 2) * xAxis45, y: (lineLength / 2) * yAxis45 }; // Quadrant II\n  const pointQ3 = { x: -(lineLength / 2) * xAxis45, y: -(lineLength / 2) * yAxis45 }; // Quadrant III\n  const pointQ4 = { x: (lineLength / 2) * xAxis45, y: -(lineLength / 2) * yAxis45 }; // Quadrant IV\n\n  return `M ${pointQ2.x},${pointQ2.y} L ${pointQ4.x},${pointQ4.y}\n                   M ${pointQ1.x},${pointQ1.y} L ${pointQ3.x},${pointQ3.y}`;\n}\n\nexport function crossedCircle<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  node.label = '';\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const radius = Math.max(30, node?.width ?? 0);\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n  const linePath = createLine(radius);\n  const lineNode = rc.path(linePath, options);\n\n  const crossedCircle = shapeSvg.insert(() => circleNode, ':first-child');\n  crossedCircle.insert(() => lineNode);\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    crossedCircle.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    crossedCircle.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, crossedCircle);\n\n  node.intersect = function (point) {\n    log.info('crossedCircle intersect', node, { radius, point });\n    const pos = intersect.circle(node, radius, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints = 100,\n  startAngle = 0,\n  endAngle = 180\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n\n  return points;\n}\n\nexport async function curlyBraceLeft<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n\n  const { cssStyles } = node;\n\n  const points = [\n    ...generateCirclePoints(w / 2, -h / 2, radius, 30, -90, 0),\n    { x: -w / 2 - radius, y: radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n  ];\n\n  const rectPoints = [\n    { x: w / 2, y: -h / 2 - radius },\n    { x: -w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: -w / 2 - radius, y: -radius },\n    ...generateCirclePoints(w / 2 + w * 0.1, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + w * 0.1, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n    { x: -w / 2, y: h / 2 + radius },\n    { x: w / 2, y: h / 2 + radius },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const curlyBraceLeftPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceLeftPath.replace('Z', '');\n  const curlyBraceLeftNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBraceLeftShape = shapeSvg.insert('g', ':first-child');\n  curlyBraceLeftShape.insert(() => rectShape, ':first-child').attr('stroke-opacity', 0);\n  curlyBraceLeftShape.insert(() => curlyBraceLeftNode, ':first-child');\n  curlyBraceLeftShape.attr('class', 'text');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    curlyBraceLeftShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    curlyBraceLeftShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  curlyBraceLeftShape.attr('transform', `translate(${radius}, 0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + radius - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, curlyBraceLeftShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints = 100,\n  startAngle = 0,\n  endAngle = 180\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x, y });\n  }\n\n  return points;\n}\n\nexport async function curlyBraceRight<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n\n  const { cssStyles } = node;\n\n  const points = [\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: w / 2 + radius, y: -radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: w / 2 + radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n  ];\n\n  const rectPoints = [\n    { x: -w / 2, y: -h / 2 - radius },\n    { x: w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: w / 2 + radius, y: -radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: w / 2 + radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n    { x: w / 2, y: h / 2 + radius },\n    { x: -w / 2, y: h / 2 + radius },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const curlyBraceRightPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceRightPath.replace('Z', '');\n  const curlyBraceRightNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBraceRightShape = shapeSvg.insert('g', ':first-child');\n  curlyBraceRightShape.insert(() => rectShape, ':first-child').attr('stroke-opacity', 0);\n  curlyBraceRightShape.insert(() => curlyBraceRightNode, ':first-child');\n  curlyBraceRightShape.attr('class', 'text');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    curlyBraceRightShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    curlyBraceRightShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  curlyBraceRightShape.attr('transform', `translate(${-radius}, 0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, curlyBraceRightShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints = 100,\n  startAngle = 0,\n  endAngle = 180\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n\n  return points;\n}\n\nexport async function curlyBraces<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n\n  const { cssStyles } = node;\n\n  const leftCurlyBracePoints = [\n    ...generateCirclePoints(w / 2, -h / 2, radius, 30, -90, 0),\n    { x: -w / 2 - radius, y: radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n  ];\n\n  const rightCurlyBracePoints = [\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180),\n    { x: w / 2 - radius / 2, y: radius },\n    ...generateCirclePoints(-w / 2 - radius / 2, -radius, radius, 20, 0, 90),\n    ...generateCirclePoints(-w / 2 - radius / 2, radius, radius, 20, -90, 0),\n    { x: w / 2 - radius / 2, y: -radius },\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270),\n  ];\n\n  const rectPoints = [\n    { x: w / 2, y: -h / 2 - radius },\n    { x: -w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: -w / 2 - radius, y: -radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n    { x: -w / 2, y: h / 2 + radius },\n    { x: w / 2 - radius - radius / 2, y: h / 2 + radius },\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180),\n    { x: w / 2 - radius / 2, y: radius },\n    ...generateCirclePoints(-w / 2 - radius / 2, -radius, radius, 20, 0, 90),\n    ...generateCirclePoints(-w / 2 - radius / 2, radius, radius, 20, -90, 0),\n    { x: w / 2 - radius / 2, y: -radius },\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270),\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const leftCurlyBracePath = createPathFromPoints(leftCurlyBracePoints);\n  const newLeftCurlyBracePath = leftCurlyBracePath.replace('Z', '');\n  const leftCurlyBraceNode = rc.path(newLeftCurlyBracePath, options);\n  const rightCurlyBracePath = createPathFromPoints(rightCurlyBracePoints);\n  const newRightCurlyBracePath = rightCurlyBracePath.replace('Z', '');\n  const rightCurlyBraceNode = rc.path(newRightCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBracesShape = shapeSvg.insert('g', ':first-child');\n  curlyBracesShape.insert(() => rectShape, ':first-child').attr('stroke-opacity', 0);\n  curlyBracesShape.insert(() => leftCurlyBraceNode, ':first-child');\n  curlyBracesShape.insert(() => rightCurlyBraceNode, ':first-child');\n  curlyBracesShape.attr('class', 'text');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    curlyBracesShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    curlyBracesShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  curlyBracesShape.attr('transform', `translate(${radius - radius / 4}, 0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, curlyBracesShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateCirclePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function curvedTrapezoid<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 80,\n    minHeight = 20;\n  const w = Math.max(minWidth, (bbox.width + (node.padding ?? 0) * 2) * 1.25, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const totalWidth = w,\n    totalHeight = h;\n  const rw = totalWidth - radius;\n  const tw = totalHeight / 4;\n\n  const points = [\n    { x: rw, y: 0 },\n    { x: tw, y: 0 },\n    { x: 0, y: totalHeight / 2 },\n    { x: tw, y: totalHeight },\n    { x: rw, y: totalHeight },\n    ...generateCirclePoints(-rw, -totalHeight / 2, radius, 50, 270, 90),\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(${-w / 2}, ${-h / 2})`);\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n  ].join(' ');\n};\nexport const createOuterCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `M${x + width},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n  ].join(' ');\n};\nexport const createInnerCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(' ');\n};\nexport async function cylinder<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + node.padding, node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + node.padding, node.height ?? 0);\n\n  let cylinder: D3Selection<SVGPathElement> | D3Selection<SVGGElement>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD(0, ry, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, { fill: 'none' }));\n\n    cylinder = shapeSvg.insert(() => innerLine, ':first-child');\n    cylinder = shapeSvg.insert(() => outerNode, ':first-child');\n    cylinder.attr('class', 'basic label-container');\n    if (cssStyles) {\n      cylinder.attr('style', cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry);\n    cylinder = shapeSvg\n      .insert('path', ':first-child')\n      .attr('d', pathData)\n      .attr('class', 'basic label-container')\n      .attr('style', handleUndefinedAttr(cssStyles))\n      .attr('style', nodeStyles);\n  }\n\n  cylinder.attr('label-offset-y', ry);\n  cylinder.attr('transform', `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n\n  updateNodeBounds(node, cylinder);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + (node.padding ?? 0) / 1.5 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n\n    if (\n      rx != 0 &&\n      (Math.abs(x) < (node.width ?? 0) / 2 ||\n        (Math.abs(x) == (node.width ?? 0) / 2 &&\n          Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry))\n    ) {\n      let y = ry * ry * (1 - (x * x) / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n\n      pos.y += y;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function dividedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const rectOffset = h * 0.2;\n\n  const x = -w / 2;\n  const y = -h / 2 - rectOffset / 2;\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const pts = [\n    { x, y: y + rectOffset },\n    { x: -x, y: y + rectOffset },\n    { x: -x, y: -y },\n    { x, y: -y },\n    { x, y },\n    { x: -x, y },\n    { x: -x, y: y + rectOffset },\n  ];\n\n  const poly = rc.polygon(\n    pts.map((p) => [p.x, p.y]),\n    options\n  );\n\n  const polygon = shapeSvg.insert(() => poly, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${x + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))}, ${y + rectOffset + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function doublecircle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getNodeClasses(node));\n  const gap = 5;\n  const outerRadius = bbox.width / 2 + halfPadding + gap;\n  const innerRadius = bbox.width / 2 + halfPadding;\n\n  let circleGroup;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerOptions = userNodeOverrides(node, { roughness: 0.2, strokeWidth: 2.5 });\n\n    const innerOptions = userNodeOverrides(node, { roughness: 0.2, strokeWidth: 1.5 });\n    const outerRoughNode = rc.circle(0, 0, outerRadius * 2, outerOptions);\n    const innerRoughNode = rc.circle(0, 0, innerRadius * 2, innerOptions);\n\n    circleGroup = shapeSvg.insert('g', ':first-child');\n    // circleGroup = circleGroup.insert(() => outerRoughNode, ':first-child');\n    circleGroup\n      .attr('class', handleUndefinedAttr(node.cssClasses))\n      .attr('style', handleUndefinedAttr(cssStyles));\n\n    circleGroup.node()?.appendChild(outerRoughNode);\n    circleGroup.node()?.appendChild(innerRoughNode);\n  } else {\n    circleGroup = shapeSvg.insert('g', ':first-child');\n\n    const outerCircle = circleGroup.insert('circle', ':first-child');\n    const innerCircle = circleGroup.insert('circle');\n    circleGroup.attr('class', 'basic label-container').attr('style', nodeStyles);\n\n    outerCircle\n      .attr('class', 'outer-circle')\n      .attr('style', nodeStyles)\n      .attr('r', outerRadius)\n      .attr('cx', 0)\n      .attr('cy', 0);\n\n    innerCircle\n      .attr('class', 'inner-circle')\n      .attr('style', nodeStyles)\n      .attr('r', innerRadius)\n      .attr('cx', 0)\n      .attr('cy', 0);\n  }\n\n  updateNodeBounds(node, circleGroup);\n\n  node.intersect = function (point) {\n    log.info('DoubleCircle intersect', node, outerRadius, point);\n    return intersect.circle(node, outerRadius, point);\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function filledCircle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = '';\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const radius = 7;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const { nodeBorder } = themeVariables;\n  const options = userNodeOverrides(node, { fillStyle: 'solid' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n  }\n\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n\n  const filledCircle = shapeSvg.insert(() => circleNode, ':first-child');\n\n  filledCircle.selectAll('path').attr('style', `fill: ${nodeBorder} !important;`);\n\n  if (cssStyles && cssStyles.length > 0 && node.look !== 'handDrawn') {\n    filledCircle.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    filledCircle.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, filledCircle);\n\n  node.intersect = function (point) {\n    log.info('filledCircle intersect', node, { radius, point });\n    const pos = intersect.circle(node, radius, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { createPathFromPoints } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function flippedTriangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n\n  const tw = w + bbox.height;\n  const points = [\n    { x: 0, y: -h },\n    { x: tw, y: -h },\n    { x: tw / 2, y: 0 },\n  ];\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n\n  const flippedTriangle = shapeSvg\n    .insert(() => roughNode, ':first-child')\n    .attr('transform', `translate(${-h / 2}, ${h / 2})`);\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    flippedTriangle.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    flippedTriangle.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, flippedTriangle);\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-h / 2 + (node.padding ?? 0) / 2 + (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  node.intersect = function (point) {\n    log.info('Triangle intersect', node, points, point);\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function forkJoin<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { dir, config: { state, themeVariables } }: ShapeRenderOptions\n) {\n  const { nodeStyles } = styles2String(node);\n  node.label = '';\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n\n  const { cssStyles } = node;\n  let width = Math.max(70, node?.width ?? 0);\n  let height = Math.max(10, node?.height ?? 0);\n\n  if (dir === 'LR') {\n    width = Math.max(10, node?.width ?? 0);\n    height = Math.max(70, node?.height ?? 0);\n  }\n\n  const x = (-1 * width) / 2;\n  const y = (-1 * height) / 2;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    stroke: themeVariables.lineColor,\n    fill: themeVariables.lineColor,\n  });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const roughNode = rc.rectangle(x, y, width, height, options);\n\n  const shape = shapeSvg.insert(() => roughNode, ':first-child');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, shape);\n  const padding = state?.padding ?? 0;\n  if (node.width && node.height) {\n    node.width += padding / 2 || 0;\n    node.height += padding / 2 || 0;\n  }\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateCirclePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function halfRoundedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const minWidth = 80,\n    minHeight = 50;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2, y: -h / 2 },\n    { x: w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints(-w / 2 + radius, 0, radius, 50, 90, 270),\n    { x: w / 2 - radius, y: h / 2 },\n    { x: -w / 2, y: h / 2 },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  // label.attr(\n  //   'transform',\n  //   `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))}, ${-h / 2 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  // );\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    log.info('Pill intersect', node, { radius, point });\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport const createHexagonPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  m: number\n): string => {\n  return [\n    `M${x + m},${y}`,\n    `L${x + width - m},${y}`,\n    `L${x + width},${y - height / 2}`,\n    `L${x + width - m},${y - height}`,\n    `L${x + m},${y - height}`,\n    `L${x},${y - height / 2}`,\n    'Z',\n  ].join(' ');\n};\n\nexport async function hexagon<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n\n  let polygon: D3Selection<SVGGElement> | Awaited<ReturnType<typeof insertPolygonShape>>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createHexagonPathD(0, 0, w, h, m);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function hourglass<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = '';\n  node.labelStyle = labelStyles;\n  const { shapeSvg } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = Math.max(30, node?.width ?? 0);\n  const h = Math.max(30, node?.height ?? 0);\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: 0, y: h },\n    { x: w, y: h },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(${-w / 2}, ${-h / 2})`);\n\n  updateNodeBounds(node, polygon);\n\n  // label.attr('transform', `translate(${-bbox.width / 2}, ${(h/2)})`); // To transform text below hourglass shape\n\n  node.intersect = function (point) {\n    log.info('Pill intersect', node, { points });\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function icon<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'icon-shape default');\n\n  const topLabel = node.pos === 't';\n\n  const height = iconSize;\n  const width = iconSize;\n  const { nodeBorder } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  const x = -width / 2;\n  const y = -height / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { stroke: 'none', fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const iconNode = rc.rectangle(x, y, width, height, options);\n\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.icon) {\n    const iconElem = shapeSvg.append('g');\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n    const iconBBox = iconElem.node()!.getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      'transform',\n      `translate(${-iconWidth / 2 - iconX},${\n        topLabel\n          ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n          : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n      })`\n    );\n    iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function iconCircle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'icon-shape default');\n\n  const padding = 20;\n  const labelPadding = node.label ? 8 : 0;\n\n  const topLabel = node.pos === 't';\n\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const fill = stylesMap.get('fill');\n  options.stroke = fill ?? mainBkg;\n\n  const iconElem = shapeSvg.append('g');\n  if (node.icon) {\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n  }\n  const iconBBox = iconElem.node()!.getBBox();\n  const iconWidth = iconBBox.width;\n  const iconHeight = iconBBox.height;\n  const iconX = iconBBox.x;\n  const iconY = iconBBox.y;\n\n  const diameter = Math.max(iconWidth, iconHeight) * Math.SQRT2 + padding * 2;\n  const iconNode = rc.circle(0, 0, diameter, options);\n\n  const outerWidth = Math.max(diameter, bbox.width);\n  const outerHeight = diameter + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n  iconElem.attr(\n    'transform',\n    `translate(${-iconWidth / 2 - iconX},${\n      topLabel\n        ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n        : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n    })`\n  );\n  iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    const pos = intersect.rect(node, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function iconRounded<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, halfPadding, label } = await labelHelper(\n    parent,\n    node,\n    'icon-shape default'\n  );\n\n  const topLabel = node.pos === 't';\n\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  const x = -width / 2;\n  const y = -height / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const fill = stylesMap.get('fill');\n  options.stroke = fill ?? mainBkg;\n\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 5), options);\n\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child').attr('class', 'icon-shape2');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.icon) {\n    const iconElem = shapeSvg.append('g');\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n    const iconBBox = iconElem.node()!.getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      'transform',\n      `translate(${-iconWidth / 2 - iconX},${\n        topLabel\n          ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n          : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n      })`\n    );\n    iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function iconSquare<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, halfPadding, label } = await labelHelper(\n    parent,\n    node,\n    'icon-shape default'\n  );\n\n  const topLabel = node.pos === 't';\n\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  const x = -width / 2;\n  const y = -height / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const fill = stylesMap.get('fill');\n  options.stroke = fill ?? mainBkg;\n\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 0.1), options);\n\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.icon) {\n    const iconElem = shapeSvg.append('g');\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n    const iconBBox = iconElem.node()!.getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      'transform',\n      `translate(${-iconWidth / 2 - iconX},${\n        topLabel\n          ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n          : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n      })`\n    );\n    iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function imageSquare<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { flowchart } }: ShapeRenderOptions\n) {\n  const img = new Image();\n  img.src = node?.img ?? '';\n  await img.decode();\n\n  const imageNaturalWidth = Number(img.naturalWidth.toString().replace('px', ''));\n  const imageNaturalHeight = Number(img.naturalHeight.toString().replace('px', ''));\n  node.imageAspectRatio = imageNaturalWidth / imageNaturalHeight;\n\n  const { labelStyles } = styles2String(node);\n\n  node.labelStyle = labelStyles;\n\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.defaultWidth = flowchart?.wrappingWidth;\n\n  const imageRawWidth = Math.max(\n    node.label ? (defaultWidth ?? 0) : 0,\n    node?.assetWidth ?? imageNaturalWidth\n  );\n\n  const imageWidth =\n    node.constraint === 'on'\n      ? node?.assetHeight\n        ? node.assetHeight * node.imageAspectRatio\n        : imageRawWidth\n      : imageRawWidth;\n\n  const imageHeight =\n    node.constraint === 'on'\n      ? imageWidth / node.imageAspectRatio\n      : (node?.assetHeight ?? imageNaturalHeight);\n  node.width = Math.max(imageWidth, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'image-shape default');\n\n  const topLabel = node.pos === 't';\n\n  const x = -imageWidth / 2;\n  const y = -imageHeight / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const imageNode = rc.rectangle(x, y, imageWidth, imageHeight, options);\n\n  const outerWidth = Math.max(imageWidth, bbox.width);\n  const outerHeight = imageHeight + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'none',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => imageNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.img) {\n    const image = shapeSvg.append('image');\n\n    // Set the image attributes\n    image.attr('href', node.img);\n    image.attr('width', imageWidth);\n    image.attr('height', imageHeight);\n    image.attr('preserveAspectRatio', 'none');\n\n    image.attr(\n      'transform',\n      `translate(${-imageWidth / 2},${topLabel ? outerHeight / 2 - imageHeight : -outerHeight / 2})`\n    );\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel\n        ? -imageHeight / 2 - bbox.height / 2 - labelPadding / 2\n        : imageHeight / 2 - bbox.height / 2 + labelPadding / 2\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + imageWidth / 2, y: dy + nodeHeight / 2 },\n        { x: dx - imageWidth / 2, y: dy + nodeHeight / 2 },\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 + imageHeight },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\n// export const createInvertedTrapezoidPathD = (\n//   x: number,\n//   y: number,\n//   width: number,\n//   height: number\n// ): string => {\n//   return [\n//     `M${x + height / 6},${y}`,\n//     `L${x + width - height / 6},${y}`,\n//     `L${x + width + (2 * height) / 6},${y - height}`,\n//     `L${x - (2 * height) / 6},${y - height}`,\n//     'Z',\n//   ].join(' ');\n// };\n\nexport async function inv_trapezoid<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w + (3 * h) / 6, y: -h },\n    { x: (-3 * h) / 6, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    // const pathData = createInvertedTrapezoidPathD(0, 0, w, h);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node, RectOptions } from '../../types.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { userNodeOverrides, styles2String } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function drawRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  options: RectOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  // console.log('IPI labelStyles:', labelStyles);\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const totalWidth = Math.max(bbox.width + options.labelPaddingX * 2, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + options.labelPaddingY * 2, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n\n  // log.info('IPI node = ', node);\n\n  let rect;\n  let { rx, ry } = node;\n  const { cssStyles } = node;\n\n  //use options rx, ry overrides if present\n  if (options?.rx && options.ry) {\n    rx = options.rx;\n    ry = options.ry;\n  }\n\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n\n    const roughNode =\n      rx || ry\n        ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options)\n        : rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n    rect = shapeSvg.insert(() => roughNode, ':first-child');\n    rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n  } else {\n    rect = shapeSvg.insert('rect', ':first-child');\n\n    rect\n      .attr('class', 'basic label-container')\n      .attr('style', nodeStyles)\n      .attr('rx', handleUndefinedAttr(rx))\n      .attr('ry', handleUndefinedAttr(ry))\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', totalWidth)\n      .attr('height', totalHeight);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport { drawRect } from './drawRect.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function roundedRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: '',\n    labelPaddingX: (node?.padding || 0) * 1,\n    labelPaddingY: (node?.padding || 0) * 1,\n  } as RectOptions;\n\n  return drawRect(parent, node, options);\n}\n\nexport async function labelRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'label');\n\n  // log.trace('Classes = ', node.class);\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // Hide the rect we are only after the label\n  const totalWidth = 0.1;\n  const totalHeight = 0.1;\n  rect.attr('width', totalWidth).attr('height', totalHeight);\n  shapeSvg.attr('class', 'label edgeLabel');\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  // if (node.props) {\n  //   const propKeys = new Set(Object.keys(node.props));\n  //   if (node.props.borders) {\n  //     applyNodePropertyBorders(rect, node.borders, totalWidth, totalHeight);\n  //     propKeys.delete('borders');\n  //   }\n  //   propKeys.forEach((propKey) => {\n  //     log.warn(`Unknown node property ${propKey}`);\n  //   });\n  // }\n\n  updateNodeBounds(node, rect);\n  // node.width = 1;\n  // node.height = 1;\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function lean_left<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [\n    { x: 0, y: 0 },\n    { x: w + (3 * h) / 6, y: 0 },\n    { x: w, y: -h },\n    { x: -(3 * h) / 6, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    // const pathData = createLeanLeftPathD(0, 0, w, h);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function lean_right<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [\n    { x: (-3 * h) / 6, y: 0 },\n    { x: w, y: 0 },\n    { x: w + (3 * h) / 6, y: -h },\n    { x: 0, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport { createPathFromPoints } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function lightningBolt<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = '';\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const { cssStyles } = node;\n  const width = Math.max(35, node?.width ?? 0);\n  const height = Math.max(35, node?.height ?? 0);\n  const gap = 7;\n\n  const points = [\n    { x: width, y: 0 },\n    { x: 0, y: height + gap / 2 },\n    { x: width - 2 * gap, y: height + gap / 2 },\n    { x: 0, y: 2 * height },\n    { x: width, y: height - gap / 2 },\n    { x: 2 * gap, y: height - gap / 2 },\n  ];\n\n  // @ts-expect-error shapeSvg d3 class is incorrect?\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const linePath = createPathFromPoints(points);\n  const lineNode = rc.path(linePath, options);\n\n  const lightningBolt = shapeSvg.insert(() => lineNode, ':first-child');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    lightningBolt.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    lightningBolt.selectAll('path').attr('style', nodeStyles);\n  }\n\n  lightningBolt.attr('transform', `translate(-${width / 2},${-height})`);\n\n  updateNodeBounds(node, lightningBolt);\n\n  node.intersect = function (point) {\n    log.info('lightningBolt intersect', node, point);\n    const pos = intersect.polygon(node, points, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number,\n  outerOffset: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n    `M${x},${y + ry + outerOffset}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n  ].join(' ');\n};\nexport const createOuterCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number,\n  outerOffset: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `M${x + width},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n    `M${x},${y + ry + outerOffset}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n  ].join(' ');\n};\nexport const createInnerCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(' ');\n};\nexport async function linedCylinder<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + (node.padding ?? 0), node.height ?? 0);\n  const outerOffset = h * 0.1; // 10% of height\n\n  let cylinder: typeof shapeSvg | D3Selection<SVGPathElement>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry, outerOffset);\n    const innerPathData = createInnerCylinderPathD(0, ry, w, h, rx, ry);\n    const options = userNodeOverrides(node, {});\n\n    const outerNode = rc.path(outerPathData, options);\n    const innerLine = rc.path(innerPathData, options);\n\n    const innerLineEl = shapeSvg.insert(() => innerLine, ':first-child');\n    innerLineEl.attr('class', 'line');\n    cylinder = shapeSvg.insert(() => outerNode, ':first-child');\n    cylinder.attr('class', 'basic label-container');\n    if (cssStyles) {\n      cylinder.attr('style', cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry, outerOffset);\n    cylinder = shapeSvg\n      .insert('path', ':first-child')\n      .attr('d', pathData)\n      .attr('class', 'basic label-container')\n      .attr('style', handleUndefinedAttr(cssStyles))\n      .attr('style', nodeStyles);\n  }\n\n  // find label and move it down\n  cylinder.attr('label-offset-y', ry);\n  cylinder.attr('transform', `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n\n  updateNodeBounds(node, cylinder);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + ry - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n\n    if (\n      rx != 0 &&\n      (Math.abs(x) < (node.width ?? 0) / 2 ||\n        (Math.abs(x) == (node.width ?? 0) / 2 &&\n          Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry))\n    ) {\n      let y = ry * ry * (1 - (x * x) / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n\n      pos.y += y;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  generateFullSineWavePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function linedWaveEdgedRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2 - (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - (w / 2) * 0.1, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - (w / 2) * 0.1,\n      finalH / 2,\n      w / 2 + (w / 2) * 0.1,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n    { x: w / 2 + (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2, y: -finalH / 2 },\n    { x: -w / 2, y: (finalH / 2) * 1.1 },\n    { x: -w / 2, y: -finalH / 2 },\n  ];\n\n  const poly = rc.polygon(\n    points.map((p) => [p.x, p.y]),\n    options\n  );\n\n  const waveEdgeRect = shapeSvg.insert(() => poly, ':first-child');\n\n  waveEdgeRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  waveEdgeRect.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) + ((w / 2) * 0.1) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds, createPathFromPoints } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function multiRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y + rectOffset },\n    { x: x - rectOffset, y: y + h + rectOffset },\n    { x: x + w - rectOffset, y: y + h + rectOffset },\n    { x: x + w - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y + h - rectOffset },\n    { x: x + w + rectOffset, y: y + h - rectOffset },\n    { x: x + w + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y },\n    { x, y },\n    { x, y: y + rectOffset },\n  ];\n\n  const innerPathPoints = [\n    { x, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y },\n    { x, y },\n  ];\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, { ...options, fill: 'none' });\n\n  const multiRect = shapeSvg.insert(() => innerNode, ':first-child');\n  multiRect.insert(() => outerNode, ':first-child');\n\n  multiRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    multiRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    multiRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, multiRect);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateFullSineWavePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function multiWaveEdgedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const x = -w / 2;\n  const y = -finalH / 2;\n  const rectOffset = 5;\n\n  const { cssStyles } = node;\n\n  const wavePoints = generateFullSineWavePoints(\n    x - rectOffset,\n    y + finalH + rectOffset,\n    x + w - rectOffset,\n    y + finalH + rectOffset,\n    waveAmplitude,\n    0.8\n  );\n\n  const lastWavePoint = wavePoints?.[wavePoints.length - 1];\n\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y + rectOffset },\n    { x: x - rectOffset, y: y + finalH + rectOffset },\n    ...wavePoints,\n    { x: x + w - rectOffset, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - 2 * rectOffset },\n    { x: x + w + rectOffset, y: lastWavePoint.y - 2 * rectOffset },\n    { x: x + w + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y },\n    { x, y },\n    { x, y: y + rectOffset },\n  ];\n\n  const innerPathPoints = [\n    { x, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + rectOffset },\n    { x: x + w - rectOffset, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y },\n    { x, y },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, options);\n\n  const shape = shapeSvg.insert(() => outerNode, ':first-child');\n  shape.insert(() => innerNode);\n\n  shape.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  shape.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, shape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { getNodeClasses, labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\nimport { getConfig } from '../../../config.js';\n\nexport async function note<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const useHtmlLabels = node.useHtmlLabels || getConfig().flowchart?.htmlLabels !== false;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const totalHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const { cssStyles } = node;\n\n  // add the rect\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: themeVariables.noteBkgColor,\n    stroke: themeVariables.noteBorderColor,\n  });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const noteShapeNode = rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n  const rect = shapeSvg.insert(() => noteShapeNode, ':first-child');\n  rect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport const createDecisionBoxPathD = (x: number, y: number, size: number): string => {\n  return [\n    `M${x + size / 2},${y}`,\n    `L${x + size},${y - size / 2}`,\n    `L${x + size / 2},${y - size}`,\n    `L${x},${y - size / 2}`,\n    'Z',\n  ].join(' ');\n};\n\nexport async function question<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n\n  const points = [\n    { x: s / 2, y: 0 },\n    { x: s, y: -s / 2 },\n    { x: s / 2, y: -s },\n    { x: 0, y: -s / 2 },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createDecisionBoxPathD(0, 0, s);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-s / 2}, ${s / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, s, s, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    log.debug(\n      'APA12 Intersect called SPLIT\\npoint:',\n      point,\n      '\\nnode:\\n',\n      node,\n      '\\nres:',\n      intersect.polygon(node, points, point)\n    );\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function rect_left_inv_arrow<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n\n  const x = -w / 2;\n  const y = -h / 2;\n  const notch = y / 2;\n\n  const points = [\n    { x: x + notch, y },\n    { x: x, y: 0 },\n    { x: x + notch, y: -y },\n    { x: -x, y: -y },\n    { x: -x, y },\n  ];\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => roughNode, ':first-child');\n\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', cssStyles);\n  }\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(${-notch / 2},0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-notch / 2 - bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node } from '../../types.js';\nimport { select } from 'd3';\nimport { evaluate } from '../../../diagrams/common/common.js';\nimport { updateNodeBounds } from './util.js';\nimport createLabel from '../createLabel.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { log } from '../../../logger.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function rectWithTitle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  let classes;\n  if (!node.cssClasses) {\n    classes = 'node default';\n  } else {\n    classes = 'node ' + node.cssClasses;\n  }\n\n  // Add outer g element\n  const shapeSvg = parent\n    // @ts-ignore - d3 typings are not correct\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the title label and insert it after the rect\n  const g = shapeSvg.insert('g');\n\n  const label = shapeSvg.insert('g').attr('class', 'label').attr('style', nodeStyles);\n\n  const description = node.description;\n\n  const title = node.label;\n\n  const text = label.node()!.appendChild(await createLabel(title, node.labelStyle, true, true));\n  let bbox = { width: 0, height: 0 };\n  if (evaluate(getConfig()?.flowchart?.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  log.info('Text 2', description);\n  const textRows = description || [];\n  const titleBox = text.getBBox();\n  const descr = label\n    .node()!\n    .appendChild(\n      await createLabel(\n        textRows.join ? textRows.join('<br/>') : textRows,\n        node.labelStyle,\n        true,\n        true\n      )\n    );\n\n  //if (evaluate(getConfig()?.flowchart?.htmlLabels)) {\n  const div = descr.children[0];\n  const dv = select(descr);\n  bbox = div.getBoundingClientRect();\n  dv.attr('width', bbox.width);\n  dv.attr('height', bbox.height);\n  // }\n\n  const halfPadding = (node.padding || 0) / 2;\n  select(descr).attr(\n    'transform',\n    'translate( ' +\n      (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) +\n      ', ' +\n      (titleBox.height + halfPadding + 5) +\n      ')'\n  );\n  select(text).attr(\n    'transform',\n    'translate( ' +\n      (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) +\n      ', ' +\n      0 +\n      ')'\n  );\n  // Get the size of the label\n\n  // Bounding box for title and text\n  bbox = label.node()!.getBBox();\n\n  // Center the label\n  label.attr(\n    'transform',\n    'translate(' + -bbox.width / 2 + ', ' + (-bbox.height / 2 - halfPadding + 3) + ')'\n  );\n\n  const totalWidth = bbox.width + (node.padding || 0);\n  const totalHeight = bbox.height + (node.padding || 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  let rect;\n  let innerLine;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore No typings for rough\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.path(\n      createRoundedRectPathD(x, y, totalWidth, totalHeight, node.rx || 0),\n      options\n    );\n\n    const roughLine = rc.line(\n      -bbox.width / 2 - halfPadding,\n      -bbox.height / 2 - halfPadding + titleBox.height + halfPadding,\n      bbox.width / 2 + halfPadding,\n      -bbox.height / 2 - halfPadding + titleBox.height + halfPadding,\n      options\n    );\n\n    innerLine = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughLine;\n    }, ':first-child');\n    rect = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughNode;\n    }, ':first-child');\n  } else {\n    rect = g.insert('rect', ':first-child');\n    innerLine = g.insert('line');\n    rect\n      .attr('class', 'outer title-state')\n      .attr('style', nodeStyles)\n      .attr('x', -bbox.width / 2 - halfPadding)\n      .attr('y', -bbox.height / 2 - halfPadding)\n      .attr('width', bbox.width + (node.padding || 0))\n      .attr('height', bbox.height + (node.padding || 0));\n\n    innerLine\n      .attr('class', 'divider')\n      .attr('x1', -bbox.width / 2 - halfPadding)\n      .attr('x2', bbox.width / 2 + halfPadding)\n      .attr('y1', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding)\n      .attr('y2', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n  }\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport type { D3Selection } from '../../../types.js';\nimport { drawRect } from './drawRect.js';\n\nexport async function roundedRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: '',\n    labelPaddingX: (node?.padding || 0) * 1,\n    labelPaddingY: (node?.padding || 0) * 1,\n  } as RectOptions;\n\n  return drawRect(parent, node, options);\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function shadedProcess<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = node?.padding ?? 0;\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x, y },\n    { x: x + w + 8, y },\n    { x: x + w + 8, y: y + h },\n    { x: x - 8, y: y + h },\n    { x: x - 8, y: y },\n    { x, y },\n    { x, y: y + h },\n  ];\n\n  const roughNode = rc.polygon(\n    points.map((p) => [p.x, p.y]),\n    options\n  );\n\n  const rect = shapeSvg.insert(() => roughNode, ':first-child');\n\n  rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + 4 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function slopedRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x, y },\n    { x, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y - h / 2 },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(0, ${h / 4})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))}, ${-h / 4 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport type { D3Selection } from '../../../types.js';\nimport { drawRect } from './drawRect.js';\n\nexport async function squareRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const options = {\n    rx: 0,\n    ry: 0,\n    classes: '',\n    labelPaddingX: (node?.padding || 0) * 2,\n    labelPaddingY: (node?.padding || 0) * 1,\n  } as RectOptions;\n  return drawRect(parent, node, options);\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createStadiumPathD = (\n  x: number,\n  y: number,\n  totalWidth: number,\n  totalHeight: number\n) => {\n  const radius = totalHeight / 2;\n  return [\n    'M',\n    x + radius,\n    y, // Move to the start of the top-left arc\n    'H',\n    x + totalWidth - radius, // Draw horizontal line to the start of the top-right arc\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth,\n    y + radius, // Draw top-right arc\n    'H',\n    x, // Draw horizontal line to the start of the bottom-right arc\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth - radius,\n    y + totalHeight, // Draw bottom-right arc\n    'H',\n    x + radius, // Draw horizontal line to the start of the bottom-left arc\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x,\n    y + radius, // Draw bottom-left arc\n    'Z', // Close the path\n  ].join(' ');\n};\n\nexport async function stadium<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n\n  let rect;\n  const { cssStyles } = node;\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n\n    const pathData = createRoundedRectPathD(-w / 2, -h / 2, w, h, h / 2);\n    const roughNode = rc.path(pathData, options);\n\n    rect = shapeSvg.insert(() => roughNode, ':first-child');\n    rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n  } else {\n    rect = shapeSvg.insert('rect', ':first-child');\n\n    rect\n      .attr('class', 'basic label-container')\n      .attr('style', nodeStyles)\n      .attr('rx', h / 2)\n      .attr('ry', h / 2)\n      .attr('x', -w / 2)\n      .attr('y', -h / 2)\n      .attr('width', w)\n      .attr('height', h);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport type { D3Selection } from '../../../types.js';\nimport { drawRect } from './drawRect.js';\n\nexport async function state<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: 'flowchart-node',\n  } as RectOptions;\n  return drawRect(parent, node, options);\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function stateEnd<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { cssStyles } = node;\n  const { lineColor, stateBorder, nodeBorder } = themeVariables;\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const roughNode = rc.circle(0, 0, 14, {\n    ...options,\n    stroke: lineColor,\n    strokeWidth: 2,\n  });\n  const innerFill = stateBorder ?? nodeBorder;\n  const roughInnerNode = rc.circle(0, 0, 5, {\n    ...options,\n    fill: innerFill,\n    stroke: innerFill,\n    strokeWidth: 2,\n    fillStyle: 'solid',\n  });\n  const circle = shapeSvg.insert(() => roughNode, ':first-child');\n  circle.insert(() => roughInnerNode);\n\n  if (cssStyles) {\n    circle.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles) {\n    circle.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { solidStateFill } from './handDrawnShapeStyles.js';\nimport { updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function stateStart<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { lineColor } = themeVariables;\n\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  let circle: D3Selection<SVGCircleElement> | D3Selection<SVGGElement>;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const roughNode = rc.circle(0, 0, 14, solidStateFill(lineColor));\n    circle = shapeSvg.insert(() => roughNode);\n    // center the circle around its coordinate\n    circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n  } else {\n    circle = shapeSvg.insert('circle', ':first-child');\n    // center the circle around its coordinate\n    circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n  }\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createSubroutinePathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number\n): string => {\n  const offset = 8;\n  return [\n    `M${x - offset},${y}`,\n    `H${x + width + offset}`,\n    `V${y + height}`,\n    `H${x - offset}`,\n    `V${y}`,\n    'M',\n    x,\n    y,\n    'H',\n    x + width,\n    'V',\n    y + height,\n    'H',\n    x,\n    'Z',\n  ].join(' ');\n};\n\nexport async function subroutine<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = (node?.padding || 0) / 2;\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n    { x: 0, y: 0 },\n    { x: -8, y: 0 },\n    { x: w + 8, y: 0 },\n    { x: w + 8, y: -h },\n    { x: -8, y: -h },\n    { x: -8, y: 0 },\n  ];\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n\n    const roughNode = rc.rectangle(x - 8, y, w + 16, h, options);\n    const l1 = rc.line(x, y, x, y + h, options);\n    const l2 = rc.line(x + w, y, x + w, y + h, options);\n\n    shapeSvg.insert(() => l1, ':first-child');\n    shapeSvg.insert(() => l2, ':first-child');\n    const rect = shapeSvg.insert(() => roughNode, ':first-child');\n    const { cssStyles } = node;\n    rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n    updateNodeBounds(node, rect);\n  } else {\n    const el = insertPolygonShape(shapeSvg, w, h, points);\n    if (nodeStyles) {\n      el.attr('style', nodeStyles);\n    }\n    updateNodeBounds(node, el);\n  }\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds, createPathFromPoints } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function taggedRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const tagWidth = 0.2 * h;\n  const tagHeight = 0.2 * h;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  const rectPoints = [\n    { x: x - tagWidth / 2, y },\n    { x: x + w + tagWidth / 2, y },\n    { x: x + w + tagWidth / 2, y: y + h },\n    { x: x - tagWidth / 2, y: y + h },\n  ];\n\n  const tagPoints = [\n    { x: x + w - tagWidth / 2, y: y + h },\n    { x: x + w + tagWidth / 2, y: y + h },\n    { x: x + w + tagWidth / 2, y: y + h - tagHeight },\n  ];\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectNode = rc.path(rectPath, options);\n\n  const tagPath = createPathFromPoints(tagPoints);\n  const tagNode = rc.path(tagPath, { ...options, fillStyle: 'solid' });\n\n  const taggedRect = shapeSvg.insert(() => tagNode, ':first-child');\n  taggedRect.insert(() => rectNode, ':first-child');\n\n  taggedRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    taggedRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    taggedRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, taggedRect);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  generateFullSineWavePoints,\n  createPathFromPoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function taggedWaveEdgedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const tagWidth = 0.2 * w;\n  const tagHeight = 0.2 * h;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2 - (w / 2) * 0.1, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - (w / 2) * 0.1,\n      finalH / 2,\n      w / 2 + (w / 2) * 0.1,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n\n    { x: w / 2 + (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - (w / 2) * 0.1, y: -finalH / 2 },\n  ];\n\n  const x = -w / 2 + (w / 2) * 0.1;\n  const y = -finalH / 2 - tagHeight * 0.4;\n\n  const tagPoints = [\n    { x: x + w - tagWidth, y: (y + h) * 1.4 },\n    { x: x + w, y: y + h - tagHeight },\n    { x: x + w, y: (y + h) * 0.9 },\n    ...generateFullSineWavePoints(\n      x + w,\n      (y + h) * 1.3,\n      x + w - tagWidth,\n      (y + h) * 1.5,\n      -h * 0.03,\n      0.5\n    ),\n  ];\n\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n\n  const taggedWaveEdgeRectPath = createPathFromPoints(tagPoints);\n  const taggedWaveEdgeRectNode = rc.path(taggedWaveEdgeRectPath, {\n    ...options,\n    fillStyle: 'solid',\n  });\n\n  const waveEdgeRect = shapeSvg.insert(() => taggedWaveEdgeRectNode, ':first-child');\n  waveEdgeRect.insert(() => waveEdgeRectNode, ':first-child');\n\n  waveEdgeRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  waveEdgeRect.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function text<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const totalWidth = Math.max(bbox.width + node.padding, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + node.padding, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  rect\n    .attr('class', 'text')\n    .attr('style', nodeStyles)\n    .attr('rx', 0)\n    .attr('ry', 0)\n    .attr('x', x)\n    .attr('y', y)\n    .attr('width', totalWidth)\n    .attr('height', totalHeight);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return `M${x},${y}\n    a${rx},${ry} 0,0,1 ${0},${-height}\n    l${width},${0}\n    a${rx},${ry} 0,0,1 ${0},${height}\n    M${width},${-height}\n    a${rx},${ry} 0,0,0 ${0},${height}\n    l${-width},${0}`;\n};\n\nexport const createOuterCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [\n    `M${x},${y}`,\n    `M${x + width},${y}`,\n    `a${rx},${ry} 0,0,0 ${0},${-height}`,\n    `l${-width},0`,\n    `a${rx},${ry} 0,0,0 ${0},${height}`,\n    `l${width},0`,\n  ].join(' ');\n};\nexport const createInnerCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [`M${x + width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 0,${height}`].join(' ');\n};\n\nexport async function tiltedCylinder<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getNodeClasses(node)\n  );\n  const labelPadding = node.look === 'neo' ? halfPadding * 2 : halfPadding;\n  const h = bbox.height + labelPadding;\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n  const w = bbox.width + rx + labelPadding;\n  const { cssStyles } = node;\n\n  let cylinder: D3Selection<SVGGElement> | D3Selection<SVGPathElement>;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD(0, 0, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, { fill: 'none' }));\n    cylinder = shapeSvg.insert(() => innerLine, ':first-child');\n    cylinder = shapeSvg.insert(() => outerNode, ':first-child');\n    cylinder.attr('class', 'basic label-container');\n    if (cssStyles) {\n      cylinder.attr('style', cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry);\n    cylinder = shapeSvg\n      .insert('path', ':first-child')\n      .attr('d', pathData)\n      .attr('class', 'basic label-container')\n      .attr('style', handleUndefinedAttr(cssStyles))\n      .attr('style', nodeStyles);\n    cylinder.attr('class', 'basic label-container');\n\n    if (cssStyles) {\n      cylinder.selectAll('path').attr('style', cssStyles);\n    }\n\n    if (nodeStyles) {\n      cylinder.selectAll('path').attr('style', nodeStyles);\n    }\n  }\n\n  cylinder.attr('label-offset-x', rx);\n  cylinder.attr('transform', `translate(${-w / 2}, ${h / 2} )`);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - rx - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, cylinder);\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const y = pos.y - (node.y ?? 0);\n\n    if (\n      ry != 0 &&\n      (Math.abs(y) < (node.height ?? 0) / 2 ||\n        (Math.abs(y) == (node.height ?? 0) / 2 &&\n          Math.abs(pos.x - (node.x ?? 0)) > (node.width ?? 0) / 2 - rx))\n    ) {\n      let x = rx * rx * (1 - (y * y) / (ry * ry));\n      if (x != 0) {\n        x = Math.sqrt(Math.abs(x));\n      }\n      x = rx - x;\n      if (point.x - (node.x ?? 0) > 0) {\n        x = -x;\n      }\n\n      pos.x += x;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\n// export const createTrapezoidPathD = (\n//   x: number,\n//   y: number,\n//   width: number,\n//   height: number\n// ): string => {\n//   return [\n//     `M${x - (2 * height) / 6},${y}`,\n//     `L${x + width + (2 * height) / 6},${y}`,\n//     `L${x + width - height / 6},${y - height}`,\n//     `L${x + height / 6},${y - height}`,\n//     'Z',\n//   ].join(' ');\n// };\n\nexport async function trapezoid<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (-3 * h) / 6, y: 0 },\n    { x: w + (3 * h) / 6, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function trapezoidalPentagon<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 60,\n    minHeight = 20;\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: (-w / 2) * 0.8, y: -h / 2 },\n    { x: (w / 2) * 0.8, y: -h / 2 },\n    { x: w / 2, y: (-h / 2) * 0.6 },\n    { x: w / 2, y: h / 2 },\n    { x: -w / 2, y: h / 2 },\n    { x: -w / 2, y: (-h / 2) * 0.6 },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { createPathFromPoints } from './util.js';\nimport { evaluate } from '../../../diagrams/common/common.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function triangle<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const useHtmlLabels = evaluate(getConfig().flowchart?.htmlLabels);\n\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n\n  const tw = w + bbox.height;\n  const points = [\n    { x: 0, y: 0 },\n    { x: tw, y: 0 },\n    { x: tw / 2, y: -h },\n  ];\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg\n    .insert(() => roughNode, ':first-child')\n    .attr('transform', `translate(${-h / 2}, ${h / 2})`);\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${h / 2 - (bbox.height + (node.padding ?? 0) / (useHtmlLabels ? 2 : 1) - (bbox.y - (bbox.top ?? 0)))})`\n  );\n\n  node.intersect = function (point) {\n    log.info('Triangle intersect', node, points, point);\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  generateFullSineWavePoints,\n  createPathFromPoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function waveEdgedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 8;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n\n  // To maintain minimum width\n  const minWidth = 70;\n  const widthDif = minWidth - w;\n  const extraW = widthDif > 0 ? widthDif / 2 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2 - extraW, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - extraW,\n      finalH / 2,\n      w / 2 + extraW,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n    { x: w / 2 + extraW, y: -finalH / 2 },\n    { x: -w / 2 - extraW, y: -finalH / 2 },\n  ];\n\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n\n  const waveEdgeRect = shapeSvg.insert(() => waveEdgeRectNode, ':first-child');\n\n  waveEdgeRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  waveEdgeRect.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateFullSineWavePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function waveRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const minWidth = 100; // Minimum width\n  const minHeight = 50; // Minimum height\n\n  const baseWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const baseHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n\n  const aspectRatio = baseWidth / baseHeight;\n\n  let w = baseWidth;\n  let h = baseHeight;\n\n  if (w > h * aspectRatio) {\n    h = w / aspectRatio;\n  } else {\n    w = h * aspectRatio;\n  }\n\n  w = Math.max(w, minWidth);\n  h = Math.max(h, minHeight);\n\n  const waveAmplitude = Math.min(h * 0.2, h / 4);\n  const finalH = h + waveAmplitude * 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2, y: finalH / 2 },\n    ...generateFullSineWavePoints(-w / 2, finalH / 2, w / 2, finalH / 2, waveAmplitude, 1),\n    { x: w / 2, y: -finalH / 2 },\n    ...generateFullSineWavePoints(w / 2, -finalH / 2, -w / 2, -finalH / 2, waveAmplitude, -1),\n  ];\n\n  const waveRectPath = createPathFromPoints(points);\n  const waveRectNode = rc.path(waveRectPath, options);\n\n  const waveRect = shapeSvg.insert(() => waveRectNode, ':first-child');\n\n  waveRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, waveRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function windowPane<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y - rectOffset },\n    { x: x - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y - rectOffset },\n  ];\n\n  const path = `M${x - rectOffset},${y - rectOffset} L${x + w},${y - rectOffset} L${x + w},${y + h} L${x - rectOffset},${y + h} L${x - rectOffset},${y - rectOffset}\n                M${x - rectOffset},${y} L${x + w},${y}\n                M${x},${y - rectOffset} L${x},${y + h}`;\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const no = rc.path(path, options);\n\n  const windowPane = shapeSvg.insert(() => no, ':first-child');\n  windowPane.attr('transform', `translate(${rectOffset / 2}, ${rectOffset / 2})`);\n\n  windowPane.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    windowPane.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    windowPane.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) + rectOffset / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, windowPane);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { userNodeOverrides, styles2String } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { drawRect } from './drawRect.js';\nimport { getConfig } from '../../../config.js';\nimport type { EntityNode } from '../../../diagrams/er/erTypes.js';\nimport { createText } from '../../createText.js';\nimport { evaluate, parseGenericTypes } from '../../../diagrams/common/common.js';\nimport { select } from 'd3';\nimport { calculateTextWidth } from '../../../utils.js';\nimport type { MermaidConfig } from '../../../config.type.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function erBox<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  // Treat node as entityNode for certain entityNode checks\n  const entityNode = node as unknown as EntityNode;\n  if (entityNode.alias) {\n    node.label = entityNode.alias;\n  }\n\n  // Background shapes are drawn to fill in the background color and cover up the ER diagram edge markers.\n  // Draw background shape once.\n  if (node.look === 'handDrawn') {\n    const { themeVariables } = getConfig();\n    const { background } = themeVariables;\n    const backgroundNode = {\n      ...node,\n      id: node.id + '-background',\n      look: 'default',\n      cssStyles: ['stroke: none', `fill: ${background}`],\n    };\n    await erBox(parent, backgroundNode);\n  }\n\n  const config = getConfig();\n  node.useHtmlLabels = config.htmlLabels;\n  let PADDING = config.er?.diagramPadding ?? 10;\n  let TEXT_PADDING = config.er?.entityPadding ?? 6;\n\n  const { cssStyles } = node;\n  const { labelStyles, nodeStyles } = styles2String(node);\n\n  // Draw rect if no attributes are found\n  if (entityNode.attributes.length === 0 && node.label) {\n    const options = {\n      rx: 0,\n      ry: 0,\n      labelPaddingX: PADDING,\n      labelPaddingY: PADDING * 1.5,\n      classes: '',\n    };\n    // Set minimum width\n    if (\n      calculateTextWidth(node.label, config) + options.labelPaddingX * 2 <\n      config.er!.minEntityWidth!\n    ) {\n      node.width = config.er!.minEntityWidth;\n    }\n    const shapeSvg = await drawRect(parent, node, options);\n\n    // drawRect doesn't center non-htmlLabels correctly as of now, so translate label\n    if (!evaluate(config.htmlLabels)) {\n      const textElement = shapeSvg.select('text');\n      const bbox = (textElement.node() as SVGTextElement)?.getBBox();\n      textElement.attr('transform', `translate(${-bbox.width / 2}, 0)`);\n    }\n    return shapeSvg;\n  }\n\n  if (!config.htmlLabels) {\n    PADDING *= 1.25;\n    TEXT_PADDING *= 1.25;\n  }\n\n  let cssClasses = getNodeClasses(node);\n  if (!cssClasses) {\n    cssClasses = 'node default';\n  }\n\n  const shapeSvg = parent\n    // @ts-ignore Ignore .insert on SVGAElement\n    .insert('g')\n    .attr('class', cssClasses)\n    .attr('id', node.domId || node.id);\n\n  const nameBBox = await addText(shapeSvg, node.label ?? '', config, 0, 0, ['name'], labelStyles);\n  nameBBox.height += TEXT_PADDING;\n  let yOffset = 0;\n  const yOffsets = [];\n  const rows = [];\n  let maxTypeWidth = 0;\n  let maxNameWidth = 0;\n  let maxKeysWidth = 0;\n  let maxCommentWidth = 0;\n  let keysPresent = true;\n  let commentPresent = true;\n  for (const attribute of entityNode.attributes) {\n    const typeBBox = await addText(\n      shapeSvg,\n      attribute.type,\n      config,\n      0,\n      yOffset,\n      ['attribute-type'],\n      labelStyles\n    );\n    maxTypeWidth = Math.max(maxTypeWidth, typeBBox.width + PADDING);\n    const nameBBox = await addText(\n      shapeSvg,\n      attribute.name,\n      config,\n      0,\n      yOffset,\n      ['attribute-name'],\n      labelStyles\n    );\n    maxNameWidth = Math.max(maxNameWidth, nameBBox.width + PADDING);\n    const keysBBox = await addText(\n      shapeSvg,\n      attribute.keys.join(),\n      config,\n      0,\n      yOffset,\n      ['attribute-keys'],\n      labelStyles\n    );\n    maxKeysWidth = Math.max(maxKeysWidth, keysBBox.width + PADDING);\n    const commentBBox = await addText(\n      shapeSvg,\n      attribute.comment,\n      config,\n      0,\n      yOffset,\n      ['attribute-comment'],\n      labelStyles\n    );\n    maxCommentWidth = Math.max(maxCommentWidth, commentBBox.width + PADDING);\n\n    const rowHeight =\n      Math.max(typeBBox.height, nameBBox.height, keysBBox.height, commentBBox.height) +\n      TEXT_PADDING;\n    rows.push({ yOffset, rowHeight });\n    yOffset += rowHeight;\n  }\n  let totalWidthSections = 4;\n\n  if (maxKeysWidth <= PADDING) {\n    keysPresent = false;\n    maxKeysWidth = 0;\n    totalWidthSections--;\n  }\n  if (maxCommentWidth <= PADDING) {\n    commentPresent = false;\n    maxCommentWidth = 0;\n    totalWidthSections--;\n  }\n\n  const shapeBBox = shapeSvg.node()!.getBBox();\n  // Add extra padding to attribute components to accommodate for difference in width\n  if (\n    nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth) >\n    0\n  ) {\n    const difference =\n      nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth);\n    maxTypeWidth += difference / totalWidthSections;\n    maxNameWidth += difference / totalWidthSections;\n    if (maxKeysWidth > 0) {\n      maxKeysWidth += difference / totalWidthSections;\n    }\n    if (maxCommentWidth > 0) {\n      maxCommentWidth += difference / totalWidthSections;\n    }\n  }\n\n  const maxWidth = maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth;\n\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  let totalShapeBBoxHeight = 0;\n  if (rows.length > 0) {\n    totalShapeBBoxHeight = rows.reduce((sum, row) => sum + (row?.rowHeight ?? 0), 0);\n  }\n  const w = Math.max(shapeBBox.width + PADDING * 2, node?.width || 0, maxWidth);\n  const h = Math.max((totalShapeBBoxHeight ?? 0) + nameBBox.height, node?.height || 0);\n  const x = -w / 2;\n  const y = -h / 2;\n\n  // Translate attribute text labels\n  shapeSvg.selectAll('g:not(:first-child)').each((_: any, i: number, nodes: any) => {\n    const text = select<any, unknown>(nodes[i]);\n    const transform = text.attr('transform');\n    let translateX = 0;\n    let translateY = 0;\n\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n        if (text.attr('class').includes('attribute-name')) {\n          translateX += maxTypeWidth;\n        } else if (text.attr('class').includes('attribute-keys')) {\n          translateX += maxTypeWidth + maxNameWidth;\n        } else if (text.attr('class').includes('attribute-comment')) {\n          translateX += maxTypeWidth + maxNameWidth + maxKeysWidth;\n        }\n      }\n    }\n\n    text.attr(\n      'transform',\n      `translate(${x + PADDING / 2 + translateX}, ${translateY + y + nameBBox.height + TEXT_PADDING / 2})`\n    );\n  });\n  // Center the name\n  shapeSvg\n    .select('.name')\n    .attr('transform', 'translate(' + -nameBBox.width / 2 + ', ' + (y + TEXT_PADDING / 2) + ')');\n\n  // Draw shape\n  const roughRect = rc.rectangle(x, y, w, h, options);\n  const rect = shapeSvg.insert(() => roughRect, ':first-child').attr('style', cssStyles!.join(''));\n\n  const { themeVariables } = getConfig();\n  const { rowEven, rowOdd, nodeBorder } = themeVariables;\n\n  yOffsets.push(0);\n  // Draw row rects\n  for (const [i, row] of rows.entries()) {\n    const contentRowIndex = i + 1; // Adjusted index to skip the header (name) row\n    const isEven = contentRowIndex % 2 === 0 && row.yOffset !== 0;\n    const roughRect = rc.rectangle(x, nameBBox.height + y + row?.yOffset, w, row?.rowHeight, {\n      ...options,\n      fill: isEven ? rowEven : rowOdd,\n      stroke: nodeBorder,\n    });\n    shapeSvg\n      .insert(() => roughRect, 'g.label')\n      .attr('style', cssStyles!.join(''))\n      .attr('class', `row-rect-${isEven ? 'even' : 'odd'}`);\n  }\n\n  // Draw divider lines\n  // Name line\n  let roughLine = rc.line(x, nameBBox.height + y, w + x, nameBBox.height + y, options);\n  shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  // First line\n  roughLine = rc.line(maxTypeWidth + x, nameBBox.height + y, maxTypeWidth + x, h + y, options);\n  shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  // Second line\n  if (keysPresent) {\n    roughLine = rc.line(\n      maxTypeWidth + maxNameWidth + x,\n      nameBBox.height + y,\n      maxTypeWidth + maxNameWidth + x,\n      h + y,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  }\n  // Third line\n  if (commentPresent) {\n    roughLine = rc.line(\n      maxTypeWidth + maxNameWidth + maxKeysWidth + x,\n      nameBBox.height + y,\n      maxTypeWidth + maxNameWidth + maxKeysWidth + x,\n      h + y,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  }\n\n  // Attribute divider lines\n  for (const yOffset of yOffsets) {\n    roughLine = rc.line(\n      x,\n      nameBBox.height + y + yOffset,\n      w + x,\n      nameBBox.height + y + yOffset,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  }\n\n  updateNodeBounds(node, rect);\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    const allStyle = nodeStyles.split(';');\n    const strokeStyles = allStyle\n      ?.filter((e) => {\n        return e.includes('stroke');\n      })\n      ?.map((s) => `${s}`)\n      .join('; ');\n    shapeSvg.selectAll('path').attr('style', strokeStyles ?? '');\n    shapeSvg.selectAll('.row-rect-even path').attr('style', nodeStyles);\n  }\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n  return shapeSvg;\n}\n\n// Helper function to add label text g with translate position and style\nasync function addText<T extends SVGGraphicsElement>(\n  shapeSvg: D3Selection<T>,\n  labelText: string,\n  config: MermaidConfig,\n  translateX = 0,\n  translateY = 0,\n  classes: string[] = [],\n  style = ''\n) {\n  const label = shapeSvg\n    .insert('g')\n    .attr('class', `label ${classes.join(' ')}`)\n    .attr('transform', `translate(${translateX}, ${translateY})`)\n    .attr('style', style);\n\n  // Return types need to be parsed\n  if (labelText !== parseGenericTypes(labelText)) {\n    labelText = parseGenericTypes(labelText);\n    // Work around\n    labelText = labelText.replaceAll('<', '&lt;').replaceAll('>', '&gt;');\n  }\n\n  const text = label.node()!.appendChild(\n    await createText(\n      label,\n      labelText,\n      {\n        width: calculateTextWidth(labelText, config) + 100,\n        style,\n        useHtmlLabels: config.htmlLabels,\n      },\n      config\n    )\n  );\n  // Undo work around now that text passed through correctly\n  if (labelText.includes('&lt;') || labelText.includes('&gt;')) {\n    let child = text.children[0];\n    child.textContent = child.textContent.replaceAll('&lt;', '<').replaceAll('&gt;', '>');\n    while (child.childNodes[0]) {\n      child = child.childNodes[0];\n      // Replace its text content\n      child.textContent = child.textContent.replaceAll('&lt;', '<').replaceAll('&gt;', '>');\n    }\n  }\n\n  let bbox = text.getBBox();\n  if (evaluate(config.htmlLabels)) {\n    const div = text.children[0];\n    div.style.textAlign = 'start';\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  return bbox;\n}\n", "import { updateNodeBounds } from './util.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport { select } from 'd3';\nimport type { Node } from '../../types.js';\nimport type { ClassNode } from '../../../diagrams/class/classTypes.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport intersect from '../intersect/index.js';\nimport { textHelper } from '../../../diagrams/class/shapeUtil.js';\nimport { evaluate } from '../../../diagrams/common/common.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function classBox<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const config = getConfig();\n  const PADDING = config.class!.padding ?? 12;\n  const GAP = PADDING;\n  const useHtmlLabels = node.useHtmlLabels ?? evaluate(config.htmlLabels) ?? true;\n  // Treat node as classNode\n  const classNode = node as unknown as ClassNode;\n  classNode.annotations = classNode.annotations ?? [];\n  classNode.members = classNode.members ?? [];\n  classNode.methods = classNode.methods ?? [];\n\n  const { shapeSvg, bbox } = await textHelper(parent, node, config, useHtmlLabels, GAP);\n\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n\n  node.cssStyles = classNode.styles || '';\n\n  const styles = classNode.styles?.join(';') || nodeStyles || '';\n\n  if (!node.cssStyles) {\n    node.cssStyles = styles.replaceAll('!important', '').split(';');\n  }\n\n  const renderExtraBox =\n    classNode.members.length === 0 &&\n    classNode.methods.length === 0 &&\n    !config.class?.hideEmptyMembersBox;\n\n  // Setup roughjs\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const w = bbox.width;\n  let h = bbox.height;\n  if (classNode.members.length === 0 && classNode.methods.length === 0) {\n    h += GAP;\n  } else if (classNode.members.length > 0 && classNode.methods.length === 0) {\n    h += GAP * 2;\n  }\n  const x = -w / 2;\n  const y = -h / 2;\n\n  // Create and center rectangle\n  const roughRect = rc.rectangle(\n    x - PADDING,\n    y -\n      PADDING -\n      (renderExtraBox\n        ? PADDING\n        : classNode.members.length === 0 && classNode.methods.length === 0\n          ? -PADDING / 2\n          : 0),\n    w + 2 * PADDING,\n    h +\n      2 * PADDING +\n      (renderExtraBox\n        ? PADDING * 2\n        : classNode.members.length === 0 && classNode.methods.length === 0\n          ? -PADDING\n          : 0),\n    options\n  );\n\n  const rect = shapeSvg.insert(() => roughRect, ':first-child');\n  rect.attr('class', 'basic label-container');\n  const rectBBox = rect.node()!.getBBox();\n\n  // Rect is centered so now adjust labels.\n  // TODO: Fix types\n  shapeSvg.selectAll('.text').each((_: any, i: number, nodes: any) => {\n    const text = select<any, unknown>(nodes[i]);\n    // Get the current transform attribute\n    const transform = text.attr('transform');\n    // Initialize variables for the translation values\n    let translateY = 0;\n    // Check if the transform attribute exists\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateY = parseFloat(translate[2]);\n      }\n    }\n    // Add to the y value\n    let newTranslateY =\n      translateY +\n      y +\n      PADDING -\n      (renderExtraBox\n        ? PADDING\n        : classNode.members.length === 0 && classNode.methods.length === 0\n          ? -PADDING / 2\n          : 0);\n    if (!useHtmlLabels) {\n      // Fix so non html labels are better centered.\n      // BBox of text seems to be slightly different when calculated so we offset\n      newTranslateY -= 4;\n    }\n    let newTranslateX = x;\n    if (\n      text.attr('class').includes('label-group') ||\n      text.attr('class').includes('annotation-group')\n    ) {\n      newTranslateX = -text.node()?.getBBox().width / 2 || 0;\n      shapeSvg.selectAll('text').each(function (_: any, i: number, nodes: any) {\n        if (window.getComputedStyle(nodes[i]).textAnchor === 'middle') {\n          newTranslateX = 0;\n        }\n      });\n    }\n    // Set the updated transform attribute\n    text.attr('transform', `translate(${newTranslateX}, ${newTranslateY})`);\n  });\n\n  // Render divider lines.\n  const annotationGroupHeight =\n    (shapeSvg.select('.annotation-group').node() as SVGGraphicsElement).getBBox().height -\n      (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const labelGroupHeight =\n    (shapeSvg.select('.label-group').node() as SVGGraphicsElement).getBBox().height -\n      (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const membersGroupHeight =\n    (shapeSvg.select('.members-group').node() as SVGGraphicsElement).getBBox().height -\n      (renderExtraBox ? PADDING / 2 : 0) || 0;\n  // First line (under label)\n  if (classNode.members.length > 0 || classNode.methods.length > 0 || renderExtraBox) {\n    const roughLine = rc.line(\n      rectBBox.x,\n      annotationGroupHeight + labelGroupHeight + y + PADDING,\n      rectBBox.x + rectBBox.width,\n      annotationGroupHeight + labelGroupHeight + y + PADDING,\n      options\n    );\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr('class', 'divider').attr('style', styles);\n  }\n\n  // Second line (under members)\n  if (renderExtraBox || classNode.members.length > 0 || classNode.methods.length > 0) {\n    const roughLine = rc.line(\n      rectBBox.x,\n      annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + GAP * 2 + PADDING,\n      rectBBox.x + rectBBox.width,\n      annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + PADDING + GAP * 2,\n      options\n    );\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr('class', 'divider').attr('style', styles);\n  }\n\n  /// Apply styles ///\n  if (classNode.look !== 'handDrawn') {\n    shapeSvg.selectAll('path').attr('style', styles);\n  }\n  // Apply other styles like stroke-width and stroke-dasharray to border (not background of shape)\n  rect.select(':nth-child(2)').attr('style', styles);\n  // Divider lines\n  shapeSvg.selectAll('.divider').select('path').attr('style', styles);\n  // Text elements\n  if (node.labelStyle) {\n    shapeSvg.selectAll('span').attr('style', node.labelStyle);\n  } else {\n    shapeSvg.selectAll('span').attr('style', styles);\n  }\n  // SVG text uses fill not color\n  if (!useHtmlLabels) {\n    // We just want to apply color to the text\n    const colorRegex = RegExp(/color\\s*:\\s*([^;]*)/);\n    const match = colorRegex.exec(styles);\n    if (match) {\n      const colorStyle = match[0].replace('color', 'fill');\n      shapeSvg.selectAll('tspan').attr('style', colorStyle);\n    } else if (labelStyles) {\n      const match = colorRegex.exec(labelStyles);\n      if (match) {\n        const colorStyle = match[0].replace('color', 'fill');\n        shapeSvg.selectAll('tspan').attr('style', colorStyle);\n      }\n    }\n  }\n\n  updateNodeBounds(node, rect);\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { select } from 'd3';\nimport { getConfig } from '../../config.js';\nimport { getNodeClasses } from '../../rendering-util/rendering-elements/shapes/util.js';\nimport { calculateTextWidth, decodeEntities } from '../../utils.js';\nimport type { ClassMember, ClassNode } from './classTypes.js';\nimport { sanitizeText } from '../../diagram-api/diagramAPI.js';\nimport { createText } from '../../rendering-util/createText.js';\nimport { evaluate, hasKatex } from '../common/common.js';\nimport type { Node } from '../../rendering-util/types.js';\nimport type { MermaidConfig } from '../../config.type.js';\nimport type { D3Selection } from '../../types.js';\n\n// Creates the shapeSvg and inserts text\nexport async function textHelper<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: any,\n  config: MermaidConfig,\n  useHtmlLabels: boolean,\n  GAP = config.class!.padding ?? 12\n) {\n  const TEXT_PADDING = !useHtmlLabels ? 3 : 0;\n  const shapeSvg = parent\n    // @ts-ignore: Ignore error for using .insert on SVGAElement\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId || node.id);\n\n  let annotationGroup = null;\n  let labelGroup = null;\n  let membersGroup = null;\n  let methodsGroup = null;\n\n  let annotationGroupHeight = 0;\n  let labelGroupHeight = 0;\n  let membersGroupHeight = 0;\n\n  annotationGroup = shapeSvg.insert('g').attr('class', 'annotation-group text');\n  if (node.annotations.length > 0) {\n    const annotation = node.annotations[0];\n    await addText(annotationGroup, { text: `«${annotation}»` } as unknown as ClassMember, 0);\n\n    const annotationGroupBBox = annotationGroup.node()!.getBBox();\n    annotationGroupHeight = annotationGroupBBox.height;\n  }\n\n  labelGroup = shapeSvg.insert('g').attr('class', 'label-group text');\n  await addText(labelGroup, node, 0, ['font-weight: bolder']);\n  const labelGroupBBox = labelGroup.node()!.getBBox();\n  labelGroupHeight = labelGroupBBox.height;\n\n  membersGroup = shapeSvg.insert('g').attr('class', 'members-group text');\n  let yOffset = 0;\n  for (const member of node.members) {\n    const height = await addText(membersGroup, member, yOffset, [member.parseClassifier()]);\n    yOffset += height + TEXT_PADDING;\n  }\n  membersGroupHeight = membersGroup.node()!.getBBox().height;\n  if (membersGroupHeight <= 0) {\n    membersGroupHeight = GAP / 2;\n  }\n\n  methodsGroup = shapeSvg.insert('g').attr('class', 'methods-group text');\n  let methodsYOffset = 0;\n  for (const method of node.methods) {\n    const height = await addText(methodsGroup, method, methodsYOffset, [method.parseClassifier()]);\n    methodsYOffset += height + TEXT_PADDING;\n  }\n\n  let bbox = shapeSvg.node()!.getBBox();\n\n  // Center annotation\n  if (annotationGroup !== null) {\n    const annotationGroupBBox = annotationGroup.node()!.getBBox();\n    annotationGroup.attr('transform', `translate(${-annotationGroupBBox.width / 2})`);\n  }\n\n  // Adjust label\n  labelGroup.attr('transform', `translate(${-labelGroupBBox.width / 2}, ${annotationGroupHeight})`);\n\n  bbox = shapeSvg.node()!.getBBox();\n\n  membersGroup.attr(\n    'transform',\n    `translate(${0}, ${annotationGroupHeight + labelGroupHeight + GAP * 2})`\n  );\n  bbox = shapeSvg.node()!.getBBox();\n  methodsGroup.attr(\n    'transform',\n    `translate(${0}, ${annotationGroupHeight + labelGroupHeight + (membersGroupHeight ? membersGroupHeight + GAP * 4 : GAP * 2)})`\n  );\n\n  bbox = shapeSvg.node()!.getBBox();\n\n  return { shapeSvg, bbox };\n}\n\n// Modified version of labelHelper() to help create and place text for classes\nasync function addText<T extends SVGGraphicsElement>(\n  parentGroup: D3Selection<T>,\n  node: Node | ClassNode | ClassMember,\n  yOffset: number,\n  styles: string[] = []\n) {\n  const textEl = parentGroup.insert('g').attr('class', 'label').attr('style', styles.join('; '));\n  const config = getConfig();\n  let useHtmlLabels =\n    'useHtmlLabels' in node ? node.useHtmlLabels : (evaluate(config.htmlLabels) ?? true);\n\n  let textContent = '';\n  // Support regular node type (.label) and classNodes (.text)\n  if ('text' in node) {\n    textContent = node.text;\n  } else {\n    textContent = node.label!;\n  }\n\n  // createText() will cause unwanted behavior because of classDiagram syntax so workarounds are needed\n\n  if (!useHtmlLabels && textContent.startsWith('\\\\')) {\n    textContent = textContent.substring(1);\n  }\n\n  if (hasKatex(textContent)) {\n    useHtmlLabels = true;\n  }\n\n  const text = await createText(\n    textEl,\n    sanitizeText(decodeEntities(textContent)),\n    {\n      width: calculateTextWidth(textContent, config) + 50, // Add room for error when splitting text into multiple lines\n      classes: 'markdown-node-label',\n      useHtmlLabels,\n    },\n    config\n  );\n  let bbox;\n  let numberOfLines = 1;\n\n  if (!useHtmlLabels) {\n    // Undo font-weight normal\n    if (styles.includes('font-weight: bolder')) {\n      select(text).selectAll('tspan').attr('font-weight', '');\n    }\n\n    numberOfLines = text.children.length;\n\n    const textChild = text.children[0];\n    if (text.textContent === '' || text.textContent.includes('&gt')) {\n      textChild.textContent =\n        textContent[0] +\n        textContent.substring(1).replaceAll('&gt;', '>').replaceAll('&lt;', '<').trim();\n\n      // Text was improperly removed due to spaces (preserve one space if present)\n      const preserveSpace = textContent[1] === ' ';\n      if (preserveSpace) {\n        textChild.textContent = textChild.textContent[0] + ' ' + textChild.textContent.substring(1);\n      }\n    }\n\n    // To support empty boxes\n    if (textChild.textContent === 'undefined') {\n      textChild.textContent = '';\n    }\n\n    // Get the bounding box after the text update\n    bbox = text.getBBox();\n  } else {\n    const div = text.children[0];\n    const dv = select(text);\n\n    numberOfLines = div.innerHTML.split('<br>').length;\n    // Katex math support\n    if (div.innerHTML.includes('</math>')) {\n      numberOfLines += div.innerHTML.split('<mrow>').length - 1;\n    }\n\n    // Support images\n    const images = div.getElementsByTagName('img');\n    if (images) {\n      const noImgText = textContent.replace(/<img[^>]*>/g, '').trim() === '';\n      await Promise.all(\n        [...images].map(\n          (img) =>\n            new Promise((res) => {\n              function setupImage() {\n                img.style.display = 'flex';\n                img.style.flexDirection = 'column';\n\n                if (noImgText) {\n                  // default size if no text\n                  const bodyFontSize =\n                    config.fontSize?.toString() ?? window.getComputedStyle(document.body).fontSize;\n                  const enlargingFactor = 5;\n                  const width = parseInt(bodyFontSize, 10) * enlargingFactor + 'px';\n                  img.style.minWidth = width;\n                  img.style.maxWidth = width;\n                } else {\n                  img.style.width = '100%';\n                }\n                res(img);\n              }\n              setTimeout(() => {\n                if (img.complete) {\n                  setupImage();\n                }\n              });\n              img.addEventListener('error', setupImage);\n              img.addEventListener('load', setupImage);\n            })\n        )\n      );\n    }\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center text and offset by yOffset\n  textEl.attr('transform', 'translate(0,' + (-bbox.height / (2 * numberOfLines) + yOffset) + ')');\n  return bbox.height;\n}\n", "import { getNodeClasses, updateNodeBounds } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { calculateTextWidth, decodeEntities } from '../../../utils.js';\nimport { getConfig, sanitizeText } from '../../../diagram-api/diagramAPI.js';\nimport { createText } from '../../createText.js';\nimport { select } from 'd3';\nimport type { Requirement, Element } from '../../../diagrams/requirement/types.js';\n\nexport async function requirementBox<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const requirementNode = node as unknown as Requirement;\n  const elementNode = node as unknown as Element;\n  const padding = 20;\n  const gap = 20;\n  const isRequirementNode = 'verifyMethod' in node;\n  const classes = getNodeClasses(node);\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId ?? node.id);\n\n  let typeHeight;\n  if (isRequirementNode) {\n    typeHeight = await addText(\n      shapeSvg,\n      `&lt;&lt;${requirementNode.type}&gt;&gt;`,\n      0,\n      node.labelStyle\n    );\n  } else {\n    typeHeight = await addText(shapeSvg, '&lt;&lt;Element&gt;&gt;', 0, node.labelStyle);\n  }\n\n  let accumulativeHeight = typeHeight;\n  const nameHeight = await addText(\n    shapeSvg,\n    requirementNode.name,\n    accumulativeHeight,\n    node.labelStyle + '; font-weight: bold;'\n  );\n  accumulativeHeight += nameHeight + gap;\n\n  // Requirement\n  if (isRequirementNode) {\n    const idHeight = await addText(\n      shapeSvg,\n      `${requirementNode.requirementId ? `id: ${requirementNode.requirementId}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n\n    accumulativeHeight += idHeight;\n    const textHeight = await addText(\n      shapeSvg,\n      `${requirementNode.text ? `Text: ${requirementNode.text}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += textHeight;\n    const riskHeight = await addText(\n      shapeSvg,\n      `${requirementNode.risk ? `Risk: ${requirementNode.risk}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += riskHeight;\n    await addText(\n      shapeSvg,\n      `${requirementNode.verifyMethod ? `Verification: ${requirementNode.verifyMethod}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n  } else {\n    // Element\n    const typeHeight = await addText(\n      shapeSvg,\n      `${elementNode.type ? `Type: ${elementNode.type}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += typeHeight;\n    await addText(\n      shapeSvg,\n      `${elementNode.docRef ? `Doc Ref: ${elementNode.docRef}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n  }\n\n  const totalWidth = (shapeSvg.node()?.getBBox().width ?? 200) + padding;\n  const totalHeight = (shapeSvg.node()?.getBBox().height ?? 200) + padding;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n\n  // Setup roughjs\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  // Create and center rectangle\n  const roughRect = rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n  const rect = shapeSvg.insert(() => roughRect, ':first-child');\n  rect.attr('class', 'basic label-container').attr('style', nodeStyles);\n\n  // Re-translate labels now that rect is centered\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  shapeSvg.selectAll('.label').each((_: any, i: number, nodes: any) => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const text = select<any, unknown>(nodes[i]);\n\n    const transform = text.attr('transform');\n    let translateX = 0;\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n      }\n    }\n\n    const newTranslateY = translateY - totalHeight / 2;\n    let newTranslateX = x + padding / 2;\n\n    // Keep type and name labels centered.\n    if (i === 0 || i === 1) {\n      newTranslateX = translateX;\n    }\n    // Set the updated transform attribute\n    text.attr('transform', `translate(${newTranslateX}, ${newTranslateY + padding})`);\n  });\n\n  // Insert divider line if there is body text\n  if (accumulativeHeight > typeHeight + nameHeight + gap) {\n    const roughLine = rc.line(\n      x,\n      y + typeHeight + nameHeight + gap,\n      x + totalWidth,\n      y + typeHeight + nameHeight + gap,\n      options\n    );\n    const dividerLine = shapeSvg.insert(() => roughLine);\n    dividerLine.attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n\nasync function addText<T extends SVGGraphicsElement>(\n  parentGroup: D3Selection<T>,\n  inputText: string,\n  yOffset: number,\n  style = ''\n) {\n  if (inputText === '') {\n    return 0;\n  }\n  const textEl = parentGroup.insert('g').attr('class', 'label').attr('style', style);\n  const config = getConfig();\n  const useHtmlLabels = config.htmlLabels ?? true;\n\n  const text = await createText(\n    textEl,\n    sanitizeText(decodeEntities(inputText)),\n    {\n      width: calculateTextWidth(inputText, config) + 50, // Add room for error when splitting text into multiple lines\n      classes: 'markdown-node-label',\n      useHtmlLabels,\n      style,\n    },\n    config\n  );\n  let bbox;\n\n  if (!useHtmlLabels) {\n    const textChild = text.children[0];\n    for (const child of textChild.children) {\n      child.textContent = child.textContent.replaceAll('&gt;', '>').replaceAll('&lt;', '<');\n      if (style) {\n        child.setAttribute('style', style);\n      }\n    }\n    // Get the bounding box after the text update\n    bbox = text.getBBox();\n    // Add extra height so it is similar to the html labels\n    bbox.height += 6;\n  } else {\n    const div = text.children[0];\n    const dv = select(text);\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center text and offset by yOffset\n  textEl.attr('transform', `translate(${-bbox.width / 2},${-bbox.height / 2 + yOffset})`);\n  return bbox.height;\n}\n", "import { labelHelper, insert<PERSON>abel, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node, KanbanNode, ShapeRenderOptions } from '../../types.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { userNodeOverrides, styles2String } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nconst colorFromPriority = (priority: NonNullable<KanbanNode['priority']>) => {\n  switch (priority) {\n    case 'Very High':\n      return 'red';\n    case 'High':\n      return 'orange';\n    case 'Medium':\n      return null; // no stroke\n    case 'Low':\n      return 'blue';\n    case 'Very Low':\n      return 'lightblue';\n  }\n};\nexport async function kanbanItem<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  // Omit the 'shape' prop since otherwise, it causes a TypeScript circular dependency error\n  kanbanNode: Omit<Node, 'shape'> | Omit<KanbanNode, 'level' | 'shape'>,\n  { config }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(kanbanNode);\n  kanbanNode.labelStyle = labelStyles || '';\n\n  const labelPaddingX = 10;\n  const orgWidth = kanbanNode.width;\n  kanbanNode.width = (kanbanNode.width ?? 200) - 10;\n\n  const {\n    shapeSvg,\n    bbox,\n    label: labelElTitle,\n  } = await labelHelper(parent, kanbanNode, getNodeClasses(kanbanNode));\n  const padding = kanbanNode.padding || 10;\n\n  let ticketUrl = '';\n  let link;\n\n  if ('ticket' in kanbanNode && kanbanNode.ticket && config?.kanban?.ticketBaseUrl) {\n    ticketUrl = config?.kanban?.ticketBaseUrl.replace('#TICKET#', kanbanNode.ticket);\n    link = shapeSvg\n      .insert<SVGAElement>('svg:a', ':first-child')\n      .attr('class', 'kanban-ticket-link')\n      .attr('xlink:href', ticketUrl)\n      .attr('target', '_blank');\n  }\n\n  const options = {\n    useHtmlLabels: kanbanNode.useHtmlLabels,\n    labelStyle: kanbanNode.labelStyle || '',\n    width: kanbanNode.width,\n    img: kanbanNode.img,\n    padding: kanbanNode.padding || 8,\n    centerLabel: false,\n  };\n  let labelEl, bbox2;\n  if (link) {\n    ({ label: labelEl, bbox: bbox2 } = await insertLabel(\n      link,\n      ('ticket' in kanbanNode && kanbanNode.ticket) || '',\n      options\n    ));\n  } else {\n    ({ label: labelEl, bbox: bbox2 } = await insertLabel(\n      shapeSvg,\n      ('ticket' in kanbanNode && kanbanNode.ticket) || '',\n      options\n    ));\n  }\n  const { label: labelElAssigned, bbox: bboxAssigned } = await insertLabel(\n    shapeSvg,\n    ('assigned' in kanbanNode && kanbanNode.assigned) || '',\n    options\n  );\n  kanbanNode.width = orgWidth;\n  const labelPaddingY = 10;\n  const totalWidth = kanbanNode?.width || 0;\n  const heightAdj = Math.max(bbox2.height, bboxAssigned.height) / 2;\n  const totalHeight =\n    Math.max(bbox.height + labelPaddingY * 2, kanbanNode?.height || 0) + heightAdj;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  labelElTitle.attr(\n    'transform',\n    'translate(' + (padding - totalWidth / 2) + ', ' + (-heightAdj - bbox.height / 2) + ')'\n  );\n  labelEl.attr(\n    'transform',\n    'translate(' + (padding - totalWidth / 2) + ', ' + (-heightAdj + bbox.height / 2) + ')'\n  );\n  labelElAssigned.attr(\n    'transform',\n    'translate(' +\n      (padding + totalWidth / 2 - bboxAssigned.width - 2 * labelPaddingX) +\n      ', ' +\n      (-heightAdj + bbox.height / 2) +\n      ')'\n  );\n\n  let rect;\n\n  const { rx, ry } = kanbanNode;\n  const { cssStyles } = kanbanNode;\n\n  if (kanbanNode.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(kanbanNode, {});\n\n    const roughNode =\n      rx || ry\n        ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options)\n        : rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n    rect = shapeSvg.insert(() => roughNode, ':first-child');\n    rect.attr('class', 'basic label-container').attr('style', cssStyles ? cssStyles : null);\n  } else {\n    rect = shapeSvg.insert('rect', ':first-child');\n\n    rect\n      .attr('class', 'basic label-container __APA__')\n      .attr('style', nodeStyles)\n      .attr('rx', rx ?? 5)\n      .attr('ry', ry ?? 5)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', totalWidth)\n      .attr('height', totalHeight);\n\n    const priority = 'priority' in kanbanNode && kanbanNode.priority;\n    if (priority) {\n      const line = shapeSvg.append('line');\n      const lineX = x + 2;\n\n      const y1 = y + Math.floor((rx ?? 0) / 2);\n      const y2 = y + totalHeight - Math.floor((rx ?? 0) / 2);\n      line\n        .attr('x1', lineX)\n        .attr('y1', y1)\n        .attr('x2', lineX)\n        .attr('y2', y2)\n\n        .attr('stroke-width', '4')\n        .attr('stroke', colorFromPriority(priority));\n    }\n  }\n\n  updateNodeBounds(kanbanNode, rect);\n  kanbanNode.height = totalHeight;\n\n  kanbanNode.intersect = function (point) {\n    return intersect.rect(kanbanNode, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Entries } from 'type-fest';\nimport type { D3Selection, MaybePromise } from '../../types.js';\nimport type { Node, ShapeRenderOptions } from '../types.js';\nimport { anchor } from './shapes/anchor.js';\nimport { bowTieRect } from './shapes/bowTieRect.js';\nimport { card } from './shapes/card.js';\nimport { choice } from './shapes/choice.js';\nimport { circle } from './shapes/circle.js';\nimport { crossedCircle } from './shapes/crossedCircle.js';\nimport { curlyBraceLeft } from './shapes/curlyBraceLeft.js';\nimport { curlyBraceRight } from './shapes/curlyBraceRight.js';\nimport { curlyBraces } from './shapes/curlyBraces.js';\nimport { curvedTrapezoid } from './shapes/curvedTrapezoid.js';\nimport { cylinder } from './shapes/cylinder.js';\nimport { dividedRectangle } from './shapes/dividedRect.js';\nimport { doublecircle } from './shapes/doubleCircle.js';\nimport { filledCircle } from './shapes/filledCircle.js';\nimport { flippedTriangle } from './shapes/flippedTriangle.js';\nimport { forkJoin } from './shapes/forkJoin.js';\nimport { halfRoundedRectangle } from './shapes/halfRoundedRectangle.js';\nimport { hexagon } from './shapes/hexagon.js';\nimport { hourglass } from './shapes/hourglass.js';\nimport { icon } from './shapes/icon.js';\nimport { iconCircle } from './shapes/iconCircle.js';\nimport { iconRounded } from './shapes/iconRounded.js';\nimport { iconSquare } from './shapes/iconSquare.js';\nimport { imageSquare } from './shapes/imageSquare.js';\nimport { inv_trapezoid } from './shapes/invertedTrapezoid.js';\nimport { labelRect } from './shapes/labelRect.js';\nimport { lean_left } from './shapes/leanLeft.js';\nimport { lean_right } from './shapes/leanRight.js';\nimport { lightningBolt } from './shapes/lightningBolt.js';\nimport { linedCylinder } from './shapes/linedCylinder.js';\nimport { linedWaveEdgedRect } from './shapes/linedWaveEdgedRect.js';\nimport { multiRect } from './shapes/multiRect.js';\nimport { multiWaveEdgedRectangle } from './shapes/multiWaveEdgedRectangle.js';\nimport { note } from './shapes/note.js';\nimport { question } from './shapes/question.js';\nimport { rect_left_inv_arrow } from './shapes/rectLeftInvArrow.js';\nimport { rectWithTitle } from './shapes/rectWithTitle.js';\nimport { roundedRect } from './shapes/roundedRect.js';\nimport { shadedProcess } from './shapes/shadedProcess.js';\nimport { slopedRect } from './shapes/slopedRect.js';\nimport { squareRect } from './shapes/squareRect.js';\nimport { stadium } from './shapes/stadium.js';\nimport { state } from './shapes/state.js';\nimport { stateEnd } from './shapes/stateEnd.js';\nimport { stateStart } from './shapes/stateStart.js';\nimport { subroutine } from './shapes/subroutine.js';\nimport { taggedRect } from './shapes/taggedRect.js';\nimport { taggedWaveEdgedRectangle } from './shapes/taggedWaveEdgedRectangle.js';\nimport { text } from './shapes/text.js';\nimport { tiltedCylinder } from './shapes/tiltedCylinder.js';\nimport { trapezoid } from './shapes/trapezoid.js';\nimport { trapezoidalPentagon } from './shapes/trapezoidalPentagon.js';\nimport { triangle } from './shapes/triangle.js';\nimport { waveEdgedRectangle } from './shapes/waveEdgedRectangle.js';\nimport { waveRectangle } from './shapes/waveRectangle.js';\nimport { windowPane } from './shapes/windowPane.js';\nimport { erBox } from './shapes/erBox.js';\nimport { classBox } from './shapes/classBox.js';\nimport { requirementBox } from './shapes/requirementBox.js';\nimport { kanbanItem } from './shapes/kanbanItem.js';\n\ntype ShapeHandler = <T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  options: ShapeRenderOptions\n) => MaybePromise<D3Selection<SVGGElement>>;\n\nexport interface ShapeDefinition {\n  semanticName: string;\n  name: string;\n  shortName: string;\n  description: string;\n  /**\n   * Aliases can include descriptive names, other short names, etc.\n   */\n  aliases?: string[];\n  /**\n   * These are names used by mermaid before the introduction of new shapes. These will not be in standard formats, and shouldn't be used by the users\n   */\n  internalAliases?: string[];\n  handler: ShapeHandler;\n}\n\nexport const shapesDefs = [\n  {\n    semanticName: 'Process',\n    name: 'Rectangle',\n    shortName: 'rect',\n    description: 'Standard process shape',\n    aliases: ['proc', 'process', 'rectangle'],\n    internalAliases: ['squareRect'],\n    handler: squareRect,\n  },\n  {\n    semanticName: 'Event',\n    name: 'Rounded Rectangle',\n    shortName: 'rounded',\n    description: 'Represents an event',\n    aliases: ['event'],\n    internalAliases: ['roundedRect'],\n    handler: roundedRect,\n  },\n  {\n    semanticName: 'Terminal Point',\n    name: 'Stadium',\n    shortName: 'stadium',\n    description: 'Terminal point',\n    aliases: ['terminal', 'pill'],\n    handler: stadium,\n  },\n  {\n    semanticName: 'Subprocess',\n    name: 'Framed Rectangle',\n    shortName: 'fr-rect',\n    description: 'Subprocess',\n    aliases: ['subprocess', 'subproc', 'framed-rectangle', 'subroutine'],\n    handler: subroutine,\n  },\n  {\n    semanticName: 'Database',\n    name: 'Cylinder',\n    shortName: 'cyl',\n    description: 'Database storage',\n    aliases: ['db', 'database', 'cylinder'],\n    handler: cylinder,\n  },\n  {\n    semanticName: 'Start',\n    name: 'Circle',\n    shortName: 'circle',\n    description: 'Starting point',\n    aliases: ['circ'],\n    handler: circle,\n  },\n  {\n    semanticName: 'Decision',\n    name: 'Diamond',\n    shortName: 'diam',\n    description: 'Decision-making step',\n    aliases: ['decision', 'diamond', 'question'],\n    handler: question,\n  },\n  {\n    semanticName: 'Prepare Conditional',\n    name: 'Hexagon',\n    shortName: 'hex',\n    description: 'Preparation or condition step',\n    aliases: ['hexagon', 'prepare'],\n    handler: hexagon,\n  },\n  {\n    semanticName: 'Data Input/Output',\n    name: 'Lean Right',\n    shortName: 'lean-r',\n    description: 'Represents input or output',\n    aliases: ['lean-right', 'in-out'],\n    internalAliases: ['lean_right'],\n    handler: lean_right,\n  },\n  {\n    semanticName: 'Data Input/Output',\n    name: 'Lean Left',\n    shortName: 'lean-l',\n    description: 'Represents output or input',\n    aliases: ['lean-left', 'out-in'],\n    internalAliases: ['lean_left'],\n    handler: lean_left,\n  },\n  {\n    semanticName: 'Priority Action',\n    name: 'Trapezoid Base Bottom',\n    shortName: 'trap-b',\n    description: 'Priority action',\n    aliases: ['priority', 'trapezoid-bottom', 'trapezoid'],\n    handler: trapezoid,\n  },\n  {\n    semanticName: 'Manual Operation',\n    name: 'Trapezoid Base Top',\n    shortName: 'trap-t',\n    description: 'Represents a manual task',\n    aliases: ['manual', 'trapezoid-top', 'inv-trapezoid'],\n    internalAliases: ['inv_trapezoid'],\n    handler: inv_trapezoid,\n  },\n  {\n    semanticName: 'Stop',\n    name: 'Double Circle',\n    shortName: 'dbl-circ',\n    description: 'Represents a stop point',\n    aliases: ['double-circle'],\n    internalAliases: ['doublecircle'],\n    handler: doublecircle,\n  },\n  {\n    semanticName: 'Text Block',\n    name: 'Text Block',\n    shortName: 'text',\n    description: 'Text block',\n    handler: text,\n  },\n  {\n    semanticName: 'Card',\n    name: 'Notched Rectangle',\n    shortName: 'notch-rect',\n    description: 'Represents a card',\n    aliases: ['card', 'notched-rectangle'],\n    handler: card,\n  },\n  {\n    semanticName: 'Lined/Shaded Process',\n    name: 'Lined Rectangle',\n    shortName: 'lin-rect',\n    description: 'Lined process shape',\n    aliases: ['lined-rectangle', 'lined-process', 'lin-proc', 'shaded-process'],\n    handler: shadedProcess,\n  },\n  {\n    semanticName: 'Start',\n    name: 'Small Circle',\n    shortName: 'sm-circ',\n    description: 'Small starting point',\n    aliases: ['start', 'small-circle'],\n    internalAliases: ['stateStart'],\n    handler: stateStart,\n  },\n  {\n    semanticName: 'Stop',\n    name: 'Framed Circle',\n    shortName: 'fr-circ',\n    description: 'Stop point',\n    aliases: ['stop', 'framed-circle'],\n    internalAliases: ['stateEnd'],\n    handler: stateEnd,\n  },\n  {\n    semanticName: 'Fork/Join',\n    name: 'Filled Rectangle',\n    shortName: 'fork',\n    description: 'Fork or join in process flow',\n    aliases: ['join'],\n    internalAliases: ['forkJoin'],\n    handler: forkJoin,\n  },\n  {\n    semanticName: 'Collate',\n    name: 'Hourglass',\n    shortName: 'hourglass',\n    description: 'Represents a collate operation',\n    aliases: ['hourglass', 'collate'],\n    handler: hourglass,\n  },\n  {\n    semanticName: 'Comment',\n    name: 'Curly Brace',\n    shortName: 'brace',\n    description: 'Adds a comment',\n    aliases: ['comment', 'brace-l'],\n    handler: curlyBraceLeft,\n  },\n  {\n    semanticName: 'Comment Right',\n    name: 'Curly Brace',\n    shortName: 'brace-r',\n    description: 'Adds a comment',\n    handler: curlyBraceRight,\n  },\n  {\n    semanticName: 'Comment with braces on both sides',\n    name: 'Curly Braces',\n    shortName: 'braces',\n    description: 'Adds a comment',\n    handler: curlyBraces,\n  },\n  {\n    semanticName: 'Com Link',\n    name: 'Lightning Bolt',\n    shortName: 'bolt',\n    description: 'Communication link',\n    aliases: ['com-link', 'lightning-bolt'],\n    handler: lightningBolt,\n  },\n  {\n    semanticName: 'Document',\n    name: 'Document',\n    shortName: 'doc',\n    description: 'Represents a document',\n    aliases: ['doc', 'document'],\n    handler: waveEdgedRectangle,\n  },\n  {\n    semanticName: 'Delay',\n    name: 'Half-Rounded Rectangle',\n    shortName: 'delay',\n    description: 'Represents a delay',\n    aliases: ['half-rounded-rectangle'],\n    handler: halfRoundedRectangle,\n  },\n  {\n    semanticName: 'Direct Access Storage',\n    name: 'Horizontal Cylinder',\n    shortName: 'h-cyl',\n    description: 'Direct access storage',\n    aliases: ['das', 'horizontal-cylinder'],\n    handler: tiltedCylinder,\n  },\n  {\n    semanticName: 'Disk Storage',\n    name: 'Lined Cylinder',\n    shortName: 'lin-cyl',\n    description: 'Disk storage',\n    aliases: ['disk', 'lined-cylinder'],\n    handler: linedCylinder,\n  },\n  {\n    semanticName: 'Display',\n    name: 'Curved Trapezoid',\n    shortName: 'curv-trap',\n    description: 'Represents a display',\n    aliases: ['curved-trapezoid', 'display'],\n    handler: curvedTrapezoid,\n  },\n  {\n    semanticName: 'Divided Process',\n    name: 'Divided Rectangle',\n    shortName: 'div-rect',\n    description: 'Divided process shape',\n    aliases: ['div-proc', 'divided-rectangle', 'divided-process'],\n    handler: dividedRectangle,\n  },\n  {\n    semanticName: 'Extract',\n    name: 'Triangle',\n    shortName: 'tri',\n    description: 'Extraction process',\n    aliases: ['extract', 'triangle'],\n    handler: triangle,\n  },\n  {\n    semanticName: 'Internal Storage',\n    name: 'Window Pane',\n    shortName: 'win-pane',\n    description: 'Internal storage',\n    aliases: ['internal-storage', 'window-pane'],\n    handler: windowPane,\n  },\n  {\n    semanticName: 'Junction',\n    name: 'Filled Circle',\n    shortName: 'f-circ',\n    description: 'Junction point',\n    aliases: ['junction', 'filled-circle'],\n    handler: filledCircle,\n  },\n  {\n    semanticName: 'Loop Limit',\n    name: 'Trapezoidal Pentagon',\n    shortName: 'notch-pent',\n    description: 'Loop limit step',\n    aliases: ['loop-limit', 'notched-pentagon'],\n    handler: trapezoidalPentagon,\n  },\n  {\n    semanticName: 'Manual File',\n    name: 'Flipped Triangle',\n    shortName: 'flip-tri',\n    description: 'Manual file operation',\n    aliases: ['manual-file', 'flipped-triangle'],\n    handler: flippedTriangle,\n  },\n  {\n    semanticName: 'Manual Input',\n    name: 'Sloped Rectangle',\n    shortName: 'sl-rect',\n    description: 'Manual input step',\n    aliases: ['manual-input', 'sloped-rectangle'],\n    handler: slopedRect,\n  },\n  {\n    semanticName: 'Multi-Document',\n    name: 'Stacked Document',\n    shortName: 'docs',\n    description: 'Multiple documents',\n    aliases: ['documents', 'st-doc', 'stacked-document'],\n    handler: multiWaveEdgedRectangle,\n  },\n  {\n    semanticName: 'Multi-Process',\n    name: 'Stacked Rectangle',\n    shortName: 'st-rect',\n    description: 'Multiple processes',\n    aliases: ['procs', 'processes', 'stacked-rectangle'],\n    handler: multiRect,\n  },\n  {\n    semanticName: 'Stored Data',\n    name: 'Bow Tie Rectangle',\n    shortName: 'bow-rect',\n    description: 'Stored data',\n    aliases: ['stored-data', 'bow-tie-rectangle'],\n    handler: bowTieRect,\n  },\n  {\n    semanticName: 'Summary',\n    name: 'Crossed Circle',\n    shortName: 'cross-circ',\n    description: 'Summary',\n    aliases: ['summary', 'crossed-circle'],\n    handler: crossedCircle,\n  },\n  {\n    semanticName: 'Tagged Document',\n    name: 'Tagged Document',\n    shortName: 'tag-doc',\n    description: 'Tagged document',\n    aliases: ['tag-doc', 'tagged-document'],\n    handler: taggedWaveEdgedRectangle,\n  },\n  {\n    semanticName: 'Tagged Process',\n    name: 'Tagged Rectangle',\n    shortName: 'tag-rect',\n    description: 'Tagged process',\n    aliases: ['tagged-rectangle', 'tag-proc', 'tagged-process'],\n    handler: taggedRect,\n  },\n  {\n    semanticName: 'Paper Tape',\n    name: 'Flag',\n    shortName: 'flag',\n    description: 'Paper tape',\n    aliases: ['paper-tape'],\n    handler: waveRectangle,\n  },\n  {\n    semanticName: 'Odd',\n    name: 'Odd',\n    shortName: 'odd',\n    description: 'Odd shape',\n    internalAliases: ['rect_left_inv_arrow'],\n    handler: rect_left_inv_arrow,\n  },\n  {\n    semanticName: 'Lined Document',\n    name: 'Lined Document',\n    shortName: 'lin-doc',\n    description: 'Lined document',\n    aliases: ['lined-document'],\n    handler: linedWaveEdgedRect,\n  },\n] as const satisfies ShapeDefinition[];\n\nconst generateShapeMap = () => {\n  // These are the shapes that didn't have documentation present\n  const undocumentedShapes = {\n    // States\n    state,\n    choice,\n    note,\n\n    // Rectangles\n    rectWithTitle,\n    labelRect,\n\n    // Icons\n    iconSquare,\n    iconCircle,\n    icon,\n    iconRounded,\n    imageSquare,\n    anchor,\n\n    // Kanban diagram\n    kanbanItem,\n\n    // class diagram\n    classBox,\n\n    // er diagram\n    erBox,\n\n    // Requirement diagram\n    requirementBox,\n  } as const;\n\n  const entries = [\n    ...(Object.entries(undocumentedShapes) as Entries<typeof undocumentedShapes>),\n    ...shapesDefs.flatMap((shape) => {\n      const aliases = [\n        shape.shortName,\n        ...('aliases' in shape ? shape.aliases : []),\n        ...('internalAliases' in shape ? shape.internalAliases : []),\n      ];\n      return aliases.map((alias) => [alias, shape.handler] as const);\n    }),\n  ];\n  return Object.fromEntries(entries) as Record<\n    (typeof entries)[number][0],\n    (typeof entries)[number][1]\n  > satisfies Record<string, ShapeHandler>;\n};\n\nexport const shapes = generateShapeMap();\n\nexport function isValidShape(shape: string): shape is ShapeID {\n  return shape in shapes;\n}\n\nexport type ShapeID = keyof typeof shapes;\n", "import { log } from '../../logger.js';\nimport { shapes } from './shapes.js';\nimport type { Node, NonClusterNode, ShapeRenderOptions } from '../types.js';\nimport type { SVGGroup } from '../../mermaid.js';\nimport type { D3Selection } from '../../types.js';\nimport type { graphlib } from 'dagre-d3-es';\n\ntype ShapeHandler = (typeof shapes)[keyof typeof shapes];\ntype NodeElement = D3Selection<SVGAElement> | Awaited<ReturnType<ShapeHandler>>;\n\nconst nodeElems = new Map<string, NodeElement>();\n\nexport async function insertNode(\n  elem: SVGGroup,\n  node: NonClusterNode,\n  renderOptions: ShapeRenderOptions\n) {\n  let newEl: NodeElement | undefined;\n  let el;\n\n  //special check for rect shape (with or without rounded corners)\n  if (node.shape === 'rect') {\n    if (node.rx && node.ry) {\n      node.shape = 'roundedRect';\n    } else {\n      node.shape = 'squareRect';\n    }\n  }\n\n  const shapeHandler = node.shape ? shapes[node.shape] : undefined;\n\n  if (!shapeHandler) {\n    throw new Error(`No such shape: ${node.shape}. Please check your syntax.`);\n  }\n\n  if (node.link) {\n    // Add link when appropriate\n    let target;\n    if (renderOptions.config.securityLevel === 'sandbox') {\n      target = '_top';\n    } else if (node.linkTarget) {\n      target = node.linkTarget || '_blank';\n    }\n    newEl = elem\n      .insert<SVGAElement>('svg:a')\n      .attr('xlink:href', node.link)\n      .attr('target', target ?? null);\n    el = await shapeHandler(newEl, node, renderOptions);\n  } else {\n    el = await shapeHandler(elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr('title', node.tooltip);\n  }\n\n  nodeElems.set(node.id, newEl);\n\n  if (node.haveCallback) {\n    newEl.attr('class', newEl.attr('class') + ' clickable');\n  }\n  return newEl;\n}\n\nexport const setNodeElem = (elem: NodeElement, node: Pick<Node, 'id'>) => {\n  nodeElems.set(node.id, elem);\n};\n\nexport const clear = () => {\n  nodeElems.clear();\n};\n\nexport const positionNode = (node: ReturnType<graphlib.Graph['node']>) => {\n  const el = nodeElems.get(node.id)!;\n  log.trace(\n    'Transforming node',\n    node.diff,\n    node,\n    'translate(' + (node.x - node.width / 2 - 5) + ', ' + node.width / 2 + ')'\n  );\n  const padding = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\n      'transform',\n      'translate(' +\n        (node.x + diff - node.width / 2) +\n        ', ' +\n        (node.y - node.height / 2 - padding) +\n        ')'\n    );\n  } else {\n    el.attr('transform', 'translate(' + node.x + ', ' + node.y + ')');\n  }\n  return diff;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,cAAc;AAMhB,IAAM,cAAc,8BACzB,QACA,MACA,aACG;AACH,MAAI;AACJ,QAAM,gBAAgB,KAAK,iBAAiB,SAASA,WAAU,GAAG,UAAU;AAC5E,MAAI,CAAC,UAAU;AACb,iBAAa;AAAA,EACf,OAAO;AACL,iBAAa;AAAA,EACf;AAGA,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAGnC,QAAM,UAAU,SACb,OAAO,GAAG,EACV,KAAK,SAAS,OAAO,EACrB,KAAK,SAAS,oBAAoB,KAAK,UAAU,CAAC;AAGrD,MAAI;AACJ,MAAI,KAAK,UAAU,QAAW;AAC5B,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ,OAAO,KAAK,UAAU,WAAW,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,EACpE;AAEA,QAAMC,QAAO,MAAM,WAAW,SAAS,aAAa,eAAe,KAAK,GAAGD,WAAU,CAAC,GAAG;AAAA,IACvF;AAAA,IACA,OAAO,KAAK,SAASA,WAAU,EAAE,WAAW;AAAA;AAAA,IAE5C,YAAY;AAAA,IACZ,OAAO,KAAK;AAAA,IACZ,kBAAkB,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,KAAK;AAAA,EAC1C,CAAC;AAED,MAAI,OAAOC,MAAK,QAAQ;AACxB,QAAM,eAAe,MAAM,WAAW,KAAK;AAE3C,MAAI,eAAe;AACjB,UAAM,MAAMA,MAAK,SAAS,CAAC;AAC3B,UAAM,KAAK,OAAOA,KAAI;AAGtB,UAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,QAAI,QAAQ;AACV,YAAM,YAAY,MAAM,QAAQ,eAAe,EAAE,EAAE,KAAK,MAAM;AAE9D,YAAM,QAAQ;AAAA,QACZ,CAAC,GAAG,MAAM,EAAE;AAAA,UACV,CAAC,QACC,IAAI,QAAQ,CAAC,QAAQ;AAInB,qBAAS,aAAa;AACpB,kBAAI,MAAM,UAAU;AACpB,kBAAI,MAAM,gBAAgB;AAE1B,kBAAI,WAAW;AAEb,sBAAM,eAAeD,WAAU,EAAE,WAC7BA,WAAU,EAAE,WACZ,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC3C,sBAAM,kBAAkB;AACxB,sBAAM,CAAC,qBAAqB,sBAAc,QAAQ,IAAI,cAAc,YAAY;AAChF,sBAAM,QAAQ,qBAAqB,kBAAkB;AACrD,oBAAI,MAAM,WAAW;AACrB,oBAAI,MAAM,WAAW;AAAA,cACvB,OAAO;AACL,oBAAI,MAAM,QAAQ;AAAA,cACpB;AACA,kBAAI,GAAG;AAAA,YACT;AAlBS;AAmBT,uBAAW,MAAM;AACf,kBAAI,IAAI,UAAU;AAChB,2BAAW;AAAA,cACb;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,SAAS,UAAU;AACxC,gBAAI,iBAAiB,QAAQ,UAAU;AAAA,UACzC,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAGA,MAAI,eAAe;AACjB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F,OAAO;AACL,YAAQ,KAAK,aAAa,kBAA0B,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC5E;AACA,MAAI,KAAK,aAAa;AACpB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F;AACA,UAAQ,OAAO,QAAQ,cAAc;AACrC,SAAO,EAAE,UAAU,MAAM,aAAa,OAAO,QAAQ;AACvD,GA5G2B;AA6GpB,IAAM,cAAc,8BACzB,QACA,OACA,YAUG;AACH,QAAM,gBAAgB,QAAQ,iBAAiB,SAASA,WAAU,GAAG,WAAW,UAAU;AAG1F,QAAM,UAAU,OACb,OAAO,GAAG,EACV,KAAK,SAAS,OAAO,EACrB,KAAK,SAAS,QAAQ,cAAc,EAAE;AAEzC,QAAMC,QAAO,MAAM,WAAW,SAAS,aAAa,eAAe,KAAK,GAAGD,WAAU,CAAC,GAAG;AAAA,IACvF;AAAA,IACA,OAAO,QAAQ,SAASA,WAAU,GAAG,WAAW;AAAA,IAChD,OAAO,QAAQ;AAAA,IACf,kBAAkB,CAAC,CAAC,QAAQ,QAAQ,CAAC,CAAC,QAAQ;AAAA,EAChD,CAAC;AAED,MAAI,OAAOC,MAAK,QAAQ;AACxB,QAAM,cAAc,QAAQ,UAAU;AAEtC,MAAI,SAASD,WAAU,GAAG,WAAW,UAAU,GAAG;AAChD,UAAM,MAAMC,MAAK,SAAS,CAAC;AAC3B,UAAM,KAAK,OAAOA,KAAI;AAEtB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAGA,MAAI,eAAe;AACjB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F,OAAO;AACL,YAAQ,KAAK,aAAa,kBAA0B,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC5E;AACA,MAAI,QAAQ,aAAa;AACvB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F;AACA,UAAQ,OAAO,QAAQ,cAAc;AACrC,SAAO,EAAE,UAAU,QAAQ,MAAM,aAAa,OAAO,QAAQ;AAC/D,GApD2B;AAqDpB,IAAM,mBAAmB,wBAC9B,MAEA,YACG;AACH,QAAM,OAAO,QAAQ,KAAK,EAAG,QAAQ;AACrC,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACrB,GARgC;AAoCzB,IAAM,iBAAiB,wBAAC,MAAY,WACxC,KAAK,SAAS,cAAc,eAAe,UAAU,MAAM,KAAK,aAAa,OAAO,SAAS,KADlE;AAGvB,SAAS,qBAAqB,QAAiB;AACpD,QAAM,eAAe,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE;AAC/E,eAAa,KAAK,GAAG;AACrB,SAAO,aAAa,KAAK,GAAG;AAC9B;AAJgB;AAMT,SAAS,2BACd,IACA,IACA,IACA,IACA,WACA,WACA;AACA,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ;AACd,QAAM,SAAS,KAAK;AACpB,QAAM,SAAS,KAAK;AACpB,QAAM,cAAc,SAAS;AAG7B,QAAM,YAAa,IAAI,KAAK,KAAM;AAClC,QAAM,OAAO,KAAK,SAAS;AAE3B,WAAS,IAAI,GAAG,KAAK,OAAO,KAAK;AAC/B,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,KAAK,IAAI;AACnB,UAAM,IAAI,OAAO,YAAY,KAAK,IAAI,aAAa,IAAI,GAAG;AAE1D,WAAO,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,EACtB;AAEA,SAAO;AACT;AA3BgB;AAqCT,SAAS,qBACd,SACA,SACA,QACA,WACA,YACA,UACA;AACA,QAAM,SAAS,CAAC;AAGhB,QAAM,gBAAiB,aAAa,KAAK,KAAM;AAC/C,QAAM,cAAe,WAAW,KAAK,KAAM;AAG3C,QAAM,aAAa,cAAc;AAGjC,QAAM,YAAY,cAAc,YAAY;AAE5C,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,QAAQ,gBAAgB,IAAI;AAClC,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;AAAA,EAC9B;AAEA,SAAO;AACT;AA5BgB;;;ACzPhB,SAAS,UAAAC,eAAc;AACvB,OAAO,WAAW;;;ACLlB,IAAM,gBAAgB,wBAAC,MAAM,UAAU;AACrC,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AAIb,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,IAAI,KAAK,QAAQ;AACrB,MAAI,IAAI,KAAK,SAAS;AAEtB,MAAI,IAAI;AACR,MAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG;AAEvC,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;AAAA,IACP;AACA,SAAK,OAAO,IAAI,IAAK,IAAI,KAAM;AAC/B,SAAK;AAAA,EACP,OAAO;AAEL,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;AAAA,IACP;AACA,SAAK;AACL,SAAK,OAAO,IAAI,IAAK,IAAI,KAAM;AAAA,EACjC;AAEA,SAAO,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG;AAChC,GA7BsB;AA+BtB,IAAO,yBAAQ;;;AC/Bf,SAAS,UAAAC,eAAc;AAUvB,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AAJS;AAUT,eAAe,aAAa,MAAM;AAChC,QAAM,KAAKC,QAAO,SAAS,gBAAgB,8BAA8B,eAAe,CAAC;AACzF,QAAM,MAAM,GAAG,OAAO,WAAW;AAEjC,MAAI,QAAQ,KAAK;AACjB,MAAI,KAAK,SAAS,SAAS,KAAK,KAAK,GAAG;AACtC,YAAQ,MAAM,YAAY,KAAK,MAAM,QAAQ,eAAO,gBAAgB,IAAI,GAAGC,WAAU,CAAC;AAAA,EACxF;AACA,QAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,MAAI;AAAA,IACF,kBACE,aACA,QACC,KAAK,aAAa,YAAY,KAAK,aAAa,MAAM;AAAA,IACvD,MACA,QACA;AAAA,EACJ;AAEA,aAAW,KAAK,KAAK,UAAU;AAC/B,MAAI,MAAM,WAAW,cAAc;AACnC,MAAI,MAAM,iBAAiB,KAAK;AAEhC,MAAI,MAAM,eAAe,QAAQ;AACjC,MAAI,KAAK,SAAS,8BAA8B;AAChD,SAAO,GAAG,KAAK;AACjB;AA1Be;AAkCf,IAAM,cAAc,8BAAO,aAAa,OAAO,SAAS,WAAW;AACjE,MAAI,aAAa,eAAe;AAChC,MAAI,OAAO,eAAe,UAAU;AAClC,iBAAa,WAAW,CAAC;AAAA,EAC3B;AAEA,MAAI,SAASA,WAAU,EAAE,UAAU,UAAU,GAAG;AAE9C,iBAAa,WAAW,QAAQ,WAAW,QAAQ;AACnD,QAAI,KAAK,eAAe,UAAU;AAClC,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO,eAAe,UAAU,EAAE;AAAA,QAChC;AAAA,QACA,CAAC,MAAM,aAAa,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,MACzC;AAAA,MACA,YAAY,QAAQ,MAAM,QAAQ,SAAS,QAAQ,IAAI;AAAA,IACzD;AACA,QAAI,aAAa,MAAM,aAAa,IAAI;AAExC,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW,SAAS,gBAAgB,8BAA8B,MAAM;AAC9E,aAAS,aAAa,SAAS,MAAM,QAAQ,UAAU,OAAO,CAAC;AAC/D,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,WAAW,MAAM,qBAAqB;AAAA,IAC/C,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC;AAAA,IACV;AAEA,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,SAAS,gBAAgB,8BAA8B,OAAO;AAC5E,YAAM,eAAe,wCAAwC,aAAa,UAAU;AACpF,YAAM,aAAa,MAAM,KAAK;AAC9B,YAAM,aAAa,KAAK,GAAG;AAC3B,UAAI,SAAS;AACX,cAAM,aAAa,SAAS,WAAW;AAAA,MACzC,OAAO;AACL,cAAM,aAAa,SAAS,KAAK;AAAA,MACnC;AACA,YAAM,cAAc,IAAI,KAAK;AAC7B,eAAS,YAAY,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF,GAhDoB;AAkDpB,IAAO,sBAAQ;;;ACxGR,IAAM,yBAAyB,wBACpC,GACA,GACA,YACA,aACA,WAEA;AAAA,EACE;AAAA,EACA,IAAI;AAAA,EACJ;AAAA;AAAA,EACA;AAAA,EACA,IAAI,aAAa;AAAA;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ,IAAI;AAAA;AAAA,EACJ;AAAA,EACA,IAAI,cAAc;AAAA;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,aAAa;AAAA,EACjB,IAAI;AAAA;AAAA,EACJ;AAAA,EACA,IAAI;AAAA;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,cAAc;AAAA;AAAA,EAClB;AAAA,EACA,IAAI;AAAA;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ;AAAA;AAAA,EACA;AAAA;AACF,EAAE,KAAK,GAAG,GApD0B;;;AHYtC,IAAM,OAAO,8BAAO,QAAQ,SAAS;AACnC,MAAI,KAAK,+BAA+B,KAAK,IAAI,IAAI;AACrD,QAAM,aAAaC,WAAU;AAC7B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,YAAY,cAAc,IAAI;AAEtC,QAAM,EAAE,aAAa,YAAY,cAAc,iBAAiB,IAAI,cAAc,IAAI;AAGtF,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,aAAa,KAAK,UAAU,EAC1C,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,aAAa,KAAK,IAAI;AAE9B,QAAM,gBAAgB,SAAS,WAAW,UAAU,UAAU;AAG9D,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AAEnE,QAAMC,QAAO,MAAM,WAAW,SAAS,KAAK,OAAO;AAAA,IACjD,OAAO,KAAK;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AAGD,MAAI,OAAOA,MAAK,QAAQ;AAExB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAMA,MAAK,SAAS,CAAC;AAC3B,UAAM,KAAKC,QAAOD,KAAI;AACtB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAEA,QAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK;AACzF,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AAEA,QAAM,SAAS,KAAK;AACpB,QAAM,IAAI,KAAK,IAAI,QAAQ;AAC3B,QAAM,IAAI,KAAK,IAAI,SAAS;AAE5B,MAAI,MAAM,SAAS,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,MAAIE;AACJ,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAK,MAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,WAAW;AAAA,MACX,MAAM;AAAA;AAAA,MAEN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,UAAM,YAAY,GAAG,KAAK,uBAAuB,GAAG,GAAG,OAAO,QAAQ,CAAC,GAAG,OAAO;AACjF,IAAAA,QAAO,SAAS,OAAO,MAAM;AAC3B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AAEjB,IAAAA,MAAK,OAAO,mBAAmB,EAAE,KAAK,SAAS,aAAa,KAAK,GAAG,CAAC;AACrE,IAAAA,MAAK,OAAO,MAAM,EAAE,KAAK,SAAS,iBAAiB,KAAK,GAAG,EAAE,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACxF,OAAO;AAEL,IAAAA,QAAO,SAAS,OAAO,QAAQ,cAAc;AAE7C,IAAAA,MACG,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM;AAAA,EAC1B;AACA,QAAM,EAAE,uBAAuB,IAAI,wBAAwB,UAAU;AACrE,UAAQ;AAAA,IACN;AAAA;AAAA,IAEA,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,sBAAsB;AAAA,EAC5F;AAEA,MAAI,aAAa;AACf,UAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,QAAI,MAAM;AACR,WAAK,KAAK,SAAS,WAAW;AAAA,IAChC;AAAA,EACF;AAGA,QAAM,UAAUA,MAAK,KAAK,EAAE,QAAQ;AACpC,OAAK,UAAU;AACf,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AAEtB,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAE5C,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAc,MAAM,KAAK;AAAA,EAClC;AAEA,SAAO,EAAE,SAAS,UAAU,WAAW,KAAK;AAC9C,GA7Ga;AAsHb,IAAM,YAAY,wBAAC,QAAQ,SAAS;AAElC,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,EAAE;AAGpF,QAAMA,QAAO,SAAS,OAAO,QAAQ,cAAc;AAEnD,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,UAAU;AAG9B,EAAAA,MACG,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI,WAAW,EAC/C,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,WAAW,EAChD,KAAK,SAAS,KAAK,QAAQ,OAAO,EAClC,KAAK,UAAU,KAAK,SAAS,OAAO,EACpC,KAAK,QAAQ,MAAM;AAEtB,QAAM,UAAUA,MAAK,KAAK,EAAE,QAAQ;AACpC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AAEtB,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAc,MAAM,KAAK;AAAA,EAClC;AAEA,SAAO,EAAE,SAAS,UAAU,WAAW,EAAE,OAAO,GAAG,QAAQ,EAAE,EAAE;AACjE,GA7BkB;AA+BlB,IAAM,mBAAmB,8BAAO,QAAQ,SAAS;AAC/C,QAAM,aAAaH,WAAU;AAE7B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,eAAe,qBAAqB,0BAA0B,WAAW,IAC/E;AAGF,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,KAAK,UAAU,EAC7B,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,WAAW,KAAK,EAAE,EACvB,KAAK,aAAa,KAAK,IAAI;AAG9B,QAAM,aAAa,SAAS,OAAO,KAAK,cAAc;AAGtD,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAChE,MAAI,YAAY,SAAS,OAAO,MAAM;AAEtC,QAAMC,QAAO,MACV,KAAK,EACL,YAAY,MAAM,oBAAY,KAAK,OAAO,KAAK,YAAY,QAAW,IAAI,CAAC;AAG9E,MAAI,OAAOA,MAAK,QAAQ;AAExB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAMA,MAAK,SAAS,CAAC;AAC3B,UAAM,KAAKC,QAAOD,KAAI;AACtB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAGA,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,UAAU;AAE9B,QAAM,SACH,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK,SAAS;AACvF,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AAEA,QAAM,SAAS,KAAK,SAAS;AAE7B,QAAM,cAAc,KAAK,SAAS,UAAU,KAAK,SAAS;AAC1D,QAAM,IAAI,KAAK,IAAI,QAAQ;AAC3B,QAAM,IAAI,KAAK,IAAI,SAAS;AAC5B,OAAK,QAAQ;AACb,QAAM,SAAS,KAAK,IAAI,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS;AAGtE,MAAIE;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,QAAQ,KAAK,WAAW,SAAS,0BAA0B;AACjE,UAAM,KAAK,MAAM,IAAI,QAAQ;AAC7B,UAAM,iBACJ,KAAK,MAAM,KAAK,KACZ,GAAG,KAAK,uBAAuB,GAAG,GAAG,OAAO,QAAQ,EAAE,GAAG;AAAA,MACvD,WAAW;AAAA,MACX,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC,IACD,GAAG,UAAU,GAAG,GAAG,OAAO,QAAQ,EAAE,MAAM,cAAc,CAAC;AAE/D,IAAAA,QAAO,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAC3D,UAAM,iBAAiB,GAAG,UAAU,GAAG,QAAQ,OAAO,aAAa;AAAA,MACjE,MAAM,QAAQ,gBAAgB;AAAA,MAC9B,WAAW,QAAQ,YAAY;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAED,IAAAA,QAAO,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAC3D,gBAAY,SAAS,OAAO,MAAM,cAAc;AAAA,EAClD,OAAO;AACL,IAAAA,QAAO,WAAW,OAAO,QAAQ,cAAc;AAC/C,UAAM,iBAAiB;AAGvB,IAAAA,MACG,KAAK,SAAS,cAAc,EAC5B,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,aAAa,KAAK,IAAI;AAC9B,cACG,KAAK,SAAS,OAAO,EACrB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,MAAM,EAChB,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,WAAW;AAAA,EAC/B;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,IAAI,KAAK,SAAS,WAAW,UAAU,UAAU,IAAI,IAAI,EAAE;AAAA,EACtG;AAEA,QAAM,UAAUA,MAAK,KAAK,EAAE,QAAQ;AACpC,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU;AAEf,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAC5C,OAAK,YAAY;AAEjB,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAc,MAAM,KAAK;AAAA,EAClC;AAEA,SAAO,EAAE,SAAS,UAAU,WAAW,KAAK;AAC9C,GAxHyB;AAyHzB,IAAM,gBAAgB,8BAAO,QAAQ,SAAS;AAC5C,MAAI,KAAK,+BAA+B,KAAK,IAAI,IAAI;AACrD,QAAM,aAAaH,WAAU;AAC7B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,YAAY,cAAc,IAAI;AAEtC,QAAM,EAAE,aAAa,YAAY,cAAc,iBAAiB,IAAI,cAAc,IAAI;AAGtF,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,aAAa,KAAK,UAAU,EAC1C,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,aAAa,KAAK,IAAI;AAE9B,QAAM,gBAAgB,SAAS,WAAW,UAAU,UAAU;AAG9D,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AAEnE,QAAMC,QAAO,MAAM,WAAW,SAAS,KAAK,OAAO;AAAA,IACjD,OAAO,KAAK;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,KAAK;AAAA,EACd,CAAC;AAGD,MAAI,OAAOA,MAAK,QAAQ;AAExB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAMA,MAAK,SAAS,CAAC;AAC3B,UAAM,KAAKC,QAAOD,KAAI;AACtB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAEA,QAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK;AACzF,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AAEA,QAAM,SAAS,KAAK;AACpB,QAAM,IAAI,KAAK,IAAI,QAAQ;AAC3B,QAAM,IAAI,KAAK,IAAI,SAAS;AAE5B,MAAI,MAAM,SAAS,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,MAAIE;AACJ,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAK,MAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,WAAW;AAAA,MACX,MAAM;AAAA;AAAA,MAEN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,UAAM,YAAY,GAAG,KAAK,uBAAuB,GAAG,GAAG,OAAO,QAAQ,KAAK,EAAE,GAAG,OAAO;AACvF,IAAAA,QAAO,SAAS,OAAO,MAAM;AAC3B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AAEjB,IAAAA,MAAK,OAAO,mBAAmB,EAAE,KAAK,SAAS,aAAa,KAAK,GAAG,CAAC;AACrE,IAAAA,MAAK,OAAO,MAAM,EAAE,KAAK,SAAS,iBAAiB,KAAK,GAAG,EAAE,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACxF,OAAO;AAEL,IAAAA,QAAO,SAAS,OAAO,QAAQ,cAAc;AAE7C,IAAAA,MACG,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM;AAAA,EAC1B;AACA,QAAM,EAAE,uBAAuB,IAAI,wBAAwB,UAAU;AACrE,UAAQ;AAAA,IACN;AAAA;AAAA,IAEA,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,sBAAsB;AAAA,EAC5F;AAEA,MAAI,aAAa;AACf,UAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,QAAI,MAAM;AACR,WAAK,KAAK,SAAS,WAAW;AAAA,IAChC;AAAA,EACF;AAGA,QAAM,UAAUA,MAAK,KAAK,EAAE,QAAQ;AACpC,OAAK,UAAU;AACf,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AAEtB,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAE5C,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAc,MAAM,KAAK;AAAA,EAClC;AAEA,SAAO,EAAE,SAAS,UAAU,WAAW,KAAK;AAC9C,GA9GsB;AA+GtB,IAAM,UAAU,wBAAC,QAAQ,SAAS;AAChC,QAAM,aAAaH,WAAU;AAE7B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,WAAW,IAAI;AAGvB,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,KAAK,UAAU,EAC7B,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,aAAa,KAAK,IAAI;AAG9B,QAAM,aAAa,SAAS,OAAO,KAAK,cAAc;AAEtD,QAAM,UAAU,IAAI,KAAK;AAEzB,QAAM,QAAQ,KAAK,QAAQ;AAE3B,OAAK,OAAO,CAAC,KAAK;AAElB,QAAM,SAAS,KAAK,SAAS;AAE7B,QAAM,IAAI,KAAK,IAAI,QAAQ;AAC3B,QAAM,IAAI,KAAK,IAAI,SAAS;AAC5B,OAAK,QAAQ;AAGb,MAAIG;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,MAAM,IAAI,QAAQ;AAC7B,UAAM,iBAAiB,GAAG,UAAU,GAAG,GAAG,OAAO,QAAQ;AAAA,MACvD,MAAM;AAAA,MACN,WAAW;AAAA,MACX,gBAAgB,CAAC,CAAC;AAAA,MAClB,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAED,IAAAA,QAAO,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAAA,EAC7D,OAAO;AACL,IAAAA,QAAO,WAAW,OAAO,QAAQ,cAAc;AAC/C,UAAM,iBAAiB;AAGvB,IAAAA,MACG,KAAK,SAAS,cAAc,EAC5B,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,aAAa,KAAK,IAAI;AAAA,EAChC;AAEA,QAAM,UAAUA,MAAK,KAAK,EAAE,QAAQ;AACpC,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU;AAEf,OAAK,UAAU;AAEf,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAc,MAAM,KAAK;AAAA,EAClC;AAEA,SAAO,EAAE,SAAS,UAAU,WAAW,CAAC,EAAE;AAC5C,GAlEgB;AAoEhB,IAAM,aAAa;AACnB,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,eAAe,oBAAI,IAAI;AASpB,IAAM,gBAAgB,8BAAO,MAAM,SAAS;AACjD,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,UAAU,MAAM,OAAO,KAAK,EAAE,MAAM,IAAI;AAC9C,eAAa,IAAI,KAAK,IAAI,OAAO;AACjC,SAAO;AACT,GAL6B;AAetB,IAAM,QAAQ,6BAAM;AACzB,iBAAe,oBAAI,IAAI;AACzB,GAFqB;;;AI/erB,SAAS,cAAc,MAAM,OAAO;AAClC,SAAO,KAAK,UAAU,KAAK;AAC7B;AAFS;AAIT,IAAO,yBAAQ;;;ACJf,SAAS,iBAAiB,MAAM,IAAI,IAAI,OAAO;AAG7C,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AAEd,MAAI,KAAK,KAAK,MAAM;AACpB,MAAI,KAAK,KAAK,MAAM;AAEpB,MAAI,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEzD,MAAI,KAAK,KAAK,IAAK,KAAK,KAAK,KAAM,GAAG;AACtC,MAAI,MAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AACA,MAAI,KAAK,KAAK,IAAK,KAAK,KAAK,KAAM,GAAG;AACtC,MAAI,MAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AAEA,SAAO,EAAE,GAAG,KAAK,IAAI,GAAG,KAAK,GAAG;AAClC;AArBS;AAuBT,IAAO,4BAAQ;;;ACrBf,SAAS,gBAAgB,MAAM,IAAI,OAAO;AACxC,SAAO,0BAAiB,MAAM,IAAI,IAAI,KAAK;AAC7C;AAFS;AAIT,IAAO,2BAAQ;;;ACHf,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI;AAIrC,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,OAAO,QAAQ;AACnB,MAAI,GAAG;AAIP,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAG7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAI7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AAGA,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAG7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAK7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AAGA,UAAQ,KAAK,KAAK,KAAK;AACvB,MAAI,UAAU,GAAG;AACf;AAAA,EACF;AAEA,WAAS,KAAK,IAAI,QAAQ,CAAC;AAK3B,QAAM,KAAK,KAAK,KAAK;AACrB,MAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AAExD,QAAM,KAAK,KAAK,KAAK;AACrB,MAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AAExD,SAAO,EAAE,GAAM,EAAK;AACtB;AA3DS;AA6DT,SAAS,SAAS,IAAI,IAAI;AACxB,SAAO,KAAK,KAAK;AACnB;AAFS;AAIT,IAAO,yBAAQ;;;AC9Df,SAAS,iBAAiB,MAAM,YAAY,OAAO;AACjD,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AAEd,MAAI,gBAAgB,CAAC;AAErB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,WAAW,YAAY,YAAY;AAC5C,eAAW,QAAQ,SAAU,OAAO;AAClC,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH,OAAO;AACL,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAAA,EACpC;AAEA,MAAI,OAAO,KAAK,KAAK,QAAQ,IAAI;AACjC,MAAI,MAAM,KAAK,KAAK,SAAS,IAAI;AAEjC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,KAAK,WAAW,CAAC;AACrB,QAAI,KAAK,WAAW,IAAI,WAAW,SAAS,IAAI,IAAI,IAAI,CAAC;AACzD,QAAI,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,MAChC,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,IAClC;AACA,QAAI,WAAW;AACb,oBAAc,KAAK,SAAS;AAAA,IAC9B;AAAA,EACF;AAEA,MAAI,CAAC,cAAc,QAAQ;AACzB,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,SAAS,GAAG;AAE5B,kBAAc,KAAK,SAAU,GAAG,GAAG;AACjC,UAAI,MAAM,EAAE,IAAI,MAAM;AACtB,UAAI,MAAM,EAAE,IAAI,MAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAE3C,UAAI,MAAM,EAAE,IAAI,MAAM;AACtB,UAAI,MAAM,EAAE,IAAI,MAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAE3C,aAAO,QAAQ,QAAQ,KAAK,UAAU,QAAQ,IAAI;AAAA,IACpD,CAAC;AAAA,EACH;AACA,SAAO,cAAc,CAAC;AACxB;AAtDS;AAwDT,IAAO,4BAAQ;;;ACpDf,IAAO,oBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACXA,OAAOC,YAAW;AAIX,SAAS,OAAqC,QAAwB,MAAY;AACvF,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,UAAU,eAAe,IAAI;AACnC,MAAI,aAAa;AACjB,MAAI,CAAC,SAAS;AACZ,iBAAa;AAAA,EACf;AACA,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,QAAM,SAAS;AAEf,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,WAAW,QAAQ,CAAC;AAE7F,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACrD,QAAM,aAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAClE,aAAW,KAAK,SAAS,QAAQ,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAE/E,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,oBAAoB,MAAM,QAAQ,KAAK;AAChD,WAAO,kBAAU,OAAO,MAAM,QAAQ,KAAK;AAAA,EAC7C;AAEA,SAAO;AACT;AApCgB;;;ACLhB,OAAOC,YAAW;AAGlB,SAAS,kBACP,IACA,IACA,IACA,IACA,IACA,IACA,WACA;AACA,QAAM,YAAY;AAElB,QAAM,QAAQ,KAAK,MAAM;AACzB,QAAM,QAAQ,KAAK,MAAM;AAGzB,QAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;AAGzC,QAAM,MAAM,KAAK,MAAM;AACvB,QAAM,MAAM,KAAK,MAAM;AAGvB,QAAM,eAAe,KAAK;AAC1B,QAAM,eAAe,KAAK;AAG1B,QAAM,WAAW,KAAK,KAAK,gBAAgB,IAAI,gBAAgB,CAAC;AAGhE,MAAI,WAAW,GAAG;AAChB,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACtF;AAGA,QAAM,uBAAuB,KAAK,KAAK,IAAI,YAAY,CAAC;AAGxD,QAAM,UAAU,OAAO,uBAAuB,KAAK,KAAK,IAAI,KAAK,KAAK,YAAY,KAAK;AACvF,QAAM,UAAU,OAAO,uBAAuB,KAAK,KAAK,IAAI,KAAK,KAAK,YAAY,KAAK;AAGvF,QAAM,aAAa,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,WAAW,EAAE;AACtE,QAAM,WAAW,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,WAAW,EAAE;AAGpE,MAAI,aAAa,WAAW;AAC5B,MAAI,aAAa,aAAa,GAAG;AAC/B,kBAAc,IAAI,KAAK;AAAA,EACzB;AACA,MAAI,CAAC,aAAa,aAAa,GAAG;AAChC,kBAAc,IAAI,KAAK;AAAA,EACzB;AAGA,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,IAAI,KAAK,YAAY;AAC3B,UAAMC,SAAQ,aAAa,IAAI;AAC/B,UAAM,IAAI,UAAU,KAAK,KAAK,IAAIA,MAAK;AACvC,UAAM,IAAI,UAAU,KAAK,KAAK,IAAIA,MAAK;AACvC,WAAO,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,EACtB;AAEA,SAAO;AACT;AAhES;AAkET,eAAsB,WAAyC,QAAwB,MAAY;AACjG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,IAAI,KAAK,QAAQ,KAAK,UAAU;AACtC,QAAM,IAAI,KAAK,SAAS,KAAK;AAE7B,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,MAAM,MAAM,IAAI;AAG3B,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IACtB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IACvB,GAAG,kBAAkB,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,KAAK;AAAA,IACjE,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACrB,GAAG,kBAAkB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI;AAAA,EAChE;AAGA,QAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,iBAAiB,qBAAqB,MAAM;AAClD,QAAM,sBAAsB,GAAG,KAAK,gBAAgB,OAAO;AAC3D,QAAM,kBAAkB,SAAS,OAAO,MAAM,qBAAqB,cAAc;AAEjF,kBAAgB,KAAK,SAAS,uBAAuB;AAErD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,oBAAgB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC3D;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,oBAAgB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC5D;AAEA,kBAAgB,KAAK,aAAa,aAAa,KAAK,CAAC,MAAM;AAE3D,mBAAiB,MAAM,eAAe;AAEtC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AArDsB;;;ACrEtB,OAAOC,YAAW;;;ACFX,SAAS,mBACd,QACA,GACA,GACA,QACA;AACA,SAAO,OACJ,OAAO,WAAW,cAAc,EAChC;AAAA,IACC;AAAA,IACA,OACG,IAAI,SAAU,GAAG;AAChB,aAAO,EAAE,IAAI,MAAM,EAAE;AAAA,IACvB,CAAC,EACA,KAAK,GAAG;AAAA,EACb,EACC,KAAK,SAAS,iBAAiB,EAC/B,KAAK,aAAa,eAAe,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG;AAChE;AAlBgB;;;ADchB,eAAsB,KAAmC,QAAwB,MAAY;AAC3F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,UAAU;AAChB,QAAM,IAAI,KAAK,QAAQ,KAAK,UAAU;AACtC,QAAM,OAAO;AACb,QAAM,QAAQ;AACd,QAAM,MAAM,CAAC;AACb,QAAM,SAAS;AACf,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,OAAO,SAAS,GAAG,IAAI;AAAA,IAC5B,EAAE,GAAG,OAAO,GAAG,IAAI;AAAA,IACnB,EAAE,GAAG,OAAO,GAAG,OAAO;AAAA,IACtB,EAAE,GAAG,MAAM,GAAG,OAAO;AAAA,IACrB,EAAE,GAAG,MAAM,GAAG,MAAM,QAAQ;AAAA,IAC5B,EAAE,GAAG,OAAO,SAAS,GAAG,IAAI;AAAA,EAC9B;AAEA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,cAAU,SACP,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAAA,EACrD;AAEA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AAEA,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AArDsB;;;AEdtB,OAAOC,YAAW;AAKX,SAAS,OAAqC,QAAwB,MAAY;AACvF,QAAM,EAAE,WAAW,IAAI,cAAc,IAAI;AACzC,OAAK,QAAQ;AACb,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,IAAI,CAAC,EAClC,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACnC,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,IAAI,KAAK,IAAI,IAAI,KAAK,SAAS,CAAC;AAEtC,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,IAAI,EAAE;AAAA,IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE;AAAA,EACpB;AAGA,QAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,aAAa,qBAAqB,MAAM;AAC9C,QAAM,YAAY,GAAG,KAAK,YAAY,OAAO;AAC7C,QAAM,cAAc,SAAS,OAAO,MAAM,WAAW,cAAc;AAEnE,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACvD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACxD;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AA/CgB;;;ACFhB,OAAOC,YAAW;AAIlB,eAAsB,OAAqC,QAAwB,MAAY;AAC7F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE5F,QAAM,SAAS,KAAK,QAAQ,IAAI;AAChC,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,YAAY,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AAErD,iBAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAC5D,eAAW,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,EAChG,OAAO;AACL,iBAAa,SACV,OAAO,UAAU,cAAc,EAC/B,KAAK,SAAS,uBAAuB,EACrC,KAAK,SAAS,UAAU,EACxB,KAAK,KAAK,MAAM,EAChB,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC;AAAA,EACjB;AAEA,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,oBAAoB,MAAM,QAAQ,KAAK;AAChD,WAAO,kBAAU,OAAO,MAAM,QAAQ,KAAK;AAAA,EAC7C;AAEA,SAAO;AACT;AAnCsB;;;ACLtB,OAAOC,YAAW;AAIlB,SAAS,WAAW,GAAW;AAC7B,QAAM,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC;AACpC,QAAM,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC;AACpC,QAAM,aAAa,IAAI;AAEvB,QAAM,UAAU,EAAE,GAAI,aAAa,IAAK,SAAS,GAAI,aAAa,IAAK,QAAQ;AAC/E,QAAM,UAAU,EAAE,GAAG,EAAE,aAAa,KAAK,SAAS,GAAI,aAAa,IAAK,QAAQ;AAChF,QAAM,UAAU,EAAE,GAAG,EAAE,aAAa,KAAK,SAAS,GAAG,EAAE,aAAa,KAAK,QAAQ;AACjF,QAAM,UAAU,EAAE,GAAI,aAAa,IAAK,SAAS,GAAG,EAAE,aAAa,KAAK,QAAQ;AAEhF,SAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,uBACzC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACzE;AAZS;AAcF,SAAS,cAA4C,QAAwB,MAAY;AAC9F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,OAAK,QAAQ;AACb,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,IAAI,CAAC,EAClC,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACnC,QAAM,SAAS,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AAC5C,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,aAAa,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACtD,QAAM,WAAW,WAAW,MAAM;AAClC,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAE1C,QAAMC,iBAAgB,SAAS,OAAO,MAAM,YAAY,cAAc;AACtE,EAAAA,eAAc,OAAO,MAAM,QAAQ;AAEnC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,eAAc,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACzD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,eAAc,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC1D;AAEA,mBAAiB,MAAMA,cAAa;AAEpC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,2BAA2B,MAAM,EAAE,QAAQ,MAAM,CAAC;AAC3D,UAAM,MAAM,kBAAU,OAAO,MAAM,QAAQ,KAAK;AAChD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA5CgB;;;AClBhB,OAAOC,YAAW;AAGlB,SAASC,sBACP,SACA,SACA,QACA,YAAY,KACZ,aAAa,GACb,WAAW,KACX;AACA,QAAM,SAAS,CAAC;AAGhB,QAAM,gBAAiB,aAAa,KAAK,KAAM;AAC/C,QAAM,cAAe,WAAW,KAAK,KAAM;AAG3C,QAAM,aAAa,cAAc;AAGjC,QAAM,YAAY,cAAc,YAAY;AAE5C,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,QAAQ,gBAAgB,IAAI;AAClC,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;AAAA,EAC9B;AAEA,SAAO;AACT;AA5BS,OAAAA,uBAAA;AA8BT,eAAsB,eACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAM,IAAI,KAAK,UAAU,KAAK,WAAW;AACzC,QAAM,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG;AAElC,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,SAAS;AAAA,IACb,GAAGA,sBAAqB,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IACzD,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,OAAO;AAAA,IAChC,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC3E,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IACzE,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,EAAE;AAAA,IAChC,GAAGA,sBAAqB,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,EACzD;AAEA,QAAM,aAAa;AAAA,IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,OAAO;AAAA,IAChC,GAAGA,sBAAqB,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IACzD,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IACjC,GAAGA,sBAAqB,IAAI,IAAI,IAAI,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IACxE,GAAGA,sBAAqB,IAAI,IAAI,IAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IACtE,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,IAC/B,GAAGA,sBAAqB,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,IACvD,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,OAAO;AAAA,EAChC;AAGA,QAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC;AAExD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,qBAAqB,qBAAqB,MAAM;AACtD,QAAM,oBAAoB,mBAAmB,QAAQ,KAAK,EAAE;AAC5D,QAAM,qBAAqB,GAAG,KAAK,mBAAmB,OAAO;AAC7D,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,YAAY,GAAG,KAAK,UAAU,EAAE,GAAG,QAAQ,CAAC;AAClD,QAAM,sBAAsB,SAAS,OAAO,KAAK,cAAc;AAC/D,sBAAoB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACpF,sBAAoB,OAAO,MAAM,oBAAoB,cAAc;AACnE,sBAAoB,KAAK,SAAS,MAAM;AAExC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,wBAAoB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC/D;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,wBAAoB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAChE;AAEA,sBAAoB,KAAK,aAAa,aAAa,MAAM,MAAM;AAE/D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,IAAI,UAAU,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC7H;AAEA,mBAAiB,MAAM,mBAAmB;AAE1C,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,YAAY,KAAK;AAErD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA7EsB;;;ACjCtB,OAAOC,YAAW;AAGlB,SAASC,sBACP,SACA,SACA,QACA,YAAY,KACZ,aAAa,GACb,WAAW,KACX;AACA,QAAM,SAAS,CAAC;AAGhB,QAAM,gBAAiB,aAAa,KAAK,KAAM;AAC/C,QAAM,cAAe,WAAW,KAAK,KAAM;AAG3C,QAAM,aAAa,cAAc;AAGjC,QAAM,YAAY,cAAc,YAAY;AAE5C,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,QAAQ,gBAAgB,IAAI;AAClC,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,EACtB;AAEA,SAAO;AACT;AA5BS,OAAAA,uBAAA;AA8BT,eAAsB,gBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAM,IAAI,KAAK,UAAU,KAAK,WAAW;AACzC,QAAM,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG;AAElC,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,SAAS;AAAA,IACb,GAAGA,sBAAqB,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IACzD,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IAChC,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC3E,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IACzE,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,IAC9B,GAAGA,sBAAqB,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,EACzD;AAEA,QAAM,aAAa;AAAA,IACjB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,OAAO;AAAA,IAChC,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,OAAO;AAAA,IAC/B,GAAGA,sBAAqB,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IACzD,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IAChC,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC3E,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IACzE,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,IAC9B,GAAGA,sBAAqB,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,IACvD,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,OAAO;AAAA,IAC9B,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,OAAO;AAAA,EACjC;AAGA,QAAM,KAAKC,OAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC;AAExD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,sBAAsB,qBAAqB,MAAM;AACvD,QAAM,oBAAoB,oBAAoB,QAAQ,KAAK,EAAE;AAC7D,QAAM,sBAAsB,GAAG,KAAK,mBAAmB,OAAO;AAC9D,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,YAAY,GAAG,KAAK,UAAU,EAAE,GAAG,QAAQ,CAAC;AAClD,QAAM,uBAAuB,SAAS,OAAO,KAAK,cAAc;AAChE,uBAAqB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACrF,uBAAqB,OAAO,MAAM,qBAAqB,cAAc;AACrE,uBAAqB,KAAK,SAAS,MAAM;AAEzC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,yBAAqB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAChE;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,yBAAqB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACjE;AAEA,uBAAqB,KAAK,aAAa,aAAa,CAAC,MAAM,MAAM;AAEjE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9I;AAEA,mBAAiB,MAAM,oBAAoB;AAE3C,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,YAAY,KAAK;AAErD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA7EsB;;;ACjCtB,OAAOC,aAAW;AAGlB,SAASC,sBACP,SACA,SACA,QACA,YAAY,KACZ,aAAa,GACb,WAAW,KACX;AACA,QAAM,SAAS,CAAC;AAGhB,QAAM,gBAAiB,aAAa,KAAK,KAAM;AAC/C,QAAM,cAAe,WAAW,KAAK,KAAM;AAG3C,QAAM,aAAa,cAAc;AAGjC,QAAM,YAAY,cAAc,YAAY;AAE5C,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,QAAQ,gBAAgB,IAAI;AAClC,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;AAAA,EAC9B;AAEA,SAAO;AACT;AA5BS,OAAAA,uBAAA;AA8BT,eAAsB,YACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAM,IAAI,KAAK,UAAU,KAAK,WAAW;AACzC,QAAM,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG;AAElC,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,uBAAuB;AAAA,IAC3B,GAAGA,sBAAqB,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IACzD,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,OAAO;AAAA,IAChC,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC3E,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IACzE,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,EAAE;AAAA,IAChC,GAAGA,sBAAqB,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,EACzD;AAEA,QAAM,wBAAwB;AAAA,IAC5B,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,SAAS,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,IAAI;AAAA,IACnF,EAAE,GAAG,IAAI,IAAI,SAAS,GAAG,GAAG,OAAO;AAAA,IACnC,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,GAAG,EAAE;AAAA,IACvE,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,CAAC;AAAA,IACvE,EAAE,GAAG,IAAI,IAAI,SAAS,GAAG,GAAG,CAAC,OAAO;AAAA,IACpC,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,SAAS,GAAG,IAAI,GAAG,QAAQ,IAAI,MAAM,IAAI;AAAA,EACrF;AAEA,QAAM,aAAa;AAAA,IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,OAAO;AAAA,IAChC,GAAGA,sBAAqB,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IACzD,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IACjC,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC3E,GAAGA,sBAAqB,IAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IACzE,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,IAC/B,GAAGA,sBAAqB,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,IACvD,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAG,IAAI,IAAI,SAAS,SAAS,GAAG,GAAG,IAAI,IAAI,OAAO;AAAA,IACpD,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,SAAS,GAAG,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,IAAI;AAAA,IACnF,EAAE,GAAG,IAAI,IAAI,SAAS,GAAG,GAAG,OAAO;AAAA,IACnC,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,GAAG,EAAE;AAAA,IACvE,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,CAAC;AAAA,IACvE,EAAE,GAAG,IAAI,IAAI,SAAS,GAAG,GAAG,CAAC,OAAO;AAAA,IACpC,GAAGA,sBAAqB,CAAC,IAAI,IAAI,SAAS,SAAS,GAAG,IAAI,GAAG,QAAQ,IAAI,MAAM,IAAI;AAAA,EACrF;AAGA,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC;AAExD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,qBAAqB,qBAAqB,oBAAoB;AACpE,QAAM,wBAAwB,mBAAmB,QAAQ,KAAK,EAAE;AAChE,QAAM,qBAAqB,GAAG,KAAK,uBAAuB,OAAO;AACjE,QAAM,sBAAsB,qBAAqB,qBAAqB;AACtE,QAAM,yBAAyB,oBAAoB,QAAQ,KAAK,EAAE;AAClE,QAAM,sBAAsB,GAAG,KAAK,wBAAwB,OAAO;AACnE,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,YAAY,GAAG,KAAK,UAAU,EAAE,GAAG,QAAQ,CAAC;AAClD,QAAM,mBAAmB,SAAS,OAAO,KAAK,cAAc;AAC5D,mBAAiB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACjF,mBAAiB,OAAO,MAAM,oBAAoB,cAAc;AAChE,mBAAiB,OAAO,MAAM,qBAAqB,cAAc;AACjE,mBAAiB,KAAK,SAAS,MAAM;AAErC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,qBAAiB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC5D;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,qBAAiB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC7D;AAEA,mBAAiB,KAAK,aAAa,aAAa,SAAS,SAAS,CAAC,MAAM;AAEzE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9I;AAEA,mBAAiB,MAAM,gBAAgB;AAEvC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,YAAY,KAAK;AAErD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAhGsB;;;AC3BtB,OAAOC,aAAW;AAGlB,eAAsB,gBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,WAAW,IACf,YAAY;AACd,QAAM,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,KAAK,WAAW,KAAK,KAAK,MAAM,MAAM,SAAS,CAAC;AAC5F,QAAM,IAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACtF,QAAM,SAAS,IAAI;AAEnB,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,aAAa,GACjB,cAAc;AAChB,QAAM,KAAK,aAAa;AACxB,QAAM,KAAK,cAAc;AAEzB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,cAAc,EAAE;AAAA,IAC3B,EAAE,GAAG,IAAI,GAAG,YAAY;AAAA,IACxB,EAAE,GAAG,IAAI,GAAG,YAAY;AAAA,IACxB,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,GAAG,QAAQ,IAAI,KAAK,EAAE;AAAA,EACpE;AAEA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAE7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,UAAQ,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;AAE3D,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA7DsB;;;ACTtB,OAAOC,aAAW;AAIX,IAAM,sBAAsB,wBACjC,GACA,GACA,OACA,QACA,IACA,OACW;AACX,SAAO;AAAA,IACL,IAAI,CAAC,IAAI,IAAI,EAAE;AAAA,IACf,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,EACf,EAAE,KAAK,GAAG;AACZ,GAhBmC;AAiB5B,IAAM,2BAA2B,wBACtC,GACA,GACA,OACA,QACA,IACA,OACW;AACX,SAAO;AAAA,IACL,IAAI,CAAC,IAAI,IAAI,EAAE;AAAA,IACf,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,IACvB,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,EACf,EAAE,KAAK,GAAG;AACZ,GAhBwC;AAiBjC,IAAM,2BAA2B,wBACtC,GACA,GACA,OACA,QACA,IACA,OACW;AACX,SAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AACvF,GATwC;AAUxC,eAAsB,SAAuC,QAAwB,MAAY;AAC/F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS,KAAK,SAAS,CAAC;AAC7D,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,MAAM,MAAM,IAAI;AAC3B,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,UAAU,CAAC;AAEpE,MAAIC;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,gBAAgB,yBAAyB,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;AACjE,UAAM,gBAAgB,yBAAyB,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAClE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,CAAC,CAAC,CAAC;AACpE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC,CAAC;AAElF,IAAAD,YAAW,SAAS,OAAO,MAAM,WAAW,cAAc;AAC1D,IAAAA,YAAW,SAAS,OAAO,MAAM,WAAW,cAAc;AAC1D,IAAAA,UAAS,KAAK,SAAS,uBAAuB;AAC9C,QAAI,WAAW;AACb,MAAAA,UAAS,KAAK,SAAS,SAAS;AAAA,IAClC;AAAA,EACF,OAAO;AACL,UAAM,WAAW,oBAAoB,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;AACvD,IAAAA,YAAW,SACR,OAAO,QAAQ,cAAc,EAC7B,KAAK,KAAK,QAAQ,EAClB,KAAK,SAAS,uBAAuB,EACrC,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAC5C,KAAK,SAAS,UAAU;AAAA,EAC7B;AAEA,EAAAA,UAAS,KAAK,kBAAkB,EAAE;AAClC,EAAAA,UAAS,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,GAAG;AAEnE,mBAAiB,MAAMA,SAAQ;AAE/B,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,WAAW,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9I;AAEA,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,KAAK,MAAM,KAAK;AACtC,UAAM,IAAI,IAAI,KAAK,KAAK,KAAK;AAE7B,QACE,MAAM,MACL,KAAK,IAAI,CAAC,KAAK,KAAK,SAAS,KAAK,KAChC,KAAK,IAAI,CAAC,MAAM,KAAK,SAAS,KAAK,KAClC,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,UAAU,KAAK,IAAI,KAC/D;AACA,UAAI,IAAI,KAAK,MAAM,IAAK,IAAI,KAAM,KAAK;AACvC,UAAI,IAAI,GAAG;AACT,YAAI,KAAK,KAAK,CAAC;AAAA,MACjB;AACA,UAAI,KAAK;AACT,UAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,YAAI,CAAC;AAAA,MACP;AAEA,UAAI,KAAK;AAAA,IACX;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAxEsB;;;AChDtB,OAAOE,aAAW;AAGlB,eAAsB,iBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,aAAa,IAAI;AAEvB,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI,IAAI,aAAa;AAEhC,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,MAAM;AAAA,IACV,EAAE,GAAG,GAAG,IAAI,WAAW;AAAA,IACvB,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,WAAW;AAAA,IAC3B,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE;AAAA,IACf,EAAE,GAAG,GAAG,CAAC,EAAE;AAAA,IACX,EAAE,GAAG,EAAE;AAAA,IACP,EAAE,GAAG,CAAC,GAAG,EAAE;AAAA,IACX,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,WAAW;AAAA,EAC7B;AAEA,QAAM,OAAO,GAAG;AAAA,IACd,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,IACzB;AAAA,EACF;AAEA,QAAM,UAAU,SAAS,OAAO,MAAM,MAAM,cAAc;AAC1D,UAAQ,KAAK,SAAS,uBAAuB;AAE7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACnD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACpD;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,IAAI,cAAc,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAClJ;AAEA,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,KAAK,MAAM,KAAK;AACtC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA/DsB;;;ACFtB,OAAOC,aAAW;AAIlB,eAAsB,aACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC5F,QAAM,MAAM;AACZ,QAAM,cAAc,KAAK,QAAQ,IAAI,cAAc;AACnD,QAAM,cAAc,KAAK,QAAQ,IAAI;AAErC,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,eAAe,kBAAkB,MAAM,EAAE,WAAW,KAAK,aAAa,IAAI,CAAC;AAEjF,UAAM,eAAe,kBAAkB,MAAM,EAAE,WAAW,KAAK,aAAa,IAAI,CAAC;AACjF,UAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,cAAc,GAAG,YAAY;AACpE,UAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,cAAc,GAAG,YAAY;AAEpE,kBAAc,SAAS,OAAO,KAAK,cAAc;AAEjD,gBACG,KAAK,SAAS,oBAAoB,KAAK,UAAU,CAAC,EAClD,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAE/C,gBAAY,KAAK,GAAG,YAAY,cAAc;AAC9C,gBAAY,KAAK,GAAG,YAAY,cAAc;AAAA,EAChD,OAAO;AACL,kBAAc,SAAS,OAAO,KAAK,cAAc;AAEjD,UAAM,cAAc,YAAY,OAAO,UAAU,cAAc;AAC/D,UAAM,cAAc,YAAY,OAAO,QAAQ;AAC/C,gBAAY,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU;AAE3E,gBACG,KAAK,SAAS,cAAc,EAC5B,KAAK,SAAS,UAAU,EACxB,KAAK,KAAK,WAAW,EACrB,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC;AAEf,gBACG,KAAK,SAAS,cAAc,EAC5B,KAAK,SAAS,UAAU,EACxB,KAAK,KAAK,WAAW,EACrB,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC;AAAA,EACjB;AAEA,mBAAiB,MAAM,WAAW;AAElC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,0BAA0B,MAAM,aAAa,KAAK;AAC3D,WAAO,kBAAU,OAAO,MAAM,aAAa,KAAK;AAAA,EAClD;AAEA,SAAO;AACT;AA7DsB;;;ACTtB,OAAOC,aAAW;AAQX,SAAS,aACd,QACA,MACA,EAAE,QAAQ,EAAE,eAAe,EAAE,GAC7B;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,IAAI,CAAC,EAClC,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACnC,QAAM,SAAS;AACf,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,UAAU,kBAAkB,MAAM,EAAE,WAAW,QAAQ,CAAC;AAE9D,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,aAAa,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AAEtD,QAAMC,gBAAe,SAAS,OAAO,MAAM,YAAY,cAAc;AAErE,EAAAA,cAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS,UAAU,cAAc;AAE9E,MAAI,aAAa,UAAU,SAAS,KAAK,KAAK,SAAS,aAAa;AAClE,IAAAA,cAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,cAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,mBAAiB,MAAMA,aAAY;AAEnC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,0BAA0B,MAAM,EAAE,QAAQ,MAAM,CAAC;AAC1D,UAAM,MAAM,kBAAU,OAAO,MAAM,QAAQ,KAAK;AAChD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA/CgB;;;ACHhB,OAAOC,aAAW;AAIlB,eAAsB,gBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAEtF,QAAM,IAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAM,IAAI,IAAI,KAAK;AAEnB,QAAM,KAAK,IAAI,KAAK;AACpB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE;AAAA,IACf,EAAE,GAAG,KAAK,GAAG,GAAG,EAAE;AAAA,EACpB;AAEA,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,QAAMC,mBAAkB,SACrB,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,iBAAgB,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAChE;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,iBAAgB,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACjE;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,mBAAiB,MAAMA,gBAAe;AAEtC,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9H;AAEA,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,sBAAsB,MAAM,QAAQ,KAAK;AAClD,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AA1DsB;;;ACTtB,OAAOC,aAAW;AAOX,SAAS,SACd,QACA,MACA,EAAE,KAAK,QAAQ,EAAE,OAAAC,QAAO,eAAe,EAAE,GACzC;AACA,QAAM,EAAE,WAAW,IAAI,cAAc,IAAI;AACzC,OAAK,QAAQ;AACb,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,IAAI,CAAC,EAClC,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AACzC,MAAI,SAAS,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AAE3C,MAAI,QAAQ,MAAM;AAChB,YAAQ,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AACrC,aAAS,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AAAA,EACzC;AAEA,QAAM,IAAK,KAAK,QAAS;AACzB,QAAM,IAAK,KAAK,SAAU;AAG1B,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM;AAAA,IACtC,QAAQ,eAAe;AAAA,IACvB,MAAM,eAAe;AAAA,EACvB,CAAC;AAED,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,YAAY,GAAG,UAAU,GAAG,GAAG,OAAO,QAAQ,OAAO;AAE3D,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAE7D,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACjD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AAEA,mBAAiB,MAAM,KAAK;AAC5B,QAAM,UAAUD,QAAO,WAAW;AAClC,MAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,UAAU,UAAU,KAAK;AAAA,EAChC;AACA,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AACA,SAAO;AACT;AA1DgB;;;ACIhB,OAAOE,aAAW;AAGlB,eAAsB,qBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,WAAW,IACf,YAAY;AACd,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,IAAI,KAAK,IAAI,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACnF,QAAM,IAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACtF,QAAM,SAAS,IAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IACvB,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,EAAE;AAAA,IAC/B,GAAG,qBAAqB,CAAC,IAAI,IAAI,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG;AAAA,IAC/D,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,IAC9B,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,EACxB;AAEA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAE7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAOA,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,kBAAkB,MAAM,EAAE,QAAQ,MAAM,CAAC;AAClD,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAzDsB;;;ACVtB,OAAOC,aAAW;AAIX,IAAM,qBAAqB,wBAChC,GACA,GACA,OACA,QACA,MACW;AACX,SAAO;AAAA,IACL,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,IACd,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC;AAAA,IACtB,IAAI,IAAI,KAAK,IAAI,IAAI,SAAS,CAAC;AAAA,IAC/B,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,MAAM;AAAA,IAC/B,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM;AAAA,IACvB,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC;AAAA,IACvB;AAAA,EACF,EAAE,KAAK,GAAG;AACZ,GAhBkC;AAkBlC,eAAsB,QAAsC,QAAwB,MAAY;AAC9F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,IAAI;AACV,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AACpC,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,EACpB;AAEA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,mBAAmB,GAAG,GAAG,GAAG,GAAG,CAAC;AACjD,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,cAAU,SACP,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAAA,EACrD;AAEA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AArDsB;;;ACrBtB,OAAOC,aAAW;AAGlB,eAAsB,UAAwC,QAAwB,MAAY;AAChG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,EAAE,SAAS,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAEzE,QAAM,IAAI,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AACvC,QAAM,IAAI,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AAExC,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,EACf;AAEA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAE7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,UAAQ,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;AAE3D,mBAAiB,MAAM,OAAO;AAI9B,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,kBAAkB,MAAM,EAAE,OAAO,CAAC;AAC3C,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AArDsB;;;ACRtB,OAAOC,aAAW;AASlB,eAAsB,KACpB,QACA,MACA,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GACxC;AACA,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,WAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AAEtF,QAAM,WAAW,KAAK,QAAQ;AAE9B,QAAM,SAAS;AACf,QAAM,QAAQ;AACd,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AAExC,QAAM,IAAI,CAAC,QAAQ;AACnB,QAAM,IAAI,CAAC,SAAS;AAEpB,QAAM,eAAe,KAAK,QAAQ,IAAI;AAGtC,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,EAAE,QAAQ,QAAQ,MAAM,OAAO,CAAC;AAExE,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,WAAW,GAAG,UAAU,GAAG,GAAG,OAAO,QAAQ,OAAO;AAE1D,QAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,QAAM,cAAc,SAAS,KAAK,SAAS;AAE3C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAElD,MAAI,KAAK,MAAM;AACb,UAAM,WAAW,SAAS,OAAO,GAAG;AACpC,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,WAAW,SAAS,KAAK,EAAG,QAAQ;AAC1C,UAAM,YAAY,SAAS;AAC3B,UAAM,aAAa,SAAS;AAC5B,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,aAAS;AAAA,MACP;AAAA,MACA,aAAa,CAAC,YAAY,IAAI,KAAK,IACjC,WACI,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QACtD,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAC7D;AAAA,IACF;AACA,aAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,EAC3E;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IACxD,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MACvD;AAAA,EACF;AAEA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IACZ,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CACpF;AAAA,EACF;AAEA,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,MACvD;AAAA,IACF;AAEA,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAhIsB;;;ACTtB,OAAOC,aAAW;AASlB,eAAsB,WACpB,QACA,MACA,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GACxC;AACA,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,WAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AAEtF,QAAM,UAAU;AAChB,QAAM,eAAe,KAAK,QAAQ,IAAI;AAEtC,QAAM,WAAW,KAAK,QAAQ;AAE9B,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AAExC,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,OAAO,UAAU,IAAI,MAAM;AACjC,UAAQ,SAAS,QAAQ;AAEzB,QAAM,WAAW,SAAS,OAAO,GAAG;AACpC,MAAI,KAAK,MAAM;AACb,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,QAAM,WAAW,SAAS,KAAK,EAAG,QAAQ;AAC1C,QAAM,YAAY,SAAS;AAC3B,QAAM,aAAa,SAAS;AAC5B,QAAM,QAAQ,SAAS;AACvB,QAAM,QAAQ,SAAS;AAEvB,QAAM,WAAW,KAAK,IAAI,WAAW,UAAU,IAAI,KAAK,QAAQ,UAAU;AAC1E,QAAM,WAAW,GAAG,OAAO,GAAG,GAAG,UAAU,OAAO;AAElD,QAAM,aAAa,KAAK,IAAI,UAAU,KAAK,KAAK;AAChD,QAAM,cAAc,WAAW,KAAK,SAAS;AAE7C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,WAAS;AAAA,IACP;AAAA,IACA,aAAa,CAAC,YAAY,IAAI,KAAK,IACjC,WACI,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QACtD,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAC7D;AAAA,EACF;AACA,WAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AACzE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IACxD,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MACvD;AAAA,EACF;AAEA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IACZ,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CACpF;AAAA,EACF;AAEA,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,UAAM,MAAM,kBAAU,KAAK,MAAM,KAAK;AACtC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA9FsB;;;ACTtB,OAAOC,aAAW;AAUlB,eAAsB,YACpB,QACA,MACA,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GACxC;AACA,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,WAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,aAAa,MAAM,IAAI,MAAM;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAW,KAAK,QAAQ;AAE9B,QAAM,SAAS,WAAW,cAAc;AACxC,QAAM,QAAQ,WAAW,cAAc;AACvC,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AAExC,QAAM,IAAI,CAAC,QAAQ;AACnB,QAAM,IAAI,CAAC,SAAS;AAEpB,QAAM,eAAe,KAAK,QAAQ,IAAI;AAGtC,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,OAAO,UAAU,IAAI,MAAM;AACjC,UAAQ,SAAS,QAAQ;AAEzB,QAAM,WAAW,GAAG,KAAK,uBAAuB,GAAG,GAAG,OAAO,QAAQ,CAAC,GAAG,OAAO;AAEhF,QAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,QAAM,cAAc,SAAS,KAAK,SAAS;AAE3C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc,EAAE,KAAK,SAAS,aAAa;AAC7F,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAElD,MAAI,KAAK,MAAM;AACb,UAAM,WAAW,SAAS,OAAO,GAAG;AACpC,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,WAAW,SAAS,KAAK,EAAG,QAAQ;AAC1C,UAAM,YAAY,SAAS;AAC3B,UAAM,aAAa,SAAS;AAC5B,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,aAAS;AAAA,MACP;AAAA,MACA,aAAa,CAAC,YAAY,IAAI,KAAK,IACjC,WACI,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QACtD,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAC7D;AAAA,IACF;AACA,aAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,EAC3E;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IACxD,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MACvD;AAAA,EACF;AAEA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IACZ,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CACpF;AAAA,EACF;AAEA,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,MACvD;AAAA,IACF;AAEA,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAtIsB;;;ACVtB,OAAOC,aAAW;AAUlB,eAAsB,WACpB,QACA,MACA,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GACxC;AACA,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,WAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,aAAa,MAAM,IAAI,MAAM;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAW,KAAK,QAAQ;AAE9B,QAAM,SAAS,WAAW,cAAc;AACxC,QAAM,QAAQ,WAAW,cAAc;AACvC,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AAExC,QAAM,IAAI,CAAC,QAAQ;AACnB,QAAM,IAAI,CAAC,SAAS;AAEpB,QAAM,eAAe,KAAK,QAAQ,IAAI;AAGtC,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,OAAO,UAAU,IAAI,MAAM;AACjC,UAAQ,SAAS,QAAQ;AAEzB,QAAM,WAAW,GAAG,KAAK,uBAAuB,GAAG,GAAG,OAAO,QAAQ,GAAG,GAAG,OAAO;AAElF,QAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,QAAM,cAAc,SAAS,KAAK,SAAS;AAE3C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAElD,MAAI,KAAK,MAAM;AACb,UAAM,WAAW,SAAS,OAAO,GAAG;AACpC,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,WAAW,SAAS,KAAK,EAAG,QAAQ;AAC1C,UAAM,YAAY,SAAS;AAC3B,UAAM,aAAa,SAAS;AAC5B,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,aAAS;AAAA,MACP;AAAA,MACA,aAAa,CAAC,YAAY,IAAI,KAAK,IACjC,WACI,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QACtD,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAC7D;AAAA,IACF;AACA,aAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,EAC3E;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IACxD,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MACvD;AAAA,EACF;AAEA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IACZ,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CACpF;AAAA,EACF;AAEA,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,MACvD;AAAA,IACF;AAEA,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAtIsB;;;ACVtB,OAAOC,aAAW;AAQlB,eAAsB,YACpB,QACA,MACA,EAAE,QAAQ,EAAE,UAAU,EAAE,GACxB;AACA,QAAM,MAAM,IAAI,MAAM;AACtB,MAAI,MAAM,MAAM,OAAO;AACvB,QAAM,IAAI,OAAO;AAEjB,QAAM,oBAAoB,OAAO,IAAI,aAAa,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAC9E,QAAM,qBAAqB,OAAO,IAAI,cAAc,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAChF,OAAK,mBAAmB,oBAAoB;AAE5C,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAE1C,OAAK,aAAa;AAElB,QAAM,eAAe,WAAW;AAChC,OAAK,eAAe,WAAW;AAE/B,QAAM,gBAAgB,KAAK;AAAA,IACzB,KAAK,QAAS,gBAAgB,IAAK;AAAA,IACnC,MAAM,cAAc;AAAA,EACtB;AAEA,QAAM,aACJ,KAAK,eAAe,OAChB,MAAM,cACJ,KAAK,cAAc,KAAK,mBACxB,gBACF;AAEN,QAAM,cACJ,KAAK,eAAe,OAChB,aAAa,KAAK,mBACjB,MAAM,eAAe;AAC5B,OAAK,QAAQ,KAAK,IAAI,YAAY,gBAAgB,CAAC;AACnD,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,qBAAqB;AAEvF,QAAM,WAAW,KAAK,QAAQ;AAE9B,QAAM,IAAI,CAAC,aAAa;AACxB,QAAM,IAAI,CAAC,cAAc;AAEzB,QAAM,eAAe,KAAK,QAAQ,IAAI;AAGtC,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,YAAY,GAAG,UAAU,GAAG,GAAG,YAAY,aAAa,OAAO;AAErE,QAAM,aAAa,KAAK,IAAI,YAAY,KAAK,KAAK;AAClD,QAAM,cAAc,cAAc,KAAK,SAAS;AAEhD,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,YAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AACjE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAElD,MAAI,KAAK,KAAK;AACZ,UAAM,QAAQ,SAAS,OAAO,OAAO;AAGrC,UAAM,KAAK,QAAQ,KAAK,GAAG;AAC3B,UAAM,KAAK,SAAS,UAAU;AAC9B,UAAM,KAAK,UAAU,WAAW;AAChC,UAAM,KAAK,uBAAuB,MAAM;AAExC,UAAM;AAAA,MACJ;AAAA,MACA,aAAa,CAAC,aAAa,CAAC,IAAI,WAAW,cAAc,IAAI,cAAc,CAAC,cAAc,CAAC;AAAA,IAC7F;AAAA,EACF;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IACxD,WACI,CAAC,cAAc,IAAI,KAAK,SAAS,IAAI,eAAe,IACpD,cAAc,IAAI,KAAK,SAAS,IAAI,eAAe,CACzD;AAAA,EACF;AAEA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IACZ,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CACpF;AAAA,EACF;AAEA,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,QAC/D,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,QAC/D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,QAC/D,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,MACjE;AAAA,IACF;AAEA,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA5IsB;;;ACJtB,OAAOC,aAAW;AAmBlB,eAAsB,cACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAE3E,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,CAAC,EAAE;AAAA,IAC5B,EAAE,GAAI,KAAK,IAAK,GAAG,GAAG,CAAC,EAAE;AAAA,EAC3B;AAEA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAE5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,cAAU,SACP,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAAA,EACrD;AAEA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AAtDsB;;;AClBtB,OAAOC,aAAW;AAIlB,eAAsB,SACpB,QACA,MACA,SACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAElB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,QAAQ,gBAAgB,GAAG,MAAM,SAAS,CAAC;AACpF,QAAM,cAAc,KAAK,IAAI,KAAK,SAAS,QAAQ,gBAAgB,GAAG,MAAM,UAAU,CAAC;AACvF,QAAM,IAAI,CAAC,aAAa;AACxB,QAAM,IAAI,CAAC,cAAc;AAIzB,MAAIC;AACJ,MAAI,EAAE,IAAI,GAAG,IAAI;AACjB,QAAM,EAAE,UAAU,IAAI;AAGtB,MAAI,SAAS,MAAM,QAAQ,IAAI;AAC7B,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAEA,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAMC,WAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,UAAM,YACJ,MAAM,KACF,GAAG,KAAK,uBAAuB,GAAG,GAAG,YAAY,aAAa,MAAM,CAAC,GAAGA,QAAO,IAC/E,GAAG,UAAU,GAAG,GAAG,YAAY,aAAaA,QAAO;AAEzD,IAAAF,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc;AACtD,IAAAA,MAAK,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,EAC1F,OAAO;AACL,IAAAA,QAAO,SAAS,OAAO,QAAQ,cAAc;AAE7C,IAAAA,MACG,KAAK,SAAS,uBAAuB,EACrC,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,oBAAoB,EAAE,CAAC,EAClC,KAAK,MAAM,oBAAoB,EAAE,CAAC,EAClC,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,UAAU,EACxB,KAAK,UAAU,WAAW;AAAA,EAC/B;AAEA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AA5DsB;;;ACYtB,eAAsB,UAAwC,QAAwB,MAAY;AAChG,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,OAAO;AAIzE,QAAMG,QAAO,SAAS,OAAO,QAAQ,cAAc;AAGnD,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,EAAAA,MAAK,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AACzD,WAAS,KAAK,SAAS,iBAAiB;AACxC,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAClH;AAaA,mBAAiB,MAAMA,KAAI;AAI3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AArCsB;;;ACjBtB,OAAOC,aAAW;AAIlB,eAAsB,UAAwC,QAAwB,MAAY;AAChG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,MAAM,SAAS,CAAC;AACrE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,IAAI,MAAM,UAAU,CAAC;AACvE,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,EAAE;AAAA,IAC3B,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,EAAE;AAAA,EAC3B;AAEA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAE5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,cAAU,SACP,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAAA,EACrD;AAEA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AAjDsB;;;ACJtB,OAAOC,aAAW;AAIlB,eAAsB,WAAyC,QAAwB,MAAY;AACjG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,MAAM,SAAS,CAAC;AACrE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,IAAI,MAAM,UAAU,CAAC;AACvE,QAAM,SAAS;AAAA,IACb,EAAE,GAAI,KAAK,IAAK,GAAG,GAAG,EAAE;AAAA,IACxB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,CAAC,EAAE;AAAA,IAC5B,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,EAChB;AAEA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,cAAU,SACP,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAAA,EACrD;AAEA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AAhDsB;;;ACJtB,OAAOC,aAAW;AAKX,SAAS,cAA4C,QAAwB,MAAY;AAC9F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,IAAI,CAAC,EAClC,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACnC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AAC3C,QAAM,SAAS,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AAC7C,QAAM,MAAM;AAEZ,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,SAAS,MAAM,EAAE;AAAA,IAC5B,EAAE,GAAG,QAAQ,IAAI,KAAK,GAAG,SAAS,MAAM,EAAE;AAAA,IAC1C,EAAE,GAAG,GAAG,GAAG,IAAI,OAAO;AAAA,IACtB,EAAE,GAAG,OAAO,GAAG,SAAS,MAAM,EAAE;AAAA,IAChC,EAAE,GAAG,IAAI,KAAK,GAAG,SAAS,MAAM,EAAE;AAAA,EACpC;AAGA,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAE1C,QAAMC,iBAAgB,SAAS,OAAO,MAAM,UAAU,cAAc;AAEpE,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,eAAc,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACzD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,eAAc,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC1D;AAEA,EAAAA,eAAc,KAAK,aAAa,cAAc,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG;AAErE,mBAAiB,MAAMA,cAAa;AAEpC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,2BAA2B,MAAM,KAAK;AAC/C,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAEjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAxDgB;;;ACLhB,OAAOC,aAAW;AAIX,IAAMC,uBAAsB,wBACjC,GACA,GACA,OACA,QACA,IACA,IACA,gBACW;AACX,SAAO;AAAA,IACL,IAAI,CAAC,IAAI,IAAI,EAAE;AAAA,IACf,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,IACb,IAAI,CAAC,IAAI,IAAI,KAAK,WAAW;AAAA,IAC7B,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,EAC7B,EAAE,KAAK,GAAG;AACZ,GAnBmC;AAoB5B,IAAMC,4BAA2B,wBACtC,GACA,GACA,OACA,QACA,IACA,IACA,gBACW;AACX,SAAO;AAAA,IACL,IAAI,CAAC,IAAI,IAAI,EAAE;AAAA,IACf,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,IACvB,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,IACb,IAAI,CAAC,IAAI,IAAI,KAAK,WAAW;AAAA,IAC7B,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,EAC7B,EAAE,KAAK,GAAG;AACZ,GAnBwC;AAoBjC,IAAMC,4BAA2B,wBACtC,GACA,GACA,OACA,QACA,IACA,OACW;AACX,SAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AACvF,GATwC;AAUxC,eAAsB,cACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,KAAK,SAAS,CAAC;AACpE,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,MAAM,MAAM,IAAI;AAC3B,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,WAAW,IAAI,KAAK,UAAU,CAAC;AAC3E,QAAM,cAAc,IAAI;AAExB,MAAIC;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,gBAAgBH,0BAAyB,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,WAAW;AAC9E,UAAM,gBAAgBC,0BAAyB,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAClE,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,UAAM,YAAY,GAAG,KAAK,eAAe,OAAO;AAChD,UAAM,YAAY,GAAG,KAAK,eAAe,OAAO;AAEhD,UAAM,cAAc,SAAS,OAAO,MAAM,WAAW,cAAc;AACnE,gBAAY,KAAK,SAAS,MAAM;AAChC,IAAAC,YAAW,SAAS,OAAO,MAAM,WAAW,cAAc;AAC1D,IAAAA,UAAS,KAAK,SAAS,uBAAuB;AAC9C,QAAI,WAAW;AACb,MAAAA,UAAS,KAAK,SAAS,SAAS;AAAA,IAClC;AAAA,EACF,OAAO;AACL,UAAM,WAAWH,qBAAoB,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,WAAW;AACpE,IAAAG,YAAW,SACR,OAAO,QAAQ,cAAc,EAC7B,KAAK,KAAK,QAAQ,EAClB,KAAK,SAAS,uBAAuB,EACrC,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAC5C,KAAK,SAAS,UAAU;AAAA,EAC7B;AAGA,EAAAA,UAAS,KAAK,kBAAkB,EAAE;AAClC,EAAAA,UAAS,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,GAAG;AAEnE,mBAAiB,MAAMA,SAAQ;AAE/B,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACvH;AAEA,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,KAAK,MAAM,KAAK;AACtC,UAAM,IAAI,IAAI,KAAK,KAAK,KAAK;AAE7B,QACE,MAAM,MACL,KAAK,IAAI,CAAC,KAAK,KAAK,SAAS,KAAK,KAChC,KAAK,IAAI,CAAC,MAAM,KAAK,SAAS,KAAK,KAClC,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,UAAU,KAAK,IAAI,KAC/D;AACA,UAAI,IAAI,KAAK,MAAM,IAAK,IAAI,KAAM,KAAK;AACvC,UAAI,IAAI,GAAG;AACT,YAAI,KAAK,KAAK,CAAC;AAAA,MACjB;AACA,UAAI,KAAK;AACT,UAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,YAAI,CAAC;AAAA,MACP;AAEA,UAAI,KAAK;AAAA,IACX;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAhFsB;;;AClDtB,OAAOE,aAAW;AAIlB,eAAsB,mBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,gBAAgB,IAAI;AAC1B,QAAM,SAAS,IAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAAC,IAAI,IAAK,IAAI,IAAK,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IAC5C,EAAE,GAAG,CAAC,IAAI,IAAK,IAAI,IAAK,KAAK,GAAG,SAAS,EAAE;AAAA,IAC3C,GAAG;AAAA,MACD,CAAC,IAAI,IAAK,IAAI,IAAK;AAAA,MACnB,SAAS;AAAA,MACT,IAAI,IAAK,IAAI,IAAK;AAAA,MAClB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,EAAE,GAAG,IAAI,IAAK,IAAI,IAAK,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IAC3C,EAAE,GAAG,CAAC,IAAI,IAAK,IAAI,IAAK,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IAC5C,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IAC5B,EAAE,GAAG,CAAC,IAAI,GAAG,GAAI,SAAS,IAAK,IAAI;AAAA,IACnC,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,EAC9B;AAEA,QAAM,OAAO,GAAG;AAAA,IACd,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,IAC5B;AAAA,EACF;AAEA,QAAM,eAAe,SAAS,OAAO,MAAM,MAAM,cAAc;AAE/D,eAAa,KAAK,SAAS,uBAAuB;AAElD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,eAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,KAAK,KAAK,WAAW,KAAO,IAAI,IAAK,MAAO,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAChL;AAEA,mBAAiB,MAAM,YAAY;AACnC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAtEsB;;;ACTtB,OAAOC,aAAW;AAIlB,eAAsB,UAAwC,QAAwB,MAAY;AAChG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,aAAa;AACnB,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IACvC,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,IAAI,WAAW;AAAA,IAC3C,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,IAAI,WAAW;AAAA,IAC/C,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,EAAE;AAAA,IAClC,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACrB,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,WAAW;AAAA,IAClC,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,IAAI,WAAW;AAAA,IAC/C,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IAC3C,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IACvC,EAAE,GAAG,IAAI,YAAY,EAAK;AAAA,IAC1B,EAAE,GAAG,EAAE;AAAA,IACP,EAAE,GAAG,GAAG,IAAI,WAAW;AAAA,EACzB;AAEA,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAG,GAAG,IAAI,WAAW;AAAA,IACvB,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IAC3C,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,EAAE;AAAA,IAClC,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACrB,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,EAAE;AAAA,EACT;AAEA,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,EAAE,GAAG,SAAS,MAAM,OAAO,CAAC;AAEjE,QAAMC,aAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AACjE,EAAAA,WAAU,OAAO,MAAM,WAAW,cAAc;AAEhD,EAAAA,WAAU,KAAK,SAAS,uBAAuB;AAE/C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,WAAU,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACrD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,WAAU,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACtD;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,cAAc,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC5I;AAEA,mBAAiB,MAAMA,UAAS;AAEhC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,iBAAiB,KAAK;AAC1D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA3EsB;;;ACEtB,OAAOC,aAAW;AAIlB,eAAsB,wBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,gBAAgB,IAAI;AAC1B,QAAM,SAAS,IAAI;AACnB,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,SAAS;AACpB,QAAM,aAAa;AAEnB,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,aAAa;AAAA,IACjB,IAAI;AAAA,IACJ,IAAI,SAAS;AAAA,IACb,IAAI,IAAI;AAAA,IACR,IAAI,SAAS;AAAA,IACb;AAAA,IACA;AAAA,EACF;AAEA,QAAM,gBAAgB,aAAa,WAAW,SAAS,CAAC;AAExD,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IACvC,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,SAAS,WAAW;AAAA,IAChD,GAAG;AAAA,IACH,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,cAAc,IAAI,WAAW;AAAA,IACzD,EAAE,GAAG,IAAI,GAAG,GAAG,cAAc,IAAI,WAAW;AAAA,IAC5C,EAAE,GAAG,IAAI,GAAG,GAAG,cAAc,IAAI,IAAI,WAAW;AAAA,IAChD,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,cAAc,IAAI,IAAI,WAAW;AAAA,IAC7D,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IAC3C,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IACvC,EAAE,GAAG,IAAI,YAAY,EAAK;AAAA,IAC1B,EAAE,GAAG,EAAE;AAAA,IACP,EAAE,GAAG,GAAG,IAAI,WAAW;AAAA,EACzB;AAEA,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAG,GAAG,IAAI,WAAW;AAAA,IACvB,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IAC3C,EAAE,GAAG,IAAI,IAAI,YAAY,GAAG,cAAc,IAAI,WAAW;AAAA,IACzD,EAAE,GAAG,IAAI,GAAG,GAAG,cAAc,IAAI,WAAW;AAAA,IAC5C,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,EAAE;AAAA,EACT;AAGA,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAE5C,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,QAAM,OAAO,MAAM,SAAS;AAE5B,QAAM,KAAK,SAAS,uBAAuB;AAE3C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACjD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AAEA,QAAM,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AAE5D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,aAAa,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAChK;AAEA,mBAAiB,MAAM,KAAK;AAE5B,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,iBAAiB,KAAK;AAC1D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA9FsB;;;ACbtB,OAAOC,aAAW;AAQlB,eAAsB,KACpB,QACA,MACA,EAAE,QAAQ,EAAE,eAAe,EAAE,GAC7B;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,gBAAgB,KAAK,iBAAiB,UAAU,EAAE,WAAW,eAAe;AAClF,MAAI,CAAC,eAAe;AAClB,SAAK,cAAc;AAAA,EACrB;AACA,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,aAAa,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AAClF,QAAM,cAAc,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACrF,QAAM,IAAI,CAAC,aAAa;AACxB,QAAM,IAAI,CAAC,cAAc;AACzB,QAAM,EAAE,UAAU,IAAI;AAItB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM;AAAA,IACtC,MAAM,eAAe;AAAA,IACrB,QAAQ,eAAe;AAAA,EACzB,CAAC;AAED,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,gBAAgB,GAAG,UAAU,GAAG,GAAG,YAAY,aAAa,OAAO;AAEzE,QAAMC,QAAO,SAAS,OAAO,MAAM,eAAe,cAAc;AAChE,EAAAA,MAAK,KAAK,SAAS,uBAAuB;AAE1C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,MAAK,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAChD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,MAAK,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACjD;AAEA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AAnDsB;;;ACHtB,OAAOC,aAAW;AAIX,IAAM,yBAAyB,wBAAC,GAAW,GAAW,SAAyB;AACpF,SAAO;AAAA,IACL,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;AAAA,IACrB,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC;AAAA,IAC5B,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI;AAAA,IAC5B,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC;AAAA,IACrB;AAAA,EACF,EAAE,KAAK,GAAG;AACZ,GARsC;AAUtC,eAAsB,SAAuC,QAAwB,MAAY;AAC/F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,IAAI;AAEd,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,EACpB;AAEA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,uBAAuB,GAAG,GAAG,CAAC;AAC/C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,cAAU,SACP,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAAA,EACrD;AAEA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AAEA,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACvC;AACA,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AAxDsB;;;ACftB,OAAOC,aAAW;AAGlB,eAAsB,oBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAEtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,MAAM,SAAS,CAAC;AACrE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,IAAI,MAAM,UAAU,CAAC;AAEvE,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,QAAQ,IAAI;AAElB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,IAAI,OAAO,EAAE;AAAA,IAClB,EAAE,GAAM,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,OAAO,GAAG,CAAC,EAAE;AAAA,IACtB,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE;AAAA,IACf,EAAE,GAAG,CAAC,GAAG,EAAE;AAAA,EACb;AAEA,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAE/D,UAAQ,KAAK,SAAS,uBAAuB;AAE7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACnD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACpD;AAEA,UAAQ,KAAK,aAAa,aAAa,CAAC,QAAQ,CAAC,KAAK;AAEtD,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,QAAQ,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC5H;AACA,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AA5DsB;;;ACNtB,SAAS,UAAAC,eAAc;AAMvB,OAAOC,aAAW;AAMlB,eAAsB,cACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,MAAI;AACJ,MAAI,CAAC,KAAK,YAAY;AACpB,cAAU;AAAA,EACZ,OAAO;AACL,cAAU,UAAU,KAAK;AAAA,EAC3B;AAGA,QAAM,WAAW,OAEd,OAAO,GAAG,EACV,KAAK,SAAS,OAAO,EACrB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAGnC,QAAM,IAAI,SAAS,OAAO,GAAG;AAE7B,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,UAAU;AAElF,QAAM,cAAc,KAAK;AAEzB,QAAM,QAAQ,KAAK;AAEnB,QAAMC,QAAO,MAAM,KAAK,EAAG,YAAY,MAAM,oBAAY,OAAO,KAAK,YAAY,MAAM,IAAI,CAAC;AAC5F,MAAI,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AACjC,MAAI,SAASC,WAAU,GAAG,WAAW,UAAU,GAAG;AAChD,UAAMC,OAAMF,MAAK,SAAS,CAAC;AAC3B,UAAMG,MAAKC,QAAOJ,KAAI;AACtB,WAAOE,KAAI,sBAAsB;AACjC,IAAAC,IAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,IAAAA,IAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,MAAI,KAAK,UAAU,WAAW;AAC9B,QAAM,WAAW,eAAe,CAAC;AACjC,QAAM,WAAWH,MAAK,QAAQ;AAC9B,QAAM,QAAQ,MACX,KAAK,EACL;AAAA,IACC,MAAM;AAAA,MACJ,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI;AAAA,MACzC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAGF,QAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,QAAM,KAAKI,QAAO,KAAK;AACvB,SAAO,IAAI,sBAAsB;AACjC,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,KAAG,KAAK,UAAU,KAAK,MAAM;AAG7B,QAAM,eAAe,KAAK,WAAW,KAAK;AAC1C,EAAAA,QAAO,KAAK,EAAE;AAAA,IACZ;AAAA,IACA,iBACG,KAAK,QAAQ,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,SAAS,KACnE,QACC,SAAS,SAAS,cAAc,KACjC;AAAA,EACJ;AACA,EAAAA,QAAOJ,KAAI,EAAE;AAAA,IACX;AAAA,IACA,iBACG,KAAK,QAAQ,SAAS,QAAQ,IAAI,EAAE,SAAS,QAAQ,KAAK,SAAS,KACpE;AAAA,EAGJ;AAIA,SAAO,MAAM,KAAK,EAAG,QAAQ;AAG7B,QAAM;AAAA,IACJ;AAAA,IACA,eAAe,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,cAAc,KAAK;AAAA,EACjF;AAEA,QAAM,aAAa,KAAK,SAAS,KAAK,WAAW;AACjD,QAAM,cAAc,KAAK,UAAU,KAAK,WAAW;AACnD,QAAM,IAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,QAAM,IAAI,CAAC,KAAK,SAAS,IAAI;AAC7B,MAAIK;AACJ,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,YAAY,GAAG;AAAA,MACnB,uBAAuB,GAAG,GAAG,YAAY,aAAa,KAAK,MAAM,CAAC;AAAA,MAClE;AAAA,IACF;AAEA,UAAM,YAAY,GAAG;AAAA,MACnB,CAAC,KAAK,QAAQ,IAAI;AAAA,MAClB,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS;AAAA,MACnD,KAAK,QAAQ,IAAI;AAAA,MACjB,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS;AAAA,MACnD;AAAA,IACF;AAEA,gBAAY,SAAS,OAAO,MAAM;AAChC,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AACjB,IAAAD,QAAO,SAAS,OAAO,MAAM;AAC3B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AAAA,EACnB,OAAO;AACL,IAAAA,QAAO,EAAE,OAAO,QAAQ,cAAc;AACtC,gBAAY,EAAE,OAAO,MAAM;AAC3B,IAAAA,MACG,KAAK,SAAS,mBAAmB,EACjC,KAAK,SAAS,UAAU,EACxB,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,WAAW,EACvC,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,WAAW,EACxC,KAAK,SAAS,KAAK,SAAS,KAAK,WAAW,EAAE,EAC9C,KAAK,UAAU,KAAK,UAAU,KAAK,WAAW,EAAE;AAEnD,cACG,KAAK,SAAS,SAAS,EACvB,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,WAAW,EACxC,KAAK,MAAM,KAAK,QAAQ,IAAI,WAAW,EACvC,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW,EACzE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW;AAAA,EAC9E;AACA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AAhJsB;;;ACTtB,eAAsB,YACpB,QACA,MACA;AACA,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,gBAAgB,MAAM,WAAW,KAAK;AAAA,IACtC,gBAAgB,MAAM,WAAW,KAAK;AAAA,EACxC;AAEA,SAAO,SAAS,QAAQ,MAAM,OAAO;AACvC;AAbsB;;;ACAtB,OAAOE,aAAW;AAIlB,eAAsB,cACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,cAAc,MAAM,WAAW;AACrC,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,IAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,QAAM,IAAI,CAAC,KAAK,SAAS,IAAI;AAE7B,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,EAAE;AAAA,IACP,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACzB,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACrB,EAAE,GAAG,IAAI,GAAG,EAAK;AAAA,IACjB,EAAE,GAAG,EAAE;AAAA,IACP,EAAE,GAAG,GAAG,IAAI,EAAE;AAAA,EAChB;AAEA,QAAM,YAAY,GAAG;AAAA,IACnB,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,IAC5B;AAAA,EACF;AAEA,QAAMC,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc;AAE5D,EAAAA,MAAK,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAExF,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,MAAK,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACjD;AAEA,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,MAAK,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACjD;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC1I;AAEA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AA9DsB;;;ACJtB,OAAOC,aAAW;AAGlB,eAAsB,WAAyC,QAAwB,MAAY;AACjG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI;AAEf,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,EAAE;AAAA,IACP,EAAE,GAAG,GAAG,IAAI,EAAE;AAAA,IACd,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACrB,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,EAAE;AAAA,EAC3B;AAEA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAE7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,UAAQ,KAAK,aAAa,gBAAgB,IAAI,CAAC,GAAG;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACvI;AAEA,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAvDsB;;;ACHtB,eAAsBC,YAAyC,QAAwB,MAAY;AACjG,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,gBAAgB,MAAM,WAAW,KAAK;AAAA,IACtC,gBAAgB,MAAM,WAAW,KAAK;AAAA,EACxC;AACA,SAAO,SAAS,QAAQ,MAAM,OAAO;AACvC;AATsB,OAAAA,aAAA;;;ACAtB,OAAOC,aAAW;AAkDlB,eAAsB,QAAsC,QAAwB,MAAY;AAC9F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AAEpC,MAAIC;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,UAAM,WAAW,uBAAuB,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;AACnE,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,IAAAD,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc;AACtD,IAAAA,MAAK,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,EAC1F,OAAO;AACL,IAAAA,QAAO,SAAS,OAAO,QAAQ,cAAc;AAE7C,IAAAA,MACG,KAAK,SAAS,uBAAuB,EACrC,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,IAAI,CAAC,EAChB,KAAK,MAAM,IAAI,CAAC,EAChB,KAAK,KAAK,CAAC,IAAI,CAAC,EAChB,KAAK,KAAK,CAAC,IAAI,CAAC,EAChB,KAAK,SAAS,CAAC,EACf,KAAK,UAAU,CAAC;AAAA,EACrB;AAEA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AAzCsB;;;AClDtB,eAAsB,MAAoC,QAAwB,MAAY;AAC5F,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,SAAS;AAAA,EACX;AACA,SAAO,SAAS,QAAQ,MAAM,OAAO;AACvC;AAPsB;;;ACJtB,OAAOE,aAAW;AAOX,SAAS,SACd,QACA,MACA,EAAE,QAAQ,EAAE,eAAe,EAAE,GAC7B;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,WAAW,aAAa,WAAW,IAAI;AAC/C,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,cAAc,EAC5B,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAGnC,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,YAAY,GAAG,OAAO,GAAG,GAAG,IAAI;AAAA,IACpC,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,aAAa;AAAA,EACf,CAAC;AACD,QAAM,YAAY,eAAe;AACjC,QAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,GAAG;AAAA,IACxC,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,EACb,CAAC;AACD,QAAMC,UAAS,SAAS,OAAO,MAAM,WAAW,cAAc;AAC9D,EAAAA,QAAO,OAAO,MAAM,cAAc;AAElC,MAAI,WAAW;AACb,IAAAA,QAAO,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAClD;AAEA,MAAI,YAAY;AACd,IAAAA,QAAO,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACnD;AAEA,mBAAiB,MAAMA,OAAM;AAE7B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,OAAO,MAAM,GAAG,KAAK;AAAA,EACxC;AAEA,SAAO;AACT;AAtDgB;;;ACPhB,OAAOC,aAAW;AAOX,SAAS,WACd,QACA,MACA,EAAE,QAAQ,EAAE,eAAe,EAAE,GAC7B;AACA,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,cAAc,EAC5B,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,MAAIC;AACJ,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,YAAY,GAAG,OAAO,GAAG,GAAG,IAAI,eAAe,SAAS,CAAC;AAC/D,IAAAD,UAAS,SAAS,OAAO,MAAM,SAAS;AAExC,IAAAA,QAAO,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAAA,EACtF,OAAO;AACL,IAAAA,UAAS,SAAS,OAAO,UAAU,cAAc;AAEjD,IAAAA,QAAO,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAAA,EACtF;AAEA,mBAAiB,MAAMA,OAAM;AAE7B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,OAAO,MAAM,GAAG,KAAK;AAAA,EACxC;AAEA,SAAO;AACT;AAjCgB;;;ACHhB,OAAOE,aAAW;AA+BlB,eAAsB,WAAyC,QAAwB,MAAY;AACjG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,eAAe,MAAM,WAAW,KAAK;AAC3C,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,QAAM,IAAI,CAAC,KAAK,SAAS,IAAI;AAE7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE;AAAA,IACf,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,EAChB;AAEA,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,UAAM,YAAY,GAAG,UAAU,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,OAAO;AAC3D,UAAM,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,OAAO;AAC1C,UAAM,KAAK,GAAG,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,OAAO;AAElD,aAAS,OAAO,MAAM,IAAI,cAAc;AACxC,aAAS,OAAO,MAAM,IAAI,cAAc;AACxC,UAAMC,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc;AAC5D,UAAM,EAAE,UAAU,IAAI;AACtB,IAAAA,MAAK,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AACxF,qBAAiB,MAAMA,KAAI;AAAA,EAC7B,OAAO;AACL,UAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,QAAI,YAAY;AACd,SAAG,KAAK,SAAS,UAAU;AAAA,IAC7B;AACA,qBAAiB,MAAM,EAAE;AAAA,EAC3B;AAEA,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AAnDsB;;;AChCtB,OAAOC,aAAW;AAIlB,eAAsB,WAAyC,QAAwB,MAAY;AACjG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,WAAW,MAAM;AACvB,QAAM,YAAY,MAAM;AACxB,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,QAAM,aAAa;AAAA,IACjB,EAAE,GAAG,IAAI,WAAW,GAAG,EAAE;AAAA,IACzB,EAAE,GAAG,IAAI,IAAI,WAAW,GAAG,EAAE;AAAA,IAC7B,EAAE,GAAG,IAAI,IAAI,WAAW,GAAG,GAAG,IAAI,EAAE;AAAA,IACpC,EAAE,GAAG,IAAI,WAAW,GAAG,GAAG,IAAI,EAAE;AAAA,EAClC;AAEA,QAAM,YAAY;AAAA,IAChB,EAAE,GAAG,IAAI,IAAI,WAAW,GAAG,GAAG,IAAI,EAAE;AAAA,IACpC,EAAE,GAAG,IAAI,IAAI,WAAW,GAAG,GAAG,IAAI,EAAE;AAAA,IACpC,EAAE,GAAG,IAAI,IAAI,WAAW,GAAG,GAAG,IAAI,IAAI,UAAU;AAAA,EAClD;AAEA,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAE1C,QAAM,UAAU,qBAAqB,SAAS;AAC9C,QAAM,UAAU,GAAG,KAAK,SAAS,EAAE,GAAG,SAAS,WAAW,QAAQ,CAAC;AAEnE,QAAMC,cAAa,SAAS,OAAO,MAAM,SAAS,cAAc;AAChE,EAAAA,YAAW,OAAO,MAAM,UAAU,cAAc;AAEhD,EAAAA,YAAW,KAAK,SAAS,uBAAuB;AAEhD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,YAAW,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACtD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,YAAW,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACvD;AAEA,mBAAiB,MAAMA,WAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,YAAY,KAAK;AAErD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA9DsB;;;ACEtB,OAAOC,aAAW;AAIlB,eAAsB,yBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,gBAAgB,IAAI;AAC1B,QAAM,WAAW,MAAM;AACvB,QAAM,YAAY,MAAM;AACxB,QAAM,SAAS,IAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAAC,IAAI,IAAK,IAAI,IAAK,KAAK,GAAG,SAAS,EAAE;AAAA,IAC3C,GAAG;AAAA,MACD,CAAC,IAAI,IAAK,IAAI,IAAK;AAAA,MACnB,SAAS;AAAA,MACT,IAAI,IAAK,IAAI,IAAK;AAAA,MAClB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IAEA,EAAE,GAAG,IAAI,IAAK,IAAI,IAAK,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IAC3C,EAAE,GAAG,CAAC,IAAI,IAAK,IAAI,IAAK,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,EAC9C;AAEA,QAAM,IAAI,CAAC,IAAI,IAAK,IAAI,IAAK;AAC7B,QAAM,IAAI,CAAC,SAAS,IAAI,YAAY;AAEpC,QAAM,YAAY;AAAA,IAChB,EAAE,GAAG,IAAI,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI;AAAA,IACxC,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,UAAU;AAAA,IACjC,EAAE,GAAG,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAAA,IAC7B,GAAG;AAAA,MACD,IAAI;AAAA,OACH,IAAI,KAAK;AAAA,MACV,IAAI,IAAI;AAAA,OACP,IAAI,KAAK;AAAA,MACV,CAAC,IAAI;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAEA,QAAM,mBAAmB,qBAAqB,MAAM;AACpD,QAAM,mBAAmB,GAAG,KAAK,kBAAkB,OAAO;AAE1D,QAAM,yBAAyB,qBAAqB,SAAS;AAC7D,QAAM,yBAAyB,GAAG,KAAK,wBAAwB;AAAA,IAC7D,GAAG;AAAA,IACH,WAAW;AAAA,EACb,CAAC;AAED,QAAM,eAAe,SAAS,OAAO,MAAM,wBAAwB,cAAc;AACjF,eAAa,OAAO,MAAM,kBAAkB,cAAc;AAE1D,eAAa,KAAK,SAAS,uBAAuB;AAElD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,eAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC1J;AAEA,mBAAiB,MAAM,YAAY;AACnC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA3FsB;;;ACPtB,eAAsB,KAAmC,QAAwB,MAAY;AAC3F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAElB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS,MAAM,SAAS,CAAC;AACvE,QAAM,cAAc,KAAK,IAAI,KAAK,SAAS,KAAK,SAAS,MAAM,UAAU,CAAC;AAC1E,QAAM,IAAI,CAAC,aAAa;AACxB,QAAM,IAAI,CAAC,cAAc;AAEzB,QAAMC,QAAO,SAAS,OAAO,QAAQ,cAAc;AAEnD,EAAAA,MACG,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,UAAU,EACxB,KAAK,UAAU,WAAW;AAE7B,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AA9BsB;;;ACHtB,OAAOC,aAAW;AAKX,IAAMC,uBAAsB,wBACjC,GACA,GACA,OACA,QACA,IACA,OACW;AACX,SAAO,IAAI,CAAC,IAAI,CAAC;AAAA,OACZ,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM;AAAA,OAC9B,KAAK,IAAI,CAAC;AAAA,OACV,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,OAC7B,KAAK,IAAI,CAAC,MAAM;AAAA,OAChB,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,OAC7B,CAAC,KAAK,IAAI,CAAC;AAClB,GAfmC;AAiB5B,IAAMC,4BAA2B,wBACtC,GACA,GACA,OACA,QACA,IACA,OACW;AACX,SAAO;AAAA,IACL,IAAI,CAAC,IAAI,CAAC;AAAA,IACV,IAAI,IAAI,KAAK,IAAI,CAAC;AAAA,IAClB,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM;AAAA,IAClC,IAAI,CAAC,KAAK;AAAA,IACV,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,IACjC,IAAI,KAAK;AAAA,EACX,EAAE,KAAK,GAAG;AACZ,GAhBwC;AAiBjC,IAAMC,4BAA2B,wBACtC,GACA,GACA,OACA,QACA,IACA,OACW;AACX,SAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,YAAY,MAAM,EAAE,EAAE,KAAK,GAAG;AACxF,GATwC;AAWxC,eAAsB,eACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,OAAO,YAAY,IAAI,MAAM;AAAA,IACnD;AAAA,IACA;AAAA,IACA,eAAe,IAAI;AAAA,EACrB;AACA,QAAM,eAAe,KAAK,SAAS,QAAQ,cAAc,IAAI;AAC7D,QAAM,IAAI,KAAK,SAAS;AACxB,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,MAAM,MAAM,IAAI;AAC3B,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAIC;AAEJ,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,gBAAgBH,0BAAyB,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;AACjE,UAAM,gBAAgBC,0BAAyB,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;AACjE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,CAAC,CAAC,CAAC;AACpE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC,CAAC;AAClF,IAAAC,YAAW,SAAS,OAAO,MAAM,WAAW,cAAc;AAC1D,IAAAA,YAAW,SAAS,OAAO,MAAM,WAAW,cAAc;AAC1D,IAAAA,UAAS,KAAK,SAAS,uBAAuB;AAC9C,QAAI,WAAW;AACb,MAAAA,UAAS,KAAK,SAAS,SAAS;AAAA,IAClC;AAAA,EACF,OAAO;AACL,UAAM,WAAWH,qBAAoB,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;AACvD,IAAAG,YAAW,SACR,OAAO,QAAQ,cAAc,EAC7B,KAAK,KAAK,QAAQ,EAClB,KAAK,SAAS,uBAAuB,EACrC,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAC5C,KAAK,SAAS,UAAU;AAC3B,IAAAA,UAAS,KAAK,SAAS,uBAAuB;AAE9C,QAAI,WAAW;AACb,MAAAA,UAAS,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACpD;AAEA,QAAI,YAAY;AACd,MAAAA,UAAS,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACrD;AAAA,EACF;AAEA,EAAAA,UAAS,KAAK,kBAAkB,EAAE;AAClC,EAAAA,UAAS,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI;AAE5D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACvH;AAEA,mBAAiB,MAAMA,SAAQ;AAE/B,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,KAAK,MAAM,KAAK;AACtC,UAAM,IAAI,IAAI,KAAK,KAAK,KAAK;AAE7B,QACE,MAAM,MACL,KAAK,IAAI,CAAC,KAAK,KAAK,UAAU,KAAK,KACjC,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU,KAAK,KACnC,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,SAAS,KAAK,IAAI,KAC9D;AACA,UAAI,IAAI,KAAK,MAAM,IAAK,IAAI,KAAM,KAAK;AACvC,UAAI,KAAK,GAAG;AACV,YAAI,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,MAC3B;AACA,UAAI,KAAK;AACT,UAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,YAAI,CAAC;AAAA,MACP;AAEA,UAAI,KAAK;AAAA,IACX;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAxFsB;;;ACjDtB,OAAOE,aAAW;AAmBlB,eAAsB,UAAwC,QAAwB,MAAY;AAChG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAI,KAAK,IAAK,GAAG,GAAG,EAAE;AAAA,IACxB,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,EAAE;AAAA,IAC3B,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,EAChB;AAEA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,KAAK,SAAS,aAAa;AAE7B,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,cAAU,SACP,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAAA,EACrD;AAEA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AAjDsB;;;ACnBtB,OAAOC,aAAW;AAGlB,eAAsB,oBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,WAAW,IACf,YAAY;AACd,QAAM,IAAI,KAAK,IAAI,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACnF,QAAM,IAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAEtF,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAI,CAAC,IAAI,IAAK,KAAK,GAAG,CAAC,IAAI,EAAE;AAAA,IAC/B,EAAE,GAAI,IAAI,IAAK,KAAK,GAAG,CAAC,IAAI,EAAE;AAAA,IAC9B,EAAE,GAAG,IAAI,GAAG,GAAI,CAAC,IAAI,IAAK,IAAI;AAAA,IAC9B,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACrB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACtB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAI,CAAC,IAAI,IAAK,IAAI;AAAA,EACjC;AAEA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAE7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,mBAAiB,MAAM,OAAO;AAE9B,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AArDsB;;;ACFtB,OAAOC,aAAW;AAMlB,eAAsB,SAAuC,QAAwB,MAAY;AAC/F,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,gBAAgB,SAASC,WAAU,EAAE,WAAW,UAAU;AAEhE,QAAM,IAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAM,IAAI,IAAI,KAAK;AAEnB,QAAM,KAAK,IAAI,KAAK;AACpB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE;AAAA,EACrB;AAEA,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAE3C,QAAM,UAAU,SACb,OAAO,MAAM,WAAW,cAAc,EACtC,KAAK,aAAa,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAErD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,mBAAiB,MAAM,OAAO;AAE9B,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK,UAAU,KAAK,WAAW,MAAM,gBAAgB,IAAI,MAAM,KAAK,KAAK,KAAK,OAAO,IAAI;AAAA,EACnK;AAEA,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,sBAAsB,MAAM,QAAQ,KAAK;AAClD,WAAO,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AAAA,EAC9C;AAEA,SAAO;AACT;AAxDsB;;;ACFtB,OAAOC,aAAW;AAIlB,eAAsB,mBACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,gBAAgB,IAAI;AAC1B,QAAM,SAAS,IAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,WAAW;AACjB,QAAM,WAAW,WAAW;AAC5B,QAAM,SAAS,WAAW,IAAI,WAAW,IAAI;AAG7C,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,SAAS,EAAE;AAAA,IACpC,GAAG;AAAA,MACD,CAAC,IAAI,IAAI;AAAA,MACT,SAAS;AAAA,MACT,IAAI,IAAI;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG,CAAC,SAAS,EAAE;AAAA,IACpC,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,SAAS,EAAE;AAAA,EACvC;AAEA,QAAM,mBAAmB,qBAAqB,MAAM;AACpD,QAAM,mBAAmB,GAAG,KAAK,kBAAkB,OAAO;AAE1D,QAAM,eAAe,SAAS,OAAO,MAAM,kBAAkB,cAAc;AAE3E,eAAa,KAAK,SAAS,uBAAuB;AAElD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AAEA,eAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,WAAW,KAAK,iBAAiB,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACtJ;AAEA,mBAAiB,MAAM,YAAY;AACnC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AArEsB;;;ACHtB,OAAOC,aAAW;AAGlB,eAAsB,cACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAE/E,QAAM,WAAW;AACjB,QAAM,YAAY;AAElB,QAAM,YAAY,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACjF,QAAM,aAAa,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAEpF,QAAM,cAAc,YAAY;AAEhC,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,IAAI,IAAI,aAAa;AACvB,QAAI,IAAI;AAAA,EACV,OAAO;AACL,QAAI,IAAI;AAAA,EACV;AAEA,MAAI,KAAK,IAAI,GAAG,QAAQ;AACxB,MAAI,KAAK,IAAI,GAAG,SAAS;AAEzB,QAAM,gBAAgB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAC7C,QAAM,SAAS,IAAI,gBAAgB;AACnC,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,SAAS,EAAE;AAAA,IAC3B,GAAG,2BAA2B,CAAC,IAAI,GAAG,SAAS,GAAG,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC;AAAA,IACrF,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IAC3B,GAAG,2BAA2B,IAAI,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,GAAG,CAAC,SAAS,GAAG,eAAe,EAAE;AAAA,EAC1F;AAEA,QAAM,eAAe,qBAAqB,MAAM;AAChD,QAAM,eAAe,GAAG,KAAK,cAAc,OAAO;AAElD,QAAM,WAAW,SAAS,OAAO,MAAM,cAAc,cAAc;AAEnE,WAAS,KAAK,SAAS,uBAAuB;AAE9C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACpD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACrD;AAEA,mBAAiB,MAAM,QAAQ;AAC/B,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,QAAQ,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAtEsB;;;ACVtB,OAAOC,aAAW;AAIlB,eAAsB,WAAyC,QAAwB,MAAY;AACjG,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,QAAM,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,QAAM,aAAa;AACnB,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,EAAE,UAAU,IAAI;AAGtB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,WAAW;AAAA,IACvC,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,EAAE;AAAA,IAC9B,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,IACrB,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,WAAW;AAAA,EAChC;AAEA,QAAM,OAAO,IAAI,IAAI,UAAU,IAAI,IAAI,UAAU,KAAK,IAAI,CAAC,IAAI,IAAI,UAAU,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,KAAK,IAAI,UAAU,IAAI,IAAI,UAAU;AAAA,mBAChJ,IAAI,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC;AAAA,mBAClC,CAAC,IAAI,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC;AAEnD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,KAAK,GAAG,KAAK,MAAM,OAAO;AAEhC,QAAMC,cAAa,SAAS,OAAO,MAAM,IAAI,cAAc;AAC3D,EAAAA,YAAW,KAAK,aAAa,aAAa,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;AAE9E,EAAAA,YAAW,KAAK,SAAS,uBAAuB;AAEhD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,IAAAA,YAAW,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACtD;AAEA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,IAAAA,YAAW,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACvD;AAEA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,aAAa,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,aAAa,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACpJ;AAEA,mBAAiB,MAAMA,WAAU;AAEjC,OAAK,YAAY,SAAU,OAAO;AAChC,UAAM,MAAM,kBAAU,QAAQ,MAAM,iBAAiB,KAAK;AAC1D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA3DsB;;;ACHtB,OAAOC,aAAW;AAMlB,SAAS,UAAAC,eAAc;AAKvB,eAAsB,MAAoC,QAAwB,MAAY;AAE5F,QAAM,aAAa;AACnB,MAAI,WAAW,OAAO;AACpB,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAIA,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,EAAE,gBAAAC,gBAAe,IAAI,UAAU;AACrC,UAAM,EAAE,WAAW,IAAIA;AACvB,UAAM,iBAAiB;AAAA,MACrB,GAAG;AAAA,MACH,IAAI,KAAK,KAAK;AAAA,MACd,MAAM;AAAA,MACN,WAAW,CAAC,gBAAgB,SAAS,UAAU,EAAE;AAAA,IACnD;AACA,UAAM,MAAM,QAAQ,cAAc;AAAA,EACpC;AAEA,QAAM,SAAS,UAAU;AACzB,OAAK,gBAAgB,OAAO;AAC5B,MAAI,UAAU,OAAO,IAAI,kBAAkB;AAC3C,MAAI,eAAe,OAAO,IAAI,iBAAiB;AAE/C,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AAGtD,MAAI,WAAW,WAAW,WAAW,KAAK,KAAK,OAAO;AACpD,UAAMC,WAAU;AAAA,MACd,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,eAAe;AAAA,MACf,eAAe,UAAU;AAAA,MACzB,SAAS;AAAA,IACX;AAEA,QACE,mBAAmB,KAAK,OAAO,MAAM,IAAIA,SAAQ,gBAAgB,IACjE,OAAO,GAAI,gBACX;AACA,WAAK,QAAQ,OAAO,GAAI;AAAA,IAC1B;AACA,UAAMC,YAAW,MAAM,SAAS,QAAQ,MAAMD,QAAO;AAGrD,QAAI,CAAC,SAAS,OAAO,UAAU,GAAG;AAChC,YAAM,cAAcC,UAAS,OAAO,MAAM;AAC1C,YAAM,OAAQ,YAAY,KAAK,GAAsB,QAAQ;AAC7D,kBAAY,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM;AAAA,IAClE;AACA,WAAOA;AAAA,EACT;AAEA,MAAI,CAAC,OAAO,YAAY;AACtB,eAAW;AACX,oBAAgB;AAAA,EAClB;AAEA,MAAI,aAAa,eAAe,IAAI;AACpC,MAAI,CAAC,YAAY;AACf,iBAAa;AAAA,EACf;AAEA,QAAM,WAAW,OAEd,OAAO,GAAG,EACV,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,QAAM,WAAW,MAAM,QAAQ,UAAU,KAAK,SAAS,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,GAAG,WAAW;AAC9F,WAAS,UAAU;AACnB,MAAI,UAAU;AACd,QAAM,WAAW,CAAC;AAClB,QAAM,OAAO,CAAC;AACd,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,MAAI,kBAAkB;AACtB,MAAI,cAAc;AAClB,MAAI,iBAAiB;AACrB,aAAW,aAAa,WAAW,YAAY;AAC7C,UAAM,WAAW,MAAM;AAAA,MACrB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,gBAAgB;AAAA,MACjB;AAAA,IACF;AACA,mBAAe,KAAK,IAAI,cAAc,SAAS,QAAQ,OAAO;AAC9D,UAAMC,YAAW,MAAM;AAAA,MACrB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,gBAAgB;AAAA,MACjB;AAAA,IACF;AACA,mBAAe,KAAK,IAAI,cAAcA,UAAS,QAAQ,OAAO;AAC9D,UAAM,WAAW,MAAM;AAAA,MACrB;AAAA,MACA,UAAU,KAAK,KAAK;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,gBAAgB;AAAA,MACjB;AAAA,IACF;AACA,mBAAe,KAAK,IAAI,cAAc,SAAS,QAAQ,OAAO;AAC9D,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,mBAAmB;AAAA,MACpB;AAAA,IACF;AACA,sBAAkB,KAAK,IAAI,iBAAiB,YAAY,QAAQ,OAAO;AAEvE,UAAM,YACJ,KAAK,IAAI,SAAS,QAAQA,UAAS,QAAQ,SAAS,QAAQ,YAAY,MAAM,IAC9E;AACF,SAAK,KAAK,EAAE,SAAS,UAAU,CAAC;AAChC,eAAW;AAAA,EACb;AACA,MAAI,qBAAqB;AAEzB,MAAI,gBAAgB,SAAS;AAC3B,kBAAc;AACd,mBAAe;AACf;AAAA,EACF;AACA,MAAI,mBAAmB,SAAS;AAC9B,qBAAiB;AACjB,sBAAkB;AAClB;AAAA,EACF;AAEA,QAAM,YAAY,SAAS,KAAK,EAAG,QAAQ;AAE3C,MACE,SAAS,QAAQ,UAAU,KAAK,eAAe,eAAe,eAAe,mBAC7E,GACA;AACA,UAAM,aACJ,SAAS,QAAQ,UAAU,KAAK,eAAe,eAAe,eAAe;AAC/E,oBAAgB,aAAa;AAC7B,oBAAgB,aAAa;AAC7B,QAAI,eAAe,GAAG;AACpB,sBAAgB,aAAa;AAAA,IAC/B;AACA,QAAI,kBAAkB,GAAG;AACvB,yBAAmB,aAAa;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,WAAW,eAAe,eAAe,eAAe;AAG9D,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,MAAI,uBAAuB;AAC3B,MAAI,KAAK,SAAS,GAAG;AACnB,2BAAuB,KAAK,OAAO,CAAC,KAAK,QAAQ,OAAO,KAAK,aAAa,IAAI,CAAC;AAAA,EACjF;AACA,QAAM,IAAI,KAAK,IAAI,UAAU,QAAQ,UAAU,GAAG,MAAM,SAAS,GAAG,QAAQ;AAC5E,QAAM,IAAI,KAAK,KAAK,wBAAwB,KAAK,SAAS,QAAQ,MAAM,UAAU,CAAC;AACnF,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI;AAGf,WAAS,UAAU,qBAAqB,EAAE,KAAK,CAAC,GAAQ,GAAW,UAAe;AAChF,UAAMC,QAAOC,QAAqB,MAAM,CAAC,CAAC;AAC1C,UAAM,YAAYD,MAAK,KAAK,WAAW;AACvC,QAAI,aAAa;AACjB,QAAI,aAAa;AAEjB,QAAI,WAAW;AACb,YAAM,QAAQ,OAAO,8BAA8B;AACnD,YAAM,YAAY,MAAM,KAAK,SAAS;AACtC,UAAI,WAAW;AACb,qBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,qBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,YAAIA,MAAK,KAAK,OAAO,EAAE,SAAS,gBAAgB,GAAG;AACjD,wBAAc;AAAA,QAChB,WAAWA,MAAK,KAAK,OAAO,EAAE,SAAS,gBAAgB,GAAG;AACxD,wBAAc,eAAe;AAAA,QAC/B,WAAWA,MAAK,KAAK,OAAO,EAAE,SAAS,mBAAmB,GAAG;AAC3D,wBAAc,eAAe,eAAe;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAEA,IAAAA,MAAK;AAAA,MACH;AAAA,MACA,aAAa,IAAI,UAAU,IAAI,UAAU,KAAK,aAAa,IAAI,SAAS,SAAS,eAAe,CAAC;AAAA,IACnG;AAAA,EACF,CAAC;AAED,WACG,OAAO,OAAO,EACd,KAAK,aAAa,eAAe,CAAC,SAAS,QAAQ,IAAI,QAAQ,IAAI,eAAe,KAAK,GAAG;AAG7F,QAAM,YAAY,GAAG,UAAU,GAAG,GAAG,GAAG,GAAG,OAAO;AAClD,QAAME,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,SAAS,UAAW,KAAK,EAAE,CAAC;AAE/F,QAAM,EAAE,eAAe,IAAI,UAAU;AACrC,QAAM,EAAE,SAAS,QAAQ,WAAW,IAAI;AAExC,WAAS,KAAK,CAAC;AAEf,aAAW,CAAC,GAAG,GAAG,KAAK,KAAK,QAAQ,GAAG;AACrC,UAAM,kBAAkB,IAAI;AAC5B,UAAM,SAAS,kBAAkB,MAAM,KAAK,IAAI,YAAY;AAC5D,UAAMC,aAAY,GAAG,UAAU,GAAG,SAAS,SAAS,IAAI,KAAK,SAAS,GAAG,KAAK,WAAW;AAAA,MACvF,GAAG;AAAA,MACH,MAAM,SAAS,UAAU;AAAA,MACzB,QAAQ;AAAA,IACV,CAAC;AACD,aACG,OAAO,MAAMA,YAAW,SAAS,EACjC,KAAK,SAAS,UAAW,KAAK,EAAE,CAAC,EACjC,KAAK,SAAS,YAAY,SAAS,SAAS,KAAK,EAAE;AAAA,EACxD;AAIA,MAAI,YAAY,GAAG,KAAK,GAAG,SAAS,SAAS,GAAG,IAAI,GAAG,SAAS,SAAS,GAAG,OAAO;AACnF,WAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAExD,cAAY,GAAG,KAAK,eAAe,GAAG,SAAS,SAAS,GAAG,eAAe,GAAG,IAAI,GAAG,OAAO;AAC3F,WAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAExD,MAAI,aAAa;AACf,gBAAY,GAAG;AAAA,MACb,eAAe,eAAe;AAAA,MAC9B,SAAS,SAAS;AAAA,MAClB,eAAe,eAAe;AAAA,MAC9B,IAAI;AAAA,MACJ;AAAA,IACF;AACA,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AAEA,MAAI,gBAAgB;AAClB,gBAAY,GAAG;AAAA,MACb,eAAe,eAAe,eAAe;AAAA,MAC7C,SAAS,SAAS;AAAA,MAClB,eAAe,eAAe,eAAe;AAAA,MAC7C,IAAI;AAAA,MACJ;AAAA,IACF;AACA,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AAGA,aAAWC,YAAW,UAAU;AAC9B,gBAAY,GAAG;AAAA,MACb;AAAA,MACA,SAAS,SAAS,IAAIA;AAAA,MACtB,IAAI;AAAA,MACJ,SAAS,SAAS,IAAIA;AAAA,MACtB;AAAA,IACF;AACA,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AAEA,mBAAiB,MAAMF,KAAI;AAE3B,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,WAAW,WAAW,MAAM,GAAG;AACrC,UAAM,eAAe,UACjB,OAAO,CAAC,MAAM;AACd,aAAO,EAAE,SAAS,QAAQ;AAAA,IAC5B,CAAC,GACC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,EAClB,KAAK,IAAI;AACZ,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,gBAAgB,EAAE;AAC3D,aAAS,UAAU,qBAAqB,EAAE,KAAK,SAAS,UAAU;AAAA,EACpE;AAEA,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AACA,SAAO;AACT;AA1SsB;AA6StB,eAAe,QACb,UACA,WACA,QACA,aAAa,GACb,aAAa,GACb,UAAoB,CAAC,GACrB,QAAQ,IACR;AACA,QAAM,QAAQ,SACX,OAAO,GAAG,EACV,KAAK,SAAS,SAAS,QAAQ,KAAK,GAAG,CAAC,EAAE,EAC1C,KAAK,aAAa,aAAa,UAAU,KAAK,UAAU,GAAG,EAC3D,KAAK,SAAS,KAAK;AAGtB,MAAI,cAAc,kBAAkB,SAAS,GAAG;AAC9C,gBAAY,kBAAkB,SAAS;AAEvC,gBAAY,UAAU,WAAW,KAAK,MAAM,EAAE,WAAW,KAAK,MAAM;AAAA,EACtE;AAEA,QAAMF,QAAO,MAAM,KAAK,EAAG;AAAA,IACzB,MAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,QACE,OAAO,mBAAmB,WAAW,MAAM,IAAI;AAAA,QAC/C;AAAA,QACA,eAAe,OAAO;AAAA,MACxB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,UAAU,SAAS,MAAM,KAAK,UAAU,SAAS,MAAM,GAAG;AAC5D,QAAI,QAAQA,MAAK,SAAS,CAAC;AAC3B,UAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AACpF,WAAO,MAAM,WAAW,CAAC,GAAG;AAC1B,cAAQ,MAAM,WAAW,CAAC;AAE1B,YAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AAAA,IACtF;AAAA,EACF;AAEA,MAAI,OAAOA,MAAK,QAAQ;AACxB,MAAI,SAAS,OAAO,UAAU,GAAG;AAC/B,UAAM,MAAMA,MAAK,SAAS,CAAC;AAC3B,QAAI,MAAM,YAAY;AACtB,UAAM,KAAKC,QAAOD,KAAI;AACtB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAEA,SAAO;AACT;AAxDe;;;AC1Tf,SAAS,UAAAK,eAAc;AAGvB,OAAOC,aAAW;;;ACLlB,SAAS,UAAAC,eAAc;AAavB,eAAsB,WACpB,QACA,MACA,QACA,eACA,MAAM,OAAO,MAAO,WAAW,IAC/B;AACA,QAAM,eAAe,CAAC,gBAAgB,IAAI;AAC1C,QAAM,WAAW,OAEd,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,IAAI,CAAC,EAClC,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,MAAI,kBAAkB;AACtB,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,MAAI,eAAe;AAEnB,MAAI,wBAAwB;AAC5B,MAAI,mBAAmB;AACvB,MAAI,qBAAqB;AAEzB,oBAAkB,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,uBAAuB;AAC5E,MAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,UAAM,aAAa,KAAK,YAAY,CAAC;AACrC,UAAMC,SAAQ,iBAAiB,EAAE,MAAM,OAAI,UAAU,OAAI,GAA6B,CAAC;AAEvF,UAAM,sBAAsB,gBAAgB,KAAK,EAAG,QAAQ;AAC5D,4BAAwB,oBAAoB;AAAA,EAC9C;AAEA,eAAa,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,kBAAkB;AAClE,QAAMA,SAAQ,YAAY,MAAM,GAAG,CAAC,qBAAqB,CAAC;AAC1D,QAAM,iBAAiB,WAAW,KAAK,EAAG,QAAQ;AAClD,qBAAmB,eAAe;AAElC,iBAAe,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACtE,MAAI,UAAU;AACd,aAAW,UAAU,KAAK,SAAS;AACjC,UAAM,SAAS,MAAMA,SAAQ,cAAc,QAAQ,SAAS,CAAC,OAAO,gBAAgB,CAAC,CAAC;AACtF,eAAW,SAAS;AAAA,EACtB;AACA,uBAAqB,aAAa,KAAK,EAAG,QAAQ,EAAE;AACpD,MAAI,sBAAsB,GAAG;AAC3B,yBAAqB,MAAM;AAAA,EAC7B;AAEA,iBAAe,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACtE,MAAI,iBAAiB;AACrB,aAAW,UAAU,KAAK,SAAS;AACjC,UAAM,SAAS,MAAMA,SAAQ,cAAc,QAAQ,gBAAgB,CAAC,OAAO,gBAAgB,CAAC,CAAC;AAC7F,sBAAkB,SAAS;AAAA,EAC7B;AAEA,MAAI,OAAO,SAAS,KAAK,EAAG,QAAQ;AAGpC,MAAI,oBAAoB,MAAM;AAC5B,UAAM,sBAAsB,gBAAgB,KAAK,EAAG,QAAQ;AAC5D,oBAAgB,KAAK,aAAa,aAAa,CAAC,oBAAoB,QAAQ,CAAC,GAAG;AAAA,EAClF;AAGA,aAAW,KAAK,aAAa,aAAa,CAAC,eAAe,QAAQ,CAAC,KAAK,qBAAqB,GAAG;AAEhG,SAAO,SAAS,KAAK,EAAG,QAAQ;AAEhC,eAAa;AAAA,IACX;AAAA,IACA,aAAa,CAAC,KAAK,wBAAwB,mBAAmB,MAAM,CAAC;AAAA,EACvE;AACA,SAAO,SAAS,KAAK,EAAG,QAAQ;AAChC,eAAa;AAAA,IACX;AAAA,IACA,aAAa,CAAC,KAAK,wBAAwB,oBAAoB,qBAAqB,qBAAqB,MAAM,IAAI,MAAM,EAAE;AAAA,EAC7H;AAEA,SAAO,SAAS,KAAK,EAAG,QAAQ;AAEhC,SAAO,EAAE,UAAU,KAAK;AAC1B;AAjFsB;AAoFtB,eAAeA,SACb,aACA,MACA,SACA,SAAmB,CAAC,GACpB;AACA,QAAM,SAAS,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,OAAO,KAAK,IAAI,CAAC;AAC7F,QAAM,SAAS,UAAU;AACzB,MAAI,gBACF,mBAAmB,OAAO,KAAK,gBAAiB,SAAS,OAAO,UAAU,KAAK;AAEjF,MAAI,cAAc;AAElB,MAAI,UAAU,MAAM;AAClB,kBAAc,KAAK;AAAA,EACrB,OAAO;AACL,kBAAc,KAAK;AAAA,EACrB;AAIA,MAAI,CAAC,iBAAiB,YAAY,WAAW,IAAI,GAAG;AAClD,kBAAc,YAAY,UAAU,CAAC;AAAA,EACvC;AAEA,MAAI,SAAS,WAAW,GAAG;AACzB,oBAAgB;AAAA,EAClB;AAEA,QAAMC,QAAO,MAAM;AAAA,IACjB;AAAA,IACAC,cAAa,eAAe,WAAW,CAAC;AAAA,IACxC;AAAA,MACE,OAAO,mBAAmB,aAAa,MAAM,IAAI;AAAA;AAAA,MACjD,SAAS;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,MAAI;AACJ,MAAI,gBAAgB;AAEpB,MAAI,CAAC,eAAe;AAElB,QAAI,OAAO,SAAS,qBAAqB,GAAG;AAC1C,MAAAC,QAAOF,KAAI,EAAE,UAAU,OAAO,EAAE,KAAK,eAAe,EAAE;AAAA,IACxD;AAEA,oBAAgBA,MAAK,SAAS;AAE9B,UAAM,YAAYA,MAAK,SAAS,CAAC;AACjC,QAAIA,MAAK,gBAAgB,MAAMA,MAAK,YAAY,SAAS,KAAK,GAAG;AAC/D,gBAAU,cACR,YAAY,CAAC,IACb,YAAY,UAAU,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,KAAK;AAGhF,YAAM,gBAAgB,YAAY,CAAC,MAAM;AACzC,UAAI,eAAe;AACjB,kBAAU,cAAc,UAAU,YAAY,CAAC,IAAI,MAAM,UAAU,YAAY,UAAU,CAAC;AAAA,MAC5F;AAAA,IACF;AAGA,QAAI,UAAU,gBAAgB,aAAa;AACzC,gBAAU,cAAc;AAAA,IAC1B;AAGA,WAAOA,MAAK,QAAQ;AAAA,EACtB,OAAO;AACL,UAAM,MAAMA,MAAK,SAAS,CAAC;AAC3B,UAAM,KAAKE,QAAOF,KAAI;AAEtB,oBAAgB,IAAI,UAAU,MAAM,MAAM,EAAE;AAE5C,QAAI,IAAI,UAAU,SAAS,SAAS,GAAG;AACrC,uBAAiB,IAAI,UAAU,MAAM,QAAQ,EAAE,SAAS;AAAA,IAC1D;AAGA,UAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,QAAI,QAAQ;AACV,YAAM,YAAY,YAAY,QAAQ,eAAe,EAAE,EAAE,KAAK,MAAM;AACpE,YAAM,QAAQ;AAAA,QACZ,CAAC,GAAG,MAAM,EAAE;AAAA,UACV,CAAC,QACC,IAAI,QAAQ,CAAC,QAAQ;AACnB,qBAAS,aAAa;AACpB,kBAAI,MAAM,UAAU;AACpB,kBAAI,MAAM,gBAAgB;AAE1B,kBAAI,WAAW;AAEb,sBAAM,eACJ,OAAO,UAAU,SAAS,KAAK,OAAO,iBAAiB,SAAS,IAAI,EAAE;AACxE,sBAAM,kBAAkB;AACxB,sBAAM,QAAQ,SAAS,cAAc,EAAE,IAAI,kBAAkB;AAC7D,oBAAI,MAAM,WAAW;AACrB,oBAAI,MAAM,WAAW;AAAA,cACvB,OAAO;AACL,oBAAI,MAAM,QAAQ;AAAA,cACpB;AACA,kBAAI,GAAG;AAAA,YACT;AAhBS;AAiBT,uBAAW,MAAM;AACf,kBAAI,IAAI,UAAU;AAChB,2BAAW;AAAA,cACb;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,SAAS,UAAU;AACxC,gBAAI,iBAAiB,QAAQ,UAAU;AAAA,UACzC,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAGA,SAAO,KAAK,aAAa,kBAAkB,CAAC,KAAK,UAAU,IAAI,iBAAiB,WAAW,GAAG;AAC9F,SAAO,KAAK;AACd;AA7He,OAAAD,UAAA;;;ADrFf,eAAsB,SAAuC,QAAwB,MAAY;AAC/F,QAAM,SAASI,WAAU;AACzB,QAAM,UAAU,OAAO,MAAO,WAAW;AACzC,QAAM,MAAM;AACZ,QAAM,gBAAgB,KAAK,iBAAiB,SAAS,OAAO,UAAU,KAAK;AAE3E,QAAM,YAAY;AAClB,YAAU,cAAc,UAAU,eAAe,CAAC;AAClD,YAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,YAAU,UAAU,UAAU,WAAW,CAAC;AAE1C,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,WAAW,QAAQ,MAAM,QAAQ,eAAe,GAAG;AAEpF,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAElB,OAAK,YAAY,UAAU,UAAU;AAErC,QAAM,SAAS,UAAU,QAAQ,KAAK,GAAG,KAAK,cAAc;AAE5D,MAAI,CAAC,KAAK,WAAW;AACnB,SAAK,YAAY,OAAO,WAAW,cAAc,EAAE,EAAE,MAAM,GAAG;AAAA,EAChE;AAEA,QAAM,iBACJ,UAAU,QAAQ,WAAW,KAC7B,UAAU,QAAQ,WAAW,KAC7B,CAAC,OAAO,OAAO;AAIjB,QAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAEA,QAAM,IAAI,KAAK;AACf,MAAI,IAAI,KAAK;AACb,MAAI,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,GAAG;AACpE,SAAK;AAAA,EACP,WAAW,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,WAAW,GAAG;AACzE,SAAK,MAAM;AAAA,EACb;AACA,QAAM,IAAI,CAAC,IAAI;AACf,QAAM,IAAI,CAAC,IAAI;AAGf,QAAM,YAAY,GAAG;AAAA,IACnB,IAAI;AAAA,IACJ,IACE,WACC,iBACG,UACA,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAC7D,CAAC,UAAU,IACX;AAAA,IACR,IAAI,IAAI;AAAA,IACR,IACE,IAAI,WACH,iBACG,UAAU,IACV,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAC7D,CAAC,UACD;AAAA,IACR;AAAA,EACF;AAEA,QAAMC,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc;AAC5D,EAAAA,MAAK,KAAK,SAAS,uBAAuB;AAC1C,QAAM,WAAWA,MAAK,KAAK,EAAG,QAAQ;AAItC,WAAS,UAAU,OAAO,EAAE,KAAK,CAAC,GAAQ,GAAW,UAAe;AAClE,UAAMC,QAAOC,QAAqB,MAAM,CAAC,CAAC;AAE1C,UAAM,YAAYD,MAAK,KAAK,WAAW;AAEvC,QAAI,aAAa;AAEjB,QAAI,WAAW;AACb,YAAM,QAAQ,OAAO,8BAA8B;AACnD,YAAM,YAAY,MAAM,KAAK,SAAS;AACtC,UAAI,WAAW;AACb,qBAAa,WAAW,UAAU,CAAC,CAAC;AAAA,MACtC;AAAA,IACF;AAEA,QAAI,gBACF,aACA,IACA,WACC,iBACG,UACA,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAC7D,CAAC,UAAU,IACX;AACR,QAAI,CAAC,eAAe;AAGlB,uBAAiB;AAAA,IACnB;AACA,QAAI,gBAAgB;AACpB,QACEA,MAAK,KAAK,OAAO,EAAE,SAAS,aAAa,KACzCA,MAAK,KAAK,OAAO,EAAE,SAAS,kBAAkB,GAC9C;AACA,sBAAgB,CAACA,MAAK,KAAK,GAAG,QAAQ,EAAE,QAAQ,KAAK;AACrD,eAAS,UAAU,MAAM,EAAE,KAAK,SAAUE,IAAQC,IAAWC,QAAY;AACvE,YAAI,OAAO,iBAAiBA,OAAMD,EAAC,CAAC,EAAE,eAAe,UAAU;AAC7D,0BAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,IAAAH,MAAK,KAAK,aAAa,aAAa,aAAa,KAAK,aAAa,GAAG;AAAA,EACxE,CAAC;AAGD,QAAM,wBACH,SAAS,OAAO,mBAAmB,EAAE,KAAK,EAAyB,QAAQ,EAAE,UAC3E,iBAAiB,UAAU,IAAI,MAAM;AAC1C,QAAM,mBACH,SAAS,OAAO,cAAc,EAAE,KAAK,EAAyB,QAAQ,EAAE,UACtE,iBAAiB,UAAU,IAAI,MAAM;AAC1C,QAAM,qBACH,SAAS,OAAO,gBAAgB,EAAE,KAAK,EAAyB,QAAQ,EAAE,UACxE,iBAAiB,UAAU,IAAI,MAAM;AAE1C,MAAI,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,SAAS,KAAK,gBAAgB;AAClF,UAAM,YAAY,GAAG;AAAA,MACnB,SAAS;AAAA,MACT,wBAAwB,mBAAmB,IAAI;AAAA,MAC/C,SAAS,IAAI,SAAS;AAAA,MACtB,wBAAwB,mBAAmB,IAAI;AAAA,MAC/C;AAAA,IACF;AACA,UAAM,OAAO,SAAS,OAAO,MAAM,SAAS;AAC5C,SAAK,KAAK,SAAS,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,EACpD;AAGA,MAAI,kBAAkB,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,SAAS,GAAG;AAClF,UAAM,YAAY,GAAG;AAAA,MACnB,SAAS;AAAA,MACT,wBAAwB,mBAAmB,qBAAqB,IAAI,MAAM,IAAI;AAAA,MAC9E,SAAS,IAAI,SAAS;AAAA,MACtB,wBAAwB,mBAAmB,qBAAqB,IAAI,UAAU,MAAM;AAAA,MACpF;AAAA,IACF;AACA,UAAM,OAAO,SAAS,OAAO,MAAM,SAAS;AAC5C,SAAK,KAAK,SAAS,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,EACpD;AAGA,MAAI,UAAU,SAAS,aAAa;AAClC,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,MAAM;AAAA,EACjD;AAEA,EAAAD,MAAK,OAAO,eAAe,EAAE,KAAK,SAAS,MAAM;AAEjD,WAAS,UAAU,UAAU,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,MAAM;AAElE,MAAI,KAAK,YAAY;AACnB,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,KAAK,UAAU;AAAA,EAC1D,OAAO;AACL,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,MAAM;AAAA,EACjD;AAEA,MAAI,CAAC,eAAe;AAElB,UAAM,aAAa,OAAO,qBAAqB;AAC/C,UAAM,QAAQ,WAAW,KAAK,MAAM;AACpC,QAAI,OAAO;AACT,YAAM,aAAa,MAAM,CAAC,EAAE,QAAQ,SAAS,MAAM;AACnD,eAAS,UAAU,OAAO,EAAE,KAAK,SAAS,UAAU;AAAA,IACtD,WAAW,aAAa;AACtB,YAAMM,SAAQ,WAAW,KAAK,WAAW;AACzC,UAAIA,QAAO;AACT,cAAM,aAAaA,OAAM,CAAC,EAAE,QAAQ,SAAS,MAAM;AACnD,iBAAS,UAAU,OAAO,EAAE,KAAK,SAAS,UAAU;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AAEA,mBAAiB,MAAMN,KAAI;AAC3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AAlMsB;;;AERtB,OAAOO,aAAW;AAKlB,SAAS,UAAAC,eAAc;AAGvB,eAAsB,eACpB,QACA,MACA;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,kBAAkB;AACxB,QAAM,cAAc;AACpB,QAAM,UAAU;AAChB,QAAM,MAAM;AACZ,QAAM,oBAAoB,kBAAkB;AAC5C,QAAM,UAAU,eAAe,IAAI;AAGnC,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,OAAO,EACrB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,MAAI;AACJ,MAAI,mBAAmB;AACrB,iBAAa,MAAMC;AAAA,MACjB;AAAA,MACA,WAAW,gBAAgB,IAAI;AAAA,MAC/B;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF,OAAO;AACL,iBAAa,MAAMA,SAAQ,UAAU,2BAA2B,GAAG,KAAK,UAAU;AAAA,EACpF;AAEA,MAAI,qBAAqB;AACzB,QAAM,aAAa,MAAMA;AAAA,IACvB;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA,KAAK,aAAa;AAAA,EACpB;AACA,wBAAsB,aAAa;AAGnC,MAAI,mBAAmB;AACrB,UAAM,WAAW,MAAMA;AAAA,MACrB;AAAA,MACA,GAAG,gBAAgB,gBAAgB,OAAO,gBAAgB,aAAa,KAAK,EAAE;AAAA,MAC9E;AAAA,MACA,KAAK;AAAA,IACP;AAEA,0BAAsB;AACtB,UAAM,aAAa,MAAMA;AAAA,MACvB;AAAA,MACA,GAAG,gBAAgB,OAAO,SAAS,gBAAgB,IAAI,KAAK,EAAE;AAAA,MAC9D;AAAA,MACA,KAAK;AAAA,IACP;AACA,0BAAsB;AACtB,UAAM,aAAa,MAAMA;AAAA,MACvB;AAAA,MACA,GAAG,gBAAgB,OAAO,SAAS,gBAAgB,IAAI,KAAK,EAAE;AAAA,MAC9D;AAAA,MACA,KAAK;AAAA,IACP;AACA,0BAAsB;AACtB,UAAMA;AAAA,MACJ;AAAA,MACA,GAAG,gBAAgB,eAAe,iBAAiB,gBAAgB,YAAY,KAAK,EAAE;AAAA,MACtF;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF,OAAO;AAEL,UAAMC,cAAa,MAAMD;AAAA,MACvB;AAAA,MACA,GAAG,YAAY,OAAO,SAAS,YAAY,IAAI,KAAK,EAAE;AAAA,MACtD;AAAA,MACA,KAAK;AAAA,IACP;AACA,0BAAsBC;AACtB,UAAMD;AAAA,MACJ;AAAA,MACA,GAAG,YAAY,SAAS,YAAY,YAAY,MAAM,KAAK,EAAE;AAAA,MAC7D;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAEA,QAAM,cAAc,SAAS,KAAK,GAAG,QAAQ,EAAE,SAAS,OAAO;AAC/D,QAAM,eAAe,SAAS,KAAK,GAAG,QAAQ,EAAE,UAAU,OAAO;AACjE,QAAM,IAAI,CAAC,aAAa;AACxB,QAAM,IAAI,CAAC,cAAc;AAIzB,QAAM,KAAKE,QAAM,IAAI,QAAQ;AAC7B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAE1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AAGA,QAAM,YAAY,GAAG,UAAU,GAAG,GAAG,YAAY,aAAa,OAAO;AAErE,QAAMC,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc;AAC5D,EAAAA,MAAK,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU;AAIpE,WAAS,UAAU,QAAQ,EAAE,KAAK,CAAC,GAAQ,GAAW,UAAe;AAEnE,UAAMC,QAAOC,QAAqB,MAAM,CAAC,CAAC;AAE1C,UAAM,YAAYD,MAAK,KAAK,WAAW;AACvC,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,WAAW;AACb,YAAM,QAAQ,OAAO,8BAA8B;AACnD,YAAM,YAAY,MAAM,KAAK,SAAS;AACtC,UAAI,WAAW;AACb,qBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,qBAAa,WAAW,UAAU,CAAC,CAAC;AAAA,MACtC;AAAA,IACF;AAEA,UAAM,gBAAgB,aAAa,cAAc;AACjD,QAAI,gBAAgB,IAAI,UAAU;AAGlC,QAAI,MAAM,KAAK,MAAM,GAAG;AACtB,sBAAgB;AAAA,IAClB;AAEA,IAAAA,MAAK,KAAK,aAAa,aAAa,aAAa,KAAK,gBAAgB,OAAO,GAAG;AAAA,EAClF,CAAC;AAGD,MAAI,qBAAqB,aAAa,aAAa,KAAK;AACtD,UAAM,YAAY,GAAG;AAAA,MACnB;AAAA,MACA,IAAI,aAAa,aAAa;AAAA,MAC9B,IAAI;AAAA,MACJ,IAAI,aAAa,aAAa;AAAA,MAC9B;AAAA,IACF;AACA,UAAM,cAAc,SAAS,OAAO,MAAM,SAAS;AACnD,gBAAY,KAAK,SAAS,UAAU;AAAA,EACtC;AAEA,mBAAiB,MAAMD,KAAI;AAE3B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAU,KAAK,MAAM,KAAK;AAAA,EACnC;AAEA,SAAO;AACT;AA7JsB;AA+JtB,eAAeH,SACb,aACA,WACA,SACA,QAAQ,IACR;AACA,MAAI,cAAc,IAAI;AACpB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,KAAK;AACjF,QAAM,SAASM,WAAU;AACzB,QAAM,gBAAgB,OAAO,cAAc;AAE3C,QAAMF,QAAO,MAAM;AAAA,IACjB;AAAA,IACAG,cAAa,eAAe,SAAS,CAAC;AAAA,IACtC;AAAA,MACE,OAAO,mBAAmB,WAAW,MAAM,IAAI;AAAA;AAAA,MAC/C,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,MAAI;AAEJ,MAAI,CAAC,eAAe;AAClB,UAAM,YAAYH,MAAK,SAAS,CAAC;AACjC,eAAW,SAAS,UAAU,UAAU;AACtC,YAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AACpF,UAAI,OAAO;AACT,cAAM,aAAa,SAAS,KAAK;AAAA,MACnC;AAAA,IACF;AAEA,WAAOA,MAAK,QAAQ;AAEpB,SAAK,UAAU;AAAA,EACjB,OAAO;AACL,UAAM,MAAMA,MAAK,SAAS,CAAC;AAC3B,UAAM,KAAKC,QAAOD,KAAI;AAEtB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAGA,SAAO,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,GAAG;AACtF,SAAO,KAAK;AACd;AAlDe,OAAAJ,UAAA;;;ACtKf,OAAOQ,aAAW;AAGlB,IAAM,oBAAoB,wBAAC,aAAkD;AAC3E,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACF,GAb0B;AAc1B,eAAsB,WACpB,QAEA,YACA,EAAE,OAAO,GACT;AACA,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,UAAU;AAC5D,aAAW,aAAa,eAAe;AAEvC,QAAM,gBAAgB;AACtB,QAAM,WAAW,WAAW;AAC5B,aAAW,SAAS,WAAW,SAAS,OAAO;AAE/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,IAAI,MAAM,YAAY,QAAQ,YAAY,eAAe,UAAU,CAAC;AACpE,QAAM,UAAU,WAAW,WAAW;AAEtC,MAAI,YAAY;AAChB,MAAI;AAEJ,MAAI,YAAY,cAAc,WAAW,UAAU,QAAQ,QAAQ,eAAe;AAChF,gBAAY,QAAQ,QAAQ,cAAc,QAAQ,YAAY,WAAW,MAAM;AAC/E,WAAO,SACJ,OAAoB,SAAS,cAAc,EAC3C,KAAK,SAAS,oBAAoB,EAClC,KAAK,cAAc,SAAS,EAC5B,KAAK,UAAU,QAAQ;AAAA,EAC5B;AAEA,QAAM,UAAU;AAAA,IACd,eAAe,WAAW;AAAA,IAC1B,YAAY,WAAW,cAAc;AAAA,IACrC,OAAO,WAAW;AAAA,IAClB,KAAK,WAAW;AAAA,IAChB,SAAS,WAAW,WAAW;AAAA,IAC/B,aAAa;AAAA,EACf;AACA,MAAI,SAAS;AACb,MAAI,MAAM;AACR,KAAC,EAAE,OAAO,SAAS,MAAM,MAAM,IAAI,MAAM;AAAA,MACvC;AAAA,MACC,YAAY,cAAc,WAAW,UAAW;AAAA,MACjD;AAAA,IACF;AAAA,EACF,OAAO;AACL,KAAC,EAAE,OAAO,SAAS,MAAM,MAAM,IAAI,MAAM;AAAA,MACvC;AAAA,MACC,YAAY,cAAc,WAAW,UAAW;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,OAAO,iBAAiB,MAAM,aAAa,IAAI,MAAM;AAAA,IAC3D;AAAA,IACC,cAAc,cAAc,WAAW,YAAa;AAAA,IACrD;AAAA,EACF;AACA,aAAW,QAAQ;AACnB,QAAM,gBAAgB;AACtB,QAAM,aAAa,YAAY,SAAS;AACxC,QAAM,YAAY,KAAK,IAAI,MAAM,QAAQ,aAAa,MAAM,IAAI;AAChE,QAAM,cACJ,KAAK,IAAI,KAAK,SAAS,gBAAgB,GAAG,YAAY,UAAU,CAAC,IAAI;AACvE,QAAM,IAAI,CAAC,aAAa;AACxB,QAAM,IAAI,CAAC,cAAc;AACzB,eAAa;AAAA,IACX;AAAA,IACA,gBAAgB,UAAU,aAAa,KAAK,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK;AAAA,EACtF;AACA,UAAQ;AAAA,IACN;AAAA,IACA,gBAAgB,UAAU,aAAa,KAAK,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK;AAAA,EACtF;AACA,kBAAgB;AAAA,IACd;AAAA,IACA,gBACG,UAAU,aAAa,IAAI,aAAa,QAAQ,IAAI,iBACrD,QACC,CAAC,YAAY,KAAK,SAAS,KAC5B;AAAA,EACJ;AAEA,MAAIC;AAEJ,QAAM,EAAE,IAAI,GAAG,IAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AAEtB,MAAI,WAAW,SAAS,aAAa;AAEnC,UAAM,KAAKC,QAAM,IAAI,QAAQ;AAC7B,UAAMC,WAAU,kBAAkB,YAAY,CAAC,CAAC;AAEhD,UAAM,YACJ,MAAM,KACF,GAAG,KAAK,uBAAuB,GAAG,GAAG,YAAY,aAAa,MAAM,CAAC,GAAGA,QAAO,IAC/E,GAAG,UAAU,GAAG,GAAG,YAAY,aAAaA,QAAO;AAEzD,IAAAF,QAAO,SAAS,OAAO,MAAM,WAAW,cAAc;AACtD,IAAAA,MAAK,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,YAAY,YAAY,IAAI;AAAA,EACxF,OAAO;AACL,IAAAA,QAAO,SAAS,OAAO,QAAQ,cAAc;AAE7C,IAAAA,MACG,KAAK,SAAS,+BAA+B,EAC7C,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,MAAM,CAAC,EAClB,KAAK,MAAM,MAAM,CAAC,EAClB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,UAAU,EACxB,KAAK,UAAU,WAAW;AAE7B,UAAM,WAAW,cAAc,cAAc,WAAW;AACxD,QAAI,UAAU;AACZ,YAAM,OAAO,SAAS,OAAO,MAAM;AACnC,YAAM,QAAQ,IAAI;AAElB,YAAM,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,CAAC;AACvC,YAAM,KAAK,IAAI,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AACrD,WACG,KAAK,MAAM,KAAK,EAChB,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,KAAK,EAChB,KAAK,MAAM,EAAE,EAEb,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,kBAAkB,QAAQ,CAAC;AAAA,IAC/C;AAAA,EACF;AAEA,mBAAiB,YAAYA,KAAI;AACjC,aAAW,SAAS;AAEpB,aAAW,YAAY,SAAU,OAAO;AACtC,WAAO,kBAAU,KAAK,YAAY,KAAK;AAAA,EACzC;AAEA,SAAO;AACT;AA5IsB;;;ACgEf,IAAM,aAAa;AAAA,EACxB;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,WAAW,WAAW;AAAA,IACxC,iBAAiB,CAAC,YAAY;AAAA,IAC9B,SAASG;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,OAAO;AAAA,IACjB,iBAAiB,CAAC,aAAa;AAAA,IAC/B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,MAAM;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,cAAc,WAAW,oBAAoB,YAAY;AAAA,IACnE,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,MAAM,YAAY,UAAU;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,MAAM;AAAA,IAChB,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,WAAW,UAAU;AAAA,IAC3C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,SAAS;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,cAAc,QAAQ;AAAA,IAChC,iBAAiB,CAAC,YAAY;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,aAAa,QAAQ;AAAA,IAC/B,iBAAiB,CAAC,WAAW;AAAA,IAC7B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,oBAAoB,WAAW;AAAA,IACrD,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,UAAU,iBAAiB,eAAe;AAAA,IACpD,iBAAiB,CAAC,eAAe;AAAA,IACjC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,eAAe;AAAA,IACzB,iBAAiB,CAAC,cAAc;AAAA,IAChC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,mBAAmB;AAAA,IACrC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,mBAAmB,iBAAiB,YAAY,gBAAgB;AAAA,IAC1E,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,SAAS,cAAc;AAAA,IACjC,iBAAiB,CAAC,YAAY;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,eAAe;AAAA,IACjC,iBAAiB,CAAC,UAAU;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,MAAM;AAAA,IAChB,iBAAiB,CAAC,UAAU;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,aAAa,SAAS;AAAA,IAChC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,SAAS;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,gBAAgB;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,OAAO,UAAU;AAAA,IAC3B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,wBAAwB;AAAA,IAClC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,OAAO,qBAAqB;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,gBAAgB;AAAA,IAClC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,oBAAoB,SAAS;AAAA,IACvC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,qBAAqB,iBAAiB;AAAA,IAC5D,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,UAAU;AAAA,IAC/B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,oBAAoB,aAAa;AAAA,IAC3C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,eAAe;AAAA,IACrC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,cAAc,kBAAkB;AAAA,IAC1C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,eAAe,kBAAkB;AAAA,IAC3C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,gBAAgB,kBAAkB;AAAA,IAC5C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,aAAa,UAAU,kBAAkB;AAAA,IACnD,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,SAAS,aAAa,mBAAmB;AAAA,IACnD,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,eAAe,mBAAmB;AAAA,IAC5C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,gBAAgB;AAAA,IACrC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,iBAAiB;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,oBAAoB,YAAY,gBAAgB;AAAA,IAC1D,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,iBAAiB,CAAC,qBAAqB;AAAA,IACvC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,gBAAgB;AAAA,IAC1B,SAAS;AAAA,EACX;AACF;AAEA,IAAM,mBAAmB,6BAAM;AAE7B,QAAM,qBAAqB;AAAA;AAAA,IAEzB;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA;AAAA,IAGA;AAAA;AAAA,IAGA;AAAA;AAAA,IAGA;AAAA,EACF;AAEA,QAAM,UAAU;AAAA,IACd,GAAI,OAAO,QAAQ,kBAAkB;AAAA,IACrC,GAAG,WAAW,QAAQ,CAAC,UAAU;AAC/B,YAAM,UAAU;AAAA,QACd,MAAM;AAAA,QACN,GAAI,aAAa,QAAQ,MAAM,UAAU,CAAC;AAAA,QAC1C,GAAI,qBAAqB,QAAQ,MAAM,kBAAkB,CAAC;AAAA,MAC5D;AACA,aAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,OAAO,CAAU;AAAA,IAC/D,CAAC;AAAA,EACH;AACA,SAAO,OAAO,YAAY,OAAO;AAInC,GAhDyB;AAkDlB,IAAMC,UAAS,iBAAiB;AAEhC,SAAS,aAAa,OAAiC;AAC5D,SAAO,SAASA;AAClB;AAFgB;;;ACjfhB,IAAM,YAAY,oBAAI,IAAyB;AAE/C,eAAsB,WACpB,MACA,MACA,eACA;AACA,MAAI;AACJ,MAAI;AAGJ,MAAI,KAAK,UAAU,QAAQ;AACzB,QAAI,KAAK,MAAM,KAAK,IAAI;AACtB,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAEA,QAAM,eAAe,KAAK,QAAQC,QAAO,KAAK,KAAK,IAAI;AAEvD,MAAI,CAAC,cAAc;AACjB,UAAM,IAAI,MAAM,kBAAkB,KAAK,KAAK,6BAA6B;AAAA,EAC3E;AAEA,MAAI,KAAK,MAAM;AAEb,QAAI;AACJ,QAAI,cAAc,OAAO,kBAAkB,WAAW;AACpD,eAAS;AAAA,IACX,WAAW,KAAK,YAAY;AAC1B,eAAS,KAAK,cAAc;AAAA,IAC9B;AACA,YAAQ,KACL,OAAoB,OAAO,EAC3B,KAAK,cAAc,KAAK,IAAI,EAC5B,KAAK,UAAU,UAAU,IAAI;AAChC,SAAK,MAAM,aAAa,OAAO,MAAM,aAAa;AAAA,EACpD,OAAO;AACL,SAAK,MAAM,aAAa,MAAM,MAAM,aAAa;AACjD,YAAQ;AAAA,EACV;AACA,MAAI,KAAK,SAAS;AAChB,OAAG,KAAK,SAAS,KAAK,OAAO;AAAA,EAC/B;AAEA,YAAU,IAAI,KAAK,IAAI,KAAK;AAE5B,MAAI,KAAK,cAAc;AACrB,UAAM,KAAK,SAAS,MAAM,KAAK,OAAO,IAAI,YAAY;AAAA,EACxD;AACA,SAAO;AACT;AAlDsB;AAoDf,IAAM,cAAc,wBAAC,MAAmB,SAA2B;AACxE,YAAU,IAAI,KAAK,IAAI,IAAI;AAC7B,GAF2B;AAIpB,IAAMC,SAAQ,6BAAM;AACzB,YAAU,MAAM;AAClB,GAFqB;AAId,IAAM,eAAe,wBAAC,SAA6C;AACxE,QAAM,KAAK,UAAU,IAAI,KAAK,EAAE;AAChC,MAAI;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA,gBAAgB,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI;AAAA,EACzE;AACA,QAAM,UAAU;AAChB,QAAM,OAAO,KAAK,QAAQ;AAC1B,MAAI,KAAK,aAAa;AACpB,OAAG;AAAA,MACD;AAAA,MACA,gBACG,KAAK,IAAI,OAAO,KAAK,QAAQ,KAC9B,QACC,KAAK,IAAI,KAAK,SAAS,IAAI,WAC5B;AAAA,IACJ;AAAA,EACF,OAAO;AACL,OAAG,KAAK,aAAa,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI,GAAG;AAAA,EAClE;AACA,SAAO;AACT,GAvB4B;", "names": ["getConfig", "text", "select", "select", "select", "getConfig", "getConfig", "text", "select", "rect", "rough", "rough", "rough", "angle", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "crossedCircle", "rough", "generateCirclePoints", "rough", "rough", "generateCirclePoints", "rough", "rough", "generateCirclePoints", "rough", "rough", "rough", "rough", "cylinder", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "filledCircle", "rough", "rough", "flippedTriangle", "rough", "state", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "rect", "rough", "options", "rect", "rough", "rough", "rough", "rough", "rough", "rough", "lightningBolt", "rough", "createCylinderPathD", "createOuterCylinderPathD", "createInnerCylinderPathD", "cylinder", "rough", "rough", "rough", "rough", "rough", "multiRect", "rough", "rough", "rough", "rough", "rect", "rough", "rough", "rough", "rough", "select", "rough", "text", "getConfig", "div", "dv", "select", "rect", "rough", "rough", "rough", "rect", "rough", "rough", "squareRect", "rough", "rect", "rough", "rough", "rough", "circle", "rough", "circle", "rough", "rough", "rough", "rect", "rough", "rough", "taggedRect", "rough", "rough", "rect", "rough", "createCylinderPathD", "createOuterCylinderPathD", "createInnerCylinderPathD", "cylinder", "rough", "rough", "rough", "rough", "rough", "rough", "getConfig", "rough", "rough", "rough", "rough", "rough", "rough", "rough", "windowPane", "rough", "select", "themeVariables", "options", "shapeSvg", "nameBBox", "rough", "text", "select", "rect", "roughRect", "yOffset", "select", "rough", "select", "addText", "text", "sanitizeText", "select", "getConfig", "rough", "rect", "text", "select", "_", "i", "nodes", "match", "rough", "select", "addText", "typeHeight", "rough", "rect", "text", "select", "getConfig", "sanitizeText", "rough", "rect", "rough", "options", "squareRect", "shapes", "shapes", "clear"]}