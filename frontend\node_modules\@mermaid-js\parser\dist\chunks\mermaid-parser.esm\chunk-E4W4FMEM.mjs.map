{"version": 3, "sources": ["../../../src/language/packet/tokenBuilder.ts", "../../../src/language/packet/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class PacketTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['packet-beta']);\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { CommonValueConverter } from '../common/valueConverter.js';\nimport { MermaidGeneratedSharedModule, PacketGeneratedModule } from '../generated/module.js';\nimport { PacketTokenBuilder } from './tokenBuilder.js';\n\n/**\n * Declaration of `Packet` services.\n */\ninterface PacketAddedServices {\n  parser: {\n    TokenBuilder: PacketTokenBuilder;\n    ValueConverter: CommonValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Packet` services.\n */\nexport type PacketServices = LangiumCoreServices & PacketAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Packet` services.\n */\nexport const PacketModule: Module<\n  PacketServices,\n  PartialLangiumCoreServices & PacketAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new PacketTokenBuilder(),\n    ValueConverter: () => new CommonValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createPacketServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Packet: PacketServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Packet: PacketServices = inject(\n    createDefaultCoreModule({ shared }),\n    PacketGeneratedModule,\n    PacketModule\n  );\n  shared.ServiceRegistry.register(Packet);\n  return { shared, Packet };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAM,qBAAN,cAAiC,4BAA4B;AAAA,EAFpE,OAEoE;AAAA;AAAA;AAAA,EAC3D,cAAc;AACnB,UAAM,CAAC,aAAa,CAAC;AAAA,EACvB;AACF;;;AC+BO,IAAM,eAGT;AAAA,EACF,QAAQ;AAAA,IACN,cAAc,6BAAM,IAAI,mBAAmB,GAA7B;AAAA,IACd,gBAAgB,6BAAM,IAAI,qBAAqB,GAA/B;AAAA,EAClB;AACF;AAgBO,SAAS,qBAAqB,UAA0C,iBAG7E;AACA,QAAM,SAAoC;AAAA,IACxC,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,SAAyB;AAAA,IAC7B,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,MAAM;AACtC,SAAO,EAAE,QAAQ,OAAO;AAC1B;AAfgB;", "names": []}