import{_ as ue,h as v,a2 as he,r as q,o as me,am as ge,i as f,j as a,k as r,n as e,p as n,w as d,M as m,z as l,F as g,x as y,B as C,A as R,N as z,m as de,y as I,E as ye,G as ce,R as M,C as J,t as fe,K as ke,H as re,a3 as be,a4 as $e}from"./index-b6a2842e.js";const xe={class:"multimodal-interaction-hub"},Ce={class:"interaction-control-center"},Me={class:"center-header"},we={class:"interaction-status"},Ve={class:"modality-switcher"},Se={class:"switcher-header"},ze={class:"active-modalities"},je={class:"modality-controls"},Ue={class:"control-group"},Ie={class:"control-info"},Re={key:0,class:"control-metrics"},Te={class:"control-info"},Pe={key:0,class:"control-metrics"},De={class:"control-info"},Ae={key:0,class:"control-metrics"},Ee={class:"control-info"},qe={key:0,class:"control-metrics"},Je={class:"multimodal-fusion-analysis"},Ne={class:"fusion-header"},Be={class:"fusion-confidence"},Fe={class:"confidence-meter"},Ke={class:"confidence-value"},Le={class:"fusion-visualization"},He={key:0,class:"fusion-panel voice-emotion-panel"},Qe={class:"panel-header"},Ge={class:"panel-content"},We={class:"voice-waveform-enhanced"},Oe={class:"waveform-container"},Xe={class:"emotion-overlay"},Ye={class:"emotion-name"},Ze={class:"emotion-value"},et={key:1,class:"fusion-panel text-semantic-panel"},tt={class:"panel-header"},st={class:"panel-content"},nt={class:"semantic-cloud"},ot={class:"keyword-cloud"},lt={class:"sentiment-analysis"},it={class:"sentiment-meter"},at={class:"intelligent-interaction-suggestions"},dt={class:"suggestions-header"},ct={class:"suggestion-mode"},rt={class:"suggestions-grid"},ut={class:"suggestion-type"},mt={class:"suggestion-content"},pt={class:"suggestion-actions"},vt={class:"interaction-quality-assessment"},_t={class:"assessment-header"},ht={class:"overall-quality"},gt={class:"quality-score"},yt={class:"quality-level"},ft={class:"quality-metrics"},kt={class:"metric-header"},bt={class:"metric-name"},$t={class:"metric-value"},xt={class:"metric-progress"},Ct={class:"metric-trend"},Mt={__name:"MultimodalInteractionHub",setup(K){const w=v({active:!1,text:"待激活"}),_=v({voice:!0,video:!0,text:!0,gesture:!1}),N=he(()=>Object.keys(_).filter(p=>_[p])),L=v({recognition:94}),H=v({detection:89}),Q=v({understanding:92}),A=v({accuracy:87}),V=q(91),B=v([{name:"自信",intensity:85},{name:"专业",intensity:92},{name:"紧张",intensity:15},{name:"友好",intensity:78}]),G=v(Array.from({length:64},(p,o)=>({id:o,intensity:Math.random()*100})));v([{name:"眼神交流",color:"#2ecc71"},{name:"手势表达",color:"#3498db"},{name:"面部表情",color:"#f39c12"},{name:"身体姿态",color:"#e74c3c"}]);const W=v([{word:"技术架构",weight:.9,sentiment:"positive"},{word:"团队协作",weight:.7,sentiment:"positive"},{word:"项目管理",weight:.8,sentiment:"neutral"},{word:"性能优化",weight:.6,sentiment:"positive"},{word:"挑战困难",weight:.5,sentiment:"negative"},{word:"解决方案",weight:.8,sentiment:"positive"},{word:"学习成长",weight:.7,sentiment:"positive"}]),j=v({positive:65,neutral:25,negative:10}),T=v([]);v([]),v([{name:"点赞",icon:"👍",confidence:92},{name:"强调",icon:"☝️",confidence:87},{name:"展示",icon:"🤲",confidence:78}]);const O=v({realtime:!0}),P=v([{id:1,type:"primary",category:"语音优化",title:"建议放慢语速",description:"当前语速略快，建议适当放慢以提高表达清晰度"},{id:2,type:"warning",category:"视觉交互",title:"增加眼神交流",description:"建议更多地看向摄像头，增强与面试官的眼神交流"},{id:3,type:"success",category:"内容表达",title:"技术描述优秀",description:"技术概念表达清晰，逻辑性强，继续保持"}]),b=v({score:87,level:"优秀"}),D=v([{name:"语音清晰度",value:92,trend:3},{name:"视觉表现",value:85,trend:-2},{name:"内容质量",value:89,trend:5},{name:"交互自然度",value:83,trend:1}]),Z=()=>w.active?"success":"info",ee=p=>({voice:"primary",video:"success",text:"warning",gesture:"info"})[p]||"info",te=p=>({voice:"语音",video:"视频",text:"文本",gesture:"手势"})[p]||p,E=p=>{console.log(`切换模态: ${p}`,_[p]),X()},X=()=>{const p=N.value.length;p===0?(w.active=!1,w.text="已停用"):p===1?(w.active=!0,w.text="单模态激活"):(w.active=!0,w.text=`多模态融合 (${p})`)},se=p=>({positive:"#2ecc71",neutral:"#95a5a6",negative:"#e74c3c"})[p]||"#95a5a6",ne=p=>p>=90?"#2ecc71":p>=80?"#f39c12":p>=70?"#e67e22":"#e74c3c",oe=p=>{console.log("应用建议:",p);const o=P.findIndex(h=>h.id===p.id);o>-1&&P.splice(o,1)},le=p=>{const o=P.findIndex(h=>h.id===p.id);o>-1&&P.splice(o,1)};let F=null;return me(()=>{X(),F=setInterval(()=>{document.querySelectorAll(".wave-bar").forEach((h,$)=>{const c=Math.random()*40+10;h.style.height=c+"px"}),V.value=Math.max(80,Math.min(98,V.value+(Math.random()-.5)*4)),B.forEach(h=>{h.intensity=Math.max(10,Math.min(100,h.intensity+(Math.random()-.5)*6))}),G.forEach(h=>{h.intensity=Math.max(0,Math.min(100,h.intensity+(Math.random()-.5)*10))}),D.forEach(h=>{const $=h.value;h.value=Math.max(60,Math.min(100,h.value+(Math.random()-.5)*3)),h.trend=h.value-$});const o=D.reduce((h,$)=>h+$.value,0)/D.length;b.score=Math.round(o),b.score>=90?b.level="卓越":b.score>=80?b.level="优秀":b.score>=70?b.level="良好":b.level="需改进"},1500),setInterval(()=>{_.gesture&&T.length<5&&T.push({id:Date.now(),x:Math.random()*80+10,y:Math.random()*80+10,color:`hsl(${Math.random()*360}, 70%, 60%)`}),T.length>8&&T.splice(0,T.length-8)},2e3)}),ge(()=>{F&&clearInterval(F)}),(p,o)=>{const h=f("el-tag"),$=f("el-switch"),c=f("el-icon"),s=f("Mouse"),U=f("el-button");return a(),r("div",xe,[e("div",Ce,[e("div",Me,[o[9]||(o[9]=e("h3",null,"iFlytek Spark 多模态交互中心",-1)),e("div",we,[n(h,{type:Z(),size:"large"},{default:d(()=>[m(l(w.text),1)]),_:1},8,["type"])])]),e("div",Ve,[e("div",Se,[o[10]||(o[10]=e("h4",null,"交互模式选择",-1)),e("div",ze,[(a(!0),r(g,null,y(N.value,i=>(a(),C(h,{key:i,type:ee(i),size:"small"},{default:d(()=>[m(l(te(i)),1)]),_:2},1032,["type"]))),128))])]),e("div",je,[e("div",Ue,[e("div",{class:R(["control-item",{active:_.voice}])},[n($,{modelValue:_.voice,"onUpdate:modelValue":o[0]||(o[0]=i=>_.voice=i),onChange:o[1]||(o[1]=i=>E("voice")),"active-color":"var(--iflytek-primary)"},null,8,["modelValue"]),e("div",Ie,[n(c,null,{default:d(()=>[n(z(de))]),_:1}),o[11]||(o[11]=e("span",null,"语音交互",-1))]),_.voice?(a(),r("div",Re,[e("small",null,"识别率: "+l(L.recognition)+"%",1)])):I("",!0)],2),e("div",{class:R(["control-item",{active:_.video}])},[n($,{modelValue:_.video,"onUpdate:modelValue":o[2]||(o[2]=i=>_.video=i),onChange:o[3]||(o[3]=i=>E("video")),"active-color":"var(--iflytek-primary)"},null,8,["modelValue"]),e("div",Te,[n(c,null,{default:d(()=>[n(z(ye))]),_:1}),o[12]||(o[12]=e("span",null,"视频分析",-1))]),_.video?(a(),r("div",Pe,[e("small",null,"检测精度: "+l(H.detection)+"%",1)])):I("",!0)],2),e("div",{class:R(["control-item",{active:_.text}])},[n($,{modelValue:_.text,"onUpdate:modelValue":o[4]||(o[4]=i=>_.text=i),onChange:o[5]||(o[5]=i=>E("text")),"active-color":"var(--iflytek-primary)"},null,8,["modelValue"]),e("div",De,[n(c,null,{default:d(()=>[n(z(ce))]),_:1}),o[13]||(o[13]=e("span",null,"文本理解",-1))]),_.text?(a(),r("div",Ae,[e("small",null,"理解度: "+l(Q.understanding)+"%",1)])):I("",!0)],2),e("div",{class:R(["control-item",{active:_.gesture}])},[n($,{modelValue:_.gesture,"onUpdate:modelValue":o[6]||(o[6]=i=>_.gesture=i),onChange:o[7]||(o[7]=i=>E("gesture")),"active-color":"var(--iflytek-primary)"},null,8,["modelValue"]),e("div",Ee,[n(c,null,{default:d(()=>[n(s)]),_:1}),o[14]||(o[14]=e("span",null,"手势识别",-1))]),_.gesture?(a(),r("div",qe,[e("small",null,"识别准确率: "+l(A.accuracy)+"%",1)])):I("",!0)],2)])])])]),e("div",Je,[e("div",Ne,[o[16]||(o[16]=e("h4",null,"实时多模态融合分析",-1)),e("div",Be,[o[15]||(o[15]=e("span",{class:"confidence-label"},"融合置信度",-1)),e("div",Fe,[e("div",{class:"confidence-fill",style:M({width:V.value+"%"})},null,4)]),e("span",Ke,l(V.value)+"%",1)])]),e("div",Le,[_.voice?(a(),r("div",He,[e("div",Qe,[n(c,null,{default:d(()=>[n(z(de))]),_:1}),o[17]||(o[17]=e("span",null,"语音情感融合",-1))]),e("div",Ge,[e("div",We,[e("div",Oe,[(a(),r(g,null,y(12,i=>e("div",{class:"wave-bar",key:i})),64))]),e("div",Xe,[(a(!0),r(g,null,y(B,i=>(a(),r("div",{class:"emotion-indicator",key:i.name,style:M({opacity:i.intensity/100})},[e("span",Ye,l(i.name),1),e("span",Ze,l(i.intensity)+"%",1)],4))),128))])])])])):I("",!0),_.text?(a(),r("div",et,[e("div",tt,[n(c,null,{default:d(()=>[n(z(ce))]),_:1}),o[18]||(o[18]=e("span",null,"文本语义融合",-1))]),e("div",st,[e("div",nt,[e("div",ot,[(a(!0),r(g,null,y(W,i=>(a(),r("span",{key:i.word,class:"keyword-bubble",style:M({fontSize:i.weight*20+12+"px",color:se(i.sentiment)})},l(i.word),5))),128))]),e("div",lt,[e("div",it,[e("div",{class:"sentiment-bar positive",style:M({width:j.positive+"%"})},[e("span",null,"积极 "+l(j.positive)+"%",1)],4),e("div",{class:"sentiment-bar neutral",style:M({width:j.neutral+"%"})},[e("span",null,"中性 "+l(j.neutral)+"%",1)],4),e("div",{class:"sentiment-bar negative",style:M({width:j.negative+"%"})},[e("span",null,"消极 "+l(j.negative)+"%",1)],4)])])])])])):I("",!0)])]),e("div",at,[e("div",dt,[o[19]||(o[19]=e("h4",null,"智能交互建议",-1)),e("div",ct,[n($,{modelValue:O.realtime,"onUpdate:modelValue":o[8]||(o[8]=i=>O.realtime=i),"active-text":"实时建议","inactive-text":"定时建议"},null,8,["modelValue"])])]),e("div",rt,[(a(!0),r(g,null,y(P,i=>(a(),r("div",{class:"suggestion-card",key:i.id},[e("div",ut,[n(h,{type:i.type},{default:d(()=>[m(l(i.category),1)]),_:2},1032,["type"])]),e("div",mt,[e("h5",null,l(i.title),1),e("p",null,l(i.description),1)]),e("div",pt,[n(U,{size:"small",type:"primary",onClick:S=>oe(i)},{default:d(()=>o[20]||(o[20]=[m(" 应用建议 ")])),_:2,__:[20]},1032,["onClick"]),n(U,{size:"small",onClick:S=>le(i)},{default:d(()=>o[21]||(o[21]=[m(" 忽略 ")])),_:2,__:[21]},1032,["onClick"])])]))),128))])]),e("div",vt,[e("div",_t,[o[22]||(o[22]=e("h4",null,"交互质量评估",-1)),e("div",ht,[e("span",gt,l(b.score),1),e("span",yt,l(b.level),1)])]),e("div",ft,[(a(!0),r(g,null,y(D,i=>(a(),r("div",{class:"metric-item",key:i.name},[e("div",kt,[e("span",bt,l(i.name),1),e("span",$t,l(i.value)+"%",1)]),e("div",xt,[e("div",{class:"progress-bar",style:M({width:i.value+"%",backgroundColor:ne(i.value)})},null,4)]),e("div",Ct,[n(c,{class:R({"trend-up":i.trend>0,"trend-down":i.trend<0})},{default:d(()=>[(a(),C(J(i.trend>0?"ArrowUp":i.trend<0?"ArrowDown":"Minus")))]),_:2},1032,["class"]),e("span",null,l(Math.abs(i.trend))+"%",1)])]))),128))])])])}}},$n=ue(Mt,[["__scopeId","data-v-69d6cd16"]]);const wt={class:"personalized-recommendation-engine"},Vt={class:"recommendation-console"},St={class:"console-header"},zt={class:"engine-status"},jt={class:"engine-metrics"},Ut={class:"metric-item"},It={class:"metric-item"},Rt={class:"recommendation-modes"},Tt={class:"mode-selector"},Pt={class:"mode-description"},Dt={key:0,class:"enterprise-recommendations"},At={class:"recommendations-header"},Et={class:"recommendation-filters"},qt={class:"recommendation-sections"},Jt={class:"recommendation-section candidate-recommendations"},Nt={class:"section-header"},Bt={class:"recommendation-count"},Ft={class:"candidates-grid"},Kt={class:"candidate-avatar"},Lt=["src","alt"],Ht={class:"match-score"},Qt={class:"candidate-info"},Gt={class:"candidate-position"},Wt={class:"candidate-experience"},Ot={class:"candidate-skills"},Xt={class:"candidate-actions"},Yt={class:"recommendation-reason"},Zt={class:"recommendation-section question-recommendations"},es={class:"section-header"},ts={class:"difficulty-filter"},ss={class:"questions-list"},ns={class:"question-header"},os={class:"question-category"},ls={class:"question-difficulty"},is={class:"question-content"},as={class:"question-metadata"},ds={class:"metadata-item"},cs={class:"metadata-value"},rs={class:"metadata-item"},us={class:"metadata-value"},ms={class:"metadata-item"},ps={class:"metadata-value"},vs={class:"question-actions"},_s={class:"recommendation-section strategy-recommendations"},hs={class:"strategies-grid"},gs={class:"strategy-content"},ys={class:"strategy-benefits"},fs={class:"strategy-actions"},ks={key:1,class:"candidate-recommendations-panel"},bs={class:"recommendations-header"},$s={class:"learning-progress"},xs={class:"learning-sections"},Cs={class:"learning-section skill-improvement"},Ms={class:"section-header"},ws={class:"skill-level"},Vs={class:"skills-roadmap"},Ss={class:"step-number"},zs={class:"step-content"},js={class:"step-resources"},Us={class:"step-actions"},Is={class:"learning-section interview-preparation"},Rs={class:"preparation-categories"},Ts=["onClick"],Ps={class:"category-info"},Ds={class:"category-progress"},As={class:"progress-text"},Es={class:"category-toggle"},qs={class:"category-content"},Js={class:"item-header"},Ns={class:"item-title"},Bs={class:"item-description"},Fs={class:"item-actions"},Ks={class:"learning-section job-matching"},Ls={class:"section-header"},Hs={class:"matching-filter"},Qs={class:"job-recommendations"},Gs={class:"job-header"},Ws={class:"company-logo"},Os=["src","alt"],Xs={class:"job-basic-info"},Ys={class:"company-name"},Zs={class:"job-location"},en={class:"match-indicator"},tn={class:"match-text"},sn={class:"job-details"},nn={class:"job-requirements"},on={class:"requirements-list"},ln={class:"job-benefits"},an={class:"benefit-item"},dn={class:"benefit-value"},cn={class:"benefit-item"},rn={class:"benefit-value"},un={class:"job-actions"},mn={class:"recommendation-analytics"},pn={class:"analytics-header"},vn={class:"analytics-controls"},_n={class:"analytics-metrics"},hn={class:"metric-content"},gn={class:"metric-value"},yn={class:"metric-label"},fn={__name:"PersonalizedRecommendationEngine",props:{userType:{type:String,default:"enterprise"}},setup(K){const w=K,_=q("intelligent"),N=q("medium"),L=q("intermediate"),H=q([]),Q=v({type:"success",text:"运行正常"}),A=v({accuracy:94.2,responseTime:156}),V=v({position:"frontend",experience:"mid"}),B=v({location:"beijing"}),G=q(68),W=v([{id:1,name:"张小明",avatar:"/images/placeholder-demo.jpg",position:"前端工程师",experience:3,matchScore:92,skills:["Vue.js","React","TypeScript","Node.js"],reason:"技能匹配度高，项目经验丰富"},{id:2,name:"李小红",avatar:"/images/placeholder-demo.jpg",position:"前端工程师",experience:4,matchScore:88,skills:["React","Angular","JavaScript","CSS3"],reason:"工作经验符合要求，技术栈匹配"},{id:3,name:"王小华",avatar:"/images/placeholder-demo.jpg",position:"前端工程师",experience:2,matchScore:85,skills:["Vue.js","JavaScript","HTML5","Webpack"],reason:"学习能力强，发展潜力大"}]),j=v([{id:1,title:"Vue.js组件通信方式",description:"请详细说明Vue.js中父子组件、兄弟组件之间的通信方式，并举例说明使用场景。",category:"前端框架",difficulty:3,duration:15,passRate:78,usageCount:245},{id:2,title:"JavaScript异步编程",description:"解释Promise、async/await的工作原理，并实现一个简单的Promise。",category:"JavaScript",difficulty:4,duration:20,passRate:65,usageCount:189},{id:3,title:"前端性能优化策略",description:"从多个角度分析前端性能优化的方法，包括代码层面、网络层面、渲染层面等。",category:"性能优化",difficulty:4,duration:25,passRate:72,usageCount:156}]),T=v([{id:1,title:"渐进式面试法",description:"从简单问题开始，逐步增加难度，帮助候选人建立信心",color:"#3498db",icon:"TrendCharts",benefits:["降低候选人紧张感","更好评估真实水平","提高面试体验"]},{id:2,title:"项目驱动面试",description:"围绕候选人的实际项目经验进行深入探讨",color:"#2ecc71",icon:"Document",benefits:["评估实际工作能力","了解解决问题思路","验证项目真实性"]},{id:3,title:"团队协作评估",description:"通过情景模拟评估候选人的团队合作能力",color:"#f39c12",icon:"User",benefits:["评估软技能","了解沟通风格","预测团队适应性"]}]),O=v([{id:1,step:1,name:"JavaScript基础强化",description:"深入理解ES6+语法、原型链、闭包等核心概念",resources:["MDN文档","在线练习","视频教程"],completed:!0,current:!1},{id:2,step:2,name:"Vue.js框架精通",description:"掌握Vue3 Composition API、状态管理、路由等高级特性",resources:["官方文档","实战项目","源码分析"],completed:!1,current:!0},{id:3,step:3,name:"前端工程化实践",description:"学习Webpack、Vite等构建工具，掌握CI/CD流程",resources:["工具文档","配置实践","最佳实践"],completed:!1,current:!1},{id:4,step:4,name:"性能优化专项",description:"深入学习前端性能优化策略和监控方法",resources:["性能分析工具","优化案例","监控平台"],completed:!1,current:!1}]),P=v([{id:1,name:"技术基础",icon:"Setting",color:"#3498db",progress:75,expanded:!1,items:[{id:1,title:"JavaScript核心概念",description:"掌握变量提升、作用域、this指向等基础概念",completed:!0},{id:2,title:"CSS布局技巧",description:"熟练使用Flexbox、Grid等现代布局方式",completed:!1}]},{id:2,name:"框架应用",icon:"Document",color:"#2ecc71",progress:60,expanded:!1,items:[{id:3,title:"Vue.js组件设计",description:"理解组件化思想，掌握组件通信方式",completed:!1},{id:4,title:"状态管理模式",description:"掌握Vuex/Pinia等状态管理工具的使用",completed:!1}]},{id:3,name:"项目经验",icon:"Medal",color:"#f39c12",progress:45,expanded:!1,items:[{id:5,title:"项目架构设计",description:"能够设计合理的前端项目架构",completed:!1},{id:6,title:"团队协作经验",description:"具备良好的团队协作和沟通能力",completed:!1}]}]),b=v([{id:1,title:"高级前端工程师",company:"阿里巴巴",companyLogo:"/images/placeholder-case.jpg",location:"杭州",salary:"25K-40K",experience:"3-5年",matchPercentage:92,requirements:[{skill:"Vue.js",matched:!0},{skill:"React",matched:!1},{skill:"TypeScript",matched:!0},{skill:"Node.js",matched:!0}]},{id:2,title:"前端技术专家",company:"腾讯",companyLogo:"/images/placeholder-case.jpg",location:"深圳",salary:"30K-50K",experience:"5年以上",matchPercentage:85,requirements:[{skill:"React",matched:!1},{skill:"Vue.js",matched:!0},{skill:"微前端",matched:!1},{skill:"性能优化",matched:!0}]}]),D=v([{name:"推荐准确率",value:"94.2%",change:5.3,color:"#2ecc71",icon:"TrendCharts"},{name:"用户满意度",value:"4.8/5.0",change:2.1,color:"#f39c12",icon:"Medal"},{name:"匹配成功率",value:"78.5%",change:-1.2,color:"#e74c3c",icon:"DataBoard"},{name:"响应时间",value:"156ms",change:-8.7,color:"#3498db",icon:"Timer"}]),Z=c=>c>=80?"#2ecc71":c>=60?"#f39c12":"#e74c3c",ee=c=>({intelligent:"基于iFlytek Spark AI的深度学习算法，提供最精准的个性化推荐",collaborative:"基于用户行为和偏好的协同过滤算法，发现相似用户的选择",content:"基于内容特征匹配的推荐算法，确保推荐结果的相关性",hybrid:"融合多种推荐算法的混合模式，提供最全面的推荐结果"})[c]||"",te=c=>{console.log("推荐模式切换:",c)},E=c=>({前端框架:"primary",JavaScript:"success",性能优化:"warning",算法:"danger"})[c]||"info",X=c=>{console.log("邀请候选人:",c.name)},se=c=>{console.log("查看候选人详情:",c.name)},ne=c=>{console.log("添加题目到面试:",c.title)},oe=c=>{console.log("预览题目:",c.title)},le=c=>{console.log("应用面试策略:",c.title)},F=c=>{console.log("开始学习:",c.name)},p=c=>{c.expanded=!c.expanded},o=c=>{console.log("开始准备:",c.title)},h=c=>{console.log("申请职位:",c.title)},$=c=>{console.log("查看职位详情:",c.title)};return me(()=>{console.log("个性化推荐引擎已加载，用户类型:",w.userType),setInterval(()=>{A.accuracy=94.2+(Math.random()-.5)*2,A.responseTime=156+Math.floor((Math.random()-.5)*50),D.forEach(c=>{const s=c.change;c.change=s+(Math.random()-.5)*2})},5e3)}),(c,s)=>{const U=f("el-tag"),i=f("el-icon"),S=f("el-radio-button"),ie=f("el-radio-group"),k=f("el-option"),Y=f("el-select"),x=f("el-button"),pe=f("el-badge"),ve=f("el-rate"),ae=f("el-progress"),_e=f("el-date-picker");return a(),r("div",wt,[e("div",Vt,[e("div",St,[s[7]||(s[7]=e("h3",null,"iFlytek Spark 个性化推荐引擎",-1)),e("div",zt,[n(U,{type:Q.type,size:"large"},{default:d(()=>[m(l(Q.text),1)]),_:1},8,["type"]),e("div",jt,[e("span",Ut,[n(i,null,{default:d(()=>[n(z(fe))]),_:1}),m(" 匹配精度: "+l(A.accuracy)+"% ",1)]),e("span",It,[n(i,null,{default:d(()=>[n(z(ke))]),_:1}),m(" 响应时间: "+l(A.responseTime)+"ms ",1)])])])]),e("div",Rt,[e("div",Tt,[n(ie,{modelValue:_.value,"onUpdate:modelValue":s[0]||(s[0]=t=>_.value=t),onChange:te},{default:d(()=>[n(S,{value:"intelligent"},{default:d(()=>s[8]||(s[8]=[m("智能推荐")])),_:1,__:[8]}),n(S,{value:"collaborative"},{default:d(()=>s[9]||(s[9]=[m("协同过滤")])),_:1,__:[9]}),n(S,{value:"content"},{default:d(()=>s[10]||(s[10]=[m("内容匹配")])),_:1,__:[10]}),n(S,{value:"hybrid"},{default:d(()=>s[11]||(s[11]=[m("混合模式")])),_:1,__:[11]})]),_:1},8,["modelValue"])]),e("div",Pt,[e("p",null,l(ee(_.value)),1)])])]),K.userType==="enterprise"?(a(),r("div",Dt,[e("div",At,[s[12]||(s[12]=e("h4",null,"企业智能推荐",-1)),e("div",Et,[n(Y,{modelValue:V.position,"onUpdate:modelValue":s[1]||(s[1]=t=>V.position=t),placeholder:"选择职位"},{default:d(()=>[n(k,{label:"前端工程师",value:"frontend"}),n(k,{label:"后端工程师",value:"backend"}),n(k,{label:"全栈工程师",value:"fullstack"}),n(k,{label:"算法工程师",value:"algorithm"})]),_:1},8,["modelValue"]),n(Y,{modelValue:V.experience,"onUpdate:modelValue":s[2]||(s[2]=t=>V.experience=t),placeholder:"经验要求"},{default:d(()=>[n(k,{label:"1-3年",value:"junior"}),n(k,{label:"3-5年",value:"mid"}),n(k,{label:"5年以上",value:"senior"})]),_:1},8,["modelValue"])])]),e("div",qt,[e("div",Jt,[e("div",Nt,[s[14]||(s[14]=e("h5",null,"优质候选人推荐",-1)),e("div",Bt,[n(pe,{value:W.length,type:"primary"},{default:d(()=>[n(x,{size:"small"},{default:d(()=>s[13]||(s[13]=[m("查看全部")])),_:1,__:[13]})]),_:1},8,["value"])])]),e("div",Ft,[(a(!0),r(g,null,y(W,t=>(a(),r("div",{class:"candidate-card",key:t.id},[e("div",Kt,[e("img",{src:t.avatar,alt:t.name},null,8,Lt),e("div",Ht,l(t.matchScore)+"%",1)]),e("div",Qt,[e("h6",null,l(t.name),1),e("div",Gt,l(t.position),1),e("div",Wt,l(t.experience)+"年经验",1),e("div",Ot,[(a(!0),r(g,null,y(t.skills.slice(0,3),u=>(a(),C(U,{key:u,size:"small"},{default:d(()=>[m(l(u),1)]),_:2},1024))),128))])]),e("div",Xt,[n(x,{size:"small",type:"primary",onClick:u=>X(t)},{default:d(()=>s[15]||(s[15]=[m(" 邀请面试 ")])),_:2,__:[15]},1032,["onClick"]),n(x,{size:"small",onClick:u=>se(t)},{default:d(()=>s[16]||(s[16]=[m(" 查看详情 ")])),_:2,__:[16]},1032,["onClick"])]),e("div",Yt,[e("small",null,"推荐理由: "+l(t.reason),1)])]))),128))])]),e("div",Zt,[e("div",es,[s[20]||(s[20]=e("h5",null,"智能题目推荐",-1)),e("div",ts,[n(ie,{modelValue:N.value,"onUpdate:modelValue":s[3]||(s[3]=t=>N.value=t),size:"small"},{default:d(()=>[n(S,{value:"easy"},{default:d(()=>s[17]||(s[17]=[m("简单")])),_:1,__:[17]}),n(S,{value:"medium"},{default:d(()=>s[18]||(s[18]=[m("中等")])),_:1,__:[18]}),n(S,{value:"hard"},{default:d(()=>s[19]||(s[19]=[m("困难")])),_:1,__:[19]})]),_:1},8,["modelValue"])])]),e("div",ss,[(a(!0),r(g,null,y(j,t=>(a(),r("div",{class:"question-item",key:t.id},[e("div",ns,[e("div",os,[n(U,{type:E(t.category)},{default:d(()=>[m(l(t.category),1)]),_:2},1032,["type"])]),e("div",ls,[n(ve,{modelValue:t.difficulty,"onUpdate:modelValue":u=>t.difficulty=u,disabled:"","show-score":""},null,8,["modelValue","onUpdate:modelValue"])])]),e("div",is,[e("h6",null,l(t.title),1),e("p",null,l(t.description),1)]),e("div",as,[e("div",ds,[s[21]||(s[21]=e("span",{class:"metadata-label"},"预计时长:",-1)),e("span",cs,l(t.duration)+"分钟",1)]),e("div",rs,[s[22]||(s[22]=e("span",{class:"metadata-label"},"通过率:",-1)),e("span",us,l(t.passRate)+"%",1)]),e("div",ms,[s[23]||(s[23]=e("span",{class:"metadata-label"},"使用次数:",-1)),e("span",ps,l(t.usageCount),1)])]),e("div",vs,[n(x,{size:"small",type:"primary",onClick:u=>ne(t)},{default:d(()=>s[24]||(s[24]=[m(" 添加到面试 ")])),_:2,__:[24]},1032,["onClick"]),n(x,{size:"small",onClick:u=>oe(t)},{default:d(()=>s[25]||(s[25]=[m(" 预览题目 ")])),_:2,__:[25]},1032,["onClick"])])]))),128))])]),e("div",_s,[s[27]||(s[27]=e("div",{class:"section-header"},[e("h5",null,"面试策略建议")],-1)),e("div",hs,[(a(!0),r(g,null,y(T,t=>(a(),r("div",{class:"strategy-card",key:t.id},[e("div",{class:"strategy-icon",style:M({backgroundColor:t.color})},[n(i,null,{default:d(()=>[(a(),C(J(t.icon)))]),_:2},1024)],4),e("div",gs,[e("h6",null,l(t.title),1),e("p",null,l(t.description),1),e("div",ys,[(a(!0),r(g,null,y(t.benefits,u=>(a(),r("div",{class:"benefit-item",key:u},[n(i,null,{default:d(()=>[n(z(re))]),_:1}),e("span",null,l(u),1)]))),128))])]),e("div",fs,[n(x,{size:"small",type:"primary",onClick:u=>le(t)},{default:d(()=>s[26]||(s[26]=[m(" 应用策略 ")])),_:2,__:[26]},1032,["onClick"])])]))),128))])])])])):I("",!0),K.userType==="candidate"?(a(),r("div",ks,[e("div",bs,[s[29]||(s[29]=e("h4",null,"个性化学习推荐",-1)),e("div",$s,[s[28]||(s[28]=e("span",{class:"progress-label"},"学习进度",-1)),n(ae,{percentage:G.value,color:Z(G.value)},null,8,["percentage","color"])])]),e("div",xs,[e("div",Cs,[e("div",Ms,[s[30]||(s[30]=e("h5",null,"技能提升建议",-1)),e("div",ws,[n(Y,{modelValue:L.value,"onUpdate:modelValue":s[4]||(s[4]=t=>L.value=t),placeholder:"选择当前水平"},{default:d(()=>[n(k,{label:"初级",value:"beginner"}),n(k,{label:"中级",value:"intermediate"}),n(k,{label:"高级",value:"advanced"})]),_:1},8,["modelValue"])])]),e("div",Vs,[(a(!0),r(g,null,y(O,t=>(a(),r("div",{class:"roadmap-item",key:t.id},[e("div",{class:R(["roadmap-step",{completed:t.completed,current:t.current}])},[e("div",Ss,l(t.step),1),e("div",zs,[e("h6",null,l(t.name),1),e("p",null,l(t.description),1),e("div",js,[(a(!0),r(g,null,y(t.resources,u=>(a(),C(U,{key:u,size:"small"},{default:d(()=>[m(l(u),1)]),_:2},1024))),128))])]),e("div",Us,[t.completed?(a(),C(x,{key:1,size:"small",disabled:""},{default:d(()=>[n(i,null,{default:d(()=>[n(z(re))]),_:1}),s[32]||(s[32]=m(" 已完成 "))]),_:1,__:[32]})):(a(),C(x,{key:0,size:"small",type:"primary",onClick:u=>F(t)},{default:d(()=>s[31]||(s[31]=[m(" 开始学习 ")])),_:2,__:[31]},1032,["onClick"]))])],2)]))),128))])]),e("div",Is,[s[33]||(s[33]=e("div",{class:"section-header"},[e("h5",null,"面试准备指导")],-1)),e("div",Rs,[(a(!0),r(g,null,y(P,t=>(a(),r("div",{class:"category-tab",key:t.id},[e("div",{class:"category-header",onClick:u=>p(t)},[e("div",{class:"category-icon",style:M({backgroundColor:t.color})},[n(i,null,{default:d(()=>[(a(),C(J(t.icon)))]),_:2},1024)],4),e("div",Ps,[e("h6",null,l(t.name),1),e("div",Ds,[n(ae,{percentage:t.progress,size:"small","show-text":!1},null,8,["percentage"]),e("span",As,l(t.progress)+"%",1)])]),e("div",Es,[n(i,null,{default:d(()=>[(a(),C(J(t.expanded?"ArrowUp":"ArrowDown")))]),_:2},1024)])],8,Ts),be(e("div",qs,[(a(!0),r(g,null,y(t.items,u=>(a(),r("div",{class:"preparation-item",key:u.id},[e("div",Js,[e("span",Ns,l(u.title),1),n(U,{type:u.completed?"success":"info",size:"small"},{default:d(()=>[m(l(u.completed?"已完成":"待完成"),1)]),_:2},1032,["type"])]),e("div",Bs,l(u.description),1),e("div",Fs,[n(x,{size:"small",type:"primary",onClick:kn=>o(u)},{default:d(()=>[m(l(u.completed?"复习":"开始"),1)]),_:2},1032,["onClick"])])]))),128))],512),[[$e,t.expanded]])]))),128))])]),e("div",Ks,[e("div",Ls,[s[34]||(s[34]=e("h5",null,"职位匹配推荐",-1)),e("div",Hs,[n(Y,{modelValue:B.location,"onUpdate:modelValue":s[5]||(s[5]=t=>B.location=t),placeholder:"期望地点"},{default:d(()=>[n(k,{label:"北京",value:"beijing"}),n(k,{label:"上海",value:"shanghai"}),n(k,{label:"深圳",value:"shenzhen"}),n(k,{label:"杭州",value:"hangzhou"})]),_:1},8,["modelValue"])])]),e("div",Qs,[(a(!0),r(g,null,y(b,t=>(a(),r("div",{class:"job-card",key:t.id},[e("div",Gs,[e("div",Ws,[e("img",{src:t.companyLogo,alt:t.company},null,8,Os)]),e("div",Xs,[e("h6",null,l(t.title),1),e("div",Ys,l(t.company),1),e("div",Zs,l(t.location),1)]),e("div",en,[e("div",{class:"match-circle",style:M({"--match-percentage":t.matchPercentage+"%"})},[e("span",tn,l(t.matchPercentage)+"%",1)],4)])]),e("div",sn,[e("div",nn,[s[35]||(s[35]=e("h6",null,"技能要求",-1)),e("div",on,[(a(!0),r(g,null,y(t.requirements,u=>(a(),r("span",{key:u.skill,class:R(["requirement-tag",{matched:u.matched}])},l(u.skill),3))),128))])]),e("div",ln,[e("div",an,[s[36]||(s[36]=e("span",{class:"benefit-label"},"薪资范围:",-1)),e("span",dn,l(t.salary),1)]),e("div",cn,[s[37]||(s[37]=e("span",{class:"benefit-label"},"工作经验:",-1)),e("span",rn,l(t.experience),1)])])]),e("div",un,[n(x,{size:"small",type:"primary",onClick:u=>h(t)},{default:d(()=>s[38]||(s[38]=[m(" 立即申请 ")])),_:2,__:[38]},1032,["onClick"]),n(x,{size:"small",onClick:u=>$(t)},{default:d(()=>s[39]||(s[39]=[m(" 查看详情 ")])),_:2,__:[39]},1032,["onClick"])])]))),128))])])])])):I("",!0),e("div",mn,[e("div",pn,[s[40]||(s[40]=e("h4",null,"推荐效果分析",-1)),e("div",vn,[n(_e,{modelValue:H.value,"onUpdate:modelValue":s[6]||(s[6]=t=>H.value=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small"},null,8,["modelValue"])])]),e("div",_n,[(a(!0),r(g,null,y(D,t=>(a(),r("div",{class:"metric-card",key:t.name},[e("div",{class:"metric-icon",style:M({backgroundColor:t.color})},[n(i,null,{default:d(()=>[(a(),C(J(t.icon)))]),_:2},1024)],4),e("div",hn,[e("div",gn,l(t.value),1),e("div",yn,l(t.name),1),e("div",{class:R(["metric-change",{positive:t.change>0,negative:t.change<0}])},[n(i,null,{default:d(()=>[(a(),C(J(t.change>0?"ArrowUp":"ArrowDown")))]),_:2},1024),m(" "+l(Math.abs(t.change))+"% ",1)],2)])]))),128))])])])}}},xn=ue(fn,[["__scopeId","data-v-e681e9f4"]]);export{$n as M,xn as P};
