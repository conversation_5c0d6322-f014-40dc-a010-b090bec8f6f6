#!/usr/bin/env node

/**
 * 即梦AI API连接测试脚本
 * 测试API连接并生成第一个演示视频
 */

import fetch from 'node-fetch';

console.log('🔍 即梦AI API连接测试\n');

// 检查环境变量
const ACCESS_KEY_ID = process.env.JIMENG_ACCESS_KEY_ID;
const SECRET_ACCESS_KEY = process.env.JIMENG_SECRET_ACCESS_KEY;

if (!ACCESS_KEY_ID || !SECRET_ACCESS_KEY) {
    console.log('❌ 环境变量未设置');
    console.log('请先设置环境变量:');
    console.log('Windows: set JIMENG_ACCESS_KEY_ID=your_key');
    console.log('Linux/Mac: export JIMENG_ACCESS_KEY_ID="your_key"');
    process.exit(1);
}

console.log('✅ 环境变量已设置');
console.log(`Access Key ID: ${ACCESS_KEY_ID.substring(0, 10)}...`);

// 测试提示词 - 简化版本
const testPrompt = `专业AI面试系统界面演示，科大讯飞Spark技术，蓝紫色渐变UI，清晰的中文标题"智能面试评估系统"，包含"开始面试""能力分析""生成报告"按钮，现代企业设计，白色文字，高对比度，3分钟演示`;

console.log('🎬 测试提示词:');
console.log(testPrompt);
console.log('');

// 简化的API调用测试
async function testAPI() {
    try {
        console.log('🚀 发送API请求...');
        
        // 这里需要根据即梦AI的实际API文档调整
        // 以下是通用的API调用格式
        const requestBody = {
            access_key_id: ACCESS_KEY_ID,
            secret_access_key: SECRET_ACCESS_KEY,
            prompt: testPrompt,
            duration: 180, // 3分钟
            resolution: "1920x1080",
            format: "mp4"
        };
        
        // 注意：这里的API端点需要根据即梦AI的实际文档调整
        const response = await fetch('https://api.jimeng.ai/v1/video/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'MultimodalInterviewSystem/1.0'
            },
            body: JSON.stringify(requestBody)
        });
        
        console.log(`📡 响应状态: ${response.status}`);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ API调用成功!');
            console.log('📋 响应数据:', JSON.stringify(result, null, 2));
            
            if (result.task_id || result.taskId) {
                const taskId = result.task_id || result.taskId;
                console.log(`\n🎯 任务已创建: ${taskId}`);
                console.log('⏳ 视频生成中，请稍后检查状态...');
                
                // 保存任务信息
                const taskInfo = {
                    taskId: taskId,
                    prompt: testPrompt,
                    status: 'pending',
                    createdAt: new Date().toISOString()
                };
                
                const fs = await import('fs');
                fs.writeFileSync('test-task.json', JSON.stringify(taskInfo, null, 2));
                console.log('💾 任务信息已保存到 test-task.json');
            }
            
        } else {
            const errorText = await response.text();
            console.log('❌ API调用失败');
            console.log(`错误信息: ${errorText}`);
            
            // 常见错误处理建议
            if (response.status === 401) {
                console.log('\n💡 可能的解决方案:');
                console.log('1. 检查API密钥是否正确');
                console.log('2. 确认密钥是否已激活');
                console.log('3. 检查账户余额');
            } else if (response.status === 429) {
                console.log('\n💡 请求过于频繁，请稍后再试');
            }
        }
        
    } catch (error) {
        console.log('❌ 网络请求失败');
        console.log(`错误: ${error.message}`);
        
        console.log('\n💡 可能的解决方案:');
        console.log('1. 检查网络连接');
        console.log('2. 确认API端点地址是否正确');
        console.log('3. 检查防火墙设置');
    }
}

// 运行测试
testAPI().then(() => {
    console.log('\n🏁 测试完成');
}).catch(error => {
    console.error('测试过程中出现错误:', error);
});
