export { FullIconCustomisations, IconifyIconCustomisations, IconifyIconSize, IconifyIconSizeCustomisations, defaultIconCustomisations, defaultIconSizeCustomisations } from './customisations/defaults.js';
export { mergeCustomisations } from './customisations/merge.js';
export { toBoolean } from './customisations/bool.js';
export { flipFromString } from './customisations/flip.js';
export { rotateFromString } from './customisations/rotate.js';
export { IconifyIconName, IconifyIconSource, matchIconName, stringToIcon, validateIconName } from './icon/name.js';
export { mergeIconData } from './icon/merge.js';
export { mergeIconTransformations } from './icon/transformations.js';
export { FullExtendedIconifyIcon, FullIconifyIcon, PartialExtendedIconifyIcon, defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations } from './icon/defaults.js';
export { makeIconSquare } from './icon/square.js';
export { ParentIconsList, ParentIconsTree, getIconsTree } from './icon-set/tree.js';
export { parseIconSet, parseIconSetAsync } from './icon-set/parse.js';
export { validateIconSet } from './icon-set/validate.js';
export { quicklyValidateIconSet } from './icon-set/validate-basic.js';
export { expandIconSet } from './icon-set/expand.js';
export { minifyIconSet } from './icon-set/minify.js';
export { getIcons } from './icon-set/get-icons.js';
export { getIconData } from './icon-set/get-icon.js';
export { convertIconSetInfo } from './icon-set/convert-info.js';
export { IconifyIconBuildResult, iconToSVG } from './svg/build.js';
export { mergeDefsAndContent, splitSVGDefs, wrapSVGContent } from './svg/defs.js';
export { replaceIDs } from './svg/id.js';
export { calculateSize } from './svg/size.js';
export { encodeSvgForCss } from './svg/encode-svg-for-css.js';
export { trimSVG } from './svg/trim.js';
export { prettifySVG } from './svg/pretty.js';
export { iconToHTML } from './svg/html.js';
export { svgToData, svgToURL } from './svg/url.js';
export { cleanUpInnerHTML } from './svg/inner-html.js';
export { SVGViewBox, getSVGViewBox } from './svg/viewbox.js';
export { ParsedSVGContent, buildParsedSVG, convertParsedSVG, parseSVGContent } from './svg/parse.js';
export { colorKeywords } from './colors/keywords.js';
export { colorToString, compareColors, stringToColor } from './colors/index.js';
export { getIconCSS, getIconContentCSS } from './css/icon.js';
export { getIconsCSS, getIconsContentCSS } from './css/icons.js';
export { CustomCollections, CustomIconLoader, ExternalPkgName, IconCustomizations, IconCustomizer, IconifyLoaderOptions, InlineCollection, UniversalIconLoader } from './loader/types.js';
export { mergeIconProps } from './loader/utils.js';
export { getCustomIcon } from './loader/custom.js';
export { searchForIcon } from './loader/modern.js';
export { loadIcon } from './loader/loader.js';
export { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from './emoji/cleanup.js';
export { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number } from './emoji/convert.js';
export { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString } from './emoji/format.js';
export { parseEmojiTestFile } from './emoji/test/parse.js';
export { getQualifiedEmojiVariations } from './emoji/test/variations.js';
export { findMissingEmojis } from './emoji/test/missing.js';
export { createOptimisedRegex, createOptimisedRegexForEmojiSequences } from './emoji/regex/create.js';
export { prepareEmojiForIconSet, prepareEmojiForIconsList } from './emoji/parse.js';
export { findAndReplaceEmojisInText } from './emoji/replace/replace.js';
export { camelToKebab, camelize, pascalize, snakelize } from './misc/strings.js';
export { commonObjectProps, compareObjects, unmergeObjects } from './misc/objects.js';
export { sanitiseTitleAttribute } from './misc/title.js';
export { IconifyIcon } from '@iconify/types';
import './colors/types.js';
import './css/types.js';
import '@antfu/utils';
import './emoji/test/tree.js';
import './emoji/data.js';
import './emoji/test/similar.js';
import './emoji/test/components.js';
import './emoji/test/name.js';
import './emoji/replace/find.js';
