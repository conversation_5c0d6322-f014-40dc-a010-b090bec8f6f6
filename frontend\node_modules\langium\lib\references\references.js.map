{"version": 3, "file": "references.js", "sourceRoot": "", "sources": ["../../src/references/references.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAUhF,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AA8CjD,MAAM,OAAO,iBAAiB;IAK1B,YAAY,QAA6B;QACrC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;QACpD,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC;IACzD,CAAC;IAED,eAAe,CAAC,aAAsB;QAClC,IAAI,aAAa,EAAE,CAAC;YAChB,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC;YACvC,IAAI,UAAU,IAAI,QAAQ,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAI,QAA2B,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAEnE,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;oBACzB,OAAO,SAAS,CAAC,GAAG,CAAC;gBACzB,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClC,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;wBAC1B,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ;+BAC7B,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM;+BAC3C,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC;4BAC3C,OAAO,GAAG,CAAC,GAAG,CAAC;wBACnB,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACX,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACzD,6FAA6F;gBAC7F,IAAI,QAAQ,IAAI,CAAC,QAAQ,KAAK,aAAa,IAAI,WAAW,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC;oBACnF,OAAO,QAAQ,CAAC;gBACpB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,mBAAmB,CAAC,aAAsB;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC1D,OAAO,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,OAAO,CAAC,QAAQ,CAAC;QAC1C,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,cAAc,CAAC,UAAmB,EAAE,OAA8B;QAC9D,MAAM,IAAI,GAA2B,EAAE,CAAC;QACxC,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,GAAG,EAAE,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC;QACL,CAAC;QACD,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;QAC5G,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QACzG,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAES,kBAAkB,CAAC,UAAmB;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO;gBACH,SAAS,EAAE,GAAG,CAAC,GAAG;gBAClB,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,GAAG,CAAC,GAAG;gBAClB,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,iBAAiB,CAAC,QAAQ,CAAC;gBACpC,KAAK,EAAE,IAAI;aACd,CAAC;QACN,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ"}