{"version": 3, "file": "stacking-context.js", "sourceRoot": "", "sources": ["../../../src/render/stacking-context.ts"], "names": [], "mappings": ";;;AACA,2CAAyC;AACzC,+CAA4F;AAC5F,qCAAiH;AAEjH,+BAAiC;AAEjC,6EAAwE;AACxE,6EAAwE;AACxE,0DAAiE;AAGjE;IAUI,yBAAY,SAAuB;QAC/B,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,sCAAsC,GAAG,EAAE,CAAC;QACjD,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC;IACvC,CAAC;IACL,sBAAC;AAAD,CAAC,AApBD,IAoBC;AApBY,0CAAe;AAsB5B;IAKI,sBAAqB,SAA2B,EAAW,MAA2B;QAAjE,cAAS,GAAT,SAAS,CAAkB;QAAW,WAAM,GAAN,MAAM,CAAqB;QAJ7E,YAAO,GAAqB,EAAE,CAAC;QAKpC,IAAI,CAAC,MAAM,GAAG,IAAI,0BAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,uBAAa,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,EAAE;YAC1C,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC7F,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC5F,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,yBAAe,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;SACpE;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,oBAAqB,EAAE;YACtD,IAAM,SAAS,GAAG,qCAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtD,IAAM,UAAU,GAAG,sCAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAExD,IAAI,gBAAS,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;gBAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,oBAAU,CAAC,SAAS,EAAE,4CAAsD,CAAC,CAAC,CAAC;aACxG;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,oBAAU,CAAC,SAAS,6BAAkC,CAAC,CAAC;gBAC9E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,oBAAU,CAAC,UAAU,kBAAuB,CAAC,CAAC;aACvE;SACJ;IACL,CAAC;IAED,iCAAU,GAAV,UAAW,MAAoB;QAC3B,IAAI,MAAM,GAAG,iCAAmC,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAChG,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,MAAM,EAAE;YACX,IAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,MAAM,IAAK,OAAA,CAAC,sBAAY,CAAC,MAAM,CAAC,EAArB,CAAqB,CAAC,CAAC;YACjF,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,mBAAoB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAClF,OAAO,CAAC,OAAO,OAAf,OAAO,EAAY,eAAe,EAAE;gBACpC,MAAM,GAAG,iCAAmC,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC9F,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,oBAAqB,EAAE;oBACxD,IAAM,SAAS,GAAG,qCAAsB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACxD,IAAM,UAAU,GAAG,sCAAuB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC1D,IAAI,CAAC,gBAAS,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;wBACnC,OAAO,CAAC,OAAO,CACX,IAAI,oBAAU,CAAC,UAAU,EAAE,4CAAsD,CAAC,CACrF,CAAC;qBACL;iBACJ;aACJ;iBAAM;gBACH,OAAO,CAAC,OAAO,OAAf,OAAO,EAAY,eAAe,EAAE;aACvC;YAED,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SAC1B;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,UAAC,MAAM,IAAK,OAAA,kBAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAA/B,CAA+B,CAAC,CAAC;IACvE,CAAC;IACL,mBAAC;AAAD,CAAC,AA1DD,IA0DC;AA1DY,oCAAY;AA4DzB,IAAM,cAAc,GAAG,UACnB,MAAoB,EACpB,eAAgC,EAChC,mBAAoC,EACpC,SAAyB;IAEzB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAK;QACpC,IAAM,0BAA0B,GAAG,kBAAQ,CAAC,KAAK,CAAC,KAAK,wCAAsC,CAAC;QAC9F,IAAM,sBAAsB,GAAG,kBAAQ,CAAC,KAAK,CAAC,KAAK,mCAAiC,CAAC;QACrF,IAAM,cAAc,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACvD,IAAI,kBAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,uBAAoB,EAAE;YACnD,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAClC;QAED,IAAM,cAAc,GAAG,kBAAQ,CAAC,KAAK,CAAC,KAAK,wBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAEnF,IAAI,0BAA0B,IAAI,sBAAsB,EAAE;YACtD,IAAM,WAAW,GACb,0BAA0B,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC;YAEtG,IAAM,KAAK,GAAG,IAAI,eAAe,CAAC,cAAc,CAAC,CAAC;YAElD,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE;gBACzF,IAAM,OAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;gBACxC,IAAI,OAAK,GAAG,CAAC,EAAE;oBACX,IAAI,OAAK,GAAG,CAAC,CAAC;oBAEd,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,UAAC,OAAO,EAAE,CAAC;wBACvC,IAAI,OAAK,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;4BACvD,OAAK,GAAG,CAAC,CAAC;4BACV,OAAO,KAAK,CAAC;yBAChB;6BAAM,IAAI,OAAK,GAAG,CAAC,EAAE;4BAClB,OAAO,IAAI,CAAC;yBACf;wBAED,OAAO,KAAK,CAAC;oBACjB,CAAC,CAAC,CAAC;oBACH,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,OAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;iBACtD;qBAAM,IAAI,OAAK,GAAG,CAAC,EAAE;oBAClB,IAAI,OAAK,GAAG,CAAC,CAAC;oBACd,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,UAAC,OAAO,EAAE,CAAC;wBACvC,IAAI,OAAK,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;4BACxD,OAAK,GAAG,CAAC,GAAG,CAAC,CAAC;4BACd,OAAO,KAAK,CAAC;yBAChB;6BAAM,IAAI,OAAK,GAAG,CAAC,EAAE;4BAClB,OAAO,IAAI,CAAC;yBACf;wBAED,OAAO,KAAK,CAAC;oBACjB,CAAC,CAAC,CAAC;oBACH,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,OAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;iBACtD;qBAAM;oBACH,WAAW,CAAC,sCAAsC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAClE;aACJ;iBAAM;gBACH,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE;oBAC3B,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC/C;qBAAM;oBACH,WAAW,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpD;aACJ;YAED,cAAc,CACV,cAAc,EACd,KAAK,EACL,0BAA0B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB,EACxD,cAAc,CACjB,CAAC;SACL;aAAM;YACH,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE;gBAC9B,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACpD;iBAAM;gBACH,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACvD;YAED,cAAc,CAAC,cAAc,EAAE,eAAe,EAAE,mBAAmB,EAAE,cAAc,CAAC,CAAC;SACxF;QAED,IAAI,kBAAQ,CAAC,KAAK,CAAC,KAAK,wBAAsB,EAAE;YAC5C,gBAAgB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;SAC3C;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,IAAM,gBAAgB,GAAG,UAAC,KAAuB,EAAE,QAAwB;IACvE,IAAI,SAAS,GAAG,KAAK,YAAY,yCAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,IAAM,QAAQ,GAAG,KAAK,YAAY,yCAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IAC9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACzB,IACI,IAAI,CAAC,SAAS,YAAY,yCAAkB;YAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,QAAQ;YACxC,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC,EAC5B;YACE,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;SACpC;QAED,IAAI,CAAC,SAAS,GAAG,2BAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAEzF,SAAS,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAClC;AACL,CAAC,CAAC;AAEK,IAAM,qBAAqB,GAAG,UAAC,SAA2B;IAC7D,IAAM,cAAc,GAAG,IAAI,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACzD,IAAM,IAAI,GAAG,IAAI,eAAe,CAAC,cAAc,CAAC,CAAC;IACjD,IAAM,SAAS,GAAmB,EAAE,CAAC;IACrC,cAAc,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IACtD,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACtD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAPW,QAAA,qBAAqB,yBAOhC"}