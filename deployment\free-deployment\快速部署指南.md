# 🚀 免费云服务器快速部署指南

## 📋 部署流程概览

```
申请免费服务器 → 上传项目文件 → 配置API密钥 → 一键部署 → 访问系统
     5分钟           2分钟         1分钟       5分钟      完成！
```

## 🎯 第一步：申请Oracle Cloud免费服务器

### 快速申请链接
👉 **直接访问**：https://www.oracle.com/cloud/free/

### 关键配置
- **地区选择**：Japan East (Tokyo) 
- **实例类型**：VM.Standard.A1.Flex (4核24GB ARM)
- **操作系统**：Ubuntu 20.04
- **存储**：50GB
- **网络**：勾选"分配公网IP"

### 安全组设置
开放端口：22 (SSH)、80 (HTTP)、443 (HTTPS)

## 📁 第二步：准备项目文件

### 方法一：直接下载（推荐）
在你的电脑上创建一个文件夹，将以下文件保存：

1. **创建 `deployment.zip` 文件**，包含：
   - `free-deploy.sh` (部署脚本)
   - `docker-compose-free.yml` (Docker配置)
   - `Dockerfile.backend-free` (后端配置)
   - `Dockerfile.frontend-free` (前端配置)
   - `nginx-free.conf` (Nginx配置)

### 方法二：使用Git
```bash
git clone https://github.com/your-repo/cursor_softcup.git
cd cursor_softcup/deployment/free-deployment
```

## 🔑 第三步：连接服务器

### Windows用户
1. 下载 **WinSCP** (文件传输) + **PuTTY** (终端连接)
2. 使用WinSCP上传 `deployment` 文件夹到服务器
3. 使用PuTTY连接服务器终端

### Mac/Linux用户
```bash
# 上传文件
scp -i your-key.pem -r deployment/ ubuntu@your-server-ip:/home/<USER>/

# 连接服务器
ssh -i your-key.pem ubuntu@your-server-ip
```

## ⚙️ 第四步：配置iFlytek API密钥

### 获取iFlytek密钥
1. 访问：https://console.xfyun.cn/
2. 注册/登录账号
3. 创建应用，获取：
   - APP_ID
   - API_KEY  
   - API_SECRET

### 在服务器上配置
```bash
# 进入部署目录
cd deployment/free-deployment

# 创建配置文件
nano .env

# 输入以下内容：
IFLYTEK_APP_ID=你的APP_ID
IFLYTEK_API_KEY=你的API_KEY
IFLYTEK_API_SECRET=你的API_SECRET
IFLYTEK_SPARK_URL=wss://spark-api.xf-yun.com/v3.5/chat
DOMAIN_NAME=你的服务器IP
```

## 🚀 第五步：一键部署

```bash
# 给脚本执行权限
chmod +x free-deploy.sh

# 运行部署脚本
./free-deploy.sh
```

部署过程大约需要5-10分钟，脚本会自动：
- 安装Docker和Docker Compose
- 优化系统性能（创建swap）
- 构建和启动服务
- 配置网络和安全设置

## 🌐 第六步：访问系统

部署完成后，你会看到：
```
========================================
          免费部署完成！
========================================
访问地址: http://your-server-ip
```

现在任何人都可以通过这个网址访问你的系统了！

## 🔧 管理命令

```bash
# 查看服务状态
docker-compose -f docker-compose-free.yml ps

# 查看日志
docker-compose -f docker-compose-free.yml logs -f

# 重启服务
docker-compose -f docker-compose-free.yml restart

# 停止服务
docker-compose -f docker-compose-free.yml down

# 更新系统
git pull origin main
docker-compose -f docker-compose-free.yml up -d --build
```

## 📊 性能优化建议

### 针对免费服务器的优化
- **内存优化**：系统会自动创建swap文件
- **CPU优化**：使用单进程模式
- **网络优化**：启用gzip压缩
- **存储优化**：定期清理Docker缓存

### 监控命令
```bash
# 查看系统资源
htop

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看Docker资源
docker stats
```

## 🆘 故障排除

### 常见问题及解决方案

**问题1：部署脚本执行失败**
```bash
# 检查错误日志
sudo journalctl -u docker

# 重新运行部署
./free-deploy.sh
```

**问题2：服务无法访问**
```bash
# 检查防火墙
sudo ufw status

# 开放端口
sudo ufw allow 80
sudo ufw allow 443
```

**问题3：内存不足**
```bash
# 检查swap
swapon --show

# 清理Docker缓存
docker system prune -a
```

**问题4：iFlytek API连接失败**
```bash
# 检查配置文件
cat .env

# 重启服务
docker-compose -f docker-compose-free.yml restart
```

## 💡 成本分析

### 完全免费方案
- **Oracle Cloud Always Free**：永久免费
- **iFlytek API**：有免费额度
- **域名**：可选，约50元/年
- **总成本**：0元/月 🎉

### 升级选项
如果需要更好性能：
- **阿里云学生机**：9.9元/月
- **腾讯云轻量服务器**：50元/月
- **AWS/GCP**：按使用量付费

## 🎯 部署成功验证

访问 `http://你的服务器IP`，如果看到系统首页，说明部署成功！

测试功能：
- [ ] 首页加载正常
- [ ] 用户注册/登录
- [ ] AI面试功能
- [ ] 文件上传
- [ ] 报告生成

---

**🎉 恭喜！你现在拥有了一个永久免费的在线多模态面试系统！**

任何人都可以通过网址直接访问，无需你启动本地服务器！
