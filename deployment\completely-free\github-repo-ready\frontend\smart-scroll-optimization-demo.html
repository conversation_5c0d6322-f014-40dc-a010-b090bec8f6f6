<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek星火智能滚动控制优化演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1890ff 0%, #0066cc 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .problem-section {
            background: #fff2f0;
            border-left: 4px solid #ff4d4f;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }

        .problem-title {
            font-size: 1.3em;
            color: #ff4d4f;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .problem-list {
            list-style: none;
            padding: 0;
        }

        .problem-list li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }

        .problem-list li:before {
            content: "❌";
            position: absolute;
            left: 0;
        }

        .solution-section {
            background: #f6ffed;
            border-left: 4px solid #52c41a;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }

        .solution-title {
            font-size: 1.3em;
            color: #52c41a;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .solution-list {
            list-style: none;
            padding: 0;
        }

        .solution-list li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }

        .solution-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }

        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .demo-box {
            border: 2px solid #d9d9d9;
            border-radius: 12px;
            overflow: hidden;
            background: white;
        }

        .demo-header {
            background: #f5f5f5;
            padding: 15px;
            font-weight: 600;
            text-align: center;
            border-bottom: 1px solid #d9d9d9;
        }

        .demo-content {
            height: 300px;
            overflow-y: auto;
            padding: 15px;
            position: relative;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 8px;
            max-width: 80%;
            animation: fadeIn 0.5s ease-out;
        }

        .ai-message {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            margin-left: auto;
        }

        .user-message {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }

        .typing-indicator {
            background: #fff7e6;
            border: 1px solid #ffd591;
            font-style: italic;
            color: #fa8c16;
        }

        .scroll-hint {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: #1890ff;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            animation: bounce 2s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #1890ff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .feature-title {
            font-size: 1.2em;
            color: #262626;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
        }

        .code-block {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }

        .comparison-table th {
            background: #f5f5f5;
            font-weight: 600;
            color: #262626;
        }

        .comparison-table .before {
            color: #ff4d4f;
        }

        .comparison-table .after {
            color: #52c41a;
        }

        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e8e8e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📜 智能滚动控制优化</h1>
            <p>解决AI生成内容时强制滚动的用户体验问题</p>
        </div>

        <div class="content">
            <!-- 问题描述 -->
            <div class="problem-section">
                <div class="problem-title">🚨 原有问题</div>
                <ul class="problem-list">
                    <li>AI生成内容时强制滚动到底部，用户无法查看历史对话</li>
                    <li>打字机效果期间频繁自动滚动，影响用户阅读体验</li>
                    <li>用户向上滚动查看历史时被强制拉回到当前位置</li>
                    <li>缺乏用户主动滚动行为的检测机制</li>
                </ul>
            </div>

            <!-- 解决方案 -->
            <div class="solution-section">
                <div class="solution-title">✨ 优化方案</div>
                <ul class="solution-list">
                    <li>智能检测用户滚动行为，区分主动滚动和自动滚动</li>
                    <li>只在用户位于底部附近时才执行自动滚动</li>
                    <li>提供"回到最新消息"按钮，让用户主动选择</li>
                    <li>优化打字机效果，减少不必要的滚动操作</li>
                </ul>
            </div>

            <!-- 对比演示 -->
            <div class="demo-container">
                <div class="demo-box">
                    <div class="demo-header" style="background: #fff2f0; color: #ff4d4f;">
                        ❌ 优化前：强制滚动
                    </div>
                    <div class="demo-content" id="beforeDemo">
                        <div class="message user-message">用户: 请介绍一下机器学习</div>
                        <div class="message ai-message">AI: 机器学习是人工智能的一个分支...</div>
                        <div class="message user-message">用户: 深度学习和机器学习有什么区别？</div>
                        <div class="message ai-message">AI: 深度学习是机器学习的一个子集...</div>
                        <div class="message user-message">用户: 我不知道，请告诉我答案</div>
                        <div class="message typing-indicator">AI正在思考中...</div>
                        <div style="color: #ff4d4f; font-size: 12px; text-align: center; margin-top: 20px;">
                            ⚠️ 用户无法自由滚动查看历史对话
                        </div>
                    </div>
                </div>

                <div class="demo-box">
                    <div class="demo-header" style="background: #f6ffed; color: #52c41a;">
                        ✅ 优化后：智能滚动
                    </div>
                    <div class="demo-content" id="afterDemo">
                        <div class="message user-message">用户: 请介绍一下机器学习</div>
                        <div class="message ai-message">AI: 机器学习是人工智能的一个分支...</div>
                        <div class="message user-message">用户: 深度学习和机器学习有什么区别？</div>
                        <div class="message ai-message">AI: 深度学习是机器学习的一个子集...</div>
                        <div class="message user-message">用户: 我不知道，请告诉我答案</div>
                        <div class="message typing-indicator">AI正在思考中...</div>
                        <div class="scroll-hint">📍 回到最新</div>
                        <div style="color: #52c41a; font-size: 12px; text-align: center; margin-top: 20px;">
                            ✅ 用户可以自由滚动，AI智能判断是否自动滚动
                        </div>
                    </div>
                </div>
            </div>

            <!-- 核心功能 -->
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-title">🎯 用户行为检测</div>
                    <div class="feature-description">
                        实时监控用户滚动行为，区分主动向上滚动和自然浏览，智能判断是否需要自动滚动到底部。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">📍 智能滚动策略</div>
                    <div class="feature-description">
                        只在用户位于消息底部附近（100px阈值内）时才执行自动滚动，避免打断用户查看历史对话。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">🔄 回到最新按钮</div>
                    <div class="feature-description">
                        当用户向上滚动查看历史时，显示浮动的"回到最新消息"按钮，让用户主动选择是否回到底部。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">⚡ 优化打字机效果</div>
                    <div class="feature-description">
                        减少打字机效果中的频繁滚动调用，只在必要时进行滚动，提升用户阅读体验。
                    </div>
                </div>
            </div>

            <!-- 技术实现 -->
            <h2 style="color: #1890ff; margin: 30px 0 20px 0;">⚙️ 技术实现</h2>
            
            <div class="code-block">
// 智能滚动控制系统
const scrollState = ref({
  userScrolledUp: false,        // 用户是否主动向上滚动
  lastScrollTop: 0,             // 上次滚动位置
  autoScrollEnabled: true,      // 是否启用自动滚动
  scrollThreshold: 100,         // 滚动阈值
  showScrollToBottomHint: false // 是否显示"回到最新"提示
})

// 检测用户滚动行为
const handleUserScroll = () => {
  const container = messagesContainer.value
  const currentScrollTop = container.scrollTop
  const distanceFromBottom = container.scrollHeight - container.clientHeight - currentScrollTop

  // 检测用户是否主动向上滚动
  if (currentScrollTop < scrollState.value.lastScrollTop) {
    scrollState.value.userScrolledUp = true
    if (distanceFromBottom > scrollState.value.scrollThreshold) {
      scrollState.value.showScrollToBottomHint = true
    }
  } else if (distanceFromBottom <= 10) {
    // 用户滚动到底部附近，重新启用自动滚动
    scrollState.value.userScrolledUp = false
    scrollState.value.showScrollToBottomHint = false
  }
}

// 智能滚动到底部
const scrollToBottom = (force = false) => {
  if (scrollState.value.userScrolledUp && !force) {
    console.log('用户正在查看历史消息，跳过自动滚动')
    return
  }
  // 执行滚动...
}
            </div>

            <!-- 对比表格 -->
            <h2 style="color: #1890ff; margin: 30px 0 20px 0;">📊 优化效果对比</h2>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能特性</th>
                        <th>优化前</th>
                        <th>优化后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>AI生成时滚动行为</td>
                        <td class="before">强制滚动到底部</td>
                        <td class="after">智能判断是否滚动</td>
                    </tr>
                    <tr>
                        <td>用户查看历史对话</td>
                        <td class="before">被强制拉回底部</td>
                        <td class="after">可以自由浏览历史</td>
                    </tr>
                    <tr>
                        <td>打字机效果期间</td>
                        <td class="before">频繁自动滚动</td>
                        <td class="after">减少不必要滚动</td>
                    </tr>
                    <tr>
                        <td>回到最新消息</td>
                        <td class="before">无主动选择</td>
                        <td class="after">提供"回到最新"按钮</td>
                    </tr>
                    <tr>
                        <td>用户体验</td>
                        <td class="before">被动、受限</td>
                        <td class="after">主动、自由</td>
                    </tr>
                </tbody>
            </table>

            <!-- 使用说明 -->
            <h2 style="color: #1890ff; margin: 30px 0 20px 0;">🚀 使用说明</h2>
            
            <div style="background: #f0f7ff; padding: 20px; border-radius: 8px; border-left: 4px solid #1890ff;">
                <h3 style="color: #1890ff; margin-bottom: 15px;">如何体验优化效果：</h3>
                <ol style="padding-left: 20px; line-height: 1.8;">
                    <li>进入iFlytek星火智能面试系统的文本主导面试页面</li>
                    <li>开始与AI面试官对话，观察正常的自动滚动行为</li>
                    <li>当AI开始生成回复时，尝试向上滚动查看历史对话</li>
                    <li>注意系统不会强制将您拉回到底部</li>
                    <li>观察右下角出现的"回到最新消息"按钮</li>
                    <li>点击按钮可以主动回到最新的对话内容</li>
                </ol>
            </div>
        </div>

        <div class="footer">
            <p>iFlytek星火智能面试系统 - 智能滚动控制优化 🚀</p>
            <p>让用户在AI生成内容时也能自由浏览对话历史</p>
        </div>
    </div>

    <script>
        // 模拟滚动行为演示
        document.addEventListener('DOMContentLoaded', function() {
            const beforeDemo = document.getElementById('beforeDemo');
            const afterDemo = document.getElementById('afterDemo');
            
            // 为优化前的演示添加强制滚动效果
            beforeDemo.addEventListener('scroll', function() {
                setTimeout(() => {
                    this.scrollTop = this.scrollHeight;
                }, 100);
            });
            
            // 为优化后的演示添加智能滚动效果
            let userScrolled = false;
            afterDemo.addEventListener('scroll', function() {
                const distanceFromBottom = this.scrollHeight - this.clientHeight - this.scrollTop;
                if (distanceFromBottom > 50) {
                    userScrolled = true;
                } else {
                    userScrolled = false;
                }
            });
            
            // 点击"回到最新"按钮
            afterDemo.querySelector('.scroll-hint').addEventListener('click', function() {
                afterDemo.scrollTop = afterDemo.scrollHeight;
                userScrolled = false;
            });
        });
    </script>
</body>
</html>
