/**
 * iFlytek API配置修复脚本
 * 自动修复iFlytek API配置问题
 */

import { writeFileSync, readFileSync } from 'fs';
import { join } from 'path';

console.log('🔧 iFlytek API配置修复工具');
console.log('='.repeat(50));

// 检查当前配置
function checkCurrentConfig() {
  console.log('📋 当前配置状态：');
  
  const envVars = [
    'VUE_APP_IFLYTEK_API_URL',
    'VUE_APP_IFLYTEK_APP_ID', 
    'VUE_APP_IFLYTEK_API_KEY',
    'VUE_APP_IFLYTEK_API_SECRET',
    'VUE_APP_MOCK_API_RESPONSES'
  ];

  envVars.forEach(varName => {
    const value = process.env[varName];
    if (value && !value.includes('your_') && !value.includes('您的')) {
      console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
    } else {
      console.log(`❌ ${varName}: 未配置或使用默认值`);
    }
  });
}

// 创建临时配置以启用模拟模式
function enableSimulationMode() {
  console.log('\n🔄 启用模拟模式...');
  
  const envLocalContent = `# 临时配置 - 启用模拟模式
# 这将允许系统正常运行，但使用模拟的AI响应

# iFlytek星火大模型配置
VUE_APP_IFLYTEK_API_URL=https://spark-api.xf-yun.com
VUE_APP_IFLYTEK_APP_ID=simulation_mode
VUE_APP_IFLYTEK_API_KEY=simulation_mode  
VUE_APP_IFLYTEK_API_SECRET=simulation_mode

# 启用模拟模式
VUE_APP_MOCK_API_RESPONSES=true

# 开发模式配置
VUE_APP_DEBUG_MODE=true

# 注意：要使用真实的iFlytek API，请：
# 1. 访问 https://console.xfyun.cn/ 获取真实API密钥
# 2. 替换上面的配置值
# 3. 将 VUE_APP_MOCK_API_RESPONSES 设置为 false
`;

  try {
    writeFileSync('.env.local', envLocalContent);
    console.log('✅ 已创建 .env.local 文件，启用模拟模式');
    console.log('✅ 系统现在可以正常运行（使用模拟AI响应）');
  } catch (error) {
    console.error('❌ 创建配置文件失败:', error.message);
  }
}

// 显示下一步操作指南
function showNextSteps() {
  console.log('\n📝 下一步操作：');
  console.log('1. 重启开发服务器：npm run dev');
  console.log('2. 控制台警告将消失，系统使用模拟模式运行');
  console.log('3. 要使用真实API，请获取iFlytek API密钥并更新 .env.local');
  
  console.log('\n🔗 获取真实API密钥：');
  console.log('- 访问：https://console.xfyun.cn/');
  console.log('- 注册并创建应用');
  console.log('- 获取 APP_ID、API_KEY、API_SECRET');
  console.log('- 更新 .env.local 文件中的配置');
  
  console.log('\n⚠️  注意事项：');
  console.log('- 模拟模式下AI回答是预设的，不是真实的AI生成');
  console.log('- 真实API模式提供更好的面试体验');
  console.log('- .env.local 文件不会提交到版本控制');
}

// 主函数
function main() {
  checkCurrentConfig();
  enableSimulationMode();
  showNextSteps();
  
  console.log('\n✨ 配置修复完成！');
  console.log('请重启开发服务器以应用更改。');
}

// 运行修复
main();
