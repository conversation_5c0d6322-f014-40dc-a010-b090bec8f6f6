{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-VX7I4HLL.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  MermaidGeneratedSharedModule,\n  TreemapGeneratedModule,\n  __name\n} from \"./chunk-YAJQ3QCK.mjs\";\n\n// src/language/treemap/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/treemap/tokenBuilder.ts\nvar TreemapTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"TreemapTokenBuilder\");\n  }\n  constructor() {\n    super([\"treemap\"]);\n  }\n};\n\n// src/language/treemap/valueConverter.ts\nvar classDefRegex = /classDef\\s+([A-Z_a-z]\\w+)(?:\\s+([^\\n\\r;]*))?;?/;\nvar TreemapValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"TreemapValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name === \"NUMBER2\") {\n      return parseFloat(input.replace(/,/g, \"\"));\n    } else if (rule.name === \"SEPARATOR\") {\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === \"STRING2\") {\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === \"INDENTATION\") {\n      return input.length;\n    } else if (rule.name === \"ClassDef\") {\n      if (typeof input !== \"string\") {\n        return input;\n      }\n      const match = classDefRegex.exec(input);\n      if (match) {\n        return {\n          $type: \"ClassDefStatement\",\n          className: match[1],\n          styleText: match[2] || void 0\n        };\n      }\n    }\n    return void 0;\n  }\n};\n\n// src/language/treemap/treemap-validator.ts\nfunction registerValidationChecks(services) {\n  const validator = services.validation.TreemapValidator;\n  const registry = services.validation.ValidationRegistry;\n  if (registry) {\n    const checks = {\n      Treemap: validator.checkSingleRoot.bind(validator)\n      // Remove unused validation for TreemapRow\n    };\n    registry.register(checks, validator);\n  }\n}\n__name(registerValidationChecks, \"registerValidationChecks\");\nvar TreemapValidator = class {\n  static {\n    __name(this, \"TreemapValidator\");\n  }\n  /**\n   * Validates that a treemap has only one root node.\n   * A root node is defined as a node that has no indentation.\n   */\n  checkSingleRoot(doc, accept) {\n    let rootNodeIndentation;\n    for (const row of doc.TreemapRows) {\n      if (!row.item) {\n        continue;\n      }\n      if (rootNodeIndentation === void 0 && // Check if this is a root node (no indentation)\n      row.indent === void 0) {\n        rootNodeIndentation = 0;\n      } else if (row.indent === void 0) {\n        accept(\"error\", \"Multiple root nodes are not allowed in a treemap.\", {\n          node: row,\n          property: \"item\"\n        });\n      } else if (rootNodeIndentation !== void 0 && rootNodeIndentation >= parseInt(row.indent, 10)) {\n        accept(\"error\", \"Multiple root nodes are not allowed in a treemap.\", {\n          node: row,\n          property: \"item\"\n        });\n      }\n    }\n  }\n};\n\n// src/language/treemap/module.ts\nvar TreemapModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new TreemapTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new TreemapValueConverter(), \"ValueConverter\")\n  },\n  validation: {\n    TreemapValidator: /* @__PURE__ */ __name(() => new TreemapValidator(), \"TreemapValidator\")\n  }\n};\nfunction createTreemapServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Treemap = inject(\n    createDefaultCoreModule({ shared }),\n    TreemapGeneratedModule,\n    TreemapModule\n  );\n  shared.ServiceRegistry.register(Treemap);\n  registerValidationChecks(Treemap);\n  return { shared, Treemap };\n}\n__name(createTreemapServices, \"createTreemapServices\");\n\nexport {\n  TreemapModule,\n  createTreemapServices\n};\n"], "mappings": "4IAiBA,IAAIA,EAAsB,cAAcC,CAA4B,CAjBpE,MAiBoE,CAAAC,EAAA,4BAClE,MAAO,CACLA,EAAO,KAAM,qBAAqB,CACpC,CACA,aAAc,CACZ,MAAM,CAAC,SAAS,CAAC,CACnB,CACF,EAGIC,EAAgB,iDAChBC,EAAwB,cAAcC,CAA8B,CA5BxE,MA4BwE,CAAAH,EAAA,8BACtE,MAAO,CACLA,EAAO,KAAM,uBAAuB,CACtC,CACA,mBAAmBI,EAAMC,EAAOC,EAAU,CACxC,GAAIF,EAAK,OAAS,UAChB,OAAO,WAAWC,EAAM,QAAQ,KAAM,EAAE,CAAC,EACpC,GAAID,EAAK,OAAS,YACvB,OAAOC,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,EACrC,GAAID,EAAK,OAAS,UACvB,OAAOC,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,EACrC,GAAID,EAAK,OAAS,cACvB,OAAOC,EAAM,OACR,GAAID,EAAK,OAAS,WAAY,CACnC,GAAI,OAAOC,GAAU,SACnB,OAAOA,EAET,IAAME,EAAQN,EAAc,KAAKI,CAAK,EACtC,GAAIE,EACF,MAAO,CACL,MAAO,oBACP,UAAWA,EAAM,CAAC,EAClB,UAAWA,EAAM,CAAC,GAAK,MACzB,CAEJ,CAEF,CACF,EAGA,SAASC,EAAyBC,EAAU,CAC1C,IAAMC,EAAYD,EAAS,WAAW,iBAChCE,EAAWF,EAAS,WAAW,mBACrC,GAAIE,EAAU,CACZ,IAAMC,EAAS,CACb,QAASF,EAAU,gBAAgB,KAAKA,CAAS,CAEnD,EACAC,EAAS,SAASC,EAAQF,CAAS,CACrC,CACF,CAVSV,EAAAQ,EAAA,4BAWTR,EAAOQ,EAA0B,0BAA0B,EAC3D,IAAIK,EAAmB,KAAM,CAvE7B,MAuE6B,CAAAb,EAAA,yBAC3B,MAAO,CACLA,EAAO,KAAM,kBAAkB,CACjC,CAKA,gBAAgBc,EAAKC,EAAQ,CAC3B,IAAIC,EACJ,QAAWC,KAAOH,EAAI,YACfG,EAAI,OAGLD,IAAwB,QAC5BC,EAAI,SAAW,OACbD,EAAsB,EACbC,EAAI,SAAW,OACxBF,EAAO,QAAS,oDAAqD,CACnE,KAAME,EACN,SAAU,MACZ,CAAC,EACQD,IAAwB,QAAUA,GAAuB,SAASC,EAAI,OAAQ,EAAE,GACzFF,EAAO,QAAS,oDAAqD,CACnE,KAAME,EACN,SAAU,MACZ,CAAC,EAGP,CACF,EAGIC,EAAgB,CAClB,OAAQ,CACN,aAA8BlB,EAAO,IAAM,IAAIF,EAAuB,cAAc,EACpF,eAAgCE,EAAO,IAAM,IAAIE,EAAyB,gBAAgB,CAC5F,EACA,WAAY,CACV,iBAAkCF,EAAO,IAAM,IAAIa,EAAoB,kBAAkB,CAC3F,CACF,EACA,SAASM,EAAsBC,EAAUC,EAAiB,CACxD,IAAMC,EAASC,EACbC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAUH,EACdI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAV,CACF,EACA,OAAAI,EAAO,gBAAgB,SAASI,CAAO,EACvClB,EAAyBkB,CAAO,EACzB,CAAE,OAAAJ,EAAQ,QAAAI,CAAQ,CAC3B,CAbS1B,EAAAmB,EAAA,yBAcTnB,EAAOmB,EAAuB,uBAAuB", "names": ["TreemapTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "classDefRegex", "TreemapValueConverter", "AbstractMermaidValueConverter", "rule", "input", "_cstNode", "match", "registerValidationChecks", "services", "validator", "registry", "checks", "TreemapValidator", "doc", "accept", "rootNodeIndentation", "row", "TreemapModule", "createTreemapServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Treemap", "createDefaultCoreModule", "TreemapGeneratedModule"]}