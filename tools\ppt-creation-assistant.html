<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek PPT制作助手</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1890ff 0%, #667eea 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .color-palette {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .color-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .color-item:hover {
            transform: scale(1.1);
        }
        
        .color-box {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            border: 2px solid white;
            margin-bottom: 5px;
            position: relative;
        }
        
        .color-code {
            font-size: 12px;
            font-weight: bold;
            text-align: center;
        }
        
        .slide-preview {
            background: white;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            min-height: 300px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .slide-number {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #1890ff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .layout-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .layout-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
            text-align: center;
        }
        
        .layout-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .btn {
            background: #52c41a;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #389e0d;
        }
        
        .btn-primary {
            background: #1890ff;
        }
        
        .btn-primary:hover {
            background: #0066cc;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #52c41a;
            width: 0%;
            transition: width 0.3s;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            cursor: pointer;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .checklist li:before {
            content: '☐ ';
            margin-right: 10px;
            font-size: 16px;
        }
        
        .checklist li.checked:before {
            content: '☑ ';
            color: #52c41a;
        }
        
        .text-input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .slide-content {
            white-space: pre-wrap;
            font-family: monospace;
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .icon-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .icon-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .export-section {
            background: rgba(82, 196, 26, 0.2);
            border: 2px solid #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 iFlytek PPT制作助手</h1>
        
        <!-- 品牌色彩方案 -->
        <div class="section">
            <h3>🎨 iFlytek品牌色彩方案</h3>
            <div class="color-palette">
                <div class="color-item" onclick="copyColor('#1890ff')">
                    <div class="color-box" style="background: #1890ff;"></div>
                    <div class="color-code">#1890ff<br>主色调</div>
                </div>
                <div class="color-item" onclick="copyColor('#0066cc')">
                    <div class="color-box" style="background: #0066cc;"></div>
                    <div class="color-code">#0066cc<br>深蓝色</div>
                </div>
                <div class="color-item" onclick="copyColor('#667eea')">
                    <div class="color-box" style="background: #667eea;"></div>
                    <div class="color-code">#667eea<br>渐变蓝</div>
                </div>
                <div class="color-item" onclick="copyColor('#52c41a')">
                    <div class="color-box" style="background: #52c41a;"></div>
                    <div class="color-code">#52c41a<br>成功绿</div>
                </div>
                <div class="color-item" onclick="copyColor('#faad14')">
                    <div class="color-box" style="background: #faad14;"></div>
                    <div class="color-code">#faad14<br>警告橙</div>
                </div>
                <div class="color-item" onclick="copyColor('#f5222d')">
                    <div class="color-box" style="background: #f5222d;"></div>
                    <div class="color-code">#f5222d<br>错误红</div>
                </div>
            </div>
            <p>💡 点击色块复制颜色代码到剪贴板</p>
        </div>
        
        <!-- 幻灯片进度跟踪 -->
        <div class="section">
            <h3>📊 制作进度跟踪</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="slideProgress"></div>
            </div>
            <p id="progressText">已完成：0/14 页</p>
            
            <ul class="checklist" id="slideChecklist">
                <li data-slide="1">第1页：封面页</li>
                <li data-slide="2">第2页：项目背景与价值</li>
                <li data-slide="3">第3页：系统架构设计</li>
                <li data-slide="4">第4页：功能特性展示</li>
                <li data-slide="5">第5页：多模态分析能力</li>
                <li data-slide="6">第6页：智能反馈系统</li>
                <li data-slide="7">第7页：个性化学习路径</li>
                <li data-slide="8">第8页：创新亮点</li>
                <li data-slide="9">第9页：系统演示</li>
                <li data-slide="10">第10页：实用价值与影响</li>
                <li data-slide="11">第11页：技术实现亮点</li>
                <li data-slide="12">第12页：未来发展规划</li>
                <li data-slide="13">第13页：总结</li>
                <li data-slide="14">第14页：致谢</li>
            </ul>
        </div>
        
        <!-- 布局模板选择 -->
        <div class="section">
            <h3>📐 布局模板选择</h3>
            <div class="layout-grid">
                <div class="layout-item" onclick="selectLayout('title')">
                    <h4>标题页</h4>
                    <p>大标题 + 副标题 + 背景</p>
                </div>
                <div class="layout-item" onclick="selectLayout('two-column')">
                    <h4>双列布局</h4>
                    <p>左右分栏显示内容</p>
                </div>
                <div class="layout-item" onclick="selectLayout('three-column')">
                    <h4>三列布局</h4>
                    <p>三栏并列展示</p>
                </div>
                <div class="layout-item" onclick="selectLayout('image-text')">
                    <h4>图文混排</h4>
                    <p>图片 + 文字说明</p>
                </div>
                <div class="layout-item" onclick="selectLayout('chart')">
                    <h4>图表展示</h4>
                    <p>数据图表 + 说明</p>
                </div>
                <div class="layout-item" onclick="selectLayout('timeline')">
                    <h4>时间轴</h4>
                    <p>流程或时间展示</p>
                </div>
            </div>
        </div>
        
        <!-- 当前幻灯片预览 -->
        <div class="section">
            <h3>👁️ 当前幻灯片预览</h3>
            <div class="slide-preview" id="slidePreview">
                <div class="slide-number" id="slideNumber">1/14</div>
                <div id="slideContent">
                    <h2 style="color: #1890ff; text-align: center; margin-bottom: 30px;">
                        iFlytek多模态智能面试评测系统
                    </h2>
                    <h3 style="color: #667eea; text-align: center; margin-bottom: 40px;">
                        基于科大讯飞星火大模型的高校学生面试训练平台
                    </h3>
                    <div style="text-align: center; margin-top: 50px;">
                        <p style="font-size: 18px; color: #666;">[团队名称]</p>
                        <p style="font-size: 16px; color: #999;">2025年7月</p>
                    </div>
                </div>
            </div>
            
            <button class="btn btn-primary" onclick="prevSlide()">⬅️ 上一页</button>
            <button class="btn btn-primary" onclick="nextSlide()">下一页 ➡️</button>
            <button class="btn" onclick="markSlideComplete()">✅ 标记完成</button>
        </div>
        
        <!-- 图标资源 -->
        <div class="section">
            <h3>🎯 推荐图标资源</h3>
            <div class="icon-grid">
                <div class="icon-item" onclick="openIconResource('ai')">
                    🤖<br>AI相关
                </div>
                <div class="icon-item" onclick="openIconResource('data')">
                    📊<br>数据分析
                </div>
                <div class="icon-item" onclick="openIconResource('iot')">
                    🌐<br>物联网
                </div>
                <div class="icon-item" onclick="openIconResource('voice')">
                    🎤<br>语音
                </div>
                <div class="icon-item" onclick="openIconResource('video')">
                    📹<br>视频
                </div>
                <div class="icon-item" onclick="openIconResource('text')">
                    📝<br>文本
                </div>
                <div class="icon-item" onclick="openIconResource('chart')">
                    📈<br>图表
                </div>
                <div class="icon-item" onclick="openIconResource('tech')">
                    ⚙️<br>技术
                </div>
            </div>
            
            <h4>推荐图标网站：</h4>
            <button class="btn" onclick="openUrl('https://www.iconfont.cn/')">Iconfont 阿里图标库</button>
            <button class="btn" onclick="openUrl('https://flaticon.com/')">Flaticon</button>
            <button class="btn" onclick="openUrl('https://icons8.com/')">Icons8</button>
        </div>
        
        <!-- 内容生成器 -->
        <div class="section">
            <h3>✍️ 内容生成器</h3>
            <label>选择幻灯片类型：</label>
            <select id="contentType" onchange="generateContent()">
                <option value="">请选择...</option>
                <option value="title">标题页</option>
                <option value="features">功能特性</option>
                <option value="architecture">系统架构</option>
                <option value="innovation">创新亮点</option>
                <option value="summary">总结页</option>
            </select>
            
            <div class="slide-content" id="generatedContent">
                选择幻灯片类型后，这里将显示生成的内容模板...
            </div>
            
            <button class="btn" onclick="copyContent()">📋 复制内容</button>
        </div>
        
        <!-- 导出和分享 -->
        <div class="section export-section">
            <h3>📤 导出和分享</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn" onclick="exportProgress()">📊 导出进度报告</button>
                <button class="btn" onclick="exportColorPalette()">🎨 导出色彩方案</button>
                <button class="btn" onclick="exportChecklist()">✅ 导出检查清单</button>
                <button class="btn" onclick="generatePPTGuide()">📖 生成制作指南</button>
            </div>
            
            <h4>💡 制作提示：</h4>
            <ul style="text-align: left; margin-top: 15px;">
                <li>保持字体一致性：标题用微软雅黑粗体，正文用微软雅黑常规</li>
                <li>使用统一的配色方案，主要使用iFlytek品牌色</li>
                <li>每页内容不要过多，保持简洁明了</li>
                <li>重要信息使用高亮颜色或加粗显示</li>
                <li>添加适当的动画效果，但不要过度使用</li>
            </ul>
        </div>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 14;
        let completedSlides = new Set();
        
        // 幻灯片内容模板
        const slideTemplates = {
            1: {
                title: "封面页",
                content: `iFlytek多模态智能面试评测系统

基于科大讯飞星火大模型的高校学生面试训练平台

[团队名称]
2025年7月`
            },
            2: {
                title: "项目背景与价值",
                content: `🎯 解决的核心问题
• 就业难题：高校毕业生人数逐年攀升
• 面试短板：缺乏实战经验、难以把握企业需求
• 技能鸿沟：课堂教学与职业实践之间的体验鸿沟

💡 创新价值
• AI赋能：利用iFlytek星火大模型提供智能化面试训练
• 多模态分析：语音+视频+文本全方位评估
• 个性化指导：针对性提升方案，缓解"求职焦虑"`
            },
            3: {
                title: "系统架构设计",
                content: `🏗️ 技术架构
前端层 (Vue.js 3 + Element Plus)
    ↓
API网关层 (FastAPI)
    ↓
AI服务层 (iFlytek Spark LLM)
    ↓
数据存储层 (SQLite + Redis)

🔧 核心技术栈
Vue.js 3 | Element Plus | ECharts
FastAPI | SQLAlchemy | iFlytek星火大模型`
            }
        };
        
        // 复制颜色代码
        function copyColor(color) {
            navigator.clipboard.writeText(color).then(() => {
                showMessage(`颜色代码 ${color} 已复制到剪贴板`);
            });
        }
        
        // 显示消息
        function showMessage(message) {
            const msg = document.createElement('div');
            msg.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #52c41a;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            msg.textContent = message;
            document.body.appendChild(msg);
            
            setTimeout(() => {
                msg.remove();
            }, 3000);
        }
        
        // 更新进度
        function updateProgress() {
            const progress = (completedSlides.size / totalSlides) * 100;
            document.getElementById('slideProgress').style.width = progress + '%';
            document.getElementById('progressText').textContent = 
                `已完成：${completedSlides.size}/${totalSlides} 页`;
        }
        
        // 标记幻灯片完成
        function markSlideComplete() {
            completedSlides.add(currentSlide);
            const listItem = document.querySelector(`[data-slide="${currentSlide}"]`);
            if (listItem) {
                listItem.classList.add('checked');
            }
            updateProgress();
            showMessage(`第${currentSlide}页已标记为完成`);
        }
        
        // 幻灯片导航
        function prevSlide() {
            if (currentSlide > 1) {
                currentSlide--;
                updateSlidePreview();
            }
        }
        
        function nextSlide() {
            if (currentSlide < totalSlides) {
                currentSlide++;
                updateSlidePreview();
            }
        }
        
        // 更新幻灯片预览
        function updateSlidePreview() {
            document.getElementById('slideNumber').textContent = `${currentSlide}/${totalSlides}`;
            
            const template = slideTemplates[currentSlide];
            if (template) {
                document.getElementById('slideContent').innerHTML = `
                    <h3 style="color: #1890ff; margin-bottom: 20px;">${template.title}</h3>
                    <div style="white-space: pre-wrap; line-height: 1.6;">${template.content}</div>
                `;
            } else {
                document.getElementById('slideContent').innerHTML = `
                    <h3 style="color: #1890ff;">第${currentSlide}页</h3>
                    <p>请参考制作指南完成此页内容</p>
                `;
            }
        }
        
        // 选择布局
        function selectLayout(layout) {
            showMessage(`已选择${layout}布局模板`);
        }
        
        // 打开图标资源
        function openIconResource(type) {
            const resources = {
                'ai': 'https://www.iconfont.cn/search/index?searchType=icon&q=AI',
                'data': 'https://www.iconfont.cn/search/index?searchType=icon&q=数据',
                'iot': 'https://www.iconfont.cn/search/index?searchType=icon&q=物联网',
                'voice': 'https://www.iconfont.cn/search/index?searchType=icon&q=语音',
                'video': 'https://www.iconfont.cn/search/index?searchType=icon&q=视频',
                'text': 'https://www.iconfont.cn/search/index?searchType=icon&q=文本',
                'chart': 'https://www.iconfont.cn/search/index?searchType=icon&q=图表',
                'tech': 'https://www.iconfont.cn/search/index?searchType=icon&q=技术'
            };
            
            if (resources[type]) {
                window.open(resources[type], '_blank');
            }
        }
        
        // 打开URL
        function openUrl(url) {
            window.open(url, '_blank');
        }
        
        // 生成内容
        function generateContent() {
            const type = document.getElementById('contentType').value;
            const templates = {
                'title': `标题：[您的标题]
副标题：[副标题说明]
背景：使用iFlytek品牌色渐变
字体：微软雅黑，标题48pt，副标题24pt`,
                
                'features': `功能特性展示模板：

✅ 核心功能1
• 详细说明1
• 详细说明2

✅ 核心功能2  
• 详细说明1
• 详细说明2

建议使用图标+文字的形式展示`,
                
                'architecture': `系统架构模板：

┌─────────────────┐
│     前端层      │
│   Vue.js 3      │
└─────────────────┘
         ↓
┌─────────────────┐
│   API网关层     │
│    FastAPI      │
└─────────────────┘
         ↓
┌─────────────────┐
│   AI服务层      │
│ iFlytek Spark   │
└─────────────────┘`,
                
                'innovation': `创新亮点模板：

🚀 技术创新
1. [创新点1]
2. [创新点2]
3. [创新点3]

💎 用户体验创新
1. [体验创新1]
2. [体验创新2]
3. [体验创新3]`,
                
                'summary': `总结页模板：

🎯 项目成果
✅ [成果1]
✅ [成果2]
✅ [成果3]

💡 核心优势
1. [优势1]
2. [优势2]
3. [优势3]`
            };
            
            const content = templates[type] || '请选择有效的幻灯片类型';
            document.getElementById('generatedContent').textContent = content;
        }
        
        // 复制内容
        function copyContent() {
            const content = document.getElementById('generatedContent').textContent;
            navigator.clipboard.writeText(content).then(() => {
                showMessage('内容已复制到剪贴板');
            });
        }
        
        // 导出功能
        function exportProgress() {
            const report = `iFlytek PPT制作进度报告
生成时间：${new Date().toLocaleString()}

总体进度：${completedSlides.size}/${totalSlides} (${Math.round(completedSlides.size/totalSlides*100)}%)

已完成页面：
${Array.from(completedSlides).map(slide => `- 第${slide}页`).join('\n')}

待完成页面：
${Array.from({length: totalSlides}, (_, i) => i + 1)
  .filter(slide => !completedSlides.has(slide))
  .map(slide => `- 第${slide}页`)
  .join('\n')}`;
            
            downloadText(report, 'ppt-progress-report.txt');
        }
        
        function exportColorPalette() {
            const palette = `iFlytek品牌色彩方案

主色调：
- iFlytek蓝：#1890ff
- 深蓝色：#0066cc
- 渐变蓝：#667eea

辅助色：
- 成功绿：#52c41a
- 警告橙：#faad14
- 错误红：#f5222d
- 中性灰：#8c8c8c

背景色：
- 主背景：#ffffff
- 次背景：#f5f5f5
- 深背景：#001529`;
            
            downloadText(palette, 'iflytek-color-palette.txt');
        }
        
        function exportChecklist() {
            const checklist = `iFlytek PPT制作检查清单

设计一致性：
☐ 所有页面使用统一的字体和配色
☐ 标题样式保持一致
☐ 图标风格统一
☐ 布局对齐规范

内容完整性：
☐ 14页内容全部制作完成
☐ 关键信息突出显示
☐ 数据准确无误
☐ 文字无错别字

视觉效果：
☐ 动画效果适度使用
☐ 图表清晰易读
☐ 色彩搭配和谐
☐ 整体风格专业

技术要求：
☐ 文件格式为.pptx
☐ 文件大小控制在50MB以内
☐ 字体嵌入或使用系统字体
☐ 图片分辨率适中`;
            
            downloadText(checklist, 'ppt-creation-checklist.txt');
        }
        
        function generatePPTGuide() {
            window.open('../docs/ppt-creation-guide.md', '_blank');
        }
        
        // 下载文本文件
        function downloadText(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
            showMessage(`${filename} 已下载`);
        }
        
        // 清单点击事件
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'LI' && e.target.closest('#slideChecklist')) {
                const slideNum = parseInt(e.target.dataset.slide);
                if (e.target.classList.contains('checked')) {
                    e.target.classList.remove('checked');
                    completedSlides.delete(slideNum);
                } else {
                    e.target.classList.add('checked');
                    completedSlides.add(slideNum);
                }
                updateProgress();
            }
        });
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateSlidePreview();
            updateProgress();
            console.log('🎨 PPT制作助手已加载');
        });
    </script>
</body>
</html>
