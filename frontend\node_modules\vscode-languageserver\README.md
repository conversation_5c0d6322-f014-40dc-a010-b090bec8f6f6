# VSCode Language Server

[![NPM Version](https://img.shields.io/npm/v/vscode-languageserver.svg)](https://npmjs.org/package/vscode-languageserver)
[![NPM Downloads](https://img.shields.io/npm/dm/vscode-languageserver.svg)](https://npmjs.org/package/vscode-languageserver)
[![Build Status](https://travis-ci.org/Microsoft/vscode-languageserver-node.svg?branch=master)](https://travis-ci.org/Microsoft/vscode-languageserver-node)

Npm module to implement a VSCode language server using [Node.js](https://nodejs.org/) as a runtime.

Click [here](https://code.visualstudio.com/docs/extensions/example-language-server) for a detailed document on how to use this npm module
to implement language servers for [VSCode](https://code.visualstudio.com/).

## History

For the history please see the [main repository](https://github.com/Microsoft/vscode-languageserver-node/blob/master/README.md)

## License
[MIT](https://github.com/Microsoft/vscode-languageserver-node/blob/master/License.txt)