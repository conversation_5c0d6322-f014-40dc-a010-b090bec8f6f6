{"version": 3, "sources": ["../../../src/diagrams/state/parser/stateDiagram.jison", "../../../src/diagrams/state/stateCommon.ts", "../../../src/diagrams/state/stateRenderer-v3-unified.ts", "../../../src/diagrams/state/dataFetcher.ts", "../../../src/diagrams/state/stateDb.ts", "../../../src/diagrams/state/styles.js"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,2],$V1=[1,3],$V2=[1,4],$V3=[2,4],$V4=[1,9],$V5=[1,11],$V6=[1,16],$V7=[1,17],$V8=[1,18],$V9=[1,19],$Va=[1,33],$Vb=[1,20],$Vc=[1,21],$Vd=[1,22],$Ve=[1,23],$Vf=[1,24],$Vg=[1,26],$Vh=[1,27],$Vi=[1,28],$Vj=[1,29],$Vk=[1,30],$Vl=[1,31],$Vm=[1,32],$Vn=[1,35],$Vo=[1,36],$Vp=[1,37],$Vq=[1,38],$Vr=[1,34],$Vs=[1,4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],$Vt=[1,4,5,14,15,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,39,40,41,45,48,51,52,53,54,57],$Vu=[4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"SPACE\":4,\"NL\":5,\"SD\":6,\"document\":7,\"line\":8,\"statement\":9,\"classDefStatement\":10,\"styleStatement\":11,\"cssClassStatement\":12,\"idStatement\":13,\"DESCR\":14,\"-->\":15,\"HIDE_EMPTY\":16,\"scale\":17,\"WIDTH\":18,\"COMPOSIT_STATE\":19,\"STRUCT_START\":20,\"STRUCT_STOP\":21,\"STATE_DESCR\":22,\"AS\":23,\"ID\":24,\"FORK\":25,\"JOIN\":26,\"CHOICE\":27,\"CONCURRENT\":28,\"note\":29,\"notePosition\":30,\"NOTE_TEXT\":31,\"direction\":32,\"acc_title\":33,\"acc_title_value\":34,\"acc_descr\":35,\"acc_descr_value\":36,\"acc_descr_multiline_value\":37,\"CLICK\":38,\"STRING\":39,\"HREF\":40,\"classDef\":41,\"CLASSDEF_ID\":42,\"CLASSDEF_STYLEOPTS\":43,\"DEFAULT\":44,\"style\":45,\"STYLE_IDS\":46,\"STYLEDEF_STYLEOPTS\":47,\"class\":48,\"CLASSENTITY_IDS\":49,\"STYLECLASS\":50,\"direction_tb\":51,\"direction_bt\":52,\"direction_rl\":53,\"direction_lr\":54,\"eol\":55,\";\":56,\"EDGE_STATE\":57,\"STYLE_SEPARATOR\":58,\"left_of\":59,\"right_of\":60,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SPACE\",5:\"NL\",6:\"SD\",14:\"DESCR\",15:\"-->\",16:\"HIDE_EMPTY\",17:\"scale\",18:\"WIDTH\",19:\"COMPOSIT_STATE\",20:\"STRUCT_START\",21:\"STRUCT_STOP\",22:\"STATE_DESCR\",23:\"AS\",24:\"ID\",25:\"FORK\",26:\"JOIN\",27:\"CHOICE\",28:\"CONCURRENT\",29:\"note\",31:\"NOTE_TEXT\",33:\"acc_title\",34:\"acc_title_value\",35:\"acc_descr\",36:\"acc_descr_value\",37:\"acc_descr_multiline_value\",38:\"CLICK\",39:\"STRING\",40:\"HREF\",41:\"classDef\",42:\"CLASSDEF_ID\",43:\"CLASSDEF_STYLEOPTS\",44:\"DEFAULT\",45:\"style\",46:\"STYLE_IDS\",47:\"STYLEDEF_STYLEOPTS\",48:\"class\",49:\"CLASSENTITY_IDS\",50:\"STYLECLASS\",51:\"direction_tb\",52:\"direction_bt\",53:\"direction_rl\",54:\"direction_lr\",56:\";\",57:\"EDGE_STATE\",58:\"STYLE_SEPARATOR\",59:\"left_of\",60:\"right_of\"},\nproductions_: [0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,3],[9,4],[9,1],[9,2],[9,1],[9,4],[9,3],[9,6],[9,1],[9,1],[9,1],[9,1],[9,4],[9,4],[9,1],[9,2],[9,2],[9,1],[9,5],[9,5],[10,3],[10,3],[11,3],[12,3],[32,1],[32,1],[32,1],[32,1],[55,1],[55,1],[13,1],[13,1],[13,3],[13,3],[30,1],[30,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 3:\n /* console.log('--> Root document', $$[$0]); */   yy.setRootDoc($$[$0]); return $$[$0]; \nbreak;\ncase 4:\n /*console.log('empty document'); */ this.$ = [] \nbreak;\ncase 5:\n\n        if($$[$0] !='nl'){\n            /* console.log(' document: 1: ', $$[$0-1], ' pushing 2: ', $$[$0]); */\n            $$[$0-1].push($$[$0]); this.$ = $$[$0-1]\n        }\n        /* console.log('Got document',$$[$0-1], $$[$0]); */\n    \nbreak;\ncase 6: case 7:\n this.$ = $$[$0] \nbreak;\ncase 8:\n this.$='nl';\nbreak;\ncase 12:\n /* console.log('got id', $$[$0]); */\n            this.$=$$[$0];\n\t    \nbreak;\ncase 13:\n\n            const stateStmt = $$[$0-1];\n            stateStmt.description = yy.trimColon($$[$0]);\n            this.$ = stateStmt;\n\t    \nbreak;\ncase 14:\n\n            /* console.info('got ids: 1: ', $$[$0-2], ' 2:', $$[$0-1],'  3: ', $$[$0]); */\n            // console.log(' idStatement --> idStatement : state1 =', $$[$0-2], ' state2 =', $$[$0]);\n            this.$={ stmt: 'relation', state1: $$[$0-2], state2: $$[$0]};\n        \nbreak;\ncase 15:\n\n            const relDescription = yy.trimColon($$[$0]);\n            /* console.log(' idStatement --> idStatement DESCR : state1 =', $$[$0-3], ' state2stmt =', $$[$0-1], '  description: ', relDescription); */\n            this.$={ stmt: 'relation', state1: $$[$0-3], state2: $$[$0-1], description: relDescription};\n        \nbreak;\ncase 19:\n\n        // console.log('Adding document for state without id ', $$[$0-3]);\n        this.$={ stmt: 'state', id: $$[$0-3], type: 'default', description: '', doc: $$[$0-1] }\n    \nbreak;\ncase 20:\n\n        var id=$$[$0];\n        var description = $$[$0-2].trim();\n        if($$[$0].match(':')){\n            var parts = $$[$0].split(':');\n            id=parts[0];\n            description = [description, parts[1]];\n        }\n        this.$={stmt: 'state', id: id, type: 'default', description: description};\n\n    \nbreak;\ncase 21:\n\n         // console.log('state with id ', $$[$0-3],' document = ', $$[$0-1], );\n         this.$={ stmt: 'state', id: $$[$0-3], type: 'default', description: $$[$0-5], doc: $$[$0-1] }\n    \nbreak;\ncase 22:\n\n        this.$={ stmt: 'state', id: $$[$0], type: 'fork' }\n    \nbreak;\ncase 23:\n\n        this.$={ stmt: 'state', id: $$[$0], type: 'join' }\n    \nbreak;\ncase 24:\n\n        this.$={ stmt: 'state', id: $$[$0], type: 'choice' }\n    \nbreak;\ncase 25:\n\n        this.$={ stmt: 'state', id: yy.getDividerId(), type: 'divider' }\n    \nbreak;\ncase 26:\n\n        /* console.warn('got NOTE, position: ', $$[$0-2].trim(), 'id = ', $$[$0-1].trim(), 'note: ', $$[$0]);*/\n        this.$={ stmt: 'state', id: $$[$0-1].trim(), note:{position: $$[$0-2].trim(), text: $$[$0].trim()}};\n    \nbreak;\ncase 29:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 30: case 31:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 32:\n\n        this.$ = {\n            stmt: \"click\",\n            id: $$[$0-3],\n            url: $$[$0-2],\n            tooltip: $$[$0-1]\n        };\n    \nbreak;\ncase 33:\n\n        this.$ = {\n            stmt: \"click\",\n            id: $$[$0-3],\n            url: $$[$0-1],\n            tooltip: \"\"\n        };\n    \nbreak;\ncase 34: case 35:\n\n        this.$ = { stmt: 'classDef', id: $$[$0-1].trim(), classes: $$[$0].trim() };\n        \nbreak;\ncase 36:\n\n        this.$ = { stmt: 'style', id: $$[$0-1].trim(), styleClass: $$[$0].trim() };\n        \nbreak;\ncase 37:\n\n        //console.log('apply class: id(s): ',$$[$0-1], '  style class: ', $$[$0]);\n        this.$={ stmt: 'applyClass', id: $$[$0-1].trim(), styleClass: $$[$0].trim() };\n        \nbreak;\ncase 38:\n yy.setDirection('TB');this.$={stmt:'dir', value:'TB'};\nbreak;\ncase 39:\n yy.setDirection('BT');this.$={stmt:'dir', value:'BT'};\nbreak;\ncase 40:\n yy.setDirection('RL'); this.$={stmt:'dir', value:'RL'};\nbreak;\ncase 41:\n yy.setDirection('LR');this.$={stmt:'dir', value:'LR'};\nbreak;\ncase 44: case 45:\n   /* console.log('idStatement id: ', $$[$0]); */\n            this.$={ stmt: 'state', id: $$[$0].trim(), type: 'default', description: '' };\n        \nbreak;\ncase 46:\n   /*console.log('idStatement ID STYLE_SEPARATOR ID'); */\n            this.$={ stmt: 'state', id: $$[$0-2].trim(), classes: [$$[$0].trim()], type: 'default', description: '' };\n        \nbreak;\ncase 47:\n   /*console.log('idStatement EDGE_STATE STYLE_SEPARATOR ID'); */\n            this.$={ stmt: 'state', id: $$[$0-2].trim(), classes: [$$[$0].trim()], type: 'default', description: '' };\n        \nbreak;\n}\n},\ntable: [{3:1,4:$V0,5:$V1,6:$V2},{1:[3]},{3:5,4:$V0,5:$V1,6:$V2},{3:6,4:$V0,5:$V1,6:$V2},o([1,4,5,16,17,19,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],$V3,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:$V4,5:$V5,8:8,9:10,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,5]),{9:39,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,7]),o($Vs,[2,8]),o($Vs,[2,9]),o($Vs,[2,10]),o($Vs,[2,11]),o($Vs,[2,12],{14:[1,40],15:[1,41]}),o($Vs,[2,16]),{18:[1,42]},o($Vs,[2,18],{20:[1,43]}),{23:[1,44]},o($Vs,[2,22]),o($Vs,[2,23]),o($Vs,[2,24]),o($Vs,[2,25]),{30:45,31:[1,46],59:[1,47],60:[1,48]},o($Vs,[2,28]),{34:[1,49]},{36:[1,50]},o($Vs,[2,31]),{13:51,24:$Va,57:$Vr},{42:[1,52],44:[1,53]},{46:[1,54]},{49:[1,55]},o($Vt,[2,44],{58:[1,56]}),o($Vt,[2,45],{58:[1,57]}),o($Vs,[2,38]),o($Vs,[2,39]),o($Vs,[2,40]),o($Vs,[2,41]),o($Vs,[2,6]),o($Vs,[2,13]),{13:58,24:$Va,57:$Vr},o($Vs,[2,17]),o($Vu,$V3,{7:59}),{24:[1,60]},{24:[1,61]},{23:[1,62]},{24:[2,48]},{24:[2,49]},o($Vs,[2,29]),o($Vs,[2,30]),{39:[1,63],40:[1,64]},{43:[1,65]},{43:[1,66]},{47:[1,67]},{50:[1,68]},{24:[1,69]},{24:[1,70]},o($Vs,[2,14],{14:[1,71]}),{4:$V4,5:$V5,8:8,9:10,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,21:[1,72],22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,20],{20:[1,73]}),{31:[1,74]},{24:[1,75]},{39:[1,76]},{39:[1,77]},o($Vs,[2,34]),o($Vs,[2,35]),o($Vs,[2,36]),o($Vs,[2,37]),o($Vt,[2,46]),o($Vt,[2,47]),o($Vs,[2,15]),o($Vs,[2,19]),o($Vu,$V3,{7:78}),o($Vs,[2,26]),o($Vs,[2,27]),{5:[1,79]},{5:[1,80]},{4:$V4,5:$V5,8:8,9:10,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,21:[1,81],22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,32]),o($Vs,[2,33]),o($Vs,[2,21])],\ndefaultActions: {5:[2,1],6:[2,2],47:[2,48],48:[2,49]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:return 38;\nbreak;\ncase 1:return 40;\nbreak;\ncase 2:return 39;   \nbreak;\ncase 3:return 44;\nbreak;\ncase 4:return 51;\nbreak;\ncase 5:return 52;\nbreak;\ncase 6:return 53;\nbreak;\ncase 7:return 54;\nbreak;\ncase 8:/* skip comments */\nbreak;\ncase 9:/* skip comments */{ /*console.log('Crap after close');*/ }\nbreak;\ncase 10:return 5;\nbreak;\ncase 11:/* skip all whitespace */\nbreak;\ncase 12:/* skip same-line whitespace */\nbreak;\ncase 13:/* skip comments */\nbreak;\ncase 14:/* skip comments */\nbreak;\ncase 15: this.pushState('SCALE'); /* console.log('Got scale', yy_.yytext);*/ return 17; \nbreak;\ncase 16:return 18;\nbreak;\ncase 17: this.popState(); \nbreak;\ncase 18: this.begin(\"acc_title\");return 33; \nbreak;\ncase 19: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 20: this.begin(\"acc_descr\");return 35; \nbreak;\ncase 21: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 22: this.begin(\"acc_descr_multiline\"); \nbreak;\ncase 23: this.popState(); \nbreak;\ncase 24:return \"acc_descr_multiline_value\";\nbreak;\ncase 25: this.pushState('CLASSDEF'); return 41; \nbreak;\ncase 26: this.popState(); this.pushState('CLASSDEFID'); return 'DEFAULT_CLASSDEF_ID' \nbreak;\ncase 27: this.popState(); this.pushState('CLASSDEFID'); return 42 \nbreak;\ncase 28: this.popState(); return 43 \nbreak;\ncase 29: this.pushState('CLASS'); return 48; \nbreak;\ncase 30: this.popState(); this.pushState('CLASS_STYLE'); return 49 \nbreak;\ncase 31: this.popState(); return 50 \nbreak;\ncase 32: this.pushState('STYLE'); return 45; \nbreak;\ncase 33: this.popState(); this.pushState('STYLEDEF_STYLES'); return 46 \nbreak;\ncase 34: this.popState(); return 47 \nbreak;\ncase 35: this.pushState('SCALE'); /* console.log('Got scale', yy_.yytext);*/ return 17; \nbreak;\ncase 36:return 18;\nbreak;\ncase 37:this.popState();\nbreak;\ncase 38: /* console.log('Starting STATE '); */ this.pushState('STATE'); \nbreak;\ncase 39:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim(); /*console.warn('Fork Fork: ',yy_.yytext);*/return 25;\nbreak;\ncase 40:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 26;\nbreak;\ncase 41:this.popState();yy_.yytext=yy_.yytext.slice(0,-10).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 27;\nbreak;\ncase 42:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim();/*console.warn('Fork Fork: ',yy_.yytext);*/return 25;\nbreak;\ncase 43:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 26;\nbreak;\ncase 44:this.popState();yy_.yytext=yy_.yytext.slice(0,-10).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 27;\nbreak;\ncase 45: return 51;\nbreak;\ncase 46: return 52;\nbreak;\ncase 47: return 53;\nbreak;\ncase 48: return 54;\nbreak;\ncase 49: /* console.log('Starting STATE_STRING'); */ this.pushState(\"STATE_STRING\"); \nbreak;\ncase 50: this.pushState('STATE_ID'); /* console.log('pushState(STATE_ID)'); */ return \"AS\"; \nbreak;\ncase 51: this.popState(); /* console.log('STATE_ID', yy_.yytext); */ return \"ID\"; \nbreak;\ncase 52: this.popState(); \nbreak;\ncase 53: /* console.log('Long description:', yy_.yytext); */ return \"STATE_DESCR\"; \nbreak;\ncase 54: /* console.log('COMPOSIT_STATE', yy_.yytext); */ return 19; \nbreak;\ncase 55: this.popState(); \nbreak;\ncase 56: this.popState(); this.pushState('struct'); /* console.log('begin struct', yy_.yytext); */ return 20; \nbreak;\ncase 57:/* skip comments inside state*/\nbreak;\ncase 58: /*console.log('Ending struct');*/ this.popState(); return 21;\nbreak;\ncase 59:/* nothing */\nbreak;\ncase 60: this.begin('NOTE'); return 29; \nbreak;\ncase 61: this.popState(); this.pushState('NOTE_ID'); return 59; \nbreak;\ncase 62: this.popState(); this.pushState('NOTE_ID'); return 60; \nbreak;\ncase 63: this.popState(); this.pushState('FLOATING_NOTE'); \nbreak;\ncase 64: this.popState(); this.pushState('FLOATING_NOTE_ID'); return \"AS\"; \nbreak;\ncase 65:/**/\nbreak;\ncase 66: /* console.log('Floating note text: ', yy_.yytext); */ return \"NOTE_TEXT\"; \nbreak;\ncase 67: this.popState(); /* console.log('Floating note ID', yy_.yytext);*/ return \"ID\"; \nbreak;\ncase 68: this.popState(); this.pushState('NOTE_TEXT'); /*console.log('Got ID for note', yy_.yytext);*/ return 24; \nbreak;\ncase 69: this.popState(); /* console.log('Got NOTE_TEXT for note',yy_.yytext);*/yy_.yytext = yy_.yytext.substr(2).trim(); return 31; \nbreak;\ncase 70: this.popState(); /* console.log('Got NOTE_TEXT for note',yy_.yytext);*/yy_.yytext = yy_.yytext.slice(0,-8).trim(); return 31; \nbreak;\ncase 71: /* console.log('Got state diagram', yy_.yytext,'#'); */ return 6; \nbreak;\ncase 72: /* console.log('Got state diagram', yy_.yytext,'#'); */ return 6; \nbreak;\ncase 73: /* console.log('HIDE_EMPTY', yy_.yytext,'#'); */ return 16; \nbreak;\ncase 74: /* console.log('EDGE_STATE=',yy_.yytext); */ return 57; \nbreak;\ncase 75: /* console.log('=>ID=',yy_.yytext); */ return 24; \nbreak;\ncase 76: yy_.yytext = yy_.yytext.trim(); /* console.log('Descr = ', yy_.yytext); */ return 14; \nbreak;\ncase 77:return 15;\nbreak;\ncase 78:return 28;\nbreak;\ncase 79:return 58;\nbreak;\ncase 80:return 5;\nbreak;\ncase 81:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:click\\b)/i,/^(?:href\\b)/i,/^(?:\"[^\"]*\")/i,/^(?:default\\b)/i,/^(?:.*direction\\s+TB[^\\n]*)/i,/^(?:.*direction\\s+BT[^\\n]*)/i,/^(?:.*direction\\s+RL[^\\n]*)/i,/^(?:.*direction\\s+LR[^\\n]*)/i,/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n]+)/i,/^(?:[\\s]+)/i,/^(?:((?!\\n)\\s)+)/i,/^(?:#[^\\n]*)/i,/^(?:%[^\\n]*)/i,/^(?:scale\\s+)/i,/^(?:\\d+)/i,/^(?:\\s+width\\b)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:classDef\\s+)/i,/^(?:DEFAULT\\s+)/i,/^(?:\\w+\\s+)/i,/^(?:[^\\n]*)/i,/^(?:class\\s+)/i,/^(?:(\\w+)+((,\\s*\\w+)*))/i,/^(?:[^\\n]*)/i,/^(?:style\\s+)/i,/^(?:[\\w,]+\\s+)/i,/^(?:[^\\n]*)/i,/^(?:scale\\s+)/i,/^(?:\\d+)/i,/^(?:\\s+width\\b)/i,/^(?:state\\s+)/i,/^(?:.*<<fork>>)/i,/^(?:.*<<join>>)/i,/^(?:.*<<choice>>)/i,/^(?:.*\\[\\[fork\\]\\])/i,/^(?:.*\\[\\[join\\]\\])/i,/^(?:.*\\[\\[choice\\]\\])/i,/^(?:.*direction\\s+TB[^\\n]*)/i,/^(?:.*direction\\s+BT[^\\n]*)/i,/^(?:.*direction\\s+RL[^\\n]*)/i,/^(?:.*direction\\s+LR[^\\n]*)/i,/^(?:[\"])/i,/^(?:\\s*as\\s+)/i,/^(?:[^\\n\\{]*)/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:[^\\n\\s\\{]+)/i,/^(?:\\n)/i,/^(?:\\{)/i,/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:\\})/i,/^(?:[\\n])/i,/^(?:note\\s+)/i,/^(?:left of\\b)/i,/^(?:right of\\b)/i,/^(?:\")/i,/^(?:\\s*as\\s*)/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:[^\\n]*)/i,/^(?:\\s*[^:\\n\\s\\-]+)/i,/^(?:\\s*:[^:\\n;]+)/i,/^(?:[\\s\\S]*?end note\\b)/i,/^(?:stateDiagram\\s+)/i,/^(?:stateDiagram-v2\\s+)/i,/^(?:hide empty description\\b)/i,/^(?:\\[\\*\\])/i,/^(?:[^:\\n\\s\\-\\{]+)/i,/^(?:\\s*:[^:\\n;]+)/i,/^(?:-->)/i,/^(?:--)/i,/^(?::::)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"LINE\":{\"rules\":[12,13],\"inclusive\":false},\"struct\":{\"rules\":[12,13,25,29,32,38,45,46,47,48,57,58,59,60,74,75,76,77,78],\"inclusive\":false},\"FLOATING_NOTE_ID\":{\"rules\":[67],\"inclusive\":false},\"FLOATING_NOTE\":{\"rules\":[64,65,66],\"inclusive\":false},\"NOTE_TEXT\":{\"rules\":[69,70],\"inclusive\":false},\"NOTE_ID\":{\"rules\":[68],\"inclusive\":false},\"NOTE\":{\"rules\":[61,62,63],\"inclusive\":false},\"STYLEDEF_STYLEOPTS\":{\"rules\":[],\"inclusive\":false},\"STYLEDEF_STYLES\":{\"rules\":[34],\"inclusive\":false},\"STYLE_IDS\":{\"rules\":[],\"inclusive\":false},\"STYLE\":{\"rules\":[33],\"inclusive\":false},\"CLASS_STYLE\":{\"rules\":[31],\"inclusive\":false},\"CLASS\":{\"rules\":[30],\"inclusive\":false},\"CLASSDEFID\":{\"rules\":[28],\"inclusive\":false},\"CLASSDEF\":{\"rules\":[26,27],\"inclusive\":false},\"acc_descr_multiline\":{\"rules\":[23,24],\"inclusive\":false},\"acc_descr\":{\"rules\":[21],\"inclusive\":false},\"acc_title\":{\"rules\":[19],\"inclusive\":false},\"SCALE\":{\"rules\":[16,17,36,37],\"inclusive\":false},\"ALIAS\":{\"rules\":[],\"inclusive\":false},\"STATE_ID\":{\"rules\":[51],\"inclusive\":false},\"STATE_STRING\":{\"rules\":[52,53],\"inclusive\":false},\"FORK_STATE\":{\"rules\":[],\"inclusive\":false},\"STATE\":{\"rules\":[12,13,39,40,41,42,43,44,49,50,54,55,56],\"inclusive\":false},\"ID\":{\"rules\":[12,13],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,8,9,10,11,13,14,15,18,20,22,25,29,32,35,38,56,60,71,72,73,74,75,76,77,79,80,81],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "/**\n * Constants common to all State Diagram code\n */\n\n// default diagram direction\nexport const DEFAULT_DIAGRAM_DIRECTION = 'TB';\n\n// default direction for any nested documents (composites)\nexport const DEFAULT_NESTED_DOC_DIR = 'TB';\n\n// parsed statement type for a direction\nexport const STMT_DIRECTION = 'dir';\n\n// parsed statement type for a state\nexport const STMT_STATE = 'state';\n\n// parsed statement type for a root\nexport const STMT_ROOT = 'root';\n\n// parsed statement type for a relation\nexport const STMT_RELATION = 'relation';\n// parsed statement type for a classDef\nexport const STMT_CLASSDEF = 'classDef';\nexport const STMT_STYLEDEF = 'style';\n// parsed statement type for applyClass\nexport const STMT_APPLYCLASS = 'applyClass';\n\nexport const DEFAULT_STATE_TYPE = 'default';\nexport const DIVIDER_TYPE = 'divider';\n\n// Graph edge settings\nexport const G_EDGE_STYLE = 'fill:none';\nexport const G_EDGE_ARROWHEADSTYLE = 'fill: #333';\nexport const G_EDGE_LABELPOS = 'c';\nexport const G_EDGE_LABELTYPE = 'text';\nexport const G_EDGE_THICKNESS = 'normal';\n\nexport const SHAPE_STATE = 'rect';\nexport const SHAPE_STATE_WITH_DESC = 'rectWithTitle';\nexport const SHAPE_START = 'stateStart';\nexport const SHAPE_END = 'stateEnd';\nexport const SHAPE_DIVIDER = 'divider';\nexport const SHAPE_GROUP = 'roundedWithTitle';\nexport const SHAPE_NOTE = 'note';\nexport const SHAPE_NOTEGROUP = 'noteGroup';\n\n// CSS classes\nexport const CSS_DIAGRAM = 'statediagram';\nexport const CSS_STATE = 'state';\nexport const CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nexport const CSS_EDGE = 'transition';\nexport const CSS_NOTE = 'note';\nexport const CSS_NOTE_EDGE = 'note-edge';\nexport const CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nexport const CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nexport const CSS_CLUSTER = 'cluster';\nexport const CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nexport const CSS_CLUSTER_ALT = 'cluster-alt';\nexport const CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\n\nexport const PARENT = 'parent';\nexport const NOTE = 'note';\nexport const DOMID_STATE = 'state';\nexport const DOMID_TYPE_SPACER = '----';\nexport const NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nexport const PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n// --------------------------------------\n\nexport default {\n  DEFAULT_DIAGRAM_DIRECTION,\n  DEFAULT_NESTED_DOC_DIR,\n  STMT_STATE,\n  STMT_RELATION,\n  STMT_CLASSDEF,\n  STMT_STYLEDEF,\n  STMT_APPLYCLASS,\n  DEFAULT_STATE_TYPE,\n  DIVIDER_TYPE,\n  G_EDGE_STYLE,\n  G_EDGE_ARROWHEADSTYLE,\n  G_EDGE_LABELPOS,\n  G_EDGE_LABELTYPE,\n  G_EDGE_THICKNESS,\n  CSS_EDGE,\n  CSS_DIAGRAM,\n  SHAPE_STATE,\n  SHAPE_STATE_WITH_DESC,\n  SHAPE_START,\n  SHAPE_END,\n  SHAPE_DIVIDER,\n  SHAPE_GROUP,\n  SHAPE_NOTE,\n  SHAPE_NOTEGROUP,\n  CSS_STATE,\n  CSS_DIAGRAM_STATE,\n  CSS_NOTE,\n  CSS_NOTE_EDGE,\n  CSS_EDGE_NOTE_EDGE,\n  CSS_DIAGRAM_NOTE,\n  CSS_CLUSTER,\n  CSS_DIAGRAM_CLUSTER,\n  CSS_CLUSTER_ALT,\n  CSS_DIAGRAM_CLUSTER_ALT,\n  PARENT,\n  NOTE,\n  DOMID_STATE,\n  DOMID_TYPE_SPACER,\n  NOTE_ID,\n  PARENT_ID,\n};\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DiagramStyleClassDef } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { getDiagramElement } from '../../rendering-util/insertElementsForSize.js';\nimport { render } from '../../rendering-util/render.js';\nimport { setupViewPortForSVG } from '../../rendering-util/setupViewPortForSVG.js';\nimport type { LayoutData } from '../../rendering-util/types.js';\nimport utils from '../../utils.js';\nimport { CSS_DIAGRAM, DEFAULT_NESTED_DOC_DIR } from './stateCommon.js';\n\n/**\n * Get the direction from the statement items.\n * Look through all of the documents (docs) in the parsedItems\n * Because is a _document_ direction, the default direction is not necessarily the same as the overall default _diagram_ direction.\n * @param parsedItem - the parsed statement item to look through\n * @param defaultDir - the direction to use if none is found\n * @returns The direction to use\n */\nexport const getDir = (parsedItem: any, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n\n  let dir = defaultDir;\n\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === 'dir') {\n      dir = parsedItemDoc.value;\n    }\n  }\n\n  return dir;\n};\n\nexport const getClasses = function (\n  text: string,\n  diagramObj: any\n): Map<string, DiagramStyleClassDef> {\n  return diagramObj.db.getClasses();\n};\n\nexport const draw = async function (text: string, id: string, _version: string, diag: any) {\n  log.info('REF0:');\n  log.info('Drawing state diagram (v2)', id);\n  const { securityLevel, state: conf, layout } = getConfig();\n  // Extracting the data from the parsed structure into a more usable form\n  // Not related to the refactoring, but this is the first step in the rendering process\n  diag.db.extract(diag.db.getRootDocV2());\n\n  //const DIR = getDir(diag.db.getRootDocV2());\n\n  // The getData method provided in all supported diagrams is used to extract the data from the parsed structure\n  // into the Layout data format\n  const data4Layout = diag.db.getData() as LayoutData;\n\n  // Create the root SVG - the element is the div containing the SVG element\n  const svg = getDiagramElement(id, securityLevel);\n\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n\n  // TODO: Should we move these two to baseConfig? These types are not there in StateConfig.\n\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = ['barb'];\n  data4Layout.diagramId = id;\n  // console.log('REF1:', data4Layout);\n  await render(data4Layout, svg);\n  const padding = 8;\n\n  // Inject clickable links after nodes are rendered\n  try {\n    const links: Map<string, { url: string; tooltip: string }> =\n      typeof diag.db.getLinks === 'function' ? diag.db.getLinks() : new Map();\n\n    type StateKey = string | { id: string };\n\n    links.forEach((linkInfo, key: StateKey) => {\n      const stateId = typeof key === 'string' ? key : typeof key?.id === 'string' ? key.id : '';\n\n      if (!stateId) {\n        log.warn('⚠️ Invalid or missing stateId from key:', JSON.stringify(key));\n        return;\n      }\n\n      const allNodes = svg.node()?.querySelectorAll('g');\n      let matchedElem: SVGGElement | undefined;\n\n      allNodes?.forEach((g: SVGGElement) => {\n        const text = g.textContent?.trim();\n        if (text === stateId) {\n          matchedElem = g;\n        }\n      });\n\n      if (!matchedElem) {\n        log.warn('⚠️ Could not find node matching text:', stateId);\n        return;\n      }\n\n      const parent = matchedElem.parentNode;\n      if (!parent) {\n        log.warn('⚠️ Node has no parent, cannot wrap:', stateId);\n        return;\n      }\n\n      const a = document.createElementNS('http://www.w3.org/2000/svg', 'a');\n      const cleanedUrl = linkInfo.url.replace(/^\"+|\"+$/g, ''); // remove leading/trailing quotes\n      a.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', cleanedUrl);\n      a.setAttribute('target', '_blank');\n      if (linkInfo.tooltip) {\n        const tooltip = linkInfo.tooltip.replace(/^\"+|\"+$/g, '');\n        a.setAttribute('title', tooltip);\n      }\n\n      parent.replaceChild(a, matchedElem);\n      a.appendChild(matchedElem);\n\n      log.info('🔗 Wrapped node in <a> tag for:', stateId, linkInfo.url);\n    });\n  } catch (err) {\n    log.error('❌ Error injecting clickable links:', err);\n  }\n\n  utils.insertTitle(\n    svg,\n    'statediagramTitleText',\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n};\n\nexport default {\n  getClasses,\n  draw,\n  getDir,\n};\n", "import type { MermaidConfig } from '../../config.type.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport common from '../common/common.js';\nimport {\n  CSS_DIAGRAM_CLUSTER,\n  CSS_DIAGRAM_CLUSTER_ALT,\n  CSS_DIAGRAM_NOTE,\n  CSS_DIAGRAM_STATE,\n  CSS_EDGE,\n  CSS_EDGE_NOTE_EDGE,\n  DEFAULT_NESTED_DOC_DIR,\n  DEFAULT_STATE_TYPE,\n  DIVIDER_TYPE,\n  DOMID_STATE,\n  DOMID_TYPE_SPACER,\n  G_EDGE_ARROWHEADSTYLE,\n  G_EDGE_LABELPOS,\n  G_EDGE_LABELTYPE,\n  G_EDGE_STYLE,\n  G_EDGE_THICKNESS,\n  NOTE,\n  NOTE_ID,\n  PARENT,\n  PARENT_ID,\n  SHAPE_DIVIDER,\n  SHAPE_END,\n  SHAPE_GROUP,\n  SHAPE_NOTE,\n  SHAPE_NOTEGROUP,\n  SHAPE_START,\n  SHAPE_STATE,\n  SHAPE_STATE_WITH_DESC,\n  STMT_RELATION,\n  STMT_STATE,\n} from './stateCommon.js';\nimport type { Edge, NodeData, StateStmt, Stmt, StyleClass } from './stateDb.js';\n\n// List of nodes created from the parsed diagram statement items\nconst nodeDb = new Map<string, NodeData>();\n\nlet graphItemCount = 0; // used to construct ids, etc.\n\n/**\n * Create a standard string for the dom ID of an item.\n * If a type is given, insert that before the counter, preceded by the type spacer\n *\n */\nexport function stateDomId(\n  itemId = '',\n  counter = 0,\n  type: string | null = '',\n  typeSpacer = DOMID_TYPE_SPACER\n) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : '';\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n\nconst setupDoc = (\n  parentParsedItem: StateStmt | undefined,\n  doc: Stmt[],\n  diagramStates: Map<string, StateStmt>,\n  nodes: NodeData[],\n  edges: Edge[],\n  altFlag: boolean,\n  look: MermaidConfig['look'],\n  classes: Map<string, StyleClass>\n) => {\n  // graphItemCount = 0;\n  log.trace('items', doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          const edgeData = {\n            id: 'edge' + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: 'normal',\n            arrowTypeEnd: 'arrow_barb',\n            style: G_EDGE_STYLE,\n            labelStyle: '',\n            label: common.sanitizeText(item.description ?? '', getConfig()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look,\n          };\n          edges.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n};\n\n/**\n * Get the direction from the statement items.\n * Look through all of the documents (docs) in the parsedItems\n * Because is a _document_ direction, the default direction is not necessarily the same as the overall default _diagram_ direction.\n * @param parsedItem - the parsed statement item to look through\n * @param defaultDir - the direction to use if none is found\n */\nconst getDir = (parsedItem: { doc?: Stmt[] }, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === 'dir') {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n};\n\nfunction insertOrUpdateNode(\n  nodes: NodeData[],\n  nodeData: NodeData,\n  classes: Map<string, StyleClass>\n) {\n  if (!nodeData.id || nodeData.id === '</join></fork>' || nodeData.id === '</choice>') {\n    return;\n  }\n\n  //Populate node style attributes if nodeData has classes defined\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n\n    nodeData.cssClasses.split(' ').forEach((cssClass) => {\n      const classDef = classes.get(cssClass);\n      if (classDef) {\n        nodeData.cssCompiledStyles = [...(nodeData.cssCompiledStyles ?? []), ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    //update the existing nodeData\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes.push(nodeData);\n  }\n}\n/**\n * Get classes from the db for the info item.\n * If there aren't any or if dbInfoItem isn't defined, return an empty string.\n * Else create 1 string from the list of classes found\n *\n */\nfunction getClassesFromDbInfo(dbInfoItem?: StateStmt): string {\n  return dbInfoItem?.classes?.join(' ') ?? '';\n}\n\nfunction getStylesFromDbInfo(dbInfoItem?: StateStmt): string[] {\n  return dbInfoItem?.styles ?? [];\n}\n\nexport const dataFetcher = (\n  parent: StateStmt | undefined,\n  parsedItem: StateStmt,\n  diagramStates: Map<string, StateStmt>,\n  nodes: NodeData[],\n  edges: Edge[],\n  altFlag: boolean,\n  look: MermaidConfig['look'],\n  classes: Map<string, StyleClass>\n) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  const config = getConfig();\n\n  log.info('dataFetcher parsedItem', parsedItem, dbState, style);\n\n  if (itemId !== 'root') {\n    let shape = SHAPE_STATE;\n    // The if === true / false can be removed if we can guarantee that the parsedItem.start is always a boolean\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n\n    // Add the node to our list (nodeDb)\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: common.sanitizeText(itemId, config),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style,\n      });\n    }\n\n    const newNode = nodeDb.get(itemId)!;\n\n    // Save data for description and group so that for instance a statement without description overwrites\n    // one with description  @todo TODO What does this mean? If important, add a test for it\n\n    // Build of the array of description strings\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        // There already is an array of strings,add to it\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length && newNode.description.length > 0) {\n          // if there is a description already transform it to an array\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            // If the previous description was this, remove it\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = common.sanitizeTextOrArray(newNode.description, config);\n    }\n\n    // If there's only 1 description entry, just use a regular state shape\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === 'group') {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n\n    // group\n    if (!newNode.type && parsedItem.doc) {\n      log.info('Setting cluster for XCX', itemId, getDir(parsedItem));\n      newNode.type = 'group';\n      newNode.isGroup = true;\n      newNode.dir = getDir(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : ''}`;\n    }\n\n    // This is what will be added to the graph\n    const nodeData: NodeData = {\n      labelStyle: '',\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === 'group',\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look,\n    };\n\n    // Clear the label for dividers who have no description\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = '';\n    }\n\n    if (parent && parent.id !== 'root') {\n      log.trace('Setting node ', itemId, ' to be child of its parent ', parent.id);\n      nodeData.parentId = parent.id;\n    }\n\n    nodeData.centerLabel = true;\n\n    if (parsedItem.note) {\n      // Todo: set random id\n      const noteData: NodeData = {\n        labelStyle: '',\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompiledStyles: [],\n        id: itemId + NOTE_ID + '-' + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === 'group',\n        padding: config.flowchart?.padding,\n        look,\n        position: parsedItem.note.position,\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: '',\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: 'group',\n        isGroup: true,\n        padding: 16, //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position,\n      };\n      graphItemCount++;\n\n      //add parent id to groupData\n      groupData.id = parentNodeId;\n      //add parent id to noteData\n      noteData.parentId = parentNodeId;\n      //nodeData.parentId = parentNodeId;\n\n      //insert groupData\n      insertOrUpdateNode(nodes, groupData, classes);\n      //insert noteData\n      insertOrUpdateNode(nodes, noteData, classes);\n      //insert nodeData\n      insertOrUpdateNode(nodes, nodeData, classes);\n\n      let from = itemId;\n      let to = noteData.id;\n\n      if (parsedItem.note.position === 'left of') {\n        from = noteData.id;\n        to = itemId;\n      }\n\n      edges.push({\n        id: from + '-' + to,\n        start: from,\n        end: to,\n        arrowhead: 'none',\n        arrowTypeEnd: '',\n        style: G_EDGE_STYLE,\n        labelStyle: '',\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look,\n      });\n    } else {\n      insertOrUpdateNode(nodes, nodeData, classes);\n    }\n  }\n  if (parsedItem.doc) {\n    log.trace('Adding nodes children ');\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes, edges, !altFlag, look, classes);\n  }\n};\n\nexport const reset = () => {\n  nodeDb.clear();\n  graphItemCount = 0;\n};\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport { generateId } from '../../utils.js';\nimport common from '../common/common.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport { dataFetcher, reset as resetDataFetcher } from './dataFetcher.js';\nimport { getDir } from './stateRenderer-v3-unified.js';\nimport {\n  DEFAULT_DIAGRAM_DIRECTION,\n  DEFAULT_STATE_TYPE,\n  DIVIDER_TYPE,\n  STMT_APPLYCLASS,\n  STMT_CLASSDEF,\n  STMT_RELATION,\n  STMT_ROOT,\n  STMT_DIRECTION,\n  STMT_STATE,\n  STMT_STYLEDEF,\n} from './stateCommon.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\nconst CONSTANTS = {\n  START_NODE: '[*]',\n  START_TYPE: 'start',\n  END_NODE: '[*]',\n  END_TYPE: 'end',\n  COLOR_KEYWORD: 'color',\n  FILL_KEYWORD: 'fill',\n  BG_FILL: 'bgFill',\n  STYLECLASS_SEP: ',',\n} as const;\n\ninterface BaseStmt {\n  stmt:\n    | 'applyClass'\n    | 'classDef'\n    | 'dir'\n    | 'relation'\n    | 'state'\n    | 'style'\n    | 'root'\n    | 'default'\n    | 'click';\n}\n\ninterface ApplyClassStmt extends BaseStmt {\n  stmt: 'applyClass';\n  id: string;\n  styleClass: string;\n}\n\ninterface ClassDefStmt extends BaseStmt {\n  stmt: 'classDef';\n  id: string;\n  classes: string;\n}\n\ninterface DirectionStmt extends BaseStmt {\n  stmt: 'dir';\n  value: 'TB' | 'BT' | 'RL' | 'LR';\n}\n\ninterface RelationStmt extends BaseStmt {\n  stmt: 'relation';\n  state1: StateStmt;\n  state2: StateStmt;\n  description?: string;\n}\n\nexport interface StateStmt extends BaseStmt {\n  stmt: 'state' | 'default';\n  id: string;\n  type: 'default' | 'fork' | 'join' | 'choice' | 'divider' | 'start' | 'end';\n  description?: string;\n  descriptions?: string[];\n  doc?: Stmt[];\n  note?: Note;\n  start?: boolean;\n  classes?: string[];\n  styles?: string[];\n  textStyles?: string[];\n}\n\ninterface StyleStmt extends BaseStmt {\n  stmt: 'style';\n  id: string;\n  styleClass: string;\n}\n\nexport interface RootStmt {\n  id: 'root';\n  stmt: 'root';\n  doc?: Stmt[];\n}\n\nexport interface ClickStmt extends BaseStmt {\n  stmt: 'click';\n  id: string;\n  url: string;\n  tooltip: string;\n}\n\ninterface Note {\n  position?: 'left of' | 'right of';\n  text: string;\n}\n\nexport type Stmt =\n  | ApplyClassStmt\n  | ClassDefStmt\n  | DirectionStmt\n  | RelationStmt\n  | StateStmt\n  | StyleStmt\n  | RootStmt\n  | ClickStmt;\n\ninterface DiagramEdge {\n  id1: string;\n  id2: string;\n  relationTitle?: string;\n}\n\ninterface Document {\n  relations: DiagramEdge[];\n  states: Map<string, StateStmt>;\n  documents: Record<string, Document>;\n}\n\nexport interface StyleClass {\n  id: string;\n  styles: string[];\n  textStyles: string[];\n}\n\nexport interface NodeData {\n  labelStyle?: string;\n  shape: string;\n  label?: string | string[];\n  cssClasses: string;\n  cssCompiledStyles?: string[];\n  cssStyles: string[];\n  id: string;\n  dir?: string;\n  domId?: string;\n  type?: string;\n  isGroup?: boolean;\n  padding?: number;\n  rx?: number;\n  ry?: number;\n  look?: MermaidConfig['look'];\n  parentId?: string;\n  centerLabel?: boolean;\n  position?: string;\n  description?: string | string[];\n}\n\nexport interface Edge {\n  id: string;\n  start: string;\n  end: string;\n  arrowhead: string;\n  arrowTypeEnd: string;\n  style: string;\n  labelStyle: string;\n  label?: string;\n  arrowheadStyle: string;\n  labelpos: string;\n  labelType: string;\n  thickness: string;\n  classes: string;\n  look: MermaidConfig['look'];\n}\n\n/**\n * Returns a new list of classes.\n * In the future, this can be replaced with a class common to all diagrams.\n * ClassDef information = \\{ id: id, styles: [], textStyles: [] \\}\n */\nconst newClassesList = (): Map<string, StyleClass> => new Map();\nconst newDoc = (): Document => ({\n  relations: [],\n  states: new Map(),\n  documents: {},\n});\nconst clone = <T>(o: T): T => JSON.parse(JSON.stringify(o));\n\nexport class StateDB {\n  private nodes: NodeData[] = [];\n  private edges: Edge[] = [];\n  private rootDoc: Stmt[] = [];\n  private classes = newClassesList();\n  private documents = { root: newDoc() };\n  private currentDocument = this.documents.root;\n  private startEndCount = 0;\n  private dividerCnt = 0;\n  private links = new Map<string, { url: string; tooltip: string }>();\n\n  static readonly relationType = {\n    AGGREGATION: 0,\n    EXTENSION: 1,\n    COMPOSITION: 2,\n    DEPENDENCY: 3,\n  } as const;\n\n  constructor(private version: 1 | 2) {\n    this.clear();\n    // Bind methods used by JISON\n    this.setRootDoc = this.setRootDoc.bind(this);\n    this.getDividerId = this.getDividerId.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.trimColon = this.trimColon.bind(this);\n  }\n\n  /**\n   * Convert all of the statements (stmts) that were parsed into states and relationships.\n   * This is done because a state diagram may have nested sections,\n   * where each section is a 'document' and has its own set of statements.\n   * Ex: the section within a fork has its own statements, and incoming and outgoing statements\n   * refer to the fork as a whole (document).\n   * See the parser grammar:  the definition of a document is a document then a 'line', where a line can be a statement.\n   * This will push the statement into the list of statements for the current document.\n   */\n  extract(statements: Stmt[] | { doc: Stmt[] }) {\n    this.clear(true);\n    for (const item of Array.isArray(statements) ? statements : statements.doc) {\n      switch (item.stmt) {\n        case STMT_STATE:\n          this.addState(item.id.trim(), item.type, item.doc, item.description, item.note);\n          break;\n        case STMT_RELATION:\n          this.addRelation(item.state1, item.state2, item.description);\n          break;\n        case STMT_CLASSDEF:\n          this.addStyleClass(item.id.trim(), item.classes);\n          break;\n        case STMT_STYLEDEF:\n          this.handleStyleDef(item);\n          break;\n        case STMT_APPLYCLASS:\n          this.setCssClass(item.id.trim(), item.styleClass);\n          break;\n        case 'click':\n          this.addLink(item.id, item.url, item.tooltip);\n          break;\n      }\n    }\n    const diagramStates = this.getStates();\n    const config = getConfig();\n\n    resetDataFetcher();\n    dataFetcher(\n      undefined,\n      this.getRootDocV2() as StateStmt,\n      diagramStates,\n      this.nodes,\n      this.edges,\n      true,\n      config.look,\n      this.classes\n    );\n\n    // Process node labels\n    for (const node of this.nodes) {\n      if (!Array.isArray(node.label)) {\n        continue;\n      }\n\n      node.description = node.label.slice(1);\n      if (node.isGroup && node.description.length > 0) {\n        throw new Error(\n          `Group nodes can only have label. Remove the additional description for node [${node.id}]`\n        );\n      }\n      node.label = node.label[0];\n    }\n  }\n\n  private handleStyleDef(item: StyleStmt) {\n    const ids = item.id.trim().split(',');\n    const styles = item.styleClass.split(',');\n\n    for (const id of ids) {\n      let state = this.getState(id);\n      if (!state) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        state = this.getState(trimmedId);\n      }\n      if (state) {\n        state.styles = styles.map((s) => s.replace(/;/g, '')?.trim());\n      }\n    }\n  }\n\n  setRootDoc(o: Stmt[]) {\n    log.info('Setting root doc', o);\n    this.rootDoc = o;\n    if (this.version === 1) {\n      this.extract(o);\n    } else {\n      this.extract(this.getRootDocV2());\n    }\n  }\n\n  docTranslator(parent: RootStmt | StateStmt, node: Stmt, first: boolean) {\n    if (node.stmt === STMT_RELATION) {\n      this.docTranslator(parent, node.state1, true);\n      this.docTranslator(parent, node.state2, false);\n      return;\n    }\n\n    if (node.stmt === STMT_STATE) {\n      if (node.id === CONSTANTS.START_NODE) {\n        node.id = parent.id + (first ? '_start' : '_end');\n        node.start = first;\n      } else {\n        // This is just a plain state, not a start or end\n        node.id = node.id.trim();\n      }\n    }\n\n    if ((node.stmt !== STMT_ROOT && node.stmt !== STMT_STATE) || !node.doc) {\n      return;\n    }\n\n    const doc = [];\n    // Check for concurrency\n    let currentDoc = [];\n    for (const stmt of node.doc) {\n      if ((stmt as StateStmt).type === DIVIDER_TYPE) {\n        const newNode = clone(stmt as StateStmt);\n        newNode.doc = clone(currentDoc);\n        doc.push(newNode);\n        currentDoc = [];\n      } else {\n        currentDoc.push(stmt);\n      }\n    }\n\n    // If any divider was encountered\n    if (doc.length > 0 && currentDoc.length > 0) {\n      const newNode = {\n        stmt: STMT_STATE,\n        id: generateId(),\n        type: 'divider',\n        doc: clone(currentDoc),\n      } satisfies StateStmt;\n      doc.push(clone(newNode));\n      node.doc = doc;\n    }\n\n    node.doc.forEach((docNode) => this.docTranslator(node, docNode, true));\n  }\n\n  private getRootDocV2() {\n    this.docTranslator(\n      { id: STMT_ROOT, stmt: STMT_ROOT },\n      { id: STMT_ROOT, stmt: STMT_ROOT, doc: this.rootDoc },\n      true\n    );\n    return { id: STMT_ROOT, doc: this.rootDoc };\n  }\n\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param descr - description for the state. Can be a string or a list or strings\n   * @param classes - class styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 class, convert it to an array of that 1 class.\n   * @param styles - styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 style, convert it to an array of that 1 style.\n   * @param textStyles - text styles to apply to this state. Can be a string (1 text test) or an array of text styles. If it's just 1 text style, convert it to an array of that 1 text style.\n   */\n  addState(\n    id: string,\n    type: StateStmt['type'] = DEFAULT_STATE_TYPE,\n    doc: Stmt[] | undefined = undefined,\n    descr: string | string[] | undefined = undefined,\n    note: Note | undefined = undefined,\n    classes: string | string[] | undefined = undefined,\n    styles: string | string[] | undefined = undefined,\n    textStyles: string | string[] | undefined = undefined\n  ) {\n    const trimmedId = id?.trim();\n    if (!this.currentDocument.states.has(trimmedId)) {\n      log.info('Adding state ', trimmedId, descr);\n      this.currentDocument.states.set(trimmedId, {\n        stmt: STMT_STATE,\n        id: trimmedId,\n        descriptions: [],\n        type,\n        doc,\n        note,\n        classes: [],\n        styles: [],\n        textStyles: [],\n      });\n    } else {\n      const state = this.currentDocument.states.get(trimmedId);\n      if (!state) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      if (!state.doc) {\n        state.doc = doc;\n      }\n      if (!state.type) {\n        state.type = type;\n      }\n    }\n\n    if (descr) {\n      log.info('Setting state description', trimmedId, descr);\n      const descriptions = Array.isArray(descr) ? descr : [descr];\n      descriptions.forEach((des) => this.addDescription(trimmedId, des.trim()));\n    }\n\n    if (note) {\n      const doc2 = this.currentDocument.states.get(trimmedId);\n      if (!doc2) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      doc2.note = note;\n      doc2.note.text = common.sanitizeText(doc2.note.text, getConfig());\n    }\n\n    if (classes) {\n      log.info('Setting state classes', trimmedId, classes);\n      const classesList = Array.isArray(classes) ? classes : [classes];\n      classesList.forEach((cssClass) => this.setCssClass(trimmedId, cssClass.trim()));\n    }\n\n    if (styles) {\n      log.info('Setting state styles', trimmedId, styles);\n      const stylesList = Array.isArray(styles) ? styles : [styles];\n      stylesList.forEach((style) => this.setStyle(trimmedId, style.trim()));\n    }\n\n    if (textStyles) {\n      log.info('Setting state styles', trimmedId, styles);\n      const textStylesList = Array.isArray(textStyles) ? textStyles : [textStyles];\n      textStylesList.forEach((textStyle) => this.setTextStyle(trimmedId, textStyle.trim()));\n    }\n  }\n\n  clear(saveCommon?: boolean) {\n    this.nodes = [];\n    this.edges = [];\n    this.documents = { root: newDoc() };\n    this.currentDocument = this.documents.root;\n\n    // number of start and end nodes; used to construct ids\n    this.startEndCount = 0;\n    this.classes = newClassesList();\n    if (!saveCommon) {\n      this.links = new Map(); // <-- add here\n      commonClear();\n    }\n  }\n\n  getState(id: string) {\n    return this.currentDocument.states.get(id);\n  }\n\n  getStates() {\n    return this.currentDocument.states;\n  }\n\n  logDocuments() {\n    log.info('Documents = ', this.documents);\n  }\n\n  getRelations() {\n    return this.currentDocument.relations;\n  }\n\n  /**\n   * Adds a clickable link to a state.\n   */\n  addLink(stateId: string, url: string, tooltip: string): void {\n    this.links.set(stateId, { url, tooltip });\n    log.warn('Adding link', stateId, url, tooltip);\n  }\n\n  /**\n   * Get all registered links.\n   */\n  getLinks(): Map<string, { url: string; tooltip: string }> {\n    return this.links;\n  }\n\n  /**\n   * If the id is a start node ( [*] ), then return a new id constructed from\n   * the start node name and the current start node count.\n   * else return the given id\n   */\n  startIdIfNeeded(id = '') {\n    if (id === CONSTANTS.START_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.START_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n\n  /**\n   * If the id is a start node ( [*] ), then return the start type ('start')\n   * else return the given type\n   */\n  startTypeIfNeeded(id = '', type: StateStmt['type'] = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.START_NODE ? CONSTANTS.START_TYPE : type;\n  }\n\n  /**\n   * If the id is an end node ( [*] ), then return a new id constructed from\n   * the end node name and the current start_end node count.\n   * else return the given id\n   */\n  endIdIfNeeded(id = '') {\n    if (id === CONSTANTS.END_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.END_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n\n  /**\n   * If the id is an end node ( [*] ), then return the end type\n   * else return the given type\n   *\n   */\n  endTypeIfNeeded(id = '', type: StateStmt['type'] = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.END_NODE ? CONSTANTS.END_TYPE : type;\n  }\n\n  addRelationObjs(item1: StateStmt, item2: StateStmt, relationTitle = '') {\n    const id1 = this.startIdIfNeeded(item1.id.trim());\n    const type1 = this.startTypeIfNeeded(item1.id.trim(), item1.type);\n    const id2 = this.startIdIfNeeded(item2.id.trim());\n    const type2 = this.startTypeIfNeeded(item2.id.trim(), item2.type);\n    this.addState(\n      id1,\n      type1,\n      item1.doc,\n      item1.description,\n      item1.note,\n      item1.classes,\n      item1.styles,\n      item1.textStyles\n    );\n    this.addState(\n      id2,\n      type2,\n      item2.doc,\n      item2.description,\n      item2.note,\n      item2.classes,\n      item2.styles,\n      item2.textStyles\n    );\n    this.currentDocument.relations.push({\n      id1,\n      id2,\n      relationTitle: common.sanitizeText(relationTitle, getConfig()),\n    });\n  }\n\n  /**\n   * Add a relation between two items.  The items may be full objects or just the string id of a state.\n   */\n  addRelation(item1: string | StateStmt, item2: string | StateStmt, title?: string) {\n    if (typeof item1 === 'object' && typeof item2 === 'object') {\n      this.addRelationObjs(item1, item2, title);\n    } else if (typeof item1 === 'string' && typeof item2 === 'string') {\n      const id1 = this.startIdIfNeeded(item1.trim());\n      const type1 = this.startTypeIfNeeded(item1);\n      const id2 = this.endIdIfNeeded(item2.trim());\n      const type2 = this.endTypeIfNeeded(item2);\n\n      this.addState(id1, type1);\n      this.addState(id2, type2);\n      this.currentDocument.relations.push({\n        id1,\n        id2,\n        relationTitle: title ? common.sanitizeText(title, getConfig()) : undefined,\n      });\n    }\n  }\n\n  addDescription(id: string, descr: string) {\n    const theState = this.currentDocument.states.get(id);\n    const _descr = descr.startsWith(':') ? descr.replace(':', '').trim() : descr;\n    theState?.descriptions?.push(common.sanitizeText(_descr, getConfig()));\n  }\n\n  cleanupLabel(label: string) {\n    return label.startsWith(':') ? label.slice(2).trim() : label.trim();\n  }\n\n  getDividerId() {\n    this.dividerCnt++;\n    return `divider-id-${this.dividerCnt}`;\n  }\n\n  /**\n   * Called when the parser comes across a (style) class definition\n   * @example classDef my-style fill:#f96;\n   *\n   * @param id - the id of this (style) class\n   * @param styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n   */\n  addStyleClass(id: string, styleAttributes = '') {\n    // create a new style class object with this id\n    if (!this.classes.has(id)) {\n      this.classes.set(id, { id, styles: [], textStyles: [] });\n    }\n    const foundClass = this.classes.get(id);\n    if (styleAttributes && foundClass) {\n      styleAttributes.split(CONSTANTS.STYLECLASS_SEP).forEach((attrib) => {\n        const fixedAttrib = attrib.replace(/([^;]*);/, '$1').trim();\n        if (RegExp(CONSTANTS.COLOR_KEYWORD).exec(attrib)) {\n          const newStyle1 = fixedAttrib.replace(CONSTANTS.FILL_KEYWORD, CONSTANTS.BG_FILL);\n          const newStyle2 = newStyle1.replace(CONSTANTS.COLOR_KEYWORD, CONSTANTS.FILL_KEYWORD);\n          foundClass.textStyles.push(newStyle2);\n        }\n        foundClass.styles.push(fixedAttrib);\n      });\n    }\n  }\n\n  getClasses() {\n    return this.classes;\n  }\n\n  /**\n   * Add a (style) class or css class to a state with the given id.\n   * If the state isn't already in the list of known states, add it.\n   * Might be called by parser when a style class or CSS class should be applied to a state\n   *\n   * @param itemIds - The id or a list of ids of the item(s) to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setCssClass(itemIds: string, cssClassName: string) {\n    itemIds.split(',').forEach((id) => {\n      let foundState = this.getState(id);\n      if (!foundState) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        foundState = this.getState(trimmedId);\n      }\n      foundState?.classes?.push(cssClassName);\n    });\n  }\n\n  /**\n   * Add a style to a state with the given id.\n   * @example style stateId fill:#f9f,stroke:#333,stroke-width:4px\n   *   where 'style' is the keyword\n   *   stateId is the id of a state\n   *   the rest of the string is the styleText (all of the attributes to be applied to the state)\n   *\n   * @param itemId - The id of item to apply the style to\n   * @param styleText - the text of the attributes for the style\n   */\n  setStyle(itemId: string, styleText: string) {\n    this.getState(itemId)?.styles?.push(styleText);\n  }\n\n  /**\n   * Add a text style to a state with the given id\n   *\n   * @param itemId - The id of item to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setTextStyle(itemId: string, cssClassName: string) {\n    this.getState(itemId)?.textStyles?.push(cssClassName);\n  }\n\n  /**\n   * Finds the direction statement in the root document.\n   * @returns the direction statement if present\n   */\n  private getDirectionStatement() {\n    return this.rootDoc.find((doc): doc is DirectionStmt => doc.stmt === STMT_DIRECTION);\n  }\n\n  getDirection() {\n    return this.getDirectionStatement()?.value ?? DEFAULT_DIAGRAM_DIRECTION;\n  }\n\n  setDirection(dir: DirectionStmt['value']) {\n    const doc = this.getDirectionStatement();\n    if (doc) {\n      doc.value = dir;\n    } else {\n      this.rootDoc.unshift({ stmt: STMT_DIRECTION, value: dir });\n    }\n  }\n\n  trimColon(str: string) {\n    return str.startsWith(':') ? str.slice(1).trim() : str.trim();\n  }\n\n  getData() {\n    const config = getConfig();\n    return {\n      nodes: this.nodes,\n      edges: this.edges,\n      other: {},\n      config,\n      direction: getDir(this.getRootDocV2()),\n    };\n  }\n\n  getConfig() {\n    return getConfig().state;\n  }\n\n  getAccTitle = getAccTitle;\n  setAccTitle = setAccTitle;\n  getAccDescription = getAccDescription;\n  setAccDescription = setAccDescription;\n  setDiagramTitle = setDiagramTitle;\n  getDiagramTitle = getDiagramTitle;\n}\n", "const getStyles = (options) =>\n  `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : '#efefef'};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : '#efefef'};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`;\n\n// todo: change composit to composite\n// cspell:ignore composit\n\nexport default getStyles;\n"], "mappings": "mUAyEA,IAAIA,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,EAAE,OAAOE,IAAIJ,EAAEE,EAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAC9mBpC,GAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,MAAQ,EAAE,GAAK,EAAE,GAAK,EAAE,SAAW,EAAE,KAAO,EAAE,UAAY,EAAE,kBAAoB,GAAG,eAAiB,GAAG,kBAAoB,GAAG,YAAc,GAAG,MAAQ,GAAG,MAAM,GAAG,WAAa,GAAG,MAAQ,GAAG,MAAQ,GAAG,eAAiB,GAAG,aAAe,GAAG,YAAc,GAAG,YAAc,GAAG,GAAK,GAAG,GAAK,GAAG,KAAO,GAAG,KAAO,GAAG,OAAS,GAAG,WAAa,GAAG,KAAO,GAAG,aAAe,GAAG,UAAY,GAAG,UAAY,GAAG,UAAY,GAAG,gBAAkB,GAAG,UAAY,GAAG,gBAAkB,GAAG,0BAA4B,GAAG,MAAQ,GAAG,OAAS,GAAG,KAAO,GAAG,SAAW,GAAG,YAAc,GAAG,mBAAqB,GAAG,QAAU,GAAG,MAAQ,GAAG,UAAY,GAAG,mBAAqB,GAAG,MAAQ,GAAG,gBAAkB,GAAG,WAAa,GAAG,aAAe,GAAG,aAAe,GAAG,aAAe,GAAG,aAAe,GAAG,IAAM,GAAG,IAAI,GAAG,WAAa,GAAG,gBAAkB,GAAG,QAAU,GAAG,SAAW,GAAG,QAAU,EAAE,KAAO,CAAC,EACp4B,WAAY,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,aAAa,GAAG,QAAQ,GAAG,QAAQ,GAAG,iBAAiB,GAAG,eAAe,GAAG,cAAc,GAAG,cAAc,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,GAAG,OAAO,GAAG,SAAS,GAAG,aAAa,GAAG,OAAO,GAAG,YAAY,GAAG,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,GAAG,4BAA4B,GAAG,QAAQ,GAAG,SAAS,GAAG,OAAO,GAAG,WAAW,GAAG,cAAc,GAAG,qBAAqB,GAAG,UAAU,GAAG,QAAQ,GAAG,YAAY,GAAG,qBAAqB,GAAG,QAAQ,GAAG,kBAAkB,GAAG,aAAa,GAAG,eAAe,GAAG,eAAe,GAAG,eAAe,GAAG,eAAe,GAAG,IAAI,GAAG,aAAa,GAAG,kBAAkB,GAAG,UAAU,GAAG,UAAU,EACtsB,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EACtU,cAAeA,EAAA,SAAmBmC,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,EAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GAC8C,OAAAD,EAAG,WAAWE,EAAGE,CAAE,CAAC,EAAUF,EAAGE,CAAE,EACtF,MACA,IAAK,GACgC,KAAK,EAAI,CAAC,EAC/C,MACA,IAAK,GAEMF,EAAGE,CAAE,GAAI,OAERF,EAAGE,EAAG,CAAC,EAAE,KAAKF,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAIF,EAAGE,EAAG,CAAC,GAInD,MACA,IAAK,GAAG,IAAK,GACZ,KAAK,EAAIF,EAAGE,CAAE,EACf,MACA,IAAK,GACJ,KAAK,EAAE,KACR,MACA,IAAK,IAEO,KAAK,EAAEF,EAAGE,CAAE,EAExB,MACA,IAAK,IAEO,IAAMC,GAAYH,EAAGE,EAAG,CAAC,EACzBC,GAAU,YAAcL,EAAG,UAAUE,EAAGE,CAAE,CAAC,EAC3C,KAAK,EAAIC,GAErB,MACA,IAAK,IAIO,KAAK,EAAE,CAAE,KAAM,WAAY,OAAQH,EAAGE,EAAG,CAAC,EAAG,OAAQF,EAAGE,CAAE,CAAC,EAEvE,MACA,IAAK,IAEO,IAAME,GAAiBN,EAAG,UAAUE,EAAGE,CAAE,CAAC,EAE1C,KAAK,EAAE,CAAE,KAAM,WAAY,OAAQF,EAAGE,EAAG,CAAC,EAAG,OAAQF,EAAGE,EAAG,CAAC,EAAG,YAAaE,EAAc,EAEtG,MACA,IAAK,IAGG,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIJ,EAAGE,EAAG,CAAC,EAAG,KAAM,UAAW,YAAa,GAAI,IAAKF,EAAGE,EAAG,CAAC,CAAE,EAE9F,MACA,IAAK,IAEG,IAAIG,EAAGL,EAAGE,CAAE,EACRI,EAAcN,EAAGE,EAAG,CAAC,EAAE,KAAK,EAChC,GAAGF,EAAGE,CAAE,EAAE,MAAM,GAAG,EAAE,CACjB,IAAIK,EAAQP,EAAGE,CAAE,EAAE,MAAM,GAAG,EAC5BG,EAAGE,EAAM,CAAC,EACVD,EAAc,CAACA,EAAaC,EAAM,CAAC,CAAC,CACxC,CACA,KAAK,EAAE,CAAC,KAAM,QAAS,GAAIF,EAAI,KAAM,UAAW,YAAaC,CAAW,EAGhF,MACA,IAAK,IAGI,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIN,EAAGE,EAAG,CAAC,EAAG,KAAM,UAAW,YAAaF,EAAGE,EAAG,CAAC,EAAG,IAAKF,EAAGE,EAAG,CAAC,CAAE,EAErG,MACA,IAAK,IAEG,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIF,EAAGE,CAAE,EAAG,KAAM,MAAO,EAEzD,MACA,IAAK,IAEG,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIF,EAAGE,CAAE,EAAG,KAAM,MAAO,EAEzD,MACA,IAAK,IAEG,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIF,EAAGE,CAAE,EAAG,KAAM,QAAS,EAE3D,MACA,IAAK,IAEG,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIJ,EAAG,aAAa,EAAG,KAAM,SAAU,EAEvE,MACA,IAAK,IAGG,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIE,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,KAAK,CAAC,SAAUF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,KAAMF,EAAGE,CAAE,EAAE,KAAK,CAAC,CAAC,EAE1G,MACA,IAAK,IACJ,KAAK,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,YAAY,KAAK,CAAC,EAC3C,MACA,IAAK,IAAI,IAAK,IACb,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,kBAAkB,KAAK,CAAC,EACjD,MACA,IAAK,IAEG,KAAK,EAAI,CACL,KAAM,QACN,GAAIE,EAAGE,EAAG,CAAC,EACX,IAAKF,EAAGE,EAAG,CAAC,EACZ,QAASF,EAAGE,EAAG,CAAC,CACpB,EAER,MACA,IAAK,IAEG,KAAK,EAAI,CACL,KAAM,QACN,GAAIF,EAAGE,EAAG,CAAC,EACX,IAAKF,EAAGE,EAAG,CAAC,EACZ,QAAS,EACb,EAER,MACA,IAAK,IAAI,IAAK,IAEN,KAAK,EAAI,CAAE,KAAM,WAAY,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,QAASF,EAAGE,CAAE,EAAE,KAAK,CAAE,EAEjF,MACA,IAAK,IAEG,KAAK,EAAI,CAAE,KAAM,QAAS,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,WAAYF,EAAGE,CAAE,EAAE,KAAK,CAAE,EAEjF,MACA,IAAK,IAGG,KAAK,EAAE,CAAE,KAAM,aAAc,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,WAAYF,EAAGE,CAAE,EAAE,KAAK,CAAE,EAEpF,MACA,IAAK,IACJJ,EAAG,aAAa,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,MAAO,MAAM,IAAI,EACrD,MACA,IAAK,IACJA,EAAG,aAAa,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,MAAO,MAAM,IAAI,EACrD,MACA,IAAK,IACJA,EAAG,aAAa,IAAI,EAAG,KAAK,EAAE,CAAC,KAAK,MAAO,MAAM,IAAI,EACtD,MACA,IAAK,IACJA,EAAG,aAAa,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,MAAO,MAAM,IAAI,EACrD,MACA,IAAK,IAAI,IAAK,IAEF,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIE,EAAGE,CAAE,EAAE,KAAK,EAAG,KAAM,UAAW,YAAa,EAAG,EAExF,MACA,IAAK,IAEO,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,QAAS,CAACF,EAAGE,CAAE,EAAE,KAAK,CAAC,EAAG,KAAM,UAAW,YAAa,EAAG,EAEpH,MACA,IAAK,IAEO,KAAK,EAAE,CAAE,KAAM,QAAS,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,QAAS,CAACF,EAAGE,CAAE,EAAE,KAAK,CAAC,EAAG,KAAM,UAAW,YAAa,EAAG,EAEpH,KACA,CACA,EA7Ke,aA8Kf,MAAO,CAAC,CAAC,EAAE,EAAE,EAAEtC,EAAI,EAAEC,EAAI,EAAEC,CAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAEF,EAAI,EAAEC,EAAI,EAAEC,CAAG,EAAE,CAAC,EAAE,EAAE,EAAEF,EAAI,EAAEC,EAAI,EAAEC,CAAG,EAAEP,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEQ,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAEC,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,CAAG,EAAEhC,EAAEiC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGtB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,CAAG,EAAEhC,EAAEiC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGlB,EAAI,GAAGiB,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEhC,EAAEkC,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAElC,EAAEkC,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAElC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGlB,EAAI,GAAGiB,CAAG,EAAEhC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEmC,GAAI3B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAER,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAExB,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,CAAG,EAAEhC,EAAEiC,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEkC,GAAI,CAAC,EAAE,EAAE,CAAC,EAAElC,EAAEkC,GAAI,CAAC,EAAE,EAAE,CAAC,EAAElC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEmC,GAAI3B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAER,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAExB,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,CAAG,EAAEhC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEjC,EAAEiC,EAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EACzkE,eAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EACpD,WAAYhC,EAAA,SAAqBgD,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOlD,EAAA,SAAemD,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOtB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGsB,EAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAAS9D,MAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IAC/C8D,EAAY,GAAG9D,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGrC6D,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,GAAQF,EAAM,OAClBN,EAAO,KAAKQ,EAAK,EACjB,IAAIC,GAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJSnE,EAAAkE,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,GACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXarE,EAAAoE,GAAA,OAajB,QADIE,EAAQC,GAAgBC,EAAOC,EAAQC,GAAGC,GAAGC,EAAQ,CAAC,EAAGC,GAAGC,EAAKC,GAAUC,KAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,GAAS,GACbD,GAAW,CAAC,EACZ,IAAKH,MAAKpB,EAAMe,CAAK,EACb,KAAK,WAAWK,EAAC,GAAKA,GAAIlB,IAC1BqB,GAAS,KAAK,IAAO,KAAK,WAAWH,EAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,GAAS,wBAA0B5C,EAAW,GAAK;AAAA,EAAQyB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,GAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0B5C,EAAW,GAAK,iBAAmBiC,GAAUV,GAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,GAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,GACL,SAAUgB,EACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,IASDD,EAASC,GACTA,GAAiB,OATjBnC,EAAS0B,EAAM,OACf3B,EAAS2B,EAAM,OACfzB,EAAWyB,EAAM,SACjBE,GAAQF,EAAM,OACVJ,EAAa,GACbA,KAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,KACAW,EAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,GAAI,KAAK,cAAc,MAAMC,EAAO,CAChCzC,EACAC,EACAC,EACA0B,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,GAAM,IACb,OAAOA,GAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,GAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,GAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAW9D,EAAA,SAAoBgD,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAAShD,EAAA,SAAUmD,EAAOb,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASa,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAMnD,EAAA,UAAY,CACV,IAAIkF,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMlF,EAAA,SAAUkF,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAK9E,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAUmE,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUnE,EAAA,UAAY,CACd,IAAIqF,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAcrF,EAAA,UAAY,CAClB,IAAIsF,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAatF,EAAA,UAAY,CACjB,IAAIuF,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAWxF,EAAA,SAASyF,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAASpE,KAAK0F,EACV,KAAK1F,CAAC,EAAI0F,EAAO1F,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIqE,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtB,EAAI,EAAG,EAAIA,EAAM,OAAQ,IAE9B,GADAF,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAM,CAAC,CAAC,CAAC,EAC9CF,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQ,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADAxB,EAAQ,KAAK,WAAWuB,EAAWE,EAAM,CAAC,CAAC,EACvCzB,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAIrE,EAAA,UAAgB,CACZ,IAAI2E,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAM3E,EAAA,SAAgB+F,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAAS/F,EAAA,UAAqB,CACtB,IAAImE,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAcnE,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmBmE,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAUnE,EAAA,SAAoB+F,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAe/F,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBsC,EAAG0D,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,IAAG,MAAO,GAEf,IAAK,IACL,MACA,IAAK,IACL,MACA,IAAK,IACL,MACA,IAAK,IACL,MACA,IAAK,IAAI,YAAK,UAAU,OAAO,EAAqD,GACpF,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAI,YAAK,MAAM,WAAW,EAAS,GACxC,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,YAAK,MAAM,WAAW,EAAS,GACxC,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,KAAK,MAAM,qBAAqB,EACzC,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,MAAO,4BAEf,IAAK,IAAI,YAAK,UAAU,UAAU,EAAU,GAC5C,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,YAAY,EAAU,sBAC/D,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,YAAY,EAAU,GAC/D,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAI,YAAK,UAAU,OAAO,EAAU,GACzC,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,aAAa,EAAU,GAChE,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAI,YAAK,UAAU,OAAO,EAAU,GACzC,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,iBAAiB,EAAU,GACpE,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAI,YAAK,UAAU,OAAO,EAAqD,GACpF,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,KAAK,SAAS,EACtB,MACA,IAAK,IAA0C,KAAK,UAAU,OAAO,EACrE,MACA,IAAK,IAAG,YAAK,SAAS,EAAED,EAAI,OAAOA,EAAI,OAAO,MAAM,EAAE,EAAE,EAAE,KAAK,EAAqD,GACpH,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAI,OAAOA,EAAI,OAAO,MAAM,EAAE,EAAE,EAAE,KAAK,EAAoD,GACnH,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAI,OAAOA,EAAI,OAAO,MAAM,EAAE,GAAG,EAAE,KAAK,EAAoD,GACpH,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAI,OAAOA,EAAI,OAAO,MAAM,EAAE,EAAE,EAAE,KAAK,EAAoD,GACnH,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAI,OAAOA,EAAI,OAAO,MAAM,EAAE,EAAE,EAAE,KAAK,EAAoD,GACnH,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAI,OAAOA,EAAI,OAAO,MAAM,EAAE,GAAG,EAAE,KAAK,EAAoD,GACpH,MACA,IAAK,IAAI,MAAO,IAEhB,IAAK,IAAI,MAAO,IAEhB,IAAK,IAAI,MAAO,IAEhB,IAAK,IAAI,MAAO,IAEhB,IAAK,IAAgD,KAAK,UAAU,cAAc,EAClF,MACA,IAAK,IAAI,YAAK,UAAU,UAAU,EAAoD,KACtF,MACA,IAAK,IAAI,YAAK,SAAS,EAAqD,KAC5E,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAwD,MAAO,cAEpE,IAAK,IAAqD,MAAO,IAEjE,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,QAAQ,EAAyD,GAC1G,MACA,IAAK,IACL,MACA,IAAK,IAAsC,YAAK,SAAS,EAAU,GACnE,MACA,IAAK,IACL,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,SAAS,EAAU,GAC5D,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,SAAS,EAAU,GAC5D,MACA,IAAK,IAAI,KAAK,SAAS,EAAG,KAAK,UAAU,eAAe,EACxD,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,kBAAkB,EAAU,KACrE,MACA,IAAK,IACL,MACA,IAAK,IAA2D,MAAO,YAEvE,IAAK,IAAI,YAAK,SAAS,EAA4D,KACnF,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,WAAW,EAA0D,GAC9G,MACA,IAAK,IAAI,YAAK,SAAS,EAAyDA,EAAI,OAASA,EAAI,OAAO,OAAO,CAAC,EAAE,KAAK,EAAU,GACjI,MACA,IAAK,IAAI,YAAK,SAAS,EAAyDA,EAAI,OAASA,EAAI,OAAO,MAAM,EAAE,EAAE,EAAE,KAAK,EAAU,GACnI,MACA,IAAK,IAA4D,MAAO,GAExE,IAAK,IAA4D,MAAO,GAExE,IAAK,IAAqD,MAAO,IAEjE,IAAK,IAAiD,MAAO,IAE7D,IAAK,IAA2C,MAAO,IAEvD,IAAK,IAAI,OAAAA,EAAI,OAASA,EAAI,OAAO,KAAK,EAAqD,GAC3F,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,SAEf,CACA,EAxKe,aAyKf,MAAO,CAAC,gBAAgB,eAAe,gBAAgB,kBAAkB,+BAA+B,+BAA+B,+BAA+B,+BAA+B,uBAAuB,sBAAsB,cAAc,cAAc,oBAAoB,gBAAgB,gBAAgB,iBAAiB,YAAY,mBAAmB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,aAAa,eAAe,oBAAoB,mBAAmB,eAAe,eAAe,iBAAiB,2BAA2B,eAAe,iBAAiB,kBAAkB,eAAe,iBAAiB,YAAY,mBAAmB,iBAAiB,mBAAmB,mBAAmB,qBAAqB,uBAAuB,uBAAuB,yBAAyB,+BAA+B,+BAA+B,+BAA+B,+BAA+B,YAAY,iBAAiB,iBAAiB,YAAY,cAAc,mBAAmB,WAAW,WAAW,uBAAuB,WAAW,aAAa,gBAAgB,kBAAkB,mBAAmB,UAAU,iBAAiB,YAAY,cAAc,eAAe,uBAAuB,qBAAqB,2BAA2B,wBAAwB,2BAA2B,iCAAiC,eAAe,sBAAsB,qBAAqB,YAAY,WAAW,YAAY,UAAU,SAAS,EAC5hD,WAAY,CAAC,KAAO,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,iBAAmB,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,cAAgB,CAAC,MAAQ,CAAC,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,KAAO,CAAC,MAAQ,CAAC,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,mBAAqB,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,gBAAkB,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,YAAc,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,WAAa,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,SAAW,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,SAAW,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,aAAe,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,WAAa,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,GAAK,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CAC/2C,EACA,OAAOlC,CACP,EAAG,EACHhE,GAAO,MAAQgE,GACf,SAASsC,IAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAApG,EAAAoG,GAAA,UAGTA,GAAO,UAAYtG,GAAOA,GAAO,OAASsG,GACnC,IAAIA,EACX,EAAG,EACFtG,GAAO,OAASA,GAEhB,IAAOuG,GAAQC,GC74BT,IAAMC,GAA4B,KAG5BC,GAAyB,KAGzBC,GAAiB,MAGjBC,EAAa,QAGbC,EAAY,OAGZC,GAAgB,WAEhBC,GAAgB,WAChBC,GAAgB,QAEhBC,GAAkB,aAElBC,EAAqB,UACrBC,GAAe,UAGfC,GAAe,YACfC,GAAwB,aACxBC,GAAkB,IAClBC,GAAmB,OACnBC,GAAmB,SAEnBC,GAAc,OACdC,GAAwB,gBACxBC,GAAc,aACdC,GAAY,WACZC,GAAgB,UAChBC,GAAc,mBACdC,GAAa,OACbC,GAAkB,YAGlBC,EAAc,eACdC,GAAY,QACZC,GAAoB,GAAGF,CAAW,IAAIC,EAAS,GAC/CE,GAAW,aACXC,GAAW,OACXC,GAAgB,YAChBC,GAAqB,GAAGH,EAAQ,IAAIE,EAAa,GACjDE,GAAmB,GAAGP,CAAW,IAAII,EAAQ,GAC7CI,GAAc,UACdC,GAAsB,GAAGT,CAAW,IAAIQ,EAAW,GACnDE,GAAkB,cAClBC,GAA0B,GAAGX,CAAW,IAAIU,EAAe,GAE3DE,GAAS,SACTC,GAAO,OACPC,GAAc,QACdC,GAAoB,OACpBC,GAAU,GAAGD,EAAiB,GAAGF,EAAI,GACrCI,GAAY,GAAGF,EAAiB,GAAGH,EAAM,GC/C/C,IAAMM,GAASC,EAAA,CAACC,EAAiBC,EAAaC,KAA2B,CAC9E,GAAI,CAACF,EAAW,IACd,OAAOC,EAGT,IAAIE,EAAMF,EAEV,QAAWG,KAAiBJ,EAAW,IACjCI,EAAc,OAAS,QACzBD,EAAMC,EAAc,OAIxB,OAAOD,CACT,EAdsB,UAgBTE,GAAaN,EAAA,SACxBO,EACAC,EACmC,CACnC,OAAOA,EAAW,GAAG,WAAW,CAClC,EAL0B,cAObC,GAAOT,EAAA,eAAgBO,EAAcG,EAAYC,EAAkBC,EAAW,CACzFC,EAAI,KAAK,OAAO,EAChBA,EAAI,KAAK,6BAA8BH,CAAE,EACzC,GAAM,CAAE,cAAAI,EAAe,MAAOC,EAAM,OAAAC,CAAO,EAAIC,EAAU,EAGzDL,EAAK,GAAG,QAAQA,EAAK,GAAG,aAAa,CAAC,EAMtC,IAAMM,EAAcN,EAAK,GAAG,QAAQ,EAG9BO,EAAMC,GAAkBV,EAAII,CAAa,EAE/CI,EAAY,KAAON,EAAK,KACxBM,EAAY,gBAAkBF,EAI9BE,EAAY,YAAcH,GAAM,aAAe,GAC/CG,EAAY,YAAcH,GAAM,aAAe,GAC/CG,EAAY,QAAU,CAAC,MAAM,EAC7BA,EAAY,UAAYR,EAExB,MAAMW,GAAOH,EAAaC,CAAG,EAC7B,IAAMG,EAAU,EAGhB,GAAI,EAEA,OAAOV,EAAK,GAAG,UAAa,WAAaA,EAAK,GAAG,SAAS,EAAI,IAAI,KAI9D,QAAQ,CAACW,EAAUC,IAAkB,CACzC,IAAMC,EAAU,OAAOD,GAAQ,SAAWA,EAAM,OAAOA,GAAK,IAAO,SAAWA,EAAI,GAAK,GAEvF,GAAI,CAACC,EAAS,CACZZ,EAAI,KAAK,oDAA2C,KAAK,UAAUW,CAAG,CAAC,EACvE,MACF,CAEA,IAAME,EAAWP,EAAI,KAAK,GAAG,iBAAiB,GAAG,EAC7CQ,EASJ,GAPAD,GAAU,QAASE,GAAmB,CACvBA,EAAE,aAAa,KAAK,IACpBH,IACXE,EAAcC,EAElB,CAAC,EAEG,CAACD,EAAa,CAChBd,EAAI,KAAK,kDAAyCY,CAAO,EACzD,MACF,CAEA,IAAMI,EAASF,EAAY,WAC3B,GAAI,CAACE,EAAQ,CACXhB,EAAI,KAAK,gDAAuCY,CAAO,EACvD,MACF,CAEA,IAAMK,EAAI,SAAS,gBAAgB,6BAA8B,GAAG,EAC9DC,EAAaR,EAAS,IAAI,QAAQ,WAAY,EAAE,EAGtD,GAFAO,EAAE,eAAe,+BAAgC,aAAcC,CAAU,EACzED,EAAE,aAAa,SAAU,QAAQ,EAC7BP,EAAS,QAAS,CACpB,IAAMS,EAAUT,EAAS,QAAQ,QAAQ,WAAY,EAAE,EACvDO,EAAE,aAAa,QAASE,CAAO,CACjC,CAEAH,EAAO,aAAaC,EAAGH,CAAW,EAClCG,EAAE,YAAYH,CAAW,EAEzBd,EAAI,KAAK,yCAAmCY,EAASF,EAAS,GAAG,CACnE,CAAC,CACH,OAASU,EAAK,CACZpB,EAAI,MAAM,0CAAsCoB,CAAG,CACrD,CAEAC,GAAM,YACJf,EACA,wBACAJ,GAAM,gBAAkB,GACxBH,EAAK,GAAG,gBAAgB,CAC1B,EACAuB,GAAoBhB,EAAKG,EAASc,EAAarB,GAAM,aAAe,EAAI,CAC1E,EA3FoB,QA6FbsB,GAAQ,CACb,WAAA/B,GACA,KAAAG,GACA,OAAAV,EACF,ECnGA,IAAMuC,GAAS,IAAI,IAEfC,EAAiB,EAOd,SAASC,GACdC,EAAS,GACTC,EAAU,EACVC,EAAsB,GACtBC,EAAaC,GACb,CACA,IAAMC,EAAUH,IAAS,MAAQA,EAAK,OAAS,EAAI,GAAGC,CAAU,GAAGD,CAAI,GAAK,GAC5E,MAAO,GAAGI,EAAW,IAAIN,CAAM,GAAGK,CAAO,IAAIJ,CAAO,EACtD,CARgBM,EAAAR,GAAA,cAUhB,IAAMS,GAAWD,EAAA,CACfE,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CAEHC,EAAI,MAAM,QAASP,CAAG,EACtBA,EAAI,QAASQ,GAAS,CACpB,OAAQA,EAAK,KAAM,CACjB,KAAKC,EACHC,EAAYX,EAAkBS,EAAMP,EAAeC,EAAOC,EAAOC,EAASC,EAAMC,CAAO,EACvF,MACF,KAAKK,EACHD,EAAYX,EAAkBS,EAAMP,EAAeC,EAAOC,EAAOC,EAASC,EAAMC,CAAO,EACvF,MACF,KAAKM,GACH,CACEF,EACEX,EACAS,EAAK,OACLP,EACAC,EACAC,EACAC,EACAC,EACAC,CACF,EACAI,EACEX,EACAS,EAAK,OACLP,EACAC,EACAC,EACAC,EACAC,EACAC,CACF,EACA,IAAMO,EAAW,CACf,GAAI,OAASzB,EACb,MAAOoB,EAAK,OAAO,GACnB,IAAKA,EAAK,OAAO,GACjB,UAAW,SACX,aAAc,aACd,MAAOM,GACP,WAAY,GACZ,MAAOC,EAAO,aAAaP,EAAK,aAAe,GAAIQ,EAAU,CAAC,EAC9D,eAAgBC,GAChB,SAAUC,GACV,UAAWC,GACX,UAAWC,GACX,QAASC,GACT,KAAAhB,CACF,EACAF,EAAM,KAAKU,CAAQ,EACnBzB,GACF,CACA,KACJ,CACF,CAAC,CACH,EAhEiB,YAyEXkC,GAASzB,EAAA,CAAC0B,EAA8BC,EAAaC,KAA2B,CACpF,IAAIC,EAAMF,EACV,GAAID,EAAW,IACb,QAAWI,KAAiBJ,EAAW,IACjCI,EAAc,OAAS,QACzBD,EAAMC,EAAc,OAI1B,OAAOD,CACT,EAVe,UAYf,SAASE,GACP1B,EACA2B,EACAvB,EACA,CACA,GAAI,CAACuB,EAAS,IAAMA,EAAS,KAAO,kBAAoBA,EAAS,KAAO,YACtE,OAIEA,EAAS,aACN,MAAM,QAAQA,EAAS,iBAAiB,IAC3CA,EAAS,kBAAoB,CAAC,GAGhCA,EAAS,WAAW,MAAM,GAAG,EAAE,QAASC,GAAa,CACnD,IAAMC,EAAWzB,EAAQ,IAAIwB,CAAQ,EACjCC,IACFF,EAAS,kBAAoB,CAAC,GAAIA,EAAS,mBAAqB,CAAC,EAAI,GAAGE,EAAS,MAAM,EAE3F,CAAC,GAEH,IAAMC,EAAmB9B,EAAM,KAAM+B,GAASA,EAAK,KAAOJ,EAAS,EAAE,EACjEG,EAEF,OAAO,OAAOA,EAAkBH,CAAQ,EAExC3B,EAAM,KAAK2B,CAAQ,CAEvB,CA7BShC,EAAA+B,GAAA,sBAoCT,SAASM,GAAqBC,EAAgC,CAC5D,OAAOA,GAAY,SAAS,KAAK,GAAG,GAAK,EAC3C,CAFStC,EAAAqC,GAAA,wBAIT,SAASE,GAAoBD,EAAkC,CAC7D,OAAOA,GAAY,QAAU,CAAC,CAChC,CAFStC,EAAAuC,GAAA,uBAIF,IAAM1B,EAAcb,EAAA,CACzBwC,EACAd,EACAtB,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CACH,IAAMhB,EAASiC,EAAW,GACpBe,EAAUrC,EAAc,IAAIX,CAAM,EAClCiD,EAAWL,GAAqBI,CAAO,EACvCE,EAAQJ,GAAoBE,CAAO,EACnCG,EAASzB,EAAU,EAIzB,GAFAT,EAAI,KAAK,yBAA0BgB,EAAYe,EAASE,CAAK,EAEzDlD,IAAW,OAAQ,CACrB,IAAIoD,EAAQC,GAERpB,EAAW,QAAU,GACvBmB,EAAQE,GACCrB,EAAW,QAAU,KAC9BmB,EAAQG,IAENtB,EAAW,OAASZ,IACtB+B,EAAQnB,EAAW,MAIhBpC,GAAO,IAAIG,CAAM,GACpBH,GAAO,IAAIG,EAAQ,CACjB,GAAIA,EACJ,MAAAoD,EACA,YAAa3B,EAAO,aAAazB,EAAQmD,CAAM,EAC/C,WAAY,GAAGF,CAAQ,IAAIO,EAAiB,GAC5C,UAAWN,CACb,CAAC,EAGH,IAAMO,EAAU5D,GAAO,IAAIG,CAAM,EAM7BiC,EAAW,cACT,MAAM,QAAQwB,EAAQ,WAAW,GAEnCA,EAAQ,MAAQC,GAChBD,EAAQ,YAAY,KAAKxB,EAAW,WAAW,GAE3CwB,EAAQ,aAAa,QAAUA,EAAQ,YAAY,OAAS,GAE9DA,EAAQ,MAAQC,GACZD,EAAQ,cAAgBzD,EAE1ByD,EAAQ,YAAc,CAACxB,EAAW,WAAW,EAE7CwB,EAAQ,YAAc,CAACA,EAAQ,YAAaxB,EAAW,WAAW,IAGpEwB,EAAQ,MAAQJ,GAChBI,EAAQ,YAAcxB,EAAW,aAGrCwB,EAAQ,YAAchC,EAAO,oBAAoBgC,EAAQ,YAAaN,CAAM,GAI1EM,EAAQ,aAAa,SAAW,GAAKA,EAAQ,QAAUC,KACrDD,EAAQ,OAAS,QACnBA,EAAQ,MAAQE,GAEhBF,EAAQ,MAAQJ,IAKhB,CAACI,EAAQ,MAAQxB,EAAW,MAC9BhB,EAAI,KAAK,0BAA2BjB,EAAQgC,GAAOC,CAAU,CAAC,EAC9DwB,EAAQ,KAAO,QACfA,EAAQ,QAAU,GAClBA,EAAQ,IAAMzB,GAAOC,CAAU,EAC/BwB,EAAQ,MAAQxB,EAAW,OAAS2B,GAAeC,GAAgBF,GACnEF,EAAQ,WAAa,GAAGA,EAAQ,UAAU,IAAIK,EAAmB,IAAIhD,EAAUiD,GAA0B,EAAE,IAI7G,IAAMxB,EAAqB,CACzB,WAAY,GACZ,MAAOkB,EAAQ,MACf,MAAOA,EAAQ,YACf,WAAYA,EAAQ,WACpB,kBAAmB,CAAC,EACpB,UAAWA,EAAQ,UACnB,GAAIzD,EACJ,IAAKyD,EAAQ,IACb,MAAO1D,GAAWC,EAAQF,CAAc,EACxC,KAAM2D,EAAQ,KACd,QAASA,EAAQ,OAAS,QAC1B,QAAS,EACT,GAAI,GACJ,GAAI,GACJ,KAAA1C,CACF,EAcA,GAXIwB,EAAS,QAAUsB,KACrBtB,EAAS,MAAQ,IAGfQ,GAAUA,EAAO,KAAO,SAC1B9B,EAAI,MAAM,gBAAiBjB,EAAQ,8BAA+B+C,EAAO,EAAE,EAC3ER,EAAS,SAAWQ,EAAO,IAG7BR,EAAS,YAAc,GAEnBN,EAAW,KAAM,CAEnB,IAAM+B,EAAqB,CACzB,WAAY,GACZ,MAAOC,GACP,MAAOhC,EAAW,KAAK,KACvB,WAAYiC,GAEZ,UAAW,CAAC,EACZ,kBAAmB,CAAC,EACpB,GAAIlE,EAASmE,GAAU,IAAMrE,EAC7B,MAAOC,GAAWC,EAAQF,EAAgBsE,EAAI,EAC9C,KAAMX,EAAQ,KACd,QAASA,EAAQ,OAAS,QAC1B,QAASN,EAAO,WAAW,QAC3B,KAAApC,EACA,SAAUkB,EAAW,KAAK,QAC5B,EACMoC,EAAerE,EAASsE,GACxBC,EAAY,CAChB,WAAY,GACZ,MAAOC,GACP,MAAOvC,EAAW,KAAK,KACvB,WAAYwB,EAAQ,WACpB,UAAW,CAAC,EACZ,GAAIzD,EAASsE,GACb,MAAOvE,GAAWC,EAAQF,EAAgB2E,EAAM,EAChD,KAAM,QACN,QAAS,GACT,QAAS,GACT,KAAA1D,EACA,SAAUkB,EAAW,KAAK,QAC5B,EACAnC,IAGAyE,EAAU,GAAKF,EAEfL,EAAS,SAAWK,EAIpB/B,GAAmB1B,EAAO2D,EAAWvD,CAAO,EAE5CsB,GAAmB1B,EAAOoD,EAAUhD,CAAO,EAE3CsB,GAAmB1B,EAAO2B,EAAUvB,CAAO,EAE3C,IAAI0D,EAAO1E,EACP2E,EAAKX,EAAS,GAEd/B,EAAW,KAAK,WAAa,YAC/ByC,EAAOV,EAAS,GAChBW,EAAK3E,GAGPa,EAAM,KAAK,CACT,GAAI6D,EAAO,IAAMC,EACjB,MAAOD,EACP,IAAKC,EACL,UAAW,OACX,aAAc,GACd,MAAOnD,GACP,WAAY,GACZ,QAASoD,GACT,eAAgBjD,GAChB,SAAUC,GACV,UAAWC,GACX,UAAWC,GACX,KAAAf,CACF,CAAC,CACH,MACEuB,GAAmB1B,EAAO2B,EAAUvB,CAAO,CAE/C,CACIiB,EAAW,MACbhB,EAAI,MAAM,wBAAwB,EAClCT,GAASyB,EAAYA,EAAW,IAAKtB,EAAeC,EAAOC,EAAO,CAACC,EAASC,EAAMC,CAAO,EAE7F,EAvM2B,eAyMd6D,GAAQtE,EAAA,IAAM,CACzBV,GAAO,MAAM,EACbC,EAAiB,CACnB,EAHqB,SCvWrB,IAAMgF,EAAY,CAChB,WAAY,MACZ,WAAY,QACZ,SAAU,MACV,SAAU,MACV,cAAe,QACf,aAAc,OACd,QAAS,SACT,eAAgB,GAClB,EAqJMC,GAAiBC,EAAA,IAA+B,IAAI,IAAnC,kBACjBC,GAASD,EAAA,KAAiB,CAC9B,UAAW,CAAC,EACZ,OAAQ,IAAI,IACZ,UAAW,CAAC,CACd,GAJe,UAKTE,GAAQF,EAAIG,GAAY,KAAK,MAAM,KAAK,UAAUA,CAAC,CAAC,EAA5C,SAEDC,GAAN,KAAc,CAkBnB,YAAoBC,EAAgB,CAAhB,aAAAA,EAjBpB,KAAQ,MAAoB,CAAC,EAC7B,KAAQ,MAAgB,CAAC,EACzB,KAAQ,QAAkB,CAAC,EAC3B,KAAQ,QAAUN,GAAe,EACjC,KAAQ,UAAY,CAAE,KAAME,GAAO,CAAE,EACrC,KAAQ,gBAAkB,KAAK,UAAU,KACzC,KAAQ,cAAgB,EACxB,KAAQ,WAAa,EACrB,KAAQ,MAAQ,IAAI,IAugBpB,iBAAcK,GACd,iBAAcC,GACd,uBAAoBC,GACpB,uBAAoBC,GACpB,qBAAkBC,GAClB,qBAAkBC,GAlgBhB,KAAK,MAAM,EAEX,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,CAC3C,CA5NF,MAmMqB,CAAAX,EAAA,gBAWnB,YAAgB,aAAe,CAC7B,YAAa,EACb,UAAW,EACX,YAAa,EACb,WAAY,CACd,EAoBA,QAAQY,EAAsC,CAC5C,KAAK,MAAM,EAAI,EACf,QAAWC,KAAQ,MAAM,QAAQD,CAAU,EAAIA,EAAaA,EAAW,IACrE,OAAQC,EAAK,KAAM,CACjB,KAAKC,EACH,KAAK,SAASD,EAAK,GAAG,KAAK,EAAGA,EAAK,KAAMA,EAAK,IAAKA,EAAK,YAAaA,EAAK,IAAI,EAC9E,MACF,KAAKE,GACH,KAAK,YAAYF,EAAK,OAAQA,EAAK,OAAQA,EAAK,WAAW,EAC3D,MACF,KAAKG,GACH,KAAK,cAAcH,EAAK,GAAG,KAAK,EAAGA,EAAK,OAAO,EAC/C,MACF,KAAKI,GACH,KAAK,eAAeJ,CAAI,EACxB,MACF,KAAKK,GACH,KAAK,YAAYL,EAAK,GAAG,KAAK,EAAGA,EAAK,UAAU,EAChD,MACF,IAAK,QACH,KAAK,QAAQA,EAAK,GAAIA,EAAK,IAAKA,EAAK,OAAO,EAC5C,KACJ,CAEF,IAAMM,EAAgB,KAAK,UAAU,EAC/BC,EAASC,EAAU,EAEzBC,GAAiB,EACjBC,EACE,OACA,KAAK,aAAa,EAClBJ,EACA,KAAK,MACL,KAAK,MACL,GACAC,EAAO,KACP,KAAK,OACP,EAGA,QAAWI,KAAQ,KAAK,MACtB,GAAK,MAAM,QAAQA,EAAK,KAAK,EAK7B,IADAA,EAAK,YAAcA,EAAK,MAAM,MAAM,CAAC,EACjCA,EAAK,SAAWA,EAAK,YAAY,OAAS,EAC5C,MAAM,IAAI,MACR,gFAAgFA,EAAK,EAAE,GACzF,EAEFA,EAAK,MAAQA,EAAK,MAAM,CAAC,EAE7B,CAEQ,eAAeX,EAAiB,CACtC,IAAMY,EAAMZ,EAAK,GAAG,KAAK,EAAE,MAAM,GAAG,EAC9Ba,EAASb,EAAK,WAAW,MAAM,GAAG,EAExC,QAAWc,KAAMF,EAAK,CACpB,IAAIG,EAAQ,KAAK,SAASD,CAAE,EAC5B,GAAI,CAACC,EAAO,CACV,IAAMC,EAAYF,EAAG,KAAK,EAC1B,KAAK,SAASE,CAAS,EACvBD,EAAQ,KAAK,SAASC,CAAS,CACjC,CACID,IACFA,EAAM,OAASF,EAAO,IAAKI,GAAMA,EAAE,QAAQ,KAAM,EAAE,GAAG,KAAK,CAAC,EAEhE,CACF,CAEA,WAAW3B,EAAW,CACpB4B,EAAI,KAAK,mBAAoB5B,CAAC,EAC9B,KAAK,QAAUA,EACX,KAAK,UAAY,EACnB,KAAK,QAAQA,CAAC,EAEd,KAAK,QAAQ,KAAK,aAAa,CAAC,CAEpC,CAEA,cAAc6B,EAA8BR,EAAYS,EAAgB,CACtE,GAAIT,EAAK,OAAST,GAAe,CAC/B,KAAK,cAAciB,EAAQR,EAAK,OAAQ,EAAI,EAC5C,KAAK,cAAcQ,EAAQR,EAAK,OAAQ,EAAK,EAC7C,MACF,CAYA,GAVIA,EAAK,OAASV,IACZU,EAAK,KAAO1B,EAAU,YACxB0B,EAAK,GAAKQ,EAAO,IAAMC,EAAQ,SAAW,QAC1CT,EAAK,MAAQS,GAGbT,EAAK,GAAKA,EAAK,GAAG,KAAK,GAItBA,EAAK,OAASU,GAAaV,EAAK,OAASV,GAAe,CAACU,EAAK,IACjE,OAGF,IAAMW,EAAM,CAAC,EAETC,EAAa,CAAC,EAClB,QAAWC,KAAQb,EAAK,IACtB,GAAKa,EAAmB,OAASC,GAAc,CAC7C,IAAMC,EAAUrC,GAAMmC,CAAiB,EACvCE,EAAQ,IAAMrC,GAAMkC,CAAU,EAC9BD,EAAI,KAAKI,CAAO,EAChBH,EAAa,CAAC,CAChB,MACEA,EAAW,KAAKC,CAAI,EAKxB,GAAIF,EAAI,OAAS,GAAKC,EAAW,OAAS,EAAG,CAC3C,IAAMG,EAAU,CACd,KAAMzB,EACN,GAAI0B,GAAW,EACf,KAAM,UACN,IAAKtC,GAAMkC,CAAU,CACvB,EACAD,EAAI,KAAKjC,GAAMqC,CAAO,CAAC,EACvBf,EAAK,IAAMW,CACb,CAEAX,EAAK,IAAI,QAASiB,GAAY,KAAK,cAAcjB,EAAMiB,EAAS,EAAI,CAAC,CACvE,CAEQ,cAAe,CACrB,YAAK,cACH,CAAE,GAAIP,EAAW,KAAMA,CAAU,EACjC,CAAE,GAAIA,EAAW,KAAMA,EAAW,IAAK,KAAK,OAAQ,EACpD,EACF,EACO,CAAE,GAAIA,EAAW,IAAK,KAAK,OAAQ,CAC5C,CAUA,SACEP,EACAe,EAA0BC,EAC1BR,EAA0B,OAC1BS,EAAuC,OACvCC,EAAyB,OACzBC,EAAyC,OACzCpB,EAAwC,OACxCqB,EAA4C,OAC5C,CACA,IAAMlB,EAAYF,GAAI,KAAK,EAC3B,GAAI,CAAC,KAAK,gBAAgB,OAAO,IAAIE,CAAS,EAC5CE,EAAI,KAAK,gBAAiBF,EAAWe,CAAK,EAC1C,KAAK,gBAAgB,OAAO,IAAIf,EAAW,CACzC,KAAMf,EACN,GAAIe,EACJ,aAAc,CAAC,EACf,KAAAa,EACA,IAAAP,EACA,KAAAU,EACA,QAAS,CAAC,EACV,OAAQ,CAAC,EACT,WAAY,CAAC,CACf,CAAC,MACI,CACL,IAAMjB,EAAQ,KAAK,gBAAgB,OAAO,IAAIC,CAAS,EACvD,GAAI,CAACD,EACH,MAAM,IAAI,MAAM,oBAAoBC,CAAS,EAAE,EAE5CD,EAAM,MACTA,EAAM,IAAMO,GAETP,EAAM,OACTA,EAAM,KAAOc,EAEjB,CAQA,GANIE,IACFb,EAAI,KAAK,4BAA6BF,EAAWe,CAAK,GACjC,MAAM,QAAQA,CAAK,EAAIA,EAAQ,CAACA,CAAK,GAC7C,QAASI,GAAQ,KAAK,eAAenB,EAAWmB,EAAI,KAAK,CAAC,CAAC,GAGtEH,EAAM,CACR,IAAMI,EAAO,KAAK,gBAAgB,OAAO,IAAIpB,CAAS,EACtD,GAAI,CAACoB,EACH,MAAM,IAAI,MAAM,oBAAoBpB,CAAS,EAAE,EAEjDoB,EAAK,KAAOJ,EACZI,EAAK,KAAK,KAAOC,EAAO,aAAaD,EAAK,KAAK,KAAM5B,EAAU,CAAC,CAClE,CAEIyB,IACFf,EAAI,KAAK,wBAAyBF,EAAWiB,CAAO,GAChC,MAAM,QAAQA,CAAO,EAAIA,EAAU,CAACA,CAAO,GACnD,QAASK,GAAa,KAAK,YAAYtB,EAAWsB,EAAS,KAAK,CAAC,CAAC,GAG5EzB,IACFK,EAAI,KAAK,uBAAwBF,EAAWH,CAAM,GAC/B,MAAM,QAAQA,CAAM,EAAIA,EAAS,CAACA,CAAM,GAChD,QAAS0B,GAAU,KAAK,SAASvB,EAAWuB,EAAM,KAAK,CAAC,CAAC,GAGlEL,IACFhB,EAAI,KAAK,uBAAwBF,EAAWH,CAAM,GAC3B,MAAM,QAAQqB,CAAU,EAAIA,EAAa,CAACA,CAAU,GAC5D,QAASM,GAAc,KAAK,aAAaxB,EAAWwB,EAAU,KAAK,CAAC,CAAC,EAExF,CAEA,MAAMC,EAAsB,CAC1B,KAAK,MAAQ,CAAC,EACd,KAAK,MAAQ,CAAC,EACd,KAAK,UAAY,CAAE,KAAMrD,GAAO,CAAE,EAClC,KAAK,gBAAkB,KAAK,UAAU,KAGtC,KAAK,cAAgB,EACrB,KAAK,QAAUF,GAAe,EACzBuD,IACH,KAAK,MAAQ,IAAI,IACjBC,GAAY,EAEhB,CAEA,SAAS5B,EAAY,CACnB,OAAO,KAAK,gBAAgB,OAAO,IAAIA,CAAE,CAC3C,CAEA,WAAY,CACV,OAAO,KAAK,gBAAgB,MAC9B,CAEA,cAAe,CACbI,EAAI,KAAK,eAAgB,KAAK,SAAS,CACzC,CAEA,cAAe,CACb,OAAO,KAAK,gBAAgB,SAC9B,CAKA,QAAQyB,EAAiBC,EAAaC,EAAuB,CAC3D,KAAK,MAAM,IAAIF,EAAS,CAAE,IAAAC,EAAK,QAAAC,CAAQ,CAAC,EACxC3B,EAAI,KAAK,cAAeyB,EAASC,EAAKC,CAAO,CAC/C,CAKA,UAA0D,CACxD,OAAO,KAAK,KACd,CAOA,gBAAgB/B,EAAK,GAAI,CACvB,OAAIA,IAAO7B,EAAU,YACnB,KAAK,gBACE,GAAGA,EAAU,UAAU,GAAG,KAAK,aAAa,IAE9C6B,CACT,CAMA,kBAAkBA,EAAK,GAAIe,EAA0BC,EAAoB,CACvE,OAAOhB,IAAO7B,EAAU,WAAaA,EAAU,WAAa4C,CAC9D,CAOA,cAAcf,EAAK,GAAI,CACrB,OAAIA,IAAO7B,EAAU,UACnB,KAAK,gBACE,GAAGA,EAAU,QAAQ,GAAG,KAAK,aAAa,IAE5C6B,CACT,CAOA,gBAAgBA,EAAK,GAAIe,EAA0BC,EAAoB,CACrE,OAAOhB,IAAO7B,EAAU,SAAWA,EAAU,SAAW4C,CAC1D,CAEA,gBAAgBiB,EAAkBC,EAAkBC,EAAgB,GAAI,CACtE,IAAMC,EAAM,KAAK,gBAAgBH,EAAM,GAAG,KAAK,CAAC,EAC1CI,EAAQ,KAAK,kBAAkBJ,EAAM,GAAG,KAAK,EAAGA,EAAM,IAAI,EAC1DK,EAAM,KAAK,gBAAgBJ,EAAM,GAAG,KAAK,CAAC,EAC1CK,EAAQ,KAAK,kBAAkBL,EAAM,GAAG,KAAK,EAAGA,EAAM,IAAI,EAChE,KAAK,SACHE,EACAC,EACAJ,EAAM,IACNA,EAAM,YACNA,EAAM,KACNA,EAAM,QACNA,EAAM,OACNA,EAAM,UACR,EACA,KAAK,SACHK,EACAC,EACAL,EAAM,IACNA,EAAM,YACNA,EAAM,KACNA,EAAM,QACNA,EAAM,OACNA,EAAM,UACR,EACA,KAAK,gBAAgB,UAAU,KAAK,CAClC,IAAAE,EACA,IAAAE,EACA,cAAed,EAAO,aAAaW,EAAexC,EAAU,CAAC,CAC/D,CAAC,CACH,CAKA,YAAYsC,EAA2BC,EAA2BM,EAAgB,CAChF,GAAI,OAAOP,GAAU,UAAY,OAAOC,GAAU,SAChD,KAAK,gBAAgBD,EAAOC,EAAOM,CAAK,UAC/B,OAAOP,GAAU,UAAY,OAAOC,GAAU,SAAU,CACjE,IAAME,EAAM,KAAK,gBAAgBH,EAAM,KAAK,CAAC,EACvCI,EAAQ,KAAK,kBAAkBJ,CAAK,EACpCK,EAAM,KAAK,cAAcJ,EAAM,KAAK,CAAC,EACrCK,EAAQ,KAAK,gBAAgBL,CAAK,EAExC,KAAK,SAASE,EAAKC,CAAK,EACxB,KAAK,SAASC,EAAKC,CAAK,EACxB,KAAK,gBAAgB,UAAU,KAAK,CAClC,IAAAH,EACA,IAAAE,EACA,cAAeE,EAAQhB,EAAO,aAAagB,EAAO7C,EAAU,CAAC,EAAI,MACnE,CAAC,CACH,CACF,CAEA,eAAeM,EAAYiB,EAAe,CACxC,IAAMuB,EAAW,KAAK,gBAAgB,OAAO,IAAIxC,CAAE,EAC7CyC,EAASxB,EAAM,WAAW,GAAG,EAAIA,EAAM,QAAQ,IAAK,EAAE,EAAE,KAAK,EAAIA,EACvEuB,GAAU,cAAc,KAAKjB,EAAO,aAAakB,EAAQ/C,EAAU,CAAC,CAAC,CACvE,CAEA,aAAagD,EAAe,CAC1B,OAAOA,EAAM,WAAW,GAAG,EAAIA,EAAM,MAAM,CAAC,EAAE,KAAK,EAAIA,EAAM,KAAK,CACpE,CAEA,cAAe,CACb,YAAK,aACE,cAAc,KAAK,UAAU,EACtC,CASA,cAAc1C,EAAY2C,EAAkB,GAAI,CAEzC,KAAK,QAAQ,IAAI3C,CAAE,GACtB,KAAK,QAAQ,IAAIA,EAAI,CAAE,GAAAA,EAAI,OAAQ,CAAC,EAAG,WAAY,CAAC,CAAE,CAAC,EAEzD,IAAM4C,EAAa,KAAK,QAAQ,IAAI5C,CAAE,EAClC2C,GAAmBC,GACrBD,EAAgB,MAAMxE,EAAU,cAAc,EAAE,QAAS0E,GAAW,CAClE,IAAMC,EAAcD,EAAO,QAAQ,WAAY,IAAI,EAAE,KAAK,EAC1D,GAAI,OAAO1E,EAAU,aAAa,EAAE,KAAK0E,CAAM,EAAG,CAEhD,IAAME,EADYD,EAAY,QAAQ3E,EAAU,aAAcA,EAAU,OAAO,EACnD,QAAQA,EAAU,cAAeA,EAAU,YAAY,EACnFyE,EAAW,WAAW,KAAKG,CAAS,CACtC,CACAH,EAAW,OAAO,KAAKE,CAAW,CACpC,CAAC,CAEL,CAEA,YAAa,CACX,OAAO,KAAK,OACd,CAUA,YAAYE,EAAiBC,EAAsB,CACjDD,EAAQ,MAAM,GAAG,EAAE,QAAShD,GAAO,CACjC,IAAIkD,EAAa,KAAK,SAASlD,CAAE,EACjC,GAAI,CAACkD,EAAY,CACf,IAAMhD,EAAYF,EAAG,KAAK,EAC1B,KAAK,SAASE,CAAS,EACvBgD,EAAa,KAAK,SAAShD,CAAS,CACtC,CACAgD,GAAY,SAAS,KAAKD,CAAY,CACxC,CAAC,CACH,CAYA,SAASE,EAAgBC,EAAmB,CAC1C,KAAK,SAASD,CAAM,GAAG,QAAQ,KAAKC,CAAS,CAC/C,CAQA,aAAaD,EAAgBF,EAAsB,CACjD,KAAK,SAASE,CAAM,GAAG,YAAY,KAAKF,CAAY,CACtD,CAMQ,uBAAwB,CAC9B,OAAO,KAAK,QAAQ,KAAMzC,GAA8BA,EAAI,OAAS6C,EAAc,CACrF,CAEA,cAAe,CACb,OAAO,KAAK,sBAAsB,GAAG,OAASC,EAChD,CAEA,aAAaC,EAA6B,CACxC,IAAM/C,EAAM,KAAK,sBAAsB,EACnCA,EACFA,EAAI,MAAQ+C,EAEZ,KAAK,QAAQ,QAAQ,CAAE,KAAMF,GAAgB,MAAOE,CAAI,CAAC,CAE7D,CAEA,UAAUC,EAAa,CACrB,OAAOA,EAAI,WAAW,GAAG,EAAIA,EAAI,MAAM,CAAC,EAAE,KAAK,EAAIA,EAAI,KAAK,CAC9D,CAEA,SAAU,CACR,IAAM/D,EAASC,EAAU,EACzB,MAAO,CACL,MAAO,KAAK,MACZ,MAAO,KAAK,MACZ,MAAO,CAAC,EACR,OAAAD,EACA,UAAWgE,GAAO,KAAK,aAAa,CAAC,CACvC,CACF,CAEA,WAAY,CACV,OAAO/D,EAAU,EAAE,KACrB,CAQF,ECztBA,IAAMgE,GAAYC,EAACC,GACjB;AAAA;AAAA,YAEUA,EAAQ,eAAe;AAAA,cACrBA,EAAQ,eAAe;AAAA;AAAA;AAAA,UAG3BA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKlBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOjBA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA,UAIvBA,EAAQ,OAAO;AAAA,YACbA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIlBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,YAKjBA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMzBA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAUhBA,EAAQ,eAAe;AAAA,UACzBA,EAAQ,YAAY;AAAA;AAAA;AAAA,YAGlBA,EAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASvBA,EAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,UAKfA,EAAQ,oBAAoB;AAAA;AAAA;AAAA;AAAA,sBAIhBA,EAAQ,mBAAmB;AAAA;AAAA,wBAEzBA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA,wBAI3BA,EAAQ,mBAAmB;AAAA,YACvCA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,UAK7BA,EAAQ,sBAAwBA,EAAQ,iBAAiB;AAAA;AAAA;AAAA,WAGxDA,EAAQ,sBAAwBA,EAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI1DA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMvBA,EAAQ,iBAAiB;AAAA,YACvBA,EAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI3BA,EAAQ,iBAAiB;AAAA,YACvBA,EAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI3BA,EAAQ,kBAAkB;AAAA,YACxBA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAIpBA,EAAQ,qBAAuBA,EAAQ,UAAU;AAAA,eAC5CA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKvBA,EAAQ,UAAYA,EAAQ,OAAO;AAAA,YACjCA,EAAQ,aAAeA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAI3CA,EAAQ,OAAO;AAAA,YACbA,EAAQ,aAAeA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAI3CA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,UAIjBA,EAAQ,wBAAwB;AAAA,YAC9BA,EAAQ,aAAeA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,WAK1CA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAStBA,EAAQ,aAAeA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQ3CA,EAAQ,qBAAuBA,EAAQ,UAAU;AAAA;AAAA;AAAA,UAGjDA,EAAQ,cAAgBA,EAAQ,cAAgB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAczDA,EAAQ,cAAgBA,EAAQ,cAAgB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQzDA,EAAQ,YAAY;AAAA,YAClBA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMzBA,EAAQ,YAAY;AAAA,YAClBA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOzBA,EAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,WAIpBA,EAAQ,aAAa;AAAA;AAAA;AAAA,mBAGbA,EAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,UAI9BA,EAAQ,SAAS;AAAA,YACfA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOnBA,EAAQ,SAAS;AAAA;AAAA,EArNT,aA4NXC,GAAQH", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "stateStmt", "relDescription", "id", "description", "parts", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "stateDiagram_default", "parser", "DEFAULT_DIAGRAM_DIRECTION", "DEFAULT_NESTED_DOC_DIR", "STMT_DIRECTION", "STMT_STATE", "STMT_ROOT", "STMT_RELATION", "STMT_CLASSDEF", "STMT_STYLEDEF", "STMT_APPLYCLASS", "DEFAULT_STATE_TYPE", "DIVIDER_TYPE", "G_EDGE_STYLE", "G_EDGE_ARROWHEADSTYLE", "G_EDGE_LABELPOS", "G_EDGE_LABELTYPE", "G_EDGE_THICKNESS", "SHAPE_STATE", "SHAPE_STATE_WITH_DESC", "SHAPE_START", "SHAPE_END", "SHAPE_DIVIDER", "SHAPE_GROUP", "SHAPE_NOTE", "SHAPE_NOTEGROUP", "CSS_DIAGRAM", "CSS_STATE", "CSS_DIAGRAM_STATE", "CSS_EDGE", "CSS_NOTE", "CSS_NOTE_EDGE", "CSS_EDGE_NOTE_EDGE", "CSS_DIAGRAM_NOTE", "CSS_CLUSTER", "CSS_DIAGRAM_CLUSTER", "CSS_CLUSTER_ALT", "CSS_DIAGRAM_CLUSTER_ALT", "PARENT", "NOTE", "DOMID_STATE", "DOMID_TYPE_SPACER", "NOTE_ID", "PARENT_ID", "getDir", "__name", "parsedItem", "defaultDir", "DEFAULT_NESTED_DOC_DIR", "dir", "parsedItemDoc", "getClasses", "text", "diagramObj", "draw", "id", "_version", "diag", "log", "securityLevel", "conf", "layout", "getConfig", "data4Layout", "svg", "getDiagramElement", "render", "padding", "linkInfo", "key", "stateId", "allNodes", "matchedElem", "g", "parent", "a", "cleanedUrl", "tooltip", "err", "utils_default", "setupViewPortForSVG", "CSS_DIAGRAM", "stateRenderer_v3_unified_default", "nodeDb", "graphItemCount", "stateDomId", "itemId", "counter", "type", "typeSpacer", "DOMID_TYPE_SPACER", "typeStr", "DOMID_STATE", "__name", "setupDoc", "parentParsedItem", "doc", "diagramStates", "nodes", "edges", "altFlag", "look", "classes", "log", "item", "STMT_STATE", "dataFetcher", "DEFAULT_STATE_TYPE", "STMT_RELATION", "edgeData", "G_EDGE_STYLE", "common_default", "getConfig", "G_EDGE_ARROWHEADSTYLE", "G_EDGE_LABELPOS", "G_EDGE_LABELTYPE", "G_EDGE_THICKNESS", "CSS_EDGE", "getDir", "parsedItem", "defaultDir", "DEFAULT_NESTED_DOC_DIR", "dir", "parsedItemDoc", "insertOrUpdateNode", "nodeData", "cssClass", "classDef", "existingNodeData", "node", "getClassesFromDbInfo", "dbInfoItem", "getStylesFromDbInfo", "parent", "dbState", "classStr", "style", "config", "shape", "SHAPE_STATE", "SHAPE_START", "SHAPE_END", "CSS_DIAGRAM_STATE", "newNode", "SHAPE_STATE_WITH_DESC", "SHAPE_GROUP", "DIVIDER_TYPE", "SHAPE_DIVIDER", "CSS_DIAGRAM_CLUSTER", "CSS_DIAGRAM_CLUSTER_ALT", "noteData", "SHAPE_NOTE", "CSS_DIAGRAM_NOTE", "NOTE_ID", "NOTE", "parentNodeId", "PARENT_ID", "groupData", "SHAPE_NOTEGROUP", "PARENT", "from", "to", "CSS_EDGE_NOTE_EDGE", "reset", "CONSTANTS", "newClassesList", "__name", "newDoc", "clone", "o", "StateDB", "version", "getAccTitle", "setAccTitle", "getAccDescription", "setAccDescription", "setDiagramTitle", "getDiagramTitle", "statements", "item", "STMT_STATE", "STMT_RELATION", "STMT_CLASSDEF", "STMT_STYLEDEF", "STMT_APPLYCLASS", "diagramStates", "config", "getConfig", "reset", "dataFetcher", "node", "ids", "styles", "id", "state", "trimmedId", "s", "log", "parent", "first", "STMT_ROOT", "doc", "currentDoc", "stmt", "DIVIDER_TYPE", "newNode", "generateId", "docNode", "type", "DEFAULT_STATE_TYPE", "descr", "note", "classes", "textStyles", "des", "doc2", "common_default", "cssClass", "style", "textStyle", "saveCommon", "clear", "stateId", "url", "tooltip", "item1", "item2", "relationTitle", "id1", "type1", "id2", "type2", "title", "theState", "_descr", "label", "styleAttributes", "foundClass", "attrib", "fixedAttrib", "newStyle2", "itemIds", "cssClassName", "foundState", "itemId", "styleText", "STMT_DIRECTION", "DEFAULT_DIAGRAM_DIRECTION", "dir", "str", "getDir", "getStyles", "__name", "options", "styles_default"]}