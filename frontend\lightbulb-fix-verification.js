#!/usr/bin/env node

/**
 * Lightbulb 图标修复验证脚本
 * 验证 Lightbulb 图标是否已完全修复
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 验证 Lightbulb 图标修复状态...\n')

// 扫描文件中的 Lightbulb 使用
function scanFileForLightbulb(filePath) {
  if (!fs.existsSync(filePath)) {
    return []
  }

  const content = fs.readFileSync(filePath, 'utf8')
  const issues = []

  // 检查导入语句
  const importMatches = content.match(/import\s*{[^}]*Lightbulb[^}]*}\s*from\s*['"]@element-plus\/icons-vue['"]/g)
  if (importMatches) {
    issues.push({
      type: 'import',
      line: content.substring(0, content.indexOf(importMatches[0])).split('\n').length,
      content: importMatches[0].trim()
    })
  }

  // 检查模板使用
  const templateMatches = content.match(/<el-icon><Lightbulb\s*\/><\/el-icon>/g)
  if (templateMatches) {
    templateMatches.forEach(match => {
      const line = content.substring(0, content.indexOf(match)).split('\n').length
      issues.push({
        type: 'template',
        line,
        content: match
      })
    })
  }

  // 检查字符串引用
  const stringMatches = content.match(/['"`]Lightbulb['"`]/g)
  if (stringMatches) {
    stringMatches.forEach(match => {
      const line = content.substring(0, content.indexOf(match)).split('\n').length
      issues.push({
        type: 'string',
        line,
        content: match
      })
    })
  }

  return issues
}

// 递归扫描目录
function scanDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const issues = scanFileForLightbulb(fullPath)
        if (issues.length > 0) {
          results.push({
            file: path.relative(dir, fullPath),
            issues
          })
        }
      }
    })
  }
  
  scan(dir)
  return results
}

// 验证 Element Plus 图标导入
async function verifyElementPlusIcons() {
  try {
    const icons = await import('@element-plus/icons-vue')
    const hasLightbulb = 'Lightbulb' in icons
    
    console.log('📦 Element Plus 图标库验证:')
    console.log(`   Lightbulb 图标存在: ${hasLightbulb ? '❌ 是' : '✅ 否'}`)
    
    if (hasLightbulb) {
      console.log('   ⚠️  警告: Element Plus 可能已添加 Lightbulb 图标')
    }
    
    // 验证替代图标
    const hasStar = 'Star' in icons
    console.log(`   Star 图标存在: ${hasStar ? '✅ 是' : '❌ 否'}`)
    
    return { hasLightbulb, hasStar }
  } catch (error) {
    console.log('❌ 无法验证 Element Plus 图标库:', error.message)
    return { hasLightbulb: false, hasStar: false }
  }
}

// 主函数
async function main() {
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  console.log('📋 代码扫描结果:')
  
  if (results.length === 0) {
    console.log('✅ 未发现任何 Lightbulb 图标使用！')
  } else {
    console.log(`❌ 发现 ${results.length} 个文件仍在使用 Lightbulb 图标:\n`)
    
    results.forEach(({ file, issues }) => {
      console.log(`📄 ${file}:`)
      issues.forEach(issue => {
        console.log(`   第${issue.line}行 [${issue.type}]: ${issue.content}`)
      })
      console.log()
    })
  }
  
  // 验证图标库
  console.log('\n' + '='.repeat(50))
  await verifyElementPlusIcons()
  
  // 总结
  console.log('\n' + '='.repeat(50))
  console.log('📊 修复验证总结:')
  
  if (results.length === 0) {
    console.log('🎉 Lightbulb 图标修复完成！')
    console.log('✅ 所有 Lightbulb 引用已成功替换为 Star 图标')
    console.log('✅ 应用现在可以正常运行，无语法错误')
  } else {
    console.log('⚠️  仍有文件需要修复')
    console.log('💡 建议运行 fix-all-icons.js 脚本进行自动修复')
  }
  
  console.log('\n🔗 相关文档:')
  console.log('   Element Plus 图标: https://element-plus.org/zh-CN/component/icon.html')
  console.log('   修复报告: LIGHTBULB_FIX_REPORT.md')
}

// 运行验证
main().catch(console.error)
