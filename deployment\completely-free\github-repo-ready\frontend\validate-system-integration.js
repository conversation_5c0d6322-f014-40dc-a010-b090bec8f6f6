#!/usr/bin/env node

/**
 * 🔧 系统集成验证工具
 * System Integration Validation Tool
 * 
 * 验证iFlytek Spark多模态演示系统的完整集成
 * Validate complete integration of iFlytek Spark multimodal demo system
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 iFlytek Spark系统集成验证工具');
console.log('iFlytek Spark System Integration Validation Tool\n');

// 验证配置
const VALIDATION_CONFIG = {
  requiredFiles: {
    components: [
      'src/components/Demo/iFlytek SparkShowcase.vue',
      'src/components/Demo/ResponsiveMediaViewer.vue',
      'src/utils/mediaIntegrationTest.js'
    ],
    views: [
      'src/views/DemoPage.vue'
    ],
    assets: {
      images: [
        'public/generated-images/interface-complete-system.png',
        'public/generated-images/interface-ai-architecture.png',
        'public/generated-images/interface-case-analysis.png',
        'public/generated-images/interface-bigdata-analysis.png',
        'public/generated-images/interface-iot-systems.png'
      ],
      videos: [
        'public/generated-videos/demo-complete.mp4',
        'public/generated-videos/demo-ai-tech.mp4',
        'public/generated-videos/demo-cases.mp4',
        'public/generated-videos/demo-bigdata.mp4',
        'public/generated-videos/demo-iot.mp4'
      ]
    }
  },
  expectedFeatures: [
    'Vue.js 3组件集成',
    'Element Plus UI组件',
    '响应式设计',
    '中文界面本地化',
    '多媒体播放支持',
    'Microsoft YaHei字体优化',
    '移动端适配',
    '质量验证工具'
  ]
}

// 验证结果
let validationResults = {
  timestamp: new Date().toISOString(),
  overall: {
    status: 'pending',
    score: 0,
    issues: [],
    recommendations: []
  },
  components: {
    total: 0,
    passed: 0,
    failed: 0,
    results: []
  },
  assets: {
    images: { total: 0, passed: 0, failed: 0, results: [] },
    videos: { total: 0, passed: 0, failed: 0, results: [] }
  },
  features: {
    total: 0,
    implemented: 0,
    results: []
  }
}

// 验证文件存在性
function validateFileExists(filePath, category) {
  const fullPath = path.resolve(filePath)
  const exists = fs.existsSync(fullPath)
  
  let size = 0
  let details = {}
  
  if (exists) {
    const stats = fs.statSync(fullPath)
    size = stats.size
    
    if (category === 'image') {
      details.sizeKB = Math.round(size / 1024)
      details.expectedRange = '500KB - 2MB'
      details.sizeOK = size >= 500 * 1024 && size <= 2 * 1024 * 1024
    } else if (category === 'video') {
      details.sizeMB = Math.round(size / (1024 * 1024))
      details.expectedRange = '5MB - 20MB'
      details.sizeOK = size >= 5 * 1024 * 1024 && size <= 20 * 1024 * 1024
    } else if (category === 'component') {
      details.sizeKB = Math.round(size / 1024)
      details.sizeOK = size > 1000 // 至少1KB
    }
  }
  
  return {
    path: filePath,
    exists: exists,
    size: size,
    details: details,
    status: exists ? (details.sizeOK !== false ? 'success' : 'warning') : 'failed'
  }
}

// 验证Vue组件内容
function validateVueComponent(filePath) {
  if (!fs.existsSync(filePath)) {
    return {
      path: filePath,
      status: 'failed',
      error: '文件不存在',
      features: []
    }
  }
  
  const content = fs.readFileSync(filePath, 'utf-8')
  const features = []
  
  // 检查Vue 3特性
  if (content.includes('<script setup>')) {
    features.push('Vue 3 Composition API')
  }
  
  // 检查Element Plus组件
  if (content.includes('el-')) {
    features.push('Element Plus集成')
  }
  
  // 检查响应式设计
  if (content.includes('@media') || content.includes('isMobile')) {
    features.push('响应式设计')
  }
  
  // 检查中文内容
  if (content.includes('iFlytek') || content.includes('科大讯飞')) {
    features.push('中文本地化')
  }
  
  // 检查多媒体支持
  if (content.includes('video') || content.includes('img')) {
    features.push('多媒体支持')
  }
  
  // 检查Microsoft YaHei字体
  if (content.includes('Microsoft YaHei')) {
    features.push('Microsoft YaHei字体')
  }
  
  return {
    path: filePath,
    status: 'success',
    features: features,
    size: content.length,
    lines: content.split('\n').length
  }
}

// 验证所有组件
function validateComponents() {
  console.log('🧩 验证Vue组件...\n')
  
  validationResults.components.total = VALIDATION_CONFIG.requiredFiles.components.length + VALIDATION_CONFIG.requiredFiles.views.length
  
  const allComponents = [
    ...VALIDATION_CONFIG.requiredFiles.components,
    ...VALIDATION_CONFIG.requiredFiles.views
  ]
  
  allComponents.forEach(componentPath => {
    console.log(`📄 检查组件: ${componentPath}`)
    
    const fileResult = validateFileExists(componentPath, 'component')
    const componentResult = validateVueComponent(componentPath)
    
    const result = {
      ...fileResult,
      ...componentResult,
      category: 'component'
    }
    
    validationResults.components.results.push(result)
    
    if (result.status === 'success') {
      validationResults.components.passed++
      console.log(`   ✅ 通过 (${result.details.sizeKB}KB, ${result.lines}行)`)
      if (result.features.length > 0) {
        console.log(`   🎯 特性: ${result.features.join(', ')}`)
      }
    } else {
      validationResults.components.failed++
      console.log(`   ❌ 失败: ${result.error || '未知错误'}`)
    }
    console.log('')
  })
}

// 验证静态资源
function validateAssets() {
  console.log('📁 验证静态资源...\n')
  
  // 验证图片
  console.log('📸 检查图片文件:')
  validationResults.assets.images.total = VALIDATION_CONFIG.requiredFiles.assets.images.length
  
  VALIDATION_CONFIG.requiredFiles.assets.images.forEach(imagePath => {
    const result = validateFileExists(imagePath, 'image')
    validationResults.assets.images.results.push(result)
    
    if (result.status === 'success') {
      validationResults.assets.images.passed++
      console.log(`   ✅ ${path.basename(imagePath)} (${result.details.sizeKB}KB)`)
    } else {
      validationResults.assets.images.failed++
      console.log(`   ❌ ${path.basename(imagePath)} - 文件不存在`)
    }
  })
  
  console.log('')
  
  // 验证视频
  console.log('🎬 检查视频文件:')
  validationResults.assets.videos.total = VALIDATION_CONFIG.requiredFiles.assets.videos.length
  
  VALIDATION_CONFIG.requiredFiles.assets.videos.forEach(videoPath => {
    const result = validateFileExists(videoPath, 'video')
    validationResults.assets.videos.results.push(result)
    
    if (result.status === 'success') {
      validationResults.assets.videos.passed++
      console.log(`   ✅ ${path.basename(videoPath)} (${result.details.sizeMB}MB)`)
    } else {
      validationResults.assets.videos.failed++
      console.log(`   ❌ ${path.basename(videoPath)} - 文件不存在`)
    }
  })
  
  console.log('')
}

// 验证系统特性
function validateFeatures() {
  console.log('🎯 验证系统特性...\n')
  
  validationResults.features.total = VALIDATION_CONFIG.expectedFeatures.length
  
  // 基于组件验证结果检查特性实现
  const allFeatures = validationResults.components.results.flatMap(r => r.features || [])
  
  VALIDATION_CONFIG.expectedFeatures.forEach(feature => {
    let implemented = false
    let evidence = []
    
    switch (feature) {
      case 'Vue.js 3组件集成':
        implemented = allFeatures.includes('Vue 3 Composition API')
        evidence = ['使用<script setup>语法']
        break
      case 'Element Plus UI组件':
        implemented = allFeatures.includes('Element Plus集成')
        evidence = ['使用el-前缀组件']
        break
      case '响应式设计':
        implemented = allFeatures.includes('响应式设计')
        evidence = ['包含@media查询或移动端适配代码']
        break
      case '中文界面本地化':
        implemented = allFeatures.includes('中文本地化')
        evidence = ['包含iFlytek和中文内容']
        break
      case '多媒体播放支持':
        implemented = allFeatures.includes('多媒体支持')
        evidence = ['包含video和img元素']
        break
      case 'Microsoft YaHei字体优化':
        implemented = allFeatures.includes('Microsoft YaHei字体')
        evidence = ['明确指定Microsoft YaHei字体']
        break
      case '移动端适配':
        implemented = validationResults.assets.images.passed > 0 && validationResults.assets.videos.passed > 0
        evidence = ['静态资源文件存在']
        break
      case '质量验证工具':
        implemented = fs.existsSync('src/utils/mediaIntegrationTest.js')
        evidence = ['mediaIntegrationTest.js文件存在']
        break
    }
    
    validationResults.features.results.push({
      feature: feature,
      implemented: implemented,
      evidence: evidence
    })
    
    if (implemented) {
      validationResults.features.implemented++
      console.log(`   ✅ ${feature}`)
    } else {
      console.log(`   ❌ ${feature}`)
    }
  })
  
  console.log('')
}

// 生成验证报告
function generateValidationReport() {
  console.log('📊 生成验证报告...\n')
  
  // 计算总体得分
  const totalItems = validationResults.components.total + 
                    validationResults.assets.images.total + 
                    validationResults.assets.videos.total +
                    validationResults.features.total
  
  const passedItems = validationResults.components.passed + 
                     validationResults.assets.images.passed + 
                     validationResults.assets.videos.passed +
                     validationResults.features.implemented
  
  validationResults.overall.score = Math.round((passedItems / totalItems) * 100)
  
  // 确定状态
  if (validationResults.overall.score >= 95) {
    validationResults.overall.status = 'excellent'
  } else if (validationResults.overall.score >= 85) {
    validationResults.overall.status = 'good'
  } else if (validationResults.overall.score >= 70) {
    validationResults.overall.status = 'fair'
  } else {
    validationResults.overall.status = 'poor'
  }
  
  // 生成问题和建议
  if (validationResults.components.failed > 0) {
    validationResults.overall.issues.push(`${validationResults.components.failed}个组件文件缺失或有问题`)
    validationResults.overall.recommendations.push('检查Vue组件文件是否正确创建')
  }
  
  if (validationResults.assets.images.failed > 0) {
    validationResults.overall.issues.push(`${validationResults.assets.images.failed}个图片文件缺失`)
    validationResults.overall.recommendations.push('运行图片生成工具: node backup-image-generator.js')
  }
  
  if (validationResults.assets.videos.failed > 0) {
    validationResults.overall.issues.push(`${validationResults.assets.videos.failed}个视频文件缺失`)
    validationResults.overall.recommendations.push('运行视频生成工具: node web-video-generator.js')
  }
  
  // 显示报告
  console.log('=' .repeat(60))
  console.log('📋 iFlytek Spark系统集成验证报告')
  console.log('=' .repeat(60))
  console.log(`🎯 总体得分: ${validationResults.overall.score}分`)
  console.log(`📊 状态: ${validationResults.overall.status}`)
  console.log('')
  
  console.log('📈 详细结果:')
  console.log(`   Vue组件: ${validationResults.components.passed}/${validationResults.components.total} 通过`)
  console.log(`   图片资源: ${validationResults.assets.images.passed}/${validationResults.assets.images.total} 通过`)
  console.log(`   视频资源: ${validationResults.assets.videos.passed}/${validationResults.assets.videos.total} 通过`)
  console.log(`   系统特性: ${validationResults.features.implemented}/${validationResults.features.total} 实现`)
  console.log('')
  
  if (validationResults.overall.issues.length > 0) {
    console.log('⚠️  发现的问题:')
    validationResults.overall.issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`)
    })
    console.log('')
  }
  
  if (validationResults.overall.recommendations.length > 0) {
    console.log('💡 建议操作:')
    validationResults.overall.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`)
    })
    console.log('')
  }
  
  // 保存报告
  fs.writeFileSync('system-integration-report.json', JSON.stringify(validationResults, null, 2))
  console.log('📄 详细报告已保存到 system-integration-report.json')
  
  return validationResults
}

// 主验证流程
async function runSystemValidation() {
  console.log('🚀 开始iFlytek Spark系统集成验证\n')
  
  try {
    // 验证组件
    validateComponents()
    
    // 验证静态资源
    validateAssets()
    
    // 验证系统特性
    validateFeatures()
    
    // 生成报告
    const report = generateValidationReport()
    
    console.log('\n🎉 系统集成验证完成！')
    
    if (report.overall.score >= 85) {
      console.log('✅ 系统集成质量良好，可以用于演示和竞赛提交')
    } else {
      console.log('⚠️  系统集成需要改进，请根据建议进行优化')
    }
    
    return report
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message)
    return null
  }
}

// 主程序
if (import.meta.url === `file://${process.argv[1]}`) {
  runSystemValidation()
}

export {
  runSystemValidation,
  validateComponents,
  validateAssets,
  validateFeatures,
  generateValidationReport
}
