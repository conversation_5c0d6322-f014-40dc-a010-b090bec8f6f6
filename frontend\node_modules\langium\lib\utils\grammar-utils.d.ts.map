{"version": 3, "file": "grammar-utils.d.ts", "sourceRoot": "", "sources": ["../../src/utils/grammar-utils.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAGhF,OAAO,KAAK,GAAG,MAAM,+BAA+B,CAAC;AACrD,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAM1D;;;GAGG;AACH,wBAAgB,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,UAAU,GAAG,SAAS,CAE7E;AAED;;GAEG;AACH,wBAAgB,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,sBAElD;AAED;;;;;;;GAOG;AACH,wBAAgB,oBAAoB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAmBvG;AAcD;;;;;;;GAOG;AACH,wBAAgB,yBAAyB,CAAC,QAAQ,EAAE,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,eAAe,GAAG,SAAS,CAQvG;AAED;;;;GAIG;AACH,wBAAgB,iBAAiB,CAAC,YAAY,EAAE,GAAG,CAAC,YAAY,GAAG,OAAO,CAEzE;AAED;;;;;GAKG;AACH,wBAAgB,oBAAoB,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS,EAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG,OAAO,EAAE,CAKvG;AAED;;;;;;;;GAQG;AACH,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS,EAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,GAAG,SAAS,CAchI;AAeD;;;;;GAKG;AACH,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAKzF;AAED;;;;;;;;GAQG;AACH,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,GAAG,SAAS,CAclH;AAED,wBAAgB,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,SAAS,GAAG,OAAO,EAAE,CAwBnH;AAED;;;;;GAKG;AACH,wBAAgB,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,GAAG,CAAC,UAAU,GAAG,SAAS,CAY3E;AAED;;;;GAIG;AACH,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,UAAU,GAAG,SAAS,CAerF;AAgCD,wBAAgB,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,MAAM,GAAG,SAAS,CAsBvF;AAED,MAAM,MAAM,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS,CAAC;AACtD,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC;AAErD,wBAAgB,qBAAqB,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,eAAe,GAAG,OAAO,CAEvG;AAED,wBAAgB,kBAAkB,CAAC,WAAW,CAAC,EAAE,WAAW,GAAG,OAAO,CAErE;AAED,wBAAgB,eAAe,CAAC,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAE5D;AAED;;;GAGG;AACH,wBAAgB,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,GAAG,OAAO,CAE5D;AA0BD,wBAAgB,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,GAAG,OAAO,CAElD;AAkCD,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,GAAG,MAAM,GAAG,SAAS,CAiB5E;AAED,wBAAgB,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,MAAM,GAAG,MAAM,CAcvE;AAED,wBAAgB,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,SAAS,CAOpE;AAED;;;;;;GAMG;AACH,wBAAgB,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,GAAG,MAAM,CAM9D;AAED;;;;;;GAMG;AACH,wBAAgB,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,GAAG,MAAM,CAM1D;AAED,wBAAgB,aAAa,CAAC,YAAY,EAAE,GAAG,CAAC,YAAY,GAAG,MAAM,CASpE"}