#!/usr/bin/env python3
"""
iFlytek Spark面试AI系统 - AI思考过程优化测试脚本
测试修复后的AI评估算法是否能正确识别详细技术回答
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.services.advanced_interviewer_service import advanced_interviewer_service

def test_technical_response_classification():
    """测试技术回答分类的准确性"""
    print("=" * 60)
    print("测试AI思考过程优化 - 技术回答分类准确性")
    print("=" * 60)
    
    # 测试用例：详细的技术回答（之前被误判为"不知道"）
    test_cases = [
        {
            "name": "Kubernetes机器学习部署详细回答",
            "response": """我是通过机器学习算法来分析用户300万的网络行为数据未来一段时间内用户的情绪走势。

在Kubernetes环境下部署机器学习模型时，版本控制确实是一个关键问题。我的实现方案包括：

1. 模型版本管理：使用语义化版本号（如v1.2.3）标记每个模型版本，结合Git进行代码版本控制
2. 容器镜像版本控制：将模型打包到Docker镜像中，使用镜像标签管理不同版本
3. 配置管理：通过ConfigMap和Secret管理模型配置文件和敏感信息
4. 部署策略：实施蓝绿部署，确保新版本模型可以快速回滚
5. 监控体系：设置模型性能监控、资源使用监控和业务指标监控
6. 数据版本控制：使用DVC（Data Version Control）管理训练数据版本

通过这套完整的版本控制体系，我们能够确保生产环境的稳定性和可追溯性。""",
            "expected_type": "confident_answer",
            "description": "包含大量技术术语和详细实现步骤的专业回答"
        },
        {
            "name": "简短但专业的技术回答",
            "response": "使用Docker容器化部署，配合Kubernetes的Deployment和Service进行版本管理，通过Helm Charts实现配置版本控制。",
            "expected_type": "confident_answer", 
            "description": "简短但包含多个专业术语的回答"
        },
        {
            "name": "明确表达不知道",
            "response": "不知道，我对Kubernetes完全不了解，也没有相关经验。",
            "expected_type": "express_unknown",
            "description": "明确表达不知道且缺乏技术内容"
        },
        {
            "name": "请求技术指导",
            "response": "请给出标准答案，我想学习Kubernetes部署的最佳实践。",
            "expected_type": "request_answer",
            "description": "明确请求答案和指导"
        }
    ]
    
    results = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['name']}")
        print(f"描述: {case['description']}")
        print(f"回答内容: {case['response'][:100]}...")
        
        # 检测回答类型
        detected_type = advanced_interviewer_service._detect_response_type(case['response'])
        
        # 验证分类准确性
        validation_result = advanced_interviewer_service.validate_response_classification(
            case['response'], detected_type, "人工智能"
        )
        
        final_type = validation_result['final_classification']
        is_correct = final_type == case['expected_type']
        
        print(f"初始分类: {detected_type}")
        print(f"最终分类: {final_type}")
        print(f"期望分类: {case['expected_type']}")
        print(f"分类正确: {'✓' if is_correct else '✗'}")
        
        if validation_result['potential_misjudgment']:
            print(f"检测到误判: {validation_result['validation_reason']}")
        
        # 分析技术内容质量
        quality_analysis = validation_result['quality_analysis']
        print(f"技术术语数量: {quality_analysis['technical_word_count']}")
        print(f"专业指标数量: {quality_analysis['professional_indicators']}")
        print(f"置信度分数: {quality_analysis['confidence_score']:.2f}")
        
        results.append({
            'case_name': case['name'],
            'detected_type': detected_type,
            'final_type': final_type,
            'expected_type': case['expected_type'],
            'is_correct': is_correct,
            'quality_analysis': quality_analysis,
            'validation_result': validation_result
        })
    
    # 统计结果
    correct_count = sum(1 for r in results if r['is_correct'])
    total_count = len(results)
    accuracy = correct_count / total_count * 100
    
    print(f"\n" + "=" * 60)
    print(f"测试结果统计:")
    print(f"总测试用例: {total_count}")
    print(f"分类正确: {correct_count}")
    print(f"分类准确率: {accuracy:.1f}%")
    print(f"=" * 60)
    
    return results

def test_ai_thinking_content_generation():
    """测试AI思考内容生成的准确性"""
    print("\n" + "=" * 60)
    print("测试AI思考内容生成准确性")
    print("=" * 60)
    
    # 模拟一个专业技术回答的分析过程
    user_response = """我通过Kubernetes部署了一个推荐系统模型，使用了以下版本控制策略：
    
1. 模型版本：使用MLflow跟踪模型版本和实验
2. 代码版本：Git + GitLab CI/CD流水线
3. 容器版本：Docker镜像标签 + Harbor镜像仓库
4. 配置版本：Helm Charts管理Kubernetes配置
5. 数据版本：DVC管理训练数据集版本
6. 监控：Prometheus + Grafana监控模型性能

通过这套体系，我们实现了模型的快速迭代和稳定部署。"""
    
    original_question = "请描述您在Kubernetes环境下部署机器学习模型时的版本控制经验。"
    
    # 分析候选人回答
    response_analysis = advanced_interviewer_service.analyze_candidate_response(
        user_response, original_question, "人工智能", "算法工程师"
    )
    
    # 验证分类准确性
    validation_result = advanced_interviewer_service.validate_response_classification(
        user_response, response_analysis['response_type'], "人工智能"
    )
    
    print(f"用户回答: {user_response[:100]}...")
    print(f"初始分类: {response_analysis['response_type']}")
    print(f"最终分类: {validation_result['final_classification']}")
    print(f"验证原因: {validation_result['validation_reason']}")
    
    # 检查是否正确识别为专业回答
    is_professional = validation_result['final_classification'] == 'confident_answer'
    print(f"正确识别为专业回答: {'✓' if is_professional else '✗'}")
    
    return {
        'response_analysis': response_analysis,
        'validation_result': validation_result,
        'is_professional': is_professional
    }

def main():
    """主测试函数"""
    print("iFlytek Spark面试AI系统 - AI思考过程优化测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 测试技术回答分类准确性
        classification_results = test_technical_response_classification()
        
        # 测试AI思考内容生成
        thinking_results = test_ai_thinking_content_generation()
        
        # 生成测试报告
        report = {
            'test_time': datetime.now().isoformat(),
            'classification_test': {
                'results': classification_results,
                'accuracy': sum(1 for r in classification_results if r['is_correct']) / len(classification_results) * 100
            },
            'thinking_generation_test': thinking_results,
            'overall_status': 'PASSED' if all(r['is_correct'] for r in classification_results) and thinking_results['is_professional'] else 'FAILED'
        }
        
        # 保存测试报告
        with open('ai_thinking_optimization_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n测试完成！整体状态: {report['overall_status']}")
        print("详细测试报告已保存到: ai_thinking_optimization_test_report.json")
        
        return report['overall_status'] == 'PASSED'
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
