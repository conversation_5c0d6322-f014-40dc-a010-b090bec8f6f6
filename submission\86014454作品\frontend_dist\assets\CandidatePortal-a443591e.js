import{_ as hs,r as D,h as f,a2 as vs,o as fs,i as k,j as a,k as c,n as s,p as e,w as i,N as u,T as cs,t as ys,z as n,F as p,x as h,R as Q,B as S,C as H,A as P,W as rs,M as _,H as ss,d as es,y as ds,a5 as gs,m as Ls,E as ts,G as Fs,ap as E,aq as B,S as Ns,a9 as Us,ar as ms,c as Xs,X as ps}from"./index-b6a2842e.js";import{M as Hs,P as Qs}from"./PersonalizedRecommendationEngine-925273b6.js";const Ys={class:"candidate-experience-optimization"},Os={class:"experience-navigation"},Js={class:"tab-label"},Ks={class:"tab-label"},Ws={class:"tab-label"},Gs={key:0,class:"feature-panel interview-prep-panel"},Zs={class:"panel-header"},se={class:"prep-status"},ee={class:"progress-text"},te={class:"preparation-overview"},oe={class:"overview-cards"},le={class:"prep-content"},ne={class:"prep-value"},ie={class:"prep-label"},ae={class:"personalized-plan"},de={class:"plan-header"},ce={class:"plan-controls"},re={class:"plan-timeline"},ue={class:"marker-number"},_e={class:"timeline-content"},ve={class:"timeline-header"},me={class:"timeline-meta"},pe={class:"timeline-duration"},he={class:"timeline-description"},fe={class:"timeline-resources"},ye={class:"timeline-actions"},ge={class:"mock-interview"},ke={class:"mock-header"},$e={class:"mock-stats"},be={class:"stat-item"},we={class:"stat-item"},xe={class:"mock-scenarios"},Ce={class:"scenario-header"},Se={class:"scenario-title"},Ve={class:"scenario-difficulty"},Me={class:"scenario-details"},Ie={class:"scenario-description"},Pe={class:"scenario-features"},Te={class:"scenario-stats"},ze={class:"stat-item"},Ae={class:"stat-value"},De={class:"stat-item"},je={class:"stat-value"},Re={class:"stat-item"},qe={class:"stat-value"},Ee={class:"scenario-actions"},Be={key:1,class:"feature-panel guidance-panel"},Le={class:"panel-header"},Fe={class:"guidance-status"},Ne={class:"real-time-monitoring"},Ue={class:"monitoring-header"},Xe={class:"monitoring-controls"},He={class:"monitoring-grid"},Qe={class:"monitor-card speech-monitor"},Ye={class:"monitor-header"},Oe={class:"monitor-content"},Je={class:"voice-waveform"},Ke={class:"speech-metrics"},We={class:"metric-item"},Ge={class:"metric-item"},Ze={class:"metric-item"},st={class:"monitor-card visual-monitor"},et={class:"monitor-header"},tt={class:"monitor-content"},ot={class:"visual-feedback"},lt={class:"feedback-text"},nt={class:"feedback-label"},it={class:"feedback-value"},at={class:"monitor-card content-monitor"},dt={class:"monitor-header"},ct={class:"monitor-content"},rt={class:"content-analysis"},ut={class:"analysis-circle"},_t={class:"circle-text"},vt={class:"quality-breakdown"},mt={class:"quality-name"},pt={class:"quality-bar"},ht={class:"quality-score"},ft={class:"intelligent-hints"},yt={class:"hints-header"},gt={class:"hints-mode"},kt={class:"hints-container"},$t={class:"hint-header"},bt={class:"hint-meta"},wt={class:"hint-category"},xt={class:"hint-time"},Ct={class:"hint-content"},St={class:"hint-actions"},Vt={key:2,class:"feature-panel skill-panel"},Mt={class:"panel-header"},It={class:"skill-level"},Pt={class:"skill-assessment"},Tt={class:"assessment-header"},zt={class:"assessment-date"},At={class:"skill-radar"},Dt={class:"radar-chart"},jt={viewBox:"0 0 300 300",class:"radar-svg"},Rt={class:"radar-grid"},qt=["r"],Et=["x2","y2"],Bt=["x","y"],Lt=["points"],Ft=["cx","cy"],Nt={class:"skill-legend"},Ut={class:"legend-text"},Xt={class:"learning-path"},Ht={class:"path-header"},Qt={class:"path-filter"},Yt={class:"learning-modules"},Ot={class:"module-header"},Jt={class:"module-info"},Kt={class:"module-meta"},Wt={class:"module-duration"},Gt={class:"module-difficulty"},Zt={class:"module-progress"},so={class:"module-content"},eo={class:"module-description"},to={class:"module-skills"},oo={class:"module-actions"},lo={__name:"CandidateExperienceOptimization",setup(ks){const w=D("interview-prep"),$=D("frontend"),L=D(!0),Y=D("active"),O=D(3),C=D("2024-01-15"),M=D(68),os=f([{name:"知识点掌握",value:"85%",status:"good",statusText:"良好",color:"#2ecc71",icon:"Notebook"},{name:"模拟练习",value:"12次",status:"excellent",statusText:"优秀",color:"#3498db",icon:"Trophy"},{name:"薄弱环节",value:"3个",status:"attention",statusText:"需关注",color:"#f39c12",icon:"Warning"},{name:"预计通过率",value:"78%",status:"good",statusText:"良好",color:"#e74c3c",icon:"TrendCharts"}]),ls=f([{id:1,title:"JavaScript基础知识复习",description:"复习ES6+语法、原型链、闭包等核心概念",difficulty:"简单",duration:"2小时",completed:!0,current:!1,resources:[{name:"MDN文档",icon:"Document"},{name:"在线练习",icon:"Monitor"},{name:"视频教程",icon:"VideoPlay"}]},{id:2,title:"Vue.js框架深入理解",description:"掌握Vue3 Composition API、响应式原理、组件设计模式",difficulty:"中等",duration:"4小时",completed:!1,current:!0,resources:[{name:"官方文档",icon:"Document"},{name:"实战项目",icon:"FolderOpened"},{name:"源码分析",icon:"View"}]},{id:3,title:"算法与数据结构",description:"常见算法题型练习，时间复杂度分析",difficulty:"困难",duration:"6小时",completed:!1,current:!1,resources:[{name:"LeetCode",icon:"Monitor"},{name:"算法书籍",icon:"Reading"},{name:"视频讲解",icon:"VideoPlay"}]}]),J=f({completed:8,averageScore:4.2}),ns=f([{id:1,title:"技术基础面试",type:"technical",typeText:"技术面",description:"考察JavaScript、Vue.js等前端技术基础",difficulty:3,duration:45,participants:1247,passRate:78,features:["代码编写","概念解释","项目经验"]},{id:2,title:"算法编程挑战",type:"algorithm",typeText:"算法面",description:"在线编程解决算法问题，考察编程思维",difficulty:4,duration:60,participants:892,passRate:65,features:["算法设计","代码实现","复杂度分析"]},{id:3,title:"项目经验分享",type:"project",typeText:"项目面",description:"深入讨论项目经验，技术选型和解决方案",difficulty:3,duration:30,participants:1563,passRate:82,features:["项目介绍","技术难点","团队协作"]}]),K=f({type:"success",text:"系统正常运行"}),x=f({speed:180,volume:75,clarity:88}),F=f([{type:"eye-contact",label:"眼神交流",value:"良好",status:"good",icon:"View"},{type:"posture",label:"坐姿端正",value:"优秀",status:"excellent",icon:"User"},{type:"expression",label:"表情自然",value:"需改善",status:"attention",icon:"Smile"}]),T=f({overall:82,breakdown:[{name:"相关性",score:85},{name:"深度",score:78},{name:"逻辑性",score:88},{name:"创新性",score:75}]}),A=f([{id:1,category:"语音表达",title:"语速建议",message:"当前语速略快，建议适当放慢以提高表达清晰度",priority:"medium",icon:"Microphone",timestamp:new Date},{id:2,category:"内容深度",title:"技术细节",message:"可以增加更多技术实现细节，展示深度理解",priority:"high",icon:"Document",timestamp:new Date}]),j=f({timeframe:"3months",intensity:"moderate"}),W=f([{name:"JavaScript",score:85},{name:"Vue.js",score:78},{name:"算法思维",score:72},{name:"系统设计",score:65},{name:"项目经验",score:80},{name:"沟通表达",score:75}]),G=f([{name:"JavaScript",angle:0,x:275,y:150,labelX:285,labelY:155},{name:"Vue.js",angle:60,x:212.5,y:66.5,labelX:220,labelY:55},{name:"算法思维",angle:120,x:87.5,y:66.5,labelX:80,labelY:55},{name:"系统设计",angle:180,x:25,y:150,labelX:15,labelY:155},{name:"项目经验",angle:240,x:87.5,y:233.5,labelX:80,labelY:245},{name:"沟通表达",angle:300,x:212.5,y:233.5,labelX:220,labelY:245}]),y=f([{id:1,title:"Vue.js进阶开发",description:"深入学习Vue3 Composition API、状态管理、性能优化",duration:"4周",difficulty:"中级",progress:35,color:"#4fc08d",icon:"Monitor",skills:["Vue3","Composition API","Pinia","性能优化"]},{id:2,title:"算法与数据结构",description:"系统学习常用算法和数据结构，提升编程思维",duration:"6周",difficulty:"高级",progress:0,color:"#f39c12",icon:"TrendCharts",skills:["算法设计","数据结构","复杂度分析","编程思维"]},{id:3,title:"前端工程化实践",description:"学习现代前端工程化工具和最佳实践",duration:"3周",difficulty:"中级",progress:60,color:"#3498db",icon:"Setting",skills:["Webpack","Vite","CI/CD","代码规范"]}]),l=vs(()=>G.map((d,t)=>{const q=W[t].score/100*125,Z=(d.angle-90)*(Math.PI/180);return{x:150+q*Math.cos(Z),y:150+q*Math.sin(Z)}})),v=vs(()=>l.value.map(d=>`${d.x},${d.y}`).join(" ")),b=d=>{console.log("切换到标签:",d)},z=d=>d>=80?"#2ecc71":d>=60?"#f39c12":"#e74c3c",N=d=>({简单:"success",中等:"warning",困难:"danger"})[d]||"info",r=d=>({technical:"primary",algorithm:"warning",project:"success"})[d]||"info",R=d=>d<150?"slow":d>200?"fast":"normal",$s=d=>d<50?"low":d>80?"high":"normal",bs=d=>d>=85?"excellent":d>=70?"good":"poor",ws=d=>d>=85?"#2ecc71":d>=70?"#f39c12":"#e74c3c",xs=d=>d.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),Cs=()=>{console.log("更新准备计划，目标职位:",$.value)},Ss=()=>{console.log("生成AI定制计划")},Vs=d=>{console.log("开始准备项目:",d.title),d.current=!0},Ms=d=>{console.log("查看准备详情:",d.title)},Is=d=>{console.log("开始模拟面试:",d.title)},Ps=d=>{console.log("查看场景详情:",d.title)},Ts=()=>{console.log("校准系统")},zs=d=>{console.log("应用提示:",d.title);const t=A.findIndex(m=>m.id===d.id);t>-1&&A.splice(t,1)},As=d=>{console.log("忽略提示:",d.title);const t=A.findIndex(m=>m.id===d.id);t>-1&&A.splice(t,1)},Ds=()=>{console.log("开始新的技能评估")},js=d=>{console.log("🎓 开始学习模块:",d.title);try{switch(E.success(`正在启动 ${d.title} 学习模块...`),d.id){case 1:B.success({title:"🚀 Vue.js进阶开发",message:"开始您的Vue.js学习之旅！",duration:3e3});break;case 2:B.success({title:"📊 算法与数据结构",message:"开始算法学习！",duration:3e3});break;case 3:B.success({title:"🔧 前端工程化实践",message:"开始工程化学习！",duration:3e3});break;default:E.info(`${d.title} 学习模块正在开发中...`)}d.progress===0&&(d.progress=5)}catch(t){console.error("❌ 启动学习模块失败:",t),E.error("启动学习模块失败，请重试")}},Rs=d=>{console.log("查看模块详情:",d.title)};return fs(()=>{console.log("候选人体验优化组件已加载"),setInterval(()=>{L.value&&(document.querySelectorAll(".wave-bar").forEach(t=>{const m=Math.random()*30+5;t.style.height=m+"px"}),x.speed=Math.max(120,Math.min(220,x.speed+(Math.random()-.5)*10)),x.volume=Math.max(40,Math.min(90,x.volume+(Math.random()-.5)*8)),x.clarity=Math.max(60,Math.min(95,x.clarity+(Math.random()-.5)*6)),T.overall=Math.max(60,Math.min(95,T.overall+(Math.random()-.5)*4)),T.breakdown.forEach(t=>{t.score=Math.max(60,Math.min(95,t.score+(Math.random()-.5)*5))}))},2e3)}),(d,t)=>{const m=k("el-icon"),q=k("el-tab-pane"),Z=k("QuestionFilled"),qs=k("el-tabs"),us=k("el-progress"),I=k("el-option"),is=k("el-select"),V=k("el-button"),U=k("el-tag"),_s=k("el-rate"),Es=k("el-switch"),as=k("el-radio-button"),Bs=k("el-radio-group");return a(),c("div",Ys,[s("div",Os,[e(qs,{modelValue:w.value,"onUpdate:modelValue":t[0]||(t[0]=o=>w.value=o),type:"card",onTabChange:b},{default:i(()=>[e(q,{label:"面试准备助手",name:"interview-prep"},{label:i(()=>[s("div",Js,[e(m,null,{default:i(()=>[e(u(cs))]),_:1}),t[7]||(t[7]=s("span",null,"面试准备助手",-1))])]),_:1}),e(q,{label:"实时指导",name:"real-time-guidance"},{label:i(()=>[s("div",Ks,[e(m,null,{default:i(()=>[e(Z)]),_:1}),t[8]||(t[8]=s("span",null,"实时指导",-1))])]),_:1}),e(q,{label:"技能提升",name:"skill-improvement"},{label:i(()=>[s("div",Ws,[e(m,null,{default:i(()=>[e(u(ys))]),_:1}),t[9]||(t[9]=s("span",null,"技能提升",-1))])]),_:1})]),_:1},8,["modelValue"])]),w.value==="interview-prep"?(a(),c("div",Gs,[s("div",Zs,[t[10]||(t[10]=s("h4",null,"iFlytek Spark 面试准备助手",-1)),s("div",se,[e(us,{percentage:M.value,color:z(M.value),"show-text":!1},null,8,["percentage","color"]),s("span",ee,"准备进度: "+n(M.value)+"%",1)])]),s("div",te,[s("div",oe,[(a(!0),c(p,null,h(os,o=>(a(),c("div",{class:"prep-card",key:o.name},[s("div",{class:"prep-icon",style:Q({backgroundColor:o.color})},[e(m,null,{default:i(()=>[(a(),S(H(o.icon)))]),_:2},1024)],4),s("div",le,[s("div",ne,n(o.value),1),s("div",ie,n(o.name),1),s("div",{class:P(["prep-status",o.status])},n(o.statusText),3)])]))),128))])]),s("div",ae,[s("div",de,[t[12]||(t[12]=s("h5",null,"个性化准备计划",-1)),s("div",ce,[e(is,{modelValue:$.value,"onUpdate:modelValue":t[1]||(t[1]=o=>$.value=o),placeholder:"选择目标职位",onChange:Cs},{default:i(()=>[e(I,{label:"前端工程师",value:"frontend"}),e(I,{label:"后端工程师",value:"backend"}),e(I,{label:"全栈工程师",value:"fullstack"}),e(I,{label:"算法工程师",value:"algorithm"})]),_:1},8,["modelValue"]),e(V,{type:"primary",onClick:Ss},{default:i(()=>[e(m,null,{default:i(()=>[e(u(rs))]),_:1}),t[11]||(t[11]=_(" AI定制计划 "))]),_:1,__:[11]})])]),s("div",re,[(a(!0),c(p,null,h(ls,(o,g)=>(a(),c("div",{class:"timeline-item",key:o.id},[s("div",{class:P(["timeline-marker",{completed:o.completed,current:o.current}])},[s("span",ue,n(g+1),1)],2),s("div",_e,[s("div",ve,[s("h6",null,n(o.title),1),s("div",me,[e(U,{type:N(o.difficulty),size:"small"},{default:i(()=>[_(n(o.difficulty),1)]),_:2},1032,["type"]),s("span",pe,n(o.duration),1)])]),s("div",he,n(o.description),1),s("div",fe,[(a(!0),c(p,null,h(o.resources,X=>(a(),c("div",{class:"resource-item",key:X.name},[e(m,null,{default:i(()=>[(a(),S(H(X.icon)))]),_:2},1024),s("span",null,n(X.name),1)]))),128))]),s("div",ye,[o.completed?(a(),S(V,{key:1,size:"small",disabled:""},{default:i(()=>[e(m,null,{default:i(()=>[e(u(ss))]),_:1}),t[13]||(t[13]=_(" 已完成 "))]),_:1,__:[13]})):(a(),S(V,{key:0,size:"small",type:"primary",onClick:X=>Vs(o)},{default:i(()=>[_(n(o.current?"继续学习":"开始准备"),1)]),_:2},1032,["onClick"])),e(V,{size:"small",onClick:X=>Ms(o)},{default:i(()=>t[14]||(t[14]=[_(" 查看详情 ")])),_:2,__:[14]},1032,["onClick"])])])]))),128))])]),s("div",ge,[s("div",ke,[t[15]||(t[15]=s("h5",null,"模拟面试练习",-1)),s("div",$e,[s("span",be,[e(m,null,{default:i(()=>[e(u(es))]),_:1}),_(" 已完成: "+n(J.completed)+"次 ",1)]),s("span",we,[e(m,null,{default:i(()=>[e(u(rs))]),_:1}),_(" 平均分: "+n(J.averageScore),1)])])]),s("div",xe,[(a(!0),c(p,null,h(ns,o=>(a(),c("div",{class:"scenario-card",key:o.id},[s("div",Ce,[s("div",Se,[s("h6",null,n(o.title),1),e(U,{type:r(o.type)},{default:i(()=>[_(n(o.typeText),1)]),_:2},1032,["type"])]),s("div",Ve,[e(_s,{modelValue:o.difficulty,"onUpdate:modelValue":g=>o.difficulty=g,disabled:"","show-score":""},null,8,["modelValue","onUpdate:modelValue"])])]),s("div",Me,[s("div",Ie,n(o.description),1),s("div",Pe,[(a(!0),c(p,null,h(o.features,g=>(a(),S(U,{key:g,size:"small"},{default:i(()=>[_(n(g),1)]),_:2},1024))),128))])]),s("div",Te,[s("div",ze,[t[16]||(t[16]=s("span",{class:"stat-label"},"预计时长:",-1)),s("span",Ae,n(o.duration)+"分钟",1)]),s("div",De,[t[17]||(t[17]=s("span",{class:"stat-label"},"参与人数:",-1)),s("span",je,n(o.participants)+"人",1)]),s("div",Re,[t[18]||(t[18]=s("span",{class:"stat-label"},"通过率:",-1)),s("span",qe,n(o.passRate)+"%",1)])]),s("div",Ee,[e(V,{type:"primary",onClick:g=>Is(o)},{default:i(()=>t[19]||(t[19]=[_(" 开始模拟面试 ")])),_:2,__:[19]},1032,["onClick"]),e(V,{onClick:g=>Ps(o)},{default:i(()=>t[20]||(t[20]=[_(" 查看详情 ")])),_:2,__:[20]},1032,["onClick"])])]))),128))])])])):ds("",!0),w.value==="real-time-guidance"?(a(),c("div",Be,[s("div",Le,[t[21]||(t[21]=s("h4",null,"实时面试指导系统",-1)),s("div",Fe,[e(U,{type:K.type,size:"large"},{default:i(()=>[_(n(K.text),1)]),_:1},8,["type"])])]),s("div",Ne,[s("div",Ue,[t[23]||(t[23]=s("h5",null,"实时表现监测",-1)),s("div",Xe,[e(Es,{modelValue:L.value,"onUpdate:modelValue":t[2]||(t[2]=o=>L.value=o),"active-text":"开启监测"},null,8,["modelValue"]),e(V,{size:"small",onClick:Ts},{default:i(()=>[e(m,null,{default:i(()=>[e(u(gs))]),_:1}),t[22]||(t[22]=_(" 校准系统 "))]),_:1,__:[22]})])]),s("div",He,[s("div",Qe,[s("div",Ye,[e(m,null,{default:i(()=>[e(u(Ls))]),_:1}),t[24]||(t[24]=s("span",null,"语音表现",-1))]),s("div",Oe,[s("div",Je,[(a(),c(p,null,h(8,o=>s("div",{class:"wave-bar",key:o})),64))]),s("div",Ke,[s("div",We,[t[25]||(t[25]=s("span",{class:"metric-label"},"语速:",-1)),s("div",{class:P(["metric-indicator",R(x.speed)])},n(x.speed)+"字/分 ",3)]),s("div",Ge,[t[26]||(t[26]=s("span",{class:"metric-label"},"音量:",-1)),s("div",{class:P(["metric-indicator",$s(x.volume)])},n(x.volume)+"% ",3)]),s("div",Ze,[t[27]||(t[27]=s("span",{class:"metric-label"},"清晰度:",-1)),s("div",{class:P(["metric-indicator",bs(x.clarity)])},n(x.clarity)+"% ",3)])])])]),s("div",st,[s("div",et,[e(m,null,{default:i(()=>[e(u(ts))]),_:1}),t[28]||(t[28]=s("span",null,"视觉表现",-1))]),s("div",tt,[s("div",ot,[(a(!0),c(p,null,h(F,o=>(a(),c("div",{class:"feedback-item",key:o.type},[s("div",{class:P(["feedback-icon",o.status])},[e(m,null,{default:i(()=>[(a(),S(H(o.icon)))]),_:2},1024)],2),s("div",lt,[s("span",nt,n(o.label),1),s("span",it,n(o.value),1)])]))),128))])])]),s("div",at,[s("div",dt,[e(m,null,{default:i(()=>[e(u(Fs))]),_:1}),t[29]||(t[29]=s("span",null,"回答质量",-1))]),s("div",ct,[s("div",rt,[s("div",ut,[s("div",{class:"circle-progress",style:Q({"--progress":T.overall+"%"})},[s("span",_t,n(T.overall)+"%",1),t[30]||(t[30]=s("small",null,"综合评分",-1))],4)]),s("div",vt,[(a(!0),c(p,null,h(T.breakdown,o=>(a(),c("div",{class:"quality-item",key:o.name},[s("span",mt,n(o.name),1),s("div",pt,[s("div",{class:"quality-fill",style:Q({width:o.score+"%",backgroundColor:ws(o.score)})},null,4)]),s("span",ht,n(o.score)+"%",1)]))),128))])])])])])]),s("div",ft,[s("div",yt,[t[34]||(t[34]=s("h5",null,"智能提示与建议",-1)),s("div",gt,[e(Bs,{modelValue:Y.value,"onUpdate:modelValue":t[3]||(t[3]=o=>Y.value=o),size:"small"},{default:i(()=>[e(as,{value:"gentle"},{default:i(()=>t[31]||(t[31]=[_("温和提示")])),_:1,__:[31]}),e(as,{value:"active"},{default:i(()=>t[32]||(t[32]=[_("主动建议")])),_:1,__:[32]}),e(as,{value:"silent"},{default:i(()=>t[33]||(t[33]=[_("静默模式")])),_:1,__:[33]})]),_:1},8,["modelValue"])])]),s("div",kt,[(a(!0),c(p,null,h(A,o=>(a(),c("div",{class:"hint-card",key:o.id},[s("div",$t,[s("div",{class:P(["hint-type",o.priority])},[e(m,null,{default:i(()=>[(a(),S(H(o.icon)))]),_:2},1024)],2),s("div",bt,[s("span",wt,n(o.category),1),s("span",xt,n(xs(o.timestamp)),1)])]),s("div",Ct,[s("h6",null,n(o.title),1),s("p",null,n(o.message),1)]),s("div",St,[e(V,{size:"small",type:"primary",onClick:g=>zs(o)},{default:i(()=>t[35]||(t[35]=[_(" 应用建议 ")])),_:2,__:[35]},1032,["onClick"]),e(V,{size:"small",onClick:g=>As(o)},{default:i(()=>t[36]||(t[36]=[_(" 忽略 ")])),_:2,__:[36]},1032,["onClick"])])]))),128))])])])):ds("",!0),w.value==="skill-improvement"?(a(),c("div",Vt,[s("div",Mt,[t[38]||(t[38]=s("h4",null,"个性化技能提升计划",-1)),s("div",It,[t[37]||(t[37]=s("span",{class:"level-label"},"当前水平:",-1)),e(_s,{modelValue:O.value,"onUpdate:modelValue":t[4]||(t[4]=o=>O.value=o),disabled:"","show-text":""},null,8,["modelValue"])])]),s("div",Pt,[s("div",Tt,[t[40]||(t[40]=s("h5",null,"技能评估报告",-1)),s("div",zt,[s("span",null,"最近评估: "+n(C.value),1),e(V,{size:"small",type:"primary",onClick:Ds},{default:i(()=>t[39]||(t[39]=[_(" 重新评估 ")])),_:1,__:[39]})])]),s("div",At,[s("div",Dt,[(a(),c("svg",jt,[s("g",Rt,[(a(),c(p,null,h(5,o=>s("circle",{key:o,cx:"150",cy:"150",r:o*25,stroke:"#e9ecef","stroke-width":"1",fill:"none"},null,8,qt)),64)),(a(!0),c(p,null,h(G,(o,g)=>(a(),c("g",{key:o.name},[s("line",{x1:"150",y1:"150",x2:o.x,y2:o.y,stroke:"#e9ecef","stroke-width":"1"},null,8,Et),s("text",{x:o.labelX,y:o.labelY,"text-anchor":"middle",class:"axis-label"},n(o.name),9,Bt)]))),128))]),s("polygon",{points:v.value,fill:"rgba(33, 111, 255, 0.3)",stroke:"var(--iflytek-primary)","stroke-width":"2"},null,8,Lt),(a(!0),c(p,null,h(l.value,(o,g)=>(a(),c("g",{key:g},[s("circle",{cx:o.x,cy:o.y,r:"4",fill:"var(--iflytek-primary)"},null,8,Ft)]))),128))]))]),s("div",Nt,[(a(!0),c(p,null,h(W,(o,g)=>(a(),c("div",{class:"legend-item",key:o.name},[t[41]||(t[41]=s("span",{class:"legend-color"},null,-1)),s("span",Ut,n(o.name)+": "+n(o.score)+"%",1)]))),128))])])]),s("div",Xt,[s("div",Ht,[t[42]||(t[42]=s("h5",null,"智能学习路径",-1)),s("div",Qt,[e(is,{modelValue:j.timeframe,"onUpdate:modelValue":t[5]||(t[5]=o=>j.timeframe=o),placeholder:"学习周期"},{default:i(()=>[e(I,{label:"1个月",value:"1month"}),e(I,{label:"3个月",value:"3months"}),e(I,{label:"6个月",value:"6months"})]),_:1},8,["modelValue"]),e(is,{modelValue:j.intensity,"onUpdate:modelValue":t[6]||(t[6]=o=>j.intensity=o),placeholder:"学习强度"},{default:i(()=>[e(I,{label:"轻松",value:"light"}),e(I,{label:"适中",value:"moderate"}),e(I,{label:"密集",value:"intensive"})]),_:1},8,["modelValue"])])]),s("div",Yt,[(a(!0),c(p,null,h(y,o=>(a(),c("div",{class:"module-card",key:o.id},[s("div",Ot,[s("div",{class:"module-icon",style:Q({backgroundColor:o.color})},[e(m,null,{default:i(()=>[(a(),S(H(o.icon)))]),_:2},1024)],4),s("div",Jt,[s("h6",null,n(o.title),1),s("div",Kt,[s("span",Wt,n(o.duration),1),s("span",Gt,n(o.difficulty),1)])]),s("div",Zt,[e(us,{percentage:o.progress,type:"circle",width:50,"show-text":!1},null,8,["percentage"]),s("small",null,n(o.progress)+"%",1)])]),s("div",so,[s("div",eo,n(o.description),1),s("div",to,[(a(!0),c(p,null,h(o.skills,g=>(a(),S(U,{key:g,size:"small"},{default:i(()=>[_(n(g),1)]),_:2},1024))),128))])]),s("div",oo,[e(V,{type:"primary",onClick:g=>js(o)},{default:i(()=>[_(n(o.progress>0?"继续学习":"开始学习"),1)]),_:2},1032,["onClick"]),e(V,{onClick:g=>Rs(o)},{default:i(()=>t[43]||(t[43]=[_(" 查看详情 ")])),_:2,__:[43]},1032,["onClick"])])]))),128))])])])):ds("",!0)])}}},no=hs(lo,[["__scopeId","data-v-43576b03"]]);const io={class:"candidate-portal"},ao={class:"candidate-header"},co={class:"header-container"},ro={class:"brand-section"},uo={class:"brand-logo"},_o={class:"header-actions"},vo={class:"candidate-overview"},mo={class:"overview-container"},po={class:"stats-grid"},ho={class:"stat-card primary"},fo={class:"stat-header"},yo={class:"stat-trend up"},go={class:"stat-content"},ko={class:"stat-value"},$o={class:"stat-detail"},bo={class:"stat-card success"},wo={class:"stat-header"},xo={class:"stat-trend up"},Co={class:"stat-content"},So={class:"stat-value"},Vo={class:"stat-detail"},Mo={class:"stat-card warning"},Io={class:"stat-header"},Po={class:"stat-badge"},To={class:"stat-content"},zo={class:"stat-value"},Ao={class:"stat-detail"},Do={class:"stat-card info"},jo={class:"stat-header"},Ro={class:"stat-trend up"},qo={class:"stat-content"},Eo={class:"stat-value"},Bo={class:"stat-detail"},Lo={class:"candidate-modules"},Fo={class:"modules-container"},No={class:"module-section"},Uo={class:"module-grid"},Xo={class:"module-icon practice"},Ho={class:"module-content"},Qo={class:"module-stats"},Yo={class:"module-icon assistant"},Oo={class:"module-content"},Jo={class:"module-stats"},Ko={class:"module-icon assessment"},Wo={class:"module-content"},Go={class:"module-stats"},Zo={class:"module-section multimodal-training-section"},sl={class:"training-container"},el={class:"module-section ai-assistant-section"},tl={class:"ai-assistant-grid"},ol={class:"assistant-card voice-assistant"},ll={class:"assistant-header"},nl={class:"assistant-icon"},il={class:"assistant-demo"},al={class:"voice-status"},dl={class:"voice-wave-candidate"},cl={class:"status-info"},rl={class:"status-text"},ul={class:"confidence-meter"},_l={class:"confidence-bar"},vl={class:"confidence-value"},ml={class:"assistant-card emotion-assistant"},pl={class:"assistant-header"},hl={class:"assistant-icon"},fl={class:"assistant-demo"},yl={class:"emotion-monitor"},gl={class:"emotion-circle"},kl={class:"emotion-score"},$l={class:"emotion-suggestions"},bl={class:"suggestion-text"},wl={class:"assistant-card answer-assistant"},xl={class:"assistant-header"},Cl={class:"assistant-icon"},Sl={class:"assistant-demo"},Vl={class:"answer-hints"},Ml={class:"current-question"},Il={class:"question-type"},Pl={class:"hint-keywords"},Tl={class:"keywords-list"},zl={class:"module-section personalized-learning-section"},Al={class:"personalized-container"},Dl={class:"module-section"},jl={class:"learning-path-grid"},Rl={class:"learning-card"},ql={class:"learning-header"},El={class:"progress-info"},Bl={class:"learning-content"},Ll={class:"learning-modules"},Fl={class:"module-name"},Nl={class:"learning-card"},Ul={class:"learning-header"},Xl={class:"progress-info"},Hl={class:"learning-content"},Ql={class:"learning-modules"},Yl={class:"module-name"},Ol={class:"module-section"},Jl={class:"recommendations-grid"},Kl={class:"recommendation-card"},Wl={class:"recommendation-header"},Gl={class:"recommendation-content"},Zl={class:"job-info"},sn={class:"job-title"},en={class:"job-company"},tn={class:"job-match"},on={class:"recommendation-card"},ln={class:"recommendation-header"},nn={class:"recommendation-content"},an={class:"course-info"},dn={class:"course-title"},cn={class:"course-description"},rn={class:"course-difficulty"},un={class:"module-section candidate-experience-section"},_n={class:"experience-container"},vn={class:"module-section"},mn={class:"history-analysis"},pn={class:"history-timeline"},hn={class:"timeline-content"},fn={class:"interview-title"},yn={class:"interview-company"},gn={class:"interview-score"},kn={class:"interview-date"},$n={__name:"CandidatePortal",setup(ks){const w=Ns(),$=f({totalInterviews:23,weeklyInterviews:5,avgScore:82.5,bestScore:94,scoreImprovement:12.3,skillLevel:"中级",skillProgress:68,nextLevelPoints:320,achievements:8,monthlyAchievements:2,newAchievements:1,improvementRate:15.8}),L=f({totalSessions:23,weeklyGoal:5,completionRate:85}),Y=f({successRate:78,totalAssists:156,avgHelpTime:3.2}),O=f({currentLevel:"中级",totalSkills:12,masteredSkills:8}),C=f({voiceStatus:"语音识别中...",voiceClarity:88,emotionScore:85,currentQuestionType:"技术能力评估",keywords:["算法优化","数据结构","时间复杂度","系统设计"],suggestions:[{id:1,text:"保持自然的语速"},{id:2,text:"适当使用手势表达"},{id:3,text:"回答要点明确"}]}),M=f({ai:{progress:68,modules:[{id:1,name:"机器学习基础",completed:!0},{id:2,name:"深度学习框架",completed:!0},{id:3,name:"计算机视觉",completed:!1},{id:4,name:"自然语言处理",completed:!1}]},bigdata:{progress:45,modules:[{id:1,name:"Hadoop生态系统",completed:!0},{id:2,name:"Spark数据处理",completed:!1},{id:3,name:"实时流处理",completed:!1},{id:4,name:"数据仓库设计",completed:!1}]}}),os=f([{id:1,title:"AI算法工程师",company:"腾讯科技",matchScore:92,salary:"25-40K"},{id:2,title:"机器学习工程师",company:"字节跳动",matchScore:88,salary:"30-50K"},{id:3,title:"深度学习研究员",company:"百度AI",matchScore:85,salary:"35-60K"}]),ls=f([{id:1,title:"PyTorch深度学习实战",description:"从零开始学习PyTorch框架",difficulty:"中级"},{id:2,title:"计算机视觉项目实践",description:"通过实际项目掌握CV技能",difficulty:"高级"}]),J=f([{id:1,position:"AI算法工程师",company:"阿里巴巴",score:85,date:new Date(Date.now()-1e3*60*60*24*2)},{id:2,position:"机器学习工程师",company:"腾讯",score:78,date:new Date(Date.now()-1e3*60*60*24*5)},{id:3,position:"数据科学家",company:"字节跳动",score:92,date:new Date(Date.now()-1e3*60*60*24*7)}]),ns=()=>{w.push("/practice-interview")},K=()=>{w.push("/learning-path")},x=()=>{w.push("/candidate-profile")},F=y=>{w.push(y)},T=y=>{console.log("申请职位:",y)},A=y=>{console.log("🎓 开始学习课程:",y);try{switch(E.success("正在启动学习课程..."),y){case 1:B.success({title:"🚀 Vue.js进阶开发",message:"开始您的Vue.js学习之旅！",duration:3e3}),w.push("/learning-path/vue-advanced");break;case 2:B.success({title:"📊 算法与数据结构",message:"开始算法学习！",duration:3e3}),w.push("/learning-path/algorithms");break;case 3:B.success({title:"🔧 前端工程化实践",message:"开始工程化学习！",duration:3e3}),w.push("/learning-path/frontend-engineering");break;default:E.info("该课程正在开发中..."),w.push(`/course/${y}`)}}catch(l){console.error("❌ 启动学习课程失败:",l),E.error("启动学习课程失败，请重试")}},j=y=>{w.push(`/interview-detail/${y}`)},W=y=>y>=90?"excellent":y>=80?"good":y>=70?"average":"poor",G=y=>{const v=new Date-y,b=Math.floor(v/(1e3*60*60*24));return b===0?"今天":b===1?"昨天":`${b}天前`};return fs(()=>{console.log("求职者中心已加载"),setTimeout(()=>{document.querySelectorAll(".voice-wave-candidate .wave-bar").forEach((N,r)=>{setInterval(()=>{const R=Math.random()*25+10;N.style.height=R+"px"},180+r*40)});const l=["语音识别中...","检测到技术关键词","语音清晰度良好","建议放慢语速","表达逻辑清晰"],v=["技术能力评估","项目经验询问","算法思维考察","系统设计问题"];let b=0,z=0;setInterval(()=>{b=(b+1)%l.length,C.voiceStatus=l[b],C.voiceClarity=88+Math.floor((Math.random()-.5)*12),C.emotionScore=85+Math.floor((Math.random()-.5)*10)},2500),setInterval(()=>{z=(z+1)%v.length,C.currentQuestionType=v[z]},4e3)},1e3)}),(y,l)=>{const v=k("el-icon"),b=k("el-button"),z=k("el-progress"),N=k("OfficeBuilding");return a(),c("div",io,[s("div",ao,[s("div",co,[s("div",ro,[s("div",uo,[e(v,{class:"logo-icon"},{default:i(()=>[e(u(Us))]),_:1}),l[3]||(l[3]=s("span",{class:"brand-text"},"iFlytek 求职者中心",-1))]),l[4]||(l[4]=s("div",{class:"brand-subtitle"},"AI助力，让面试更轻松",-1))]),s("div",_o,[e(b,{type:"primary",size:"large",onClick:ns},{default:i(()=>[e(v,null,{default:i(()=>[e(u(ts))]),_:1}),l[5]||(l[5]=_(" 开始练习面试 "))]),_:1,__:[5]}),e(b,{onClick:K},{default:i(()=>[e(v,null,{default:i(()=>[e(u(cs))]),_:1}),l[6]||(l[6]=_(" 学习路径 "))]),_:1,__:[6]}),e(b,{onClick:x},{default:i(()=>[e(v,null,{default:i(()=>[e(u(gs))]),_:1}),l[7]||(l[7]=_(" 个人设置 "))]),_:1,__:[7]})])])]),s("div",vo,[s("div",mo,[l[12]||(l[12]=s("div",{class:"overview-title"},[s("h2",null,"我的面试成长"),s("p",null,"跟踪您的面试技能提升进度")],-1)),s("div",po,[s("div",ho,[s("div",fo,[e(v,{class:"stat-icon"},{default:i(()=>[e(u(ys))]),_:1}),s("span",yo,"+"+n($.improvementRate)+"%",1)]),s("div",go,[s("div",ko,n($.totalInterviews),1),l[8]||(l[8]=s("div",{class:"stat-label"},"练习面试次数",-1)),s("div",$o,"本周练习 "+n($.weeklyInterviews)+" 次",1)])]),s("div",bo,[s("div",wo,[e(v,{class:"stat-icon"},{default:i(()=>[e(u(es))]),_:1}),s("span",xo,"+"+n($.scoreImprovement)+"分",1)]),s("div",Co,[s("div",So,n($.avgScore)+"分",1),l[9]||(l[9]=s("div",{class:"stat-label"},"平均面试得分",-1)),s("div",Vo,"最高得分 "+n($.bestScore)+"分",1)])]),s("div",Mo,[s("div",Io,[e(v,{class:"stat-icon"},{default:i(()=>[e(u(rs))]),_:1}),s("span",Po,n($.skillLevel),1)]),s("div",To,[s("div",zo,n($.skillProgress)+"%",1),l[10]||(l[10]=s("div",{class:"stat-label"},"技能完成度",-1)),s("div",Ao,"距离下一级还需 "+n($.nextLevelPoints)+" 分",1)])]),s("div",Do,[s("div",jo,[e(v,{class:"stat-icon"},{default:i(()=>[e(u(es))]),_:1}),s("span",Ro,"+"+n($.newAchievements),1)]),s("div",qo,[s("div",Eo,n($.achievements),1),l[11]||(l[11]=s("div",{class:"stat-label"},"获得成就",-1)),s("div",Bo,"本月新增 "+n($.monthlyAchievements)+" 个",1)])])])])]),s("div",Lo,[s("div",Fo,[s("div",No,[l[19]||(l[19]=s("div",{class:"module-header"},[s("h3",null,"面试练习与提升"),s("p",null,"通过AI模拟面试，提升您的面试技能")],-1)),s("div",Uo,[s("div",{class:"module-card",onClick:l[0]||(l[0]=r=>F("/practice-interview"))},[s("div",Xo,[e(v,null,{default:i(()=>[e(u(ts))]),_:1})]),s("div",Ho,[l[13]||(l[13]=s("h4",null,"模拟面试",-1)),l[14]||(l[14]=s("p",null,"AI面试官一对一模拟真实面试场景",-1)),s("div",Qo,[s("span",null,"已练习 "+n(L.totalSessions)+" 次",1)])])]),s("div",{class:"module-card",onClick:l[1]||(l[1]=r=>F("/interview-assistant"))},[s("div",Yo,[e(v,null,{default:i(()=>[e(u(ms))]),_:1})]),s("div",Oo,[l[15]||(l[15]=s("h4",null,"实时面试辅助",-1)),l[16]||(l[16]=s("p",null,"面试过程中提供智能提示和建议",-1)),s("div",Jo,[s("span",null,"辅助成功率 "+n(Y.successRate)+"%",1)])])]),s("div",{class:"module-card",onClick:l[2]||(l[2]=r=>F("/skill-assessment"))},[s("div",Ko,[e(v,null,{default:i(()=>[e(u(es))]),_:1})]),s("div",Wo,[l[17]||(l[17]=s("h4",null,"技能评估",-1)),l[18]||(l[18]=s("p",null,"全面评估您的专业技能水平",-1)),s("div",Go,[s("span",null,"当前等级 "+n(O.currentLevel),1)])])])])]),s("div",Zo,[l[20]||(l[20]=s("div",{class:"module-header"},[s("h3",null,"多模态面试交互训练"),s("p",null,"提升语音、视频、手势等多维度表达能力")],-1)),s("div",sl,[e(Hs)])]),s("div",el,[l[28]||(l[28]=s("div",{class:"module-header"},[s("h3",null,"iFlytek Spark AI面试助手"),s("p",null,"智能AI助力，让面试更轻松自信")],-1)),s("div",tl,[s("div",ol,[s("div",ll,[s("div",nl,[e(v,null,{default:i(()=>[e(u(ms))]),_:1})]),l[21]||(l[21]=s("div",{class:"assistant-info"},[s("h4",null,"实时语音辅助"),s("p",null,"面试过程中的智能提示")],-1))]),s("div",il,[s("div",al,[s("div",dl,[(a(),c(p,null,h(5,r=>s("div",{class:"wave-bar",key:r})),64))]),s("div",cl,[s("span",rl,n(C.voiceStatus),1),s("div",ul,[l[22]||(l[22]=s("span",{class:"confidence-label"},"语音清晰度",-1)),s("div",_l,[s("div",{class:"confidence-fill",style:Q({width:C.voiceClarity+"%"})},null,4)]),s("span",vl,n(C.voiceClarity)+"%",1)])])])])]),s("div",ml,[s("div",pl,[s("div",hl,[e(v,null,{default:i(()=>[e(u(ts))]),_:1})]),l[23]||(l[23]=s("div",{class:"assistant-info"},[s("h4",null,"情绪状态分析"),s("p",null,"帮助调整面试状态")],-1))]),s("div",fl,[s("div",yl,[s("div",gl,[s("div",kl,n(C.emotionScore),1),l[24]||(l[24]=s("div",{class:"emotion-label"},"情绪指数",-1))]),s("div",$l,[(a(!0),c(p,null,h(C.suggestions,r=>(a(),c("div",{class:"suggestion-item",key:r.id},[e(v,{class:"suggestion-icon"},{default:i(()=>[e(u(ss))]),_:1}),s("span",bl,n(r.text),1)]))),128))])])])]),s("div",wl,[s("div",xl,[s("div",Cl,[e(v,null,{default:i(()=>[e(u(Xs))]),_:1})]),l[25]||(l[25]=s("div",{class:"assistant-info"},[s("h4",null,"智能答题提示"),s("p",null,"关键词提醒和思路引导")],-1))]),s("div",Sl,[s("div",Vl,[s("div",Ml,[l[26]||(l[26]=s("span",{class:"question-label"},"当前问题类型",-1)),s("span",Il,n(C.currentQuestionType),1)]),s("div",Pl,[l[27]||(l[27]=s("span",{class:"keywords-label"},"关键词提示",-1)),s("div",Tl,[(a(!0),c(p,null,h(C.keywords,r=>(a(),c("span",{class:"keyword-tag",key:r},n(r),1))),128))])])])])])])]),s("div",zl,[l[29]||(l[29]=s("div",{class:"module-header"},[s("h3",null,"个性化学习与职业推荐"),s("p",null,"基于iFlytek Spark的智能学习路径和职位匹配")],-1)),s("div",Al,[e(Qs,{"user-type":"candidate"})])]),s("div",Dl,[l[32]||(l[32]=s("div",{class:"module-header"},[s("h3",null,"个性化学习路径"),s("p",null,"基于您的表现，AI为您定制专属学习计划")],-1)),s("div",jl,[s("div",Rl,[s("div",ql,[l[30]||(l[30]=s("h4",null,"AI算法工程师路径",-1)),s("div",El,[s("span",null,"进度: "+n(M.ai.progress)+"%",1)])]),s("div",Bl,[e(z,{percentage:M.ai.progress,"show-text":!1},null,8,["percentage"]),s("div",Ll,[(a(!0),c(p,null,h(M.ai.modules,r=>(a(),c("div",{class:"module-item",key:r.id},[e(v,{class:P(["module-icon",{completed:r.completed}])},{default:i(()=>[r.completed?(a(),S(u(ss),{key:0})):(a(),S(u(ps),{key:1}))]),_:2},1032,["class"]),s("span",Fl,n(r.name),1)]))),128))])])]),s("div",Nl,[s("div",Ul,[l[31]||(l[31]=s("h4",null,"大数据开发路径",-1)),s("div",Xl,[s("span",null,"进度: "+n(M.bigdata.progress)+"%",1)])]),s("div",Hl,[e(z,{percentage:M.bigdata.progress,"show-text":!1},null,8,["percentage"]),s("div",Ql,[(a(!0),c(p,null,h(M.bigdata.modules,r=>(a(),c("div",{class:"module-item",key:r.id},[e(v,{class:P(["module-icon",{completed:r.completed}])},{default:i(()=>[r.completed?(a(),S(u(ss),{key:0})):(a(),S(u(ps),{key:1}))]),_:2},1032,["class"]),s("span",Yl,n(r.name),1)]))),128))])])])])]),s("div",Ol,[l[37]||(l[37]=s("div",{class:"module-header"},[s("h3",null,"AI智能推荐"),s("p",null,"基于您的技能和兴趣，为您推荐最适合的机会")],-1)),s("div",Jl,[s("div",Kl,[s("div",Wl,[e(v,{class:"recommendation-icon"},{default:i(()=>[e(N)]),_:1}),l[33]||(l[33]=s("h4",null,"职位推荐",-1))]),s("div",Gl,[(a(!0),c(p,null,h(os,r=>(a(),c("div",{class:"job-item",key:r.id},[s("div",Zl,[s("div",sn,n(r.title),1),s("div",en,n(r.company),1),s("div",tn,"匹配度: "+n(r.matchScore)+"%",1)]),e(b,{size:"small",type:"primary",onClick:R=>T(r.id)},{default:i(()=>l[34]||(l[34]=[_(" 申请 ")])),_:2,__:[34]},1032,["onClick"])]))),128))])]),s("div",on,[s("div",ln,[e(v,{class:"recommendation-icon"},{default:i(()=>[e(u(cs))]),_:1}),l[35]||(l[35]=s("h4",null,"学习推荐",-1))]),s("div",nn,[(a(!0),c(p,null,h(ls,r=>(a(),c("div",{class:"learning-item",key:r.id},[s("div",an,[s("div",dn,n(r.title),1),s("div",cn,n(r.description),1),s("div",rn,"难度: "+n(r.difficulty),1)]),e(b,{size:"small",onClick:R=>A(r.id)},{default:i(()=>l[36]||(l[36]=[_(" 开始学习 ")])),_:2,__:[36]},1032,["onClick"])]))),128))])])])]),s("div",un,[l[38]||(l[38]=s("div",{class:"module-header"},[s("h3",null,"候选人体验优化"),s("p",null,"面试准备助手、实时指导、技能提升一站式服务")],-1)),s("div",_n,[e(no)])]),s("div",vn,[l[40]||(l[40]=s("div",{class:"module-header"},[s("h3",null,"面试历史与分析"),s("p",null,"回顾您的面试表现，发现改进空间")],-1)),s("div",mn,[s("div",pn,[(a(!0),c(p,null,h(J,r=>(a(),c("div",{class:"timeline-item",key:r.id},[s("div",{class:P(["timeline-dot",W(r.score)])},null,2),s("div",hn,[s("div",fn,n(r.position),1),s("div",yn,n(r.company),1),s("div",gn,"得分: "+n(r.score)+"分",1),s("div",kn,n(G(r.date)),1),e(b,{text:"",size:"small",onClick:R=>j(r.id)},{default:i(()=>l[39]||(l[39]=[_(" 查看详情 ")])),_:2,__:[39]},1032,["onClick"])])]))),128))])])])])])])}}},xn=hs($n,[["__scopeId","data-v-1bc8c429"]]);export{xn as default};
