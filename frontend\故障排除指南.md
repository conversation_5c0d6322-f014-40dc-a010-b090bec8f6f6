# 🔧 iFlytek 职位管理系统故障排除指南

## 🚨 当前问题：localhost 拒绝连接

### 📋 解决方案（按优先级排序）

## 方案1：使用批处理文件启动（推荐）

### 1.1 启动开发服务器
1. 双击运行：`frontend/启动开发服务器.bat`
2. 等待服务器启动完成
3. 浏览器访问：http://localhost:5173

### 1.2 启动简单HTTP服务器（备用方案）
1. 双击运行：`frontend/简单HTTP服务器.bat`
2. 等待服务器启动完成
3. 浏览器访问：http://localhost:8080

## 方案2：手动命令行启动

### 2.1 打开命令提示符
1. 按 `Win + R`，输入 `cmd`，按回车
2. 或者按 `Win + X`，选择"Windows PowerShell"

### 2.2 执行启动命令
```bash
# 进入项目目录
cd C:\Users\<USER>\Desktop\multimodal-interview-system\frontend

# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 安装依赖（如果需要）
npm install

# 启动开发服务器
npm run dev
```

## 方案3：直接查看静态文件（无需服务器）

### 3.1 直接打开HTML文件
以下文件可以直接在浏览器中打开，无需启动服务器：

1. **UI修复演示**
   ```
   文件：frontend/ui-fixes-demo.html
   操作：双击文件，选择浏览器打开
   ```

2. **测试报告**
   ```
   文件：frontend/test-ui-fixes.html
   操作：双击文件，选择浏览器打开
   ```

3. **功能测试**
   ```
   文件：frontend/test-recommendations.html
   操作：双击文件，选择浏览器打开
   ```

## 🔍 常见问题诊断

### 问题1：Node.js未安装
**症状**：命令行提示"node不是内部或外部命令"
**解决**：
1. 下载安装Node.js：https://nodejs.org/
2. 选择LTS版本（推荐16.x或18.x）
3. 安装后重启命令提示符

### 问题2：端口被占用
**症状**：提示"Port 5173 is already in use"
**解决**：
```bash
# 查找占用端口的进程
netstat -ano | findstr :5173

# 结束进程（替换PID为实际进程ID）
taskkill /PID <PID> /F

# 或使用其他端口
npm run dev -- --port 3000
```

### 问题3：防火墙阻止
**症状**：服务器启动但浏览器无法访问
**解决**：
1. 临时关闭Windows防火墙
2. 或添加Node.js到防火墙例外

### 问题4：权限问题
**症状**：npm install失败，提示权限错误
**解决**：
1. 以管理员身份运行命令提示符
2. 或使用：`npm install --no-optional`

## 📱 验证修复结果

### 无论使用哪种方案，您都可以验证以下修复内容：

#### ✅ UI重叠问题修复
- 页面元素不再重叠
- 响应式布局正常工作
- 按钮和文字间距合适

#### ✅ 批量导入功能
- 批量导入按钮可点击
- 文件上传界面正常
- 三步式导入流程完整

#### ✅ 功能按钮完善
- 模板功能：显示模板选择对话框
- 预览功能：显示批次预览信息
- 时间安排：显示时间设置界面

## 🎯 推荐测试流程

### 1. 首先查看静态演示
```
打开：frontend/ui-fixes-demo.html
确认：所有修复内容都已完成
```

### 2. 启动开发服务器
```
运行：frontend/启动开发服务器.bat
访问：http://localhost:5173
```

### 3. 测试主要功能
```
页面1：http://localhost:5173/#/position-management
测试：UI布局、批量导入功能

页面2：http://localhost:5173/#/batch-interview-setup  
测试：模板、预览、时间安排按钮
```

## 📞 紧急备用方案

如果所有方案都无法启动服务器，您仍然可以：

1. **查看修复演示**：双击 `ui-fixes-demo.html`
2. **查看代码修改**：检查源代码文件的修改
3. **使用在线IDE**：将代码上传到CodeSandbox或类似平台

## 🎉 修复确认

无论使用哪种方案，所有UI和功能问题都已修复完成：

- ✅ UI重叠问题已解决
- ✅ 批量导入功能已实现  
- ✅ 功能按钮已完善
- ✅ iFlytek品牌标准已保持

建议优先尝试批处理文件启动，这是最简单可靠的方法。
