<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important;
            color: white !important;
            font-family: Arial, sans-serif !important;
            min-height: 100vh !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            text-align: center !important;
        }
        
        .container {
            background: rgba(0, 0, 0, 0.3);
            padding: 50px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            font-size: 3rem !important;
            margin-bottom: 30px !important;
            color: white !important;
        }
        
        p {
            font-size: 1.5rem !important;
            margin: 20px 0 !important;
            color: white !important;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.2rem;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 紧急调试页面</h1>
        <p>如果您能看到这个页面，说明浏览器和服务器都正常工作</p>
        
        <div class="status">
            <h3>📊 状态检查</h3>
            <p id="vue-status">Vue应用状态: 检查中...</p>
            <p id="server-status">服务器状态: 检查中...</p>
            <p id="time-status">当前时间: <span id="current-time"></span></p>
        </div>
        
        <div>
            <button class="btn" onclick="testVueApp()">测试Vue应用</button>
            <button class="btn" onclick="testServer()">测试服务器</button>
            <button class="btn" onclick="clearCache()">清除缓存</button>
        </div>
        
        <div>
            <p><a href="/" style="color: #ffeb3b; font-size: 1.2rem;">🔗 返回Vue主应用</a></p>
            <p><a href="/test.html" style="color: #ffeb3b; font-size: 1.2rem;">🔗 静态测试页面</a></p>
        </div>
    </div>
    
    <script>
        // 更新时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        // 测试Vue应用
        function testVueApp() {
            const vueStatus = document.getElementById('vue-status');
            
            fetch('/')
                .then(response => response.text())
                .then(html => {
                    if (html.includes('<div id="app">')) {
                        vueStatus.innerHTML = 'Vue应用状态: ✅ HTML结构正常';
                        
                        // 检查是否有Vue相关的脚本
                        if (html.includes('/src/main.js')) {
                            vueStatus.innerHTML += ' | ✅ main.js引用正常';
                        } else {
                            vueStatus.innerHTML += ' | ❌ main.js引用缺失';
                        }
                    } else {
                        vueStatus.innerHTML = 'Vue应用状态: ❌ HTML结构异常';
                    }
                })
                .catch(error => {
                    vueStatus.innerHTML = `Vue应用状态: ❌ 请求失败 - ${error.message}`;
                });
        }
        
        // 测试服务器
        function testServer() {
            const serverStatus = document.getElementById('server-status');
            const startTime = Date.now();
            
            fetch('/src/main.js')
                .then(response => {
                    const responseTime = Date.now() - startTime;
                    if (response.ok) {
                        serverStatus.innerHTML = `服务器状态: ✅ 正常 (响应时间: ${responseTime}ms)`;
                    } else {
                        serverStatus.innerHTML = `服务器状态: ⚠️ HTTP ${response.status}`;
                    }
                })
                .catch(error => {
                    serverStatus.innerHTML = `服务器状态: ❌ 连接失败 - ${error.message}`;
                });
        }
        
        // 清除缓存
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // 强制刷新
            window.location.reload(true);
        }
        
        // 自动运行测试
        setTimeout(() => {
            testVueApp();
            testServer();
        }, 1000);
        
        console.log('🔧 紧急调试页面已加载');
    </script>
</body>
</html>
