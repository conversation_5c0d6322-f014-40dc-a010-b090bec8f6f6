!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((n=n||self).heyListen={})}(this,function(n){"use strict";n.warning=function(){},n.invariant=function(){},"production"!==process.env.NODE_ENV&&(n.warning=function(n,e){n||"undefined"==typeof console||console.warn(e)},n.invariant=function(n,e){if(!n)throw new Error(e)}),Object.defineProperty(n,"__esModule",{value:!0})});
