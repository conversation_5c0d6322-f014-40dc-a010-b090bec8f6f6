/**
 * iFlytek 星火大模型智能面试系统 - 增强图标系统样式
 * Enhanced Icon System Styles for iFlytek Spark Interview System
 *
 * 版本: 2.0
 * 更新: 2025-07-16
 *
 * 功能特性:
 * - 统一的图标尺寸和间距标准
 * - iFlytek 品牌色彩一致性
 * - 响应式设计和可访问性支持
 * - 面试系统专用图标样式
 * - 多模态分析图标动效
 */

/* ===== CSS 变量定义 ===== */
:root {
  /* 图标尺寸变量 */
  --icon-xs: 12px;
  --icon-sm: 14px;
  --icon-md: 16px;
  --icon-lg: 20px;
  --icon-xl: 24px;
  --icon-xxl: 32px;
  --icon-xxxl: 48px;

  /* 图标间距变量 */
  --icon-margin-xs: 4px;
  --icon-margin-sm: 6px;
  --icon-margin-md: 8px;
  --icon-margin-lg: 12px;

  /* iFlytek 品牌色彩 */
  --iflytek-icon-primary: #1890ff;
  --iflytek-icon-secondary: #667eea;
  --iflytek-icon-accent: #0066cc;

  /* 技术领域主题色 */
  --ai-icon-color: #0066cc;
  --bigdata-icon-color: #059669;
  --iot-icon-color: #dc2626;
  --cloud-icon-color: #7c3aed;

  /* 状态色彩 */
  --icon-success: #10b981;
  --icon-warning: #f59e0b;
  --icon-error: #ef4444;
  --icon-info: #3b82f6;
}

/* ===== 基础图标样式 ===== */
.el-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--icon-md);
  color: inherit;
  /* 确保图标与文本基线对齐 */
  position: relative;
  top: -0.1em;
}

/* 图标基础类 */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保图标与文本基线对齐 */
  position: relative;
  top: -0.1em;
}

/* ===== 图标尺寸系统 ===== */
.icon-xs, .el-icon.icon-xs {
  width: var(--icon-xs);
  height: var(--icon-xs);
  font-size: var(--icon-xs);
  min-width: var(--icon-xs);
}

.icon-sm, .el-icon.icon-sm {
  width: var(--icon-sm);
  height: var(--icon-sm);
  font-size: var(--icon-sm);
  min-width: var(--icon-sm);
}

.icon-md, .el-icon.icon-md {
  width: var(--icon-md);
  height: var(--icon-md);
  font-size: var(--icon-md);
  min-width: var(--icon-md);
}

.icon-lg, .el-icon.icon-lg {
  width: var(--icon-lg);
  height: var(--icon-lg);
  font-size: var(--icon-lg);
  min-width: var(--icon-lg);
}

.icon-xl, .el-icon.icon-xl {
  width: var(--icon-xl);
  height: var(--icon-xl);
  font-size: var(--icon-xl);
  min-width: var(--icon-xl);
}

.icon-xxl, .el-icon.icon-xxl {
  width: var(--icon-xxl);
  height: var(--icon-xxl);
  font-size: var(--icon-xxl);
  min-width: var(--icon-xxl);
}

.icon-xxxl, .el-icon.icon-xxxl {
  width: var(--icon-xxxl);
  height: var(--icon-xxxl);
  font-size: var(--icon-xxxl);
  min-width: var(--icon-xxxl);
}

/* ===== 面试系统专用图标尺寸 ===== */
/* 元数据区域图标 */
.meta-item .el-icon {
  font-size: var(--icon-sm);
  margin-right: var(--icon-margin-sm);
}

/* 面板标题图标 */
.panel-header .el-icon,
.skills-header .el-icon,
.scoring-header .el-icon,
.thinking-header .el-icon,
.hints-header .el-icon {
  font-size: var(--icon-md);
  margin-right: var(--icon-margin-md);
}

/* 控制按钮图标 */
.control-btn .el-icon {
  font-size: var(--icon-sm);
  margin-right: var(--icon-margin-sm);
}

/* 大型控制按钮图标 */
.control-action-btn .el-icon {
  font-size: var(--icon-md);
  margin-right: var(--icon-margin-md);
}

/* 快捷按钮图标 */
.quick-btn .el-icon {
  font-size: var(--icon-lg);
}

/* 头像区域图标 */
.avatar-icon .el-icon {
  font-size: var(--icon-xl);
}

/* 候选人信息区域图标 */
.candidate-company .el-icon {
  font-size: var(--icon-sm);
  margin-right: var(--icon-margin-xs);
}

/* 问题元数据图标 */
.question-type .el-icon,
.question-difficulty .el-icon {
  font-size: var(--icon-xs);
  margin-right: var(--icon-margin-xs);
}

/* 分析标签页图标 */
.analysis-tab .el-icon {
  font-size: var(--icon-sm);
  margin-right: var(--icon-margin-sm);
}

/* ===== 多模态分析图标系统 ===== */
.icon-multimodal {
  background: linear-gradient(135deg, var(--iflytek-icon-primary), var(--iflytek-icon-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  position: relative;
}

/* 语音分析图标 */
.icon-voice-analysis,
.multimodal-icon.voice-icon {
  color: var(--ai-icon-color);
  position: relative;
}

.icon-voice-analysis::after,
.multimodal-icon.voice-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  border: 2px solid var(--ai-icon-color);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: voicePulse 2s ease-in-out infinite;
}

@keyframes voicePulse {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
  }
}

/* 视频分析图标 */
.icon-video-analysis,
.multimodal-icon.video-icon {
  color: var(--bigdata-icon-color);
  position: relative;
  overflow: hidden;
}

.icon-video-analysis::before,
.multimodal-icon.video-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 150, 105, 0.3), transparent);
  animation: videoScan 3s ease-in-out infinite;
}

@keyframes videoScan {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 文本分析图标 */
.icon-text-analysis,
.multimodal-icon.text-icon {
  color: var(--iot-icon-color);
  position: relative;
}

.icon-text-analysis::after,
.multimodal-icon.text-icon::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--iot-icon-color);
  animation: textUnderline 2s ease-in-out infinite;
}

@keyframes textUnderline {
  0%, 100% { width: 0; }
  50% { width: 100%; }
}

/* 数据分析图标 */
.icon-data-analysis,
.multimodal-icon.data-icon {
  color: var(--cloud-icon-color);
  position: relative;
}

.icon-data-analysis::before,
.multimodal-icon.data-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 1px solid var(--cloud-icon-color);
  border-radius: 4px;
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: dataGlow 2.5s ease-in-out infinite;
}

@keyframes dataGlow {
  0%, 100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* ===== 技术领域图标主题 ===== */
.icon-ai,
.domain-icon.ai,
.tech-icon.ai {
  color: var(--ai-icon-color);
  background: rgba(0, 102, 204, 0.1);
  border-radius: 8px;
  padding: 8px;
  border: 1px solid rgba(0, 102, 204, 0.2);
  transition: all 0.3s ease;
}

.icon-ai:hover,
.domain-icon.ai:hover,
.tech-icon.ai:hover {
  background: rgba(0, 102, 204, 0.15);
  border-color: var(--ai-icon-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.2);
}

.icon-bigdata,
.domain-icon.bigdata,
.tech-icon.bigdata {
  color: var(--bigdata-icon-color);
  background: rgba(5, 150, 105, 0.1);
  border-radius: 8px;
  padding: 8px;
  border: 1px solid rgba(5, 150, 105, 0.2);
  transition: all 0.3s ease;
}

.icon-bigdata:hover,
.domain-icon.bigdata:hover,
.tech-icon.bigdata:hover {
  background: rgba(5, 150, 105, 0.15);
  border-color: var(--bigdata-icon-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
}

.icon-iot,
.domain-icon.iot,
.tech-icon.iot {
  color: var(--iot-icon-color);
  background: rgba(220, 38, 38, 0.1);
  border-radius: 8px;
  padding: 8px;
  border: 1px solid rgba(220, 38, 38, 0.2);
  transition: all 0.3s ease;
}

.icon-iot:hover,
.domain-icon.iot:hover,
.tech-icon.iot:hover {
  background: rgba(220, 38, 38, 0.15);
  border-color: var(--iot-icon-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.icon-cloud,
.domain-icon.cloud,
.tech-icon.cloud {
  color: var(--cloud-icon-color);
  background: rgba(124, 58, 237, 0.1);
  border-radius: 8px;
  padding: 8px;
  border: 1px solid rgba(124, 58, 237, 0.2);
  transition: all 0.3s ease;
}

.icon-cloud:hover,
.domain-icon.cloud:hover,
.tech-icon.cloud:hover {
  background: rgba(124, 58, 237, 0.15);
  border-color: var(--cloud-icon-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
}

/* ===== 状态图标系统 ===== */
.icon-success,
.status-icon.success,
.icon-status.active {
  color: var(--icon-success);
  position: relative;
}

.icon-success::after,
.status-icon.success::after,
.icon-status.active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--icon-success);
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.icon-warning,
.status-icon.warning,
.icon-status.warning {
  color: var(--icon-warning);
  animation: warningBlink 2s ease-in-out infinite;
}

@keyframes warningBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.icon-error,
.status-icon.error,
.icon-status.error {
  color: var(--icon-error);
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.icon-info,
.status-icon.info,
.icon-status.processing {
  color: var(--icon-info);
}

.icon-status.processing {
  animation: processingRotate 1s linear infinite;
}

@keyframes processingRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== 面试流程专用图标 ===== */
.interview-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.interview-icon.recording {
  color: var(--icon-error);
  animation: recordingPulse 1.5s ease-in-out infinite;
}

@keyframes recordingPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.interview-icon.paused {
  color: var(--icon-warning);
}

.interview-icon.completed {
  color: var(--icon-success);
}

.interview-icon.ai-thinking {
  color: var(--iflytek-icon-primary);
  animation: aiThinking 2s ease-in-out infinite;
}

@keyframes aiThinking {
  0%, 100% {
    opacity: 0.6;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* 交互图标 */
.icon-interactive {
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-interactive:hover {
  transform: scale(1.1);
  color: var(--iflytek-primary);
}

.icon-interactive:active {
  transform: scale(0.95);
}

/* 旋转图标 */
.icon-spin {
  animation: iconSpin 1s linear infinite;
}

@keyframes iconSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.icon-spin-slow {
  animation: iconSpin 2s linear infinite;
}

.icon-spin-fast {
  animation: iconSpin 0.5s linear infinite;
}

/* 脉冲图标 */
.icon-pulse {
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 弹跳图标 */
.icon-bounce {
  animation: iconBounce 1s infinite;
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 摇摆图标 */
.icon-shake {
  animation: iconShake 0.5s infinite;
}

@keyframes iconShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 图标容器 */
.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--iflytek-bg-secondary);
  border: 2px solid var(--iflytek-border-secondary);
  transition: all 0.3s ease;
}

.icon-container:hover {
  background: var(--iflytek-primary);
  border-color: var(--iflytek-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--iflytek-shadow-md);
}

.icon-container-square {
  border-radius: 8px;
}

.icon-container-rounded {
  border-radius: 12px;
}

/* 图标徽章 */
.icon-badge {
  position: relative;
}

.icon-badge::after {
  content: attr(data-badge);
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--iflytek-error);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

.icon-badge-dot::after {
  content: '';
  width: 8px;
  height: 8px;
  top: -4px;
  right: -4px;
}

/* 图标组合 */
.icon-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-group .icon {
  flex-shrink: 0;
}

.icon-stack {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-stack .icon-background {
  position: absolute;
  z-index: 1;
  opacity: 0.3;
}

.icon-stack .icon-foreground {
  position: relative;
  z-index: 2;
}

/* 图标文本组合 */
.icon-text {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: var(--font-size-base);
  color: var(--iflytek-text-primary);
}

.icon-text-vertical {
  flex-direction: column;
  text-align: center;
  gap: 4px;
}

.icon-text-reverse {
  flex-direction: row-reverse;
}

/* 图标按钮 */
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--iflytek-text-secondary);
}

.icon-button:hover {
  background: var(--iflytek-bg-secondary);
  color: var(--iflytek-primary);
}

.icon-button:active {
  transform: scale(0.95);
}

.icon-button-primary {
  background: var(--iflytek-primary);
  color: white;
}

.icon-button-primary:hover {
  background: var(--iflytek-primary-dark);
}

/* 图标分隔符 */
.icon-divider {
  color: var(--iflytek-border-primary);
  margin: 0 12px;
}

/* 图标加载状态 */
.icon-loading {
  opacity: 0.5;
  pointer-events: none;
}

/* 图标禁用状态 */
.icon-disabled {
  opacity: 0.4;
  cursor: not-allowed;
  pointer-events: none;
}

/* 图标渐变效果 */
.icon-gradient-ai {
  background: var(--ai-theme-bg);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.icon-gradient-bigdata {
  background: var(--bigdata-theme-bg);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.icon-gradient-iot {
  background: var(--iot-theme-bg);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.icon-gradient-cloud {
  background: var(--cloud-theme-bg);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 图标阴影效果 */
.icon-shadow {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.icon-shadow-lg {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

.icon-shadow-colored {
  filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.3));
}

/* 图标发光效果 */
.icon-glow {
  filter: drop-shadow(0 0 8px currentColor);
}

.icon-glow-strong {
  filter: drop-shadow(0 0 16px currentColor);
}

/* 图标边框 */
.icon-border {
  border: 2px solid currentColor;
  border-radius: 4px;
  padding: 4px;
}

.icon-border-circle {
  border-radius: 50%;
}

.icon-border-dashed {
  border-style: dashed;
}

/* ===== 响应式图标设计 ===== */

/* 桌面端 (>1200px) */
@media (min-width: 1201px) {
  .hero-section .el-icon,
  .stats-section .el-icon {
    font-size: var(--icon-xxxl);
  }

  .panel-header .el-icon {
    font-size: var(--icon-lg);
  }

  .control-action-btn .el-icon {
    font-size: var(--icon-md);
  }
}

/* 笔记本电脑 (769px - 1200px) */
@media (min-width: 769px) and (max-width: 1200px) {
  .hero-section .el-icon,
  .stats-section .el-icon {
    font-size: var(--icon-xxl);
  }

  .panel-header .el-icon {
    font-size: var(--icon-md);
  }

  .control-action-btn .el-icon {
    font-size: var(--icon-sm);
  }
}

/* 平板端 (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .hero-section .el-icon,
  .stats-section .el-icon {
    font-size: var(--icon-xl);
  }

  .panel-header .el-icon {
    font-size: var(--icon-sm);
  }

  .control-action-btn .el-icon {
    font-size: var(--icon-xs);
  }

  .quick-btn .el-icon {
    font-size: var(--icon-md);
  }

  .avatar-icon .el-icon {
    font-size: var(--icon-lg);
  }

  .meta-item .el-icon {
    margin-right: var(--icon-margin-xs);
  }

  /* 确保触摸目标足够大 */
  .quick-btn,
  .control-btn {
    min-width: 44px;
    min-height: 44px;
    padding: 8px;
  }
}

/* 手机端 (≤480px) */
@media (max-width: 480px) {
  .hero-section .el-icon,
  .stats-section .el-icon {
    font-size: var(--icon-lg);
  }

  .panel-header .el-icon {
    font-size: var(--icon-xs);
  }

  .control-action-btn .el-icon {
    font-size: var(--icon-xs);
  }

  .quick-btn .el-icon {
    font-size: var(--icon-sm);
  }

  .avatar-icon .el-icon {
    font-size: var(--icon-md);
  }

  .meta-item .el-icon,
  .feature-tag .el-icon {
    margin-right: var(--icon-margin-xs);
  }

  /* 移动端触摸优化 */
  .quick-btn,
  .control-btn,
  .icon-button {
    min-width: 44px;
    min-height: 44px;
    padding: 10px;
  }

  /* 减少动画以提升性能 */
  .icon-voice-analysis::after,
  .icon-video-analysis::before,
  .icon-text-analysis::after {
    animation-duration: 3s;
  }
}

/* ===== 可访问性增强 ===== */
.icon-accessible,
.el-icon[tabindex],
.icon-button,
.quick-btn {
  outline: none;
  border-radius: 4px;
}

.icon-accessible:focus,
.el-icon[tabindex]:focus,
.icon-button:focus,
.quick-btn:focus {
  outline: 2px solid var(--iflytek-icon-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .el-icon,
  .icon {
    filter: contrast(1.5);
    font-weight: bold;
  }

  .icon-ai,
  .icon-bigdata,
  .icon-iot,
  .icon-cloud {
    border-width: 2px;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .el-icon,
  .icon,
  .icon-interactive,
  .interview-icon {
    transition: none !important;
    animation: none !important;
  }

  .icon-voice-analysis::after,
  .icon-video-analysis::before,
  .icon-text-analysis::after,
  .icon-data-analysis::before,
  .icon-success::after,
  .status-icon.success::after {
    animation: none !important;
  }

  .icon-interactive:hover,
  .interview-icon:hover {
    transform: none !important;
  }
}

/* 深色主题支持 */
[data-theme="dark"] .el-icon,
[data-theme="dark"] .icon {
  color: #e5e7eb;
}

[data-theme="dark"] .icon-ai {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .icon-bigdata {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
}

[data-theme="dark"] .icon-iot {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
}

[data-theme="dark"] .icon-cloud {
  background: rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
}

/* 打印样式优化 */
@media print {
  .el-icon,
  .icon {
    color: #000 !important;
    background: none !important;
    border: none !important;
    animation: none !important;
  }

  .icon-voice-analysis::after,
  .icon-video-analysis::before,
  .icon-text-analysis::after {
    display: none !important;
  }
}

/* 图标工具提示 */
.icon-tooltip {
  position: relative;
  cursor: help;
}

.icon-tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--iflytek-text-primary);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: var(--font-size-xs);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  margin-bottom: 5px;
}

.icon-tooltip::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--iflytek-text-primary);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.icon-tooltip:hover::before,
.icon-tooltip:hover::after {
  opacity: 1;
  visibility: visible;
}

/* 图标网格布局 */
.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 16px;
  padding: 16px;
}

.icon-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-grid-item:hover {
  background: var(--iflytek-bg-secondary);
  transform: translateY(-2px);
}

.icon-grid-label {
  font-size: var(--font-size-xs);
  color: var(--iflytek-text-secondary);
  text-align: center;
}

/* 图标动画延迟 */
.icon-delay-1 { animation-delay: 0.1s; }
.icon-delay-2 { animation-delay: 0.2s; }
.icon-delay-3 { animation-delay: 0.3s; }
.icon-delay-4 { animation-delay: 0.4s; }
.icon-delay-5 { animation-delay: 0.5s; }

/* 图标主题切换 */
[data-theme="dark"] .icon {
  color: var(--iflytek-text-primary);
}

[data-theme="dark"] .icon-container {
  background: var(--iflytek-bg-secondary);
  border-color: var(--iflytek-border-secondary);
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .icon-spin,
  .icon-pulse,
  .icon-bounce,
  .icon-shake,
  .icon-interactive {
    animation: none !important;
  }
  
  .icon-interactive:hover {
    transform: none !important;
  }
}
