/**
 * iFlytek 多模态面试系统 - 简洁样式系统
 * 统一的、易维护的样式配置
 */

/* 引入响应式设计框架 */
@import './responsive-framework.css';

/* ===== CSS变量定义 ===== */
:root {
  /* iFlytek 品牌色彩 */
  --iflytek-primary: #1890ff;
  --iflytek-secondary: #667eea;
  --iflytek-accent: #764ba2;
  
  /* 中性色彩 */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #f0f0f0;
  --gray-600: #595959;
  --gray-800: #262626;
  --gray-900: #1f1f1f;
  
  /* 语义化颜色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-white: #ffffff;
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --border-light: #f0f0f0;
  
  /* 渐变 */
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-button: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  
  /* 字体 */
  --font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimHei', sans-serif;
  
  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* ===== 全局重置 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== 标题样式 ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family);
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* ===== 主要布局 ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

/* ===== Hero区域 ===== */
.hero-section {
  background: var(--gradient-hero);
  color: var(--text-white);
  padding: var(--space-2xl) 0;
  text-align: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: var(--space-md);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: var(--space-xl);
  opacity: 0.9;
}

/* ===== 统计数据 ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  display: block;
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* ===== 按钮样式 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--gradient-button);
  color: var(--text-white);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--iflytek-primary);
  border: 1px solid var(--iflytek-primary);
}

.btn-secondary:hover {
  background: var(--iflytek-primary);
  color: var(--text-white);
}

/* ===== 卡片样式 ===== */
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--space-md);
  color: var(--text-primary);
}

.card-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== 网格布局 ===== */
.grid {
  display: grid;
  gap: var(--space-lg);
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* ===== 导航样式 ===== */
.header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-md) 0;
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  gap: var(--space-lg);
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: var(--iflytek-primary);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .nav {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .nav-menu {
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }
}

/* ===== Element Plus 组件优化 ===== */
.el-button {
  font-family: var(--font-family);
}

.el-menu-item {
  font-family: var(--font-family);
}

.el-card {
  font-family: var(--font-family);
}

/* ===== 工具类 ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
