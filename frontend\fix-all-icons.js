#!/usr/bin/env node

/**
 * 全面修复 Element Plus 图标脚本
 * 修复所有已知的图标问题
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Element Plus 中确实存在的图标（根据官方文档验证）
const VALID_ICONS = [
  // 系统图标
  'Reading', 'Guide', 'MagicStick', 'Notebook', 'QuestionFilled',
  'WarningFilled', 'Menu', 'ZoomIn', 'ZoomOut', 'FullScreen',
  'StarFilled', 'RefreshRight', 'SuccessFilled', 'UploadFilled',
  'CaretTop', 'UserFilled', 'School', 'List', 'CaretBottom',
  'CloseBold', 'Select', 'Brush', 'Bottom', 'ChatLineRound',
  'Bulb', 'DataLine', 'Filter', 'Mouse', 'Platform', 'Headset',
  'Pointer', 'Lightning', 'Right', 'Sort', 'Money', 'Service',
  'MoreFilled', 'DocumentAdd', 'RefreshLeft', 'PieChart',
  'Back', 'Sunny', 'CircleCloseFilled'
]

// 需要替换的图标映射
const ICON_REPLACEMENTS = {
  'DataAnalysis': 'Grid',
  'Brain': 'Cpu',
  'CloudUpload': 'Upload',
  'PlayArrow': 'VideoPlay',
  'Iphone': 'Phone',
  'Cellphone': 'Phone',
  'Smartphone': 'Phone',
  'Monitor': 'TrendCharts',
  'Dashboard': 'DataBoard',
  'Analytics': 'TrendCharts',
  'Intelligence': 'Cpu',
  'Smart': 'Cpu',
  'Shield': 'Lock',
  'Play': 'VideoPlay',
  'Lightbulb': 'Star',
  'PieChartIcon': 'PieChart',
  'CircleCloseFillled': 'CircleCloseFilled'  // 修复拼写错误
}

// 扫描并修复文件
function fixIconsInFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return { fixed: false, changes: [] }
  }

  let content = fs.readFileSync(filePath, 'utf8')
  let originalContent = content
  const changes = []

  // 修复导入语句中的图标
  Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
    const importRegex = new RegExp(`(import\\s*{[^}]*?)\\b${oldIcon}\\b([^}]*}\\s*from\\s*['"]@element-plus/icons-vue['"])`, 'g')
    if (importRegex.test(content)) {
      content = content.replace(importRegex, `$1${newIcon}$2`)
      changes.push(`导入语句: ${oldIcon} → ${newIcon}`)
    }
  })

  // 修复模板中的图标使用
  Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
    const templateRegex = new RegExp(`<${oldIcon}\\s*/>`, 'g')
    if (templateRegex.test(content)) {
      content = content.replace(templateRegex, `<${newIcon} />`)
      changes.push(`模板使用: ${oldIcon} → ${newIcon}`)
    }
  })

  // 修复字符串引用中的图标
  Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
    const stringRegex = new RegExp(`(['"])${oldIcon}\\1`, 'g')
    if (stringRegex.test(content)) {
      content = content.replace(stringRegex, `$1${newIcon}$1`)
      changes.push(`字符串引用: ${oldIcon} → ${newIcon}`)
    }
  })

  // 修复对象属性中的图标
  Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
    const propRegex = new RegExp(`(icon\\s*:\\s*['"])${oldIcon}(['"])`, 'g')
    if (propRegex.test(content)) {
      content = content.replace(propRegex, `$1${newIcon}$2`)
      changes.push(`对象属性: ${oldIcon} → ${newIcon}`)
    }
  })

  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    return { fixed: true, changes }
  }

  return { fixed: false, changes: [] }
}

// 递归扫描目录
function scanAndFixDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const relativePath = path.relative(__dirname, fullPath)
        const result = fixIconsInFile(fullPath)
        
        if (result.fixed) {
          results.push({
            file: relativePath,
            changes: result.changes
          })
        }
      }
    }
  }
  
  scan(dir)
  return results
}

// 主函数
function main() {
  console.log('🔧 开始全面修复 Element Plus 图标...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanAndFixDirectory(srcDir)
  
  if (results.length === 0) {
    console.log('✅ 没有发现需要修复的图标问题！')
    return
  }
  
  console.log(`🎉 成功修复 ${results.length} 个文件:\n`)
  
  results.forEach(({ file, changes }) => {
    console.log(`📄 ${file}:`)
    changes.forEach(change => {
      console.log(`  ✅ ${change}`)
    })
    console.log()
  })
  
  console.log('🎯 修复完成！所有无效图标已替换为有效图标。')
  console.log('\n💡 建议：')
  console.log('1. 重新启动开发服务器')
  console.log('2. 检查浏览器控制台确认无错误')
  console.log('3. 运行 node comprehensive-icon-check.js 验证修复结果')
}

// 运行主函数
main()
