#!/usr/bin/env node

/**
 * 🔧 中文字体渲染问题排查工具
 * Chinese Font Rendering Troubleshooting Tool
 * 
 * 分析生成的图片中的中文字体质量并提供改进建议
 * Analyze Chinese font quality in generated images and provide improvement suggestions
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 中文字体渲染问题排查工具');
console.log('Chinese Font Rendering Troubleshooting Tool\n');

// 中文字体质量检查标准
const FONT_QUALITY_CRITERIA = {
    clarity: {
        name: '字体清晰度',
        description: '中文字符边缘是否清晰锐利',
        checkpoints: [
            '字符边缘无模糊',
            '笔画清晰可辨',
            '无像素化现象',
            '字体渲染平滑'
        ]
    },
    completeness: {
        name: '字符完整性', 
        description: '中文字符是否完整显示',
        checkpoints: [
            '无乱码字符',
            '无缺失笔画',
            '字符结构完整',
            '编码正确显示'
        ]
    },
    contrast: {
        name: '对比度',
        description: '文字与背景的对比度',
        checkpoints: [
            '高对比度显示',
            '文字易于阅读',
            '背景不干扰文字',
            '颜色搭配合理'
        ]
    },
    consistency: {
        name: '一致性',
        description: '字体风格和大小的一致性',
        checkpoints: [
            '字体风格统一',
            '字号大小合适',
            '行间距合理',
            '整体布局协调'
        ]
    }
};

// 常见问题及解决方案
const COMMON_ISSUES = {
    blurry_text: {
        problem: '中文文字模糊不清',
        symptoms: ['字符边缘模糊', '看起来不清晰', '像素化严重'],
        causes: [
            '提示词中未明确指定中文字体',
            'AI平台默认使用不支持中文的字体',
            '文字渲染质量设置过低'
        ],
        solutions: [
            '在提示词中明确添加: "Microsoft YaHei font"',
            '强调文字清晰度: "crystal clear Chinese text"',
            '要求高质量渲染: "sharp font edges", "crisp text"',
            '增加负面提示: "blurry text, pixelated fonts"'
        ],
        improved_prompt: 'Professional AI interface, Microsoft YaHei font rendering, crystal clear Chinese text "科大讯飞Spark系统", sharp font edges, no blur'
    },
    garbled_characters: {
        problem: '中文字符乱码',
        symptoms: ['显示为方块', '问号字符', '其他乱码符号'],
        causes: [
            '字符编码不匹配',
            'AI平台不支持中文字符集',
            '字体文件缺失中文字符'
        ],
        solutions: [
            '指定UTF-8编码支持',
            '使用广泛支持的中文字体: "Microsoft YaHei", "SimHei"',
            '强调字符完整性: "complete Chinese character support"',
            '添加多语言支持: "multi-lingual support"'
        ],
        improved_prompt: 'Professional interface with Microsoft YaHei font, complete Chinese character support, UTF-8 encoding, no garbled characters'
    },
    low_contrast: {
        problem: '文字对比度不足',
        symptoms: ['文字难以阅读', '与背景颜色相近', '可读性差'],
        causes: [
            '背景色与文字色相近',
            '未指定高对比度要求',
            '渐变背景影响文字可读性'
        ],
        solutions: [
            '明确对比度要求: "high-contrast white text"',
            '指定背景色: "dark background", "blue gradient"',
            '强调可读性: "clearly readable", "maximum contrast"',
            '避免复杂背景: "clean background", "simple design"'
        ],
        improved_prompt: 'Professional interface, Microsoft YaHei font, high-contrast white text on dark blue background, maximum readability'
    },
    inconsistent_style: {
        problem: '字体风格不一致',
        symptoms: ['不同区域字体不同', '大小不统一', '风格混乱'],
        causes: [
            '提示词描述不够具体',
            '未强调风格一致性',
            'AI生成的随机性'
        ],
        solutions: [
            '强调风格统一: "consistent font style"',
            '指定具体字体: "Microsoft YaHei throughout"',
            '要求专业设计: "professional typography"',
            '统一界面风格: "uniform interface design"'
        ],
        improved_prompt: 'Professional interface, consistent Microsoft YaHei font throughout, uniform typography, professional design standards'
    }
};

// 分析图片中的中文字体问题
function analyzeChineseFontIssues() {
    console.log('🔍 分析中文字体渲染问题...\n');
    
    const imageDir = './generated-images/';
    
    if (!fs.existsSync(imageDir)) {
        console.log('❌ 图片目录不存在');
        console.log('💡 请先下载图片到 ./generated-images/ 目录');
        return;
    }
    
    const imageFiles = fs.readdirSync(imageDir).filter(file => file.endsWith('.png'));
    
    if (imageFiles.length === 0) {
        console.log('❌ 未找到图片文件');
        console.log('💡 请确保图片已下载到 ./generated-images/ 目录');
        return;
    }
    
    console.log(`📊 找到 ${imageFiles.length} 个图片文件`);
    console.log('📋 请手动检查每个图片的中文字体质量:\n');
    
    imageFiles.forEach((filename, index) => {
        console.log(`${index + 1}. ${filename}`);
        console.log('   🔍 检查项目:');
        
        Object.entries(FONT_QUALITY_CRITERIA).forEach(([key, criteria]) => {
            console.log(`   📝 ${criteria.name}:`);
            criteria.checkpoints.forEach(checkpoint => {
                console.log(`      [ ] ${checkpoint}`);
            });
        });
        console.log('');
    });
    
    console.log('💡 如果发现问题，请运行: node chinese-font-troubleshooting.js --solutions');
}

// 显示问题解决方案
function showSolutions() {
    console.log('🛠️  中文字体问题解决方案\n');
    
    Object.entries(COMMON_ISSUES).forEach(([key, issue]) => {
        console.log(`❌ 问题: ${issue.problem}`);
        console.log('=' .repeat(50));
        
        console.log('🔍 症状:');
        issue.symptoms.forEach(symptom => {
            console.log(`   • ${symptom}`);
        });
        
        console.log('\n🔎 可能原因:');
        issue.causes.forEach(cause => {
            console.log(`   • ${cause}`);
        });
        
        console.log('\n✅ 解决方案:');
        issue.solutions.forEach(solution => {
            console.log(`   • ${solution}`);
        });
        
        console.log('\n📝 改进后的提示词示例:');
        console.log(`   ${issue.improved_prompt}`);
        
        console.log('\n' + '─'.repeat(60) + '\n');
    });
}

// 生成改进的提示词
function generateImprovedPrompts() {
    console.log('📝 生成改进的中文字体提示词...\n');
    
    const improvedPrompts = {
        'interface-complete-system.png': {
            title: '系统完整演示界面',
            original_issues: ['字体可能模糊', '对比度不足'],
            improved_prompt: 'Professional AI interview system main interface, Microsoft YaHei font rendering with ultra-sharp edges, crystal clear Chinese text "科大讯飞Spark智能面试评估系统" as prominent title, high-contrast white text on blue-purple gradient background (#667eea to #764ba2), clean modern corporate UI design, maximum text clarity, no blur or pixelation, enterprise-grade quality, 1024x1024 resolution'
        },
        'interface-ai-architecture.png': {
            title: 'AI技术架构界面',
            original_issues: ['技术标签可能不清晰', '图表文字模糊'],
            improved_prompt: 'iFlytek Spark LLM technical architecture interface, Microsoft YaHei font with maximum sharpness, crystal clear Chinese title "AI技术架构", technical labels "神经网络" "算法优化" "多模态融合" "深度学习" in crisp white text, deep blue tech background, professional technical visualization, sharp font rendering, no blurry text, high-tech design, 1024x1024 resolution'
        },
        'interface-case-analysis.png': {
            title: '案例分析界面',
            original_issues: ['标签文字可能重叠', '分屏显示不清晰'],
            improved_prompt: 'Interview case analysis interface, Microsoft YaHei font with perfect clarity, clear Chinese title "面试案例分析", professional position labels "AI工程师" "大数据分析师" "IoT开发者" in high-contrast white text, clean split-screen layout, assessment dashboard, corporate design, sharp text rendering, maximum readability, 1024x1024 resolution'
        },
        'interface-bigdata-analysis.png': {
            title: '大数据分析界面',
            original_issues: ['数据标签可能过小', '图表文字不清晰'],
            improved_prompt: 'Big data analysis interface, Microsoft YaHei font with ultra-high clarity, clear Chinese title "大数据分析技术", data visualization with crisp labels "机器学习" "数据挖掘" "实时分析" "数据仓库", blue data-themed interface, high-contrast white text, professional analytics design, sharp chart labels, maximum text sharpness, 1024x1024 resolution'
        },
        'interface-iot-systems.png': {
            title: 'IoT物联网界面',
            original_issues: ['网络标签可能重叠', '设备名称不清晰'],
            improved_prompt: 'IoT technology interface, Microsoft YaHei font with crystal clarity, clear Chinese title "物联网技术架构", IoT network topology with crisp labels "传感器网络" "嵌入式系统" "边缘计算" "智能设备", green tech-themed design, high-contrast white text, professional network visualization, sharp font edges, maximum readability, 1024x1024 resolution'
        }
    };
    
    Object.entries(improvedPrompts).forEach(([filename, config]) => {
        console.log(`📁 ${filename}`);
        console.log(`📝 ${config.title}`);
        console.log('⚠️  可能问题:');
        config.original_issues.forEach(issue => {
            console.log(`   • ${issue}`);
        });
        console.log('\n✅ 改进后的提示词:');
        console.log(`   ${config.improved_prompt}`);
        console.log('\n' + '─'.repeat(80) + '\n');
    });
    
    // 保存改进的提示词
    fs.writeFileSync('improved-chinese-prompts.json', JSON.stringify(improvedPrompts, null, 2));
    console.log('📄 改进的提示词已保存到 improved-chinese-prompts.json');
}

// 提供重新生成建议
function provideRegenerationAdvice() {
    console.log('🔄 重新生成建议\n');
    
    console.log('如果当前图片的中文字体质量不满意，建议采取以下步骤:\n');
    
    console.log('1️⃣ 使用改进的提示词重新生成:');
    console.log('   node chinese-font-troubleshooting.js --improved-prompts');
    console.log('   # 查看改进后的提示词，手动在ModelsLab中重新生成\n');
    
    console.log('2️⃣ 调整ModelsLab参数:');
    console.log('   • 增加推理步数: num_inference_steps: 30-50');
    console.log('   • 提高引导强度: guidance_scale: 8-10');
    console.log('   • 启用提示词增强: enhance_prompt: "yes"');
    console.log('   • 添加负面提示: "blurry text, pixelated fonts, low quality"\n');
    
    console.log('3️⃣ 尝试不同的AI平台:');
    console.log('   • Midjourney: 对中文字体支持较好');
    console.log('   • DALL-E 3: 文字渲染质量高');
    console.log('   • Stable Diffusion: 可本地控制参数\n');
    
    console.log('4️⃣ 后期处理选项:');
    console.log('   • 使用图像编辑软件手动修复文字');
    console.log('   • 添加文字图层覆盖模糊文字');
    console.log('   • 使用AI图像增强工具提升清晰度\n');
    
    console.log('💡 推荐流程:');
    console.log('   1. 先尝试改进的提示词重新生成');
    console.log('   2. 如果仍有问题，考虑更换AI平台');
    console.log('   3. 最后考虑后期处理方案');
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--analyze')) {
    analyzeChineseFontIssues();
} else if (args.includes('--solutions')) {
    showSolutions();
} else if (args.includes('--improved-prompts')) {
    generateImprovedPrompts();
} else if (args.includes('--regenerate-advice')) {
    provideRegenerationAdvice();
} else {
    console.log('🔧 中文字体渲染问题排查工具使用说明:\n');
    console.log('node chinese-font-troubleshooting.js --analyze           # 分析图片中的字体问题');
    console.log('node chinese-font-troubleshooting.js --solutions         # 显示常见问题解决方案');
    console.log('node chinese-font-troubleshooting.js --improved-prompts  # 生成改进的提示词');
    console.log('node chinese-font-troubleshooting.js --regenerate-advice # 重新生成建议');
    console.log('\n🚀 推荐使用流程:');
    console.log('1. 下载图片后运行 --analyze 检查问题');
    console.log('2. 如有问题运行 --solutions 查看解决方案');
    console.log('3. 使用 --improved-prompts 获取改进的提示词');
    console.log('4. 根据 --regenerate-advice 重新生成图片');
}

export {
    analyzeChineseFontIssues,
    showSolutions,
    generateImprovedPrompts,
    provideRegenerationAdvice,
    FONT_QUALITY_CRITERIA,
    COMMON_ISSUES
};
