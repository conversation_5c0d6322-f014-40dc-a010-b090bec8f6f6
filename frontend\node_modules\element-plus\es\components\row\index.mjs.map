{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/row/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Row from './src/row.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElRow: SFCWithInstall<typeof Row> = withInstall(Row)\nexport default ElRow\n\nexport * from './src/row'\nexport * from './src/constants'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,KAAK,GAAG,WAAW,CAAC,GAAG;;;;"}