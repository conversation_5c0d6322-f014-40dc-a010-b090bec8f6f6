#!/usr/bin/env node

/**
 * Element Plus 图标验证脚本
 * 验证项目中使用的所有 Element Plus 图标是否有效
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Element Plus 官方可用图标列表（常用的）
const validElementPlusIcons = [
  'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'Back', 'Calendar', 
  'Check', 'CircleCheck', 'CircleClose', 'Clock', 'Close', 'Collection', 
  'Connection', 'Cpu', 'DataAnalysis', 'Document', 'Edit', 'Folder',
  'Grid', 'House', 'InfoFilled', 'Key', 'Loading', 'Lock', 'Menu',
  'Monitor', 'Mouse', 'Odometer', 'Platform', 'Promotion', 'QuestionFilled', 
  'Reading', 'Refresh', 'Search', 'Setting', 'Star', 'StarFilled', 
  'SuccessFilled', 'Timer', 'TrendCharts', 'User', 'VideoCamera', 
  'VideoPlay', 'VideoPause', 'View', 'Warning', 'WarningFilled',
  'ZoomIn', 'ZoomOut', 'FullScreen', 'Magic', 'Cellphone', 'Phone',
  'Headset', 'Microphone', 'Upload', 'Download', 'Share', 'Trophy',
  'Location', 'OfficeBuilding', 'School', 'DataBoard', 'ChatDotRound'
]

// 扫描文件中的图标使用
function scanFileForIcons(filePath) {
  if (!fs.existsSync(filePath)) {
    return { imported: [], used: [] }
  }

  const content = fs.readFileSync(filePath, 'utf8')
  const imported = []
  const used = []

  // 匹配导入语句中的图标
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@element-plus\/icons-vue['"]/g
  let importMatch
  while ((importMatch = importRegex.exec(content)) !== null) {
    const iconList = importMatch[1]
    const iconNames = iconList
      .split(',')
      .map(name => name.trim())
      .filter(name => name && !name.includes('as'))
      .map(name => name.replace(/\s+as\s+\w+/, '').trim())
    
    imported.push(...iconNames)
  }

  // 匹配模板中的图标使用
  const templateRegex = /<el-icon[^>]*>\s*<([A-Z][a-zA-Z]*)\s*\/?\s*>\s*<\/el-icon>/g
  let templateMatch
  while ((templateMatch = templateRegex.exec(content)) !== null) {
    used.push(templateMatch[1])
  }

  // 匹配组件注册中的图标
  const componentRegex = /components:\s*{[^}]*([A-Z][a-zA-Z]*)[^}]*}/g
  let componentMatch
  while ((componentMatch = componentRegex.exec(content)) !== null) {
    // 这里需要更精确的解析，暂时跳过
  }

  return { 
    imported: [...new Set(imported)], 
    used: [...new Set(used)] 
  }
}

// 递归扫描目录
function scanDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const relativePath = path.relative(__dirname, fullPath)
        const icons = scanFileForIcons(fullPath)
        
        if (icons.imported.length > 0 || icons.used.length > 0) {
          results.push({
            file: relativePath,
            ...icons
          })
        }
      }
    }
  }
  
  scan(dir)
  return results
}

// 主函数
function main() {
  console.log('🔍 验证 Element Plus 图标使用情况...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  let totalValid = 0
  let totalInvalid = 0
  const invalidIcons = new Set()
  
  console.log('📋 扫描结果:\n')
  
  results.forEach(({ file, imported, used }) => {
    console.log(`📄 ${file}:`)
    
    // 检查导入的图标
    if (imported.length > 0) {
      console.log('  📥 导入的图标:')
      imported.forEach(icon => {
        const isValid = validElementPlusIcons.includes(icon)
        const status = isValid ? '✅' : '❌'
        console.log(`     ${status} ${icon}`)
        
        if (isValid) {
          totalValid++
        } else {
          totalInvalid++
          invalidIcons.add(icon)
        }
      })
    }
    
    // 检查使用的图标
    if (used.length > 0) {
      console.log('  🎯 使用的图标:')
      used.forEach(icon => {
        const isValid = validElementPlusIcons.includes(icon)
        const status = isValid ? '✅' : '❌'
        console.log(`     ${status} ${icon}`)
        
        if (!isValid) {
          invalidIcons.add(icon)
        }
      })
    }
    
    console.log()
  })
  
  console.log('📊 统计信息:')
  console.log(`   有效图标: ${totalValid}`)
  console.log(`   无效图标: ${totalInvalid}`)
  console.log(`   无效图标类型: ${invalidIcons.size}`)
  
  if (invalidIcons.size > 0) {
    console.log('\n❌ 发现无效图标:')
    invalidIcons.forEach(icon => {
      console.log(`   - ${icon}`)
    })
    
    console.log('\n💡 建议替换:')
    const suggestions = {
      'Keyboard': 'Key',
      'Magic': 'Star',
      'Guide': 'QuestionFilled',
      'Lightbulb': 'Star',
      'Aim': 'Star',
      'Target': 'Star',
      'Brain': 'Connection',
      'BrainFilled': 'Connection'
    }
    
    invalidIcons.forEach(icon => {
      if (suggestions[icon]) {
        console.log(`   ${icon} → ${suggestions[icon]}`)
      } else {
        console.log(`   ${icon} → 需要手动选择替换`)
      }
    })
  } else {
    console.log('\n✅ 所有图标都是有效的!')
  }
  
  console.log('\n🔗 Element Plus 图标文档:')
  console.log('   https://element-plus.org/zh-CN/component/icon.html')
}

// 运行验证
main()
