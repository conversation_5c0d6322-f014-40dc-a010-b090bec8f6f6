{"version": 3, "file": "hydrator.d.ts", "sourceRoot": "", "sources": ["../../src/serializer/hydrator.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAIhF,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAE5C,OAAO,EAAqB,KAAK,eAAe,EAAE,KAAK,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACtG,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAC/D,OAAO,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAwC,MAAM,mBAAmB,CAAC;AAGxH,OAAO,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAC;AAEhD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAE/D;;GAEG;AACH,MAAM,WAAW,QAAQ;IACrB;;OAEG;IACH,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAC7D;;;OAGG;IACH,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;CACrF;AAED,MAAM,WAAW,gBAAgB;IAC7B,QAAQ,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5B,QAAQ,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,cAAc;IAC3B,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC5B,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;CAC/B;AAED,qBAAa,eAAgB,YAAW,QAAQ;IAE5C,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;IACpC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;IAChC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IAElC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,iCAAwC;IAC9E,SAAS,CAAC,QAAQ,CAAC,cAAc,2BAAkC;gBAEvD,QAAQ,EAAE,mBAAmB;IAMzC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;IAW5D,SAAS,CAAC,oBAAoB,CAAC,WAAW,EAAE,YAAY,GAAG,YAAY;IAKvE,SAAS,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,GAAG,gBAAgB;IAiBlE,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,GAAG,MAAM;IAmC5E,SAAS,CAAC,kBAAkB,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,GAAG,GAAG;IASlF,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,GAAG,GAAG;IAwBzE,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;IAcjF,SAAS,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,GAAG,cAAc;IA8B3D,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO;IAmCrE,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,GAAG,GAAG;IAKhD,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,GAAG,SAAS;IAI3G,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,SAAI,GAAG,OAAO;IAejF,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,GAAG,WAAW;IA4BvD,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS;IAI/C,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,eAAe,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS;IAUpF,SAAS,CAAC,iBAAiB,CAAC,EAAE,EAAE,MAAM,GAAG,eAAe,GAAG,SAAS;IAQpE,SAAS,CAAC,yBAAyB,IAAI,IAAI;CAS9C"}