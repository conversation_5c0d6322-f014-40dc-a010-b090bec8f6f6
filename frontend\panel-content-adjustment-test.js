#!/usr/bin/env node

/**
 * 面板内容向上调整验证脚本
 * 验证实时分析状态和文本分析结果面板内部内容的向上移动调整
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 验证面板内容向上调整...\n')

// 检查的文件
const targetFile = 'src/views/TextPrimaryInterviewPage.vue'
const fullPath = path.join(__dirname, targetFile)

console.log(`📄 检查文件: ${targetFile}`)

if (!fs.existsSync(fullPath)) {
  console.log(`❌ 文件不存在: ${targetFile}`)
  process.exit(1)
}

const content = fs.readFileSync(fullPath, 'utf8')
console.log(`✅ 文件存在 (${content.length} 字符)\n`)

// 验证项目
const verificationChecks = [
  {
    name: '实时分析状态面板内容区域调整',
    pattern: /\.status-panel-external\s+\.processing-stats\s*{[^}]*margin-top:\s*-4px[^}]*}/s,
    description: '检查实时分析状态面板的processing-stats区域margin-top是否调整为-4px'
  },
  {
    name: '文本分析结果面板内容区域调整',
    pattern: /\.results-panel-external\s+\.score-breakdown\s*{[^}]*margin-top:\s*-4px[^}]*}/s,
    description: '检查文本分析结果面板的score-breakdown区域margin-top是否调整为-4px'
  },
  {
    name: '状态面板项目对齐方式调整',
    pattern: /\.status-panel-external\s+\.stat-item\s*{[^}]*justify-content:\s*flex-start[^}]*}/s,
    description: '检查状态面板项目是否改为顶部对齐'
  },
  {
    name: '得分面板项目对齐方式调整',
    pattern: /\.results-panel-external\s+\.score-item\s*{[^}]*justify-content:\s*flex-start[^}]*}/s,
    description: '检查得分面板项目是否改为顶部对齐'
  },
  {
    name: '状态标签向上微调',
    pattern: /\.status-panel-external\s+\.stat-label\s*{[^}]*transform:\s*translateY\(-2px\)[^}]*}/s,
    description: '检查状态标签是否添加了向上2px的微调'
  },
  {
    name: '状态数值向上微调',
    pattern: /\.status-panel-external\s+\.stat-value\s*{[^}]*transform:\s*translateY\(-2px\)[^}]*}/s,
    description: '检查状态数值是否添加了向上2px的微调'
  },
  {
    name: '得分标签向上微调',
    pattern: /\.results-panel-external\s+\.score-name\s*{[^}]*transform:\s*translateY\(-2px\)[^}]*}/s,
    description: '检查得分标签是否添加了向上2px的微调'
  },
  {
    name: '得分数值向上微调',
    pattern: /\.results-panel-external\s+\.score-value\s*{[^}]*transform:\s*translateY\(-2px\)[^}]*}/s,
    description: '检查得分数值是否添加了向上2px的微调'
  }
]

let allChecksPass = true
let passedChecks = 0

console.log('🔧 执行面板内容调整验证...\n')

verificationChecks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name}`)
  console.log(`   描述: ${check.description}`)
  
  const result = check.pattern.test(content)
  
  if (result) {
    console.log(`   ✅ 通过`)
    passedChecks++
  } else {
    console.log(`   ❌ 失败`)
    allChecksPass = false
  }
  
  console.log()
})

// 额外检查：提取实际的调整值
console.log('📊 实际调整值检查:')

// 检查processing-stats的margin-top
const processingStatsMatch = content.match(/\.status-panel-external\s+\.processing-stats\s*{[^}]*margin-top:\s*([^;]+);/s)
if (processingStatsMatch) {
  console.log(`   实时分析状态内容区域: margin-top: ${processingStatsMatch[1].trim()}`)
} else {
  console.log(`   实时分析状态内容区域: 未找到margin-top设置`)
}

// 检查score-breakdown的margin-top
const scoreBreakdownMatch = content.match(/\.results-panel-external\s+\.score-breakdown\s*{[^}]*margin-top:\s*([^;]+);/s)
if (scoreBreakdownMatch) {
  console.log(`   文本分析结果内容区域: margin-top: ${scoreBreakdownMatch[1].trim()}`)
} else {
  console.log(`   文本分析结果内容区域: 未找到margin-top设置`)
}

// 检查transform设置
const transformMatches = content.match(/transform:\s*translateY\([^)]+\)/g) || []
console.log(`   发现 ${transformMatches.length} 个transform调整`)
transformMatches.forEach((match, index) => {
  console.log(`     ${index + 1}. ${match}`)
})

console.log()

// 生成测试报告
console.log('📊 面板内容调整验证报告')
console.log('='.repeat(50))

if (allChecksPass) {
  console.log('🎉 面板内容向上调整成功！')
  console.log('')
  console.log('✅ 完成的调整:')
  console.log('  - 实时分析状态面板内容区域向上移动16px (margin-top: -4px)')
  console.log('  - 文本分析结果面板内容区域向上移动16px (margin-top: -4px)')
  console.log('  - 所有内容项目改为顶部对齐 (justify-content: flex-start)')
  console.log('  - 文字标签和数值向上微调2px (transform: translateY(-2px))')
  console.log('')
  console.log('✅ 影响的内容:')
  console.log('  - 已处理消息数、分析耗时等状态信息')
  console.log('  - 技术能力、沟通技巧、表达能力、逻辑思维等得分数据')
  console.log('  - 所有文字标签和数值显示')
  console.log('')
  console.log('✅ 视觉效果:')
  console.log('  - 面板内容更加紧凑')
  console.log('  - 文字和数字向上移动，减少空白区域')
  console.log('  - 保持良好的可读性和视觉层次')
} else {
  console.log('⚠️  面板内容调整验证中发现问题')
  console.log('')
  console.log(`📊 验证结果: ${passedChecks}/${verificationChecks.length} 项通过`)
  console.log('')
  console.log('💡 建议:')
  console.log('  - 检查上述失败的验证项目')
  console.log('  - 确保CSS修改正确应用')
  console.log('  - 重新运行此验证脚本')
}

console.log('')
console.log('🔗 相关信息:')
console.log('  - 目标文件: src/views/TextPrimaryInterviewPage.vue')
console.log('  - 调整类型: 面板内部内容向上移动')
console.log('  - 主要调整: margin-top: -4px + transform: translateY(-2px)')
console.log('  - 影响区域: .processing-stats, .score-breakdown, 文字和数值')

console.log('')
console.log('📞 测试访问:')
console.log('  - 文本面试页面: http://localhost:8080/text-primary-interview')
console.log('  - 产品演示页面: http://localhost:8080/demo')

console.log('')
console.log('🎯 预期效果:')
console.log('  - 面板内的得分数字向上移动')
console.log('  - 面板内的文字标签向上移动')
console.log('  - 整体内容更加紧凑，减少空白')
console.log('  - 保持良好的视觉平衡和可读性')

export { allChecksPass, passedChecks, verificationChecks }
