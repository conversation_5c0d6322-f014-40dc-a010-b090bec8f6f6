{"name": "knitwork", "version": "1.2.0", "description": "Utilities to generate JavaScript code.", "repository": "unjs/knitwork", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": "./dist/index.mjs", "types": "./dist/index.d.ts", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest dev --coverage", "lint": "eslint --cache . && prettier -c src test", "lint:fix": "automd && eslint --cache . --fix && prettier -c src test -w", "prepack": "pnpm run build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@vitest/coverage-v8": "^2.1.8", "automd": "^0.3.12", "changelogen": "^0.5.7", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-config-unjs": "^0.4.2", "prettier": "^3.4.2", "typescript": "^5.7.2", "unbuild": "^3.0.1", "vitest": "^2.1.8"}, "packageManager": "pnpm@9.15.0"}