import fs from 'fs';

function checkCSSBraces(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let inStyleSection = false;
  let braceCount = 0;
  let lineNumber = 0;
  
  for (const line of lines) {
    lineNumber++;
    
    if (line.includes('<style')) {
      inStyleSection = true;
      console.log(`开始CSS部分: 第${lineNumber}行`);
      continue;
    }
    
    if (line.includes('</style>')) {
      inStyleSection = false;
      console.log(`结束CSS部分: 第${lineNumber}行，最终大括号计数: ${braceCount}`);
      break;
    }
    
    if (inStyleSection) {
      const openBraces = (line.match(/\{/g) || []).length;
      const closeBraces = (line.match(/\}/g) || []).length;
      
      braceCount += openBraces - closeBraces;
      
      if (openBraces > 0 || closeBraces > 0) {
        console.log(`第${lineNumber}行: "${line.trim()}" | 开放: ${openBraces}, 闭合: ${closeBraces}, 累计: ${braceCount}`);
      }
      
      if (braceCount < 0) {
        console.log(`❌ 错误: 第${lineNumber}行有多余的闭合大括号!`);
        console.log(`问题行: "${line}"`);
        break;
      }
    }
  }
  
  if (braceCount !== 0) {
    console.log(`❌ CSS大括号不匹配! 最终计数: ${braceCount}`);
  } else {
    console.log(`✅ CSS大括号匹配正确!`);
  }
}

checkCSSBraces('src/views/ReportView.vue');
