{"version": 3, "file": "linker.js", "sourceRoot": "", "sources": ["../../src/references/linker.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAOhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACpF,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAClF,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAsD1D,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;AAO9C,MAAM,OAAO,aAAa;IAMtB,YAAY,QAA6B;QACrC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QAChD,IAAI,CAAC,gBAAgB,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACzE,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC;QACvD,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAyB,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QACtE,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAES,MAAM,CAAC,OAAsB,EAAE,QAAyB;;QAC9D,MAAM,GAAG,GAAG,OAAO,CAAC,SAA6B,CAAC;QAClD,uFAAuF;QACvF,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACzB,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC;YACzB,IAAI,CAAC;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBAC/C,IAAI,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC9B,GAAG,CAAC,IAAI,GAAG,WAAW,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACJ,GAAG,CAAC,gBAAgB,GAAG,WAAW,CAAC;oBACnC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC/D,wCAAwC;wBACxC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;wBACjD,GAAG,CAAC,IAAI,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;oBAC3E,CAAC;yBAAM,CAAC;wBACJ,+EAA+E;wBAC/E,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;oBACzB,CAAC;gBACL,CAAC;YACL,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,OAAO,CAAC,KAAK,CAAC,mDAAmD,GAAG,CAAC,QAAQ,IAAI,EAAE,GAAG,CAAC,CAAC;gBACxF,MAAM,YAAY,GAAG,MAAC,GAAa,CAAC,OAAO,mCAAI,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC3D,GAAG,CAAC,IAAI,mCACD,OAAO,KACV,OAAO,EAAE,mDAAmD,GAAG,CAAC,QAAQ,MAAM,YAAY,EAAE,GAC/F,CAAC;YACN,CAAC;YACD,0DAA0D;YAC1D,+DAA+D;YAC/D,sCAAsC;YACtC,8CAA8C;YAC9C,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,QAAyB;QAC5B,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACpC,OAAQ,GAAwB,CAAC,IAAI,CAAC;YACtC,OAAQ,GAAwB,CAAC,gBAAgB,CAAC;QACtD,CAAC;QACD,QAAQ,CAAC,UAAU,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED,YAAY,CAAC,OAAsB;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACjE,OAAO,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,cAAc,CAAC,IAAa,EAAE,QAAgB,EAAE,OAA4B,EAAE,OAAe;QACzF,6EAA6E;QAC7E,4DAA4D;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,MAAM,SAAS,GAAqB;YAChC,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,OAAO;YAEjB,IAAI,GAAG;;gBACH,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvB,sDAAsD;oBACtD,OAAO,IAAI,CAAC,IAAI,CAAC;gBACrB,CAAC;qBAAM,IAAI,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBACrD,+DAA+D;oBAC/D,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAC7D,IAAI,CAAC,IAAI,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAClB,MAAM,CAAC,kBAAkB,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnG,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACjC,yDAAyD;oBACzD,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;oBAC1B,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;oBAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;oBAC/E,IAAI,OAAO,CAAC,KAAK,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;wBAC7E,gFAAgF;wBAChF,OAAO,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;oBACjC,CAAC;oBACD,IAAI,CAAC,IAAI,GAAG,MAAA,OAAO,CAAC,IAAI,mCAAI,OAAO,CAAC,KAAK,CAAC;oBAC1C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC;oBACtC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;oBACrC,MAAM,IAAI,KAAK,CAAC,yCAAyC,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,QAAQ,aAAa,OAAO,IAAI,CAAC,CAAC;gBAC7I,CAAC;gBACD,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;YACxD,CAAC;YACD,IAAI,gBAAgB;gBAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC;YACjC,CAAC;YACD,IAAI,KAAK;gBACL,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;YAC7D,CAAC;SACJ,CAAC;QACF,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,aAAa,CAAC,OAAsB;;QAC1C,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9B,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;YAClC,CAAC;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACjD,IAAI,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;YACpD,CAAC;iBACI,CAAC;gBACF,OAAO;oBACH,KAAK,EAAE,WAAW;oBAClB,KAAK,EACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC;iBACpD,CAAC;YACN,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,mDAAmD,OAAO,CAAC,SAAS,CAAC,QAAQ,IAAI,EAAE,GAAG,CAAC,CAAC;YACtG,MAAM,YAAY,GAAG,MAAC,GAAa,CAAC,OAAO,mCAAI,MAAM,CAAC,GAAG,CAAC,CAAC;YAC3D,OAAO;gBACH,KAAK,kCACE,OAAO,KACV,OAAO,EAAE,mDAAmD,OAAO,CAAC,SAAS,CAAC,QAAQ,MAAM,YAAY,EAAE,GAC7G;aACJ,CAAC;QACN,CAAC;IACL,CAAC;IAES,WAAW,CAAC,eAAmC;QACrD,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;YACvB,OAAO,eAAe,CAAC,IAAI,CAAC;QAChC,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC7E,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAES,kBAAkB,CAAC,OAAsB,EAAE,iBAAsC;QACvF,gHAAgH;QAChH,kCAAkC;QAClC,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;QAC3D,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,gFAAgF,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;QACnH,CAAC;QACD,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAChE,uCACO,OAAO,KACV,OAAO,EAAE,kCAAkC,aAAa,WAAW,OAAO,CAAC,SAAS,CAAC,QAAQ,IAAI,EACjG,iBAAiB,IACnB;IACN,CAAC;CAEJ"}