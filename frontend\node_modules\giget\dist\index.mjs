export { d as downloadTemplate, r as registryProvider, s as startShell } from './shared/giget.OCaTp9b-.mjs';
import 'node:fs/promises';
import 'node:fs';
import 'assert';
import 'path';
import 'events';
import 'stream';
import 'string_decoder';
import 'buffer';
import 'zlib';
import 'process';
import 'fs';
import 'util';
import 'crypto';
import 'pathe';
import 'defu';
import 'nypm';
import 'node:stream';
import 'node:child_process';
import 'node:os';
import 'node:util';
import 'node-fetch-native/proxy';
