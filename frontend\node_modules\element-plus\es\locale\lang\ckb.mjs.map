{"version": 3, "file": "ckb.mjs", "sources": ["../../../../../packages/locale/lang/ckb.ts"], "sourcesContent": ["export default {\n  name: 'ckb',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'باشە',\n      clear: 'پاککردنەوە',\n      defaultLabel: 'هەڵبژاردنی ڕەنگ',\n      description:\n        'ڕەنگی ئێستا {color}. ئینتەر دابگرە بۆ هەڵبژاردنی ڕەنگی نوێ.',\n    },\n    datepicker: {\n      now: 'ئێستا',\n      today: 'ئەمڕۆ',\n      cancel: 'پەشیمانبوونەوە',\n      clear: 'پاککردنەوە',\n      confirm: 'باشە',\n      dateTablePrompt:\n        'کلیلی ئاراستەکان بەکاربهێنەر بۆ هەڵبژاردنی ڕۆژی مانگەکە',\n      monthTablePrompt: 'کلیلی ئاراستەکان بەکاربهێنەر بۆ هەڵبژاردنی مانگ',\n      yearTablePrompt: 'کلیلی ئاراستەکان بەکاربهێنەر بۆ هەڵبژاردنی ساڵ',\n      selectedDate: 'بەرواری هەڵبژێردراو',\n      selectDate: 'هەڵبژاردنی بەروار',\n      selectTime: 'هەڵبژاردنی کات',\n      startDate: 'بەرواری دەستپێک',\n      startTime: 'کاتی دەستپێک',\n      endDate: 'بەرواری کۆتایی',\n      endTime: 'کاتی کۆتایی',\n      prevYear: 'ساڵی پێشوو',\n      nextYear: 'ساڵ داهاتوو',\n      prevMonth: 'مانگی پێشوو',\n      nextMonth: 'مانگی داهاتوو',\n      year: '',\n      month1: 'ڕێبەندان',\n      month2: 'ڕەشەمە',\n      month3: 'نەورۆز',\n      month4: 'گوڵان',\n      month5: 'جۆزەردان',\n      month6: 'پووشپەڕ',\n      month7: 'گەلاوێژ',\n      month8: 'خەرمانان',\n      month9: 'ڕەزبەر',\n      month10: 'گەڵاڕێزان',\n      month11: 'سەرماوەز',\n      month12: 'بەفرانبار',\n      week: 'هەفت',\n      weeks: {\n        sun: 'یەکشەممە',\n        mon: 'دووشەممە',\n        tue: 'سێشەممە',\n        wed: 'چوارشەممە',\n        thu: 'پێنجشەممە',\n        fri: 'هەینی',\n        sat: 'شەممە',\n      },\n      weeksFull: {\n        sun: 'یەکشەممە',\n        mon: 'دووشەممە',\n        tue: 'سێشەممە',\n        wed: 'چوارشەممە',\n        thu: 'پێنجشەممە',\n        fri: 'هەینی',\n        sat: 'شەممە',\n      },\n      months: {\n        jan: 'ڕێبەندان',\n        feb: 'ڕەشەمە',\n        mar: 'نەورۆز',\n        apr: 'گوڵان',\n        may: 'جۆزەردان',\n        jun: 'پووشپەڕ',\n        jul: 'گەلاوێژ',\n        aug: 'خەرمانان',\n        sep: 'ڕەزبەر',\n        oct: 'گەڵاڕێزان',\n        nov: 'سەرماوەز',\n        dec: 'بەفرانبار',\n      },\n    },\n    inputNumber: {\n      decrease: 'کەمکردنەوەی ژمارە',\n      increase: 'زیادکردنی ژمارە',\n    },\n    select: {\n      loading: 'بارکردن',\n      noMatch: 'هیچ داتایەکی هاوتا نیە',\n      noData: 'هیچ داتایەک نیە',\n      placeholder: 'هەڵبژاردن',\n    },\n    mention: {\n      loading: 'بارکردن',\n    },\n    dropdown: {\n      toggleDropdown: 'کردنەوەو داخستنی کشاو',\n    },\n    cascader: {\n      noMatch: 'هیچ داتایەکی هاوتا نیە',\n      loading: 'بارکردن',\n      placeholder: 'هەڵبژاردن',\n      noData: 'هیچ داتایەک نیە',\n    },\n    pagination: {\n      goto: 'بڕۆ بۆ',\n      pagesize: '/لاپەڕە',\n      total: 'کۆی گشتیی {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'بەکارهێنانی بەکارنەهێنراو دۆزراوەتەوە، تکایە بۆ وردەکاری زیاتر سەردانی بەڵگەنامەکانی el-pagination بکە',\n    },\n    dialog: {\n      close: 'داخستنی ئەم دیالۆگە',\n    },\n    drawer: {\n      close: 'داخستنی ئەم دیالۆگە',\n    },\n    messagebox: {\n      title: 'پەیام',\n      confirm: 'باشە',\n      cancel: 'پەشایمانبوونەوە',\n      error: 'داخلکردنی نایاسایی',\n      close: 'داخستنی ئەم دیالۆگە',\n    },\n    upload: {\n      deleteTip: 'فشار لەسەر سڕینەوە بکە بۆ لابردن',\n      delete: 'سڕینەوە',\n      preview: 'بینینەوە',\n      continue: 'بەردەوامبوون',\n    },\n    slider: {\n      defaultLabel: 'سلاید لە نێوان {min} و {max}',\n      defaultRangeStartLabel: 'بەهای دەستپێک هەلبژێرە',\n      defaultRangeEndLabel: 'بەهای کۆتایی هەلبژێرە',\n    },\n    table: {\n      emptyText: 'هیچ داتا نیە',\n      confirmFilter: 'دووپاتکردنەوە',\n      resetFilter: 'جێگیرکردنەوە',\n      clearFilter: 'هەموو',\n      sumText: 'کۆ',\n    },\n    tree: {\n      emptyText: 'هیچ داتا نیە',\n    },\n    transfer: {\n      noMatch: 'هیچ داتای هاوتا نیە',\n      noData: 'هیچ داتا نیە',\n      titles: ['لیستی 1', 'لیستی 2'], // to be translated\n      filterPlaceholder: 'کلیلەوشە داخڵ بکە', // to be translated\n      noCheckedFormat: '{total} دانە', // to be translated\n      hasCheckedFormat: '{checked}/{total} هەڵبژێردراوە', // to be translated\n    },\n    image: {\n      error: 'شکستی هێنا',\n    },\n    pageHeader: {\n      title: 'گەڕانەوە', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'بەڵێ',\n      cancelButtonText: 'نەخێر',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,UAAe;AACf,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,KAAK,EAAE,8DAA8D;AAC3E,MAAM,YAAY,EAAE,uFAAuF;AAC3G,MAAM,WAAW,EAAE,+QAA+Q;AAClS,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,gCAAgC;AAC3C,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,sFAAsF;AACpG,MAAM,KAAK,EAAE,8DAA8D;AAC3E,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,eAAe,EAAE,8SAA8S;AACrU,MAAM,gBAAgB,EAAE,mQAAmQ;AAC3R,MAAM,eAAe,EAAE,6PAA6P;AACpR,MAAM,YAAY,EAAE,+GAA+G;AACnI,MAAM,UAAU,EAAE,mGAAmG;AACrH,MAAM,UAAU,EAAE,iFAAiF;AACnG,MAAM,SAAS,EAAE,uFAAuF;AACxG,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,QAAQ,EAAE,yDAAyD;AACzE,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,SAAS,EAAE,2EAA2E;AAC5F,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,wDAAwD;AACrE,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,mGAAmG;AACnH,MAAM,QAAQ,EAAE,uFAAuF;AACvG,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,uHAAuH;AACtI,MAAM,MAAM,EAAE,kFAAkF;AAChG,MAAM,WAAW,EAAE,wDAAwD;AAC3E,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,4CAA4C;AAC3D,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,sHAAsH;AAC5I,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,uHAAuH;AACtI,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,WAAW,EAAE,wDAAwD;AAC3E,MAAM,MAAM,EAAE,kFAAkF;AAChG,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,iCAAiC;AAC7C,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,KAAK,EAAE,2DAA2D;AACxE,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,kBAAkB,EAAE,mfAAmf;AAC7gB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,0GAA0G;AACvH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,0GAA0G;AACvH,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,MAAM,EAAE,4FAA4F;AAC1G,MAAM,KAAK,EAAE,yGAAyG;AACtH,MAAM,KAAK,EAAE,0GAA0G;AACvH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,yKAAyK;AAC1L,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,QAAQ,EAAE,0EAA0E;AAC1F,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,+FAA+F;AACnH,MAAM,sBAAsB,EAAE,4HAA4H;AAC1J,MAAM,oBAAoB,EAAE,sHAAsH;AAClJ,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gEAAgE;AACjF,MAAM,aAAa,EAAE,gFAAgF;AACrG,MAAM,WAAW,EAAE,0EAA0E;AAC7F,MAAM,WAAW,EAAE,gCAAgC;AACnD,MAAM,OAAO,EAAE,cAAc;AAC7B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gEAAgE;AACjF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,qGAAqG;AACpH,MAAM,MAAM,EAAE,gEAAgE;AAC9E,MAAM,MAAM,EAAE,CAAC,kCAAkC,EAAE,kCAAkC,CAAC;AACtF,MAAM,iBAAiB,EAAE,8FAA8F;AACvH,MAAM,eAAe,EAAE,kCAAkC;AACzD,MAAM,gBAAgB,EAAE,4FAA4F;AACpH,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,yDAAyD;AACtE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,kDAAkD;AAC/D,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,0BAA0B;AACnD,MAAM,gBAAgB,EAAE,gCAAgC;AACxD,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}