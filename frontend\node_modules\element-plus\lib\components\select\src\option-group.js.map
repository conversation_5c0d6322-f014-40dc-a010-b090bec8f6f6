{"version": 3, "file": "option-group.js", "sources": ["../../../../../../packages/components/select/src/option-group.vue"], "sourcesContent": ["<template>\n  <ul v-show=\"visible\" ref=\"groupRef\" :class=\"ns.be('group', 'wrap')\">\n    <li :class=\"ns.be('group', 'title')\">{{ label }}</li>\n    <li>\n      <ul :class=\"ns.b('group')\">\n        <slot />\n      </ul>\n    </li>\n  </ul>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  isVNode,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRefs,\n} from 'vue'\nimport { useMutationObserver } from '@vueuse/core'\nimport { ensureArray, isArray } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { selectGroupKey } from './token'\n\nimport type { Component, VNode, VNodeArrayChildren } from 'vue'\nimport type { OptionInternalInstance, OptionPublicInstance } from './type'\n\nexport default defineComponent({\n  name: 'ElOptionGroup',\n  componentName: 'ElOptionGroup',\n\n  props: {\n    /**\n     * @description name of the group\n     */\n    label: String,\n    /**\n     * @description whether to disable all options in this group\n     */\n    disabled: Boolean,\n  },\n  setup(props) {\n    const ns = useNamespace('select')\n    const groupRef = ref<HTMLElement>()\n    const instance = getCurrentInstance()!\n    const children = ref<OptionPublicInstance[]>([])\n\n    provide(\n      selectGroupKey,\n      reactive({\n        ...toRefs(props),\n      })\n    )\n\n    const visible = computed(() =>\n      children.value.some((option) => option.visible === true)\n    )\n\n    const isOption = (\n      node: VNode\n    ): node is VNode & { component: OptionInternalInstance } =>\n      (node.type as Component).name === 'ElOption' && !!node.component?.proxy\n\n    // get all instances of options\n    const flattedChildren = (node: VNode | VNodeArrayChildren) => {\n      const nodes = ensureArray(node) as VNode[] | VNodeArrayChildren\n      const children: OptionPublicInstance[] = []\n\n      nodes.forEach((child) => {\n        if (!isVNode(child)) return\n\n        if (isOption(child)) {\n          children.push(child.component.proxy)\n        } else if (isArray(child.children) && child.children.length) {\n          children.push(...flattedChildren(child.children))\n        } else if (child.component?.subTree) {\n          children.push(...flattedChildren(child.component.subTree))\n        }\n      })\n\n      return children\n    }\n\n    const updateChildren = () => {\n      children.value = flattedChildren(instance.subTree)\n    }\n\n    onMounted(() => {\n      updateChildren()\n    })\n\n    useMutationObserver(groupRef, updateChildren, {\n      attributes: true,\n      subtree: true,\n      childList: true,\n    })\n\n    return {\n      groupRef,\n      visible,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "useNamespace", "ref", "getCurrentInstance", "provide", "selectGroupKey", "reactive", "toRefs", "ensureArray", "isArray", "onMounted", "children", "useMutationObserver", "_withDirectives", "_openBlock", "_createElementBlock", "_normalizeClass", "_toDisplayString", "_createElementVNode", "_renderSlot", "_vShow", "_export_sfc"], "mappings": ";;;;;;;;;;;;AA+BA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,eAAA;AAAA,EACN,aAAe,EAAA,eAAA;AAAA,EAEf,KAAO,EAAA;AAAA,IAAA,KAAA,EAAA,MAAA;AAAA,IAAA,QAAA,EAAA,OAAA;AAAA,GAAA;AAAA,EAAA,KAIE,CAAA,KAAA,EAAA;AAAA,IAAA,MAAA,EAAA,GAAAC,kBAAA,CAAA,QAAA,CAAA,CAAA;AAAA,IAAA,MAAA,QAAA,GAAAC,OAAA,EAAA,CAAA;AAAA,IAAA,MAAA,QAAA,GAAAC,sBAAA,EAAA,CAAA;AAAA,IAIP,MAAU,QAAA,GAAAD,OAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IACZE,WAAA,CAAAC,oBAAA,EAAAC,YAAA,CAAA;AAAA,SACaC,UAAA,CAAA,KAAA,CAAA;AACX,KAAM,CAAA,CAAA,CAAA;AACN,IAAA,MAAM,sBAA4B,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA,MAAA,CAAA,OAAA,KAAA,IAAA,CAAA,CAAA,CAAA;AAClC,IAAA,MAAM,WAAW,CAAmB,IAAA,KAAA;AACpC,MAAM,IAAA,EAAA,CAAA;AAEN,MAAA,OAAA,IAAA,CAAA,IAAA,CAAA,IAAA,KAAA,UAAA,IAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACE,CAAA;AAAA,IAAA,MACS,eAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACP,cAAeC,uBAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACjB,MAAC,SAAA,GAAA,EAAA,CAAA;AAAA,MACH,KAAA,CAAA,OAAA,CAAA,CAAA,KAAA,KAAA;AAEA,QAAA,IAAgB,EAAA,CAAA;AAAA,QAAS,IACvB,aAAS,KAAM,CAAA;AAAwC,UACzD,OAAA;AAEA,QAAM,IAAA,QAAA,CAAW,KACf,CAAA,EAAA;AAKF,UAAM,SAAA,CAAA,IAAA,CAAA,KAAwD,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAC5D,SAAM,MAAA,IAAAC,oBAAoB,CAAI,QAAA,CAAA,IAAA,KAAA,CAAA,QAAA,CAAA,MAAA,EAAA;AAC9B,UAAA,cAAyC,CAAC,GAAA,eAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAE1C,SAAM,MAAA,IAAA,CAAQ,EAAW,GAAA,KAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA;AACvB,UAAI,SAAS,CAAA,IAAA,CAAA,GAAQ,eAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAErB,SAAI;AACF,OAAA,CAAA,CAAA;AAAmC,MAAA,gBAClB,CAAA;AACjB,KAAA,CAAA;AAAgD,IAClD,MAAA,cAAiB,GAAA,MAAA;AACf,MAAA,cAAS,GAAK,wBAAyB,CAAA,OAAA,CAAA,CAAA;AAAkB,KAC3D,CAAA;AAAA,IAAAC,aACD,CAAA,MAAA;AAED,MAAOC,cAAAA,EAAAA,CAAAA;AAAA,KACT,CAAA,CAAA;AAEA,IAAAC,iCAA6B,EAAA,cAAA,EAAA;AAC3B,MAAS,UAAA,EAAA,IAAA;AAAwC,MACnD,OAAA,EAAA,IAAA;AAEA,MAAA,SAAgB,EAAA,IAAA;AACd,KAAe,CAAA,CAAA;AAAA,IACjB,OAAC;AAED,MAAA,QAAA;AAA8C,MAC5C,OAAY;AAAA,MACZ,EAAS;AAAA,KAAA,CACT;AAAW,GAAA;AAGb,CAAO,CAAA,CAAA;AACL,SACA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,OAAAC,kBAAA,EAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,IAAA,EAAA;AAAA,IACF,GAAA,EAAA,UAAA;AAAA,IACF,KAAA,EAAAC,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;AACF,GAAC,EAAA;;;AA1GC,KAAA,EAAAC,mBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IAOKC,sBAAA,CAAA,IAAA,EAAA,IAAA,EAAA;AAAA,MAAAA,sBAAA,CAAA,IAAA,EAAA;AAAA,QAPoB,KAAA,EAAAF,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,OAAY,EAAA;AAAY,QAAAG,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;;AAC/C,KAAA,CAAA;AAAA,GAAqD,EAAA,CAAA,CAAA,GAAA;AAAA,IAAA,CAAAC,SAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AAAA,GAAhD,CAAA,CAAA;AAAY,CAAA;AAA4B,kBAAA,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;;;;"}