{"version": 3, "file": "promise-utils.js", "sourceRoot": "", "sources": ["../../src/utils/promise-utils.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,iBAAiB,EAAE,uBAAuB,EAAwC,MAAM,0BAA0B,CAAC;AAI5H;;;GAGG;AACH,MAAM,UAAU,aAAa;IACzB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QACzB,oFAAoF;QACpF,sEAAsE;QACtE,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;YACtC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC;aAAM,CAAC;YACJ,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,IAAI,QAAQ,GAAG,CAAC,CAAC;AACjB,IAAI,wBAAwB,GAAG,EAAE,CAAC;AAElC;;GAEG;AACH,MAAM,UAAU,wBAAwB;IACpC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;IAC7B,OAAO,IAAI,uBAAuB,EAAE,CAAC;AACzC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CAAC,MAAc;IAChD,wBAAwB,GAAG,MAAM,CAAC;AACtC,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAE/D;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAAC,GAAY;IAC7C,OAAO,GAAG,KAAK,kBAAkB,CAAC;AACtC,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,KAAwB;IAC5D,IAAI,KAAK,KAAK,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACnC,6DAA6D;QAC7D,OAAO;IACX,CAAC;IACD,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;IAClC,IAAI,OAAO,GAAG,QAAQ,IAAI,wBAAwB,EAAE,CAAC;QACjD,QAAQ,GAAG,OAAO,CAAC;QACnB,MAAM,aAAa,EAAE,CAAC;QACtB,wDAAwD;QACxD,0DAA0D;QAC1D,kCAAkC;QAClC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;IACjC,CAAC;IACD,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;QAChC,MAAM,kBAAkB,CAAC;IAC7B,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,QAAQ;IAArB;QAII,YAAO,GAAG,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,EAAE,EAAE;gBACnB,OAAO,CAAC,GAAG,CAAC,CAAC;gBACb,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,EAAE,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,CAAC;gBACZ,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;CAAA"}