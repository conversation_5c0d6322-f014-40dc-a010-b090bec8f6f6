{"version": 3, "file": "completion-provider.js", "sourceRoot": "", "sources": ["../../../src/lsp/completion/completion-provider.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAmBhF,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACrF,OAAO,KAAK,GAAG,MAAM,kCAAkC,CAAC;AACxD,OAAO,EAAE,yBAAyB,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AACzF,OAAO,EAAE,2BAA2B,EAAE,wBAAwB,EAAE,MAAM,0BAA0B,CAAC;AACjG,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACjF,OAAO,EAAE,MAAM,EAAe,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAgEtF,MAAM,UAAU,8BAA8B,CAAC,OAAqD;IAChG,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,WAAC,OAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,mCAAI,EAAE,CAAA,EAAA,CAAC,CAAC,CAAC,CAAC;IAC1G,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,WAAC,OAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,mBAAmB,mCAAI,EAAE,CAAA,EAAA,CAAC,CAAC,CAAC,CAAC;IAC9G,OAAO;QACH,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;QAC/E,mBAAmB,EAAE,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS;KACxF,CAAC;AACN,CAAC;AAyBD,MAAM,OAAO,yBAAyB;IAclC,YAAY,QAAyB;QACjC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACzD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC;QAC7D,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC;IAC9E,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAyB,EAAE,MAAwB,EAAE,YAAgC;QACrG,MAAM,KAAK,GAAqB,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE/D,MAAM,QAAQ,GAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,cAAc,EAAE,CAAC;gBACjB,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,mBAAmB,GAAG,CAAC,OAAoB,EAAE,EAAE;YACjD,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACjC,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACJ,OAAO,OAAO,CAAC,OAAO,CAAC;YAC3B,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,iBAAiB,GAAkB,EAAE,CAAC;QAC5C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,MAAM,OAAO,CAAC,GAAG,CACb,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;iBACnB,QAAQ,CAAC,mBAAmB,CAAC;iBAC7B,OAAO,CAAC,iBAAiB,CAAC;iBAC1B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAC1D,CAAC;YACF,yDAAyD;YACzD,iBAAiB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,MAAM;YACV,CAAC;QACL,CAAC;QAED,OAAO,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACO,gBAAgB,CAAC,KAAuB;QAC9C,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;IACjG,CAAC;IAES,cAAc,CAAC,QAAsB,EAAE,MAAc;QAC3D,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC;YAC1B,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,GAAG,EAAE,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC;SACnC,CAAC,CAAC;QACH,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QACnC,oFAAoF;QACpF,IAAI,YAAY,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAE,CAAC;YAC/C,MAAM,aAAa,GAAG,iBAAiB,CAAC;gBACpC,OAAO,EAAE,UAAU,CAAC,UAAU;gBAC9B,IAAI,EAAE,mBAAmB,CAAC,UAAU,CAAC;aACxC,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpB,kCAAkC;gBAClC,kGAAkG;gBAClG,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,OAAO,gBAAgB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACJ,OAAO,aAAa,CAAC;YACzB,CAAC;QACL,CAAC;QACD,MAAM,cAAc,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACnE,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;QAC7G,OAAO,QAAQ,CAAC;IACpB,CAAC;IAES,CAAC,aAAa,CAAC,QAAyB,EAAE,QAAkB;;QAClE,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC;QAChD,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,OAAO;QACX,CAAC;QACD,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;QAC3C,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,cAAc,GAAG;YACnB,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,QAAQ;SACX,CAAC;QACF,iGAAiG;QACjG,sGAAsG;QACtG,MAAM,mBAAmB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACpE,IAAI,mBAAmB,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,mBAAmB,CAAC;YACjD,MAAM,UAAU,GAAG,MAAA,wBAAwB,CAAC,GAAG,EAAE,SAAS,CAAC,0CAAE,OAAO,CAAC;YACrE,sCACO,cAAc,KACjB,IAAI,EAAE,UAAU,EAChB,WAAW,EAAE,SAAS,EACtB,cAAc,EAAE,OAAO,EACvB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,SAAS,CAAC,GACzD,CAAC;QACN,CAAC;QACD,yFAAyF;QACzF,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACtH,IAAI,aAAa,GAAG,cAAc,CAAC;QACnC,IAAI,MAAM,IAAI,cAAc,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAC/D,6HAA6H;YAC7H,aAAa,GAAG,kBAAkB,CAAC;QACvC,CAAC;QACD,MAAM,OAAO,GAAG,MAAA,wBAAwB,CAAC,GAAG,EAAE,aAAa,CAAC,0CAAE,OAAO,CAAC;QACtE,IAAI,qBAAqB,GAAG,IAAI,CAAC;QACjC,IAAI,kBAAkB,KAAK,SAAS,IAAI,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,MAAM,EAAE,CAAC;YACpG,oDAAoD;YACpD,sCACO,cAAc,KACjB,IAAI,EAAE,OAAO,EACb,WAAW,EAAE,kBAAkB,EAC/B,cAAc,EAAE,gBAAgB,EAChC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,kBAAkB,CAAC,GAClE,CAAC;YACF,uIAAuI;YACvI,oEAAoE;YACpE,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CACnD,QAAQ,EACR,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,EACpD,kBAAkB,EAClB,gBAAgB,CACnB,CAAC;YACF,IAAI,qBAAqB,EAAE,CAAC;gBACxB,0GAA0G;gBAC1G,kDAAkD;gBAClD,sCACO,cAAc,KACjB,IAAI,EAAE,OAAO,EACb,WAAW,EAAE,gBAAgB,EAC7B,cAAc,EAAE,gBAAgB,EAChC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,gBAAgB,CAAC,GAChE,CAAC;YACN,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACjD,CAAC;YACD,qGAAqG;YACrG,sCACO,cAAc,KACjB,WAAW,EAAE,cAAc,EAC3B,cAAc,EAAE,YAAY,EAC5B,QAAQ,EAAE,iBAAiB,CAAC,UAAU,CAAC,UAAU,CAAC,GACrD,CAAC;QACN,CAAC;aAAM,IAAI,qBAAqB,EAAE,CAAC;YAC/B,+EAA+E;YAC/E,sCACO,cAAc,KACjB,IAAI,EAAE,OAAO,EACb,WAAW,EAAE,cAAc,EAC3B,cAAc,EAAE,YAAY,EAC5B,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,cAAc,CAAC,GAC9D,CAAC;QACN,CAAC;IACL,CAAC;IAES,0BAA0B,CAAC,QAAyB,EAAE,IAAY,EAAE,OAAe,EAAE,IAAY;QACvG,2DAA2D;QAC3D,0EAA0E;QAC1E,uEAAuE;QACvE,4FAA4F;QAC5F,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAES,qBAAqB,CAAC,GAAY,EAAE,MAAc;;QACxD,IAAI,aAAa,GAAwB,2BAA2B,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACjH,sEAAsE;QACtE,IAAI,cAAc,GAAG,OAAO,CAAC,MAAA,kBAAkB,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,aAAa,EAAE,GAAG,CAAC,YAAY,CAAC,0CAAE,QAAQ,CAAC,CAAC;QAC3G,IAAI,cAAc,EAAE,CAAC;YACjB,OAAO,cAAc,EAAE,CAAC;gBACpB,uDAAuD;gBACvD,aAAa,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,SAAS,CAAC;gBACzC,cAAc,GAAG,OAAO,CAAC,MAAA,kBAAkB,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,aAAa,EAAE,GAAG,CAAC,YAAY,CAAC,0CAAE,QAAQ,CAAC,CAAC;YAC3G,CAAC;YACD,IAAI,aAAa,EAAE,CAAC;gBAChB,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACO,kBAAkB,CAAC,KAAuB;QAChD,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;;;;OAQG;IACO,mBAAmB,CAAC,IAAY,EAAE,MAAc;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QAChD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,+EAA+E;YAC/E,OAAO;gBACH,cAAc,EAAE,MAAM;gBACtB,YAAY,EAAE,MAAM;aACvB,CAAC;QACN,CAAC;QACD,IAAI,aAAiC,CAAC;QACtC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,WAAW,IAAI,MAAM,EAAE,CAAC;gBAC9B,4BAA4B;gBAC5B,oDAAoD;gBACpD,OAAO;oBACH,cAAc,EAAE,MAAM;oBACtB,YAAY,EAAE,MAAM;oBACpB,kBAAkB,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;oBACzE,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,SAAU,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC7E,CAAC;YACN,CAAC;YACD,IAAI,KAAK,CAAC,SAAU,IAAI,MAAM,EAAE,CAAC;gBAC7B,wBAAwB;gBACxB,0DAA0D;gBAC1D,OAAO;oBACH,cAAc,EAAE,KAAK,CAAC,WAAW;oBACjC,YAAY,EAAE,KAAK,CAAC,SAAU,GAAG,CAAC;oBAClC,kBAAkB,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;oBACzE,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,SAAU,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC7E,CAAC;YACN,CAAC;YACD,aAAa,GAAG,KAAK,CAAC;QAC1B,CAAC;QACD,uCAAuC;QACvC,oDAAoD;QACpD,OAAO;YACH,cAAc,EAAE,MAAM;YACtB,YAAY,EAAE,MAAM;YACpB,kBAAkB,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;YACzE,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,SAAU,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;SAC7E,CAAC;IACN,CAAC;IAES,aAAa,CAAC,OAA0B,EAAE,IAAiB,EAAE,QAA4B;QAC/F,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACtE,CAAC;aAAM,IAAI,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,IAAuC,EAAE,QAAQ,CAAC,CAAC;QACxG,CAAC;QACD,iFAAiF;QACjF,yEAAyE;QACzE,6EAA6E;IACjF,CAAC;IAES,2BAA2B,CAAC,OAA0B,EAAE,IAAqC,EAAE,QAA4B;QACjI,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;QACtE,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACxB,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,gFAAgF;gBAChF,gGAAgG;gBAChG,0EAA0E;gBAC1E,IAAI,GAAG;oBACH,KAAK,EAAE,IAAI,CAAC,IAAI;oBAChB,UAAU,EAAE,IAAI;oBAChB,kBAAkB,EAAE,IAAI,CAAC,QAAQ;iBACpC,CAAC;gBACF,yBAAyB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YACxD,CAAC;YACD,MAAM,OAAO,GAAkB;gBAC3B,SAAS,EAAE;oBACP,QAAQ,EAAE,EAAE;iBACf;gBACD,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,UAAU,CAAC,OAAO;aAC/B,CAAC;YACF,IAAI,CAAC;gBACD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;oBACpE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC,CAAC;gBACrE,CAAC;YACL,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACO,sBAAsB,CAAC,OAAsB,EAAE,QAA2B;QAChF,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,CAAC;IACjE,CAAC;IAED;;;;;;;;OAQG;IACO,6BAA6B,CAAC,eAAmC;QACvE,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;QAC1E,MAAM,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,CAAC;QACtE,OAAO;YACH,eAAe;YACf,IAAI;YACJ,aAAa;YACb,MAAM,EAAE,eAAe,CAAC,IAAI;YAC5B,QAAQ,EAAE,GAAG;SAChB,CAAC;IACN,CAAC;IAES,yBAAyB,CAAC,eAAmC;QACnE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;YACxB,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC5F,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC;IAC1D,CAAC;IAES,oBAAoB,CAAC,OAA0B,EAAE,OAAoB,EAAE,QAA4B;QACzG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;YACxC,OAAO;QACX,CAAC;QACD,QAAQ,CAAC,OAAO,EAAE;YACd,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;YAChD,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,GAAG;SAChB,CAAC,CAAC;IACP,CAAC;IAES,4BAA4B,CAAC,QAAqB;QACxD,OAAO,kBAAkB,CAAC,OAAO,CAAC;IACtC,CAAC;IAES,aAAa,CAAC,OAA0B,EAAE,OAAoB;QACpE,6DAA6D;QAC7D,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAES,kBAAkB,CAAC,OAA0B,EAAE,IAAyB;;QAC9E,IAAI,KAAa,CAAC;QAClB,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACjC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,CAAC;aAAM,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,OAAO,SAAS,CAAC;YACrB,CAAC;YACD,KAAK,GAAG,IAAI,CAAC;QACjB,CAAC;aAAM,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;YACnC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QACtC,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,IAAI,UAAkB,CAAC;QACvB,IAAI,OAAO,CAAA,MAAA,IAAI,CAAC,QAAQ,0CAAE,OAAO,CAAA,KAAK,QAAQ,EAAE,CAAC;YAC7C,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;QACvC,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC7C,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,CAAC;aAAM,CAAC;YACJ,UAAU,GAAG,KAAK,CAAC;QACvB,CAAC;QACD,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,QAAQ,mCAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;QAC3F,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,gDAAgD;QAChD,MAAM,cAAc,GAAmB;YACnC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ;YACR,KAAK;SACR,CAAC;QACF,OAAO,cAAc,CAAC;IAC1B,CAAC;IAES,uBAAuB,CAAC,OAA0B,EAAE,KAAa,EAAE,OAAe;QACxF,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC/C,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAC1E,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACnE,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC;YAC7B,OAAO;gBACH,OAAO;gBACP,KAAK,EAAE;oBACH,KAAK;oBACL,GAAG;iBACN;aACJ,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;CACJ"}