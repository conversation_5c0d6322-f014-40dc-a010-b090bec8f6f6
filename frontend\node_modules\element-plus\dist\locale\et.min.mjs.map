{"version": 3, "file": "et.min.mjs", "sources": ["../../../../packages/locale/lang/et.ts"], "sourcesContent": ["export default {\n  name: 'et',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: '<PERSON>ä<PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON>h<PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON> kuupäev',\n      selectTime: '<PERSON><PERSON> kellaaeg',\n      startDate: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n      startTime: 'Algusaeg',\n      endDate: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ev',\n      endTime: '<PERSON><PERSON><PERSON>aeg',\n      prevYear: 'Eelmine aasta',\n      nextYear: '<PERSON><PERSON><PERSON><PERSON> aasta',\n      prevMonth: 'Eelmine kuu',\n      nextMonth: '<PERSON><PERSON><PERSON><PERSON> kuu',\n      year: '',\n      month1: 'Jaanuar',\n      month2: 'Veebruar',\n      month3: 'Märts',\n      month4: 'Aprill',\n      month5: 'Mai',\n      month6: '<PERSON><PERSON>',\n      month7: '<PERSON><PERSON>',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktoober',\n      month11: 'November',\n      month12: 'Detsember',\n      // week: 'nädal',\n      weeks: {\n        sun: 'P',\n        mon: 'E',\n        tue: 'T',\n        wed: 'K',\n        thu: 'N',\n        fri: 'R',\n        sat: 'L',\n      },\n      months: {\n        jan: 'Jaan',\n        feb: 'Veeb',\n        mar: 'Mär',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Juun',\n        jul: 'Juul',\n        aug: 'Aug',\n        sep: 'Sept',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dets',\n      },\n    },\n    select: {\n      loading: 'Laadimine',\n      noMatch: 'Sobivad andmed puuduvad',\n      noData: 'Andmed puuduvad',\n      placeholder: 'Vali',\n    },\n    mention: {\n      loading: 'Laadimine',\n    },\n    cascader: {\n      noMatch: 'Sobivad andmed puuduvad',\n      loading: 'Laadimine',\n      placeholder: 'Vali',\n      noData: 'Andmed puuduvad',\n    },\n    pagination: {\n      goto: 'Mine lehele',\n      pagesize: '/page',\n      total: 'Kokku {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Teade',\n      confirm: 'OK',\n      cancel: 'Tühista',\n      error: 'Vigane sisend',\n    },\n    upload: {\n      deleteTip: 'Vajuta \"Kustuta\", et eemaldada',\n      delete: 'Kustuta',\n      preview: 'Eelvaate',\n      continue: 'Jätka',\n    },\n    table: {\n      emptyText: 'Andmed puuduvad',\n      confirmFilter: 'Kinnita',\n      resetFilter: 'Taasta',\n      clearFilter: 'Kõik',\n      sumText: 'Summa',\n    },\n    tree: {\n      emptyText: 'Andmed puuduvad',\n    },\n    transfer: {\n      noMatch: 'Sobivad andmed puuduvad',\n      noData: 'Andmed puuduvad',\n      titles: ['Loend 1', 'Loend 2'],\n      filterPlaceholder: 'Sisesta märksõna',\n      noCheckedFormat: '{total} objekti',\n      hasCheckedFormat: '{checked}/{total} valitud',\n    },\n    image: {\n      error: 'Ebaõnnestus',\n    },\n    pageHeader: {\n      title: 'Tagasi',\n    },\n    popconfirm: {\n      confirmButtonText: 'Jah',\n      cancelButtonText: 'Ei',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}