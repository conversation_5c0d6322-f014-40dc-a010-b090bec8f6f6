{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/config-provider/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport ConfigProvider from './src/config-provider'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElConfigProvider: SFCWithInstall<typeof ConfigProvider> =\n  withInstall(ConfigProvider)\nexport default ElConfigProvider\n\nexport * from './src/config-provider'\nexport * from './src/config-provider-props'\nexport * from './src/constants'\nexport * from './src/hooks/use-global-config'\n"], "names": [], "mappings": ";;;;;;;AAEY,MAAC,gBAAgB,GAAG,WAAW,CAAC,cAAc;;;;"}