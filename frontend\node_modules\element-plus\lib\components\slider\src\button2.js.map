{"version": 3, "file": "button2.js", "sources": ["../../../../../../packages/components/slider/src/button.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"button\"\n    :class=\"[ns.e('button-wrapper'), { hover: hovering, dragging }]\"\n    :style=\"wrapperStyle\"\n    :tabindex=\"disabled ? -1 : 0\"\n    @mouseenter=\"handleMouseEnter\"\n    @mouseleave=\"handleMouseLeave\"\n    @mousedown=\"onButtonDown\"\n    @focus=\"handleMouseEnter\"\n    @blur=\"handleMouseLeave\"\n    @keydown=\"onKeyDown\"\n  >\n    <el-tooltip\n      ref=\"tooltip\"\n      :visible=\"tooltipVisible\"\n      :placement=\"placement\"\n      :fallback-placements=\"['top', 'bottom', 'right', 'left']\"\n      :stop-popper-mouse-event=\"false\"\n      :popper-class=\"tooltipClass\"\n      :disabled=\"!showTooltip\"\n      :persistent=\"tooltipPersistent\"\n    >\n      <template #content>\n        <span>{{ formatValue }}</span>\n      </template>\n      <div :class=\"[ns.e('button'), { hover: hovering, dragging }]\" />\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, reactive, toRefs } from 'vue'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useSliderButton } from './composables'\nimport { sliderButtonEmits, sliderButtonProps } from './button'\nimport type { SliderButtonInitData } from './button'\n\ndefineOptions({\n  name: 'ElSliderButton',\n})\n\nconst props = defineProps(sliderButtonProps)\nconst emit = defineEmits(sliderButtonEmits)\n\nconst ns = useNamespace('slider')\n\nconst initData = reactive<SliderButtonInitData>({\n  hovering: false,\n  dragging: false,\n  isClick: false,\n  startX: 0,\n  currentX: 0,\n  startY: 0,\n  currentY: 0,\n  startPosition: 0,\n  newPosition: 0,\n  oldValue: props.modelValue,\n})\n\nconst tooltipPersistent = computed(() =>\n  !showTooltip.value ? false : persistent.value\n)\n\nconst {\n  disabled,\n  button,\n  tooltip,\n  showTooltip,\n  persistent,\n  tooltipVisible,\n  wrapperStyle,\n  formatValue,\n  handleMouseEnter,\n  handleMouseLeave,\n  onButtonDown,\n  onKeyDown,\n  setPosition,\n} = useSliderButton(props, initData, emit)\n\nconst { hovering, dragging } = toRefs(initData)\n\ndefineExpose({\n  onButtonDown,\n  onKeyDown,\n  setPosition,\n  hovering,\n  dragging,\n})\n</script>\n"], "names": ["useNamespace", "reactive", "computed", "useSliderButton", "toRefs", "_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;;;;uCAuCc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAKA,mBAAa,QAAQ,CAAA,CAAA;AAEhC,IAAA,MAAM,WAAWC,YAA+B,CAAA;AAAA,MAC9C,QAAU,EAAA,KAAA;AAAA,MACV,QAAU,EAAA,KAAA;AAAA,MACV,OAAS,EAAA,KAAA;AAAA,MACT,MAAQ,EAAA,CAAA;AAAA,MACR,QAAU,EAAA,CAAA;AAAA,MACV,MAAQ,EAAA,CAAA;AAAA,MACR,QAAU,EAAA,CAAA;AAAA,MACV,aAAe,EAAA,CAAA;AAAA,MACf,WAAa,EAAA,CAAA;AAAA,MACb,UAAU,KAAM,CAAA,UAAA;AAAA,KACjB,CAAA,CAAA;AAED,IAAA,MAAM,iBAAoB,GAAAC,YAAA,CAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MACxB;AAAwC,MAC1C,QAAA;AAEA,MAAM,MAAA;AAAA,MACJ,OAAA;AAAA,MACA,WAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,MACA,gBAAA;AAAA,MACA,gBAAA;AAAA,MACA,YAAA;AAAA,MACA,SAAA;AAAA,MACA,WAAA;AAAA,KACA,GAAAC,+BAAA,CAAA,KAAA,EAAA,QAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,GAAAC,UAAA,CAAA,QAAA,CAAA,CAAA;AAAA,IACF,MAAI,CAAA;AAEJ,MAAA,YAAQ;AAER,MAAa,SAAA;AAAA,MACX,WAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}