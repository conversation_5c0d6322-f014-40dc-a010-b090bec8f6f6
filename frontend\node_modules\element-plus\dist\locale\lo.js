/*! Element Plus v2.10.2 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleLo = factory());
})(this, (function () { 'use strict';

  var lo = {
    name: "lo",
    el: {
      breadcrumb: {
        label: "\u0EC0\u0EA1\u0E99\u0EB9\u0E99\u0EB3\u0E97\u0EB2\u0E87"
      },
      colorpicker: {
        confirm: "\u0E95\u0EBB\u0E81\u0EA5\u0EBB\u0E87",
        clear: "\u0EA5\u0EC9\u0EB2\u0E87",
        defaultLabel: "\u0EC0\u0E84\u0EB7\u0EC8\u0EAD\u0E87\u0EA1\u0EB7\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0EAA\u0EB5",
        description: "\u0EAA\u0EB5\u0E9B\u0EB1\u0E94\u0E88\u0EB8\u0E9A\u0EB1\u0E99\u0EC1\u0EA1\u0EC8\u0E99 {color}. \u0E81\u0EBB\u0E94 enter \u0EC0\u0E9E\u0EB7\u0EC8\u0EAD\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0EAA\u0EB5\u0EC3\u0EDD\u0EC8.",
        alphaLabel: "\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0E84\u0EC8\u0EB2\u0E84\u0EA7\u0EB2\u0EA1\u0EC2\u0E9B\u0EC8\u0E87\u0EC3\u0EAA"
      },
      datepicker: {
        now: "\u0E95\u0EAD\u0E99\u0E99\u0EB5\u0EC9",
        today: "\u0EA1\u0EB7\u0EC9\u0E99\u0EB5\u0EC9",
        cancel: "\u0E8D\u0EBB\u0E81\u0EC0\u0EA5\u0EB5\u0E81",
        clear: "\u0EA5\u0EC9\u0EB2\u0E87",
        confirm: "\u0E95\u0EBB\u0E81\u0EA5\u0EBB\u0E87",
        dateTablePrompt: "\u0EC3\u0E8A\u0EC9\u0E9B\u0EB8\u0EC8\u0EA1\u0EA5\u0EB9\u0E81\u0EAA\u0EAD\u0E99 \u0EC1\u0EA5\u0EB0 \u0E9B\u0EB8\u0EC8\u0EA1 enter \u0EC0\u0E9E\u0EB7\u0EC8\u0EAD\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0EA7\u0EB1\u0E99\u0E97\u0EB5\u0E82\u0EAD\u0E87\u0EC0\u0E94\u0EB7\u0EAD\u0E99",
        monthTablePrompt: "\u0EC3\u0E8A\u0EC9\u0E9B\u0EB8\u0EC8\u0EA1\u0EA5\u0EB9\u0E81\u0EAA\u0EAD\u0E99 \u0EC1\u0EA5\u0EB0 \u0E9B\u0EB8\u0EC8\u0EA1 enter \u0EC0\u0E9E\u0EB7\u0EC8\u0EAD\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0EC0\u0E94\u0EB7\u0EAD\u0E99",
        yearTablePrompt: "\u0EC3\u0E8A\u0EC9\u0E9B\u0EB8\u0EC8\u0EA1\u0EA5\u0EB9\u0E81\u0EAA\u0EAD\u0E99 \u0EC1\u0EA5\u0EB0 \u0E9B\u0EB8\u0EC8\u0EA1 enter \u0EC0\u0E9E\u0EB7\u0EC8\u0EAD\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0E9B\u0EB5",
        selectedDate: "\u0EA7\u0EB1\u0E99\u0E97\u0EB5\u0E97\u0EB5\u0EC8\u0EC0\u0EA5\u0EB7\u0EAD\u0E81",
        selectDate: "\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0EA7\u0EB1\u0E99\u0E97\u0EB5",
        selectTime: "\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0EC0\u0EA7\u0EA5\u0EB2",
        startDate: "\u0EA7\u0EB1\u0E99\u0E97\u0EB5\u0EC0\u0EA5\u0EB5\u0EC8\u0EA1\u0E95\u0EBB\u0EC9\u0E99",
        startTime: "\u0EC0\u0EA7\u0EA5\u0EB2\u0EC0\u0EA5\u0EB5\u0EC8\u0EA1\u0E95\u0EBB\u0EC9\u0E99",
        endDate: "\u0EA7\u0EB1\u0E99\u0E97\u0EB5\u0EAA\u0EB4\u0EC9\u0E99\u0EAA\u0EB8\u0E94",
        endTime: "\u0EC0\u0EA7\u0EA5\u0EB2\u0EAA\u0EB4\u0EC9\u0E99\u0EAA\u0EB8\u0E94",
        prevYear: "\u0E9B\u0EB5\u0E81\u0EC8\u0EAD\u0E99\u0EDC\u0EC9\u0EB2",
        nextYear: "\u0E9B\u0EB5\u0E96\u0EB1\u0E94\u0EC4\u0E9B",
        prevMonth: "\u0EC0\u0E94\u0EB7\u0EAD\u0E99\u0E81\u0EC8\u0EAD\u0E99\u0EDC\u0EC9\u0EB2",
        nextMonth: "\u0EC0\u0E94\u0EB7\u0EAD\u0E99\u0E96\u0EB1\u0E94\u0EC4\u0E9B",
        year: "\u0E9B\u0EB5",
        month1: "\u0EA1\u0EB1\u0E87\u0E81\u0EAD\u0E99",
        month2: "\u0E81\u0EB8\u0EA1\u0E9E\u0EB2",
        month3: "\u0EA1\u0EB5\u0E99\u0EB2",
        month4: "\u0EC0\u0EA1\u0EAA\u0EB2",
        month5: "\u0E9E\u0EB6\u0E94\u0EAA\u0EB0\u0E9E\u0EB2",
        month6: "\u0EA1\u0EB4\u0E96\u0EB8\u0E99\u0EB2",
        month7: "\u0E81\u0ECD\u0EA5\u0EB0\u0E81\u0EBB\u0E94",
        month8: "\u0EAA\u0EB4\u0E87\u0EAB\u0EB2",
        month9: "\u0E81\u0EB1\u0E99\u0E8D\u0EB2",
        month10: "\u0E95\u0EB8\u0EA5\u0EB2",
        month11: "\u0E9E\u0EB0\u0E88\u0EB4\u0E81",
        month12: "\u0E97\u0EB1\u0E99\u0EA7\u0EB2",
        week: "\u0EAD\u0EB2\u0E97\u0EB4\u0E94",
        weeks: {
          sun: "\u0EAD\u0EB2",
          mon: "\u0E88",
          tue: "\u0EAD",
          wed: "\u0E9E",
          thu: "\u0E9E\u0EAB",
          fri: "\u0EAA",
          sat: "\u0EC0\u0EAA\u0EBB\u0EB2"
        },
        weeksFull: {
          sun: "\u0EAD\u0EB2\u0E97\u0EB4\u0E94",
          mon: "\u0E88\u0EB1\u0E99",
          tue: "\u0EAD\u0EB1\u0E87\u0E84\u0EB2\u0E99",
          wed: "\u0E9E\u0EB8\u0E94",
          thu: "\u0E9E\u0EB0\u0EAB\u0EB1\u0E94",
          fri: "\u0EAA\u0EB8\u0E81",
          sat: "\u0EC0\u0EAA\u0EBB\u0EB2"
        },
        months: {
          jan: "\u0EA1\u0EB1\u0E87\u0E81\u0EAD\u0E99",
          feb: "\u0E81\u0EB8\u0EA1\u0E9E\u0EB2",
          mar: "\u0EA1\u0EB5\u0E99\u0EB2",
          apr: "\u0EC0\u0EA1\u0EAA\u0EB2",
          may: "\u0E9E\u0EB6\u0E94\u0EAA\u0EB0\u0E9E\u0EB2",
          jun: "\u0EA1\u0EB4\u0E96\u0EB8\u0E99\u0EB2",
          jul: "\u0E81\u0ECD\u0EA5\u0EB0\u0E81\u0EBB\u0E94",
          aug: "\u0EAA\u0EB4\u0E87\u0EAB\u0EB2",
          sep: "\u0E81\u0EB1\u0E99\u0E8D\u0EB2",
          oct: "\u0E95\u0EB8\u0EA5\u0EB2",
          nov: "\u0E9E\u0EB0\u0E88\u0EB4\u0E81",
          dec: "\u0E97\u0EB1\u0E99\u0EA7\u0EB2"
        }
      },
      inputNumber: {
        decrease: "\u0EAB\u0EBC\u0EB8\u0E94\u0E88\u0EB3\u0E99\u0EA7\u0E99",
        increase: "\u0EC0\u0E9E\u0EB5\u0EC8\u0EA1\u0E88\u0EB3\u0E99\u0EA7\u0E99"
      },
      select: {
        loading: "\u0E81\u0EB3\u0EA5\u0EB1\u0E87\u0EC2\u0EAB\u0EBC\u0E94",
        noMatch: "\u0E9A\u0ECD\u0EC8\u0E9E\u0EBB\u0E9A\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99\u0E97\u0EB5\u0EC8\u0E81\u0EBB\u0E87\u0E81\u0EB1\u0E99",
        noData: "\u0E9A\u0ECD\u0EC8\u0EA1\u0EB5\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99",
        placeholder: "\u0EC0\u0EA5\u0EB7\u0EAD\u0E81"
      },
      mention: {
        loading: "\u0E81\u0EB3\u0EA5\u0EB1\u0E87\u0EC2\u0EAB\u0EBC\u0E94"
      },
      dropdown: {
        toggleDropdown: "\u0EAA\u0EB0\u0EAB\u0EBC\u0EB1\u0E9A\u0EC0\u0EA1\u0E99\u0EB9"
      },
      cascader: {
        noMatch: "\u0E9A\u0ECD\u0EC8\u0E9E\u0EBB\u0E9A\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99\u0E97\u0EB5\u0EC8\u0E81\u0EBB\u0E87\u0E81\u0EB1\u0E99",
        loading: "\u0E81\u0EB3\u0EA5\u0EB1\u0E87\u0EC2\u0EAB\u0EBC\u0E94",
        placeholder: "\u0EC0\u0EA5\u0EB7\u0EAD\u0E81",
        noData: "\u0E9A\u0ECD\u0EC8\u0EA1\u0EB5\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99"
      },
      pagination: {
        goto: "\u0EC4\u0E9B\u0E97\u0EB5\u0EC8",
        pagesize: "/\u0EDC\u0EC9\u0EB2",
        total: "\u0E97\u0EB1\u0E87\u0EDD\u0EBB\u0E94 {total}",
        pageClassifier: "",
        page: "\u0EDC\u0EC9\u0EB2",
        prev: "\u0E8D\u0EC9\u0EAD\u0E99\u0E81\u0EB1\u0E9A",
        next: "\u0E96\u0EB1\u0E94\u0EC4\u0E9B",
        currentPage: "\u0EDC\u0EC9\u0EB2 {pager}",
        prevPages: "\u0E8D\u0EC9\u0EAD\u0E99\u0E81\u0EB1\u0E9A {pager} \u0EDC\u0EC9\u0EB2",
        nextPages: "\u0E96\u0EB1\u0E94\u0EC4\u0E9B {pager} \u0EDC\u0EC9\u0EB2",
        deprecationWarning: "\u0E9E\u0EBB\u0E9A\u0E81\u0EB2\u0E99\u0EC3\u0E8A\u0EC9\u0E87\u0EB2\u0E99\u0E97\u0EB5\u0EC8\u0E9A\u0ECD\u0EC8\u0E96\u0EB7\u0E81\u0E95\u0EC9\u0EAD\u0E87, \u0E81\u0EB0\u0EA5\u0EB8\u0E99\u0EB2\u0EC0\u0E9A\u0EB4\u0EC8\u0E87\u0EC0\u0EAD\u0E81\u0EB0\u0EAA\u0EB2\u0E99\u0E81\u0EB2\u0E99\u0EC3\u0E8A\u0EC9\u0E87\u0EB2\u0E99 el-pagination \u0EAA\u0EB3\u0EA5\u0EB1\u0E9A\u0EA5\u0EB2\u0E8D\u0EA5\u0EB0\u0EAD\u0EBD\u0E94\u0EC0\u0E9E\u0EB5\u0EC8\u0EA1\u0EC0\u0E95\u0EB5\u0EA1"
      },
      dialog: {
        close: "\u0E9B\u0EB4\u0E94\u0E81\u0EC8\u0EAD\u0E87\u0E82\u0ECD\u0EC9\u0E84\u0EA7\u0EB2\u0EA1\u0E99\u0EB5\u0EC9"
      },
      drawer: {
        close: "\u0E9B\u0EB4\u0E94\u0E81\u0EC8\u0EAD\u0E87\u0E82\u0ECD\u0EC9\u0E84\u0EA7\u0EB2\u0EA1\u0E99\u0EB5\u0EC9"
      },
      messagebox: {
        title: "\u0E82\u0ECD\u0EC9\u0E84\u0EA7\u0EB2\u0EA1",
        confirm: "\u0E95\u0EBB\u0E81\u0EA5\u0EBB\u0E87",
        cancel: "\u0E8D\u0EBB\u0E81\u0EC0\u0EA5\u0EB5\u0E81",
        error: "\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99\u0E9A\u0ECD\u0EC8\u0E96\u0EB7\u0E81\u0E95\u0EC9\u0EAD\u0E87",
        close: "\u0E9B\u0EB4\u0E94\u0E81\u0EC8\u0EAD\u0E87\u0E82\u0ECD\u0EC9\u0E84\u0EA7\u0EB2\u0EA1\u0E99\u0EB5\u0EC9"
      },
      upload: {
        deleteTip: "\u0E81\u0EBB\u0E94\u0E9B\u0EB8\u0EC8\u0EA1\u0EA5\u0EBB\u0E9A\u0EC0\u0E9E\u0EB7\u0EC8\u0EAD\u0EA5\u0EBB\u0E9A\u0EAD\u0EAD\u0E81",
        delete: "\u0EA5\u0EBB\u0E9A",
        preview: "\u0EC0\u0E9A\u0EB4\u0EC8\u0E87\u0E95\u0EBB\u0EA7\u0EA2\u0EC8\u0EB2\u0E87",
        continue: "\u0EAA\u0EB7\u0E9A\u0E95\u0ECD\u0EC8"
      },
      slider: {
        defaultLabel: "\u0EAA\u0EB0\u0EC4\u0EA5\u0EC0\u0E94\u0EB5\u0EA5\u0EB0\u0EAB\u0EA7\u0EC8\u0EB2\u0E87 {min} \u0EC1\u0EA5\u0EB0 {max}",
        defaultRangeStartLabel: "\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0E84\u0EC8\u0EB2\u0EC0\u0EA5\u0EB5\u0EC8\u0EA1\u0E95\u0EBB\u0EC9\u0E99",
        defaultRangeEndLabel: "\u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0E84\u0EC8\u0EB2\u0EAA\u0EB4\u0EC9\u0E99\u0EAA\u0EB8\u0E94"
      },
      table: {
        emptyText: "\u0E9A\u0ECD\u0EC8\u0EA1\u0EB5\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99",
        confirmFilter: "\u0EA2\u0EB7\u0E99\u0EA2\u0EB1\u0E99",
        resetFilter: "\u0E84\u0EB7\u0E99\u0E84\u0EC8\u0EB2\u0EC3\u0EDD\u0EC8",
        clearFilter: "\u0E97\u0EB1\u0E87\u0EDD\u0EBB\u0E94",
        sumText: "\u0E9C\u0EBB\u0E99\u0EA5\u0EA7\u0EA1"
      },
      tour: {
        next: "\u0E96\u0EB1\u0E94\u0EC4\u0E9B",
        previous: "\u0E8D\u0EC9\u0EAD\u0E99\u0E81\u0EB1\u0E9A",
        finish: "\u0EAA\u0EB3\u0EC0\u0EA5\u0EB1\u0E94"
      },
      tree: {
        emptyText: "\u0E9A\u0ECD\u0EC8\u0EA1\u0EB5\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99"
      },
      transfer: {
        noMatch: "\u0E9A\u0ECD\u0EC8\u0E9E\u0EBB\u0E9A\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99\u0E97\u0EB5\u0EC8\u0E81\u0EBB\u0E87\u0E81\u0EB1\u0E99",
        noData: "\u0E9A\u0ECD\u0EC8\u0EA1\u0EB5\u0E82\u0ECD\u0EC9\u0EA1\u0EB9\u0E99",
        titles: ["\u0EA5\u0EB2\u0E8D\u0E81\u0EB2\u0E99\u0E97\u0EB5 1", "\u0EA5\u0EB2\u0E8D\u0E81\u0EB2\u0E99\u0E97\u0EB5 2"],
        filterPlaceholder: "\u0E9B\u0EC9\u0EAD\u0E99\u0E84\u0EB3\u0E84\u0EBB\u0EC9\u0E99\u0EAB\u0EB2",
        noCheckedFormat: "{total} \u0EA5\u0EB2\u0E8D\u0E81\u0EB2\u0E99",
        hasCheckedFormat: "{checked}/{total} \u0EC0\u0EA5\u0EB7\u0EAD\u0E81\u0EC1\u0EA5\u0EC9\u0EA7"
      },
      image: {
        error: "\u0EC0\u0E81\u0EB5\u0E94\u0E82\u0ECD\u0EC9\u0E9C\u0EB4\u0E94\u0E9E\u0EB2\u0E94"
      },
      pageHeader: {
        title: "\u0E81\u0EB1\u0E9A\u0E84\u0EB7\u0E99"
      },
      popconfirm: {
        confirmButtonText: "\u0E95\u0EBB\u0E81\u0EA5\u0EBB\u0E87",
        cancelButtonText: "\u0E8D\u0EBB\u0E81\u0EC0\u0EA5\u0EB5\u0E81"
      },
      carousel: {
        leftArrow: "\u0EA5\u0EB9\u0E81\u0EAA\u0EAD\u0E99\u0EC0\u0E9A\u0EB7\u0EC9\u0EAD\u0E87\u0E8A\u0EC9\u0EB2\u0E8D\u0E82\u0EAD\u0E87 Carousel",
        rightArrow: "\u0EA5\u0EB9\u0E81\u0EAA\u0EAD\u0E99\u0EC0\u0E9A\u0EB7\u0EC9\u0EAD\u0E87\u0E82\u0EA7\u0EB2\u0E82\u0EAD\u0E87 Carousel",
        indicator: "Carousel \u0EAA\u0EB0\u0EAB\u0EBC\u0EB1\u0E9A\u0EC4\u0E9B\u0EA5\u0EB3\u0E94\u0EB1\u0E9A\u0E97\u0EB5 {index}"
      }
    }
  };

  return lo;

}));
