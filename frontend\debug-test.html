<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .info { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 多模态面试评估系统 - 调试页面</h1>
        
        <div class="status info">
            <strong>系统状态检查</strong>
        </div>
        
        <div id="status-container">
            <div class="status info">正在检查系统状态...</div>
        </div>
        
        <h3>快速操作</h3>
        <button onclick="checkFrontend()">检查前端服务</button>
        <button onclick="checkBackend()">检查后端服务</button>
        <button onclick="testVueApp()">测试Vue应用</button>
        <button onclick="clearCache()">清除缓存</button>
        
        <h3>系统信息</h3>
        <div id="system-info">
            <p><strong>当前时间:</strong> <span id="current-time"></span></p>
            <p><strong>用户代理:</strong> <span id="user-agent"></span></p>
            <p><strong>页面URL:</strong> <span id="page-url"></span></p>
        </div>
        
        <h3>控制台日志</h3>
        <div id="console-log" style="background: #f8f8f8; padding: 10px; border-radius: 4px; font-family: monospace; max-height: 200px; overflow-y: auto;">
            <div>系统初始化...</div>
        </div>
    </div>

    <script>
        // 更新系统信息
        function updateSystemInfo() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('page-url').textContent = window.location.href;
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('console-log');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff4d4f' : type === 'success' ? '#52c41a' : '#262626';
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 检查前端服务
        async function checkFrontend() {
            log('检查前端服务...');
            try {
                const response = await fetch('/');
                if (response.ok) {
                    log('✅ 前端服务正常', 'success');
                    updateStatus('前端服务运行正常', 'success');
                } else {
                    log('❌ 前端服务响应异常', 'error');
                    updateStatus('前端服务响应异常', 'error');
                }
            } catch (error) {
                log(`❌ 前端服务连接失败: ${error.message}`, 'error');
                updateStatus('前端服务连接失败', 'error');
            }
        }
        
        // 检查后端服务
        async function checkBackend() {
            log('检查后端服务...');
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 后端服务正常', 'success');
                    log(`后端状态: ${data.status}`, 'success');
                    updateStatus('后端服务运行正常', 'success');
                } else {
                    log('❌ 后端服务响应异常', 'error');
                    updateStatus('后端服务响应异常', 'error');
                }
            } catch (error) {
                log(`❌ 后端服务连接失败: ${error.message}`, 'error');
                updateStatus('后端服务连接失败', 'error');
            }
        }
        
        // 测试Vue应用
        function testVueApp() {
            log('测试Vue应用...');
            try {
                // 尝试访问Vue应用
                window.location.href = '/';
            } catch (error) {
                log(`❌ Vue应用测试失败: ${error.message}`, 'error');
            }
        }
        
        // 清除缓存
        function clearCache() {
            log('清除浏览器缓存...');
            try {
                // 清除localStorage
                localStorage.clear();
                // 清除sessionStorage
                sessionStorage.clear();
                log('✅ 缓存清除成功', 'success');
                updateStatus('缓存已清除', 'success');
            } catch (error) {
                log(`❌ 缓存清除失败: ${error.message}`, 'error');
            }
        }
        
        // 更新状态显示
        function updateStatus(message, type) {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemInfo();
            log('调试页面已加载');
            
            // 自动检查服务状态
            setTimeout(() => {
                checkFrontend();
                setTimeout(() => {
                    checkBackend();
                }, 1000);
            }, 500);
        });
        
        // 定时更新时间
        setInterval(updateSystemInfo, 1000);
    </script>
</body>
</html>
