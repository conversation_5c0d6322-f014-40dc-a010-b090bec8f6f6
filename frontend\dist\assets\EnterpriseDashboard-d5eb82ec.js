import{_ as $s,r as es,h,o as bs,i as $,j as a,k as i,n as s,p as t,w as l,M as _,z as n,F as g,x as y,B as R,C as G,N as f,t as Ms,K as Gs,y as rs,ad as Es,Q as Zs,m as st,E as Cs,R as z,G as Ns,A as L,H as tt,ae as Ds,a2 as Z,q as et,a0 as Ls,e as ot,af as lt,ag as Ps,ah as nt,ai as at,aj as Us,ak as js,al as xs,am as it,an as dt,a5 as fs,S as rt,ao as ms,O as Rs,D as Bs,a9 as qs,W as Ss}from"./index-b6a2842e.js";import{M as ct,P as ut}from"./PersonalizedRecommendationEngine-925273b6.js";const _t={class:"smart-interview-flow"},vt={class:"flow-console"},mt={class:"console-header"},pt={class:"flow-status"},ht={class:"flow-progress"},gt={class:"adaptive-question-generator"},yt={class:"generator-header"},ft={class:"generation-stats"},$t={class:"stat-item"},bt={class:"stat-item"},kt={class:"question-preview"},wt={class:"current-question"},xt={class:"question-meta"},St={class:"question-content"},Mt={key:0,class:"question-hints"},Ct={class:"hints-list"},Tt={class:"question-actions"},It={class:"realtime-assessment"},Vt={class:"assessment-header"},zt={class:"assessment-mode"},At={class:"assessment-grid"},Dt={class:"assessment-card speech-analysis"},jt={class:"card-header"},Ft={class:"analysis-content"},Pt={class:"metric-item"},Ut={class:"metric-value"},Rt={class:"metric-item"},Bt={class:"metric-value"},qt={class:"metric-item"},Et={class:"metric-value"},Nt={class:"assessment-card behavior-analysis"},Lt={class:"card-header"},Jt={class:"analysis-content"},Qt={class:"behavior-radar"},Wt={class:"radar-center"},Xt={class:"overall-score"},Yt={class:"behavior-indicators"},Ot={class:"indicator-name"},Ht={class:"indicator-bar"},Kt={class:"indicator-value"},Gt={class:"assessment-card content-analysis"},Zt={class:"card-header"},se={class:"analysis-content"},te={class:"content-metrics"},ee={class:"metric-circle"},oe={class:"circle-text"},le={class:"metric-circle"},ne={class:"circle-text"},ae={class:"metric-circle"},ie={class:"circle-text"},de={class:"content-keywords"},re={class:"keywords-match"},ce={class:"smart-followup"},ue={class:"followup-header"},_e={class:"followup-status"},ve={class:"followup-suggestions"},me={class:"suggestion-header"},pe={class:"suggestion-trigger"},he={class:"suggestion-content"},ge={class:"suggestion-actions"},ye={key:0,class:"interview-summary"},fe={class:"summary-header"},$e={class:"summary-score"},be={class:"final-score"},ke={class:"summary-content"},we={class:"strengths-weaknesses"},xe={class:"strengths"},Se={class:"weaknesses"},Me={class:"recommendation"},Ce={class:"recommendation-content"},Te={__name:"SmartInterviewFlow",setup(ks){const C=es(0),T=h({status:"preparing",statusText:"准备中"}),q=h([{title:"候选人准备",description:"系统检测与环境准备",icon:"User"},{title:"智能问题生成",description:"基于简历自适应生成问题",icon:"Setting"},{title:"实时面试进行",description:"多模态交互与实时评估",icon:"VideoCamera"},{title:"智能追问分析",description:"深度挖掘与能力验证",icon:"TrendCharts"},{title:"综合评估报告",description:"生成详细评估与建议",icon:"Document"}]),W=h({matchRate:92,generateTime:156}),V=h({category:"技术能力",difficulty:"中等",duration:3,content:"请描述一下您在项目中遇到的最具挑战性的技术问题，以及您是如何解决的？",hints:["问题分析","解决方案","技术选型","结果验证","经验总结"]}),X=h({realtime:!0}),j=h({speedScore:85,clarityScore:92,logicScore:78}),S=h({overallScore:86,indicators:[{name:"眼神交流",value:88},{name:"肢体语言",value:82},{name:"表情自然",value:90},{name:"坐姿端正",value:85}]}),x=h({relevanceScore:89,depthScore:76,innovationScore:83,keywords:[{word:"架构设计",matched:!0},{word:"性能优化",matched:!0},{word:"团队协作",matched:!1},{word:"项目管理",matched:!0}]}),os=h([]),I=h([{id:1,priority:"high",priorityText:"高优先级",trigger:"技术深度不足",question:"您刚才提到的解决方案，能否详细说明具体的技术实现细节？"},{id:2,priority:"medium",priorityText:"中优先级",trigger:"团队协作提及",question:"在解决这个问题的过程中，您是如何与团队成员协作的？"}]),Y=es(!1),U=h({finalScore:82,strengths:["技术基础扎实，能够清晰表达技术概念","问题分析能力强，思路清晰有条理","学习能力突出，对新技术有敏锐度"],weaknesses:["在团队协作方面的表达可以更具体","项目管理经验需要进一步积累","可以增加更多实际案例的分享"],recommendation:"候选人具备良好的技术基础和学习能力，建议重点关注其团队协作和项目管理能力的培养。整体表现符合岗位要求，建议进入下一轮面试。"}),as=w=>({preparing:"info","in-progress":"primary",completed:"success",paused:"warning",error:"danger"})[w]||"info",O=w=>w<C.value?"finish":w===C.value?"process":"wait",ss=w=>w>=90?"#2ecc71":w>=80?"#f39c12":w>=70?"#e67e22":"#e74c3c",M=w=>w>=85?"var(--iflytek-primary)":w>=70?"#f39c12":"#e74c3c",o=()=>{const w=[{category:"项目经验",difficulty:"困难",duration:5,content:"请详细介绍您负责的最复杂的项目，包括技术架构、团队规模和您的具体贡献。",hints:["项目背景","技术架构","团队协作","个人贡献","项目成果"]},{category:"算法思维",difficulty:"中等",duration:4,content:"如何优化一个处理大量数据的算法？请从时间复杂度和空间复杂度两个角度分析。",hints:["算法分析","复杂度优化","数据结构","性能测试","实际应用"]}],c=w[Math.floor(Math.random()*w.length)];Object.assign(V,c),W.matchRate=88+Math.floor(Math.random()*10),W.generateTime=120+Math.floor(Math.random()*80)},k=()=>{T.status="in-progress",T.statusText="面试进行中",C.value=2},A=w=>{os.push(w);const c=I.findIndex(J=>J.id===w.id);c>-1&&I.splice(c,1)},ts=w=>{console.log("修改追问:",w)};return bs(()=>{setInterval(()=>{X.realtime&&T.status==="in-progress"&&(j.speedScore=Math.max(60,Math.min(100,j.speedScore+(Math.random()-.5)*6)),j.clarityScore=Math.max(60,Math.min(100,j.clarityScore+(Math.random()-.5)*4)),j.logicScore=Math.max(60,Math.min(100,j.logicScore+(Math.random()-.5)*5)),S.indicators.forEach(w=>{w.value=Math.max(60,Math.min(100,w.value+(Math.random()-.5)*3))}),S.overallScore=Math.round(S.indicators.reduce((w,c)=>w+c.value,0)/S.indicators.length),x.relevanceScore=Math.max(60,Math.min(100,x.relevanceScore+(Math.random()-.5)*4)),x.depthScore=Math.max(60,Math.min(100,x.depthScore+(Math.random()-.5)*6)),x.innovationScore=Math.max(60,Math.min(100,x.innovationScore+(Math.random()-.5)*5)))},2e3),setTimeout(()=>{C.value=1,T.status="preparing",T.statusText="问题生成中"},3e3)}),(w,c)=>{const J=$("el-tag"),Q=$("el-icon"),cs=$("el-step"),us=$("el-steps"),ls=$("el-button"),_s=$("el-switch"),is=$("el-progress"),m=$("el-badge");return a(),i("div",_t,[s("div",vt,[s("div",mt,[c[1]||(c[1]=s("h3",null,"iFlytek Spark 智能面试流程",-1)),s("div",pt,[t(J,{type:as(T.status),size:"large"},{default:l(()=>[_(n(T.statusText),1)]),_:1},8,["type"])])]),s("div",ht,[t(us,{active:C.value,"align-center":""},{default:l(()=>[(a(!0),i(g,null,y(q,(d,p)=>(a(),R(cs,{key:p,title:d.title,description:d.description,status:O(p)},{icon:l(()=>[t(Q,null,{default:l(()=>[(a(),R(G(d.icon)))]),_:2},1024)]),_:2},1032,["title","description","status"]))),128))]),_:1},8,["active"])])]),s("div",gt,[s("div",yt,[c[2]||(c[2]=s("h4",null,"自适应问题生成",-1)),s("div",ft,[s("span",$t,[t(Q,null,{default:l(()=>[t(f(Ms))]),_:1}),_(" 适配度: "+n(W.matchRate)+"% ",1)]),s("span",bt,[t(Q,null,{default:l(()=>[t(f(Gs))]),_:1}),_(" 生成时间: "+n(W.generateTime)+"ms ",1)])])]),s("div",kt,[s("div",wt,[s("div",xt,[t(J,{type:"primary"},{default:l(()=>[_(n(V.category),1)]),_:1}),t(J,{type:"warning"},{default:l(()=>[_("难度: "+n(V.difficulty),1)]),_:1}),t(J,{type:"info"},{default:l(()=>[_("预计时长: "+n(V.duration)+"分钟",1)]),_:1})]),s("div",St,[s("p",null,n(V.content),1)]),V.hints.length>0?(a(),i("div",Mt,[c[3]||(c[3]=s("h5",null,"智能提示关键词:",-1)),s("div",Ct,[(a(!0),i(g,null,y(V.hints,d=>(a(),R(J,{key:d,size:"small",effect:"plain"},{default:l(()=>[_(n(d),1)]),_:2},1024))),128))])])):rs("",!0)]),s("div",Tt,[t(ls,{type:"primary",onClick:o},{default:l(()=>[t(Q,null,{default:l(()=>[t(f(Es))]),_:1}),c[4]||(c[4]=_(" 生成下一题 "))]),_:1,__:[4]}),t(ls,{type:"success",onClick:k},{default:l(()=>[t(Q,null,{default:l(()=>[t(f(Zs))]),_:1}),c[5]||(c[5]=_(" 开始回答 "))]),_:1,__:[5]})])])]),s("div",It,[s("div",Vt,[c[6]||(c[6]=s("h4",null,"实时能力评估",-1)),s("div",zt,[t(_s,{modelValue:X.realtime,"onUpdate:modelValue":c[0]||(c[0]=d=>X.realtime=d),"active-text":"实时评估","inactive-text":"延迟评估"},null,8,["modelValue"])])]),s("div",At,[s("div",Dt,[s("div",jt,[t(Q,null,{default:l(()=>[t(f(st))]),_:1}),c[7]||(c[7]=s("span",null,"语音表达分析",-1))]),s("div",Ft,[s("div",Pt,[c[8]||(c[8]=s("span",{class:"metric-label"},"语速适中度",-1)),t(is,{percentage:j.speedScore,color:ss(j.speedScore),"show-text":!1},null,8,["percentage","color"]),s("span",Ut,n(j.speedScore)+"%",1)]),s("div",Rt,[c[9]||(c[9]=s("span",{class:"metric-label"},"表达清晰度",-1)),t(is,{percentage:j.clarityScore,color:ss(j.clarityScore),"show-text":!1},null,8,["percentage","color"]),s("span",Bt,n(j.clarityScore)+"%",1)]),s("div",qt,[c[10]||(c[10]=s("span",{class:"metric-label"},"逻辑连贯性",-1)),t(is,{percentage:j.logicScore,color:ss(j.logicScore),"show-text":!1},null,8,["percentage","color"]),s("span",Et,n(j.logicScore)+"%",1)])])]),s("div",Nt,[s("div",Lt,[t(Q,null,{default:l(()=>[t(f(Cs))]),_:1}),c[11]||(c[11]=s("span",null,"行为表现分析",-1))]),s("div",Jt,[s("div",Qt,[s("div",Wt,[s("span",Xt,n(S.overallScore),1),c[12]||(c[12]=s("small",null,"综合评分",-1))]),s("div",Yt,[(a(!0),i(g,null,y(S.indicators,d=>(a(),i("div",{class:"indicator-item",key:d.name},[s("span",Ot,n(d.name),1),s("div",Ht,[s("div",{class:"indicator-fill",style:z({width:d.value+"%",backgroundColor:M(d.value)})},null,4)]),s("span",Kt,n(d.value)+"%",1)]))),128))])])])]),s("div",Gt,[s("div",Zt,[t(Q,null,{default:l(()=>[t(f(Ns))]),_:1}),c[13]||(c[13]=s("span",null,"回答内容分析",-1))]),s("div",se,[s("div",te,[s("div",ee,[s("div",{class:"circle-progress",style:z({"--progress":x.relevanceScore+"%"})},[s("span",oe,n(x.relevanceScore)+"%",1),c[14]||(c[14]=s("small",null,"相关性",-1))],4)]),s("div",le,[s("div",{class:"circle-progress",style:z({"--progress":x.depthScore+"%"})},[s("span",ne,n(x.depthScore)+"%",1),c[15]||(c[15]=s("small",null,"深度",-1))],4)]),s("div",ae,[s("div",{class:"circle-progress",style:z({"--progress":x.innovationScore+"%"})},[s("span",ie,n(x.innovationScore)+"%",1),c[16]||(c[16]=s("small",null,"创新性",-1))],4)])]),s("div",de,[c[17]||(c[17]=s("h5",null,"关键词匹配:",-1)),s("div",re,[(a(!0),i(g,null,y(x.keywords,d=>(a(),i("span",{key:d.word,class:L(["keyword-match",{matched:d.matched}])},n(d.word),3))),128))])])])])])]),s("div",ce,[s("div",ue,[c[19]||(c[19]=s("h4",null,"智能追问机制",-1)),s("div",_e,[t(m,{value:os.length,type:"primary"},{default:l(()=>[t(ls,{size:"small"},{default:l(()=>c[18]||(c[18]=[_("待追问")])),_:1,__:[18]})]),_:1},8,["value"])])]),s("div",ve,[(a(!0),i(g,null,y(I,d=>(a(),i("div",{class:"suggestion-item",key:d.id},[s("div",me,[t(J,{type:d.priority==="high"?"danger":d.priority==="medium"?"warning":"info"},{default:l(()=>[_(n(d.priorityText),1)]),_:2},1032,["type"]),s("span",pe,"基于: "+n(d.trigger),1)]),s("div",he,[s("p",null,n(d.question),1)]),s("div",ge,[t(ls,{size:"small",type:"primary",onClick:p=>A(d)},{default:l(()=>c[20]||(c[20]=[_(" 使用此追问 ")])),_:2,__:[20]},1032,["onClick"]),t(ls,{size:"small",onClick:p=>ts(d)},{default:l(()=>c[21]||(c[21]=[_(" 修改后使用 ")])),_:2,__:[21]},1032,["onClick"])])]))),128))])]),Y.value?(a(),i("div",ye,[s("div",fe,[c[23]||(c[23]=s("h4",null,"面试总结与建议",-1)),s("div",$e,[s("span",be,n(U.finalScore),1),c[22]||(c[22]=s("small",null,"综合评分",-1))])]),s("div",ke,[s("div",we,[s("div",xe,[c[24]||(c[24]=s("h5",null,"优势表现",-1)),s("ul",null,[(a(!0),i(g,null,y(U.strengths,d=>(a(),i("li",{key:d},[t(Q,null,{default:l(()=>[t(f(tt))]),_:1}),_(" "+n(d),1)]))),128))])]),s("div",Se,[c[25]||(c[25]=s("h5",null,"改进建议",-1)),s("ul",null,[(a(!0),i(g,null,y(U.weaknesses,d=>(a(),i("li",{key:d},[t(Q,null,{default:l(()=>[t(f(Ds))]),_:1}),_(" "+n(d),1)]))),128))])])]),s("div",Me,[c[26]||(c[26]=s("h5",null,"iFlytek Spark 智能建议",-1)),s("div",Ce,[s("p",null,n(U.recommendation),1)])])])])):rs("",!0)])}}},Ie=$s(Te,[["__scopeId","data-v-c69e27ac"]]);const Ve={class:"ai-data-analytics"},ze={class:"analytics-console"},Ae={class:"console-header"},De={class:"analysis-controls"},je={class:"metrics-overview"},Fe={class:"metric-content"},Pe={class:"metric-value"},Ue={class:"metric-label"},Re={class:"interview-quality-analysis"},Be={class:"analysis-header"},qe={class:"quality-filter"},Ee={class:"quality-charts"},Ne={class:"chart-container quality-distribution"},Le={class:"chart-header"},Je={class:"chart-legend"},Qe={class:"legend-text"},We={class:"pie-chart"},Xe={class:"pie-center"},Ye={class:"center-value"},Oe={class:"pie-svg",viewBox:"0 0 200 200"},He=["stroke","stroke-dasharray","stroke-dashoffset","transform"],Ke={class:"chart-container skill-radar"},Ge={class:"chart-header"},Ze={class:"radar-controls"},so={class:"radar-chart"},to={class:"radar-svg",viewBox:"0 0 300 300"},eo={class:"radar-grid"},oo=["r"],lo=["x2","y2"],no=["x","y"],ao=["points"],io=["cx","cy"],ro={class:"chart-container trend-analysis"},co={class:"chart-header"},uo={class:"trend-metrics"},_o={class:"trend-metric"},vo={class:"trend-metric"},mo={class:"line-chart"},po={class:"trend-svg",viewBox:"0 0 400 200"},ho={class:"grid-lines"},go=["y1","y2"],yo=["x1","x2"],fo=["points"],$o=["cx","cy"],bo=["x","y"],ko={class:"candidate-insights"},wo={class:"insights-header"},xo={class:"insight-tabs"},So={class:"insights-content"},Mo={key:0,class:"ability-analysis"},Co={class:"ability-matrix"},To={class:"matrix-grid"},Io={class:"skill-name"},Vo={class:"skill-levels"},zo=["title"],Ao={class:"skill-average"},Do={class:"ability-recommendations"},jo={class:"recommendation-list"},Fo={class:"rec-content"},Po={class:"rec-impact"},Uo={class:"impact-value"},Ro={key:1,class:"behavior-analysis"},Bo={class:"behavior-patterns"},qo={class:"pattern-grid"},Eo={class:"pattern-info"},No={class:"pattern-percentage"},Lo={class:"pattern-description"},Jo={class:"interaction-heatmap"},Qo={class:"heatmap-container"},Wo={class:"heatmap-grid"},Xo=["title"],Yo={class:"heatmap-labels"},Oo={class:"time-labels"},Ho={class:"day-labels"},Ko={key:2,class:"matching-analysis"},Go={class:"matching-overview"},Zo={class:"matching-stats"},sl={class:"stat-item"},tl={class:"stat-value"},el={class:"stat-item"},ol={class:"stat-value"},ll={class:"stat-item"},nl={class:"stat-value"},al={class:"matching-factors"},il={class:"factors-list"},dl={class:"factor-name"},rl={class:"factor-weight"},cl={class:"factor-bar"},ul={class:"factor-score"},_l={class:"intelligent-report-generation"},vl={class:"report-header"},ml={class:"report-actions"},pl={class:"report-templates"},hl={class:"template-preview"},gl={class:"template-info"},yl={class:"template-features"},fl={class:"template-actions"},$l={__name:"AIDataAnalytics",setup(ks){const C=es("30d"),T=es("all"),q=es("abilities"),W=es(!1),V=h([{name:"总面试数量",value:"2,847",trend:12.5,color:"#3498db",icon:"User"},{name:"平均面试质量",value:"87.3",trend:8.2,color:"#2ecc71",icon:"TrendCharts"},{name:"候选人满意度",value:"94.6%",trend:5.7,color:"#f39c12",icon:"Medal"},{name:"面试效率",value:"76.8%",trend:-2.1,color:"#e74c3c",icon:"DataBoard"}]),X=h([{name:"优秀",percentage:35,color:"#2ecc71"},{name:"良好",percentage:42,color:"#f39c12"},{name:"一般",percentage:18,color:"#e67e22"},{name:"待改进",percentage:5,color:"#e74c3c"}]),j=Z(()=>X.reduce((p,u)=>p+u.percentage,0)*28),S=Z(()=>{let p=0;const u=2*Math.PI*80;return X.map(E=>{const ns=E.percentage/100,H=`${ns*u} ${u}`,ds=-p*u,v=p*360;return p+=ns,{color:E.color,dashArray:H,dashOffset:ds,transform:`rotate(${v} 100 100)`}})}),x=h([{name:"技术能力",angle:0,x:275,y:150,labelX:285,labelY:155},{name:"沟通表达",angle:60,x:212.5,y:66.5,labelX:220,labelY:55},{name:"逻辑思维",angle:120,x:87.5,y:66.5,labelX:80,labelY:55},{name:"学习能力",angle:180,x:25,y:150,labelX:15,labelY:155},{name:"团队协作",angle:240,x:87.5,y:233.5,labelX:80,labelY:245},{name:"创新思维",angle:300,x:212.5,y:233.5,labelX:220,labelY:245}]),os=h([85,78,92,88,76,82]),I=Z(()=>x.map((p,u)=>{const ns=os[u]/100*125,H=(p.angle-90)*(Math.PI/180);return{x:150+ns*Math.cos(H),y:150+ns*Math.sin(H)}})),Y=Z(()=>I.value.map(p=>`${p.x},${p.y}`).join(" ")),U=h([72,75,78,82,85,87,89]),as=Z(()=>(U.reduce((p,u)=>p+u,0)/U.length).toFixed(1)),O=Z(()=>{const p=U[0];return((U[U.length-1]-p)/p*100).toFixed(1)}),ss=Z(()=>U.map((p,u)=>({x:u*50+65,y:180-(p-60)*3,value:p}))),M=Z(()=>ss.value.map(p=>`${p.x},${p.y}`).join(" ")),o=h([{name:"JavaScript",levels:[{name:"高水平",count:45,class:"high"},{name:"中等",count:32,class:"medium"},{name:"待提升",count:8,class:"low"}],average:82},{name:"Vue.js",levels:[{name:"高水平",count:38,class:"high"},{name:"中等",count:28,class:"medium"},{name:"待提升",count:12,class:"low"}],average:78},{name:"系统设计",levels:[{name:"高水平",count:25,class:"high"},{name:"中等",count:35,class:"medium"},{name:"待提升",count:18,class:"low"}],average:71},{name:"算法思维",levels:[{name:"高水平",count:42,class:"high"},{name:"中等",count:30,class:"medium"},{name:"待提升",count:6,class:"low"}],average:85}]),k=h([{id:1,priority:"high",priorityText:"高优先级",title:"加强系统设计能力培训",description:"系统设计是当前候选人普遍薄弱的环节，建议增加相关培训内容",expectedImprovement:15},{id:2,priority:"medium",priorityText:"中优先级",title:"优化Vue.js技术面试题",description:"当前Vue.js相关题目难度分布不均，建议调整题目梯度",expectedImprovement:8},{id:3,priority:"low",priorityText:"低优先级",title:"增加实战项目评估",description:"理论知识掌握较好，但实际项目经验评估可以加强",expectedImprovement:12}]),A=h([{name:"积极主动型",percentage:42,icon:"🚀",color:"#2ecc71",description:"主动提问，表达积极"},{name:"稳重思考型",percentage:35,icon:"🤔",color:"#3498db",description:"深思熟虑，回答谨慎"},{name:"紧张焦虑型",percentage:18,icon:"😰",color:"#f39c12",description:"表现紧张，需要引导"},{name:"过度自信型",percentage:5,icon:"😎",color:"#e74c3c",description:"过于自信，可能夸大"}]),ts=h(Array.from({length:168},(p,u)=>({time:`${Math.floor(u/7)}:00`,intensity:Math.random()*100}))),w=["00","04","08","12","16","20"],c=["周一","周二","周三","周四","周五","周六","周日"],J=h({highMatch:156,mediumMatch:89,lowMatch:23}),Q=h([{name:"技术技能匹配",weight:35,score:87},{name:"工作经验匹配",weight:25,score:78},{name:"教育背景匹配",weight:15,score:92},{name:"软技能匹配",weight:15,score:83},{name:"文化适应性",weight:10,score:76}]),cs=h([{id:1,name:"综合评估报告",description:"包含完整的面试数据分析和候选人评估",features:["数据统计","趋势分析","建议推荐"],color:"#3498db",icon:"Document"},{id:2,name:"技能分析报告",description:"专注于技术技能和能力评估的详细报告",features:["技能矩阵","能力雷达","提升建议"],color:"#2ecc71",icon:"TrendCharts"},{id:3,name:"行为洞察报告",description:"深度分析候选人行为模式和特征",features:["行为分析","模式识别","个性评估"],color:"#f39c12",icon:"User"}]),us=()=>{console.log("生成报告，时间范围:",C.value)},ls=p=>`rgba(33, 111, 255, ${p/100})`,_s=p=>p>=85?"#2ecc71":p>=70?"#f39c12":"#e74c3c",is=()=>{ElMessage.info("正在生成报告预览..."),setTimeout(()=>{ElMessage.success("报告预览已生成")},1e3)},m=()=>{ElMessage.info("正在导出报告..."),setTimeout(()=>{ElMessage.success("报告导出成功")},1500)},d=p=>{console.log("选择模板:",p.name)};return bs(()=>{console.log("AI数据分析组件已加载")}),(p,u)=>{const E=$("el-option"),ns=$("el-select"),H=$("el-icon"),ds=$("el-button"),v=$("el-radio-button"),K=$("el-radio-group"),ps=$("el-switch"),hs=$("el-tab-pane"),Ts=$("el-tabs"),b=$("el-tag");return a(),i("div",Ve,[s("div",ze,[s("div",Ae,[u[5]||(u[5]=s("h3",null,"iFlytek Spark AI数据分析中心",-1)),s("div",De,[t(ns,{modelValue:C.value,"onUpdate:modelValue":u[0]||(u[0]=e=>C.value=e),placeholder:"选择时间范围",size:"large"},{default:l(()=>[t(E,{label:"最近7天",value:"7d"}),t(E,{label:"最近30天",value:"30d"}),t(E,{label:"最近90天",value:"90d"}),t(E,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"]),t(ds,{type:"primary",onClick:us},{default:l(()=>[t(H,null,{default:l(()=>[t(f(Ns))]),_:1}),u[4]||(u[4]=_(" 生成报告 "))]),_:1,__:[4]})])]),s("div",je,[(a(!0),i(g,null,y(V,e=>(a(),i("div",{class:"metric-card",key:e.name},[s("div",{class:"metric-icon",style:z({backgroundColor:e.color})},[t(H,null,{default:l(()=>[(a(),R(G(e.icon)))]),_:2},1024)],4),s("div",Fe,[s("div",Pe,n(e.value),1),s("div",Ue,n(e.name),1),s("div",{class:L(["metric-trend",{positive:e.trend>0,negative:e.trend<0}])},[t(H,null,{default:l(()=>[(a(),R(G(e.trend>0?"ArrowUp":"ArrowDown")))]),_:2},1024),_(" "+n(Math.abs(e.trend))+"% ",1)],2)])]))),128))])]),s("div",Re,[s("div",Be,[u[10]||(u[10]=s("h4",null,"面试质量深度分析",-1)),s("div",qe,[t(K,{modelValue:T.value,"onUpdate:modelValue":u[1]||(u[1]=e=>T.value=e),size:"small"},{default:l(()=>[t(v,{value:"all"},{default:l(()=>u[6]||(u[6]=[_("全部")])),_:1,__:[6]}),t(v,{value:"excellent"},{default:l(()=>u[7]||(u[7]=[_("优秀")])),_:1,__:[7]}),t(v,{value:"good"},{default:l(()=>u[8]||(u[8]=[_("良好")])),_:1,__:[8]}),t(v,{value:"average"},{default:l(()=>u[9]||(u[9]=[_("一般")])),_:1,__:[9]})]),_:1},8,["modelValue"])])]),s("div",Ee,[s("div",Ne,[s("div",Le,[u[11]||(u[11]=s("h5",null,"面试质量分布",-1)),s("div",Je,[(a(!0),i(g,null,y(X,e=>(a(),i("div",{class:"legend-item",key:e.name},[s("span",{class:"legend-color",style:z({backgroundColor:e.color})},null,4),s("span",Qe,n(e.name)+" ("+n(e.percentage)+"%)",1)]))),128))])]),s("div",We,[s("div",Xe,[s("span",Ye,n(j.value),1),u[12]||(u[12]=s("small",null,"总面试数",-1))]),(a(),i("svg",Oe,[(a(!0),i(g,null,y(S.value,(e,D)=>(a(),i("circle",{key:D,cx:"100",cy:"100",r:"80",stroke:e.color,"stroke-width":20,"stroke-dasharray":e.dashArray,"stroke-dashoffset":e.dashOffset,fill:"none",transform:e.transform},null,8,He))),128))]))])]),s("div",Ke,[s("div",Ge,[u[13]||(u[13]=s("h5",null,"技能评估雷达图",-1)),s("div",Ze,[t(ps,{modelValue:W.value,"onUpdate:modelValue":u[2]||(u[2]=e=>W.value=e),"active-text":"对比模式"},null,8,["modelValue"])])]),s("div",so,[(a(),i("svg",to,[s("g",eo,[(a(),i(g,null,y(5,e=>s("circle",{key:e,cx:"150",cy:"150",r:e*25,stroke:"#e9ecef","stroke-width":"1",fill:"none"},null,8,oo)),64)),(a(!0),i(g,null,y(x,(e,D)=>(a(),i("g",{key:e.name},[s("line",{x1:"150",y1:"150",x2:e.x,y2:e.y,stroke:"#e9ecef","stroke-width":"1"},null,8,lo),s("text",{x:e.labelX,y:e.labelY,"text-anchor":"middle",class:"axis-label"},n(e.name),9,no)]))),128))]),s("polygon",{points:Y.value,fill:"rgba(33, 111, 255, 0.3)",stroke:"var(--iflytek-primary)","stroke-width":"2"},null,8,ao),(a(!0),i(g,null,y(I.value,(e,D)=>(a(),i("g",{key:D},[s("circle",{cx:e.x,cy:e.y,r:"4",fill:"var(--iflytek-primary)"},null,8,io)]))),128))]))])]),s("div",ro,[s("div",co,[u[16]||(u[16]=s("h5",null,"面试表现趋势",-1)),s("div",uo,[s("span",_o,[u[14]||(u[14]=_(" 平均分: ")),s("strong",null,n(as.value),1)]),s("span",vo,[u[15]||(u[15]=_(" 改进率: ")),s("strong",null,n(O.value)+"%",1)])])]),s("div",mo,[(a(),i("svg",po,[s("g",ho,[(a(),i(g,null,y(5,e=>s("line",{key:"h"+e,x1:"40",y1:e*32+20,x2:"380",y2:e*32+20,stroke:"#f0f0f0","stroke-width":"1"},null,8,go)),64)),(a(),i(g,null,y(7,e=>s("line",{key:"v"+e,x1:e*50+40,y1:"20",x2:e*50+40,y2:"180",stroke:"#f0f0f0","stroke-width":"1"},null,8,yo)),64))]),s("polyline",{points:M.value,fill:"none",stroke:"var(--iflytek-primary)","stroke-width":"3"},null,8,fo),(a(!0),i(g,null,y(ss.value,(e,D)=>(a(),i("g",{key:D},[s("circle",{cx:e.x,cy:e.y,r:"5",fill:"var(--iflytek-primary)"},null,8,$o),s("text",{x:e.x,y:e.y-10,"text-anchor":"middle",class:"point-label"},n(e.value),9,bo)]))),128))]))])])])]),s("div",ko,[s("div",wo,[u[17]||(u[17]=s("h4",null,"候选人深度洞察",-1)),s("div",xo,[t(Ts,{modelValue:q.value,"onUpdate:modelValue":u[3]||(u[3]=e=>q.value=e),type:"card"},{default:l(()=>[t(hs,{label:"能力分析",name:"abilities"}),t(hs,{label:"行为特征",name:"behavior"}),t(hs,{label:"匹配度评估",name:"matching"})]),_:1},8,["modelValue"])])]),s("div",So,[q.value==="abilities"?(a(),i("div",Mo,[s("div",Co,[u[18]||(u[18]=et('<div class="matrix-header" data-v-a518992c><h5 data-v-a518992c>技能能力矩阵</h5><div class="matrix-legend" data-v-a518992c><span class="legend-item" data-v-a518992c><span class="legend-dot high" data-v-a518992c></span>高水平 (80-100) </span><span class="legend-item" data-v-a518992c><span class="legend-dot medium" data-v-a518992c></span>中等 (60-79) </span><span class="legend-item" data-v-a518992c><span class="legend-dot low" data-v-a518992c></span>待提升 (0-59) </span></div></div>',1)),s("div",To,[(a(!0),i(g,null,y(o,e=>(a(),i("div",{class:"matrix-row",key:e.name},[s("div",Io,n(e.name),1),s("div",Vo,[(a(!0),i(g,null,y(e.levels,D=>(a(),i("div",{key:D.name,class:L(["level-cell",D.class]),title:`${D.name}: ${D.count}人`},n(D.count),11,zo))),128))]),s("div",Ao,n(e.average),1)]))),128))])]),s("div",Do,[u[20]||(u[20]=s("h5",null,"AI智能建议",-1)),s("div",jo,[(a(!0),i(g,null,y(k,e=>(a(),i("div",{class:"recommendation-item",key:e.id},[s("div",{class:L(["rec-priority",e.priority])},n(e.priorityText),3),s("div",Fo,[s("h6",null,n(e.title),1),s("p",null,n(e.description),1)]),s("div",Po,[u[19]||(u[19]=s("span",{class:"impact-label"},"预期提升",-1)),s("span",Uo,n(e.expectedImprovement)+"%",1)])]))),128))])])])):rs("",!0),q.value==="behavior"?(a(),i("div",Ro,[s("div",Bo,[u[21]||(u[21]=s("h5",null,"行为模式识别",-1)),s("div",qo,[(a(!0),i(g,null,y(A,e=>(a(),i("div",{class:"pattern-card",key:e.name},[s("div",{class:"pattern-icon",style:z({backgroundColor:e.color})},n(e.icon),5),s("div",Eo,[s("h6",null,n(e.name),1),s("div",No,n(e.percentage)+"%",1),s("div",Lo,n(e.description),1)])]))),128))])]),s("div",Jo,[u[22]||(u[22]=s("h5",null,"交互热力图",-1)),s("div",Qo,[s("div",Wo,[(a(!0),i(g,null,y(ts,(e,D)=>(a(),i("div",{key:D,class:"heatmap-cell",style:z({backgroundColor:ls(e.intensity)}),title:`时间段 ${e.time}: ${e.intensity}% 活跃度`},null,12,Xo))),128))]),s("div",Yo,[s("div",Oo,[(a(),i(g,null,y(w,e=>s("span",{key:e},n(e),1)),64))]),s("div",Ho,[(a(),i(g,null,y(c,e=>s("span",{key:e},n(e),1)),64))])])])])])):rs("",!0),q.value==="matching"?(a(),i("div",Ko,[s("div",Go,[u[26]||(u[26]=s("h5",null,"岗位匹配度分析",-1)),s("div",Zo,[s("div",sl,[s("div",tl,n(J.highMatch),1),u[23]||(u[23]=s("div",{class:"stat-label"},"高匹配度",-1))]),s("div",el,[s("div",ol,n(J.mediumMatch),1),u[24]||(u[24]=s("div",{class:"stat-label"},"中等匹配",-1))]),s("div",ll,[s("div",nl,n(J.lowMatch),1),u[25]||(u[25]=s("div",{class:"stat-label"},"低匹配度",-1))])])]),s("div",al,[u[27]||(u[27]=s("h5",null,"关键匹配因素",-1)),s("div",il,[(a(!0),i(g,null,y(Q,e=>(a(),i("div",{class:"factor-item",key:e.name},[s("div",dl,n(e.name),1),s("div",rl,"权重: "+n(e.weight)+"%",1),s("div",cl,[s("div",{class:"factor-fill",style:z({width:e.score+"%",backgroundColor:_s(e.score)})},null,4)]),s("div",ul,n(e.score)+"%",1)]))),128))])])])):rs("",!0)])]),s("div",_l,[s("div",vl,[u[30]||(u[30]=s("h4",null,"智能报告生成",-1)),s("div",ml,[t(ds,{onClick:is},{default:l(()=>u[28]||(u[28]=[_("预览报告")])),_:1,__:[28]}),t(ds,{type:"primary",onClick:m},{default:l(()=>[t(H,null,{default:l(()=>[t(f(Ls))]),_:1}),u[29]||(u[29]=_(" 导出报告 "))]),_:1,__:[29]})])]),s("div",pl,[(a(!0),i(g,null,y(cs,e=>(a(),i("div",{class:"template-item",key:e.id},[s("div",hl,[s("div",{class:"preview-image",style:z({backgroundColor:e.color})},[t(H,null,{default:l(()=>[(a(),R(G(e.icon)))]),_:2},1024)],4)]),s("div",gl,[s("h6",null,n(e.name),1),s("p",null,n(e.description),1),s("div",yl,[(a(!0),i(g,null,y(e.features,D=>(a(),R(b,{key:D,size:"small"},{default:l(()=>[_(n(D),1)]),_:2},1024))),128))])]),s("div",fl,[t(ds,{size:"small",onClick:D=>d(e)},{default:l(()=>u[31]||(u[31]=[_("选择模板")])),_:2,__:[31]},1032,["onClick"])])]))),128))])])])}}},bl=$s($l,[["__scopeId","data-v-a518992c"]]);const kl={class:"enterprise-professional-features"},wl={class:"features-navigation"},xl={class:"tab-label"},Sl={class:"tab-label"},Ml={class:"tab-label"},Cl={key:0,class:"feature-panel batch-interview-panel"},Tl={class:"panel-header"},Il={class:"batch-actions"},Vl={class:"batch-overview"},zl={class:"overview-stats"},Al={class:"stat-content"},Dl={class:"stat-value"},jl={class:"stat-label"},Fl={class:"batch-interviews-list"},Pl={class:"list-header"},Ul={class:"list-filters"},Rl={class:"interviews-grid"},Bl={class:"interview-header"},ql={class:"interview-title"},El={class:"interview-progress"},Nl={class:"progress-text"},Ll={class:"interview-details"},Jl={class:"detail-item"},Ql={class:"detail-value"},Wl={class:"detail-item"},Xl={class:"interviewers-list"},Yl={key:0,class:"more-count"},Ol={class:"detail-item"},Hl={class:"detail-value"},Kl={class:"interview-actions"},Gl={key:1,class:"feature-panel team-collaboration-panel"},Zl={class:"panel-header"},sn={class:"collaboration-actions"},tn={class:"team-overview"},en={class:"team-stats"},on={class:"team-stat-content"},ln={class:"team-stat-value"},nn={class:"team-stat-label"},an={class:"interviewer-assignment"},dn={class:"assignment-header"},rn={class:"assignment-controls"},cn={class:"interviewers-grid"},un={class:"interviewer-avatar"},_n={class:"interviewer-info"},vn={class:"interviewer-role"},mn={class:"interviewer-department"},pn={class:"interviewer-stats"},hn={class:"stat-item"},gn={class:"stat-value"},yn={class:"stat-item"},fn={class:"stat-value"},$n={class:"stat-item"},bn={class:"expertise-tags"},kn={class:"interviewer-actions"},wn={class:"collaborative-scoring"},xn={class:"scoring-header"},Sn={class:"scoring-mode"},Mn={class:"scoring-sessions"},Cn={class:"session-header"},Tn={class:"candidate-info"},In={class:"candidate-details"},Vn={class:"candidate-position"},zn={class:"session-status"},An={class:"scoring-progress"},Dn={class:"interviewer-name"},jn={class:"score-status"},Fn={key:1,class:"pending-score"},Pn={class:"session-actions"},Un={key:2,class:"feature-panel talent-pool-panel"},Rn={class:"panel-header"},Bn={class:"talent-actions"},qn={class:"talent-statistics"},En={class:"stats-overview"},Nn={class:"overview-content"},Ln={class:"overview-value"},Jn={class:"overview-label"},Qn={class:"skills-distribution"},Wn={class:"skills-chart"},Xn={class:"skill-info"},Yn={class:"skill-name"},On={class:"skill-count"},Hn={class:"skill-progress"},Kn={class:"talent-search-filter"},Gn={class:"search-header"},Zn={class:"search-stats"},sa={class:"search-result-count"},ta={class:"search-controls"},ea={class:"search-input"},oa={class:"filter-controls"},la={class:"advanced-filters"},na={key:0,class:"advanced-filter-panel"},aa={class:"filter-section"},ia={class:"filter-section"},da={class:"filter-section"},ra={__name:"EnterpriseProfessionalFeatures",setup(ks){const C=es("batch-interview"),T=es(""),q=es(!1),W=es("independent"),V=h({status:"all",position:"all"}),X=h([{name:"进行中面试",value:"12",trend:8.5,color:"#3498db",icon:"UserFilled"},{name:"今日完成",value:"28",trend:15.2,color:"#2ecc71",icon:"Check"},{name:"待处理",value:"6",trend:-5.3,color:"#f39c12",icon:"Clock"},{name:"总候选人",value:"156",trend:12.8,color:"#e74c3c",icon:"DataBoard"}]),j=h([{id:1,title:"前端工程师批量面试 - 第3期",status:"ongoing",statusText:"进行中",progress:65,completedCount:13,totalCount:20,position:"前端工程师",interviewers:[{id:1,avatar:"/images/placeholder-feature.jpg"},{id:2,avatar:"/images/placeholder-feature.jpg"},{id:3,avatar:"/images/placeholder-feature.jpg"},{id:4,avatar:"/images/placeholder-feature.jpg"}],createdAt:new Date("2024-01-15")},{id:2,title:"算法工程师专场面试",status:"preparing",statusText:"准备中",progress:25,completedCount:3,totalCount:12,position:"算法工程师",interviewers:[{id:5,avatar:"/images/placeholder-feature.jpg"},{id:6,avatar:"/images/placeholder-feature.jpg"}],createdAt:new Date("2024-01-18")},{id:3,title:"后端开发团队扩招",status:"completed",statusText:"已完成",progress:100,completedCount:15,totalCount:15,position:"后端工程师",interviewers:[{id:7,avatar:"/images/placeholder-feature.jpg"},{id:8,avatar:"/images/placeholder-feature.jpg"},{id:9,avatar:"/images/placeholder-feature.jpg"}],createdAt:new Date("2024-01-10")}]),S=h([{name:"团队成员",value:"24",color:"#3498db",icon:"UserFilled"},{name:"活跃面试官",value:"18",color:"#2ecc71",icon:"Connection"},{name:"本周面试",value:"89",color:"#f39c12",icon:"Calendar"},{name:"平均评分",value:"4.2",color:"#e74c3c",icon:"Medal"}]),x=h([{id:1,name:"张技术总监",role:"技术总监",department:"研发部",avatar:"/images/placeholder-demo.jpg",status:"online",weeklyInterviews:8,avgRating:4.5,expertise:["Vue.js","React","Node.js","系统架构"]},{id:2,name:"李高级工程师",role:"高级工程师",department:"前端团队",avatar:"/images/placeholder-demo.jpg",status:"busy",weeklyInterviews:12,avgRating:4.2,expertise:["JavaScript","TypeScript","性能优化"]},{id:3,name:"王算法专家",role:"算法专家",department:"AI团队",avatar:"/images/placeholder-demo.jpg",status:"offline",weeklyInterviews:6,avgRating:4.8,expertise:["机器学习","深度学习","Python"]}]),os=h([{id:1,candidate:{name:"候选人A",position:"前端工程师",avatar:"/images/placeholder-case.jpg"},status:"in-progress",statusText:"评分中",interviewers:[{id:1,name:"张总监",score:4},{id:2,name:"李工程师",score:null},{id:3,name:"王专家",score:5}]},{id:2,candidate:{name:"候选人B",position:"算法工程师",avatar:"/images/placeholder-case.jpg"},status:"completed",statusText:"已完成",interviewers:[{id:1,name:"张总监",score:3},{id:2,name:"李工程师",score:4},{id:3,name:"王专家",score:4}]}]),I=h({experience:"",education:"",location:"",status:""}),Y=h({skills:[],salaryRange:[20,80],minRating:0}),U=h([{name:"总人才数",value:"2,847",change:12.5,color:"#3498db",icon:"UserFilled"},{name:"活跃候选人",value:"1,256",change:8.3,color:"#2ecc71",icon:"Connection"},{name:"本月新增",value:"189",change:15.7,color:"#f39c12",icon:"Plus"},{name:"成功入职",value:"78",change:22.1,color:"#e74c3c",icon:"Medal"}]),as=h([{name:"JavaScript",count:456,color:"#f7df1e"},{name:"Vue.js",count:389,color:"#4fc08d"},{name:"React",count:342,color:"#61dafb"},{name:"Python",count:298,color:"#3776ab"},{name:"Java",count:267,color:"#ed8b00"},{name:"Node.js",count:234,color:"#339933"},{name:"TypeScript",count:198,color:"#3178c6"},{name:"Go",count:156,color:"#00add8"}]),O=["JavaScript","Vue.js","React","Python","Java","Node.js","TypeScript","Go","Docker","Kubernetes","AWS","MySQL"],ss=h([{id:1,name:"张小明",position:"前端工程师",experience:"3-5",education:"bachelor",location:"beijing",status:"active",skills:["Vue.js","JavaScript","TypeScript"],rating:4.2,salary:25},{id:2,name:"李小红",position:"后端工程师",experience:"5-8",education:"master",location:"shanghai",status:"job-seeking",skills:["Java","Spring","MySQL"],rating:4.5,salary:35}]),M=Z(()=>Math.max(...as.map(b=>b.count))),o=Z(()=>j.filter(b=>{const e=V.status==="all"||b.status===V.status,D=V.position==="all"||b.position.includes(V.position);return e&&D})),k=Z(()=>ss.filter(b=>{const e=!T.value||b.name.includes(T.value)||b.skills.some(ys=>ys.includes(T.value)),D=!I.experience||b.experience===I.experience,F=!I.education||b.education===I.education,gs=!I.location||b.location===I.location,Is=!I.status||b.status===I.status;return e&&D&&F&&gs&&Is})),A=b=>{console.log("切换到标签:",b)},ts=b=>({preparing:"info",ongoing:"primary",completed:"success",paused:"warning",cancelled:"danger"})[b]||"info",w=b=>b>=80?"#2ecc71":b>=60?"#f39c12":"#e74c3c",c=b=>({pending:"info","in-progress":"primary",completed:"success"})[b]||"info",J=b=>b.toLocaleDateString("zh-CN"),Q=b=>`${b}K`,cs=()=>{console.log("创建批量面试")},us=()=>{console.log("批量导入候选人")},ls=b=>{console.log("管理批量面试:",b.title)},_s=b=>{console.log("查看批量面试报告:",b.title)},is=b=>{console.log("批量面试操作:",b.action,b.interview.title)},m=()=>{console.log("邀请团队成员")},d=()=>{console.log("创建团队")},p=()=>{console.log("智能分配面试官")},u=()=>{console.log("查看日程日历")},E=b=>{console.log("分配面试给:",b.name)},ns=b=>{console.log("查看面试官详情:",b.name)},H=b=>{console.log("参与评分:",b.candidate.name)},ds=b=>{console.log("查看评分详情:",b.candidate.name)},v=()=>{console.log("添加人才档案")},K=()=>{console.log("批量导入人才数据")},ps=()=>{console.log("导出人才数据")},hs=()=>{console.log("搜索输入:",T.value)},Ts=()=>{console.log("保存搜索模板")};return bs(()=>{console.log("企业端专业功能组件已加载")}),(b,e)=>{const D=$("User"),F=$("el-icon"),gs=$("el-tab-pane"),Is=$("el-tabs"),ys=$("CirclePlus"),B=$("el-button"),P=$("el-option"),vs=$("el-select"),Vs=$("el-tag"),Js=$("el-progress"),zs=$("el-avatar"),ws=$("el-dropdown-item"),Qs=$("el-dropdown-menu"),Ws=$("el-dropdown"),Xs=$("Star"),As=$("el-radio-button"),Ys=$("el-radio-group"),Fs=$("el-rate"),Os=$("el-input"),Hs=$("el-slider");return a(),i("div",kl,[s("div",wl,[t(Is,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=r=>C.value=r),type:"card",onTabChange:A},{default:l(()=>[t(gs,{label:"批量面试管理",name:"batch-interview"},{label:l(()=>[s("div",xl,[t(F,null,{default:l(()=>[t(D)]),_:1}),e[13]||(e[13]=s("span",null,"批量面试管理",-1))])]),_:1}),t(gs,{label:"团队协作",name:"team-collaboration"},{label:l(()=>[s("div",Sl,[t(F,null,{default:l(()=>[t(f(ot))]),_:1}),e[14]||(e[14]=s("span",null,"团队协作",-1))])]),_:1}),t(gs,{label:"人才库运营",name:"talent-pool"},{label:l(()=>[s("div",Ml,[t(F,null,{default:l(()=>[t(f(lt))]),_:1}),e[15]||(e[15]=s("span",null,"人才库运营",-1))])]),_:1})]),_:1},8,["modelValue"])]),C.value==="batch-interview"?(a(),i("div",Cl,[s("div",Tl,[e[18]||(e[18]=s("h4",null,"批量面试管理系统",-1)),s("div",Il,[t(B,{type:"primary",onClick:cs},{default:l(()=>[t(F,null,{default:l(()=>[t(ys)]),_:1}),e[16]||(e[16]=_(" 创建批量面试 "))]),_:1,__:[16]}),t(B,{onClick:us},{default:l(()=>[t(F,null,{default:l(()=>[t(f(Ps))]),_:1}),e[17]||(e[17]=_(" 批量导入候选人 "))]),_:1,__:[17]})])]),s("div",Vl,[s("div",zl,[(a(!0),i(g,null,y(X,r=>(a(),i("div",{class:"stat-card",key:r.name},[s("div",{class:"stat-icon",style:z({backgroundColor:r.color})},[t(F,null,{default:l(()=>[(a(),R(G(r.icon)))]),_:2},1024)],4),s("div",Al,[s("div",Dl,n(r.value),1),s("div",jl,n(r.name),1),s("div",{class:L(["stat-trend",{positive:r.trend>0,negative:r.trend<0}])},[t(F,null,{default:l(()=>[(a(),R(G(r.trend>0?"ArrowUp":"ArrowDown")))]),_:2},1024),_(" "+n(Math.abs(r.trend))+"% ",1)],2)])]))),128))])]),s("div",Fl,[s("div",Pl,[e[19]||(e[19]=s("h5",null,"进行中的批量面试",-1)),s("div",Ul,[t(vs,{modelValue:V.status,"onUpdate:modelValue":e[1]||(e[1]=r=>V.status=r),placeholder:"状态筛选",size:"small"},{default:l(()=>[t(P,{label:"全部",value:"all"}),t(P,{label:"准备中",value:"preparing"}),t(P,{label:"进行中",value:"ongoing"}),t(P,{label:"已完成",value:"completed"})]),_:1},8,["modelValue"]),t(vs,{modelValue:V.position,"onUpdate:modelValue":e[2]||(e[2]=r=>V.position=r),placeholder:"职位筛选",size:"small"},{default:l(()=>[t(P,{label:"全部职位",value:"all"}),t(P,{label:"前端工程师",value:"frontend"}),t(P,{label:"后端工程师",value:"backend"}),t(P,{label:"算法工程师",value:"algorithm"})]),_:1},8,["modelValue"])])]),s("div",Rl,[(a(!0),i(g,null,y(o.value,r=>(a(),i("div",{class:"interview-card",key:r.id},[s("div",Bl,[s("div",ql,[s("h6",null,n(r.title),1),t(Vs,{type:ts(r.status),size:"small"},{default:l(()=>[_(n(r.statusText),1)]),_:2},1032,["type"])]),s("div",El,[t(Js,{percentage:r.progress,color:w(r.progress),"show-text":!1},null,8,["percentage","color"]),s("span",Nl,n(r.completedCount)+"/"+n(r.totalCount),1)])]),s("div",Ll,[s("div",Jl,[e[20]||(e[20]=s("span",{class:"detail-label"},"职位:",-1)),s("span",Ql,n(r.position),1)]),s("div",Wl,[e[21]||(e[21]=s("span",{class:"detail-label"},"面试官:",-1)),s("div",Xl,[(a(!0),i(g,null,y(r.interviewers.slice(0,3),N=>(a(),R(zs,{key:N.id,size:24,src:N.avatar},null,8,["src"]))),128)),r.interviewers.length>3?(a(),i("span",Yl," +"+n(r.interviewers.length-3),1)):rs("",!0)])]),s("div",Ol,[e[22]||(e[22]=s("span",{class:"detail-label"},"创建时间:",-1)),s("span",Hl,n(J(r.createdAt)),1)])]),s("div",Kl,[t(B,{size:"small",type:"primary",onClick:N=>ls(r)},{default:l(()=>e[23]||(e[23]=[_(" 管理面试 ")])),_:2,__:[23]},1032,["onClick"]),t(B,{size:"small",onClick:N=>_s(r)},{default:l(()=>e[24]||(e[24]=[_(" 查看报告 ")])),_:2,__:[24]},1032,["onClick"]),t(Ws,{onCommand:is},{dropdown:l(()=>[t(Qs,null,{default:l(()=>[t(ws,{command:{action:"edit",interview:r}},{default:l(()=>e[26]||(e[26]=[_("编辑设置")])),_:2,__:[26]},1032,["command"]),t(ws,{command:{action:"duplicate",interview:r}},{default:l(()=>e[27]||(e[27]=[_("复制面试")])),_:2,__:[27]},1032,["command"]),t(ws,{command:{action:"export",interview:r}},{default:l(()=>e[28]||(e[28]=[_("导出数据")])),_:2,__:[28]},1032,["command"]),t(ws,{command:{action:"archive",interview:r},divided:""},{default:l(()=>e[29]||(e[29]=[_("归档面试")])),_:2,__:[29]},1032,["command"])]),_:2},1024)]),default:l(()=>[t(B,{size:"small"},{default:l(()=>[e[25]||(e[25]=_(" 更多操作")),t(F,{class:"el-icon--right"},{default:l(()=>[t(f(nt))]),_:1})]),_:1,__:[25]})]),_:2},1024)])]))),128))])])])):rs("",!0),C.value==="team-collaboration"?(a(),i("div",Gl,[s("div",Zl,[e[32]||(e[32]=s("h4",null,"团队协作管理",-1)),s("div",sn,[t(B,{type:"primary",onClick:m},{default:l(()=>[t(F,null,{default:l(()=>[t(D)]),_:1}),e[30]||(e[30]=_(" 邀请成员 "))]),_:1,__:[30]}),t(B,{onClick:d},{default:l(()=>[t(F,null,{default:l(()=>[t(ys)]),_:1}),e[31]||(e[31]=_(" 创建团队 "))]),_:1,__:[31]})])]),s("div",tn,[s("div",en,[(a(!0),i(g,null,y(S,r=>(a(),i("div",{class:"team-stat-card",key:r.name},[s("div",{class:"team-stat-icon",style:z({backgroundColor:r.color})},[t(F,null,{default:l(()=>[(a(),R(G(r.icon)))]),_:2},1024)],4),s("div",on,[s("div",ln,n(r.value),1),s("div",nn,n(r.name),1)])]))),128))])]),s("div",an,[s("div",dn,[e[35]||(e[35]=s("h5",null,"面试官分配与调度",-1)),s("div",rn,[t(B,{size:"small",onClick:p},{default:l(()=>[t(F,null,{default:l(()=>[t(Xs)]),_:1}),e[33]||(e[33]=_(" 智能分配 "))]),_:1,__:[33]}),t(B,{size:"small",onClick:u},{default:l(()=>[t(F,null,{default:l(()=>[t(f(at))]),_:1}),e[34]||(e[34]=_(" 查看日程 "))]),_:1,__:[34]})])]),s("div",cn,[(a(!0),i(g,null,y(x,r=>(a(),i("div",{class:"interviewer-card",key:r.id},[s("div",un,[t(zs,{size:60,src:r.avatar},null,8,["src"]),s("div",{class:L(["status-indicator",r.status])},null,2)]),s("div",_n,[s("h6",null,n(r.name),1),s("div",vn,n(r.role),1),s("div",mn,n(r.department),1)]),s("div",pn,[s("div",hn,[e[36]||(e[36]=s("span",{class:"stat-label"},"本周面试:",-1)),s("span",gn,n(r.weeklyInterviews),1)]),s("div",yn,[e[37]||(e[37]=s("span",{class:"stat-label"},"评分平均:",-1)),s("span",fn,n(r.avgRating),1)]),s("div",$n,[e[38]||(e[38]=s("span",{class:"stat-label"},"专业领域:",-1)),s("div",bn,[(a(!0),i(g,null,y(r.expertise.slice(0,2),N=>(a(),R(Vs,{key:N,size:"small"},{default:l(()=>[_(n(N),1)]),_:2},1024))),128))])])]),s("div",kn,[t(B,{size:"small",type:"primary",onClick:N=>E(r)},{default:l(()=>e[39]||(e[39]=[_(" 分配面试 ")])),_:2,__:[39]},1032,["onClick"]),t(B,{size:"small",onClick:N=>ns(r)},{default:l(()=>e[40]||(e[40]=[_(" 查看详情 ")])),_:2,__:[40]},1032,["onClick"])])]))),128))])]),s("div",wn,[s("div",xn,[e[44]||(e[44]=s("h5",null,"协作评分与决策",-1)),s("div",Sn,[t(Ys,{modelValue:W.value,"onUpdate:modelValue":e[3]||(e[3]=r=>W.value=r),size:"small"},{default:l(()=>[t(As,{value:"independent"},{default:l(()=>e[41]||(e[41]=[_("独立评分")])),_:1,__:[41]}),t(As,{value:"discussion"},{default:l(()=>e[42]||(e[42]=[_("讨论评分")])),_:1,__:[42]}),t(As,{value:"consensus"},{default:l(()=>e[43]||(e[43]=[_("共识评分")])),_:1,__:[43]})]),_:1},8,["modelValue"])])]),s("div",Mn,[(a(!0),i(g,null,y(os,r=>(a(),i("div",{class:"session-card",key:r.id},[s("div",Cn,[s("div",Tn,[t(zs,{size:40,src:r.candidate.avatar},null,8,["src"]),s("div",In,[s("h6",null,n(r.candidate.name),1),s("span",Vn,n(r.candidate.position),1)])]),s("div",zn,[t(Vs,{type:c(r.status)},{default:l(()=>[_(n(r.statusText),1)]),_:2},1032,["type"])])]),s("div",An,[(a(!0),i(g,null,y(r.interviewers,N=>(a(),i("div",{class:"progress-item",key:N.id},[s("div",Dn,n(N.name),1),s("div",jn,[N.score?(a(),R(Fs,{key:0,modelValue:N.score,"onUpdate:modelValue":Ks=>N.score=Ks,disabled:"","show-score":"","text-color":"#ff9900"},null,8,["modelValue","onUpdate:modelValue"])):(a(),i("span",Fn,"待评分"))])]))),128))]),s("div",Pn,[t(B,{size:"small",type:"primary",onClick:N=>H(r)},{default:l(()=>e[45]||(e[45]=[_(" 参与评分 ")])),_:2,__:[45]},1032,["onClick"]),t(B,{size:"small",onClick:N=>ds(r)},{default:l(()=>e[46]||(e[46]=[_(" 查看详情 ")])),_:2,__:[46]},1032,["onClick"])])]))),128))])])])):rs("",!0),C.value==="talent-pool"?(a(),i("div",Un,[s("div",Rn,[e[50]||(e[50]=s("h4",null,"人才库运营管理",-1)),s("div",Bn,[t(B,{type:"primary",onClick:v},{default:l(()=>[t(F,null,{default:l(()=>[t(ys)]),_:1}),e[47]||(e[47]=_(" 添加人才档案 "))]),_:1,__:[47]}),t(B,{onClick:K},{default:l(()=>[t(F,null,{default:l(()=>[t(f(Ps))]),_:1}),e[48]||(e[48]=_(" 批量导入 "))]),_:1,__:[48]}),t(B,{onClick:ps},{default:l(()=>[t(F,null,{default:l(()=>[t(f(Ls))]),_:1}),e[49]||(e[49]=_(" 导出数据 "))]),_:1,__:[49]})])]),s("div",qn,[s("div",En,[(a(!0),i(g,null,y(U,r=>(a(),i("div",{class:"overview-card",key:r.name},[s("div",{class:"overview-icon",style:z({backgroundColor:r.color})},[t(F,null,{default:l(()=>[(a(),R(G(r.icon)))]),_:2},1024)],4),s("div",Nn,[s("div",Ln,n(r.value),1),s("div",Jn,n(r.name),1),s("div",{class:L(["overview-change",{positive:r.change>0}])},[t(F,null,{default:l(()=>[(a(),R(G(r.change>0?"ArrowUp":"ArrowDown")))]),_:2},1024),_(" "+n(Math.abs(r.change))+"% ",1)],2)])]))),128))]),s("div",Qn,[e[51]||(e[51]=s("h5",null,"技能分布统计",-1)),s("div",Wn,[(a(!0),i(g,null,y(as,r=>(a(),i("div",{class:"skill-bar",key:r.name},[s("div",Xn,[s("span",Yn,n(r.name),1),s("span",On,n(r.count)+"人",1)]),s("div",Hn,[s("div",{class:"skill-fill",style:z({width:r.count/M.value*100+"%",backgroundColor:r.color})},null,4)])]))),128))])])]),s("div",Kn,[s("div",Gn,[e[52]||(e[52]=s("h5",null,"人才搜索与筛选",-1)),s("div",Zn,[s("span",sa,"找到 "+n(k.value.length)+" 位候选人",1)])]),s("div",ta,[s("div",ea,[t(Os,{modelValue:T.value,"onUpdate:modelValue":e[4]||(e[4]=r=>T.value=r),placeholder:"搜索姓名、技能、公司...",onInput:hs},{prefix:l(()=>[t(F,null,{default:l(()=>[t(f(Us))]),_:1})]),_:1},8,["modelValue"])]),s("div",oa,[t(vs,{modelValue:I.experience,"onUpdate:modelValue":e[5]||(e[5]=r=>I.experience=r),placeholder:"工作经验",clearable:""},{default:l(()=>[t(P,{label:"1-3年",value:"1-3"}),t(P,{label:"3-5年",value:"3-5"}),t(P,{label:"5-8年",value:"5-8"}),t(P,{label:"8年以上",value:"8+"})]),_:1},8,["modelValue"]),t(vs,{modelValue:I.education,"onUpdate:modelValue":e[6]||(e[6]=r=>I.education=r),placeholder:"学历要求",clearable:""},{default:l(()=>[t(P,{label:"本科",value:"bachelor"}),t(P,{label:"硕士",value:"master"}),t(P,{label:"博士",value:"phd"})]),_:1},8,["modelValue"]),t(vs,{modelValue:I.location,"onUpdate:modelValue":e[7]||(e[7]=r=>I.location=r),placeholder:"工作地点",clearable:""},{default:l(()=>[t(P,{label:"北京",value:"beijing"}),t(P,{label:"上海",value:"shanghai"}),t(P,{label:"深圳",value:"shenzhen"}),t(P,{label:"杭州",value:"hangzhou"})]),_:1},8,["modelValue"]),t(vs,{modelValue:I.status,"onUpdate:modelValue":e[8]||(e[8]=r=>I.status=r),placeholder:"候选人状态",clearable:""},{default:l(()=>[t(P,{label:"活跃",value:"active"}),t(P,{label:"求职中",value:"job-seeking"}),t(P,{label:"观望中",value:"passive"})]),_:1},8,["modelValue"])]),s("div",la,[t(B,{size:"small",onClick:e[9]||(e[9]=r=>q.value=!q.value)},{default:l(()=>[t(F,null,{default:l(()=>[t(f(Us))]),_:1}),e[53]||(e[53]=_(" 高级筛选 "))]),_:1,__:[53]}),t(B,{size:"small",onClick:Ts},{default:l(()=>[t(F,null,{default:l(()=>[t(f(js))]),_:1}),e[54]||(e[54]=_(" 保存搜索 "))]),_:1,__:[54]})])]),q.value?(a(),i("div",na,[s("div",aa,[e[55]||(e[55]=s("h6",null,"技能要求",-1)),t(vs,{modelValue:Y.skills,"onUpdate:modelValue":e[10]||(e[10]=r=>Y.skills=r),multiple:"",placeholder:"选择技能标签"},{default:l(()=>[(a(),i(g,null,y(O,r=>t(P,{key:r,label:r,value:r},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),s("div",ia,[e[56]||(e[56]=s("h6",null,"薪资范围",-1)),t(Hs,{modelValue:Y.salaryRange,"onUpdate:modelValue":e[11]||(e[11]=r=>Y.salaryRange=r),range:"",min:0,max:100,"format-tooltip":Q},null,8,["modelValue"])]),s("div",da,[e[57]||(e[57]=s("h6",null,"面试评分",-1)),t(Fs,{modelValue:Y.minRating,"onUpdate:modelValue":e[12]||(e[12]=r=>Y.minRating=r),"show-text":""},null,8,["modelValue"])])])):rs("",!0)])])):rs("",!0)])}}},ca=$s(ra,[["__scopeId","data-v-cc841dde"]]);const ua={class:"system-integration-monitor"},_a={class:"system-overview"},va={class:"overview-header"},ma={class:"status-grid"},pa={class:"metric-header"},ha={class:"metric-info"},ga={class:"metric-chart"},ya={class:"chart-line"},fa={class:"performance-monitoring"},$a={class:"monitoring-header"},ba={class:"monitoring-controls"},ka={class:"performance-charts"},wa={class:"chart-container"},xa={class:"load-performance"},Sa={class:"performance-bar"},Ma={class:"timing-details"},Ca={class:"timing-label"},Ta={class:"timing-value"},Ia={class:"chart-container"},Va={class:"api-performance"},za={class:"api-list"},Aa={class:"api-info"},Da={class:"api-name"},ja={class:"api-timing"},Fa={class:"timing-bar"},Pa={class:"timing-text"},Ua={class:"chart-container"},Ra={class:"memory-usage"},Ba={class:"memory-chart"},qa={class:"memory-text"},Ea={class:"usage-value"},Na={class:"usage-total"},La={class:"memory-details"},Ja={class:"memory-item"},Qa={class:"memory-value"},Wa={class:"memory-item"},Xa={class:"memory-value"},Ya={class:"memory-item"},Oa={class:"error-monitoring"},Ha={class:"error-header"},Ka={class:"error-stats"},Ga={class:"error-content"},Za={class:"error-summary"},si={class:"error-type"},ti={class:"recent-errors"},ei={class:"error-list"},oi={class:"error-details"},li={class:"error-message"},ni={class:"error-meta"},ai={class:"error-time"},ii={class:"error-source"},di={class:"error-actions"},ri={class:"cache-management"},ci={class:"cache-header"},ui={class:"cache-actions"},_i={class:"cache-stats"},vi={class:"cache-overview"},mi={class:"cache-label"},pi={class:"cache-distribution"},hi={class:"distribution-chart"},gi={class:"bar-label"},yi={class:"bar-container"},fi={class:"bar-value"},$i={class:"optimization-suggestions"},bi={class:"suggestions-header"},ki={class:"suggestions-count"},wi={class:"suggestions-list"},xi={class:"suggestion-content"},Si={class:"suggestion-title"},Mi={class:"suggestion-description"},Ci={class:"suggestion-impact"},Ti={class:"impact-value"},Ii={class:"suggestion-actions"},Vi={__name:"SystemIntegrationMonitor",setup(ks){const C=es(!0),T=es(null),q=h({level:"healthy",text:"系统运行正常",icon:"Check"}),W=h([{name:"响应时间",value:"156ms",status:"good",color:"#2ecc71",icon:"Timer",trend:-5.2,history:[120,135,142,156,148,152,156],max:200},{name:"CPU使用率",value:"45%",status:"normal",color:"#3498db",icon:"Monitor",trend:2.1,history:[40,42,45,48,46,44,45],max:100},{name:"内存使用",value:"68MB",status:"normal",color:"#f39c12",icon:"Cpu",trend:8.3,history:[55,58,62,65,66,67,68],max:100},{name:"错误率",value:"0.2%",status:"good",color:"#e74c3c",icon:"Warning",trend:-12.5,history:[.5,.4,.3,.2,.3,.2,.2],max:1}]),V=h({dns:45,connect:120,request:85,response:230,render:180}),X=h([{name:"/api/interview/start",responseTime:156,status:"success",statusText:"正常"},{name:"/api/candidate/profile",responseTime:89,status:"success",statusText:"正常"},{name:"/api/analysis/report",responseTime:234,status:"warning",statusText:"较慢"},{name:"/api/spark/generate",responseTime:445,status:"error",statusText:"超时"}]),j=Z(()=>Math.max(...X.map(m=>m.responseTime))),S=h({used:68,total:128,available:60,percentage:53}),x=h({level:"success",text:"错误率较低"}),os=h({javascript:2,api:1,network:0,system:1}),I=h([{id:1,type:"javascript",severity:"medium",message:"Cannot read property of undefined",source:"InterviewComponent.vue:45",timestamp:new Date(Date.now()-5*60*1e3)},{id:2,type:"api",severity:"high",message:"API请求超时: /api/spark/generate",source:"SparkService.js:128",timestamp:new Date(Date.now()-15*60*1e3)},{id:3,type:"system",severity:"low",message:"缓存清理完成",source:"CacheManager.js:67",timestamp:new Date(Date.now()-30*60*1e3)}]),Y=h([{name:"命中率",value:"87%",status:"good"},{name:"缓存大小",value:"24MB",status:"normal"},{name:"过期项",value:"12",status:"warning"}]),U=h([{type:"API响应",size:8500,color:"#3498db"},{type:"图片资源",size:12300,color:"#2ecc71"},{type:"用户数据",size:3200,color:"#f39c12"},{type:"其他",size:1800,color:"#95a5a6"}]),as=Z(()=>U.reduce((m,d)=>m+d.size,0)),O=h([{id:1,priority:"high",title:"API响应时间优化",description:"检测到/api/spark/generate接口响应时间过长，建议优化算法或增加缓存",impact:"响应时间减少40%"},{id:2,priority:"medium",title:"内存使用优化",description:"当前内存使用率较高，建议清理无用缓存和优化数据结构",impact:"内存使用减少25%"},{id:3,priority:"low",title:"缓存策略优化",description:"缓存命中率可以进一步提升，建议调整缓存策略",impact:"缓存命中率提升15%"}]),ss=m=>({dns:"DNS解析",connect:"建立连接",request:"发送请求",response:"接收响应",render:"页面渲染"})[m]||m,M=m=>m<100?"#2ecc71":m<300?"#f39c12":"#e74c3c",o=m=>m<60?"good":m<80?"warning":"danger",k=m=>({javascript:"JS错误",api:"API错误",network:"网络错误",system:"系统错误"})[m]||m,A=m=>m===0?"good":m<5?"warning":"danger",ts=m=>({javascript:"Warning",api:"Connection",network:"Wifi",system:"Setting"})[m]||"Warning",w=m=>({high:"Warning",medium:"Notification",low:"InfoFilled"})[m]||"InfoFilled",c=m=>m.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),J=()=>{console.log("刷新系统指标"),is()},Q=()=>{console.log("清空缓存"),xs.cacheManager&&xs.cacheManager.clear()},cs=()=>{console.log("优化缓存")},us=m=>{console.log("查看错误详情:",m)},ls=m=>{console.log("应用优化建议:",m.title);const d=O.findIndex(p=>p.id===m.id);d>-1&&O.splice(d,1)},_s=m=>{console.log("忽略优化建议:",m.title);const d=O.findIndex(p=>p.id===m.id);d>-1&&O.splice(d,1)},is=()=>{C.value&&(W.forEach(m=>{m.history.shift();const d=m.history[m.history.length-1]+(Math.random()-.5)*10;m.history.push(Math.max(0,Math.min(m.max,d)));const p=m.history[m.history.length-1];m.name==="响应时间"?m.value=Math.round(p)+"ms":m.name==="CPU使用率"||m.name==="错误率"?m.value=p.toFixed(1)+"%":m.name==="内存使用"&&(m.value=Math.round(p)+"MB")}),X.forEach(m=>{m.responseTime+=(Math.random()-.5)*20,m.responseTime=Math.max(50,Math.min(500,m.responseTime)),m.responseTime=Math.round(m.responseTime)}),S.used+=(Math.random()-.5)*2,S.used=Math.max(40,Math.min(100,S.used)),S.used=Math.round(S.used),S.percentage=Math.round(S.used/S.total*100),S.available=S.total-S.used)};return bs(()=>{if(console.log("系统集成监控组件已加载"),C.value&&(T.value=setInterval(is,3e3)),xs){const m=xs.getSystemStatus();console.log("系统状态:",m)}}),it(()=>{T.value&&clearInterval(T.value)}),(m,d)=>{const p=$("el-icon"),u=$("el-switch"),E=$("el-button"),ns=$("el-tag"),H=$("Bell"),ds=$("el-badge");return a(),i("div",ua,[s("div",_a,[s("div",va,[d[1]||(d[1]=s("h3",null,"iFlytek Spark 系统集成监控",-1)),s("div",{class:L(["system-status",q.level])},[t(p,null,{default:l(()=>[(a(),R(G(q.icon)))]),_:1}),s("span",null,n(q.text),1)],2)]),s("div",ma,[(a(!0),i(g,null,y(W,v=>(a(),i("div",{class:"status-card",key:v.name},[s("div",pa,[s("div",{class:"metric-icon",style:z({backgroundColor:v.color})},[t(p,null,{default:l(()=>[(a(),R(G(v.icon)))]),_:2},1024)],4),s("div",ha,[s("h4",null,n(v.name),1),s("div",{class:L(["metric-value",v.status])},n(v.value),3)])]),s("div",ga,[s("div",ya,[(a(!0),i(g,null,y(v.history,(K,ps)=>(a(),i("div",{key:ps,class:"chart-point",style:z({left:ps/(v.history.length-1)*100+"%",bottom:K/v.max*100+"%"})},null,4))),128))])]),s("div",{class:L(["metric-trend",{positive:v.trend>0,negative:v.trend<0}])},[t(p,null,{default:l(()=>[(a(),R(G(v.trend>0?"ArrowUp":"ArrowDown")))]),_:2},1024),_(" "+n(Math.abs(v.trend))+"% ",1)],2)]))),128))])]),s("div",fa,[s("div",$a,[d[3]||(d[3]=s("h4",null,"性能监控",-1)),s("div",ba,[t(u,{modelValue:C.value,"onUpdate:modelValue":d[0]||(d[0]=v=>C.value=v),"active-text":"实时监控"},null,8,["modelValue"]),t(E,{size:"small",onClick:J},{default:l(()=>[t(p,null,{default:l(()=>[t(f(Es))]),_:1}),d[2]||(d[2]=_(" 刷新数据 "))]),_:1,__:[2]})])]),s("div",ka,[s("div",wa,[d[9]||(d[9]=s("h5",null,"页面加载性能",-1)),s("div",xa,[s("div",Sa,[s("div",{class:"bar-segment dns",style:z({width:V.dns+"%"})},d[4]||(d[4]=[s("span",null,"DNS",-1)]),4),s("div",{class:"bar-segment connect",style:z({width:V.connect+"%"})},d[5]||(d[5]=[s("span",null,"连接",-1)]),4),s("div",{class:"bar-segment request",style:z({width:V.request+"%"})},d[6]||(d[6]=[s("span",null,"请求",-1)]),4),s("div",{class:"bar-segment response",style:z({width:V.response+"%"})},d[7]||(d[7]=[s("span",null,"响应",-1)]),4),s("div",{class:"bar-segment render",style:z({width:V.render+"%"})},d[8]||(d[8]=[s("span",null,"渲染",-1)]),4)]),s("div",Ma,[(a(!0),i(g,null,y(V,(v,K)=>(a(),i("div",{class:"timing-item",key:K},[s("span",Ca,n(ss(K))+":",1),s("span",Ta,n(v)+"ms",1)]))),128))])])]),s("div",Ia,[d[10]||(d[10]=s("h5",null,"API响应时间",-1)),s("div",Va,[s("div",za,[(a(!0),i(g,null,y(X,v=>(a(),i("div",{class:"api-item",key:v.name},[s("div",Aa,[s("span",Da,n(v.name),1),s("span",{class:L(["api-status",v.status])},n(v.statusText),3)]),s("div",ja,[s("div",Fa,[s("div",{class:"timing-fill",style:z({width:v.responseTime/j.value*100+"%",backgroundColor:M(v.responseTime)})},null,4)]),s("span",Pa,n(v.responseTime)+"ms",1)])]))),128))])])]),s("div",Ua,[d[14]||(d[14]=s("h5",null,"内存使用情况",-1)),s("div",Ra,[s("div",Ba,[s("div",{class:"memory-circle",style:z({"--usage":S.percentage+"%"})},[s("div",qa,[s("span",Ea,n(S.used)+"MB",1),s("span",Na,"/ "+n(S.total)+"MB",1)])],4)]),s("div",La,[s("div",Ja,[d[11]||(d[11]=s("span",{class:"memory-label"},"已使用:",-1)),s("span",Qa,n(S.used)+"MB",1)]),s("div",Wa,[d[12]||(d[12]=s("span",{class:"memory-label"},"可用:",-1)),s("span",Xa,n(S.available)+"MB",1)]),s("div",Ya,[d[13]||(d[13]=s("span",{class:"memory-label"},"使用率:",-1)),s("span",{class:L(["memory-value",o(S.percentage)])},n(S.percentage)+"% ",3)])])])])])]),s("div",Oa,[s("div",Ha,[d[15]||(d[15]=s("h4",null,"错误监控",-1)),s("div",Ka,[t(ns,{type:x.level,size:"large"},{default:l(()=>[_(n(x.text),1)]),_:1},8,["type"])])]),s("div",Ga,[s("div",Za,[(a(!0),i(g,null,y(os,(v,K)=>(a(),i("div",{class:"summary-item",key:K},[s("div",si,n(k(K)),1),s("div",{class:L(["error-count",A(v)])},n(v),3)]))),128))]),s("div",ti,[d[17]||(d[17]=s("h5",null,"最近错误",-1)),s("div",ei,[(a(!0),i(g,null,y(I,v=>(a(),i("div",{class:"error-item",key:v.id},[s("div",{class:L(["error-icon",v.severity])},[t(p,null,{default:l(()=>[(a(),R(G(ts(v.type))))]),_:2},1024)],2),s("div",oi,[s("div",li,n(v.message),1),s("div",ni,[s("span",ai,n(c(v.timestamp)),1),s("span",ii,n(v.source),1)])]),s("div",di,[t(E,{size:"small",onClick:K=>us(v)},{default:l(()=>d[16]||(d[16]=[_(" 查看详情 ")])),_:2,__:[16]},1032,["onClick"])])]))),128))])])])]),s("div",ri,[s("div",ci,[d[20]||(d[20]=s("h4",null,"缓存管理",-1)),s("div",ui,[t(E,{size:"small",onClick:Q},{default:l(()=>[t(p,null,{default:l(()=>[t(f(dt))]),_:1}),d[18]||(d[18]=_(" 清空缓存 "))]),_:1,__:[18]}),t(E,{size:"small",onClick:cs},{default:l(()=>[t(p,null,{default:l(()=>[t(f(fs))]),_:1}),d[19]||(d[19]=_(" 优化缓存 "))]),_:1,__:[19]})])]),s("div",_i,[s("div",vi,[(a(!0),i(g,null,y(Y,v=>(a(),i("div",{class:"cache-metric",key:v.name},[s("div",mi,n(v.name),1),s("div",{class:L(["cache-value",v.status])},n(v.value),3)]))),128))]),s("div",pi,[d[21]||(d[21]=s("h5",null,"缓存分布",-1)),s("div",hi,[(a(!0),i(g,null,y(U,v=>(a(),i("div",{key:v.type,class:"distribution-bar"},[s("div",gi,n(v.type),1),s("div",yi,[s("div",{class:"bar-fill",style:z({width:v.size/as.value*100+"%",backgroundColor:v.color})},null,4)]),s("div",fi,n(v.size)+"KB",1)]))),128))])])])]),s("div",$i,[s("div",bi,[d[22]||(d[22]=s("h4",null,"优化建议",-1)),s("div",ki,[t(ds,{value:O.length,type:"warning"},{default:l(()=>[t(p,null,{default:l(()=>[t(H)]),_:1})]),_:1},8,["value"])])]),s("div",wi,[(a(!0),i(g,null,y(O,v=>(a(),i("div",{class:"suggestion-item",key:v.id},[s("div",{class:L(["suggestion-priority",v.priority])},[t(p,null,{default:l(()=>[(a(),R(G(w(v.priority))))]),_:2},1024)],2),s("div",xi,[s("div",Si,n(v.title),1),s("div",Mi,n(v.description),1),s("div",Ci,[d[23]||(d[23]=s("span",{class:"impact-label"},"预期提升:",-1)),s("span",Ti,n(v.impact),1)])]),s("div",Ii,[t(E,{size:"small",type:"primary",onClick:K=>ls(v)},{default:l(()=>d[24]||(d[24]=[_(" 应用建议 ")])),_:2,__:[24]},1032,["onClick"]),t(E,{size:"small",onClick:K=>_s(v)},{default:l(()=>d[25]||(d[25]=[_(" 忽略 ")])),_:2,__:[25]},1032,["onClick"])])]))),128))])])])}}},zi=$s(Vi,[["__scopeId","data-v-1903a421"]]);const Ai={class:"enterprise-dashboard"},Di={class:"enterprise-header"},ji={class:"header-container"},Fi={class:"brand-section"},Pi={class:"brand-logo"},Ui={class:"header-actions"},Ri={class:"enterprise-overview"},Bi={class:"overview-container"},qi={class:"stats-grid"},Ei={class:"stat-card primary"},Ni={class:"stat-header"},Li={class:"stat-content"},Ji={class:"stat-value"},Qi={class:"stat-detail"},Wi={class:"stat-card success"},Xi={class:"stat-header"},Yi={class:"stat-content"},Oi={class:"stat-value"},Hi={class:"stat-detail"},Ki={class:"stat-card warning"},Gi={class:"stat-header"},Zi={class:"stat-content"},sd={class:"stat-value"},td={class:"stat-detail"},ed={class:"stat-card info"},od={class:"stat-header"},ld={class:"stat-content"},nd={class:"stat-value"},ad={class:"stat-detail"},id={class:"enterprise-modules"},dd={class:"modules-container"},rd={class:"module-section"},cd={class:"module-grid"},ud={class:"module-icon batch"},_d={class:"module-content"},vd={class:"module-stats"},md={class:"module-icon position"},pd={class:"module-content"},hd={class:"module-stats"},gd={class:"module-icon pool"},yd={class:"module-content"},fd={class:"module-stats"},$d={class:"module-section status-section"},bd={class:"status-grid"},kd={class:"status-card"},wd={class:"status-icon success"},xd={class:"status-content"},Sd={class:"status-card"},Md={class:"status-icon success"},Cd={class:"status-content"},Td={class:"status-card"},Id={class:"status-icon success"},Vd={class:"status-content"},zd={class:"module-section quick-actions-section"},Ad={class:"quick-actions-grid"},Dd={class:"module-section ai-analytics-section"},jd={class:"analytics-container"},Fd={class:"module-section"},Pd={class:"analytics-grid"},Ud={class:"analytics-card"},Rd={class:"analytics-header"},Bd={class:"analytics-content"},qd={class:"stage-info"},Ed={class:"stage-name"},Nd={class:"stage-count"},Ld={class:"stage-bar"},Jd={class:"analytics-card"},Qd={class:"analytics-header"},Wd={class:"analytics-content"},Xd={class:"skill-distribution"},Yd={class:"skill-info"},Od={class:"skill-name"},Hd={class:"skill-score"},Kd={class:"skill-bar"},Gd={class:"module-section smart-flow-section"},Zd={class:"flow-management-container"},sr={class:"module-section multimodal-hub-section"},tr={class:"multimodal-container"},er={class:"module-section ai-showcase-section"},or={class:"ai-capabilities-grid"},lr={class:"capability-card voice-capability"},nr={class:"capability-header"},ar={class:"capability-icon"},ir={class:"capability-demo"},dr={class:"voice-wave-mini"},rr={class:"capability-metrics"},cr={class:"metric"},ur={class:"metric-value"},_r={class:"metric"},vr={class:"metric-value"},mr={class:"capability-card video-capability"},pr={class:"capability-header"},hr={class:"capability-icon"},gr={class:"capability-demo"},yr={class:"emotion-indicators-mini"},fr={class:"emotion-name"},$r={class:"emotion-progress"},br={class:"capability-card assessment-capability"},kr={class:"capability-header"},wr={class:"capability-icon"},xr={class:"capability-demo"},Sr={class:"assessment-overview"},Mr={class:"assessment-score"},Cr={class:"score-value"},Tr={class:"assessment-dimensions"},Ir={class:"dim-name"},Vr={class:"dim-score"},zr={class:"module-section recommendation-section"},Ar={class:"recommendation-container"},Dr={class:"module-section"},jr={class:"insights-grid"},Fr={class:"insight-card"},Pr={class:"insight-header"},Ur={class:"insight-content"},Rr={class:"insight-action"},Br={class:"insight-card"},qr={class:"insight-header"},Er={class:"insight-content"},Nr={class:"insight-action"},Lr={class:"insight-card"},Jr={class:"insight-header"},Qr={class:"insight-content"},Wr={class:"insight-action"},Xr={class:"module-section professional-features-section"},Yr={class:"professional-container"},Or={class:"module-section system-monitor-section"},Hr={class:"system-monitor-container"},Kr={class:"module-section"},Gr={class:"activity-timeline"},Zr={class:"timeline-content"},sc={class:"activity-title"},tc={class:"activity-description"},ec={class:"activity-time"},oc={__name:"EnterpriseDashboard",setup(ks){const C=rt(),T=h({totalCandidates:2456,newCandidates:186,totalInterviews:1834,todayInterviews:23,avgScore:85.6,scoreImprovement:8.2,passRate:68.5,excellentCandidates:342}),q=h({totalBatches:45,activeBatches:12,completedBatches:33}),W=h({activePositions:28,totalPositions:156,urgentPositions:5}),V=h({totalTalents:8934,activeTalents:2341,newTalents:234}),X=h([{name:"简历投递",count:2456,percentage:100},{name:"初步筛选",count:1834,percentage:74.7},{name:"AI面试",count:1245,percentage:50.7},{name:"人工复试",count:687,percentage:28},{name:"最终录用",count:234,percentage:9.5}]),j=h([{name:"AI算法",avgScore:82.5},{name:"大数据处理",avgScore:78.3},{name:"IoT开发",avgScore:75.8},{name:"系统架构",avgScore:80.2},{name:"项目管理",avgScore:77.9}]),S=h([{id:1,type:"interview",title:"张三完成AI工程师面试",description:"综合得分85分，建议进入下一轮",timestamp:new Date(Date.now()-1e3*60*30)},{id:2,type:"batch",title:"批量面试任务创建成功",description:"为大数据开发岗位创建了15场面试",timestamp:new Date(Date.now()-1e3*60*60*2)},{id:3,type:"position",title:"新增IoT架构师职位",description:"职位要求已更新，开始接收简历",timestamp:new Date(Date.now()-1e3*60*60*4)}]),x=h({speechAccuracy:98.5,responseTime:156,overallScore:85,emotions:[{name:"自信",value:85},{name:"专业",value:92},{name:"紧张",value:15},{name:"友好",value:88}],dimensions:[{name:"技术能力",score:88},{name:"沟通表达",score:92},{name:"逻辑思维",score:85},{name:"学习能力",score:90}]}),os=M=>{console.log("🎯 点击批量创建面试按钮"),console.log("🎯 事件对象:",M),console.log("🎯 当前路由:",C.currentRoute.value.path),console.log("🎯 准备跳转到:","/batch-interview-setup"),M&&(M.preventDefault(),M.stopPropagation()),setTimeout(()=>{console.log("🎯 执行路由跳转"),C.push("/batch-interview-setup").then(()=>{console.log("✅ 路由跳转成功")}).catch(o=>{console.error("❌ 路由跳转失败:",o)})},100)},I=M=>{console.log("🎯 点击职位管理按钮"),console.log("🎯 事件对象:",M),console.log("🎯 当前路由:",C.currentRoute.value.path),console.log("🎯 准备跳转到:","/position-management"),M&&(M.preventDefault(),M.stopPropagation()),setTimeout(()=>{console.log("🎯 执行路由跳转"),C.push("/position-management").then(()=>{console.log("✅ 路由跳转成功")}).catch(o=>{console.error("❌ 路由跳转失败:",o)})},100)},Y=M=>{console.log("🎯 点击数据报表按钮"),console.log("🎯 事件对象:",M),console.log("🎯 当前路由:",C.currentRoute.value.path),console.log("🎯 准备跳转到:","/enterprise-reports"),M&&(M.preventDefault(),M.stopPropagation()),setTimeout(()=>{console.log("🎯 执行路由跳转"),C.push("/enterprise-reports").then(()=>{console.log("✅ 路由跳转成功")}).catch(o=>{console.error("❌ 路由跳转失败:",o)})},100)},U=M=>{console.log("🎯 navigateTo 方法调用，跳转到:",M),C.push(M)},as=()=>{console.log("🧪 测试路由功能"),alert("测试路由按钮被点击！现在将跳转到批量面试页面"),C.push("/batch-interview-setup")},O=M=>{C.push(`/reports/${M}`)},ss=M=>{const k=new Date-M,A=Math.floor(k/(1e3*60)),ts=Math.floor(k/(1e3*60*60)),w=Math.floor(k/(1e3*60*60*24));return A<60?`${A}分钟前`:ts<24?`${ts}小时前`:`${w}天前`};return bs(()=>{console.log("企业端管理中心已加载"),setTimeout(()=>{document.querySelectorAll(".voice-wave-mini .wave-bar").forEach((o,k)=>{setInterval(()=>{const A=Math.random()*20+8;o.style.height=A+"px"},200+k*50)}),setInterval(()=>{x.speechAccuracy=98.5+(Math.random()-.5)*.8,x.responseTime=156+Math.floor((Math.random()-.5)*30),x.overallScore=85+Math.floor((Math.random()-.5)*8),x.emotions.forEach(o=>{o.value=Math.max(10,Math.min(95,o.value+(Math.random()-.5)*8))}),x.dimensions.forEach(o=>{o.score=Math.max(70,Math.min(95,o.score+(Math.random()-.5)*4))})},3e3)},1e3)}),(M,o)=>{const k=$("el-icon"),A=$("el-button"),ts=$("Trophy"),w=$("el-tag");return a(),i("div",Ai,[s("div",Di,[s("div",ji,[s("div",Fi,[s("div",Pi,[t(k,{class:"logo-icon"},{default:l(()=>[t(f(fs))]),_:1}),o[6]||(o[6]=s("span",{class:"brand-text"},"iFlytek 企业招聘中心",-1))]),o[7]||(o[7]=s("div",{class:"brand-subtitle"},"AI驱动的智能招聘管理平台",-1))]),s("div",Ui,[t(A,{type:"primary",size:"large",onClick:ms(os,["stop","prevent"])},{default:l(()=>[t(k,null,{default:l(()=>[t(f(Rs))]),_:1}),o[8]||(o[8]=_(" 批量创建面试 "))]),_:1,__:[8]}),t(A,{onClick:ms(I,["stop","prevent"])},{default:l(()=>[t(k,null,{default:l(()=>[t(f(fs))]),_:1}),o[9]||(o[9]=_(" 职位管理 "))]),_:1,__:[9]}),t(A,{onClick:ms(Y,["stop","prevent"])},{default:l(()=>[t(k,null,{default:l(()=>[t(f(Bs))]),_:1}),o[10]||(o[10]=_(" 数据报表 "))]),_:1,__:[10]}),t(A,{type:"danger",onClick:ms(as,["stop"])},{default:l(()=>[t(k,null,{default:l(()=>[t(f(Ds))]),_:1}),o[11]||(o[11]=_(" 测试路由 "))]),_:1,__:[11]})])])]),s("div",Ri,[s("div",Bi,[o[20]||(o[20]=s("div",{class:"overview-title"},[s("h2",null,"招聘数据概览"),s("p",null,"实时掌握企业招聘全局数据")],-1)),s("div",qi,[s("div",Ei,[s("div",Ni,[t(k,{class:"stat-icon"},{default:l(()=>[t(f(qs))]),_:1}),o[12]||(o[12]=s("span",{class:"stat-trend up"},"+12%",-1))]),s("div",Li,[s("div",Ji,n(T.totalCandidates),1),o[13]||(o[13]=s("div",{class:"stat-label"},"候选人总数",-1)),s("div",Qi,"本月新增 "+n(T.newCandidates)+" 人",1)])]),s("div",Wi,[s("div",Xi,[t(k,{class:"stat-icon"},{default:l(()=>[t(f(Cs))]),_:1}),o[14]||(o[14]=s("span",{class:"stat-trend up"},"+8%",-1))]),s("div",Yi,[s("div",Oi,n(T.totalInterviews),1),o[15]||(o[15]=s("div",{class:"stat-label"},"面试总场次",-1)),s("div",Hi,"今日进行 "+n(T.todayInterviews)+" 场",1)])]),s("div",Ki,[s("div",Gi,[t(k,{class:"stat-icon"},{default:l(()=>[t(f(Ms))]),_:1}),o[16]||(o[16]=s("span",{class:"stat-trend up"},"+5%",-1))]),s("div",Zi,[s("div",sd,n(T.avgScore)+"分",1),o[17]||(o[17]=s("div",{class:"stat-label"},"平均面试得分",-1)),s("div",td,"较上月提升 "+n(T.scoreImprovement)+"%",1)])]),s("div",ed,[s("div",od,[t(k,{class:"stat-icon"},{default:l(()=>[t(ts)]),_:1}),o[18]||(o[18]=s("span",{class:"stat-trend up"},"+15%",-1))]),s("div",ld,[s("div",nd,n(T.passRate)+"%",1),o[19]||(o[19]=s("div",{class:"stat-label"},"面试通过率",-1)),s("div",ad,"优秀候选人 "+n(T.excellentCandidates)+" 人",1)])])])])]),s("div",id,[s("div",dd,[s("div",rd,[o[27]||(o[27]=s("div",{class:"module-header"},[s("h3",null,"批量招聘管理"),s("p",null,"高效管理大规模招聘需求")],-1)),s("div",cd,[s("div",{class:"module-card",onClick:o[0]||(o[0]=c=>U("/batch-interview-setup"))},[s("div",ud,[t(k,null,{default:l(()=>[t(f(qs))]),_:1})]),s("div",_d,[o[21]||(o[21]=s("h4",null,"批量面试",-1)),o[22]||(o[22]=s("p",null,"一键创建多场面试，支持同时面试多个候选人",-1)),s("div",vd,[s("span",null,"本月已创建 "+n(q.totalBatches)+" 批次",1)])])]),s("div",{class:"module-card",onClick:o[1]||(o[1]=c=>U("/position-management"))},[s("div",md,[t(k,null,{default:l(()=>[t(f(fs))]),_:1})]),s("div",pd,[o[23]||(o[23]=s("h4",null,"职位管理",-1)),o[24]||(o[24]=s("p",null,"统一管理企业所有招聘职位和要求",-1)),s("div",hd,[s("span",null,"活跃职位 "+n(W.activePositions)+" 个",1)])])]),s("div",{class:"module-card",onClick:o[2]||(o[2]=c=>U("/candidate-pool"))},[s("div",gd,[t(k,null,{default:l(()=>[t(f(js))]),_:1})]),s("div",yd,[o[25]||(o[25]=s("h4",null,"人才库管理",-1)),o[26]||(o[26]=s("p",null,"建立企业专属人才库，长期维护候选人关系",-1)),s("div",fd,[s("span",null,"人才库 "+n(V.totalTalents)+" 人",1)])])])])]),s("div",$d,[o[37]||(o[37]=s("div",{class:"module-header"},[s("h3",null,"iFlytek Spark AI功能状态"),s("p",null,"实时监控各功能模块的AI集成状态")],-1)),s("div",bd,[s("div",kd,[s("div",wd,[t(k,null,{default:l(()=>[t(f(Ss))]),_:1})]),s("div",xd,[o[29]||(o[29]=s("h4",null,"批量面试管理",-1)),o[30]||(o[30]=s("p",null,"AI智能分析已就绪",-1)),t(w,{type:"success",size:"small"},{default:l(()=>o[28]||(o[28]=[_("已集成")])),_:1,__:[28]})])]),s("div",Sd,[s("div",Md,[t(k,null,{default:l(()=>[t(f(Ss))]),_:1})]),s("div",Cd,[o[32]||(o[32]=s("h4",null,"职位管理系统",-1)),o[33]||(o[33]=s("p",null,"AI助手功能正常",-1)),t(w,{type:"success",size:"small"},{default:l(()=>o[31]||(o[31]=[_("已集成")])),_:1,__:[31]})])]),s("div",Td,[s("div",Id,[t(k,null,{default:l(()=>[t(f(Ss))]),_:1})]),s("div",Vd,[o[35]||(o[35]=s("h4",null,"数据报表分析",-1)),o[36]||(o[36]=s("p",null,"AI洞察分析可用",-1)),t(w,{type:"success",size:"small"},{default:l(()=>o[34]||(o[34]=[_("已集成")])),_:1,__:[34]})])])])]),s("div",zd,[o[42]||(o[42]=s("div",{class:"module-header"},[s("h3",null,"快速操作"),s("p",null,"一键访问常用功能，提升工作效率")],-1)),s("div",Ad,[t(A,{type:"primary",size:"large",onClick:ms(os,["stop"]),class:"quick-action-btn"},{default:l(()=>[t(k,null,{default:l(()=>[t(f(Rs))]),_:1}),o[38]||(o[38]=s("span",null,"创建批量面试",-1))]),_:1,__:[38]}),t(A,{type:"success",size:"large",onClick:ms(I,["stop"]),class:"quick-action-btn"},{default:l(()=>[t(k,null,{default:l(()=>[t(f(fs))]),_:1}),o[39]||(o[39]=s("span",null,"管理职位",-1))]),_:1,__:[39]}),t(A,{type:"warning",size:"large",onClick:ms(Y,["stop"]),class:"quick-action-btn"},{default:l(()=>[t(k,null,{default:l(()=>[t(f(Bs))]),_:1}),o[40]||(o[40]=s("span",null,"查看报表",-1))]),_:1,__:[40]}),t(A,{type:"info",size:"large",onClick:o[3]||(o[3]=c=>U("/candidate-pool")),class:"quick-action-btn"},{default:l(()=>[t(k,null,{default:l(()=>[t(f(js))]),_:1}),o[41]||(o[41]=s("span",null,"人才库",-1))]),_:1,__:[41]})])]),s("div",Dd,[o[43]||(o[43]=s("div",{class:"module-header"},[s("h3",null,"AI数据分析与可视化"),s("p",null,"基于iFlytek Spark的深度数据洞察和智能报告")],-1)),s("div",jd,[t(bl)])]),s("div",Fd,[o[48]||(o[48]=s("div",{class:"module-header"},[s("h3",null,"数据分析与报表"),s("p",null,"深度洞察招聘数据，优化招聘策略")],-1)),s("div",Pd,[s("div",Ud,[s("div",Rd,[o[45]||(o[45]=s("h4",null,"招聘漏斗分析",-1)),t(A,{text:"",onClick:o[4]||(o[4]=c=>O("funnel"))},{default:l(()=>o[44]||(o[44]=[_("查看详情")])),_:1,__:[44]})]),s("div",Bd,[(a(!0),i(g,null,y(X,c=>(a(),i("div",{class:"funnel-stage",key:c.name},[s("div",qd,[s("span",Ed,n(c.name),1),s("span",Nd,n(c.count),1)]),s("div",Ld,[s("div",{class:"stage-progress",style:z({width:c.percentage+"%"})},null,4)])]))),128))])]),s("div",Jd,[s("div",Qd,[o[47]||(o[47]=s("h4",null,"技能评估分布",-1)),t(A,{text:"",onClick:o[5]||(o[5]=c=>O("skills"))},{default:l(()=>o[46]||(o[46]=[_("查看详情")])),_:1,__:[46]})]),s("div",Wd,[s("div",Xd,[(a(!0),i(g,null,y(j,c=>(a(),i("div",{class:"skill-item",key:c.name},[s("div",Yd,[s("span",Od,n(c.name),1),s("span",Hd,n(c.avgScore)+"分",1)]),s("div",Kd,[s("div",{class:"skill-progress",style:z({width:c.avgScore/100*100+"%"})},null,4)])]))),128))])])])])]),s("div",Gd,[o[49]||(o[49]=s("div",{class:"module-header"},[s("h3",null,"智能面试流程管理"),s("p",null,"iFlytek Spark驱动的自适应面试流程")],-1)),s("div",Zd,[t(Ie)])]),s("div",sr,[o[50]||(o[50]=s("div",{class:"module-header"},[s("h3",null,"多模态交互监控中心"),s("p",null,"实时监控和优化面试交互体验")],-1)),s("div",tr,[t(ct)])]),s("div",er,[o[57]||(o[57]=s("div",{class:"module-header"},[s("h3",null,"iFlytek Spark AI核心能力"),s("p",null,"展示领先的多模态AI面试技术优势")],-1)),s("div",or,[s("div",lr,[s("div",nr,[s("div",ar,[t(k,null,{default:l(()=>[t(f(Cs))]),_:1})]),o[51]||(o[51]=s("div",{class:"capability-info"},[s("h4",null,"实时语音识别"),s("p",null,"98%+ 准确率，支持多方言")],-1))]),s("div",ir,[s("div",dr,[(a(),i(g,null,y(6,c=>s("div",{class:"wave-bar",key:c})),64))]),s("div",rr,[s("div",cr,[s("span",ur,n(x.speechAccuracy)+"%",1),o[52]||(o[52]=s("span",{class:"metric-label"},"识别准确率",-1))]),s("div",_r,[s("span",vr,n(x.responseTime)+"ms",1),o[53]||(o[53]=s("span",{class:"metric-label"},"响应时间",-1))])])])]),s("div",mr,[s("div",pr,[s("div",hr,[t(k,null,{default:l(()=>[t(f(Cs))]),_:1})]),o[54]||(o[54]=s("div",{class:"capability-info"},[s("h4",null,"视频情绪分析"),s("p",null,"多维度情绪状态识别")],-1))]),s("div",gr,[s("div",yr,[(a(!0),i(g,null,y(x.emotions,c=>(a(),i("div",{class:"emotion-item",key:c.name},[s("span",fr,n(c.name),1),s("div",$r,[s("div",{class:"emotion-fill",style:z({width:c.value+"%"})},null,4)])]))),128))])])]),s("div",br,[s("div",kr,[s("div",wr,[t(k,null,{default:l(()=>[t(f(Ms))]),_:1})]),o[55]||(o[55]=s("div",{class:"capability-info"},[s("h4",null,"智能能力评估"),s("p",null,"12维度全方位分析")],-1))]),s("div",xr,[s("div",Sr,[s("div",Mr,[s("span",Cr,n(x.overallScore),1),o[56]||(o[56]=s("span",{class:"score-label"},"综合评分",-1))]),s("div",Tr,[(a(!0),i(g,null,y(x.dimensions,c=>(a(),i("div",{class:"dimension",key:c.name},[s("span",Ir,n(c.name),1),s("span",Vr,n(c.score),1)]))),128))])])])])])]),s("div",zr,[o[58]||(o[58]=s("div",{class:"module-header"},[s("h3",null,"个性化智能推荐系统"),s("p",null,"基于iFlytek Spark的智能候选人和策略推荐")],-1)),s("div",Ar,[t(ut,{"user-type":"enterprise"})])]),s("div",Dr,[o[68]||(o[68]=s("div",{class:"module-header"},[s("h3",null,"AI智能洞察"),s("p",null,"基于iFlytek Spark的智能分析与建议")],-1)),s("div",jr,[s("div",Fr,[s("div",Pr,[t(k,{class:"insight-icon"},{default:l(()=>[t(f(Ms))]),_:1}),o[59]||(o[59]=s("h4",null,"招聘趋势预测",-1))]),s("div",Ur,[o[61]||(o[61]=s("p",null,"基于历史数据分析，预计下月AI领域候选人需求将增长25%",-1)),s("div",Rr,[t(A,{text:"",type:"primary"},{default:l(()=>o[60]||(o[60]=[_("查看详细预测")])),_:1,__:[60]})])])]),s("div",Br,[s("div",qr,[t(k,{class:"insight-icon"},{default:l(()=>[t(f(Ss))]),_:1}),o[62]||(o[62]=s("h4",null,"优质候选人推荐",-1))]),s("div",Er,[o[64]||(o[64]=s("p",null,"系统为您推荐了3位高匹配度的AI工程师候选人",-1)),s("div",Nr,[t(A,{text:"",type:"primary"},{default:l(()=>o[63]||(o[63]=[_("查看推荐")])),_:1,__:[63]})])])]),s("div",Lr,[s("div",Jr,[t(k,{class:"insight-icon"},{default:l(()=>[t(f(Ds))]),_:1}),o[65]||(o[65]=s("h4",null,"面试质量提醒",-1))]),s("div",Qr,[o[67]||(o[67]=s("p",null,"检测到部分面试官评分标准不一致，建议进行标准化培训",-1)),s("div",Wr,[t(A,{text:"",type:"primary"},{default:l(()=>o[66]||(o[66]=[_("查看详情")])),_:1,__:[66]})])])])])]),s("div",Xr,[o[69]||(o[69]=s("div",{class:"module-header"},[s("h3",null,"企业端专业功能"),s("p",null,"批量面试管理、团队协作、人才库运营一体化解决方案")],-1)),s("div",Yr,[t(ca)])]),s("div",Or,[o[70]||(o[70]=s("div",{class:"module-header"},[s("h3",null,"系统集成与性能监控"),s("p",null,"实时监控系统性能，确保最佳用户体验")],-1)),s("div",Hr,[t(zi)])]),s("div",Kr,[o[71]||(o[71]=s("div",{class:"module-header"},[s("h3",null,"最近活动"),s("p",null,"实时跟踪招聘进展")],-1)),s("div",Gr,[(a(!0),i(g,null,y(S,c=>(a(),i("div",{class:"timeline-item",key:c.id},[s("div",{class:L(["timeline-dot",c.type])},null,2),s("div",Zr,[s("div",sc,n(c.title),1),s("div",tc,n(c.description),1),s("div",ec,n(ss(c.timestamp)),1)])]))),128))])])])])])}}},ac=$s(oc,[["__scopeId","data-v-1b3589be"]]);export{ac as default};
