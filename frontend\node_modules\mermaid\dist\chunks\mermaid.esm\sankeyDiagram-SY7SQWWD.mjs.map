{"version": 3, "sources": ["../../../src/diagrams/sankey/parser/sankey.jison", "../../../src/diagrams/sankey/sankeyDB.ts", "../../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/max.js", "../../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/min.js", "../../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/sum.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/align.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/constant.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/sankey.js", "../../../../../node_modules/.pnpm/d3-path@1.0.9/node_modules/d3-path/src/path.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/constant.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/point.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/array.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/link/index.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "../../../src/rendering-util/uid.ts", "../../../src/diagrams/sankey/sankeyRenderer.ts", "../../../src/diagrams/sankey/sankeyUtils.ts", "../../../src/diagrams/sankey/styles.js", "../../../src/diagrams/sankey/sankeyDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,9],$V1=[1,10],$V2=[1,5,10,12];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"SANKEY\":4,\"NEWLINE\":5,\"csv\":6,\"opt_eof\":7,\"record\":8,\"csv_tail\":9,\"EOF\":10,\"field[source]\":11,\"COMMA\":12,\"field[target]\":13,\"field[value]\":14,\"field\":15,\"escaped\":16,\"non_escaped\":17,\"DQUOTE\":18,\"ESCAPED_TEXT\":19,\"NON_ESCAPED_TEXT\":20,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SANKEY\",5:\"NEWLINE\",10:\"EOF\",11:\"field[source]\",12:\"COMMA\",13:\"field[target]\",14:\"field[value]\",18:\"DQUOTE\",19:\"ESCAPED_TEXT\",20:\"NON_ESCAPED_TEXT\"},\nproductions_: [0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 7:\n\n      const source = yy.findOrCreateNode($$[$0-4].trim().replaceAll('\"\"', '\"'));\n      const target = yy.findOrCreateNode($$[$0-2].trim().replaceAll('\"\"', '\"'));\n      const value = parseFloat($$[$0].trim());\n      yy.addLink(source,target,value);\n    \nbreak;\ncase 8: case 9: case 11:\n this.$=$$[$0]; \nbreak;\ncase 10:\n this.$=$$[$0-1]; \nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:$V0,20:$V1},{1:[2,6],7:11,10:[1,12]},o($V1,[2,4],{9:13,5:[1,14]}),{12:[1,15]},o($V2,[2,8]),o($V2,[2,9]),{19:[1,16]},o($V2,[2,11]),{1:[2,1]},{1:[2,5]},o($V1,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:$V0,20:$V1},{15:18,16:7,17:8,18:$V0,20:$V1},{18:[1,19]},o($V1,[2,3]),{12:[1,20]},o($V2,[2,10]),{15:21,16:7,17:8,18:$V0,20:$V1},o([1,5,10],[2,7])],\ndefaultActions: {11:[2,1],12:[2,5]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0: this.pushState('csv'); return 4; \nbreak;\ncase 1: return 10 \nbreak;\ncase 2: return 5 \nbreak;\ncase 3: return 12 \nbreak;\ncase 4: this.pushState('escaped_text'); return 18; \nbreak;\ncase 5: return 20 \nbreak;\ncase 6:this.popState('escaped_text'); return 18; \nbreak;\ncase 7: return 19; \nbreak;\n}\n},\nrules: [/^(?:sankey-beta\\b)/i,/^(?:$)/i,/^(?:((\\u000D\\u000A)|(\\u000A)))/i,/^(?:(\\u002C))/i,/^(?:(\\u0022))/i,/^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i,/^(?:(\\u0022)(?!(\\u0022)))/i,/^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\nconditions: {\"csv\":{\"rules\":[1,2,3,4,5,6,7],\"inclusive\":false},\"escaped_text\":{\"rules\":[6,7],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport common from '../common/common.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  setDiagramTitle,\n  getDiagramTitle,\n  clear as commonClear,\n} from '../common/commonDb.js';\n\n// Sankey diagram represented by nodes and links between those nodes\nlet links: SankeyLink[] = [];\n// Array of nodes guarantees their order\nlet nodes: SankeyNode[] = [];\n// We also have to track nodes uniqueness (by ID)\nlet nodesMap = new Map<string, SankeyNode>();\n\nconst clear = (): void => {\n  links = [];\n  nodes = [];\n  nodesMap = new Map();\n  commonClear();\n};\n\nclass SankeyLink {\n  constructor(\n    public source: SankeyNode,\n    public target: SankeyNode,\n    public value = 0\n  ) {}\n}\n\n/**\n * @param source - Node where the link starts\n * @param target - Node where the link ends\n * @param value - Describes the amount to be passed\n */\nconst addLink = (source: SankeyN<PERSON>, target: SankeyNode, value: number): void => {\n  links.push(new SankeyLink(source, target, value));\n};\n\nclass SankeyNode {\n  constructor(public ID: string) {}\n}\n\nconst findOrCreateNode = (ID: string): SankeyNode => {\n  ID = common.sanitizeText(ID, getConfig());\n\n  let node = nodesMap.get(ID);\n  if (node === undefined) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n};\n\nconst getNodes = () => nodes;\nconst getLinks = () => links;\n\nconst getGraph = () => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value,\n  })),\n});\n\nexport default {\n  nodesMap,\n  getConfig: () => getConfig().sankey,\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear,\n};\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "export var slice = Array.prototype.slice;\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "export class Uid {\n  private static count = 0;\n  id: string;\n  href: string;\n\n  public static next(name: string): Uid {\n    return new Uid(name + ++Uid.count);\n  }\n\n  constructor(id: string) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n\n  toString(): string {\n    return 'url(' + this.href + ')';\n  }\n}\n", "import type { Diagram } from '../../Diagram.js';\nimport { getConfig, defaultConfig } from '../../diagram-api/diagramAPI.js';\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10,\n} from 'd3';\nimport type { SankeyNode as d3SankeyNode } from 'd3-sankey';\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify,\n} from 'd3-sankey';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport { Uid } from '../../rendering-util/uid.js';\nimport type { SankeyNodeAlignment } from '../../config.type.js';\n\n// Map config options to alignment functions\nconst alignmentsMap: Record<\n  SankeyNodeAlignment,\n  (node: d3SankeyNode<object, object>, n: number) => number\n> = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify,\n};\n\n/**\n * Draws Sankey diagram.\n *\n * @param text - The text of the diagram\n * @param id - The id of the diagram which will be used as a DOM element id¨\n * @param _version - Mermaid version from package.json\n * @param diagObj - A standard diagram containing the db and the text and type etc of the diagram\n */\nexport const draw = function (text: string, id: string, _version: string, diagObj: Diagram): void {\n  // Get Sankey config\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey!;\n\n  // TODO:\n  // This code repeats for every diagram\n  // Figure out what is happening there, probably it should be separated\n  // The main thing is svg object that is a d3 wrapper for svg operations\n  //\n  let sandboxElement: any;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = d3select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? d3select(sandboxElement.nodes()[0].contentDocument.body)\n      : d3select('body');\n  // @ts-ignore TODO root.select is not callable\n  const svg = securityLevel === 'sandbox' ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n\n  // Establish svg dimensions and get width and height\n  //\n  const width = conf?.width ?? defaultSankeyConfig.width!;\n  const height = conf?.height ?? defaultSankeyConfig.width!;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth!;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment!;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix!;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix!;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues!;\n\n  // Prepare data for construction based on diagObj.db\n  // This must be a mutable object with `nodes` and `links` properties:\n  //\n  //    {\n  //      \"nodes\": [ { \"id\": \"Alice\" }, { \"id\": \"Bob\" }, { \"id\": \"Carol\" } ],\n  //      \"links\": [ { \"source\": \"Alice\", \"target\": \"Bob\", \"value\": 23 }, { \"source\": \"Bob\", \"target\": \"Carol\", \"value\": 43 } ]\n  //    }\n  //\n  // @ts-ignore TODO: db should be coerced to sankey DB type\n  const graph = diagObj.db.getGraph();\n\n  // Get alignment function\n  const nodeAlign = alignmentsMap[nodeAlignment];\n\n  // Construct and configure a Sankey generator\n  // That will be a function that calculates nodes and links dimensions\n  //\n  const nodeWidth = 10;\n  const sankey = d3Sankey()\n    .nodeId((d: any) => d.id) // we use 'id' property to identify node\n    .nodeWidth(nodeWidth)\n    .nodePadding(10 + (showValues ? 15 : 0))\n    .nodeAlign(nodeAlign)\n    .extent([\n      [0, 0],\n      [width, height],\n    ]);\n\n  // Compute the Sankey layout: calculate nodes and links positions\n  // Our `graph` object will be mutated by this and enriched with other properties\n  //\n  sankey(graph);\n\n  // Get color scheme for the graph\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n\n  // Create rectangles for nodes\n  svg\n    .append('g')\n    .attr('class', 'nodes')\n    .selectAll('.node')\n    .data(graph.nodes)\n    .join('g')\n    .attr('class', 'node')\n    .attr('id', (d: any) => (d.uid = Uid.next('node-')).id)\n    .attr('transform', function (d: any) {\n      return 'translate(' + d.x0 + ',' + d.y0 + ')';\n    })\n    .attr('x', (d: any) => d.x0)\n    .attr('y', (d: any) => d.y0)\n    .append('rect')\n    .attr('height', (d: any) => {\n      return d.y1 - d.y0;\n    })\n    .attr('width', (d: any) => d.x1 - d.x0)\n    .attr('fill', (d: any) => colorScheme(d.id));\n\n  const getText = ({ id, value }: { id: string; value: number }) => {\n    if (!showValues) {\n      return id;\n    }\n    return `${id}\\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  };\n\n  // Create labels for nodes\n  svg\n    .append('g')\n    .attr('class', 'node-labels')\n    .attr('font-size', 14)\n    .selectAll('text')\n    .data(graph.nodes)\n    .join('text')\n    .attr('x', (d: any) => (d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6))\n    .attr('y', (d: any) => (d.y1 + d.y0) / 2)\n    .attr('dy', `${showValues ? '0' : '0.35'}em`)\n    .attr('text-anchor', (d: any) => (d.x0 < width / 2 ? 'start' : 'end'))\n    .text(getText);\n\n  // Creates the paths that represent the links.\n  const link = svg\n    .append('g')\n    .attr('class', 'links')\n    .attr('fill', 'none')\n    .attr('stroke-opacity', 0.5)\n    .selectAll('.link')\n    .data(graph.links)\n    .join('g')\n    .attr('class', 'link')\n    .style('mix-blend-mode', 'multiply');\n\n  const linkColor = conf?.linkColor ?? 'gradient';\n\n  if (linkColor === 'gradient') {\n    const gradient = link\n      .append('linearGradient')\n      .attr('id', (d: any) => (d.uid = Uid.next('linearGradient-')).id)\n      .attr('gradientUnits', 'userSpaceOnUse')\n      .attr('x1', (d: any) => d.source.x1)\n      .attr('x2', (d: any) => d.target.x0);\n\n    gradient\n      .append('stop')\n      .attr('offset', '0%')\n      .attr('stop-color', (d: any) => colorScheme(d.source.id));\n\n    gradient\n      .append('stop')\n      .attr('offset', '100%')\n      .attr('stop-color', (d: any) => colorScheme(d.target.id));\n  }\n\n  let coloring: any;\n  switch (linkColor) {\n    case 'gradient':\n      coloring = (d: any) => d.uid;\n      break;\n    case 'source':\n      coloring = (d: any) => colorScheme(d.source.id);\n      break;\n    case 'target':\n      coloring = (d: any) => colorScheme(d.target.id);\n      break;\n    default:\n      coloring = linkColor;\n  }\n\n  link\n    .append('path')\n    .attr('d', d3SankeyLinkHorizontal())\n    .attr('stroke', coloring)\n    .attr('stroke-width', (d: any) => Math.max(1, d.width));\n\n  setupGraphViewbox(undefined, svg, 0, useMaxWidth);\n};\n\nexport default {\n  draw,\n};\n", "export const prepareTextForParsing = (text: string): string => {\n  const textToParse = text\n    .replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, '') // remove all trailing spaces for each row\n    .replaceAll(/([\\n\\r])+/g, '\\n') // remove empty lines duplicated\n    .trim();\n\n  return textToParse;\n};\n", "const getStyles = (options) =>\n  `.label {\n      font-family: ${options.fontFamily};\n    }`;\n\nexport default getStyles;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: jison doesn't export types\nimport parser from './parser/sankey.jison';\nimport db from './sankeyDB.js';\nimport renderer from './sankeyRenderer.js';\nimport { prepareTextForParsing } from './sankeyUtils.js';\nimport sankeyStyles from './styles.js';\n\nconst originalParse = parser.parse.bind(parser);\nparser.parse = (text: string) => originalParse(prepareTextForParsing(text));\n\nexport const diagram: DiagramDefinition = {\n  styles: sankeyStyles,\n  parser,\n  db,\n  renderer,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,EAAE;AAC3G,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,UAAS,GAAE,WAAU,GAAE,OAAM,GAAE,WAAU,GAAE,UAAS,GAAE,YAAW,GAAE,OAAM,IAAG,iBAAgB,IAAG,SAAQ,IAAG,iBAAgB,IAAG,gBAAe,IAAG,SAAQ,IAAG,WAAU,IAAG,eAAc,IAAG,UAAS,IAAG,gBAAe,IAAG,oBAAmB,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IAC/R,YAAY,EAAC,GAAE,SAAQ,GAAE,UAAS,GAAE,WAAU,IAAG,OAAM,IAAG,iBAAgB,IAAG,SAAQ,IAAG,iBAAgB,IAAG,gBAAe,IAAG,UAAS,IAAG,gBAAe,IAAG,mBAAkB;AAAA,IAC7K,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACtF,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AAEC,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAG,CAAC,EAAE,KAAK,EAAE,WAAW,MAAM,GAAG,CAAC;AACxE,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAG,CAAC,EAAE,KAAK,EAAE,WAAW,MAAM,GAAG,CAAC;AACxE,gBAAMC,SAAQ,WAAW,GAAG,EAAE,EAAE,KAAK,CAAC;AACtC,aAAG,QAAQ,QAAO,QAAOA,MAAK;AAEpC;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,KAAK;AACpB,eAAK,IAAE,GAAG,EAAE;AACb;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,KAAG,CAAC;AACf;AAAA,MACA;AAAA,IACA,GApBe;AAAA,IAqBf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,IAAG,GAAE,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC;AAAA,IAClZ,gBAAgB,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC;AAAA,IAClC,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAED,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAG,iBAAK,UAAU,KAAK;AAAG,mBAAO;AACtC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,iBAAK,UAAU,cAAc;AAAG,mBAAO;AAC/C;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAE,iBAAK,SAAS,cAAc;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GApBe;AAAA,MAqBf,OAAO,CAAC,uBAAsB,WAAU,mCAAkC,kBAAiB,kBAAiB,sDAAqD,8BAA6B,kGAAkG;AAAA,MAChS,YAAY,EAAC,OAAM,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,gBAAe,EAAC,SAAQ,CAAC,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,aAAY,KAAI,EAAC;AAAA,IACtK;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAF,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,iBAAQ;;;ACvlBhB,IAAI,QAAsB,CAAC;AAE3B,IAAI,QAAsB,CAAC;AAE3B,IAAI,WAAW,oBAAI,IAAwB;AAE3C,IAAMG,SAAQ,6BAAY;AACxB,UAAQ,CAAC;AACT,UAAQ,CAAC;AACT,aAAW,oBAAI,IAAI;AACnB,QAAY;AACd,GALc;AAOd,IAAM,aAAN,MAAiB;AAAA,EACf,YACS,QACA,QACAC,SAAQ,GACf;AAHO;AACA;AACA,iBAAAA;AAAA,EACN;AAAA,EA/BL,OA0BiB;AAAA;AAAA;AAMjB;AAOA,IAAM,UAAU,wBAAC,QAAoB,QAAoBA,WAAwB;AAC/E,QAAM,KAAK,IAAI,WAAW,QAAQ,QAAQA,MAAK,CAAC;AAClD,GAFgB;AAIhB,IAAM,aAAN,MAAiB;AAAA,EACf,YAAmB,IAAY;AAAZ;AAAA,EAAa;AAAA,EA5ClC,OA2CiB;AAAA;AAAA;AAEjB;AAEA,IAAM,mBAAmB,wBAAC,OAA2B;AACnD,OAAK,eAAO,aAAa,IAAI,UAAU,CAAC;AAExC,MAAI,OAAO,SAAS,IAAI,EAAE;AAC1B,MAAI,SAAS,QAAW;AACtB,WAAO,IAAI,WAAW,EAAE;AACxB,aAAS,IAAI,IAAI,IAAI;AACrB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT,GAVyB;AAYzB,IAAM,WAAW,6BAAM,OAAN;AACjB,IAAM,WAAW,6BAAM,OAAN;AAEjB,IAAM,WAAW,8BAAO;AAAA,EACtB,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,GAAG,EAAE;AAAA,EAC5C,OAAO,MAAM,IAAI,CAACC,WAAU;AAAA,IAC1B,QAAQA,MAAK,OAAO;AAAA,IACpB,QAAQA,MAAK,OAAO;AAAA,IACpB,OAAOA,MAAK;AAAA,EACd,EAAE;AACJ,IAPiB;AASjB,IAAO,mBAAQ;AAAA,EACb;AAAA,EACA,WAAW,6BAAM,UAAU,EAAE,QAAlB;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAF;AACF;;;ACtFe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIG;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWC,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SACLD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,QAAQ;AACZ,aAASA,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAE,OAAO,MAAM,MAAM,SACzCD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;AAnBwB;;;ACAT,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIE;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWC,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SACLD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,QAAQ;AACZ,aAASA,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAE,OAAO,MAAM,MAAM,SACzCD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;AAnBwB;;;ACAT,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIE,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAASC,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAACA,QAAO;AAClB,QAAAD,QAAOC;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,QAAQ;AACZ,aAASA,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAAC,QAAQA,QAAO,EAAE,OAAO,MAAM,GAAG;AAC5C,QAAAD,QAAOC;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;AAjBwB;;;ACExB,SAAS,YAAY,GAAG;AACtB,SAAO,EAAE,OAAO;AAClB;AAFS;AAIF,SAAS,KAAK,MAAM;AACzB,SAAO,KAAK;AACd;AAFgB;AAIT,SAAS,MAAM,MAAM,GAAG;AAC7B,SAAO,IAAI,IAAI,KAAK;AACtB;AAFgB;AAIT,SAAS,QAAQ,MAAM,GAAG;AAC/B,SAAO,KAAK,YAAY,SAAS,KAAK,QAAQ,IAAI;AACpD;AAFgB;AAIT,SAAS,OAAO,MAAM;AAC3B,SAAO,KAAK,YAAY,SAAS,KAAK,QAChC,KAAK,YAAY,SAAS,IAAI,KAAK,aAAa,WAAW,IAAI,IAC/D;AACR;AAJgB;;;AClBD,SAAR,SAA0BE,IAAG;AAClC,SAAO,WAAW;AAChB,WAAOA;AAAA,EACT;AACF;AAJwB;;;ACIxB,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,iBAAiB,EAAE,QAAQ,EAAE,MAAM,KAAK,EAAE,QAAQ,EAAE;AAC7D;AAFS;AAIT,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,iBAAiB,EAAE,QAAQ,EAAE,MAAM,KAAK,EAAE,QAAQ,EAAE;AAC7D;AAFS;AAIT,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,EAAE,KAAK,EAAE;AAClB;AAFS;AAIT,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE;AACX;AAFS;AAIT,SAAS,UAAU,GAAG;AACpB,SAAO,EAAE;AACX;AAFS;AAIT,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AAFS;AAIT,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AAFS;AAIT,SAAS,KAAK,UAAU,IAAI;AAC1B,QAAM,OAAO,SAAS,IAAI,EAAE;AAC5B,MAAI,CAAC,KAAM,OAAM,IAAI,MAAM,cAAc,EAAE;AAC3C,SAAO;AACT;AAJS;AAMT,SAAS,oBAAoB,EAAC,OAAAC,OAAK,GAAG;AACpC,aAAW,QAAQA,QAAO;AACxB,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,eAAWC,SAAQ,KAAK,aAAa;AACnC,MAAAA,MAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;AAAA,IACb;AACA,eAAWA,SAAQ,KAAK,aAAa;AACnC,MAAAA,MAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;AAAA,IACb;AAAA,EACF;AACF;AAbS;AAeM,SAAR,SAA0B;AAC/B,MAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACjC,MAAI,KAAK;AACT,MAAI,KAAK,GAAG;AACZ,MAAI,KAAK;AACT,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAAID,SAAQ;AACZ,MAAIE,SAAQ;AACZ,MAAI,aAAa;AAEjB,WAAS,SAAS;AAChB,UAAM,QAAQ,EAAC,OAAOF,OAAM,MAAM,MAAM,SAAS,GAAG,OAAOE,OAAM,MAAM,MAAM,SAAS,EAAC;AACvF,qBAAiB,KAAK;AACtB,sBAAkB,KAAK;AACvB,sBAAkB,KAAK;AACvB,uBAAmB,KAAK;AACxB,wBAAoB,KAAK;AACzB,wBAAoB,KAAK;AACzB,WAAO;AAAA,EACT;AATS;AAWT,SAAO,SAAS,SAAS,OAAO;AAC9B,wBAAoB,KAAK;AACzB,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,EACvF;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,EAC1F;AAEA,SAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,OAAO,GAAG,UAAU;AAAA,EACjD;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,KAAK,CAAC,GAAG,UAAU;AAAA,EAChD;AAEA,SAAO,cAAc,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,KAAK,KAAK,CAAC,GAAG,UAAU;AAAA,EACrD;AAEA,SAAO,QAAQ,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUF,SAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAUA;AAAA,EAC1F;AAEA,SAAO,QAAQ,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUE,SAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAUA;AAAA,EAC1F;AAEA,SAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,WAAW,GAAG,UAAU;AAAA,EACrD;AAEA,SAAO,OAAO,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;AAAA,EAC7F;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAAA,EACtH;AAEA,SAAO,aAAa,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,UAAU;AAAA,EACxD;AAEA,WAAS,iBAAiB,EAAC,OAAAF,QAAO,OAAAE,OAAK,GAAG;AACxC,eAAW,CAAC,GAAG,IAAI,KAAKF,OAAM,QAAQ,GAAG;AACvC,WAAK,QAAQ;AACb,WAAK,cAAc,CAAC;AACpB,WAAK,cAAc,CAAC;AAAA,IACtB;AACA,UAAM,WAAW,IAAI,IAAIA,OAAM,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAGA,MAAK,GAAG,CAAC,CAAC,CAAC;AAClE,eAAW,CAAC,GAAGC,KAAI,KAAKC,OAAM,QAAQ,GAAG;AACvC,MAAAD,MAAK,QAAQ;AACb,UAAI,EAAC,QAAQ,OAAM,IAAIA;AACvB,UAAI,OAAO,WAAW,SAAU,UAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,UAAI,OAAO,WAAW,SAAU,UAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,aAAO,YAAY,KAAKA,KAAI;AAC5B,aAAO,YAAY,KAAKA,KAAI;AAAA,IAC9B;AACA,QAAI,YAAY,MAAM;AACpB,iBAAW,EAAC,aAAa,YAAW,KAAKD,QAAO;AAC9C,oBAAY,KAAK,QAAQ;AACzB,oBAAY,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AArBS;AAuBT,WAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,eAAW,QAAQA,QAAO;AACxB,WAAK,QAAQ,KAAK,eAAe,SAC3B,KAAK,IAAI,IAAI,KAAK,aAAa,KAAK,GAAG,IAAI,KAAK,aAAa,KAAK,CAAC,IACnE,KAAK;AAAA,IACb;AAAA,EACF;AANS;AAQT,WAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI;AACf,QAAIG,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,QAAQA;AACb,mBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AACA,UAAI,EAAEA,KAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI;AAAA,IACb;AAAA,EACF;AAhBS;AAkBT,WAAS,mBAAmB,EAAC,OAAAH,OAAK,GAAG;AACnC,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI;AACf,QAAIG,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,SAASA;AACd,mBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AACA,UAAI,EAAEA,KAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI;AAAA,IACb;AAAA,EACF;AAhBS;AAkBT,WAAS,kBAAkB,EAAC,OAAAH,OAAK,GAAG;AAClC,UAAMG,KAAI,IAAIH,QAAO,OAAK,EAAE,KAAK,IAAI;AACrC,UAAM,MAAM,KAAK,KAAK,OAAOG,KAAI;AACjC,UAAM,UAAU,IAAI,MAAMA,EAAC;AAC3B,eAAW,QAAQH,QAAO;AACxB,YAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAIG,KAAI,GAAG,KAAK,MAAM,MAAM,KAAK,MAAM,MAAMA,EAAC,CAAC,CAAC,CAAC;AAC5E,WAAK,QAAQ;AACb,WAAK,KAAK,KAAK,IAAI;AACnB,WAAK,KAAK,KAAK,KAAK;AACpB,UAAI,QAAQ,CAAC,EAAG,SAAQ,CAAC,EAAE,KAAK,IAAI;AAAA,UAC/B,SAAQ,CAAC,IAAI,CAAC,IAAI;AAAA,IACzB;AACA,QAAI,KAAM,YAAW,UAAU,SAAS;AACtC,aAAO,KAAK,IAAI;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAhBS;AAkBT,WAAS,uBAAuB,SAAS;AACvC,UAAM,KAAK,IAAI,SAAS,QAAM,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,CAAC;AAC5E,eAAWH,UAAS,SAAS;AAC3B,UAAII,KAAI;AACR,iBAAW,QAAQJ,QAAO;AACxB,aAAK,KAAKI;AACV,aAAK,KAAKA,KAAI,KAAK,QAAQ;AAC3B,QAAAA,KAAI,KAAK,KAAK;AACd,mBAAWH,SAAQ,KAAK,aAAa;AACnC,UAAAA,MAAK,QAAQA,MAAK,QAAQ;AAAA,QAC5B;AAAA,MACF;AACA,MAAAG,MAAK,KAAKA,KAAI,OAAOJ,OAAM,SAAS;AACpC,eAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,GAAG;AACrC,cAAM,OAAOA,OAAM,CAAC;AACpB,aAAK,MAAMI,MAAK,IAAI;AACpB,aAAK,MAAMA,MAAK,IAAI;AAAA,MACtB;AACA,mBAAaJ,MAAK;AAAA,IACpB;AAAA,EACF;AApBS;AAsBT,WAAS,oBAAoB,OAAO;AAClC,UAAM,UAAU,kBAAkB,KAAK;AACvC,SAAK,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,SAAS,OAAK,EAAE,MAAM,IAAI,EAAE;AAC/D,2BAAuB,OAAO;AAC9B,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,YAAM,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC9B,YAAM,OAAO,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,UAAU;AACrD,uBAAiB,SAAS,OAAO,IAAI;AACrC,uBAAiB,SAAS,OAAO,IAAI;AAAA,IACvC;AAAA,EACF;AAVS;AAaT,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAII,KAAI;AACR,YAAI,IAAI;AACR,mBAAW,EAAC,QAAQ,OAAAC,OAAK,KAAK,OAAO,aAAa;AAChD,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,UAAAD,MAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;AAAA,QACP;AACA,YAAI,EAAE,IAAI,GAAI;AACd,YAAIE,OAAMF,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAME;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;AAAA,MACzB;AACA,UAAI,SAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;AAAA,IAChC;AAAA,EACF;AApBS;AAuBT,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,QAAQ,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACnD,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAIF,KAAI;AACR,YAAI,IAAI;AACR,mBAAW,EAAC,QAAQ,OAAAC,OAAK,KAAK,OAAO,aAAa;AAChD,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,UAAAD,MAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;AAAA,QACP;AACA,YAAI,EAAE,IAAI,GAAI;AACd,YAAIE,OAAMF,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAME;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;AAAA,MACzB;AACA,UAAI,SAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;AAAA,IAChC;AAAA,EACF;AApBS;AAsBT,WAAS,kBAAkBN,QAAO,OAAO;AACvC,UAAM,IAAIA,OAAM,UAAU;AAC1B,UAAM,UAAUA,OAAM,CAAC;AACvB,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,IAAIA,OAAM,SAAS,GAAG,KAAK;AAC/D,iCAA6BA,QAAO,IAAI,GAAG,KAAK;AAAA,EAClD;AAPS;AAUT,WAAS,6BAA6BA,QAAOI,IAAG,GAAG,OAAO;AACxD,WAAO,IAAIJ,OAAM,QAAQ,EAAE,GAAG;AAC5B,YAAM,OAAOA,OAAM,CAAC;AACpB,YAAMM,OAAMF,KAAI,KAAK,MAAM;AAC3B,UAAIE,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,MAAAF,KAAI,KAAK,KAAK;AAAA,IAChB;AAAA,EACF;AAPS;AAUT,WAAS,6BAA6BJ,QAAOI,IAAG,GAAG,OAAO;AACxD,WAAO,KAAK,GAAG,EAAE,GAAG;AAClB,YAAM,OAAOJ,OAAM,CAAC;AACpB,YAAMM,OAAM,KAAK,KAAKF,MAAK;AAC3B,UAAIE,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,MAAAF,KAAI,KAAK,KAAK;AAAA,IAChB;AAAA,EACF;AAPS;AAST,WAAS,iBAAiB,EAAC,aAAa,YAAW,GAAG;AACpD,QAAI,aAAa,QAAW;AAC1B,iBAAW,EAAC,QAAQ,EAAC,aAAAG,aAAW,EAAC,KAAK,aAAa;AACjD,QAAAA,aAAY,KAAK,sBAAsB;AAAA,MACzC;AACA,iBAAW,EAAC,QAAQ,EAAC,aAAAC,aAAW,EAAC,KAAK,aAAa;AACjD,QAAAA,aAAY,KAAK,sBAAsB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AATS;AAWT,WAAS,aAAaR,QAAO;AAC3B,QAAI,aAAa,QAAW;AAC1B,iBAAW,EAAC,aAAa,YAAW,KAAKA,QAAO;AAC9C,oBAAY,KAAK,sBAAsB;AACvC,oBAAY,KAAK,sBAAsB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAPS;AAUT,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAII,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK,QAAQ;AAAA,IACf;AACA,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK;AAAA,IACP;AACA,WAAOA;AAAA,EACT;AAXS;AAcT,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAIA,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK,QAAQ;AAAA,IACf;AACA,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK;AAAA,IACP;AACA,WAAOA;AAAA,EACT;AAXS;AAaT,SAAO;AACT;AA3TwB;;;ACrDxB,IAAI,KAAK,KAAK;AAAd,IACI,MAAM,IAAI;AADd,IAEI,UAAU;AAFd,IAGI,aAAa,MAAM;AAEvB,SAAS,OAAO;AACd,OAAK,MAAM,KAAK;AAAA,EAChB,KAAK,MAAM,KAAK,MAAM;AACtB,OAAK,IAAI;AACX;AAJS;AAMT,SAAS,OAAO;AACd,SAAO,IAAI;AACb;AAFS;AAIT,KAAK,YAAY,KAAK,YAAY;AAAA,EAChC,aAAa;AAAA,EACb,QAAQ,gCAASK,IAAGC,IAAG;AACrB,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACC;AAAA,EAC7E,GAFQ;AAAA,EAGR,WAAW,kCAAW;AACpB,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF,GALW;AAAA,EAMX,QAAQ,gCAASD,IAAGC,IAAG;AACrB,SAAK,KAAK,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EACvD,GAFQ;AAAA,EAGR,kBAAkB,gCAAS,IAAI,IAAID,IAAGC,IAAG;AACvC,SAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EACnF,GAFkB;AAAA,EAGlB,eAAe,gCAAS,IAAI,IAAI,IAAI,IAAID,IAAGC,IAAG;AAC5C,SAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EAC/G,GAFe;AAAA,EAGf,OAAO,gCAAS,IAAI,IAAI,IAAI,IAAI,GAAG;AACjC,SAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;AAC7C,QAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;AAG9B,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IACtD,WAGS,EAAE,QAAQ,SAAS;AAAA,aAKnB,EAAE,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG;AAC3D,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IACtD,OAGK;AACH,UAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,KAAK,KAAK,GACrB,MAAM,KAAK,KAAK,KAAK,GACrB,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,QAAQ,UAAU,IAAI,MAAM,IAAI,KAAK,CAAC,GAChF,MAAM,IAAI,KACV,MAAM,IAAI;AAGd,UAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS;AAC/B,aAAK,KAAK,OAAO,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM;AAAA,MACvD;AAEA,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAW,EAAE,MAAM,MAAM,MAAM,OAAQ,OAAO,KAAK,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK,MAAM;AAAA,IACxI;AAAA,EACF,GA/CO;AAAA,EAgDP,KAAK,gCAASD,IAAGC,IAAG,GAAG,IAAI,IAAI,KAAK;AAClC,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChC,QAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAKD,KAAI,IACT,KAAKC,KAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;AAG9B,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IAC7B,WAGS,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS;AAC/E,WAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IAC7B;AAGA,QAAI,CAAC,EAAG;AAGR,QAAI,KAAK,EAAG,MAAK,KAAK,MAAM;AAG5B,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAOD,KAAI,MAAM,OAAOC,KAAI,MAAM,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IAC9J,WAGS,KAAK,SAAS;AACrB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,QAAS,EAAE,MAAM,MAAO,MAAM,KAAK,OAAO,KAAK,MAAMD,KAAI,IAAI,KAAK,IAAI,EAAE,KAAK,OAAO,KAAK,MAAMC,KAAI,IAAI,KAAK,IAAI,EAAE;AAAA,IAClJ;AAAA,EACF,GArCK;AAAA,EAsCL,MAAM,gCAASD,IAAGC,IAAG,GAAG,GAAG;AACzB,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACC,MAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK;AAAA,EACzH,GAFM;AAAA,EAGN,UAAU,kCAAW;AACnB,WAAO,KAAK;AAAA,EACd,GAFU;AAGZ;AAEA,IAAO,eAAQ;;;ACjIA,SAAR,iBAAiBC,IAAG;AACzB,SAAO,gCAASC,YAAW;AACzB,WAAOD;AAAA,EACT,GAFO;AAGT;AAJO;;;ACAA,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AAFgB;AAIT,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AAFgB;;;ACJT,IAAI,QAAQ,MAAM,UAAU;;;ACMnC,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAFS;AAIT,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAFS;AAIT,SAAS,KAAK,OAAO;AACnB,MAAI,SAAS,YACT,SAAS,YACTE,KAAI,GACJC,KAAI,GACJ,UAAU;AAEd,WAASC,QAAO;AACd,QAAI,QAAQ,OAAO,MAAM,KAAK,SAAS,GAAG,IAAI,OAAO,MAAM,MAAM,IAAI,GAAG,IAAI,OAAO,MAAM,MAAM,IAAI;AACnG,QAAI,CAAC,QAAS,WAAU,SAAS,aAAK;AACtC,UAAM,SAAS,CAACF,GAAE,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAACC,GAAE,MAAM,MAAM,IAAI,GAAG,CAACD,GAAE,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAACC,GAAE,MAAM,MAAM,IAAI,CAAC;AACnI,QAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,EACpD;AALS,SAAAC,OAAA;AAOT,EAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AAEA,EAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AAEA,EAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUF,KAAI,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAGE,SAAQF;AAAA,EACrF;AAEA,EAAAE,MAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUD,KAAI,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAGC,SAAQD;AAAA,EACrF;AAEA,EAAAC,MAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAIA,SAAQ;AAAA,EACvE;AAEA,SAAOA;AACT;AAnCS;AAqCT,SAAS,gBAAgB,SAAS,IAAI,IAAI,IAAI,IAAI;AAChD,UAAQ,OAAO,IAAI,EAAE;AACrB,UAAQ,cAAc,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAC9D;AAHS;AAmBF,SAAS,iBAAiB;AAC/B,SAAO,KAAK,eAAe;AAC7B;AAFgB;;;ACpEhB,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AAFS;AAIT,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AAFS;AAIM,SAAR,+BAAmB;AACxB,SAAO,eAAe,EACjB,OAAO,gBAAgB,EACvB,OAAO,gBAAgB;AAC9B;AAJO;;;ACVA,IAAM,MAAN,MAAM,KAAI;AAAA,EAAjB,OAAiB;AAAA;AAAA;AAAA,EACf;AAAA,SAAe,QAAQ;AAAA;AAAA,EAIvB,OAAc,KAAK,MAAmB;AACpC,WAAO,IAAI,KAAI,OAAO,EAAE,KAAI,KAAK;AAAA,EACnC;AAAA,EAEA,YAAY,IAAY;AACtB,SAAK,KAAK;AACV,SAAK,OAAO,IAAI,EAAE;AAAA,EACpB;AAAA,EAEA,WAAmB;AACjB,WAAO,SAAS,KAAK,OAAO;AAAA,EAC9B;AACF;;;ACIA,IAAM,gBAGF;AAAA,EACF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAUO,IAAM,OAAO,gCAAU,MAAc,IAAY,UAAkB,SAAwB;AAEhG,QAAM,EAAE,eAAe,QAAQ,KAAK,IAAI,UAAU;AAClD,QAAM,sBAAsB,cAAc;AAO1C,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAS,OAAO,EAAE;AAAA,EACrC;AACA,QAAM,OACJ,kBAAkB,YACd,eAAS,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACvD,eAAS,MAAM;AAErB,QAAM,MAAM,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAI,eAAS,QAAQ,EAAE,IAAI;AAI/F,QAAM,QAAQ,MAAM,SAAS,oBAAoB;AACjD,QAAM,SAAS,MAAM,UAAU,oBAAoB;AACnD,QAAM,cAAc,MAAM,eAAe,oBAAoB;AAC7D,QAAM,gBAAgB,MAAM,iBAAiB,oBAAoB;AACjE,QAAM,SAAS,MAAM,UAAU,oBAAoB;AACnD,QAAM,SAAS,MAAM,UAAU,oBAAoB;AACnD,QAAM,aAAa,MAAM,cAAc,oBAAoB;AAW3D,QAAM,QAAQ,QAAQ,GAAG,SAAS;AAGlC,QAAM,YAAY,cAAc,aAAa;AAK7C,QAAM,YAAY;AAClB,QAAM,SAAS,OAAS,EACrB,OAAO,CAAC,MAAW,EAAE,EAAE,EACvB,UAAU,SAAS,EACnB,YAAY,MAAM,aAAa,KAAK,EAAE,EACtC,UAAU,SAAS,EACnB,OAAO;AAAA,IACN,CAAC,GAAG,CAAC;AAAA,IACL,CAAC,OAAO,MAAM;AAAA,EAChB,CAAC;AAKH,SAAO,KAAK;AAGZ,QAAM,cAAc,QAAe,iBAAiB;AAGpD,MACG,OAAO,GAAG,EACV,KAAK,SAAS,OAAO,EACrB,UAAU,OAAO,EACjB,KAAK,MAAM,KAAK,EAChB,KAAK,GAAG,EACR,KAAK,SAAS,MAAM,EACpB,KAAK,MAAM,CAAC,OAAY,EAAE,MAAM,IAAI,KAAK,OAAO,GAAG,EAAE,EACrD,KAAK,aAAa,SAAU,GAAQ;AACnC,WAAO,eAAe,EAAE,KAAK,MAAM,EAAE,KAAK;AAAA,EAC5C,CAAC,EACA,KAAK,KAAK,CAAC,MAAW,EAAE,EAAE,EAC1B,KAAK,KAAK,CAAC,MAAW,EAAE,EAAE,EAC1B,OAAO,MAAM,EACb,KAAK,UAAU,CAAC,MAAW;AAC1B,WAAO,EAAE,KAAK,EAAE;AAAA,EAClB,CAAC,EACA,KAAK,SAAS,CAAC,MAAW,EAAE,KAAK,EAAE,EAAE,EACrC,KAAK,QAAQ,CAAC,MAAW,YAAY,EAAE,EAAE,CAAC;AAE7C,QAAM,UAAU,wBAAC,EAAE,IAAAC,KAAI,OAAAC,OAAM,MAAqC;AAChE,QAAI,CAAC,YAAY;AACf,aAAOD;AAAA,IACT;AACA,WAAO,GAAGA,GAAE;AAAA,EAAK,MAAM,GAAG,KAAK,MAAMC,SAAQ,GAAG,IAAI,GAAG,GAAG,MAAM;AAAA,EAClE,GALgB;AAQhB,MACG,OAAO,GAAG,EACV,KAAK,SAAS,aAAa,EAC3B,KAAK,aAAa,EAAE,EACpB,UAAU,MAAM,EAChB,KAAK,MAAM,KAAK,EAChB,KAAK,MAAM,EACX,KAAK,KAAK,CAAC,MAAY,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAE,EAC9D,KAAK,KAAK,CAAC,OAAY,EAAE,KAAK,EAAE,MAAM,CAAC,EACvC,KAAK,MAAM,GAAG,aAAa,MAAM,MAAM,IAAI,EAC3C,KAAK,eAAe,CAAC,MAAY,EAAE,KAAK,QAAQ,IAAI,UAAU,KAAM,EACpE,KAAK,OAAO;AAGf,QAAMC,QAAO,IACV,OAAO,GAAG,EACV,KAAK,SAAS,OAAO,EACrB,KAAK,QAAQ,MAAM,EACnB,KAAK,kBAAkB,GAAG,EAC1B,UAAU,OAAO,EACjB,KAAK,MAAM,KAAK,EAChB,KAAK,GAAG,EACR,KAAK,SAAS,MAAM,EACpB,MAAM,kBAAkB,UAAU;AAErC,QAAM,YAAY,MAAM,aAAa;AAErC,MAAI,cAAc,YAAY;AAC5B,UAAM,WAAWA,MACd,OAAO,gBAAgB,EACvB,KAAK,MAAM,CAAC,OAAY,EAAE,MAAM,IAAI,KAAK,iBAAiB,GAAG,EAAE,EAC/D,KAAK,iBAAiB,gBAAgB,EACtC,KAAK,MAAM,CAAC,MAAW,EAAE,OAAO,EAAE,EAClC,KAAK,MAAM,CAAC,MAAW,EAAE,OAAO,EAAE;AAErC,aACG,OAAO,MAAM,EACb,KAAK,UAAU,IAAI,EACnB,KAAK,cAAc,CAAC,MAAW,YAAY,EAAE,OAAO,EAAE,CAAC;AAE1D,aACG,OAAO,MAAM,EACb,KAAK,UAAU,MAAM,EACrB,KAAK,cAAc,CAAC,MAAW,YAAY,EAAE,OAAO,EAAE,CAAC;AAAA,EAC5D;AAEA,MAAI;AACJ,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,iBAAW,wBAAC,MAAW,EAAE,KAAd;AACX;AAAA,IACF,KAAK;AACH,iBAAW,wBAAC,MAAW,YAAY,EAAE,OAAO,EAAE,GAAnC;AACX;AAAA,IACF,KAAK;AACH,iBAAW,wBAAC,MAAW,YAAY,EAAE,OAAO,EAAE,GAAnC;AACX;AAAA,IACF;AACE,iBAAW;AAAA,EACf;AAEA,EAAAA,MACG,OAAO,MAAM,EACb,KAAK,KAAK,6BAAuB,CAAC,EAClC,KAAK,UAAU,QAAQ,EACvB,KAAK,gBAAgB,CAAC,MAAW,KAAK,IAAI,GAAG,EAAE,KAAK,CAAC;AAExD,oBAAkB,QAAW,KAAK,GAAG,WAAW;AAClD,GApKoB;AAsKpB,IAAO,yBAAQ;AAAA,EACb;AACF;;;AC/MO,IAAM,wBAAwB,wBAAC,SAAyB;AAC7D,QAAM,cAAc,KACjB,WAAW,4BAA4B,EAAE,EACzC,WAAW,cAAc,IAAI,EAC7B,KAAK;AAER,SAAO;AACT,GAPqC;;;ACArC,IAAM,YAAY,wBAAC,YACjB;AAAA,qBACmB,QAAQ,UAAU;AAAA,QAFrB;AAKlB,IAAO,iBAAQ;;;ACGf,IAAM,gBAAgB,eAAO,MAAM,KAAK,cAAM;AAC9C,eAAO,QAAQ,CAAC,SAAiB,cAAc,sBAAsB,IAAI,CAAC;AAEnE,IAAM,UAA6B;AAAA,EACxC,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AACF;", "names": ["o", "parser", "value", "lexer", "clear", "value", "link", "max", "value", "min", "value", "sum", "value", "x", "nodes", "link", "links", "x", "y", "value", "dy", "sourceLinks", "targetLinks", "x", "y", "x", "constant", "x", "y", "link", "id", "value", "link"]}