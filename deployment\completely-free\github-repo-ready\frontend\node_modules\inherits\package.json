{"name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "version": "2.0.4", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "main": "./inherits.js", "browser": "./inherits_browser.js", "repository": "git://github.com/isaacs/inherits", "license": "ISC", "scripts": {"test": "tap"}, "devDependencies": {"tap": "^14.2.4"}, "files": ["inherits.js", "inherits_browser.js"]}