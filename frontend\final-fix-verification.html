<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek 最终修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #1890ff 0%, #0066cc 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .success { background: #f6ffed; border-color: #b7eb8f; }
        .error { background: #fff2f0; border-color: #ffccc7; }
        .warning { background: #fffbe6; border-color: #ffe58f; }
        .info { background: #f0f9ff; border-color: #91d5ff; }
        .fix-button {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .fix-button:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0066cc;
        }
        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 iFlytek 最终修复验证</h1>
            <p>检测并修复所有导航问题，确保系统正常运行</p>
        </div>
        
        <div class="content">
            <div class="status-card error">
                <h3>🚨 检测到导航问题</h3>
                <p>系统检测到导航菜单、按钮和产品卡片可能未正确加载。点击下方按钮进行应急修复。</p>
                <button class="fix-button" onclick="runEmergencyFix()">🚨 立即应急修复</button>
                <div id="fix-result" style="margin-top: 15px;"></div>
            </div>

            <div class="status-card info">
                <h3>📊 当前系统状态</h3>
                <div id="system-status">正在检查...</div>
                <button class="test-button" onclick="checkSystemStatus()">🔄 重新检查</button>
            </div>

            <div class="result-grid">
                <div class="status-card">
                    <h4>📋 导航菜单测试</h4>
                    <div id="menu-test">等待测试...</div>
                    <button class="test-button" onclick="testMenuItems()">测试菜单</button>
                </div>
                
                <div class="status-card">
                    <h4>🔘 按钮功能测试</h4>
                    <div id="button-test">等待测试...</div>
                    <button class="test-button" onclick="testButtons()">测试按钮</button>
                </div>
                
                <div class="status-card">
                    <h4>🃏 产品卡片测试</h4>
                    <div id="card-test">等待测试...</div>
                    <button class="test-button" onclick="testProductCards()">测试卡片</button>
                </div>
                
                <div class="status-card">
                    <h4>🛣️ 路由功能测试</h4>
                    <div id="router-test">等待测试...</div>
                    <button class="test-button" onclick="testRouter()">测试路由</button>
                </div>
            </div>

            <div class="status-card success" id="success-message" style="display: none;">
                <h3>🎉 修复完成！</h3>
                <p>所有导航功能已修复完成，现在可以正常使用系统。</p>
                <a href="/" style="display: inline-block; background: #1890ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin-top: 10px;">返回主页测试</a>
            </div>
        </div>
    </div>

    <script>
        // 加载应急修复脚本
        function loadEmergencyScript() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = '/emergency-navigation-fix.js';
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // 运行应急修复
        async function runEmergencyFix() {
            const result = document.getElementById('fix-result');
            result.innerHTML = '<div style="color: #1890ff;">🔄 正在加载修复脚本...</div>';

            try {
                // 如果脚本未加载，先加载
                if (!window.iflytekEmergencyFix) {
                    await loadEmergencyScript();
                }

                result.innerHTML = '<div style="color: #1890ff;">🔧 正在执行应急修复...</div>';

                // 运行应急修复
                const fixReport = window.iflytekEmergencyFix.runEmergencyNavigationFix();

                if (fixReport && fixReport.success) {
                    result.innerHTML = '<div style="color: #52c41a;">✅ 应急修复完成！页面结构已重建。</div>';
                    
                    // 延迟显示成功消息
                    setTimeout(() => {
                        document.getElementById('success-message').style.display = 'block';
                        checkSystemStatus();
                    }, 1000);
                } else {
                    result.innerHTML = '<div style="color: #ff4d4f;">❌ 应急修复失败，请刷新页面重试。</div>';
                }

            } catch (error) {
                result.innerHTML = '<div style="color: #ff4d4f;">❌ 修复脚本加载失败: ' + error.message + '</div>';
            }
        }

        // 检查系统状态
        function checkSystemStatus() {
            const status = document.getElementById('system-status');
            
            const app = document.getElementById('app');
            const vueApp = app && app.__vue_app__;
            const menuItems = document.querySelectorAll('.el-menu-item').length;
            const buttons = document.querySelectorAll('.primary-cta, .secondary-cta').length;
            const cards = document.querySelectorAll('.product-card').length;

            let statusHtml = `
                <div>Vue应用: ${vueApp ? '✅ 正常' : '❌ 异常'}</div>
                <div>导航菜单: ${menuItems} 个 ${menuItems >= 5 ? '✅' : '❌'}</div>
                <div>主要按钮: ${buttons} 个 ${buttons >= 2 ? '✅' : '❌'}</div>
                <div>产品卡片: ${cards} 个 ${cards >= 3 ? '✅' : '❌'}</div>
            `;

            status.innerHTML = statusHtml;
        }

        // 测试菜单项
        function testMenuItems() {
            const result = document.getElementById('menu-test');
            const menuItems = document.querySelectorAll('.el-menu-item');
            
            if (menuItems.length >= 5) {
                result.innerHTML = `<div style="color: #52c41a;">✅ 找到 ${menuItems.length} 个菜单项，功能正常</div>`;
            } else {
                result.innerHTML = `<div style="color: #ff4d4f;">❌ 只找到 ${menuItems.length} 个菜单项，需要修复</div>`;
            }
        }

        // 测试按钮
        function testButtons() {
            const result = document.getElementById('button-test');
            const buttons = document.querySelectorAll('.primary-cta, .secondary-cta');
            
            if (buttons.length >= 2) {
                result.innerHTML = `<div style="color: #52c41a;">✅ 找到 ${buttons.length} 个主要按钮，功能正常</div>`;
            } else {
                result.innerHTML = `<div style="color: #ff4d4f;">❌ 只找到 ${buttons.length} 个主要按钮，需要修复</div>`;
            }
        }

        // 测试产品卡片
        function testProductCards() {
            const result = document.getElementById('card-test');
            const cards = document.querySelectorAll('.product-card');
            
            if (cards.length >= 3) {
                result.innerHTML = `<div style="color: #52c41a;">✅ 找到 ${cards.length} 个产品卡片，功能正常</div>`;
            } else {
                result.innerHTML = `<div style="color: #ff4d4f;">❌ 只找到 ${cards.length} 个产品卡片，需要修复</div>`;
            }
        }

        // 测试路由
        function testRouter() {
            const result = document.getElementById('router-test');
            
            try {
                const originalPath = window.location.pathname;
                window.history.pushState({}, '', '/test-route');
                window.history.pushState({}, '', originalPath);
                result.innerHTML = '<div style="color: #52c41a;">✅ 路由功能正常</div>';
            } catch (error) {
                result.innerHTML = '<div style="color: #ff4d4f;">❌ 路由功能异常: ' + error.message + '</div>';
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
