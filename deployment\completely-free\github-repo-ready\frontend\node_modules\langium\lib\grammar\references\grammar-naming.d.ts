/******************************************************************************
* Copyright 2022 TypeFox GmbH
* This program and the accompanying materials are made available under the
* terms of the MIT License, which is available in the project root.
******************************************************************************/
import type { AstNode, CstNode } from '../../syntax-tree.js';
import { DefaultNameProvider } from '../../references/name-provider.js';
export declare class LangiumGrammarNameProvider extends DefaultNameProvider {
    getName(node: AstNode): string | undefined;
    getNameNode(node: AstNode): CstNode | undefined;
}
//# sourceMappingURL=grammar-naming.d.ts.map