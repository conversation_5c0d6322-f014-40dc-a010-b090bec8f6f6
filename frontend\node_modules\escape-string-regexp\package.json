{"name": "escape-string-regexp", "version": "5.0.0", "description": "Escape RegExp special characters", "license": "MIT", "repository": "sindresorhus/escape-string-regexp", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["escape", "regex", "regexp", "regular", "expression", "string", "special", "characters"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}