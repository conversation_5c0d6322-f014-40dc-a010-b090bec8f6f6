{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/row/src/constants.ts"], "sourcesContent": ["import type { ComputedRef, InjectionKey } from 'vue'\n\ninterface RowContext {\n  gutter: ComputedRef<number>\n}\n\nexport const rowContextKey: InjectionKey<RowContext> = Symbol('rowContextKey')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,aAAa,GAAG,MAAM,CAAC,eAAe;;;;"}