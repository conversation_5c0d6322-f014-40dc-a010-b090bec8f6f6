#!/usr/bin/env node

/**
 * 🎨 创建演示图片用于测试
 * Create Demo Images for Testing
 * 
 * 创建模拟的界面图片文件用于测试质量验证系统
 * Create simulated interface image files for testing the quality validation system
 */

import fs from 'fs';
import path from 'path';

console.log('🎨 创建演示图片用于测试质量验证系统');
console.log('Creating Demo Images for Quality Validation Testing\n');

// 模拟图片数据 (1x1像素的PNG)
const DEMO_PNG_DATA = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x04, 0x38,
    0x08, 0x06, 0x00, 0x00, 0x00, 0xCC, 0x45, 0x4D, 0x7B, 0x00, 0x00, 0x00,
    0x0B, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
]);

// 需要创建的演示图片
const DEMO_IMAGES = [
    'interface-complete-system.png',
    'interface-ai-architecture.png',
    'interface-case-analysis.png',
    'interface-bigdata-analysis.png',
    'interface-iot-systems.png'
];

// 创建演示图片
function createDemoImages() {
    const imageDir = './generated-images/';
    
    // 确保目录存在
    if (!fs.existsSync(imageDir)) {
        fs.mkdirSync(imageDir, { recursive: true });
        console.log(`📁 创建目录: ${imageDir}`);
    }
    
    console.log('🎨 创建演示图片文件...\n');
    
    DEMO_IMAGES.forEach((filename, index) => {
        const filePath = path.join(imageDir, filename);
        
        // 创建不同大小的文件来模拟不同质量
        const fileSize = 150 * 1024 + (index * 50 * 1024); // 150KB - 350KB
        const demoData = Buffer.alloc(fileSize, DEMO_PNG_DATA);
        
        fs.writeFileSync(filePath, demoData);
        
        const sizeKB = Math.round(fileSize / 1024);
        console.log(`✅ 创建: ${filename} (${sizeKB} KB)`);
    });
    
    console.log(`\n🎉 成功创建 ${DEMO_IMAGES.length} 个演示图片文件`);
    console.log('📊 现在可以运行质量验证测试');
}

// 清理演示图片
function cleanupDemoImages() {
    const imageDir = './generated-images/';
    
    console.log('🧹 清理演示图片文件...\n');
    
    DEMO_IMAGES.forEach(filename => {
        const filePath = path.join(imageDir, filename);
        
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`🗑️  删除: ${filename}`);
        }
    });
    
    console.log('\n✅ 演示图片清理完成');
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--cleanup')) {
    cleanupDemoImages();
} else {
    createDemoImages();
}

export { createDemoImages, cleanupDemoImages, DEMO_IMAGES };
