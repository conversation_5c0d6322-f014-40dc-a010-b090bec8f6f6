#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function validateCSS(filePath) {
    console.log(`🔍 检查CSS语法: ${filePath}`);
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 提取<style>标签内的CSS
    const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/);
    if (!styleMatch) {
        console.log('❌ 未找到<style>标签');
        return;
    }
    
    const cssContent = styleMatch[1];
    const lines = cssContent.split('\n');
    
    let braceStack = [];
    let errors = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNum = i + 1;
        
        // 计算大括号
        for (let j = 0; j < line.length; j++) {
            const char = line[j];
            if (char === '{') {
                braceStack.push({ line: lineNum, char: j });
            } else if (char === '}') {
                if (braceStack.length === 0) {
                    errors.push(`第${lineNum}行第${j+1}列: 多余的 '}' 字符`);
                } else {
                    braceStack.pop();
                }
            }
        }
        
        // 检查孤立的CSS属性
        const trimmed = line.trim();
        if (trimmed.includes(':') && trimmed.endsWith(';') && !trimmed.includes('{') && !trimmed.includes('}')) {
            // 检查前面是否有选择器
            let hasSelector = false;
            for (let k = i - 1; k >= 0; k--) {
                const prevLine = lines[k].trim();
                if (prevLine.includes('{')) {
                    hasSelector = true;
                    break;
                }
                if (prevLine.includes('}')) {
                    break;
                }
            }
            if (!hasSelector) {
                errors.push(`第${lineNum}行: 孤立的CSS属性 "${trimmed}"`);
            }
        }
    }
    
    // 检查未闭合的大括号
    if (braceStack.length > 0) {
        braceStack.forEach(brace => {
            errors.push(`第${brace.line}行: 未闭合的 '{' 字符`);
        });
    }
    
    if (errors.length === 0) {
        console.log('✅ CSS语法检查通过');
    } else {
        console.log('❌ 发现CSS语法错误:');
        errors.forEach(error => console.log(`  - ${error}`));
    }
    
    return errors;
}

// 检查InterviewingPage.vue
const filePath = path.join(__dirname, 'src/views/InterviewingPage.vue');
validateCSS(filePath);
