{"version": 3, "file": "color-tests.js", "sourceRoot": "", "sources": ["../../../../../src/css/types/__tests__/color-tests.ts"], "names": [], "mappings": ";;AAAA,iCAAmC;AACnC,kCAA8D;AAC9D,8CAA2C;AAG3C,IAAM,KAAK,GAAG,UAAC,KAAa,IAAK,OAAA,aAAK,CAAC,KAAK,CAAC,EAAa,EAAE,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAApD,CAAoD,CAAC;AAEtF,QAAQ,CAAC,OAAO,EAAE;IACd,QAAQ,CAAC,SAAS,EAAE;QAChB,QAAQ,CAAC,SAAS,EAAE;YAChB,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAA5C,CAA4C,CAAC,CAAC;YAC/D,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAA7C,CAA6C,CAAC,CAAC;YACjE,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAA7C,CAA6C,CAAC,CAAC;YACjE,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAlD,CAAkD,CAAC,CAAC;YACrE,EAAE,CAAC,SAAS,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAA/C,CAA+C,CAAC,CAAC;YACrE,EAAE,CAAC,WAAW,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAjD,CAAiD,CAAC,CAAC;YACzE,EAAE,CAAC,SAAS,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAArD,CAAqD,CAAC,CAAC;YAC3E,EAAE,CAAC,WAAW,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAvD,CAAuD,CAAC,CAAC;YAC/E,EAAE,CAAC,SAAS,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAArD,CAAqD,CAAC,CAAC;YAC3E,EAAE,CAAC,SAAS,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAArD,CAAqD,CAAC,CAAC;YAC3E,EAAE,CAAC,aAAa,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAnD,CAAmD,CAAC,CAAC;YAC7E,EAAE,CAAC,QAAQ,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAApD,CAAoD,CAAC,CAAC;YACzE,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAA9C,CAA8C,CAAC,CAAC;YACjE,EAAE,CAAC,cAAc,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAApD,CAAoD,CAAC,CAAC;YAC/E,EAAE,CAAC,eAAe,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAArD,CAAqD,CAAC,CAAC;YACjF,EAAE,CAAC,kBAAkB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAA9D,CAA8D,CAAC,CAAC;YAC7F,EAAE,CAAC,uBAAuB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAArE,CAAqE,CAAC,CAAC;YACzG,EAAE,CAAC,qBAAqB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAjE,CAAiE,CAAC,CAAC;YACnG,EAAE,CAAC,iBAAiB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAA5D,CAA4D,CAAC,CAAC;YAC1F,EAAE,CAAC,oBAAoB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAA5D,CAA4D,CAAC,CAAC;YAC7F,EAAE,CAAC,0BAA0B,EAAE;gBAC3B,OAAA,oBAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YAAxE,CAAwE,CAAC,CAAC;YAC9E,EAAE,CAAC,wBAAwB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,EAAvE,CAAuE,CAAC,CAAC;YAC5G,EAAE,CAAC,kBAAkB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAjE,CAAiE,CAAC,CAAC;YAChG,EAAE,CAAC,oBAAoB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAnE,CAAmE,CAAC,CAAC;YACpG,EAAE,CAAC,kBAAkB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAjE,CAAiE,CAAC,CAAC;YAChG,EAAE,CAAC,uBAAuB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAtE,CAAsE,CAAC,CAAC;YAC1G,EAAE,CAAC,2BAA2B,EAAE;gBAC5B,OAAA,oBAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAA1E,CAA0E,CAAC,CAAC;YAChF,EAAE,CAAC,wBAAwB,EAAE,cAAM,OAAA,oBAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAvE,CAAuE,CAAC,CAAC;YAC5G,EAAE,CAAC,8BAA8B,EAAE;gBAC/B,OAAA,oBAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAAlF,CAAkF,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QACH,QAAQ,CAAC,MAAM,EAAE;YACb,QAAQ,CAAC,eAAe,EAAE;gBACtB,EAAE,CAAC,aAAa,EAAE,cAAM,OAAA,oBAAW,CAAC,qBAAa,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,EAAtD,CAAsD,CAAC,CAAC;gBAChF,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,oBAAW,CAAC,qBAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAhD,CAAgD,CAAC,CAAC;gBACnE,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,qBAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAjD,CAAiD,CAAC,CAAC;gBACrE,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,qBAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAjD,CAAiD,CAAC,CAAC;gBACrE,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,qBAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,EAAhD,CAAgD,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,UAAU,EAAE;gBACjB,EAAE,CAAC,aAAa,EAAE,cAAM,OAAA,oBAAW,CAAC,gBAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,eAAe,CAAC,EAA5D,CAA4D,CAAC,CAAC;gBACtF,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,oBAAW,CAAC,gBAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,EAAlD,CAAkD,CAAC,CAAC;gBACrE,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,gBAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC,EAAnD,CAAmD,CAAC,CAAC;gBACvE,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,gBAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,iBAAiB,CAAC,EAAxD,CAAwD,CAAC,CAAC;gBAC5E,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,oBAAW,CAAC,gBAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,kBAAkB,CAAC,EAAxD,CAAwD,CAAC,CAAC;gBAC3E,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,gBAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,kBAAkB,CAAC,EAAzD,CAAyD,CAAC,CAAC;gBAC7E,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,oBAAW,CAAC,gBAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,uBAAuB,CAAC,EAA9D,CAA8D,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}