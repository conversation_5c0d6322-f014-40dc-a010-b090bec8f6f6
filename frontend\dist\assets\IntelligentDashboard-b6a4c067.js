import{_ as D,r,o as N,i as u,j as l,k as d,n as t,p as a,w as o,F as y,x as b,M as n,Y as W,Z as x,$ as k,N as p,a0 as M,R as P,B as C,C as I,A as S,z as i,a1 as R,y as U,f as j,P as q}from"./index-b6a2842e.js";const E={class:"intelligent-dashboard"},G={class:"dashboard-header"},Y={class:"header-content"},Z={class:"header-actions"},$={class:"metrics-grid"},H={class:"metric-header"},J={class:"metric-content"},K={class:"metric-value"},Q={class:"metric-label"},X={class:"metric-description"},tt={class:"charts-section"},et={class:"charts-grid"},st={class:"chart-card large-chart"},at={class:"chart-header"},ot={class:"chart-controls"},it={class:"chart-content"},lt={class:"chart-card"},nt={class:"chart-content"},rt={class:"chart-card"},dt={class:"chart-content"},ct={class:"activity-section"},_t={class:"section-header"},ut={class:"section-title"},pt={class:"activity-list"},ht={class:"activity-avatar"},vt=["src","alt"],gt={class:"activity-content"},ft={class:"activity-text"},mt={class:"activity-user"},yt={class:"activity-action"},bt={class:"activity-target"},xt={class:"activity-meta"},kt={class:"activity-time"},Ct={key:0,class:"activity-score"},It={class:"score-value"},St={class:"insights-section"},Tt={class:"section-header"},wt={class:"section-title"},At={class:"insights-grid"},zt={class:"insight-header"},Vt={class:"insight-content"},Lt={class:"insight-title"},Bt={class:"insight-description"},Ft={class:"insight-actions"},Ot={__name:"IntelligentDashboard",setup(Dt){const T=r([]),w=r("month"),h=r(null),v=r(null),g=r(null),A=r([{id:1,label:"本月面试总数",value:"1,247",description:"较上月增长 12.5%",icon:"Users",gradient:"linear-gradient(135deg, #1890ff 0%, #0066cc 100%)",trend:"up",trendIcon:"CaretTop",change:"+12.5%"},{id:2,label:"平均AI评分",value:"87.3",description:"整体表现优秀",icon:"Star",gradient:"linear-gradient(135deg, #52c41a 0%, #389e0d 100%)",trend:"up",trendIcon:"CaretTop",change:"****"},{id:3,label:"平均面试时长",value:"28分钟",description:"效率提升 8%",icon:"Clock",gradient:"linear-gradient(135deg, #722ed1 0%, #531dab 100%)",trend:"down",trendIcon:"CaretBottom",change:"-8%"},{id:4,label:"技能匹配度",value:"92.1%",description:"匹配精准度高",icon:"TrendCharts",gradient:"linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)",trend:"up",trendIcon:"CaretTop",change:"****%"}]),z=r([{id:1,user:"张三",action:"完成了",target:"AI算法工程师面试",time:"2分钟前",avatar:"/images/candidate-avatar.svg",status:"success",statusText:"已完成",score:89},{id:2,user:"李四",action:"开始了",target:"大数据工程师面试",time:"5分钟前",avatar:"/images/candidate-avatar.svg",status:"progress",statusText:"进行中"},{id:3,user:"王五",action:"完成了",target:"IoT开发工程师面试",time:"10分钟前",avatar:"/images/candidate-avatar.svg",status:"success",statusText:"已完成",score:92}]),V=r([{id:1,type:"warning",priority:"high",priorityText:"高优先级",icon:"Warning",title:"面试通过率下降",description:"本周AI算法岗位面试通过率较上周下降15%，建议调整面试难度或优化题目设置"},{id:2,type:"info",priority:"medium",priorityText:"中优先级",icon:"InfoFilled",title:"候选人技能分布变化",description:"深度学习技能的候选人比例上升20%，建议增加相关岗位需求"},{id:3,type:"success",priority:"low",priorityText:"低优先级",icon:"SuccessFilled",title:"面试体验优化建议",description:"候选人反馈AI面试官响应速度很好，建议保持当前配置"}]),L=()=>{W(()=>{if(h.value){const _=x(h.value),e={tooltip:{trigger:"axis",backgroundColor:"rgba(255, 255, 255, 0.95)",borderColor:"#e6f7ff",borderWidth:1,textStyle:{color:"#2c3e50"}},legend:{data:["面试数量","通过数量"],textStyle:{color:"#64748b"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"],axisLine:{lineStyle:{color:"#e6f7ff"}},axisTick:{show:!1},axisLabel:{color:"#64748b"}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#64748b"},splitLine:{lineStyle:{color:"#f1f5f9"}}},series:[{name:"面试数量",type:"line",data:[45,52,38,67,73,89,94],smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3},itemStyle:{color:"#1890ff"},areaStyle:{color:new k(0,0,0,1,[{offset:0,color:"rgba(24, 144, 255, 0.3)"},{offset:1,color:"rgba(24, 144, 255, 0.05)"}])}},{name:"通过数量",type:"line",data:[32,41,28,52,58,71,76],smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3},itemStyle:{color:"#52c41a"},areaStyle:{color:new k(0,0,0,1,[{offset:0,color:"rgba(82, 196, 26, 0.3)"},{offset:1,color:"rgba(82, 196, 26, 0.05)"}])}}]};_.setOption(e)}if(v.value){const _=x(v.value),e={tooltip:{trigger:"item"},series:[{type:"pie",radius:["40%","70%"],data:[{value:35,name:"优秀(90-100)"},{value:45,name:"良好(80-89)"},{value:15,name:"中等(70-79)"},{value:5,name:"待提升(<70)"}],itemStyle:{borderRadius:8,borderColor:"#fff",borderWidth:2}}]};_.setOption(e)}if(g.value){const _=x(g.value),e={tooltip:{trigger:"axis"},xAxis:{type:"category",data:["Python","机器学习","深度学习","数据分析","TensorFlow"]},yAxis:{type:"value",max:100},series:[{type:"bar",data:[92,88,85,90,87],itemStyle:{color:new k(0,0,0,1,[{offset:0,color:"#1890ff"},{offset:1,color:"#0066cc"}])},barWidth:"60%"}]};_.setOption(e)}})};return N(()=>{L()}),(_,e)=>{const B=u("el-date-picker"),c=u("el-icon"),f=u("el-button"),m=u("el-radio-button"),F=u("el-radio-group"),O=u("el-tag");return l(),d("div",E,[t("div",G,[t("div",Y,[e[3]||(e[3]=t("div",{class:"header-info"},[t("h1",{class:"dashboard-title"},"智能招聘仪表板"),t("p",{class:"dashboard-subtitle"},"基于iFlytek Spark AI的数据洞察与决策支持")],-1)),t("div",Z,[a(B,{modelValue:T.value,"onUpdate:modelValue":e[0]||(e[0]=s=>T.value=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"date-picker"},null,8,["modelValue"]),a(f,{type:"primary",class:"export-btn"},{default:o(()=>[a(c,null,{default:o(()=>[a(p(M))]),_:1}),e[2]||(e[2]=n(" 导出报告 "))]),_:1,__:[2]})])])]),t("div",$,[(l(!0),d(y,null,b(A.value,s=>(l(),d("div",{class:"metric-card",key:s.id},[t("div",H,[t("div",{class:"metric-icon",style:P({background:s.gradient})},[a(c,{size:24},{default:o(()=>[(l(),C(I(s.icon)))]),_:2},1024)],4),t("div",{class:S(["metric-trend",s.trend])},[a(c,null,{default:o(()=>[(l(),C(I(s.trendIcon)))]),_:2},1024),t("span",null,i(s.change),1)],2)]),t("div",J,[t("h3",K,i(s.value),1),t("p",Q,i(s.label),1),t("div",X,i(s.description),1)])]))),128))]),t("div",tt,[t("div",et,[t("div",st,[t("div",at,[e[7]||(e[7]=t("h3",{class:"chart-title"},"面试活动趋势",-1)),t("div",ot,[a(F,{modelValue:w.value,"onUpdate:modelValue":e[1]||(e[1]=s=>w.value=s),size:"small"},{default:o(()=>[a(m,{label:"week"},{default:o(()=>e[4]||(e[4]=[n("本周")])),_:1,__:[4]}),a(m,{label:"month"},{default:o(()=>e[5]||(e[5]=[n("本月")])),_:1,__:[5]}),a(m,{label:"quarter"},{default:o(()=>e[6]||(e[6]=[n("本季度")])),_:1,__:[6]})]),_:1},8,["modelValue"])])]),t("div",it,[t("div",{ref_key:"trendChart",ref:h,class:"chart-container"},null,512)])]),t("div",lt,[e[8]||(e[8]=t("div",{class:"chart-header"},[t("h3",{class:"chart-title"},"AI评分分布")],-1)),t("div",nt,[t("div",{ref_key:"scoreChart",ref:v,class:"chart-container"},null,512)])]),t("div",rt,[e[9]||(e[9]=t("div",{class:"chart-header"},[t("h3",{class:"chart-title"},"技能匹配度分析")],-1)),t("div",dt,[t("div",{ref_key:"skillChart",ref:g,class:"chart-container"},null,512)])])])]),t("div",ct,[t("div",_t,[t("h3",ut,[a(c,null,{default:o(()=>[a(p(R))]),_:1}),e[10]||(e[10]=n(" 实时活动动态 "))]),a(f,{size:"small",text:"",type:"primary"},{default:o(()=>e[11]||(e[11]=[n("查看全部")])),_:1,__:[11]})]),t("div",pt,[(l(!0),d(y,null,b(z.value,s=>(l(),d("div",{class:"activity-item",key:s.id},[t("div",ht,[t("img",{src:s.avatar,alt:s.user},null,8,vt)]),t("div",gt,[t("div",ft,[t("span",mt,i(s.user),1),t("span",yt,i(s.action),1),t("span",bt,i(s.target),1)]),t("div",xt,[t("span",kt,i(s.time),1),a(O,{type:s.status==="success"?"success":"info",size:"small"},{default:o(()=>[n(i(s.statusText),1)]),_:2},1032,["type"])])]),s.score?(l(),d("div",Ct,[t("span",It,i(s.score),1),e[12]||(e[12]=t("span",{class:"score-label"},"分",-1))])):U("",!0)]))),128))])]),t("div",St,[t("div",Tt,[t("h3",wt,[a(c,null,{default:o(()=>[a(p(j))]),_:1}),e[13]||(e[13]=n(" AI智能洞察 "))])]),t("div",At,[(l(!0),d(y,null,b(V.value,s=>(l(),d("div",{class:"insight-card",key:s.id},[t("div",zt,[t("div",{class:S(["insight-icon",s.type])},[a(c,null,{default:o(()=>[(l(),C(I(s.icon)))]),_:2},1024)],2),t("div",{class:S(["insight-priority",s.priority])},i(s.priorityText),3)]),t("div",Vt,[t("h4",Lt,i(s.title),1),t("p",Bt,i(s.description),1),t("div",Ft,[a(f,{size:"small",type:"primary",text:""},{default:o(()=>[e[14]||(e[14]=n(" 查看详情 ")),a(c,null,{default:o(()=>[a(p(q))]),_:1})]),_:1,__:[14]})])])]))),128))])])])}}},Wt=D(Ot,[["__scopeId","data-v-5a546781"]]);export{Wt as default};
