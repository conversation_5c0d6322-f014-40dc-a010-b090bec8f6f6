(function(R,g,n){"use strict";function Ce(e,t,o){let l;n.isRef(o)?l={evaluating:o}:l=o||{};const{lazy:r=!1,flush:i="pre",evaluating:s=void 0,shallow:a=!0,onError:u=g.noop}=l,f=n.shallowRef(!r),c=a?n.shallowRef(t):n.ref(t);let d=0;return n.watchEffect(async h=>{if(!f.value)return;d++;const m=d;let v=!1;s&&Promise.resolve().then(()=>{s.value=!0});try{const S=await e(p=>{h(()=>{s&&(s.value=!1),v||p()})});m===d&&(c.value=S)}catch(S){u(S)}finally{s&&m===d&&(s.value=!1),v=!0}},{flush:i}),r?n.computed(()=>(f.value=!0,c.value)):c}function kt(e,t,o,l){let r=n.inject(e);return o&&(r=n.inject(e,o)),l&&(r=n.inject(e,o,l)),typeof t=="function"?n.computed(i=>t(r,i)):n.computed({get:i=>t.get(r,i),set:t.set})}function _t(e={}){const{inheritAttrs:t=!0}=e,o=n.shallowRef(),l=n.defineComponent({setup(i,{slots:s}){return()=>{o.value=s.default}}}),r=n.defineComponent({inheritAttrs:t,props:e.props,setup(i,{attrs:s,slots:a}){return()=>{var u;if(!o.value&&process.env.NODE_ENV!=="production")throw new Error("[VueUse] Failed to find the definition of reusable template");const f=(u=o.value)==null?void 0:u.call(o,{...e.props==null?Vt(s):i,$slots:a});return t&&f?.length===1?f[0]:f}}});return g.makeDestructurable({define:l,reuse:r},[l,r])}function Vt(e){const t={};for(const o in e)t[g.camelize(o)]=e[o];return t}function Ft(e={}){let t=0;const o=n.ref([]);function l(...s){const a=n.shallowReactive({key:t++,args:s,promise:void 0,resolve:()=>{},reject:()=>{},isResolving:!1,options:e});return o.value.push(a),a.promise=new Promise((u,f)=>{a.resolve=c=>(a.isResolving=!0,u(c)),a.reject=f}).finally(()=>{a.promise=void 0;const u=o.value.indexOf(a);u!==-1&&o.value.splice(u,1)}),a.promise}function r(...s){return e.singleton&&o.value.length>0?o.value[0].promise:l(...s)}const i=n.defineComponent((s,{slots:a})=>{const u=()=>o.value.map(f=>{var c;return n.h(n.Fragment,{key:f.key},(c=a.default)==null?void 0:c.call(a,f))});return e.transition?()=>n.h(n.TransitionGroup,e.transition,u):u});return i.start=r,i}function Pt(e){return function(...t){return e.apply(this,t.map(o=>n.toValue(o)))}}const L=g.isClient?window:void 0,q=g.isClient?window.document:void 0,G=g.isClient?window.navigator:void 0,Ct=g.isClient?window.location:void 0;function W(e){var t;const o=n.toValue(e);return(t=o?.$el)!=null?t:o}function k(...e){const t=[],o=()=>{t.forEach(a=>a()),t.length=0},l=(a,u,f,c)=>(a.addEventListener(u,f,c),()=>a.removeEventListener(u,f,c)),r=n.computed(()=>{const a=g.toArray(n.toValue(e[0])).filter(u=>u!=null);return a.every(u=>typeof u!="string")?a:void 0}),i=g.watchImmediate(()=>{var a,u;return[(u=(a=r.value)==null?void 0:a.map(f=>W(f)))!=null?u:[L].filter(f=>f!=null),g.toArray(n.toValue(r.value?e[1]:e[0])),g.toArray(n.unref(r.value?e[2]:e[1])),n.toValue(r.value?e[3]:e[2])]},([a,u,f,c])=>{if(o(),!a?.length||!u?.length||!f?.length)return;const d=g.isObject(c)?{...c}:c;t.push(...a.flatMap(h=>u.flatMap(m=>f.map(v=>l(h,m,v,d)))))},{flush:"post"}),s=()=>{i(),o()};return g.tryOnScopeDispose(o),s}let De=!1;function Dt(e,t,o={}){const{window:l=L,ignore:r=[],capture:i=!0,detectIframe:s=!1,controls:a=!1}=o;if(!l)return a?{stop:g.noop,cancel:g.noop,trigger:g.noop}:g.noop;if(g.isIOS&&!De){De=!0;const p={passive:!0};Array.from(l.document.body.children).forEach(w=>w.addEventListener("click",g.noop,p)),l.document.documentElement.addEventListener("click",g.noop,p)}let u=!0;const f=p=>n.toValue(r).some(w=>{if(typeof w=="string")return Array.from(l.document.querySelectorAll(w)).some(y=>y===p.target||p.composedPath().includes(y));{const y=W(w);return y&&(p.target===y||p.composedPath().includes(y))}});function c(p){const w=n.toValue(p);return w&&w.$.subTree.shapeFlag===16}function d(p,w){const y=n.toValue(p),b=y.$.subTree&&y.$.subTree.children;return b==null||!Array.isArray(b)?!1:b.some(E=>E.el===w.target||w.composedPath().includes(E.el))}const h=p=>{const w=W(e);if(p.target!=null&&!(!(w instanceof Element)&&c(e)&&d(e,p))&&!(!w||w===p.target||p.composedPath().includes(w))){if("detail"in p&&p.detail===0&&(u=!f(p)),!u){u=!0;return}t(p)}};let m=!1;const v=[k(l,"click",p=>{m||(m=!0,setTimeout(()=>{m=!1},0),h(p))},{passive:!0,capture:i}),k(l,"pointerdown",p=>{const w=W(e);u=!f(p)&&!!(w&&!p.composedPath().includes(w))},{passive:!0}),s&&k(l,"blur",p=>{setTimeout(()=>{var w;const y=W(e);((w=l.document.activeElement)==null?void 0:w.tagName)==="IFRAME"&&!y?.contains(l.document.activeElement)&&t(p)},0)},{passive:!0})].filter(Boolean),S=()=>v.forEach(p=>p());return a?{stop:S,cancel:()=>{u=!1},trigger:p=>{u=!0,h(p),u=!1}}:S}function Ae(){const e=n.shallowRef(!1),t=n.getCurrentInstance();return t&&n.onMounted(()=>{e.value=!0},t),e}function H(e){const t=Ae();return n.computed(()=>(t.value,!!e()))}function X(e,t,o={}){const{window:l=L,...r}=o;let i;const s=H(()=>l&&"MutationObserver"in l),a=()=>{i&&(i.disconnect(),i=void 0)},u=n.computed(()=>{const h=n.toValue(e),m=g.toArray(h).map(W).filter(g.notNullish);return new Set(m)}),f=n.watch(()=>u.value,h=>{a(),s.value&&h.size&&(i=new MutationObserver(t),h.forEach(m=>i.observe(m,r)))},{immediate:!0,flush:"post"}),c=()=>i?.takeRecords(),d=()=>{f(),a()};return g.tryOnScopeDispose(d),{isSupported:s,stop:d,takeRecords:c}}function we(e,t,o={}){const{window:l=L,document:r=l?.document,flush:i="sync"}=o;if(!l||!r)return g.noop;let s;const a=c=>{s?.(),s=c},u=n.watchEffect(()=>{const c=W(e);if(c){const{stop:d}=X(r,h=>{h.map(v=>[...v.removedNodes]).flat().some(v=>v===c||v.contains(c))&&t(h)},{window:l,childList:!0,subtree:!0});a(d)}},{flush:i}),f=()=>{u(),a()};return g.tryOnScopeDispose(f),f}function At(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function se(...e){let t,o,l={};e.length===3?(t=e[0],o=e[1],l=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,o=e[0],l=e[1]):(t=e[0],o=e[1]):(t=!0,o=e[0]);const{target:r=L,eventName:i="keydown",passive:s=!1,dedupe:a=!1}=l,u=At(t);return k(r,i,c=>{c.repeat&&n.toValue(a)||u(c)&&o(c)},s)}function Mt(e,t,o={}){return se(e,t,{...o,eventName:"keydown"})}function It(e,t,o={}){return se(e,t,{...o,eventName:"keypress"})}function Lt(e,t,o={}){return se(e,t,{...o,eventName:"keyup"})}const Nt=500,xt=10;function Wt(e,t,o){var l,r;const i=n.computed(()=>W(e));let s,a,u,f=!1;function c(){s&&(clearTimeout(s),s=void 0),a=void 0,u=void 0,f=!1}function d(w){var y,b,E;const[O,P,T]=[u,a,f];if(c(),!o?.onMouseUp||!P||!O||(y=o?.modifiers)!=null&&y.self&&w.target!==i.value)return;(b=o?.modifiers)!=null&&b.prevent&&w.preventDefault(),(E=o?.modifiers)!=null&&E.stop&&w.stopPropagation();const _=w.x-P.x,F=w.y-P.y,A=Math.sqrt(_*_+F*F);o.onMouseUp(w.timeStamp-O,A,T)}function h(w){var y,b,E,O;(y=o?.modifiers)!=null&&y.self&&w.target!==i.value||(c(),(b=o?.modifiers)!=null&&b.prevent&&w.preventDefault(),(E=o?.modifiers)!=null&&E.stop&&w.stopPropagation(),a={x:w.x,y:w.y},u=w.timeStamp,s=setTimeout(()=>{f=!0,t(w)},(O=o?.delay)!=null?O:Nt))}function m(w){var y,b,E,O;if((y=o?.modifiers)!=null&&y.self&&w.target!==i.value||!a||o?.distanceThreshold===!1)return;(b=o?.modifiers)!=null&&b.prevent&&w.preventDefault(),(E=o?.modifiers)!=null&&E.stop&&w.stopPropagation();const P=w.x-a.x,T=w.y-a.y;Math.sqrt(P*P+T*T)>=((O=o?.distanceThreshold)!=null?O:xt)&&c()}const v={capture:(l=o?.modifiers)==null?void 0:l.capture,once:(r=o?.modifiers)==null?void 0:r.once},S=[k(i,"pointerdown",h,v),k(i,"pointermove",m,v),k(i,["pointerup","pointerleave"],d,v)];return()=>S.forEach(w=>w())}function Ht(){const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")}function Ut({keyCode:e,metaKey:t,ctrlKey:o,altKey:l}){return t||o||l?!1:e>=48&&e<=57||e>=96&&e<=105||e>=65&&e<=90}function $t(e,t={}){const{document:o=q}=t;o&&k(o,"keydown",r=>{!Ht()&&Ut(r)&&e(r)},{passive:!0})}function Bt(e,t=null){const o=n.getCurrentInstance();let l=()=>{};const r=n.customRef((i,s)=>(l=s,{get(){var a,u;return i(),(u=(a=o?.proxy)==null?void 0:a.$refs[e])!=null?u:t},set(){}}));return g.tryOnMounted(l),n.onUpdated(l),r}function Me(e={}){var t;const{window:o=L,deep:l=!0,triggerOnRemoval:r=!1}=e,i=(t=e.document)!=null?t:o?.document,s=()=>{var f;let c=i?.activeElement;if(l)for(;c?.shadowRoot;)c=(f=c?.shadowRoot)==null?void 0:f.activeElement;return c},a=n.shallowRef(),u=()=>{a.value=s()};if(o){const f={capture:!0,passive:!0};k(o,"blur",c=>{c.relatedTarget===null&&u()},f),k(o,"focus",u,f)}return r&&we(a,u,{document:i}),u(),a}function K(e,t={}){const{immediate:o=!0,fpsLimit:l=void 0,window:r=L,once:i=!1}=t,s=n.shallowRef(!1),a=n.computed(()=>l?1e3/n.toValue(l):null);let u=0,f=null;function c(m){if(!s.value||!r)return;u||(u=m);const v=m-u;if(a.value&&v<a.value){f=r.requestAnimationFrame(c);return}if(u=m,e({delta:v,timestamp:m}),i){s.value=!1,f=null;return}f=r.requestAnimationFrame(c)}function d(){!s.value&&r&&(s.value=!0,u=0,f=r.requestAnimationFrame(c))}function h(){s.value=!1,f!=null&&r&&(r.cancelAnimationFrame(f),f=null)}return o&&d(),g.tryOnScopeDispose(h),{isActive:n.readonly(s),pause:h,resume:d}}function jt(e,t,o){let l,r;g.isObject(o)?(l=o,r=g.objectOmit(o,["window","immediate","commitStyles","persist","onReady","onError"])):(l={duration:o},r=o);const{window:i=L,immediate:s=!0,commitStyles:a,persist:u,playbackRate:f=1,onReady:c,onError:d=I=>{console.error(I)}}=l,h=H(()=>i&&HTMLElement&&"animate"in HTMLElement.prototype),m=n.shallowRef(void 0),v=n.shallowReactive({startTime:null,currentTime:null,timeline:null,playbackRate:f,pending:!1,playState:s?"idle":"paused",replaceState:"active"}),S=n.computed(()=>v.pending),p=n.computed(()=>v.playState),w=n.computed(()=>v.replaceState),y=n.computed({get(){return v.startTime},set(I){v.startTime=I,m.value&&(m.value.startTime=I)}}),b=n.computed({get(){return v.currentTime},set(I){v.currentTime=I,m.value&&(m.value.currentTime=I,N())}}),E=n.computed({get(){return v.timeline},set(I){v.timeline=I,m.value&&(m.value.timeline=I)}}),O=n.computed({get(){return v.playbackRate},set(I){v.playbackRate=I,m.value&&(m.value.playbackRate=I)}}),P=()=>{if(m.value)try{m.value.play(),N()}catch(I){x(),d(I)}else M()},T=()=>{var I;try{(I=m.value)==null||I.pause(),x()}catch(U){d(U)}},_=()=>{var I;m.value||M();try{(I=m.value)==null||I.reverse(),N()}catch(U){x(),d(U)}},F=()=>{var I;try{(I=m.value)==null||I.finish(),x()}catch(U){d(U)}},A=()=>{var I;try{(I=m.value)==null||I.cancel(),x()}catch(U){d(U)}};n.watch(()=>W(e),I=>{I?M():m.value=void 0}),n.watch(()=>t,I=>{if(m.value){M();const U=W(e);U&&(m.value.effect=new KeyframeEffect(U,n.toValue(I),r))}},{deep:!0}),g.tryOnMounted(()=>M(!0),!1),g.tryOnScopeDispose(A);function M(I){const U=W(e);!h.value||!U||(m.value||(m.value=U.animate(n.toValue(t),r)),u&&m.value.persist(),f!==1&&(m.value.playbackRate=f),I&&!s?m.value.pause():N(),c?.(m.value))}const V={passive:!0};k(m,["cancel","finish","remove"],x,V),k(m,"finish",()=>{var I;a&&((I=m.value)==null||I.commitStyles())},V);const{resume:D,pause:C}=K(()=>{m.value&&(v.pending=m.value.pending,v.playState=m.value.playState,v.replaceState=m.value.replaceState,v.startTime=m.value.startTime,v.currentTime=m.value.currentTime,v.timeline=m.value.timeline,v.playbackRate=m.value.playbackRate)},{immediate:!1});function N(){h.value&&D()}function x(){h.value&&i&&i.requestAnimationFrame(C)}return{isSupported:h,animate:m,play:P,pause:T,reverse:_,finish:F,cancel:A,pending:S,playState:p,replaceState:w,startTime:y,currentTime:b,timeline:E,playbackRate:O}}function zt(e,t){const{interrupt:o=!0,onError:l=g.noop,onFinished:r=g.noop,signal:i}=t||{},s={aborted:"aborted",fulfilled:"fulfilled",pending:"pending",rejected:"rejected"},a=Array.from(Array.from({length:e.length}),()=>({state:s.pending,data:null})),u=n.reactive(a),f=n.shallowRef(-1);if(!e||e.length===0)return r(),{activeIndex:f,result:u};function c(d,h){f.value++,u[f.value].data=h,u[f.value].state=d}return e.reduce((d,h)=>d.then(m=>{var v;if(i?.aborted){c(s.aborted,new Error("aborted"));return}if(((v=u[f.value])==null?void 0:v.state)===s.rejected&&o){r();return}const S=h(m).then(p=>(c(s.fulfilled,p),f.value===e.length-1&&r(),p));return i?Promise.race([S,qt(i)]):S}).catch(m=>i?.aborted?(c(s.aborted,m),m):(c(s.rejected,m),l(),m)),Promise.resolve()),{activeIndex:f,result:u}}function qt(e){return new Promise((t,o)=>{const l=new Error("aborted");e.aborted?o(l):e.addEventListener("abort",()=>o(l),{once:!0})})}function Ie(e,t,o){const{immediate:l=!0,delay:r=0,onError:i=g.noop,onSuccess:s=g.noop,resetOnExecute:a=!0,shallow:u=!0,throwError:f}=o??{},c=u?n.shallowRef(t):n.ref(t),d=n.shallowRef(!1),h=n.shallowRef(!1),m=n.shallowRef(void 0);async function v(w=0,...y){a&&(c.value=t),m.value=void 0,d.value=!1,h.value=!0,w>0&&await g.promiseTimeout(w);const b=typeof e=="function"?e(...y):e;try{const E=await b;c.value=E,d.value=!0,s(E)}catch(E){if(m.value=E,i(E),f)throw E}finally{h.value=!1}return c.value}l&&v(r);const S={state:c,isReady:d,isLoading:h,error:m,execute:v,executeImmediate:(...w)=>v(0,...w)};function p(){return new Promise((w,y)=>{g.until(h).toBe(!1).then(()=>w(S)).catch(y)})}return{...S,then(w,y){return p().then(w,y)}}}const te={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function Gt(e){return e?e instanceof Map?te.map:e instanceof Set?te.set:Array.isArray(e)?te.array:te.object:te.null}function Yt(e,t){const o=n.shallowRef(""),l=n.shallowRef();function r(){if(g.isClient)return l.value=new Promise((i,s)=>{try{const a=n.toValue(e);if(a==null)i("");else if(typeof a=="string")i(ge(new Blob([a],{type:"text/plain"})));else if(a instanceof Blob)i(ge(a));else if(a instanceof ArrayBuffer)i(window.btoa(String.fromCharCode(...new Uint8Array(a))));else if(a instanceof HTMLCanvasElement)i(a.toDataURL(t?.type,t?.quality));else if(a instanceof HTMLImageElement){const u=a.cloneNode(!1);u.crossOrigin="Anonymous",Xt(u).then(()=>{const f=document.createElement("canvas"),c=f.getContext("2d");f.width=u.width,f.height=u.height,c.drawImage(u,0,0,f.width,f.height),i(f.toDataURL(t?.type,t?.quality))}).catch(s)}else if(typeof a=="object"){const f=(t?.serializer||Gt(a))(a);return i(ge(new Blob([f],{type:"application/json"})))}else s(new Error("target is unsupported types"))}catch(a){s(a)}}),l.value.then(i=>{o.value=t?.dataUrl===!1?i.replace(/^data:.*?;base64,/,""):i}),l.value}return n.isRef(e)||typeof e=="function"?n.watch(e,r,{immediate:!0}):r(),{base64:o,promise:l,execute:r}}function Xt(e){return new Promise((t,o)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=o)})}function ge(e){return new Promise((t,o)=>{const l=new FileReader;l.onload=r=>{t(r.target.result)},l.onerror=o,l.readAsDataURL(e)})}function Kt(e={}){const{navigator:t=G}=e,o=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],l=H(()=>t&&"getBattery"in t&&typeof t.getBattery=="function"),r=n.shallowRef(!1),i=n.shallowRef(0),s=n.shallowRef(0),a=n.shallowRef(1);let u;function f(){r.value=this.charging,i.value=this.chargingTime||0,s.value=this.dischargingTime||0,a.value=this.level}return l.value&&t.getBattery().then(c=>{u=c,f.call(u),k(u,o,f,{passive:!0})}),{isSupported:l,charging:r,chargingTime:i,dischargingTime:s,level:a}}function Jt(e){let{acceptAllDevices:t=!1}=e||{};const{filters:o=void 0,optionalServices:l=void 0,navigator:r=G}=e||{},i=H(()=>r&&"bluetooth"in r),s=n.shallowRef(),a=n.shallowRef(null);n.watch(s,()=>{h()});async function u(){if(i.value){a.value=null,o&&o.length>0&&(t=!1);try{s.value=await r?.bluetooth.requestDevice({acceptAllDevices:t,filters:o,optionalServices:l})}catch(m){a.value=m}}}const f=n.shallowRef(),c=n.shallowRef(!1);function d(){c.value=!1,s.value=void 0,f.value=void 0}async function h(){if(a.value=null,s.value&&s.value.gatt){k(s,"gattserverdisconnected",d,{passive:!0});try{f.value=await s.value.gatt.connect(),c.value=f.value.connected}catch(m){a.value=m}}}return g.tryOnMounted(()=>{var m;s.value&&((m=s.value.gatt)==null||m.connect())}),g.tryOnScopeDispose(()=>{var m;s.value&&((m=s.value.gatt)==null||m.disconnect())}),{isSupported:i,isConnected:n.readonly(c),device:s,requestDevice:u,server:f,error:a}}const be=Symbol("vueuse-ssr-width");function Se(){const e=n.hasInjectionContext()?g.injectLocal(be,null):null;return typeof e=="number"?e:void 0}function Qt(e,t){t!==void 0?t.provide(be,e):g.provideLocal(be,e)}function $(e,t={}){const{window:o=L,ssrWidth:l=Se()}=t,r=H(()=>o&&"matchMedia"in o&&typeof o.matchMedia=="function"),i=n.shallowRef(typeof l=="number"),s=n.shallowRef(),a=n.shallowRef(!1),u=f=>{a.value=f.matches};return n.watchEffect(()=>{if(i.value){i.value=!r.value;const f=n.toValue(e).split(",");a.value=f.some(c=>{const d=c.includes("not all"),h=c.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),m=c.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let v=!!(h||m);return h&&v&&(v=l>=g.pxValue(h[1])),m&&v&&(v=l<=g.pxValue(m[1])),d?!v:v});return}r.value&&(s.value=o.matchMedia(n.toValue(e)),a.value=s.value.matches)}),k(s,"change",u,{passive:!0}),n.computed(()=>a.value)}const Zt={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},en={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400},Le={xs:0,sm:600,md:960,lg:1264,xl:1904},tn={xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560},nn=Le,on={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},ln={xs:0,sm:600,md:1024,lg:1440,xl:1920},an={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},rn={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560},sn={sm:576,md:768,lg:992,xl:1200},un={xs:0,sm:768,md:992,lg:1200,xl:1920};function cn(e,t={}){function o(m,v){let S=n.toValue(e[n.toValue(m)]);return v!=null&&(S=g.increaseWithUnit(S,v)),typeof S=="number"&&(S=`${S}px`),S}const{window:l=L,strategy:r="min-width",ssrWidth:i=Se()}=t,s=typeof i=="number",a=s?n.shallowRef(!1):{value:!0};s&&g.tryOnMounted(()=>a.value=!!l);function u(m,v){return!a.value&&s?m==="min"?i>=g.pxValue(v):i<=g.pxValue(v):l?l.matchMedia(`(${m}-width: ${v})`).matches:!1}const f=m=>$(()=>`(min-width: ${o(m)})`,t),c=m=>$(()=>`(max-width: ${o(m)})`,t),d=Object.keys(e).reduce((m,v)=>(Object.defineProperty(m,v,{get:()=>r==="min-width"?f(v):c(v),enumerable:!0,configurable:!0}),m),{});function h(){const m=Object.keys(e).map(v=>[v,d[v],g.pxValue(o(v))]).sort((v,S)=>v[2]-S[2]);return n.computed(()=>m.filter(([,v])=>v.value).map(([v])=>v))}return Object.assign(d,{greaterOrEqual:f,smallerOrEqual:c,greater(m){return $(()=>`(min-width: ${o(m,.1)})`,t)},smaller(m){return $(()=>`(max-width: ${o(m,-.1)})`,t)},between(m,v){return $(()=>`(min-width: ${o(m)}) and (max-width: ${o(v,-.1)})`,t)},isGreater(m){return u("min",o(m,.1))},isGreaterOrEqual(m){return u("min",o(m))},isSmaller(m){return u("max",o(m,-.1))},isSmallerOrEqual(m){return u("max",o(m))},isInBetween(m,v){return u("min",o(m))&&u("max",o(v,-.1))},current:h,active(){const m=h();return n.computed(()=>m.value.length===0?"":m.value.at(r==="min-width"?-1:0))}})}function fn(e){const{name:t,window:o=L}=e,l=H(()=>o&&"BroadcastChannel"in o),r=n.shallowRef(!1),i=n.ref(),s=n.ref(),a=n.shallowRef(null),u=c=>{i.value&&i.value.postMessage(c)},f=()=>{i.value&&i.value.close(),r.value=!0};return l.value&&g.tryOnMounted(()=>{a.value=null,i.value=new BroadcastChannel(t);const c={passive:!0};k(i,"message",d=>{s.value=d.data},c),k(i,"messageerror",d=>{a.value=d},c),k(i,"close",()=>{r.value=!0},c)}),g.tryOnScopeDispose(()=>{f()}),{isSupported:l,channel:i,data:s,post:u,close:f,error:a,isClosed:r}}const Ne=["hash","host","hostname","href","pathname","port","protocol","search"];function dn(e={}){const{window:t=L}=e,o=Object.fromEntries(Ne.map(i=>[i,n.ref()]));for(const[i,s]of g.objectEntries(o))n.watch(s,a=>{!t?.location||t.location[i]===a||(t.location[i]=a)});const l=i=>{var s;const{state:a,length:u}=t?.history||{},{origin:f}=t?.location||{};for(const c of Ne)o[c].value=(s=t?.location)==null?void 0:s[c];return n.reactive({trigger:i,state:a,length:u,origin:f,...o})},r=n.ref(l("load"));if(t){const i={passive:!0};k(t,"popstate",()=>r.value=l("popstate"),i),k(t,"hashchange",()=>r.value=l("hashchange"),i)}return r}function mn(e,t=(l,r)=>l===r,o){const{deepRefs:l=!0,...r}=o||{},i=g.createRef(e.value,l);return n.watch(()=>e.value,s=>{t(s,i.value)||(i.value=s)},r),i}function ue(e,t={}){const{controls:o=!1,navigator:l=G}=t,r=H(()=>l&&"permissions"in l),i=n.shallowRef(),s=typeof e=="string"?{name:e}:e,a=n.shallowRef(),u=()=>{var c,d;a.value=(d=(c=i.value)==null?void 0:c.state)!=null?d:"prompt"};k(i,"change",u,{passive:!0});const f=g.createSingletonPromise(async()=>{if(r.value){if(!i.value)try{i.value=await l.permissions.query(s)}catch{i.value=void 0}finally{u()}if(o)return n.toRaw(i.value)}});return f(),o?{state:a,isSupported:r,query:f}:a}function vn(e={}){const{navigator:t=G,read:o=!1,source:l,copiedDuring:r=1500,legacy:i=!1}=e,s=H(()=>t&&"clipboard"in t),a=ue("clipboard-read"),u=ue("clipboard-write"),f=n.computed(()=>s.value||i),c=n.shallowRef(""),d=n.shallowRef(!1),h=g.useTimeoutFn(()=>d.value=!1,r,{immediate:!1});async function m(){let y=!(s.value&&w(a.value));if(!y)try{c.value=await t.clipboard.readText()}catch{y=!0}y&&(c.value=p())}f.value&&o&&k(["copy","cut"],m,{passive:!0});async function v(y=n.toValue(l)){if(f.value&&y!=null){let b=!(s.value&&w(u.value));if(!b)try{await t.clipboard.writeText(y)}catch{b=!0}b&&S(y),c.value=y,d.value=!0,h.start()}}function S(y){const b=document.createElement("textarea");b.value=y??"",b.style.position="absolute",b.style.opacity="0",document.body.appendChild(b),b.select(),document.execCommand("copy"),b.remove()}function p(){var y,b,E;return(E=(b=(y=document?.getSelection)==null?void 0:y.call(document))==null?void 0:b.toString())!=null?E:""}function w(y){return y==="granted"||y==="prompt"}return{isSupported:f,text:c,copied:d,copy:v}}function pn(e={}){const{navigator:t=G,read:o=!1,source:l,copiedDuring:r=1500}=e,i=H(()=>t&&"clipboard"in t),s=n.ref([]),a=n.shallowRef(!1),u=g.useTimeoutFn(()=>a.value=!1,r,{immediate:!1});function f(){i.value&&t.clipboard.read().then(d=>{s.value=d})}i.value&&o&&k(["copy","cut"],f,{passive:!0});async function c(d=n.toValue(l)){i.value&&d!=null&&(await t.clipboard.write(d),s.value=d,a.value=!0,u.start())}return{isSupported:i,content:s,copied:a,copy:c}}function ne(e){return JSON.parse(JSON.stringify(e))}function hn(e,t={}){const o=n.ref({}),l=n.shallowRef(!1);let r=!1;const{manual:i,clone:s=ne,deep:a=!0,immediate:u=!0}=t;n.watch(o,()=>{if(r){r=!1;return}l.value=!0},{deep:!0,flush:"sync"});function f(){r=!0,l.value=!1,o.value=s(n.toValue(e))}return!i&&(n.isRef(e)||typeof e=="function")?n.watch(e,f,{...t,deep:a,immediate:u}):f(),{cloned:o,isModified:l,sync:f}}const ce=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},fe="__vueuse_ssr_handlers__",xe=yn();function yn(){return fe in ce||(ce[fe]=ce[fe]||{}),ce[fe]}function de(e,t){return xe[e]||t}function wn(e,t){xe[e]=t}function We(e){return $("(prefers-color-scheme: dark)",e)}function He(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Re={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Ee="vueuse-storage";function me(e,t,o,l={}){var r;const{flush:i="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=L,eventFilter:h,onError:m=C=>{console.error(C)},initOnMounted:v}=l,S=(c?n.shallowRef:n.ref)(typeof t=="function"?t():t),p=n.computed(()=>n.toValue(e));if(!o)try{o=de("getDefaultStorage",()=>{var C;return(C=L)==null?void 0:C.localStorage})()}catch(C){m(C)}if(!o)return S;const w=n.toValue(t),y=He(w),b=(r=l.serializer)!=null?r:Re[y],{pause:E,resume:O}=g.pausableWatch(S,()=>A(S.value),{flush:i,deep:s,eventFilter:h});n.watch(p,()=>V(),{flush:i});let P=!1;const T=C=>{v&&!P||V(C)},_=C=>{v&&!P||D(C)};d&&a&&(o instanceof Storage?k(d,"storage",T,{passive:!0}):k(d,Ee,_)),v?g.tryOnMounted(()=>{P=!0,V()}):V();function F(C,N){if(d){const x={key:p.value,oldValue:C,newValue:N,storageArea:o};d.dispatchEvent(o instanceof Storage?new StorageEvent("storage",x):new CustomEvent(Ee,{detail:x}))}}function A(C){try{const N=o.getItem(p.value);if(C==null)F(N,null),o.removeItem(p.value);else{const x=b.write(C);N!==x&&(o.setItem(p.value,x),F(N,x))}}catch(N){m(N)}}function M(C){const N=C?C.newValue:o.getItem(p.value);if(N==null)return u&&w!=null&&o.setItem(p.value,b.write(w)),w;if(!C&&f){const x=b.read(N);return typeof f=="function"?f(x,w):y==="object"&&!Array.isArray(x)?{...w,...x}:x}else return typeof N!="string"?N:b.read(N)}function V(C){if(!(C&&C.storageArea!==o)){if(C&&C.key==null){S.value=w;return}if(!(C&&C.key!==p.value)){E();try{C?.newValue!==b.write(S.value)&&(S.value=M(C))}catch(N){m(N)}finally{C?n.nextTick(O):O()}}}}function D(C){V(C.detail)}return S}const gn="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Ue(e={}){const{selector:t="html",attribute:o="class",initialValue:l="auto",window:r=L,storage:i,storageKey:s="vueuse-color-scheme",listenToStorageChanges:a=!0,storageRef:u,emitAuto:f,disableTransition:c=!0}=e,d={auto:"",light:"light",dark:"dark",...e.modes||{}},h=We({window:r}),m=n.computed(()=>h.value?"dark":"light"),v=u||(s==null?g.toRef(l):me(s,l,i,{window:r,listenToStorageChanges:a})),S=n.computed(()=>v.value==="auto"?m.value:v.value),p=de("updateHTMLAttrs",(E,O,P)=>{const T=typeof E=="string"?r?.document.querySelector(E):W(E);if(!T)return;const _=new Set,F=new Set;let A=null;if(O==="class"){const V=P.split(/\s/g);Object.values(d).flatMap(D=>(D||"").split(/\s/g)).filter(Boolean).forEach(D=>{V.includes(D)?_.add(D):F.add(D)})}else A={key:O,value:P};if(_.size===0&&F.size===0&&A===null)return;let M;c&&(M=r.document.createElement("style"),M.appendChild(document.createTextNode(gn)),r.document.head.appendChild(M));for(const V of _)T.classList.add(V);for(const V of F)T.classList.remove(V);A&&T.setAttribute(A.key,A.value),c&&(r.getComputedStyle(M).opacity,document.head.removeChild(M))});function w(E){var O;p(t,o,(O=d[E])!=null?O:E)}function y(E){e.onChanged?e.onChanged(E,w):w(E)}n.watch(S,y,{flush:"post",immediate:!0}),g.tryOnMounted(()=>y(S.value));const b=n.computed({get(){return f?v.value:S.value},set(E){v.value=E}});return Object.assign(b,{store:v,system:m,state:S})}function bn(e=n.shallowRef(!1)){const t=g.createEventHook(),o=g.createEventHook(),l=g.createEventHook();let r=g.noop;const i=u=>(l.trigger(u),e.value=!0,new Promise(f=>{r=f})),s=u=>{e.value=!1,t.trigger(u),r({data:u,isCanceled:!1})},a=u=>{e.value=!1,o.trigger(u),r({data:u,isCanceled:!0})};return{isRevealed:n.computed(()=>e.value),reveal:i,confirm:s,cancel:a,onReveal:l.on,onConfirm:t.on,onCancel:o.on}}function Sn(e,t){var o,l;const r=n.shallowRef(n.toValue(e)),i=g.useIntervalFn(()=>{var c,d;const h=r.value-1;r.value=h<0?0:h,(c=t?.onTick)==null||c.call(t),r.value<=0&&(i.pause(),(d=t?.onComplete)==null||d.call(t))},(o=t?.interval)!=null?o:1e3,{immediate:(l=t?.immediate)!=null?l:!1}),s=c=>{var d;r.value=(d=n.toValue(c))!=null?d:n.toValue(e)},a=()=>{i.pause(),s()},u=()=>{i.isActive.value||r.value>0&&i.resume()};return{remaining:r,reset:s,stop:a,start:c=>{s(c),i.resume()},pause:i.pause,resume:u,isActive:i.isActive}}function oe(e,t,o={}){const{window:l=L,initialValue:r,observe:i=!1}=o,s=n.shallowRef(r),a=n.computed(()=>{var f;return W(t)||((f=l?.document)==null?void 0:f.documentElement)});function u(){var f;const c=n.toValue(e),d=n.toValue(a);if(d&&l&&c){const h=(f=l.getComputedStyle(d).getPropertyValue(c))==null?void 0:f.trim();s.value=h||s.value||r}}return i&&X(a,u,{attributeFilter:["style","class"],window:l}),n.watch([a,()=>n.toValue(e)],(f,c)=>{c[0]&&c[1]&&c[0].style.removeProperty(c[1]),u()},{immediate:!0}),n.watch([s,a],([f,c])=>{const d=n.toValue(e);c?.style&&d&&(f==null?c.style.removeProperty(d):c.style.setProperty(d,f))},{immediate:!0}),s}function $e(e){const t=n.getCurrentInstance(),o=g.computedWithControl(()=>null,()=>e?W(e):t.proxy.$el);return n.onUpdated(o.trigger),n.onMounted(o.trigger),o}function Rn(e,t){const o=n.shallowRef(f()),l=g.toRef(e),r=n.computed({get(){var c;const d=l.value;let h=t?.getIndexOf?t.getIndexOf(o.value,d):d.indexOf(o.value);return h<0&&(h=(c=t?.fallbackIndex)!=null?c:0),h},set(c){i(c)}});function i(c){const d=l.value,h=d.length,m=(c%h+h)%h,v=d[m];return o.value=v,v}function s(c=1){return i(r.value+c)}function a(c=1){return s(c)}function u(c=1){return s(-c)}function f(){var c,d;return(d=n.toValue((c=t?.initialValue)!=null?c:n.toValue(e)[0]))!=null?d:void 0}return n.watch(l,()=>i(r.value)),{state:o,index:r,next:a,prev:u,go:i}}function En(e={}){const{valueDark:t="dark",valueLight:o=""}=e,l=Ue({...e,onChanged:(s,a)=>{var u;e.onChanged?(u=e.onChanged)==null||u.call(e,s==="dark",a,s):a(s)},modes:{dark:t,light:o}}),r=n.computed(()=>l.system.value);return n.computed({get(){return l.value==="dark"},set(s){const a=s?"dark":"light";r.value===a?l.value="auto":l.value=a}})}function Be(e){return e}function Tn(e,t){return e.value=t}function On(e){return e?typeof e=="function"?e:ne:Be}function kn(e){return e?typeof e=="function"?e:ne:Be}function je(e,t={}){const{clone:o=!1,dump:l=On(o),parse:r=kn(o),setSource:i=Tn}=t;function s(){return n.markRaw({snapshot:l(e.value),timestamp:g.timestamp()})}const a=n.ref(s()),u=n.ref([]),f=n.ref([]),c=b=>{i(e,r(b.snapshot)),a.value=b},d=()=>{u.value.unshift(a.value),a.value=s(),t.capacity&&u.value.length>t.capacity&&u.value.splice(t.capacity,Number.POSITIVE_INFINITY),f.value.length&&f.value.splice(0,f.value.length)},h=()=>{u.value.splice(0,u.value.length),f.value.splice(0,f.value.length)},m=()=>{const b=u.value.shift();b&&(f.value.unshift(a.value),c(b))},v=()=>{const b=f.value.shift();b&&(u.value.unshift(a.value),c(b))},S=()=>{c(a.value)},p=n.computed(()=>[a.value,...u.value]),w=n.computed(()=>u.value.length>0),y=n.computed(()=>f.value.length>0);return{source:e,undoStack:u,redoStack:f,last:a,history:p,canUndo:w,canRedo:y,clear:h,commit:d,reset:S,undo:m,redo:v}}function Te(e,t={}){const{deep:o=!1,flush:l="pre",eventFilter:r,shouldCommit:i=()=>!0}=t,{eventFilter:s,pause:a,resume:u,isActive:f}=g.pausableFilter(r);let c=e.value;const{ignoreUpdates:d,ignorePrevAsyncUpdates:h,stop:m}=g.watchIgnorable(e,y,{deep:o,flush:l,eventFilter:s});function v(P,T){h(),d(()=>{P.value=T,c=T})}const S=je(e,{...t,clone:t.clone||o,setSource:v}),{clear:p,commit:w}=S;function y(){h(),i(c,e.value)&&(c=e.value,w())}function b(P){u(),P&&y()}function E(P){let T=!1;const _=()=>T=!0;d(()=>{P(_)}),T||y()}function O(){m(),p()}return{...S,isTracking:f,pause:a,resume:b,commit:y,batch:E,dispose:O}}function _n(e,t={}){const o=t.debounce?g.debounceFilter(t.debounce):void 0;return{...Te(e,{...t,eventFilter:o})}}function Vn(e={}){const{window:t=L,requestPermissions:o=!1,eventFilter:l=g.bypassFilter}=e,r=H(()=>typeof DeviceMotionEvent<"u"),i=H(()=>r.value&&"requestPermission"in DeviceMotionEvent&&typeof DeviceMotionEvent.requestPermission=="function"),s=n.shallowRef(!1),a=n.ref({x:null,y:null,z:null}),u=n.ref({alpha:null,beta:null,gamma:null}),f=n.shallowRef(0),c=n.ref({x:null,y:null,z:null});function d(){if(t){const m=g.createFilterWrapper(l,v=>{var S,p,w,y,b,E,O,P,T;a.value={x:((S=v.acceleration)==null?void 0:S.x)||null,y:((p=v.acceleration)==null?void 0:p.y)||null,z:((w=v.acceleration)==null?void 0:w.z)||null},c.value={x:((y=v.accelerationIncludingGravity)==null?void 0:y.x)||null,y:((b=v.accelerationIncludingGravity)==null?void 0:b.y)||null,z:((E=v.accelerationIncludingGravity)==null?void 0:E.z)||null},u.value={alpha:((O=v.rotationRate)==null?void 0:O.alpha)||null,beta:((P=v.rotationRate)==null?void 0:P.beta)||null,gamma:((T=v.rotationRate)==null?void 0:T.gamma)||null},f.value=v.interval});k(t,"devicemotion",m,{passive:!0})}}const h=async()=>{if(i.value||(s.value=!0),!s.value&&i.value){const m=DeviceMotionEvent.requestPermission;try{await m()==="granted"&&(s.value=!0,d())}catch(v){console.error(v)}}};return r.value&&(o&&i.value?h().then(()=>d()):d()),{acceleration:a,accelerationIncludingGravity:c,rotationRate:u,interval:f,isSupported:r,requirePermissions:i,ensurePermissions:h,permissionGranted:s}}function ze(e={}){const{window:t=L}=e,o=H(()=>t&&"DeviceOrientationEvent"in t),l=n.shallowRef(!1),r=n.shallowRef(null),i=n.shallowRef(null),s=n.shallowRef(null);return t&&o.value&&k(t,"deviceorientation",a=>{l.value=a.absolute,r.value=a.alpha,i.value=a.beta,s.value=a.gamma},{passive:!0}),{isSupported:o,isAbsolute:l,alpha:r,beta:i,gamma:s}}function Fn(e={}){const{window:t=L}=e,o=n.shallowRef(1),l=$(()=>`(resolution: ${o.value}dppx)`,e);let r=g.noop;return t&&(r=g.watchImmediate(l,()=>o.value=t.devicePixelRatio)),{pixelRatio:n.readonly(o),stop:r}}function Pn(e={}){const{navigator:t=G,requestPermissions:o=!1,constraints:l={audio:!0,video:!0},onUpdated:r}=e,i=n.ref([]),s=n.computed(()=>i.value.filter(v=>v.kind==="videoinput")),a=n.computed(()=>i.value.filter(v=>v.kind==="audioinput")),u=n.computed(()=>i.value.filter(v=>v.kind==="audiooutput")),f=H(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),c=n.shallowRef(!1);let d;async function h(){f.value&&(i.value=await t.mediaDevices.enumerateDevices(),r?.(i.value),d&&(d.getTracks().forEach(v=>v.stop()),d=null))}async function m(){const v=l.video?"camera":"microphone";if(!f.value)return!1;if(c.value)return!0;const{state:S,query:p}=ue(v,{controls:!0});if(await p(),S.value!=="granted"){let w=!0;try{const y=await t.mediaDevices.enumerateDevices(),b=y.some(O=>O.kind==="videoinput"),E=y.some(O=>O.kind==="audioinput"||O.kind==="audiooutput");l.video=b?l.video:!1,l.audio=E?l.audio:!1,d=await t.mediaDevices.getUserMedia(l)}catch{d=null,w=!1}h(),c.value=w}else c.value=!0;return c.value}return f.value&&(o&&m(),k(t.mediaDevices,"devicechange",h,{passive:!0}),h()),{devices:i,ensurePermissions:m,permissionGranted:c,videoInputs:s,audioInputs:a,audioOutputs:u,isSupported:f}}function Cn(e={}){var t;const o=n.shallowRef((t=e.enabled)!=null?t:!1),l=e.video,r=e.audio,{navigator:i=G}=e,s=H(()=>{var m;return(m=i?.mediaDevices)==null?void 0:m.getDisplayMedia}),a={audio:r,video:l},u=n.shallowRef();async function f(){var m;if(!(!s.value||u.value))return u.value=await i.mediaDevices.getDisplayMedia(a),(m=u.value)==null||m.getTracks().forEach(v=>k(v,"ended",d,{passive:!0})),u.value}async function c(){var m;(m=u.value)==null||m.getTracks().forEach(v=>v.stop()),u.value=void 0}function d(){c(),o.value=!1}async function h(){return await f(),u.value&&(o.value=!0),u.value}return n.watch(o,m=>{m?f():c()},{immediate:!0}),{isSupported:s,stream:u,start:h,stop:d,enabled:o}}function qe(e={}){const{document:t=q}=e;if(!t)return n.shallowRef("visible");const o=n.shallowRef(t.visibilityState);return k(t,"visibilitychange",()=>{o.value=t.visibilityState},{passive:!0}),o}function Dn(e,t={}){var o;const{pointerTypes:l,preventDefault:r,stopPropagation:i,exact:s,onMove:a,onEnd:u,onStart:f,initialValue:c,axis:d="both",draggingElement:h=L,containerElement:m,handle:v=e,buttons:S=[0]}=t,p=n.ref((o=n.toValue(c))!=null?o:{x:0,y:0}),w=n.ref(),y=T=>l?l.includes(T.pointerType):!0,b=T=>{n.toValue(r)&&T.preventDefault(),n.toValue(i)&&T.stopPropagation()},E=T=>{var _;if(!n.toValue(S).includes(T.button)||n.toValue(t.disabled)||!y(T)||n.toValue(s)&&T.target!==n.toValue(e))return;const F=n.toValue(m),A=(_=F?.getBoundingClientRect)==null?void 0:_.call(F),M=n.toValue(e).getBoundingClientRect(),V={x:T.clientX-(F?M.left-A.left+F.scrollLeft:M.left),y:T.clientY-(F?M.top-A.top+F.scrollTop:M.top)};f?.(V,T)!==!1&&(w.value=V,b(T))},O=T=>{if(n.toValue(t.disabled)||!y(T)||!w.value)return;const _=n.toValue(m),F=n.toValue(e).getBoundingClientRect();let{x:A,y:M}=p.value;(d==="x"||d==="both")&&(A=T.clientX-w.value.x,_&&(A=Math.min(Math.max(0,A),_.scrollWidth-F.width))),(d==="y"||d==="both")&&(M=T.clientY-w.value.y,_&&(M=Math.min(Math.max(0,M),_.scrollHeight-F.height))),p.value={x:A,y:M},a?.(p.value,T),b(T)},P=T=>{n.toValue(t.disabled)||!y(T)||w.value&&(w.value=void 0,u?.(p.value,T),b(T))};if(g.isClient){const T=()=>{var _;return{capture:(_=t.capture)!=null?_:!0,passive:!n.toValue(r)}};k(v,"pointerdown",E,T),k(h,"pointermove",O,T),k(h,"pointerup",P,T)}return{...g.toRefs(p),position:p,isDragging:n.computed(()=>!!w.value),style:n.computed(()=>`left:${p.value.x}px;top:${p.value.y}px;`)}}function An(e,t={}){var o,l;const r=n.shallowRef(!1),i=n.shallowRef(null);let s=0,a=!0;if(g.isClient){const u=typeof t=="function"?{onDrop:t}:t,f=(o=u.multiple)!=null?o:!0,c=(l=u.preventDefaultForUnhandled)!=null?l:!1,d=p=>{var w,y;const b=Array.from((y=(w=p.dataTransfer)==null?void 0:w.files)!=null?y:[]);return b.length===0?null:f?b:[b[0]]},h=p=>{const w=n.unref(u.dataTypes);return typeof w=="function"?w(p):w?.length?p.length===0?!1:p.every(y=>w.some(b=>y.includes(b))):!0},m=p=>{const w=Array.from(p??[]).map(E=>E.type),y=h(w),b=f||p.length<=1;return y&&b},v=()=>/^(?:(?!chrome|android).)*safari/i.test(navigator.userAgent)&&!("chrome"in window),S=(p,w)=>{var y,b,E,O,P,T;const _=(y=p.dataTransfer)==null?void 0:y.items;if(a=(b=_&&m(_))!=null?b:!1,c&&p.preventDefault(),!v()&&!a){p.dataTransfer&&(p.dataTransfer.dropEffect="none");return}p.preventDefault(),p.dataTransfer&&(p.dataTransfer.dropEffect="copy");const F=d(p);switch(w){case"enter":s+=1,r.value=!0,(E=u.onEnter)==null||E.call(u,null,p);break;case"over":(O=u.onOver)==null||O.call(u,null,p);break;case"leave":s-=1,s===0&&(r.value=!1),(P=u.onLeave)==null||P.call(u,null,p);break;case"drop":s=0,r.value=!1,a&&(i.value=F,(T=u.onDrop)==null||T.call(u,F,p));break}};k(e,"dragenter",p=>S(p,"enter")),k(e,"dragover",p=>S(p,"over")),k(e,"dragleave",p=>S(p,"leave")),k(e,"drop",p=>S(p,"drop"))}return{files:i,isOverDropZone:r}}function le(e,t,o={}){const{window:l=L,...r}=o;let i;const s=H(()=>l&&"ResizeObserver"in l),a=()=>{i&&(i.disconnect(),i=void 0)},u=n.computed(()=>{const d=n.toValue(e);return Array.isArray(d)?d.map(h=>W(h)):[W(d)]}),f=n.watch(u,d=>{if(a(),s.value&&l){i=new ResizeObserver(t);for(const h of d)h&&i.observe(h,r)}},{immediate:!0,flush:"post"}),c=()=>{a(),f()};return g.tryOnScopeDispose(c),{isSupported:s,stop:c}}function Mn(e,t={}){const{reset:o=!0,windowResize:l=!0,windowScroll:r=!0,immediate:i=!0,updateTiming:s="sync"}=t,a=n.shallowRef(0),u=n.shallowRef(0),f=n.shallowRef(0),c=n.shallowRef(0),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0);function S(){const w=W(e);if(!w){o&&(a.value=0,u.value=0,f.value=0,c.value=0,d.value=0,h.value=0,m.value=0,v.value=0);return}const y=w.getBoundingClientRect();a.value=y.height,u.value=y.bottom,f.value=y.left,c.value=y.right,d.value=y.top,h.value=y.width,m.value=y.x,v.value=y.y}function p(){s==="sync"?S():s==="next-frame"&&requestAnimationFrame(()=>S())}return le(e,p),n.watch(()=>W(e),w=>!w&&p()),X(e,p,{attributeFilter:["style","class"]}),r&&k("scroll",p,{capture:!0,passive:!0}),l&&k("resize",p,{passive:!0}),g.tryOnMounted(()=>{i&&p()}),{height:a,bottom:u,left:f,right:c,top:d,width:h,x:m,y:v,update:p}}function In(e){const{x:t,y:o,document:l=q,multiple:r,interval:i="requestAnimationFrame",immediate:s=!0}=e,a=H(()=>n.toValue(r)?l&&"elementsFromPoint"in l:l&&"elementFromPoint"in l),u=n.shallowRef(null),f=()=>{var d,h;u.value=n.toValue(r)?(d=l?.elementsFromPoint(n.toValue(t),n.toValue(o)))!=null?d:[]:(h=l?.elementFromPoint(n.toValue(t),n.toValue(o)))!=null?h:null},c=i==="requestAnimationFrame"?K(f,{immediate:s}):g.useIntervalFn(f,i,{immediate:s});return{isSupported:a,element:u,...c}}function Ln(e,t={}){const{delayEnter:o=0,delayLeave:l=0,triggerOnRemoval:r=!1,window:i=L}=t,s=n.shallowRef(!1);let a;const u=f=>{const c=f?o:l;a&&(clearTimeout(a),a=void 0),c?a=setTimeout(()=>s.value=f,c):s.value=f};return i&&(k(e,"mouseenter",()=>u(!0),{passive:!0}),k(e,"mouseleave",()=>u(!1),{passive:!0}),r&&we(n.computed(()=>W(e)),()=>u(!1))),s}function Ge(e,t={width:0,height:0},o={}){const{window:l=L,box:r="content-box"}=o,i=n.computed(()=>{var d,h;return(h=(d=W(e))==null?void 0:d.namespaceURI)==null?void 0:h.includes("svg")}),s=n.shallowRef(t.width),a=n.shallowRef(t.height),{stop:u}=le(e,([d])=>{const h=r==="border-box"?d.borderBoxSize:r==="content-box"?d.contentBoxSize:d.devicePixelContentBoxSize;if(l&&i.value){const m=W(e);if(m){const v=m.getBoundingClientRect();s.value=v.width,a.value=v.height}}else if(h){const m=g.toArray(h);s.value=m.reduce((v,{inlineSize:S})=>v+S,0),a.value=m.reduce((v,{blockSize:S})=>v+S,0)}else s.value=d.contentRect.width,a.value=d.contentRect.height},o);g.tryOnMounted(()=>{const d=W(e);d&&(s.value="offsetWidth"in d?d.offsetWidth:t.width,a.value="offsetHeight"in d?d.offsetHeight:t.height)});const f=n.watch(()=>W(e),d=>{s.value=d?t.width:0,a.value=d?t.height:0});function c(){u(),f()}return{width:s,height:a,stop:c}}function Ye(e,t,o={}){const{root:l,rootMargin:r="0px",threshold:i=0,window:s=L,immediate:a=!0}=o,u=H(()=>s&&"IntersectionObserver"in s),f=n.computed(()=>{const v=n.toValue(e);return g.toArray(v).map(W).filter(g.notNullish)});let c=g.noop;const d=n.shallowRef(a),h=u.value?n.watch(()=>[f.value,W(l),d.value],([v,S])=>{if(c(),!d.value||!v.length)return;const p=new IntersectionObserver(t,{root:W(S),rootMargin:r,threshold:i});v.forEach(w=>w&&p.observe(w)),c=()=>{p.disconnect(),c=g.noop}},{immediate:a,flush:"post"}):g.noop,m=()=>{c(),h(),d.value=!1};return g.tryOnScopeDispose(m),{isSupported:u,isActive:d,pause(){c(),d.value=!1},resume(){d.value=!0},stop:m}}function Xe(e,t={}){const{window:o=L,scrollTarget:l,threshold:r=0,rootMargin:i,once:s=!1}=t,a=n.shallowRef(!1),{stop:u}=Ye(e,f=>{let c=a.value,d=0;for(const h of f)h.time>=d&&(d=h.time,c=h.isIntersecting);a.value=c,s&&g.watchOnce(a,()=>{u()})},{root:l,window:o,threshold:r,rootMargin:n.toValue(i)});return a}const ae=new Map;function Nn(e){const t=n.getCurrentScope();function o(a){var u;const f=ae.get(e)||new Set;f.add(a),ae.set(e,f);const c=()=>r(a);return(u=t?.cleanups)==null||u.push(c),c}function l(a){function u(...f){r(u),a(...f)}return o(u)}function r(a){const u=ae.get(e);u&&(u.delete(a),u.size||i())}function i(){ae.delete(e)}function s(a,u){var f;(f=ae.get(e))==null||f.forEach(c=>c(a,u))}return{on:o,once:l,off:r,emit:s,reset:i}}function xn(e){return e===!0?{}:e}function Wn(e,t=[],o={}){const l=n.shallowRef(null),r=n.shallowRef(null),i=n.shallowRef("CONNECTING"),s=n.ref(null),a=n.shallowRef(null),u=g.toRef(e),f=n.shallowRef(null);let c=!1,d=0;const{withCredentials:h=!1,immediate:m=!0,autoConnect:v=!0,autoReconnect:S}=o,p=()=>{g.isClient&&s.value&&(s.value.close(),s.value=null,i.value="CLOSED",c=!0)},w=()=>{if(c||typeof u.value>"u")return;const b=new EventSource(u.value,{withCredentials:h});i.value="CONNECTING",s.value=b,b.onopen=()=>{i.value="OPEN",a.value=null},b.onerror=E=>{if(i.value="CLOSED",a.value=E,b.readyState===2&&!c&&S){b.close();const{retries:O=-1,delay:P=1e3,onFailed:T}=xn(S);d+=1,typeof O=="number"&&(O<0||d<O)||typeof O=="function"&&O()?setTimeout(w,P):T?.()}},b.onmessage=E=>{l.value=null,r.value=E.data,f.value=E.lastEventId};for(const E of t)k(b,E,O=>{l.value=E,r.value=O.data||null,f.value=O.lastEventId||null},{passive:!0})},y=()=>{g.isClient&&(p(),c=!1,d=0,w())};return m&&y(),v&&n.watch(u,y),g.tryOnScopeDispose(p),{eventSource:s,event:l,data:r,status:i,error:a,open:y,close:p,lastEventId:f}}function Hn(e={}){const{initialValue:t=""}=e,o=H(()=>typeof window<"u"&&"EyeDropper"in window),l=n.shallowRef(t);async function r(i){if(!o.value)return;const a=await new window.EyeDropper().open(i);return l.value=a.sRGBHex,a}return{isSupported:o,sRGBHex:l,open:r}}function Un(e=null,t={}){const{baseUrl:o="",rel:l="icon",document:r=q}=t,i=g.toRef(e),s=a=>{const u=r?.head.querySelectorAll(`link[rel*="${l}"]`);if(!u||u.length===0){const f=r?.createElement("link");f&&(f.rel=l,f.href=`${o}${a}`,f.type=`image/${a.split(".").pop()}`,r?.head.append(f));return}u?.forEach(f=>f.href=`${o}${a}`)};return n.watch(i,(a,u)=>{typeof a=="string"&&a!==u&&s(a)},{immediate:!0}),i}const $n={json:"application/json",text:"text/plain"};function ve(e){return e&&g.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}const Bn=/^(?:[a-z][a-z\d+\-.]*:)?\/\//i;function jn(e){return Bn.test(e)}function re(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries(e.entries()):e}function Z(e,...t){return e==="overwrite"?async o=>{let l;for(let r=t.length-1;r>=0;r--)if(t[r]!=null){l=t[r];break}return l?{...o,...await l(o)}:o}:async o=>{for(const l of t)l&&(o={...o,...await l(o)});return o}}function zn(e={}){const t=e.combination||"chain",o=e.options||{},l=e.fetchOptions||{};function r(i,...s){const a=n.computed(()=>{const c=n.toValue(e.baseUrl),d=n.toValue(i);return c&&!jn(d)?qn(c,d):d});let u=o,f=l;return s.length>0&&(ve(s[0])?u={...u,...s[0],beforeFetch:Z(t,o.beforeFetch,s[0].beforeFetch),afterFetch:Z(t,o.afterFetch,s[0].afterFetch),onFetchError:Z(t,o.onFetchError,s[0].onFetchError)}:f={...f,...s[0],headers:{...re(f.headers)||{},...re(s[0].headers)||{}}}),s.length>1&&ve(s[1])&&(u={...u,...s[1],beforeFetch:Z(t,o.beforeFetch,s[1].beforeFetch),afterFetch:Z(t,o.afterFetch,s[1].afterFetch),onFetchError:Z(t,o.onFetchError,s[1].onFetchError)}),Ke(a,f,u)}return r}function Ke(e,...t){var o,l;const r=typeof AbortController=="function";let i={},s={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const a={method:"GET",type:"text",payload:void 0};t.length>0&&(ve(t[0])?s={...s,...t[0]}:i=t[0]),t.length>1&&ve(t[1])&&(s={...s,...t[1]});const{fetch:u=(l=(o=L)==null?void 0:o.fetch)!=null?l:globalThis?.fetch,initialData:f,timeout:c}=s,d=g.createEventHook(),h=g.createEventHook(),m=g.createEventHook(),v=n.shallowRef(!1),S=n.shallowRef(!1),p=n.shallowRef(!1),w=n.shallowRef(null),y=n.shallowRef(null),b=n.shallowRef(null),E=n.shallowRef(f||null),O=n.computed(()=>r&&S.value);let P,T;const _=I=>{r&&(P?.abort(I),P=new AbortController,P.signal.onabort=()=>p.value=!0,i={...i,signal:P.signal})},F=I=>{S.value=I,v.value=!I};c&&(T=g.useTimeoutFn(_,c,{immediate:!1}));let A=0;const M=async(I=!1)=>{var U,B;_(),F(!0),b.value=null,w.value=null,p.value=!1,A+=1;const j=A,Y={method:a.method,headers:{}},ee=n.toValue(a.payload);if(ee){const z=re(Y.headers),ie=Object.getPrototypeOf(ee);!a.payloadType&&ee&&(ie===Object.prototype||Array.isArray(ie))&&!(ee instanceof FormData)&&(a.payloadType="json"),a.payloadType&&(z["Content-Type"]=(U=$n[a.payloadType])!=null?U:a.payloadType),Y.body=a.payloadType==="json"?JSON.stringify(ee):ee}let Ot=!1;const J={url:n.toValue(e),options:{...Y,...i},cancel:()=>{Ot=!0}};if(s.beforeFetch&&Object.assign(J,await s.beforeFetch(J)),Ot||!u)return F(!1),Promise.resolve(null);let Q=null;return T&&T.start(),u(J.url,{...Y,...J.options,headers:{...re(Y.headers),...re((B=J.options)==null?void 0:B.headers)}}).then(async z=>{if(y.value=z,w.value=z.status,Q=await z.clone()[a.type](),!z.ok)throw E.value=f||null,new Error(z.statusText);return s.afterFetch&&({data:Q}=await s.afterFetch({data:Q,response:z,context:J,execute:M})),E.value=Q,d.trigger(z),z}).catch(async z=>{let ie=z.message||z.name;if(s.onFetchError&&({error:ie,data:Q}=await s.onFetchError({data:Q,error:z,response:y.value,context:J,execute:M})),b.value=ie,s.updateDataOnError&&(E.value=Q),h.trigger(z),I)throw z;return null}).finally(()=>{j===A&&F(!1),T&&T.stop(),m.trigger(null)})},V=g.toRef(s.refetch);n.watch([V,g.toRef(e)],([I])=>I&&M(),{deep:!0});const D={isFinished:n.readonly(v),isFetching:n.readonly(S),statusCode:w,response:y,error:b,data:E,canAbort:O,aborted:p,abort:_,execute:M,onFetchResponse:d.on,onFetchError:h.on,onFetchFinally:m.on,get:C("GET"),put:C("PUT"),post:C("POST"),delete:C("DELETE"),patch:C("PATCH"),head:C("HEAD"),options:C("OPTIONS"),json:x("json"),text:x("text"),blob:x("blob"),arrayBuffer:x("arrayBuffer"),formData:x("formData")};function C(I){return(U,B)=>{if(!S.value)return a.method=I,a.payload=U,a.payloadType=B,n.isRef(a.payload)&&n.watch([V,g.toRef(a.payload)],([j])=>j&&M(),{deep:!0}),{...D,then(j,Y){return N().then(j,Y)}}}}function N(){return new Promise((I,U)=>{g.until(v).toBe(!0).then(()=>I(D)).catch(U)})}function x(I){return()=>{if(!S.value)return a.type=I,{...D,then(U,B){return N().then(U,B)}}}}return s.immediate&&Promise.resolve().then(()=>M()),{...D,then(I,U){return N().then(I,U)}}}function qn(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:e.endsWith("/")&&t.startsWith("/")?`${e.slice(0,-1)}${t}`:`${e}${t}`}const Gn={multiple:!0,accept:"*",reset:!1,directory:!1};function Yn(e){if(!e)return null;if(e instanceof FileList)return e;const t=new DataTransfer;for(const o of e)t.items.add(o);return t.files}function Xn(e={}){const{document:t=q}=e,o=n.ref(Yn(e.initialFiles)),{on:l,trigger:r}=g.createEventHook(),{on:i,trigger:s}=g.createEventHook();let a;t&&(a=W(e.input)||t.createElement("input"),a.type="file",a.onchange=c=>{const d=c.target;o.value=d.files,r(o.value)},a.oncancel=()=>{s()});const u=()=>{o.value=null,a&&a.value&&(a.value="",r(null))},f=c=>{if(!a)return;const d={...Gn,...e,...c};a.multiple=d.multiple,a.accept=d.accept,a.webkitdirectory=d.directory,g.hasOwn(d,"capture")&&(a.capture=d.capture),d.reset&&u(),a.click()};return{files:n.readonly(o),open:f,reset:u,onCancel:i,onChange:l}}function Kn(e={}){const{window:t=L,dataType:o="Text"}=e,l=t,r=H(()=>l&&"showSaveFilePicker"in l&&"showOpenFilePicker"in l),i=n.shallowRef(),s=n.shallowRef(),a=n.shallowRef(),u=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.name)!=null?b:""}),f=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.type)!=null?b:""}),c=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.size)!=null?b:0}),d=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.lastModified)!=null?b:0});async function h(y={}){if(!r.value)return;const[b]=await l.showOpenFilePicker({...n.toValue(e),...y});i.value=b,await w()}async function m(y={}){r.value&&(i.value=await l.showSaveFilePicker({...e,...y}),s.value=void 0,await w())}async function v(y={}){if(r.value){if(!i.value)return S(y);if(s.value){const b=await i.value.createWritable();await b.write(s.value),await b.close()}await p()}}async function S(y={}){if(r.value){if(i.value=await l.showSaveFilePicker({...e,...y}),s.value){const b=await i.value.createWritable();await b.write(s.value),await b.close()}await p()}}async function p(){var y;a.value=await((y=i.value)==null?void 0:y.getFile())}async function w(){var y,b;await p();const E=n.toValue(o);E==="Text"?s.value=await((y=a.value)==null?void 0:y.text()):E==="ArrayBuffer"?s.value=await((b=a.value)==null?void 0:b.arrayBuffer()):E==="Blob"&&(s.value=a.value)}return n.watch(()=>n.toValue(o),w),{isSupported:r,data:s,file:a,fileName:u,fileMIME:f,fileSize:c,fileLastModified:d,open:h,create:m,save:v,saveAs:S,updateData:w}}function Jn(e,t={}){const{initialValue:o=!1,focusVisible:l=!1,preventScroll:r=!1}=t,i=n.shallowRef(!1),s=n.computed(()=>W(e)),a={passive:!0};k(s,"focus",f=>{var c,d;(!l||(d=(c=f.target).matches)!=null&&d.call(c,":focus-visible"))&&(i.value=!0)},a),k(s,"blur",()=>i.value=!1,a);const u=n.computed({get:()=>i.value,set(f){var c,d;!f&&i.value?(c=s.value)==null||c.blur():f&&!i.value&&((d=s.value)==null||d.focus({preventScroll:r}))}});return n.watch(s,()=>{u.value=o},{immediate:!0,flush:"post"}),{focused:u}}const Qn="focusin",Zn="focusout",eo=":focus-within";function to(e,t={}){const{window:o=L}=t,l=n.computed(()=>W(e)),r=n.shallowRef(!1),i=n.computed(()=>r.value),s=Me(t);if(!o||!s.value)return{focused:i};const a={passive:!0};return k(l,Qn,()=>r.value=!0,a),k(l,Zn,()=>{var u,f,c;return r.value=(c=(f=(u=l.value)==null?void 0:u.matches)==null?void 0:f.call(u,eo))!=null?c:!1},a),{focused:i}}function no(e){var t;const o=n.shallowRef(0);if(typeof performance>"u")return o;const l=(t=e?.every)!=null?t:10;let r=performance.now(),i=0;return K(()=>{if(i+=1,i>=l){const s=performance.now(),a=s-r;o.value=Math.round(1e3/(a/i)),r=s,i=0}}),o}const Je=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function oo(e,t={}){const{document:o=q,autoExit:l=!1}=t,r=n.computed(()=>{var y;return(y=W(e))!=null?y:o?.documentElement}),i=n.shallowRef(!1),s=n.computed(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(y=>o&&y in o||r.value&&y in r.value)),a=n.computed(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(y=>o&&y in o||r.value&&y in r.value)),u=n.computed(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(y=>o&&y in o||r.value&&y in r.value)),f=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(y=>o&&y in o),c=H(()=>r.value&&o&&s.value!==void 0&&a.value!==void 0&&u.value!==void 0),d=()=>f?o?.[f]===r.value:!1,h=()=>{if(u.value){if(o&&o[u.value]!=null)return o[u.value];{const y=r.value;if(y?.[u.value]!=null)return!!y[u.value]}}return!1};async function m(){if(!(!c.value||!i.value)){if(a.value)if(o?.[a.value]!=null)await o[a.value]();else{const y=r.value;y?.[a.value]!=null&&await y[a.value]()}i.value=!1}}async function v(){if(!c.value||i.value)return;h()&&await m();const y=r.value;s.value&&y?.[s.value]!=null&&(await y[s.value](),i.value=!0)}async function S(){await(i.value?m():v())}const p=()=>{const y=h();(!y||y&&d())&&(i.value=y)},w={capture:!1,passive:!0};return k(o,Je,p,w),k(()=>W(r),Je,p,w),g.tryOnMounted(p,!1),l&&g.tryOnScopeDispose(m),{isSupported:c,isFullscreen:i,enter:v,exit:m,toggle:S}}function lo(e){return n.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function ao(e={}){const{navigator:t=G}=e,o=H(()=>t&&"getGamepads"in t),l=n.ref([]),r=g.createEventHook(),i=g.createEventHook(),s=v=>{const S=[],p="vibrationActuator"in v?v.vibrationActuator:null;return p&&S.push(p),v.hapticActuators&&S.push(...v.hapticActuators),{id:v.id,index:v.index,connected:v.connected,mapping:v.mapping,timestamp:v.timestamp,vibrationActuator:v.vibrationActuator,hapticActuators:S,axes:v.axes.map(w=>w),buttons:v.buttons.map(w=>({pressed:w.pressed,touched:w.touched,value:w.value}))}},a=()=>{const v=t?.getGamepads()||[];for(const S of v)S&&l.value[S.index]&&(l.value[S.index]=s(S))},{isActive:u,pause:f,resume:c}=K(a),d=v=>{l.value.some(({index:S})=>S===v.index)||(l.value.push(s(v)),r.trigger(v.index)),c()},h=v=>{l.value=l.value.filter(S=>S.index!==v.index),i.trigger(v.index)},m={passive:!0};return k("gamepadconnected",v=>d(v.gamepad),m),k("gamepaddisconnected",v=>h(v.gamepad),m),g.tryOnMounted(()=>{const v=t?.getGamepads()||[];for(const S of v)S&&l.value[S.index]&&d(S)}),f(),{isSupported:o,onConnected:r.on,onDisconnected:i.on,gamepads:l,pause:f,resume:c,isActive:u}}function ro(e={}){const{enableHighAccuracy:t=!0,maximumAge:o=3e4,timeout:l=27e3,navigator:r=G,immediate:i=!0}=e,s=H(()=>r&&"geolocation"in r),a=n.shallowRef(null),u=n.shallowRef(null),f=n.ref({accuracy:0,latitude:Number.POSITIVE_INFINITY,longitude:Number.POSITIVE_INFINITY,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function c(v){a.value=v.timestamp,f.value=v.coords,u.value=null}let d;function h(){s.value&&(d=r.geolocation.watchPosition(c,v=>u.value=v,{enableHighAccuracy:t,maximumAge:o,timeout:l}))}i&&h();function m(){d&&r&&r.geolocation.clearWatch(d)}return g.tryOnScopeDispose(()=>{m()}),{isSupported:s,coords:f,locatedAt:a,error:u,resume:h,pause:m}}const io=["mousemove","mousedown","resize","keydown","touchstart","wheel"],so=6e4;function uo(e=so,t={}){const{initialState:o=!1,listenForVisibilityChange:l=!0,events:r=io,window:i=L,eventFilter:s=g.throttleFilter(50)}=t,a=n.shallowRef(o),u=n.shallowRef(g.timestamp());let f;const c=()=>{a.value=!1,clearTimeout(f),f=setTimeout(()=>a.value=!0,e)},d=g.createFilterWrapper(s,()=>{u.value=g.timestamp(),c()});if(i){const h=i.document,m={passive:!0};for(const v of r)k(i,v,d,m);l&&k(h,"visibilitychange",()=>{h.hidden||d()},m),o||c()}return{idle:a,lastActive:u,reset:c}}async function co(e){return new Promise((t,o)=>{const l=new Image,{src:r,srcset:i,sizes:s,class:a,loading:u,crossorigin:f,referrerPolicy:c,width:d,height:h,decoding:m,fetchPriority:v,ismap:S,usemap:p}=e;l.src=r,i!=null&&(l.srcset=i),s!=null&&(l.sizes=s),a!=null&&(l.className=a),u!=null&&(l.loading=u),f!=null&&(l.crossOrigin=f),c!=null&&(l.referrerPolicy=c),d!=null&&(l.width=d),h!=null&&(l.height=h),m!=null&&(l.decoding=m),v!=null&&(l.fetchPriority=v),S!=null&&(l.isMap=S),p!=null&&(l.useMap=p),l.onload=()=>t(l),l.onerror=o})}function fo(e,t={}){const o=Ie(()=>co(n.toValue(e)),void 0,{resetOnExecute:!0,...t});return n.watch(()=>n.toValue(e),()=>o.execute(t.delay),{deep:!0}),o}function pe(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}const Qe=1;function Oe(e,t={}){const{throttle:o=0,idle:l=200,onStop:r=g.noop,onScroll:i=g.noop,offset:s={left:0,right:0,top:0,bottom:0},observe:a={mutation:!1},eventListenerOptions:u={capture:!1,passive:!0},behavior:f="auto",window:c=L,onError:d=F=>{console.error(F)}}=t,h=typeof a=="boolean"?{mutation:a}:a,m=n.shallowRef(0),v=n.shallowRef(0),S=n.computed({get(){return m.value},set(F){w(F,void 0)}}),p=n.computed({get(){return v.value},set(F){w(void 0,F)}});function w(F,A){var M,V,D,C;if(!c)return;const N=n.toValue(e);if(!N)return;(D=N instanceof Document?c.document.body:N)==null||D.scrollTo({top:(M=n.toValue(A))!=null?M:p.value,left:(V=n.toValue(F))!=null?V:S.value,behavior:n.toValue(f)});const x=((C=N?.document)==null?void 0:C.documentElement)||N?.documentElement||N;S!=null&&(m.value=x.scrollLeft),p!=null&&(v.value=x.scrollTop)}const y=n.shallowRef(!1),b=n.reactive({left:!0,right:!1,top:!0,bottom:!1}),E=n.reactive({left:!1,right:!1,top:!1,bottom:!1}),O=F=>{y.value&&(y.value=!1,E.left=!1,E.right=!1,E.top=!1,E.bottom=!1,r(F))},P=g.useDebounceFn(O,o+l),T=F=>{var A;if(!c)return;const M=((A=F?.document)==null?void 0:A.documentElement)||F?.documentElement||W(F),{display:V,flexDirection:D,direction:C}=getComputedStyle(M),N=C==="rtl"?-1:1,x=M.scrollLeft;E.left=x<m.value,E.right=x>m.value;const I=Math.abs(x*N)<=(s.left||0),U=Math.abs(x*N)+M.clientWidth>=M.scrollWidth-(s.right||0)-Qe;V==="flex"&&D==="row-reverse"?(b.left=U,b.right=I):(b.left=I,b.right=U),m.value=x;let B=M.scrollTop;F===c.document&&!B&&(B=c.document.body.scrollTop),E.top=B<v.value,E.bottom=B>v.value;const j=Math.abs(B)<=(s.top||0),Y=Math.abs(B)+M.clientHeight>=M.scrollHeight-(s.bottom||0)-Qe;V==="flex"&&D==="column-reverse"?(b.top=Y,b.bottom=j):(b.top=j,b.bottom=Y),v.value=B},_=F=>{var A;if(!c)return;const M=(A=F.target.documentElement)!=null?A:F.target;T(M),y.value=!0,P(F),i(F)};return k(e,"scroll",o?g.useThrottleFn(_,o,!0,!1):_,u),g.tryOnMounted(()=>{try{const F=n.toValue(e);if(!F)return;T(F)}catch(F){d(F)}}),h?.mutation&&e!=null&&e!==c&&e!==document&&X(e,()=>{const F=n.toValue(e);F&&T(F)},{attributes:!0,childList:!0,subtree:!0}),k(e,"scrollend",O,u),{x:S,y:p,isScrolling:y,arrivedState:b,directions:E,measure(){const F=n.toValue(e);c&&F&&T(F)}}}function mo(e,t,o={}){var l;const{direction:r="bottom",interval:i=100,canLoadMore:s=()=>!0}=o,a=n.reactive(Oe(e,{...o,offset:{[r]:(l=o.distance)!=null?l:0,...o.offset}})),u=n.ref(),f=n.computed(()=>!!u.value),c=n.computed(()=>pe(n.toValue(e))),d=Xe(c);function h(){if(a.measure(),!c.value||!d.value||!s(c.value))return;const{scrollHeight:v,clientHeight:S,scrollWidth:p,clientWidth:w}=c.value,y=r==="bottom"||r==="top"?v<=S:p<=w;(a.arrivedState[r]||y)&&(u.value||(u.value=Promise.all([t(a),new Promise(b=>setTimeout(b,i))]).finally(()=>{u.value=null,n.nextTick(()=>h())})))}const m=n.watch(()=>[a.arrivedState[r],d.value],h,{immediate:!0});return g.tryOnUnmounted(m),{isLoading:f,reset(){n.nextTick(()=>h())}}}const vo=["mousedown","mouseup","keydown","keyup"];function po(e,t={}){const{events:o=vo,document:l=q,initial:r=null}=t,i=n.shallowRef(r);return l&&o.forEach(s=>{k(l,s,a=>{typeof a.getModifierState=="function"&&(i.value=a.getModifierState(e))},{passive:!0})}),i}function ho(e,t,o={}){const{window:l=L}=o;return me(e,t,l?.localStorage,o)}const Ze={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function yo(e={}){const{reactive:t=!1,target:o=L,aliasMap:l=Ze,passive:r=!0,onEventFired:i=g.noop}=e,s=n.reactive(new Set),a={toJSON(){return{}},current:s},u=t?n.reactive(a):a,f=new Set,c=new Set,d=new Set;function h(p,w){p in u&&(t?u[p]=w:u[p].value=w)}function m(){s.clear();for(const p of d)h(p,!1)}function v(p,w){var y,b;const E=(y=p.key)==null?void 0:y.toLowerCase(),P=[(b=p.code)==null?void 0:b.toLowerCase(),E].filter(Boolean);E&&(w?s.add(E):s.delete(E));for(const T of P)d.add(T),h(T,w);E==="shift"&&!w?(c.forEach(T=>{s.delete(T),h(T,!1)}),c.clear()):typeof p.getModifierState=="function"&&p.getModifierState("Shift")&&w&&[...s,...P].forEach(T=>c.add(T)),E==="meta"&&!w?(f.forEach(T=>{s.delete(T),h(T,!1)}),f.clear()):typeof p.getModifierState=="function"&&p.getModifierState("Meta")&&w&&[...s,...P].forEach(T=>f.add(T))}k(o,"keydown",p=>(v(p,!0),i(p)),{passive:r}),k(o,"keyup",p=>(v(p,!1),i(p)),{passive:r}),k("blur",m,{passive:r}),k("focus",m,{passive:r});const S=new Proxy(u,{get(p,w,y){if(typeof w!="string")return Reflect.get(p,w,y);if(w=w.toLowerCase(),w in l&&(w=l[w]),!(w in u))if(/[+_-]/.test(w)){const E=w.split(/[+_-]/g).map(O=>O.trim());u[w]=n.computed(()=>E.map(O=>n.toValue(S[O])).every(Boolean))}else u[w]=n.shallowRef(!1);const b=Reflect.get(p,w,y);return t?n.toValue(b):b}});return S}function ke(e,t){n.toValue(e)&&t(n.toValue(e))}function wo(e){let t=[];for(let o=0;o<e.length;++o)t=[...t,[e.start(o),e.end(o)]];return t}function _e(e){return Array.from(e).map(({label:t,kind:o,language:l,mode:r,activeCues:i,cues:s,inBandMetadataTrackDispatchType:a},u)=>({id:u,label:t,kind:o,language:l,mode:r,activeCues:i,cues:s,inBandMetadataTrackDispatchType:a}))}const go={src:"",tracks:[]};function bo(e,t={}){e=g.toRef(e),t={...go,...t};const{document:o=q}=t,l={passive:!0},r=n.shallowRef(0),i=n.shallowRef(0),s=n.shallowRef(!1),a=n.shallowRef(1),u=n.shallowRef(!1),f=n.shallowRef(!1),c=n.shallowRef(!1),d=n.shallowRef(1),h=n.shallowRef(!1),m=n.ref([]),v=n.ref([]),S=n.shallowRef(-1),p=n.shallowRef(!1),w=n.shallowRef(!1),y=o&&"pictureInPictureEnabled"in o,b=g.createEventHook(),E=g.createEventHook(),O=V=>{ke(e,D=>{if(V){const C=typeof V=="number"?V:V.id;D.textTracks[C].mode="disabled"}else for(let C=0;C<D.textTracks.length;++C)D.textTracks[C].mode="disabled";S.value=-1})},P=(V,D=!0)=>{ke(e,C=>{const N=typeof V=="number"?V:V.id;D&&O(),C.textTracks[N].mode="showing",S.value=N})},T=()=>new Promise((V,D)=>{ke(e,async C=>{y&&(p.value?o.exitPictureInPicture().then(V).catch(D):C.requestPictureInPicture().then(V).catch(D))})});n.watchEffect(()=>{if(!o)return;const V=n.toValue(e);if(!V)return;const D=n.toValue(t.src);let C=[];D&&(typeof D=="string"?C=[{src:D}]:Array.isArray(D)?C=D:g.isObject(D)&&(C=[D]),V.querySelectorAll("source").forEach(N=>{N.remove()}),C.forEach(({src:N,type:x,media:I})=>{const U=o.createElement("source");U.setAttribute("src",N),U.setAttribute("type",x||""),U.setAttribute("media",I||""),k(U,"error",b.trigger,l),V.appendChild(U)}),V.load())}),n.watch([e,a],()=>{const V=n.toValue(e);V&&(V.volume=a.value)}),n.watch([e,w],()=>{const V=n.toValue(e);V&&(V.muted=w.value)}),n.watch([e,d],()=>{const V=n.toValue(e);V&&(V.playbackRate=d.value)}),n.watchEffect(()=>{if(!o)return;const V=n.toValue(t.tracks),D=n.toValue(e);!V||!V.length||!D||(D.querySelectorAll("track").forEach(C=>C.remove()),V.forEach(({default:C,kind:N,label:x,src:I,srcLang:U},B)=>{const j=o.createElement("track");j.default=C||!1,j.kind=N,j.label=x,j.src=I,j.srclang=U,j.default&&(S.value=B),D.appendChild(j)}))});const{ignoreUpdates:_}=g.watchIgnorable(r,V=>{const D=n.toValue(e);D&&(D.currentTime=V)}),{ignoreUpdates:F}=g.watchIgnorable(c,V=>{const D=n.toValue(e);D&&(V?D.play().catch(C=>{throw E.trigger(C),C}):D.pause())});k(e,"timeupdate",()=>_(()=>r.value=n.toValue(e).currentTime),l),k(e,"durationchange",()=>i.value=n.toValue(e).duration,l),k(e,"progress",()=>m.value=wo(n.toValue(e).buffered),l),k(e,"seeking",()=>s.value=!0,l),k(e,"seeked",()=>s.value=!1,l),k(e,["waiting","loadstart"],()=>{u.value=!0,F(()=>c.value=!1)},l),k(e,"loadeddata",()=>u.value=!1,l),k(e,"playing",()=>{u.value=!1,f.value=!1,F(()=>c.value=!0)},l),k(e,"ratechange",()=>d.value=n.toValue(e).playbackRate,l),k(e,"stalled",()=>h.value=!0,l),k(e,"ended",()=>f.value=!0,l),k(e,"pause",()=>F(()=>c.value=!1),l),k(e,"play",()=>F(()=>c.value=!0),l),k(e,"enterpictureinpicture",()=>p.value=!0,l),k(e,"leavepictureinpicture",()=>p.value=!1,l),k(e,"volumechange",()=>{const V=n.toValue(e);V&&(a.value=V.volume,w.value=V.muted)},l);const A=[],M=n.watch([e],()=>{const V=n.toValue(e);V&&(M(),A[0]=k(V.textTracks,"addtrack",()=>v.value=_e(V.textTracks),l),A[1]=k(V.textTracks,"removetrack",()=>v.value=_e(V.textTracks),l),A[2]=k(V.textTracks,"change",()=>v.value=_e(V.textTracks),l))});return g.tryOnScopeDispose(()=>A.forEach(V=>V())),{currentTime:r,duration:i,waiting:u,seeking:s,ended:f,stalled:h,buffered:m,playing:c,rate:d,volume:a,muted:w,tracks:v,selectedTrack:S,enableTrack:P,disableTrack:O,supportsPictureInPicture:y,togglePictureInPicture:T,isPictureInPicture:p,onSourceError:b.on,onPlaybackError:E.on}}function So(e,t){const l=t?.cache?n.shallowReactive(t.cache):n.shallowReactive(new Map),r=(...c)=>t?.getKey?t.getKey(...c):JSON.stringify(c),i=(c,...d)=>(l.set(c,e(...d)),l.get(c)),s=(...c)=>i(r(...c),...c),a=(...c)=>{l.delete(r(...c))},u=()=>{l.clear()},f=(...c)=>{const d=r(...c);return l.has(d)?l.get(d):i(d,...c)};return f.load=s,f.delete=a,f.clear=u,f.generateKey=r,f.cache=l,f}function Ro(e={}){const t=n.ref(),o=H(()=>typeof performance<"u"&&"memory"in performance);if(o.value){const{interval:l=1e3}=e;g.useIntervalFn(()=>{t.value=performance.memory},l,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:o,memory:t}}const Eo={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof MouseEvent?[e.movementX,e.movementY]:null};function et(e={}){const{type:t="page",touch:o=!0,resetOnTouchEnds:l=!1,initialValue:r={x:0,y:0},window:i=L,target:s=i,scroll:a=!0,eventFilter:u}=e;let f=null,c=0,d=0;const h=n.shallowRef(r.x),m=n.shallowRef(r.y),v=n.shallowRef(null),S=typeof t=="function"?t:Eo[t],p=T=>{const _=S(T);f=T,_&&([h.value,m.value]=_,v.value="mouse"),i&&(c=i.scrollX,d=i.scrollY)},w=T=>{if(T.touches.length>0){const _=S(T.touches[0]);_&&([h.value,m.value]=_,v.value="touch")}},y=()=>{if(!f||!i)return;const T=S(f);f instanceof MouseEvent&&T&&(h.value=T[0]+i.scrollX-c,m.value=T[1]+i.scrollY-d)},b=()=>{h.value=r.x,m.value=r.y},E=u?T=>u(()=>p(T),{}):T=>p(T),O=u?T=>u(()=>w(T),{}):T=>w(T),P=u?()=>u(()=>y(),{}):()=>y();if(s){const T={passive:!0};k(s,["mousemove","dragover"],E,T),o&&t!=="movement"&&(k(s,["touchstart","touchmove"],O,T),l&&k(s,"touchend",b,T)),a&&t==="page"&&k(i,"scroll",P,T)}return{x:h,y:m,sourceType:v}}function tt(e,t={}){const{windowResize:o=!0,windowScroll:l=!0,handleOutside:r=!0,window:i=L}=t,s=t.type||"page",{x:a,y:u,sourceType:f}=et(t),c=n.shallowRef(e??i?.document.body),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0),S=n.shallowRef(0),p=n.shallowRef(0),w=n.shallowRef(!0);function y(){if(!i)return;const O=W(c);if(!O||!(O instanceof Element))return;const{left:P,top:T,width:_,height:F}=O.getBoundingClientRect();m.value=P+(s==="page"?i.pageXOffset:0),v.value=T+(s==="page"?i.pageYOffset:0),S.value=F,p.value=_;const A=a.value-m.value,M=u.value-v.value;w.value=_===0||F===0||A<0||M<0||A>_||M>F,r&&(d.value=A,h.value=M)}const b=[];function E(){b.forEach(O=>O()),b.length=0}if(g.tryOnMounted(()=>{y()}),i){const{stop:O}=le(c,y),{stop:P}=X(c,y,{attributeFilter:["style","class"]}),T=n.watch([c,a,u],y);b.push(O,P,T),k(document,"mouseleave",()=>w.value=!0,{passive:!0}),l&&b.push(k("scroll",y,{capture:!0,passive:!0})),o&&b.push(k("resize",y,{passive:!0}))}return{x:a,y:u,sourceType:f,elementX:d,elementY:h,elementPositionX:m,elementPositionY:v,elementHeight:S,elementWidth:p,isOutside:w,stop:E}}function To(e={}){const{touch:t=!0,drag:o=!0,capture:l=!1,initialValue:r=!1,window:i=L}=e,s=n.shallowRef(r),a=n.shallowRef(null);if(!i)return{pressed:s,sourceType:a};const u=h=>m=>{var v;s.value=!0,a.value=h,(v=e.onPressed)==null||v.call(e,m)},f=h=>{var m;s.value=!1,a.value=null,(m=e.onReleased)==null||m.call(e,h)},c=n.computed(()=>W(e.target)||i),d={passive:!0,capture:l};return k(c,"mousedown",u("mouse"),d),k(i,"mouseleave",f,d),k(i,"mouseup",f,d),o&&(k(c,"dragstart",u("mouse"),d),k(i,"drop",f,d),k(i,"dragend",f,d)),t&&(k(c,"touchstart",u("touch"),d),k(i,"touchend",f,d),k(i,"touchcancel",f,d)),{pressed:s,sourceType:a}}function Oo(e={}){const{window:t=L}=e,o=t?.navigator,l=H(()=>o&&"language"in o),r=n.shallowRef(o?.language);return k(t,"languagechange",()=>{o&&(r.value=o.language)},{passive:!0}),{isSupported:l,language:r}}function nt(e={}){const{window:t=L}=e,o=t?.navigator,l=H(()=>o&&"connection"in o),r=n.shallowRef(!0),i=n.shallowRef(!1),s=n.shallowRef(void 0),a=n.shallowRef(void 0),u=n.shallowRef(void 0),f=n.shallowRef(void 0),c=n.shallowRef(void 0),d=n.shallowRef(void 0),h=n.shallowRef("unknown"),m=l.value&&o.connection;function v(){o&&(r.value=o.onLine,s.value=r.value?void 0:Date.now(),a.value=r.value?Date.now():void 0,m&&(u.value=m.downlink,f.value=m.downlinkMax,d.value=m.effectiveType,c.value=m.rtt,i.value=m.saveData,h.value=m.type))}const S={passive:!0};return t&&(k(t,"offline",()=>{r.value=!1,s.value=Date.now()},S),k(t,"online",()=>{r.value=!0,a.value=Date.now()},S)),m&&k(m,"change",v,S),v(),{isSupported:l,isOnline:n.readonly(r),saveData:n.readonly(i),offlineAt:n.readonly(s),onlineAt:n.readonly(a),downlink:n.readonly(u),downlinkMax:n.readonly(f),effectiveType:n.readonly(d),rtt:n.readonly(c),type:n.readonly(h)}}function ot(e={}){const{controls:t=!1,interval:o="requestAnimationFrame",immediate:l=!0}=e,r=n.ref(new Date),i=()=>r.value=new Date,s=o==="requestAnimationFrame"?K(i,{immediate:l}):g.useIntervalFn(i,o,{immediate:l});return t?{now:r,...s}:r}function ko(e){const t=n.shallowRef(),o=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return n.watch(()=>n.toValue(e),l=>{o(),l&&(t.value=URL.createObjectURL(l))},{immediate:!0}),g.tryOnScopeDispose(o),n.readonly(t)}function lt(e,t,o){if(typeof e=="function"||n.isReadonly(e))return n.computed(()=>g.clamp(n.toValue(e),n.toValue(t),n.toValue(o)));const l=n.ref(e);return n.computed({get(){return l.value=g.clamp(l.value,n.toValue(t),n.toValue(o))},set(r){l.value=g.clamp(r,n.toValue(t),n.toValue(o))}})}function _o(e){const{total:t=Number.POSITIVE_INFINITY,pageSize:o=10,page:l=1,onPageChange:r=g.noop,onPageSizeChange:i=g.noop,onPageCountChange:s=g.noop}=e,a=lt(o,1,Number.POSITIVE_INFINITY),u=n.computed(()=>Math.max(1,Math.ceil(n.toValue(t)/n.toValue(a)))),f=lt(l,1,u),c=n.computed(()=>f.value===1),d=n.computed(()=>f.value===u.value);n.isRef(l)&&g.syncRef(l,f,{direction:n.isReadonly(l)?"ltr":"both"}),n.isRef(o)&&g.syncRef(o,a,{direction:n.isReadonly(o)?"ltr":"both"});function h(){f.value--}function m(){f.value++}const v={currentPage:f,currentPageSize:a,pageCount:u,isFirstPage:c,isLastPage:d,prev:h,next:m};return n.watch(f,()=>{r(n.reactive(v))}),n.watch(a,()=>{i(n.reactive(v))}),n.watch(u,()=>{s(n.reactive(v))}),v}function Vo(e={}){const{isOnline:t}=nt(e);return t}function Fo(e={}){const{window:t=L}=e,o=n.shallowRef(!1),l=r=>{if(!t)return;r=r||t.event;const i=r.relatedTarget||r.toElement;o.value=!i};if(t){const r={passive:!0};k(t,"mouseout",l,r),k(t.document,"mouseleave",l,r),k(t.document,"mouseenter",l,r)}return o}function at(e={}){const{window:t=L}=e,o=H(()=>t&&"screen"in t&&"orientation"in t.screen),l=o.value?t.screen.orientation:{},r=n.ref(l.type),i=n.shallowRef(l.angle||0);return o.value&&k(t,"orientationchange",()=>{r.value=l.type,i.value=l.angle},{passive:!0}),{isSupported:o,orientation:r,angle:i,lockOrientation:u=>o.value&&typeof l.lock=="function"?l.lock(u):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{o.value&&typeof l.unlock=="function"&&l.unlock()}}}function Po(e,t={}){const{deviceOrientationTiltAdjust:o=p=>p,deviceOrientationRollAdjust:l=p=>p,mouseTiltAdjust:r=p=>p,mouseRollAdjust:i=p=>p,window:s=L}=t,a=n.reactive(ze({window:s})),u=n.reactive(at({window:s})),{elementX:f,elementY:c,elementWidth:d,elementHeight:h}=tt(e,{handleOutside:!1,window:s}),m=n.computed(()=>a.isSupported&&(a.alpha!=null&&a.alpha!==0||a.gamma!=null&&a.gamma!==0)?"deviceOrientation":"mouse"),v=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.gamma/90;break;case"landscape-secondary":p=-a.gamma/90;break;case"portrait-primary":p=-a.beta/90;break;case"portrait-secondary":p=a.beta/90;break;default:p=-a.beta/90}return l(p)}else{const p=-(c.value-h.value/2)/h.value;return i(p)}}),S=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.beta/90;break;case"landscape-secondary":p=-a.beta/90;break;case"portrait-primary":p=a.gamma/90;break;case"portrait-secondary":p=-a.gamma/90;break;default:p=a.gamma/90}return o(p)}else{const p=(f.value-d.value/2)/d.value;return r(p)}});return{roll:v,tilt:S,source:m}}function Co(e=$e()){const t=n.shallowRef(),o=()=>{const l=W(e);l&&(t.value=l.parentElement)};return g.tryOnMounted(o),n.watch(()=>n.toValue(e),o),t}function Do(e,t){const{window:o=L,immediate:l=!0,...r}=e,i=H(()=>o&&"PerformanceObserver"in o);let s;const a=()=>{s?.disconnect()},u=()=>{i.value&&(a(),s=new PerformanceObserver(t),s.observe(r))};return g.tryOnScopeDispose(a),l&&u(),{isSupported:i,start:u,stop:a}}const rt={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},Ao=Object.keys(rt);function Mo(e={}){const{target:t=L}=e,o=n.shallowRef(!1),l=n.ref(e.initialValue||{});Object.assign(l.value,rt,l.value);const r=i=>{o.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(i.pointerType))&&(l.value=g.objectPick(i,Ao,!1))};if(t){const i={passive:!0};k(t,["pointerdown","pointermove","pointerup"],r,i),k(t,"pointerleave",()=>o.value=!1,i)}return{...g.toRefs(l),isInside:o}}function Io(e,t={}){const{document:o=q}=t,l=H(()=>o&&"pointerLockElement"in o),r=n.shallowRef(),i=n.shallowRef();let s;if(l.value){const f={passive:!0};k(o,"pointerlockchange",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;s&&d===s&&(r.value=o.pointerLockElement,r.value||(s=i.value=null))},f),k(o,"pointerlockerror",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;if(s&&d===s){const h=o.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${h} pointer lock.`)}},f)}async function a(f){var c;if(!l.value)throw new Error("Pointer Lock API is not supported by your browser.");if(i.value=f instanceof Event?f.currentTarget:null,s=f instanceof Event?(c=W(e))!=null?c:i.value:W(f),!s)throw new Error("Target element undefined.");return s.requestPointerLock(),await g.until(r).toBe(s)}async function u(){return r.value?(o.exitPointerLock(),await g.until(r).toBeNull(),!0):!1}return{isSupported:l,element:r,triggerElement:i,lock:a,unlock:u}}function Lo(e,t={}){const o=g.toRef(e),{threshold:l=50,onSwipe:r,onSwipeEnd:i,onSwipeStart:s,disableTextSelect:a=!1}=t,u=n.reactive({x:0,y:0}),f=(_,F)=>{u.x=_,u.y=F},c=n.reactive({x:0,y:0}),d=(_,F)=>{c.x=_,c.y=F},h=n.computed(()=>u.x-c.x),m=n.computed(()=>u.y-c.y),{max:v,abs:S}=Math,p=n.computed(()=>v(S(h.value),S(m.value))>=l),w=n.shallowRef(!1),y=n.shallowRef(!1),b=n.computed(()=>p.value?S(h.value)>S(m.value)?h.value>0?"left":"right":m.value>0?"up":"down":"none"),E=_=>{var F,A,M;const V=_.buttons===0,D=_.buttons===1;return(M=(A=(F=t.pointerTypes)==null?void 0:F.includes(_.pointerType))!=null?A:V||D)!=null?M:!0},O={passive:!0},P=[k(e,"pointerdown",_=>{if(!E(_))return;y.value=!0;const F=_.target;F?.setPointerCapture(_.pointerId);const{clientX:A,clientY:M}=_;f(A,M),d(A,M),s?.(_)},O),k(e,"pointermove",_=>{if(!E(_)||!y.value)return;const{clientX:F,clientY:A}=_;d(F,A),!w.value&&p.value&&(w.value=!0),w.value&&r?.(_)},O),k(e,"pointerup",_=>{E(_)&&(w.value&&i?.(_,b.value),y.value=!1,w.value=!1)},O)];g.tryOnMounted(()=>{var _,F,A,M,V,D,C,N;(F=(_=o.value)==null?void 0:_.style)==null||F.setProperty("touch-action","pan-y"),a&&((M=(A=o.value)==null?void 0:A.style)==null||M.setProperty("-webkit-user-select","none"),(D=(V=o.value)==null?void 0:V.style)==null||D.setProperty("-ms-user-select","none"),(N=(C=o.value)==null?void 0:C.style)==null||N.setProperty("user-select","none"))});const T=()=>P.forEach(_=>_());return{isSwiping:n.readonly(w),direction:n.readonly(b),posStart:n.readonly(u),posEnd:n.readonly(c),distanceX:h,distanceY:m,stop:T}}function No(e){const t=$("(prefers-color-scheme: light)",e),o=$("(prefers-color-scheme: dark)",e);return n.computed(()=>o.value?"dark":t.value?"light":"no-preference")}function xo(e){const t=$("(prefers-contrast: more)",e),o=$("(prefers-contrast: less)",e),l=$("(prefers-contrast: custom)",e);return n.computed(()=>t.value?"more":o.value?"less":l.value?"custom":"no-preference")}function Wo(e={}){const{window:t=L}=e;if(!t)return n.ref(["en"]);const o=t.navigator,l=n.ref(o.languages);return k(t,"languagechange",()=>{l.value=o.languages},{passive:!0}),l}function Ho(e){const t=$("(prefers-reduced-motion: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function Uo(e){const t=$("(prefers-reduced-transparency: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function $o(e,t){const o=n.shallowRef(t);return n.watch(g.toRef(e),(l,r)=>{o.value=r},{flush:"sync"}),n.readonly(o)}const it="--vueuse-safe-area-top",st="--vueuse-safe-area-right",ut="--vueuse-safe-area-bottom",ct="--vueuse-safe-area-left";function Bo(){const e=n.shallowRef(""),t=n.shallowRef(""),o=n.shallowRef(""),l=n.shallowRef("");if(g.isClient){const i=oe(it),s=oe(st),a=oe(ut),u=oe(ct);i.value="env(safe-area-inset-top, 0px)",s.value="env(safe-area-inset-right, 0px)",a.value="env(safe-area-inset-bottom, 0px)",u.value="env(safe-area-inset-left, 0px)",g.tryOnMounted(r),k("resize",g.useDebounceFn(r),{passive:!0})}function r(){e.value=he(it),t.value=he(st),o.value=he(ut),l.value=he(ct)}return{top:e,right:t,bottom:o,left:l,update:r}}function he(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function jo(e,t=g.noop,o={}){const{immediate:l=!0,manual:r=!1,type:i="text/javascript",async:s=!0,crossOrigin:a,referrerPolicy:u,noModule:f,defer:c,document:d=q,attrs:h={},nonce:m=void 0}=o,v=n.shallowRef(null);let S=null;const p=b=>new Promise((E,O)=>{const P=A=>(v.value=A,E(A),A);if(!d){E(!1);return}let T=!1,_=d.querySelector(`script[src="${n.toValue(e)}"]`);_?_.hasAttribute("data-loaded")&&P(_):(_=d.createElement("script"),_.type=i,_.async=s,_.src=n.toValue(e),c&&(_.defer=c),a&&(_.crossOrigin=a),f&&(_.noModule=f),u&&(_.referrerPolicy=u),m&&(_.nonce=m),Object.entries(h).forEach(([A,M])=>_?.setAttribute(A,M)),T=!0);const F={passive:!0};k(_,"error",A=>O(A),F),k(_,"abort",A=>O(A),F),k(_,"load",()=>{_.setAttribute("data-loaded","true"),t(_),P(_)},F),T&&(_=d.head.appendChild(_)),b||P(_)}),w=(b=!0)=>(S||(S=p(b)),S),y=()=>{if(!d)return;S=null,v.value&&(v.value=null);const b=d.querySelector(`script[src="${n.toValue(e)}"]`);b&&d.head.removeChild(b)};return l&&!r&&g.tryOnMounted(w),r||g.tryOnUnmounted(y),{scriptTag:v,load:w,unload:y}}function ft(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const o=e.parentNode;return!o||o.tagName==="BODY"?!1:ft(o)}}function zo(e){const t=e||window.event,o=t.target;return ft(o)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const Ve=new WeakMap;function qo(e,t=!1){const o=n.shallowRef(t);let l=null,r="";n.watch(g.toRef(e),a=>{const u=pe(n.toValue(a));if(u){const f=u;if(Ve.get(f)||Ve.set(f,f.style.overflow),f.style.overflow!=="hidden"&&(r=f.style.overflow),f.style.overflow==="hidden")return o.value=!0;if(o.value)return f.style.overflow="hidden"}},{immediate:!0});const i=()=>{const a=pe(n.toValue(e));!a||o.value||(g.isIOS&&(l=k(a,"touchmove",u=>{zo(u)},{passive:!1})),a.style.overflow="hidden",o.value=!0)},s=()=>{const a=pe(n.toValue(e));!a||!o.value||(g.isIOS&&l?.(),a.style.overflow=r,Ve.delete(a),o.value=!1)};return g.tryOnScopeDispose(s),n.computed({get(){return o.value},set(a){a?i():s()}})}function Go(e,t,o={}){const{window:l=L}=o;return me(e,t,l?.sessionStorage,o)}function Yo(e={},t={}){const{navigator:o=G}=t,l=o,r=H(()=>l&&"canShare"in l);return{isSupported:r,share:async(s={})=>{if(r.value){const a={...n.toValue(e),...n.toValue(s)};let u=!0;if(a.files&&l.canShare&&(u=l.canShare({files:a.files})),u)return l.share(a)}}}}const Xo=(e,t)=>e.sort(t),ye=(e,t)=>e-t;function Ko(...e){var t,o,l,r;const[i]=e;let s=ye,a={};e.length===2?typeof e[1]=="object"?(a=e[1],s=(t=a.compareFn)!=null?t:ye):s=(o=e[1])!=null?o:ye:e.length>2&&(s=(l=e[1])!=null?l:ye,a=(r=e[2])!=null?r:{});const{dirty:u=!1,sortFn:f=Xo}=a;return u?(n.watchEffect(()=>{const c=f(n.toValue(i),s);n.isRef(i)?i.value=c:i.splice(0,i.length,...c)}),i):n.computed(()=>f([...n.toValue(i)],s))}function Jo(e={}){const{interimResults:t=!0,continuous:o=!0,maxAlternatives:l=1,window:r=L}=e,i=g.toRef(e.lang||"en-US"),s=n.shallowRef(!1),a=n.shallowRef(!1),u=n.shallowRef(""),f=n.shallowRef(void 0);let c;const d=()=>{s.value=!0},h=()=>{s.value=!1},m=(p=!s.value)=>{p?d():h()},v=r&&(r.SpeechRecognition||r.webkitSpeechRecognition),S=H(()=>v);return S.value&&(c=new v,c.continuous=o,c.interimResults=t,c.lang=n.toValue(i),c.maxAlternatives=l,c.onstart=()=>{s.value=!0,a.value=!1},n.watch(i,p=>{c&&!s.value&&(c.lang=p)}),c.onresult=p=>{const w=p.results[p.resultIndex],{transcript:y}=w[0];a.value=w.isFinal,u.value=y,f.value=void 0},c.onerror=p=>{f.value=p},c.onend=()=>{s.value=!1,c.lang=n.toValue(i)},n.watch(s,(p,w)=>{p!==w&&(p?c.start():c.stop())})),g.tryOnScopeDispose(()=>{h()}),{isSupported:S,isListening:s,isFinal:a,recognition:c,result:u,error:f,toggle:m,start:d,stop:h}}function Qo(e,t={}){const{pitch:o=1,rate:l=1,volume:r=1,window:i=L}=t,s=i&&i.speechSynthesis,a=H(()=>s),u=n.shallowRef(!1),f=n.shallowRef("init"),c=g.toRef(e||""),d=g.toRef(t.lang||"en-US"),h=n.shallowRef(void 0),m=(y=!u.value)=>{u.value=y},v=y=>{y.lang=n.toValue(d),y.voice=n.toValue(t.voice)||null,y.pitch=n.toValue(o),y.rate=n.toValue(l),y.volume=n.toValue(r),y.onstart=()=>{u.value=!0,f.value="play"},y.onpause=()=>{u.value=!1,f.value="pause"},y.onresume=()=>{u.value=!0,f.value="play"},y.onend=()=>{u.value=!1,f.value="end"},y.onerror=b=>{h.value=b}},S=n.computed(()=>{u.value=!1,f.value="init";const y=new SpeechSynthesisUtterance(c.value);return v(y),y}),p=()=>{s.cancel(),S&&s.speak(S.value)},w=()=>{s.cancel(),u.value=!1};return a.value&&(v(S.value),n.watch(d,y=>{S.value&&!u.value&&(S.value.lang=y)}),t.voice&&n.watch(t.voice,()=>{s.cancel()}),n.watch(u,()=>{u.value?s.resume():s.pause()})),g.tryOnScopeDispose(()=>{u.value=!1}),{isSupported:a,isPlaying:u,status:f,utterance:S,error:h,stop:w,toggle:m,speak:p}}function Zo(e,t){const o=n.ref(e),l=n.computed(()=>Array.isArray(o.value)?o.value:Object.keys(o.value)),r=n.ref(l.value.indexOf(t??l.value[0])),i=n.computed(()=>c(r.value)),s=n.computed(()=>r.value===0),a=n.computed(()=>r.value===l.value.length-1),u=n.computed(()=>l.value[r.value+1]),f=n.computed(()=>l.value[r.value-1]);function c(O){return Array.isArray(o.value)?o.value[O]:o.value[l.value[O]]}function d(O){if(l.value.includes(O))return c(l.value.indexOf(O))}function h(O){l.value.includes(O)&&(r.value=l.value.indexOf(O))}function m(){a.value||r.value++}function v(){s.value||r.value--}function S(O){E(O)&&h(O)}function p(O){return l.value.indexOf(O)===r.value+1}function w(O){return l.value.indexOf(O)===r.value-1}function y(O){return l.value.indexOf(O)===r.value}function b(O){return r.value<l.value.indexOf(O)}function E(O){return r.value>l.value.indexOf(O)}return{steps:o,stepNames:l,index:r,current:i,next:u,previous:f,isFirst:s,isLast:a,at:c,get:d,goTo:h,goToNext:m,goToPrevious:v,goBackTo:S,isNext:p,isPrevious:w,isCurrent:y,isBefore:b,isAfter:E}}function el(e,t,o,l={}){var r;const{flush:i="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=L,eventFilter:h,onError:m=b=>{console.error(b)}}=l,v=n.toValue(t),S=He(v),p=(c?n.shallowRef:n.ref)(n.toValue(t)),w=(r=l.serializer)!=null?r:Re[S];if(!o)try{o=de("getDefaultStorageAsync",()=>{var b;return(b=L)==null?void 0:b.localStorage})()}catch(b){m(b)}async function y(b){if(!(!o||b&&b.key!==e))try{const E=b?b.newValue:await o.getItem(e);if(E==null)p.value=v,u&&v!==null&&await o.setItem(e,await w.write(v));else if(f){const O=await w.read(E);typeof f=="function"?p.value=f(O,v):S==="object"&&!Array.isArray(O)?p.value={...v,...O}:p.value=O}else p.value=await w.read(E)}catch(E){m(E)}}return y(),d&&a&&k(d,"storage",b=>Promise.resolve().then(()=>y(b)),{passive:!0}),o&&g.watchWithFilter(p,async()=>{try{p.value==null?await o.removeItem(e):await o.setItem(e,await w.write(p.value))}catch(b){m(b)}},{flush:i,deep:s,eventFilter:h}),p}let tl=0;function nl(e,t={}){const o=n.shallowRef(!1),{document:l=q,immediate:r=!0,manual:i=!1,id:s=`vueuse_styletag_${++tl}`}=t,a=n.shallowRef(e);let u=()=>{};const f=()=>{if(!l)return;const d=l.getElementById(s)||l.createElement("style");d.isConnected||(d.id=s,t.nonce&&(d.nonce=t.nonce),t.media&&(d.media=t.media),l.head.appendChild(d)),!o.value&&(u=n.watch(a,h=>{d.textContent=h},{immediate:!0}),o.value=!0)},c=()=>{!l||!o.value||(u(),l.head.removeChild(l.getElementById(s)),o.value=!1)};return r&&!i&&g.tryOnMounted(f),i||g.tryOnScopeDispose(c),{id:s,css:a,unload:c,load:f,isLoaded:n.readonly(o)}}function ol(e,t={}){const{threshold:o=50,onSwipe:l,onSwipeEnd:r,onSwipeStart:i,passive:s=!0}=t,a=n.reactive({x:0,y:0}),u=n.reactive({x:0,y:0}),f=n.computed(()=>a.x-u.x),c=n.computed(()=>a.y-u.y),{max:d,abs:h}=Math,m=n.computed(()=>d(h(f.value),h(c.value))>=o),v=n.shallowRef(!1),S=n.computed(()=>m.value?h(f.value)>h(c.value)?f.value>0?"left":"right":c.value>0?"up":"down":"none"),p=T=>[T.touches[0].clientX,T.touches[0].clientY],w=(T,_)=>{a.x=T,a.y=_},y=(T,_)=>{u.x=T,u.y=_},b={passive:s,capture:!s},E=T=>{v.value&&r?.(T,S.value),v.value=!1},O=[k(e,"touchstart",T=>{if(T.touches.length!==1)return;const[_,F]=p(T);w(_,F),y(_,F),i?.(T)},b),k(e,"touchmove",T=>{if(T.touches.length!==1)return;const[_,F]=p(T);y(_,F),b.capture&&!b.passive&&Math.abs(f.value)>Math.abs(c.value)&&T.preventDefault(),!v.value&&m.value&&(v.value=!0),v.value&&l?.(T)},b),k(e,["touchend","touchcancel"],E,b)];return{isSwiping:v,direction:S,coordsStart:a,coordsEnd:u,lengthX:f,lengthY:c,stop:()=>O.forEach(T=>T()),isPassiveEventSupported:!0}}function ll(){const e=n.ref([]);return e.value.set=t=>{t&&e.value.push(t)},n.onBeforeUpdate(()=>{e.value.length=0}),e}function al(e={}){const{document:t=q,selector:o="html",observe:l=!1,initialValue:r="ltr"}=e;function i(){var a,u;return(u=(a=t?.querySelector(o))==null?void 0:a.getAttribute("dir"))!=null?u:r}const s=n.ref(i());return g.tryOnMounted(()=>s.value=i()),l&&t&&X(t.querySelector(o),()=>s.value=i(),{attributes:!0}),n.computed({get(){return s.value},set(a){var u,f;s.value=a,t&&(s.value?(u=t.querySelector(o))==null||u.setAttribute("dir",s.value):(f=t.querySelector(o))==null||f.removeAttribute("dir"))}})}function rl(e){var t;const o=(t=e.rangeCount)!=null?t:0;return Array.from({length:o},(l,r)=>e.getRangeAt(r))}function il(e={}){const{window:t=L}=e,o=n.ref(null),l=n.computed(()=>{var a,u;return(u=(a=o.value)==null?void 0:a.toString())!=null?u:""}),r=n.computed(()=>o.value?rl(o.value):[]),i=n.computed(()=>r.value.map(a=>a.getBoundingClientRect()));function s(){o.value=null,t&&(o.value=t.getSelection())}return t&&k(t.document,"selectionchange",s,{passive:!0}),{text:l,rects:i,ranges:r,selection:o}}function sl(e=L,t){e&&typeof e.requestAnimationFrame=="function"?e.requestAnimationFrame(t):t()}function ul(e={}){var t,o;const{window:l=L}=e,r=g.toRef(e?.element),i=g.toRef((t=e?.input)!=null?t:""),s=(o=e?.styleProp)!=null?o:"height",a=n.shallowRef(1),u=n.shallowRef(0);function f(){var c;if(!r.value)return;let d="";r.value.style[s]="1px",a.value=(c=r.value)==null?void 0:c.scrollHeight;const h=n.toValue(e?.styleTarget);h?h.style[s]=`${a.value}px`:d=`${a.value}px`,r.value.style[s]=d}return n.watch([i,r],()=>n.nextTick(f),{immediate:!0}),n.watch(a,()=>{var c;return(c=e?.onResize)==null?void 0:c.call(e)}),le(r,([{contentRect:c}])=>{u.value!==c.width&&sl(l,()=>{u.value=c.width,f()})}),e?.watch&&n.watch(e.watch,f,{immediate:!0,deep:!0}),{textarea:r,input:i,triggerResize:f}}function cl(e,t={}){const{throttle:o=200,trailing:l=!0}=t,r=g.throttleFilter(o,l);return{...Te(e,{...t,eventFilter:r})}}const fl=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:Number.POSITIVE_INFINITY,value:31536e6,name:"year"}],dl={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""};function ml(e){return e.toISOString().slice(0,10)}function vl(e,t={}){const{controls:o=!1,updateInterval:l=3e4}=t,{now:r,...i}=ot({interval:l,controls:!0}),s=n.computed(()=>dt(new Date(n.toValue(e)),t,n.toValue(r)));return o?{timeAgo:s,...i}:s}function dt(e,t={},o=Date.now()){var l;const{max:r,messages:i=dl,fullDateFormatter:s=ml,units:a=fl,showSecond:u=!1,rounding:f="round"}=t,c=typeof f=="number"?p=>+p.toFixed(f):Math[f],d=+o-+e,h=Math.abs(d);function m(p,w){return c(Math.abs(p)/w.value)}function v(p,w){const y=m(p,w),b=p>0,E=S(w.name,y,b);return S(b?"past":"future",E,b)}function S(p,w,y){const b=i[p];return typeof b=="function"?b(w,y):b.replace("{0}",w.toString())}if(h<6e4&&!u)return i.justNow;if(typeof r=="number"&&h>r)return s(new Date(e));if(typeof r=="string"){const p=(l=a.find(w=>w.name===r))==null?void 0:l.max;if(p&&h>p)return s(new Date(e))}for(const[p,w]of a.entries()){if(m(d,w)<=0&&a[p-1])return v(d,a[p-1]);if(h<w.max)return v(d,w)}return i.invalid}function pl(e,t,o={}){const{immediate:l=!0,immediateCallback:r=!1}=o,{start:i}=g.useTimeoutFn(a,t,{immediate:l}),s=n.shallowRef(!1);async function a(){s.value&&(await e(),i())}function u(){s.value||(s.value=!0,r&&e(),i())}function f(){s.value=!1}return l&&g.isClient&&u(),g.tryOnScopeDispose(f),{isActive:s,pause:f,resume:u}}function hl(e={}){const{controls:t=!1,offset:o=0,immediate:l=!0,interval:r="requestAnimationFrame",callback:i}=e,s=n.shallowRef(g.timestamp()+o),a=()=>s.value=g.timestamp()+o,u=i?()=>{a(),i(s.value)}:a,f=r==="requestAnimationFrame"?K(u,{immediate:l}):g.useIntervalFn(u,r,{immediate:l});return t?{timestamp:s,...f}:s}function yl(e=null,t={}){var o,l,r;const{document:i=q,restoreOnUnmount:s=d=>d}=t,a=(o=i?.title)!=null?o:"",u=g.toRef((l=e??i?.title)!=null?l:null),f=!!(e&&typeof e=="function");function c(d){if(!("titleTemplate"in t))return d;const h=t.titleTemplate||"%s";return typeof h=="function"?h(d):n.toValue(h).replace(/%s/g,d)}return n.watch(u,(d,h)=>{d!==h&&i&&(i.title=c(d??""))},{immediate:!0}),t.observe&&!t.titleTemplate&&i&&!f&&X((r=i.head)==null?void 0:r.querySelector("title"),()=>{i&&i.title!==u.value&&(u.value=c(i.title))},{childList:!0}),g.tryOnScopeDispose(()=>{if(s){const d=s(a,u.value||"");d!=null&&i&&(i.title=d)}}),u}const wl={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},gl=Object.assign({},{linear:g.identity},wl);function bl([e,t,o,l]){const r=(c,d)=>1-3*d+3*c,i=(c,d)=>3*d-6*c,s=c=>3*c,a=(c,d,h)=>((r(d,h)*c+i(d,h))*c+s(d))*c,u=(c,d,h)=>3*r(d,h)*c*c+2*i(d,h)*c+s(d),f=c=>{let d=c;for(let h=0;h<4;++h){const m=u(d,e,o);if(m===0)return d;const v=a(d,e,o)-c;d-=v/m}return d};return c=>e===t&&o===l?c:a(f(c),t,l)}function mt(e,t,o){return e+o*(t-e)}function Fe(e){return(typeof e=="number"?[e]:e)||[]}function vt(e,t,o,l={}){var r,i;const s=n.toValue(t),a=n.toValue(o),u=Fe(s),f=Fe(a),c=(r=n.toValue(l.duration))!=null?r:1e3,d=Date.now(),h=Date.now()+c,m=typeof l.transition=="function"?l.transition:(i=n.toValue(l.transition))!=null?i:g.identity,v=typeof m=="function"?m:bl(m);return new Promise(S=>{e.value=s;const p=()=>{var w;if((w=l.abort)!=null&&w.call(l)){S();return}const y=Date.now(),b=v((y-d)/c),E=Fe(e.value).map((O,P)=>mt(u[P],f[P],b));Array.isArray(e.value)?e.value=E.map((O,P)=>{var T,_;return mt((T=u[P])!=null?T:0,(_=f[P])!=null?_:0,b)}):typeof e.value=="number"&&(e.value=E[0]),y<h?requestAnimationFrame(p):(e.value=a,S())};p()})}function Sl(e,t={}){let o=0;const l=()=>{const i=n.toValue(e);return typeof i=="number"?i:i.map(n.toValue)},r=n.ref(l());return n.watch(l,async i=>{var s,a;if(n.toValue(t.disabled))return;const u=++o;if(t.delay&&await g.promiseTimeout(n.toValue(t.delay)),u!==o)return;const f=Array.isArray(i)?i.map(n.toValue):n.toValue(i);(s=t.onStarted)==null||s.call(t),await vt(r,r.value,f,{...t,abort:()=>{var c;return u!==o||((c=t.abort)==null?void 0:c.call(t))}}),(a=t.onFinished)==null||a.call(t)},{deep:!0}),n.watch(()=>n.toValue(t.disabled),i=>{i&&(o++,r.value=l())}),g.tryOnScopeDispose(()=>{o++}),n.computed(()=>n.toValue(t.disabled)?l():r.value)}function Rl(e="history",t={}){const{initialValue:o={},removeNullishValues:l=!0,removeFalsyValues:r=!1,write:i=!0,writeMode:s="replace",window:a=L,stringify:u=E=>E.toString()}=t;if(!a)return n.reactive(o);const f=n.reactive({});function c(){if(e==="history")return a.location.search||"";if(e==="hash"){const E=a.location.hash||"",O=E.indexOf("?");return O>0?E.slice(O):""}else return(a.location.hash||"").replace(/^#/,"")}function d(E){const O=u(E);if(e==="history")return`${O?`?${O}`:""}${a.location.hash||""}`;if(e==="hash-params")return`${a.location.search||""}${O?`#${O}`:""}`;const P=a.location.hash||"#",T=P.indexOf("?");return T>0?`${a.location.search||""}${P.slice(0,T)}${O?`?${O}`:""}`:`${a.location.search||""}${P}${O?`?${O}`:""}`}function h(){return new URLSearchParams(c())}function m(E){const O=new Set(Object.keys(f));for(const P of E.keys()){const T=E.getAll(P);f[P]=T.length>1?T:E.get(P)||"",O.delete(P)}Array.from(O).forEach(P=>delete f[P])}const{pause:v,resume:S}=g.pausableWatch(f,()=>{const E=new URLSearchParams("");Object.keys(f).forEach(O=>{const P=f[O];Array.isArray(P)?P.forEach(T=>E.append(O,T)):l&&P==null||r&&!P?E.delete(O):E.set(O,P)}),p(E,!1)},{deep:!0});function p(E,O){v(),O&&m(E),s==="replace"?a.history.replaceState(a.history.state,a.document.title,a.location.pathname+d(E)):a.history.pushState(a.history.state,a.document.title,a.location.pathname+d(E)),S()}function w(){i&&p(h(),!0)}const y={passive:!0};k(a,"popstate",w,y),e!=="history"&&k(a,"hashchange",w,y);const b=h();return b.keys().next().value?m(b):Object.assign(f,o),f}function El(e={}){var t,o;const l=n.shallowRef((t=e.enabled)!=null?t:!1),r=n.shallowRef((o=e.autoSwitch)!=null?o:!0),i=n.ref(e.constraints),{navigator:s=G}=e,a=H(()=>{var S;return(S=s?.mediaDevices)==null?void 0:S.getUserMedia}),u=n.shallowRef();function f(S){switch(S){case"video":{if(i.value)return i.value.video||!1;break}case"audio":{if(i.value)return i.value.audio||!1;break}}}async function c(){if(!(!a.value||u.value))return u.value=await s.mediaDevices.getUserMedia({video:f("video"),audio:f("audio")}),u.value}function d(){var S;(S=u.value)==null||S.getTracks().forEach(p=>p.stop()),u.value=void 0}function h(){d(),l.value=!1}async function m(){return await c(),u.value&&(l.value=!0),u.value}async function v(){return d(),await m()}return n.watch(l,S=>{S?c():d()},{immediate:!0}),n.watch(i,()=>{r.value&&u.value&&v()},{immediate:!0}),g.tryOnScopeDispose(()=>{h()}),{isSupported:a,stream:u,start:m,stop:h,restart:v,constraints:i,enabled:l,autoSwitch:r}}function pt(e,t,o,l={}){var r,i,s;const{clone:a=!1,passive:u=!1,eventName:f,deep:c=!1,defaultValue:d,shouldEmit:h}=l,m=n.getCurrentInstance(),v=o||m?.emit||((r=m?.$emit)==null?void 0:r.bind(m))||((s=(i=m?.proxy)==null?void 0:i.$emit)==null?void 0:s.bind(m?.proxy));let S=f;t||(t="modelValue"),S=S||`update:${t.toString()}`;const p=b=>a?typeof a=="function"?a(b):ne(b):b,w=()=>g.isDef(e[t])?p(e[t]):d,y=b=>{h?h(b)&&v(S,b):v(S,b)};if(u){const b=w(),E=n.ref(b);let O=!1;return n.watch(()=>e[t],P=>{O||(O=!0,E.value=p(P),n.nextTick(()=>O=!1))}),n.watch(E,P=>{!O&&(P!==e[t]||c)&&y(P)},{deep:c}),E}else return n.computed({get(){return w()},set(b){y(b)}})}function Tl(e,t,o={}){const l={};for(const r in e)l[r]=pt(e,r,t,o);return l}function Ol(e){const{pattern:t=[],interval:o=0,navigator:l=G}=e||{},r=H(()=>typeof l<"u"&&"vibrate"in l),i=g.toRef(t);let s;const a=(f=i.value)=>{r.value&&l.vibrate(f)},u=()=>{r.value&&l.vibrate(0),s?.pause()};return o>0&&(s=g.useIntervalFn(a,o,{immediate:!1,immediateCallback:!1})),{isSupported:r,pattern:t,intervalControls:s,vibrate:a,stop:u}}function kl(e,t){const{containerStyle:o,wrapperProps:l,scrollTo:r,calculateRange:i,currentList:s,containerRef:a}="itemHeight"in t?Fl(t,e):Vl(t,e);return{list:s,scrollTo:r,containerProps:{ref:a,onScroll:()=>{i()},style:o},wrapperProps:l}}function ht(e){const t=n.shallowRef(null),o=Ge(t),l=n.ref([]),r=n.shallowRef(e);return{state:n.ref({start:0,end:10}),source:r,currentList:l,size:o,containerRef:t}}function yt(e,t,o){return l=>{if(typeof o=="number")return Math.ceil(l/o);const{start:r=0}=e.value;let i=0,s=0;for(let a=r;a<t.value.length;a++){const u=o(a);if(i+=u,s=a,i>l)break}return s-r}}function wt(e,t){return o=>{if(typeof t=="number")return Math.floor(o/t)+1;let l=0,r=0;for(let i=0;i<e.value.length;i++){const s=t(i);if(l+=s,l>=o){r=i;break}}return r+1}}function gt(e,t,o,l,{containerRef:r,state:i,currentList:s,source:a}){return()=>{const u=r.value;if(u){const f=o(e==="vertical"?u.scrollTop:u.scrollLeft),c=l(e==="vertical"?u.clientHeight:u.clientWidth),d=f-t,h=f+c+t;i.value={start:d<0?0:d,end:h>a.value.length?a.value.length:h},s.value=a.value.slice(i.value.start,i.value.end).map((m,v)=>({data:m,index:v+i.value.start}))}}}function bt(e,t){return o=>typeof e=="number"?o*e:t.value.slice(0,o).reduce((r,i,s)=>r+e(s),0)}function St(e,t,o,l){n.watch([e.width,e.height,t,o],()=>{l()})}function Rt(e,t){return n.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((o,l,r)=>o+e(r),0))}const _l={horizontal:"scrollLeft",vertical:"scrollTop"};function Et(e,t,o,l){return r=>{l.value&&(l.value[_l[e]]=o(r),t())}}function Vl(e,t){const o=ht(t),{state:l,source:r,currentList:i,size:s,containerRef:a}=o,u={overflowX:"auto"},{itemWidth:f,overscan:c=5}=e,d=yt(l,r,f),h=wt(r,f),m=gt("horizontal",c,h,d,o),v=bt(f,r),S=n.computed(()=>v(l.value.start)),p=Rt(f,r);St(s,t,a,m);const w=Et("horizontal",m,v,a),y=n.computed(()=>({style:{height:"100%",width:`${p.value-S.value}px`,marginLeft:`${S.value}px`,display:"flex"}}));return{scrollTo:w,calculateRange:m,wrapperProps:y,containerStyle:u,currentList:i,containerRef:a}}function Fl(e,t){const o=ht(t),{state:l,source:r,currentList:i,size:s,containerRef:a}=o,u={overflowY:"auto"},{itemHeight:f,overscan:c=5}=e,d=yt(l,r,f),h=wt(r,f),m=gt("vertical",c,h,d,o),v=bt(f,r),S=n.computed(()=>v(l.value.start)),p=Rt(f,r);St(s,t,a,m);const w=Et("vertical",m,v,a),y=n.computed(()=>({style:{width:"100%",height:`${p.value-S.value}px`,marginTop:`${S.value}px`}}));return{calculateRange:m,scrollTo:w,containerStyle:u,wrapperProps:y,currentList:i,containerRef:a}}function Pl(e={}){const{navigator:t=G,document:o=q}=e,l=n.shallowRef(!1),r=n.shallowRef(null),i=qe({document:o}),s=H(()=>t&&"wakeLock"in t),a=n.computed(()=>!!r.value&&i.value==="visible");s.value&&(k(r,"release",()=>{var d,h;l.value=(h=(d=r.value)==null?void 0:d.type)!=null?h:!1},{passive:!0}),g.whenever(()=>i.value==="visible"&&o?.visibilityState==="visible"&&l.value,d=>{l.value=!1,u(d)}));async function u(d){var h;await((h=r.value)==null?void 0:h.release()),r.value=s.value?await t.wakeLock.request(d):null}async function f(d){i.value==="visible"?await u(d):l.value=d}async function c(){l.value=!1;const d=r.value;r.value=null,await d?.release()}return{sentinel:r,isSupported:s,isActive:a,request:f,forceRequest:u,release:c}}function Cl(e={}){const{window:t=L,requestPermissions:o=!0}=e,l=e,r=H(()=>{if(!t||!("Notification"in t))return!1;if(Notification.permission==="granted")return!0;try{const y=new Notification("");y.onshow=()=>{y.close()}}catch(y){if(y.name==="TypeError")return!1}return!0}),i=n.shallowRef(r.value&&"permission"in Notification&&Notification.permission==="granted"),s=n.ref(null),a=async()=>{if(r.value)return!i.value&&Notification.permission!=="denied"&&await Notification.requestPermission()==="granted"&&(i.value=!0),i.value},{on:u,trigger:f}=g.createEventHook(),{on:c,trigger:d}=g.createEventHook(),{on:h,trigger:m}=g.createEventHook(),{on:v,trigger:S}=g.createEventHook(),p=async y=>{if(!r.value||!i.value)return;const b=Object.assign({},l,y);return s.value=new Notification(b.title||"",b),s.value.onclick=f,s.value.onshow=d,s.value.onerror=m,s.value.onclose=S,s.value},w=()=>{s.value&&s.value.close(),s.value=null};if(o&&g.tryOnMounted(a),g.tryOnScopeDispose(w),r.value&&t){const y=t.document;k(y,"visibilitychange",b=>{b.preventDefault(),y.visibilityState==="visible"&&w()})}return{isSupported:r,notification:s,ensurePermissions:a,permissionGranted:i,show:p,close:w,onClick:u,onShow:c,onError:h,onClose:v}}const Tt="ping";function Pe(e){return e===!0?{}:e}function Dl(e,t={}){const{onConnected:o,onDisconnected:l,onError:r,onMessage:i,immediate:s=!0,autoConnect:a=!0,autoClose:u=!0,protocols:f=[]}=t,c=n.ref(null),d=n.shallowRef("CLOSED"),h=n.ref(),m=g.toRef(e);let v,S,p=!1,w=0,y=[],b,E;const O=()=>{if(y.length&&h.value&&d.value==="OPEN"){for(const V of y)h.value.send(V);y=[]}},P=()=>{b!=null&&(clearTimeout(b),b=void 0)},T=()=>{clearTimeout(E),E=void 0},_=(V=1e3,D)=>{P(),!(!g.isClient&&!g.isWorker||!h.value)&&(p=!0,T(),v?.(),h.value.close(V,D),h.value=void 0)},F=(V,D=!0)=>!h.value||d.value!=="OPEN"?(D&&y.push(V),!1):(O(),h.value.send(V),!0),A=()=>{if(p||typeof m.value>"u")return;const V=new WebSocket(m.value,f);h.value=V,d.value="CONNECTING",V.onopen=()=>{d.value="OPEN",w=0,o?.(V),S?.(),O()},V.onclose=D=>{if(d.value="CLOSED",T(),v?.(),l?.(V,D),!p&&t.autoReconnect&&(h.value==null||V===h.value)){const{retries:C=-1,delay:N=1e3,onFailed:x}=Pe(t.autoReconnect);(typeof C=="function"?C:()=>typeof C=="number"&&(C<0||w<C))(w)?(w+=1,b=setTimeout(A,N)):x?.()}},V.onerror=D=>{r?.(V,D)},V.onmessage=D=>{if(t.heartbeat){T();const{message:C=Tt,responseMessage:N=C}=Pe(t.heartbeat);if(D.data===n.toValue(N))return}c.value=D.data,i?.(V,D)}};if(t.heartbeat){const{message:V=Tt,interval:D=1e3,pongTimeout:C=1e3}=Pe(t.heartbeat),{pause:N,resume:x}=g.useIntervalFn(()=>{F(n.toValue(V),!1),E==null&&(E=setTimeout(()=>{_(),p=!1},C))},D,{immediate:!1});v=N,S=x}u&&(g.isClient&&k("beforeunload",()=>_(),{passive:!0}),g.tryOnScopeDispose(_));const M=()=>{!g.isClient&&!g.isWorker||(_(),p=!1,w=0,A())};return s&&M(),a&&n.watch(m,M),{data:c,status:d,close:_,send:F,open:M,ws:h}}function Al(e,t,o){const{window:l=L}=o??{},r=n.ref(null),i=n.shallowRef(),s=(...u)=>{i.value&&i.value.postMessage(...u)},a=function(){i.value&&i.value.terminate()};return l&&(typeof e=="string"?i.value=new Worker(e,t):typeof e=="function"?i.value=e():i.value=e,i.value.onmessage=u=>{r.value=u.data},g.tryOnScopeDispose(()=>{i.value&&i.value.terminate()})),{data:r,post:s,terminate:a,worker:i}}function Ml(e,t){if(e.length===0&&t.length===0)return"";const o=e.map(i=>`'${i}'`).toString(),l=t.filter(i=>typeof i=="function").map(i=>{const s=i.toString();return s.trim().startsWith("function")?s:`const ${i.name} = ${s}`}).join(";"),r=`importScripts(${o});`;return`${o.trim()===""?"":r} ${l}`}function Il(e){return t=>{const o=t.data[0];return Promise.resolve(e.apply(void 0,o)).then(l=>{postMessage(["SUCCESS",l])}).catch(l=>{postMessage(["ERROR",l])})}}function Ll(e,t,o){const l=`${Ml(t,o)}; onmessage=(${Il})(${e})`,r=new Blob([l],{type:"text/javascript"});return URL.createObjectURL(r)}function Nl(e,t={}){const{dependencies:o=[],localDependencies:l=[],timeout:r,window:i=L}=t,s=n.ref(),a=n.shallowRef("PENDING"),u=n.ref({}),f=n.shallowRef(),c=(v="PENDING")=>{s.value&&s.value._url&&i&&(s.value.terminate(),URL.revokeObjectURL(s.value._url),u.value={},s.value=void 0,i.clearTimeout(f.value),a.value=v)};c(),g.tryOnScopeDispose(c);const d=()=>{const v=Ll(e,o,l),S=new Worker(v);return S._url=v,S.onmessage=p=>{const{resolve:w=()=>{},reject:y=()=>{}}=u.value,[b,E]=p.data;switch(b){case"SUCCESS":w(E),c(b);break;default:y(E),c("ERROR");break}},S.onerror=p=>{const{reject:w=()=>{}}=u.value;p.preventDefault(),w(p),c("ERROR")},r&&(f.value=setTimeout(()=>c("TIMEOUT_EXPIRED"),r)),S},h=(...v)=>new Promise((S,p)=>{var w;u.value={resolve:S,reject:p},(w=s.value)==null||w.postMessage([[...v]]),a.value="RUNNING"});return{workerFn:(...v)=>a.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(s.value=d(),h(...v)),workerStatus:a,workerTerminate:c}}function xl(e={}){const{window:t=L}=e;if(!t)return n.shallowRef(!1);const o=n.shallowRef(t.document.hasFocus()),l={passive:!0};return k(t,"blur",()=>{o.value=!1},l),k(t,"focus",()=>{o.value=!0},l),o}function Wl(e={}){const{window:t=L,...o}=e;return Oe(t,o)}function Hl(e={}){const{window:t=L,initialWidth:o=Number.POSITIVE_INFINITY,initialHeight:l=Number.POSITIVE_INFINITY,listenOrientation:r=!0,includeScrollbar:i=!0,type:s="inner"}=e,a=n.shallowRef(o),u=n.shallowRef(l),f=()=>{if(t)if(s==="outer")a.value=t.outerWidth,u.value=t.outerHeight;else if(s==="visual"&&t.visualViewport){const{width:d,height:h,scale:m}=t.visualViewport;a.value=Math.round(d*m),u.value=Math.round(h*m)}else i?(a.value=t.innerWidth,u.value=t.innerHeight):(a.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight)};f(),g.tryOnMounted(f);const c={passive:!0};if(k("resize",f,c),t&&s==="visual"&&t.visualViewport&&k(t.visualViewport,"resize",f,c),r){const d=$("(orientation: portrait)");n.watch(d,()=>f())}return{width:a,height:u}}R.DefaultMagicKeysAliasMap=Ze,R.StorageSerializers=Re,R.TransitionPresets=gl,R.asyncComputed=Ce,R.breakpointsAntDesign=on,R.breakpointsBootstrapV5=en,R.breakpointsElement=un,R.breakpointsMasterCss=rn,R.breakpointsPrimeFlex=sn,R.breakpointsQuasar=ln,R.breakpointsSematic=an,R.breakpointsTailwind=Zt,R.breakpointsVuetify=nn,R.breakpointsVuetifyV2=Le,R.breakpointsVuetifyV3=tn,R.cloneFnJSON=ne,R.computedAsync=Ce,R.computedInject=kt,R.createFetch=zn,R.createReusableTemplate=_t,R.createTemplatePromise=Ft,R.createUnrefFn=Pt,R.customStorageEventName=Ee,R.defaultDocument=q,R.defaultLocation=Ct,R.defaultNavigator=G,R.defaultWindow=L,R.executeTransition=vt,R.formatTimeAgo=dt,R.getSSRHandler=de,R.mapGamepadToXbox360Controller=lo,R.onClickOutside=Dt,R.onElementRemoval=we,R.onKeyDown=Mt,R.onKeyPressed=It,R.onKeyStroke=se,R.onKeyUp=Lt,R.onLongPress=Wt,R.onStartTyping=$t,R.provideSSRWidth=Qt,R.setSSRHandler=wn,R.templateRef=Bt,R.unrefElement=W,R.useActiveElement=Me,R.useAnimate=jt,R.useAsyncQueue=zt,R.useAsyncState=Ie,R.useBase64=Yt,R.useBattery=Kt,R.useBluetooth=Jt,R.useBreakpoints=cn,R.useBroadcastChannel=fn,R.useBrowserLocation=dn,R.useCached=mn,R.useClipboard=vn,R.useClipboardItems=pn,R.useCloned=hn,R.useColorMode=Ue,R.useConfirmDialog=bn,R.useCountdown=Sn,R.useCssVar=oe,R.useCurrentElement=$e,R.useCycleList=Rn,R.useDark=En,R.useDebouncedRefHistory=_n,R.useDeviceMotion=Vn,R.useDeviceOrientation=ze,R.useDevicePixelRatio=Fn,R.useDevicesList=Pn,R.useDisplayMedia=Cn,R.useDocumentVisibility=qe,R.useDraggable=Dn,R.useDropZone=An,R.useElementBounding=Mn,R.useElementByPoint=In,R.useElementHover=Ln,R.useElementSize=Ge,R.useElementVisibility=Xe,R.useEventBus=Nn,R.useEventListener=k,R.useEventSource=Wn,R.useEyeDropper=Hn,R.useFavicon=Un,R.useFetch=Ke,R.useFileDialog=Xn,R.useFileSystemAccess=Kn,R.useFocus=Jn,R.useFocusWithin=to,R.useFps=no,R.useFullscreen=oo,R.useGamepad=ao,R.useGeolocation=ro,R.useIdle=uo,R.useImage=fo,R.useInfiniteScroll=mo,R.useIntersectionObserver=Ye,R.useKeyModifier=po,R.useLocalStorage=ho,R.useMagicKeys=yo,R.useManualRefHistory=je,R.useMediaControls=bo,R.useMediaQuery=$,R.useMemoize=So,R.useMemory=Ro,R.useMounted=Ae,R.useMouse=et,R.useMouseInElement=tt,R.useMousePressed=To,R.useMutationObserver=X,R.useNavigatorLanguage=Oo,R.useNetwork=nt,R.useNow=ot,R.useObjectUrl=ko,R.useOffsetPagination=_o,R.useOnline=Vo,R.usePageLeave=Fo,R.useParallax=Po,R.useParentElement=Co,R.usePerformanceObserver=Do,R.usePermission=ue,R.usePointer=Mo,R.usePointerLock=Io,R.usePointerSwipe=Lo,R.usePreferredColorScheme=No,R.usePreferredContrast=xo,R.usePreferredDark=We,R.usePreferredLanguages=Wo,R.usePreferredReducedMotion=Ho,R.usePreferredReducedTransparency=Uo,R.usePrevious=$o,R.useRafFn=K,R.useRefHistory=Te,R.useResizeObserver=le,R.useSSRWidth=Se,R.useScreenOrientation=at,R.useScreenSafeArea=Bo,R.useScriptTag=jo,R.useScroll=Oe,R.useScrollLock=qo,R.useSessionStorage=Go,R.useShare=Yo,R.useSorted=Ko,R.useSpeechRecognition=Jo,R.useSpeechSynthesis=Qo,R.useStepper=Zo,R.useStorage=me,R.useStorageAsync=el,R.useStyleTag=nl,R.useSupported=H,R.useSwipe=ol,R.useTemplateRefsList=ll,R.useTextDirection=al,R.useTextSelection=il,R.useTextareaAutosize=ul,R.useThrottledRefHistory=cl,R.useTimeAgo=vl,R.useTimeoutPoll=pl,R.useTimestamp=hl,R.useTitle=yl,R.useTransition=Sl,R.useUrlSearchParams=Rl,R.useUserMedia=El,R.useVModel=pt,R.useVModels=Tl,R.useVibrate=Ol,R.useVirtualList=kl,R.useWakeLock=Pl,R.useWebNotification=Cl,R.useWebSocket=Dl,R.useWebWorker=Al,R.useWebWorkerFn=Nl,R.useWindowFocus=xl,R.useWindowScroll=Wl,R.useWindowSize=Hl,Object.keys(g).forEach(function(e){e!=="default"&&!Object.prototype.hasOwnProperty.call(R,e)&&Object.defineProperty(R,e,{enumerable:!0,get:function(){return g[e]}})})})(this.VueUse=this.VueUse||{},VueUse,Vue);
