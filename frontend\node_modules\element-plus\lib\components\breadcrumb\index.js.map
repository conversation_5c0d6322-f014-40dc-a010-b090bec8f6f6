{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/breadcrumb/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Breadcrumb from './src/breadcrumb.vue'\nimport BreadcrumbItem from './src/breadcrumb-item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElBreadcrumb: SFCWithInstall<typeof Breadcrumb> & {\n  BreadcrumbItem: typeof BreadcrumbItem\n} = withInstall(Breadcrumb, {\n  BreadcrumbItem,\n})\nexport const ElBreadcrumbItem: SFCWithInstall<typeof BreadcrumbItem> =\n  withNoopInstall(BreadcrumbItem)\nexport default ElBreadcrumb\n\nexport * from './src/breadcrumb'\nexport * from './src/breadcrumb-item'\nexport * from './src/constants'\nexport type {\n  BreadcrumbInstance,\n  BreadcrumbItemInstance,\n} from './src/instances'\n"], "names": ["withInstall", "Breadcrumb", "BreadcrumbItem", "withNoopInstall"], "mappings": ";;;;;;;;;;;AAGY,MAAC,YAAY,GAAGA,mBAAW,CAACC,uBAAU,EAAE;AACpD,kBAAEC,2BAAc;AAChB,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAGC,uBAAe,CAACD,2BAAc;;;;;;;;;"}