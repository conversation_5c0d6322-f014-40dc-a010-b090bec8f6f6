<svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="sparkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1890ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#722ed1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#eb2f96;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="120" height="40" rx="8" fill="url(#sparkGradient)" opacity="0.1"/>
  
  <!-- Spark Icon -->
  <g transform="translate(8, 8)">
    <!-- Lightning bolt -->
    <path d="M12 2L8 12h4l-4 10 8-10h-4l4-10z" fill="url(#sparkGradient)" stroke="none"/>
    <!-- Sparkles -->
    <circle cx="18" cy="6" r="1.5" fill="#ffd700"/>
    <circle cx="20" cy="10" r="1" fill="#ffd700"/>
    <circle cx="16" cy="18" r="1" fill="#ffd700"/>
  </g>
  
  <!-- Text -->
  <text x="32" y="16" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="url(#sparkGradient)">
    iFlytek
  </text>
  <text x="32" y="28" font-family="Arial, sans-serif" font-size="8" fill="#666">
    Spark LLM
  </text>
</svg>
