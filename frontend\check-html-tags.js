import fs from 'fs';

function checkHTMLTags(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 提取template部分
  const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/);
  if (!templateMatch) {
    console.log('No template found');
    return;
  }
  
  const template = templateMatch[1];
  const lines = template.split('\n');
  
  const stack = [];
  const errors = [];
  
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    
    // 查找开始标签
    const openTags = line.match(/<(\w+)(?:\s[^>]*)?(?<!\/)\s*>/g);
    if (openTags) {
      openTags.forEach(tag => {
        const tagName = tag.match(/<(\w+)/)[1];
        stack.push({ tag: tagName, line: lineNum, content: line.trim() });
      });
    }
    
    // 查找自闭合标签
    const selfClosingTags = line.match(/<(\w+)(?:\s[^>]*)?\/\s*>/g);
    if (selfClosingTags) {
      // 自闭合标签不需要处理
    }
    
    // 查找结束标签
    const closeTags = line.match(/<\/(\w+)\s*>/g);
    if (closeTags) {
      closeTags.forEach(tag => {
        const tagName = tag.match(/<\/(\w+)/)[1];
        
        if (stack.length === 0) {
          errors.push(`Line ${lineNum}: Unexpected closing tag ${tag}`);
          return;
        }
        
        const lastOpen = stack[stack.length - 1];
        if (lastOpen.tag === tagName) {
          stack.pop();
        } else {
          errors.push(`Line ${lineNum}: Mismatched closing tag ${tag}, expected </${lastOpen.tag}> (opened at line ${lastOpen.line})`);
        }
      });
    }
  });
  
  // 检查未闭合的标签
  if (stack.length > 0) {
    stack.forEach(openTag => {
      errors.push(`Line ${openTag.line}: Unclosed tag <${openTag.tag}> - ${openTag.content}`);
    });
  }
  
  if (errors.length === 0) {
    console.log(`✅ ${filePath}: All HTML tags are properly closed`);
  } else {
    console.log(`❌ ${filePath}: Found ${errors.length} errors:`);
    errors.slice(0, 5).forEach(error => console.log(`  ${error}`));
    if (errors.length > 5) {
      console.log(`  ... and ${errors.length - 5} more errors`);
    }
  }
  
  return errors.length === 0;
}

// 检查文件
const files = [
  'src/views/EnterpriseDashboard.vue',
  'src/components/Enhanced/MultimodalDataFusion.vue'
];

files.forEach(file => {
  try {
    checkHTMLTags(file);
  } catch (error) {
    console.log(`Error checking ${file}: ${error.message}`);
  }
});
