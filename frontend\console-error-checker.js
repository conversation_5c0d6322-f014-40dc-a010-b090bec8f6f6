// iFlytek 面试系统控制台错误检查脚本
// 在浏览器控制台中运行此脚本检查JavaScript错误和Vue.js警告

console.log('🔍 开始iFlytek面试系统控制台错误检查...');

// 错误收集器
const errorCollector = {
    errors: [],
    warnings: [],
    vueWarnings: [],
    networkErrors: [],
    startTime: Date.now()
};

// 1. 设置全局错误监听器
function setupErrorListeners() {
    console.log('📡 设置错误监听器...');
    
    // JavaScript错误监听
    const originalError = console.error;
    console.error = function(...args) {
        const errorMessage = args.join(' ');
        errorCollector.errors.push({
            type: 'javascript',
            message: errorMessage,
            timestamp: new Date().toISOString(),
            stack: new Error().stack
        });
        originalError.apply(console, args);
    };
    
    // 警告监听
    const originalWarn = console.warn;
    console.warn = function(...args) {
        const warningMessage = args.join(' ');
        
        // 检查是否为Vue警告
        if (warningMessage.includes('Vue') || warningMessage.includes('[Vue warn]')) {
            errorCollector.vueWarnings.push({
                type: 'vue-warning',
                message: warningMessage,
                timestamp: new Date().toISOString()
            });
        } else {
            errorCollector.warnings.push({
                type: 'warning',
                message: warningMessage,
                timestamp: new Date().toISOString()
            });
        }
        originalWarn.apply(console, args);
    };
    
    // 未捕获的错误监听
    window.addEventListener('error', (event) => {
        errorCollector.errors.push({
            type: 'uncaught',
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            timestamp: new Date().toISOString()
        });
    });
    
    // Promise拒绝监听
    window.addEventListener('unhandledrejection', (event) => {
        errorCollector.errors.push({
            type: 'promise-rejection',
            message: event.reason.toString(),
            timestamp: new Date().toISOString()
        });
    });
    
    console.log('✅ 错误监听器设置完成');
}

// 2. 检查Vue应用错误
function checkVueApplicationErrors() {
    console.log('\n📱 检查Vue应用错误...');
    
    const app = document.getElementById('app');
    if (!app) {
        errorCollector.errors.push({
            type: 'vue-mount',
            message: 'Vue应用容器#app不存在',
            timestamp: new Date().toISOString()
        });
        return false;
    }
    
    if (!app.__vue_app__) {
        errorCollector.errors.push({
            type: 'vue-mount',
            message: 'Vue应用实例未挂载到#app',
            timestamp: new Date().toISOString()
        });
        return false;
    }
    
    try {
        const vueApp = app.__vue_app__;
        
        // 检查Vue应用配置
        if (!vueApp.config) {
            errorCollector.warnings.push({
                type: 'vue-config',
                message: 'Vue应用配置不可访问',
                timestamp: new Date().toISOString()
            });
        }
        
        // 检查全局属性
        if (vueApp.config && !vueApp.config.globalProperties) {
            errorCollector.warnings.push({
                type: 'vue-global',
                message: 'Vue全局属性不可访问',
                timestamp: new Date().toISOString()
            });
        }
        
        console.log('✅ Vue应用状态正常');
        return true;
        
    } catch (error) {
        errorCollector.errors.push({
            type: 'vue-check',
            message: `Vue应用检查失败: ${error.message}`,
            timestamp: new Date().toISOString()
        });
        return false;
    }
}

// 3. 检查路由器错误
function checkRouterErrors() {
    console.log('\n🛣️ 检查路由器错误...');
    
    try {
        // 检查当前路径
        const currentPath = window.location.pathname;
        console.log(`📍 当前路径: ${currentPath}`);
        
        // 检查History API
        if (!window.history || !window.history.pushState) {
            errorCollector.errors.push({
                type: 'router',
                message: 'History API不支持',
                timestamp: new Date().toISOString()
            });
            return false;
        }
        
        // 测试路由导航
        const originalPath = currentPath;
        try {
            window.history.pushState({}, '', '/test-route');
            window.history.pushState({}, '', originalPath);
            console.log('✅ 路由导航功能正常');
        } catch (error) {
            errorCollector.errors.push({
                type: 'router-navigation',
                message: `路由导航测试失败: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }
        
        return true;
        
    } catch (error) {
        errorCollector.errors.push({
            type: 'router-check',
            message: `路由器检查失败: ${error.message}`,
            timestamp: new Date().toISOString()
        });
        return false;
    }
}

// 4. 检查Element Plus错误
function checkElementPlusErrors() {
    console.log('\n🎨 检查Element Plus错误...');
    
    try {
        // 检查Element Plus样式
        const stylesheets = Array.from(document.styleSheets);
        const elementPlusStyles = stylesheets.find(sheet => 
            sheet.href && sheet.href.includes('element-plus')
        );
        
        if (!elementPlusStyles) {
            errorCollector.warnings.push({
                type: 'element-plus-styles',
                message: 'Element Plus样式表未检测到',
                timestamp: new Date().toISOString()
            });
        }
        
        // 检查Element Plus组件
        const elComponents = document.querySelectorAll('[class*="el-"]');
        if (elComponents.length === 0) {
            errorCollector.warnings.push({
                type: 'element-plus-components',
                message: 'Element Plus组件未找到',
                timestamp: new Date().toISOString()
            });
        } else {
            console.log(`✅ 找到 ${elComponents.length} 个Element Plus组件`);
        }
        
        // 检查特定组件
        const menus = document.querySelectorAll('.el-menu');
        const buttons = document.querySelectorAll('.el-button');
        const icons = document.querySelectorAll('.el-icon');
        
        console.log(`📋 菜单组件: ${menus.length} 个`);
        console.log(`🔘 按钮组件: ${buttons.length} 个`);
        console.log(`🎨 图标组件: ${icons.length} 个`);
        
        if (menus.length === 0) {
            errorCollector.warnings.push({
                type: 'element-plus-menu',
                message: 'Element Plus菜单组件未找到',
                timestamp: new Date().toISOString()
            });
        }
        
        return true;
        
    } catch (error) {
        errorCollector.errors.push({
            type: 'element-plus-check',
            message: `Element Plus检查失败: ${error.message}`,
            timestamp: new Date().toISOString()
        });
        return false;
    }
}

// 5. 检查网络错误
function checkNetworkErrors() {
    console.log('\n🌐 检查网络错误...');
    
    // 检查失败的资源加载
    const failedResources = [];
    
    // 检查图片加载错误
    const images = document.querySelectorAll('img');
    images.forEach((img, index) => {
        if (img.complete && img.naturalWidth === 0) {
            failedResources.push({
                type: 'image',
                src: img.src,
                alt: img.alt || `图片${index + 1}`
            });
        }
    });
    
    // 检查CSS加载错误
    const stylesheets = Array.from(document.styleSheets);
    stylesheets.forEach(sheet => {
        try {
            // 尝试访问样式表规则
            const rules = sheet.cssRules || sheet.rules;
            if (!rules) {
                failedResources.push({
                    type: 'stylesheet',
                    href: sheet.href || '内联样式'
                });
            }
        } catch (error) {
            if (sheet.href) {
                failedResources.push({
                    type: 'stylesheet',
                    href: sheet.href,
                    error: error.message
                });
            }
        }
    });
    
    if (failedResources.length > 0) {
        errorCollector.networkErrors = failedResources;
        console.warn(`⚠️ 发现 ${failedResources.length} 个资源加载问题`);
        failedResources.forEach(resource => {
            console.warn(`  - ${resource.type}: ${resource.src || resource.href}`);
        });
    } else {
        console.log('✅ 未发现网络资源加载错误');
    }
    
    return failedResources.length === 0;
}

// 6. 生成错误报告
function generateErrorReport() {
    console.log('\n📊 生成错误报告...');
    
    const report = {
        timestamp: new Date().toISOString(),
        duration: Date.now() - errorCollector.startTime,
        summary: {
            totalErrors: errorCollector.errors.length,
            totalWarnings: errorCollector.warnings.length,
            vueWarnings: errorCollector.vueWarnings.length,
            networkErrors: errorCollector.networkErrors.length
        },
        details: {
            errors: errorCollector.errors,
            warnings: errorCollector.warnings,
            vueWarnings: errorCollector.vueWarnings,
            networkErrors: errorCollector.networkErrors
        }
    };
    
    console.log('📈 错误检查摘要:');
    console.log(`  JavaScript错误: ${report.summary.totalErrors} 个`);
    console.log(`  一般警告: ${report.summary.totalWarnings} 个`);
    console.log(`  Vue警告: ${report.summary.vueWarnings} 个`);
    console.log(`  网络错误: ${report.summary.networkErrors} 个`);
    console.log(`  检查耗时: ${report.duration}ms`);
    
    // 显示详细错误
    if (report.summary.totalErrors > 0) {
        console.log('\n❌ JavaScript错误详情:');
        errorCollector.errors.forEach((error, index) => {
            console.error(`  ${index + 1}. [${error.type}] ${error.message}`);
        });
    }
    
    if (report.summary.vueWarnings > 0) {
        console.log('\n⚠️ Vue警告详情:');
        errorCollector.vueWarnings.forEach((warning, index) => {
            console.warn(`  ${index + 1}. ${warning.message}`);
        });
    }
    
    if (report.summary.totalWarnings > 0) {
        console.log('\n⚠️ 一般警告详情:');
        errorCollector.warnings.forEach((warning, index) => {
            console.warn(`  ${index + 1}. ${warning.message}`);
        });
    }
    
    // 计算健康度评分
    let healthScore = 100;
    healthScore -= report.summary.totalErrors * 20;
    healthScore -= report.summary.vueWarnings * 10;
    healthScore -= report.summary.totalWarnings * 5;
    healthScore -= report.summary.networkErrors * 15;
    healthScore = Math.max(0, healthScore);
    
    report.healthScore = healthScore;
    console.log(`\n🏥 系统健康度评分: ${healthScore}/100`);
    
    if (healthScore >= 90) {
        console.log('🎉 系统状态优秀！');
    } else if (healthScore >= 70) {
        console.log('👍 系统状态良好');
    } else if (healthScore >= 50) {
        console.log('⚠️ 系统状态一般，建议优化');
    } else {
        console.log('🚨 系统状态较差，需要修复');
    }
    
    return report;
}

// 7. 主检查函数
function runConsoleErrorCheck() {
    console.log('🚀 开始控制台错误检查...\n');
    
    // 重置错误收集器
    errorCollector.errors = [];
    errorCollector.warnings = [];
    errorCollector.vueWarnings = [];
    errorCollector.networkErrors = [];
    errorCollector.startTime = Date.now();
    
    // 设置监听器
    setupErrorListeners();
    
    // 执行检查
    const vueStatus = checkVueApplicationErrors();
    const routerStatus = checkRouterErrors();
    const elementPlusStatus = checkElementPlusErrors();
    const networkStatus = checkNetworkErrors();
    
    // 等待一段时间收集错误
    setTimeout(() => {
        const report = generateErrorReport();
        
        // 保存报告到全局变量
        window.iflytekErrorReport = report;
        
        console.log('\n✅ 控制台错误检查完成!');
        console.log('💡 错误报告已保存到 window.iflytekErrorReport');
        
        return report;
    }, 2000);
}

// 导出到全局作用域
window.iflytekErrorChecker = {
    runConsoleErrorCheck,
    setupErrorListeners,
    checkVueApplicationErrors,
    checkRouterErrors,
    checkElementPlusErrors,
    checkNetworkErrors,
    generateErrorReport,
    getErrors: () => errorCollector
};

console.log('✅ 控制台错误检查脚本已加载');
console.log('💡 使用 iflytekErrorChecker.runConsoleErrorCheck() 开始检查');
console.log('💡 使用 window.iflytekErrorReport 查看错误报告');
