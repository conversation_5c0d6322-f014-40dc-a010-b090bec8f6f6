{"version": 3, "file": "lookahead.js", "sourceRoot": "", "sources": ["../../../../src/parse/grammar/lookahead.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAC/E,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAEvC,OAAO,EACL,sBAAsB,EACtB,kCAAkC,GACnC,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EACL,WAAW,EACX,WAAW,IAAI,eAAe,EAC9B,WAAW,EACX,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,gCAAgC,EAChC,uBAAuB,GACxB,MAAM,kBAAkB,CAAC;AAY1B,MAAM,CAAN,IAAY,SAOX;AAPD,WAAY,SAAS;IACnB,6CAAM,CAAA;IACN,qDAAU,CAAA;IACV,yEAAoB,CAAA;IACpB,uGAAmC,CAAA;IACnC,mFAAyB,CAAA;IACzB,uDAAW,CAAA;AACb,CAAC,EAPW,SAAS,KAAT,SAAS,QAOpB;AAED,MAAM,UAAU,WAAW,CACzB,IAA2C;IAE3C,0BAA0B;IAC1B,IAAI,IAAI,YAAY,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;QAC/C,OAAO,SAAS,CAAC,MAAM,CAAC;KACzB;SAAM,IAAI,IAAI,YAAY,UAAU,IAAI,IAAI,KAAK,YAAY,EAAE;QAC9D,OAAO,SAAS,CAAC,UAAU,CAAC;KAC7B;SAAM,IACL,IAAI,YAAY,mBAAmB;QACnC,IAAI,KAAK,qBAAqB,EAC9B;QACA,OAAO,SAAS,CAAC,oBAAoB,CAAC;KACvC;SAAM,IACL,IAAI,YAAY,gCAAgC;QAChD,IAAI,KAAK,kCAAkC,EAC3C;QACA,OAAO,SAAS,CAAC,mCAAmC,CAAC;KACtD;SAAM,IACL,IAAI,YAAY,uBAAuB;QACvC,IAAI,KAAK,yBAAyB,EAClC;QACA,OAAO,SAAS,CAAC,yBAAyB,CAAC;KAC5C;SAAM,IAAI,IAAI,YAAY,WAAW,IAAI,IAAI,KAAK,aAAa,EAAE;QAChE,OAAO,SAAS,CAAC,WAAW,CAAC;KAC9B;SAAM;QACL,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACrC;AACH,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,OAKjC;IACC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;IAC7D,MAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,IAAI,KAAK,SAAS,CAAC,WAAW,EAAE;QAClC,OAAO,sBAAsB,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;KAC/D;SAAM;QACL,OAAO,gCAAgC,CACrC,UAAU,EACV,IAAI,EACJ,IAAI,EACJ,YAAY,CACb,CAAC;KACH;AACH,CAAC;AAED,MAAM,UAAU,uBAAuB,CACrC,UAAkB,EAClB,WAAiB,EACjB,YAAoB,EACpB,aAAsB,EACtB,oBAA6B,EAC7B,aAAuB;IAEvB,MAAM,cAAc,GAAG,sBAAsB,CAC3C,UAAU,EACV,WAAW,EACX,YAAY,CACb,CAAC;IAEF,MAAM,YAAY,GAAG,yBAAyB,CAAC,cAAc,CAAC;QAC5D,CAAC,CAAC,kCAAkC;QACpC,CAAC,CAAC,sBAAsB,CAAC;IAE3B,OAAO,aAAa,CAClB,cAAc,EACd,aAAa,EACb,YAAY,EACZ,oBAAoB,CACrB,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,iCAAiC,CAC/C,UAAkB,EAClB,WAAiB,EACjB,CAAS,EACT,oBAA6B,EAC7B,QAAmB,EACnB,gBAIkB;IAElB,MAAM,cAAc,GAAG,gCAAgC,CACrD,UAAU,EACV,WAAW,EACX,QAAQ,EACR,CAAC,CACF,CAAC;IAEF,MAAM,YAAY,GAAG,yBAAyB,CAAC,cAAc,CAAC;QAC5D,CAAC,CAAC,kCAAkC;QACpC,CAAC,CAAC,sBAAsB,CAAC;IAE3B,OAAO,gBAAgB,CACrB,cAAc,CAAC,CAAC,CAAC,EACjB,YAAY,EACZ,oBAAoB,CACrB,CAAC;AACJ,CAAC;AAID,MAAM,UAAU,8BAA8B,CAC5C,IAAyB,EACzB,aAAsB,EACtB,YAA0B,EAC1B,oBAA6B;IAE7B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;IAC9B,MAAM,uBAAuB,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;QACtD,OAAO,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;YACjC,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,0DAA0D;IAC1D,IAAI,aAAa,EAAE;QACjB;;WAEG;QACH,OAAO,UAEL,MAAqB;YAErB,mEAAmE;YACnE,4FAA4F;YAC5F,qGAAqG;YACrG,MAAM,UAAU,GAA8B,GAAG,CAC/C,MAAM,EACN,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAC1B,CAAC;YAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;gBAClC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;gBAEtC,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;oBACrE,0EAA0E;oBAC1E,SAAS;iBACV;gBACD,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;oBACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;oBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;wBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wBACjC,IAAI,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;4BAClD,2BAA2B;4BAC3B,mBAAmB;4BACnB,SAAS,QAAQ,CAAC;yBACnB;qBACF;oBACD,kCAAkC;oBAClC,mEAAmE;oBACnE,OAAO,CAAC,CAAC;iBACV;gBACD,wDAAwD;gBACxD,2BAA2B;aAC5B;YACD,4CAA4C;YAC5C,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;KACH;SAAM,IAAI,uBAAuB,IAAI,CAAC,oBAAoB,EAAE;QAC3D,qEAAqE;QACrE,oGAAoG;QACpG,MAAM,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;YAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,MAAM,CACxB,eAAe,EACf,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE;YACvB,OAAO,CAAC,OAAO,EAAE,CAAC,WAAW,EAAE,EAAE;gBAC/B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,YAAa,CAAC,EAAE;oBAC3C,MAAM,CAAC,WAAW,CAAC,YAAa,CAAC,GAAG,GAAG,CAAC;iBACzC;gBACD,OAAO,CAAC,WAAW,CAAC,eAAgB,EAAE,CAAC,iBAAiB,EAAE,EAAE;oBAC1D,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAAE;wBACnC,MAAM,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC;qBACjC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,EAA4B,CAC7B,CAAC;QAEF;;WAEG;QACH,OAAO;YACL,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7B,OAAO,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC7C,CAAC,CAAC;KACH;SAAM;QACL,sEAAsE;QACtE,4EAA4E;QAC5E;;WAEG;QACH,OAAO;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;gBAClC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;gBACtC,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;oBACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;oBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;wBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wBACjC,IAAI,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;4BAClD,2BAA2B;4BAC3B,mBAAmB;4BACnB,SAAS,QAAQ,CAAC;yBACnB;qBACF;oBACD,kCAAkC;oBAClC,mEAAmE;oBACnE,OAAO,CAAC,CAAC;iBACV;gBACD,wDAAwD;gBACxD,2BAA2B;aAC5B;YACD,4CAA4C;YAC5C,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;KACH;AACH,CAAC;AAED,MAAM,UAAU,uCAAuC,CACrD,GAAsB,EACtB,YAA0B,EAC1B,oBAA6B;IAE7B,MAAM,uBAAuB,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE;QACtD,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC;IAE9B,qEAAqE;IACrE,4BAA4B;IAC5B,IAAI,uBAAuB,IAAI,CAAC,oBAAoB,EAAE;QACpD,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAEvC,IACE,iBAAiB,CAAC,MAAM,KAAK,CAAC;YAC9B,OAAO,CAAO,iBAAiB,CAAC,CAAC,CAAE,CAAC,eAAe,CAAC,EACpD;YACA,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,sBAAsB,GAAS,iBAAkB,CAAC,YAAY,CAAC;YAErE,OAAO;gBACL,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,KAAK,sBAAsB,CAAC;YAC5D,CAAC,CAAC;SACH;aAAM;YACL,MAAM,WAAW,GAAG,MAAM,CACxB,iBAAiB,EACjB,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE;gBAC3B,MAAM,CAAC,WAAW,CAAC,YAAa,CAAC,GAAG,IAAI,CAAC;gBACzC,OAAO,CAAC,WAAW,CAAC,eAAgB,EAAE,CAAC,iBAAiB,EAAE,EAAE;oBAC1D,MAAM,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;gBACnC,CAAC,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAChB,CAAC,EACD,EAAe,CAChB,CAAC;YAEF,OAAO;gBACL,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7B,OAAO,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC;YACtD,CAAC,CAAC;SACH;KACF;SAAM;QACL,OAAO;YACL,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBAC7C,MAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;gBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;oBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACjC,IAAI,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;wBAClD,2BAA2B;wBAC3B,mBAAmB;wBACnB,SAAS,QAAQ,CAAC;qBACnB;iBACF;gBACD,kCAAkC;gBAClC,OAAO,IAAI,CAAC;aACb;YAED,4BAA4B;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;KACH;AACH,CAAC;AAED,MAAM,0BAA2B,SAAQ,UAAU;IAGjD,YACU,OAAa,EACb,gBAAwB,EACxB,cAAyB;QAEjC,KAAK,EAAE,CAAC;QAJA,YAAO,GAAP,OAAO,CAAM;QACb,qBAAgB,GAAhB,gBAAgB,CAAQ;QACxB,mBAAc,GAAd,cAAc,CAAW;IAGnC,CAAC;IAED,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEO,aAAa,CACnB,IAA+B,EAC/B,gBAA2B,EAC3B,QAAuB,EACvB,QAAuB;QAEvB,IACE,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,cAAc,KAAK,gBAAgB,EACxC;YACA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;SACb;QACD,sGAAsG;QACtG,OAAO,KAAK,CAAC;IACf,CAAC;IAED,UAAU,CACR,UAAkB,EAClB,QAAuB,EACvB,QAAuB;QAEvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;YACzE,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAClD;IACH,CAAC;IAED,cAAc,CACZ,cAAmC,EACnC,QAAuB,EACvB,QAAuB;QAEvB,IACE,CAAC,IAAI,CAAC,aAAa,CACjB,cAAc,EACd,SAAS,CAAC,oBAAoB,EAC9B,QAAQ,EACR,QAAQ,CACT,EACD;YACA,KAAK,CAAC,UAAU,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SACtD;IACH,CAAC;IAED,iBAAiB,CACf,iBAAmD,EACnD,QAAuB,EACvB,QAAuB;QAEvB,IACE,CAAC,IAAI,CAAC,aAAa,CACjB,iBAAiB,EACjB,SAAS,CAAC,mCAAmC,EAC7C,QAAQ,EACR,QAAQ,CACT,EACD;YACA,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SACzD;IACH,CAAC;IAED,QAAQ,CACN,QAAoB,EACpB,QAAuB,EACvB,QAAuB;QAEvB,IACE,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EACvE;YACA,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAChD;IACH,CAAC;IAED,WAAW,CACT,WAAoC,EACpC,QAAuB,EACvB,QAAuB;QAEvB,IACE,CAAC,IAAI,CAAC,aAAa,CACjB,WAAW,EACX,SAAS,CAAC,yBAAyB,EACnC,QAAQ,EACR,QAAQ,CACT,EACD;YACA,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SACnD;IACH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,6BAA8B,SAAQ,WAAW;IAGrD,YACU,gBAAwB,EACxB,cAAyB,EACzB,SAAe;QAEvB,KAAK,EAAE,CAAC;QAJA,qBAAgB,GAAhB,gBAAgB,CAAQ;QACxB,mBAAc,GAAd,cAAc,CAAW;QACzB,cAAS,GAAT,SAAS,CAAM;QALlB,WAAM,GAAkB,EAAE,CAAC;IAQlC,CAAC;IAEO,aAAa,CACnB,IAA+D,EAC/D,gBAA2B;QAE3B,IACE,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,cAAc,KAAK,gBAAgB;YACxC,CAAC,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,EACzD;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;SAC/B;IACH,CAAC;IAEM,WAAW,CAAC,IAAY;QAC7B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAEM,eAAe,CAAC,IAAgB;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAEM,wBAAwB,CAAC,IAAyB;QACvD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,oBAAoB,CAAC,CAAC;IAC3D,CAAC;IAEM,qCAAqC,CAC1C,IAAsC;QAEtC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,mCAAmC,CAAC,CAAC;IAC1E,CAAC;IAEM,4BAA4B,CAAC,IAA6B;QAC/D,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAChE,CAAC;IAEM,gBAAgB,CAAC,IAAiB;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAED,SAAS,uBAAuB,CAAC,IAAY;IAC3C,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;QAC7B,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;KAChB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,cAAc,CAAC,IAAiB;IACvC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/B,UAAU,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,eAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxD,MAAM,mBAAmB,GAAG,GAAG,GAAG,OAAO,CAAC,eAAgB,CAAC,CAAC,CAAC,CAAC;gBAC9D,UAAU,CAAC,IAAI,CAAC,cAAc,GAAG,mBAAmB,CAAC,CAAC;aACvD;SACF;QACD,IAAI,GAAG,UAAU,CAAC;KACnB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACzB,iBAA4C,EAC5C,cAAwB,EACxB,GAAW;IAEX,KACE,IAAI,UAAU,GAAG,CAAC,EAClB,UAAU,GAAG,iBAAiB,CAAC,MAAM,EACrC,UAAU,EAAE,EACZ;QACA,iDAAiD;QACjD,IAAI,UAAU,KAAK,GAAG,EAAE;YACtB,SAAS;SACV;QACD,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAC7D,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;YACtE,MAAM,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,sBAAsB,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC9C,OAAO,KAAK,CAAC;aACd;SACF;KACF;IACD,yEAAyE;IACzE,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,iCAAiC,CAC/C,QAAuB,EACvB,CAAS;IAET,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAC5C,iBAAiB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAChC,CAAC;IACF,MAAM,WAAW,GAAG,uBAAuB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAChE,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,YAAY,EAAE,EAAE;QACnD,MAAM,IAAI,GAA+B,EAAE,CAAC;QAC5C,OAAO,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE;YAC7B,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;gBACxB,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IACH,IAAI,OAAO,GAAG,WAAW,CAAC;IAE1B,oBAAoB;IACpB,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI,CAAC,EAAE,UAAU,EAAE,EAAE;QACtD,MAAM,WAAW,GAAG,OAAO,CAAC;QAC5B,OAAO,GAAG,uBAAuB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAEtD,oBAAoB;QACpB,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;YAC1D,MAAM,uBAAuB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;YACpD,oCAAoC;YACpC,KACE,IAAI,WAAW,GAAG,CAAC,EACnB,WAAW,GAAG,uBAAuB,CAAC,MAAM,EAC5C,WAAW,EAAE,EACb;gBACA,MAAM,cAAc,GAAG,uBAAuB,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC;gBACxE,MAAM,SAAS,GAAG,uBAAuB,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC;gBACjE,MAAM,UAAU,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;gBAClD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;gBACpE,iCAAiC;gBACjC,IAAI,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;oBACjE,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;oBAC1C,iEAAiE;oBACjE,IAAI,YAAY,CAAC,aAAa,EAAE,cAAc,CAAC,KAAK,KAAK,EAAE;wBACzD,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACnC,6CAA6C;wBAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BAC1C,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;4BAC9B,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;yBACpC;qBACF;iBACF;gBACD,sBAAsB;qBACjB;oBACH,MAAM,0BAA0B,GAAG,iBAAiB,CAClD,SAAS,EACT,UAAU,GAAG,CAAC,EACd,cAAc,CACf,CAAC;oBACF,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;oBAErE,kCAAkC;oBAClC,OAAO,CAAC,0BAA0B,EAAE,CAAC,IAAI,EAAE,EAAE;wBAC3C,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBACpD,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE;4BAC1B,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;wBACjC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;iBACJ;aACF;SACF;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,UAAkB,EAClB,WAAiB,EACjB,CAAS,EACT,MAAoB;IAEpB,MAAM,OAAO,GAAG,IAAI,6BAA6B,CAC/C,UAAU,EACV,SAAS,CAAC,WAAW,EACrB,MAAM,CACP,CAAC;IACF,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC5B,OAAO,iCAAiC,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED,MAAM,UAAU,gCAAgC,CAC9C,UAAkB,EAClB,WAAiB,EACjB,QAAmB,EACnB,CAAS;IAET,MAAM,gBAAgB,GAAG,IAAI,6BAA6B,CACxD,UAAU,EACV,QAAQ,CACT,CAAC;IACF,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACrC,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAE1C,MAAM,cAAc,GAAG,IAAI,0BAA0B,CACnD,WAAW,EACX,UAAU,EACV,QAAQ,CACT,CAAC;IACF,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC;IAE/C,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;IAEhE,OAAO,iCAAiC,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,WAAwB,EACxB,UAAuB;IAEvB,gBAAgB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7D,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,SAAS,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;YAC1C,SAAS;SACV;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAE9B,MAAM,cAAc,GAClB,SAAS,KAAK,QAAQ;gBACtB,QAAQ,CAAC,kBAAmB,CAAC,SAAS,CAAC,YAAa,CAAC,KAAK,SAAS,CAAC;YACtE,IAAI,cAAc,KAAK,KAAK,EAAE;gBAC5B,SAAS,gBAAgB,CAAC;aAC3B;SACF;QACD,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,MAAmB,EACnB,KAAkB;IAElB,OAAO,CACL,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;QAC5B,KAAK,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;YAC7B,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YAChC,OAAO,CACL,OAAO,KAAK,YAAY;gBACxB,YAAY,CAAC,kBAAmB,CAAC,OAAO,CAAC,YAAa,CAAC,CACxD,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,cAAmC;IAEnC,OAAO,KAAK,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,EAAE,CAC9C,KAAK,CAAC,cAAc,EAAE,CAAC,UAAU,EAAE,EAAE,CACnC,KAAK,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,eAAgB,CAAC,CAAC,CAC9D,CACF,CAAC;AACJ,CAAC"}