import{E as u,L as f,M as T,O as S,a as t,b as l,c as p,d as m,e as c,f as v}from"./chunk-6TGVXIR7.mjs";var n=class extends S{static{t(this,"TreemapTokenBuilder")}constructor(){super(["treemap"])}};var C=/classDef\s+([A-Z_a-z]\w+)(?:\s+([^\n\r;]*))?;?/,s=class extends T{static{t(this,"TreemapValueConverter")}runCustomConverter(r,e,i){if(r.name==="NUMBER2")return parseFloat(e.replace(/,/g,""));if(r.name==="SEPARATOR")return e.substring(1,e.length-1);if(r.name==="STRING2")return e.substring(1,e.length-1);if(r.name==="INDENTATION")return e.length;if(r.name==="ClassDef"){if(typeof e!="string")return e;let a=C.exec(e);if(a)return{$type:"ClassDefStatement",className:a[1],styleText:a[2]||void 0}}}};function g(o){let r=o.validation.TreemapValidator,e=o.validation.ValidationRegistry;if(e){let i={Treemap:r.checkSingleRoot.bind(r)};e.register(i,r)}}t(g,"registerValidationChecks");var d=class{static{t(this,"TreemapValidator")}checkSingleRoot(r,e){let i;for(let a of r.TreemapRows)a.item&&(i===void 0&&a.indent===void 0?i=0:a.indent===void 0?e("error","Multiple root nodes are not allowed in a treemap.",{node:a,property:"item"}):i!==void 0&&i>=parseInt(a.indent,10)&&e("error","Multiple root nodes are not allowed in a treemap.",{node:a,property:"item"}))}};var y={parser:{TokenBuilder:t(()=>new n,"TokenBuilder"),ValueConverter:t(()=>new s,"ValueConverter")},validation:{TreemapValidator:t(()=>new d,"TreemapValidator")}};function G(o=c){let r=m(p(o),u),e=m(l({shared:r}),f,y);return r.ServiceRegistry.register(e),g(e),{shared:r,Treemap:e}}t(G,"createTreemapServices");export{y as a,G as b};
