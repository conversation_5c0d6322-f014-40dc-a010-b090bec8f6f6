/**
 * iFlytek Spark 面试系统 - 文字与容器重叠修复样式
 * Text Container Overlap Fix for iFlytek Spark Interview System
 * 
 * 版本: 1.0
 * 更新: 2025-07-18
 * 
 * 功能特性:
 * - 修复文字与UI容器重叠问题
 * - 优化中文字体显示效果
 * - 确保适当的内边距和外边距
 * - 维持iFlytek品牌视觉一致性
 * - 响应式设计支持
 */

/* ===== 全局文字容器修复 ===== */

/* 确保所有文字容器有足够的内边距 */
.el-dialog,
.el-card,
.el-drawer,
.el-popover,
.el-tooltip,
.el-message-box {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
}

/* Element Plus 对话框优化 */
.el-dialog__header {
  padding: 24px 24px 16px !important;
}

.el-dialog__title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  line-height: 1.4 !important;
  word-wrap: break-word !important;
}

.el-dialog__body {
  padding: 8px 24px 24px !important;
  line-height: 1.6 !important;
}

.el-dialog__footer {
  padding: 16px 24px 24px !important;
}

/* Element Plus 单选按钮组修复 */
.el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.el-radio {
  margin-right: 0 !important;
  margin-bottom: 8px !important;
  position: relative;
  min-height: 24px;
  display: flex;
  align-items: flex-start;
}

.el-radio__input {
  margin-top: 2px !important;
  margin-right: 12px !important;
  flex-shrink: 0;
}

.el-radio__label {
  padding-left: 8px !important;
  line-height: 1.5 !important;
  word-wrap: break-word !important;
  flex: 1;
  min-width: 0;
}

/* Element Plus 复选框修复 */
.el-checkbox {
  margin-right: 0 !important;
  margin-bottom: 8px !important;
  display: flex;
  align-items: flex-start;
  min-height: 24px;
}

.el-checkbox__input {
  margin-top: 2px !important;
  margin-right: 12px !important;
  flex-shrink: 0;
}

.el-checkbox__label {
  padding-left: 8px !important;
  line-height: 1.5 !important;
  word-wrap: break-word !important;
  flex: 1;
  min-width: 0;
}

/* Element Plus 卡片组件修复 */
.el-card {
  border-radius: 12px !important;
  overflow: hidden;
}

.el-card__header {
  padding: 20px 24px 16px !important;
  background: #fafbfc;
  border-bottom: 1px solid #e5e7eb;
}

.el-card__body {
  padding: 24px !important;
  line-height: 1.6;
}

/* Element Plus 表单项修复 */
.el-form-item {
  margin-bottom: 24px !important;
}

.el-form-item__label {
  padding-bottom: 8px !important;
  line-height: 1.4 !important;
  font-weight: 500 !important;
  color: #374151 !important;
}

.el-form-item__content {
  line-height: 1.5 !important;
}

/* Element Plus 输入框修复 */
.el-input,
.el-textarea,
.el-select {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
}

.el-input__inner,
.el-textarea__inner {
  padding: 12px 16px !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
}

.el-select .el-input__inner {
  padding-right: 40px !important;
}

/* Element Plus 按钮修复 */
.el-button {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  padding: 10px 20px !important;
  line-height: 1.4 !important;
  border-radius: 8px !important;
}

.el-button .el-icon {
  margin-right: 6px !important;
  vertical-align: middle !important;
}

.el-button--large {
  padding: 14px 24px !important;
  font-size: 16px !important;
}

.el-button--small {
  padding: 8px 16px !important;
  font-size: 13px !important;
}

/* Element Plus 标签修复 */
.el-tag {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  padding: 4px 12px !important;
  line-height: 1.4 !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  margin-right: 8px !important;
  margin-bottom: 4px !important;
  display: inline-flex !important;
  align-items: center !important;
  word-wrap: break-word !important;
}

/* Element Plus 进度条修复 */
.el-progress {
  margin-bottom: 16px !important;
}

.el-progress__text {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  margin-left: 12px !important;
}

/* Element Plus 表格修复 */
.el-table {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
}

.el-table th,
.el-table td {
  padding: 16px 12px !important;
  line-height: 1.5 !important;
}

.el-table th {
  font-weight: 600 !important;
  background: #fafbfc !important;
}

/* Element Plus 分页修复 */
.el-pagination {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  padding: 16px 0 !important;
}

.el-pagination .el-pager li,
.el-pagination .btn-prev,
.el-pagination .btn-next {
  min-width: 36px !important;
  height: 36px !important;
  line-height: 34px !important;
  border-radius: 6px !important;
}

/* 自定义组件修复 */
.feature-card,
.stat-card,
.info-card {
  padding: 24px !important;
  border-radius: 12px !important;
  line-height: 1.6 !important;
}

.feature-card h3,
.stat-card h3,
.info-card h3 {
  margin-bottom: 12px !important;
  line-height: 1.4 !important;
  word-wrap: break-word !important;
}

.feature-card p,
.stat-card p,
.info-card p {
  margin-bottom: 16px !important;
  line-height: 1.6 !important;
  word-wrap: break-word !important;
}

/* 图标与文字对齐修复 */
.icon-text-container {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.icon-text-container .el-icon {
  flex-shrink: 0 !important;
  margin-right: 0 !important;
}

.icon-text-container span,
.icon-text-container p {
  flex: 1 !important;
  min-width: 0 !important;
  word-wrap: break-word !important;
}

/* 响应式文字修复 */
@media (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }

  .el-dialog__header {
    padding: 20px 20px 12px !important;
  }

  .el-dialog__body {
    padding: 8px 20px 20px !important;
  }

  .el-dialog__footer {
    padding: 12px 20px 20px !important;
  }

  .el-card__header {
    padding: 16px 20px 12px !important;
  }

  .el-card__body {
    padding: 20px !important;
  }

  .feature-card,
  .stat-card,
  .info-card {
    padding: 20px !important;
  }

  .el-button {
    padding: 12px 16px !important;
  }

  .el-button--large {
    padding: 16px 20px !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .el-dialog,
  .el-card,
  .el-button,
  .el-input,
  .el-select {
    border-width: 2px !important;
  }

  .el-dialog__title,
  .el-card__header,
  .el-form-item__label {
    font-weight: 700 !important;
  }
}

/* Element Plus 日期选择器修复 */
.el-date-editor {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  width: 100% !important;
}

.el-date-editor .el-input__inner {
  padding: 12px 16px !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
}

.el-date-editor .el-range-separator {
  line-height: 1.5 !important;
  padding: 0 8px !important;
}

/* Element Plus 数字输入框修复 */
.el-input-number {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  width: 100% !important;
}

.el-input-number .el-input__inner {
  padding: 12px 40px 12px 16px !important;
  line-height: 1.5 !important;
  text-align: left !important;
}

/* Element Plus 开关组件修复 */
.el-switch {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: middle !important;
}

.el-switch__label {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  line-height: 1.5 !important;
  margin: 0 8px !important;
}

/* Element Plus 下拉菜单修复 */
.el-dropdown-menu {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.el-dropdown-menu__item {
  padding: 12px 20px !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
}

/* Element Plus 消息框修复 */
.el-message-box {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  border-radius: 12px !important;
}

.el-message-box__header {
  padding: 24px 24px 16px !important;
}

.el-message-box__title {
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
}

.el-message-box__content {
  padding: 8px 24px 24px !important;
  line-height: 1.6 !important;
}

/* Element Plus 通知组件修复 */
.el-notification {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.el-notification__title {
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin-bottom: 8px !important;
}

.el-notification__content {
  line-height: 1.6 !important;
  font-size: 14px !important;
}

/* Element Plus 抽屉组件修复 */
.el-drawer {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
}

.el-drawer__header {
  padding: 24px 24px 16px !important;
  margin-bottom: 0 !important;
}

.el-drawer__title {
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
}

.el-drawer__body {
  padding: 8px 24px 24px !important;
  line-height: 1.6 !important;
}

/* Element Plus 弹出框修复 */
.el-popover {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  border-radius: 8px !important;
  padding: 16px !important;
  line-height: 1.6 !important;
}

.el-popover__title {
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin-bottom: 8px !important;
}

/* Element Plus 工具提示修复 */
.el-tooltip__popper {
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif !important;
  padding: 8px 12px !important;
  line-height: 1.4 !important;
  font-size: 13px !important;
  border-radius: 6px !important;
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .el-dialog,
  .el-card,
  .el-button,
  .feature-card,
  .stat-card,
  .info-card,
  .el-notification,
  .el-message,
  .el-drawer {
    transition: none !important;
    animation: none !important;
  }
}
