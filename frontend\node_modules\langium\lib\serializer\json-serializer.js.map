{"version": 3, "file": "json-serializer.js", "sourceRoot": "", "sources": ["../../src/serializer/json-serializer.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAKjC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAyCjE,MAAM,UAAU,oBAAoB,CAAC,IAAa;IAC9C,OAAO,OAAQ,IAA2B,CAAC,QAAQ,KAAK,QAAQ,CAAC;AACrE,CAAC;AAgDD,SAAS,uBAAuB,CAAC,GAAY;IACzC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,CAAC,CAAC;AAClF,CAAC;AAED,MAAM,OAAO,qBAAqB;IAa9B,YAAY,QAA6B;QAXzC,qEAAqE;QACrE,qBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;QAWzG,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACnE,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC;IAClE,CAAC;IAED,SAAS,CAAC,IAAa,EAAE,OAA8B;QACnD,MAAM,gBAAgB,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;QACvC,MAAM,gBAAgB,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;QAC3C,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;QACrG,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;QAErI,IAAI,CAAC;YACD,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACrC,CAAC;IACL,CAAC;IAED,WAAW,CAA8B,OAAe,EAAE,OAAgC;QACtF,MAAM,kBAAkB,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,QAAQ,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAwB;;QAC9H,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,SAAS,CAAC;QACrB,CAAC;aAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;YACtD,IAAI,QAAQ,EAAE,CAAC;gBACX,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC7C,IAAI,SAAS,GAAG,EAAE,CAAC;gBACnB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,KAAK,cAAc,EAAE,CAAC;oBAClE,IAAI,YAAY,EAAE,CAAC;wBACf,SAAS,GAAG,YAAY,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBACxD,CAAC;yBAAM,CAAC;wBACJ,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;oBAC9C,CAAC;gBACL,CAAC;gBACD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAChE,OAAO;oBACH,IAAI,EAAE,GAAG,SAAS,IAAI,UAAU,EAAE;oBAClC,QAAQ;iBACqB,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACJ,OAAO;oBACH,MAAM,EAAE,MAAA,MAAA,KAAK,CAAC,KAAK,0CAAE,OAAO,mCAAI,6BAA6B;oBAC7D,QAAQ;iBACqB,CAAC;YACtC,CAAC;QACL,CAAC;aAAM,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,OAAO,GAAsC,SAAS,CAAC;YAC3D,IAAI,WAAW,EAAE,CAAC;gBACd,OAAO,GAAG,IAAI,CAAC,iCAAiC,mBAAM,KAAK,EAAG,CAAC;gBAC/D,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,KAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAA,EAAE,CAAC;oBACpD,wEAAwE;oBACxE,OAAO,CAAC,WAAW,CAAC,WAAW,GAAG,MAAA,IAAI,CAAC,eAAe,0CAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;gBAC3E,CAAC;YACL,CAAC;YACD,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE,CAAC;gBACrB,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,qBAAU,KAAK,GAAG;gBACzB,OAAO,CAAC,WAAW,GAAG,MAAA,KAAK,CAAC,QAAQ,0CAAE,IAAI,CAAC;YAC/C,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,qBAAU,KAAK,GAAG;gBACzB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvD,IAAI,OAAO,EAAE,CAAC;oBACT,OAA8B,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC1E,CAAC;YACL,CAAC;YACD,OAAO,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,KAAK,CAAC;QAC5B,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAES,iCAAiC,CAAC,IAA2B;QACnE,MAAM,qBAAqB,GAAuD,OAAO,CAAC,EAAE,CAAC,CAAiB;YAC1G,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,OAAO,CAAC,KAAK;SACvB,CAAA,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3E,MAAM,WAAW,GAAsC,UAAU,CAAC,WAAW,GAAG,EAAE,CAAC;YAEnF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAChE,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBAChG,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,WAAW,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC;gBAC3C,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,QAAQ,CAAC,IAAoB,EAAE,IAAa,EAAE,OAA+B,EAAE,SAAmB,EAAE,iBAA0B,EAAE,cAAuB;QAC7J,KAAK,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;oBAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;wBACnC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;oBACnF,CAAC;yBAAM,IAAI,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC5B,IAAI,CAAC,QAAQ,CAAC,OAAyB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;oBACvF,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,IAAI,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACvF,CAAC;iBAAM,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,QAAQ,CAAC,IAAsB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC;QACD,MAAM,OAAO,GAAG,IAAwB,CAAC;QACzC,OAAO,CAAC,UAAU,GAAG,SAAS,CAAC;QAC/B,OAAO,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC/C,OAAO,CAAC,eAAe,GAAG,cAAc,CAAC;IAC7C,CAAC;IAES,eAAe,CAAC,SAAkB,EAAE,QAAgB,EAAE,IAAa,EAAE,SAAgC,EAAE,OAA+B;QAC5I,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;QACjC,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;QAC7B,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;YACxE,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACX,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC7C,CAAC;gBACD,OAAO;oBACH,QAAQ,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE;oBACvB,GAAG;iBACN,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,KAAK,GAAG,GAAG,CAAC;YAChB,CAAC;QACL,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,GAAG,GAAuB;gBAC5B,QAAQ,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE;aAC1B,CAAC;YACF,GAAG,CAAC,KAAK,GAAG;gBACR,SAAS;gBACT,QAAQ;gBACR,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,GAAG;aACjB,CAAC;YACF,OAAO,GAAG,CAAC;QACf,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAES,UAAU,CAAC,IAAa,EAAE,GAAW,EAAE,YAAmC;QAChF,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,OAAO,0BAA0B,GAAG,GAAG,CAAC;gBAC5C,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBACpB,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtE,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAChE,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,OAAO,mCAAmC,GAAG,GAAG,CAAC;gBACrD,CAAC;gBACD,OAAO,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;YACtC,CAAC;YACD,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;YAC9H,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,OAAO,mCAAmC,GAAG,GAAG,CAAC;YACrD,CAAC;YACD,IAAI,aAAa,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,OAAO,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;YACtC,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,SAAS,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;YAC1G,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,OAAO,yBAAyB,GAAG,GAAG,CAAC;YAC3C,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;IACL,CAAC;CAEJ"}