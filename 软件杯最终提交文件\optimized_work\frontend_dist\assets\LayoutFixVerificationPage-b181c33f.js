import{_ as F,S as B,r as v,o as D,ap as m,i as h,j as i,k as o,n as t,F as u,x as _,A as g,p as c,w as d,B as l,C as p,z as n,R as y,M as C}from"./index-b6a2842e.js";const L={class:"layout-verification-page"},M={class:"fix-overview"},N={class:"overview-container"},P={class:"status-grid"},U={class:"status-icon"},$={class:"status-content"},q={class:"status-badge"},E={class:"fix-details"},I={class:"details-container"},R={class:"fix-categories"},j={class:"category-header"},A={class:"category-info"},G={class:"category-status"},H={class:"fix-count"},J={class:"progress-bar"},K={class:"fix-items"},O={class:"fix-text"},Q={class:"test-tools"},W={class:"tools-container"},X={class:"tools-grid"},Y=["onClick"],Z={class:"tool-icon"},tt={class:"tool-info"},et={class:"quick-navigation"},it={class:"nav-container"},st={class:"nav-buttons"},nt={__name:"LayoutFixVerificationPage",setup(ot){const k=B(),T=v([{id:1,name:"面试页面",description:"面试进行页面布局",status:"fixed",statusText:"已修复",icon:"VideoCamera"},{id:2,name:"报告页面",description:"面试报告展示页面",status:"fixed",statusText:"已修复",icon:"Document"},{id:3,name:"企业管理",description:"企业端管理界面",status:"fixed",statusText:"已修复",icon:"Setting"},{id:4,name:"候选人门户",description:"候选人端界面",status:"fixed",statusText:"已修复",icon:"User"}]),S=v([{id:1,name:"文字重叠问题",description:"修复文字与其他元素重叠、溢出容器边界",gradient:"linear-gradient(135deg, #1890ff 0%, #0066cc 100%)",icon:"Document",fixedCount:8,totalCount:8,progress:100,items:[{id:1,description:"对话框标题文字重叠修复",fixed:!0},{id:2,description:"卡片内容文字溢出修复",fixed:!0},{id:3,description:"按钮文字与图标重叠修复",fixed:!0},{id:4,description:"表单标签文字重叠修复",fixed:!0}]},{id:2,name:"板块重叠问题",description:"修复组件之间相互覆盖、z-index层级错误",gradient:"linear-gradient(135deg, #52c41a 0%, #389e0d 100%)",icon:"Setting",fixedCount:6,totalCount:6,progress:100,items:[{id:1,description:"导航栏与内容区重叠修复",fixed:!0},{id:2,description:"模态框层级问题修复",fixed:!0},{id:3,description:"浮动面板重叠修复",fixed:!0}]},{id:3,name:"响应式失效",description:"修复移动端/平板端布局破坏",gradient:"linear-gradient(135deg, #722ed1 0%, #531dab 100%)",icon:"Smartphone",fixedCount:12,totalCount:12,progress:100,items:[{id:1,description:"移动端网格布局修复",fixed:!0},{id:2,description:"平板端导航栏修复",fixed:!0},{id:3,description:"小屏幕按钮布局修复",fixed:!0},{id:4,description:"响应式图片显示修复",fixed:!0}]}]),b=v([{id:1,name:"移动端测试",description:"模拟不同移动设备的布局效果",icon:"Smartphone"},{id:2,name:"平板端测试",description:"验证平板设备的响应式布局",icon:"Tablet"},{id:3,name:"桌面端测试",description:"检查桌面端的布局完整性",icon:"Monitor"}]),w=v([{id:1,name:"面试页面",path:"/interviewing",icon:"VideoCamera",type:"primary"},{id:2,name:"报告页面",path:"/report",icon:"Document",type:"success"},{id:3,name:"企业管理",path:"/enterprise",icon:"Setting",type:"warning"},{id:4,name:"候选人门户",path:"/candidate",icon:"User",type:"info"}]),V=f=>{m.success(`正在运行 ${f.name}...`)},z=f=>{k.push(f)};return D(()=>{m.success("布局修复验证页面加载完成")}),(f,s)=>{const r=h("el-icon"),x=h("el-button");return i(),o("div",L,[s[5]||(s[5]=t("div",{class:"verification-header"},[t("div",{class:"header-container"},[t("h1",null,"iFlytek 系统布局修复验证"),t("p",null,"全面检查系统UI布局和显示问题修复效果")])],-1)),t("div",M,[t("div",N,[s[0]||(s[0]=t("div",{class:"overview-title"},[t("h2",null,"修复状态概览"),t("p",null,"实时监控各页面布局修复状态")],-1)),t("div",P,[(i(!0),o(u,null,_(T.value,e=>(i(),o("div",{class:g(["status-card",e.status]),key:e.id},[t("div",U,[c(r,null,{default:d(()=>[(i(),l(p(e.icon)))]),_:2},1024)]),t("div",$,[t("h3",null,n(e.name),1),t("p",null,n(e.description),1),t("div",q,n(e.statusText),1)])],2))),128))])])]),t("div",E,[t("div",I,[s[1]||(s[1]=t("div",{class:"section-header"},[t("h3",null,"修复项目详情"),t("p",null,"查看具体的布局问题修复内容")],-1)),t("div",R,[(i(!0),o(u,null,_(S.value,e=>(i(),o("div",{class:"category-card",key:e.id},[t("div",j,[t("div",{class:"category-icon",style:y({background:e.gradient})},[c(r,null,{default:d(()=>[(i(),l(p(e.icon)))]),_:2},1024)],4),t("div",A,[t("h4",null,n(e.name),1),t("p",null,n(e.description),1)]),t("div",G,[t("span",H,n(e.fixedCount)+"/"+n(e.totalCount),1),t("div",J,[t("div",{class:"progress-fill",style:y({width:e.progress+"%"})},null,4)])])]),t("div",K,[(i(!0),o(u,null,_(e.items,a=>(i(),o("div",{class:g(["fix-item",{fixed:a.fixed}]),key:a.id},[c(r,{class:"fix-icon"},{default:d(()=>[(i(),l(p(a.fixed?"Check":"Close")))]),_:2},1024),t("span",O,n(a.description),1)],2))),128))])]))),128))])])]),t("div",Q,[t("div",W,[s[3]||(s[3]=t("div",{class:"section-header"},[t("h3",null,"布局测试工具"),t("p",null,"使用工具验证不同设备和场景下的布局效果")],-1)),t("div",X,[(i(!0),o(u,null,_(b.value,e=>(i(),o("div",{class:"tool-card",key:e.id,onClick:a=>V(e)},[t("div",Z,[c(r,null,{default:d(()=>[(i(),l(p(e.icon)))]),_:2},1024)]),t("div",tt,[t("h4",null,n(e.name),1),t("p",null,n(e.description),1)]),c(x,{type:"primary",size:"small"},{default:d(()=>s[2]||(s[2]=[C("运行测试")])),_:1,__:[2]})],8,Y))),128))])])]),t("div",et,[t("div",it,[s[4]||(s[4]=t("h3",null,"快速导航到各页面",-1)),t("div",st,[(i(!0),o(u,null,_(w.value,e=>(i(),l(x,{key:e.id,onClick:a=>z(e.path),type:e.type,size:"large"},{default:d(()=>[c(r,null,{default:d(()=>[(i(),l(p(e.icon)))]),_:2},1024),C(" "+n(e.name),1)]),_:2},1032,["onClick","type"]))),128))])])])])}}},at=F(nt,[["__scopeId","data-v-8df06439"]]);export{at as default};
