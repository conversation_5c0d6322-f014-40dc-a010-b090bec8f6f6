{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/loading/index.ts"], "sourcesContent": ["import Loading from './src/service'\nimport vLoading from './src/directive'\n\nimport type { App, AppContext, Directive } from 'vue'\nimport type { ElementLoading, LoadingBinding } from './src/directive'\n\n// installer and everything in all\nexport const ElLoading = {\n  install(app: App) {\n    Loading._context = app._context\n    ;(\n      vLoading as Directive<ElementLoading, LoadingBinding> & {\n        _context: AppContext | null\n      }\n    )._context = app._context\n    app.directive('loading', vLoading)\n    app.config.globalProperties.$loading = Loading\n  },\n  directive: vLoading,\n  service: Loading,\n}\n\nexport default ElLoading\nexport { vLoading, vLoading as ElLoadingDirective, Loading as ElLoadingService }\n\nexport * from './src/types'\n"], "names": ["Loading", "vLoading"], "mappings": ";;;;;;;AAEY,MAAC,SAAS,GAAG;AACzB,EAAE,OAAO,CAAC,GAAG,EAAE;AACf,IAAIA,kBAAO,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AACpC,IAAIC,oBAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AACrC,IAAI,GAAG,CAAC,SAAS,CAAC,SAAS,EAAEA,oBAAQ,CAAC,CAAC;AACvC,IAAI,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,GAAGD,kBAAO,CAAC;AACnD,GAAG;AACH,EAAE,SAAS,EAAEC,oBAAQ;AACrB,EAAE,OAAO,EAAED,kBAAO;AAClB;;;;;;;;"}