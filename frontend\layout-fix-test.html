<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek 布局修复测试页面</title>
    <style>
        /* 导入修复样式 */
        @import url('./src/styles/emergency-layout-fix.css');
        @import url('./src/styles/layout-optimization.css');
        
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        /* 紫色背景模拟 */
        .purple-bg-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #1890ff 100%);
            color: white;
            padding: 60px 0;
            position: relative;
            overflow: hidden;
        }
        
        .purple-bg-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.8) 100%);
            z-index: 1;
        }
        
        .content-wrapper {
            position: relative;
            z-index: 2;
        }
        
        /* 测试内容样式 */
        .test-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .test-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            text-align: center;
            opacity: 0.9;
        }
        
        .test-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .test-stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem 1.5rem;
            border-radius: 12px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
            max-width: 200px;
        }
        
        .test-stat-number {
            display: block;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .test-stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .test-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 3rem;
        }
        
        .test-feature-card {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1890ff;
        }
        
        .test-feature-description {
            line-height: 1.6;
            color: #666;
        }
        
        /* 调试信息 */
        .debug-info {
            background: #f8f9fa;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        
        .debug-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1890ff;
        }
        
        .debug-item {
            margin-bottom: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .debug-success {
            color: #52c41a;
        }
        
        .debug-error {
            color: #ff4d4f;
        }
        
        .debug-warning {
            color: #faad14;
        }
        
        /* 测试按钮 */
        .test-buttons {
            text-align: center;
            margin: 2rem 0;
        }
        
        .test-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #0066cc;
            transform: translateY(-2px);
        }
        
        /* 响应式测试 */
        @media (max-width: 768px) {
            .test-title {
                font-size: 2rem;
            }
            
            .test-stats {
                flex-direction: column;
                align-items: center;
                gap: 1rem;
            }
            
            .test-stat-item {
                width: 100%;
                max-width: 250px;
            }
            
            .test-features {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 紫色背景测试区域 -->
    <section class="purple-bg-section">
        <div class="optimized-container content-wrapper">
            <h1 class="test-title">iFlytek 布局修复测试</h1>
            <p class="test-subtitle">验证内容是否正确显示在紫色背景区域内</p>
            
            <div class="test-stats">
                <div class="test-stat-item">
                    <span class="test-stat-number">100%</span>
                    <span class="test-stat-label">容器宽度</span>
                </div>
                <div class="test-stat-item">
                    <span class="test-stat-number">0px</span>
                    <span class="test-stat-label">水平溢出</span>
                </div>
                <div class="test-stat-item">
                    <span class="test-stat-number">✓</span>
                    <span class="test-stat-label">响应式布局</span>
                </div>
            </div>
            
            <div class="test-features">
                <div class="test-feature-card">
                    <h3 class="test-feature-title">容器宽度测试</h3>
                    <p class="test-feature-description">
                        这个卡片应该完全显示在紫色背景区域内，不会超出边界。
                        文字内容应该自动换行，不会造成水平滚动。
                    </p>
                </div>
                <div class="test-feature-card">
                    <h3 class="test-feature-title">响应式测试</h3>
                    <p class="test-feature-description">
                        在不同屏幕尺寸下，布局应该自动调整。
                        移动端显示为单列，桌面端显示为多列。
                    </p>
                </div>
                <div class="test-feature-card">
                    <h3 class="test-feature-title">文字可见性测试</h3>
                    <p class="test-feature-description">
                        所有文字都应该清晰可见，不会被截断或隐藏。
                        长文本应该正确换行而不是溢出容器。
                    </p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 调试信息区域 -->
    <section class="debug-info">
        <div class="optimized-container">
            <h2 class="debug-title">实时调试信息</h2>
            <div id="debug-output">
                <div class="debug-item">正在加载调试信息...</div>
            </div>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="runLayoutTest()">运行布局测试</button>
                <button class="test-btn" onclick="toggleDebugMode()">切换调试模式</button>
                <button class="test-btn" onclick="simulateMobile()">模拟移动端</button>
            </div>
        </div>
    </section>
    
    <script src="./layout-overflow-fix-validation.js"></script>
    <script>
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 iFlytek 布局修复测试页面已加载')
            updateDebugInfo()
            
            // 自动运行验证
            setTimeout(() => {
                if (typeof window.validateLayoutFix === 'function') {
                    window.validateLayoutFix()
                }
            }, 1000)
        })
        
        // 更新调试信息
        function updateDebugInfo() {
            const debugOutput = document.getElementById('debug-output')
            const viewportWidth = window.innerWidth
            const viewportHeight = window.innerHeight
            const bodyScrollWidth = document.body.scrollWidth
            const hasHorizontalScroll = bodyScrollWidth > viewportWidth
            
            const debugInfo = [
                `视口尺寸: ${viewportWidth} × ${viewportHeight}px`,
                `页面宽度: ${bodyScrollWidth}px`,
                `水平滚动: ${hasHorizontalScroll ? '❌ 存在' : '✅ 无'}`,
                `设备类型: ${viewportWidth <= 768 ? '📱 移动端' : viewportWidth <= 1024 ? '📱 平板' : '💻 桌面端'}`,
                `容器最大宽度: ${Math.min(1200, viewportWidth - 48)}px`
            ]
            
            debugOutput.innerHTML = debugInfo.map(info => 
                `<div class="debug-item ${info.includes('❌') ? 'debug-error' : info.includes('✅') ? 'debug-success' : ''}">${info}</div>`
            ).join('')
        }
        
        // 运行布局测试
        function runLayoutTest() {
            console.log('🔍 手动运行布局测试...')
            if (typeof window.validateLayoutFix === 'function') {
                const results = window.validateLayoutFix()
                
                if (results) {
                    const successRate = results.summary.successRate
                    const message = successRate >= 90 ? '🎉 布局修复效果优秀！' : 
                                   successRate >= 70 ? '✅ 布局修复基本成功' : 
                                   '⚠️ 布局仍需进一步优化'
                    
                    alert(`${message}\n成功率: ${successRate}%\n通过: ${results.summary.passedChecks}\n失败: ${results.summary.failedChecks}`)
                }
            } else {
                alert('❌ 验证脚本未加载')
            }
        }
        
        // 切换调试模式
        function toggleDebugMode() {
            document.body.classList.toggle('debug-layout')
            const isDebug = document.body.classList.contains('debug-layout')
            console.log(isDebug ? '🔍 调试模式已开启' : '🔍 调试模式已关闭')
        }
        
        // 模拟移动端
        function simulateMobile() {
            const viewport = document.querySelector('meta[name="viewport"]')
            if (viewport) {
                viewport.content = 'width=375, initial-scale=1.0'
                setTimeout(() => {
                    location.reload()
                }, 500)
            }
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            updateDebugInfo()
        })
        
        // 监听滚动事件
        window.addEventListener('scroll', function() {
            const scrollX = window.scrollX || window.pageXOffset
            if (scrollX > 0) {
                console.warn('⚠️ 检测到水平滚动:', scrollX)
            }
        })
    </script>
</body>
</html>
