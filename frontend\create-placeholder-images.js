import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建简单的SVG占位符图片
function createSVGPlaceholder(filename, title, color = '#667eea') {
    const svgContent = `<svg width="400" height="225" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
                <stop offset="100%" style="stop-color:${adjustBrightness(color, -30)};stop-opacity:1" />
            </linearGradient>
        </defs>
        <rect width="400" height="225" fill="url(#grad)"/>
        <text x="200" y="100" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle" font-weight="bold">
            ${title}
        </text>
        <text x="200" y="130" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)" text-anchor="middle">
            多模态面试评估系统
        </text>
        <rect x="20" y="20" width="360" height="185" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="5"/>
        <circle cx="50" cy="50" r="8" fill="rgba(255,255,255,0.5)"/>
        <circle cx="70" cy="50" r="8" fill="rgba(255,255,255,0.3)"/>
        <circle cx="90" cy="50" r="8" fill="rgba(255,255,255,0.2)"/>
    </svg>`;
    
    return svgContent;
}

function adjustBrightness(hex, percent) {
    // 简单的颜色调整函数
    const num = parseInt(hex.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

// 需要创建的图片列表
const images = [
    { filename: 'video-poster-1.jpg', title: '系统演示视频', color: '#667eea' },
    { filename: 'video-poster-2.jpg', title: '功能展示视频', color: '#f093fb' },
    { filename: 'video-poster-3.jpg', title: '技术架构视频', color: '#a8edea' },
    { filename: 'video-poster-ai.jpg', title: 'AI领域演示', color: '#4CAF50' },
    { filename: 'video-poster-bigdata.jpg', title: '大数据演示', color: '#2196F3' },
    { filename: 'video-poster-iot.jpg', title: 'IoT演示', color: '#FF9800' },
    { filename: 'chapter-1.jpg', title: '第一章节', color: '#9C27B0' },
    { filename: 'chapter-2.jpg', title: '第二章节', color: '#F44336' },
    { filename: 'chapter-3.jpg', title: '第三章节', color: '#009688' },
    { filename: 'chapter-4.jpg', title: '第四章节', color: '#795548' }
];

// 创建图片目录
const imageDir = path.join(__dirname, 'public/demo/images');
if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
}

console.log('🎨 开始创建占位符图片...');

// 生成所有图片
images.forEach((img, index) => {
    const svgPath = path.join(imageDir, img.filename.replace('.jpg', '.svg'));
    const svgContent = createSVGPlaceholder(img.filename, img.title, img.color);
    
    fs.writeFileSync(svgPath, svgContent);
    console.log(`  ✅ 创建: ${img.filename} -> ${img.filename.replace('.jpg', '.svg')}`);
    
    // 同时创建一个简单的HTML文件作为JPG的替代
    const htmlPath = path.join(imageDir, img.filename.replace('.jpg', '.html'));
    const htmlContent = `<!DOCTYPE html>
<html><head><title>${img.title}</title></head>
<body style="margin:0;padding:0;background:${img.color};color:white;font-family:Arial;display:flex;align-items:center;justify-content:center;height:225px;width:400px;">
<div style="text-align:center;">
<h3>${img.title}</h3>
<p>多模态面试评估系统</p>
</div>
</body></html>`;
    
    fs.writeFileSync(htmlPath, htmlContent);
});

// 创建视频占位符
const videoDir = path.join(__dirname, 'public/demo/videos');
if (!fs.existsSync(videoDir)) {
    fs.mkdirSync(videoDir, { recursive: true });
}

const videoPlaceholder = path.join(videoDir, 'demo-complete.mp4.txt');
fs.writeFileSync(videoPlaceholder, `这是demo-complete.mp4的占位符文件。
实际部署时，请将真实的演示视频重命名为demo-complete.mp4并放置在此目录中。

建议的视频规格：
- 分辨率: 1920x1080
- 帧率: 30fps
- 格式: MP4 (H.264)
- 时长: 5-15分钟

创建时间: ${new Date().toLocaleString()}
`);

console.log('🎬 创建视频占位符说明文件');
console.log('\n🎉 占位符创建完成！');
console.log('\n📋 创建的文件:');
images.forEach(img => {
    console.log(`  - ${img.filename.replace('.jpg', '.svg')}`);
    console.log(`  - ${img.filename.replace('.jpg', '.html')}`);
});
console.log('  - demo-complete.mp4.txt');

console.log('\n💡 使用说明:');
console.log('  1. SVG文件可以直接在浏览器中显示');
console.log('  2. HTML文件提供了简单的占位符页面');
console.log('  3. 实际部署时请替换为真实的图片和视频文件');
