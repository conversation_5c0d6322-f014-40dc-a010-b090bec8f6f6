#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态面试评估系统演示功能完整测试脚本
测试所有演示组件和功能的完整性
"""

import requests
import json
import time
import sys
from pathlib import Path

class DemoFunctionalityTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:5173"
        self.test_results = []
        
    def log_result(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
        
    def test_backend_health(self):
        """测试后端健康状态"""
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_result("后端健康检查", True, f"状态: {data.get('status')}")
                return True
            else:
                self.log_result("后端健康检查", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("后端健康检查", False, str(e))
            return False
            
    def test_frontend_accessibility(self):
        """测试前端可访问性"""
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.log_result("前端可访问性", True, "页面正常加载")
                return True
            else:
                self.log_result("前端可访问性", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("前端可访问性", False, str(e))
            return False
            
    def test_demo_page_accessibility(self):
        """测试演示页面可访问性"""
        try:
            response = requests.get(f"{self.frontend_url}/demo", timeout=5)
            if response.status_code == 200:
                self.log_result("演示页面可访问性", True, "演示页面正常加载")
                return True
            else:
                self.log_result("演示页面可访问性", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("演示页面可访问性", False, str(e))
            return False
            
    def test_api_endpoints(self):
        """测试API端点"""
        endpoints = [
            ("/api/v1/domains", "技术领域API"),
            ("/api/v1/positions", "岗位信息API"),
            ("/health", "健康检查API")
        ]
        
        all_passed = True
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    self.log_result(name, True, "API响应正常")
                else:
                    self.log_result(name, False, f"状态码: {response.status_code}")
                    all_passed = False
            except Exception as e:
                self.log_result(name, False, str(e))
                all_passed = False
                
        return all_passed
        
    def test_interview_flow(self):
        """测试面试流程API"""
        try:
            # 测试面试开始
            start_data = {
                "domain": "人工智能",
                "position": "AI工程师"
            }
            response = requests.post(
                f"{self.backend_url}/api/v1/interview/start",
                json=start_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if "question" in result and len(result["question"]) > 0:
                    self.log_result("面试开始API", True, f"问题长度: {len(result['question'])}")
                    return True
                else:
                    self.log_result("面试开始API", False, "返回数据格式错误")
                    return False
            else:
                self.log_result("面试开始API", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_result("面试开始API", False, str(e))
            return False
            
    def test_multimodal_analysis(self):
        """测试多模态分析功能"""
        try:
            # 模拟多模态分析请求
            analysis_data = {
                "text": "我有5年的机器学习开发经验，熟悉TensorFlow和PyTorch框架。",
                "domain": "人工智能",
                "position": "机器学习工程师"
            }
            
            response = requests.post(
                f"{self.backend_url}/api/v1/analysis/multimodal",
                json=analysis_data,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                if "capabilities" in result and len(result["capabilities"]) >= 6:
                    self.log_result("多模态分析API", True, f"生成了 {len(result['capabilities'])} 个能力指标")
                    return True
                else:
                    self.log_result("多模态分析API", False, "返回的能力指标不完整")
                    return False
            else:
                self.log_result("多模态分析API", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_result("多模态分析API", False, str(e))
            return False
            
    def test_component_files(self):
        """测试演示组件文件完整性"""
        frontend_path = Path("frontend/src/components/demo")
        required_components = [
            "SystemOverview.vue",
            "DomainDemos.vue", 
            "VideoDemo.vue",
            "EnhancedVideoPlayer.vue",
            "InteractiveDemo.vue",
            "AnalysisDemo.vue",
            "ArchitectureDemo.vue"
        ]
        
        all_exist = True
        for component in required_components:
            component_path = frontend_path / component
            if component_path.exists():
                self.log_result(f"组件文件 {component}", True, "文件存在")
            else:
                self.log_result(f"组件文件 {component}", False, "文件缺失")
                all_exist = False
                
        return all_exist
        
    def test_demo_service_data(self):
        """测试演示服务数据完整性"""
        try:
            demo_service_path = Path("frontend/src/services/demoService.js")
            if demo_service_path.exists():
                content = demo_service_path.read_text(encoding='utf-8')
                
                # 检查关键数据结构
                required_exports = [
                    "coreCapabilities",
                    "domainVideos", 
                    "demoVideos",
                    "DemoService"
                ]
                
                all_present = True
                for export in required_exports:
                    if export in content:
                        self.log_result(f"演示数据 {export}", True, "数据结构存在")
                    else:
                        self.log_result(f"演示数据 {export}", False, "数据结构缺失")
                        all_present = False
                        
                return all_present
            else:
                self.log_result("演示服务文件", False, "demoService.js 文件缺失")
                return False
                
        except Exception as e:
            self.log_result("演示服务数据", False, str(e))
            return False
            
    def test_dependencies(self):
        """测试依赖库安装"""
        package_json_path = Path("frontend/package.json")
        if package_json_path.exists():
            try:
                with open(package_json_path, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                    
                dependencies = package_data.get('dependencies', {})
                required_deps = ['echarts', 'aos', 'element-plus', 'vue']
                
                all_installed = True
                for dep in required_deps:
                    if dep in dependencies:
                        self.log_result(f"依赖库 {dep}", True, f"版本: {dependencies[dep]}")
                    else:
                        self.log_result(f"依赖库 {dep}", False, "未安装")
                        all_installed = False
                        
                return all_installed
                
            except Exception as e:
                self.log_result("依赖库检查", False, str(e))
                return False
        else:
            self.log_result("package.json", False, "文件不存在")
            return False
            
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 多模态面试评估系统演示功能完整测试开始\n")
        
        tests = [
            ("后端服务", self.test_backend_health),
            ("前端服务", self.test_frontend_accessibility),
            ("演示页面", self.test_demo_page_accessibility),
            ("API端点", self.test_api_endpoints),
            ("面试流程", self.test_interview_flow),
            ("多模态分析", self.test_multimodal_analysis),
            ("组件文件", self.test_component_files),
            ("演示数据", self.test_demo_service_data),
            ("依赖库", self.test_dependencies)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n=== {test_name}测试 ===")
            if test_func():
                passed_tests += 1
            time.sleep(0.5)  # 避免请求过快
            
        # 输出总结
        print("\n" + "="*50)
        print("📊 测试结果总结")
        print("="*50)
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"通过测试: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("🎉 演示功能完整性优秀！系统已准备就绪")
        elif success_rate >= 70:
            print("✅ 演示功能基本完整，可以正常使用")
        else:
            print("⚠️ 演示功能存在问题，需要进一步修复")
            
        # 详细结果
        print("\n详细测试结果:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test']}: {result['message']}")
            
        return success_rate >= 70

if __name__ == "__main__":
    tester = DemoFunctionalityTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
