{"version": 3, "sources": ["../../../src/diagrams/xychart/parser/xychart.jison", "../../../src/diagrams/xychart/chartBuilder/interfaces.ts", "../../../src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/index.ts", "../../../src/diagrams/xychart/chartBuilder/components/chartTitle.ts", "../../../src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts", "../../../src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts", "../../../src/diagrams/xychart/chartBuilder/components/plot/index.ts", "../../../src/diagrams/xychart/chartBuilder/orchestrator.ts", "../../../src/diagrams/xychart/chartBuilder/index.ts", "../../../src/diagrams/xychart/xychartDb.ts", "../../../src/diagrams/xychart/xychartRenderer.ts", "../../../src/diagrams/xychart/xychartDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,10,12,14,16,18,19,21,23],$V1=[2,6],$V2=[1,3],$V3=[1,5],$V4=[1,6],$V5=[1,7],$V6=[1,5,10,12,14,16,18,19,21,23,34,35,36],$V7=[1,25],$V8=[1,26],$V9=[1,28],$Va=[1,29],$Vb=[1,30],$Vc=[1,31],$Vd=[1,32],$Ve=[1,33],$Vf=[1,34],$Vg=[1,35],$Vh=[1,36],$Vi=[1,37],$Vj=[1,43],$Vk=[1,42],$Vl=[1,47],$Vm=[1,50],$Vn=[1,10,12,14,16,18,19,21,23,34,35,36],$Vo=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36],$Vp=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36,41,42,43,44,45,46,47,48,49,50],$Vq=[1,64];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"eol\":4,\"XYCHART\":5,\"chartConfig\":6,\"document\":7,\"CHART_ORIENTATION\":8,\"statement\":9,\"title\":10,\"text\":11,\"X_AXIS\":12,\"parseXAxis\":13,\"Y_AXIS\":14,\"parseYAxis\":15,\"LINE\":16,\"plotData\":17,\"BAR\":18,\"acc_title\":19,\"acc_title_value\":20,\"acc_descr\":21,\"acc_descr_value\":22,\"acc_descr_multiline_value\":23,\"SQUARE_BRACES_START\":24,\"commaSeparatedNumbers\":25,\"SQUARE_BRACES_END\":26,\"NUMBER_WITH_DECIMAL\":27,\"COMMA\":28,\"xAxisData\":29,\"bandData\":30,\"ARROW_DELIMITER\":31,\"commaSeparatedTexts\":32,\"yAxisData\":33,\"NEWLINE\":34,\"SEMI\":35,\"EOF\":36,\"alphaNum\":37,\"STR\":38,\"MD_STR\":39,\"alphaNumToken\":40,\"AMP\":41,\"NUM\":42,\"ALPHA\":43,\"PLUS\":44,\"EQUALS\":45,\"MULT\":46,\"DOT\":47,\"BRKT\":48,\"MINUS\":49,\"UNDERSCORE\":50,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",5:\"XYCHART\",8:\"CHART_ORIENTATION\",10:\"title\",12:\"X_AXIS\",14:\"Y_AXIS\",16:\"LINE\",18:\"BAR\",19:\"acc_title\",20:\"acc_title_value\",21:\"acc_descr\",22:\"acc_descr_value\",23:\"acc_descr_multiline_value\",24:\"SQUARE_BRACES_START\",26:\"SQUARE_BRACES_END\",27:\"NUMBER_WITH_DECIMAL\",28:\"COMMA\",31:\"ARROW_DELIMITER\",34:\"NEWLINE\",35:\"SEMI\",36:\"EOF\",38:\"STR\",39:\"MD_STR\",41:\"AMP\",42:\"NUM\",43:\"ALPHA\",44:\"PLUS\",45:\"EQUALS\",46:\"MULT\",47:\"DOT\",48:\"BRKT\",49:\"MINUS\",50:\"UNDERSCORE\"},\nproductions_: [0,[3,2],[3,3],[3,2],[3,1],[6,1],[7,0],[7,2],[9,2],[9,2],[9,2],[9,2],[9,2],[9,3],[9,2],[9,3],[9,2],[9,2],[9,1],[17,3],[25,3],[25,1],[13,1],[13,2],[13,1],[29,1],[29,3],[30,3],[32,3],[32,1],[15,1],[15,2],[15,1],[33,3],[4,1],[4,1],[4,1],[11,1],[11,1],[11,1],[37,1],[37,2],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 5:\n yy.setOrientation($$[$0]); \nbreak;\ncase 9:\n yy.setDiagramTitle($$[$0].text.trim()); \nbreak;\ncase 12:\n yy.setLineData({text: '', type: 'text'}, $$[$0]); \nbreak;\ncase 13:\n yy.setLineData($$[$0-1], $$[$0]); \nbreak;\ncase 14:\n yy.setBarData({text: '', type: 'text'}, $$[$0]); \nbreak;\ncase 15:\n yy.setBarData($$[$0-1], $$[$0]); \nbreak;\ncase 16:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 17: case 18:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 19:\n this.$ = $$[$0-1] \nbreak;\ncase 20:\n this.$ = [Number($$[$0-2]), ...$$[$0]] \nbreak;\ncase 21:\n this.$ = [Number($$[$0])] \nbreak;\ncase 22:\nyy.setXAxisTitle($$[$0]);\nbreak;\ncase 23:\nyy.setXAxisTitle($$[$0-1]);\nbreak;\ncase 24:\nyy.setXAxisTitle({type: 'text', text: ''});\nbreak;\ncase 25:\nyy.setXAxisBand($$[$0]);\nbreak;\ncase 26:\nyy.setXAxisRangeData(Number($$[$0-2]), Number($$[$0]));\nbreak;\ncase 27:\nthis.$ = $$[$0-1]\nbreak;\ncase 28:\n this.$ = [$$[$0-2], ...$$[$0]] \nbreak;\ncase 29:\n this.$ = [$$[$0]] \nbreak;\ncase 30:\nyy.setYAxisTitle($$[$0]);\nbreak;\ncase 31:\nyy.setYAxisTitle($$[$0-1]);\nbreak;\ncase 32:\nyy.setYAxisTitle({type: \"text\", text: \"\"});\nbreak;\ncase 33:\nyy.setYAxisRangeData(Number($$[$0-2]), Number($$[$0]));\nbreak;\ncase 37:\n this.$={text:$$[$0], type: 'text'};\nbreak;\ncase 38:\n this.$={text: $$[$0], type: 'text'};\nbreak;\ncase 39:\n this.$={text: $$[$0], type: 'markdown'};\nbreak;\ncase 40:\nthis.$=$$[$0];\nbreak;\ncase 41:\nthis.$=$$[$0-1]+''+$$[$0];\nbreak;\n}\n},\ntable: [o($V0,$V1,{3:1,4:2,7:4,5:$V2,34:$V3,35:$V4,36:$V5}),{1:[3]},o($V0,$V1,{4:2,7:4,3:8,5:$V2,34:$V3,35:$V4,36:$V5}),o($V0,$V1,{4:2,7:4,6:9,3:10,5:$V2,8:[1,11],34:$V3,35:$V4,36:$V5}),{1:[2,4],9:12,10:[1,13],12:[1,14],14:[1,15],16:[1,16],18:[1,17],19:[1,18],21:[1,19],23:[1,20]},o($V6,[2,34]),o($V6,[2,35]),o($V6,[2,36]),{1:[2,1]},o($V0,$V1,{4:2,7:4,3:21,5:$V2,34:$V3,35:$V4,36:$V5}),{1:[2,3]},o($V6,[2,5]),o($V0,[2,7],{4:22,34:$V3,35:$V4,36:$V5}),{11:23,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:39,13:38,24:$Vj,27:$Vk,29:40,30:41,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:45,15:44,27:$Vl,33:46,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:49,17:48,24:$Vm,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:52,17:51,24:$Vm,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{20:[1,53]},{22:[1,54]},o($Vn,[2,18]),{1:[2,2]},o($Vn,[2,8]),o($Vn,[2,9]),o($Vo,[2,37],{40:55,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi}),o($Vo,[2,38]),o($Vo,[2,39]),o($Vp,[2,40]),o($Vp,[2,42]),o($Vp,[2,43]),o($Vp,[2,44]),o($Vp,[2,45]),o($Vp,[2,46]),o($Vp,[2,47]),o($Vp,[2,48]),o($Vp,[2,49]),o($Vp,[2,50]),o($Vp,[2,51]),o($Vn,[2,10]),o($Vn,[2,22],{30:41,29:56,24:$Vj,27:$Vk}),o($Vn,[2,24]),o($Vn,[2,25]),{31:[1,57]},{11:59,32:58,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},o($Vn,[2,11]),o($Vn,[2,30],{33:60,27:$Vl}),o($Vn,[2,32]),{31:[1,61]},o($Vn,[2,12]),{17:62,24:$Vm},{25:63,27:$Vq},o($Vn,[2,14]),{17:65,24:$Vm},o($Vn,[2,16]),o($Vn,[2,17]),o($Vp,[2,41]),o($Vn,[2,23]),{27:[1,66]},{26:[1,67]},{26:[2,29],28:[1,68]},o($Vn,[2,31]),{27:[1,69]},o($Vn,[2,13]),{26:[1,70]},{26:[2,21],28:[1,71]},o($Vn,[2,15]),o($Vn,[2,26]),o($Vn,[2,27]),{11:59,32:72,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},o($Vn,[2,33]),o($Vn,[2,19]),{25:73,27:$Vq},{26:[2,28]},{26:[2,20]}],\ndefaultActions: {8:[2,1],10:[2,3],21:[2,2],72:[2,28],73:[2,20]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2: this.popState(); return 34; \nbreak;\ncase 3: this.popState(); return 34; \nbreak;\ncase 4:return 34;\nbreak;\ncase 5:/* do nothing */\nbreak;\ncase 6: return 10; \nbreak;\ncase 7: this.pushState(\"acc_title\");return 19; \nbreak;\ncase 8: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 9: this.pushState(\"acc_descr\");return 21; \nbreak;\ncase 10: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 11: this.pushState(\"acc_descr_multiline\");\nbreak;\ncase 12: this.popState(); \nbreak;\ncase 13: return \"acc_descr_multiline_value\"; \nbreak;\ncase 14:return 5;\nbreak;\ncase 15:return 8\nbreak;\ncase 16: this.pushState(\"axis_data\"); return \"X_AXIS\"; \nbreak;\ncase 17: this.pushState(\"axis_data\"); return \"Y_AXIS\"; \nbreak;\ncase 18: this.pushState(\"axis_band_data\"); return 24; \nbreak;\ncase 19: return 31; \nbreak;\ncase 20: this.pushState(\"data\"); return 16; \nbreak;\ncase 21: this.pushState(\"data\"); return 18; \nbreak;\ncase 22: this.pushState(\"data_inner\"); return 24; \nbreak;\ncase 23: return 27; \nbreak;\ncase 24: this.popState(); return 26; \nbreak;\ncase 25: this.popState(); \nbreak;\ncase 26:this.pushState(\"string\");\nbreak;\ncase 27:this.popState();\nbreak;\ncase 28:return \"STR\";\nbreak;\ncase 29:return 24\nbreak;\ncase 30:return 26\nbreak;\ncase 31:return 43;\nbreak;\ncase 32:return 'COLON';\nbreak;\ncase 33:return 44;\nbreak;\ncase 34:return 28;\nbreak;\ncase 35:return 45;\nbreak;\ncase 36:return 46;\nbreak;\ncase 37:return 48;\nbreak;\ncase 38:return 50;\nbreak;\ncase 39:return 47;\nbreak;\ncase 40:return 41;\nbreak;\ncase 41:return 49;\nbreak;\ncase 42:return 42;\nbreak;\ncase 43:/* skip */\nbreak;\ncase 44:return 35;\nbreak;\ncase 45:return 36;\nbreak;\n}\n},\nrules: [/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:(\\r?\\n))/i,/^(?:(\\r?\\n))/i,/^(?:[\\n\\r]+)/i,/^(?:%%[^\\n]*)/i,/^(?:title\\b)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:\\{)/i,/^(?:[^\\}]*)/i,/^(?:xychart-beta\\b)/i,/^(?:(?:vertical|horizontal))/i,/^(?:x-axis\\b)/i,/^(?:y-axis\\b)/i,/^(?:\\[)/i,/^(?:-->)/i,/^(?:line\\b)/i,/^(?:bar\\b)/i,/^(?:\\[)/i,/^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i,/^(?:\\])/i,/^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i,/^(?:[\"])/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:\\[)/i,/^(?:\\])/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\\*)/i,/^(?:#)/i,/^(?:[\\_])/i,/^(?:\\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\\s+)/i,/^(?:;)/i,/^(?:$)/i],\nconditions: {\"data_inner\":{\"rules\":[0,1,4,5,6,7,9,11,14,15,16,17,20,21,23,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"data\":{\"rules\":[0,1,3,4,5,6,7,9,11,14,15,16,17,20,21,22,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"axis_band_data\":{\"rules\":[0,1,4,5,6,7,9,11,14,15,16,17,20,21,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"axis_data\":{\"rules\":[0,1,2,4,5,6,7,9,11,14,15,16,17,18,19,20,21,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"acc_descr_multiline\":{\"rules\":[12,13],\"inclusive\":false},\"acc_descr\":{\"rules\":[10],\"inclusive\":false},\"acc_title\":{\"rules\":[8],\"inclusive\":false},\"title\":{\"rules\":[],\"inclusive\":false},\"md_string\":{\"rules\":[],\"inclusive\":false},\"string\":{\"rules\":[27,28],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,4,5,6,7,9,11,14,15,16,17,20,21,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "export interface XYChartAxisThemeConfig {\n  titleColor: string;\n  labelColor: string;\n  tickColor: string;\n  axisLineColor: string;\n}\n\nexport interface XYChartThemeConfig {\n  backgroundColor: string;\n  titleColor: string;\n  xAxisLabelColor: string;\n  xAxisTitleColor: string;\n  xAxisTickColor: string;\n  xAxisLineColor: string;\n  yAxisLabelColor: string;\n  yAxisTitleColor: string;\n  yAxisTickColor: string;\n  yAxisLineColor: string;\n  plotColorPalette: string;\n}\n\nexport interface ChartComponent {\n  calculateSpace(availableSpace: Dimension): Dimension;\n  setBoundingBoxXY(point: Point): void;\n  getDrawableElements(): DrawableElem[];\n}\n\nexport type SimplePlotDataType = [string, number][];\n\nexport interface LinePlotData {\n  type: 'line';\n  strokeFill: string;\n  strokeWidth: number;\n  data: SimplePlotDataType;\n}\n\nexport interface BarPlotData {\n  type: 'bar';\n  fill: string;\n  data: SimplePlotDataType;\n}\n\nexport type PlotData = LinePlotData | BarPlotData;\n\nexport function isBarPlot(data: PlotData): data is BarPlotData {\n  return data.type === 'bar';\n}\n\nexport interface BandAxisDataType {\n  type: 'band';\n  title: string;\n  categories: string[];\n}\n\nexport interface LinearAxisDataType {\n  type: 'linear';\n  title: string;\n  min: number;\n  max: number;\n}\n\nexport type AxisDataType = LinearAxisDataType | BandAxisDataType;\n\nexport function isBandAxisData(data: AxisDataType): data is BandAxisDataType {\n  return data.type === 'band';\n}\n\nexport function isLinearAxisData(data: AxisDataType): data is LinearAxisDataType {\n  return data.type === 'linear';\n}\n\n/**\n * For now we are keeping this configs as we are removing the required fields while generating the config.type.ts file\n * we should remove `XYChartAxisConfig` and `XYChartConfig` after we started using required fields\n */\nexport interface XYChartAxisConfig {\n  showLabel: boolean;\n  labelFontSize: number;\n  labelPadding: number;\n  showTitle: boolean;\n  titleFontSize: number;\n  titlePadding: number;\n  showTick: boolean;\n  tickLength: number;\n  tickWidth: number;\n  showAxisLine: boolean;\n  axisLineWidth: number;\n}\n\nexport interface XYChartConfig {\n  width: number;\n  height: number;\n  titleFontSize: number;\n  titlePadding: number;\n  showTitle: boolean;\n  showDataLabel: boolean;\n  xAxis: XYChartAxisConfig;\n  yAxis: XYChartAxisConfig;\n  chartOrientation: 'vertical' | 'horizontal';\n  plotReservedSpacePercent: number;\n}\n\nexport interface XYChartData {\n  xAxis: AxisDataType;\n  yAxis: AxisDataType;\n  title: string;\n  plots: PlotData[];\n}\n\nexport interface Dimension {\n  width: number;\n  height: number;\n}\n\nexport interface BoundingRect extends Point, Dimension {}\n\nexport interface Point {\n  x: number;\n  y: number;\n}\n\nexport type TextHorizontalPos = 'left' | 'center' | 'right';\nexport type TextVerticalPos = 'top' | 'middle';\n\nexport interface RectElem extends Point {\n  width: number;\n  height: number;\n  fill: string;\n  strokeWidth: number;\n  strokeFill: string;\n}\n\nexport interface TextElem extends Point {\n  text: string;\n  fill: string;\n  verticalPos: TextVerticalPos;\n  horizontalPos: TextHorizontalPos;\n  fontSize: number;\n  rotation: number;\n}\n\nexport interface PathElem {\n  path: string;\n  fill?: string;\n  strokeWidth: number;\n  strokeFill: string;\n}\n\nexport type DrawableElem =\n  | {\n      groupTexts: string[];\n      type: 'rect';\n      data: RectElem[];\n    }\n  | {\n      groupTexts: string[];\n      type: 'text';\n      data: TextElem[];\n    }\n  | {\n      groupTexts: string[];\n      type: 'path';\n      data: PathElem[];\n    };\n", "import type { SVGGroup } from '../../../diagram-api/types.js';\nimport { computeDimensionOfText } from '../../../rendering-util/createText.js';\nimport type { Dimension } from './interfaces.js';\n\nexport interface TextDimensionCalculator {\n  getMaxDimension(texts: string[], fontSize: number): Dimension;\n}\n\nexport class TextDimensionCalculatorWithFont implements TextDimensionCalculator {\n  constructor(private parentGroup: SVGGroup) {}\n  getMaxDimension(texts: string[], fontSize: number): Dimension {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize,\n      };\n    }\n\n    const dimension: Dimension = {\n      width: 0,\n      height: 0,\n    };\n\n    const elem = this.parentGroup\n      .append('g')\n      .attr('visibility', 'hidden')\n      .attr('font-size', fontSize);\n\n    for (const t of texts) {\n      const bbox = computeDimensionOfText(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n}\n", "import type {\n  BoundingRect,\n  Dimension,\n  DrawableElem,\n  Point,\n  XYChartAxisConfig,\n  XYChartAxisThemeConfig,\n} from '../../interfaces.js';\nimport type { TextDimensionCalculator } from '../../textDimensionCalculator.js';\nimport type { Axis, AxisPosition } from './index.js';\n\nconst BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nconst MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\n\nexport abstract class BaseAxis implements Axis {\n  protected boundingRect: BoundingRect = { x: 0, y: 0, width: 0, height: 0 };\n  protected axisPosition: AxisPosition = 'left';\n  private range: [number, number];\n  protected showTitle = false;\n  protected showLabel = false;\n  protected showTick = false;\n  protected showAxisLine = false;\n  protected outerPadding = 0;\n  protected titleTextHeight = 0;\n  protected labelTextHeight = 0;\n\n  constructor(\n    protected axisConfig: XYChartAxisConfig,\n    protected title: string,\n    protected textDimensionCalculator: TextDimensionCalculator,\n    protected axisThemeConfig: XYChartAxisThemeConfig\n  ) {\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = 'left';\n  }\n\n  setRange(range: [number, number]): void {\n    this.range = range;\n    if (this.axisPosition === 'left' || this.axisPosition === 'right') {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n\n  getRange(): [number, number] {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n\n  setAxisPosition(axisPosition: AxisPosition): void {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n\n  abstract getScaleValue(value: number | string): number;\n\n  abstract recalculateScale(): void;\n\n  abstract getTickValues(): (string | number)[];\n\n  getTickDistance(): number {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n\n  getAxisOuterPadding(): number {\n    return this.outerPadding;\n  }\n\n  private getLabelDimension(): Dimension {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n\n  recalculateOuterPaddingToDrawBar(): void {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor((BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance()) / 2);\n    }\n    this.recalculateScale();\n  }\n\n  private calculateSpaceIfDrawnHorizontally(availableSpace: Dimension) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n\n  private calculateSpaceIfDrawnVertical(availableSpace: Dimension) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n\n  calculateSpace(availableSpace: Dimension): Dimension {\n    if (this.axisPosition === 'left' || this.axisPosition === 'right') {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height,\n    };\n  }\n\n  setBoundingBoxXY(point: Point): void {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n\n  private getDrawableElementsForLeftAxis(): DrawableElem[] {\n    const drawableElement: DrawableElem[] = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['left-axis', 'axisl-line'],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${\n              this.boundingRect.y + this.boundingRect.height\n            } `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth,\n          },\n        ],\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['left-axis', 'label'],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x:\n            this.boundingRect.x +\n            this.boundingRect.width -\n            (this.showLabel ? this.axisConfig.labelPadding : 0) -\n            (this.showTick ? this.axisConfig.tickLength : 0) -\n            (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: 'middle',\n          horizontalPos: 'right',\n        })),\n      });\n    }\n    if (this.showTick) {\n      const x =\n        this.boundingRect.x +\n        this.boundingRect.width -\n        (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['left-axis', 'ticks'],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${\n            x - this.axisConfig.tickLength\n          },${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth,\n        })),\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['left-axis', 'title'],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: 'top',\n            horizontalPos: 'center',\n          },\n        ],\n      });\n    }\n    return drawableElement;\n  }\n  private getDrawableElementsForBottomAxis(): DrawableElem[] {\n    const drawableElement: DrawableElem[] = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['bottom-axis', 'axis-line'],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${\n              this.boundingRect.x + this.boundingRect.width\n            },${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth,\n          },\n        ],\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['bottom-axis', 'label'],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y:\n            this.boundingRect.y +\n            this.axisConfig.labelPadding +\n            (this.showTick ? this.axisConfig.tickLength : 0) +\n            (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: 'top',\n          horizontalPos: 'center',\n        })),\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['bottom-axis', 'ticks'],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${\n            y + this.axisConfig.tickLength\n          }`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth,\n        })),\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['bottom-axis', 'title'],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y:\n              this.boundingRect.y +\n              this.boundingRect.height -\n              this.axisConfig.titlePadding -\n              this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: 'top',\n            horizontalPos: 'center',\n          },\n        ],\n      });\n    }\n    return drawableElement;\n  }\n  private getDrawableElementsForTopAxis(): DrawableElem[] {\n    const drawableElement: DrawableElem[] = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['top-axis', 'axis-line'],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${\n              this.boundingRect.x + this.boundingRect.width\n            },${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth,\n          },\n        ],\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['top-axis', 'label'],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y:\n            this.boundingRect.y +\n            (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) +\n            this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: 'top',\n          horizontalPos: 'center',\n        })),\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['top-axis', 'ticks'],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${\n            y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)\n          } L ${this.getScaleValue(tick)},${\n            y +\n            this.boundingRect.height -\n            this.axisConfig.tickLength -\n            (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)\n          }`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth,\n        })),\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['top-axis', 'title'],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: 'top',\n            horizontalPos: 'center',\n          },\n        ],\n      });\n    }\n    return drawableElement;\n  }\n\n  getDrawableElements(): DrawableElem[] {\n    if (this.axisPosition === 'left') {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === 'right') {\n      throw Error('Drawing of right axis is not implemented');\n    }\n    if (this.axisPosition === 'bottom') {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === 'top') {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n}\n", "import type { ScaleBand } from 'd3';\nimport { scaleBand } from 'd3';\nimport { log } from '../../../../../logger.js';\nimport type { TextDimensionCalculator } from '../../textDimensionCalculator.js';\nimport { BaseAxis } from './baseAxis.js';\nimport type { XYChartAxisThemeConfig, XYChartAxisConfig } from '../../interfaces.js';\n\nexport class BandAxis extends BaseAxis {\n  private scale: ScaleBand<string>;\n  private categories: string[];\n\n  constructor(\n    axisConfig: XYChartAxisConfig,\n    axisThemeConfig: XYChartAxisThemeConfig,\n    categories: string[],\n    title: string,\n    textDimensionCalculator: TextDimensionCalculator\n  ) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = scaleBand().domain(this.categories).range(this.getRange());\n  }\n\n  setRange(range: [number, number]): void {\n    super.setRange(range);\n  }\n\n  recalculateScale(): void {\n    this.scale = scaleBand()\n      .domain(this.categories)\n      .range(this.getRange())\n      .paddingInner(1)\n      .paddingOuter(0)\n      .align(0.5);\n    log.trace('BandAxis axis final categories, range: ', this.categories, this.getRange());\n  }\n\n  getTickValues(): (string | number)[] {\n    return this.categories;\n  }\n\n  getScaleValue(value: string): number {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n}\n", "import type { ScaleLinear } from 'd3';\nimport { scaleLinear } from 'd3';\nimport type { TextDimensionCalculator } from '../../textDimensionCalculator.js';\nimport { BaseAxis } from './baseAxis.js';\nimport type { XYChartAxisThemeConfig, XYChartAxisConfig } from '../../interfaces.js';\n\nexport class LinearAxis extends BaseAxis {\n  private scale: ScaleLinear<number, number>;\n  private domain: [number, number];\n\n  constructor(\n    axisConfig: XYChartAxisConfig,\n    axisThemeConfig: XYChartAxisThemeConfig,\n    domain: [number, number],\n    title: string,\n    textDimensionCalculator: TextDimensionCalculator\n  ) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = scaleLinear().domain(this.domain).range(this.getRange());\n  }\n\n  getTickValues(): (string | number)[] {\n    return this.scale.ticks();\n  }\n\n  recalculateScale(): void {\n    const domain = [...this.domain]; // copy the array so if reverse is called two times it should not cancel the reverse effect\n    if (this.axisPosition === 'left') {\n      domain.reverse(); // since y-axis in svg start from top\n    }\n    this.scale = scaleLinear().domain(domain).range(this.getRange());\n  }\n\n  getScaleValue(value: number): number {\n    return this.scale(value);\n  }\n}\n", "import type { SVGGroup } from '../../../../../diagram-api/types.js';\nimport type {\n  AxisDataType,\n  ChartComponent,\n  XYChartAxisConfig,\n  XYChartAxisThemeConfig,\n} from '../../interfaces.js';\nimport { isBandAxisData } from '../../interfaces.js';\nimport { TextDimensionCalculatorWithFont } from '../../textDimensionCalculator.js';\nimport { BandAxis } from './bandAxis.js';\nimport { LinearAxis } from './linearAxis.js';\n\nexport type AxisPosition = 'left' | 'right' | 'top' | 'bottom';\n\nexport interface Axis extends ChartComponent {\n  getScaleValue(value: string | number): number;\n  setAxisPosition(axisPosition: AxisPosition): void;\n  getAxisOuterPadding(): number;\n  getTickDistance(): number;\n  recalculateOuterPaddingToDrawBar(): void;\n  setRange(range: [number, number]): void;\n}\n\nexport function getAxis(\n  data: AxisDataType,\n  axisConfig: XYChartAxisConfig,\n  axisThemeConfig: XYChartAxisThemeConfig,\n  tmpSVGGroup: SVGGroup\n): Axis {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n", "import type { SVGGroup } from '../../../../diagram-api/types.js';\nimport type {\n  BoundingRect,\n  ChartComponent,\n  Dimension,\n  DrawableElem,\n  Point,\n  XYChartConfig,\n  XYChartData,\n  XYChartThemeConfig,\n} from '../interfaces.js';\nimport type { TextDimensionCalculator } from '../textDimensionCalculator.js';\nimport { TextDimensionCalculatorWithFont } from '../textDimensionCalculator.js';\n\nexport class ChartTitle implements ChartComponent {\n  private boundingRect: BoundingRect;\n  private showChartTitle: boolean;\n  constructor(\n    private textDimensionCalculator: TextDimensionCalculator,\n    private chartConfig: XYChartConfig,\n    private chartData: XYChartData,\n    private chartThemeConfig: XYChartThemeConfig\n  ) {\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0,\n    };\n    this.showChartTitle = false;\n  }\n  setBoundingBoxXY(point: Point): void {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace: Dimension): Dimension {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (\n      titleDimension.width <= widthRequired &&\n      titleDimension.height <= heightRequired &&\n      this.chartConfig.showTitle &&\n      this.chartData.title\n    ) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height,\n    };\n  }\n  getDrawableElements(): DrawableElem[] {\n    const drawableElem: DrawableElem[] = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: ['chart-title'],\n        type: 'text',\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: 'middle',\n            horizontalPos: 'center',\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0,\n          },\n        ],\n      });\n    }\n    return drawableElem;\n  }\n}\n\nexport function getChartTitleComponent(\n  chartConfig: XYChartConfig,\n  chartData: XYChartData,\n  chartThemeConfig: XYChartThemeConfig,\n  tmpSVGGroup: SVGGroup\n): ChartComponent {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n", "import { line } from 'd3';\nimport type { DrawableElem, LinePlotData, XYChartConfig } from '../../interfaces.js';\nimport type { Axis } from '../axis/index.js';\n\nexport class LinePlot {\n  constructor(\n    private plotData: LinePlotData,\n    private xAxis: Axis,\n    private yAxis: Axis,\n    private orientation: XYChartConfig['chartOrientation'],\n    private plotIndex: number\n  ) {}\n\n  getDrawableElement(): DrawableElem[] {\n    const finalData: [number, number][] = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1]),\n    ]);\n\n    let path: string | null;\n    if (this.orientation === 'horizontal') {\n      path = line()\n        .y((d) => d[0])\n        .x((d) => d[1])(finalData);\n    } else {\n      path = line()\n        .x((d) => d[0])\n        .y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: ['plot', `line-plot-${this.plotIndex}`],\n        type: 'path',\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth,\n          },\n        ],\n      },\n    ];\n  }\n}\n", "import type { BarPlotData, BoundingRect, DrawableElem, XYChartConfig } from '../../interfaces.js';\nimport type { Axis } from '../axis/index.js';\n\nexport class BarPlot {\n  constructor(\n    private barData: BarPlotData,\n    private boundingRect: BoundingRect,\n    private xAxis: Axis,\n    private yAxis: Axis,\n    private orientation: XYChartConfig['chartOrientation'],\n    private plotIndex: number\n  ) {}\n\n  getDrawableElement(): DrawableElem[] {\n    const finalData: [number, number][] = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1]),\n    ]);\n\n    const barPaddingPercent = 0.05;\n\n    const barWidth =\n      Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) *\n      (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n\n    if (this.orientation === 'horizontal') {\n      return [\n        {\n          groupTexts: ['plot', `bar-plot-${this.plotIndex}`],\n          type: 'rect',\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill,\n          })),\n        },\n      ];\n    }\n    return [\n      {\n        groupTexts: ['plot', `bar-plot-${this.plotIndex}`],\n        type: 'rect',\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill,\n        })),\n      },\n    ];\n  }\n}\n", "import type {\n  XY<PERSON>hartData,\n  Dimension,\n  BoundingRect,\n  DrawableElem,\n  Point,\n  XYChartThemeConfig,\n  XYChartConfig,\n} from '../../interfaces.js';\nimport type { Axis } from '../axis/index.js';\nimport type { ChartComponent } from '../../interfaces.js';\nimport { LinePlot } from './linePlot.js';\nimport { BarPlot } from './barPlot.js';\n\nexport interface Plot extends ChartComponent {\n  setAxes(xAxis: Axis, yAxis: Axis): void;\n}\n\nexport class BasePlot implements Plot {\n  private boundingRect: BoundingRect;\n  private xAxis?: Axis;\n  private yAxis?: Axis;\n\n  constructor(\n    private chartConfig: XYChartConfig,\n    private chartData: XYChartData,\n    private chartThemeConfig: XYChartThemeConfig\n  ) {\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0,\n    };\n  }\n  setAxes(xAxis: Axis, yAxis: Axis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point: Point): void {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace: Dimension): Dimension {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height,\n    };\n  }\n  getDrawableElements(): DrawableElem[] {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error('Axes must be passed to render Plots');\n    }\n    const drawableElem: DrawableElem[] = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case 'line':\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case 'bar':\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n}\n\nexport function getPlotComponent(\n  chartConfig: XYChartConfig,\n  chartData: XYChartData,\n  chartThemeConfig: XYChartThemeConfig\n): Plot {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n", "import type { SVGGroup } from '../../../diagram-api/types.js';\nimport type { Axis } from './components/axis/index.js';\nimport { getAxis } from './components/axis/index.js';\nimport { getChartTitleComponent } from './components/chartTitle.js';\nimport type { Plot } from './components/plot/index.js';\nimport { getPlotComponent } from './components/plot/index.js';\nimport type {\n  ChartComponent,\n  DrawableElem,\n  XYChartConfig,\n  XYChartData,\n  XYChartThemeConfig,\n} from './interfaces.js';\nimport { isBarPlot } from './interfaces.js';\n\nexport class Orchestrator {\n  private componentStore: {\n    title: ChartComponent;\n    plot: Plot;\n    xAxis: Axis;\n    yAxis: Axis;\n  };\n  constructor(\n    private chartConfig: XYChartConfig,\n    private chartData: XYChartData,\n    chartThemeConfig: XYChartThemeConfig,\n    tmpSVGGroup: SVGGroup\n  ) {\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor,\n        },\n        tmpSVGGroup\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor,\n        },\n        tmpSVGGroup\n      ),\n    };\n  }\n\n  private calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor((availableWidth * this.chartConfig.plotReservedSpacePercent) / 100);\n    let chartHeight = Math.floor(\n      (availableHeight * this.chartConfig.plotReservedSpacePercent) / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight,\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition('bottom');\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition('left');\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n\n  private calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor((availableWidth * this.chartConfig.plotReservedSpacePercent) / 100);\n    let chartHeight = Math.floor(\n      (availableHeight * this.chartConfig.plotReservedSpacePercent) / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight,\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition('left');\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition('top');\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n\n  private calculateSpace() {\n    if (this.chartConfig.chartOrientation === 'horizontal') {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem: DrawableElem[] = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n}\n", "import type { SVGGroup } from '../../../diagram-api/types.js';\nimport type { DrawableElem, XYChartConfig, XYChartData, XYChartThemeConfig } from './interfaces.js';\nimport { Orchestrator } from './orchestrator.js';\n\nexport class XY<PERSON>hartBuilder {\n  static build(\n    config: XYChartConfig,\n    chartData: XYChartData,\n    chartThemeConfig: XYChartThemeConfig,\n    tmpSVGGroup: SVGGroup\n  ): DrawableElem[] {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup);\n    return orchestrator.getDrawableElement();\n  }\n}\n", "import * as configApi from '../../config.js';\nimport defaultConfig from '../../defaultConfig.js';\nimport type { SVGGroup } from '../../diagram-api/types.js';\nimport { getThemeVariables } from '../../themes/theme-default.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport { XYChartBuilder } from './chartBuilder/index.js';\nimport type {\n  DrawableElem,\n  SimplePlotDataType,\n  XYChartConfig,\n  XYChartData,\n  XYChartThemeConfig,\n} from './chartBuilder/interfaces.js';\nimport { isBandAxisData, isLinearAxisData } from './chartBuilder/interfaces.js';\n\nlet plotIndex = 0;\n\nlet tmpSVGGroup: SVGGroup;\n\nlet xyChartConfig: XYChartConfig = getChartDefaultConfig();\nlet xyChartThemeConfig: XYChartThemeConfig = getChartDefaultThemeConfig();\nlet xyChartData: XYChartData = getChartDefaultData();\nlet plotColorPalette = xyChartThemeConfig.plotColorPalette.split(',').map((color) => color.trim());\nlet hasSetXAxis = false;\nlet hasSetYAxis = false;\n\ninterface NormalTextType {\n  type: 'text';\n  text: string;\n}\n\nfunction getChartDefaultThemeConfig(): XYChartThemeConfig {\n  const defaultThemeVariables = getThemeVariables();\n  const config = configApi.getConfig();\n  return cleanAndMerge(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\nfunction getChartDefaultConfig(): XYChartConfig {\n  const config = configApi.getConfig();\n  return cleanAndMerge<XYChartConfig>(\n    defaultConfig.xyChart as XYChartConfig,\n    config.xyChart as XYChartConfig\n  );\n}\n\nfunction getChartDefaultData(): XYChartData {\n  return {\n    yAxis: {\n      type: 'linear',\n      title: '',\n      min: Infinity,\n      max: -Infinity,\n    },\n    xAxis: {\n      type: 'band',\n      title: '',\n      categories: [],\n    },\n    title: '',\n    plots: [],\n  };\n}\n\nfunction textSanitizer(text: string) {\n  const config = configApi.getConfig();\n  return sanitizeText(text.trim(), config);\n}\n\nfunction setTmpSVGG(SVGG: SVGGroup) {\n  tmpSVGGroup = SVGG;\n}\nfunction setOrientation(orientation: string) {\n  if (orientation === 'horizontal') {\n    xyChartConfig.chartOrientation = 'horizontal';\n  } else {\n    xyChartConfig.chartOrientation = 'vertical';\n  }\n}\nfunction setXAxisTitle(title: NormalTextType) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\nfunction setXAxisRangeData(min: number, max: number) {\n  xyChartData.xAxis = { type: 'linear', title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\nfunction setXAxisBand(categories: NormalTextType[]) {\n  xyChartData.xAxis = {\n    type: 'band',\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text)),\n  };\n  hasSetXAxis = true;\n}\nfunction setYAxisTitle(title: NormalTextType) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\nfunction setYAxisRangeData(min: number, max: number) {\n  xyChartData.yAxis = { type: 'linear', title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n\n// this function does not set `hasSetYAxis` as there can be multiple data so we should calculate the range accordingly\nfunction setYAxisRangeFromPlotData(data: number[]) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: 'linear',\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue),\n  };\n}\n\nfunction transformDataWithoutCategory(data: number[]): SimplePlotDataType {\n  let retData: SimplePlotDataType = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories: string[] = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n\n  return retData;\n}\n\nfunction getPlotColorFromPalette(plotIndex: number): string {\n  return plotColorPalette[plotIndex === 0 ? 0 : plotIndex % plotColorPalette.length];\n}\n\nfunction setLineData(title: NormalTextType, data: number[]) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: 'line',\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData,\n  });\n  plotIndex++;\n}\n\nfunction setBarData(title: NormalTextType, data: number[]) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: 'bar',\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData,\n  });\n  plotIndex++;\n}\n\nfunction getDrawableElem(): DrawableElem[] {\n  if (xyChartData.plots.length === 0) {\n    throw Error('No Plot to render, please provide a plot with some data');\n  }\n  xyChartData.title = getDiagramTitle();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n\nfunction getXYChartData() {\n  return xyChartData;\n}\n\nconst clear = function () {\n  commonClear();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(',').map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n};\n\nexport default {\n  getDrawableElem,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig,\n  getXYChartData,\n};\n", "import type { Diagram } from '../../Diagram.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type {\n  DrawableElem,\n  TextElem,\n  TextHorizontalPos,\n  TextVerticalPos,\n} from './chartBuilder/interfaces.js';\nimport type XYChartDB from './xychartDb.js';\n\nexport const draw = (txt: string, id: string, _version: string, diagObj: Diagram) => {\n  const db = diagObj.db as typeof XYChartDB;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  const labelData = db.getXYChartData().plots[0].data.map((data) => data[1]);\n  function getDominantBaseLine(horizontalPos: TextVerticalPos) {\n    return horizontalPos === 'top' ? 'text-before-edge' : 'middle';\n  }\n\n  function getTextAnchor(verticalPos: TextHorizontalPos) {\n    return verticalPos === 'left' ? 'start' : verticalPos === 'right' ? 'end' : 'middle';\n  }\n\n  function getTextTransformation(data: TextElem) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n\n  log.debug('Rendering xychart chart\\n' + txt);\n\n  const svg = selectSvgElement(id);\n  const group = svg.append('g').attr('class', 'main');\n  const background = group\n    .append('rect')\n    .attr('width', chartConfig.width)\n    .attr('height', chartConfig.height)\n    .attr('class', 'background');\n\n  // @ts-ignore: TODO Fix ts errors\n  configureSvgSize(svg, chartConfig.height, chartConfig.width, true);\n\n  svg.attr('viewBox', `0 0 ${chartConfig.width} ${chartConfig.height}`);\n\n  background.attr('fill', themeConfig.backgroundColor);\n\n  db.setTmpSVGG(svg.append('g').attr('class', 'mermaid-tmp-group'));\n\n  const shapes: DrawableElem[] = db.getDrawableElem();\n\n  const groups: Record<string, any> = {};\n\n  interface BarItem {\n    data: {\n      x: number;\n      y: number;\n      width: number;\n      height: number;\n    };\n    label: string;\n  }\n\n  function getGroup(gList: string[]) {\n    let elem = group;\n    let prefix = '';\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append('g').attr('class', gList[i]);\n      }\n    }\n    return elem;\n  }\n\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n\n    const shapeGroup = getGroup(shape.groupTexts);\n\n    switch (shape.type) {\n      case 'rect':\n        shapeGroup\n          .selectAll('rect')\n          .data(shape.data)\n          .enter()\n          .append('rect')\n          .attr('x', (data) => data.x)\n          .attr('y', (data) => data.y)\n          .attr('width', (data) => data.width)\n          .attr('height', (data) => data.height)\n          .attr('fill', (data) => data.fill)\n          .attr('stroke', (data) => data.strokeFill)\n          .attr('stroke-width', (data) => data.strokeWidth);\n\n        if (chartConfig.showDataLabel) {\n          if (chartConfig.chartOrientation === 'horizontal') {\n            // Factor to approximate each character's width.\n            const charWidthFactor = 0.7;\n\n            // Filter out bars that have zero width or height.\n            const validItems = shape.data\n              .map((d, i) => ({ data: d, label: labelData[i].toString() }))\n              .filter((item) => item.data.width > 0 && item.data.height > 0);\n\n            // Helper function to check if the text fits horizontally with a 10px right margin.\n            function fitsHorizontally(item: BarItem, fontSize: number): boolean {\n              const { data, label } = item;\n              // Approximate the text width.\n              const textWidth: number = fontSize * label.length * charWidthFactor;\n              // The available width is the bar's width minus a 10px right margin.\n              return textWidth <= data.width - 10;\n            }\n\n            // For each valid bar, start with an initial candidate font size (70% of the bar's height),\n            // then reduce it until the text fits horizontally.\n            const candidateFontSizes = validItems.map((item) => {\n              const { data } = item;\n              let fontSize = data.height * 0.7;\n              // Decrease fontSize until the text fits horizontally.\n              while (!fitsHorizontally(item, fontSize) && fontSize > 0) {\n                fontSize -= 1;\n              }\n              return fontSize;\n            });\n\n            // Choose the smallest candidate font size across all valid bars for uniformity.\n            const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));\n\n            shapeGroup\n              .selectAll('text')\n              .data(validItems)\n              .enter()\n              .append('text')\n              .attr('x', (item) => item.data.x + item.data.width - 10)\n              .attr('y', (item) => item.data.y + item.data.height / 2)\n              .attr('text-anchor', 'end')\n              .attr('dominant-baseline', 'middle')\n              .attr('fill', 'black')\n              .attr('font-size', `${uniformFontSize}px`)\n              .text((item) => item.label);\n          } else {\n            const yOffset = 10;\n\n            // filter out bars that have zero width or height.\n            const validItems = shape.data\n              .map((d, i) => ({ data: d, label: labelData[i].toString() }))\n              .filter((item) => item.data.width > 0 && item.data.height > 0);\n\n            // Helper function that checks if the text with a given fontSize fits within the bar boundaries.\n            function fitsInBar(item: BarItem, fontSize: number, yOffset: number): boolean {\n              const { data, label } = item;\n              const charWidthFactor = 0.7;\n              const textWidth = fontSize * label.length * charWidthFactor;\n\n              // Compute horizontal boundaries using the center.\n              const centerX = data.x + data.width / 2;\n              const leftEdge = centerX - textWidth / 2;\n              const rightEdge = centerX + textWidth / 2;\n\n              // Check that text doesn't overflow horizontally.\n              const horizontalFits = leftEdge >= data.x && rightEdge <= data.x + data.width;\n\n              // For vertical placement, we use 'dominant-baseline: hanging' so that y marks the top of the text.\n              // Thus, the bottom edge is y + yOffset + fontSize.\n              const verticalFits = data.y + yOffset + fontSize <= data.y + data.height;\n\n              return horizontalFits && verticalFits;\n            }\n\n            // For each valid item, start with a candidate font size based on the width,\n            // then reduce it until the text fits within both the horizontal and vertical boundaries.\n            const candidateFontSizes = validItems.map((item) => {\n              const { data, label } = item;\n              let fontSize = data.width / (label.length * 0.7);\n\n              // Decrease the font size until the text fits or fontSize reaches 0.\n              while (!fitsInBar(item, fontSize, yOffset) && fontSize > 0) {\n                fontSize -= 1;\n              }\n              return fontSize;\n            });\n\n            // Choose the smallest candidate across all valid bars for uniformity.\n            const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));\n\n            // Render text only for valid items.\n            shapeGroup\n              .selectAll('text')\n              .data(validItems)\n              .enter()\n              .append('text')\n              .attr('x', (item) => item.data.x + item.data.width / 2)\n              .attr('y', (item) => item.data.y + yOffset)\n              .attr('text-anchor', 'middle')\n              .attr('dominant-baseline', 'hanging')\n              .attr('fill', 'black')\n              .attr('font-size', `${uniformFontSize}px`)\n              .text((item) => item.label);\n          }\n        }\n        break;\n      case 'text':\n        shapeGroup\n          .selectAll('text')\n          .data(shape.data)\n          .enter()\n          .append('text')\n          .attr('x', 0)\n          .attr('y', 0)\n          .attr('fill', (data) => data.fill)\n          .attr('font-size', (data) => data.fontSize)\n          .attr('dominant-baseline', (data) => getDominantBaseLine(data.verticalPos))\n          .attr('text-anchor', (data) => getTextAnchor(data.horizontalPos))\n          .attr('transform', (data) => getTextTransformation(data))\n          .text((data) => data.text);\n        break;\n      case 'path':\n        shapeGroup\n          .selectAll('path')\n          .data(shape.data)\n          .enter()\n          .append('path')\n          .attr('d', (data) => data.path)\n          .attr('fill', (data) => (data.fill ? data.fill : 'none'))\n          .attr('stroke', (data) => data.strokeFill)\n          .attr('stroke-width', (data) => data.strokeWidth);\n        break;\n    }\n  }\n};\n\nexport default {\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: <PERSON><PERSON> doesn't support types.\nimport parser from './parser/xychart.jison';\nimport db from './xychartDb.js';\nimport renderer from './xychartRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE;AAC/iB,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,OAAM,GAAE,WAAU,GAAE,eAAc,GAAE,YAAW,GAAE,qBAAoB,GAAE,aAAY,GAAE,SAAQ,IAAG,QAAO,IAAG,UAAS,IAAG,cAAa,IAAG,UAAS,IAAG,cAAa,IAAG,QAAO,IAAG,YAAW,IAAG,OAAM,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,uBAAsB,IAAG,yBAAwB,IAAG,qBAAoB,IAAG,uBAAsB,IAAG,SAAQ,IAAG,aAAY,IAAG,YAAW,IAAG,mBAAkB,IAAG,uBAAsB,IAAG,aAAY,IAAG,WAAU,IAAG,QAAO,IAAG,OAAM,IAAG,YAAW,IAAG,OAAM,IAAG,UAAS,IAAG,iBAAgB,IAAG,OAAM,IAAG,OAAM,IAAG,SAAQ,IAAG,QAAO,IAAG,UAAS,IAAG,QAAO,IAAG,OAAM,IAAG,QAAO,IAAG,SAAQ,IAAG,cAAa,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACvuB,YAAY,EAAC,GAAE,SAAQ,GAAE,WAAU,GAAE,qBAAoB,IAAG,SAAQ,IAAG,UAAS,IAAG,UAAS,IAAG,QAAO,IAAG,OAAM,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,uBAAsB,IAAG,qBAAoB,IAAG,uBAAsB,IAAG,SAAQ,IAAG,mBAAkB,IAAG,WAAU,IAAG,QAAO,IAAG,OAAM,IAAG,OAAM,IAAG,UAAS,IAAG,OAAM,IAAG,OAAM,IAAG,SAAQ,IAAG,QAAO,IAAG,UAAS,IAAG,QAAO,IAAG,OAAM,IAAG,QAAO,IAAG,SAAQ,IAAG,aAAY;AAAA,IAC9d,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IAChW,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACJ,aAAG,eAAe,GAAG,EAAE,CAAC;AACzB;AAAA,QACA,KAAK;AACJ,aAAG,gBAAgB,GAAG,EAAE,EAAE,KAAK,KAAK,CAAC;AACtC;AAAA,QACA,KAAK;AACJ,aAAG,YAAY,EAAC,MAAM,IAAI,MAAM,OAAM,GAAG,GAAG,EAAE,CAAC;AAChD;AAAA,QACA,KAAK;AACJ,aAAG,YAAY,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAChC;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,EAAC,MAAM,IAAI,MAAM,OAAM,GAAG,GAAG,EAAE,CAAC;AAC/C;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC/B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,KAAG,CAAC;AACjB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,OAAO,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AACtC;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;AACzB;AAAA,QACA,KAAK;AACL,aAAG,cAAc,GAAG,EAAE,CAAC;AACvB;AAAA,QACA,KAAK;AACL,aAAG,cAAc,GAAG,KAAG,CAAC,CAAC;AACzB;AAAA,QACA,KAAK;AACL,aAAG,cAAc,EAAC,MAAM,QAAQ,MAAM,GAAE,CAAC;AACzC;AAAA,QACA,KAAK;AACL,aAAG,aAAa,GAAG,EAAE,CAAC;AACtB;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,OAAO,GAAG,KAAG,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AACrD;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC9B;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACjB;AAAA,QACA,KAAK;AACL,aAAG,cAAc,GAAG,EAAE,CAAC;AACvB;AAAA,QACA,KAAK;AACL,aAAG,cAAc,GAAG,KAAG,CAAC,CAAC;AACzB;AAAA,QACA,KAAK;AACL,aAAG,cAAc,EAAC,MAAM,QAAQ,MAAM,GAAE,CAAC;AACzC;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,OAAO,GAAG,KAAG,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AACrD;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,EAAC,MAAK,GAAG,EAAE,GAAG,MAAM,OAAM;AAClC;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,EAAC,MAAM,GAAG,EAAE,GAAG,MAAM,OAAM;AACnC;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,EAAC,MAAM,GAAG,EAAE,GAAG,MAAM,WAAU;AACvC;AAAA,QACA,KAAK;AACL,eAAK,IAAE,GAAG,EAAE;AACZ;AAAA,QACA,KAAK;AACL,eAAK,IAAE,GAAG,KAAG,CAAC,IAAE,KAAG,GAAG,EAAE;AACxB;AAAA,MACA;AAAA,IACA,GA1Fe;AAAA,IA2Ff,OAAO,CAAC,EAAE,KAAI,KAAI,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,KAAI,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC;AAAA,IACzlE,gBAAgB,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC;AAAA,IAC9D,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,iBAAK,UAAU,WAAW;AAAE,mBAAO;AAC3C;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,UAAU,WAAW;AAAE,mBAAO;AAC3C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,qBAAqB;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,WAAW;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,WAAW;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,gBAAgB;AAAG,mBAAO;AAClD;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAG,mBAAO;AACxC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAG,mBAAO;AACxC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,YAAY;AAAG,mBAAO;AAC9C;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,iBAAK,UAAU,QAAQ;AAC/B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GAhGe;AAAA,MAiGf,OAAO,CAAC,wBAAuB,uBAAsB,iBAAgB,iBAAgB,iBAAgB,kBAAiB,iBAAgB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,YAAW,gBAAe,wBAAuB,iCAAgC,kBAAiB,kBAAiB,YAAW,aAAY,gBAAe,eAAc,YAAW,sCAAqC,YAAW,kLAAiL,aAAY,aAAY,eAAc,YAAW,YAAW,mBAAkB,WAAU,YAAW,WAAU,WAAU,YAAW,WAAU,cAAa,YAAW,WAAU,WAAU,gBAAe,aAAY,WAAU,SAAS;AAAA,MACx3B,YAAY,EAAC,cAAa,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,GAAE,QAAO,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,GAAE,kBAAiB,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,GAAE,aAAY,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,GAAE,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IAC/8B;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,kBAAQ;;;AC3sBT,SAAS,UAAU,MAAqC;AAC7D,SAAO,KAAK,SAAS;AACvB;AAFgB;AAmBT,SAAS,eAAe,MAA8C;AAC3E,SAAO,KAAK,SAAS;AACvB;AAFgB;AAIT,SAAS,iBAAiB,MAAgD;AAC/E,SAAO,KAAK,SAAS;AACvB;AAFgB;;;AC3DT,IAAM,kCAAN,MAAyE;AAAA,EAC9E,YAAoB,aAAuB;AAAvB;AAAA,EAAwB;AAAA,EAT9C,OAQgF;AAAA;AAAA;AAAA,EAE9E,gBAAgB,OAAiB,UAA6B;AAC5D,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,QACL,OAAO,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,IAAI;AAAA,QAClE,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,UAAM,YAAuB;AAAA,MAC3B,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAEA,UAAM,OAAO,KAAK,YACf,OAAO,GAAG,EACV,KAAK,cAAc,QAAQ,EAC3B,KAAK,aAAa,QAAQ;AAE7B,eAAW,KAAK,OAAO;AACrB,YAAM,OAAO,uBAAuB,MAAM,GAAG,CAAC;AAC9C,YAAM,QAAQ,OAAO,KAAK,QAAQ,EAAE,SAAS;AAC7C,YAAM,SAAS,OAAO,KAAK,SAAS;AACpC,gBAAU,QAAQ,KAAK,IAAI,UAAU,OAAO,KAAK;AACjD,gBAAU,SAAS,KAAK,IAAI,UAAU,QAAQ,MAAM;AAAA,IACtD;AACA,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AACF;;;AC3BA,IAAM,gCAAgC;AACtC,IAAM,0CAA0C;AAEzC,IAAe,WAAf,MAAwC;AAAA,EAY7C,YACY,YACA,OACA,yBACA,iBACV;AAJU;AACA;AACA;AACA;AAfZ,SAAU,eAA6B,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AACzE,SAAU,eAA6B;AAEvC,SAAU,YAAY;AACtB,SAAU,YAAY;AACtB,SAAU,WAAW;AACrB,SAAU,eAAe;AACzB,SAAU,eAAe;AACzB,SAAU,kBAAkB;AAC5B,SAAU,kBAAkB;AAQ1B,SAAK,QAAQ,CAAC,GAAG,EAAE;AACnB,SAAK,eAAe,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AACtD,SAAK,eAAe;AAAA,EACtB;AAAA,EAnCF,OAc+C;AAAA;AAAA;AAAA,EAuB7C,SAAS,OAA+B;AACtC,SAAK,QAAQ;AACb,QAAI,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,SAAS;AACjE,WAAK,aAAa,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAC/C,OAAO;AACL,WAAK,aAAa,QAAQ,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAC9C;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAEA,WAA6B;AAC3B,WAAO,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,cAAc,KAAK,MAAM,CAAC,IAAI,KAAK,YAAY;AAAA,EAC9E;AAAA,EAEA,gBAAgB,cAAkC;AAChD,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,KAAK;AAAA,EAC1B;AAAA,EAQA,kBAA0B;AACxB,UAAM,QAAQ,KAAK,SAAS;AAC5B,WAAO,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE;AAAA,EAC9D;AAAA,EAEA,sBAA8B;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ,oBAA+B;AACrC,WAAO,KAAK,wBAAwB;AAAA,MAClC,KAAK,cAAc,EAAE,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;AAAA,MAClD,KAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,mCAAyC;AACvC,QAAI,gCAAgC,KAAK,gBAAgB,IAAI,KAAK,eAAe,GAAG;AAClF,WAAK,eAAe,KAAK,MAAO,gCAAgC,KAAK,gBAAgB,IAAK,CAAC;AAAA,IAC7F;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAEQ,kCAAkC,gBAA2B;AACnE,QAAI,kBAAkB,eAAe;AACrC,QAAI,KAAK,WAAW,gBAAgB,kBAAkB,KAAK,WAAW,eAAe;AACnF,yBAAmB,KAAK,WAAW;AACnC,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,KAAK,WAAW,WAAW;AAC7B,YAAM,gBAAgB,KAAK,kBAAkB;AAC7C,YAAM,aAAa,0CAA0C,eAAe;AAC5E,WAAK,eAAe,KAAK,IAAI,cAAc,QAAQ,GAAG,UAAU;AAEhE,YAAM,iBAAiB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC7E,WAAK,kBAAkB,cAAc;AACrC,UAAI,kBAAkB,iBAAiB;AACrC,2BAAmB;AACnB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,QAAI,KAAK,WAAW,YAAY,mBAAmB,KAAK,WAAW,YAAY;AAC7E,WAAK,WAAW;AAChB,yBAAmB,KAAK,WAAW;AAAA,IACrC;AACA,QAAI,KAAK,WAAW,aAAa,KAAK,OAAO;AAC3C,YAAM,gBAAgB,KAAK,wBAAwB;AAAA,QACjD,CAAC,KAAK,KAAK;AAAA,QACX,KAAK,WAAW;AAAA,MAClB;AACA,YAAM,iBAAiB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC7E,WAAK,kBAAkB,cAAc;AACrC,UAAI,kBAAkB,iBAAiB;AACrC,2BAAmB;AACnB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,SAAK,aAAa,QAAQ,eAAe;AACzC,SAAK,aAAa,SAAS,eAAe,SAAS;AAAA,EACrD;AAAA,EAEQ,8BAA8B,gBAA2B;AAC/D,QAAI,iBAAiB,eAAe;AACpC,QAAI,KAAK,WAAW,gBAAgB,iBAAiB,KAAK,WAAW,eAAe;AAClF,wBAAkB,KAAK,WAAW;AAClC,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,KAAK,WAAW,WAAW;AAC7B,YAAM,gBAAgB,KAAK,kBAAkB;AAC7C,YAAM,aAAa,0CAA0C,eAAe;AAC5E,WAAK,eAAe,KAAK,IAAI,cAAc,SAAS,GAAG,UAAU;AACjE,YAAM,gBAAgB,cAAc,QAAQ,KAAK,WAAW,eAAe;AAC3E,UAAI,iBAAiB,gBAAgB;AACnC,0BAAkB;AAClB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,QAAI,KAAK,WAAW,YAAY,kBAAkB,KAAK,WAAW,YAAY;AAC5E,WAAK,WAAW;AAChB,wBAAkB,KAAK,WAAW;AAAA,IACpC;AACA,QAAI,KAAK,WAAW,aAAa,KAAK,OAAO;AAC3C,YAAM,gBAAgB,KAAK,wBAAwB;AAAA,QACjD,CAAC,KAAK,KAAK;AAAA,QACX,KAAK,WAAW;AAAA,MAClB;AACA,YAAM,gBAAgB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC5E,WAAK,kBAAkB,cAAc;AACrC,UAAI,iBAAiB,gBAAgB;AACnC,0BAAkB;AAClB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,SAAK,aAAa,QAAQ,eAAe,QAAQ;AACjD,SAAK,aAAa,SAAS,eAAe;AAAA,EAC5C;AAAA,EAEA,eAAe,gBAAsC;AACnD,QAAI,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,SAAS;AACjE,WAAK,8BAA8B,cAAc;AAAA,IACnD,OAAO;AACL,WAAK,kCAAkC,cAAc;AAAA,IACvD;AACA,SAAK,iBAAiB;AACtB,WAAO;AAAA,MACL,OAAO,KAAK,aAAa;AAAA,MACzB,QAAQ,KAAK,aAAa;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,iBAAiB,OAAoB;AACnC,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;AAAA,EAC9B;AAAA,EAEQ,iCAAiD;AACvD,UAAM,kBAAkC,CAAC;AACzC,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ,KAAK,WAAW,gBAAgB;AAC1F,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,YAAY;AAAA,QACtC,MAAM;AAAA,UACJ;AAAA,YACE,MAAM,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC,MAAM,CAAC,IACxC,KAAK,aAAa,IAAI,KAAK,aAAa,MAC1C;AAAA,YACA,YAAY,KAAK,gBAAgB;AAAA,YACjC,aAAa,KAAK,WAAW;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,OAAO;AAAA,QACjC,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,UAAU;AAAA,UACxC,MAAM,KAAK,SAAS;AAAA,UACpB,GACE,KAAK,aAAa,IAClB,KAAK,aAAa,SACjB,KAAK,YAAY,KAAK,WAAW,eAAe,MAChD,KAAK,WAAW,KAAK,WAAW,aAAa,MAC7C,KAAK,eAAe,KAAK,WAAW,gBAAgB;AAAA,UACvD,GAAG,KAAK,cAAc,IAAI;AAAA,UAC1B,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IACJ,KAAK,aAAa,IAClB,KAAK,aAAa,SACjB,KAAK,eAAe,KAAK,WAAW,gBAAgB;AACvD,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,OAAO;AAAA,QACjC,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,UAAU;AAAA,UACxC,MAAM,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,MACtC,IAAI,KAAK,WAAW,UACtB,IAAI,KAAK,cAAc,IAAI,CAAC;AAAA,UAC5B,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,OAAO;AAAA,QACjC,MAAM;AAAA,UACJ;AAAA,YACE,MAAM,KAAK;AAAA,YACX,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,YACzC,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS;AAAA,YACpD,MAAM,KAAK,gBAAgB;AAAA,YAC3B,UAAU,KAAK,WAAW;AAAA,YAC1B,UAAU;AAAA,YACV,aAAa;AAAA,YACb,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACQ,mCAAmD;AACzD,UAAM,kBAAkC,CAAC;AACzC,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,WAAW,gBAAgB;AAChE,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,WAAW;AAAA,QACvC,MAAM;AAAA,UACJ;AAAA,YACE,MAAM,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC,MACjC,KAAK,aAAa,IAAI,KAAK,aAAa,KAC1C,IAAI,CAAC;AAAA,YACL,YAAY,KAAK,gBAAgB;AAAA,YACjC,aAAa,KAAK,WAAW;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,OAAO;AAAA,QACnC,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,UAAU;AAAA,UACxC,MAAM,KAAK,SAAS;AAAA,UACpB,GAAG,KAAK,cAAc,IAAI;AAAA,UAC1B,GACE,KAAK,aAAa,IAClB,KAAK,WAAW,gBACf,KAAK,WAAW,KAAK,WAAW,aAAa,MAC7C,KAAK,eAAe,KAAK,WAAW,gBAAgB;AAAA,UACvD,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa,KAAK,KAAK,eAAe,KAAK,WAAW,gBAAgB;AACrF,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,OAAO;AAAA,QACnC,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,UAAU;AAAA,UACxC,MAAM,KAAK,KAAK,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,cAAc,IAAI,CAAC,IACpE,IAAI,KAAK,WAAW,UACtB;AAAA,UACA,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,OAAO;AAAA,QACnC,MAAM;AAAA,UACJ;AAAA,YACE,MAAM,KAAK;AAAA,YACX,GAAG,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK;AAAA,YACrD,GACE,KAAK,aAAa,IAClB,KAAK,aAAa,SAClB,KAAK,WAAW,eAChB,KAAK;AAAA,YACP,MAAM,KAAK,gBAAgB;AAAA,YAC3B,UAAU,KAAK,WAAW;AAAA,YAC1B,UAAU;AAAA,YACV,aAAa;AAAA,YACb,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACQ,gCAAgD;AACtD,UAAM,kBAAkC,CAAC;AACzC,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,WAAW,gBAAgB;AAC3F,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,WAAW;AAAA,QACpC,MAAM;AAAA,UACJ;AAAA,YACE,MAAM,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC,MACjC,KAAK,aAAa,IAAI,KAAK,aAAa,KAC1C,IAAI,CAAC;AAAA,YACL,YAAY,KAAK,gBAAgB;AAAA,YACjC,aAAa,KAAK,WAAW;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,OAAO;AAAA,QAChC,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,UAAU;AAAA,UACxC,MAAM,KAAK,SAAS;AAAA,UACpB,GAAG,KAAK,cAAc,IAAI;AAAA,UAC1B,GACE,KAAK,aAAa,KACjB,KAAK,YAAY,KAAK,kBAAkB,KAAK,WAAW,eAAe,IAAI,KAC5E,KAAK,WAAW;AAAA,UAClB,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa;AAC5B,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,OAAO;AAAA,QAChC,MAAM,KAAK,cAAc,EAAE,IAAI,CAAC,UAAU;AAAA,UACxC,MAAM,KAAK,KAAK,cAAc,IAAI,CAAC,IACjC,IAAI,KAAK,aAAa,UAAU,KAAK,eAAe,KAAK,WAAW,gBAAgB,EACtF,MAAM,KAAK,cAAc,IAAI,CAAC,IAC5B,IACA,KAAK,aAAa,SAClB,KAAK,WAAW,cACf,KAAK,eAAe,KAAK,WAAW,gBAAgB,EACvD;AAAA,UACA,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,OAAO;AAAA,QAChC,MAAM;AAAA,UACJ;AAAA,YACE,MAAM,KAAK;AAAA,YACX,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ;AAAA,YACnD,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,YACzC,MAAM,KAAK,gBAAgB;AAAA,YAC3B,UAAU,KAAK,WAAW;AAAA,YAC1B,UAAU;AAAA,YACV,aAAa;AAAA,YACb,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAEA,sBAAsC;AACpC,QAAI,KAAK,iBAAiB,QAAQ;AAChC,aAAO,KAAK,+BAA+B;AAAA,IAC7C;AACA,QAAI,KAAK,iBAAiB,SAAS;AACjC,YAAM,MAAM,0CAA0C;AAAA,IACxD;AACA,QAAI,KAAK,iBAAiB,UAAU;AAClC,aAAO,KAAK,iCAAiC;AAAA,IAC/C;AACA,QAAI,KAAK,iBAAiB,OAAO;AAC/B,aAAO,KAAK,8BAA8B;AAAA,IAC5C;AACA,WAAO,CAAC;AAAA,EACV;AACF;;;AC9ZO,IAAM,WAAN,cAAuB,SAAS;AAAA,EAPvC,OAOuC;AAAA;AAAA;AAAA,EAIrC,YACE,YACA,iBACA,YACA,OACA,yBACA;AACA,UAAM,YAAY,OAAO,yBAAyB,eAAe;AACjE,SAAK,aAAa;AAClB,SAAK,QAAQ,KAAU,EAAE,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,EACxE;AAAA,EAEA,SAAS,OAA+B;AACtC,UAAM,SAAS,KAAK;AAAA,EACtB;AAAA,EAEA,mBAAyB;AACvB,SAAK,QAAQ,KAAU,EACpB,OAAO,KAAK,UAAU,EACtB,MAAM,KAAK,SAAS,CAAC,EACrB,aAAa,CAAC,EACd,aAAa,CAAC,EACd,MAAM,GAAG;AACZ,QAAI,MAAM,2CAA2C,KAAK,YAAY,KAAK,SAAS,CAAC;AAAA,EACvF;AAAA,EAEA,gBAAqC;AACnC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,cAAc,OAAuB;AACnC,WAAO,KAAK,MAAM,KAAK,KAAK,KAAK,SAAS,EAAE,CAAC;AAAA,EAC/C;AACF;;;ACtCO,IAAM,aAAN,cAAyB,SAAS;AAAA,EANzC,OAMyC;AAAA;AAAA;AAAA,EAIvC,YACE,YACA,iBACA,QACA,OACA,yBACA;AACA,UAAM,YAAY,OAAO,yBAAyB,eAAe;AACjE,SAAK,SAAS;AACd,SAAK,QAAQ,OAAY,EAAE,OAAO,KAAK,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,EACtE;AAAA,EAEA,gBAAqC;AACnC,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EAEA,mBAAyB;AACvB,UAAM,SAAS,CAAC,GAAG,KAAK,MAAM;AAC9B,QAAI,KAAK,iBAAiB,QAAQ;AAChC,aAAO,QAAQ;AAAA,IACjB;AACA,SAAK,QAAQ,OAAY,EAAE,OAAO,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,EACjE;AAAA,EAEA,cAAc,OAAuB;AACnC,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AACF;;;ACdO,SAAS,QACd,MACA,YACA,iBACAE,cACM;AACN,QAAM,0BAA0B,IAAI,gCAAgCA,YAAW;AAC/E,MAAI,eAAe,IAAI,GAAG;AACxB,WAAO,IAAI;AAAA,MACT;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI;AAAA,IACT;AAAA,IACA;AAAA,IACA,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,IACnB,KAAK;AAAA,IACL;AAAA,EACF;AACF;AAvBgB;;;ACTT,IAAM,aAAN,MAA2C;AAAA,EAGhD,YACU,yBACA,aACA,WACA,kBACR;AAJQ;AACA;AACA;AACA;AAER,SAAK,eAAe;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EA9BF,OAckD;AAAA;AAAA;AAAA,EAiBhD,iBAAiB,OAAoB;AACnC,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;AAAA,EAC9B;AAAA,EACA,eAAe,gBAAsC;AACnD,UAAM,iBAAiB,KAAK,wBAAwB;AAAA,MAClD,CAAC,KAAK,UAAU,KAAK;AAAA,MACrB,KAAK,YAAY;AAAA,IACnB;AACA,UAAM,gBAAgB,KAAK,IAAI,eAAe,OAAO,eAAe,KAAK;AACzE,UAAM,iBAAiB,eAAe,SAAS,IAAI,KAAK,YAAY;AACpE,QACE,eAAe,SAAS,iBACxB,eAAe,UAAU,kBACzB,KAAK,YAAY,aACjB,KAAK,UAAU,OACf;AACA,WAAK,aAAa,QAAQ;AAC1B,WAAK,aAAa,SAAS;AAC3B,WAAK,iBAAiB;AAAA,IACxB;AAEA,WAAO;AAAA,MACL,OAAO,KAAK,aAAa;AAAA,MACzB,QAAQ,KAAK,aAAa;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,sBAAsC;AACpC,UAAM,eAA+B,CAAC;AACtC,QAAI,KAAK,gBAAgB;AACvB,mBAAa,KAAK;AAAA,QAChB,YAAY,CAAC,aAAa;AAAA,QAC1B,MAAM;AAAA,QACN,MAAM;AAAA,UACJ;AAAA,YACE,UAAU,KAAK,YAAY;AAAA,YAC3B,MAAM,KAAK,UAAU;AAAA,YACrB,aAAa;AAAA,YACb,eAAe;AAAA,YACf,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ;AAAA,YACnD,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS;AAAA,YACpD,MAAM,KAAK,iBAAiB;AAAA,YAC5B,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,uBACd,aACA,WACA,kBACAC,cACgB;AAChB,QAAM,0BAA0B,IAAI,gCAAgCA,YAAW;AAC/E,SAAO,IAAI,WAAW,yBAAyB,aAAa,WAAW,gBAAgB;AACzF;AARgB;;;AC9ET,IAAM,WAAN,MAAe;AAAA,EACpB,YACU,UACA,OACA,OACA,aACAC,YACR;AALQ;AACA;AACA;AACA;AACA,qBAAAA;AAAA,EACP;AAAA,EAXL,OAIsB;AAAA;AAAA;AAAA,EASpB,qBAAqC;AACnC,UAAM,YAAgC,KAAK,SAAS,KAAK,IAAI,CAAC,MAAM;AAAA,MAClE,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;AAAA,MAC7B,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;AAAA,IAC/B,CAAC;AAED,QAAI;AACJ,QAAI,KAAK,gBAAgB,cAAc;AACrC,aAAO,aAAK,EACT,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EACb,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,IAC7B,OAAO;AACL,aAAO,aAAK,EACT,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EACb,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,IAC7B;AACA,QAAI,CAAC,MAAM;AACT,aAAO,CAAC;AAAA,IACV;AACA,WAAO;AAAA,MACL;AAAA,QACE,YAAY,CAAC,QAAQ,aAAa,KAAK,SAAS,EAAE;AAAA,QAClD,MAAM;AAAA,QACN,MAAM;AAAA,UACJ;AAAA,YACE;AAAA,YACA,YAAY,KAAK,SAAS;AAAA,YAC1B,aAAa,KAAK,SAAS;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC3CO,IAAM,UAAN,MAAc;AAAA,EACnB,YACU,SACA,cACA,OACA,OACA,aACAC,YACR;AANQ;AACA;AACA;AACA;AACA;AACA,qBAAAA;AAAA,EACP;AAAA,EAXL,OAGqB;AAAA;AAAA;AAAA,EAUnB,qBAAqC;AACnC,UAAM,YAAgC,KAAK,QAAQ,KAAK,IAAI,CAAC,MAAM;AAAA,MACjE,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;AAAA,MAC7B,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;AAAA,IAC/B,CAAC;AAED,UAAM,oBAAoB;AAE1B,UAAM,WACJ,KAAK,IAAI,KAAK,MAAM,oBAAoB,IAAI,GAAG,KAAK,MAAM,gBAAgB,CAAC,KAC1E,IAAI;AACP,UAAM,eAAe,WAAW;AAEhC,QAAI,KAAK,gBAAgB,cAAc;AACrC,aAAO;AAAA,QACL;AAAA,UACE,YAAY,CAAC,QAAQ,YAAY,KAAK,SAAS,EAAE;AAAA,UACjD,MAAM;AAAA,UACN,MAAM,UAAU,IAAI,CAAC,UAAU;AAAA,YAC7B,GAAG,KAAK,aAAa;AAAA,YACrB,GAAG,KAAK,CAAC,IAAI;AAAA,YACb,QAAQ;AAAA,YACR,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa;AAAA,YACnC,MAAM,KAAK,QAAQ;AAAA,YACnB,aAAa;AAAA,YACb,YAAY,KAAK,QAAQ;AAAA,UAC3B,EAAE;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,QACE,YAAY,CAAC,QAAQ,YAAY,KAAK,SAAS,EAAE;AAAA,QACjD,MAAM;AAAA,QACN,MAAM,UAAU,IAAI,CAAC,UAAU;AAAA,UAC7B,GAAG,KAAK,CAAC,IAAI;AAAA,UACb,GAAG,KAAK,CAAC;AAAA,UACT,OAAO;AAAA,UACP,QAAQ,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,CAAC;AAAA,UAC/D,MAAM,KAAK,QAAQ;AAAA,UACnB,aAAa;AAAA,UACb,YAAY,KAAK,QAAQ;AAAA,QAC3B,EAAE;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;;;ACzCO,IAAM,WAAN,MAA+B;AAAA,EAKpC,YACU,aACA,WACA,kBACR;AAHQ;AACA;AACA;AAER,SAAK,eAAe;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EAlCF,OAkBsC;AAAA;AAAA;AAAA,EAiBpC,QAAQ,OAAa,OAAa;AAChC,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,iBAAiB,OAAoB;AACnC,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;AAAA,EAC9B;AAAA,EACA,eAAe,gBAAsC;AACnD,SAAK,aAAa,QAAQ,eAAe;AACzC,SAAK,aAAa,SAAS,eAAe;AAE1C,WAAO;AAAA,MACL,OAAO,KAAK,aAAa;AAAA,MACzB,QAAQ,KAAK,aAAa;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,sBAAsC;AACpC,QAAI,EAAE,KAAK,SAAS,KAAK,QAAQ;AAC/B,YAAM,MAAM,qCAAqC;AAAA,IACnD;AACA,UAAM,eAA+B,CAAC;AACtC,eAAW,CAAC,GAAG,IAAI,KAAK,KAAK,UAAU,MAAM,QAAQ,GAAG;AACtD,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH;AACE,kBAAM,WAAW,IAAI;AAAA,cACnB;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,cACjB;AAAA,YACF;AACA,yBAAa,KAAK,GAAG,SAAS,mBAAmB,CAAC;AAAA,UACpD;AACA;AAAA,QACF,KAAK;AACH;AACE,kBAAM,UAAU,IAAI;AAAA,cAClB;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,cACjB;AAAA,YACF;AACA,yBAAa,KAAK,GAAG,QAAQ,mBAAmB,CAAC;AAAA,UACnD;AACA;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,iBACd,aACA,WACA,kBACM;AACN,SAAO,IAAI,SAAS,aAAa,WAAW,gBAAgB;AAC9D;AANgB;;;AC3ET,IAAM,eAAN,MAAmB;AAAA,EAOxB,YACU,aACA,WACR,kBACAC,cACA;AAJQ;AACA;AAIR,SAAK,iBAAiB;AAAA,MACpB,OAAO,uBAAuB,aAAa,WAAW,kBAAkBA,YAAW;AAAA,MACnF,MAAM,iBAAiB,aAAa,WAAW,gBAAgB;AAAA,MAC/D,OAAO;AAAA,QACL,UAAU;AAAA,QACV,YAAY;AAAA,QACZ;AAAA,UACE,YAAY,iBAAiB;AAAA,UAC7B,YAAY,iBAAiB;AAAA,UAC7B,WAAW,iBAAiB;AAAA,UAC5B,eAAe,iBAAiB;AAAA,QAClC;AAAA,QACAA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,UAAU;AAAA,QACV,YAAY;AAAA,QACZ;AAAA,UACE,YAAY,iBAAiB;AAAA,UAC7B,YAAY,iBAAiB;AAAA,UAC7B,WAAW,iBAAiB;AAAA,UAC5B,eAAe,iBAAiB;AAAA,QAClC;AAAA,QACAA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAtDF,OAe0B;AAAA;AAAA;AAAA,EAyChB,yBAAyB;AAC/B,QAAI,iBAAiB,KAAK,YAAY;AACtC,QAAI,kBAAkB,KAAK,YAAY;AACvC,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,aAAa,KAAK,MAAO,iBAAiB,KAAK,YAAY,2BAA4B,GAAG;AAC9F,QAAI,cAAc,KAAK;AAAA,MACpB,kBAAkB,KAAK,YAAY,2BAA4B;AAAA,IAClE;AACA,QAAI,YAAY,KAAK,eAAe,KAAK,eAAe;AAAA,MACtD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,sBAAkB,UAAU;AAC5B,uBAAmB,UAAU;AAE7B,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO,KAAK,YAAY;AAAA,MACxB,QAAQ;AAAA,IACV,CAAC;AACD,YAAQ,UAAU;AAClB,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,QAAQ;AAClD,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,MAAM;AAChD,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,YAAQ,UAAU;AAClB,sBAAkB,UAAU;AAC5B,QAAI,iBAAiB,GAAG;AACtB,oBAAc;AACd,uBAAiB;AAAA,IACnB;AACA,QAAI,kBAAkB,GAAG;AACvB,qBAAe;AACf,wBAAkB;AAAA,IACpB;AACA,SAAK,eAAe,KAAK,eAAe;AAAA,MACtC,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AAED,SAAK,eAAe,KAAK,iBAAiB,EAAE,GAAG,OAAO,GAAG,MAAM,CAAC;AAChE,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,UAAU,CAAC;AAC9D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,OAAO,GAAG,QAAQ,YAAY,CAAC;AAC/E,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,WAAW,CAAC;AAC/D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC;AAC7D,QAAI,KAAK,UAAU,MAAM,KAAK,CAAC,MAAM,UAAU,CAAC,CAAC,GAAG;AAClD,WAAK,eAAe,MAAM,iCAAiC;AAAA,IAC7D;AAAA,EACF;AAAA,EAEQ,2BAA2B;AACjC,QAAI,iBAAiB,KAAK,YAAY;AACtC,QAAI,kBAAkB,KAAK,YAAY;AACvC,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,aAAa,KAAK,MAAO,iBAAiB,KAAK,YAAY,2BAA4B,GAAG;AAC9F,QAAI,cAAc,KAAK;AAAA,MACpB,kBAAkB,KAAK,YAAY,2BAA4B;AAAA,IAClE;AACA,QAAI,YAAY,KAAK,eAAe,KAAK,eAAe;AAAA,MACtD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,sBAAkB,UAAU;AAC5B,uBAAmB,UAAU;AAE7B,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO,KAAK,YAAY;AAAA,MACxB,QAAQ;AAAA,IACV,CAAC;AACD,gBAAY,UAAU;AACtB,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,MAAM;AAChD,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,sBAAkB,UAAU;AAC5B,YAAQ,UAAU;AAClB,SAAK,eAAe,MAAM,gBAAgB,KAAK;AAC/C,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,uBAAmB,UAAU;AAC7B,YAAQ,YAAY,UAAU;AAC9B,QAAI,iBAAiB,GAAG;AACtB,oBAAc;AACd,uBAAiB;AAAA,IACnB;AACA,QAAI,kBAAkB,GAAG;AACvB,qBAAe;AACf,wBAAkB;AAAA,IACpB;AACA,SAAK,eAAe,KAAK,eAAe;AAAA,MACtC,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AAED,SAAK,eAAe,KAAK,iBAAiB,EAAE,GAAG,OAAO,GAAG,MAAM,CAAC;AAChE,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,UAAU,CAAC;AAC9D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,OAAO,GAAG,UAAU,CAAC;AACrE,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,WAAW,CAAC;AAC/D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC;AAC7D,QAAI,KAAK,UAAU,MAAM,KAAK,CAAC,MAAM,UAAU,CAAC,CAAC,GAAG;AAClD,WAAK,eAAe,MAAM,iCAAiC;AAAA,IAC7D;AAAA,EACF;AAAA,EAEQ,iBAAiB;AACvB,QAAI,KAAK,YAAY,qBAAqB,cAAc;AACtD,WAAK,yBAAyB;AAAA,IAChC,OAAO;AACL,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EAEA,qBAAqB;AACnB,SAAK,eAAe;AACpB,UAAM,eAA+B,CAAC;AACtC,SAAK,eAAe,KAAK,QAAQ,KAAK,eAAe,OAAO,KAAK,eAAe,KAAK;AACrF,eAAW,aAAa,OAAO,OAAO,KAAK,cAAc,GAAG;AAC1D,mBAAa,KAAK,GAAG,UAAU,oBAAoB,CAAC;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACF;;;AC3LO,IAAM,iBAAN,MAAqB;AAAA,EAJ5B,OAI4B;AAAA;AAAA;AAAA,EAC1B,OAAO,MACL,QACA,WACA,kBACAC,cACgB;AAChB,UAAM,eAAe,IAAI,aAAa,QAAQ,WAAW,kBAAkBA,YAAW;AACtF,WAAO,aAAa,mBAAmB;AAAA,EACzC;AACF;;;ACWA,IAAI,YAAY;AAEhB,IAAI;AAEJ,IAAI,gBAA+B,sBAAsB;AACzD,IAAI,qBAAyC,2BAA2B;AACxE,IAAI,cAA2B,oBAAoB;AACnD,IAAI,mBAAmB,mBAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC;AACjG,IAAI,cAAc;AAClB,IAAI,cAAc;AAOlB,SAAS,6BAAiD;AACxD,QAAM,wBAAwB,kBAAkB;AAChD,QAAM,SAAmB,UAAU;AACnC,SAAO,cAAc,sBAAsB,SAAS,OAAO,eAAe,OAAO;AACnF;AAJS;AAKT,SAAS,wBAAuC;AAC9C,QAAM,SAAmB,UAAU;AACnC,SAAO;AAAA,IACL,sBAAc;AAAA,IACd,OAAO;AAAA,EACT;AACF;AANS;AAQT,SAAS,sBAAmC;AAC1C,SAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY,CAAC;AAAA,IACf;AAAA,IACA,OAAO;AAAA,IACP,OAAO,CAAC;AAAA,EACV;AACF;AAhBS;AAkBT,SAAS,cAAc,MAAc;AACnC,QAAM,SAAmB,UAAU;AACnC,SAAO,aAAa,KAAK,KAAK,GAAG,MAAM;AACzC;AAHS;AAKT,SAAS,WAAW,MAAgB;AAClC,gBAAc;AAChB;AAFS;AAGT,SAAS,eAAe,aAAqB;AAC3C,MAAI,gBAAgB,cAAc;AAChC,kBAAc,mBAAmB;AAAA,EACnC,OAAO;AACL,kBAAc,mBAAmB;AAAA,EACnC;AACF;AANS;AAOT,SAAS,cAAc,OAAuB;AAC5C,cAAY,MAAM,QAAQ,cAAc,MAAM,IAAI;AACpD;AAFS;AAGT,SAAS,kBAAkB,KAAa,KAAa;AACnD,cAAY,QAAQ,EAAE,MAAM,UAAU,OAAO,YAAY,MAAM,OAAO,KAAK,IAAI;AAC/E,gBAAc;AAChB;AAHS;AAIT,SAAS,aAAa,YAA8B;AAClD,cAAY,QAAQ;AAAA,IAClB,MAAM;AAAA,IACN,OAAO,YAAY,MAAM;AAAA,IACzB,YAAY,WAAW,IAAI,CAAC,MAAM,cAAc,EAAE,IAAI,CAAC;AAAA,EACzD;AACA,gBAAc;AAChB;AAPS;AAQT,SAAS,cAAc,OAAuB;AAC5C,cAAY,MAAM,QAAQ,cAAc,MAAM,IAAI;AACpD;AAFS;AAGT,SAAS,kBAAkB,KAAa,KAAa;AACnD,cAAY,QAAQ,EAAE,MAAM,UAAU,OAAO,YAAY,MAAM,OAAO,KAAK,IAAI;AAC/E,gBAAc;AAChB;AAHS;AAMT,SAAS,0BAA0B,MAAgB;AACjD,QAAM,WAAW,KAAK,IAAI,GAAG,IAAI;AACjC,QAAM,WAAW,KAAK,IAAI,GAAG,IAAI;AACjC,QAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,QAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,cAAY,QAAQ;AAAA,IAClB,MAAM;AAAA,IACN,OAAO,YAAY,MAAM;AAAA,IACzB,KAAK,KAAK,IAAI,cAAc,QAAQ;AAAA,IACpC,KAAK,KAAK,IAAI,cAAc,QAAQ;AAAA,EACtC;AACF;AAXS;AAaT,SAAS,6BAA6B,MAAoC;AACxE,MAAI,UAA8B,CAAC;AACnC,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa;AAChB,UAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,UAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,sBAAkB,KAAK,IAAI,cAAc,CAAC,GAAG,KAAK,IAAI,cAAc,KAAK,MAAM,CAAC;AAAA,EAClF;AACA,MAAI,CAAC,aAAa;AAChB,8BAA0B,IAAI;AAAA,EAChC;AAEA,MAAI,eAAe,YAAY,KAAK,GAAG;AACrC,cAAU,YAAY,MAAM,WAAW,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,EACnE;AAEA,MAAI,iBAAiB,YAAY,KAAK,GAAG;AACvC,UAAM,MAAM,YAAY,MAAM;AAC9B,UAAM,MAAM,YAAY,MAAM;AAC9B,UAAM,QAAQ,MAAM,QAAQ,KAAK,SAAS;AAC1C,UAAM,aAAuB,CAAC;AAC9B,aAAS,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM;AACrC,iBAAW,KAAK,GAAG,CAAC,EAAE;AAAA,IACxB;AACA,cAAU,WAAW,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,EACjD;AAEA,SAAO;AACT;AA9BS;AAgCT,SAAS,wBAAwBC,YAA2B;AAC1D,SAAO,iBAAiBA,eAAc,IAAI,IAAIA,aAAY,iBAAiB,MAAM;AACnF;AAFS;AAIT,SAAS,YAAY,OAAuB,MAAgB;AAC1D,QAAM,WAAW,6BAA6B,IAAI;AAClD,cAAY,MAAM,KAAK;AAAA,IACrB,MAAM;AAAA,IACN,YAAY,wBAAwB,SAAS;AAAA,IAC7C,aAAa;AAAA,IACb,MAAM;AAAA,EACR,CAAC;AACD;AACF;AATS;AAWT,SAAS,WAAW,OAAuB,MAAgB;AACzD,QAAM,WAAW,6BAA6B,IAAI;AAClD,cAAY,MAAM,KAAK;AAAA,IACrB,MAAM;AAAA,IACN,MAAM,wBAAwB,SAAS;AAAA,IACvC,MAAM;AAAA,EACR,CAAC;AACD;AACF;AARS;AAUT,SAAS,kBAAkC;AACzC,MAAI,YAAY,MAAM,WAAW,GAAG;AAClC,UAAM,MAAM,yDAAyD;AAAA,EACvE;AACA,cAAY,QAAQ,gBAAgB;AACpC,SAAO,eAAe,MAAM,eAAe,aAAa,oBAAoB,WAAW;AACzF;AANS;AAQT,SAAS,sBAAsB;AAC7B,SAAO;AACT;AAFS;AAIT,SAAS,iBAAiB;AACxB,SAAO;AACT;AAFS;AAIT,SAAS,iBAAiB;AACxB,SAAO;AACT;AAFS;AAIT,IAAMC,SAAQ,kCAAY;AACxB,QAAY;AACZ,cAAY;AACZ,kBAAgB,sBAAsB;AACtC,gBAAc,oBAAoB;AAClC,uBAAqB,2BAA2B;AAChD,qBAAmB,mBAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC;AAC7F,gBAAc;AACd,gBAAc;AAChB,GATc;AAWd,IAAO,oBAAQ;AAAA,EACb;AAAA,EACA,OAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AC7NO,IAAM,OAAO,wBAAC,KAAa,IAAY,UAAkB,YAAqB;AACnF,QAAM,KAAK,QAAQ;AACnB,QAAM,cAAc,GAAG,oBAAoB;AAC3C,QAAM,cAAc,GAAG,eAAe;AACtC,QAAM,YAAY,GAAG,eAAe,EAAE,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC;AACzE,WAAS,oBAAoB,eAAgC;AAC3D,WAAO,kBAAkB,QAAQ,qBAAqB;AAAA,EACxD;AAFS;AAIT,WAAS,cAAc,aAAgC;AACrD,WAAO,gBAAgB,SAAS,UAAU,gBAAgB,UAAU,QAAQ;AAAA,EAC9E;AAFS;AAIT,WAAS,sBAAsB,MAAgB;AAC7C,WAAO,aAAa,KAAK,CAAC,KAAK,KAAK,CAAC,YAAY,KAAK,YAAY,CAAC;AAAA,EACrE;AAFS;AAIT,MAAI,MAAM,8BAA8B,GAAG;AAE3C,QAAM,MAAM,iBAAiB,EAAE;AAC/B,QAAM,QAAQ,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM;AAClD,QAAM,aAAa,MAChB,OAAO,MAAM,EACb,KAAK,SAAS,YAAY,KAAK,EAC/B,KAAK,UAAU,YAAY,MAAM,EACjC,KAAK,SAAS,YAAY;AAG7B,mBAAiB,KAAK,YAAY,QAAQ,YAAY,OAAO,IAAI;AAEjE,MAAI,KAAK,WAAW,OAAO,YAAY,KAAK,IAAI,YAAY,MAAM,EAAE;AAEpE,aAAW,KAAK,QAAQ,YAAY,eAAe;AAEnD,KAAG,WAAW,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,mBAAmB,CAAC;AAEhE,QAAM,SAAyB,GAAG,gBAAgB;AAElD,QAAM,SAA8B,CAAC;AAYrC,WAAS,SAAS,OAAiB;AACjC,QAAI,OAAO;AACX,QAAI,SAAS;AACb,eAAW,CAAC,CAAC,KAAK,MAAM,QAAQ,GAAG;AACjC,UAAI,SAAS;AACb,UAAI,IAAI,KAAK,OAAO,MAAM,GAAG;AAC3B,iBAAS,OAAO,MAAM;AAAA,MACxB;AACA,gBAAU,MAAM,CAAC;AACjB,aAAO,OAAO,MAAM;AACpB,UAAI,CAAC,MAAM;AACT,eAAO,OAAO,MAAM,IAAI,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM,CAAC,CAAC;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAfS;AAiBT,aAAW,SAAS,QAAQ;AAC1B,QAAI,MAAM,KAAK,WAAW,GAAG;AAC3B;AAAA,IACF;AAEA,UAAM,aAAa,SAAS,MAAM,UAAU;AAE5C,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,mBACG,UAAU,MAAM,EAChB,KAAK,MAAM,IAAI,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,SAAS,KAAK,CAAC,EAC1B,KAAK,KAAK,CAAC,SAAS,KAAK,CAAC,EAC1B,KAAK,SAAS,CAAC,SAAS,KAAK,KAAK,EAClC,KAAK,UAAU,CAAC,SAAS,KAAK,MAAM,EACpC,KAAK,QAAQ,CAAC,SAAS,KAAK,IAAI,EAChC,KAAK,UAAU,CAAC,SAAS,KAAK,UAAU,EACxC,KAAK,gBAAgB,CAAC,SAAS,KAAK,WAAW;AAElD,YAAI,YAAY,eAAe;AAC7B,cAAI,YAAY,qBAAqB,cAAc;AAUjD,gBAASC,oBAAT,SAA0B,MAAe,UAA2B;AAClE,oBAAM,EAAE,MAAM,MAAM,IAAI;AAExB,oBAAM,YAAoB,WAAW,MAAM,SAAS;AAEpD,qBAAO,aAAa,KAAK,QAAQ;AAAA,YACnC;AANS,mCAAAA;AAAA,mBAAAA,mBAAA;AART,kBAAM,kBAAkB;AAGxB,kBAAM,aAAa,MAAM,KACtB,IAAI,CAAC,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,UAAU,CAAC,EAAE,SAAS,EAAE,EAAE,EAC3D,OAAO,CAAC,SAAS,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,CAAC;AAa/D,kBAAM,qBAAqB,WAAW,IAAI,CAAC,SAAS;AAClD,oBAAM,EAAE,KAAK,IAAI;AACjB,kBAAI,WAAW,KAAK,SAAS;AAE7B,qBAAO,CAACA,kBAAiB,MAAM,QAAQ,KAAK,WAAW,GAAG;AACxD,4BAAY;AAAA,cACd;AACA,qBAAO;AAAA,YACT,CAAC;AAGD,kBAAM,kBAAkB,KAAK,MAAM,KAAK,IAAI,GAAG,kBAAkB,CAAC;AAElE,uBACG,UAAU,MAAM,EAChB,KAAK,UAAU,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,EAAE,EACtD,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,SAAS,CAAC,EACtD,KAAK,eAAe,KAAK,EACzB,KAAK,qBAAqB,QAAQ,EAClC,KAAK,QAAQ,OAAO,EACpB,KAAK,aAAa,GAAG,eAAe,IAAI,EACxC,KAAK,CAAC,SAAS,KAAK,KAAK;AAAA,UAC9B,OAAO;AASL,gBAASC,aAAT,SAAmB,MAAe,UAAkBC,UAA0B;AAC5E,oBAAM,EAAE,MAAM,MAAM,IAAI;AACxB,oBAAM,kBAAkB;AACxB,oBAAM,YAAY,WAAW,MAAM,SAAS;AAG5C,oBAAM,UAAU,KAAK,IAAI,KAAK,QAAQ;AACtC,oBAAM,WAAW,UAAU,YAAY;AACvC,oBAAM,YAAY,UAAU,YAAY;AAGxC,oBAAM,iBAAiB,YAAY,KAAK,KAAK,aAAa,KAAK,IAAI,KAAK;AAIxE,oBAAM,eAAe,KAAK,IAAIA,WAAU,YAAY,KAAK,IAAI,KAAK;AAElE,qBAAO,kBAAkB;AAAA,YAC3B;AAlBS,4BAAAD;AAAA,mBAAAA,YAAA;AART,kBAAM,UAAU;AAGhB,kBAAM,aAAa,MAAM,KACtB,IAAI,CAAC,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,UAAU,CAAC,EAAE,SAAS,EAAE,EAAE,EAC3D,OAAO,CAAC,SAAS,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,CAAC;AAyB/D,kBAAM,qBAAqB,WAAW,IAAI,CAAC,SAAS;AAClD,oBAAM,EAAE,MAAM,MAAM,IAAI;AACxB,kBAAI,WAAW,KAAK,SAAS,MAAM,SAAS;AAG5C,qBAAO,CAACA,WAAU,MAAM,UAAU,OAAO,KAAK,WAAW,GAAG;AAC1D,4BAAY;AAAA,cACd;AACA,qBAAO;AAAA,YACT,CAAC;AAGD,kBAAM,kBAAkB,KAAK,MAAM,KAAK,IAAI,GAAG,kBAAkB,CAAC;AAGlE,uBACG,UAAU,MAAM,EAChB,KAAK,UAAU,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,CAAC,EACrD,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,OAAO,EACzC,KAAK,eAAe,QAAQ,EAC5B,KAAK,qBAAqB,SAAS,EACnC,KAAK,QAAQ,OAAO,EACpB,KAAK,aAAa,GAAG,eAAe,IAAI,EACxC,KAAK,CAAC,SAAS,KAAK,KAAK;AAAA,UAC9B;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,mBACG,UAAU,MAAM,EAChB,KAAK,MAAM,IAAI,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,QAAQ,CAAC,SAAS,KAAK,IAAI,EAChC,KAAK,aAAa,CAAC,SAAS,KAAK,QAAQ,EACzC,KAAK,qBAAqB,CAAC,SAAS,oBAAoB,KAAK,WAAW,CAAC,EACzE,KAAK,eAAe,CAAC,SAAS,cAAc,KAAK,aAAa,CAAC,EAC/D,KAAK,aAAa,CAAC,SAAS,sBAAsB,IAAI,CAAC,EACvD,KAAK,CAAC,SAAS,KAAK,IAAI;AAC3B;AAAA,MACF,KAAK;AACH,mBACG,UAAU,MAAM,EAChB,KAAK,MAAM,IAAI,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,SAAS,KAAK,IAAI,EAC7B,KAAK,QAAQ,CAAC,SAAU,KAAK,OAAO,KAAK,OAAO,MAAO,EACvD,KAAK,UAAU,CAAC,SAAS,KAAK,UAAU,EACxC,KAAK,gBAAgB,CAAC,SAAS,KAAK,WAAW;AAClD;AAAA,IACJ;AAAA,EACF;AACF,GAhOoB;AAkOpB,IAAO,0BAAQ;AAAA,EACb;AACF;;;AC1OO,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AACF;", "names": ["o", "parser", "lexer", "tmpSVGGroup", "tmpSVGGroup", "plotIndex", "plotIndex", "tmpSVGGroup", "tmpSVGGroup", "plotIndex", "clear", "fitsHorizontally", "fitsInBar", "yOffset"]}