{"version": 3, "sources": ["../../../src/language/treemap/tokenBuilder.ts", "../../../src/language/treemap/valueConverter.ts", "../../../src/language/treemap/treemap-validator.ts", "../../../src/language/treemap/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class TreemapTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['treemap']);\n  }\n}\n", "import type { CstNode, GrammarAST, ValueType } from 'langium';\nimport { AbstractMermaidValueConverter } from '../common/index.js';\n\n// Regular expression to extract className and styleText from a classDef terminal\nconst classDefRegex = /classDef\\s+([A-Z_a-z]\\w+)(?:\\s+([^\\n\\r;]*))?;?/;\n\nexport class TreemapValueConverter extends AbstractMermaidValueConverter {\n  protected runCustomConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    if (rule.name === 'NUMBER2') {\n      // Convert to a number by removing any commas and converting to float\n      return parseFloat(input.replace(/,/g, ''));\n    } else if (rule.name === 'SEPARATOR') {\n      // Remove quotes\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === 'STRING2') {\n      // Remove quotes\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === 'INDENTATION') {\n      return input.length;\n    } else if (rule.name === 'ClassDef') {\n      // Handle both CLASS_DEF terminal and ClassDef rule\n      if (typeof input !== 'string') {\n        // If we're dealing with an already processed object, return it as is\n        return input;\n      }\n\n      // Extract className and styleText from classDef statement\n      const match = classDefRegex.exec(input);\n      if (match) {\n        // Use any type to avoid type issues\n        return {\n          $type: 'ClassDefStatement',\n          className: match[1],\n          styleText: match[2] || undefined,\n        } as any;\n      }\n    }\n    return undefined;\n  }\n}\n", "import type { ValidationAcceptor, ValidationChecks } from 'langium';\nimport type { MermaidAstType, Treemap } from '../generated/ast.js';\nimport type { TreemapServices } from './module.js';\n\n/**\n * Register custom validation checks.\n */\nexport function registerValidationChecks(services: TreemapServices) {\n  const validator = services.validation.TreemapValidator;\n  const registry = services.validation.ValidationRegistry;\n  if (registry) {\n    // Use any to bypass type checking since we know Treemap is part of the AST\n    // but the type system is having trouble with it\n    const checks: ValidationChecks<MermaidAstType> = {\n      Treemap: validator.checkSingleRoot.bind(validator),\n      // Remove unused validation for TreemapRow\n    };\n    registry.register(checks, validator);\n  }\n}\n\n/**\n * Implementation of custom validations.\n */\nexport class TreemapValidator {\n  /**\n   * Validates that a treemap has only one root node.\n   * A root node is defined as a node that has no indentation.\n   */\n  checkSingleRoot(doc: Treemap, accept: ValidationAcceptor): void {\n    let rootNodeIndentation;\n\n    for (const row of doc.TreemapRows) {\n      // Skip non-node items or items without a type\n      if (!row.item) {\n        continue;\n      }\n\n      if (\n        rootNodeIndentation === undefined && // Check if this is a root node (no indentation)\n        row.indent === undefined\n      ) {\n        rootNodeIndentation = 0;\n      } else if (row.indent === undefined) {\n        // If we've already found a root node, report an error\n        accept('error', 'Multiple root nodes are not allowed in a treemap.', {\n          node: row,\n          property: 'item',\n        });\n      } else if (\n        rootNodeIndentation !== undefined &&\n        rootNodeIndentation >= parseInt(row.indent, 10)\n      ) {\n        accept('error', 'Multiple root nodes are not allowed in a treemap.', {\n          node: row,\n          property: 'item',\n        });\n      }\n    }\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { MermaidGeneratedSharedModule, TreemapGeneratedModule } from '../generated/module.js';\nimport { TreemapTokenBuilder } from './tokenBuilder.js';\nimport { TreemapValueConverter } from './valueConverter.js';\nimport { TreemapValidator, registerValidationChecks } from './treemap-validator.js';\n\n/**\n * Declaration of `Treemap` services.\n */\ninterface TreemapAddedServices {\n  parser: {\n    TokenBuilder: TreemapTokenBuilder;\n    ValueConverter: TreemapValueConverter;\n  };\n  validation: {\n    TreemapValidator: TreemapValidator;\n  };\n}\n\n/**\n * Union of Langium default services and `Treemap` services.\n */\nexport type TreemapServices = LangiumCoreServices & TreemapAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Treemap` services.\n */\nexport const TreemapModule: Module<\n  TreemapServices,\n  PartialLangiumCoreServices & TreemapAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new TreemapTokenBuilder(),\n    ValueConverter: () => new TreemapValueConverter(),\n  },\n  validation: {\n    TreemapValidator: () => new TreemapValidator(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createTreemapServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Treemap: TreemapServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Treemap: TreemapServices = inject(\n    createDefaultCoreModule({ shared }),\n    TreemapGeneratedModule,\n    TreemapModule\n  );\n  shared.ServiceRegistry.register(Treemap);\n\n  // Register validation checks\n  registerValidationChecks(Treemap);\n\n  return { shared, Treemap };\n}\n"], "mappings": "wGAEO,IAAMA,EAAN,cAAkCC,CAA4B,CAFrE,MAEqE,CAAAC,EAAA,4BAC5D,aAAc,CACnB,MAAM,CAAC,SAAS,CAAC,CACnB,CACF,ECFA,IAAMC,EAAgB,iDAETC,EAAN,cAAoCC,CAA8B,CANzE,MAMyE,CAAAC,EAAA,8BAC7D,mBACRC,EACAC,EACAC,EACuB,CACvB,GAAIF,EAAK,OAAS,UAEhB,OAAO,WAAWC,EAAM,QAAQ,KAAM,EAAE,CAAC,EACpC,GAAID,EAAK,OAAS,YAEvB,OAAOC,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,EACrC,GAAID,EAAK,OAAS,UAEvB,OAAOC,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,EACrC,GAAID,EAAK,OAAS,cACvB,OAAOC,EAAM,OACR,GAAID,EAAK,OAAS,WAAY,CAEnC,GAAI,OAAOC,GAAU,SAEnB,OAAOA,EAIT,IAAME,EAAQP,EAAc,KAAKK,CAAK,EACtC,GAAIE,EAEF,MAAO,CACL,MAAO,oBACP,UAAWA,EAAM,CAAC,EAClB,UAAWA,EAAM,CAAC,GAAK,MACzB,CAEJ,CAEF,CACF,ECpCO,SAASC,EAAyBC,EAA2B,CAClE,IAAMC,EAAYD,EAAS,WAAW,iBAChCE,EAAWF,EAAS,WAAW,mBACrC,GAAIE,EAAU,CAGZ,IAAMC,EAA2C,CAC/C,QAASF,EAAU,gBAAgB,KAAKA,CAAS,CAEnD,EACAC,EAAS,SAASC,EAAQF,CAAS,CACrC,CACF,CAZgBG,EAAAL,EAAA,4BAiBT,IAAMM,EAAN,KAAuB,CAxB9B,MAwB8B,CAAAD,EAAA,yBAK5B,gBAAgBE,EAAcC,EAAkC,CAC9D,IAAIC,EAEJ,QAAWC,KAAOH,EAAI,YAEfG,EAAI,OAKPD,IAAwB,QACxBC,EAAI,SAAW,OAEfD,EAAsB,EACbC,EAAI,SAAW,OAExBF,EAAO,QAAS,oDAAqD,CACnE,KAAME,EACN,SAAU,MACZ,CAAC,EAEDD,IAAwB,QACxBA,GAAuB,SAASC,EAAI,OAAQ,EAAE,GAE9CF,EAAO,QAAS,oDAAqD,CACnE,KAAME,EACN,SAAU,MACZ,CAAC,EAGP,CACF,ECnBO,IAAMC,EAGT,CACF,OAAQ,CACN,aAAcC,EAAA,IAAM,IAAIC,EAAV,gBACd,eAAgBD,EAAA,IAAM,IAAIE,EAAV,iBAClB,EACA,WAAY,CACV,iBAAkBF,EAAA,IAAM,IAAIG,EAAV,mBACpB,CACF,EAgBO,SAASC,EAAsBC,EAA0CC,EAG9E,CACA,IAAMC,EAAoCC,EACxCC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAA2BH,EAC/BI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAd,CACF,EACA,OAAAQ,EAAO,gBAAgB,SAASI,CAAO,EAGvCG,EAAyBH,CAAO,EAEzB,CAAE,OAAAJ,EAAQ,QAAAI,CAAQ,CAC3B,CAnBgBX,EAAAI,EAAA", "names": ["TreemapTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "classDefRegex", "TreemapValueConverter", "AbstractMermaidValueConverter", "__name", "rule", "input", "_cstNode", "match", "registerValidationChecks", "services", "validator", "registry", "checks", "__name", "TreemapValidator", "doc", "accept", "rootNodeIndentation", "row", "TreemapModule", "__name", "TreemapTokenBuilder", "TreemapValueConverter", "TreemapValidator", "createTreemapServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Treemap", "createDefaultCoreModule", "TreemapGeneratedModule", "registerValidationChecks"]}