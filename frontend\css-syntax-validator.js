/**
 * CSS语法验证工具
 * 验证App.vue中的CSS语法是否正确
 */

const fs = require('fs');
const path = require('path');

// 读取App.vue文件
const appVuePath = path.join(__dirname, 'src', 'App.vue');

try {
    const content = fs.readFileSync(appVuePath, 'utf8');
    
    // 提取<style>标签中的CSS内容
    const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/);
    
    if (!styleMatch) {
        console.log('❌ 未找到<style>标签');
        process.exit(1);
    }
    
    const cssContent = styleMatch[1];
    
    // 基本的CSS语法检查
    const checks = [
        {
            name: '大括号匹配检查',
            test: () => {
                const openBraces = (cssContent.match(/\{/g) || []).length;
                const closeBraces = (cssContent.match(/\}/g) || []).length;
                return openBraces === closeBraces;
            }
        },
        {
            name: '媒体查询语法检查',
            test: () => {
                const mediaQueries = cssContent.match(/@media[^{]*\{/g) || [];
                return mediaQueries.every(query => query.includes('(') && query.includes(')'));
            }
        },
        {
            name: '选择器语法检查',
            test: () => {
                // 检查是否有孤立的大括号
                const lines = cssContent.split('\n');
                let inRule = false;
                let braceCount = 0;
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (line === '') continue;
                    
                    const openBraces = (line.match(/\{/g) || []).length;
                    const closeBraces = (line.match(/\}/g) || []).length;
                    
                    braceCount += openBraces - closeBraces;
                    
                    if (braceCount < 0) {
                        console.log(`❌ 第${i + 1}行发现多余的闭合大括号: ${line}`);
                        return false;
                    }
                }
                
                return braceCount === 0;
            }
        },
        {
            name: '品牌标识样式检查',
            test: () => {
                const requiredClasses = [
                    '.ai-brand-link',
                    '.ai-logo-image',
                    '.ai-brand-name',
                    '.ai-brand-tagline'
                ];
                
                return requiredClasses.every(className => 
                    cssContent.includes(className)
                );
            }
        },
        {
            name: '响应式设计检查',
            test: () => {
                const requiredMediaQueries = [
                    '@media (max-width: 480px)',
                    '@media (max-width: 768px)',
                    '@media (min-width: 1201px)'
                ];
                
                return requiredMediaQueries.every(query => 
                    cssContent.includes(query)
                );
            }
        }
    ];
    
    console.log('🔍 开始CSS语法验证...\n');
    
    let allPassed = true;
    
    checks.forEach((check, index) => {
        try {
            const result = check.test();
            if (result) {
                console.log(`✅ ${index + 1}. ${check.name} - 通过`);
            } else {
                console.log(`❌ ${index + 1}. ${check.name} - 失败`);
                allPassed = false;
            }
        } catch (error) {
            console.log(`❌ ${index + 1}. ${check.name} - 错误: ${error.message}`);
            allPassed = false;
        }
    });
    
    console.log('\n📊 验证结果:');
    
    if (allPassed) {
        console.log('🎉 所有CSS语法检查通过！');
        console.log('✨ 品牌标识优化代码质量良好');
        
        // 统计信息
        const stats = {
            totalLines: cssContent.split('\n').length,
            mediaQueries: (cssContent.match(/@media/g) || []).length,
            cssRules: (cssContent.match(/\{/g) || []).length,
            brandClasses: (cssContent.match(/\.ai-brand/g) || []).length
        };
        
        console.log('\n📈 代码统计:');
        console.log(`   总行数: ${stats.totalLines}`);
        console.log(`   媒体查询: ${stats.mediaQueries}个`);
        console.log(`   CSS规则: ${stats.cssRules}个`);
        console.log(`   品牌相关类: ${stats.brandClasses}个`);
        
    } else {
        console.log('⚠️  发现CSS语法问题，请检查并修复');
        process.exit(1);
    }
    
} catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
    process.exit(1);
}

console.log('\n🎯 品牌标识优化验证完成！');
