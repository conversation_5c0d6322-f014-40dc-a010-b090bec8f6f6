import{E as s,K as c,N as m,O as l,a as e,b as i,c as n,d as o,e as u,f as S}from"./chunk-6TGVXIR7.mjs";var r=class extends l{static{e(this,"RadarTokenBuilder")}constructor(){super(["radar-beta"])}};var v={parser:{TokenBuilder:e(()=>new r,"TokenBuilder"),ValueConverter:e(()=>new m,"ValueConverter")}};function x(d=u){let a=o(n(d),s),t=o(i({shared:a}),c,v);return a.ServiceRegistry.register(t),{shared:a,Radar:t}}e(x,"createRadarServices");export{v as a,x as b};
