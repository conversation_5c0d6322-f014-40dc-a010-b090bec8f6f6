# iFlytek 多模态智能面试评测系统 - 源码说明

## 📁 项目结构

```
├── frontend/                 # 前端源码
│   ├── src/                 # Vue.js 源码
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 通用组件
│   │   ├── services/       # 服务层
│   │   ├── utils/          # 工具函数
│   │   └── styles/         # 样式文件
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
├── backend/                 # 后端源码
│   ├── app/                # FastAPI 应用
│   │   ├── api/           # API 路由
│   │   ├── services/      # 业务服务
│   │   ├── models/        # 数据模型
│   │   └── utils/         # 工具函数
│   └── requirements.txt    # 后端依赖
└── docs/                   # 项目文档
```

## 🚀 开发环境搭建

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

### 后端开发
```bash
cd backend
pip install -r requirements.txt
python simple_start.py
```

## 🔧 核心技术

- **前端**: Vue.js 3 + Element Plus + Vite
- **后端**: Python FastAPI + SQLite
- **AI引擎**: iFlytek Spark LLM
- **UI框架**: Element Plus + WCAG 2.1 AA

## 📝 开发说明

本系统采用前后端分离架构，支持多模态AI面试评估功能。
详细的开发文档请参考 docs/ 目录。
