<template>
  <div class="learning-path-page">
    <div class="page-header">
      <h1>学习路径推荐</h1>
      <p>基于您的面试表现，为您推荐个性化学习路径</p>
    </div>

    <div class="learning-content">
      <div class="path-overview">
        <h2>推荐学习路径</h2>
        <div class="path-cards">
          <div v-for="path in learningPaths" :key="path.id" class="path-card">
            <div class="path-header">
              <h3>{{ path.title }}</h3>
              <span class="path-duration">{{ path.duration }}</span>
            </div>
            <p class="path-description">{{ path.description }}</p>
            <div class="path-skills">
              <span v-for="skill in path.skills" :key="skill" class="skill-tag">{{ skill }}</span>
            </div>
            <div class="path-actions">
              <el-button type="primary" @click="startLearning(path.id)">开始学习</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'

const router = useRouter()

const learningPaths = ref([
  {
    id: 1,
    title: 'AI算法基础强化',
    duration: '4周',
    description: '深入学习机器学习和深度学习核心算法',
    skills: ['Python', 'TensorFlow', '算法设计', '数据分析']
  },
  {
    id: 2,
    title: '系统设计能力提升',
    duration: '6周',
    description: '学习大规模系统设计和架构模式',
    skills: ['系统架构', '分布式系统', '数据库设计', '性能优化']
  }
])

const startLearning = (pathId) => {
  console.log('🎯 开始学习路径:', pathId)

  try {
    // 显示成功消息
    ElMessage.success('正在启动学习路径...')

    // 根据路径ID跳转到相应的学习页面
    switch (pathId) {
      case 1:
        ElNotification.success({
          title: '🚀 AI算法基础强化',
          message: '开始您的AI学习之旅！',
          duration: 3000
        })
        // 跳转到AI学习模块
        router.push('/enhanced-learning-path/ai-algorithms')
        break
      case 2:
        ElNotification.success({
          title: '🏗️ 系统设计能力提升',
          message: '开始系统架构学习！',
          duration: 3000
        })
        // 跳转到系统设计学习模块
        router.push('/enhanced-learning-path/system-design')
        break
      default:
        ElMessage.info('该学习路径正在开发中...')
        // 跳转到通用学习页面
        router.push(`/enhanced-learning-path/${pathId}`)
    }
  } catch (error) {
    console.error('❌ 启动学习路径失败:', error)
    ElMessage.error('启动学习路径失败，请重试')
  }
}
</script>

<style scoped>
.learning-path-page {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(24, 144, 255, 0.08) 0%,
    rgba(102, 126, 234, 0.06) 25%,
    rgba(0, 102, 204, 0.04) 50%,
    rgba(76, 81, 191, 0.06) 75%,
    rgba(118, 75, 162, 0.08) 100%
  );
  background-attachment: fixed;
  position: relative;
  padding: 24px;
}

.learning-path-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(24, 144, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(0, 102, 204, 0.06) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.learning-path-page > * {
  position: relative;
  z-index: 1;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px 24px;
  box-shadow:
    0 8px 32px rgba(24, 144, 255, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.page-header h1 {
  font-size: var(--font-size-2xl);
  color: #1890ff;
  margin: 0 0 8px 0;
  font-weight: 700;
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
  text-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.page-header p {
  color: #666;
  font-size: var(--font-size-base);
  margin: 0;
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
}

.learning-content {
  max-width: 1000px;
  margin: 0 auto;
}

.path-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.path-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow:
    0 8px 32px rgba(24, 144, 255, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(24, 144, 255, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.path-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #667eea, #0066cc);
  opacity: 0.8;
}

.path-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 48px rgba(24, 144, 255, 0.12),
    0 8px 24px rgba(0, 0, 0, 0.06);
  border-color: rgba(24, 144, 255, 0.15);
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.path-header h3 {
  margin: 0;
  color: var(--iflytek-text-primary);
}

.path-duration {
  background: var(--iflytek-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: var(--font-size-xs);
}

.path-description {
  color: var(--iflytek-text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.path-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.skill-tag {
  background: var(--iflytek-bg-secondary);
  color: var(--iflytek-text-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--font-size-xs);
  border: 1px solid var(--iflytek-border-secondary);
}

.path-actions {
  text-align: center;
}
</style>
