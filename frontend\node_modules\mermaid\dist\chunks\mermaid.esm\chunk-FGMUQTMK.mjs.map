{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-6MM43HOH.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  ArchitectureGeneratedModule,\n  MermaidGeneratedSharedModule,\n  __name\n} from \"./chunk-YAJQ3QCK.mjs\";\n\n// src/language/architecture/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/architecture/tokenBuilder.ts\nvar ArchitectureTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"ArchitectureTokenBuilder\");\n  }\n  constructor() {\n    super([\"architecture\"]);\n  }\n};\n\n// src/language/architecture/valueConverter.ts\nvar ArchitectureValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"ArchitectureValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name === \"ARCH_ICON\") {\n      return input.replace(/[()]/g, \"\").trim();\n    } else if (rule.name === \"ARCH_TEXT_ICON\") {\n      return input.replace(/[\"()]/g, \"\");\n    } else if (rule.name === \"ARCH_TITLE\") {\n      return input.replace(/[[\\]]/g, \"\").trim();\n    }\n    return void 0;\n  }\n};\n\n// src/language/architecture/module.ts\nvar ArchitectureModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new ArchitectureTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new ArchitectureValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createArchitectureServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Architecture = inject(\n    createDefaultCoreModule({ shared }),\n    ArchitectureGeneratedModule,\n    ArchitectureModule\n  );\n  shared.ServiceRegistry.register(Architecture);\n  return { shared, Architecture };\n}\n__name(createArchitectureServices, \"createArchitectureServices\");\n\nexport {\n  ArchitectureModule,\n  createArchitectureServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,IAAI,2BAA2B,cAAc,4BAA4B;AAAA,EAjBzE,OAiByE;AAAA;AAAA;AAAA,EACvE,OAAO;AACL,IAAAA,QAAO,MAAM,0BAA0B;AAAA,EACzC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,cAAc,CAAC;AAAA,EACxB;AACF;AAGA,IAAI,6BAA6B,cAAc,8BAA8B;AAAA,EA3B7E,OA2B6E;AAAA;AAAA;AAAA,EAC3E,OAAO;AACL,IAAAA,QAAO,MAAM,4BAA4B;AAAA,EAC3C;AAAA,EACA,mBAAmB,MAAM,OAAO,UAAU;AACxC,QAAI,KAAK,SAAS,aAAa;AAC7B,aAAO,MAAM,QAAQ,SAAS,EAAE,EAAE,KAAK;AAAA,IACzC,WAAW,KAAK,SAAS,kBAAkB;AACzC,aAAO,MAAM,QAAQ,UAAU,EAAE;AAAA,IACnC,WAAW,KAAK,SAAS,cAAc;AACrC,aAAO,MAAM,QAAQ,UAAU,EAAE,EAAE,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,IACN,cAA8B,gBAAAA,QAAO,MAAM,IAAI,yBAAyB,GAAG,cAAc;AAAA,IACzF,gBAAgC,gBAAAA,QAAO,MAAM,IAAI,2BAA2B,GAAG,gBAAgB;AAAA,EACjG;AACF;AACA,SAAS,2BAA2B,UAAU,iBAAiB;AAC7D,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,eAAe;AAAA,IACnB,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,YAAY;AAC5C,SAAO,EAAE,QAAQ,aAAa;AAChC;AAZS;AAaTA,QAAO,4BAA4B,4BAA4B;", "names": ["__name"]}