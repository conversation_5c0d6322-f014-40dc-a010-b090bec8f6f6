{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../../../src/css/syntax/parser.ts"], "names": [], "mappings": ";;;AAAA,yCAQqB;AAoBrB;IAGI,gBAAY,MAAkB;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAEM,aAAM,GAAb,UAAc,KAAa;QACvB,IAAM,SAAS,GAAG,IAAI,qBAAS,EAAE,CAAC;QAClC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IACxC,CAAC;IAEM,iBAAU,GAAjB,UAAkB,KAAa;QAC3B,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE,CAAC;IACtD,CAAC;IAEM,kBAAW,GAAlB,UAAmB,KAAa;QAC5B,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,CAAC;IACvD,CAAC;IAED,oCAAmB,GAAnB;QACI,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,OAAO,KAAK,CAAC,IAAI,8BAA+B,EAAE;YAC9C,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;SAC/B;QAED,IAAI,KAAK,CAAC,IAAI,uBAAwB,EAAE;YACpC,MAAM,IAAI,WAAW,CAAC,mDAAmD,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE3C,GAAG;YACC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;SAC/B,QAAQ,KAAK,CAAC,IAAI,8BAA+B,EAAE;QAEpD,IAAI,KAAK,CAAC,IAAI,uBAAwB,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,IAAI,WAAW,CAAC,kFAAkF,CAAC,CAAC;IAC9G,CAAC;IAED,qCAAoB,GAApB;QACI,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,OAAO,IAAI,EAAE;YACT,IAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC3C,IAAI,KAAK,CAAC,IAAI,uBAAwB,EAAE;gBACpC,OAAO,MAAM,CAAC;aACjB;YACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,MAAM,CAAC,IAAI,EAAE,CAAC;SACjB;IACL,CAAC;IAEO,sCAAqB,GAA7B;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAElC,QAAQ,KAAK,CAAC,IAAI,EAAE;YAChB,uCAAwC;YACxC,wCAAyC;YACzC;gBACI,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/C;gBACI,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SAC1C;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,mCAAkB,GAA1B,UAA2B,IAAkB;QACzC,IAAM,KAAK,GAAa,EAAC,IAAI,MAAA,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC;QAE3C,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,OAAO,IAAI,EAAE;YACT,IAAI,KAAK,CAAC,IAAI,uBAAwB,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBACrE,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3B,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;YAChD,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;SAC/B;IACL,CAAC;IAEO,gCAAe,GAAvB,UAAwB,aAA+B;QACnD,IAAM,WAAW,GAAgB;YAC7B,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,MAAM,EAAE,EAAE;YACV,IAAI,mBAAoB;SAC3B,CAAC;QAEF,OAAO,IAAI,EAAE;YACT,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAClC,IAAI,KAAK,CAAC,IAAI,uBAAwB,IAAI,KAAK,CAAC,IAAI,oCAAsC,EAAE;gBACxF,OAAO,WAAW,CAAC;aACtB;YAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3B,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;SACzD;IACL,CAAC;IAEO,6BAAY,GAApB;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACnC,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,qBAAS,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5D,CAAC;IAEO,+BAAc,GAAtB,UAAuB,KAAe;QAClC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IACL,aAAC;AAAD,CAAC,AAjHD,IAiHC;AAjHY,wBAAM;AAmHZ,IAAM,gBAAgB,GAAG,UAAC,KAAe,IAA8B,OAAA,KAAK,CAAC,IAAI,6BAA8B,EAAxC,CAAwC,CAAC;AAA1G,QAAA,gBAAgB,oBAA0F;AAChH,IAAM,aAAa,GAAG,UAAC,KAAe,IAAgC,OAAA,KAAK,CAAC,IAAI,0BAA2B,EAArC,CAAqC,CAAC;AAAtG,QAAA,aAAa,iBAAyF;AAC5G,IAAM,YAAY,GAAG,UAAC,KAAe,IAAgC,OAAA,KAAK,CAAC,IAAI,yBAA0B,EAApC,CAAoC,CAAC;AAApG,QAAA,YAAY,gBAAwF;AAC1G,IAAM,aAAa,GAAG,UAAC,KAAe,IAAgC,OAAA,KAAK,CAAC,IAAI,yBAA2B,EAArC,CAAqC,CAAC;AAAtG,QAAA,aAAa,iBAAyF;AAC5G,IAAM,gBAAgB,GAAG,UAAC,KAAe,EAAE,KAAa;IAC3D,OAAA,oBAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;AAA5C,CAA4C,CAAC;AADpC,QAAA,gBAAgB,oBACoB;AAE1C,IAAM,aAAa,GAAG,UAAC,KAAe,IAAc,OAAA,KAAK,CAAC,IAAI,8BAA+B,EAAzC,CAAyC,CAAC;AAAxF,QAAA,aAAa,iBAA2E;AAC9F,IAAM,uBAAuB,GAAG,UAAC,KAAe;IACnD,OAAA,KAAK,CAAC,IAAI,8BAA+B,IAAI,KAAK,CAAC,IAAI,wBAA0B;AAAjF,CAAiF,CAAC;AADzE,QAAA,uBAAuB,2BACkD;AAE/E,IAAM,iBAAiB,GAAG,UAAC,MAAkB;IAChD,IAAM,IAAI,GAAiB,EAAE,CAAC;IAC9B,IAAI,GAAG,GAAe,EAAE,CAAC;IACzB,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;QACjB,IAAI,KAAK,CAAC,IAAI,wBAA0B,EAAE;YACtC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;gBAClB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;aACvE;YACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACf,GAAG,GAAG,EAAE,CAAC;YACT,OAAO;SACV;QAED,IAAI,KAAK,CAAC,IAAI,8BAA+B,EAAE;YAC3C,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnB;IACL,CAAC,CAAC,CAAC;IACH,IAAI,GAAG,CAAC,MAAM,EAAE;QACZ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAClB;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAtBW,QAAA,iBAAiB,qBAsB5B;AAEF,IAAM,gBAAgB,GAAG,UAAC,KAAe,EAAE,IAAkB;IACzD,IAAI,IAAI,sCAAuC,IAAI,KAAK,CAAC,IAAI,uCAAwC,EAAE;QACnG,OAAO,IAAI,CAAC;KACf;IACD,IAAI,IAAI,uCAAwC,IAAI,KAAK,CAAC,IAAI,wCAAyC,EAAE;QACrG,OAAO,IAAI,CAAC;KACf;IAED,OAAO,IAAI,mCAAqC,IAAI,KAAK,CAAC,IAAI,oCAAsC,CAAC;AACzG,CAAC,CAAC"}