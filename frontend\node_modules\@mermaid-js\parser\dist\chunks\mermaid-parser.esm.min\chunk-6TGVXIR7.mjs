var Tx=Object.create;var Pa=Object.defineProperty;var Rx=Object.getOwnPropertyDescriptor;var Ax=Object.getOwnPropertyNames;var Ex=Object.getPrototypeOf,vx=Object.prototype.hasOwnProperty;var s=(t,e)=>Pa(t,"name",{value:e,configurable:!0});var Ma=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),br=(t,e)=>{for(var r in e)Pa(t,r,{get:e[r],enumerable:!0})},Zl=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Ax(e))!vx.call(t,i)&&i!==r&&Pa(t,i,{get:()=>e[i],enumerable:!(n=Rx(e,i))||n.enumerable});return t},B=(t,e,r)=>(Zl(t,e,"default"),r&&Zl(r,e,"default")),Gf=(t,e,r)=>(r=t!=null?Tx(Ex(t)):{},Zl(e||!t||!t.__esModule?Pa(r,"default",{value:t,enumerable:!0}):r,t));var Zc=Ma(Jc=>{"use strict";Object.defineProperty(Jc,"__esModule",{value:!0});var Yc;function Xc(){if(Yc===void 0)throw new Error("No runtime abstraction layer installed");return Yc}s(Xc,"RAL");(function(t){function e(r){if(r===void 0)throw new Error("No runtime abstraction layer provided");Yc=r}s(e,"install"),t.install=e})(Xc||(Xc={}));Jc.default=Xc});var Ny=Ma(Be=>{"use strict";Object.defineProperty(Be,"__esModule",{value:!0});Be.stringArray=Be.array=Be.func=Be.error=Be.number=Be.string=Be.boolean=void 0;function GN(t){return t===!0||t===!1}s(GN,"boolean");Be.boolean=GN;function ky(t){return typeof t=="string"||t instanceof String}s(ky,"string");Be.string=ky;function UN(t){return typeof t=="number"||t instanceof Number}s(UN,"number");Be.number=UN;function BN(t){return t instanceof Error}s(BN,"error");Be.error=BN;function jN(t){return typeof t=="function"}s(jN,"func");Be.func=jN;function Cy(t){return Array.isArray(t)}s(Cy,"array");Be.array=Cy;function WN(t){return Cy(t)&&t.every(e=>ky(e))}s(WN,"stringArray");Be.stringArray=WN});var ef=Ma(Xi=>{"use strict";Object.defineProperty(Xi,"__esModule",{value:!0});Xi.Emitter=Xi.Event=void 0;var KN=Zc(),$y;(function(t){let e={dispose(){}};t.None=function(){return e}})($y||(Xi.Event=$y={}));var Qc=class{static{s(this,"CallbackList")}add(e,r=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(r),Array.isArray(n)&&n.push({dispose:s(()=>this.remove(e,r),"dispose")})}remove(e,r=null){if(!this._callbacks)return;let n=!1;for(let i=0,a=this._callbacks.length;i<a;i++)if(this._callbacks[i]===e)if(this._contexts[i]===r){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else n=!0;if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let r=[],n=this._callbacks.slice(0),i=this._contexts.slice(0);for(let a=0,o=n.length;a<o;a++)try{r.push(n[a].apply(i[a],e))}catch(l){(0,KN.default)().console.error(l)}return r}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},Al=class t{static{s(this,"Emitter")}constructor(e){this._options=e}get event(){return this._event||(this._event=(e,r,n)=>{this._callbacks||(this._callbacks=new Qc),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,r);let i={dispose:s(()=>{this._callbacks&&(this._callbacks.remove(e,r),i.dispose=t._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))},"dispose")};return Array.isArray(n)&&n.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}};Xi.Emitter=Al;Al._noop=function(){}});var wy=Ma(Ji=>{"use strict";Object.defineProperty(Ji,"__esModule",{value:!0});Ji.CancellationTokenSource=Ji.CancellationToken=void 0;var HN=Zc(),VN=Ny(),tf=ef(),El;(function(t){t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:tf.Event.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:tf.Event.None});function e(r){let n=r;return n&&(n===t.None||n===t.Cancelled||VN.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}s(e,"is"),t.is=e})(El||(Ji.CancellationToken=El={}));var zN=Object.freeze(function(t,e){let r=(0,HN.default)().timer.setTimeout(t.bind(e),0);return{dispose(){r.dispose()}}}),vl=class{static{s(this,"MutableToken")}constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?zN:(this._emitter||(this._emitter=new tf.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},rf=class{static{s(this,"CancellationTokenSource")}get token(){return this._token||(this._token=new vl),this._token}cancel(){this._token?this._token.cancel():this._token=El.Cancelled}dispose(){this._token?this._token instanceof vl&&this._token.dispose():this._token=El.None}};Ji.CancellationTokenSource=rf});var Re={};br(Re,{AbstractAstReflection:()=>Or,AbstractCstNode:()=>Js,AbstractLangiumParser:()=>Zs,AbstractParserErrorMessageProvider:()=>Tl,AbstractThreadedAsyncParser:()=>hf,AstUtils:()=>qa,BiMap:()=>nn,Cancellation:()=>w,CompositeCstNodeImpl:()=>en,ContextCache:()=>sn,CstNodeBuilder:()=>Xs,CstUtils:()=>Ga,DEFAULT_TOKENIZE_OPTIONS:()=>$l,DONE_RESULT:()=>Me,DatatypeSymbol:()=>xl,DefaultAstNodeDescriptionProvider:()=>ha,DefaultAstNodeLocator:()=>ya,DefaultAsyncParser:()=>Ca,DefaultCommentProvider:()=>ka,DefaultConfigurationProvider:()=>xa,DefaultDocumentBuilder:()=>Ta,DefaultDocumentValidator:()=>ma,DefaultHydrator:()=>$a,DefaultIndexManager:()=>Ra,DefaultJsonSerializer:()=>fa,DefaultLangiumDocumentFactory:()=>ra,DefaultLangiumDocuments:()=>na,DefaultLexer:()=>on,DefaultLexerErrorMessageProvider:()=>Ea,DefaultLinker:()=>ia,DefaultNameProvider:()=>sa,DefaultReferenceDescriptionProvider:()=>ga,DefaultReferences:()=>aa,DefaultScopeComputation:()=>oa,DefaultScopeProvider:()=>ca,DefaultServiceRegistry:()=>da,DefaultTokenBuilder:()=>ar,DefaultValueConverter:()=>rn,DefaultWorkspaceLock:()=>Na,DefaultWorkspaceManager:()=>Aa,Deferred:()=>Ye,Disposable:()=>$r,DisposableCache:()=>ts,DocumentCache:()=>Cl,DocumentState:()=>J,DocumentValidator:()=>pt,EMPTY_SCOPE:()=>YN,EMPTY_STREAM:()=>ss,EmptyFileSystem:()=>Ef,EmptyFileSystemProvider:()=>Ml,ErrorWithLocation:()=>Dr,GrammarAST:()=>ms,GrammarUtils:()=>Qa,IndentationAwareLexer:()=>Af,IndentationAwareTokenBuilder:()=>Pl,JSDocDocumentationProvider:()=>Sa,LangiumCompletionParser:()=>ea,LangiumParser:()=>Qs,LangiumParserErrorMessageProvider:()=>Yi,LeafCstNodeImpl:()=>Qr,LexingMode:()=>ln,MapScope:()=>la,Module:()=>Tf,MultiMap:()=>Rt,OperationCancelled:()=>Ut,ParserWorker:()=>gf,Reduction:()=>fn,RegExpUtils:()=>Ja,RootCstNodeImpl:()=>qi,SimpleCache:()=>ua,StreamImpl:()=>it,StreamScope:()=>es,TextDocument:()=>Zi,TreeStreamImpl:()=>Et,URI:()=>Xe,UriUtils:()=>Je,ValidationCategory:()=>ns,ValidationRegistry:()=>pa,ValueConverter:()=>Gt,WorkspaceCache:()=>rs,assertUnreachable:()=>It,createCompletionParser:()=>zc,createDefaultCoreModule:()=>yf,createDefaultSharedCoreModule:()=>xf,createGrammarConfig:()=>Wu,createLangiumParser:()=>qc,createParser:()=>ta,delayNextTick:()=>nf,diagnosticData:()=>an,eagerLoad:()=>Xy,getDiagnosticRange:()=>Gy,indentationBuilderDefaultOptions:()=>Rf,inject:()=>Ll,interruptAndCheck:()=>Te,isAstNode:()=>fe,isAstNodeDescription:()=>Ql,isAstNodeWithComment:()=>of,isCompositeCstNode:()=>ht,isIMultiModeLexerDefinition:()=>uf,isJSDoc:()=>pf,isLeafCstNode:()=>ur,isLinkingError:()=>Lr,isNamed:()=>Dy,isOperationCancelled:()=>Bt,isReference:()=>_e,isRootCstNode:()=>is,isTokenTypeArray:()=>wl,isTokenTypeDictionary:()=>lf,loadGrammarFromJson:()=>jt,parseJSDoc:()=>df,prepareLangiumParser:()=>Sy,setInterruptionPeriod:()=>by,startCancelableOperation:()=>Sl,stream:()=>H,toDiagnosticData:()=>Uy,toDiagnosticSeverity:()=>Nl});var Ga={};br(Ga,{DefaultNameRegexp:()=>Fa,RangeComparison:()=>vt,compareRange:()=>Bf,findCommentNode:()=>nu,findDeclarationNodeAtOffset:()=>kx,findLeafNodeAtOffset:()=>iu,findLeafNodeBeforeOffset:()=>jf,flattenCst:()=>Sx,getInteriorNodes:()=>$x,getNextNode:()=>Cx,getPreviousNode:()=>Kf,getStartlineNode:()=>Nx,inRange:()=>ru,isChildNode:()=>tu,isCommentNode:()=>eu,streamCst:()=>Pr,toDocumentSegment:()=>Mr,tokenToRange:()=>dn});function fe(t){return typeof t=="object"&&t!==null&&typeof t.$type=="string"}s(fe,"isAstNode");function _e(t){return typeof t=="object"&&t!==null&&typeof t.$refText=="string"}s(_e,"isReference");function Ql(t){return typeof t=="object"&&t!==null&&typeof t.name=="string"&&typeof t.type=="string"&&typeof t.path=="string"}s(Ql,"isAstNodeDescription");function Lr(t){return typeof t=="object"&&t!==null&&fe(t.container)&&_e(t.reference)&&typeof t.message=="string"}s(Lr,"isLinkingError");var Or=class{static{s(this,"AbstractAstReflection")}constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,r){return fe(e)&&this.isSubtype(e.$type,r)}isSubtype(e,r){if(e===r)return!0;let n=this.subtypes[e];n||(n=this.subtypes[e]={});let i=n[r];if(i!==void 0)return i;{let a=this.computeIsSubtype(e,r);return n[r]=a,a}}getAllSubTypes(e){let r=this.allSubtypes[e];if(r)return r;{let n=this.getAllTypes(),i=[];for(let a of n)this.isSubtype(a,e)&&i.push(a);return this.allSubtypes[e]=i,i}}};function ht(t){return typeof t=="object"&&t!==null&&Array.isArray(t.content)}s(ht,"isCompositeCstNode");function ur(t){return typeof t=="object"&&t!==null&&typeof t.tokenType=="object"}s(ur,"isLeafCstNode");function is(t){return ht(t)&&typeof t.fullText=="string"}s(is,"isRootCstNode");var it=class t{static{s(this,"StreamImpl")}constructor(e,r){this.startFn=e,this.nextFn=r}iterator(){let e={state:this.startFn(),next:s(()=>this.nextFn(e.state),"next"),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){let e=this.iterator(),r=0,n=e.next();for(;!n.done;)r++,n=e.next();return r}toArray(){let e=[],r=this.iterator(),n;do n=r.next(),n.value!==void 0&&e.push(n.value);while(!n.done);return e}toSet(){return new Set(this)}toMap(e,r){let n=this.map(i=>[e?e(i):i,r?r(i):i]);return new Map(n)}toString(){return this.join()}concat(e){return new t(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),r=>{let n;if(!r.firstDone){do if(n=this.nextFn(r.first),!n.done)return n;while(!n.done);r.firstDone=!0}do if(n=r.iterator.next(),!n.done)return n;while(!n.done);return Me})}join(e=","){let r=this.iterator(),n="",i,a=!1;do i=r.next(),i.done||(a&&(n+=e),n+=Ix(i.value)),a=!0;while(!i.done);return n}indexOf(e,r=0){let n=this.iterator(),i=0,a=n.next();for(;!a.done;){if(i>=r&&a.value===e)return i;a=n.next(),i++}return-1}every(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(!e(n.value))return!1;n=r.next()}return!0}some(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(e(n.value))return!0;n=r.next()}return!1}forEach(e){let r=this.iterator(),n=0,i=r.next();for(;!i.done;)e(i.value,n),i=r.next(),n++}map(e){return new t(this.startFn,r=>{let{done:n,value:i}=this.nextFn(r);return n?Me:{done:!1,value:e(i)}})}filter(e){return new t(this.startFn,r=>{let n;do if(n=this.nextFn(r),!n.done&&e(n.value))return n;while(!n.done);return Me})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,r){let n=this.iterator(),i=r,a=n.next();for(;!a.done;)i===void 0?i=a.value:i=e(i,a.value),a=n.next();return i}reduceRight(e,r){return this.recursiveReduce(this.iterator(),e,r)}recursiveReduce(e,r,n){let i=e.next();if(i.done)return n;let a=this.recursiveReduce(e,r,n);return a===void 0?i.value:r(a,i.value)}find(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(e(n.value))return n.value;n=r.next()}}findIndex(e){let r=this.iterator(),n=0,i=r.next();for(;!i.done;){if(e(i.value))return n;i=r.next(),n++}return-1}includes(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(n.value===e)return!0;n=r.next()}return!1}flatMap(e){return new t(()=>({this:this.startFn()}),r=>{do{if(r.iterator){let a=r.iterator.next();if(a.done)r.iterator=void 0;else return a}let{done:n,value:i}=this.nextFn(r.this);if(!n){let a=e(i);if(Da(a))r.iterator=a[Symbol.iterator]();else return{done:!1,value:a}}}while(r.iterator);return Me})}flat(e){if(e===void 0&&(e=1),e<=0)return this;let r=e>1?this.flat(e-1):this;return new t(()=>({this:r.startFn()}),n=>{do{if(n.iterator){let o=n.iterator.next();if(o.done)n.iterator=void 0;else return o}let{done:i,value:a}=r.nextFn(n.this);if(!i)if(Da(a))n.iterator=a[Symbol.iterator]();else return{done:!1,value:a}}while(n.iterator);return Me})}head(){let r=this.iterator().next();if(!r.done)return r.value}tail(e=1){return new t(()=>{let r=this.startFn();for(let n=0;n<e;n++)if(this.nextFn(r).done)return r;return r},this.nextFn)}limit(e){return new t(()=>({size:0,state:this.startFn()}),r=>(r.size++,r.size>e?Me:this.nextFn(r.state)))}distinct(e){return new t(()=>({set:new Set,internalState:this.startFn()}),r=>{let n;do if(n=this.nextFn(r.internalState),!n.done){let i=e?e(n.value):n.value;if(!r.set.has(i))return r.set.add(i),n}while(!n.done);return Me})}exclude(e,r){let n=new Set;for(let i of e){let a=r?r(i):i;n.add(a)}return this.filter(i=>{let a=r?r(i):i;return!n.has(a)})}};function Ix(t){return typeof t=="string"?t:typeof t>"u"?"undefined":typeof t.toString=="function"?t.toString():Object.prototype.toString.call(t)}s(Ix,"toString");function Da(t){return!!t&&typeof t[Symbol.iterator]=="function"}s(Da,"isIterable");var ss=new it(()=>{},()=>Me),Me=Object.freeze({done:!0,value:void 0});function H(...t){if(t.length===1){let e=t[0];if(e instanceof it)return e;if(Da(e))return new it(()=>e[Symbol.iterator](),r=>r.next());if(typeof e.length=="number")return new it(()=>({index:0}),r=>r.index<e.length?{done:!1,value:e[r.index++]}:Me)}return t.length>1?new it(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){let r=e.iterator.next();if(!r.done)return r;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<t.length){let r=t[e.collIndex++];Da(r)?e.iterator=r[Symbol.iterator]():r&&typeof r.length=="number"&&(e.array=r)}}while(e.iterator||e.array||e.collIndex<t.length);return Me}):ss}s(H,"stream");var Et=class extends it{static{s(this,"TreeStreamImpl")}constructor(e,r,n){super(()=>({iterators:n?.includeRoot?[[e][Symbol.iterator]()]:[r(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){let o=i.iterators[i.iterators.length-1].next();if(o.done)i.iterators.pop();else return i.iterators.push(r(o.value)[Symbol.iterator]()),o}return Me})}iterator(){let e={state:this.startFn(),next:s(()=>this.nextFn(e.state),"next"),prune:s(()=>{e.state.pruned=!0},"prune"),[Symbol.iterator]:()=>e};return e}},fn;(function(t){function e(a){return a.reduce((o,l)=>o+l,0)}s(e,"sum"),t.sum=e;function r(a){return a.reduce((o,l)=>o*l,0)}s(r,"product"),t.product=r;function n(a){return a.reduce((o,l)=>Math.min(o,l))}s(n,"min"),t.min=n;function i(a){return a.reduce((o,l)=>Math.max(o,l))}s(i,"max"),t.max=i})(fn||(fn={}));function Pr(t){return new Et(t,e=>ht(e)?e.content:[],{includeRoot:!0})}s(Pr,"streamCst");function Sx(t){return Pr(t).filter(ur)}s(Sx,"flattenCst");function tu(t,e){for(;t.container;)if(t=t.container,t===e)return!0;return!1}s(tu,"isChildNode");function dn(t){return{start:{character:t.startColumn-1,line:t.startLine-1},end:{character:t.endColumn,line:t.endLine-1}}}s(dn,"tokenToRange");function Mr(t){if(!t)return;let{offset:e,end:r,range:n}=t;return{range:n,offset:e,end:r,length:r-e}}s(Mr,"toDocumentSegment");var vt;(function(t){t[t.Before=0]="Before",t[t.After=1]="After",t[t.OverlapFront=2]="OverlapFront",t[t.OverlapBack=3]="OverlapBack",t[t.Inside=4]="Inside",t[t.Outside=5]="Outside"})(vt||(vt={}));function Bf(t,e){if(t.end.line<e.start.line||t.end.line===e.start.line&&t.end.character<=e.start.character)return vt.Before;if(t.start.line>e.end.line||t.start.line===e.end.line&&t.start.character>=e.end.character)return vt.After;let r=t.start.line>e.start.line||t.start.line===e.start.line&&t.start.character>=e.start.character,n=t.end.line<e.end.line||t.end.line===e.end.line&&t.end.character<=e.end.character;return r&&n?vt.Inside:r?vt.OverlapBack:n?vt.OverlapFront:vt.Outside}s(Bf,"compareRange");function ru(t,e){return Bf(t,e)>vt.After}s(ru,"inRange");var Fa=/^[\w\p{L}]$/u;function kx(t,e,r=Fa){if(t){if(e>0){let n=e-t.offset,i=t.text.charAt(n);r.test(i)||e--}return iu(t,e)}}s(kx,"findDeclarationNodeAtOffset");function nu(t,e){if(t){let r=Kf(t,!0);if(r&&eu(r,e))return r;if(is(t)){let n=t.content.findIndex(i=>!i.hidden);for(let i=n-1;i>=0;i--){let a=t.content[i];if(eu(a,e))return a}}}}s(nu,"findCommentNode");function eu(t,e){return ur(t)&&e.includes(t.tokenType.name)}s(eu,"isCommentNode");function iu(t,e){if(ur(t))return t;if(ht(t)){let r=Wf(t,e,!1);if(r)return iu(r,e)}}s(iu,"findLeafNodeAtOffset");function jf(t,e){if(ur(t))return t;if(ht(t)){let r=Wf(t,e,!0);if(r)return jf(r,e)}}s(jf,"findLeafNodeBeforeOffset");function Wf(t,e,r){let n=0,i=t.content.length-1,a;for(;n<=i;){let o=Math.floor((n+i)/2),l=t.content[o];if(l.offset<=e&&l.end>e)return l;l.end<=e?(a=r?l:void 0,n=o+1):i=o-1}return a}s(Wf,"binarySearch");function Kf(t,e=!0){for(;t.container;){let r=t.container,n=r.content.indexOf(t);for(;n>0;){n--;let i=r.content[n];if(e||!i.hidden)return i}t=r}}s(Kf,"getPreviousNode");function Cx(t,e=!0){for(;t.container;){let r=t.container,n=r.content.indexOf(t),i=r.content.length-1;for(;n<i;){n++;let a=r.content[n];if(e||!a.hidden)return a}t=r}}s(Cx,"getNextNode");function Nx(t){if(t.range.start.character===0)return t;let e=t.range.start.line,r=t,n;for(;t.container;){let i=t.container,a=n??i.content.indexOf(t);if(a===0?(t=i,n=void 0):(n=a-1,t=i.content[n]),t.range.start.line!==e)break;r=t}return r}s(Nx,"getStartlineNode");function $x(t,e){let r=wx(t,e);return r?r.parent.content.slice(r.a+1,r.b):[]}s($x,"getInteriorNodes");function wx(t,e){let r=Uf(t),n=Uf(e),i;for(let a=0;a<r.length&&a<n.length;a++){let o=r[a],l=n[a];if(o.parent===l.parent)i={parent:o.parent,a:o.index,b:l.index};else break}return i}s(wx,"getCommonParent");function Uf(t){let e=[];for(;t.container;){let r=t.container,n=r.content.indexOf(t);e.push({parent:r,index:n}),t=r}return e.reverse()}s(Uf,"getParentChain");var Qa={};br(Qa,{findAssignment:()=>Uu,findNameAssignment:()=>Za,findNodeForKeyword:()=>Fu,findNodeForProperty:()=>As,findNodesForKeyword:()=>Jx,findNodesForKeywordInternal:()=>Gu,findNodesForProperty:()=>Mu,getActionAtElement:()=>ed,getActionType:()=>rd,getAllReachableRules:()=>Rs,getCrossReferenceTerminal:()=>Lu,getEntryRule:()=>Xf,getExplicitRuleType:()=>ti,getHiddenRules:()=>Jf,getRuleType:()=>Bu,getRuleTypeName:()=>rT,getTypeName:()=>vs,isArrayCardinality:()=>Qx,isArrayOperator:()=>eT,isCommentTerminal:()=>Pu,isDataType:()=>tT,isDataTypeRule:()=>Es,isOptionalCardinality:()=>Zx,terminalRegex:()=>ri});var Dr=class extends Error{static{s(this,"ErrorWithLocation")}constructor(e,r){super(e?`${r} at ${e.range.start.line}:${e.range.start.character}`:r)}};function It(t){throw new Error("Error! The input value was not handled.")}s(It,"assertUnreachable");var ms={};br(ms,{AbstractElement:()=>hn,AbstractRule:()=>pn,AbstractType:()=>mn,Action:()=>Ln,Alternatives:()=>Pn,ArrayLiteral:()=>gn,ArrayType:()=>yn,Assignment:()=>Mn,BooleanLiteral:()=>xn,CharacterRange:()=>Dn,Condition:()=>as,Conjunction:()=>Tn,CrossReference:()=>Fn,Disjunction:()=>Rn,EndOfFile:()=>Gn,Grammar:()=>An,GrammarImport:()=>ls,Group:()=>Un,InferredType:()=>En,Interface:()=>vn,Keyword:()=>Bn,LangiumGrammarAstReflection:()=>Jn,LangiumGrammarTerminals:()=>_x,NamedArgument:()=>us,NegatedToken:()=>jn,Negation:()=>In,NumberLiteral:()=>Sn,Parameter:()=>kn,ParameterReference:()=>Cn,ParserRule:()=>Nn,ReferenceType:()=>$n,RegexToken:()=>Wn,ReturnType:()=>cs,RuleCall:()=>Kn,SimpleType:()=>wn,StringLiteral:()=>_n,TerminalAlternatives:()=>Hn,TerminalGroup:()=>Vn,TerminalRule:()=>Fr,TerminalRuleCall:()=>zn,Type:()=>bn,TypeAttribute:()=>fs,TypeDefinition:()=>Ua,UnionType:()=>On,UnorderedGroup:()=>qn,UntilToken:()=>Yn,ValueLiteral:()=>os,Wildcard:()=>Xn,isAbstractElement:()=>ds,isAbstractRule:()=>bx,isAbstractType:()=>Ox,isAction:()=>Vt,isAlternatives:()=>Ka,isArrayLiteral:()=>Fx,isArrayType:()=>su,isAssignment:()=>gt,isBooleanLiteral:()=>au,isCharacterRange:()=>mu,isCondition:()=>Lx,isConjunction:()=>ou,isCrossReference:()=>Gr,isDisjunction:()=>lu,isEndOfFile:()=>hu,isFeatureName:()=>Px,isGrammar:()=>Gx,isGrammarImport:()=>Ux,isGroup:()=>cr,isInferredType:()=>Ba,isInterface:()=>ja,isKeyword:()=>ut,isNamedArgument:()=>Bx,isNegatedToken:()=>gu,isNegation:()=>uu,isNumberLiteral:()=>jx,isParameter:()=>Wx,isParameterReference:()=>cu,isParserRule:()=>De,isPrimitiveType:()=>Hf,isReferenceType:()=>fu,isRegexToken:()=>yu,isReturnType:()=>du,isRuleCall:()=>yt,isSimpleType:()=>Wa,isStringLiteral:()=>Kx,isTerminalAlternatives:()=>xu,isTerminalGroup:()=>Tu,isTerminalRule:()=>st,isTerminalRuleCall:()=>Ha,isType:()=>ps,isTypeAttribute:()=>Hx,isTypeDefinition:()=>Mx,isUnionType:()=>pu,isUnorderedGroup:()=>Va,isUntilToken:()=>Ru,isValueLiteral:()=>Dx,isWildcard:()=>Au,reflection:()=>b});var _x={ID:/\^?[_a-zA-Z][\w_]*/,STRING:/"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,NUMBER:/NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,RegexLiteral:/\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,WS:/\s+/,ML_COMMENT:/\/\*[\s\S]*?\*\//,SL_COMMENT:/\/\/[^\n\r]*/},pn="AbstractRule";function bx(t){return b.isInstance(t,pn)}s(bx,"isAbstractRule");var mn="AbstractType";function Ox(t){return b.isInstance(t,mn)}s(Ox,"isAbstractType");var as="Condition";function Lx(t){return b.isInstance(t,as)}s(Lx,"isCondition");function Px(t){return Hf(t)||t==="current"||t==="entry"||t==="extends"||t==="false"||t==="fragment"||t==="grammar"||t==="hidden"||t==="import"||t==="interface"||t==="returns"||t==="terminal"||t==="true"||t==="type"||t==="infer"||t==="infers"||t==="with"||typeof t=="string"&&/\^?[_a-zA-Z][\w_]*/.test(t)}s(Px,"isFeatureName");function Hf(t){return t==="string"||t==="number"||t==="boolean"||t==="Date"||t==="bigint"}s(Hf,"isPrimitiveType");var Ua="TypeDefinition";function Mx(t){return b.isInstance(t,Ua)}s(Mx,"isTypeDefinition");var os="ValueLiteral";function Dx(t){return b.isInstance(t,os)}s(Dx,"isValueLiteral");var hn="AbstractElement";function ds(t){return b.isInstance(t,hn)}s(ds,"isAbstractElement");var gn="ArrayLiteral";function Fx(t){return b.isInstance(t,gn)}s(Fx,"isArrayLiteral");var yn="ArrayType";function su(t){return b.isInstance(t,yn)}s(su,"isArrayType");var xn="BooleanLiteral";function au(t){return b.isInstance(t,xn)}s(au,"isBooleanLiteral");var Tn="Conjunction";function ou(t){return b.isInstance(t,Tn)}s(ou,"isConjunction");var Rn="Disjunction";function lu(t){return b.isInstance(t,Rn)}s(lu,"isDisjunction");var An="Grammar";function Gx(t){return b.isInstance(t,An)}s(Gx,"isGrammar");var ls="GrammarImport";function Ux(t){return b.isInstance(t,ls)}s(Ux,"isGrammarImport");var En="InferredType";function Ba(t){return b.isInstance(t,En)}s(Ba,"isInferredType");var vn="Interface";function ja(t){return b.isInstance(t,vn)}s(ja,"isInterface");var us="NamedArgument";function Bx(t){return b.isInstance(t,us)}s(Bx,"isNamedArgument");var In="Negation";function uu(t){return b.isInstance(t,In)}s(uu,"isNegation");var Sn="NumberLiteral";function jx(t){return b.isInstance(t,Sn)}s(jx,"isNumberLiteral");var kn="Parameter";function Wx(t){return b.isInstance(t,kn)}s(Wx,"isParameter");var Cn="ParameterReference";function cu(t){return b.isInstance(t,Cn)}s(cu,"isParameterReference");var Nn="ParserRule";function De(t){return b.isInstance(t,Nn)}s(De,"isParserRule");var $n="ReferenceType";function fu(t){return b.isInstance(t,$n)}s(fu,"isReferenceType");var cs="ReturnType";function du(t){return b.isInstance(t,cs)}s(du,"isReturnType");var wn="SimpleType";function Wa(t){return b.isInstance(t,wn)}s(Wa,"isSimpleType");var _n="StringLiteral";function Kx(t){return b.isInstance(t,_n)}s(Kx,"isStringLiteral");var Fr="TerminalRule";function st(t){return b.isInstance(t,Fr)}s(st,"isTerminalRule");var bn="Type";function ps(t){return b.isInstance(t,bn)}s(ps,"isType");var fs="TypeAttribute";function Hx(t){return b.isInstance(t,fs)}s(Hx,"isTypeAttribute");var On="UnionType";function pu(t){return b.isInstance(t,On)}s(pu,"isUnionType");var Ln="Action";function Vt(t){return b.isInstance(t,Ln)}s(Vt,"isAction");var Pn="Alternatives";function Ka(t){return b.isInstance(t,Pn)}s(Ka,"isAlternatives");var Mn="Assignment";function gt(t){return b.isInstance(t,Mn)}s(gt,"isAssignment");var Dn="CharacterRange";function mu(t){return b.isInstance(t,Dn)}s(mu,"isCharacterRange");var Fn="CrossReference";function Gr(t){return b.isInstance(t,Fn)}s(Gr,"isCrossReference");var Gn="EndOfFile";function hu(t){return b.isInstance(t,Gn)}s(hu,"isEndOfFile");var Un="Group";function cr(t){return b.isInstance(t,Un)}s(cr,"isGroup");var Bn="Keyword";function ut(t){return b.isInstance(t,Bn)}s(ut,"isKeyword");var jn="NegatedToken";function gu(t){return b.isInstance(t,jn)}s(gu,"isNegatedToken");var Wn="RegexToken";function yu(t){return b.isInstance(t,Wn)}s(yu,"isRegexToken");var Kn="RuleCall";function yt(t){return b.isInstance(t,Kn)}s(yt,"isRuleCall");var Hn="TerminalAlternatives";function xu(t){return b.isInstance(t,Hn)}s(xu,"isTerminalAlternatives");var Vn="TerminalGroup";function Tu(t){return b.isInstance(t,Vn)}s(Tu,"isTerminalGroup");var zn="TerminalRuleCall";function Ha(t){return b.isInstance(t,zn)}s(Ha,"isTerminalRuleCall");var qn="UnorderedGroup";function Va(t){return b.isInstance(t,qn)}s(Va,"isUnorderedGroup");var Yn="UntilToken";function Ru(t){return b.isInstance(t,Yn)}s(Ru,"isUntilToken");var Xn="Wildcard";function Au(t){return b.isInstance(t,Xn)}s(Au,"isWildcard");var Jn=class extends Or{static{s(this,"LangiumGrammarAstReflection")}getAllTypes(){return[hn,pn,mn,Ln,Pn,gn,yn,Mn,xn,Dn,as,Tn,Fn,Rn,Gn,An,ls,Un,En,vn,Bn,us,jn,In,Sn,kn,Cn,Nn,$n,Wn,cs,Kn,wn,_n,Hn,Vn,Fr,zn,bn,fs,Ua,On,qn,Yn,os,Xn]}computeIsSubtype(e,r){switch(e){case Ln:case Pn:case Mn:case Dn:case Fn:case Gn:case Un:case Bn:case jn:case Wn:case Kn:case Hn:case Vn:case zn:case qn:case Yn:case Xn:return this.isSubtype(hn,r);case gn:case Sn:case _n:return this.isSubtype(os,r);case yn:case $n:case wn:case On:return this.isSubtype(Ua,r);case xn:return this.isSubtype(as,r)||this.isSubtype(os,r);case Tn:case Rn:case In:case Cn:return this.isSubtype(as,r);case En:case vn:case bn:return this.isSubtype(mn,r);case Nn:return this.isSubtype(pn,r)||this.isSubtype(mn,r);case Fr:return this.isSubtype(pn,r);default:return!1}}getReferenceType(e){let r=`${e.container.$type}:${e.property}`;switch(r){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return mn;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return pn;case"Grammar:usedGrammars":return An;case"NamedArgument:parameter":case"ParameterReference:parameter":return kn;case"TerminalRuleCall:rule":return Fr;default:throw new Error(`${r} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case hn:return{name:hn,properties:[{name:"cardinality"},{name:"lookahead"}]};case gn:return{name:gn,properties:[{name:"elements",defaultValue:[]}]};case yn:return{name:yn,properties:[{name:"elementType"}]};case xn:return{name:xn,properties:[{name:"true",defaultValue:!1}]};case Tn:return{name:Tn,properties:[{name:"left"},{name:"right"}]};case Rn:return{name:Rn,properties:[{name:"left"},{name:"right"}]};case An:return{name:An,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case ls:return{name:ls,properties:[{name:"path"}]};case En:return{name:En,properties:[{name:"name"}]};case vn:return{name:vn,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case us:return{name:us,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case In:return{name:In,properties:[{name:"value"}]};case Sn:return{name:Sn,properties:[{name:"value"}]};case kn:return{name:kn,properties:[{name:"name"}]};case Cn:return{name:Cn,properties:[{name:"parameter"}]};case Nn:return{name:Nn,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case $n:return{name:$n,properties:[{name:"referenceType"}]};case cs:return{name:cs,properties:[{name:"name"}]};case wn:return{name:wn,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case _n:return{name:_n,properties:[{name:"value"}]};case Fr:return{name:Fr,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case bn:return{name:bn,properties:[{name:"name"},{name:"type"}]};case fs:return{name:fs,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case On:return{name:On,properties:[{name:"types",defaultValue:[]}]};case Ln:return{name:Ln,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case Pn:return{name:Pn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Mn:return{name:Mn,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Dn:return{name:Dn,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case Fn:return{name:Fn,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case Gn:return{name:Gn,properties:[{name:"cardinality"},{name:"lookahead"}]};case Un:return{name:Un,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case Bn:return{name:Bn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case jn:return{name:jn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Wn:return{name:Wn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case Kn:return{name:Kn,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Hn:return{name:Hn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Vn:return{name:Vn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case zn:return{name:zn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case qn:return{name:qn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Yn:return{name:Yn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Xn:return{name:Xn,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}},b=new Jn;var qa={};br(qa,{assignMandatoryProperties:()=>Iu,copyAstNode:()=>vu,findLocalReferences:()=>zx,findRootNode:()=>hs,getContainerOfType:()=>Ur,getDocument:()=>Fe,hasContainerOfType:()=>Vx,linkContentToContainer:()=>za,streamAllContents:()=>St,streamAst:()=>ct,streamContents:()=>gs,streamReferences:()=>Zn});function za(t){for(let[e,r]of Object.entries(t))e.startsWith("$")||(Array.isArray(r)?r.forEach((n,i)=>{fe(n)&&(n.$container=t,n.$containerProperty=e,n.$containerIndex=i)}):fe(r)&&(r.$container=t,r.$containerProperty=e))}s(za,"linkContentToContainer");function Ur(t,e){let r=t;for(;r;){if(e(r))return r;r=r.$container}}s(Ur,"getContainerOfType");function Vx(t,e){let r=t;for(;r;){if(e(r))return!0;r=r.$container}return!1}s(Vx,"hasContainerOfType");function Fe(t){let r=hs(t).$document;if(!r)throw new Error("AST node has no document.");return r}s(Fe,"getDocument");function hs(t){for(;t.$container;)t=t.$container;return t}s(hs,"findRootNode");function gs(t,e){if(!t)throw new Error("Node must be an AstNode.");let r=e?.range;return new it(()=>({keys:Object.keys(t),keyIndex:0,arrayIndex:0}),n=>{for(;n.keyIndex<n.keys.length;){let i=n.keys[n.keyIndex];if(!i.startsWith("$")){let a=t[i];if(fe(a)){if(n.keyIndex++,Eu(a,r))return{done:!1,value:a}}else if(Array.isArray(a)){for(;n.arrayIndex<a.length;){let o=n.arrayIndex++,l=a[o];if(fe(l)&&Eu(l,r))return{done:!1,value:l}}n.arrayIndex=0}}n.keyIndex++}return Me})}s(gs,"streamContents");function St(t,e){if(!t)throw new Error("Root node must be an AstNode.");return new Et(t,r=>gs(r,e))}s(St,"streamAllContents");function ct(t,e){if(t){if(e?.range&&!Eu(t,e.range))return new Et(t,()=>[])}else throw new Error("Root node must be an AstNode.");return new Et(t,r=>gs(r,e),{includeRoot:!0})}s(ct,"streamAst");function Eu(t,e){var r;if(!e)return!0;let n=(r=t.$cstNode)===null||r===void 0?void 0:r.range;return n?ru(n,e):!1}s(Eu,"isAstNodeInRange");function Zn(t){return new it(()=>({keys:Object.keys(t),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){let r=e.keys[e.keyIndex];if(!r.startsWith("$")){let n=t[r];if(_e(n))return e.keyIndex++,{done:!1,value:{reference:n,container:t,property:r}};if(Array.isArray(n)){for(;e.arrayIndex<n.length;){let i=e.arrayIndex++,a=n[i];if(_e(a))return{done:!1,value:{reference:a,container:t,property:r,index:i}}}e.arrayIndex=0}}e.keyIndex++}return Me})}s(Zn,"streamReferences");function zx(t,e=Fe(t).parseResult.value){let r=[];return ct(e).forEach(n=>{Zn(n).forEach(i=>{i.reference.ref===t&&r.push(i.reference)})}),H(r)}s(zx,"findLocalReferences");function Iu(t,e){let r=t.getTypeMetaData(e.$type),n=e;for(let i of r.properties)i.defaultValue!==void 0&&n[i.name]===void 0&&(n[i.name]=Vf(i.defaultValue))}s(Iu,"assignMandatoryProperties");function Vf(t){return Array.isArray(t)?[...t.map(Vf)]:t}s(Vf,"copyDefaultValue");function vu(t,e){let r={$type:t.$type};for(let[n,i]of Object.entries(t))if(!n.startsWith("$"))if(fe(i))r[n]=vu(i,e);else if(_e(i))r[n]=e(r,n,i.$refNode,i.$refText);else if(Array.isArray(i)){let a=[];for(let o of i)fe(o)?a.push(vu(o,e)):_e(o)?a.push(e(r,n,o.$refNode,o.$refText)):a.push(o);r[n]=a}else r[n]=i;return za(r),r}s(vu,"copyAstNode");var Ja={};br(Ja,{NEWLINE_REGEXP:()=>Nu,escapeRegExp:()=>Kr,getCaseInsensitivePattern:()=>wu,getTerminalParts:()=>Xx,isMultilineComment:()=>$u,isWhitespace:()=>ei,partialMatches:()=>_u,partialRegExp:()=>Yf,whitespaceCharacters:()=>qf});function _(t){return t.charCodeAt(0)}s(_,"cc");function Ya(t,e){Array.isArray(t)?t.forEach(function(r){e.push(r)}):e.push(t)}s(Ya,"insertToSet");function Qn(t,e){if(t[e]===!0)throw"duplicate flag "+e;let r=t[e];t[e]=!0}s(Qn,"addFlag");function Br(t){if(t===void 0)throw Error("Internal Error - Should never get here!");return!0}s(Br,"ASSERT_EXISTS");function ys(){throw Error("Internal Error - Should never get here!")}s(ys,"ASSERT_NEVER_REACH_HERE");function Su(t){return t.type==="Character"}s(Su,"isCharacter");var xs=[];for(let t=_("0");t<=_("9");t++)xs.push(t);var Ts=[_("_")].concat(xs);for(let t=_("a");t<=_("z");t++)Ts.push(t);for(let t=_("A");t<=_("Z");t++)Ts.push(t);var ku=[_(" "),_("\f"),_(`
`),_("\r"),_("	"),_("\v"),_("	"),_("\xA0"),_("\u1680"),_("\u2000"),_("\u2001"),_("\u2002"),_("\u2003"),_("\u2004"),_("\u2005"),_("\u2006"),_("\u2007"),_("\u2008"),_("\u2009"),_("\u200A"),_("\u2028"),_("\u2029"),_("\u202F"),_("\u205F"),_("\u3000"),_("\uFEFF")];var qx=/[0-9a-fA-F]/,Xa=/[0-9]/,Yx=/[1-9]/,jr=class{static{s(this,"RegExpParser")}constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");let r=this.disjunction();this.consumeChar("/");let n={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":Qn(n,"global");break;case"i":Qn(n,"ignoreCase");break;case"m":Qn(n,"multiLine");break;case"u":Qn(n,"unicode");break;case"y":Qn(n,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:r,loc:this.loc(0)}}disjunction(){let e=[],r=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(r)}}alternative(){let e=[],r=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(r)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){let e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let r;switch(this.popChar()){case"=":r="Lookahead";break;case"!":r="NegativeLookahead";break}Br(r);let n=this.disjunction();return this.consumeChar(")"),{type:r,value:n,loc:this.loc(e)}}return ys()}quantifier(e=!1){let r,n=this.idx;switch(this.popChar()){case"*":r={atLeast:0,atMost:1/0};break;case"+":r={atLeast:1,atMost:1/0};break;case"?":r={atLeast:0,atMost:1};break;case"{":let i=this.integerIncludingZero();switch(this.popChar()){case"}":r={atLeast:i,atMost:i};break;case",":let a;this.isDigit()?(a=this.integerIncludingZero(),r={atLeast:i,atMost:a}):r={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&r===void 0)return;Br(r);break}if(!(e===!0&&r===void 0)&&Br(r))return this.peekChar(0)==="?"?(this.consumeChar("?"),r.greedy=!1):r.greedy=!0,r.type="Quantifier",r.loc=this.loc(n),r}atom(){let e,r=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}return e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),Br(e)?(e.loc=this.loc(r),this.isQuantifier()&&(e.quantifier=this.quantifier()),e):ys()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[_(`
`),_("\r"),_("\u2028"),_("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,r=!1;switch(this.popChar()){case"d":e=xs;break;case"D":e=xs,r=!0;break;case"s":e=ku;break;case"S":e=ku,r=!0;break;case"w":e=Ts;break;case"W":e=Ts,r=!0;break}return Br(e)?{type:"Set",value:e,complement:r}:ys()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=_("\f");break;case"n":e=_(`
`);break;case"r":e=_("\r");break;case"t":e=_("	");break;case"v":e=_("\v");break}return Br(e)?{type:"Character",value:e}:ys()}controlLetterEscapeAtom(){this.consumeChar("c");let e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:_("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){let e=this.popChar();return{type:"Character",value:_(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:let e=this.popChar();return{type:"Character",value:_(e)}}}characterClass(){let e=[],r=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),r=!0);this.isClassAtom();){let n=this.classAtom(),i=n.type==="Character";if(Su(n)&&this.isRangeDash()){this.consumeChar("-");let a=this.classAtom(),o=a.type==="Character";if(Su(a)){if(a.value<n.value)throw Error("Range out of order in character class");e.push({from:n.value,to:a.value})}else Ya(n.value,e),e.push(_("-")),Ya(a.value,e)}else Ya(n.value,e)}return this.consumeChar("]"),{type:"Set",complement:r,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:_("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}let r=this.disjunction();this.consumeChar(")");let n={type:"Group",capturing:e,value:r};return e&&(n.idx=this.groupIdx),n}positiveInteger(){let e=this.popChar();if(Yx.test(e)===!1)throw Error("Expecting a positive integer");for(;Xa.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(Xa.test(e)===!1)throw Error("Expecting an integer");for(;Xa.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){let e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:_(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return Xa.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){let e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let r="";for(let i=0;i<e;i++){let a=this.popChar();if(qx.test(a)===!1)throw Error("Expecting a HexDecimal digits");r+=a}return{type:"Character",value:parseInt(r,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){let e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}};var kt=class{static{s(this,"BaseRegExpVisitor")}visitChildren(e){for(let r in e){let n=e[r];e.hasOwnProperty(r)&&(n.type!==void 0?this.visit(n):Array.isArray(n)&&n.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}};var Nu=/\r?\n/gm,zf=new jr,Cu=class extends kt{static{s(this,"TerminalRegExpVisitor")}constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){let r=String.fromCharCode(e.value);if(!this.multiline&&r===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let n=Kr(r);this.endRegexpStack.push(n),this.isStarting&&(this.startRegexp+=n)}}visitSet(e){if(!this.multiline){let r=this.regex.substring(e.loc.begin,e.loc.end),n=new RegExp(r);this.multiline=!!`
`.match(n)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let r=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(r),this.isStarting&&(this.startRegexp+=r)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}},Wr=new Cu;function Xx(t){try{typeof t!="string"&&(t=t.source),t=`/${t}/`;let e=zf.pattern(t),r=[];for(let n of e.value.value)Wr.reset(t),Wr.visit(n),r.push({start:Wr.startRegexp,end:Wr.endRegex});return r}catch{return[]}}s(Xx,"getTerminalParts");function $u(t){try{return typeof t=="string"&&(t=new RegExp(t)),t=t.toString(),Wr.reset(t),Wr.visit(zf.pattern(t)),Wr.multiline}catch{return!1}}s($u,"isMultilineComment");var qf=`\f
\r	\v \xA0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF`.split("");function ei(t){let e=typeof t=="string"?new RegExp(t):t;return qf.some(r=>e.test(r))}s(ei,"isWhitespace");function Kr(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}s(Kr,"escapeRegExp");function wu(t){return Array.prototype.map.call(t,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:Kr(e)).join("")}s(wu,"getCaseInsensitivePattern");function _u(t,e){let r=Yf(t),n=e.match(r);return!!n&&n[0].length>0}s(_u,"partialMatches");function Yf(t){typeof t=="string"&&(t=new RegExp(t));let e=t,r=t.source,n=0;function i(){let a="",o;function l(c){a+=r.substr(n,c),n+=c}s(l,"appendRaw");function u(c){a+="(?:"+r.substr(n,c)+"|$)",n+=c}for(s(u,"appendOptional");n<r.length;)switch(r[n]){case"\\":switch(r[n+1]){case"c":u(3);break;case"x":u(4);break;case"u":e.unicode?r[n+2]==="{"?u(r.indexOf("}",n)-n+1):u(6):u(2);break;case"p":case"P":e.unicode?u(r.indexOf("}",n)-n+1):u(2);break;case"k":u(r.indexOf(">",n)-n+1);break;default:u(2);break}break;case"[":o=/\[(?:\\.|.)*?\]/g,o.lastIndex=n,o=o.exec(r)||[],u(o[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":l(1);break;case"{":o=/\{\d+,?\d*\}/g,o.lastIndex=n,o=o.exec(r),o?l(o[0].length):u(1);break;case"(":if(r[n+1]==="?")switch(r[n+2]){case":":a+="(?:",n+=3,a+=i()+"|$)";break;case"=":a+="(?=",n+=3,a+=i()+")";break;case"!":o=n,n+=3,i(),a+=r.substr(o,n-o);break;case"<":switch(r[n+3]){case"=":case"!":o=n,n+=4,i(),a+=r.substr(o,n-o);break;default:l(r.indexOf(">",n)-n+1),a+=i()+"|$)";break}break}else l(1),a+=i()+"|$)";break;case")":return++n,a;default:u(1);break}return a}return s(i,"process"),new RegExp(i(),t.flags)}s(Yf,"partialRegExp");function Xf(t){return t.rules.find(e=>De(e)&&e.entry)}s(Xf,"getEntryRule");function Jf(t){return t.rules.filter(e=>st(e)&&e.hidden)}s(Jf,"getHiddenRules");function Rs(t,e){let r=new Set,n=Xf(t);if(!n)return new Set(t.rules);let i=[n].concat(Jf(t));for(let o of i)Zf(o,r,e);let a=new Set;for(let o of t.rules)(r.has(o.name)||st(o)&&o.hidden)&&a.add(o);return a}s(Rs,"getAllReachableRules");function Zf(t,e,r){e.add(t.name),St(t).forEach(n=>{if(yt(n)||r&&Ha(n)){let i=n.rule.ref;i&&!e.has(i.name)&&Zf(i,e,r)}})}s(Zf,"ruleDfs");function Lu(t){if(t.terminal)return t.terminal;if(t.type.ref){let e=Za(t.type.ref);return e?.terminal}}s(Lu,"getCrossReferenceTerminal");function Pu(t){return t.hidden&&!ei(ri(t))}s(Pu,"isCommentTerminal");function Mu(t,e){return!t||!e?[]:Du(t,e,t.astNode,!0)}s(Mu,"findNodesForProperty");function As(t,e,r){if(!t||!e)return;let n=Du(t,e,t.astNode,!0);if(n.length!==0)return r!==void 0?r=Math.max(0,Math.min(r,n.length-1)):r=0,n[r]}s(As,"findNodeForProperty");function Du(t,e,r,n){if(!n){let i=Ur(t.grammarSource,gt);if(i&&i.feature===e)return[t]}return ht(t)&&t.astNode===r?t.content.flatMap(i=>Du(i,e,r,!1)):[]}s(Du,"findNodesForPropertyInternal");function Jx(t,e){return t?Gu(t,e,t?.astNode):[]}s(Jx,"findNodesForKeyword");function Fu(t,e,r){if(!t)return;let n=Gu(t,e,t?.astNode);if(n.length!==0)return r!==void 0?r=Math.max(0,Math.min(r,n.length-1)):r=0,n[r]}s(Fu,"findNodeForKeyword");function Gu(t,e,r){if(t.astNode!==r)return[];if(ut(t.grammarSource)&&t.grammarSource.value===e)return[t];let n=Pr(t).iterator(),i,a=[];do if(i=n.next(),!i.done){let o=i.value;o.astNode===r?ut(o.grammarSource)&&o.grammarSource.value===e&&a.push(o):n.prune()}while(!i.done);return a}s(Gu,"findNodesForKeywordInternal");function Uu(t){var e;let r=t.astNode;for(;r===((e=t.container)===null||e===void 0?void 0:e.astNode);){let n=Ur(t.grammarSource,gt);if(n)return n;t=t.container}}s(Uu,"findAssignment");function Za(t){let e=t;return Ba(e)&&(Vt(e.$container)?e=e.$container.$container:De(e.$container)?e=e.$container:It(e.$container)),Qf(t,e,new Map)}s(Za,"findNameAssignment");function Qf(t,e,r){var n;function i(a,o){let l;return Ur(a,gt)||(l=Qf(o,o,r)),r.set(t,l),l}if(s(i,"go"),r.has(t))return r.get(t);r.set(t,void 0);for(let a of St(e)){if(gt(a)&&a.feature.toLowerCase()==="name")return r.set(t,a),a;if(yt(a)&&De(a.rule.ref))return i(a,a.rule.ref);if(Wa(a)&&(!((n=a.typeRef)===null||n===void 0)&&n.ref))return i(a,a.typeRef.ref)}}s(Qf,"findNameAssignmentInternal");function ed(t){let e=t.$container;if(cr(e)){let r=e.elements,n=r.indexOf(t);for(let i=n-1;i>=0;i--){let a=r[i];if(Vt(a))return a;{let o=St(r[i]).find(Vt);if(o)return o}}}if(ds(e))return ed(e)}s(ed,"getActionAtElement");function Zx(t,e){return t==="?"||t==="*"||cr(e)&&!!e.guardCondition}s(Zx,"isOptionalCardinality");function Qx(t){return t==="*"||t==="+"}s(Qx,"isArrayCardinality");function eT(t){return t==="+="}s(eT,"isArrayOperator");function Es(t){return td(t,new Set)}s(Es,"isDataTypeRule");function td(t,e){if(e.has(t))return!0;e.add(t);for(let r of St(t))if(yt(r)){if(!r.rule.ref||De(r.rule.ref)&&!td(r.rule.ref,e))return!1}else{if(gt(r))return!1;if(Vt(r))return!1}return!!t.definition}s(td,"isDataTypeRuleInternal");function tT(t){return Ou(t.type,new Set)}s(tT,"isDataType");function Ou(t,e){if(e.has(t))return!0;if(e.add(t),su(t))return!1;if(fu(t))return!1;if(pu(t))return t.types.every(r=>Ou(r,e));if(Wa(t)){if(t.primitiveType!==void 0)return!0;if(t.stringType!==void 0)return!0;if(t.typeRef!==void 0){let r=t.typeRef.ref;return ps(r)?Ou(r.type,e):!1}else return!1}else return!1}s(Ou,"isDataTypeInternal");function ti(t){if(t.inferredType)return t.inferredType.name;if(t.dataType)return t.dataType;if(t.returnType){let e=t.returnType.ref;if(e){if(De(e))return e.name;if(ja(e)||ps(e))return e.name}}}s(ti,"getExplicitRuleType");function vs(t){var e;if(De(t))return Es(t)?t.name:(e=ti(t))!==null&&e!==void 0?e:t.name;if(ja(t)||ps(t)||du(t))return t.name;if(Vt(t)){let r=rd(t);if(r)return r}else if(Ba(t))return t.name;throw new Error("Cannot get name of Unknown Type")}s(vs,"getTypeName");function rd(t){var e;if(t.inferredType)return t.inferredType.name;if(!((e=t.type)===null||e===void 0)&&e.ref)return vs(t.type.ref)}s(rd,"getActionType");function rT(t){var e,r,n;return st(t)?(r=(e=t.type)===null||e===void 0?void 0:e.name)!==null&&r!==void 0?r:"string":Es(t)?t.name:(n=ti(t))!==null&&n!==void 0?n:t.name}s(rT,"getRuleTypeName");function Bu(t){var e,r,n;return st(t)?(r=(e=t.type)===null||e===void 0?void 0:e.name)!==null&&r!==void 0?r:"string":(n=ti(t))!==null&&n!==void 0?n:t.name}s(Bu,"getRuleType");function ri(t){let e={s:!1,i:!1,u:!1},r=ni(t.definition,e),n=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(r,n)}s(ri,"terminalRegex");var ju=/[\s\S]/.source;function ni(t,e){if(xu(t))return nT(t);if(Tu(t))return iT(t);if(mu(t))return oT(t);if(Ha(t)){let r=t.rule.ref;if(!r)throw new Error("Missing rule reference.");return zt(ni(r.definition),{cardinality:t.cardinality,lookahead:t.lookahead})}else{if(gu(t))return aT(t);if(Ru(t))return sT(t);if(yu(t)){let r=t.regex.lastIndexOf("/"),n=t.regex.substring(1,r),i=t.regex.substring(r+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),zt(n,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}else{if(Au(t))return zt(ju,{cardinality:t.cardinality,lookahead:t.lookahead});throw new Error(`Invalid terminal element: ${t?.$type}`)}}}s(ni,"abstractElementToRegex");function nT(t){return zt(t.elements.map(e=>ni(e)).join("|"),{cardinality:t.cardinality,lookahead:t.lookahead})}s(nT,"terminalAlternativesToRegex");function iT(t){return zt(t.elements.map(e=>ni(e)).join(""),{cardinality:t.cardinality,lookahead:t.lookahead})}s(iT,"terminalGroupToRegex");function sT(t){return zt(`${ju}*?${ni(t.terminal)}`,{cardinality:t.cardinality,lookahead:t.lookahead})}s(sT,"untilTokenToRegex");function aT(t){return zt(`(?!${ni(t.terminal)})${ju}*?`,{cardinality:t.cardinality,lookahead:t.lookahead})}s(aT,"negateTokenToRegex");function oT(t){return t.right?zt(`[${bu(t.left)}-${bu(t.right)}]`,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1}):zt(bu(t.left),{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}s(oT,"characterRangeToRegex");function bu(t){return Kr(t.value)}s(bu,"keywordToRegex");function zt(t,e){var r;return(e.wrap!==!1||e.lookahead)&&(t=`(${(r=e.lookahead)!==null&&r!==void 0?r:""}${t})`),e.cardinality?`${t}${e.cardinality}`:t}s(zt,"withCardinality");function Wu(t){let e=[],r=t.Grammar;for(let n of r.rules)st(n)&&Pu(n)&&$u(ri(n))&&e.push(n.name);return{multilineCommentRules:e,nameRegexp:Fa}}s(Wu,"createGrammarConfig");var lT=typeof global=="object"&&global&&global.Object===Object&&global,eo=lT;var uT=typeof self=="object"&&self&&self.Object===Object&&self,cT=eo||uT||Function("return this")(),Ae=cT;var fT=Ae.Symbol,Ce=fT;var nd=Object.prototype,dT=nd.hasOwnProperty,pT=nd.toString,Is=Ce?Ce.toStringTag:void 0;function mT(t){var e=dT.call(t,Is),r=t[Is];try{t[Is]=void 0;var n=!0}catch{}var i=pT.call(t);return n&&(e?t[Is]=r:delete t[Is]),i}s(mT,"getRawTag");var id=mT;var hT=Object.prototype,gT=hT.toString;function yT(t){return gT.call(t)}s(yT,"objectToString");var sd=yT;var xT="[object Null]",TT="[object Undefined]",ad=Ce?Ce.toStringTag:void 0;function RT(t){return t==null?t===void 0?TT:xT:ad&&ad in Object(t)?id(t):sd(t)}s(RT,"baseGetTag");var Ke=RT;function AT(t){return t!=null&&typeof t=="object"}s(AT,"isObjectLike");var ge=AT;var ET="[object Symbol]";function vT(t){return typeof t=="symbol"||ge(t)&&Ke(t)==ET}s(vT,"isSymbol");var Ct=vT;function IT(t,e){for(var r=-1,n=t==null?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}s(IT,"arrayMap");var Nt=IT;var ST=Array.isArray,N=ST;var kT=1/0,od=Ce?Ce.prototype:void 0,ld=od?od.toString:void 0;function ud(t){if(typeof t=="string")return t;if(N(t))return Nt(t,ud)+"";if(Ct(t))return ld?ld.call(t):"";var e=t+"";return e=="0"&&1/t==-kT?"-0":e}s(ud,"baseToString");var cd=ud;var CT=/\s/;function NT(t){for(var e=t.length;e--&&CT.test(t.charAt(e)););return e}s(NT,"trimmedEndIndex");var fd=NT;var $T=/^\s+/;function wT(t){return t&&t.slice(0,fd(t)+1).replace($T,"")}s(wT,"baseTrim");var dd=wT;function _T(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}s(_T,"isObject");var de=_T;var pd=NaN,bT=/^[-+]0x[0-9a-f]+$/i,OT=/^0b[01]+$/i,LT=/^0o[0-7]+$/i,PT=parseInt;function MT(t){if(typeof t=="number")return t;if(Ct(t))return pd;if(de(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=de(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=dd(t);var r=OT.test(t);return r||LT.test(t)?PT(t.slice(2),r?2:8):bT.test(t)?pd:+t}s(MT,"toNumber");var md=MT;var hd=1/0,DT=17976931348623157e292;function FT(t){if(!t)return t===0?t:0;if(t=md(t),t===hd||t===-hd){var e=t<0?-1:1;return e*DT}return t===t?t:0}s(FT,"toFinite");var gd=FT;function GT(t){var e=gd(t),r=e%1;return e===e?r?e-r:e:0}s(GT,"toInteger");var $t=GT;function UT(t){return t}s(UT,"identity");var Ze=UT;var BT="[object AsyncFunction]",jT="[object Function]",WT="[object GeneratorFunction]",KT="[object Proxy]";function HT(t){if(!de(t))return!1;var e=Ke(t);return e==jT||e==WT||e==BT||e==KT}s(HT,"isFunction");var He=HT;var VT=Ae["__core-js_shared__"],to=VT;var yd=function(){var t=/[^.]+$/.exec(to&&to.keys&&to.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function zT(t){return!!yd&&yd in t}s(zT,"isMasked");var xd=zT;var qT=Function.prototype,YT=qT.toString;function XT(t){if(t!=null){try{return YT.call(t)}catch{}try{return t+""}catch{}}return""}s(XT,"toSource");var qt=XT;var JT=/[\\^$.*+?()[\]{}|]/g,ZT=/^\[object .+?Constructor\]$/,QT=Function.prototype,eR=Object.prototype,tR=QT.toString,rR=eR.hasOwnProperty,nR=RegExp("^"+tR.call(rR).replace(JT,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function iR(t){if(!de(t)||xd(t))return!1;var e=He(t)?nR:ZT;return e.test(qt(t))}s(iR,"baseIsNative");var Td=iR;function sR(t,e){return t?.[e]}s(sR,"getValue");var Rd=sR;function aR(t,e){var r=Rd(t,e);return Td(r)?r:void 0}s(aR,"getNative");var Qe=aR;var oR=Qe(Ae,"WeakMap"),ro=oR;var Ad=Object.create,lR=function(){function t(){}return s(t,"object"),function(e){if(!de(e))return{};if(Ad)return Ad(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}(),Ed=lR;function uR(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}s(uR,"apply");var vd=uR;function cR(){}s(cR,"noop");var pe=cR;function fR(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}s(fR,"copyArray");var Id=fR;var dR=800,pR=16,mR=Date.now;function hR(t){var e=0,r=0;return function(){var n=mR(),i=pR-(n-r);if(r=n,i>0){if(++e>=dR)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}s(hR,"shortOut");var Sd=hR;function gR(t){return function(){return t}}s(gR,"constant");var kd=gR;var yR=function(){try{var t=Qe(Object,"defineProperty");return t({},"",{}),t}catch{}}(),ii=yR;var xR=ii?function(t,e){return ii(t,"toString",{configurable:!0,enumerable:!1,value:kd(e),writable:!0})}:Ze,Cd=xR;var TR=Sd(Cd),Nd=TR;function RR(t,e){for(var r=-1,n=t==null?0:t.length;++r<n&&e(t[r],r,t)!==!1;);return t}s(RR,"arrayEach");var no=RR;function AR(t,e,r,n){for(var i=t.length,a=r+(n?1:-1);n?a--:++a<i;)if(e(t[a],a,t))return a;return-1}s(AR,"baseFindIndex");var io=AR;function ER(t){return t!==t}s(ER,"baseIsNaN");var $d=ER;function vR(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return-1}s(vR,"strictIndexOf");var wd=vR;function IR(t,e,r){return e===e?wd(t,e,r):io(t,$d,r)}s(IR,"baseIndexOf");var si=IR;function SR(t,e){var r=t==null?0:t.length;return!!r&&si(t,e,0)>-1}s(SR,"arrayIncludes");var so=SR;var kR=9007199254740991,CR=/^(?:0|[1-9]\d*)$/;function NR(t,e){var r=typeof t;return e=e??kR,!!e&&(r=="number"||r!="symbol"&&CR.test(t))&&t>-1&&t%1==0&&t<e}s(NR,"isIndex");var fr=NR;function $R(t,e,r){e=="__proto__"&&ii?ii(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}s($R,"baseAssignValue");var ai=$R;function wR(t,e){return t===e||t!==t&&e!==e}s(wR,"eq");var wt=wR;var _R=Object.prototype,bR=_R.hasOwnProperty;function OR(t,e,r){var n=t[e];(!(bR.call(t,e)&&wt(n,r))||r===void 0&&!(e in t))&&ai(t,e,r)}s(OR,"assignValue");var dr=OR;function LR(t,e,r,n){var i=!r;r||(r={});for(var a=-1,o=e.length;++a<o;){var l=e[a],u=n?n(r[l],t[l],l,r,t):void 0;u===void 0&&(u=t[l]),i?ai(r,l,u):dr(r,l,u)}return r}s(LR,"copyObject");var _t=LR;var _d=Math.max;function PR(t,e,r){return e=_d(e===void 0?t.length-1:e,0),function(){for(var n=arguments,i=-1,a=_d(n.length-e,0),o=Array(a);++i<a;)o[i]=n[e+i];i=-1;for(var l=Array(e+1);++i<e;)l[i]=n[i];return l[e]=r(o),vd(t,this,l)}}s(PR,"overRest");var bd=PR;function MR(t,e){return Nd(bd(t,e,Ze),t+"")}s(MR,"baseRest");var oi=MR;var DR=9007199254740991;function FR(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=DR}s(FR,"isLength");var li=FR;function GR(t){return t!=null&&li(t.length)&&!He(t)}s(GR,"isArrayLike");var Ee=GR;function UR(t,e,r){if(!de(r))return!1;var n=typeof e;return(n=="number"?Ee(r)&&fr(e,r.length):n=="string"&&e in r)?wt(r[e],t):!1}s(UR,"isIterateeCall");var pr=UR;function BR(t){return oi(function(e,r){var n=-1,i=r.length,a=i>1?r[i-1]:void 0,o=i>2?r[2]:void 0;for(a=t.length>3&&typeof a=="function"?(i--,a):void 0,o&&pr(r[0],r[1],o)&&(a=i<3?void 0:a,i=1),e=Object(e);++n<i;){var l=r[n];l&&t(e,l,n,a)}return e})}s(BR,"createAssigner");var Od=BR;var jR=Object.prototype;function WR(t){var e=t&&t.constructor,r=typeof e=="function"&&e.prototype||jR;return t===r}s(WR,"isPrototype");var bt=WR;function KR(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}s(KR,"baseTimes");var Ld=KR;var HR="[object Arguments]";function VR(t){return ge(t)&&Ke(t)==HR}s(VR,"baseIsArguments");var Ku=VR;var Pd=Object.prototype,zR=Pd.hasOwnProperty,qR=Pd.propertyIsEnumerable,YR=Ku(function(){return arguments}())?Ku:function(t){return ge(t)&&zR.call(t,"callee")&&!qR.call(t,"callee")},mr=YR;function XR(){return!1}s(XR,"stubFalse");var Md=XR;var Gd=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Dd=Gd&&typeof module=="object"&&module&&!module.nodeType&&module,JR=Dd&&Dd.exports===Gd,Fd=JR?Ae.Buffer:void 0,ZR=Fd?Fd.isBuffer:void 0,QR=ZR||Md,Yt=QR;var eA="[object Arguments]",tA="[object Array]",rA="[object Boolean]",nA="[object Date]",iA="[object Error]",sA="[object Function]",aA="[object Map]",oA="[object Number]",lA="[object Object]",uA="[object RegExp]",cA="[object Set]",fA="[object String]",dA="[object WeakMap]",pA="[object ArrayBuffer]",mA="[object DataView]",hA="[object Float32Array]",gA="[object Float64Array]",yA="[object Int8Array]",xA="[object Int16Array]",TA="[object Int32Array]",RA="[object Uint8Array]",AA="[object Uint8ClampedArray]",EA="[object Uint16Array]",vA="[object Uint32Array]",se={};se[hA]=se[gA]=se[yA]=se[xA]=se[TA]=se[RA]=se[AA]=se[EA]=se[vA]=!0;se[eA]=se[tA]=se[pA]=se[rA]=se[mA]=se[nA]=se[iA]=se[sA]=se[aA]=se[oA]=se[lA]=se[uA]=se[cA]=se[fA]=se[dA]=!1;function IA(t){return ge(t)&&li(t.length)&&!!se[Ke(t)]}s(IA,"baseIsTypedArray");var Ud=IA;function SA(t){return function(e){return t(e)}}s(SA,"baseUnary");var Ot=SA;var Bd=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ss=Bd&&typeof module=="object"&&module&&!module.nodeType&&module,kA=Ss&&Ss.exports===Bd,Hu=kA&&eo.process,CA=function(){try{var t=Ss&&Ss.require&&Ss.require("util").types;return t||Hu&&Hu.binding&&Hu.binding("util")}catch{}}(),ft=CA;var jd=ft&&ft.isTypedArray,NA=jd?Ot(jd):Ud,ui=NA;var $A=Object.prototype,wA=$A.hasOwnProperty;function _A(t,e){var r=N(t),n=!r&&mr(t),i=!r&&!n&&Yt(t),a=!r&&!n&&!i&&ui(t),o=r||n||i||a,l=o?Ld(t.length,String):[],u=l.length;for(var c in t)(e||wA.call(t,c))&&!(o&&(c=="length"||i&&(c=="offset"||c=="parent")||a&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||fr(c,u)))&&l.push(c);return l}s(_A,"arrayLikeKeys");var ao=_A;function bA(t,e){return function(r){return t(e(r))}}s(bA,"overArg");var oo=bA;var OA=oo(Object.keys,Object),Wd=OA;var LA=Object.prototype,PA=LA.hasOwnProperty;function MA(t){if(!bt(t))return Wd(t);var e=[];for(var r in Object(t))PA.call(t,r)&&r!="constructor"&&e.push(r);return e}s(MA,"baseKeys");var lo=MA;function DA(t){return Ee(t)?ao(t):lo(t)}s(DA,"keys");var Z=DA;var FA=Object.prototype,GA=FA.hasOwnProperty,UA=Od(function(t,e){if(bt(e)||Ee(e)){_t(e,Z(e),t);return}for(var r in e)GA.call(e,r)&&dr(t,r,e[r])}),be=UA;function BA(t){var e=[];if(t!=null)for(var r in Object(t))e.push(r);return e}s(BA,"nativeKeysIn");var Kd=BA;var jA=Object.prototype,WA=jA.hasOwnProperty;function KA(t){if(!de(t))return Kd(t);var e=bt(t),r=[];for(var n in t)n=="constructor"&&(e||!WA.call(t,n))||r.push(n);return r}s(KA,"baseKeysIn");var Hd=KA;function HA(t){return Ee(t)?ao(t,!0):Hd(t)}s(HA,"keysIn");var hr=HA;var VA=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,zA=/^\w*$/;function qA(t,e){if(N(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||Ct(t)?!0:zA.test(t)||!VA.test(t)||e!=null&&t in Object(e)}s(qA,"isKey");var ci=qA;var YA=Qe(Object,"create"),Xt=YA;function XA(){this.__data__=Xt?Xt(null):{},this.size=0}s(XA,"hashClear");var Vd=XA;function JA(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}s(JA,"hashDelete");var zd=JA;var ZA="__lodash_hash_undefined__",QA=Object.prototype,eE=QA.hasOwnProperty;function tE(t){var e=this.__data__;if(Xt){var r=e[t];return r===ZA?void 0:r}return eE.call(e,t)?e[t]:void 0}s(tE,"hashGet");var qd=tE;var rE=Object.prototype,nE=rE.hasOwnProperty;function iE(t){var e=this.__data__;return Xt?e[t]!==void 0:nE.call(e,t)}s(iE,"hashHas");var Yd=iE;var sE="__lodash_hash_undefined__";function aE(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=Xt&&e===void 0?sE:e,this}s(aE,"hashSet");var Xd=aE;function fi(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(fi,"Hash");fi.prototype.clear=Vd;fi.prototype.delete=zd;fi.prototype.get=qd;fi.prototype.has=Yd;fi.prototype.set=Xd;var Vu=fi;function oE(){this.__data__=[],this.size=0}s(oE,"listCacheClear");var Jd=oE;function lE(t,e){for(var r=t.length;r--;)if(wt(t[r][0],e))return r;return-1}s(lE,"assocIndexOf");var gr=lE;var uE=Array.prototype,cE=uE.splice;function fE(t){var e=this.__data__,r=gr(e,t);if(r<0)return!1;var n=e.length-1;return r==n?e.pop():cE.call(e,r,1),--this.size,!0}s(fE,"listCacheDelete");var Zd=fE;function dE(t){var e=this.__data__,r=gr(e,t);return r<0?void 0:e[r][1]}s(dE,"listCacheGet");var Qd=dE;function pE(t){return gr(this.__data__,t)>-1}s(pE,"listCacheHas");var ep=pE;function mE(t,e){var r=this.__data__,n=gr(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this}s(mE,"listCacheSet");var tp=mE;function di(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(di,"ListCache");di.prototype.clear=Jd;di.prototype.delete=Zd;di.prototype.get=Qd;di.prototype.has=ep;di.prototype.set=tp;var yr=di;var hE=Qe(Ae,"Map"),xr=hE;function gE(){this.size=0,this.__data__={hash:new Vu,map:new(xr||yr),string:new Vu}}s(gE,"mapCacheClear");var rp=gE;function yE(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}s(yE,"isKeyable");var np=yE;function xE(t,e){var r=t.__data__;return np(e)?r[typeof e=="string"?"string":"hash"]:r.map}s(xE,"getMapData");var Tr=xE;function TE(t){var e=Tr(this,t).delete(t);return this.size-=e?1:0,e}s(TE,"mapCacheDelete");var ip=TE;function RE(t){return Tr(this,t).get(t)}s(RE,"mapCacheGet");var sp=RE;function AE(t){return Tr(this,t).has(t)}s(AE,"mapCacheHas");var ap=AE;function EE(t,e){var r=Tr(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this}s(EE,"mapCacheSet");var op=EE;function pi(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(pi,"MapCache");pi.prototype.clear=rp;pi.prototype.delete=ip;pi.prototype.get=sp;pi.prototype.has=ap;pi.prototype.set=op;var Hr=pi;var vE="Expected a function";function zu(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(vE);var r=s(function(){var n=arguments,i=e?e.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=t.apply(this,n);return r.cache=a.set(i,o)||a,o},"memoized");return r.cache=new(zu.Cache||Hr),r}s(zu,"memoize");zu.Cache=Hr;var lp=zu;var IE=500;function SE(t){var e=lp(t,function(n){return r.size===IE&&r.clear(),n}),r=e.cache;return e}s(SE,"memoizeCapped");var up=SE;var kE=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,CE=/\\(\\)?/g,NE=up(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(kE,function(r,n,i,a){e.push(i?a.replace(CE,"$1"):n||r)}),e}),cp=NE;function $E(t){return t==null?"":cd(t)}s($E,"toString");var fp=$E;function wE(t,e){return N(t)?t:ci(t,e)?[t]:cp(fp(t))}s(wE,"castPath");var Rr=wE;var _E=1/0;function bE(t){if(typeof t=="string"||Ct(t))return t;var e=t+"";return e=="0"&&1/t==-_E?"-0":e}s(bE,"toKey");var Lt=bE;function OE(t,e){e=Rr(e,t);for(var r=0,n=e.length;t!=null&&r<n;)t=t[Lt(e[r++])];return r&&r==n?t:void 0}s(OE,"baseGet");var mi=OE;function LE(t,e,r){var n=t==null?void 0:mi(t,e);return n===void 0?r:n}s(LE,"get");var dp=LE;function PE(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}s(PE,"arrayPush");var hi=PE;var pp=Ce?Ce.isConcatSpreadable:void 0;function ME(t){return N(t)||mr(t)||!!(pp&&t&&t[pp])}s(ME,"isFlattenable");var mp=ME;function hp(t,e,r,n,i){var a=-1,o=t.length;for(r||(r=mp),i||(i=[]);++a<o;){var l=t[a];e>0&&r(l)?e>1?hp(l,e-1,r,n,i):hi(i,l):n||(i[i.length]=l)}return i}s(hp,"baseFlatten");var gi=hp;function DE(t){var e=t==null?0:t.length;return e?gi(t,1):[]}s(DE,"flatten");var ye=DE;var FE=oo(Object.getPrototypeOf,Object),uo=FE;function GE(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),r=r>i?i:r,r<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var a=Array(i);++n<i;)a[n]=t[n+e];return a}s(GE,"baseSlice");var co=GE;function UE(t,e,r,n){var i=-1,a=t==null?0:t.length;for(n&&a&&(r=t[++i]);++i<a;)r=e(r,t[i],i,t);return r}s(UE,"arrayReduce");var gp=UE;function BE(){this.__data__=new yr,this.size=0}s(BE,"stackClear");var yp=BE;function jE(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}s(jE,"stackDelete");var xp=jE;function WE(t){return this.__data__.get(t)}s(WE,"stackGet");var Tp=WE;function KE(t){return this.__data__.has(t)}s(KE,"stackHas");var Rp=KE;var HE=200;function VE(t,e){var r=this.__data__;if(r instanceof yr){var n=r.__data__;if(!xr||n.length<HE-1)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new Hr(n)}return r.set(t,e),this.size=r.size,this}s(VE,"stackSet");var Ap=VE;function yi(t){var e=this.__data__=new yr(t);this.size=e.size}s(yi,"Stack");yi.prototype.clear=yp;yi.prototype.delete=xp;yi.prototype.get=Tp;yi.prototype.has=Rp;yi.prototype.set=Ap;var Ar=yi;function zE(t,e){return t&&_t(e,Z(e),t)}s(zE,"baseAssign");var Ep=zE;function qE(t,e){return t&&_t(e,hr(e),t)}s(qE,"baseAssignIn");var vp=qE;var Cp=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ip=Cp&&typeof module=="object"&&module&&!module.nodeType&&module,YE=Ip&&Ip.exports===Cp,Sp=YE?Ae.Buffer:void 0,kp=Sp?Sp.allocUnsafe:void 0;function XE(t,e){if(e)return t.slice();var r=t.length,n=kp?kp(r):new t.constructor(r);return t.copy(n),n}s(XE,"cloneBuffer");var Np=XE;function JE(t,e){for(var r=-1,n=t==null?0:t.length,i=0,a=[];++r<n;){var o=t[r];e(o,r,t)&&(a[i++]=o)}return a}s(JE,"arrayFilter");var xi=JE;function ZE(){return[]}s(ZE,"stubArray");var fo=ZE;var QE=Object.prototype,ev=QE.propertyIsEnumerable,$p=Object.getOwnPropertySymbols,tv=$p?function(t){return t==null?[]:(t=Object(t),xi($p(t),function(e){return ev.call(t,e)}))}:fo,Ti=tv;function rv(t,e){return _t(t,Ti(t),e)}s(rv,"copySymbols");var wp=rv;var nv=Object.getOwnPropertySymbols,iv=nv?function(t){for(var e=[];t;)hi(e,Ti(t)),t=uo(t);return e}:fo,po=iv;function sv(t,e){return _t(t,po(t),e)}s(sv,"copySymbolsIn");var _p=sv;function av(t,e,r){var n=e(t);return N(t)?n:hi(n,r(t))}s(av,"baseGetAllKeys");var mo=av;function ov(t){return mo(t,Z,Ti)}s(ov,"getAllKeys");var ks=ov;function lv(t){return mo(t,hr,po)}s(lv,"getAllKeysIn");var ho=lv;var uv=Qe(Ae,"DataView"),go=uv;var cv=Qe(Ae,"Promise"),yo=cv;var fv=Qe(Ae,"Set"),Er=fv;var bp="[object Map]",dv="[object Object]",Op="[object Promise]",Lp="[object Set]",Pp="[object WeakMap]",Mp="[object DataView]",pv=qt(go),mv=qt(xr),hv=qt(yo),gv=qt(Er),yv=qt(ro),Vr=Ke;(go&&Vr(new go(new ArrayBuffer(1)))!=Mp||xr&&Vr(new xr)!=bp||yo&&Vr(yo.resolve())!=Op||Er&&Vr(new Er)!=Lp||ro&&Vr(new ro)!=Pp)&&(Vr=s(function(t){var e=Ke(t),r=e==dv?t.constructor:void 0,n=r?qt(r):"";if(n)switch(n){case pv:return Mp;case mv:return bp;case hv:return Op;case gv:return Lp;case yv:return Pp}return e},"getTag"));var xt=Vr;var xv=Object.prototype,Tv=xv.hasOwnProperty;function Rv(t){var e=t.length,r=new t.constructor(e);return e&&typeof t[0]=="string"&&Tv.call(t,"index")&&(r.index=t.index,r.input=t.input),r}s(Rv,"initCloneArray");var Dp=Rv;var Av=Ae.Uint8Array,Ri=Av;function Ev(t){var e=new t.constructor(t.byteLength);return new Ri(e).set(new Ri(t)),e}s(Ev,"cloneArrayBuffer");var Ai=Ev;function vv(t,e){var r=e?Ai(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}s(vv,"cloneDataView");var Fp=vv;var Iv=/\w*$/;function Sv(t){var e=new t.constructor(t.source,Iv.exec(t));return e.lastIndex=t.lastIndex,e}s(Sv,"cloneRegExp");var Gp=Sv;var Up=Ce?Ce.prototype:void 0,Bp=Up?Up.valueOf:void 0;function kv(t){return Bp?Object(Bp.call(t)):{}}s(kv,"cloneSymbol");var jp=kv;function Cv(t,e){var r=e?Ai(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}s(Cv,"cloneTypedArray");var Wp=Cv;var Nv="[object Boolean]",$v="[object Date]",wv="[object Map]",_v="[object Number]",bv="[object RegExp]",Ov="[object Set]",Lv="[object String]",Pv="[object Symbol]",Mv="[object ArrayBuffer]",Dv="[object DataView]",Fv="[object Float32Array]",Gv="[object Float64Array]",Uv="[object Int8Array]",Bv="[object Int16Array]",jv="[object Int32Array]",Wv="[object Uint8Array]",Kv="[object Uint8ClampedArray]",Hv="[object Uint16Array]",Vv="[object Uint32Array]";function zv(t,e,r){var n=t.constructor;switch(e){case Mv:return Ai(t);case Nv:case $v:return new n(+t);case Dv:return Fp(t,r);case Fv:case Gv:case Uv:case Bv:case jv:case Wv:case Kv:case Hv:case Vv:return Wp(t,r);case wv:return new n;case _v:case Lv:return new n(t);case bv:return Gp(t);case Ov:return new n;case Pv:return jp(t)}}s(zv,"initCloneByTag");var Kp=zv;function qv(t){return typeof t.constructor=="function"&&!bt(t)?Ed(uo(t)):{}}s(qv,"initCloneObject");var Hp=qv;var Yv="[object Map]";function Xv(t){return ge(t)&&xt(t)==Yv}s(Xv,"baseIsMap");var Vp=Xv;var zp=ft&&ft.isMap,Jv=zp?Ot(zp):Vp,qp=Jv;var Zv="[object Set]";function Qv(t){return ge(t)&&xt(t)==Zv}s(Qv,"baseIsSet");var Yp=Qv;var Xp=ft&&ft.isSet,eI=Xp?Ot(Xp):Yp,Jp=eI;var tI=1,rI=2,nI=4,Zp="[object Arguments]",iI="[object Array]",sI="[object Boolean]",aI="[object Date]",oI="[object Error]",Qp="[object Function]",lI="[object GeneratorFunction]",uI="[object Map]",cI="[object Number]",em="[object Object]",fI="[object RegExp]",dI="[object Set]",pI="[object String]",mI="[object Symbol]",hI="[object WeakMap]",gI="[object ArrayBuffer]",yI="[object DataView]",xI="[object Float32Array]",TI="[object Float64Array]",RI="[object Int8Array]",AI="[object Int16Array]",EI="[object Int32Array]",vI="[object Uint8Array]",II="[object Uint8ClampedArray]",SI="[object Uint16Array]",kI="[object Uint32Array]",Q={};Q[Zp]=Q[iI]=Q[gI]=Q[yI]=Q[sI]=Q[aI]=Q[xI]=Q[TI]=Q[RI]=Q[AI]=Q[EI]=Q[uI]=Q[cI]=Q[em]=Q[fI]=Q[dI]=Q[pI]=Q[mI]=Q[vI]=Q[II]=Q[SI]=Q[kI]=!0;Q[oI]=Q[Qp]=Q[hI]=!1;function xo(t,e,r,n,i,a){var o,l=e&tI,u=e&rI,c=e&nI;if(r&&(o=i?r(t,n,i,a):r(t)),o!==void 0)return o;if(!de(t))return t;var f=N(t);if(f){if(o=Dp(t),!l)return Id(t,o)}else{var d=xt(t),p=d==Qp||d==lI;if(Yt(t))return Np(t,l);if(d==em||d==Zp||p&&!i){if(o=u||p?{}:Hp(t),!l)return u?_p(t,vp(o,t)):wp(t,Ep(o,t))}else{if(!Q[d])return i?t:{};o=Kp(t,d,l)}}a||(a=new Ar);var m=a.get(t);if(m)return m;a.set(t,o),Jp(t)?t.forEach(function(A){o.add(xo(A,e,r,A,t,a))}):qp(t)&&t.forEach(function(A,T){o.set(T,xo(A,e,r,T,t,a))});var g=c?u?ho:ks:u?hr:Z,y=f?void 0:g(t);return no(y||t,function(A,T){y&&(T=A,A=t[T]),dr(o,T,xo(A,e,r,T,t,a))}),o}s(xo,"baseClone");var tm=xo;var CI=4;function NI(t){return tm(t,CI)}s(NI,"clone");var ee=NI;function $I(t){for(var e=-1,r=t==null?0:t.length,n=0,i=[];++e<r;){var a=t[e];a&&(i[n++]=a)}return i}s($I,"compact");var Pt=$I;var wI="__lodash_hash_undefined__";function _I(t){return this.__data__.set(t,wI),this}s(_I,"setCacheAdd");var rm=_I;function bI(t){return this.__data__.has(t)}s(bI,"setCacheHas");var nm=bI;function To(t){var e=-1,r=t==null?0:t.length;for(this.__data__=new Hr;++e<r;)this.add(t[e])}s(To,"SetCache");To.prototype.add=To.prototype.push=rm;To.prototype.has=nm;var Ei=To;function OI(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}s(OI,"arraySome");var Ro=OI;function LI(t,e){return t.has(e)}s(LI,"cacheHas");var vi=LI;var PI=1,MI=2;function DI(t,e,r,n,i,a){var o=r&PI,l=t.length,u=e.length;if(l!=u&&!(o&&u>l))return!1;var c=a.get(t),f=a.get(e);if(c&&f)return c==e&&f==t;var d=-1,p=!0,m=r&MI?new Ei:void 0;for(a.set(t,e),a.set(e,t);++d<l;){var g=t[d],y=e[d];if(n)var A=o?n(y,g,d,e,t,a):n(g,y,d,t,e,a);if(A!==void 0){if(A)continue;p=!1;break}if(m){if(!Ro(e,function(T,E){if(!vi(m,E)&&(g===T||i(g,T,r,n,a)))return m.push(E)})){p=!1;break}}else if(!(g===y||i(g,y,r,n,a))){p=!1;break}}return a.delete(t),a.delete(e),p}s(DI,"equalArrays");var Ao=DI;function FI(t){var e=-1,r=Array(t.size);return t.forEach(function(n,i){r[++e]=[i,n]}),r}s(FI,"mapToArray");var im=FI;function GI(t){var e=-1,r=Array(t.size);return t.forEach(function(n){r[++e]=n}),r}s(GI,"setToArray");var Ii=GI;var UI=1,BI=2,jI="[object Boolean]",WI="[object Date]",KI="[object Error]",HI="[object Map]",VI="[object Number]",zI="[object RegExp]",qI="[object Set]",YI="[object String]",XI="[object Symbol]",JI="[object ArrayBuffer]",ZI="[object DataView]",sm=Ce?Ce.prototype:void 0,qu=sm?sm.valueOf:void 0;function QI(t,e,r,n,i,a,o){switch(r){case ZI:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case JI:return!(t.byteLength!=e.byteLength||!a(new Ri(t),new Ri(e)));case jI:case WI:case VI:return wt(+t,+e);case KI:return t.name==e.name&&t.message==e.message;case zI:case YI:return t==e+"";case HI:var l=im;case qI:var u=n&UI;if(l||(l=Ii),t.size!=e.size&&!u)return!1;var c=o.get(t);if(c)return c==e;n|=BI,o.set(t,e);var f=Ao(l(t),l(e),n,i,a,o);return o.delete(t),f;case XI:if(qu)return qu.call(t)==qu.call(e)}return!1}s(QI,"equalByTag");var am=QI;var eS=1,tS=Object.prototype,rS=tS.hasOwnProperty;function nS(t,e,r,n,i,a){var o=r&eS,l=ks(t),u=l.length,c=ks(e),f=c.length;if(u!=f&&!o)return!1;for(var d=u;d--;){var p=l[d];if(!(o?p in e:rS.call(e,p)))return!1}var m=a.get(t),g=a.get(e);if(m&&g)return m==e&&g==t;var y=!0;a.set(t,e),a.set(e,t);for(var A=o;++d<u;){p=l[d];var T=t[p],E=e[p];if(n)var R=o?n(E,T,p,e,t,a):n(T,E,p,t,e,a);if(!(R===void 0?T===E||i(T,E,r,n,a):R)){y=!1;break}A||(A=p=="constructor")}if(y&&!A){var O=t.constructor,L=e.constructor;O!=L&&"constructor"in t&&"constructor"in e&&!(typeof O=="function"&&O instanceof O&&typeof L=="function"&&L instanceof L)&&(y=!1)}return a.delete(t),a.delete(e),y}s(nS,"equalObjects");var om=nS;var iS=1,lm="[object Arguments]",um="[object Array]",Eo="[object Object]",sS=Object.prototype,cm=sS.hasOwnProperty;function aS(t,e,r,n,i,a){var o=N(t),l=N(e),u=o?um:xt(t),c=l?um:xt(e);u=u==lm?Eo:u,c=c==lm?Eo:c;var f=u==Eo,d=c==Eo,p=u==c;if(p&&Yt(t)){if(!Yt(e))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new Ar),o||ui(t)?Ao(t,e,r,n,i,a):am(t,e,u,r,n,i,a);if(!(r&iS)){var m=f&&cm.call(t,"__wrapped__"),g=d&&cm.call(e,"__wrapped__");if(m||g){var y=m?t.value():t,A=g?e.value():e;return a||(a=new Ar),i(y,A,r,n,a)}}return p?(a||(a=new Ar),om(t,e,r,n,i,a)):!1}s(aS,"baseIsEqualDeep");var fm=aS;function dm(t,e,r,n,i){return t===e?!0:t==null||e==null||!ge(t)&&!ge(e)?t!==t&&e!==e:fm(t,e,r,n,dm,i)}s(dm,"baseIsEqual");var vo=dm;var oS=1,lS=2;function uS(t,e,r,n){var i=r.length,a=i,o=!n;if(t==null)return!a;for(t=Object(t);i--;){var l=r[i];if(o&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++i<a;){l=r[i];var u=l[0],c=t[u],f=l[1];if(o&&l[2]){if(c===void 0&&!(u in t))return!1}else{var d=new Ar;if(n)var p=n(c,f,u,t,e,d);if(!(p===void 0?vo(f,c,oS|lS,n,d):p))return!1}}return!0}s(uS,"baseIsMatch");var pm=uS;function cS(t){return t===t&&!de(t)}s(cS,"isStrictComparable");var Io=cS;function fS(t){for(var e=Z(t),r=e.length;r--;){var n=e[r],i=t[n];e[r]=[n,i,Io(i)]}return e}s(fS,"getMatchData");var mm=fS;function dS(t,e){return function(r){return r==null?!1:r[t]===e&&(e!==void 0||t in Object(r))}}s(dS,"matchesStrictComparable");var So=dS;function pS(t){var e=mm(t);return e.length==1&&e[0][2]?So(e[0][0],e[0][1]):function(r){return r===t||pm(r,t,e)}}s(pS,"baseMatches");var hm=pS;function mS(t,e){return t!=null&&e in Object(t)}s(mS,"baseHasIn");var gm=mS;function hS(t,e,r){e=Rr(e,t);for(var n=-1,i=e.length,a=!1;++n<i;){var o=Lt(e[n]);if(!(a=t!=null&&r(t,o)))break;t=t[o]}return a||++n!=i?a:(i=t==null?0:t.length,!!i&&li(i)&&fr(o,i)&&(N(t)||mr(t)))}s(hS,"hasPath");var ko=hS;function gS(t,e){return t!=null&&ko(t,e,gm)}s(gS,"hasIn");var ym=gS;var yS=1,xS=2;function TS(t,e){return ci(t)&&Io(e)?So(Lt(t),e):function(r){var n=dp(r,t);return n===void 0&&n===e?ym(r,t):vo(e,n,yS|xS)}}s(TS,"baseMatchesProperty");var xm=TS;function RS(t){return function(e){return e?.[t]}}s(RS,"baseProperty");var Tm=RS;function AS(t){return function(e){return mi(e,t)}}s(AS,"basePropertyDeep");var Rm=AS;function ES(t){return ci(t)?Tm(Lt(t)):Rm(t)}s(ES,"property");var Am=ES;function vS(t){return typeof t=="function"?t:t==null?Ze:typeof t=="object"?N(t)?xm(t[0],t[1]):hm(t):Am(t)}s(vS,"baseIteratee");var he=vS;function IS(t,e,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;){var o=t[i];e(n,o,r(o),t)}return n}s(IS,"arrayAggregator");var Em=IS;function SS(t){return function(e,r,n){for(var i=-1,a=Object(e),o=n(e),l=o.length;l--;){var u=o[t?l:++i];if(r(a[u],u,a)===!1)break}return e}}s(SS,"createBaseFor");var vm=SS;var kS=vm(),Im=kS;function CS(t,e){return t&&Im(t,e,Z)}s(CS,"baseForOwn");var Sm=CS;function NS(t,e){return function(r,n){if(r==null)return r;if(!Ee(r))return t(r,n);for(var i=r.length,a=e?i:-1,o=Object(r);(e?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}s(NS,"createBaseEach");var km=NS;var $S=km(Sm),et=$S;function wS(t,e,r,n){return et(t,function(i,a,o){e(n,i,r(i),o)}),n}s(wS,"baseAggregator");var Cm=wS;function _S(t,e){return function(r,n){var i=N(r)?Em:Cm,a=e?e():{};return i(r,t,he(n,2),a)}}s(_S,"createAggregator");var Nm=_S;var $m=Object.prototype,bS=$m.hasOwnProperty,OS=oi(function(t,e){t=Object(t);var r=-1,n=e.length,i=n>2?e[2]:void 0;for(i&&pr(e[0],e[1],i)&&(n=1);++r<n;)for(var a=e[r],o=hr(a),l=-1,u=o.length;++l<u;){var c=o[l],f=t[c];(f===void 0||wt(f,$m[c])&&!bS.call(t,c))&&(t[c]=a[c])}return t}),Si=OS;function LS(t){return ge(t)&&Ee(t)}s(LS,"isArrayLikeObject");var Yu=LS;function PS(t,e,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}s(PS,"arrayIncludesWith");var Co=PS;var MS=200;function DS(t,e,r,n){var i=-1,a=so,o=!0,l=t.length,u=[],c=e.length;if(!l)return u;r&&(e=Nt(e,Ot(r))),n?(a=Co,o=!1):e.length>=MS&&(a=vi,o=!1,e=new Ei(e));e:for(;++i<l;){var f=t[i],d=r==null?f:r(f);if(f=n||f!==0?f:0,o&&d===d){for(var p=c;p--;)if(e[p]===d)continue e;u.push(f)}else a(e,d,n)||u.push(f)}return u}s(DS,"baseDifference");var wm=DS;var FS=oi(function(t,e){return Yu(t)?wm(t,gi(e,1,Yu,!0)):[]}),vr=FS;function GS(t){var e=t==null?0:t.length;return e?t[e-1]:void 0}s(GS,"last");var Mt=GS;function US(t,e,r){var n=t==null?0:t.length;return n?(e=r||e===void 0?1:$t(e),co(t,e<0?0:e,n)):[]}s(US,"drop");var xe=US;function BS(t,e,r){var n=t==null?0:t.length;return n?(e=r||e===void 0?1:$t(e),e=n-e,co(t,0,e<0?0:e)):[]}s(BS,"dropRight");var Jt=BS;function jS(t){return typeof t=="function"?t:Ze}s(jS,"castFunction");var _m=jS;function WS(t,e){var r=N(t)?no:et;return r(t,_m(e))}s(WS,"forEach");var S=WS;function KS(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}s(KS,"arrayEvery");var bm=KS;function HS(t,e){var r=!0;return et(t,function(n,i,a){return r=!!e(n,i,a),r}),r}s(HS,"baseEvery");var Om=HS;function VS(t,e,r){var n=N(t)?bm:Om;return r&&pr(t,e,r)&&(e=void 0),n(t,he(e,3))}s(VS,"every");var Ge=VS;function zS(t,e){var r=[];return et(t,function(n,i,a){e(n,i,a)&&r.push(n)}),r}s(zS,"baseFilter");var No=zS;function qS(t,e){var r=N(t)?xi:No;return r(t,he(e,3))}s(qS,"filter");var Ne=qS;function YS(t){return function(e,r,n){var i=Object(e);if(!Ee(e)){var a=he(r,3);e=Z(e),r=s(function(l){return a(i[l],l,i)},"predicate")}var o=t(e,r,n);return o>-1?i[a?e[o]:o]:void 0}}s(YS,"createFind");var Lm=YS;var XS=Math.max;function JS(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:$t(r);return i<0&&(i=XS(n+i,0)),io(t,he(e,3),i)}s(JS,"findIndex");var Pm=JS;var ZS=Lm(Pm),Dt=ZS;function QS(t){return t&&t.length?t[0]:void 0}s(QS,"head");var $e=QS;function ek(t,e){var r=-1,n=Ee(t)?Array(t.length):[];return et(t,function(i,a,o){n[++r]=e(i,a,o)}),n}s(ek,"baseMap");var Mm=ek;function tk(t,e){var r=N(t)?Nt:Mm;return r(t,he(e,3))}s(tk,"map");var v=tk;function rk(t,e){return gi(v(t,e),1)}s(rk,"flatMap");var Oe=rk;var nk=Object.prototype,ik=nk.hasOwnProperty,sk=Nm(function(t,e,r){ik.call(t,r)?t[r].push(e):ai(t,r,[e])}),Xu=sk;var ak=Object.prototype,ok=ak.hasOwnProperty;function lk(t,e){return t!=null&&ok.call(t,e)}s(lk,"baseHas");var Dm=lk;function uk(t,e){return t!=null&&ko(t,e,Dm)}s(uk,"has");var k=uk;var ck="[object String]";function fk(t){return typeof t=="string"||!N(t)&&ge(t)&&Ke(t)==ck}s(fk,"isString");var Ie=fk;function dk(t,e){return Nt(e,function(r){return t[r]})}s(dk,"baseValues");var Fm=dk;function pk(t){return t==null?[]:Fm(t,Z(t))}s(pk,"values");var q=pk;var mk=Math.max;function hk(t,e,r,n){t=Ee(t)?t:q(t),r=r&&!n?$t(r):0;var i=t.length;return r<0&&(r=mk(i+r,0)),Ie(t)?r<=i&&t.indexOf(e,r)>-1:!!i&&si(t,e,r)>-1}s(hk,"includes");var oe=hk;var gk=Math.max;function yk(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:$t(r);return i<0&&(i=gk(n+i,0)),si(t,e,i)}s(yk,"indexOf");var $o=yk;var xk="[object Map]",Tk="[object Set]",Rk=Object.prototype,Ak=Rk.hasOwnProperty;function Ek(t){if(t==null)return!0;if(Ee(t)&&(N(t)||typeof t=="string"||typeof t.splice=="function"||Yt(t)||ui(t)||mr(t)))return!t.length;var e=xt(t);if(e==xk||e==Tk)return!t.size;if(bt(t))return!lo(t).length;for(var r in t)if(Ak.call(t,r))return!1;return!0}s(Ek,"isEmpty");var D=Ek;var vk="[object RegExp]";function Ik(t){return ge(t)&&Ke(t)==vk}s(Ik,"baseIsRegExp");var Gm=Ik;var Um=ft&&ft.isRegExp,Sk=Um?Ot(Um):Gm,dt=Sk;function kk(t){return t===void 0}s(kk,"isUndefined");var Ue=kk;function Ck(t,e){return t<e}s(Ck,"baseLt");var Bm=Ck;function Nk(t,e,r){for(var n=-1,i=t.length;++n<i;){var a=t[n],o=e(a);if(o!=null&&(l===void 0?o===o&&!Ct(o):r(o,l)))var l=o,u=a}return u}s(Nk,"baseExtremum");var jm=Nk;function $k(t){return t&&t.length?jm(t,Ze,Bm):void 0}s($k,"min");var Wm=$k;var wk="Expected a function";function _k(t){if(typeof t!="function")throw new TypeError(wk);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}s(_k,"negate");var Km=_k;function bk(t,e,r,n){if(!de(t))return t;e=Rr(e,t);for(var i=-1,a=e.length,o=a-1,l=t;l!=null&&++i<a;){var u=Lt(e[i]),c=r;if(u==="__proto__"||u==="constructor"||u==="prototype")return t;if(i!=o){var f=l[u];c=n?n(f,u,l):void 0,c===void 0&&(c=de(f)?f:fr(e[i+1])?[]:{})}dr(l,u,c),l=l[u]}return t}s(bk,"baseSet");var Hm=bk;function Ok(t,e,r){for(var n=-1,i=e.length,a={};++n<i;){var o=e[n],l=mi(t,o);r(l,o)&&Hm(a,Rr(o,t),l)}return a}s(Ok,"basePickBy");var Vm=Ok;function Lk(t,e){if(t==null)return{};var r=Nt(ho(t),function(n){return[n]});return e=he(e),Vm(t,r,function(n,i){return e(n,i[0])})}s(Lk,"pickBy");var tt=Lk;function Pk(t,e,r,n,i){return i(t,function(a,o,l){r=n?(n=!1,a):e(r,a,o,l)}),r}s(Pk,"baseReduce");var zm=Pk;function Mk(t,e,r){var n=N(t)?gp:zm,i=arguments.length<3;return n(t,he(e,4),r,i,et)}s(Mk,"reduce");var me=Mk;function Dk(t,e){var r=N(t)?xi:No;return r(t,Km(he(e,3)))}s(Dk,"reject");var Ir=Dk;function Fk(t,e){var r;return et(t,function(n,i,a){return r=e(n,i,a),!r}),!!r}s(Fk,"baseSome");var qm=Fk;function Gk(t,e,r){var n=N(t)?Ro:qm;return r&&pr(t,e,r)&&(e=void 0),n(t,he(e,3))}s(Gk,"some");var Cs=Gk;var Uk=1/0,Bk=Er&&1/Ii(new Er([,-0]))[1]==Uk?function(t){return new Er(t)}:pe,Ym=Bk;var jk=200;function Wk(t,e,r){var n=-1,i=so,a=t.length,o=!0,l=[],u=l;if(r)o=!1,i=Co;else if(a>=jk){var c=e?null:Ym(t);if(c)return Ii(c);o=!1,i=vi,u=new Ei}else u=e?[]:l;e:for(;++n<a;){var f=t[n],d=e?e(f):f;if(f=r||f!==0?f:0,o&&d===d){for(var p=u.length;p--;)if(u[p]===d)continue e;e&&u.push(d),l.push(f)}else i(u,d,r)||(u!==l&&u.push(d),l.push(f))}return l}s(Wk,"baseUniq");var wo=Wk;function Kk(t){return t&&t.length?wo(t):[]}s(Kk,"uniq");var ki=Kk;function Hk(t,e){return t&&t.length?wo(t,he(e,2)):[]}s(Hk,"uniqBy");var Xm=Hk;function Ci(t){console&&console.error&&console.error(`Error: ${t}`)}s(Ci,"PRINT_ERROR");function Ns(t){console&&console.warn&&console.warn(`Warning: ${t}`)}s(Ns,"PRINT_WARNING");function $s(t){let e=new Date().getTime(),r=t();return{time:new Date().getTime()-e,value:r}}s($s,"timer");function ws(t){function e(){}s(e,"FakeConstructor"),e.prototype=t;let r=new e;function n(){return typeof r.bar}return s(n,"fakeAccess"),n(),n(),t;(0,eval)(t)}s(ws,"toFastProperties");function Vk(t){return zk(t)?t.LABEL:t.name}s(Vk,"tokenLabel");function zk(t){return Ie(t.LABEL)&&t.LABEL!==""}s(zk,"hasTokenLabel");var at=class{static{s(this,"AbstractProduction")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),S(this.definition,r=>{r.accept(e)})}},V=class extends at{static{s(this,"NonTerminal")}constructor(e){super([]),this.idx=1,be(this,tt(e,r=>r!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}},Ve=class extends at{static{s(this,"Rule")}constructor(e){super(e.definition),this.orgText="",be(this,tt(e,r=>r!==void 0))}},te=class extends at{static{s(this,"Alternative")}constructor(e){super(e.definition),this.ignoreAmbiguities=!1,be(this,tt(e,r=>r!==void 0))}},z=class extends at{static{s(this,"Option")}constructor(e){super(e.definition),this.idx=1,be(this,tt(e,r=>r!==void 0))}},re=class extends at{static{s(this,"RepetitionMandatory")}constructor(e){super(e.definition),this.idx=1,be(this,tt(e,r=>r!==void 0))}},ne=class extends at{static{s(this,"RepetitionMandatoryWithSeparator")}constructor(e){super(e.definition),this.idx=1,be(this,tt(e,r=>r!==void 0))}},G=class extends at{static{s(this,"Repetition")}constructor(e){super(e.definition),this.idx=1,be(this,tt(e,r=>r!==void 0))}},Y=class extends at{static{s(this,"RepetitionWithSeparator")}constructor(e){super(e.definition),this.idx=1,be(this,tt(e,r=>r!==void 0))}},X=class extends at{static{s(this,"Alternation")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,be(this,tt(e,r=>r!==void 0))}},F=class{static{s(this,"Terminal")}constructor(e){this.idx=1,be(this,tt(e,r=>r!==void 0))}accept(e){e.visit(this)}};function _o(t){return v(t,Ni)}s(_o,"serializeGrammar");function Ni(t){function e(r){return v(r,Ni)}if(s(e,"convertDefinition"),t instanceof V){let r={type:"NonTerminal",name:t.nonTerminalName,idx:t.idx};return Ie(t.label)&&(r.label=t.label),r}else{if(t instanceof te)return{type:"Alternative",definition:e(t.definition)};if(t instanceof z)return{type:"Option",idx:t.idx,definition:e(t.definition)};if(t instanceof re)return{type:"RepetitionMandatory",idx:t.idx,definition:e(t.definition)};if(t instanceof ne)return{type:"RepetitionMandatoryWithSeparator",idx:t.idx,separator:Ni(new F({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof Y)return{type:"RepetitionWithSeparator",idx:t.idx,separator:Ni(new F({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof G)return{type:"Repetition",idx:t.idx,definition:e(t.definition)};if(t instanceof X)return{type:"Alternation",idx:t.idx,definition:e(t.definition)};if(t instanceof F){let r={type:"Terminal",name:t.terminalType.name,label:Vk(t.terminalType),idx:t.idx};Ie(t.label)&&(r.terminalLabel=t.label);let n=t.terminalType.PATTERN;return t.terminalType.PATTERN&&(r.pattern=dt(n)?n.source:n),r}else{if(t instanceof Ve)return{type:"Rule",name:t.name,orgText:t.orgText,definition:e(t.definition)};throw Error("non exhaustive match")}}}s(Ni,"serializeProduction");var ze=class{static{s(this,"GAstVisitor")}visit(e){let r=e;switch(r.constructor){case V:return this.visitNonTerminal(r);case te:return this.visitAlternative(r);case z:return this.visitOption(r);case re:return this.visitRepetitionMandatory(r);case ne:return this.visitRepetitionMandatoryWithSeparator(r);case Y:return this.visitRepetitionWithSeparator(r);case G:return this.visitRepetition(r);case X:return this.visitAlternation(r);case F:return this.visitTerminal(r);case Ve:return this.visitRule(r);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}};function Ju(t){return t instanceof te||t instanceof z||t instanceof G||t instanceof re||t instanceof ne||t instanceof Y||t instanceof F||t instanceof Ve}s(Ju,"isSequenceProd");function zr(t,e=[]){return t instanceof z||t instanceof G||t instanceof Y?!0:t instanceof X?Cs(t.definition,n=>zr(n,e)):t instanceof V&&oe(e,t)?!1:t instanceof at?(t instanceof V&&e.push(t),Ge(t.definition,n=>zr(n,e))):!1}s(zr,"isOptionalProd");function Zu(t){return t instanceof X}s(Zu,"isBranchingProd");function rt(t){if(t instanceof V)return"SUBRULE";if(t instanceof z)return"OPTION";if(t instanceof X)return"OR";if(t instanceof re)return"AT_LEAST_ONE";if(t instanceof ne)return"AT_LEAST_ONE_SEP";if(t instanceof Y)return"MANY_SEP";if(t instanceof G)return"MANY";if(t instanceof F)return"CONSUME";throw Error("non exhaustive match")}s(rt,"getProductionDslName");var Zt=class{static{s(this,"RestWalker")}walk(e,r=[]){S(e.definition,(n,i)=>{let a=xe(e.definition,i+1);if(n instanceof V)this.walkProdRef(n,a,r);else if(n instanceof F)this.walkTerminal(n,a,r);else if(n instanceof te)this.walkFlat(n,a,r);else if(n instanceof z)this.walkOption(n,a,r);else if(n instanceof re)this.walkAtLeastOne(n,a,r);else if(n instanceof ne)this.walkAtLeastOneSep(n,a,r);else if(n instanceof Y)this.walkManySep(n,a,r);else if(n instanceof G)this.walkMany(n,a,r);else if(n instanceof X)this.walkOr(n,a,r);else throw Error("non exhaustive match")})}walkTerminal(e,r,n){}walkProdRef(e,r,n){}walkFlat(e,r,n){let i=r.concat(n);this.walk(e,i)}walkOption(e,r,n){let i=r.concat(n);this.walk(e,i)}walkAtLeastOne(e,r,n){let i=[new z({definition:e.definition})].concat(r,n);this.walk(e,i)}walkAtLeastOneSep(e,r,n){let i=Jm(e,r,n);this.walk(e,i)}walkMany(e,r,n){let i=[new z({definition:e.definition})].concat(r,n);this.walk(e,i)}walkManySep(e,r,n){let i=Jm(e,r,n);this.walk(e,i)}walkOr(e,r,n){let i=r.concat(n);S(e.definition,a=>{let o=new te({definition:[a]});this.walk(o,i)})}};function Jm(t,e,r){return[new z({definition:[new F({terminalType:t.separator})].concat(t.definition)})].concat(e,r)}s(Jm,"restForRepetitionWithSeparator");function qr(t){if(t instanceof V)return qr(t.referencedRule);if(t instanceof F)return Xk(t);if(Ju(t))return qk(t);if(Zu(t))return Yk(t);throw Error("non exhaustive match")}s(qr,"first");function qk(t){let e=[],r=t.definition,n=0,i=r.length>n,a,o=!0;for(;i&&o;)a=r[n],o=zr(a),e=e.concat(qr(a)),n=n+1,i=r.length>n;return ki(e)}s(qk,"firstForSequence");function Yk(t){let e=v(t.definition,r=>qr(r));return ki(ye(e))}s(Yk,"firstForBranching");function Xk(t){return[t.terminalType]}s(Xk,"firstForTerminal");var bo="_~IN~_";var Qu=class extends Zt{static{s(this,"ResyncFollowsWalker")}constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,r,n){}walkProdRef(e,r,n){let i=Jk(e.referencedRule,e.idx)+this.topProd.name,a=r.concat(n),o=new te({definition:a}),l=qr(o);this.follows[i]=l}};function Zm(t){let e={};return S(t,r=>{let n=new Qu(r).startWalking();be(e,n)}),e}s(Zm,"computeAllProdsFollows");function Jk(t,e){return t.name+e+bo}s(Jk,"buildBetweenProdsFollowPrefix");var Oo={},Zk=new jr;function $i(t){let e=t.toString();if(Oo.hasOwnProperty(e))return Oo[e];{let r=Zk.pattern(e);return Oo[e]=r,r}}s($i,"getRegExpAst");function Qm(){Oo={}}s(Qm,"clearRegExpParserCache");var th="Complement Sets are not supported for first char optimization",_s=`Unable to use "first char" lexer optimizations:
`;function rh(t,e=!1){try{let r=$i(t);return ec(r.value,{},r.flags.ignoreCase)}catch(r){if(r.message===th)e&&Ns(`${_s}	Unable to optimize: < ${t.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let n="";e&&(n=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),Ci(`${_s}
	Failed parsing: < ${t.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+n)}}return[]}s(rh,"getOptimizedStartCodesIndices");function ec(t,e,r){switch(t.type){case"Disjunction":for(let i=0;i<t.value.length;i++)ec(t.value[i],e,r);break;case"Alternative":let n=t.value;for(let i=0;i<n.length;i++){let a=n[i];switch(a.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}let o=a;switch(o.type){case"Character":Lo(o.value,e,r);break;case"Set":if(o.complement===!0)throw Error(th);S(o.value,u=>{if(typeof u=="number")Lo(u,e,r);else{let c=u;if(r===!0)for(let f=c.from;f<=c.to;f++)Lo(f,e,r);else{for(let f=c.from;f<=c.to&&f<wi;f++)Lo(f,e,r);if(c.to>=wi){let f=c.from>=wi?c.from:wi,d=c.to,p=Ft(f),m=Ft(d);for(let g=p;g<=m;g++)e[g]=g}}}});break;case"Group":ec(o.value,e,r);break;default:throw Error("Non Exhaustive Match")}let l=o.quantifier!==void 0&&o.quantifier.atLeast===0;if(o.type==="Group"&&tc(o)===!1||o.type!=="Group"&&l===!1)break}break;default:throw Error("non exhaustive match!")}return q(e)}s(ec,"firstCharOptimizedIndices");function Lo(t,e,r){let n=Ft(t);e[n]=n,r===!0&&Qk(t,e)}s(Lo,"addOptimizedIdxToResult");function Qk(t,e){let r=String.fromCharCode(t),n=r.toUpperCase();if(n!==r){let i=Ft(n.charCodeAt(0));e[i]=i}else{let i=r.toLowerCase();if(i!==r){let a=Ft(i.charCodeAt(0));e[a]=a}}}s(Qk,"handleIgnoreCase");function eh(t,e){return Dt(t.value,r=>{if(typeof r=="number")return oe(e,r);{let n=r;return Dt(e,i=>n.from<=i&&i<=n.to)!==void 0}})}s(eh,"findCode");function tc(t){let e=t.quantifier;return e&&e.atLeast===0?!0:t.value?N(t.value)?Ge(t.value,tc):tc(t.value):!1}s(tc,"isWholeOptional");var rc=class extends kt{static{s(this,"CharCodeFinder")}constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){oe(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?eh(e,this.targetCharCodes)===void 0&&(this.found=!0):eh(e,this.targetCharCodes)!==void 0&&(this.found=!0)}};function Po(t,e){if(e instanceof RegExp){let r=$i(e),n=new rc(t);return n.visit(r),n.found}else return Dt(e,r=>oe(t,r.charCodeAt(0)))!==void 0}s(Po,"canMatchCharCode");var Yr="PATTERN",_i="defaultMode",Mo="modes",ic=typeof new RegExp("(?:)").sticky=="boolean";function sh(t,e){e=Si(e,{useSticky:ic,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:s((E,R)=>R(),"tracer")});let r=e.tracer;r("initCharCodeToOptimizedIndexMap",()=>{yC()});let n;r("Reject Lexer.NA",()=>{n=Ir(t,E=>E[Yr]===ue.NA)});let i=!1,a;r("Transform Patterns",()=>{i=!1,a=v(n,E=>{let R=E[Yr];if(dt(R)){let O=R.source;return O.length===1&&O!=="^"&&O!=="$"&&O!=="."&&!R.ignoreCase?O:O.length===2&&O[0]==="\\"&&!oe(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],O[1])?O[1]:e.useSticky?ih(R):nh(R)}else{if(He(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{let O=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),L=new RegExp(O);return e.useSticky?ih(L):nh(L)}}else throw Error("non exhaustive match")}})});let o,l,u,c,f;r("misc mapping",()=>{o=v(n,E=>E.tokenTypeIdx),l=v(n,E=>{let R=E.GROUP;if(R!==ue.SKIPPED){if(Ie(R))return R;if(Ue(R))return!1;throw Error("non exhaustive match")}}),u=v(n,E=>{let R=E.LONGER_ALT;if(R)return N(R)?v(R,L=>$o(n,L)):[$o(n,R)]}),c=v(n,E=>E.PUSH_MODE),f=v(n,E=>k(E,"POP_MODE"))});let d;r("Line Terminator Handling",()=>{let E=ph(e.lineTerminatorCharacters);d=v(n,R=>!1),e.positionTracking!=="onlyOffset"&&(d=v(n,R=>k(R,"LINE_BREAKS")?!!R.LINE_BREAKS:dh(R,E)===!1&&Po(E,R.PATTERN)))});let p,m,g,y;r("Misc Mapping #2",()=>{p=v(n,ch),m=v(a,hC),g=me(n,(E,R)=>{let O=R.GROUP;return Ie(O)&&O!==ue.SKIPPED&&(E[O]=[]),E},{}),y=v(a,(E,R)=>({pattern:a[R],longerAlt:u[R],canLineTerminator:d[R],isCustom:p[R],short:m[R],group:l[R],push:c[R],pop:f[R],tokenTypeIdx:o[R],tokenType:n[R]}))});let A=!0,T=[];return e.safeMode||r("First Char Optimization",()=>{T=me(n,(E,R,O)=>{if(typeof R.PATTERN=="string"){let L=R.PATTERN.charCodeAt(0),ke=Ft(L);nc(E,ke,y[O])}else if(N(R.START_CHARS_HINT)){let L;S(R.START_CHARS_HINT,ke=>{let un=typeof ke=="string"?ke.charCodeAt(0):ke,Le=Ft(un);L!==Le&&(L=Le,nc(E,Le,y[O]))})}else if(dt(R.PATTERN))if(R.PATTERN.unicode)A=!1,e.ensureOptimizations&&Ci(`${_s}	Unable to analyze < ${R.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{let L=rh(R.PATTERN,e.ensureOptimizations);D(L)&&(A=!1),S(L,ke=>{nc(E,ke,y[O])})}else e.ensureOptimizations&&Ci(`${_s}	TokenType: <${R.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),A=!1;return E},[])}),{emptyGroups:g,patternIdxToConfig:y,charCodeToPatternIdxToConfig:T,hasCustom:i,canBeOptimized:A}}s(sh,"analyzeTokenTypes");function ah(t,e){let r=[],n=tC(t);r=r.concat(n.errors);let i=rC(n.valid),a=i.valid;return r=r.concat(i.errors),r=r.concat(eC(a)),r=r.concat(cC(a)),r=r.concat(fC(a,e)),r=r.concat(dC(a)),r}s(ah,"validatePatterns");function eC(t){let e=[],r=Ne(t,n=>dt(n[Yr]));return e=e.concat(iC(r)),e=e.concat(oC(r)),e=e.concat(lC(r)),e=e.concat(uC(r)),e=e.concat(sC(r)),e}s(eC,"validateRegExpPattern");function tC(t){let e=Ne(t,i=>!k(i,Yr)),r=v(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:le.MISSING_PATTERN,tokenTypes:[i]})),n=vr(t,e);return{errors:r,valid:n}}s(tC,"findMissingPatterns");function rC(t){let e=Ne(t,i=>{let a=i[Yr];return!dt(a)&&!He(a)&&!k(a,"exec")&&!Ie(a)}),r=v(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:le.INVALID_PATTERN,tokenTypes:[i]})),n=vr(t,e);return{errors:r,valid:n}}s(rC,"findInvalidPatterns");var nC=/[^\\][$]/;function iC(t){class e extends kt{static{s(this,"EndAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitEndAnchor(a){this.found=!0}}let r=Ne(t,i=>{let a=i.PATTERN;try{let o=$i(a),l=new e;return l.visit(o),l.found}catch{return nC.test(a.source)}});return v(r,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:le.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}s(iC,"findEndOfInputAnchor");function sC(t){let e=Ne(t,n=>n.PATTERN.test(""));return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' must not match an empty string",type:le.EMPTY_MATCH_PATTERN,tokenTypes:[n]}))}s(sC,"findEmptyMatchRegExps");var aC=/[^\\[][\^]|^\^/;function oC(t){class e extends kt{static{s(this,"StartAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitStartAnchor(a){this.found=!0}}let r=Ne(t,i=>{let a=i.PATTERN;try{let o=$i(a),l=new e;return l.visit(o),l.found}catch{return aC.test(a.source)}});return v(r,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:le.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}s(oC,"findStartOfInputAnchor");function lC(t){let e=Ne(t,n=>{let i=n[Yr];return i instanceof RegExp&&(i.multiline||i.global)});return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:le.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[n]}))}s(lC,"findUnsupportedFlags");function uC(t){let e=[],r=v(t,a=>me(t,(o,l)=>(a.PATTERN.source===l.PATTERN.source&&!oe(e,l)&&l.PATTERN!==ue.NA&&(e.push(l),o.push(l)),o),[]));r=Pt(r);let n=Ne(r,a=>a.length>1);return v(n,a=>{let o=v(a,u=>u.name);return{message:`The same RegExp pattern ->${$e(a).PATTERN}<-has been used in all of the following Token Types: ${o.join(", ")} <-`,type:le.DUPLICATE_PATTERNS_FOUND,tokenTypes:a}})}s(uC,"findDuplicatePatterns");function cC(t){let e=Ne(t,n=>{if(!k(n,"GROUP"))return!1;let i=n.GROUP;return i!==ue.SKIPPED&&i!==ue.NA&&!Ie(i)});return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:le.INVALID_GROUP_TYPE_FOUND,tokenTypes:[n]}))}s(cC,"findInvalidGroupType");function fC(t,e){let r=Ne(t,i=>i.PUSH_MODE!==void 0&&!oe(e,i.PUSH_MODE));return v(r,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:le.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}s(fC,"findModesThatDoNotExist");function dC(t){let e=[],r=me(t,(n,i,a)=>{let o=i.PATTERN;return o===ue.NA||(Ie(o)?n.push({str:o,idx:a,tokenType:i}):dt(o)&&mC(o)&&n.push({str:o.source,idx:a,tokenType:i})),n},[]);return S(t,(n,i)=>{S(r,({str:a,idx:o,tokenType:l})=>{if(i<o&&pC(a,n.PATTERN)){let u=`Token: ->${l.name}<- can never be matched.
Because it appears AFTER the Token Type ->${n.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:u,type:le.UNREACHABLE_PATTERN,tokenTypes:[n,l]})}})}),e}s(dC,"findUnreachablePatterns");function pC(t,e){if(dt(e)){let r=e.exec(t);return r!==null&&r.index===0}else{if(He(e))return e(t,0,[],{});if(k(e,"exec"))return e.exec(t,0,[],{});if(typeof e=="string")return e===t;throw Error("non exhaustive match")}}s(pC,"testTokenType");function mC(t){return Dt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],r=>t.source.indexOf(r)!==-1)===void 0}s(mC,"noMetaChar");function nh(t){let e=t.ignoreCase?"i":"";return new RegExp(`^(?:${t.source})`,e)}s(nh,"addStartOfInput");function ih(t){let e=t.ignoreCase?"iy":"y";return new RegExp(`${t.source}`,e)}s(ih,"addStickyFlag");function oh(t,e,r){let n=[];return k(t,_i)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+_i+`> property in its definition
`,type:le.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),k(t,Mo)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+Mo+`> property in its definition
`,type:le.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),k(t,Mo)&&k(t,_i)&&!k(t.modes,t.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${_i}: <${t.defaultMode}>which does not exist
`,type:le.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),k(t,Mo)&&S(t.modes,(i,a)=>{S(i,(o,l)=>{if(Ue(o))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${a}> at index: <${l}>
`,type:le.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(k(o,"LONGER_ALT")){let u=N(o.LONGER_ALT)?o.LONGER_ALT:[o.LONGER_ALT];S(u,c=>{!Ue(c)&&!oe(i,c)&&n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${c.name}> on token <${o.name}> outside of mode <${a}>
`,type:le.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}s(oh,"performRuntimeChecks");function lh(t,e,r){let n=[],i=!1,a=Pt(ye(q(t.modes))),o=Ir(a,u=>u[Yr]===ue.NA),l=ph(r);return e&&S(o,u=>{let c=dh(u,l);if(c!==!1){let d={message:gC(u,c),type:c.issue,tokenType:u};n.push(d)}else k(u,"LINE_BREAKS")?u.LINE_BREAKS===!0&&(i=!0):Po(l,u.PATTERN)&&(i=!0)}),e&&!i&&n.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:le.NO_LINE_BREAKS_FLAGS}),n}s(lh,"performWarningRuntimeChecks");function uh(t){let e={},r=Z(t);return S(r,n=>{let i=t[n];if(N(i))e[n]=[];else throw Error("non exhaustive match")}),e}s(uh,"cloneEmptyGroups");function ch(t){let e=t.PATTERN;if(dt(e))return!1;if(He(e))return!0;if(k(e,"exec"))return!0;if(Ie(e))return!1;throw Error("non exhaustive match")}s(ch,"isCustomPattern");function hC(t){return Ie(t)&&t.length===1?t.charCodeAt(0):!1}s(hC,"isShortPattern");var fh={test:s(function(t){let e=t.length;for(let r=this.lastIndex;r<e;r++){let n=t.charCodeAt(r);if(n===10)return this.lastIndex=r+1,!0;if(n===13)return t.charCodeAt(r+1)===10?this.lastIndex=r+2:this.lastIndex=r+1,!0}return!1},"test"),lastIndex:0};function dh(t,e){if(k(t,"LINE_BREAKS"))return!1;if(dt(t.PATTERN)){try{Po(e,t.PATTERN)}catch(r){return{issue:le.IDENTIFY_TERMINATOR,errMsg:r.message}}return!1}else{if(Ie(t.PATTERN))return!1;if(ch(t))return{issue:le.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}s(dh,"checkLineBreaksIssues");function gC(t,e){if(e.issue===le.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${t.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===le.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${t.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}s(gC,"buildLineBreakIssueMessage");function ph(t){return v(t,r=>Ie(r)?r.charCodeAt(0):r)}s(ph,"getCharCodes");function nc(t,e,r){t[e]===void 0?t[e]=[r]:t[e].push(r)}s(nc,"addToMapOfArrays");var wi=256,Do=[];function Ft(t){return t<wi?t:Do[t]}s(Ft,"charCodeToOptimizedIndex");function yC(){if(D(Do)){Do=new Array(65536);for(let t=0;t<65536;t++)Do[t]=t>255?255+~~(t/255):t}}s(yC,"initCharCodeToOptimizedIndexMap");function Qt(t,e){let r=t.tokenTypeIdx;return r===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[r]===!0}s(Qt,"tokenStructuredMatcher");function bi(t,e){return t.tokenTypeIdx===e.tokenTypeIdx}s(bi,"tokenStructuredMatcherNoCategories");var mh=1,gh={};function er(t){let e=xC(t);TC(e),AC(e),RC(e),S(e,r=>{r.isParent=r.categoryMatches.length>0})}s(er,"augmentTokenTypes");function xC(t){let e=ee(t),r=t,n=!0;for(;n;){r=Pt(ye(v(r,a=>a.CATEGORIES)));let i=vr(r,e);e=e.concat(i),D(i)?n=!1:r=i}return e}s(xC,"expandCategories");function TC(t){S(t,e=>{sc(e)||(gh[mh]=e,e.tokenTypeIdx=mh++),hh(e)&&!N(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),hh(e)||(e.CATEGORIES=[]),EC(e)||(e.categoryMatches=[]),vC(e)||(e.categoryMatchesMap={})})}s(TC,"assignTokenDefaultProps");function RC(t){S(t,e=>{e.categoryMatches=[],S(e.categoryMatchesMap,(r,n)=>{e.categoryMatches.push(gh[n].tokenTypeIdx)})})}s(RC,"assignCategoriesTokensProp");function AC(t){S(t,e=>{yh([],e)})}s(AC,"assignCategoriesMapProp");function yh(t,e){S(t,r=>{e.categoryMatchesMap[r.tokenTypeIdx]=!0}),S(e.CATEGORIES,r=>{let n=t.concat(e);oe(n,r)||yh(n,r)})}s(yh,"singleAssignCategoriesToksMap");function sc(t){return k(t,"tokenTypeIdx")}s(sc,"hasShortKeyProperty");function hh(t){return k(t,"CATEGORIES")}s(hh,"hasCategoriesProperty");function EC(t){return k(t,"categoryMatches")}s(EC,"hasExtendingTokensTypesProperty");function vC(t){return k(t,"categoryMatchesMap")}s(vC,"hasExtendingTokensTypesMapProperty");function xh(t){return k(t,"tokenTypeIdx")}s(xh,"isTokenType");var Oi={buildUnableToPopLexerModeMessage(t){return`Unable to pop Lexer Mode after encountering Token ->${t.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(t,e,r,n,i){return`unexpected character: ->${t.charAt(e)}<- at offset: ${e}, skipped ${r} characters.`}};var le;(function(t){t[t.MISSING_PATTERN=0]="MISSING_PATTERN",t[t.INVALID_PATTERN=1]="INVALID_PATTERN",t[t.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",t[t.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",t[t.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",t[t.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",t[t.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",t[t.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",t[t.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",t[t.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",t[t.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",t[t.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",t[t.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",t[t.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",t[t.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",t[t.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",t[t.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",t[t.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(le||(le={}));var bs={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:Oi,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(bs);var ue=class{static{s(this,"Lexer")}constructor(e,r=bs){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,a)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;let o=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${o}--> <${i}>`);let{time:l,value:u}=$s(a),c=l>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&c(`${o}<-- <${i}> time: ${l}ms`),this.traceInitIndent--,u}else return a()},typeof r=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=be({},bs,r);let n=this.config.traceInitPerf;n===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof n=="number"&&(this.traceInitMaxIdent=n,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,a=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===bs.lineTerminatorsPattern)this.config.lineTerminatorsPattern=fh;else if(this.config.lineTerminatorCharacters===bs.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(r.safeMode&&r.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),N(e)?i={modes:{defaultMode:ee(e)},defaultMode:_i}:(a=!1,i=ee(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(oh(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(lh(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},S(i.modes,(l,u)=>{i.modes[u]=Ir(l,c=>Ue(c))});let o=Z(i.modes);if(S(i.modes,(l,u)=>{this.TRACE_INIT(`Mode: <${u}> processing`,()=>{if(this.modes.push(u),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(ah(l,o))}),D(this.lexerDefinitionErrors)){er(l);let c;this.TRACE_INIT("analyzeTokenTypes",()=>{c=sh(l,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:r.positionTracking,ensureOptimizations:r.ensureOptimizations,safeMode:r.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[u]=c.patternIdxToConfig,this.charCodeToPatternIdxToConfig[u]=c.charCodeToPatternIdxToConfig,this.emptyGroups=be({},this.emptyGroups,c.emptyGroups),this.hasCustom=c.hasCustom||this.hasCustom,this.canModeBeOptimized[u]=c.canBeOptimized}})}),this.defaultMode=i.defaultMode,!D(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){let u=v(this.lexerDefinitionErrors,c=>c.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+u)}S(this.lexerDefinitionWarning,l=>{Ns(l.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(ic?(this.chopInput=Ze,this.match=this.matchWithTest):(this.updateLastIndex=pe,this.match=this.matchWithExec),a&&(this.handleModes=pe),this.trackStartLines===!1&&(this.computeNewColumn=Ze),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=pe),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{let l=me(this.canModeBeOptimized,(u,c,f)=>(c===!1&&u.push(f),u),[]);if(r.ensureOptimizations&&!D(l))throw Error(`Lexer Modes: < ${l.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Qm()}),this.TRACE_INIT("toFastProperties",()=>{ws(this)})})}tokenize(e,r=this.defaultMode){if(!D(this.lexerDefinitionErrors)){let i=v(this.lexerDefinitionErrors,a=>a.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,r)}tokenizeInternal(e,r){let n,i,a,o,l,u,c,f,d,p,m,g,y,A,T,E,R=e,O=R.length,L=0,ke=0,un=this.hasCustom?0:Math.floor(e.length/10),Le=new Array(un),or=[],Wt=this.trackStartLines?1:void 0,I=this.trackStartLines?1:void 0,x=uh(this.emptyGroups),$=this.trackStartLines,C=this.config.lineTerminatorsPattern,K=0,M=[],P=[],je=[],We=[];Object.freeze(We);let ie;function wr(){return M}s(wr,"getPossiblePatternsSlow");function Pf(Pe){let lt=Ft(Pe),cn=P[lt];return cn===void 0?We:cn}s(Pf,"getPossiblePatternsOptimized");let xx=s(Pe=>{if(je.length===1&&Pe.tokenType.PUSH_MODE===void 0){let lt=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(Pe);or.push({offset:Pe.startOffset,line:Pe.startLine,column:Pe.startColumn,length:Pe.image.length,message:lt})}else{je.pop();let lt=Mt(je);M=this.patternIdxToConfig[lt],P=this.charCodeToPatternIdxToConfig[lt],K=M.length;let cn=this.canModeBeOptimized[lt]&&this.config.safeMode===!1;P&&cn?ie=Pf:ie=wr}},"pop_mode");function Mf(Pe){je.push(Pe),P=this.charCodeToPatternIdxToConfig[Pe],M=this.patternIdxToConfig[Pe],K=M.length,K=M.length;let lt=this.canModeBeOptimized[Pe]&&this.config.safeMode===!1;P&&lt?ie=Pf:ie=wr}s(Mf,"push_mode"),Mf.call(this,r);let mt,Df=this.config.recoveryEnabled;for(;L<O;){u=null;let Pe=R.charCodeAt(L),lt=ie(Pe),cn=lt.length;for(n=0;n<cn;n++){mt=lt[n];let nt=mt.pattern;c=null;let Kt=mt.short;if(Kt!==!1?Pe===Kt&&(u=nt):mt.isCustom===!0?(E=nt.exec(R,L,Le,x),E!==null?(u=E[0],E.payload!==void 0&&(c=E.payload)):u=null):(this.updateLastIndex(nt,L),u=this.match(nt,e,L)),u!==null){if(l=mt.longerAlt,l!==void 0){let lr=l.length;for(a=0;a<lr;a++){let Ht=M[l[a]],_r=Ht.pattern;if(f=null,Ht.isCustom===!0?(E=_r.exec(R,L,Le,x),E!==null?(o=E[0],E.payload!==void 0&&(f=E.payload)):o=null):(this.updateLastIndex(_r,L),o=this.match(_r,e,L)),o&&o.length>u.length){u=o,c=f,mt=Ht;break}}}break}}if(u!==null){if(d=u.length,p=mt.group,p!==void 0&&(m=mt.tokenTypeIdx,g=this.createTokenInstance(u,L,m,mt.tokenType,Wt,I,d),this.handlePayload(g,c),p===!1?ke=this.addToken(Le,ke,g):x[p].push(g)),e=this.chopInput(e,d),L=L+d,I=this.computeNewColumn(I,d),$===!0&&mt.canLineTerminator===!0){let nt=0,Kt,lr;C.lastIndex=0;do Kt=C.test(u),Kt===!0&&(lr=C.lastIndex-1,nt++);while(Kt===!0);nt!==0&&(Wt=Wt+nt,I=d-lr,this.updateTokenEndLineColumnLocation(g,p,lr,nt,Wt,I,d))}this.handleModes(mt,xx,Mf,g)}else{let nt=L,Kt=Wt,lr=I,Ht=Df===!1;for(;Ht===!1&&L<O;)for(e=this.chopInput(e,1),L++,i=0;i<K;i++){let _r=M[i],Jl=_r.pattern,Ff=_r.short;if(Ff!==!1?R.charCodeAt(L)===Ff&&(Ht=!0):_r.isCustom===!0?Ht=Jl.exec(R,L,Le,x)!==null:(this.updateLastIndex(Jl,L),Ht=Jl.exec(e)!==null),Ht===!0)break}if(y=L-nt,I=this.computeNewColumn(I,y),T=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(R,nt,y,Kt,lr),or.push({offset:nt,line:Kt,column:lr,length:y,message:T}),Df===!1)break}}return this.hasCustom||(Le.length=ke),{tokens:Le,groups:x,errors:or}}handleModes(e,r,n,i){if(e.pop===!0){let a=e.push;r(i),a!==void 0&&n.call(this,a)}else e.push!==void 0&&n.call(this,e.push)}chopInput(e,r){return e.substring(r)}updateLastIndex(e,r){e.lastIndex=r}updateTokenEndLineColumnLocation(e,r,n,i,a,o,l){let u,c;r!==void 0&&(u=n===l-1,c=u?-1:0,i===1&&u===!0||(e.endLine=a+c,e.endColumn=o-1+-c))}computeNewColumn(e,r){return e+r}createOffsetOnlyToken(e,r,n,i){return{image:e,startOffset:r,tokenTypeIdx:n,tokenType:i}}createStartOnlyToken(e,r,n,i,a,o){return{image:e,startOffset:r,startLine:a,startColumn:o,tokenTypeIdx:n,tokenType:i}}createFullToken(e,r,n,i,a,o,l){return{image:e,startOffset:r,endOffset:r+l-1,startLine:a,endLine:a,startColumn:o,endColumn:o+l-1,tokenTypeIdx:n,tokenType:i}}addTokenUsingPush(e,r,n){return e.push(n),r}addTokenUsingMemberAccess(e,r,n){return e[r]=n,r++,r}handlePayloadNoCustom(e,r){}handlePayloadWithCustom(e,r){r!==null&&(e.payload=r)}matchWithTest(e,r,n){return e.test(r)===!0?r.substring(n,e.lastIndex):null}matchWithExec(e,r){let n=e.exec(r);return n!==null?n[0]:null}};ue.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";ue.NA=/NOT_APPLICABLE/;function tr(t){return ac(t)?t.LABEL:t.name}s(tr,"tokenLabel");function ac(t){return Ie(t.LABEL)&&t.LABEL!==""}s(ac,"hasTokenLabel");var IC="parent",Th="categories",Rh="label",Ah="group",Eh="push_mode",vh="pop_mode",Ih="longer_alt",Sh="line_breaks",kh="start_chars_hint";function Sr(t){return SC(t)}s(Sr,"createToken");function SC(t){let e=t.pattern,r={};if(r.name=t.name,Ue(e)||(r.PATTERN=e),k(t,IC))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return k(t,Th)&&(r.CATEGORIES=t[Th]),er([r]),k(t,Rh)&&(r.LABEL=t[Rh]),k(t,Ah)&&(r.GROUP=t[Ah]),k(t,vh)&&(r.POP_MODE=t[vh]),k(t,Eh)&&(r.PUSH_MODE=t[Eh]),k(t,Ih)&&(r.LONGER_ALT=t[Ih]),k(t,Sh)&&(r.LINE_BREAKS=t[Sh]),k(t,kh)&&(r.START_CHARS_HINT=t[kh]),r}s(SC,"createTokenInternal");var ot=Sr({name:"EOF",pattern:ue.NA});er([ot]);function rr(t,e,r,n,i,a,o,l){return{image:e,startOffset:r,endOffset:n,startLine:i,endLine:a,startColumn:o,endColumn:l,tokenTypeIdx:t.tokenTypeIdx,tokenType:t}}s(rr,"createTokenInstance");function Os(t,e){return Qt(t,e)}s(Os,"tokenMatcher");var nr={buildMismatchTokenMessage({expected:t,actual:e,previous:r,ruleName:n}){return`Expecting ${ac(t)?`--> ${tr(t)} <--`:`token of type --> ${t.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:t,ruleName:e}){return"Redundant input, expecting EOF but found: "+t.image},buildNoViableAltMessage({expectedPathsPerAlt:t,actual:e,previous:r,customUserDescription:n,ruleName:i}){let a="Expecting: ",l=`
but found: '`+$e(e).image+"'";if(n)return a+n+l;{let u=me(t,(p,m)=>p.concat(m),[]),c=v(u,p=>`[${v(p,m=>tr(m)).join(", ")}]`),d=`one of these possible Token sequences:
${v(c,(p,m)=>`  ${m+1}. ${p}`).join(`
`)}`;return a+d+l}},buildEarlyExitMessage({expectedIterationPaths:t,actual:e,customUserDescription:r,ruleName:n}){let i="Expecting: ",o=`
but found: '`+$e(e).image+"'";if(r)return i+r+o;{let u=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${v(t,c=>`[${v(c,f=>tr(f)).join(",")}]`).join(" ,")}>`;return i+u+o}}};Object.freeze(nr);var Ch={buildRuleNotFoundError(t,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+t.name+"<-"}},Tt={buildDuplicateFoundError(t,e){function r(f){return f instanceof F?f.terminalType.name:f instanceof V?f.nonTerminalName:""}s(r,"getExtraProductionArgument");let n=t.name,i=$e(e),a=i.idx,o=rt(i),l=r(i),u=a>0,c=`->${o}${u?a:""}<- ${l?`with argument: ->${l}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${n}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError(t){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${t.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(t){let e=v(t.prefixPath,i=>tr(i)).join(", "),r=t.alternation.idx===0?"":t.alternation.idx;return`Ambiguous alternatives: <${t.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(t){let e=v(t.prefixPath,i=>tr(i)).join(", "),r=t.alternation.idx===0?"":t.alternation.idx,n=`Ambiguous Alternatives Detected: <${t.ambiguityIndices.join(" ,")}> in <OR${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n},buildEmptyRepetitionError(t){let e=rt(t.repetition);return t.repetition.idx!==0&&(e+=t.repetition.idx),`The repetition <${e}> within Rule <${t.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(t){return"deprecated"},buildEmptyAlternationError(t){return`Ambiguous empty alternative: <${t.emptyChoiceIdx+1}> in <OR${t.alternation.idx}> inside <${t.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(t){return`An Alternation cannot have more than 256 alternatives:
<OR${t.alternation.idx}> inside <${t.topLevelRule.name}> Rule.
 has ${t.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(t){let e=t.topLevelRule.name,r=v(t.leftRecursionPath,a=>a.name),n=`${e} --> ${r.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(t){return"deprecated"},buildDuplicateRuleNameError(t){let e;return t.topLevelRule instanceof Ve?e=t.topLevelRule.name:e=t.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${t.grammarName}<-`}};function Nh(t,e){let r=new oc(t,e);return r.resolveRefs(),r.errors}s(Nh,"resolveGrammar");var oc=class extends ze{static{s(this,"GastRefResolverVisitor")}constructor(e,r){super(),this.nameToTopRule=e,this.errMsgProvider=r,this.errors=[]}resolveRefs(){S(q(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){let r=this.nameToTopRule[e.nonTerminalName];if(r)e.referencedRule=r;else{let n=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:n,type:Se.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}};var lc=class extends Zt{static{s(this,"AbstractNextPossibleTokensWalker")}constructor(e,r){super(),this.topProd=e,this.path=r,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=ee(this.path.ruleStack).reverse(),this.occurrenceStack=ee(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,r=[]){this.found||super.walk(e,r)}walkProdRef(e,r,n){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){let i=r.concat(n);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){D(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}},Fo=class extends lc{static{s(this,"NextAfterTokenWalker")}constructor(e,r){super(e,r),this.path=r,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,r,n){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){let i=r.concat(n),a=new te({definition:i});this.possibleTokTypes=qr(a),this.found=!0}}},Li=class extends Zt{static{s(this,"AbstractNextTerminalAfterProductionWalker")}constructor(e,r){super(),this.topRule=e,this.occurrence=r,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}},Go=class extends Li{static{s(this,"NextTerminalAfterManyWalker")}walkMany(e,r,n){if(e.idx===this.occurrence){let i=$e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,r,n)}},Ls=class extends Li{static{s(this,"NextTerminalAfterManySepWalker")}walkManySep(e,r,n){if(e.idx===this.occurrence){let i=$e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,r,n)}},Uo=class extends Li{static{s(this,"NextTerminalAfterAtLeastOneWalker")}walkAtLeastOne(e,r,n){if(e.idx===this.occurrence){let i=$e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,r,n)}},Ps=class extends Li{static{s(this,"NextTerminalAfterAtLeastOneSepWalker")}walkAtLeastOneSep(e,r,n){if(e.idx===this.occurrence){let i=$e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,r,n)}};function Bo(t,e,r=[]){r=ee(r);let n=[],i=0;function a(l){return l.concat(xe(t,i+1))}s(a,"remainingPathWith");function o(l){let u=Bo(a(l),e,r);return n.concat(u)}for(s(o,"getAlternativesForProd");r.length<e&&i<t.length;){let l=t[i];if(l instanceof te)return o(l.definition);if(l instanceof V)return o(l.definition);if(l instanceof z)n=o(l.definition);else if(l instanceof re){let u=l.definition.concat([new G({definition:l.definition})]);return o(u)}else if(l instanceof ne){let u=[new te({definition:l.definition}),new G({definition:[new F({terminalType:l.separator})].concat(l.definition)})];return o(u)}else if(l instanceof Y){let u=l.definition.concat([new G({definition:[new F({terminalType:l.separator})].concat(l.definition)})]);n=o(u)}else if(l instanceof G){let u=l.definition.concat([new G({definition:l.definition})]);n=o(u)}else{if(l instanceof X)return S(l.definition,u=>{D(u.definition)===!1&&(n=o(u.definition))}),n;if(l instanceof F)r.push(l.terminalType);else throw Error("non exhaustive match")}i++}return n.push({partialPath:r,suffixDef:xe(t,i)}),n}s(Bo,"possiblePathsFrom");function jo(t,e,r,n){let i="EXIT_NONE_TERMINAL",a=[i],o="EXIT_ALTERNATIVE",l=!1,u=e.length,c=u-n-1,f=[],d=[];for(d.push({idx:-1,def:t,ruleStack:[],occurrenceStack:[]});!D(d);){let p=d.pop();if(p===o){l&&Mt(d).idx<=c&&d.pop();continue}let m=p.def,g=p.idx,y=p.ruleStack,A=p.occurrenceStack;if(D(m))continue;let T=m[0];if(T===i){let E={idx:g,def:xe(m),ruleStack:Jt(y),occurrenceStack:Jt(A)};d.push(E)}else if(T instanceof F)if(g<u-1){let E=g+1,R=e[E];if(r(R,T.terminalType)){let O={idx:E,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(O)}}else if(g===u-1)f.push({nextTokenType:T.terminalType,nextTokenOccurrence:T.idx,ruleStack:y,occurrenceStack:A}),l=!0;else throw Error("non exhaustive match");else if(T instanceof V){let E=ee(y);E.push(T.nonTerminalName);let R=ee(A);R.push(T.idx);let O={idx:g,def:T.definition.concat(a,xe(m)),ruleStack:E,occurrenceStack:R};d.push(O)}else if(T instanceof z){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(E),d.push(o);let R={idx:g,def:T.definition.concat(xe(m)),ruleStack:y,occurrenceStack:A};d.push(R)}else if(T instanceof re){let E=new G({definition:T.definition,idx:T.idx}),R=T.definition.concat([E],xe(m)),O={idx:g,def:R,ruleStack:y,occurrenceStack:A};d.push(O)}else if(T instanceof ne){let E=new F({terminalType:T.separator}),R=new G({definition:[E].concat(T.definition),idx:T.idx}),O=T.definition.concat([R],xe(m)),L={idx:g,def:O,ruleStack:y,occurrenceStack:A};d.push(L)}else if(T instanceof Y){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(E),d.push(o);let R=new F({terminalType:T.separator}),O=new G({definition:[R].concat(T.definition),idx:T.idx}),L=T.definition.concat([O],xe(m)),ke={idx:g,def:L,ruleStack:y,occurrenceStack:A};d.push(ke)}else if(T instanceof G){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(E),d.push(o);let R=new G({definition:T.definition,idx:T.idx}),O=T.definition.concat([R],xe(m)),L={idx:g,def:O,ruleStack:y,occurrenceStack:A};d.push(L)}else if(T instanceof X)for(let E=T.definition.length-1;E>=0;E--){let R=T.definition[E],O={idx:g,def:R.definition.concat(xe(m)),ruleStack:y,occurrenceStack:A};d.push(O),d.push(o)}else if(T instanceof te)d.push({idx:g,def:T.definition.concat(xe(m)),ruleStack:y,occurrenceStack:A});else if(T instanceof Ve)d.push(kC(T,g,y,A));else throw Error("non exhaustive match")}return f}s(jo,"nextPossibleTokensAfter");function kC(t,e,r,n){let i=ee(r);i.push(t.name);let a=ee(n);return a.push(1),{idx:e,def:t.definition,ruleStack:i,occurrenceStack:a}}s(kC,"expandTopLevelRule");var ce;(function(t){t[t.OPTION=0]="OPTION",t[t.REPETITION=1]="REPETITION",t[t.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",t[t.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",t[t.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",t[t.ALTERNATION=5]="ALTERNATION"})(ce||(ce={}));function Ms(t){if(t instanceof z||t==="Option")return ce.OPTION;if(t instanceof G||t==="Repetition")return ce.REPETITION;if(t instanceof re||t==="RepetitionMandatory")return ce.REPETITION_MANDATORY;if(t instanceof ne||t==="RepetitionMandatoryWithSeparator")return ce.REPETITION_MANDATORY_WITH_SEPARATOR;if(t instanceof Y||t==="RepetitionWithSeparator")return ce.REPETITION_WITH_SEPARATOR;if(t instanceof X||t==="Alternation")return ce.ALTERNATION;throw Error("non exhaustive match")}s(Ms,"getProdType");function Ko(t){let{occurrence:e,rule:r,prodType:n,maxLookahead:i}=t,a=Ms(n);return a===ce.ALTERNATION?Pi(e,r,i):Mi(e,r,a,i)}s(Ko,"getLookaheadPaths");function wh(t,e,r,n,i,a){let o=Pi(t,e,r),l=Mh(o)?bi:Qt;return a(o,n,l,i)}s(wh,"buildLookaheadFuncForOr");function _h(t,e,r,n,i,a){let o=Mi(t,e,i,r),l=Mh(o)?bi:Qt;return a(o[0],l,n)}s(_h,"buildLookaheadFuncForOptionalProd");function bh(t,e,r,n){let i=t.length,a=Ge(t,o=>Ge(o,l=>l.length===1));if(e)return function(o){let l=v(o,u=>u.GATE);for(let u=0;u<i;u++){let c=t[u],f=c.length,d=l[u];if(!(d!==void 0&&d.call(this)===!1))e:for(let p=0;p<f;p++){let m=c[p],g=m.length;for(let y=0;y<g;y++){let A=this.LA(y+1);if(r(A,m[y])===!1)continue e}return u}}};if(a&&!n){let o=v(t,u=>ye(u)),l=me(o,(u,c,f)=>(S(c,d=>{k(u,d.tokenTypeIdx)||(u[d.tokenTypeIdx]=f),S(d.categoryMatches,p=>{k(u,p)||(u[p]=f)})}),u),{});return function(){let u=this.LA(1);return l[u.tokenTypeIdx]}}else return function(){for(let o=0;o<i;o++){let l=t[o],u=l.length;e:for(let c=0;c<u;c++){let f=l[c],d=f.length;for(let p=0;p<d;p++){let m=this.LA(p+1);if(r(m,f[p])===!1)continue e}return o}}}}s(bh,"buildAlternativesLookAheadFunc");function Oh(t,e,r){let n=Ge(t,a=>a.length===1),i=t.length;if(n&&!r){let a=ye(t);if(a.length===1&&D(a[0].categoryMatches)){let l=a[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===l}}else{let o=me(a,(l,u,c)=>(l[u.tokenTypeIdx]=!0,S(u.categoryMatches,f=>{l[f]=!0}),l),[]);return function(){let l=this.LA(1);return o[l.tokenTypeIdx]===!0}}}else return function(){e:for(let a=0;a<i;a++){let o=t[a],l=o.length;for(let u=0;u<l;u++){let c=this.LA(u+1);if(e(c,o[u])===!1)continue e}return!0}return!1}}s(Oh,"buildSingleAlternativeLookaheadFunction");var cc=class extends Zt{static{s(this,"RestDefinitionFinderWalker")}constructor(e,r,n){super(),this.topProd=e,this.targetOccurrence=r,this.targetProdType=n}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,r,n,i){return e.idx===this.targetOccurrence&&this.targetProdType===r?(this.restDef=n.concat(i),!0):!1}walkOption(e,r,n){this.checkIsTarget(e,ce.OPTION,r,n)||super.walkOption(e,r,n)}walkAtLeastOne(e,r,n){this.checkIsTarget(e,ce.REPETITION_MANDATORY,r,n)||super.walkOption(e,r,n)}walkAtLeastOneSep(e,r,n){this.checkIsTarget(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR,r,n)||super.walkOption(e,r,n)}walkMany(e,r,n){this.checkIsTarget(e,ce.REPETITION,r,n)||super.walkOption(e,r,n)}walkManySep(e,r,n){this.checkIsTarget(e,ce.REPETITION_WITH_SEPARATOR,r,n)||super.walkOption(e,r,n)}},Wo=class extends ze{static{s(this,"InsideDefinitionFinderVisitor")}constructor(e,r,n){super(),this.targetOccurrence=e,this.targetProdType=r,this.targetRef=n,this.result=[]}checkIsTarget(e,r){e.idx===this.targetOccurrence&&this.targetProdType===r&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,ce.OPTION)}visitRepetition(e){this.checkIsTarget(e,ce.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,ce.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,ce.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,ce.ALTERNATION)}};function $h(t){let e=new Array(t);for(let r=0;r<t;r++)e[r]=[];return e}s($h,"initializeArrayOfArrays");function uc(t){let e=[""];for(let r=0;r<t.length;r++){let n=t[r],i=[];for(let a=0;a<e.length;a++){let o=e[a];i.push(o+"_"+n.tokenTypeIdx);for(let l=0;l<n.categoryMatches.length;l++){let u="_"+n.categoryMatches[l];i.push(o+u)}}e=i}return e}s(uc,"pathToHashKeys");function CC(t,e,r){for(let n=0;n<t.length;n++){if(n===r)continue;let i=t[n];for(let a=0;a<e.length;a++){let o=e[a];if(i[o]===!0)return!1}}return!0}s(CC,"isUniquePrefixHash");function Lh(t,e){let r=v(t,o=>Bo([o],1)),n=$h(r.length),i=v(r,o=>{let l={};return S(o,u=>{let c=uc(u.partialPath);S(c,f=>{l[f]=!0})}),l}),a=r;for(let o=1;o<=e;o++){let l=a;a=$h(l.length);for(let u=0;u<l.length;u++){let c=l[u];for(let f=0;f<c.length;f++){let d=c[f].partialPath,p=c[f].suffixDef,m=uc(d);if(CC(i,m,u)||D(p)||d.length===e){let y=n[u];if(Ho(y,d)===!1){y.push(d);for(let A=0;A<m.length;A++){let T=m[A];i[u][T]=!0}}}else{let y=Bo(p,o+1,d);a[u]=a[u].concat(y),S(y,A=>{let T=uc(A.partialPath);S(T,E=>{i[u][E]=!0})})}}}}return n}s(Lh,"lookAheadSequenceFromAlternatives");function Pi(t,e,r,n){let i=new Wo(t,ce.ALTERNATION,n);return e.accept(i),Lh(i.result,r)}s(Pi,"getLookaheadPathsForOr");function Mi(t,e,r,n){let i=new Wo(t,r);e.accept(i);let a=i.result,l=new cc(e,t,r).startWalking(),u=new te({definition:a}),c=new te({definition:l});return Lh([u,c],n)}s(Mi,"getLookaheadPathsForOptionalProd");function Ho(t,e){e:for(let r=0;r<t.length;r++){let n=t[r];if(n.length===e.length){for(let i=0;i<n.length;i++){let a=e[i],o=n[i];if((a===o||o.categoryMatchesMap[a.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}s(Ho,"containsPath");function Ph(t,e){return t.length<e.length&&Ge(t,(r,n)=>{let i=e[n];return r===i||i.categoryMatchesMap[r.tokenTypeIdx]})}s(Ph,"isStrictPrefixOfPath");function Mh(t){return Ge(t,e=>Ge(e,r=>Ge(r,n=>D(n.categoryMatches))))}s(Mh,"areTokenCategoriesNotUsed");function Dh(t){let e=t.lookaheadStrategy.validate({rules:t.rules,tokenTypes:t.tokenTypes,grammarName:t.grammarName});return v(e,r=>Object.assign({type:Se.CUSTOM_LOOKAHEAD_VALIDATION},r))}s(Dh,"validateLookahead");function Fh(t,e,r,n){let i=Oe(t,u=>NC(u,r)),a=LC(t,e,r),o=Oe(t,u=>_C(u,r)),l=Oe(t,u=>wC(u,t,n,r));return i.concat(a,o,l)}s(Fh,"validateGrammar");function NC(t,e){let r=new fc;t.accept(r);let n=r.allProductions,i=Xu(n,$C),a=tt(i,l=>l.length>1);return v(q(a),l=>{let u=$e(l),c=e.buildDuplicateFoundError(t,l),f=rt(u),d={message:c,type:Se.DUPLICATE_PRODUCTIONS,ruleName:t.name,dslName:f,occurrence:u.idx},p=Gh(u);return p&&(d.parameter=p),d})}s(NC,"validateDuplicateProductions");function $C(t){return`${rt(t)}_#_${t.idx}_#_${Gh(t)}`}s($C,"identifyProductionForDuplicates");function Gh(t){return t instanceof F?t.terminalType.name:t instanceof V?t.nonTerminalName:""}s(Gh,"getExtraProductionArgument");var fc=class extends ze{static{s(this,"OccurrenceValidationCollector")}constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}};function wC(t,e,r,n){let i=[];if(me(e,(o,l)=>l.name===t.name?o+1:o,0)>1){let o=n.buildDuplicateRuleNameError({topLevelRule:t,grammarName:r});i.push({message:o,type:Se.DUPLICATE_RULE_NAME,ruleName:t.name})}return i}s(wC,"validateRuleDoesNotAlreadyExist");function Uh(t,e,r){let n=[],i;return oe(e,t)||(i=`Invalid rule override, rule: ->${t}<- cannot be overridden in the grammar: ->${r}<-as it is not defined in any of the super grammars `,n.push({message:i,type:Se.INVALID_RULE_OVERRIDE,ruleName:t})),n}s(Uh,"validateRuleIsOverridden");function pc(t,e,r,n=[]){let i=[],a=Vo(e.definition);if(D(a))return[];{let o=t.name;oe(a,t)&&i.push({message:r.buildLeftRecursionError({topLevelRule:t,leftRecursionPath:n}),type:Se.LEFT_RECURSION,ruleName:o});let u=vr(a,n.concat([t])),c=Oe(u,f=>{let d=ee(n);return d.push(f),pc(t,f,r,d)});return i.concat(c)}}s(pc,"validateNoLeftRecursion");function Vo(t){let e=[];if(D(t))return e;let r=$e(t);if(r instanceof V)e.push(r.referencedRule);else if(r instanceof te||r instanceof z||r instanceof re||r instanceof ne||r instanceof Y||r instanceof G)e=e.concat(Vo(r.definition));else if(r instanceof X)e=ye(v(r.definition,a=>Vo(a.definition)));else if(!(r instanceof F))throw Error("non exhaustive match");let n=zr(r),i=t.length>1;if(n&&i){let a=xe(t);return e.concat(Vo(a))}else return e}s(Vo,"getFirstNoneTerminal");var Ds=class extends ze{static{s(this,"OrCollector")}constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}};function Bh(t,e){let r=new Ds;t.accept(r);let n=r.alternations;return Oe(n,a=>{let o=Jt(a.definition);return Oe(o,(l,u)=>{let c=jo([l],[],Qt,1);return D(c)?[{message:e.buildEmptyAlternationError({topLevelRule:t,alternation:a,emptyChoiceIdx:u}),type:Se.NONE_LAST_EMPTY_ALT,ruleName:t.name,occurrence:a.idx,alternative:u+1}]:[]})})}s(Bh,"validateEmptyOrAlternative");function jh(t,e,r){let n=new Ds;t.accept(n);let i=n.alternations;return i=Ir(i,o=>o.ignoreAmbiguities===!0),Oe(i,o=>{let l=o.idx,u=o.maxLookahead||e,c=Pi(l,t,u,o),f=bC(c,o,t,r),d=OC(c,o,t,r);return f.concat(d)})}s(jh,"validateAmbiguousAlternationAlternatives");var dc=class extends ze{static{s(this,"RepetitionCollector")}constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}};function _C(t,e){let r=new Ds;t.accept(r);let n=r.alternations;return Oe(n,a=>a.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:t,alternation:a}),type:Se.TOO_MANY_ALTS,ruleName:t.name,occurrence:a.idx}]:[])}s(_C,"validateTooManyAlts");function Wh(t,e,r){let n=[];return S(t,i=>{let a=new dc;i.accept(a);let o=a.allProductions;S(o,l=>{let u=Ms(l),c=l.maxLookahead||e,f=l.idx,p=Mi(f,i,u,c)[0];if(D(ye(p))){let m=r.buildEmptyRepetitionError({topLevelRule:i,repetition:l});n.push({message:m,type:Se.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),n}s(Wh,"validateSomeNonEmptyLookaheadPath");function bC(t,e,r,n){let i=[],a=me(t,(l,u,c)=>(e.definition[c].ignoreAmbiguities===!0||S(u,f=>{let d=[c];S(t,(p,m)=>{c!==m&&Ho(p,f)&&e.definition[m].ignoreAmbiguities!==!0&&d.push(m)}),d.length>1&&!Ho(i,f)&&(i.push(f),l.push({alts:d,path:f}))}),l),[]);return v(a,l=>{let u=v(l.alts,f=>f+1);return{message:n.buildAlternationAmbiguityError({topLevelRule:r,alternation:e,ambiguityIndices:u,prefixPath:l.path}),type:Se.AMBIGUOUS_ALTS,ruleName:r.name,occurrence:e.idx,alternatives:l.alts}})}s(bC,"checkAlternativesAmbiguities");function OC(t,e,r,n){let i=me(t,(o,l,u)=>{let c=v(l,f=>({idx:u,path:f}));return o.concat(c)},[]);return Pt(Oe(i,o=>{if(e.definition[o.idx].ignoreAmbiguities===!0)return[];let u=o.idx,c=o.path,f=Ne(i,p=>e.definition[p.idx].ignoreAmbiguities!==!0&&p.idx<u&&Ph(p.path,c));return v(f,p=>{let m=[p.idx+1,u+1],g=e.idx===0?"":e.idx;return{message:n.buildAlternationPrefixAmbiguityError({topLevelRule:r,alternation:e,ambiguityIndices:m,prefixPath:p.path}),type:Se.AMBIGUOUS_PREFIX_ALTS,ruleName:r.name,occurrence:g,alternatives:m}})}))}s(OC,"checkPrefixAlternativesAmbiguities");function LC(t,e,r){let n=[],i=v(e,a=>a.name);return S(t,a=>{let o=a.name;if(oe(i,o)){let l=r.buildNamespaceConflictError(a);n.push({message:l,type:Se.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:o})}}),n}s(LC,"checkTerminalAndNoneTerminalsNameSpace");function Kh(t){let e=Si(t,{errMsgProvider:Ch}),r={};return S(t.rules,n=>{r[n.name]=n}),Nh(r,e.errMsgProvider)}s(Kh,"resolveGrammar");function Hh(t){return t=Si(t,{errMsgProvider:Tt}),Fh(t.rules,t.tokenTypes,t.errMsgProvider,t.grammarName)}s(Hh,"validateGrammar");var Vh="MismatchedTokenException",zh="NoViableAltException",qh="EarlyExitException",Yh="NotAllInputParsedException",Xh=[Vh,zh,qh,Yh];Object.freeze(Xh);function kr(t){return oe(Xh,t.name)}s(kr,"isRecognitionException");var Di=class extends Error{static{s(this,"RecognitionException")}constructor(e,r){super(e),this.token=r,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}},Xr=class extends Di{static{s(this,"MismatchedTokenException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=Vh}},Fs=class extends Di{static{s(this,"NoViableAltException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=zh}},Gs=class extends Di{static{s(this,"NotAllInputParsedException")}constructor(e,r){super(e,r),this.name=Yh}},Us=class extends Di{static{s(this,"EarlyExitException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=qh}};var mc={},gc="InRuleRecoveryException",hc=class extends Error{static{s(this,"InRuleRecoveryException")}constructor(e){super(e),this.name=gc}},zo=class{static{s(this,"Recoverable")}initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=k(e,"recoveryEnabled")?e.recoveryEnabled:qe.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=PC)}getTokenToInsert(e){let r=rr(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return r.isInsertedInRecovery=!0,r}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,r,n,i){let a=this.findReSyncTokenType(),o=this.exportLexerState(),l=[],u=!1,c=this.LA(1),f=this.LA(1),d=s(()=>{let p=this.LA(0),m=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:c,previous:p,ruleName:this.getCurrRuleFullName()}),g=new Xr(m,c,this.LA(0));g.resyncedTokens=Jt(l),this.SAVE_ERROR(g)},"generateErrorMessage");for(;!u;)if(this.tokenMatcher(f,i)){d();return}else if(n.call(this)){d(),e.apply(this,r);return}else this.tokenMatcher(f,a)?u=!0:(f=this.SKIP_TOKEN(),this.addToResyncTokens(f,l));this.importLexerState(o)}shouldInRepetitionRecoveryBeTried(e,r,n){return!(n===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,r)))}getFollowsForInRuleRecovery(e,r){let n=this.getCurrentGrammarPath(e,r);return this.getNextPossibleTokenTypes(n)}tryInRuleRecovery(e,r){if(this.canRecoverWithSingleTokenInsertion(e,r))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){let n=this.SKIP_TOKEN();return this.consumeToken(),n}throw new hc("sad sad panda")}canPerformInRuleRecovery(e,r){return this.canRecoverWithSingleTokenInsertion(e,r)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,r){if(!this.canTokenTypeBeInsertedInRecovery(e)||D(r))return!1;let n=this.LA(1);return Dt(r,a=>this.tokenMatcher(n,a))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){let r=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(r);return oe(n,e)}findReSyncTokenType(){let e=this.flattenFollowSet(),r=this.LA(1),n=2;for(;;){let i=Dt(e,a=>Os(r,a));if(i!==void 0)return i;r=this.LA(n),n++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return mc;let e=this.getLastExplicitRuleShortName(),r=this.getLastExplicitRuleOccurrenceIndex(),n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:r,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){let e=this.RULE_STACK,r=this.RULE_OCCURRENCE_STACK;return v(e,(n,i)=>i===0?mc:{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:r[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){let e=v(this.buildFullFollowKeyStack(),r=>this.getFollowSetFromFollowKey(r));return ye(e)}getFollowSetFromFollowKey(e){if(e===mc)return[ot];let r=e.ruleName+e.idxInCallingRule+bo+e.inRule;return this.resyncFollows[r]}addToResyncTokens(e,r){return this.tokenMatcher(e,ot)||r.push(e),r}reSyncTo(e){let r=[],n=this.LA(1);for(;this.tokenMatcher(n,e)===!1;)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,r);return Jt(r)}attemptInRepetitionRecovery(e,r,n,i,a,o,l){}getCurrentGrammarPath(e,r){let n=this.getHumanReadableRuleStack(),i=ee(this.RULE_OCCURRENCE_STACK);return{ruleStack:n,occurrenceStack:i,lastTok:e,lastTokOccurrence:r}}getHumanReadableRuleStack(){return v(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}};function PC(t,e,r,n,i,a,o){let l=this.getKeyForAutomaticLookahead(n,i),u=this.firstAfterRepMap[l];if(u===void 0){let p=this.getCurrRuleFullName(),m=this.getGAstProductions()[p];u=new a(m,i).startWalking(),this.firstAfterRepMap[l]=u}let c=u.token,f=u.occurrence,d=u.isEndOfRule;this.RULE_STACK.length===1&&d&&c===void 0&&(c=ot,f=1),!(c===void 0||f===void 0)&&this.shouldInRepetitionRecoveryBeTried(c,f,o)&&this.tryInRepetitionRecovery(t,e,r,c)}s(PC,"attemptInRepetitionRecovery");function qo(t,e,r){return r|e|t}s(qo,"getKeyForAutomaticLookahead");var ir=class{static{s(this,"LLkLookaheadStrategy")}constructor(e){var r;this.maxLookahead=(r=e?.maxLookahead)!==null&&r!==void 0?r:qe.maxLookahead}validate(e){let r=this.validateNoLeftRecursion(e.rules);if(D(r)){let n=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),a=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...r,...n,...i,...a]}return r}validateNoLeftRecursion(e){return Oe(e,r=>pc(r,r,Tt))}validateEmptyOrAlternatives(e){return Oe(e,r=>Bh(r,Tt))}validateAmbiguousAlternationAlternatives(e,r){return Oe(e,n=>jh(n,r,Tt))}validateSomeNonEmptyLookaheadPath(e,r){return Wh(e,r,Tt)}buildLookaheadForAlternation(e){return wh(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,bh)}buildLookaheadForOptional(e){return _h(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,Ms(e.prodType),Oh)}};var Xo=class{static{s(this,"LooksAhead")}initLooksAhead(e){this.dynamicTokensEnabled=k(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:qe.dynamicTokensEnabled,this.maxLookahead=k(e,"maxLookahead")?e.maxLookahead:qe.maxLookahead,this.lookaheadStrategy=k(e,"lookaheadStrategy")?e.lookaheadStrategy:new ir({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){S(e,r=>{this.TRACE_INIT(`${r.name} Rule Lookahead`,()=>{let{alternation:n,repetition:i,option:a,repetitionMandatory:o,repetitionMandatoryWithSeparator:l,repetitionWithSeparator:u}=MC(r);S(n,c=>{let f=c.idx===0?"":c.idx;this.TRACE_INIT(`${rt(c)}${f}`,()=>{let d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:r,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),p=qo(this.fullRuleNameToShort[r.name],256,c.idx);this.setLaFuncCache(p,d)})}),S(i,c=>{this.computeLookaheadFunc(r,c.idx,768,"Repetition",c.maxLookahead,rt(c))}),S(a,c=>{this.computeLookaheadFunc(r,c.idx,512,"Option",c.maxLookahead,rt(c))}),S(o,c=>{this.computeLookaheadFunc(r,c.idx,1024,"RepetitionMandatory",c.maxLookahead,rt(c))}),S(l,c=>{this.computeLookaheadFunc(r,c.idx,1536,"RepetitionMandatoryWithSeparator",c.maxLookahead,rt(c))}),S(u,c=>{this.computeLookaheadFunc(r,c.idx,1280,"RepetitionWithSeparator",c.maxLookahead,rt(c))})})})}computeLookaheadFunc(e,r,n,i,a,o){this.TRACE_INIT(`${o}${r===0?"":r}`,()=>{let l=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:r,rule:e,maxLookahead:a||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),u=qo(this.fullRuleNameToShort[e.name],n,r);this.setLaFuncCache(u,l)})}getKeyForAutomaticLookahead(e,r){let n=this.getLastExplicitRuleShortName();return qo(n,e,r)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,r){this.lookAheadFuncsCache.set(e,r)}},yc=class extends ze{static{s(this,"DslMethodsCollectorVisitor")}constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}},Yo=new yc;function MC(t){Yo.reset(),t.accept(Yo);let e=Yo.dslMethods;return Yo.reset(),e}s(MC,"collectMethods");function Rc(t,e){isNaN(t.startOffset)===!0?(t.startOffset=e.startOffset,t.endOffset=e.endOffset):t.endOffset<e.endOffset&&(t.endOffset=e.endOffset)}s(Rc,"setNodeLocationOnlyOffset");function Ac(t,e){isNaN(t.startOffset)===!0?(t.startOffset=e.startOffset,t.startColumn=e.startColumn,t.startLine=e.startLine,t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine):t.endOffset<e.endOffset&&(t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine)}s(Ac,"setNodeLocationFull");function Jh(t,e,r){t.children[r]===void 0?t.children[r]=[e]:t.children[r].push(e)}s(Jh,"addTerminalToCst");function Zh(t,e,r){t.children[e]===void 0?t.children[e]=[r]:t.children[e].push(r)}s(Zh,"addNoneTerminalToCst");var DC="name";function Ec(t,e){Object.defineProperty(t,DC,{enumerable:!1,configurable:!0,writable:!1,value:e})}s(Ec,"defineNameProp");function FC(t,e){let r=Z(t),n=r.length;for(let i=0;i<n;i++){let a=r[i],o=t[a],l=o.length;for(let u=0;u<l;u++){let c=o[u];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}s(FC,"defaultVisit");function Qh(t,e){let r=s(function(){},"derivedConstructor");Ec(r,t+"BaseSemantics");let n={visit:s(function(i,a){if(N(i)&&(i=i[0]),!Ue(i))return this[i.name](i.children,a)},"visit"),validateVisitor:s(function(){let i=GC(this,e);if(!D(i)){let a=v(i,o=>o.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${a.join(`

`).replace(/\n/g,`
	`)}`)}},"validateVisitor")};return r.prototype=n,r.prototype.constructor=r,r._RULE_NAMES=e,r}s(Qh,"createBaseSemanticVisitorConstructor");function eg(t,e,r){let n=s(function(){},"derivedConstructor");Ec(n,t+"BaseSemanticsWithDefaults");let i=Object.create(r.prototype);return S(e,a=>{i[a]=FC}),n.prototype=i,n.prototype.constructor=n,n}s(eg,"createBaseVisitorConstructorWithDefaults");var vc;(function(t){t[t.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",t[t.MISSING_METHOD=1]="MISSING_METHOD"})(vc||(vc={}));function GC(t,e){return UC(t,e)}s(GC,"validateVisitor");function UC(t,e){let r=Ne(e,i=>He(t[i])===!1),n=v(r,i=>({msg:`Missing visitor method: <${i}> on ${t.constructor.name} CST Visitor.`,type:vc.MISSING_METHOD,methodName:i}));return Pt(n)}s(UC,"validateMissingCstMethods");var el=class{static{s(this,"TreeBuilder")}initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=k(e,"nodeLocationTracking")?e.nodeLocationTracking:qe.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=pe,this.cstFinallyStateUpdate=pe,this.cstPostTerminal=pe,this.cstPostNonTerminal=pe,this.cstPostRule=pe;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Ac,this.setNodeLocationFromNode=Ac,this.cstPostRule=pe,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Rc,this.setNodeLocationFromNode=Rc,this.cstPostRule=pe,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=pe,this.setInitialNodeLocation=pe;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){let r=this.LA(1);e.location={startOffset:r.startOffset,startLine:r.startLine,startColumn:r.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){let r={name:e,children:Object.create(null)};this.setInitialNodeLocation(r),this.CST_STACK.push(r)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){let r=this.LA(0),n=e.location;n.startOffset<=r.startOffset?(n.endOffset=r.endOffset,n.endLine=r.endLine,n.endColumn=r.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)}cstPostRuleOnlyOffset(e){let r=this.LA(0),n=e.location;n.startOffset<=r.startOffset?n.endOffset=r.endOffset:n.startOffset=NaN}cstPostTerminal(e,r){let n=this.CST_STACK[this.CST_STACK.length-1];Jh(n,r,e),this.setNodeLocationFromToken(n.location,r)}cstPostNonTerminal(e,r){let n=this.CST_STACK[this.CST_STACK.length-1];Zh(n,r,e),this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if(Ue(this.baseCstVisitorConstructor)){let e=Qh(this.className,Z(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(Ue(this.baseCstVisitorWithDefaultsConstructor)){let e=eg(this.className,Z(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){let e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}};var tl=class{static{s(this,"LexerAdapter")}initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):Fi}LA(e){let r=this.currIdx+e;return r<0||this.tokVectorLength<=r?Fi:this.tokVector[r]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}};var rl=class{static{s(this,"RecognizerApi")}ACTION(e){return e.call(this)}consume(e,r,n){return this.consumeInternal(r,e,n)}subrule(e,r,n){return this.subruleInternal(r,e,n)}option(e,r){return this.optionInternal(r,e)}or(e,r){return this.orInternal(r,e)}many(e,r){return this.manyInternal(e,r)}atLeastOne(e,r){return this.atLeastOneInternal(e,r)}CONSUME(e,r){return this.consumeInternal(e,0,r)}CONSUME1(e,r){return this.consumeInternal(e,1,r)}CONSUME2(e,r){return this.consumeInternal(e,2,r)}CONSUME3(e,r){return this.consumeInternal(e,3,r)}CONSUME4(e,r){return this.consumeInternal(e,4,r)}CONSUME5(e,r){return this.consumeInternal(e,5,r)}CONSUME6(e,r){return this.consumeInternal(e,6,r)}CONSUME7(e,r){return this.consumeInternal(e,7,r)}CONSUME8(e,r){return this.consumeInternal(e,8,r)}CONSUME9(e,r){return this.consumeInternal(e,9,r)}SUBRULE(e,r){return this.subruleInternal(e,0,r)}SUBRULE1(e,r){return this.subruleInternal(e,1,r)}SUBRULE2(e,r){return this.subruleInternal(e,2,r)}SUBRULE3(e,r){return this.subruleInternal(e,3,r)}SUBRULE4(e,r){return this.subruleInternal(e,4,r)}SUBRULE5(e,r){return this.subruleInternal(e,5,r)}SUBRULE6(e,r){return this.subruleInternal(e,6,r)}SUBRULE7(e,r){return this.subruleInternal(e,7,r)}SUBRULE8(e,r){return this.subruleInternal(e,8,r)}SUBRULE9(e,r){return this.subruleInternal(e,9,r)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,r,n=Gi){if(oe(this.definedRulesNames,e)){let o={message:Tt.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:Se.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(o)}this.definedRulesNames.push(e);let i=this.defineRule(e,r,n);return this[e]=i,i}OVERRIDE_RULE(e,r,n=Gi){let i=Uh(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);let a=this.defineRule(e,r,n);return this[e]=a,a}BACKTRACK(e,r){return function(){this.isBackTrackingStack.push(1);let n=this.saveRecogState();try{return e.apply(this,r),!0}catch(i){if(kr(i))return!1;throw i}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return _o(q(this.gastProductionsCache))}};var nl=class{static{s(this,"RecognizerEngine")}initRecognizerEngine(e,r){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=bi,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},k(r,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(N(e)){if(D(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(N(e))this.tokensMap=me(e,(a,o)=>(a[o.name]=o,a),{});else if(k(e,"modes")&&Ge(ye(q(e.modes)),xh)){let a=ye(q(e.modes)),o=ki(a);this.tokensMap=me(o,(l,u)=>(l[u.name]=u,l),{})}else if(de(e))this.tokensMap=ee(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=ot;let n=k(e,"modes")?ye(q(e.modes)):q(e),i=Ge(n,a=>D(a.categoryMatches));this.tokenMatcher=i?bi:Qt,er(q(this.tokensMap))}defineRule(e,r,n){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);let i=k(n,"resyncEnabled")?n.resyncEnabled:Gi.resyncEnabled,a=k(n,"recoveryValueFunc")?n.recoveryValueFunc:Gi.recoveryValueFunc,o=this.ruleShortNameIdx<<12;this.ruleShortNameIdx++,this.shortRuleNameToFull[o]=e,this.fullRuleNameToShort[e]=o;let l;return this.outputCst===!0?l=s(function(...f){try{this.ruleInvocationStateUpdate(o,e,this.subruleIdx),r.apply(this,f);let d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,a)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTry"):l=s(function(...f){try{return this.ruleInvocationStateUpdate(o,e,this.subruleIdx),r.apply(this,f)}catch(d){return this.invokeRuleCatch(d,i,a)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTryCst"),Object.assign(l,{ruleName:e,originalGrammarAction:r})}invokeRuleCatch(e,r,n){let i=this.RULE_STACK.length===1,a=r&&!this.isBackTracking()&&this.recoveryEnabled;if(kr(e)){let o=e;if(a){let l=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(l))if(o.resyncedTokens=this.reSyncTo(l),this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];return u.recoveredNode=!0,u}else return n(e);else{if(this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];u.recoveredNode=!0,o.partialCstResult=u}throw o}}else{if(i)return this.moveToTerminatedState(),n(e);throw o}}else throw e}optionInternal(e,r){let n=this.getKeyForAutomaticLookahead(512,r);return this.optionInternalLogic(e,r,n)}optionInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof e!="function"){a=e.DEF;let o=e.GATE;if(o!==void 0){let l=i;i=s(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else a=e;if(i.call(this)===!0)return a.call(this)}atLeastOneInternal(e,r){let n=this.getKeyForAutomaticLookahead(1024,e);return this.atLeastOneInternalLogic(e,r,n)}atLeastOneInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof r!="function"){a=r.DEF;let o=r.GATE;if(o!==void 0){let l=i;i=s(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else a=r;if(i.call(this)===!0){let o=this.doSingleRepetition(a);for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(a)}else throw this.raiseEarlyExitException(e,ce.REPETITION_MANDATORY,r.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,r],i,1024,e,Uo)}atLeastOneSepFirstInternal(e,r){let n=this.getKeyForAutomaticLookahead(1536,e);this.atLeastOneSepFirstInternalLogic(e,r,n)}atLeastOneSepFirstInternalLogic(e,r,n){let i=r.DEF,a=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=s(()=>this.tokenMatcher(this.LA(1),a),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),a)===!0;)this.CONSUME(a),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,a,l,i,Ps],l,1536,e,Ps)}else throw this.raiseEarlyExitException(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR,r.ERR_MSG)}manyInternal(e,r){let n=this.getKeyForAutomaticLookahead(768,e);return this.manyInternalLogic(e,r,n)}manyInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof r!="function"){a=r.DEF;let l=r.GATE;if(l!==void 0){let u=i;i=s(()=>l.call(this)&&u.call(this),"lookaheadFunction")}}else a=r;let o=!0;for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(a);this.attemptInRepetitionRecovery(this.manyInternal,[e,r],i,768,e,Go,o)}manySepFirstInternal(e,r){let n=this.getKeyForAutomaticLookahead(1280,e);this.manySepFirstInternalLogic(e,r,n)}manySepFirstInternalLogic(e,r,n){let i=r.DEF,a=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=s(()=>this.tokenMatcher(this.LA(1),a),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),a)===!0;)this.CONSUME(a),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,a,l,i,Ls],l,1280,e,Ls)}}repetitionSepSecondInternal(e,r,n,i,a){for(;n();)this.CONSUME(r),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,r,n,i,a],n,1536,e,a)}doSingleRepetition(e){let r=this.getLexerPosition();return e.call(this),this.getLexerPosition()>r}orInternal(e,r){let n=this.getKeyForAutomaticLookahead(256,r),i=N(e)?e:e.DEF,o=this.getLaFuncFromCache(n).call(this,i);if(o!==void 0)return i[o].ALT.call(this);this.raiseNoAltException(r,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){let e=this.LA(1),r=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Gs(r,e))}}subruleInternal(e,r,n){let i;try{let a=n!==void 0?n.ARGS:void 0;return this.subruleIdx=r,i=e.apply(this,a),this.cstPostNonTerminal(i,n!==void 0&&n.LABEL!==void 0?n.LABEL:e.ruleName),i}catch(a){throw this.subruleInternalError(a,n,e.ruleName)}}subruleInternalError(e,r,n){throw kr(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,r!==void 0&&r.LABEL!==void 0?r.LABEL:n),delete e.partialCstResult),e}consumeInternal(e,r,n){let i;try{let a=this.LA(1);this.tokenMatcher(a,e)===!0?(this.consumeToken(),i=a):this.consumeInternalError(e,a,n)}catch(a){i=this.consumeInternalRecovery(e,r,a)}return this.cstPostTerminal(n!==void 0&&n.LABEL!==void 0?n.LABEL:e.name,i),i}consumeInternalError(e,r,n){let i,a=this.LA(0);throw n!==void 0&&n.ERR_MSG?i=n.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:r,previous:a,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Xr(i,r,a))}consumeInternalRecovery(e,r,n){if(this.recoveryEnabled&&n.name==="MismatchedTokenException"&&!this.isBackTracking()){let i=this.getFollowsForInRuleRecovery(e,r);try{return this.tryInRuleRecovery(e,i)}catch(a){throw a.name===gc?n:a}}else throw n}saveRecogState(){let e=this.errors,r=ee(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:r,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,r,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(r)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){let e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),ot)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}};var il=class{static{s(this,"ErrorHandler")}initErrorHandler(e){this._errors=[],this.errorMessageProvider=k(e,"errorMessageProvider")?e.errorMessageProvider:qe.errorMessageProvider}SAVE_ERROR(e){if(kr(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:ee(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return ee(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,r,n){let i=this.getCurrRuleFullName(),a=this.getGAstProductions()[i],l=Mi(e,a,r,this.maxLookahead)[0],u=[];for(let f=1;f<=this.maxLookahead;f++)u.push(this.LA(f));let c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:l,actual:u,previous:this.LA(0),customUserDescription:n,ruleName:i});throw this.SAVE_ERROR(new Us(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,r){let n=this.getCurrRuleFullName(),i=this.getGAstProductions()[n],a=Pi(e,i,this.maxLookahead),o=[];for(let c=1;c<=this.maxLookahead;c++)o.push(this.LA(c));let l=this.LA(0),u=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:a,actual:o,previous:l,customUserDescription:r,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Fs(u,this.LA(1),l))}};var sl=class{static{s(this,"ContentAssist")}initContentAssist(){}computeContentAssist(e,r){let n=this.gastProductionsCache[e];if(Ue(n))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return jo([n],r,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){let r=$e(e.ruleStack),i=this.getGAstProductions()[r];return new Fo(i,e).startWalking()}};var ll={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(ll);var tg=!0,rg=Math.pow(2,8)-1,ig=Sr({name:"RECORDING_PHASE_TOKEN",pattern:ue.NA});er([ig]);var sg=rr(ig,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(sg);var jC={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}},al=class{static{s(this,"GastRecorder")}initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){let r=e>0?e:"";this[`CONSUME${r}`]=function(n,i){return this.consumeInternalRecord(n,e,i)},this[`SUBRULE${r}`]=function(n,i){return this.subruleInternalRecord(n,e,i)},this[`OPTION${r}`]=function(n){return this.optionInternalRecord(n,e)},this[`OR${r}`]=function(n){return this.orInternalRecord(n,e)},this[`MANY${r}`]=function(n){this.manyInternalRecord(e,n)},this[`MANY_SEP${r}`]=function(n){this.manySepFirstInternalRecord(e,n)},this[`AT_LEAST_ONE${r}`]=function(n){this.atLeastOneInternalRecord(e,n)},this[`AT_LEAST_ONE_SEP${r}`]=function(n){this.atLeastOneSepFirstInternalRecord(e,n)}}this.consume=function(e,r,n){return this.consumeInternalRecord(r,e,n)},this.subrule=function(e,r,n){return this.subruleInternalRecord(r,e,n)},this.option=function(e,r){return this.optionInternalRecord(r,e)},this.or=function(e,r){return this.orInternalRecord(r,e)},this.many=function(e,r){this.manyInternalRecord(e,r)},this.atLeastOne=function(e,r){this.atLeastOneInternalRecord(e,r)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{let e=this;for(let r=0;r<10;r++){let n=r>0?r:"";delete e[`CONSUME${n}`],delete e[`SUBRULE${n}`],delete e[`OPTION${n}`],delete e[`OR${n}`],delete e[`MANY${n}`],delete e[`MANY_SEP${n}`],delete e[`AT_LEAST_ONE${n}`],delete e[`AT_LEAST_ONE_SEP${n}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,r){return()=>!0}LA_RECORD(e){return Fi}topLevelRuleRecord(e,r){try{let n=new Ve({definition:[],name:e});return n.name=e,this.recordingProdStack.push(n),r.call(this),this.recordingProdStack.pop(),n}catch(n){if(n.KNOWN_RECORDER_ERROR!==!0)try{n.message=n.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw n}throw n}}optionInternalRecord(e,r){return js.call(this,z,e,r)}atLeastOneInternalRecord(e,r){js.call(this,re,r,e)}atLeastOneSepFirstInternalRecord(e,r){js.call(this,ne,r,e,tg)}manyInternalRecord(e,r){js.call(this,G,r,e)}manySepFirstInternalRecord(e,r){js.call(this,Y,r,e,tg)}orInternalRecord(e,r){return WC.call(this,e,r)}subruleInternalRecord(e,r,n){if(ol(r),!e||k(e,"ruleName")===!1){let l=new Error(`<SUBRULE${ng(r)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw l.KNOWN_RECORDER_ERROR=!0,l}let i=Mt(this.recordingProdStack),a=e.ruleName,o=new V({idx:r,nonTerminalName:a,label:n?.LABEL,referencedRule:void 0});return i.definition.push(o),this.outputCst?jC:ll}consumeInternalRecord(e,r,n){if(ol(r),!sc(e)){let o=new Error(`<CONSUME${ng(r)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}let i=Mt(this.recordingProdStack),a=new F({idx:r,terminalType:e,label:n?.LABEL});return i.definition.push(a),sg}};function js(t,e,r,n=!1){ol(r);let i=Mt(this.recordingProdStack),a=He(e)?e:e.DEF,o=new t({definition:[],idx:r});return n&&(o.separator=e.SEP),k(e,"MAX_LOOKAHEAD")&&(o.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(o),a.call(this),i.definition.push(o),this.recordingProdStack.pop(),ll}s(js,"recordProd");function WC(t,e){ol(e);let r=Mt(this.recordingProdStack),n=N(t)===!1,i=n===!1?t:t.DEF,a=new X({definition:[],idx:e,ignoreAmbiguities:n&&t.IGNORE_AMBIGUITIES===!0});k(t,"MAX_LOOKAHEAD")&&(a.maxLookahead=t.MAX_LOOKAHEAD);let o=Cs(i,l=>He(l.GATE));return a.hasPredicates=o,r.definition.push(a),S(i,l=>{let u=new te({definition:[]});a.definition.push(u),k(l,"IGNORE_AMBIGUITIES")?u.ignoreAmbiguities=l.IGNORE_AMBIGUITIES:k(l,"GATE")&&(u.ignoreAmbiguities=!0),this.recordingProdStack.push(u),l.ALT.call(this),this.recordingProdStack.pop()}),ll}s(WC,"recordOrProd");function ng(t){return t===0?"":`${t}`}s(ng,"getIdxSuffix");function ol(t){if(t<0||t>rg){let e=new Error(`Invalid DSL Method idx value: <${t}>
	Idx value must be a none negative value smaller than ${rg+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}s(ol,"assertMethodIdxIsValid");var ul=class{static{s(this,"PerformanceTracer")}initPerformanceTracer(e){if(k(e,"traceInitPerf")){let r=e.traceInitPerf,n=typeof r=="number";this.traceInitMaxIdent=n?r:1/0,this.traceInitPerf=n?r>0:r}else this.traceInitMaxIdent=0,this.traceInitPerf=qe.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,r){if(this.traceInitPerf===!0){this.traceInitIndent++;let n=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--> <${e}>`);let{time:i,value:a}=$s(r),o=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&o(`${n}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,a}else return r()}};function ag(t,e){e.forEach(r=>{let n=r.prototype;Object.getOwnPropertyNames(n).forEach(i=>{if(i==="constructor")return;let a=Object.getOwnPropertyDescriptor(n,i);a&&(a.get||a.set)?Object.defineProperty(t.prototype,i,a):t.prototype[i]=r.prototype[i]})})}s(ag,"applyMixins");var Fi=rr(ot,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(Fi);var qe=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:nr,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Gi=Object.freeze({recoveryValueFunc:s(()=>{},"recoveryValueFunc"),resyncEnabled:!0}),Se;(function(t){t[t.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",t[t.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",t[t.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",t[t.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",t[t.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",t[t.LEFT_RECURSION=5]="LEFT_RECURSION",t[t.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",t[t.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",t[t.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",t[t.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",t[t.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",t[t.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",t[t.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",t[t.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(Se||(Se={}));function cl(t=void 0){return function(){return t}}s(cl,"EMPTY_ALT");var Ws=class t{static{s(this,"Parser")}static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;let r=this.className;this.TRACE_INIT("toFastProps",()=>{ws(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),S(this.definedRulesNames,i=>{let o=this[i].originalGrammarAction,l;this.TRACE_INIT(`${i} Rule`,()=>{l=this.topLevelRuleRecord(i,o)}),this.gastProductionsCache[i]=l})}finally{this.disableRecording()}});let n=[];if(this.TRACE_INIT("Grammar Resolving",()=>{n=Kh({rules:q(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(n)}),this.TRACE_INIT("Grammar Validations",()=>{if(D(n)&&this.skipValidations===!1){let i=Hh({rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),errMsgProvider:Tt,grammarName:r}),a=Dh({lookaheadStrategy:this.lookaheadStrategy,rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),grammarName:r});this.definitionErrors=this.definitionErrors.concat(i,a)}}),D(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{let i=Zm(q(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,a;(a=(i=this.lookaheadStrategy).initialize)===null||a===void 0||a.call(i,{rules:q(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(q(this.gastProductionsCache))})),!t.DEFER_DEFINITION_ERRORS_HANDLING&&!D(this.definitionErrors))throw e=v(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,r){this.definitionErrors=[],this.selfAnalysisDone=!1;let n=this;if(n.initErrorHandler(r),n.initLexerAdapter(),n.initLooksAhead(r),n.initRecognizerEngine(e,r),n.initRecoverable(r),n.initTreeBuilder(r),n.initContentAssist(),n.initGastRecorder(r),n.initPerformanceTracer(r),k(r,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=k(r,"skipValidations")?r.skipValidations:qe.skipValidations}};Ws.DEFER_DEFINITION_ERRORS_HANDLING=!1;ag(Ws,[zo,Xo,el,tl,nl,rl,il,sl,al,ul]);var Ks=class extends Ws{static{s(this,"EmbeddedActionsParser")}constructor(e,r=qe){let n=ee(r);n.outputCst=!1,super(e,n)}};function Jr(t,e,r){return`${t.name}_${e}_${r}`}s(Jr,"buildATNKey");var Cr=1,HC=2,og=4,lg=5;var ji=7,VC=8,zC=9,qC=10,YC=11,ug=12,Hs=class{static{s(this,"AbstractTransition")}constructor(e){this.target=e}isEpsilon(){return!1}},Ui=class extends Hs{static{s(this,"AtomTransition")}constructor(e,r){super(e),this.tokenType=r}},Vs=class extends Hs{static{s(this,"EpsilonTransition")}constructor(e){super(e)}isEpsilon(){return!0}},Bi=class extends Hs{static{s(this,"RuleTransition")}constructor(e,r,n){super(e),this.rule=r,this.followState=n}isEpsilon(){return!0}};function cg(t){let e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};XC(e,t);let r=t.length;for(let n=0;n<r;n++){let i=t[n],a=Zr(e,i,i);a!==void 0&&oN(e,i,a)}return e}s(cg,"createATN");function XC(t,e){let r=e.length;for(let n=0;n<r;n++){let i=e[n],a=we(t,i,void 0,{type:HC}),o=we(t,i,void 0,{type:ji});a.stop=o,t.ruleToStartState.set(i,a),t.ruleToStopState.set(i,o)}}s(XC,"createRuleStartAndStopATNStates");function fg(t,e,r){return r instanceof F?Sc(t,e,r.terminalType,r):r instanceof V?aN(t,e,r):r instanceof X?tN(t,e,r):r instanceof z?rN(t,e,r):r instanceof G?JC(t,e,r):r instanceof Y?ZC(t,e,r):r instanceof re?QC(t,e,r):r instanceof ne?eN(t,e,r):Zr(t,e,r)}s(fg,"atom");function JC(t,e,r){let n=we(t,e,r,{type:lg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return pg(t,e,r,i)}s(JC,"repetition");function ZC(t,e,r){let n=we(t,e,r,{type:lg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r)),a=Sc(t,e,r.separator,r);return pg(t,e,r,i,a)}s(ZC,"repetitionSep");function QC(t,e,r){let n=we(t,e,r,{type:og});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return dg(t,e,r,i)}s(QC,"repetitionMandatory");function eN(t,e,r){let n=we(t,e,r,{type:og});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r)),a=Sc(t,e,r.separator,r);return dg(t,e,r,i,a)}s(eN,"repetitionMandatorySep");function tN(t,e,r){let n=we(t,e,r,{type:Cr});Nr(t,n);let i=v(r.definition,o=>fg(t,e,o));return Wi(t,e,n,r,...i)}s(tN,"alternation");function rN(t,e,r){let n=we(t,e,r,{type:Cr});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return nN(t,e,r,i)}s(rN,"option");function Zr(t,e,r){let n=Ne(v(r.definition,i=>fg(t,e,i)),i=>i!==void 0);return n.length===1?n[0]:n.length===0?void 0:sN(t,n)}s(Zr,"block");function dg(t,e,r,n,i){let a=n.left,o=n.right,l=we(t,e,r,{type:YC});Nr(t,l);let u=we(t,e,r,{type:ug});return a.loopback=l,u.loopback=l,t.decisionMap[Jr(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",r.idx)]=l,ve(o,l),i===void 0?(ve(l,a),ve(l,u)):(ve(l,u),ve(l,i.left),ve(i.right,a)),{left:a,right:u}}s(dg,"plus");function pg(t,e,r,n,i){let a=n.left,o=n.right,l=we(t,e,r,{type:qC});Nr(t,l);let u=we(t,e,r,{type:ug}),c=we(t,e,r,{type:zC});return l.loopback=c,u.loopback=c,ve(l,a),ve(l,u),ve(o,c),i!==void 0?(ve(c,u),ve(c,i.left),ve(i.right,a)):ve(c,l),t.decisionMap[Jr(e,i?"RepetitionWithSeparator":"Repetition",r.idx)]=l,{left:l,right:u}}s(pg,"star");function nN(t,e,r,n){let i=n.left,a=n.right;return ve(i,a),t.decisionMap[Jr(e,"Option",r.idx)]=i,n}s(nN,"optional");function Nr(t,e){return t.decisionStates.push(e),e.decision=t.decisionStates.length-1,e.decision}s(Nr,"defineDecisionState");function Wi(t,e,r,n,...i){let a=we(t,e,n,{type:VC,start:r});r.end=a;for(let l of i)l!==void 0?(ve(r,l.left),ve(l.right,a)):ve(r,a);let o={left:r,right:a};return t.decisionMap[Jr(e,iN(n),n.idx)]=r,o}s(Wi,"makeAlts");function iN(t){if(t instanceof X)return"Alternation";if(t instanceof z)return"Option";if(t instanceof G)return"Repetition";if(t instanceof Y)return"RepetitionWithSeparator";if(t instanceof re)return"RepetitionMandatory";if(t instanceof ne)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}s(iN,"getProdType");function sN(t,e){let r=e.length;for(let a=0;a<r-1;a++){let o=e[a],l;o.left.transitions.length===1&&(l=o.left.transitions[0]);let u=l instanceof Bi,c=l,f=e[a+1].left;o.left.type===Cr&&o.right.type===Cr&&l!==void 0&&(u&&c.followState===o.right||l.target===o.right)?(u?c.followState=f:l.target=f,lN(t,o.right)):ve(o.right,f)}let n=e[0],i=e[r-1];return{left:n.left,right:i.right}}s(sN,"makeBlock");function Sc(t,e,r,n){let i=we(t,e,n,{type:Cr}),a=we(t,e,n,{type:Cr});return kc(i,new Ui(a,r)),{left:i,right:a}}s(Sc,"tokenRef");function aN(t,e,r){let n=r.referencedRule,i=t.ruleToStartState.get(n),a=we(t,e,r,{type:Cr}),o=we(t,e,r,{type:Cr}),l=new Bi(i,n,o);return kc(a,l),{left:a,right:o}}s(aN,"ruleRef");function oN(t,e,r){let n=t.ruleToStartState.get(e);ve(n,r.left);let i=t.ruleToStopState.get(e);return ve(r.right,i),{left:n,right:i}}s(oN,"buildRuleHandle");function ve(t,e){let r=new Vs(e);kc(t,r)}s(ve,"epsilon");function we(t,e,r,n){let i=Object.assign({atn:t,production:r,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:t.states.length},n);return t.states.push(i),i}s(we,"newState");function kc(t,e){t.transitions.length===0&&(t.epsilonOnlyTransitions=e.isEpsilon()),t.transitions.push(e)}s(kc,"addTransition");function lN(t,e){t.states.splice(t.states.indexOf(e),1)}s(lN,"removeState");var zs={},Ki=class{static{s(this,"ATNConfigSet")}constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){let r=Cc(e);r in this.map||(this.map[r]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return v(this.configs,e=>e.alt)}get key(){let e="";for(let r in this.map)e+=r+":";return e}};function Cc(t,e=!0){return`${e?`a${t.alt}`:""}s${t.state.stateNumber}:${t.stack.map(r=>r.stateNumber.toString()).join("_")}`}s(Cc,"getATNConfigKey");function uN(t,e){let r={};return n=>{let i=n.toString(),a=r[i];return a!==void 0||(a={atnStartState:t,decision:e,states:{}},r[i]=a),a}}s(uN,"createDFACache");var fl=class{static{s(this,"PredicateSet")}constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,r){this.predicates[e]=r}toString(){let e="",r=this.predicates.length;for(let n=0;n<r;n++)e+=this.predicates[n]===!0?"1":"0";return e}},mg=new fl,qs=class extends ir{static{s(this,"LLStarLookaheadStrategy")}constructor(e){var r;super(),this.logging=(r=e?.logging)!==null&&r!==void 0?r:n=>console.log(n)}initialize(e){this.atn=cg(e.rules),this.dfas=cN(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){let{prodOccurrence:r,rule:n,hasPredicates:i,dynamicTokensEnabled:a}=e,o=this.dfas,l=this.logging,u=Jr(n,"Alternation",r),f=this.atn.decisionMap[u].decision,d=v(Ko({maxLookahead:1,occurrence:r,prodType:"Alternation",rule:n}),p=>v(p,m=>m[0]));if(hg(d,!1)&&!a){let p=me(d,(m,g,y)=>(S(g,A=>{A&&(m[A.tokenTypeIdx]=y,S(A.categoryMatches,T=>{m[T]=y}))}),m),{});return i?function(m){var g;let y=this.LA(1),A=p[y.tokenTypeIdx];if(m!==void 0&&A!==void 0){let T=(g=m[A])===null||g===void 0?void 0:g.GATE;if(T!==void 0&&T.call(this)===!1)return}return A}:function(){let m=this.LA(1);return p[m.tokenTypeIdx]}}else return i?function(p){let m=new fl,g=p===void 0?0:p.length;for(let A=0;A<g;A++){let T=p?.[A].GATE;m.set(A,T===void 0||T.call(this))}let y=Nc.call(this,o,f,m,l);return typeof y=="number"?y:void 0}:function(){let p=Nc.call(this,o,f,mg,l);return typeof p=="number"?p:void 0}}buildLookaheadForOptional(e){let{prodOccurrence:r,rule:n,prodType:i,dynamicTokensEnabled:a}=e,o=this.dfas,l=this.logging,u=Jr(n,i,r),f=this.atn.decisionMap[u].decision,d=v(Ko({maxLookahead:1,occurrence:r,prodType:i,rule:n}),p=>v(p,m=>m[0]));if(hg(d)&&d[0][0]&&!a){let p=d[0],m=ye(p);if(m.length===1&&D(m[0].categoryMatches)){let y=m[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===y}}else{let g=me(m,(y,A)=>(A!==void 0&&(y[A.tokenTypeIdx]=!0,S(A.categoryMatches,T=>{y[T]=!0})),y),{});return function(){let y=this.LA(1);return g[y.tokenTypeIdx]===!0}}}return function(){let p=Nc.call(this,o,f,mg,l);return typeof p=="object"?!1:p===0}}};function hg(t,e=!0){let r=new Set;for(let n of t){let i=new Set;for(let a of n){if(a===void 0){if(e)break;return!1}let o=[a.tokenTypeIdx].concat(a.categoryMatches);for(let l of o)if(r.has(l)){if(!i.has(l))return!1}else r.add(l),i.add(l)}}return!0}s(hg,"isLL1Sequence");function cN(t){let e=t.decisionStates.length,r=Array(e);for(let n=0;n<e;n++)r[n]=uN(t.decisionStates[n],n);return r}s(cN,"initATNSimulator");function Nc(t,e,r,n){let i=t[e](r),a=i.start;if(a===void 0){let l=AN(i.atnStartState);a=xg(i,yg(l)),i.start=a}return fN.apply(this,[i,a,r,n])}s(Nc,"adaptivePredict");function fN(t,e,r,n){let i=e,a=1,o=[],l=this.LA(a++);for(;;){let u=yN(i,l);if(u===void 0&&(u=dN.apply(this,[t,i,l,a,r,n])),u===zs)return gN(o,i,l);if(u.isAcceptState===!0)return u.prediction;i=u,o.push(l),l=this.LA(a++)}}s(fN,"performLookahead");function dN(t,e,r,n,i,a){let o=xN(e.configs,r,i);if(o.size===0)return gg(t,e,r,zs),zs;let l=yg(o),u=RN(o,i);if(u!==void 0)l.isAcceptState=!0,l.prediction=u,l.configs.uniqueAlt=u;else if(SN(o)){let c=Wm(o.alts);l.isAcceptState=!0,l.prediction=c,l.configs.uniqueAlt=c,pN.apply(this,[t,n,o.alts,a])}return l=gg(t,e,r,l),l}s(dN,"computeLookaheadTarget");function pN(t,e,r,n){let i=[];for(let c=1;c<=e;c++)i.push(this.LA(c).tokenType);let a=t.atnStartState,o=a.rule,l=a.production,u=mN({topLevelRule:o,ambiguityIndices:r,production:l,prefixPath:i});n(u)}s(pN,"reportLookaheadAmbiguity");function mN(t){let e=v(t.prefixPath,i=>tr(i)).join(", "),r=t.production.idx===0?"":t.production.idx,n=`Ambiguous Alternatives Detected: <${t.ambiguityIndices.join(", ")}> in <${hN(t.production)}${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n}s(mN,"buildAmbiguityError");function hN(t){if(t instanceof V)return"SUBRULE";if(t instanceof z)return"OPTION";if(t instanceof X)return"OR";if(t instanceof re)return"AT_LEAST_ONE";if(t instanceof ne)return"AT_LEAST_ONE_SEP";if(t instanceof Y)return"MANY_SEP";if(t instanceof G)return"MANY";if(t instanceof F)return"CONSUME";throw Error("non exhaustive match")}s(hN,"getProductionDslName");function gN(t,e,r){let n=Oe(e.configs.elements,a=>a.state.transitions),i=Xm(n.filter(a=>a instanceof Ui).map(a=>a.tokenType),a=>a.tokenTypeIdx);return{actualToken:r,possibleTokenTypes:i,tokenPath:t}}s(gN,"buildAdaptivePredictError");function yN(t,e){return t.edges[e.tokenTypeIdx]}s(yN,"getExistingTargetState");function xN(t,e,r){let n=new Ki,i=[];for(let o of t.elements){if(r.is(o.alt)===!1)continue;if(o.state.type===ji){i.push(o);continue}let l=o.state.transitions.length;for(let u=0;u<l;u++){let c=o.state.transitions[u],f=TN(c,e);f!==void 0&&n.add({state:f,alt:o.alt,stack:o.stack})}}let a;if(i.length===0&&n.size===1&&(a=n),a===void 0){a=new Ki;for(let o of n.elements)dl(o,a)}if(i.length>0&&!vN(a))for(let o of i)a.add(o);return a}s(xN,"computeReachSet");function TN(t,e){if(t instanceof Ui&&Os(e,t.tokenType))return t.target}s(TN,"getReachableTarget");function RN(t,e){let r;for(let n of t.elements)if(e.is(n.alt)===!0){if(r===void 0)r=n.alt;else if(r!==n.alt)return}return r}s(RN,"getUniqueAlt");function yg(t){return{configs:t,edges:{},isAcceptState:!1,prediction:-1}}s(yg,"newDFAState");function gg(t,e,r,n){return n=xg(t,n),e.edges[r.tokenTypeIdx]=n,n}s(gg,"addDFAEdge");function xg(t,e){if(e===zs)return e;let r=e.configs.key,n=t.states[r];return n!==void 0?n:(e.configs.finalize(),t.states[r]=e,e)}s(xg,"addDFAState");function AN(t){let e=new Ki,r=t.transitions.length;for(let n=0;n<r;n++){let a={state:t.transitions[n].target,alt:n,stack:[]};dl(a,e)}return e}s(AN,"computeStartState");function dl(t,e){let r=t.state;if(r.type===ji){if(t.stack.length>0){let i=[...t.stack],o={state:i.pop(),alt:t.alt,stack:i};dl(o,e)}else e.add(t);return}r.epsilonOnlyTransitions||e.add(t);let n=r.transitions.length;for(let i=0;i<n;i++){let a=r.transitions[i],o=EN(t,a);o!==void 0&&dl(o,e)}}s(dl,"closure");function EN(t,e){if(e instanceof Vs)return{state:e.target,alt:t.alt,stack:t.stack};if(e instanceof Bi){let r=[...t.stack,e.followState];return{state:e.target,alt:t.alt,stack:r}}}s(EN,"getEpsilonTarget");function vN(t){for(let e of t.elements)if(e.state.type===ji)return!0;return!1}s(vN,"hasConfigInRuleStopState");function IN(t){for(let e of t.elements)if(e.state.type!==ji)return!1;return!0}s(IN,"allConfigsInRuleStopStates");function SN(t){if(IN(t))return!0;let e=kN(t.elements);return CN(e)&&!NN(e)}s(SN,"hasConflictTerminatingPrediction");function kN(t){let e=new Map;for(let r of t){let n=Cc(r,!1),i=e.get(n);i===void 0&&(i={},e.set(n,i)),i[r.alt]=!0}return e}s(kN,"getConflictingAltSets");function CN(t){for(let e of Array.from(t.values()))if(Object.keys(e).length>1)return!0;return!1}s(CN,"hasConflictingAltSet");function NN(t){for(let e of Array.from(t.values()))if(Object.keys(e).length===1)return!0;return!1}s(NN,"hasStateAssociatedWithOneAlt");var Tg;(function(t){function e(r){return typeof r=="string"}s(e,"is"),t.is=e})(Tg||(Tg={}));var $c;(function(t){function e(r){return typeof r=="string"}s(e,"is"),t.is=e})($c||($c={}));var Rg;(function(t){t.MIN_VALUE=-2147483648,t.MAX_VALUE=2147483647;function e(r){return typeof r=="number"&&t.MIN_VALUE<=r&&r<=t.MAX_VALUE}s(e,"is"),t.is=e})(Rg||(Rg={}));var pl;(function(t){t.MIN_VALUE=0,t.MAX_VALUE=2147483647;function e(r){return typeof r=="number"&&t.MIN_VALUE<=r&&r<=t.MAX_VALUE}s(e,"is"),t.is=e})(pl||(pl={}));var j;(function(t){function e(n,i){return n===Number.MAX_VALUE&&(n=pl.MAX_VALUE),i===Number.MAX_VALUE&&(i=pl.MAX_VALUE),{line:n,character:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.uinteger(i.line)&&h.uinteger(i.character)}s(r,"is"),t.is=r})(j||(j={}));var U;(function(t){function e(n,i,a,o){if(h.uinteger(n)&&h.uinteger(i)&&h.uinteger(a)&&h.uinteger(o))return{start:j.create(n,i),end:j.create(a,o)};if(j.is(n)&&j.is(i))return{start:n,end:i};throw new Error(`Range#create called with invalid arguments[${n}, ${i}, ${a}, ${o}]`)}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&j.is(i.start)&&j.is(i.end)}s(r,"is"),t.is=r})(U||(U={}));var ml;(function(t){function e(n,i){return{uri:n,range:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&(h.string(i.uri)||h.undefined(i.uri))}s(r,"is"),t.is=r})(ml||(ml={}));var Ag;(function(t){function e(n,i,a,o){return{targetUri:n,targetRange:i,targetSelectionRange:a,originSelectionRange:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.targetRange)&&h.string(i.targetUri)&&U.is(i.targetSelectionRange)&&(U.is(i.originSelectionRange)||h.undefined(i.originSelectionRange))}s(r,"is"),t.is=r})(Ag||(Ag={}));var wc;(function(t){function e(n,i,a,o){return{red:n,green:i,blue:a,alpha:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.numberRange(i.red,0,1)&&h.numberRange(i.green,0,1)&&h.numberRange(i.blue,0,1)&&h.numberRange(i.alpha,0,1)}s(r,"is"),t.is=r})(wc||(wc={}));var Eg;(function(t){function e(n,i){return{range:n,color:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&wc.is(i.color)}s(r,"is"),t.is=r})(Eg||(Eg={}));var vg;(function(t){function e(n,i,a){return{label:n,textEdit:i,additionalTextEdits:a}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.string(i.label)&&(h.undefined(i.textEdit)||Vi.is(i))&&(h.undefined(i.additionalTextEdits)||h.typedArray(i.additionalTextEdits,Vi.is))}s(r,"is"),t.is=r})(vg||(vg={}));var Ig;(function(t){t.Comment="comment",t.Imports="imports",t.Region="region"})(Ig||(Ig={}));var Sg;(function(t){function e(n,i,a,o,l,u){let c={startLine:n,endLine:i};return h.defined(a)&&(c.startCharacter=a),h.defined(o)&&(c.endCharacter=o),h.defined(l)&&(c.kind=l),h.defined(u)&&(c.collapsedText=u),c}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.uinteger(i.startLine)&&h.uinteger(i.startLine)&&(h.undefined(i.startCharacter)||h.uinteger(i.startCharacter))&&(h.undefined(i.endCharacter)||h.uinteger(i.endCharacter))&&(h.undefined(i.kind)||h.string(i.kind))}s(r,"is"),t.is=r})(Sg||(Sg={}));var _c;(function(t){function e(n,i){return{location:n,message:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&ml.is(i.location)&&h.string(i.message)}s(r,"is"),t.is=r})(_c||(_c={}));var kg;(function(t){t.Error=1,t.Warning=2,t.Information=3,t.Hint=4})(kg||(kg={}));var Cg;(function(t){t.Unnecessary=1,t.Deprecated=2})(Cg||(Cg={}));var Ng;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&h.string(n.href)}s(e,"is"),t.is=e})(Ng||(Ng={}));var hl;(function(t){function e(n,i,a,o,l,u){let c={range:n,message:i};return h.defined(a)&&(c.severity=a),h.defined(o)&&(c.code=o),h.defined(l)&&(c.source=l),h.defined(u)&&(c.relatedInformation=u),c}s(e,"create"),t.create=e;function r(n){var i;let a=n;return h.defined(a)&&U.is(a.range)&&h.string(a.message)&&(h.number(a.severity)||h.undefined(a.severity))&&(h.integer(a.code)||h.string(a.code)||h.undefined(a.code))&&(h.undefined(a.codeDescription)||h.string((i=a.codeDescription)===null||i===void 0?void 0:i.href))&&(h.string(a.source)||h.undefined(a.source))&&(h.undefined(a.relatedInformation)||h.typedArray(a.relatedInformation,_c.is))}s(r,"is"),t.is=r})(hl||(hl={}));var Hi;(function(t){function e(n,i,...a){let o={title:n,command:i};return h.defined(a)&&a.length>0&&(o.arguments=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.title)&&h.string(i.command)}s(r,"is"),t.is=r})(Hi||(Hi={}));var Vi;(function(t){function e(a,o){return{range:a,newText:o}}s(e,"replace"),t.replace=e;function r(a,o){return{range:{start:a,end:a},newText:o}}s(r,"insert"),t.insert=r;function n(a){return{range:a,newText:""}}s(n,"del"),t.del=n;function i(a){let o=a;return h.objectLiteral(o)&&h.string(o.newText)&&U.is(o.range)}s(i,"is"),t.is=i})(Vi||(Vi={}));var bc;(function(t){function e(n,i,a){let o={label:n};return i!==void 0&&(o.needsConfirmation=i),a!==void 0&&(o.description=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.string(i.label)&&(h.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(h.string(i.description)||i.description===void 0)}s(r,"is"),t.is=r})(bc||(bc={}));var zi;(function(t){function e(r){let n=r;return h.string(n)}s(e,"is"),t.is=e})(zi||(zi={}));var $g;(function(t){function e(a,o,l){return{range:a,newText:o,annotationId:l}}s(e,"replace"),t.replace=e;function r(a,o,l){return{range:{start:a,end:a},newText:o,annotationId:l}}s(r,"insert"),t.insert=r;function n(a,o){return{range:a,newText:"",annotationId:o}}s(n,"del"),t.del=n;function i(a){let o=a;return Vi.is(o)&&(bc.is(o.annotationId)||zi.is(o.annotationId))}s(i,"is"),t.is=i})($g||($g={}));var Oc;(function(t){function e(n,i){return{textDocument:n,edits:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&Fc.is(i.textDocument)&&Array.isArray(i.edits)}s(r,"is"),t.is=r})(Oc||(Oc={}));var Lc;(function(t){function e(n,i,a){let o={kind:"create",uri:n};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(o.options=i),a!==void 0&&(o.annotationId=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="create"&&h.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||h.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||h.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})(Lc||(Lc={}));var Pc;(function(t){function e(n,i,a,o){let l={kind:"rename",oldUri:n,newUri:i};return a!==void 0&&(a.overwrite!==void 0||a.ignoreIfExists!==void 0)&&(l.options=a),o!==void 0&&(l.annotationId=o),l}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="rename"&&h.string(i.oldUri)&&h.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||h.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||h.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})(Pc||(Pc={}));var Mc;(function(t){function e(n,i,a){let o={kind:"delete",uri:n};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(o.options=i),a!==void 0&&(o.annotationId=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="delete"&&h.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||h.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||h.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})(Mc||(Mc={}));var Dc;(function(t){function e(r){let n=r;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(i=>h.string(i.kind)?Lc.is(i)||Pc.is(i)||Mc.is(i):Oc.is(i)))}s(e,"is"),t.is=e})(Dc||(Dc={}));var wg;(function(t){function e(n){return{uri:n}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)}s(r,"is"),t.is=r})(wg||(wg={}));var _g;(function(t){function e(n,i){return{uri:n,version:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&h.integer(i.version)}s(r,"is"),t.is=r})(_g||(_g={}));var Fc;(function(t){function e(n,i){return{uri:n,version:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&(i.version===null||h.integer(i.version))}s(r,"is"),t.is=r})(Fc||(Fc={}));var bg;(function(t){function e(n,i,a,o){return{uri:n,languageId:i,version:a,text:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&h.string(i.languageId)&&h.integer(i.version)&&h.string(i.text)}s(r,"is"),t.is=r})(bg||(bg={}));var Gc;(function(t){t.PlainText="plaintext",t.Markdown="markdown";function e(r){let n=r;return n===t.PlainText||n===t.Markdown}s(e,"is"),t.is=e})(Gc||(Gc={}));var Ys;(function(t){function e(r){let n=r;return h.objectLiteral(r)&&Gc.is(n.kind)&&h.string(n.value)}s(e,"is"),t.is=e})(Ys||(Ys={}));var Og;(function(t){t.Text=1,t.Method=2,t.Function=3,t.Constructor=4,t.Field=5,t.Variable=6,t.Class=7,t.Interface=8,t.Module=9,t.Property=10,t.Unit=11,t.Value=12,t.Enum=13,t.Keyword=14,t.Snippet=15,t.Color=16,t.File=17,t.Reference=18,t.Folder=19,t.EnumMember=20,t.Constant=21,t.Struct=22,t.Event=23,t.Operator=24,t.TypeParameter=25})(Og||(Og={}));var Lg;(function(t){t.PlainText=1,t.Snippet=2})(Lg||(Lg={}));var Pg;(function(t){t.Deprecated=1})(Pg||(Pg={}));var Mg;(function(t){function e(n,i,a){return{newText:n,insert:i,replace:a}}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.newText)&&U.is(i.insert)&&U.is(i.replace)}s(r,"is"),t.is=r})(Mg||(Mg={}));var Dg;(function(t){t.asIs=1,t.adjustIndentation=2})(Dg||(Dg={}));var Fg;(function(t){function e(r){let n=r;return n&&(h.string(n.detail)||n.detail===void 0)&&(h.string(n.description)||n.description===void 0)}s(e,"is"),t.is=e})(Fg||(Fg={}));var Gg;(function(t){function e(r){return{label:r}}s(e,"create"),t.create=e})(Gg||(Gg={}));var Ug;(function(t){function e(r,n){return{items:r||[],isIncomplete:!!n}}s(e,"create"),t.create=e})(Ug||(Ug={}));var gl;(function(t){function e(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}s(e,"fromPlainText"),t.fromPlainText=e;function r(n){let i=n;return h.string(i)||h.objectLiteral(i)&&h.string(i.language)&&h.string(i.value)}s(r,"is"),t.is=r})(gl||(gl={}));var Bg;(function(t){function e(r){let n=r;return!!n&&h.objectLiteral(n)&&(Ys.is(n.contents)||gl.is(n.contents)||h.typedArray(n.contents,gl.is))&&(r.range===void 0||U.is(r.range))}s(e,"is"),t.is=e})(Bg||(Bg={}));var jg;(function(t){function e(r,n){return n?{label:r,documentation:n}:{label:r}}s(e,"create"),t.create=e})(jg||(jg={}));var Wg;(function(t){function e(r,n,...i){let a={label:r};return h.defined(n)&&(a.documentation=n),h.defined(i)?a.parameters=i:a.parameters=[],a}s(e,"create"),t.create=e})(Wg||(Wg={}));var Kg;(function(t){t.Text=1,t.Read=2,t.Write=3})(Kg||(Kg={}));var Hg;(function(t){function e(r,n){let i={range:r};return h.number(n)&&(i.kind=n),i}s(e,"create"),t.create=e})(Hg||(Hg={}));var Vg;(function(t){t.File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26})(Vg||(Vg={}));var zg;(function(t){t.Deprecated=1})(zg||(zg={}));var qg;(function(t){function e(r,n,i,a,o){let l={name:r,kind:n,location:{uri:a,range:i}};return o&&(l.containerName=o),l}s(e,"create"),t.create=e})(qg||(qg={}));var Yg;(function(t){function e(r,n,i,a){return a!==void 0?{name:r,kind:n,location:{uri:i,range:a}}:{name:r,kind:n,location:{uri:i}}}s(e,"create"),t.create=e})(Yg||(Yg={}));var Xg;(function(t){function e(n,i,a,o,l,u){let c={name:n,detail:i,kind:a,range:o,selectionRange:l};return u!==void 0&&(c.children=u),c}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.name)&&h.number(i.kind)&&U.is(i.range)&&U.is(i.selectionRange)&&(i.detail===void 0||h.string(i.detail))&&(i.deprecated===void 0||h.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}s(r,"is"),t.is=r})(Xg||(Xg={}));var Jg;(function(t){t.Empty="",t.QuickFix="quickfix",t.Refactor="refactor",t.RefactorExtract="refactor.extract",t.RefactorInline="refactor.inline",t.RefactorRewrite="refactor.rewrite",t.Source="source",t.SourceOrganizeImports="source.organizeImports",t.SourceFixAll="source.fixAll"})(Jg||(Jg={}));var yl;(function(t){t.Invoked=1,t.Automatic=2})(yl||(yl={}));var Zg;(function(t){function e(n,i,a){let o={diagnostics:n};return i!=null&&(o.only=i),a!=null&&(o.triggerKind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.typedArray(i.diagnostics,hl.is)&&(i.only===void 0||h.typedArray(i.only,h.string))&&(i.triggerKind===void 0||i.triggerKind===yl.Invoked||i.triggerKind===yl.Automatic)}s(r,"is"),t.is=r})(Zg||(Zg={}));var Qg;(function(t){function e(n,i,a){let o={title:n},l=!0;return typeof i=="string"?(l=!1,o.kind=i):Hi.is(i)?o.command=i:o.edit=i,l&&a!==void 0&&(o.kind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.title)&&(i.diagnostics===void 0||h.typedArray(i.diagnostics,hl.is))&&(i.kind===void 0||h.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Hi.is(i.command))&&(i.isPreferred===void 0||h.boolean(i.isPreferred))&&(i.edit===void 0||Dc.is(i.edit))}s(r,"is"),t.is=r})(Qg||(Qg={}));var ey;(function(t){function e(n,i){let a={range:n};return h.defined(i)&&(a.data=i),a}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(i.range)&&(h.undefined(i.command)||Hi.is(i.command))}s(r,"is"),t.is=r})(ey||(ey={}));var ty;(function(t){function e(n,i){return{tabSize:n,insertSpaces:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.uinteger(i.tabSize)&&h.boolean(i.insertSpaces)}s(r,"is"),t.is=r})(ty||(ty={}));var ry;(function(t){function e(n,i,a){return{range:n,target:i,data:a}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(i.range)&&(h.undefined(i.target)||h.string(i.target))}s(r,"is"),t.is=r})(ry||(ry={}));var ny;(function(t){function e(n,i){return{range:n,parent:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&(i.parent===void 0||t.is(i.parent))}s(r,"is"),t.is=r})(ny||(ny={}));var iy;(function(t){t.namespace="namespace",t.type="type",t.class="class",t.enum="enum",t.interface="interface",t.struct="struct",t.typeParameter="typeParameter",t.parameter="parameter",t.variable="variable",t.property="property",t.enumMember="enumMember",t.event="event",t.function="function",t.method="method",t.macro="macro",t.keyword="keyword",t.modifier="modifier",t.comment="comment",t.string="string",t.number="number",t.regexp="regexp",t.operator="operator",t.decorator="decorator"})(iy||(iy={}));var sy;(function(t){t.declaration="declaration",t.definition="definition",t.readonly="readonly",t.static="static",t.deprecated="deprecated",t.abstract="abstract",t.async="async",t.modification="modification",t.documentation="documentation",t.defaultLibrary="defaultLibrary"})(sy||(sy={}));var ay;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}s(e,"is"),t.is=e})(ay||(ay={}));var oy;(function(t){function e(n,i){return{range:n,text:i}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&h.string(i.text)}s(r,"is"),t.is=r})(oy||(oy={}));var ly;(function(t){function e(n,i,a){return{range:n,variableName:i,caseSensitiveLookup:a}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&h.boolean(i.caseSensitiveLookup)&&(h.string(i.variableName)||i.variableName===void 0)}s(r,"is"),t.is=r})(ly||(ly={}));var uy;(function(t){function e(n,i){return{range:n,expression:i}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&(h.string(i.expression)||i.expression===void 0)}s(r,"is"),t.is=r})(uy||(uy={}));var cy;(function(t){function e(n,i){return{frameId:n,stoppedLocation:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(n.stoppedLocation)}s(r,"is"),t.is=r})(cy||(cy={}));var Uc;(function(t){t.Type=1,t.Parameter=2;function e(r){return r===1||r===2}s(e,"is"),t.is=e})(Uc||(Uc={}));var Bc;(function(t){function e(n){return{value:n}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&(i.tooltip===void 0||h.string(i.tooltip)||Ys.is(i.tooltip))&&(i.location===void 0||ml.is(i.location))&&(i.command===void 0||Hi.is(i.command))}s(r,"is"),t.is=r})(Bc||(Bc={}));var fy;(function(t){function e(n,i,a){let o={position:n,label:i};return a!==void 0&&(o.kind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&j.is(i.position)&&(h.string(i.label)||h.typedArray(i.label,Bc.is))&&(i.kind===void 0||Uc.is(i.kind))&&i.textEdits===void 0||h.typedArray(i.textEdits,Vi.is)&&(i.tooltip===void 0||h.string(i.tooltip)||Ys.is(i.tooltip))&&(i.paddingLeft===void 0||h.boolean(i.paddingLeft))&&(i.paddingRight===void 0||h.boolean(i.paddingRight))}s(r,"is"),t.is=r})(fy||(fy={}));var dy;(function(t){function e(r){return{kind:"snippet",value:r}}s(e,"createSnippet"),t.createSnippet=e})(dy||(dy={}));var py;(function(t){function e(r,n,i,a){return{insertText:r,filterText:n,range:i,command:a}}s(e,"create"),t.create=e})(py||(py={}));var my;(function(t){function e(r){return{items:r}}s(e,"create"),t.create=e})(my||(my={}));var hy;(function(t){t.Invoked=0,t.Automatic=1})(hy||(hy={}));var gy;(function(t){function e(r,n){return{range:r,text:n}}s(e,"create"),t.create=e})(gy||(gy={}));var yy;(function(t){function e(r,n){return{triggerKind:r,selectedCompletionInfo:n}}s(e,"create"),t.create=e})(yy||(yy={}));var xy;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&$c.is(n.uri)&&h.string(n.name)}s(e,"is"),t.is=e})(xy||(xy={}));var Ty;(function(t){function e(a,o,l,u){return new jc(a,o,l,u)}s(e,"create"),t.create=e;function r(a){let o=a;return!!(h.defined(o)&&h.string(o.uri)&&(h.undefined(o.languageId)||h.string(o.languageId))&&h.uinteger(o.lineCount)&&h.func(o.getText)&&h.func(o.positionAt)&&h.func(o.offsetAt))}s(r,"is"),t.is=r;function n(a,o){let l=a.getText(),u=i(o,(f,d)=>{let p=f.range.start.line-d.range.start.line;return p===0?f.range.start.character-d.range.start.character:p}),c=l.length;for(let f=u.length-1;f>=0;f--){let d=u[f],p=a.offsetAt(d.range.start),m=a.offsetAt(d.range.end);if(m<=c)l=l.substring(0,p)+d.newText+l.substring(m,l.length);else throw new Error("Overlapping edit");c=p}return l}s(n,"applyEdits"),t.applyEdits=n;function i(a,o){if(a.length<=1)return a;let l=a.length/2|0,u=a.slice(0,l),c=a.slice(l);i(u,o),i(c,o);let f=0,d=0,p=0;for(;f<u.length&&d<c.length;)o(u[f],c[d])<=0?a[p++]=u[f++]:a[p++]=c[d++];for(;f<u.length;)a[p++]=u[f++];for(;d<c.length;)a[p++]=c[d++];return a}s(i,"mergeSort")})(Ty||(Ty={}));var jc=class{static{s(this,"FullTextDocument")}constructor(e,r,n,i){this._uri=e,this._languageId=r,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let r=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(r,n)}return this._content}update(e,r){this._content=e.text,this._version=r,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],r=this._content,n=!0;for(let i=0;i<r.length;i++){n&&(e.push(i),n=!1);let a=r.charAt(i);n=a==="\r"||a===`
`,a==="\r"&&i+1<r.length&&r.charAt(i+1)===`
`&&i++}n&&r.length>0&&e.push(r.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let r=this.getLineOffsets(),n=0,i=r.length;if(i===0)return j.create(0,e);for(;n<i;){let o=Math.floor((n+i)/2);r[o]>e?i=o:n=o+1}let a=n-1;return j.create(a,e-r[a])}offsetAt(e){let r=this.getLineOffsets();if(e.line>=r.length)return this._content.length;if(e.line<0)return 0;let n=r[e.line],i=e.line+1<r.length?r[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,i),n)}get lineCount(){return this.getLineOffsets().length}},h;(function(t){let e=Object.prototype.toString;function r(m){return typeof m<"u"}s(r,"defined"),t.defined=r;function n(m){return typeof m>"u"}s(n,"undefined"),t.undefined=n;function i(m){return m===!0||m===!1}s(i,"boolean"),t.boolean=i;function a(m){return e.call(m)==="[object String]"}s(a,"string"),t.string=a;function o(m){return e.call(m)==="[object Number]"}s(o,"number"),t.number=o;function l(m,g,y){return e.call(m)==="[object Number]"&&g<=m&&m<=y}s(l,"numberRange"),t.numberRange=l;function u(m){return e.call(m)==="[object Number]"&&-2147483648<=m&&m<=2147483647}s(u,"integer"),t.integer=u;function c(m){return e.call(m)==="[object Number]"&&0<=m&&m<=2147483647}s(c,"uinteger"),t.uinteger=c;function f(m){return e.call(m)==="[object Function]"}s(f,"func"),t.func=f;function d(m){return m!==null&&typeof m=="object"}s(d,"objectLiteral"),t.objectLiteral=d;function p(m,g){return Array.isArray(m)&&m.every(g)}s(p,"typedArray"),t.typedArray=p})(h||(h={}));var Xs=class{static{s(this,"CstNodeBuilder")}constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new qi(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){let r=new en;return r.grammarSource=e,r.root=this.rootNode,this.current.content.push(r),this.nodeStack.push(r),r}buildLeafNode(e,r){let n=new Qr(e.startOffset,e.image.length,dn(e),e.tokenType,!r);return n.grammarSource=r,n.root=this.rootNode,this.current.content.push(n),n}removeNode(e){let r=e.container;if(r){let n=r.content.indexOf(e);n>=0&&r.content.splice(n,1)}}addHiddenNodes(e){let r=[];for(let a of e){let o=new Qr(a.startOffset,a.image.length,dn(a),a.tokenType,!0);o.root=this.rootNode,r.push(o)}let n=this.current,i=!1;if(n.content.length>0){n.content.push(...r);return}for(;n.container;){let a=n.container.content.indexOf(n);if(a>0){n.container.content.splice(a,0,...r),i=!0;break}n=n.container}i||this.rootNode.content.unshift(...r)}construct(e){let r=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=r;let n=this.nodeStack.pop();n?.content.length===0&&this.removeNode(n)}},Js=class{static{s(this,"AbstractCstNode")}get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,r;let n=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(r=this.container)===null||r===void 0?void 0:r.astNode;if(!n)throw new Error("This node has no associated AST element");return n}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}},Qr=class extends Js{static{s(this,"LeafCstNodeImpl")}get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,r,n,i,a=!1){super(),this._hidden=a,this._offset=e,this._tokenType=i,this._length=r,this._range=n}},en=class extends Js{static{s(this,"CompositeCstNodeImpl")}constructor(){super(...arguments),this.content=new Wc(this)}get children(){return this.content}get offset(){var e,r;return(r=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&r!==void 0?r:0}get length(){return this.end-this.offset}get end(){var e,r;return(r=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&r!==void 0?r:0}get range(){let e=this.firstNonHiddenNode,r=this.lastNonHiddenNode;if(e&&r){if(this._rangeCache===void 0){let{range:n}=e,{range:i}=r;this._rangeCache={start:n.start,end:i.end.line<n.start.line?n.start:i.end}}return this._rangeCache}else return{start:j.create(0,0),end:j.create(0,0)}}get firstNonHiddenNode(){for(let e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){let r=this.content[e];if(!r.hidden)return r}return this.content[this.content.length-1]}},Wc=class t extends Array{static{s(this,"CstNodeContainer")}constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,t.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,r,...n){return this.addParents(n),super.splice(e,r,...n)}addParents(e){for(let r of e)r.container=this.parent}},qi=class extends en{static{s(this,"RootCstNodeImpl")}get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}};var xl=Symbol("Datatype");function Kc(t){return t.$type===xl}s(Kc,"isDataTypeNode");var Ry="\u200B",Ay=s(t=>t.endsWith(Ry)?t:t+Ry,"withRuleSuffix"),Zs=class{static{s(this,"AbstractLangiumParser")}constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;let r=this.lexer.definition,n=e.LanguageMetaData.mode==="production";this.wrapper=new Hc(r,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:n,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,r){this.wrapper.wrapOr(e,r)}optional(e,r){this.wrapper.wrapOption(e,r)}many(e,r){this.wrapper.wrapMany(e,r)}atLeastOne(e,r){this.wrapper.wrapAtLeastOne(e,r)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}},Qs=class extends Zs{static{s(this,"LangiumParser")}get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Xs,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,r){let n=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(Ay(e.name),this.startImplementation(n,r).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(Es(e))return xl;{let r=ti(e);return r??e.name}}}parse(e,r={}){this.nodeBuilder.buildRootNode(e);let n=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=n.tokens;let i=r.rule?this.allRules.get(r.rule):this.mainRule;if(!i)throw new Error(r.rule?`No rule found with name '${r.rule}'`:"No main rule available.");let a=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(n.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:a,lexerErrors:n.errors,lexerReport:n.report,parserErrors:this.wrapper.errors}}startImplementation(e,r){return n=>{let i=!this.isRecording()&&e!==void 0;if(i){let o={$type:e};this.stack.push(o),e===xl&&(o.value="")}let a;try{a=r(n)}catch{a=void 0}return a===void 0&&i&&(a=this.construct()),a}}extractHiddenTokens(e){let r=this.lexerResult.hidden;if(!r.length)return[];let n=e.startOffset;for(let i=0;i<r.length;i++)if(r[i].startOffset>n)return r.splice(0,i);return r.splice(0,r.length)}consume(e,r,n){let i=this.wrapper.wrapConsume(e,r);if(!this.isRecording()&&this.isValidToken(i)){let a=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(a);let o=this.nodeBuilder.buildLeafNode(i,n),{assignment:l,isCrossRef:u}=this.getAssignment(n),c=this.current;if(l){let f=ut(n)?i.image:this.converter.convert(i.image,o);this.assign(l.operator,l.feature,f,o,u)}else if(Kc(c)){let f=i.image;ut(n)||(f=this.converter.convert(f,o).toString()),c.value+=f}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,r,n,i,a){let o;!this.isRecording()&&!n&&(o=this.nodeBuilder.buildCompositeNode(i));let l=this.wrapper.wrapSubrule(e,r,a);!this.isRecording()&&o&&o.length>0&&this.performSubruleAssignment(l,i,o)}performSubruleAssignment(e,r,n){let{assignment:i,isCrossRef:a}=this.getAssignment(r);if(i)this.assign(i.operator,i.feature,e,n,a);else if(!i){let o=this.current;if(Kc(o))o.value+=e.toString();else if(typeof e=="object"&&e){let u=this.assignWithoutOverride(e,o);this.stack.pop(),this.stack.push(u)}}}action(e,r){if(!this.isRecording()){let n=this.current;if(r.feature&&r.operator){n=this.construct(),this.nodeBuilder.removeNode(n.$cstNode),this.nodeBuilder.buildCompositeNode(r).content.push(n.$cstNode);let a={$type:e};this.stack.push(a),this.assign(r.operator,r.feature,n,n.$cstNode,!1)}else n.$type=e}}construct(){if(this.isRecording())return;let e=this.current;return za(e),this.nodeBuilder.construct(e),this.stack.pop(),Kc(e)?this.converter.convert(e.value,e.$cstNode):(Iu(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){let r=Ur(e,gt);this.assignmentMap.set(e,{assignment:r,isCrossRef:r?Gr(r.terminal):!1})}return this.assignmentMap.get(e)}assign(e,r,n,i,a){let o=this.current,l;switch(a&&typeof n=="string"?l=this.linker.buildReference(o,r,i,n):l=n,e){case"=":{o[r]=l;break}case"?=":{o[r]=!0;break}case"+=":Array.isArray(o[r])||(o[r]=[]),o[r].push(l)}}assignWithoutOverride(e,r){for(let[i,a]of Object.entries(r)){let o=e[i];o===void 0?e[i]=a:Array.isArray(o)&&Array.isArray(a)&&(a.push(...o),e[i]=a)}let n=e.$cstNode;return n&&(n.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}},Tl=class{static{s(this,"AbstractParserErrorMessageProvider")}buildMismatchTokenMessage(e){return nr.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return nr.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return nr.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return nr.buildEarlyExitMessage(e)}},Yi=class extends Tl{static{s(this,"LangiumParserErrorMessageProvider")}buildMismatchTokenMessage({expected:e,actual:r}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${r.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}},ea=class extends Zs{static{s(this,"LangiumCompletionParser")}constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();let r=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=r.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,r){let n=this.wrapper.DEFINE_RULE(Ay(e.name),this.startImplementation(r).bind(this));return this.allRules.set(e.name,n),e.entry&&(this.mainRule=n),n}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return r=>{let n=this.keepStackSize();try{e(r)}finally{this.resetStackSize(n)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){let e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,r,n){this.wrapper.wrapConsume(e,r),this.isRecording()||(this.lastElementStack=[...this.elementStack,n],this.nextTokenIndex=this.currIdx+1)}subrule(e,r,n,i,a){this.before(i),this.wrapper.wrapSubrule(e,r,a),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){let r=this.elementStack.lastIndexOf(e);r>=0&&this.elementStack.splice(r)}}get currIdx(){return this.wrapper.currIdx}},$N={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Yi},Hc=class extends Ks{static{s(this,"ChevrotainWrapper")}constructor(e,r){let n=r&&"maxLookahead"in r;super(e,Object.assign(Object.assign(Object.assign({},$N),{lookaheadStrategy:n?new ir({maxLookahead:r.maxLookahead}):new qs({logging:r.skipValidations?()=>{}:void 0})}),r))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,r){return this.RULE(e,r)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,r){return this.consume(e,r)}wrapSubrule(e,r,n){return this.subrule(e,r,{ARGS:[n]})}wrapOr(e,r){this.or(e,r)}wrapOption(e,r){this.option(e,r)}wrapMany(e,r){this.many(e,r)}wrapAtLeastOne(e,r){this.atLeastOne(e,r)}};function ta(t,e,r){return wN({parser:e,tokens:r,ruleNames:new Map},t),e}s(ta,"createParser");function wN(t,e){let r=Rs(e,!1),n=H(e.rules).filter(De).filter(i=>r.has(i));for(let i of n){let a=Object.assign(Object.assign({},t),{consume:1,optional:1,subrule:1,many:1,or:1});t.parser.rule(i,tn(a,i.definition))}}s(wN,"buildRules");function tn(t,e,r=!1){let n;if(ut(e))n=DN(t,e);else if(Vt(e))n=_N(t,e);else if(gt(e))n=tn(t,e.terminal);else if(Gr(e))n=Ey(t,e);else if(yt(e))n=bN(t,e);else if(Ka(e))n=LN(t,e);else if(Va(e))n=PN(t,e);else if(cr(e))n=MN(t,e);else if(hu(e)){let i=t.consume++;n=s(()=>t.parser.consume(i,ot,e),"method")}else throw new Dr(e.$cstNode,`Unexpected element type: ${e.$type}`);return vy(t,r?void 0:Rl(e),n,e.cardinality)}s(tn,"buildElement");function _N(t,e){let r=vs(e);return()=>t.parser.action(r,e)}s(_N,"buildAction");function bN(t,e){let r=e.rule.ref;if(De(r)){let n=t.subrule++,i=r.fragment,a=e.arguments.length>0?ON(r,e.arguments):()=>({});return o=>t.parser.subrule(n,Iy(t,r),i,e,a(o))}else if(st(r)){let n=t.consume++,i=Vc(t,r.name);return()=>t.parser.consume(n,i,e)}else if(r)It(r);else throw new Dr(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}s(bN,"buildRuleCall");function ON(t,e){let r=e.map(n=>sr(n.value));return n=>{let i={};for(let a=0;a<r.length;a++){let o=t.parameters[a],l=r[a];i[o.name]=l(n)}return i}}s(ON,"buildRuleCallPredicate");function sr(t){if(lu(t)){let e=sr(t.left),r=sr(t.right);return n=>e(n)||r(n)}else if(ou(t)){let e=sr(t.left),r=sr(t.right);return n=>e(n)&&r(n)}else if(uu(t)){let e=sr(t.value);return r=>!e(r)}else if(cu(t)){let e=t.parameter.ref.name;return r=>r!==void 0&&r[e]===!0}else if(au(t)){let e=!!t.true;return()=>e}It(t)}s(sr,"buildPredicate");function LN(t,e){if(e.elements.length===1)return tn(t,e.elements[0]);{let r=[];for(let i of e.elements){let a={ALT:tn(t,i,!0)},o=Rl(i);o&&(a.GATE=sr(o)),r.push(a)}let n=t.or++;return i=>t.parser.alternatives(n,r.map(a=>{let o={ALT:s(()=>a.ALT(i),"ALT")},l=a.GATE;return l&&(o.GATE=()=>l(i)),o}))}}s(LN,"buildAlternatives");function PN(t,e){if(e.elements.length===1)return tn(t,e.elements[0]);let r=[];for(let l of e.elements){let u={ALT:tn(t,l,!0)},c=Rl(l);c&&(u.GATE=sr(c)),r.push(u)}let n=t.or++,i=s((l,u)=>{let c=u.getRuleStack().join("-");return`uGroup_${l}_${c}`},"idFunc"),a=s(l=>t.parser.alternatives(n,r.map((u,c)=>{let f={ALT:s(()=>!0,"ALT")},d=t.parser;f.ALT=()=>{if(u.ALT(l),!d.isRecording()){let m=i(n,d);d.unorderedGroups.get(m)||d.unorderedGroups.set(m,[]);let g=d.unorderedGroups.get(m);typeof g?.[c]>"u"&&(g[c]=!0)}};let p=u.GATE;return p?f.GATE=()=>p(l):f.GATE=()=>{let m=d.unorderedGroups.get(i(n,d));return!m?.[c]},f})),"alternatives"),o=vy(t,Rl(e),a,"*");return l=>{o(l),t.parser.isRecording()||t.parser.unorderedGroups.delete(i(n,t.parser))}}s(PN,"buildUnorderedGroup");function MN(t,e){let r=e.elements.map(n=>tn(t,n));return n=>r.forEach(i=>i(n))}s(MN,"buildGroup");function Rl(t){if(cr(t))return t.guardCondition}s(Rl,"getGuardCondition");function Ey(t,e,r=e.terminal){if(r)if(yt(r)&&De(r.rule.ref)){let n=r.rule.ref,i=t.subrule++;return a=>t.parser.subrule(i,Iy(t,n),!1,e,a)}else if(yt(r)&&st(r.rule.ref)){let n=t.consume++,i=Vc(t,r.rule.ref.name);return()=>t.parser.consume(n,i,e)}else if(ut(r)){let n=t.consume++,i=Vc(t,r.value);return()=>t.parser.consume(n,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);let n=Za(e.type.ref),i=n?.terminal;if(!i)throw new Error("Could not find name assignment for type: "+vs(e.type.ref));return Ey(t,e,i)}}s(Ey,"buildCrossReference");function DN(t,e){let r=t.consume++,n=t.tokens[e.value];if(!n)throw new Error("Could not find token for keyword: "+e.value);return()=>t.parser.consume(r,n,e)}s(DN,"buildKeyword");function vy(t,e,r,n){let i=e&&sr(e);if(!n)if(i){let a=t.or++;return o=>t.parser.alternatives(a,[{ALT:s(()=>r(o),"ALT"),GATE:s(()=>i(o),"GATE")},{ALT:cl(),GATE:s(()=>!i(o),"GATE")}])}else return r;if(n==="*"){let a=t.many++;return o=>t.parser.many(a,{DEF:s(()=>r(o),"DEF"),GATE:i?()=>i(o):void 0})}else if(n==="+"){let a=t.many++;if(i){let o=t.or++;return l=>t.parser.alternatives(o,[{ALT:s(()=>t.parser.atLeastOne(a,{DEF:s(()=>r(l),"DEF")}),"ALT"),GATE:s(()=>i(l),"GATE")},{ALT:cl(),GATE:s(()=>!i(l),"GATE")}])}else return o=>t.parser.atLeastOne(a,{DEF:s(()=>r(o),"DEF")})}else if(n==="?"){let a=t.optional++;return o=>t.parser.optional(a,{DEF:s(()=>r(o),"DEF"),GATE:i?()=>i(o):void 0})}else It(n)}s(vy,"wrap");function Iy(t,e){let r=FN(t,e),n=t.parser.getRule(r);if(!n)throw new Error(`Rule "${r}" not found."`);return n}s(Iy,"getRule");function FN(t,e){if(De(e))return e.name;if(t.ruleNames.has(e))return t.ruleNames.get(e);{let r=e,n=r.$container,i=e.$type;for(;!De(n);)(cr(n)||Ka(n)||Va(n))&&(i=n.elements.indexOf(r).toString()+":"+i),r=n,n=n.$container;return i=n.name+":"+i,t.ruleNames.set(e,i),i}}s(FN,"getRuleName");function Vc(t,e){let r=t.tokens[e];if(!r)throw new Error(`Token "${e}" not found."`);return r}s(Vc,"getToken");function zc(t){let e=t.Grammar,r=t.parser.Lexer,n=new ea(t);return ta(e,n,r.definition),n.finalize(),n}s(zc,"createCompletionParser");function qc(t){let e=Sy(t);return e.finalize(),e}s(qc,"createLangiumParser");function Sy(t){let e=t.Grammar,r=t.parser.Lexer,n=new Qs(t);return ta(e,n,r.definition)}s(Sy,"prepareLangiumParser");var ar=class{static{s(this,"DefaultTokenBuilder")}constructor(){this.diagnostics=[]}buildTokens(e,r){let n=H(Rs(e,!1)),i=this.buildTerminalTokens(n),a=this.buildKeywordTokens(n,i,r);return i.forEach(o=>{let l=o.PATTERN;typeof l=="object"&&l&&"test"in l&&ei(l)?a.unshift(o):a.push(o)}),a}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){let e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(st).filter(r=>!r.fragment).map(r=>this.buildTerminalToken(r)).toArray()}buildTerminalToken(e){let r=ri(e),n=this.requiresCustomPattern(r)?this.regexPatternFunction(r):r,i={name:e.name,PATTERN:n};return typeof n=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=ei(r)?ue.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){let r=new RegExp(e,e.flags+"y");return(n,i)=>(r.lastIndex=i,r.exec(n))}buildKeywordTokens(e,r,n){return e.filter(De).flatMap(i=>St(i).filter(ut)).distinct(i=>i.value).toArray().sort((i,a)=>a.value.length-i.value.length).map(i=>this.buildKeywordToken(i,r,!!n?.caseInsensitive))}buildKeywordToken(e,r,n){let i=this.buildKeywordPattern(e,n),a={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,r)};return typeof i=="function"&&(a.LINE_BREAKS=!0),a}buildKeywordPattern(e,r){return r?new RegExp(wu(e.value)):e.value}findLongerAlt(e,r){return r.reduce((n,i)=>{let a=i?.PATTERN;return a?.source&&_u("^"+a.source+"$",e.value)&&n.push(i),n},[])}};var rn=class{static{s(this,"DefaultValueConverter")}convert(e,r){let n=r.grammarSource;if(Gr(n)&&(n=Lu(n)),yt(n)){let i=n.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,r)}return e}runConverter(e,r,n){var i;switch(e.name.toUpperCase()){case"INT":return Gt.convertInt(r);case"STRING":return Gt.convertString(r);case"ID":return Gt.convertID(r)}switch((i=Bu(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return Gt.convertNumber(r);case"boolean":return Gt.convertBoolean(r);case"bigint":return Gt.convertBigint(r);case"date":return Gt.convertDate(r);default:return r}}},Gt;(function(t){function e(c){let f="";for(let d=1;d<c.length-1;d++){let p=c.charAt(d);if(p==="\\"){let m=c.charAt(++d);f+=r(m)}else f+=p}return f}s(e,"convertString"),t.convertString=e;function r(c){switch(c){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return c}}s(r,"convertEscapeCharacter");function n(c){return c.charAt(0)==="^"?c.substring(1):c}s(n,"convertID"),t.convertID=n;function i(c){return parseInt(c)}s(i,"convertInt"),t.convertInt=i;function a(c){return BigInt(c)}s(a,"convertBigint"),t.convertBigint=a;function o(c){return new Date(c)}s(o,"convertDate"),t.convertDate=o;function l(c){return Number(c)}s(l,"convertNumber"),t.convertNumber=l;function u(c){return c.toLowerCase()==="true"}s(u,"convertBoolean"),t.convertBoolean=u})(Gt||(Gt={}));var w={};B(w,Gf(wy(),1));function nf(){return new Promise(t=>{typeof setImmediate>"u"?setTimeout(t,0):setImmediate(t)})}s(nf,"delayNextTick");var Il=0,_y=10;function Sl(){return Il=performance.now(),new w.CancellationTokenSource}s(Sl,"startCancelableOperation");function by(t){_y=t}s(by,"setInterruptionPeriod");var Ut=Symbol("OperationCancelled");function Bt(t){return t===Ut}s(Bt,"isOperationCancelled");async function Te(t){if(t===w.CancellationToken.None)return;let e=performance.now();if(e-Il>=_y&&(Il=e,await nf(),Il=performance.now()),t.isCancellationRequested)throw Ut}s(Te,"interruptAndCheck");var Ye=class{static{s(this,"Deferred")}constructor(){this.promise=new Promise((e,r)=>{this.resolve=n=>(e(n),this),this.reject=n=>(r(n),this)})}};var kl=class t{static{s(this,"FullTextDocument")}constructor(e,r,n,i){this._uri=e,this._languageId=r,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let r=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(r,n)}return this._content}update(e,r){for(let n of e)if(t.isIncremental(n)){let i=Py(n.range),a=this.offsetAt(i.start),o=this.offsetAt(i.end);this._content=this._content.substring(0,a)+n.text+this._content.substring(o,this._content.length);let l=Math.max(i.start.line,0),u=Math.max(i.end.line,0),c=this._lineOffsets,f=Oy(n.text,!1,a);if(u-l===f.length)for(let p=0,m=f.length;p<m;p++)c[p+l+1]=f[p];else f.length<1e4?c.splice(l+1,u-l,...f):this._lineOffsets=c=c.slice(0,l+1).concat(f,c.slice(u+1));let d=n.text.length-(o-a);if(d!==0)for(let p=l+1+f.length,m=c.length;p<m;p++)c[p]=c[p]+d}else if(t.isFull(n))this._content=n.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=r}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Oy(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let r=this.getLineOffsets(),n=0,i=r.length;if(i===0)return{line:0,character:e};for(;n<i;){let o=Math.floor((n+i)/2);r[o]>e?i=o:n=o+1}let a=n-1;return e=this.ensureBeforeEOL(e,r[a]),{line:a,character:e-r[a]}}offsetAt(e){let r=this.getLineOffsets();if(e.line>=r.length)return this._content.length;if(e.line<0)return 0;let n=r[e.line];if(e.character<=0)return n;let i=e.line+1<r.length?r[e.line+1]:this._content.length,a=Math.min(n+e.character,i);return this.ensureBeforeEOL(a,n)}ensureBeforeEOL(e,r){for(;e>r&&Ly(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let r=e;return r!=null&&typeof r.text=="string"&&r.range!==void 0&&(r.rangeLength===void 0||typeof r.rangeLength=="number")}static isFull(e){let r=e;return r!=null&&typeof r.text=="string"&&r.range===void 0&&r.rangeLength===void 0}},Zi;(function(t){function e(i,a,o,l){return new kl(i,a,o,l)}s(e,"create"),t.create=e;function r(i,a,o){if(i instanceof kl)return i.update(a,o),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}s(r,"update"),t.update=r;function n(i,a){let o=i.getText(),l=sf(a.map(qN),(f,d)=>{let p=f.range.start.line-d.range.start.line;return p===0?f.range.start.character-d.range.start.character:p}),u=0,c=[];for(let f of l){let d=i.offsetAt(f.range.start);if(d<u)throw new Error("Overlapping edit");d>u&&c.push(o.substring(u,d)),f.newText.length&&c.push(f.newText),u=i.offsetAt(f.range.end)}return c.push(o.substr(u)),c.join("")}s(n,"applyEdits"),t.applyEdits=n})(Zi||(Zi={}));function sf(t,e){if(t.length<=1)return t;let r=t.length/2|0,n=t.slice(0,r),i=t.slice(r);sf(n,e),sf(i,e);let a=0,o=0,l=0;for(;a<n.length&&o<i.length;)e(n[a],i[o])<=0?t[l++]=n[a++]:t[l++]=i[o++];for(;a<n.length;)t[l++]=n[a++];for(;o<i.length;)t[l++]=i[o++];return t}s(sf,"mergeSort");function Oy(t,e,r=0){let n=e?[r]:[];for(let i=0;i<t.length;i++){let a=t.charCodeAt(i);Ly(a)&&(a===13&&i+1<t.length&&t.charCodeAt(i+1)===10&&i++,n.push(r+i+1))}return n}s(Oy,"computeLineOffsets");function Ly(t){return t===13||t===10}s(Ly,"isEOL");function Py(t){let e=t.start,r=t.end;return e.line>r.line||e.line===r.line&&e.character>r.character?{start:r,end:e}:t}s(Py,"getWellformedRange");function qN(t){let e=Py(t.range);return e!==t.range?{newText:t.newText,range:e}:t}s(qN,"getWellformedEdit");var My;(()=>{"use strict";var t={470:i=>{function a(u){if(typeof u!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(u))}s(a,"e");function o(u,c){for(var f,d="",p=0,m=-1,g=0,y=0;y<=u.length;++y){if(y<u.length)f=u.charCodeAt(y);else{if(f===47)break;f=47}if(f===47){if(!(m===y-1||g===1))if(m!==y-1&&g===2){if(d.length<2||p!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var A=d.lastIndexOf("/");if(A!==d.length-1){A===-1?(d="",p=0):p=(d=d.slice(0,A)).length-1-d.lastIndexOf("/"),m=y,g=0;continue}}else if(d.length===2||d.length===1){d="",p=0,m=y,g=0;continue}}c&&(d.length>0?d+="/..":d="..",p=2)}else d.length>0?d+="/"+u.slice(m+1,y):d=u.slice(m+1,y),p=y-m-1;m=y,g=0}else f===46&&g!==-1?++g:g=-1}return d}s(o,"r");var l={resolve:s(function(){for(var u,c="",f=!1,d=arguments.length-1;d>=-1&&!f;d--){var p;d>=0?p=arguments[d]:(u===void 0&&(u=process.cwd()),p=u),a(p),p.length!==0&&(c=p+"/"+c,f=p.charCodeAt(0)===47)}return c=o(c,!f),f?c.length>0?"/"+c:"/":c.length>0?c:"."},"resolve"),normalize:s(function(u){if(a(u),u.length===0)return".";var c=u.charCodeAt(0)===47,f=u.charCodeAt(u.length-1)===47;return(u=o(u,!c)).length!==0||c||(u="."),u.length>0&&f&&(u+="/"),c?"/"+u:u},"normalize"),isAbsolute:s(function(u){return a(u),u.length>0&&u.charCodeAt(0)===47},"isAbsolute"),join:s(function(){if(arguments.length===0)return".";for(var u,c=0;c<arguments.length;++c){var f=arguments[c];a(f),f.length>0&&(u===void 0?u=f:u+="/"+f)}return u===void 0?".":l.normalize(u)},"join"),relative:s(function(u,c){if(a(u),a(c),u===c||(u=l.resolve(u))===(c=l.resolve(c)))return"";for(var f=1;f<u.length&&u.charCodeAt(f)===47;++f);for(var d=u.length,p=d-f,m=1;m<c.length&&c.charCodeAt(m)===47;++m);for(var g=c.length-m,y=p<g?p:g,A=-1,T=0;T<=y;++T){if(T===y){if(g>y){if(c.charCodeAt(m+T)===47)return c.slice(m+T+1);if(T===0)return c.slice(m+T)}else p>y&&(u.charCodeAt(f+T)===47?A=T:T===0&&(A=0));break}var E=u.charCodeAt(f+T);if(E!==c.charCodeAt(m+T))break;E===47&&(A=T)}var R="";for(T=f+A+1;T<=d;++T)T!==d&&u.charCodeAt(T)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+c.slice(m+A):(m+=A,c.charCodeAt(m)===47&&++m,c.slice(m))},"relative"),_makeLong:s(function(u){return u},"_makeLong"),dirname:s(function(u){if(a(u),u.length===0)return".";for(var c=u.charCodeAt(0),f=c===47,d=-1,p=!0,m=u.length-1;m>=1;--m)if((c=u.charCodeAt(m))===47){if(!p){d=m;break}}else p=!1;return d===-1?f?"/":".":f&&d===1?"//":u.slice(0,d)},"dirname"),basename:s(function(u,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');a(u);var f,d=0,p=-1,m=!0;if(c!==void 0&&c.length>0&&c.length<=u.length){if(c.length===u.length&&c===u)return"";var g=c.length-1,y=-1;for(f=u.length-1;f>=0;--f){var A=u.charCodeAt(f);if(A===47){if(!m){d=f+1;break}}else y===-1&&(m=!1,y=f+1),g>=0&&(A===c.charCodeAt(g)?--g==-1&&(p=f):(g=-1,p=y))}return d===p?p=y:p===-1&&(p=u.length),u.slice(d,p)}for(f=u.length-1;f>=0;--f)if(u.charCodeAt(f)===47){if(!m){d=f+1;break}}else p===-1&&(m=!1,p=f+1);return p===-1?"":u.slice(d,p)},"basename"),extname:s(function(u){a(u);for(var c=-1,f=0,d=-1,p=!0,m=0,g=u.length-1;g>=0;--g){var y=u.charCodeAt(g);if(y!==47)d===-1&&(p=!1,d=g+1),y===46?c===-1?c=g:m!==1&&(m=1):c!==-1&&(m=-1);else if(!p){f=g+1;break}}return c===-1||d===-1||m===0||m===1&&c===d-1&&c===f+1?"":u.slice(c,d)},"extname"),format:s(function(u){if(u===null||typeof u!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof u);return function(c,f){var d=f.dir||f.root,p=f.base||(f.name||"")+(f.ext||"");return d?d===f.root?d+p:d+"/"+p:p}(0,u)},"format"),parse:s(function(u){a(u);var c={root:"",dir:"",base:"",ext:"",name:""};if(u.length===0)return c;var f,d=u.charCodeAt(0),p=d===47;p?(c.root="/",f=1):f=0;for(var m=-1,g=0,y=-1,A=!0,T=u.length-1,E=0;T>=f;--T)if((d=u.charCodeAt(T))!==47)y===-1&&(A=!1,y=T+1),d===46?m===-1?m=T:E!==1&&(E=1):m!==-1&&(E=-1);else if(!A){g=T+1;break}return m===-1||y===-1||E===0||E===1&&m===y-1&&m===g+1?y!==-1&&(c.base=c.name=g===0&&p?u.slice(1,y):u.slice(g,y)):(g===0&&p?(c.name=u.slice(1,m),c.base=u.slice(1,y)):(c.name=u.slice(g,m),c.base=u.slice(g,y)),c.ext=u.slice(m,y)),g>0?c.dir=u.slice(0,g-1):p&&(c.dir="/"),c},"parse"),sep:"/",delimiter:":",win32:null,posix:null};l.posix=l,i.exports=l}},e={};function r(i){var a=e[i];if(a!==void 0)return a.exports;var o=e[i]={exports:{}};return t[i](o,o.exports,r),o.exports}s(r,"r"),r.d=(i,a)=>{for(var o in a)r.o(a,o)&&!r.o(i,o)&&Object.defineProperty(i,o,{enumerable:!0,get:a[o]})},r.o=(i,a)=>Object.prototype.hasOwnProperty.call(i,a),r.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var n={};(()=>{let i;r.r(n),r.d(n,{URI:s(()=>p,"URI"),Utils:s(()=>Wt,"Utils")}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);let a=/^\w[\w\d+.-]*$/,o=/^\//,l=/^\/\//;function u(I,x){if(!I.scheme&&x)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${I.authority}", path: "${I.path}", query: "${I.query}", fragment: "${I.fragment}"}`);if(I.scheme&&!a.test(I.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(I.path){if(I.authority){if(!o.test(I.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(I.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}s(u,"s");let c="",f="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class p{static{s(this,"f")}static isUri(x){return x instanceof p||!!x&&typeof x.authority=="string"&&typeof x.fragment=="string"&&typeof x.path=="string"&&typeof x.query=="string"&&typeof x.scheme=="string"&&typeof x.fsPath=="string"&&typeof x.with=="function"&&typeof x.toString=="function"}scheme;authority;path;query;fragment;constructor(x,$,C,K,M,P=!1){typeof x=="object"?(this.scheme=x.scheme||c,this.authority=x.authority||c,this.path=x.path||c,this.query=x.query||c,this.fragment=x.fragment||c):(this.scheme=function(je,We){return je||We?je:"file"}(x,P),this.authority=$||c,this.path=function(je,We){switch(je){case"https":case"http":case"file":We?We[0]!==f&&(We=f+We):We=f}return We}(this.scheme,C||c),this.query=K||c,this.fragment=M||c,u(this,P))}get fsPath(){return E(this,!1)}with(x){if(!x)return this;let{scheme:$,authority:C,path:K,query:M,fragment:P}=x;return $===void 0?$=this.scheme:$===null&&($=c),C===void 0?C=this.authority:C===null&&(C=c),K===void 0?K=this.path:K===null&&(K=c),M===void 0?M=this.query:M===null&&(M=c),P===void 0?P=this.fragment:P===null&&(P=c),$===this.scheme&&C===this.authority&&K===this.path&&M===this.query&&P===this.fragment?this:new g($,C,K,M,P)}static parse(x,$=!1){let C=d.exec(x);return C?new g(C[2]||c,ke(C[4]||c),ke(C[5]||c),ke(C[7]||c),ke(C[9]||c),$):new g(c,c,c,c,c)}static file(x){let $=c;if(i&&(x=x.replace(/\\/g,f)),x[0]===f&&x[1]===f){let C=x.indexOf(f,2);C===-1?($=x.substring(2),x=f):($=x.substring(2,C),x=x.substring(C)||f)}return new g("file",$,x,c,c)}static from(x){let $=new g(x.scheme,x.authority,x.path,x.query,x.fragment);return u($,!0),$}toString(x=!1){return R(this,x)}toJSON(){return this}static revive(x){if(x){if(x instanceof p)return x;{let $=new g(x);return $._formatted=x.external,$._fsPath=x._sep===m?x.fsPath:null,$}}return x}}let m=i?1:void 0;class g extends p{static{s(this,"l")}_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=E(this,!1)),this._fsPath}toString(x=!1){return x?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){let x={$mid:1};return this._fsPath&&(x.fsPath=this._fsPath,x._sep=m),this._formatted&&(x.external=this._formatted),this.path&&(x.path=this.path),this.scheme&&(x.scheme=this.scheme),this.authority&&(x.authority=this.authority),this.query&&(x.query=this.query),this.fragment&&(x.fragment=this.fragment),x}}let y={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function A(I,x,$){let C,K=-1;for(let M=0;M<I.length;M++){let P=I.charCodeAt(M);if(P>=97&&P<=122||P>=65&&P<=90||P>=48&&P<=57||P===45||P===46||P===95||P===126||x&&P===47||$&&P===91||$&&P===93||$&&P===58)K!==-1&&(C+=encodeURIComponent(I.substring(K,M)),K=-1),C!==void 0&&(C+=I.charAt(M));else{C===void 0&&(C=I.substr(0,M));let je=y[P];je!==void 0?(K!==-1&&(C+=encodeURIComponent(I.substring(K,M)),K=-1),C+=je):K===-1&&(K=M)}}return K!==-1&&(C+=encodeURIComponent(I.substring(K))),C!==void 0?C:I}s(A,"d");function T(I){let x;for(let $=0;$<I.length;$++){let C=I.charCodeAt($);C===35||C===63?(x===void 0&&(x=I.substr(0,$)),x+=y[C]):x!==void 0&&(x+=I[$])}return x!==void 0?x:I}s(T,"p");function E(I,x){let $;return $=I.authority&&I.path.length>1&&I.scheme==="file"?`//${I.authority}${I.path}`:I.path.charCodeAt(0)===47&&(I.path.charCodeAt(1)>=65&&I.path.charCodeAt(1)<=90||I.path.charCodeAt(1)>=97&&I.path.charCodeAt(1)<=122)&&I.path.charCodeAt(2)===58?x?I.path.substr(1):I.path[1].toLowerCase()+I.path.substr(2):I.path,i&&($=$.replace(/\//g,"\\")),$}s(E,"m");function R(I,x){let $=x?T:A,C="",{scheme:K,authority:M,path:P,query:je,fragment:We}=I;if(K&&(C+=K,C+=":"),(M||K==="file")&&(C+=f,C+=f),M){let ie=M.indexOf("@");if(ie!==-1){let wr=M.substr(0,ie);M=M.substr(ie+1),ie=wr.lastIndexOf(":"),ie===-1?C+=$(wr,!1,!1):(C+=$(wr.substr(0,ie),!1,!1),C+=":",C+=$(wr.substr(ie+1),!1,!0)),C+="@"}M=M.toLowerCase(),ie=M.lastIndexOf(":"),ie===-1?C+=$(M,!1,!0):(C+=$(M.substr(0,ie),!1,!0),C+=M.substr(ie))}if(P){if(P.length>=3&&P.charCodeAt(0)===47&&P.charCodeAt(2)===58){let ie=P.charCodeAt(1);ie>=65&&ie<=90&&(P=`/${String.fromCharCode(ie+32)}:${P.substr(3)}`)}else if(P.length>=2&&P.charCodeAt(1)===58){let ie=P.charCodeAt(0);ie>=65&&ie<=90&&(P=`${String.fromCharCode(ie+32)}:${P.substr(2)}`)}C+=$(P,!0,!1)}return je&&(C+="?",C+=$(je,!1,!1)),We&&(C+="#",C+=x?We:A(We,!1,!1)),C}s(R,"y");function O(I){try{return decodeURIComponent(I)}catch{return I.length>3?I.substr(0,3)+O(I.substr(3)):I}}s(O,"v");let L=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ke(I){return I.match(L)?I.replace(L,x=>O(x)):I}s(ke,"C");var un=r(470);let Le=un.posix||un,or="/";var Wt;(function(I){I.joinPath=function(x,...$){return x.with({path:Le.join(x.path,...$)})},I.resolvePath=function(x,...$){let C=x.path,K=!1;C[0]!==or&&(C=or+C,K=!0);let M=Le.resolve(C,...$);return K&&M[0]===or&&!x.authority&&(M=M.substring(1)),x.with({path:M})},I.dirname=function(x){if(x.path.length===0||x.path===or)return x;let $=Le.dirname(x.path);return $.length===1&&$.charCodeAt(0)===46&&($=""),x.with({path:$})},I.basename=function(x){return Le.basename(x.path)},I.extname=function(x){return Le.extname(x.path)}})(Wt||(Wt={}))})(),My=n})();var{URI:Xe,Utils:Qi}=My;var Je;(function(t){t.basename=Qi.basename,t.dirname=Qi.dirname,t.extname=Qi.extname,t.joinPath=Qi.joinPath,t.resolvePath=Qi.resolvePath;function e(i,a){return i?.toString()===a?.toString()}s(e,"equals"),t.equals=e;function r(i,a){let o=typeof i=="string"?i:i.path,l=typeof a=="string"?a:a.path,u=o.split("/").filter(m=>m.length>0),c=l.split("/").filter(m=>m.length>0),f=0;for(;f<u.length&&u[f]===c[f];f++);let d="../".repeat(u.length-f),p=c.slice(f).join("/");return d+p}s(r,"relative"),t.relative=r;function n(i){return Xe.parse(i.toString()).toString()}s(n,"normalize"),t.normalize=n})(Je||(Je={}));var J;(function(t){t[t.Changed=0]="Changed",t[t.Parsed=1]="Parsed",t[t.IndexedContent=2]="IndexedContent",t[t.ComputedScopes=3]="ComputedScopes",t[t.Linked=4]="Linked",t[t.IndexedReferences=5]="IndexedReferences",t[t.Validated=6]="Validated"})(J||(J={}));var ra=class{static{s(this,"DefaultLangiumDocumentFactory")}constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,r=w.CancellationToken.None){let n=await this.fileSystemProvider.readFile(e);return this.createAsync(e,n,r)}fromTextDocument(e,r,n){return r=r??Xe.parse(e.uri),w.CancellationToken.is(n)?this.createAsync(r,e,n):this.create(r,e,n)}fromString(e,r,n){return w.CancellationToken.is(n)?this.createAsync(r,e,n):this.create(r,e,n)}fromModel(e,r){return this.create(r,{$model:e})}create(e,r,n){if(typeof r=="string"){let i=this.parse(e,r,n);return this.createLangiumDocument(i,e,void 0,r)}else if("$model"in r){let i={value:r.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{let i=this.parse(e,r.getText(),n);return this.createLangiumDocument(i,e,r)}}async createAsync(e,r,n){if(typeof r=="string"){let i=await this.parseAsync(e,r,n);return this.createLangiumDocument(i,e,void 0,r)}else{let i=await this.parseAsync(e,r.getText(),n);return this.createLangiumDocument(i,e,r)}}createLangiumDocument(e,r,n,i){let a;if(n)a={parseResult:e,uri:r,state:J.Parsed,references:[],textDocument:n};else{let o=this.createTextDocumentGetter(r,i);a={parseResult:e,uri:r,state:J.Parsed,references:[],get textDocument(){return o()}}}return e.value.$document=a,a}async update(e,r){var n,i;let a=(n=e.parseResult.value.$cstNode)===null||n===void 0?void 0:n.root.fullText,o=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),l=o?o.getText():await this.fileSystemProvider.readFile(e.uri);if(o)Object.defineProperty(e,"textDocument",{value:o});else{let u=this.createTextDocumentGetter(e.uri,l);Object.defineProperty(e,"textDocument",{get:u})}return a!==l&&(e.parseResult=await this.parseAsync(e.uri,l,r),e.parseResult.value.$document=e),e.state=J.Parsed,e}parse(e,r,n){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(r,n)}parseAsync(e,r,n){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(r,n)}createTextDocumentGetter(e,r){let n=this.serviceRegistry,i;return()=>i??(i=Zi.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,r??""))}},na=class{static{s(this,"DefaultLangiumDocuments")}constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return H(this.documentMap.values())}addDocument(e){let r=e.uri.toString();if(this.documentMap.has(r))throw new Error(`A document with the URI '${r}' is already present.`);this.documentMap.set(r,e)}getDocument(e){let r=e.toString();return this.documentMap.get(r)}async getOrCreateDocument(e,r){let n=this.getDocument(e);return n||(n=await this.langiumDocumentFactory.fromUri(e,r),this.addDocument(n),n)}createDocument(e,r,n){if(n)return this.langiumDocumentFactory.fromString(r,e,n).then(i=>(this.addDocument(i),i));{let i=this.langiumDocumentFactory.fromString(r,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){let r=e.toString(),n=this.documentMap.get(r);return n&&(this.serviceRegistry.getServices(e).references.Linker.unlink(n),n.state=J.Changed,n.precomputedScopes=void 0,n.diagnostics=void 0),n}deleteDocument(e){let r=e.toString(),n=this.documentMap.get(r);return n&&(n.state=J.Changed,this.documentMap.delete(r)),n}};var af=Symbol("ref_resolving"),ia=class{static{s(this,"DefaultLinker")}constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,r=w.CancellationToken.None){for(let n of ct(e.parseResult.value))await Te(r),Zn(n).forEach(i=>this.doLink(i,e))}doLink(e,r){var n;let i=e.reference;if(i._ref===void 0){i._ref=af;try{let a=this.getCandidate(e);if(Lr(a))i._ref=a;else if(i._nodeDescription=a,this.langiumDocuments().hasDocument(a.documentUri)){let o=this.loadAstNode(a);i._ref=o??this.createLinkingError(e,a)}else i._ref=void 0}catch(a){console.error(`An error occurred while resolving reference to '${i.$refText}':`,a);let o=(n=a.message)!==null&&n!==void 0?n:String(a);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${o}`})}r.references.push(i)}}unlink(e){for(let r of e.references)delete r._ref,delete r._nodeDescription;e.references=[]}getCandidate(e){let n=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return n??this.createLinkingError(e)}buildReference(e,r,n,i){let a=this,o={$refNode:n,$refText:i,get ref(){var l;if(fe(this._ref))return this._ref;if(Ql(this._nodeDescription)){let u=a.loadAstNode(this._nodeDescription);this._ref=u??a.createLinkingError({reference:o,container:e,property:r},this._nodeDescription)}else if(this._ref===void 0){this._ref=af;let u=hs(e).$document,c=a.getLinkedNode({reference:o,container:e,property:r});if(c.error&&u&&u.state<J.ComputedScopes)return this._ref=void 0;this._ref=(l=c.node)!==null&&l!==void 0?l:c.error,this._nodeDescription=c.descr,u?.references.push(this)}else if(this._ref===af)throw new Error(`Cyclic reference resolution detected: ${a.astNodeLocator.getAstNodePath(e)}/${r} (symbol '${i}')`);return fe(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return Lr(this._ref)?this._ref:void 0}};return o}getLinkedNode(e){var r;try{let n=this.getCandidate(e);if(Lr(n))return{error:n};let i=this.loadAstNode(n);return i?{node:i,descr:n}:{descr:n,error:this.createLinkingError(e,n)}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);let i=(r=n.message)!==null&&r!==void 0?r:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;let r=this.langiumDocuments().getDocument(e.documentUri);if(r)return this.astNodeLocator.getAstNode(r.parseResult.value,e.path)}createLinkingError(e,r){let n=hs(e.container).$document;n&&n.state<J.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${n.uri}).`);let i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:r})}};function Dy(t){return typeof t.name=="string"}s(Dy,"isNamed");var sa=class{static{s(this,"DefaultNameProvider")}getName(e){if(Dy(e))return e.name}getNameNode(e){return As(e.$cstNode,"name")}};var aa=class{static{s(this,"DefaultReferences")}constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){let r=Uu(e),n=e.astNode;if(r&&n){let i=n[r.feature];if(_e(i))return i.ref;if(Array.isArray(i)){for(let a of i)if(_e(a)&&a.$refNode&&a.$refNode.offset<=e.offset&&a.$refNode.end>=e.end)return a.ref}}if(n){let i=this.nameProvider.getNameNode(n);if(i&&(i===e||tu(e,i)))return n}}}findDeclarationNode(e){let r=this.findDeclaration(e);if(r?.$cstNode){let n=this.nameProvider.getNameNode(r);return n??r.$cstNode}}findReferences(e,r){let n=[];if(r.includeDeclaration){let a=this.getReferenceToSelf(e);a&&n.push(a)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return r.documentUri&&(i=i.filter(a=>Je.equals(a.sourceUri,r.documentUri))),n.push(...i),H(n)}getReferenceToSelf(e){let r=this.nameProvider.getNameNode(e);if(r){let n=Fe(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:n.uri,sourcePath:i,targetUri:n.uri,targetPath:i,segment:Mr(r),local:!0}}}};var Rt=class{static{s(this,"MultiMap")}constructor(e){if(this.map=new Map,e)for(let[r,n]of e)this.add(r,n)}get size(){return fn.sum(H(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,r){if(r===void 0)return this.map.delete(e);{let n=this.map.get(e);if(n){let i=n.indexOf(r);if(i>=0)return n.length===1?this.map.delete(e):n.splice(i,1),!0}return!1}}get(e){var r;return(r=this.map.get(e))!==null&&r!==void 0?r:[]}has(e,r){if(r===void 0)return this.map.has(e);{let n=this.map.get(e);return n?n.indexOf(r)>=0:!1}}add(e,r){return this.map.has(e)?this.map.get(e).push(r):this.map.set(e,[r]),this}addAll(e,r){return this.map.has(e)?this.map.get(e).push(...r):this.map.set(e,Array.from(r)),this}forEach(e){this.map.forEach((r,n)=>r.forEach(i=>e(i,n,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return H(this.map.entries()).flatMap(([e,r])=>r.map(n=>[e,n]))}keys(){return H(this.map.keys())}values(){return H(this.map.values()).flat()}entriesGroupedByKey(){return H(this.map.entries())}},nn=class{static{s(this,"BiMap")}get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(let[r,n]of e)this.set(r,n)}clear(){this.map.clear(),this.inverse.clear()}set(e,r){return this.map.set(e,r),this.inverse.set(r,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){let r=this.map.get(e);return r!==void 0?(this.map.delete(e),this.inverse.delete(r),!0):!1}};var oa=class{static{s(this,"DefaultScopeComputation")}constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,r=w.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,r)}async computeExportsForNode(e,r,n=gs,i=w.CancellationToken.None){let a=[];this.exportNode(e,a,r);for(let o of n(e))await Te(i),this.exportNode(o,a,r);return a}exportNode(e,r,n){let i=this.nameProvider.getName(e);i&&r.push(this.descriptions.createDescription(e,i,n))}async computeLocalScopes(e,r=w.CancellationToken.None){let n=e.parseResult.value,i=new Rt;for(let a of St(n))await Te(r),this.processNode(a,e,i);return i}processNode(e,r,n){let i=e.$container;if(i){let a=this.nameProvider.getName(e);a&&n.add(i,this.descriptions.createDescription(e,a,r))}}};var es=class{static{s(this,"StreamScope")}constructor(e,r,n){var i;this.elements=e,this.outerScope=r,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){let r=this.caseInsensitive?this.elements.find(n=>n.name.toLowerCase()===e.toLowerCase()):this.elements.find(n=>n.name===e);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}},la=class{static{s(this,"MapScope")}constructor(e,r,n){var i;this.elements=new Map,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1;for(let a of e){let o=this.caseInsensitive?a.name.toLowerCase():a.name;this.elements.set(o,a)}this.outerScope=r}getElement(e){let r=this.caseInsensitive?e.toLowerCase():e,n=this.elements.get(r);if(n)return n;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=H(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}},YN={getElement(){},getAllElements(){return ss}};var ts=class{static{s(this,"DisposableCache")}constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}},ua=class extends ts{static{s(this,"SimpleCache")}constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,r){this.throwIfDisposed(),this.cache.set(e,r)}get(e,r){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(r){let n=r();return this.cache.set(e,n),n}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}},sn=class extends ts{static{s(this,"ContextCache")}constructor(e){super(),this.cache=new Map,this.converter=e??(r=>r)}has(e,r){return this.throwIfDisposed(),this.cacheForContext(e).has(r)}set(e,r,n){this.throwIfDisposed(),this.cacheForContext(e).set(r,n)}get(e,r,n){this.throwIfDisposed();let i=this.cacheForContext(e);if(i.has(r))return i.get(r);if(n){let a=n();return i.set(r,a),a}else return}delete(e,r){return this.throwIfDisposed(),this.cacheForContext(e).delete(r)}clear(e){if(this.throwIfDisposed(),e){let r=this.converter(e);this.cache.delete(r)}else this.cache.clear()}cacheForContext(e){let r=this.converter(e),n=this.cache.get(r);return n||(n=new Map,this.cache.set(r,n)),n}},Cl=class extends sn{static{s(this,"DocumentCache")}constructor(e,r){super(n=>n.toString()),r?(this.toDispose.push(e.workspace.DocumentBuilder.onDocumentPhase(r,n=>{this.clear(n.uri.toString())})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{for(let a of i)this.clear(a)}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{let a=n.concat(i);for(let o of a)this.clear(o)}))}},rs=class extends ua{static{s(this,"WorkspaceCache")}constructor(e,r){super(),r?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(r,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}};var ca=class{static{s(this,"DefaultScopeProvider")}constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new rs(e.shared)}getScope(e){let r=[],n=this.reflection.getReferenceType(e),i=Fe(e.container).precomputedScopes;if(i){let o=e.container;do{let l=i.get(o);l.length>0&&r.push(H(l).filter(u=>this.reflection.isSubtype(u.type,n))),o=o.$container}while(o)}let a=this.getGlobalScope(n,e);for(let o=r.length-1;o>=0;o--)a=this.createScope(r[o],a);return a}createScope(e,r,n){return new es(H(e),r,n)}createScopeForNodes(e,r,n){let i=H(e).map(a=>{let o=this.nameProvider.getName(a);if(o)return this.descriptions.createDescription(a,o)}).nonNullable();return new es(i,r,n)}getGlobalScope(e,r){return this.globalScopeCache.get(e,()=>new la(this.indexManager.allElements(e)))}};function of(t){return typeof t.$comment=="string"}s(of,"isAstNodeWithComment");function Fy(t){return typeof t=="object"&&!!t&&("$ref"in t||"$error"in t)}s(Fy,"isIntermediateReference");var fa=class{static{s(this,"DefaultJsonSerializer")}constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,r){let n=r??{},i=r?.replacer,a=s((l,u)=>this.replacer(l,u,n),"defaultReplacer"),o=i?(l,u)=>i(l,u,a):a;try{return this.currentDocument=Fe(e),JSON.stringify(e,o,r?.space)}finally{this.currentDocument=void 0}}deserialize(e,r){let n=r??{},i=JSON.parse(e);return this.linkNode(i,i,n),i}replacer(e,r,{refText:n,sourceText:i,textRegions:a,comments:o,uriConverter:l}){var u,c,f,d;if(!this.ignoreProperties.has(e))if(_e(r)){let p=r.ref,m=n?r.$refText:void 0;if(p){let g=Fe(p),y="";this.currentDocument&&this.currentDocument!==g&&(l?y=l(g.uri,r):y=g.uri.toString());let A=this.astNodeLocator.getAstNodePath(p);return{$ref:`${y}#${A}`,$refText:m}}else return{$error:(c=(u=r.error)===null||u===void 0?void 0:u.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:m}}else if(fe(r)){let p;if(a&&(p=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},r)),(!e||r.$document)&&p?.$textRegion&&(p.$textRegion.documentURI=(f=this.currentDocument)===null||f===void 0?void 0:f.uri.toString())),i&&!e&&(p??(p=Object.assign({},r)),p.$sourceText=(d=r.$cstNode)===null||d===void 0?void 0:d.text),o){p??(p=Object.assign({},r));let m=this.commentProvider.getComment(r);m&&(p.$comment=m.replace(/\r/g,""))}return p??r}else return r}addAstNodeRegionWithAssignmentsTo(e){let r=s(n=>({offset:n.offset,end:n.end,length:n.length,range:n.range}),"createDocumentSegment");if(e.$cstNode){let n=e.$textRegion=r(e.$cstNode),i=n.assignments={};return Object.keys(e).filter(a=>!a.startsWith("$")).forEach(a=>{let o=Mu(e.$cstNode,a).map(r);o.length!==0&&(i[a]=o)}),e}}linkNode(e,r,n,i,a,o){for(let[u,c]of Object.entries(e))if(Array.isArray(c))for(let f=0;f<c.length;f++){let d=c[f];Fy(d)?c[f]=this.reviveReference(e,u,r,d,n):fe(d)&&this.linkNode(d,r,n,e,u,f)}else Fy(c)?e[u]=this.reviveReference(e,u,r,c,n):fe(c)&&this.linkNode(c,r,n,e,u);let l=e;l.$container=i,l.$containerProperty=a,l.$containerIndex=o}reviveReference(e,r,n,i,a){let o=i.$refText,l=i.$error;if(i.$ref){let u=this.getRefNode(n,i.$ref,a.uriConverter);if(fe(u))return o||(o=this.nameProvider.getName(u)),{$refText:o??"",ref:u};l=u}if(l){let u={$refText:o??""};return u.error={container:e,property:r,message:l,reference:u},u}else return}getRefNode(e,r,n){try{let i=r.indexOf("#");if(i===0){let u=this.astNodeLocator.getAstNode(e,r.substring(1));return u||"Could not resolve path: "+r}if(i<0){let u=n?n(r):Xe.parse(r),c=this.langiumDocuments.getDocument(u);return c?c.parseResult.value:"Could not find document for URI: "+r}let a=n?n(r.substring(0,i)):Xe.parse(r.substring(0,i)),o=this.langiumDocuments.getDocument(a);if(!o)return"Could not find document for URI: "+r;if(i===r.length-1)return o.parseResult.value;let l=this.astNodeLocator.getAstNode(o.parseResult.value,r.substring(i+1));return l||"Could not resolve URI: "+r}catch(i){return String(i)}}};var da=class{static{s(this,"DefaultServiceRegistry")}get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e?.workspace.TextDocuments}register(e){let r=e.LanguageMetaData;for(let n of r.fileExtensions)this.fileExtensionMap.has(n)&&console.warn(`The file extension ${n} is used by multiple languages. It is now assigned to '${r.languageId}'.`),this.fileExtensionMap.set(n,e);this.languageIdMap.set(r.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var r,n;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");let i=(n=(r=this.textDocuments)===null||r===void 0?void 0:r.get(e))===null||n===void 0?void 0:n.languageId;if(i!==void 0){let l=this.languageIdMap.get(i);if(l)return l}let a=Je.extname(e),o=this.fileExtensionMap.get(a);if(!o)throw i?new Error(`The service registry contains no services for the extension '${a}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${a}'.`);return o}hasServices(e){try{return this.getServices(e),!0}catch{return!1}}get all(){return Array.from(this.languageIdMap.values())}};function an(t){return{code:t}}s(an,"diagnosticData");var ns;(function(t){t.all=["fast","slow","built-in"]})(ns||(ns={}));var pa=class{static{s(this,"ValidationRegistry")}constructor(e){this.entries=new Rt,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,r=this,n="fast"){if(n==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(let[i,a]of Object.entries(e)){let o=a;if(Array.isArray(o))for(let l of o){let u={check:this.wrapValidationException(l,r),category:n};this.addEntry(i,u)}else if(typeof o=="function"){let l={check:this.wrapValidationException(o,r),category:n};this.addEntry(i,l)}else It(o)}}wrapValidationException(e,r){return async(n,i,a)=>{await this.handleException(()=>e.call(r,n,i,a),"An error occurred during validation",i,n)}}async handleException(e,r,n,i){try{await e()}catch(a){if(Bt(a))throw a;console.error(`${r}:`,a),a instanceof Error&&a.stack&&console.error(a.stack);let o=a instanceof Error?a.message:String(a);n("error",`${r}: ${o}`,{node:i})}}addEntry(e,r){if(e==="AstNode"){this.entries.add("AstNode",r);return}for(let n of this.reflection.getAllSubTypes(e))this.entries.add(n,r)}getChecks(e,r){let n=H(this.entries.get(e)).concat(this.entries.get("AstNode"));return r&&(n=n.filter(i=>r.includes(i.category))),n.map(i=>i.check)}registerBeforeDocument(e,r=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",r))}registerAfterDocument(e,r=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",r))}wrapPreparationException(e,r,n){return async(i,a,o,l)=>{await this.handleException(()=>e.call(n,i,a,o,l),r,a,i)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}};var ma=class{static{s(this,"DefaultDocumentValidator")}constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,r={},n=w.CancellationToken.None){let i=e.parseResult,a=[];if(await Te(n),(!r.categories||r.categories.includes("built-in"))&&(this.processLexingErrors(i,a,r),r.stopAfterLexingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.LexingError})||(this.processParsingErrors(i,a,r),r.stopAfterParsingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.ParsingError}))||(this.processLinkingErrors(e,a,r),r.stopAfterLinkingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.LinkingError}))))return a;try{a.push(...await this.validateAst(i.value,r,n))}catch(o){if(Bt(o))throw o;console.error("An error occurred during validation:",o)}return await Te(n),a}processLexingErrors(e,r,n){var i,a,o;let l=[...e.lexerErrors,...(a=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&a!==void 0?a:[]];for(let u of l){let c=(o=u.severity)!==null&&o!==void 0?o:"error",f={severity:Nl(c),range:{start:{line:u.line-1,character:u.column-1},end:{line:u.line-1,character:u.column+u.length-1}},message:u.message,data:Uy(c),source:this.getSource()};r.push(f)}}processParsingErrors(e,r,n){for(let i of e.parserErrors){let a;if(isNaN(i.token.startOffset)){if("previousToken"in i){let o=i.previousToken;if(isNaN(o.startOffset)){let l={line:0,character:0};a={start:l,end:l}}else{let l={line:o.endLine-1,character:o.endColumn};a={start:l,end:l}}}}else a=dn(i.token);if(a){let o={severity:Nl("error"),range:a,message:i.message,data:an(pt.ParsingError),source:this.getSource()};r.push(o)}}}processLinkingErrors(e,r,n){for(let i of e.references){let a=i.error;if(a){let o={node:a.container,property:a.property,index:a.index,data:{code:pt.LinkingError,containerType:a.container.$type,property:a.property,refText:a.reference.$refText}};r.push(this.toDiagnostic("error",a.message,o))}}}async validateAst(e,r,n=w.CancellationToken.None){let i=[],a=s((o,l,u)=>{i.push(this.toDiagnostic(o,l,u))},"acceptor");return await this.validateAstBefore(e,r,a,n),await this.validateAstNodes(e,r,a,n),await this.validateAstAfter(e,r,a,n),i}async validateAstBefore(e,r,n,i=w.CancellationToken.None){var a;let o=this.validationRegistry.checksBefore;for(let l of o)await Te(i),await l(e,n,(a=r.categories)!==null&&a!==void 0?a:[],i)}async validateAstNodes(e,r,n,i=w.CancellationToken.None){await Promise.all(ct(e).map(async a=>{await Te(i);let o=this.validationRegistry.getChecks(a.$type,r.categories);for(let l of o)await l(a,n,i)}))}async validateAstAfter(e,r,n,i=w.CancellationToken.None){var a;let o=this.validationRegistry.checksAfter;for(let l of o)await Te(i),await l(e,n,(a=r.categories)!==null&&a!==void 0?a:[],i)}toDiagnostic(e,r,n){return{message:r,range:Gy(n),severity:Nl(e),code:n.code,codeDescription:n.codeDescription,tags:n.tags,relatedInformation:n.relatedInformation,data:n.data,source:this.getSource()}}getSource(){return this.metadata.languageId}};function Gy(t){if(t.range)return t.range;let e;return typeof t.property=="string"?e=As(t.node.$cstNode,t.property,t.index):typeof t.keyword=="string"&&(e=Fu(t.node.$cstNode,t.keyword,t.index)),e??(e=t.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}s(Gy,"getDiagnosticRange");function Nl(t){switch(t){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+t)}}s(Nl,"toDiagnosticSeverity");function Uy(t){switch(t){case"error":return an(pt.LexingError);case"warning":return an(pt.LexingWarning);case"info":return an(pt.LexingInfo);case"hint":return an(pt.LexingHint);default:throw new Error("Invalid diagnostic severity: "+t)}}s(Uy,"toDiagnosticData");var pt;(function(t){t.LexingError="lexing-error",t.LexingWarning="lexing-warning",t.LexingInfo="lexing-info",t.LexingHint="lexing-hint",t.ParsingError="parsing-error",t.LinkingError="linking-error"})(pt||(pt={}));var ha=class{static{s(this,"DefaultAstNodeDescriptionProvider")}constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,r,n){let i=n??Fe(e);r??(r=this.nameProvider.getName(e));let a=this.astNodeLocator.getAstNodePath(e);if(!r)throw new Error(`Node at path ${a} has no name.`);let o,l=s(()=>{var u;return o??(o=Mr((u=this.nameProvider.getNameNode(e))!==null&&u!==void 0?u:e.$cstNode))},"nameSegmentGetter");return{node:e,name:r,get nameSegment(){return l()},selectionSegment:Mr(e.$cstNode),type:e.$type,documentUri:i.uri,path:a}}},ga=class{static{s(this,"DefaultReferenceDescriptionProvider")}constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,r=w.CancellationToken.None){let n=[],i=e.parseResult.value;for(let a of ct(i))await Te(r),Zn(a).filter(o=>!Lr(o)).forEach(o=>{let l=this.createDescription(o);l&&n.push(l)});return n}createDescription(e){let r=e.reference.$nodeDescription,n=e.reference.$refNode;if(!r||!n)return;let i=Fe(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:r.documentUri,targetPath:r.path,segment:Mr(n),local:Je.equals(r.documentUri,i)}}};var ya=class{static{s(this,"DefaultAstNodeLocator")}constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){let r=this.getAstNodePath(e.$container),n=this.getPathSegment(e);return r+this.segmentSeparator+n}return""}getPathSegment({$containerProperty:e,$containerIndex:r}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return r!==void 0?e+this.indexSeparator+r:e}getAstNode(e,r){return r.split(this.segmentSeparator).reduce((i,a)=>{if(!i||a.length===0)return i;let o=a.indexOf(this.indexSeparator);if(o>0){let l=a.substring(0,o),u=parseInt(a.substring(o+1)),c=i[l];return c?.[u]}return i[a]},e)}};var ae={};B(ae,Gf(ef(),1));var xa=class{static{s(this,"DefaultConfigurationProvider")}constructor(e){this._ready=new Ye,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new ae.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var r,n;this.workspaceConfig=(n=(r=e.capabilities.workspace)===null||r===void 0?void 0:r.configuration)!==null&&n!==void 0?n:!1}async initialized(e){if(this.workspaceConfig){if(e.register){let r=this.serviceRegistry.all;e.register({section:r.map(n=>this.toSectionName(n.LanguageMetaData.languageId))})}if(e.fetchConfiguration){let r=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),n=await e.fetchConfiguration(r);r.forEach((i,a)=>{this.updateSectionConfiguration(i.section,n[a])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(r=>{let n=e.settings[r];this.updateSectionConfiguration(r,n),this.onConfigurationSectionUpdateEmitter.fire({section:r,configuration:n})})}updateSectionConfiguration(e,r){this.settings[e]=r}async getConfiguration(e,r){await this.ready;let n=this.toSectionName(e);if(this.settings[n])return this.settings[n][r]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}};var $r;(function(t){function e(r){return{dispose:s(async()=>await r(),"dispose")}}s(e,"create"),t.create=e})($r||($r={}));var Ta=class{static{s(this,"DefaultDocumentBuilder")}constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new Rt,this.documentPhaseListeners=new Rt,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=J.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,r={},n=w.CancellationToken.None){var i,a;for(let o of e){let l=o.uri.toString();if(o.state===J.Validated){if(typeof r.validation=="boolean"&&r.validation)o.state=J.IndexedReferences,o.diagnostics=void 0,this.buildState.delete(l);else if(typeof r.validation=="object"){let u=this.buildState.get(l),c=(i=u?.result)===null||i===void 0?void 0:i.validationChecks;if(c){let d=((a=r.validation.categories)!==null&&a!==void 0?a:ns.all).filter(p=>!c.includes(p));d.length>0&&(this.buildState.set(l,{completed:!1,options:{validation:Object.assign(Object.assign({},r.validation),{categories:d})},result:u.result}),o.state=J.IndexedReferences)}}}else this.buildState.delete(l)}this.currentState=J.Changed,await this.emitUpdate(e.map(o=>o.uri),[]),await this.buildDocuments(e,r,n)}async update(e,r,n=w.CancellationToken.None){this.currentState=J.Changed;for(let o of r)this.langiumDocuments.deleteDocument(o),this.buildState.delete(o.toString()),this.indexManager.remove(o);for(let o of e){if(!this.langiumDocuments.invalidateDocument(o)){let u=this.langiumDocumentFactory.fromModel({$type:"INVALID"},o);u.state=J.Changed,this.langiumDocuments.addDocument(u)}this.buildState.delete(o.toString())}let i=H(e).concat(r).map(o=>o.toString()).toSet();this.langiumDocuments.all.filter(o=>!i.has(o.uri.toString())&&this.shouldRelink(o,i)).forEach(o=>{this.serviceRegistry.getServices(o.uri).references.Linker.unlink(o),o.state=Math.min(o.state,J.ComputedScopes),o.diagnostics=void 0}),await this.emitUpdate(e,r),await Te(n);let a=this.sortDocuments(this.langiumDocuments.all.filter(o=>{var l;return o.state<J.Linked||!(!((l=this.buildState.get(o.uri.toString()))===null||l===void 0)&&l.completed)}).toArray());await this.buildDocuments(a,this.updateBuildOptions,n)}async emitUpdate(e,r){await Promise.all(this.updateListeners.map(n=>n(e,r)))}sortDocuments(e){let r=0,n=e.length-1;for(;r<n;){for(;r<e.length&&this.hasTextDocument(e[r]);)r++;for(;n>=0&&!this.hasTextDocument(e[n]);)n--;r<n&&([e[r],e[n]]=[e[n],e[r]])}return e}hasTextDocument(e){var r;return!!(!((r=this.textDocuments)===null||r===void 0)&&r.get(e.uri))}shouldRelink(e,r){return e.references.some(n=>n.error!==void 0)?!0:this.indexManager.isAffected(e,r)}onUpdate(e){return this.updateListeners.push(e),$r.create(()=>{let r=this.updateListeners.indexOf(e);r>=0&&this.updateListeners.splice(r,1)})}async buildDocuments(e,r,n){this.prepareBuild(e,r),await this.runCancelable(e,J.Parsed,n,a=>this.langiumDocumentFactory.update(a,n)),await this.runCancelable(e,J.IndexedContent,n,a=>this.indexManager.updateContent(a,n)),await this.runCancelable(e,J.ComputedScopes,n,async a=>{let o=this.serviceRegistry.getServices(a.uri).references.ScopeComputation;a.precomputedScopes=await o.computeLocalScopes(a,n)}),await this.runCancelable(e,J.Linked,n,a=>this.serviceRegistry.getServices(a.uri).references.Linker.link(a,n)),await this.runCancelable(e,J.IndexedReferences,n,a=>this.indexManager.updateReferences(a,n));let i=e.filter(a=>this.shouldValidate(a));await this.runCancelable(i,J.Validated,n,a=>this.validate(a,n));for(let a of e){let o=this.buildState.get(a.uri.toString());o&&(o.completed=!0)}}prepareBuild(e,r){for(let n of e){let i=n.uri.toString(),a=this.buildState.get(i);(!a||a.completed)&&this.buildState.set(i,{completed:!1,options:r,result:a?.result})}}async runCancelable(e,r,n,i){let a=e.filter(l=>l.state<r);for(let l of a)await Te(n),await i(l),l.state=r,await this.notifyDocumentPhase(l,r,n);let o=e.filter(l=>l.state===r);await this.notifyBuildPhase(o,r,n),this.currentState=r}onBuildPhase(e,r){return this.buildPhaseListeners.add(e,r),$r.create(()=>{this.buildPhaseListeners.delete(e,r)})}onDocumentPhase(e,r){return this.documentPhaseListeners.add(e,r),$r.create(()=>{this.documentPhaseListeners.delete(e,r)})}waitUntil(e,r,n){let i;if(r&&"path"in r?i=r:n=r,n??(n=w.CancellationToken.None),i){let a=this.langiumDocuments.getDocument(i);if(a&&a.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):n.isCancellationRequested?Promise.reject(Ut):new Promise((a,o)=>{let l=this.onBuildPhase(e,()=>{if(l.dispose(),u.dispose(),i){let c=this.langiumDocuments.getDocument(i);a(c?.uri)}else a(void 0)}),u=n.onCancellationRequested(()=>{l.dispose(),u.dispose(),o(Ut)})})}async notifyDocumentPhase(e,r,n){let a=this.documentPhaseListeners.get(r).slice();for(let o of a)try{await o(e,n)}catch(l){if(!Bt(l))throw l}}async notifyBuildPhase(e,r,n){if(e.length===0)return;let a=this.buildPhaseListeners.get(r).slice();for(let o of a)await Te(n),await o(e,n)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,r){var n,i;let a=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,o=this.getBuildOptions(e).validation,l=typeof o=="object"?o:void 0,u=await a.validateDocument(e,l,r);e.diagnostics?e.diagnostics.push(...u):e.diagnostics=u;let c=this.buildState.get(e.uri.toString());if(c){(n=c.result)!==null&&n!==void 0||(c.result={});let f=(i=l?.categories)!==null&&i!==void 0?i:ns.all;c.result.validationChecks?c.result.validationChecks.push(...f):c.result.validationChecks=[...f]}}getBuildOptions(e){var r,n;return(n=(r=this.buildState.get(e.uri.toString()))===null||r===void 0?void 0:r.options)!==null&&n!==void 0?n:{}}};var Ra=class{static{s(this,"DefaultIndexManager")}constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new sn,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,r){let n=Fe(e).uri,i=[];return this.referenceIndex.forEach(a=>{a.forEach(o=>{Je.equals(o.targetUri,n)&&o.targetPath===r&&i.push(o)})}),H(i)}allElements(e,r){let n=H(this.symbolIndex.keys());return r&&(n=n.filter(i=>!r||r.has(i))),n.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,r){var n;return r?this.symbolByTypeIndex.get(e,r,()=>{var a;return((a=this.symbolIndex.get(e))!==null&&a!==void 0?a:[]).filter(l=>this.astReflection.isSubtype(l.type,r))}):(n=this.symbolIndex.get(e))!==null&&n!==void 0?n:[]}remove(e){let r=e.toString();this.symbolIndex.delete(r),this.symbolByTypeIndex.clear(r),this.referenceIndex.delete(r)}async updateContent(e,r=w.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,r),a=e.uri.toString();this.symbolIndex.set(a,i),this.symbolByTypeIndex.clear(a)}async updateReferences(e,r=w.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,r);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,r){let n=this.referenceIndex.get(e.uri.toString());return n?n.some(i=>!i.local&&r.has(i.targetUri.toString())):!1}};var Aa=class{static{s(this,"DefaultWorkspaceManager")}constructor(e){this.initialBuildOptions={},this._ready=new Ye,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var r;this.folders=(r=e.workspaceFolders)!==null&&r!==void 0?r:void 0}initialized(e){return this.mutex.write(r=>{var n;return this.initializeWorkspace((n=this.folders)!==null&&n!==void 0?n:[],r)})}async initializeWorkspace(e,r=w.CancellationToken.None){let n=await this.performStartup(e);await Te(r),await this.documentBuilder.build(n,this.initialBuildOptions,r)}async performStartup(e){let r=this.serviceRegistry.all.flatMap(a=>a.LanguageMetaData.fileExtensions),n=[],i=s(a=>{n.push(a),this.langiumDocuments.hasDocument(a.uri)||this.langiumDocuments.addDocument(a)},"collector");return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(a=>[a,this.getRootFolder(a)]).map(async a=>this.traverseFolder(...a,r,i))),this._ready.resolve(),n}loadAdditionalDocuments(e,r){return Promise.resolve()}getRootFolder(e){return Xe.parse(e.uri)}async traverseFolder(e,r,n,i){let a=await this.fileSystemProvider.readDirectory(r);await Promise.all(a.map(async o=>{if(this.includeEntry(e,o,n)){if(o.isDirectory)await this.traverseFolder(e,o.uri,n,i);else if(o.isFile){let l=await this.langiumDocuments.getOrCreateDocument(o.uri);i(l)}}}))}includeEntry(e,r,n){let i=Je.basename(r.uri);if(i.startsWith("."))return!1;if(r.isDirectory)return i!=="node_modules"&&i!=="out";if(r.isFile){let a=Je.extname(r.uri);return n.includes(a)}return!1}};var Ea=class{static{s(this,"DefaultLexerErrorMessageProvider")}buildUnexpectedCharactersMessage(e,r,n,i,a){return Oi.buildUnexpectedCharactersMessage(e,r,n,i,a)}buildUnableToPopLexerModeMessage(e){return Oi.buildUnableToPopLexerModeMessage(e)}},$l={mode:"full"},on=class{static{s(this,"DefaultLexer")}constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;let r=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(r);let n=lf(r)?Object.values(r):r,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new ue(n,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,r=$l){var n,i,a;let o=this.chevrotainLexer.tokenize(e);return{tokens:o.tokens,errors:o.errors,hidden:(n=o.groups.hidden)!==null&&n!==void 0?n:[],report:(a=(i=this.tokenBuilder).flushLexingReport)===null||a===void 0?void 0:a.call(i,e)}}toTokenTypeDictionary(e){if(lf(e))return e;let r=uf(e)?Object.values(e.modes).flat():e,n={};return r.forEach(i=>n[i.name]=i),n}};function wl(t){return Array.isArray(t)&&(t.length===0||"name"in t[0])}s(wl,"isTokenTypeArray");function uf(t){return t&&"modes"in t&&"defaultMode"in t}s(uf,"isIMultiModeLexerDefinition");function lf(t){return!wl(t)&&!uf(t)}s(lf,"isTokenTypeDictionary");function df(t,e,r){let n,i;typeof t=="string"?(i=e,n=r):(i=t.range.start,n=e),i||(i=j.create(0,0));let a=Wy(t),o=mf(n),l=JN({lines:a,position:i,options:o});return r$({index:0,tokens:l,position:i})}s(df,"parseJSDoc");function pf(t,e){let r=mf(e),n=Wy(t);if(n.length===0)return!1;let i=n[0],a=n[n.length-1],o=r.start,l=r.end;return!!o?.exec(i)&&!!l?.exec(a)}s(pf,"isJSDoc");function Wy(t){let e="";return typeof t=="string"?e=t:e=t.text,e.split(Nu)}s(Wy,"getLines");var By=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,XN=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function JN(t){var e,r,n;let i=[],a=t.position.line,o=t.position.character;for(let l=0;l<t.lines.length;l++){let u=l===0,c=l===t.lines.length-1,f=t.lines[l],d=0;if(u&&t.options.start){let m=(e=t.options.start)===null||e===void 0?void 0:e.exec(f);m&&(d=m.index+m[0].length)}else{let m=(r=t.options.line)===null||r===void 0?void 0:r.exec(f);m&&(d=m.index+m[0].length)}if(c){let m=(n=t.options.end)===null||n===void 0?void 0:n.exec(f);m&&(f=f.substring(0,m.index))}if(f=f.substring(0,t$(f)),ff(f,d)>=f.length){if(i.length>0){let m=j.create(a,o);i.push({type:"break",content:"",range:U.create(m,m)})}}else{By.lastIndex=d;let m=By.exec(f);if(m){let g=m[0],y=m[1],A=j.create(a,o+d),T=j.create(a,o+d+g.length);i.push({type:"tag",content:y,range:U.create(A,T)}),d+=g.length,d=ff(f,d)}if(d<f.length){let g=f.substring(d),y=Array.from(g.matchAll(XN));i.push(...ZN(y,g,a,o+d))}}a++,o=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}s(JN,"tokenize");function ZN(t,e,r,n){let i=[];if(t.length===0){let a=j.create(r,n),o=j.create(r,n+e.length);i.push({type:"text",content:e,range:U.create(a,o)})}else{let a=0;for(let l of t){let u=l.index,c=e.substring(a,u);c.length>0&&i.push({type:"text",content:e.substring(a,u),range:U.create(j.create(r,a+n),j.create(r,u+n))});let f=c.length+1,d=l[1];if(i.push({type:"inline-tag",content:d,range:U.create(j.create(r,a+f+n),j.create(r,a+f+d.length+n))}),f+=d.length,l.length===4){f+=l[2].length;let p=l[3];i.push({type:"text",content:p,range:U.create(j.create(r,a+f+n),j.create(r,a+f+p.length+n))})}else i.push({type:"text",content:"",range:U.create(j.create(r,a+f+n),j.create(r,a+f+n))});a=u+l[0].length}let o=e.substring(a);o.length>0&&i.push({type:"text",content:o,range:U.create(j.create(r,a+n),j.create(r,a+n+o.length))})}return i}s(ZN,"buildInlineTokens");var QN=/\S/,e$=/\s*$/;function ff(t,e){let r=t.substring(e).match(QN);return r?e+r.index:t.length}s(ff,"skipWhitespace");function t$(t){let e=t.match(e$);if(e&&typeof e.index=="number")return e.index}s(t$,"lastCharacter");function r$(t){var e,r,n,i;let a=j.create(t.position.line,t.position.character);if(t.tokens.length===0)return new _l([],U.create(a,a));let o=[];for(;t.index<t.tokens.length;){let c=n$(t,o[o.length-1]);c&&o.push(c)}let l=(r=(e=o[0])===null||e===void 0?void 0:e.range.start)!==null&&r!==void 0?r:a,u=(i=(n=o[o.length-1])===null||n===void 0?void 0:n.range.end)!==null&&i!==void 0?i:a;return new _l(o,U.create(l,u))}s(r$,"parseJSDocComment");function n$(t,e){let r=t.tokens[t.index];if(r.type==="tag")return Hy(t,!1);if(r.type==="text"||r.type==="inline-tag")return Ky(t);i$(r,e),t.index++}s(n$,"parseJSDocElement");function i$(t,e){if(e){let r=new bl("",t.range);"inlines"in e?e.inlines.push(r):e.content.inlines.push(r)}}s(i$,"appendEmptyLine");function Ky(t){let e=t.tokens[t.index],r=e,n=e,i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(s$(t)),n=e,e=t.tokens[t.index];return new Ia(i,U.create(r.range.start,n.range.end))}s(Ky,"parseJSDocText");function s$(t){return t.tokens[t.index].type==="inline-tag"?Hy(t,!0):Vy(t)}s(s$,"parseJSDocInline");function Hy(t,e){let r=t.tokens[t.index++],n=r.content.substring(1),i=t.tokens[t.index];if(i?.type==="text")if(e){let a=Vy(t);return new va(n,new Ia([a],a.range),e,U.create(r.range.start,a.range.end))}else{let a=Ky(t);return new va(n,a,e,U.create(r.range.start,a.range.end))}else{let a=r.range;return new va(n,new Ia([],a),e,a)}}s(Hy,"parseJSDocTag");function Vy(t){let e=t.tokens[t.index++];return new bl(e.content,e.range)}s(Vy,"parseJSDocLine");function mf(t){if(!t)return mf({start:"/**",end:"*/",line:"*"});let{start:e,end:r,line:n}=t;return{start:cf(e,!0),end:cf(r,!1),line:cf(n,!0)}}s(mf,"normalizeOptions");function cf(t,e){if(typeof t=="string"||typeof t=="object"){let r=typeof t=="string"?Kr(t):t.source;return e?new RegExp(`^\\s*${r}`):new RegExp(`\\s*${r}\\s*$`)}else return t}s(cf,"normalizeOption");var _l=class{static{s(this,"JSDocCommentImpl")}constructor(e,r){this.elements=e,this.range=r}getTag(e){return this.getAllTags().find(r=>r.name===e)}getTags(e){return this.getAllTags().filter(r=>r.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(let r of this.elements)if(e.length===0)e=r.toString();else{let n=r.toString();e+=jy(e)+n}return e.trim()}toMarkdown(e){let r="";for(let n of this.elements)if(r.length===0)r=n.toMarkdown(e);else{let i=n.toMarkdown(e);r+=jy(r)+i}return r.trim()}},va=class{static{s(this,"JSDocTagImpl")}constructor(e,r,n,i){this.name=e,this.content=r,this.inline=n,this.range=i}toString(){let e=`@${this.name}`,r=this.content.toString();return this.content.inlines.length===1?e=`${e} ${r}`:this.content.inlines.length>1&&(e=`${e}
${r}`),this.inline?`{${e}}`:e}toMarkdown(e){var r,n;return(n=(r=e?.renderTag)===null||r===void 0?void 0:r.call(e,this))!==null&&n!==void 0?n:this.toMarkdownDefault(e)}toMarkdownDefault(e){let r=this.content.toMarkdown(e);if(this.inline){let a=a$(this.name,r,e??{});if(typeof a=="string")return a}let n="";e?.tag==="italic"||e?.tag===void 0?n="*":e?.tag==="bold"?n="**":e?.tag==="bold-italic"&&(n="***");let i=`${n}@${this.name}${n}`;return this.content.inlines.length===1?i=`${i} \u2014 ${r}`:this.content.inlines.length>1&&(i=`${i}
${r}`),this.inline?`{${i}}`:i}};function a$(t,e,r){var n,i;if(t==="linkplain"||t==="linkcode"||t==="link"){let a=e.indexOf(" "),o=e;if(a>0){let u=ff(e,a);o=e.substring(u),e=e.substring(0,a)}return(t==="linkcode"||t==="link"&&r.link==="code")&&(o=`\`${o}\``),(i=(n=r.renderLink)===null||n===void 0?void 0:n.call(r,e,o))!==null&&i!==void 0?i:o$(e,o)}}s(a$,"renderInlineTag");function o$(t,e){try{return Xe.parse(t,!0),`[${e}](${t})`}catch{return t}}s(o$,"renderLinkDefault");var Ia=class{static{s(this,"JSDocTextImpl")}constructor(e,r){this.inlines=e,this.range=r}toString(){let e="";for(let r=0;r<this.inlines.length;r++){let n=this.inlines[r],i=this.inlines[r+1];e+=n.toString(),i&&i.range.start.line>n.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let r="";for(let n=0;n<this.inlines.length;n++){let i=this.inlines[n],a=this.inlines[n+1];r+=i.toMarkdown(e),a&&a.range.start.line>i.range.start.line&&(r+=`
`)}return r}},bl=class{static{s(this,"JSDocLineImpl")}constructor(e,r){this.text=e,this.range=r}toString(){return this.text}toMarkdown(){return this.text}};function jy(t){return t.endsWith(`
`)?`
`:`

`}s(jy,"fillNewlines");var Sa=class{static{s(this,"JSDocDocumentationProvider")}constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){let r=this.commentProvider.getComment(e);if(r&&pf(r))return df(r).toMarkdown({renderLink:s((i,a)=>this.documentationLinkRenderer(e,i,a),"renderLink"),renderTag:s(i=>this.documentationTagRenderer(e,i),"renderTag")})}documentationLinkRenderer(e,r,n){var i;let a=(i=this.findNameInPrecomputedScopes(e,r))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,r);if(a&&a.nameSegment){let o=a.nameSegment.range.start.line+1,l=a.nameSegment.range.start.character+1,u=a.documentUri.with({fragment:`L${o},${l}`});return`[${n}](${u.toString()})`}else return}documentationTagRenderer(e,r){}findNameInPrecomputedScopes(e,r){let i=Fe(e).precomputedScopes;if(!i)return;let a=e;do{let l=i.get(a).find(u=>u.name===r);if(l)return l;a=a.$container}while(a)}findNameInGlobalScope(e,r){return this.indexManager.allElements().find(i=>i.name===r)}};var ka=class{static{s(this,"DefaultCommentProvider")}constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var r;return of(e)?e.$comment:(r=nu(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||r===void 0?void 0:r.text}};var Ca=class{static{s(this,"DefaultAsyncParser")}constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,r){return Promise.resolve(this.syncParser.parse(e))}},hf=class{static{s(this,"AbstractThreadedAsyncParser")}constructor(e){this.threadCount=8,this.terminationDelay=200,this.workerPool=[],this.queue=[],this.hydrator=e.serializer.Hydrator}initializeWorkers(){for(;this.workerPool.length<this.threadCount;){let e=this.createWorker();e.onReady(()=>{if(this.queue.length>0){let r=this.queue.shift();r&&(e.lock(),r.resolve(e))}}),this.workerPool.push(e)}}async parse(e,r){let n=await this.acquireParserWorker(r),i=new Ye,a,o=r.onCancellationRequested(()=>{a=setTimeout(()=>{this.terminateWorker(n)},this.terminationDelay)});return n.parse(e).then(l=>{let u=this.hydrator.hydrate(l);i.resolve(u)}).catch(l=>{i.reject(l)}).finally(()=>{o.dispose(),clearTimeout(a)}),i.promise}terminateWorker(e){e.terminate();let r=this.workerPool.indexOf(e);r>=0&&this.workerPool.splice(r,1)}async acquireParserWorker(e){this.initializeWorkers();for(let n of this.workerPool)if(n.ready)return n.lock(),n;let r=new Ye;return e.onCancellationRequested(()=>{let n=this.queue.indexOf(r);n>=0&&this.queue.splice(n,1),r.reject(Ut)}),this.queue.push(r),r.promise}},gf=class{static{s(this,"ParserWorker")}get ready(){return this._ready}get onReady(){return this.onReadyEmitter.event}constructor(e,r,n,i){this.onReadyEmitter=new ae.Emitter,this.deferred=new Ye,this._ready=!0,this._parsing=!1,this.sendMessage=e,this._terminate=i,r(a=>{let o=a;this.deferred.resolve(o),this.unlock()}),n(a=>{this.deferred.reject(a),this.unlock()})}terminate(){this.deferred.reject(Ut),this._terminate()}lock(){this._ready=!1}unlock(){this._parsing=!1,this._ready=!0,this.onReadyEmitter.fire()}parse(e){if(this._parsing)throw new Error("Parser worker is busy");return this._parsing=!0,this.deferred=new Ye,this.sendMessage(e),this.deferred.promise}};var Na=class{static{s(this,"DefaultWorkspaceLock")}constructor(){this.previousTokenSource=new w.CancellationTokenSource,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();let r=Sl();return this.previousTokenSource=r,this.enqueue(this.writeQueue,e,r.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,r,n=w.CancellationToken.None){let i=new Ye,a={action:r,deferred:i,cancellationToken:n};return e.push(a),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;let e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:r,deferred:n,cancellationToken:i})=>{try{let a=await Promise.resolve().then(()=>r(i));n.resolve(a)}catch(a){Bt(a)?n.resolve(void 0):n.reject(a)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}};var $a=class{static{s(this,"DefaultHydrator")}constructor(e){this.grammarElementIdMap=new nn,this.tokenTypeIdMap=new nn,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(r=>Object.assign(Object.assign({},r),{message:r.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){let r=new Map,n=new Map;for(let i of ct(e))r.set(i,{});if(e.$cstNode)for(let i of Pr(e.$cstNode))n.set(i,{});return{astNodes:r,cstNodes:n}}dehydrateAstNode(e,r){let n=r.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(n.$cstNode=this.dehydrateCstNode(e.$cstNode,r));for(let[i,a]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(a)){let o=[];n[i]=o;for(let l of a)fe(l)?o.push(this.dehydrateAstNode(l,r)):_e(l)?o.push(this.dehydrateReference(l,r)):o.push(l)}else fe(a)?n[i]=this.dehydrateAstNode(a,r):_e(a)?n[i]=this.dehydrateReference(a,r):a!==void 0&&(n[i]=a);return n}dehydrateReference(e,r){let n={};return n.$refText=e.$refText,e.$refNode&&(n.$refNode=r.cstNodes.get(e.$refNode)),n}dehydrateCstNode(e,r){let n=r.cstNodes.get(e);return is(e)?n.fullText=e.fullText:n.grammarSource=this.getGrammarElementId(e.grammarSource),n.hidden=e.hidden,n.astNode=r.astNodes.get(e.astNode),ht(e)?n.content=e.content.map(i=>this.dehydrateCstNode(i,r)):ur(e)&&(n.tokenType=e.tokenType.name,n.offset=e.offset,n.length=e.length,n.startLine=e.range.start.line,n.startColumn=e.range.start.character,n.endLine=e.range.end.line,n.endColumn=e.range.end.character),n}hydrate(e){let r=e.value,n=this.createHydrationContext(r);return"$cstNode"in r&&this.hydrateCstNode(r.$cstNode,n),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(r,n)}}createHydrationContext(e){let r=new Map,n=new Map;for(let a of ct(e))r.set(a,{});let i;if(e.$cstNode)for(let a of Pr(e.$cstNode)){let o;"fullText"in a?(o=new qi(a.fullText),i=o):"content"in a?o=new en:"tokenType"in a&&(o=this.hydrateCstLeafNode(a)),o&&(n.set(a,o),o.root=i)}return{astNodes:r,cstNodes:n}}hydrateAstNode(e,r){let n=r.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode&&(n.$cstNode=r.cstNodes.get(e.$cstNode));for(let[i,a]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(a)){let o=[];n[i]=o;for(let l of a)fe(l)?o.push(this.setParent(this.hydrateAstNode(l,r),n)):_e(l)?o.push(this.hydrateReference(l,n,i,r)):o.push(l)}else fe(a)?n[i]=this.setParent(this.hydrateAstNode(a,r),n):_e(a)?n[i]=this.hydrateReference(a,n,i,r):a!==void 0&&(n[i]=a);return n}setParent(e,r){return e.$container=r,e}hydrateReference(e,r,n,i){return this.linker.buildReference(r,n,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,r,n=0){let i=r.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=r.astNodes.get(e.astNode),ht(i))for(let a of e.content){let o=this.hydrateCstNode(a,r,n++);i.content.push(o)}return i}hydrateCstLeafNode(e){let r=this.getTokenType(e.tokenType),n=e.offset,i=e.length,a=e.startLine,o=e.startColumn,l=e.endLine,u=e.endColumn,c=e.hidden;return new Qr(n,i,{start:{line:a,character:o},end:{line:l,character:u}},r,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(let r of ct(this.grammar))ds(r)&&this.grammarElementIdMap.set(r,e++)}};function yf(t){return{documentation:{CommentProvider:s(e=>new ka(e),"CommentProvider"),DocumentationProvider:s(e=>new Sa(e),"DocumentationProvider")},parser:{AsyncParser:s(e=>new Ca(e),"AsyncParser"),GrammarConfig:s(e=>Wu(e),"GrammarConfig"),LangiumParser:s(e=>qc(e),"LangiumParser"),CompletionParser:s(e=>zc(e),"CompletionParser"),ValueConverter:s(()=>new rn,"ValueConverter"),TokenBuilder:s(()=>new ar,"TokenBuilder"),Lexer:s(e=>new on(e),"Lexer"),ParserErrorMessageProvider:s(()=>new Yi,"ParserErrorMessageProvider"),LexerErrorMessageProvider:s(()=>new Ea,"LexerErrorMessageProvider")},workspace:{AstNodeLocator:s(()=>new ya,"AstNodeLocator"),AstNodeDescriptionProvider:s(e=>new ha(e),"AstNodeDescriptionProvider"),ReferenceDescriptionProvider:s(e=>new ga(e),"ReferenceDescriptionProvider")},references:{Linker:s(e=>new ia(e),"Linker"),NameProvider:s(()=>new sa,"NameProvider"),ScopeProvider:s(e=>new ca(e),"ScopeProvider"),ScopeComputation:s(e=>new oa(e),"ScopeComputation"),References:s(e=>new aa(e),"References")},serializer:{Hydrator:s(e=>new $a(e),"Hydrator"),JsonSerializer:s(e=>new fa(e),"JsonSerializer")},validation:{DocumentValidator:s(e=>new ma(e),"DocumentValidator"),ValidationRegistry:s(e=>new pa(e),"ValidationRegistry")},shared:s(()=>t.shared,"shared")}}s(yf,"createDefaultCoreModule");function xf(t){return{ServiceRegistry:s(e=>new da(e),"ServiceRegistry"),workspace:{LangiumDocuments:s(e=>new na(e),"LangiumDocuments"),LangiumDocumentFactory:s(e=>new ra(e),"LangiumDocumentFactory"),DocumentBuilder:s(e=>new Ta(e),"DocumentBuilder"),IndexManager:s(e=>new Ra(e),"IndexManager"),WorkspaceManager:s(e=>new Aa(e),"WorkspaceManager"),FileSystemProvider:s(e=>t.fileSystemProvider(e),"FileSystemProvider"),WorkspaceLock:s(()=>new Na,"WorkspaceLock"),ConfigurationProvider:s(e=>new xa(e),"ConfigurationProvider")}}}s(xf,"createDefaultSharedCoreModule");var Tf;(function(t){t.merge=(e,r)=>Ol(Ol({},e),r)})(Tf||(Tf={}));function Ll(t,e,r,n,i,a,o,l,u){let c=[t,e,r,n,i,a,o,l,u].reduce(Ol,{});return Jy(c)}s(Ll,"inject");var Yy=Symbol("isProxy");function Xy(t){if(t&&t[Yy])for(let e of Object.values(t))Xy(e);return t}s(Xy,"eagerLoad");function Jy(t,e){let r=new Proxy({},{deleteProperty:s(()=>!1,"deleteProperty"),set:s(()=>{throw new Error("Cannot set property on injected service container")},"set"),get:s((n,i)=>i===Yy?!0:qy(n,i,t,e||r),"get"),getOwnPropertyDescriptor:s((n,i)=>(qy(n,i,t,e||r),Object.getOwnPropertyDescriptor(n,i)),"getOwnPropertyDescriptor"),has:s((n,i)=>i in t,"has"),ownKeys:s(()=>[...Object.getOwnPropertyNames(t)],"ownKeys")});return r}s(Jy,"_inject");var zy=Symbol();function qy(t,e,r,n){if(e in t){if(t[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:t[e]});if(t[e]===zy)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return t[e]}else if(e in r){let i=r[e];t[e]=zy;try{t[e]=typeof i=="function"?i(n):Jy(i,n)}catch(a){throw t[e]=a instanceof Error?a:void 0,a}return t[e]}else return}s(qy,"_resolve");function Ol(t,e){if(e){for(let[r,n]of Object.entries(e))if(n!==void 0){let i=t[r];i!==null&&n!==null&&typeof i=="object"&&typeof n=="object"?t[r]=Ol(i,n):t[r]=n}}return t}s(Ol,"_merge");var Rf={indentTokenName:"INDENT",dedentTokenName:"DEDENT",whitespaceTokenName:"WS",ignoreIndentationDelimiters:[]},ln;(function(t){t.REGULAR="indentation-sensitive",t.IGNORE_INDENTATION="ignore-indentation"})(ln||(ln={}));var Pl=class extends ar{static{s(this,"IndentationAwareTokenBuilder")}constructor(e=Rf){super(),this.indentationStack=[0],this.whitespaceRegExp=/[ \t]+/y,this.options=Object.assign(Object.assign({},Rf),e),this.indentTokenType=Sr({name:this.options.indentTokenName,pattern:this.indentMatcher.bind(this),line_breaks:!1}),this.dedentTokenType=Sr({name:this.options.dedentTokenName,pattern:this.dedentMatcher.bind(this),line_breaks:!1})}buildTokens(e,r){let n=super.buildTokens(e,r);if(!wl(n))throw new Error("Invalid tokens built by default builder");let{indentTokenName:i,dedentTokenName:a,whitespaceTokenName:o,ignoreIndentationDelimiters:l}=this.options,u,c,f,d=[];for(let p of n){for(let[m,g]of l)p.name===m?p.PUSH_MODE=ln.IGNORE_INDENTATION:p.name===g&&(p.POP_MODE=!0);p.name===a?u=p:p.name===i?c=p:p.name===o?f=p:d.push(p)}if(!u||!c||!f)throw new Error("Some indentation/whitespace tokens not found!");return l.length>0?{modes:{[ln.REGULAR]:[u,c,...d,f],[ln.IGNORE_INDENTATION]:[...d,f]},defaultMode:ln.REGULAR}:[u,c,f,...d]}flushLexingReport(e){let r=super.flushLexingReport(e);return Object.assign(Object.assign({},r),{remainingDedents:this.flushRemainingDedents(e)})}isStartOfLine(e,r){return r===0||`\r
`.includes(e[r-1])}matchWhitespace(e,r,n,i){var a;this.whitespaceRegExp.lastIndex=r;let o=this.whitespaceRegExp.exec(e);return{currIndentLevel:(a=o?.[0].length)!==null&&a!==void 0?a:0,prevIndentLevel:this.indentationStack.at(-1),match:o}}createIndentationTokenInstance(e,r,n,i){let a=this.getLineNumber(r,i);return rr(e,n,i,i+n.length,a,a,1,n.length)}getLineNumber(e,r){return e.substring(0,r).split(/\r\n|\r|\n/).length}indentMatcher(e,r,n,i){if(!this.isStartOfLine(e,r))return null;let{currIndentLevel:a,prevIndentLevel:o,match:l}=this.matchWhitespace(e,r,n,i);return a<=o?null:(this.indentationStack.push(a),l)}dedentMatcher(e,r,n,i){var a,o,l,u;if(!this.isStartOfLine(e,r))return null;let{currIndentLevel:c,prevIndentLevel:f,match:d}=this.matchWhitespace(e,r,n,i);if(c>=f)return null;let p=this.indentationStack.lastIndexOf(c);if(p===-1)return this.diagnostics.push({severity:"error",message:`Invalid dedent level ${c} at offset: ${r}. Current indentation stack: ${this.indentationStack}`,offset:r,length:(o=(a=d?.[0])===null||a===void 0?void 0:a.length)!==null&&o!==void 0?o:0,line:this.getLineNumber(e,r),column:1}),null;let m=this.indentationStack.length-p-1,g=(u=(l=e.substring(0,r).match(/[\r\n]+$/))===null||l===void 0?void 0:l[0].length)!==null&&u!==void 0?u:1;for(let y=0;y<m;y++){let A=this.createIndentationTokenInstance(this.dedentTokenType,e,"",r-(g-1));n.push(A),this.indentationStack.pop()}return null}buildTerminalToken(e){let r=super.buildTerminalToken(e),{indentTokenName:n,dedentTokenName:i,whitespaceTokenName:a}=this.options;return r.name===n?this.indentTokenType:r.name===i?this.dedentTokenType:r.name===a?Sr({name:a,pattern:this.whitespaceRegExp,group:ue.SKIPPED}):r}flushRemainingDedents(e){let r=[];for(;this.indentationStack.length>1;)r.push(this.createIndentationTokenInstance(this.dedentTokenType,e,"",e.length)),this.indentationStack.pop();return this.indentationStack=[0],r}},Af=class extends on{static{s(this,"IndentationAwareLexer")}constructor(e){if(super(e),e.parser.TokenBuilder instanceof Pl)this.indentationTokenBuilder=e.parser.TokenBuilder;else throw new Error("IndentationAwareLexer requires an accompanying IndentationAwareTokenBuilder")}tokenize(e,r=$l){let n=super.tokenize(e),i=n.report;r?.mode==="full"&&n.tokens.push(...i.remainingDedents),i.remainingDedents=[];let{indentTokenType:a,dedentTokenType:o}=this.indentationTokenBuilder,l=a.tokenTypeIdx,u=o.tokenTypeIdx,c=[],f=n.tokens.length-1;for(let d=0;d<f;d++){let p=n.tokens[d],m=n.tokens[d+1];if(p.tokenTypeIdx===l&&m.tokenTypeIdx===u){d++;continue}c.push(p)}return f>=0&&c.push(n.tokens[f]),n.tokens=c,n}};var W={};br(W,{AstUtils:()=>qa,BiMap:()=>nn,Cancellation:()=>w,ContextCache:()=>sn,CstUtils:()=>Ga,DONE_RESULT:()=>Me,Deferred:()=>Ye,Disposable:()=>$r,DisposableCache:()=>ts,DocumentCache:()=>Cl,EMPTY_STREAM:()=>ss,ErrorWithLocation:()=>Dr,GrammarUtils:()=>Qa,MultiMap:()=>Rt,OperationCancelled:()=>Ut,Reduction:()=>fn,RegExpUtils:()=>Ja,SimpleCache:()=>ua,StreamImpl:()=>it,TreeStreamImpl:()=>Et,URI:()=>Xe,UriUtils:()=>Je,WorkspaceCache:()=>rs,assertUnreachable:()=>It,delayNextTick:()=>nf,interruptAndCheck:()=>Te,isOperationCancelled:()=>Bt,loadGrammarFromJson:()=>jt,setInterruptionPeriod:()=>by,startCancelableOperation:()=>Sl,stream:()=>H});B(W,ae);var Ml=class{static{s(this,"EmptyFileSystemProvider")}readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}},Ef={fileSystemProvider:s(()=>new Ml,"fileSystemProvider")};var l$={Grammar:s(()=>{},"Grammar"),LanguageMetaData:s(()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"}),"LanguageMetaData")},u$={AstReflection:s(()=>new Jn,"AstReflection")};function c$(){let t=Ll(xf(Ef),u$),e=Ll(yf({shared:t}),l$);return t.ServiceRegistry.register(e),e}s(c$,"createMinimalGrammarServices");function jt(t){var e;let r=c$(),n=r.serializer.JsonSerializer.deserialize(t);return r.shared.workspace.LangiumDocumentFactory.fromModel(n,Xe.parse(`memory://${(e=n.name)!==null&&e!==void 0?e:"grammar"}.langium`)),n}s(jt,"loadGrammarFromJson");B(Re,W);var Zy="Statement";var Kl="Architecture";function Y6(t){return At.isInstance(t,Kl)}s(Y6,"isArchitecture");var Dl="Axis";var wa="Branch";function X6(t){return At.isInstance(t,wa)}s(X6,"isBranch");var Fl="Checkout";var Gl="CherryPicking";var vf="ClassDefStatement";var _a="Commit";function J6(t){return At.isInstance(t,_a)}s(J6,"isCommit");var If="Curve";var Sf="Edge";var kf="Entry";var ba="GitGraph";function Z6(t){return At.isInstance(t,ba)}s(Z6,"isGitGraph");var Cf="Group";var Hl="Info";function Q6(t){return At.isInstance(t,Hl)}s(Q6,"isInfo");var Ul="Item";var Nf="Junction";var Oa="Merge";function e8(t){return At.isInstance(t,Oa)}s(e8,"isMerge");var $f="Option";var Vl="Packet";function t8(t){return At.isInstance(t,Vl)}s(t8,"isPacket");var zl="PacketBlock";function r8(t){return At.isInstance(t,zl)}s(r8,"isPacketBlock");var ql="Pie";function n8(t){return At.isInstance(t,ql)}s(n8,"isPie");var Yl="PieSection";function i8(t){return At.isInstance(t,Yl)}s(i8,"isPieSection");var wf="Radar";var _f="Service";var Xl="Treemap";function s8(t){return At.isInstance(t,Xl)}s(s8,"isTreemap");var bf="TreemapRow";var Bl="Direction";var jl="Leaf";var Wl="Section";var La=class extends Or{static{s(this,"MermaidAstReflection")}getAllTypes(){return[Kl,Dl,wa,Fl,Gl,vf,_a,If,Bl,Sf,kf,ba,Cf,Hl,Ul,Nf,jl,Oa,$f,Vl,zl,ql,Yl,wf,Wl,_f,Zy,Xl,bf]}computeIsSubtype(e,r){switch(e){case wa:case Fl:case Gl:case _a:case Oa:return this.isSubtype(Zy,r);case Bl:return this.isSubtype(ba,r);case jl:case Wl:return this.isSubtype(Ul,r);default:return!1}}getReferenceType(e){let r=`${e.container.$type}:${e.property}`;switch(r){case"Entry:axis":return Dl;default:throw new Error(`${r} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Kl:return{name:Kl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case Dl:return{name:Dl,properties:[{name:"label"},{name:"name"}]};case wa:return{name:wa,properties:[{name:"name"},{name:"order"}]};case Fl:return{name:Fl,properties:[{name:"branch"}]};case Gl:return{name:Gl,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case vf:return{name:vf,properties:[{name:"className"},{name:"styleText"}]};case _a:return{name:_a,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case If:return{name:If,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case Sf:return{name:Sf,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case kf:return{name:kf,properties:[{name:"axis"},{name:"value"}]};case ba:return{name:ba,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case Cf:return{name:Cf,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case Hl:return{name:Hl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case Ul:return{name:Ul,properties:[{name:"classSelector"},{name:"name"}]};case Nf:return{name:Nf,properties:[{name:"id"},{name:"in"}]};case Oa:return{name:Oa,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case $f:return{name:$f,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Vl:return{name:Vl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case zl:return{name:zl,properties:[{name:"bits"},{name:"end"},{name:"label"},{name:"start"}]};case ql:return{name:ql,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case Yl:return{name:Yl,properties:[{name:"label"},{name:"value"}]};case wf:return{name:wf,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case _f:return{name:_f,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case Xl:return{name:Xl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"},{name:"TreemapRows",defaultValue:[]}]};case bf:return{name:bf,properties:[{name:"indent"},{name:"item"}]};case Bl:return{name:Bl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};case jl:return{name:jl,properties:[{name:"classSelector"},{name:"name"},{name:"value"}]};case Wl:return{name:Wl,properties:[{name:"classSelector"},{name:"name"}]};default:return{name:e,properties:[]}}}},At=new La;var Qy,ax=s(()=>Qy??(Qy=jt(`{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@7"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"InfoGrammar"),ex,ox=s(()=>ex??(ex=jt(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}],"cardinality":"?"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"+"},{"$type":"Assignment","feature":"bits","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]}]},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),tx,lx=s(()=>tx??(tx=jt(`{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PieGrammar"),rx,ux=s(()=>rx??(rx=jt(`{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@18"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@19"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"ArchitectureGrammar"),nx,cx=s(()=>nx??(nx=jt(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@14"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"REFERENCE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),ix,fx=s(()=>ix??(ix=jt(`{"$type":"Grammar","isDeclared":true,"name":"Radar","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@2"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@16"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"interfaces":[{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@2"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar"),sx,dx=s(()=>sx??(sx=jt(`{"$type":"Grammar","isDeclared":true,"name":"Treemap","rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"ParserRule","entry":true,"name":"Treemap","returnType":{"$ref":"#/interfaces@4"},"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"TreemapRows","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"TREEMAP_KEYWORD","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap-beta"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"CLASS_DEF","definition":{"$type":"RegexToken","regex":"/classDef\\\\s+([a-zA-Z_][a-zA-Z0-9_]+)(?:\\\\s+([^;\\\\r\\\\n]*))?(?:;)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STYLE_SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":::"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"COMMA","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":","}},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WS","definition":{"$type":"RegexToken","regex":"/[ \\\\t]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"ML_COMMENT","definition":{"$type":"RegexToken","regex":"/\\\\%\\\\%[^\\\\n]*/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"NL","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false},{"$type":"ParserRule","name":"TreemapRow","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"indent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"item","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"ClassDef","dataType":"string","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Item","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Section","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Leaf","returnType":{"$ref":"#/interfaces@2"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INDENTATION","definition":{"$type":"RegexToken","regex":"/[ \\\\t]{1,}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID2","definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER2","definition":{"$type":"RegexToken","regex":"/[0-9_\\\\.\\\\,]+/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"MyNumber","dataType":"number","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"STRING2","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"interfaces":[{"$type":"Interface","name":"Item","attributes":[{"$type":"TypeAttribute","name":"name","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"classSelector","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Section","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[]},{"$type":"Interface","name":"Leaf","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}]},{"$type":"Interface","name":"ClassDefStatement","attributes":[{"$type":"TypeAttribute","name":"className","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"styleText","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false}],"superTypes":[]},{"$type":"Interface","name":"Treemap","attributes":[{"$type":"TypeAttribute","name":"TreemapRows","type":{"$type":"ArrayType","elementType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@14"}}},"isOptional":false},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[],"$comment":"/**\\n * Treemap grammar for Langium\\n * Converted from mindmap grammar\\n *\\n * The ML_COMMENT and NL hidden terminals handle whitespace, comments, and newlines\\n * before the treemap keyword, allowing for empty lines and comments before the\\n * treemap declaration.\\n */"}`)),"TreemapGrammar");var f$={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},d$={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},p$={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},m$={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},h$={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},g$={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},y$={languageId:"treemap",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},p8={AstReflection:s(()=>new La,"AstReflection")},m8={Grammar:s(()=>ax(),"Grammar"),LanguageMetaData:s(()=>f$,"LanguageMetaData"),parser:{}},h8={Grammar:s(()=>ox(),"Grammar"),LanguageMetaData:s(()=>d$,"LanguageMetaData"),parser:{}},g8={Grammar:s(()=>lx(),"Grammar"),LanguageMetaData:s(()=>p$,"LanguageMetaData"),parser:{}},y8={Grammar:s(()=>ux(),"Grammar"),LanguageMetaData:s(()=>m$,"LanguageMetaData"),parser:{}},x8={Grammar:s(()=>cx(),"Grammar"),LanguageMetaData:s(()=>h$,"LanguageMetaData"),parser:{}},T8={Grammar:s(()=>fx(),"Grammar"),LanguageMetaData:s(()=>g$,"LanguageMetaData"),parser:{}},R8={Grammar:s(()=>dx(),"Grammar"),LanguageMetaData:s(()=>y$,"LanguageMetaData"),parser:{}};var px=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,mx=/accTitle[\t ]*:([^\n\r]*)/,hx=/title([\t ][^\n\r]*|)/;var x$={ACC_DESCR:px,ACC_TITLE:mx,TITLE:hx},Of=class extends rn{static{s(this,"AbstractMermaidValueConverter")}runConverter(e,r,n){let i=this.runCommonConverter(e,r,n);return i===void 0&&(i=this.runCustomConverter(e,r,n)),i===void 0?super.runConverter(e,r,n):i}runCommonConverter(e,r,n){let i=x$[e.name];if(i===void 0)return;let a=i.exec(r);if(a!==null){if(a[1]!==void 0)return a[1].trim().replace(/[\t ]{2,}/gm," ");if(a[2]!==void 0)return a[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},gx=class extends Of{static{s(this,"CommonValueConverter")}runCustomConverter(e,r,n){}};var Lf=class extends ar{static{s(this,"AbstractMermaidTokenBuilder")}constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,r,n){let i=super.buildKeywordTokens(e,r,n);return i.forEach(a=>{this.keywords.has(a.name)&&a.PATTERN!==void 0&&(a.PATTERN=new RegExp(a.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},yx=class extends Lf{static{s(this,"CommonTokenBuilder")}};export{s as a,yf as b,xf as c,Ll as d,Ef as e,Re as f,Zy as g,Kl as h,Y6 as i,wa as j,X6 as k,_a as l,J6 as m,ba as n,Z6 as o,Hl as p,Q6 as q,Oa as r,e8 as s,Vl as t,t8 as u,zl as v,r8 as w,ql as x,n8 as y,Yl as z,i8 as A,wf as B,Xl as C,s8 as D,p8 as E,m8 as F,h8 as G,g8 as H,y8 as I,x8 as J,T8 as K,R8 as L,Of as M,gx as N,Lf as O,yx as P};
/*! Bundled license information:

lodash-es/lodash.js:
  (**
   * @license
   * Lodash (Custom Build) <https://lodash.com/>
   * Build: `lodash modularize exports="es" -o ./`
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/
