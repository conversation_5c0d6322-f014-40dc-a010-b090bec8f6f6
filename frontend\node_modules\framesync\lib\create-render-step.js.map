{"version": 3, "file": "create-render-step.js", "sourceRoot": "", "sources": ["../src/create-render-step.ts"], "names": [], "mappings": "AAEA,MAAM,UAAU,gBAAgB,CAAC,YAAwB;IAKrD,IAAI,KAAK,GAAc,EAAE,CAAA;IACzB,IAAI,cAAc,GAAc,EAAE,CAAA;IAKlC,IAAI,QAAQ,GAAG,CAAC,CAAA;IAMhB,IAAI,YAAY,GAAG,KAAK,CAAA;IAExB,IAAI,cAAc,GAAG,KAAK,CAAA;IAK1B,MAAM,WAAW,GAAG,IAAI,OAAO,EAAW,CAAA;IAE1C,MAAM,IAAI,GAAS;QAIf,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,GAAG,KAAK,EAAE,SAAS,GAAG,KAAK,EAAE,EAAE;YACzD,MAAM,iBAAiB,GAAG,SAAS,IAAI,YAAY,CAAA;YACnD,MAAM,MAAM,GAAG,iBAAiB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAA;YAEzD,IAAI,SAAS;gBAAE,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAGxC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBACjC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAGrB,IAAI,iBAAiB,IAAI,YAAY;oBAAE,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAA;aACjE;YAED,OAAO,QAAQ,CAAA;QACnB,CAAC;QAKD,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE;YACjB,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;YAC9C,IAAI,KAAK,KAAK,CAAC,CAAC;gBAAE,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YAEjD,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAChC,CAAC;QAKD,OAAO,EAAE,CAAC,SAAS,EAAE,EAAE;YAMnB,IAAI,YAAY,EAAE;gBACd,cAAc,GAAG,IAAI,CAAA;gBACrB,OAAM;aACT;YAED,YAAY,GAAG,IAAI,CAGlB;YAAA,CAAC,KAAK,EAAE,cAAc,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;YAGlD,cAAc,CAAC,MAAM,GAAG,CAAC,CAAA;YAGzB,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAA;YAEvB,IAAI,QAAQ,EAAE;gBACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;oBAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;oBAEzB,QAAQ,CAAC,SAAS,CAAC,CAAA;oBAEnB,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;wBAC3B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;wBACvB,YAAY,EAAE,CAAA;qBACjB;iBACJ;aACJ;YAED,YAAY,GAAG,KAAK,CAAA;YAEpB,IAAI,cAAc,EAAE;gBAChB,cAAc,GAAG,KAAK,CAAA;gBACtB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;aAC1B;QACL,CAAC;KACJ,CAAA;IAED,OAAO,IAAI,CAAA;AACf,CAAC"}