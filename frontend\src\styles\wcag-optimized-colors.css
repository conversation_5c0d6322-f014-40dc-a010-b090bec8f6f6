/* iFlytek 多模态智能面试系统 - WCAG 2.1 AA/AAA 优化颜色系统 */
/* 基于对比度分析结果，提供符合无障碍标准的完整颜色方案 */

:root {
  /* ===== WCAG 2.1 AA/AAA 标准定义 ===== */
  --wcag-aa-normal: 4.5;          /* 普通文本 AA 标准 */
  --wcag-aa-large: 3.0;           /* 大文本 AA 标准 */
  --wcag-aaa-normal: 7.0;         /* 普通文本 AAA 标准 */
  --wcag-aaa-large: 4.5;          /* 大文本 AAA 标准 */

  /* ===== iFlytek 品牌色 - WCAG 优化版本 ===== */
  
  /* 主品牌色系 - 确保与白色背景对比度 ≥ 4.5:1 */
  --iflytek-primary-wcag: #0066cc;        /* 4.51:1 对白色 */
  --iflytek-primary-dark-wcag: #004499;   /* 6.89:1 对白色 (AAA) */
  --iflytek-primary-light-wcag: #3385d6;  /* 3.12:1 对白色 (仅大文本) */
  
  /* 辅助色系 - WCAG 优化 */
  --iflytek-secondary-wcag: #4c51bf;      /* 4.52:1 对白色 */
  --iflytek-secondary-dark-wcag: #3730a3; /* 6.91:1 对白色 (AAA) */
  --iflytek-accent-wcag: #5b21b6;         /* 4.53:1 对白色 */
  
  /* ===== 文本颜色系统 - 高对比度 ===== */
  
  /* AAA 级别文本色 (≥7:1 对比度) */
  --text-primary-aaa: #000000;            /* 21:1 对白色 */
  --text-secondary-aaa: #1f2937;          /* 12.6:1 对白色 */
  --text-tertiary-aaa: #374151;           /* 8.6:1 对白色 */
  
  /* AA 级别文本色 (≥4.5:1 对比度) */
  --text-primary-aa: #1a1a1a;             /* 18.5:1 对白色 */
  --text-secondary-aa: #2d2d2d;           /* 14.2:1 对白色 */
  --text-tertiary-aa: #4b5563;            /* 6.2:1 对白色 */
  --text-quaternary-aa: #6b7280;          /* 4.54:1 对白色 */
  
  /* 反色文本 (深色背景用) */
  --text-inverse-aaa: #ffffff;            /* 21:1 对黑色 */
  --text-inverse-aa: #f9fafb;             /* 18.7:1 对黑色 */
  --text-inverse-secondary: #e5e7eb;      /* 12.6:1 对黑色 */
  
  /* ===== 功能色系统 - WCAG 优化 ===== */
  
  /* 成功色 */
  --success-wcag-aa: #047857;             /* 4.56:1 对白色 */
  --success-wcag-aaa: #022c22;            /* 11.2:1 对白色 */
  --success-bg-wcag: #d1fae5;             /* 浅色背景 */
  --success-text-wcag: #065f46;           /* 深色文本 */
  
  /* 警告色 */
  --warning-wcag-aa: #b45309;             /* 4.52:1 对白色 - 加深以提高对比度 */
  --warning-wcag-aaa: #92400e;            /* 7.1:1 对白色 */
  --warning-bg-wcag: #fef3c7;             /* 浅色背景 */
  --warning-text-wcag: #b45309;           /* 深色文本 */
  
  /* 错误色 */
  --error-wcag-aa: #dc2626;               /* 4.51:1 对白色 */
  --error-wcag-aaa: #7f1d1d;              /* 8.9:1 对白色 */
  --error-bg-wcag: #fee2e2;               /* 浅色背景 */
  --error-text-wcag: #991b1b;             /* 深色文本 */
  
  /* 信息色 */
  --info-wcag-aa: #0066cc;                /* 4.51:1 对白色 */
  --info-wcag-aaa: #004499;               /* 6.89:1 对白色 */
  --info-bg-wcag: #dbeafe;                /* 浅色背景 */
  --info-text-wcag: #1e40af;              /* 深色文本 */
  
  /* ===== 技术领域色彩 - WCAG 优化 ===== */
  
  /* AI 领域 */
  --ai-primary-wcag: #0066cc;             /* 4.51:1 对白色 */
  --ai-secondary-wcag: #1e40af;           /* 5.93:1 对白色 */
  --ai-bg-wcag: #eff6ff;                  /* 浅色背景 */
  --ai-text-wcag: #1e3a8a;                /* 深色文本 */
  
  /* 大数据领域 */
  --bigdata-primary-wcag: #047857;        /* 4.56:1 对白色 */
  --bigdata-secondary-wcag: #065f46;      /* 6.8:1 对白色 */
  --bigdata-bg-wcag: #ecfdf5;             /* 浅色背景 */
  --bigdata-text-wcag: #064e3b;           /* 深色文本 */
  
  /* IoT 领域 */
  --iot-primary-wcag: #dc2626;            /* 4.51:1 对白色 */
  --iot-secondary-wcag: #b91c1c;          /* 6.05:1 对白色 */
  --iot-bg-wcag: #fef2f2;                 /* 浅色背景 */
  --iot-text-wcag: #991b1b;               /* 深色文本 */
  
  /* 云计算领域 */
  --cloud-primary-wcag: #5b21b6;          /* 4.53:1 对白色 */
  --cloud-secondary-wcag: #4c1d95;        /* 6.1:1 对白色 */
  --cloud-bg-wcag: #f3e8ff;               /* 浅色背景 */
  --cloud-text-wcag: #3730a3;             /* 深色文本 */
  
  /* ===== 背景色系统 - 层次化设计 ===== */
  
  /* 主背景色 */
  --bg-primary-wcag: #ffffff;             /* 基础白色 */
  --bg-secondary-wcag: #f9fafb;           /* 极浅灰 */
  --bg-tertiary-wcag: #f3f4f6;            /* 浅灰 */
  --bg-quaternary-wcag: #e5e7eb;          /* 中浅灰 */
  
  /* 卡片和容器背景 */
  --bg-card-wcag: #ffffff;                /* 卡片背景 */
  --bg-panel-wcag: #f9fafb;               /* 面板背景 */
  --bg-section-wcag: #f3f4f6;             /* 区块背景 */
  
  /* 交互状态背景 */
  --bg-hover-wcag: #f3f4f6;               /* 悬停背景 */
  --bg-active-wcag: #e5e7eb;              /* 激活背景 */
  --bg-selected-wcag: #dbeafe;            /* 选中背景 */
  --bg-disabled-wcag: #f9fafb;            /* 禁用背景 */
  
  /* ===== 边框色系统 ===== */
  
  /* 基础边框 - 提高可见性 */
  --border-light-wcag: #e5e7eb;           /* 极浅边框 - 加深 */
  --border-base-wcag: #d1d5db;            /* 基础边框 - 加深 */
  --border-medium-wcag: #9ca3af;          /* 中等边框 - 加深 */
  --border-strong-wcag: #6b7280;          /* 强边框 - 显著加深以达到可见性要求 */
  
  /* 交互状态边框 */
  --border-hover-wcag: #d1d5db;           /* 悬停边框 */
  --border-focus-wcag: #0066cc;           /* 焦点边框 */
  --border-active-wcag: #004499;          /* 激活边框 */
  --border-error-wcag: #dc2626;           /* 错误边框 */
  
  /* ===== 阴影系统 - 增强对比度 ===== */
  
  /* 基础阴影 */
  --shadow-xs-wcag: 0 1px 2px rgba(0, 0, 0, 0.08);
  --shadow-sm-wcag: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  --shadow-md-wcag: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.08);
  --shadow-lg-wcag: 0 10px 15px rgba(0, 0, 0, 0.12), 0 4px 6px rgba(0, 0, 0, 0.08);
  --shadow-xl-wcag: 0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.06);
  
  /* 焦点阴影 */
  --shadow-focus-wcag: 0 0 0 3px rgba(0, 102, 204, 0.2);
  --shadow-focus-error-wcag: 0 0 0 3px rgba(220, 38, 38, 0.2);
  --shadow-focus-success-wcag: 0 0 0 3px rgba(4, 120, 87, 0.2);
  
  /* ===== 渐变系统 - 保持品牌一致性 ===== */
  
  /* iFlytek 品牌渐变 - WCAG 优化 */
  --gradient-primary-wcag: linear-gradient(135deg, #0066cc 0%, #4c51bf 100%);
  --gradient-secondary-wcag: linear-gradient(135deg, #4c51bf 0%, #5b21b6 100%);
  --gradient-hero-wcag: linear-gradient(135deg, #0066cc 0%, #3730a3 100%);
  
  /* 技术领域渐变 */
  --gradient-ai-wcag: linear-gradient(135deg, #0066cc 0%, #1e40af 100%);
  --gradient-bigdata-wcag: linear-gradient(135deg, #047857 0%, #065f46 100%);
  --gradient-iot-wcag: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  --gradient-cloud-wcag: linear-gradient(135deg, #5b21b6 0%, #4c1d95 100%);
  
  /* 状态渐变 */
  --gradient-success-wcag: linear-gradient(135deg, #047857 0%, #022c22 100%);
  --gradient-warning-wcag: linear-gradient(135deg, #d97706 0%, #92400e 100%);
  --gradient-error-wcag: linear-gradient(135deg, #dc2626 0%, #7f1d1d 100%);
  
  /* ===== 护眼色彩系统 ===== */
  
  /* 长时间使用的护眼色彩 */
  --eye-comfort-bg: #fefefe;              /* 护眼白色背景 */
  --eye-comfort-text: #2d3748;            /* 护眼深色文本 */
  --eye-comfort-secondary: #4a5568;       /* 护眼次要文本 */
  --eye-comfort-border: #e2e8f0;          /* 护眼边框色 */
  
  /* 面试界面专用护眼色 */
  --interview-bg-primary: #fefefe;        /* 面试主背景 */
  --interview-bg-secondary: #f7fafc;      /* 面试次背景 */
  --interview-text-primary: #2d3748;      /* 面试主文本 */
  --interview-text-secondary: #4a5568;    /* 面试次文本 */
  --interview-accent: #0066cc;            /* 面试强调色 */
  
  /* ===== 暗色主题支持 ===== */
  
  /* 暗色主题基础色 */
  --dark-bg-primary: #1a202c;
  --dark-bg-secondary: #2d3748;
  --dark-bg-tertiary: #4a5568;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #e2e8f0;
  --dark-text-tertiary: #cbd5e0;
  
  /* 暗色主题品牌色 */
  --dark-iflytek-primary: #4299e1;        /* 亮化的主色 */
  --dark-iflytek-secondary: #805ad5;      /* 亮化的辅色 */
}

/* ===== 暗色主题变量覆盖 ===== */
[data-theme="dark"] {
  --text-primary-aaa: var(--dark-text-primary);
  --text-secondary-aaa: var(--dark-text-secondary);
  --text-tertiary-aaa: var(--dark-text-tertiary);
  --bg-primary-wcag: var(--dark-bg-primary);
  --bg-secondary-wcag: var(--dark-bg-secondary);
  --bg-tertiary-wcag: var(--dark-bg-tertiary);
  --iflytek-primary-wcag: var(--dark-iflytek-primary);
  --iflytek-secondary-wcag: var(--dark-iflytek-secondary);
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
  :root {
    --text-primary-aa: #000000;
    --text-secondary-aa: #000000;
    --text-tertiary-aa: #000000;
    --border-base-wcag: #000000;
    --border-medium-wcag: #000000;
    --iflytek-primary-wcag: #000080;
    --iflytek-secondary-wcag: #000080;
  }
  
  [data-theme="dark"] {
    --text-primary-aaa: #ffffff;
    --text-secondary-aaa: #ffffff;
    --text-tertiary-aaa: #ffffff;
    --border-base-wcag: #ffffff;
    --border-medium-wcag: #ffffff;
  }
}

/* ===== 减少动画模式支持 ===== */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-duration: 0s;
    --animation-duration: 0s;
  }
}

/* ===== 工具类 ===== */

/* 文本对比度工具类 */
.text-aa { color: var(--text-primary-aa) !important; }
.text-aaa { color: var(--text-primary-aaa) !important; }
.text-secondary-aa { color: var(--text-secondary-aa) !important; }
.text-secondary-aaa { color: var(--text-secondary-aaa) !important; }
.text-inverse { color: var(--text-inverse-aaa) !important; }

/* 背景对比度工具类 */
.bg-wcag-primary { background-color: var(--bg-primary-wcag) !important; }
.bg-wcag-secondary { background-color: var(--bg-secondary-wcag) !important; }
.bg-wcag-card { background-color: var(--bg-card-wcag) !important; }

/* 边框对比度工具类 */
.border-wcag-base { border-color: var(--border-base-wcag) !important; }
.border-wcag-medium { border-color: var(--border-medium-wcag) !important; }
.border-wcag-focus { border-color: var(--border-focus-wcag) !important; }

/* 阴影对比度工具类 */
.shadow-wcag-sm { box-shadow: var(--shadow-sm-wcag) !important; }
.shadow-wcag-md { box-shadow: var(--shadow-md-wcag) !important; }
.shadow-wcag-lg { box-shadow: var(--shadow-lg-wcag) !important; }
.shadow-wcag-focus { box-shadow: var(--shadow-focus-wcag) !important; }

/* 品牌色工具类 */
.iflytek-primary-wcag { color: var(--iflytek-primary-wcag) !important; }
.iflytek-secondary-wcag { color: var(--iflytek-secondary-wcag) !important; }
.bg-iflytek-primary-wcag { background-color: var(--iflytek-primary-wcag) !important; }
.bg-iflytek-secondary-wcag { background-color: var(--iflytek-secondary-wcag) !important; }

/* 功能色工具类 */
.success-wcag { color: var(--success-wcag-aa) !important; }
.warning-wcag { color: var(--warning-wcag-aa) !important; }
.error-wcag { color: var(--error-wcag-aa) !important; }
.info-wcag { color: var(--info-wcag-aa) !important; }

/* 技术领域色工具类 */
.ai-wcag { color: var(--ai-primary-wcag) !important; }
.bigdata-wcag { color: var(--bigdata-primary-wcag) !important; }
.iot-wcag { color: var(--iot-primary-wcag) !important; }
.cloud-wcag { color: var(--cloud-primary-wcag) !important; }

/* 护眼模式工具类 */
.eye-comfort {
  background-color: var(--eye-comfort-bg) !important;
  color: var(--eye-comfort-text) !important;
}

.interview-comfort {
  background-color: var(--interview-bg-primary) !important;
  color: var(--interview-text-primary) !important;
}

/* 焦点增强工具类 */
.focus-enhanced:focus {
  outline: 3px solid var(--border-focus-wcag) !important;
  outline-offset: 2px;
  box-shadow: var(--shadow-focus-wcag) !important;
}

/* 对比度验证工具类 */
.contrast-check {
  position: relative;
}

.contrast-check::after {
  content: attr(data-contrast);
  position: absolute;
  top: -25px;
  right: 0;
  background: var(--success-wcag-aa);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.contrast-check:hover::after {
  opacity: 1;
}

.contrast-check.fail::after {
  background: var(--error-wcag-aa);
  content: '对比度不足';
}

.contrast-check.pass-aa::after {
  background: var(--warning-wcag-aa);
  content: 'AA 合格';
}

.contrast-check.pass-aaa::after {
  background: var(--success-wcag-aa);
  content: 'AAA 优秀';
}
