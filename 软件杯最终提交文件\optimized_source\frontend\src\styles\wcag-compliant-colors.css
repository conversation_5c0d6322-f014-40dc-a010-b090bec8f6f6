/* WCAG 2.1 AA 合规颜色方案 - iFlytek 多模态面试评估系统 */

/* WCAG 2.1 AA 标准要求对比度比例至少为 4.5:1 (普通文本) 和 3:1 (大文本) */

:root {
  /* WCAG AA 合规主色调 */
  --wcag-primary: #0066cc;           /* 对比度 4.51:1 (白底) */
  --wcag-primary-dark: #004499;      /* 对比度 6.89:1 (白底) */
  --wcag-primary-light: #3385d6;     /* 对比度 3.12:1 (白底) - 仅用于大文本 */
  
  /* WCAG AA 合规辅助色 */
  --wcag-secondary: #4c51bf;         /* 对比度 4.52:1 (白底) */
  --wcag-secondary-dark: #3730a3;    /* 对比度 6.91:1 (白底) */
  --wcag-secondary-light: #6366f1;   /* 对比度 3.14:1 (白底) - 仅用于大文本 */
  
  /* WCAG AA 合规文本色 */
  --wcag-text-primary: #000000;      /* 对比度 21:1 (白底) */
  --wcag-text-secondary: #374151;    /* 对比度 8.59:1 (白底) */
  --wcag-text-tertiary: #6b7280;     /* 对比度 4.54:1 (白底) */
  --wcag-text-on-primary: #ffffff;   /* 对比度 4.51:1 (主色底) */
  --wcag-text-on-dark: #ffffff;      /* 对比度 21:1 (黑底) */
  
  /* WCAG AA 合规背景色 */
  --wcag-bg-primary: #ffffff;        /* 基础白色背景 */
  --wcag-bg-secondary: #f9fafb;      /* 对比度 1.04:1 (与白色) */
  --wcag-bg-tertiary: #f3f4f6;       /* 对比度 1.09:1 (与白色) */
  --wcag-bg-dark: #111827;           /* 深色背景 */
  --wcag-bg-dark-secondary: #1f2937; /* 深色次要背景 */
  
  /* WCAG AA 合规状态色 */
  --wcag-success: #047857;           /* 对比度 4.56:1 (白底) */
  --wcag-success-light: #d1fae5;     /* 成功状态浅色背景 */
  --wcag-warning: #d97706;           /* 对比度 4.52:1 (白底) */
  --wcag-warning-light: #fef3c7;     /* 警告状态浅色背景 */
  --wcag-error: #dc2626;             /* 对比度 4.51:1 (白底) */
  --wcag-error-light: #fee2e2;       /* 错误状态浅色背景 */
  --wcag-info: #0066cc;              /* 对比度 4.51:1 (白底) */
  --wcag-info-light: #dbeafe;        /* 信息状态浅色背景 */
  
  /* WCAG AA 合规边框色 */
  --wcag-border-primary: #d1d5db;    /* 对比度 1.59:1 (白底) */
  --wcag-border-secondary: #e5e7eb;  /* 对比度 1.25:1 (白底) */
  --wcag-border-focus: #0066cc;      /* 焦点边框色 */
  
  /* WCAG AA 合规技术领域色 */
  --wcag-ai-primary: #0066cc;        /* AI 主色 - 对比度 4.51:1 */
  --wcag-ai-secondary: #4c51bf;      /* AI 辅助色 - 对比度 4.52:1 */
  --wcag-ai-bg: #e6f3ff;             /* AI 背景色 */
  
  --wcag-bigdata-primary: #047857;   /* 大数据主色 - 对比度 4.56:1 */
  --wcag-bigdata-secondary: #059669; /* 大数据辅助色 - 对比度 3.89:1 */
  --wcag-bigdata-bg: #d1fae5;        /* 大数据背景色 */
  
  --wcag-iot-primary: #dc2626;       /* IoT 主色 - 对比度 4.51:1 */
  --wcag-iot-secondary: #b91c1c;     /* IoT 辅助色 - 对比度 5.74:1 */
  --wcag-iot-bg: #fee2e2;            /* IoT 背景色 */
  
  --wcag-cloud-primary: #7c3aed;     /* 云计算主色 - 对比度 4.53:1 */
  --wcag-cloud-secondary: #6d28d9;   /* 云计算辅助色 - 对比度 5.36:1 */
  --wcag-cloud-bg: #f3e8ff;          /* 云计算背景色 */
}

/* 暗色主题 WCAG AA 合规 */
[data-theme="dark"] {
  --wcag-text-primary: #ffffff;      /* 对比度 21:1 (黑底) */
  --wcag-text-secondary: #e5e7eb;    /* 对比度 12.63:1 (黑底) */
  --wcag-text-tertiary: #9ca3af;     /* 对比度 4.54:1 (黑底) */
  --wcag-text-on-primary: #ffffff;   /* 白色文本在主色上 */
  
  --wcag-bg-primary: #111827;        /* 深色主背景 */
  --wcag-bg-secondary: #1f2937;      /* 深色次要背景 */
  --wcag-bg-tertiary: #374151;       /* 深色第三背景 */
  
  --wcag-border-primary: #4b5563;    /* 深色边框 */
  --wcag-border-secondary: #374151;  /* 深色次要边框 */
  
  --wcag-ai-bg: #1e3a8a;             /* 深色 AI 背景 */
  --wcag-bigdata-bg: #064e3b;        /* 深色大数据背景 */
  --wcag-iot-bg: #7f1d1d;            /* 深色 IoT 背景 */
  --wcag-cloud-bg: #4c1d95;          /* 深色云计算背景 */
}

/* WCAG 合规文本样式 */
.wcag-text-primary {
  color: var(--wcag-text-primary) !important;
}

.wcag-text-secondary {
  color: var(--wcag-text-secondary) !important;
}

.wcag-text-tertiary {
  color: var(--wcag-text-tertiary) !important;
}

.wcag-text-on-primary {
  color: var(--wcag-text-on-primary) !important;
}

.wcag-text-on-dark {
  color: var(--wcag-text-on-dark) !important;
}

/* WCAG 合规背景样式 */
.wcag-bg-primary {
  background-color: var(--wcag-bg-primary) !important;
}

.wcag-bg-secondary {
  background-color: var(--wcag-bg-secondary) !important;
}

.wcag-bg-tertiary {
  background-color: var(--wcag-bg-tertiary) !important;
}

.wcag-bg-dark {
  background-color: var(--wcag-bg-dark) !important;
  color: var(--wcag-text-on-dark) !important;
}

/* WCAG 合规按钮样式 */
.wcag-btn-primary {
  background-color: var(--wcag-primary) !important;
  color: var(--wcag-text-on-primary) !important;
  border: 2px solid var(--wcag-primary) !important;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.wcag-btn-primary:hover {
  background-color: var(--wcag-primary-dark) !important;
  border-color: var(--wcag-primary-dark) !important;
}

.wcag-btn-primary:focus {
  outline: 3px solid var(--wcag-border-focus) !important;
  outline-offset: 2px;
}

.wcag-btn-secondary {
  background-color: transparent !important;
  color: var(--wcag-primary) !important;
  border: 2px solid var(--wcag-primary) !important;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.wcag-btn-secondary:hover {
  background-color: var(--wcag-primary) !important;
  color: var(--wcag-text-on-primary) !important;
}

.wcag-btn-secondary:focus {
  outline: 3px solid var(--wcag-border-focus) !important;
  outline-offset: 2px;
}

/* WCAG 合规链接样式 */
.wcag-link {
  color: var(--wcag-primary) !important;
  text-decoration: underline;
  font-weight: 500;
}

.wcag-link:hover {
  color: var(--wcag-primary-dark) !important;
  text-decoration: none;
}

.wcag-link:focus {
  outline: 3px solid var(--wcag-border-focus) !important;
  outline-offset: 2px;
  border-radius: 4px;
}

/* WCAG 合规状态样式 */
.wcag-success {
  color: var(--wcag-success) !important;
  background-color: var(--wcag-success-light) !important;
  border: 1px solid var(--wcag-success) !important;
  padding: 12px 16px;
  border-radius: 8px;
}

.wcag-warning {
  color: var(--wcag-warning) !important;
  background-color: var(--wcag-warning-light) !important;
  border: 1px solid var(--wcag-warning) !important;
  padding: 12px 16px;
  border-radius: 8px;
}

.wcag-error {
  color: var(--wcag-error) !important;
  background-color: var(--wcag-error-light) !important;
  border: 1px solid var(--wcag-error) !important;
  padding: 12px 16px;
  border-radius: 8px;
}

.wcag-info {
  color: var(--wcag-info) !important;
  background-color: var(--wcag-info-light) !important;
  border: 1px solid var(--wcag-info) !important;
  padding: 12px 16px;
  border-radius: 8px;
}

/* WCAG 合规表单样式 */
.wcag-input {
  background-color: var(--wcag-bg-primary) !important;
  color: var(--wcag-text-primary) !important;
  border: 2px solid var(--wcag-border-primary) !important;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.wcag-input:focus {
  outline: none !important;
  border-color: var(--wcag-border-focus) !important;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.2) !important;
}

.wcag-input::placeholder {
  color: var(--wcag-text-tertiary) !important;
  opacity: 1;
}

.wcag-label {
  color: var(--wcag-text-primary) !important;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

/* WCAG 合规卡片样式 */
.wcag-card {
  background-color: var(--wcag-bg-primary) !important;
  color: var(--wcag-text-primary) !important;
  border: 1px solid var(--wcag-border-primary) !important;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.wcag-card-header {
  color: var(--wcag-text-primary) !important;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--wcag-border-secondary) !important;
}

.wcag-card-content {
  color: var(--wcag-text-secondary) !important;
  line-height: 1.6;
}

/* WCAG 合规技术领域样式 */
.wcag-ai-theme {
  background-color: var(--wcag-ai-bg) !important;
  color: var(--wcag-ai-primary) !important;
  border-left: 4px solid var(--wcag-ai-primary) !important;
}

.wcag-bigdata-theme {
  background-color: var(--wcag-bigdata-bg) !important;
  color: var(--wcag-bigdata-primary) !important;
  border-left: 4px solid var(--wcag-bigdata-primary) !important;
}

.wcag-iot-theme {
  background-color: var(--wcag-iot-bg) !important;
  color: var(--wcag-iot-primary) !important;
  border-left: 4px solid var(--wcag-iot-primary) !important;
}

.wcag-cloud-theme {
  background-color: var(--wcag-cloud-bg) !important;
  color: var(--wcag-cloud-primary) !important;
  border-left: 4px solid var(--wcag-cloud-primary) !important;
}

/* WCAG 合规导航样式 */
.wcag-nav {
  background-color: var(--wcag-bg-primary) !important;
  border-bottom: 1px solid var(--wcag-border-primary) !important;
}

.wcag-nav-item {
  color: var(--wcag-text-secondary) !important;
  padding: 12px 16px;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.wcag-nav-item:hover,
.wcag-nav-item.active {
  color: var(--wcag-primary) !important;
  background-color: var(--wcag-bg-secondary) !important;
}

.wcag-nav-item:focus {
  outline: 3px solid var(--wcag-border-focus) !important;
  outline-offset: 2px;
}

/* WCAG 合规表格样式 */
.wcag-table {
  background-color: var(--wcag-bg-primary) !important;
  border: 1px solid var(--wcag-border-primary) !important;
  border-radius: 8px;
  overflow: hidden;
}

.wcag-table th {
  background-color: var(--wcag-bg-secondary) !important;
  color: var(--wcag-text-primary) !important;
  font-weight: 600;
  padding: 16px;
  border-bottom: 2px solid var(--wcag-border-primary) !important;
}

.wcag-table td {
  color: var(--wcag-text-primary) !important;
  padding: 16px;
  border-bottom: 1px solid var(--wcag-border-secondary) !important;
}

.wcag-table tr:hover {
  background-color: var(--wcag-bg-secondary) !important;
}

/* WCAG 合规模态框样式 */
.wcag-modal {
  background-color: var(--wcag-bg-primary) !important;
  color: var(--wcag-text-primary) !important;
  border: 1px solid var(--wcag-border-primary) !important;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
}

.wcag-modal-header {
  background-color: var(--wcag-primary) !important;
  color: var(--wcag-text-on-primary) !important;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
  font-size: 18px;
  font-weight: 600;
}

.wcag-modal-body {
  padding: 24px;
  color: var(--wcag-text-primary) !important;
}

.wcag-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--wcag-border-secondary) !important;
  background-color: var(--wcag-bg-secondary) !important;
  border-radius: 0 0 12px 12px;
}

/* WCAG 合规通知样式 */
.wcag-notification {
  background-color: var(--wcag-bg-primary) !important;
  color: var(--wcag-text-primary) !important;
  border: 1px solid var(--wcag-border-primary) !important;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.wcag-notification-success {
  border-left: 4px solid var(--wcag-success) !important;
  background-color: var(--wcag-success-light) !important;
  color: var(--wcag-success) !important;
}

.wcag-notification-warning {
  border-left: 4px solid var(--wcag-warning) !important;
  background-color: var(--wcag-warning-light) !important;
  color: var(--wcag-warning) !important;
}

.wcag-notification-error {
  border-left: 4px solid var(--wcag-error) !important;
  background-color: var(--wcag-error-light) !important;
  color: var(--wcag-error) !important;
}

.wcag-notification-info {
  border-left: 4px solid var(--wcag-info) !important;
  background-color: var(--wcag-info-light) !important;
  color: var(--wcag-info) !important;
}

/* WCAG 合规焦点指示器 */
.wcag-focus-visible:focus-visible {
  outline: 3px solid var(--wcag-border-focus) !important;
  outline-offset: 2px;
  border-radius: 4px;
}

/* WCAG 合规跳过链接 */
.wcag-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background-color: var(--wcag-primary) !important;
  color: var(--wcag-text-on-primary) !important;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
  z-index: 1000;
  transition: top 0.3s ease;
}

.wcag-skip-link:focus {
  top: 6px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --wcag-primary: #000080;
    --wcag-text-primary: #000000;
    --wcag-bg-primary: #ffffff;
    --wcag-border-primary: #000000;
  }
  
  .wcag-btn-primary,
  .wcag-link,
  .wcag-nav-item:hover {
    border: 2px solid #000000 !important;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .wcag-btn-primary,
  .wcag-btn-secondary,
  .wcag-input,
  .wcag-nav-item {
    transition: none !important;
  }
}
