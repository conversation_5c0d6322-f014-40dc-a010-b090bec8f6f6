// iFlytek 面试系统深度诊断脚本
// 检查Vue应用挂载状态、组件加载情况和实际的DOM结构

console.log('🔍 开始iFlytek面试系统深度诊断...');

// 诊断结果收集器
const diagnosisResults = {
    vueApp: {},
    domStructure: {},
    components: {},
    routing: {},
    errors: []
};

// 1. 检查Vue应用详细状态
function checkVueApplicationDetails() {
    console.log('\n📱 检查Vue应用详细状态...');
    
    const app = document.getElementById('app');
    if (!app) {
        diagnosisResults.vueApp.status = 'container-missing';
        diagnosisResults.errors.push('Vue应用容器#app不存在');
        return false;
    }
    
    console.log('✅ #app 容器存在');
    diagnosisResults.vueApp.containerExists = true;
    
    // 检查Vue应用实例
    if (!app.__vue_app__) {
        diagnosisResults.vueApp.status = 'app-not-mounted';
        diagnosisResults.errors.push('Vue应用实例未挂载');
        return false;
    }
    
    console.log('✅ Vue应用实例已挂载');
    diagnosisResults.vueApp.instanceMounted = true;
    
    try {
        const vueApp = app.__vue_app__;
        
        // 检查Vue应用配置
        diagnosisResults.vueApp.hasConfig = !!vueApp.config;
        diagnosisResults.vueApp.hasGlobalProperties = !!(vueApp.config && vueApp.config.globalProperties);
        
        // 检查Vue实例
        diagnosisResults.vueApp.hasInstance = !!vueApp._instance;
        if (vueApp._instance) {
            diagnosisResults.vueApp.hasProxy = !!vueApp._instance.proxy;
            diagnosisResults.vueApp.hasRouter = !!(vueApp._instance.proxy && vueApp._instance.proxy.$router);
        }
        
        console.log('📊 Vue应用详情:');
        console.log(`  配置对象: ${diagnosisResults.vueApp.hasConfig ? '存在' : '缺失'}`);
        console.log(`  全局属性: ${diagnosisResults.vueApp.hasGlobalProperties ? '存在' : '缺失'}`);
        console.log(`  实例代理: ${diagnosisResults.vueApp.hasProxy ? '存在' : '缺失'}`);
        console.log(`  路由器: ${diagnosisResults.vueApp.hasRouter ? '存在' : '缺失'}`);
        
        diagnosisResults.vueApp.status = 'healthy';
        return true;
        
    } catch (error) {
        diagnosisResults.vueApp.status = 'error';
        diagnosisResults.errors.push(`Vue应用检查失败: ${error.message}`);
        return false;
    }
}

// 2. 检查DOM结构详情
function checkDOMStructure() {
    console.log('\n🏗️ 检查DOM结构详情...');
    
    // 检查当前页面的主要结构
    const body = document.body;
    const app = document.getElementById('app');
    
    diagnosisResults.domStructure.bodyClasses = body.className;
    diagnosisResults.domStructure.appClasses = app ? app.className : 'not-found';
    
    // 检查是否有Vue组件的标识
    const vueComponents = document.querySelectorAll('[data-v-]');
    diagnosisResults.domStructure.vueComponentCount = vueComponents.length;
    
    // 检查主要的页面结构
    const mainStructures = {
        'enterprise-homepage': document.querySelector('.enterprise-homepage'),
        'ai-app': document.querySelector('.ai-app'),
        'enterprise-header': document.querySelector('.enterprise-header'),
        'ai-header': document.querySelector('.ai-header'),
        'hero-section': document.querySelector('.hero-section'),
        'products-section': document.querySelector('.products-section')
    };
    
    diagnosisResults.domStructure.structures = {};
    Object.entries(mainStructures).forEach(([key, element]) => {
        diagnosisResults.domStructure.structures[key] = !!element;
        console.log(`  ${key}: ${element ? '存在' : '缺失'}`);
    });
    
    // 检查当前显示的是哪个页面组件
    if (mainStructures['enterprise-homepage']) {
        diagnosisResults.domStructure.currentPage = 'NewHomePage';
        console.log('📄 当前页面: NewHomePage.vue');
    } else if (mainStructures['ai-app']) {
        diagnosisResults.domStructure.currentPage = 'App.vue';
        console.log('📄 当前页面: App.vue (通用布局)');
    } else {
        diagnosisResults.domStructure.currentPage = 'unknown';
        console.log('📄 当前页面: 未知');
    }
    
    return diagnosisResults.domStructure;
}

// 3. 检查具体组件加载情况
function checkComponentLoading() {
    console.log('\n🧩 检查具体组件加载情况...');
    
    // 检查导航菜单
    const menuSelectors = [
        '.el-menu',
        '.el-menu-item',
        '.enterprise-nav',
        '.ai-nav'
    ];
    
    diagnosisResults.components.navigation = {};
    menuSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        diagnosisResults.components.navigation[selector] = elements.length;
        console.log(`  ${selector}: ${elements.length} 个`);
    });
    
    // 检查按钮
    const buttonSelectors = [
        '.el-button',
        '.primary-cta',
        '.secondary-cta',
        '.cta-button',
        '.secondary-btn'
    ];
    
    diagnosisResults.components.buttons = {};
    buttonSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        diagnosisResults.components.buttons[selector] = elements.length;
        console.log(`  ${selector}: ${elements.length} 个`);
    });
    
    // 检查产品卡片
    const cardSelectors = [
        '.product-card',
        '.primary-product'
    ];
    
    diagnosisResults.components.cards = {};
    cardSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        diagnosisResults.components.cards[selector] = elements.length;
        console.log(`  ${selector}: ${elements.length} 个`);
    });
    
    // 检查Element Plus组件
    const elementPlusSelectors = [
        '.el-icon',
        '.el-menu',
        '.el-button'
    ];
    
    diagnosisResults.components.elementPlus = {};
    elementPlusSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        diagnosisResults.components.elementPlus[selector] = elements.length;
        console.log(`  ${selector}: ${elements.length} 个`);
    });
    
    return diagnosisResults.components;
}

// 4. 检查路由状态
function checkRoutingStatus() {
    console.log('\n🛣️ 检查路由状态...');
    
    diagnosisResults.routing.currentPath = window.location.pathname;
    diagnosisResults.routing.currentHash = window.location.hash;
    diagnosisResults.routing.currentSearch = window.location.search;
    
    console.log(`📍 当前路径: ${diagnosisResults.routing.currentPath}`);
    console.log(`🔗 哈希: ${diagnosisResults.routing.currentHash}`);
    console.log(`🔍 查询参数: ${diagnosisResults.routing.currentSearch}`);
    
    // 检查History API
    diagnosisResults.routing.historyAPISupported = !!(window.history && window.history.pushState);
    console.log(`📚 History API: ${diagnosisResults.routing.historyAPISupported ? '支持' : '不支持'}`);
    
    // 尝试检查Vue Router
    try {
        const app = document.getElementById('app');
        if (app && app.__vue_app__ && app.__vue_app__._instance && app.__vue_app__._instance.proxy) {
            const proxy = app.__vue_app__._instance.proxy;
            diagnosisResults.routing.vueRouterExists = !!proxy.$router;
            diagnosisResults.routing.currentRoute = proxy.$route ? proxy.$route.path : 'unknown';
            
            console.log(`🧭 Vue Router: ${diagnosisResults.routing.vueRouterExists ? '存在' : '不存在'}`);
            console.log(`📋 当前路由: ${diagnosisResults.routing.currentRoute}`);
        }
    } catch (error) {
        diagnosisResults.routing.vueRouterError = error.message;
        console.warn(`⚠️ Vue Router检查失败: ${error.message}`);
    }
    
    return diagnosisResults.routing;
}

// 5. 检查样式加载
function checkStyleLoading() {
    console.log('\n🎨 检查样式加载...');
    
    const stylesheets = Array.from(document.styleSheets);
    diagnosisResults.styles = {
        totalStylesheets: stylesheets.length,
        elementPlusLoaded: false,
        customStylesLoaded: false
    };
    
    stylesheets.forEach((sheet, index) => {
        const href = sheet.href || '内联样式';
        console.log(`  ${index + 1}. ${href}`);
        
        if (href.includes('element-plus')) {
            diagnosisResults.styles.elementPlusLoaded = true;
        }
        
        if (href.includes('main') || href.includes('app') || href.includes('style')) {
            diagnosisResults.styles.customStylesLoaded = true;
        }
    });
    
    console.log(`📊 样式表总数: ${diagnosisResults.styles.totalStylesheets}`);
    console.log(`🎨 Element Plus样式: ${diagnosisResults.styles.elementPlusLoaded ? '已加载' : '未加载'}`);
    console.log(`🎨 自定义样式: ${diagnosisResults.styles.customStylesLoaded ? '已加载' : '未加载'}`);
    
    return diagnosisResults.styles;
}

// 6. 生成诊断报告
function generateDiagnosisReport() {
    console.log('\n📊 生成诊断报告...');
    
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            vueAppStatus: diagnosisResults.vueApp.status,
            currentPage: diagnosisResults.domStructure.currentPage,
            totalErrors: diagnosisResults.errors.length,
            componentIssues: []
        },
        details: diagnosisResults
    };
    
    // 分析组件问题
    if (diagnosisResults.components.navigation['.el-menu-item'] === 0) {
        report.summary.componentIssues.push('导航菜单项未找到');
    }
    
    if (diagnosisResults.components.buttons['.primary-cta'] === 0 && 
        diagnosisResults.components.buttons['.secondary-cta'] === 0) {
        report.summary.componentIssues.push('主要行动按钮未找到');
    }
    
    if (diagnosisResults.components.cards['.product-card'] === 0) {
        report.summary.componentIssues.push('产品卡片未找到');
    }
    
    console.log('📈 诊断摘要:');
    console.log(`  Vue应用状态: ${report.summary.vueAppStatus}`);
    console.log(`  当前页面: ${report.summary.currentPage}`);
    console.log(`  错误数量: ${report.summary.totalErrors}`);
    console.log(`  组件问题: ${report.summary.componentIssues.length} 个`);
    
    if (report.summary.componentIssues.length > 0) {
        console.log('\n❌ 发现的组件问题:');
        report.summary.componentIssues.forEach(issue => {
            console.log(`  - ${issue}`);
        });
    }
    
    if (diagnosisResults.errors.length > 0) {
        console.log('\n❌ 发现的错误:');
        diagnosisResults.errors.forEach(error => {
            console.log(`  - ${error}`);
        });
    }
    
    return report;
}

// 7. 主诊断函数
function runDeepSystemDiagnosis() {
    console.log('🚀 开始深度系统诊断...\n');
    
    // 重置诊断结果
    diagnosisResults.vueApp = {};
    diagnosisResults.domStructure = {};
    diagnosisResults.components = {};
    diagnosisResults.routing = {};
    diagnosisResults.errors = [];
    
    // 执行所有检查
    checkVueApplicationDetails();
    checkDOMStructure();
    checkComponentLoading();
    checkRoutingStatus();
    checkStyleLoading();
    
    // 生成报告
    const report = generateDiagnosisReport();
    
    // 保存报告
    window.iflytekDeepDiagnosisReport = report;
    
    console.log('\n✅ 深度系统诊断完成!');
    console.log('💡 诊断报告已保存到 window.iflytekDeepDiagnosisReport');
    
    return report;
}

// 导出到全局作用域
window.iflytekDeepDiagnosis = {
    runDeepSystemDiagnosis,
    checkVueApplicationDetails,
    checkDOMStructure,
    checkComponentLoading,
    checkRoutingStatus,
    checkStyleLoading,
    generateDiagnosisReport,
    getResults: () => diagnosisResults
};

console.log('✅ 深度系统诊断脚本已加载');
console.log('💡 使用 iflytekDeepDiagnosis.runDeepSystemDiagnosis() 开始诊断');
console.log('💡 使用 window.iflytekDeepDiagnosisReport 查看诊断报告');
