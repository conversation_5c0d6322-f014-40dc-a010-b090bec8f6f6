name: Patch Release

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to run the action on'
        required: true
        default: 'master'

jobs:  
  patch-release:
    runs-on: ubuntu-latest
    environment: prod
    steps:
      - name: Extract branch name
        shell: bash
        id: extract_branch
        run: |
          echo "BRANCH=${{ github.event.inputs.branch }}" >> $GITHUB_ENV
          echo "Branch: " $BRANCH
      - name: checkout patch branch
        uses: actions/checkout@v3
        with:
          ref: ${{ env.BRANCH }}
      - uses: actions/setup-node@v3
        with:
          node-version: 22
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org'
      - name: Install dependencies
        run: npm install
      - name: Install Playwright Browsers
        run: npx playwright install --with-deps
      - name: Run tests
        run : |
          npm test 
      - name: Get new version string
        id: get_new_patch_version
        run: |
          chmod +rx ./.github/workflows/scripts/new-patch-version.sh
          . ./.github/workflows/scripts/new-patch-version.sh
        shell: bash
      - name: See patch branch
        run: echo branch ${{ env.<PERSON><PERSON><PERSON> }}
        shell: bash
      - name: See new patch version
        run: echo "# version:" ${{ env.VERSION }}
        shell: bash
      - name: checkout master branch
        uses: actions/checkout@v3
        with:
          ref: master
          fetch-depth: 0
      - name: Set Git Config
        run : |
          # Set git configs
          git config --global user.name "${GITHUB_ACTOR}"
          git config --global user.email "${GITHUB_ACTOR}@users.noreply.github.com" 
      - name: Update Version on master 
        id: update_backport_version_master
        run: |
          jq --arg ver "${{ env.VERSION }}" '.versions += [$ver]' ./documentation/versions.json >> /tmp/temp.json
          mv /tmp/temp.json ./documentation/versions.json
          
          git add . && git commit -m "Docs: Add ${{ env.VERSION }} to versions.json"
          git push
      - name: checkout unstable branch
        uses: actions/checkout@v3
        with:
          ref: unstable
          fetch-depth: 0
      - name: Update Version on unstable 
        id: update_backport_version_unstable
        run: |
          jq --arg ver "${{ env.VERSION }}" '.versions += [$ver]' ./documentation/versions.json >> /tmp/temp.json
          mv /tmp/temp.json ./documentation/versions.json
          
          git add . && git commit -m "Docs: Add ${{ env.VERSION }} to versions.json"
          git push
          git checkout ${{ env.BRANCH }}
      - name: Set Git Config
        run : |
          # Set git configs
          git config --global user.name "${GITHUB_ACTOR}"
          git config --global user.email "${GITHUB_ACTOR}@users.noreply.github.com"        
      - name: Make Release
        id: release
        run: |
          chmod +rx .github/workflows/scripts/pre_release_test.sh
          . .github/workflows/scripts/pre_release_test.sh ${{ env.BRANCH }}
      - name: Archive action failure results
        uses: actions/upload-artifact@v4
        if: ${{ failure() && steps.release.conclusion == 'failure' }}
        with:
          name: npm-release--failure-report
          path: /home/<USER>/.npm/_logs/
      - name: Publish Package To npmjs
        run:  npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Publish Package to GitHub Releases
        run: |
          curl -L \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ secrets.MAIN_GH_TOKEN }}" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          https://api.github.com/repos/${{ github.repository }}/releases \
          -d '{"tag_name":"v${{ env.VERSION }}","target_commitish":"master","name":"v${{ env.VERSION }}","body":"Release version v${{ env.VERSION }}","draft":false,"prerelease":false,"generate_release_notes":false}'
      - name: Deploy to Github Pages 🚀
        if: ${{ env.BRANCH == 'master' }}
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: documentation        
