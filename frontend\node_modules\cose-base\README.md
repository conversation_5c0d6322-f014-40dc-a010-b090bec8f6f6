cose-base
================================================================================

## Description

This is a core module for compound spring embedder based layout styles such as CoSE-Bilkent, fCoSE, and CiSE.

## Dependencies

 * layout-base ^1.0.0

## Usage instructions

Add `cose-base` as a dependecy to your layout extension.

`require()` in the extension to reach functionality:

 * `var CoSEConstants = require('cose-base').CoSEConstants`,
 * `var CoSELayout = require('cose-base').CoSELayout`,
 * `...`

To reach functionality of `layout-base`:

 * `var Integer = require('cose-base').layoutBase.Integer`,
 * `var Layout = require('cose-base').layoutBase.Layout`,
 * `...`
