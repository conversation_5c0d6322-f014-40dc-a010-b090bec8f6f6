// 控制台清理和优化脚本
console.log('🧹 开始控制台清理和优化...');

// 控制台消息统计
const messageStats = {
  errors: 0,
  warnings: 0,
  info: 0,
  debug: 0,
  filtered: 0
};

// 需要过滤的消息模式
const filterPatterns = [
  /预览报告/,
  /导出报告/,
  /选择模板/,
  /错误上报.*Object/,
  /检测到长任务.*[0-9]{1,2}ms/,  // 过滤小于300ms的长任务警告
  /Vue received a Component/,
  /performance overhead/,
  /markRaw/,
  /shallowRef/
];

// 保存原始控制台方法
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
  info: console.info,
  debug: console.debug
};

// 优化的控制台方法
function createOptimizedConsoleMethod(level, originalMethod) {
  return function(...args) {
    const message = args.join(' ');
    
    // 检查是否需要过滤
    const shouldFilter = filterPatterns.some(pattern => pattern.test(message));
    
    if (shouldFilter) {
      messageStats.filtered++;
      return; // 过滤掉这些消息
    }
    
    // 统计消息类型
    messageStats[level]++;
    
    // 对于特定类型的消息进行优化处理
    if (level === 'warn') {
      // 长任务警告优化
      if (message.includes('检测到长任务')) {
        const duration = message.match(/(\d+)ms/);
        if (duration && parseInt(duration[1]) < 300) {
          messageStats.filtered++;
          return; // 过滤小于300ms的长任务警告
        }
      }
      
      // 错误上报优化
      if (message.includes('错误上报') && message.includes('Object')) {
        messageStats.filtered++;
        return; // 过滤格式不正确的错误上报
      }
    }
    
    // 调用原始方法
    originalMethod.apply(console, args);
  };
}

// 应用优化的控制台方法
console.log = createOptimizedConsoleMethod('info', originalConsole.log);
console.warn = createOptimizedConsoleMethod('warnings', originalConsole.warn);
console.error = createOptimizedConsoleMethod('errors', originalConsole.error);
console.info = createOptimizedConsoleMethod('info', originalConsole.info);
console.debug = createOptimizedConsoleMethod('debug', originalConsole.debug);

// 性能监控优化
function optimizePerformanceMonitoring() {
  console.log('\n=== 优化性能监控 ===');
  
  // 检查是否有性能观察器
  if ('PerformanceObserver' in window) {
    // 优化长任务监控
    const longTaskObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        // 只报告真正影响用户体验的长任务（>300ms）
        if (entry.duration > 300) {
          originalConsole.warn(`⚠️ 性能警告: 检测到长任务 ${entry.duration.toFixed(1)}ms`);
        }
      }
    });
    
    try {
      longTaskObserver.observe({ entryTypes: ['longtask'] });
      console.log('✅ 长任务监控已优化 (阈值: 300ms)');
    } catch (e) {
      console.log('⚠️ 长任务监控不支持');
    }
    
    // 优化资源监控
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        // 只报告加载时间过长的资源（>2秒）
        if (entry.duration > 2000) {
          originalConsole.warn(`⚠️ 资源加载缓慢: ${entry.name} (${entry.duration.toFixed(1)}ms)`);
        }
      }
    });
    
    try {
      resourceObserver.observe({ entryTypes: ['resource'] });
      console.log('✅ 资源监控已优化 (阈值: 2秒)');
    } catch (e) {
      console.log('⚠️ 资源监控不支持');
    }
  }
}

// ECharts 错误处理优化
function optimizeEChartsErrorHandling() {
  console.log('\n=== 优化 ECharts 错误处理 ===');
  
  // 检查是否有 ECharts 错误检测器
  if (window.echartsErrorDetector) {
    console.log('✅ ECharts 错误检测器已存在');
    
    // 优化错误上报格式
    const originalReportError = window.echartsErrorDetector.reportError;
    if (originalReportError) {
      window.echartsErrorDetector.reportError = function(error) {
        // 确保错误对象被正确序列化
        const errorInfo = {
          message: error.message || '未知错误',
          stack: error.stack || '无堆栈信息',
          timestamp: new Date().toISOString(),
          type: error.constructor.name || 'Error'
        };
        
        originalConsole.warn('📊 ECharts 错误上报:', JSON.stringify(errorInfo, null, 2));
      };
    }
  } else {
    console.log('⚠️ ECharts 错误检测器未找到');
  }
}

// Vue 开发警告优化
function optimizeVueWarnings() {
  console.log('\n=== 优化 Vue 开发警告 ===');
  
  // 检查是否在开发环境
  if (process.env.NODE_ENV === 'development') {
    // Vue 警告已在 main.js 中处理
    console.log('✅ Vue 开发警告过滤器已在 main.js 中配置');
  } else {
    console.log('✅ 生产环境，Vue 警告已自动禁用');
  }
}

// 清理现有的控制台消息
function cleanExistingConsoleMessages() {
  console.log('\n=== 清理控制台 ===');
  
  // 清空控制台（如果支持）
  if (console.clear) {
    console.clear();
    console.log('🧹 控制台已清理');
  }
  
  console.log('✅ 控制台优化已应用');
}

// 生成控制台优化报告
function generateOptimizationReport() {
  setTimeout(() => {
    console.log('\n🎯 控制台优化报告');
    console.log('='.repeat(50));
    console.log(`📊 消息统计 (最近5分钟):`);
    console.log(`   错误: ${messageStats.errors}`);
    console.log(`   警告: ${messageStats.warnings}`);
    console.log(`   信息: ${messageStats.info}`);
    console.log(`   调试: ${messageStats.debug}`);
    console.log(`   已过滤: ${messageStats.filtered}`);
    
    const totalMessages = Object.values(messageStats).reduce((a, b) => a + b, 0);
    const filterRate = totalMessages > 0 ? ((messageStats.filtered / totalMessages) * 100).toFixed(1) : 0;
    
    console.log(`\n📈 优化效果:`);
    console.log(`   总消息数: ${totalMessages}`);
    console.log(`   过滤率: ${filterRate}%`);
    
    if (messageStats.filtered > 0) {
      console.log(`✅ 成功过滤了 ${messageStats.filtered} 条噪音消息`);
    }
    
    console.log('\n💡 优化建议:');
    if (messageStats.warnings > 10) {
      console.log('   - 警告消息较多，建议检查代码质量');
    }
    if (messageStats.errors > 0) {
      console.log('   - 发现错误消息，建议优先修复');
    }
    if (messageStats.filtered < totalMessages * 0.1) {
      console.log('   - 控制台消息质量良好');
    }
    
  }, 5000); // 5秒后生成报告
}

// 恢复原始控制台的函数
function restoreOriginalConsole() {
  console.log = originalConsole.log;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
  console.info = originalConsole.info;
  console.debug = originalConsole.debug;
  
  originalConsole.log('🔄 控制台已恢复到原始状态');
}

// 主优化流程
function runConsoleOptimization() {
  console.log('🚀 开始控制台优化...\n');
  
  cleanExistingConsoleMessages();
  optimizePerformanceMonitoring();
  optimizeEChartsErrorHandling();
  optimizeVueWarnings();
  generateOptimizationReport();
  
  console.log('\n✅ 控制台优化完成！');
  console.log('💡 提示: 运行 restoreOriginalConsole() 可恢复原始控制台');
}

// 自动运行优化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', runConsoleOptimization);
} else {
  runConsoleOptimization();
}

// 导出函数供外部使用
window.optimizeConsole = runConsoleOptimization;
window.restoreOriginalConsole = restoreOriginalConsole;
window.getConsoleStats = () => messageStats;

console.log('💡 提示: 运行 optimizeConsole() 重新应用优化');
console.log('💡 提示: 运行 getConsoleStats() 查看消息统计');
