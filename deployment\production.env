# iFlytek 多模态面试系统 - 生产环境配置
# 请根据实际情况修改以下配置

# ==================== iFlytek Spark LLM 配置 ====================
# 从 https://console.xfyun.cn/ 获取
IFLYTEK_APP_ID=your_app_id_here
IFLYTEK_API_KEY=your_api_key_here
IFLYTEK_API_SECRET=your_api_secret_here
IFLYTEK_SPARK_URL=wss://spark-api.xf-yun.com/v3.5/chat

# ==================== 服务器配置 ====================
# 你的域名
DOMAIN_NAME=your-domain.com

# 服务端口
HTTP_PORT=80
HTTPS_PORT=443
BACKEND_PORT=8000
FRONTEND_PORT=3000

# ==================== 数据库配置 ====================
DATABASE_URL=sqlite:///./data/interview_system.db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# ==================== Redis配置 ====================
REDIS_PASSWORD=your_strong_redis_password_here
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# ==================== 安全配置 ====================
# 生成强密码：openssl rand -base64 32
SECRET_KEY=your_very_strong_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# 允许的主机
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,localhost

# CORS配置
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# ==================== SSL证书配置 ====================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ==================== 应用配置 ====================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# 文件上传限制
MAX_UPLOAD_SIZE=100MB
ALLOWED_FILE_TYPES=mp4,mp3,wav,jpg,jpeg,png,pdf

# ==================== 性能配置 ====================
# 工作进程数（建议设置为CPU核心数）
WORKERS=4
WORKER_CONNECTIONS=1000

# 缓存配置
CACHE_TTL=3600
STATIC_FILE_CACHE=86400

# ==================== 监控配置 ====================
ENABLE_METRICS=true
METRICS_PORT=9090

# 健康检查
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# ==================== 日志配置 ====================
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_RETENTION_DAYS=30

# ==================== 备份配置 ====================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=7
