{"version": 3, "sources": ["../src/marked.ts", "../src/defaults.ts", "../src/rules.ts", "../src/helpers.ts", "../src/Tokenizer.ts", "../src/Lexer.ts", "../src/Renderer.ts", "../src/TextRenderer.ts", "../src/Parser.ts", "../src/Hooks.ts", "../src/Instance.ts"], "sourcesContent": ["import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport {\n  _getDefaults,\n  changeDefaults,\n  _defaults,\n} from './defaults.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\nimport type { MaybePromise } from './Instance.ts';\n\nconst markedInstance = new Marked();\n\n/**\n * Compiles markdown to HTML asynchronously.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options, having async: true\n * @return Promise of string of compiled HTML\n */\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\n\n/**\n * Compiles markdown to HTML.\n *\n * @param src String of markdown source to be compiled\n * @param options Optional hash of options\n * @return String of compiled HTML. Will be a Promise of string if async is set to true by any extensions.\n */\nexport function marked(src: string, options: MarkedOptions & { async: false }): string;\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\nexport function marked(src: string, options?: MarkedOptions | null): string | Promise<string>;\nexport function marked(src: string, opt?: MarkedOptions | null): string | Promise<string> {\n  return markedInstance.parse(src, opt);\n}\n\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\nmarked.setOptions = function(options: MarkedOptions) {\n  markedInstance.setOptions(options);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\n\nmarked.defaults = _defaults;\n\n/**\n * Use Extension\n */\n\nmarked.use = function(...args: MarkedExtension[]) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Run callback for every token\n */\n\nmarked.walkTokens = function(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n  return markedInstance.walkTokens(tokens, callback);\n};\n\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\n\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\nexport type * from './MarkedOptions.ts';\nexport type * from './Tokens.ts';\n", "import type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Gets the original marked default options.\n */\nexport function _getDefaults(): MarkedOptions {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null,\n  };\n}\n\nexport let _defaults = _getDefaults();\n\nexport function changeDefaults(newDefaults: MarkedOptions) {\n  _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null } as unknown as RegExp;\n\nfunction edit(regex: string | RegExp, opt = '') {\n  let source = typeof regex === 'string' ? regex : regex.source;\n  const obj = {\n    replace: (name: string | RegExp, val: string | RegExp) => {\n      let valSource = typeof val === 'string' ? val : val.source;\n      valSource = valSource.replace(other.caret, '$1');\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    },\n  };\n  return obj;\n}\n\nexport const other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: (bull: string) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n\n/**\n * Block-Level Grammar\n */\n\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/\\|table/g, '') // table not in commonmark\n  .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n  .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n  .replace('label', _blockLabel)\n  .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n  .getRegex();\n\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n  .replace(/bull/g, bullet)\n  .getRegex();\n\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n  + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n  + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n  + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n  + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n  + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit(\n  '^ {0,3}(?:' // optional indentation\n+ '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n+ '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n+ '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n+ '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n+ '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n+ '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n+ '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n+ '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n+ ')', 'i')\n  .replace('comment', _comment)\n  .replace('tag', _tag)\n  .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst paragraph = edit(_paragraph)\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n  .replace('|table', '')\n  .replace('blockquote', ' {0,3}>')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n  .replace('paragraph', paragraph)\n  .getRegex();\n\n/**\n * Normal Block Grammar\n */\n\nconst blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText,\n};\n\ntype BlockKeys = keyof typeof blockNormal;\n\n/**\n * GFM Block Grammar\n */\n\nconst gfmTable = edit(\n  '^ *([^\\\\n ].*)\\\\n' // Header\n+ ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n+ '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('blockquote', ' {0,3}>')\n  .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockGfm: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('table', gfmTable) // interrupt paragraphs with table\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex(),\n};\n\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\n\nconst blockPedantic: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  html: edit(\n    '^ *(?:comment *(?:\\\\n|\\\\s*$)'\n    + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n    + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n    .replace('comment', _comment)\n    .replace(/tag/g, '(?!(?:'\n      + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n      + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n      + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n    .getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest, // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' *#{1,6} *[^\\n]')\n    .replace('lheading', lheading)\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('|fences', '')\n    .replace('|list', '')\n    .replace('|html', '')\n    .replace('|tag', '')\n    .getRegex(),\n};\n\n/**\n * Inline-Level Grammar\n */\n\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n  .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\n\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\n\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\nconst emStrongRDelimAstCore =\n  '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n+ '|[^*]+(?=[^*])' // Consume to delim\n+ '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n+ '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n+ '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n+ '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\n\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n  .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit(\n  '^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n+ '|[^_]+(?=[^_])' // Consume to delim\n+ '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n+ '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n+ '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n+ '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n+ '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n  .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n  .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n  .getRegex();\n\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit(\n  '^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n  .replace('comment', _inlineComment)\n  .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\n\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/)\n  .replace('label', _inlineLabel)\n  .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/)\n  .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n  .getRegex();\n\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n  .replace('label', _inlineLabel)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n  .replace('reflink', reflink)\n  .replace('nolink', nolink)\n  .getRegex();\n\n/**\n * Normal Inline Grammar\n */\n\nconst inlineNormal = {\n  _backpedal: noopTest, // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest,\n};\n\ntype InlineKeys = keyof typeof inlineNormal;\n\n/**\n * Pedantic Inline Grammar\n */\n\nconst inlinePedantic: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n};\n\n/**\n * GFM Inline Grammar\n */\n\nconst inlineGfm: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n    .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n    .getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n\n/**\n * GFM + Line Breaks Inline Grammar\n */\n\nconst inlineBreaks: Record<InlineKeys, RegExp> = {\n  ...inlineGfm,\n  br: edit(br).replace('{2,}', '*').getRegex(),\n  text: edit(inlineGfm.text)\n    .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n    .replace(/\\{2,\\}/g, '*')\n    .getRegex(),\n};\n\n/**\n * exports\n */\n\nexport const block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic,\n};\n\nexport const inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic,\n};\n\nexport interface Rules {\n  other: typeof other\n  block: Record<BlockKeys, RegExp>\n  inline: Record<InlineKeys, RegExp>\n}\n", "import { other } from './rules.ts';\n\n/**\n * Helpers\n */\nconst escapeReplacements: { [index: string]: string } = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch: string) => escapeReplacements[ch];\n\nexport function escape(html: string, encode?: boolean) {\n  if (encode) {\n    if (other.escapeTest.test(html)) {\n      return html.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html)) {\n      return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n\nexport function unescape(html: string) {\n  // explicitly match decimal, hex, and named HTML entities\n  return html.replace(other.unescapeTest, (_, n) => {\n    n = n.toLowerCase();\n    if (n === 'colon') return ':';\n    if (n.charAt(0) === '#') {\n      return n.charAt(1) === 'x'\n        ? String.fromCharCode(parseInt(n.substring(2), 16))\n        : String.fromCharCode(+n.substring(1));\n    }\n    return '';\n  });\n}\n\nexport function cleanUrl(href: string) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, '%');\n  } catch {\n    return null;\n  }\n  return href;\n}\n\nexport function splitCells(tableRow: string, count?: number) {\n  // ensure that every cell-delimiting pipe has a space\n  // before it to distinguish it from an escaped pipe\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n      let escaped = false;\n      let curr = offset;\n      while (--curr >= 0 && str[curr] === '\\\\') escaped = !escaped;\n      if (escaped) {\n        // odd number of slashes means | is escaped\n        // so we leave it alone\n        return '|';\n      } else {\n        // add space before unescaped |\n        return ' |';\n      }\n    }),\n    cells = row.split(other.splitPipe);\n  let i = 0;\n\n  // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push('');\n    }\n  }\n\n  for (; i < cells.length; i++) {\n    // leading or trailing whitespace is ignored per the gfm spec\n    cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n  }\n  return cells;\n}\n\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str: string, c: string, invert?: boolean) {\n  const l = str.length;\n  if (l === 0) {\n    return '';\n  }\n\n  // Length of suffix matching the invert condition.\n  let suffLen = 0;\n\n  // Step left until we fail to match the invert condition.\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n\n  return str.slice(0, l - suffLen);\n}\n\nexport function findClosingBracket(str: string, b: string) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === '\\\\') {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n\n  return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  rtrim,\n  splitCells,\n  findClosingBracket,\n} from './helpers.ts';\nimport type { Rules } from './rules.ts';\nimport type { _Lexer } from './Lexer.ts';\nimport type { Links, Tokens, Token } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\nfunction outputLink(cap: string[], link: Pick<Tokens.Link, 'href' | 'title'>, raw: string, lexer: _Lexer, rules: Rules): Tokens.Link | Tokens.Image {\n  const href = link.href;\n  const title = link.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n\n  lexer.state.inLink = true;\n  const token: Tokens.Link | Tokens.Image = {\n    type: cap[0].charAt(0) === '!' ? 'image' : 'link',\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer.inlineTokens(text),\n  };\n  lexer.state.inLink = false;\n  return token;\n}\n\nfunction indentCodeCompensation(raw: string, text: string, rules: Rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n\n  if (matchIndentToCode === null) {\n    return text;\n  }\n\n  const indentToCode = matchIndentToCode[1];\n\n  return text\n    .split('\\n')\n    .map(node => {\n      const matchIndentInNode = node.match(rules.other.beginningSpace);\n      if (matchIndentInNode === null) {\n        return node;\n      }\n\n      const [indentInNode] = matchIndentInNode;\n\n      if (indentInNode.length >= indentToCode.length) {\n        return node.slice(indentToCode.length);\n      }\n\n      return node;\n    })\n    .join('\\n');\n}\n\n/**\n * Tokenizer\n */\nexport class _Tokenizer {\n  options: MarkedOptions;\n  rules!: Rules; // set by the lexer\n  lexer!: _Lexer; // set by the lexer\n\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n  }\n\n  space(src: string): Tokens.Space | undefined {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: 'space',\n        raw: cap[0],\n      };\n    }\n  }\n\n  code(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n      return {\n        type: 'code',\n        raw: cap[0],\n        codeBlockStyle: 'indented',\n        text: !this.options.pedantic\n          ? rtrim(text, '\\n')\n          : text,\n      };\n    }\n  }\n\n  fences(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n\n      return {\n        type: 'code',\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n        text,\n      };\n    }\n  }\n\n  heading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n\n      // remove trailing #s\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, '#');\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          // CommonMark requires space before trailing #s\n          text = trimmed.trim();\n        }\n      }\n\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  hr(src: string): Tokens.Hr | undefined {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: 'hr',\n        raw: rtrim(cap[0], '\\n'),\n      };\n    }\n  }\n\n  blockquote(src: string): Tokens.Blockquote | undefined {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], '\\n').split('\\n');\n      let raw = '';\n      let text = '';\n      const tokens: Token[] = [];\n\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          // get lines up to a continuation\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n\n        const currentRaw = currentLines.join('\\n');\n        const currentText = currentRaw\n          // precede setext continuation with 4 spaces so it isn't a setext\n          .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n          .replace(this.rules.other.blockquoteSetextReplace2, '');\n        raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\\n${currentText}` : currentText;\n\n        // parse blockquote lines as top level tokens\n        // merge paragraphs if this is a continuation\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n\n        // if there is no continuation then we are done\n        if (lines.length === 0) {\n          break;\n        }\n\n        const lastToken = tokens.at(-1);\n\n        if (lastToken?.type === 'code') {\n          // blockquote continuation cannot be preceded by a code block\n          break;\n        } else if (lastToken?.type === 'blockquote') {\n          // include continuation in nested blockquote\n          const oldToken = lastToken as Tokens.Blockquote;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.blockquote(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === 'list') {\n          // include continuation in nested list\n          const oldToken = lastToken as Tokens.List;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.list(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1)!.raw.length).split('\\n');\n          continue;\n        }\n      }\n\n      return {\n        type: 'blockquote',\n        raw,\n        tokens,\n        text,\n      };\n    }\n  }\n\n  list(src: string): Tokens.List | undefined {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n\n      const list: Tokens.List = {\n        type: 'list',\n        raw: '',\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : '',\n        loose: false,\n        items: [],\n      };\n\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n\n      if (this.options.pedantic) {\n        bull = isordered ? bull : '[*+-]';\n      }\n\n      // Get next list item\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      // Check if current bullet point can start a new List Item\n      while (src) {\n        let endEarly = false;\n        let raw = '';\n        let itemContents = '';\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n\n        if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n          break;\n        }\n\n        raw = cap[0];\n        src = src.substring(raw.length);\n\n        let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t: string) => ' '.repeat(3 * t.length));\n        let nextLine = src.split('\\n', 1)[0];\n        let blankLine = !line.trim();\n\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n          indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n          raw += nextLine + '\\n';\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n\n          // Check if following lines should be included in List Item\n          while (src) {\n            const rawLine = src.split('\\n', 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n\n            // Re-align to follow commonmark nesting rules\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n            }\n\n            // End list item if found code fences\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new heading\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of html block\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new bullet\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n\n            // Horizontal rule found\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n              itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n            } else {\n              // not enough indentation\n              if (blankLine) {\n                break;\n              }\n\n              // paragraph continuation unless last line was a different block level element\n              if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n\n              itemContents += '\\n' + nextLine;\n            }\n\n            if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n              blankLine = true;\n            }\n\n            raw += rawLine + '\\n';\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n\n        if (!list.loose) {\n          // If the previous item ended with a blank line, the list is loose\n          if (endsWithBlankLine) {\n            list.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n\n        let istask: RegExpExecArray | null = null;\n        let ischecked: boolean | undefined;\n        // Check for task list items\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== '[ ] ';\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n          }\n        }\n\n        list.items.push({\n          type: 'list_item',\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: [],\n        });\n\n        list.raw += raw;\n      }\n\n      // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n      const lastItem = list.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        // not a list since there were no items\n        return;\n      }\n      list.raw = list.raw.trimEnd();\n\n      // Item child tokens handled here at end because we needed to have the final item to trim it first\n      for (let i = 0; i < list.items.length; i++) {\n        this.lexer.state.top = false;\n        list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n\n        if (!list.loose) {\n          // Check if list should be loose\n          const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n\n          list.loose = hasMultipleLineBreaks;\n        }\n      }\n\n      // Set all items to loose if list is loose\n      if (list.loose) {\n        for (let i = 0; i < list.items.length; i++) {\n          list.items[i].loose = true;\n        }\n      }\n\n      return list;\n    }\n  }\n\n  html(src: string): Tokens.HTML | undefined {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token: Tokens.HTML = {\n        type: 'html',\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n        text: cap[0],\n      };\n      return token;\n    }\n  }\n\n  def(src: string): Tokens.Def | undefined {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n      return {\n        type: 'def',\n        tag,\n        raw: cap[0],\n        href,\n        title,\n      };\n    }\n  }\n\n  table(src: string): Tokens.Table | undefined {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n      return;\n    }\n\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n\n    const item: Tokens.Table = {\n      type: 'table',\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: [],\n    };\n\n    if (headers.length !== aligns.length) {\n      // header and align columns must be equal, rows can be different.\n      return;\n    }\n\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push('right');\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push('center');\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push('left');\n      } else {\n        item.align.push(null);\n      }\n    }\n\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i],\n      });\n    }\n\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i],\n        };\n      }));\n    }\n\n    return item;\n  }\n\n  lheading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[2].charAt(0) === '=' ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1]),\n      };\n    }\n  }\n\n  paragraph(src: string): Tokens.Paragraph | undefined {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n        ? cap[1].slice(0, -1)\n        : cap[1];\n      return {\n        type: 'paragraph',\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  text(src: string): Tokens.Text | undefined {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0]),\n      };\n    }\n  }\n\n  escape(src: string): Tokens.Escape | undefined {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: 'escape',\n        raw: cap[0],\n        text: cap[1],\n      };\n    }\n  }\n\n  tag(src: string): Tokens.Tag | undefined {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n\n      return {\n        type: 'html',\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0],\n      };\n    }\n  }\n\n  link(src: string): Tokens.Link | Tokens.Image | undefined {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        // commonmark requires matching angle brackets\n        if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          return;\n        }\n\n        // ending angle bracket cannot be escaped\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        // find closing parenthesis\n        const lastParenIndex = findClosingBracket(cap[2], '()');\n        if (lastParenIndex === -2) {\n          // more open parens than closed\n          return;\n        }\n\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = '';\n        }\n      }\n      let href = cap[2];\n      let title = '';\n      if (this.options.pedantic) {\n        // split pedantic href and title\n        const link = this.rules.other.pedanticHrefTitle.exec(href);\n\n        if (link) {\n          href = link[1];\n          title = link[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : '';\n      }\n\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          // pedantic allows starting angle bracket without ending angle bracket\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  reflink(src: string, links: Links): Tokens.Link | Tokens.Image | Tokens.Text | undefined {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src))\n      || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const link = links[linkString.toLowerCase()];\n      if (!link) {\n        const text = cap[0].charAt(0);\n        return {\n          type: 'text',\n          raw: text,\n          text,\n        };\n      }\n      return outputLink(cap, link, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  emStrong(src: string, maskedSrc: string, prevChar = ''): Tokens.Em | Tokens.Strong | undefined {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n\n    // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n\n    const nextChar = match[1] || match[2] || '';\n\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n      const lLength = [...match[0]].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n\n      const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n\n      // Clip maskedSrc to same section of string as src (move to lexer?)\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n\n        if (!rDelim) continue; // skip single * in __abc*abc__\n\n        rLength = [...rDelim].length;\n\n        if (match[3] || match[4]) { // found another Left Delim\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) { // either Left or Right Delim\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue; // CommonMark Emphasis Rules 9-10\n          }\n        }\n\n        delimTotal -= rLength;\n\n        if (delimTotal > 0) continue; // Haven't found enough closing delimiters\n\n        // Remove extra characters. *a*** -> *a*\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        // char length can be >1 for unicode characters;\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n\n        // Create `em` if smallest delimiter has odd char count. *a***\n        if (Math.min(lLength, rLength) % 2) {\n          const text = raw.slice(1, -1);\n          return {\n            type: 'em',\n            raw,\n            text,\n            tokens: this.lexer.inlineTokens(text),\n          };\n        }\n\n        // Create 'strong' if smallest delimiter has even char count. **a***\n        const text = raw.slice(2, -2);\n        return {\n          type: 'strong',\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text),\n        };\n      }\n    }\n  }\n\n  codespan(src: string): Tokens.Codespan | undefined {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: 'codespan',\n        raw: cap[0],\n        text,\n      };\n    }\n  }\n\n  br(src: string): Tokens.Br | undefined {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: 'br',\n        raw: cap[0],\n      };\n    }\n  }\n\n  del(src: string): Tokens.Del | undefined {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: 'del',\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2]),\n      };\n    }\n  }\n\n  autolink(src: string): Tokens.Link | undefined {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[1];\n        href = 'mailto:' + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  url(src: string): Tokens.Link | undefined {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[0];\n        href = 'mailto:' + text;\n      } else {\n        // do extended autolink path validation\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === 'www.') {\n          href = 'http://' + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  inlineText(src: string): Tokens.Text | undefined {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        escaped,\n      };\n    }\n  }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\nimport type { Token, TokensList, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Block Lexer\n */\nexport class _Lexer {\n  tokens: TokensList;\n  options: MarkedOptions;\n  state: {\n    inLink: boolean;\n    inRawBlock: boolean;\n    top: boolean;\n  };\n\n  private tokenizer: _Tokenizer;\n  private inlineQueue: { src: string, tokens: Token[] }[];\n\n  constructor(options?: MarkedOptions) {\n    // TokenList cannot be created in one go\n    this.tokens = [] as unknown as TokensList;\n    this.tokens.links = Object.create(null);\n    this.options = options || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true,\n    };\n\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal,\n    };\n\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline,\n    };\n  }\n\n  /**\n   * Static Lex Method\n   */\n  static lex(src: string, options?: MarkedOptions) {\n    const lexer = new _Lexer(options);\n    return lexer.lex(src);\n  }\n\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src: string, options?: MarkedOptions) {\n    const lexer = new _Lexer(options);\n    return lexer.inlineTokens(src);\n  }\n\n  /**\n   * Preprocessing\n   */\n  lex(src: string) {\n    src = src.replace(other.carriageReturn, '\\n');\n\n    this.blockTokens(src, this.tokens);\n\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n\n    return this.tokens;\n  }\n\n  /**\n   * Lexing\n   */\n  blockTokens(src: string, tokens?: Token[], lastParagraphClipped?: boolean): Token[];\n  blockTokens(src: string, tokens?: TokensList, lastParagraphClipped?: boolean): TokensList;\n  blockTokens(src: string, tokens: Token[] = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n    }\n\n    while (src) {\n      let token: Tokens.Generic | undefined;\n\n      if (this.options.extensions?.block?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // newline\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== undefined) {\n          // if there's a single \\n as a spacer, it's terminating the last line,\n          // so move it there so that we don't get unnecessary paragraph tags\n          lastToken.raw += '\\n';\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        // An indented code block cannot interrupt a paragraph.\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // fences\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // heading\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // hr\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // blockquote\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // list\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // html\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // def\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.raw;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title,\n          };\n        }\n        continue;\n      }\n\n      // table (gfm)\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // lheading\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // top-level paragraph\n      // prevent paragraph consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n\n      // text\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    this.state.top = true;\n    return tokens;\n  }\n\n  inline(src: string, tokens: Token[] = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src: string, tokens: Token[] = []): Token[] {\n    // String with links masked to avoid interference with em and strong\n    let maskedSrc = src;\n    let match: RegExpExecArray | null = null;\n\n    // Mask out reflinks\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index)\n              + '[' + 'a'.repeat(match[0].length - 2) + ']'\n              + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n\n    // Mask out escaped characters\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n\n    // Mask out other blocks\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n\n    let keepPrevChar = false;\n    let prevChar = '';\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = '';\n      }\n      keepPrevChar = false;\n\n      let token: Tokens.Generic | undefined;\n\n      // extensions\n      if (this.options.extensions?.inline?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // escape\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // tag\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // link\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // reflink, nolink\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === 'text' && lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // em & strong\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // br\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // del (gfm)\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // autolink\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // url (gfm)\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // text\n      // prevent inlineText consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    return tokens;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  cleanUrl,\n  escape,\n} from './helpers.ts';\nimport { other } from './rules.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Tokens } from './Tokens.ts';\nimport type { _Parser } from './Parser.ts';\n\n/**\n * Renderer\n */\nexport class _Renderer {\n  options: MarkedOptions;\n  parser!: _Parser; // set by the parser\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n  }\n\n  space(token: Tokens.Space): string {\n    return '';\n  }\n\n  code({ text, lang, escaped }: Tokens.Code): string {\n    const langString = (lang || '').match(other.notSpaceStart)?.[0];\n\n    const code = text.replace(other.endingNewline, '') + '\\n';\n\n    if (!langString) {\n      return '<pre><code>'\n        + (escaped ? code : escape(code, true))\n        + '</code></pre>\\n';\n    }\n\n    return '<pre><code class=\"language-'\n      + escape(langString)\n      + '\">'\n      + (escaped ? code : escape(code, true))\n      + '</code></pre>\\n';\n  }\n\n  blockquote({ tokens }: Tokens.Blockquote): string {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\\n${body}</blockquote>\\n`;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag) : string {\n    return text;\n  }\n\n  heading({ tokens, depth }: Tokens.Heading): string {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n  }\n\n  hr(token: Tokens.Hr): string {\n    return '<hr>\\n';\n  }\n\n  list(token: Tokens.List): string {\n    const ordered = token.ordered;\n    const start = token.start;\n\n    let body = '';\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n\n    const type = ordered ? 'ol' : 'ul';\n    const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n    return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n  }\n\n  listitem(item: Tokens.ListItem): string {\n    let itemBody = '';\n    if (item.task) {\n      const checkbox = this.checkbox({ checked: !!item.checked });\n      if (item.loose) {\n        if (item.tokens[0]?.type === 'paragraph') {\n          item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n            item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: 'text',\n            raw: checkbox + ' ',\n            text: checkbox + ' ',\n            escaped: true,\n          });\n        }\n      } else {\n        itemBody += checkbox + ' ';\n      }\n    }\n\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n\n    return `<li>${itemBody}</li>\\n`;\n  }\n\n  checkbox({ checked }: Tokens.Checkbox): string {\n    return '<input '\n      + (checked ? 'checked=\"\" ' : '')\n      + 'disabled=\"\" type=\"checkbox\">';\n  }\n\n  paragraph({ tokens }: Tokens.Paragraph): string {\n    return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n  }\n\n  table(token: Tokens.Table): string {\n    let header = '';\n\n    // header\n    let cell = '';\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({ text: cell });\n\n    let body = '';\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n\n      cell = '';\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n\n      body += this.tablerow({ text: cell });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n\n    return '<table>\\n'\n      + '<thead>\\n'\n      + header\n      + '</thead>\\n'\n      + body\n      + '</table>\\n';\n  }\n\n  tablerow({ text }: Tokens.TableRow): string {\n    return `<tr>\\n${text}</tr>\\n`;\n  }\n\n  tablecell(token: Tokens.TableCell): string {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? 'th' : 'td';\n    const tag = token.align\n      ? `<${type} align=\"${token.align}\">`\n      : `<${type}>`;\n    return tag + content + `</${type}>\\n`;\n  }\n\n  /**\n   * span level renderer\n   */\n  strong({ tokens }: Tokens.Strong): string {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n  }\n\n  em({ tokens }: Tokens.Em): string {\n    return `<em>${this.parser.parseInline(tokens)}</em>`;\n  }\n\n  codespan({ text }: Tokens.Codespan): string {\n    return `<code>${escape(text, true)}</code>`;\n  }\n\n  br(token: Tokens.Br): string {\n    return '<br>';\n  }\n\n  del({ tokens }: Tokens.Del): string {\n    return `<del>${this.parser.parseInline(tokens)}</del>`;\n  }\n\n  link({ href, title, tokens }: Tokens.Link): string {\n    const text = this.parser.parseInline(tokens);\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + (escape(title)) + '\"';\n    }\n    out += '>' + text + '</a>';\n    return out;\n  }\n\n  image({ href, title, text, tokens }: Tokens.Image): string {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer);\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape(text);\n    }\n    href = cleanHref;\n\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape(title)}\"`;\n    }\n    out += '>';\n    return out;\n  }\n\n  text(token: Tokens.Text | Tokens.Escape) : string {\n    return 'tokens' in token && token.tokens\n      ? this.parser.parseInline(token.tokens)\n      : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n  }\n}\n", "import type { Tokens } from './Tokens.ts';\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nexport class _TextRenderer {\n  // no need for block level renderers\n  strong({ text }: Tokens.Strong) {\n    return text;\n  }\n\n  em({ text }: Tokens.Em) {\n    return text;\n  }\n\n  codespan({ text }: Tokens.Codespan) {\n    return text;\n  }\n\n  del({ text }: Tokens.Del) {\n    return text;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag) {\n    return text;\n  }\n\n  text({ text }: Tokens.Text | Tokens.Escape | Tokens.Tag) {\n    return text;\n  }\n\n  link({ text }: Tokens.Link) {\n    return '' + text;\n  }\n\n  image({ text }: Tokens.Image) {\n    return '' + text;\n  }\n\n  br() {\n    return '';\n  }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\nimport type { MarkedToken, Token, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Parsing & Compiling\n */\nexport class _Parser {\n  options: MarkedOptions;\n  renderer: _Renderer;\n  textRenderer: _TextRenderer;\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer();\n  }\n\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens: Token[], options?: MarkedOptions) {\n    const parser = new _Parser(options);\n    return parser.parse(tokens);\n  }\n\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens: Token[], options?: MarkedOptions) {\n    const parser = new _Parser(options);\n    return parser.parseInline(tokens);\n  }\n\n  /**\n   * Parse Loop\n   */\n  parse(tokens: Token[], top = true): string {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken as Tokens.Generic;\n        const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n        if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'space': {\n          out += this.renderer.space(token);\n          continue;\n        }\n        case 'hr': {\n          out += this.renderer.hr(token);\n          continue;\n        }\n        case 'heading': {\n          out += this.renderer.heading(token);\n          continue;\n        }\n        case 'code': {\n          out += this.renderer.code(token);\n          continue;\n        }\n        case 'table': {\n          out += this.renderer.table(token);\n          continue;\n        }\n        case 'blockquote': {\n          out += this.renderer.blockquote(token);\n          continue;\n        }\n        case 'list': {\n          out += this.renderer.list(token);\n          continue;\n        }\n        case 'html': {\n          out += this.renderer.html(token);\n          continue;\n        }\n        case 'paragraph': {\n          out += this.renderer.paragraph(token);\n          continue;\n        }\n        case 'text': {\n          let textToken = token;\n          let body = this.renderer.text(textToken);\n          while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n            textToken = tokens[++i] as Tokens.Text;\n            body += '\\n' + this.renderer.text(textToken);\n          }\n          if (top) {\n            out += this.renderer.paragraph({\n              type: 'paragraph',\n              raw: body,\n              text: body,\n              tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n            });\n          } else {\n            out += body;\n          }\n          continue;\n        }\n\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '';\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n\n    return out;\n  }\n\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens: Token[], renderer: _Renderer | _TextRenderer = this.renderer): string {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n        if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'escape': {\n          out += renderer.text(token);\n          break;\n        }\n        case 'html': {\n          out += renderer.html(token);\n          break;\n        }\n        case 'link': {\n          out += renderer.link(token);\n          break;\n        }\n        case 'image': {\n          out += renderer.image(token);\n          break;\n        }\n        case 'strong': {\n          out += renderer.strong(token);\n          break;\n        }\n        case 'em': {\n          out += renderer.em(token);\n          break;\n        }\n        case 'codespan': {\n          out += renderer.codespan(token);\n          break;\n        }\n        case 'br': {\n          out += renderer.br(token);\n          break;\n        }\n        case 'del': {\n          out += renderer.del(token);\n          break;\n        }\n        case 'text': {\n          out += renderer.text(token);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '';\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\n\nexport class _Hooks {\n  options: MarkedOptions;\n  block?: boolean;\n\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n  }\n\n  static passThroughHooks = new Set([\n    'preprocess',\n    'postprocess',\n    'processAllTokens',\n  ]);\n\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown: string) {\n    return markdown;\n  }\n\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html: string) {\n    return html;\n  }\n\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens: Token[] | TokensList) {\n    return tokens;\n  }\n\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse : _Parser.parseInline;\n  }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, Tokens, TokensList } from './Tokens.ts';\n\nexport type MaybePromise = void | Promise<void>;\n\ntype UnknownFunction = (...args: unknown[]) => unknown;\ntype GenericRendererFunction = (...args: unknown[]) => string | false;\n\nexport class Marked {\n  defaults = _getDefaults();\n  options = this.setOptions;\n\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n\n  Parser = _Parser;\n  Renderer = _Renderer;\n  TextRenderer = _TextRenderer;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer;\n  Hooks = _Hooks;\n\n  constructor(...args: MarkedExtension[]) {\n    this.use(...args);\n  }\n\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n    let values: MaybePromise[] = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case 'table': {\n          const tableToken = token as Tokens.Table;\n          for (const cell of tableToken.header) {\n            values = values.concat(this.walkTokens(cell.tokens, callback));\n          }\n          for (const row of tableToken.rows) {\n            for (const cell of row) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n          }\n          break;\n        }\n        case 'list': {\n          const listToken = token as Tokens.List;\n          values = values.concat(this.walkTokens(listToken.items, callback));\n          break;\n        }\n        default: {\n          const genericToken = token as Tokens.Generic;\n          if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n            this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n              const tokens = genericToken[childTokens].flat(Infinity) as Token[] | TokensList;\n              values = values.concat(this.walkTokens(tokens, callback));\n            });\n          } else if (genericToken.tokens) {\n            values = values.concat(this.walkTokens(genericToken.tokens, callback));\n          }\n        }\n      }\n    }\n    return values;\n  }\n\n  use(...args: MarkedExtension[]) {\n    const extensions: MarkedOptions['extensions'] = this.defaults.extensions || { renderers: {}, childTokens: {} };\n\n    args.forEach((pack) => {\n      // copy options to new object\n      const opts = { ...pack } as MarkedOptions;\n\n      // set async to true if it was set to true before\n      opts.async = this.defaults.async || opts.async || false;\n\n      // ==-- Parse \"addon\" extensions --== //\n      if (pack.extensions) {\n        pack.extensions.forEach((ext) => {\n          if (!ext.name) {\n            throw new Error('extension name required');\n          }\n          if ('renderer' in ext) { // Renderer extensions\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              // Replace extension with func to run new extension but fall back if false\n              extensions.renderers[ext.name] = function(...args) {\n                let ret = ext.renderer.apply(this, args);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if ('tokenizer' in ext) { // Tokenizer Extensions\n            if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) { // Function to check for start of token\n              if (ext.level === 'block') {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === 'inline') {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n\n      // ==-- Parse \"overwrite\" extensions --== //\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if (['options', 'parser'].includes(prop)) {\n            // ignore options property\n            continue;\n          }\n          const rendererProp = prop as Exclude<keyof _Renderer, 'options' | 'parser'>;\n          const rendererFunc = pack.renderer[rendererProp] as GenericRendererFunction;\n          const prevRenderer = renderer[rendererProp] as GenericRendererFunction;\n          // Replace renderer with func to run extension, but fall back if false\n          renderer[rendererProp] = (...args: unknown[]) => {\n            let ret = rendererFunc.apply(renderer, args);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args);\n            }\n            return ret || '';\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if (['options', 'rules', 'lexer'].includes(prop)) {\n            // ignore options, rules, and lexer properties\n            continue;\n          }\n          const tokenizerProp = prop as Exclude<keyof _Tokenizer, 'options' | 'rules' | 'lexer'>;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp] as UnknownFunction;\n          const prevTokenizer = tokenizer[tokenizerProp] as UnknownFunction;\n          // Replace tokenizer with func to run extension, but fall back if false\n          // @ts-expect-error cannot type tokenizer function dynamically\n          tokenizer[tokenizerProp] = (...args: unknown[]) => {\n            let ret = tokenizerFunc.apply(tokenizer, args);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n\n      // ==-- Parse Hooks extensions --== //\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if (['options', 'block'].includes(prop)) {\n            // ignore options and block properties\n            continue;\n          }\n          const hooksProp = prop as Exclude<keyof _Hooks, 'options' | 'block'>;\n          const hooksFunc = pack.hooks[hooksProp] as UnknownFunction;\n          const prevHook = hooks[hooksProp] as UnknownFunction;\n          if (_Hooks.passThroughHooks.has(prop)) {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (arg: unknown) => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                  return prevHook.call(hooks, ret);\n                });\n              }\n\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (...args: unknown[]) => {\n              let ret = hooksFunc.apply(hooks, args);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n\n      // ==-- Parse WalkTokens extensions --== //\n      if (pack.walkTokens) {\n        const walkTokens = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function(token) {\n          let values: MaybePromise[] = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens) {\n            values = values.concat(walkTokens.call(this, token));\n          }\n          return values;\n        };\n      }\n\n      this.defaults = { ...this.defaults, ...opts };\n    });\n\n    return this;\n  }\n\n  setOptions(opt: MarkedOptions) {\n    this.defaults = { ...this.defaults, ...opt };\n    return this;\n  }\n\n  lexer(src: string, options?: MarkedOptions) {\n    return _Lexer.lex(src, options ?? this.defaults);\n  }\n\n  parser(tokens: Token[], options?: MarkedOptions) {\n    return _Parser.parse(tokens, options ?? this.defaults);\n  }\n\n  private parseMarkdown(blockType: boolean) {\n    type overloadedParse = {\n      (src: string, options: MarkedOptions & { async: true }): Promise<string>;\n      (src: string, options: MarkedOptions & { async: false }): string;\n      (src: string, options?: MarkedOptions | null): string | Promise<string>;\n    };\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const parse: overloadedParse = (src: string, options?: MarkedOptions | null): any => {\n      const origOpt = { ...options };\n      const opt = { ...this.defaults, ...origOpt };\n\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n\n      // throw error if an extension set async to true but parse was called with async: false\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n      }\n\n      // throw error in case of non string input\n      if (typeof src === 'undefined' || src === null) {\n        return throwError(new Error('marked(): input parameter is undefined or null'));\n      }\n      if (typeof src !== 'string') {\n        return throwError(new Error('marked(): input parameter is of type '\n          + Object.prototype.toString.call(src) + ', string expected'));\n      }\n\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n\n      const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n      const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n          .then(src => lexer(src, opt))\n          .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n          .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n          .then(tokens => parser(tokens, opt))\n          .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n          .catch(throwError);\n      }\n\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src) as string;\n        }\n        let tokens = lexer(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html = parser(tokens, opt);\n        if (opt.hooks) {\n          html = opt.hooks.postprocess(html) as string;\n        }\n        return html;\n      } catch (e) {\n        return throwError(e as Error);\n      }\n    };\n\n    return parse;\n  }\n\n  private onError(silent: boolean, async: boolean) {\n    return (e: Error): string | Promise<string> => {\n      e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n\n      if (silent) {\n        const msg = '<p>An error occurred:</p><pre>'\n          + escape(e.message + '', true)\n          + '</pre>';\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACKO,SAAS,eAA8B;AAC5C,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AACF;AAEO,IAAI,YAAY,aAAa;AAE7B,SAAS,eAAe,aAA4B;AACzD,cAAY;AACd;;;ACxBA,IAAM,WAAW,EAAE,MAAM,MAAM,KAAK;AAEpC,SAAS,KAAK,OAAwB,MAAM,IAAI;AAC9C,MAAI,SAAS,OAAO,UAAU,WAAW,QAAQ,MAAM;AACvD,QAAM,MAAM;AAAA,IACV,SAAS,CAAC,MAAuB,QAAyB;AACxD,UAAI,YAAY,OAAO,QAAQ,WAAW,MAAM,IAAI;AACpD,kBAAY,UAAU,QAAQ,MAAM,OAAO,IAAI;AAC/C,eAAS,OAAO,QAAQ,MAAM,SAAS;AACvC,aAAO;AAAA,IACT;AAAA,IACA,UAAU,MAAM;AACd,aAAO,IAAI,OAAO,QAAQ,GAAG;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAEO,IAAM,QAAQ;AAAA,EACnB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,cAAc;AAAA,EACd,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe,CAAC,SAAiB,IAAI,OAAO,WAAW,IAAI,8BAA+B;AAAA,EAC1F,iBAAiB,CAAC,WAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAqD;AAAA,EACpI,SAAS,CAAC,WAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;AAAA,EAC3H,kBAAkB,CAAC,WAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,iBAAiB;AAAA,EACjG,mBAAmB,CAAC,WAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI;AAAA,EACrF,gBAAgB,CAAC,WAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,sBAAsB,GAAG;AACzG;AAMA,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,SAAS;AACf,IAAM,KAAK;AACX,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,WAAW,KAAK,YAAY,EAC/B,QAAQ,SAAS,MAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,YAAY,EAAE,EACtB,SAAS;AACZ,IAAM,cAAc,KAAK,YAAY,EAClC,QAAQ,SAAS,MAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,UAAU,mCAAmC,EACrD,SAAS;AACZ,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,MAAM,KAAK,6GAA6G,EAC3H,QAAQ,SAAS,WAAW,EAC5B,QAAQ,SAAS,8DAA8D,EAC/E,SAAS;AAEZ,IAAM,OAAO,KAAK,sCAAsC,EACrD,QAAQ,SAAS,MAAM,EACvB,SAAS;AAEZ,IAAM,OAAO;AAMb,IAAM,WAAW;AACjB,IAAM,OAAO;AAAA,EACX;AAAA,EASK;AAAG,EACP,QAAQ,WAAW,QAAQ,EAC3B,QAAQ,OAAO,IAAI,EACnB,QAAQ,aAAa,0EAA0E,EAC/F,SAAS;AAEZ,IAAM,YAAY,KAAK,UAAU,EAC9B,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAS;AAEZ,IAAM,aAAa,KAAK,yCAAyC,EAC9D,QAAQ,aAAa,SAAS,EAC9B,SAAS;AAMZ,IAAM,cAAc;AAAA,EAClB;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AACR;AAQA,IAAM,WAAW;AAAA,EACf;AAEsF,EACrF,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,cAAc,SAAS,EAC/B,QAAQ,QAAQ,wBAAyB,EACzC,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAS;AAEZ,IAAM,WAAsC;AAAA,EAC1C,GAAG;AAAA,EACH,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW,KAAK,UAAU,EACvB,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,SAAS,QAAQ,EACzB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAS;AACd;AAMA,IAAM,gBAA2C;AAAA,EAC/C,GAAG;AAAA,EACH,MAAM;AAAA,IACJ;AAAA,EAEwE,EACvE,QAAQ,WAAW,QAAQ,EAC3B,QAAQ,QAAQ,mKAGkB,EAClC,SAAS;AAAA,EACZ,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA;AAAA,EACR,UAAU;AAAA,EACV,WAAW,KAAK,UAAU,EACvB,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,iBAAiB,EACpC,QAAQ,YAAY,QAAQ,EAC5B,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,WAAW,EAAE,EACrB,QAAQ,SAAS,EAAE,EACnB,QAAQ,SAAS,EAAE,EACnB,QAAQ,QAAQ,EAAE,EAClB,SAAS;AACd;AAMA,IAAM,SAAS;AACf,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,aAAa;AAGnB,IAAM,eAAe;AACrB,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,cAAc,KAAK,yBAAyB,GAAG,EAClD,QAAQ,eAAe,mBAAmB,EAAE,SAAS;AAGxD,IAAM,0BAA0B;AAChC,IAAM,iCAAiC;AACvC,IAAM,oCAAoC;AAG1C,IAAM,YAAY;AAElB,IAAM,qBAAqB;AAE3B,IAAM,iBAAiB,KAAK,oBAAoB,GAAG,EAChD,QAAQ,UAAU,YAAY,EAC9B,SAAS;AAEZ,IAAM,oBAAoB,KAAK,oBAAoB,GAAG,EACnD,QAAQ,UAAU,uBAAuB,EACzC,SAAS;AAEZ,IAAM,wBACJ;AASF,IAAM,oBAAoB,KAAK,uBAAuB,IAAI,EACvD,QAAQ,kBAAkB,sBAAsB,EAChD,QAAQ,eAAe,mBAAmB,EAC1C,QAAQ,UAAU,YAAY,EAC9B,SAAS;AAEZ,IAAM,uBAAuB,KAAK,uBAAuB,IAAI,EAC1D,QAAQ,kBAAkB,iCAAiC,EAC3D,QAAQ,eAAe,8BAA8B,EACrD,QAAQ,UAAU,uBAAuB,EACzC,SAAS;AAGZ,IAAM,oBAAoB;AAAA,EACxB;AAAA,EAMiC;AAAI,EACpC,QAAQ,kBAAkB,sBAAsB,EAChD,QAAQ,eAAe,mBAAmB,EAC1C,QAAQ,UAAU,YAAY,EAC9B,SAAS;AAEZ,IAAM,iBAAiB,KAAK,aAAa,IAAI,EAC1C,QAAQ,UAAU,YAAY,EAC9B,SAAS;AAEZ,IAAM,WAAW,KAAK,qCAAqC,EACxD,QAAQ,UAAU,8BAA8B,EAChD,QAAQ,SAAS,8IAA8I,EAC/J,SAAS;AAEZ,IAAM,iBAAiB,KAAK,QAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAS;AAC3E,IAAM,MAAM;AAAA,EACV;AAKsC,EACrC,QAAQ,WAAW,cAAc,EACjC,QAAQ,aAAa,6EAA6E,EAClG,SAAS;AAEZ,IAAM,eAAe;AAErB,IAAM,OAAO,KAAK,mEAAmE,EAClF,QAAQ,SAAS,YAAY,EAC7B,QAAQ,QAAQ,yCAAyC,EACzD,QAAQ,SAAS,6DAA6D,EAC9E,SAAS;AAEZ,IAAM,UAAU,KAAK,yBAAyB,EAC3C,QAAQ,SAAS,YAAY,EAC7B,QAAQ,OAAO,WAAW,EAC1B,SAAS;AAEZ,IAAM,SAAS,KAAK,uBAAuB,EACxC,QAAQ,OAAO,WAAW,EAC1B,SAAS;AAEZ,IAAM,gBAAgB,KAAK,yBAAyB,GAAG,EACpD,QAAQ,WAAW,OAAO,EAC1B,QAAQ,UAAU,MAAM,EACxB,SAAS;AAMZ,IAAM,eAAe;AAAA,EACnB,YAAY;AAAA;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,KAAK;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,KAAK;AACP;AAQA,IAAM,iBAA6C;AAAA,EACjD,GAAG;AAAA,EACH,MAAM,KAAK,yBAAyB,EACjC,QAAQ,SAAS,YAAY,EAC7B,SAAS;AAAA,EACZ,SAAS,KAAK,+BAA+B,EAC1C,QAAQ,SAAS,YAAY,EAC7B,SAAS;AACd;AAMA,IAAM,YAAwC;AAAA,EAC5C,GAAG;AAAA,EACH,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,KAAK,KAAK,oEAAoE,GAAG,EAC9E,QAAQ,SAAS,2EAA2E,EAC5F,SAAS;AAAA,EACZ,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AACR;AAMA,IAAM,eAA2C;AAAA,EAC/C,GAAG;AAAA,EACH,IAAI,KAAK,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAS;AAAA,EAC3C,MAAM,KAAK,UAAU,IAAI,EACtB,QAAQ,QAAQ,eAAe,EAC/B,QAAQ,WAAW,GAAG,EACtB,SAAS;AACd;AAMO,IAAM,QAAQ;AAAA,EACnB,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AACZ;AAEO,IAAM,SAAS;AAAA,EACpB,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;;;ACzbA,IAAM,qBAAkD;AAAA,EACtD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAM,uBAAuB,CAAC,OAAe,mBAAmB,EAAE;AAE3D,SAASA,QAAOC,OAAc,QAAkB;AACrD,MAAI,QAAQ;AACV,QAAI,MAAM,WAAW,KAAKA,KAAI,GAAG;AAC/B,aAAOA,MAAK,QAAQ,MAAM,eAAe,oBAAoB;AAAA,IAC/D;AAAA,EACF,OAAO;AACL,QAAI,MAAM,mBAAmB,KAAKA,KAAI,GAAG;AACvC,aAAOA,MAAK,QAAQ,MAAM,uBAAuB,oBAAoB;AAAA,IACvE;AAAA,EACF;AAEA,SAAOA;AACT;AAgBO,SAAS,SAAS,MAAc;AACrC,MAAI;AACF,WAAO,UAAU,IAAI,EAAE,QAAQ,MAAM,eAAe,GAAG;AAAA,EACzD,QAAQ;AACN,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEO,SAAS,WAAW,UAAkB,OAAgB;AAG3D,QAAM,MAAM,SAAS,QAAQ,MAAM,UAAU,CAAC,OAAO,QAAQ,QAAQ;AACjE,QAAI,UAAU;AACd,QAAI,OAAO;AACX,WAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAM,WAAU,CAAC;AACrD,QAAI,SAAS;AAGX,aAAO;AAAA,IACT,OAAO;AAEL,aAAO;AAAA,IACT;AAAA,EACF,CAAC,GACD,QAAQ,IAAI,MAAM,MAAM,SAAS;AACnC,MAAI,IAAI;AAGR,MAAI,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG;AACpB,UAAM,MAAM;AAAA,EACd;AACA,MAAI,MAAM,SAAS,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG;AAC7C,UAAM,IAAI;AAAA,EACZ;AAEA,MAAI,OAAO;AACT,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,OAAO,KAAK;AAAA,IACpB,OAAO;AACL,aAAO,MAAM,SAAS,MAAO,OAAM,KAAK,EAAE;AAAA,IAC5C;AAAA,EACF;AAEA,SAAO,IAAI,MAAM,QAAQ,KAAK;AAE5B,UAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,MAAM,WAAW,GAAG;AAAA,EACzD;AACA,SAAO;AACT;AAUO,SAAS,MAAM,KAAa,GAAW,QAAkB;AAC9D,QAAM,IAAI,IAAI;AACd,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAGA,MAAI,UAAU;AAGd,SAAO,UAAU,GAAG;AAClB,UAAM,WAAW,IAAI,OAAO,IAAI,UAAU,CAAC;AAC3C,QAAI,aAAa,KAAK,CAAC,QAAQ;AAC7B;AAAA,IACF,WAAW,aAAa,KAAK,QAAQ;AACnC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,MAAM,GAAG,IAAI,OAAO;AACjC;AAEO,SAAS,mBAAmB,KAAa,GAAW;AACzD,MAAI,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,IAAI;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,IAAI,CAAC,MAAM,MAAM;AACnB;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AACA,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACzIA,SAAS,WAAW,KAAeC,OAA2C,KAAaC,QAAe,OAA0C;AAClJ,QAAM,OAAOD,MAAK;AAClB,QAAM,QAAQA,MAAK,SAAS;AAC5B,QAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,MAAM,MAAM,mBAAmB,IAAI;AAE/D,EAAAC,OAAM,MAAM,SAAS;AACrB,QAAM,QAAoC;AAAA,IACxC,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,UAAU;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQA,OAAM,aAAa,IAAI;AAAA,EACjC;AACA,EAAAA,OAAM,MAAM,SAAS;AACrB,SAAO;AACT;AAEA,SAAS,uBAAuB,KAAa,MAAc,OAAc;AACvE,QAAM,oBAAoB,IAAI,MAAM,MAAM,MAAM,sBAAsB;AAEtE,MAAI,sBAAsB,MAAM;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,kBAAkB,CAAC;AAExC,SAAO,KACJ,MAAM,IAAI,EACV,IAAI,UAAQ;AACX,UAAM,oBAAoB,KAAK,MAAM,MAAM,MAAM,cAAc;AAC/D,QAAI,sBAAsB,MAAM;AAC9B,aAAO;AAAA,IACT;AAEA,UAAM,CAAC,YAAY,IAAI;AAEvB,QAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,aAAO,KAAK,MAAM,aAAa,MAAM;AAAA,IACvC;AAEA,WAAO;AAAA,EACT,CAAC,EACA,KAAK,IAAI;AACd;AAKO,IAAM,aAAN,MAAiB;AAAA,EACtB;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EAEA,YAAYC,UAAyB;AACnC,SAAK,UAAUA,YAAW;AAAA,EAC5B;AAAA,EAEA,MAAM,KAAuC;AAC3C,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,IAAI,CAAC,EAAE,SAAS,GAAG;AAC5B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAsC;AACzC,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAkB,EAAE;AACjE,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,gBAAgB;AAAA,QAChB,MAAM,CAAC,KAAK,QAAQ,WAChB,MAAM,MAAM,IAAI,IAChB;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,KAAsC;AAC3C,UAAM,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC5C,QAAI,KAAK;AACP,YAAM,MAAM,IAAI,CAAC;AACjB,YAAM,OAAO,uBAAuB,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK;AAEjE,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AAAA,QACpF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,QAAQ,KAAyC;AAC/C,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AAGvB,UAAI,KAAK,MAAM,MAAM,WAAW,KAAK,IAAI,GAAG;AAC1C,cAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,YAAI,KAAK,QAAQ,UAAU;AACzB,iBAAO,QAAQ,KAAK;AAAA,QACtB,WAAW,CAAC,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,OAAO,GAAG;AAErE,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE;AAAA,QACd;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,GAAG,KAAoC;AACrC,UAAM,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,MAAM,IAAI,CAAC,GAAG,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,KAA4C;AACrD,UAAM,MAAM,KAAK,MAAM,MAAM,WAAW,KAAK,GAAG;AAChD,QAAI,KAAK;AACP,UAAI,QAAQ,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,IAAI;AAC1C,UAAI,MAAM;AACV,UAAI,OAAO;AACX,YAAM,SAAkB,CAAC;AAEzB,aAAO,MAAM,SAAS,GAAG;AACvB,YAAI,eAAe;AACnB,cAAM,eAAe,CAAC;AAEtB,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAEjC,cAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,GAAG;AACnD,yBAAa,KAAK,MAAM,CAAC,CAAC;AAC1B,2BAAe;AAAA,UACjB,WAAW,CAAC,cAAc;AACxB,yBAAa,KAAK,MAAM,CAAC,CAAC;AAAA,UAC5B,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,MAAM,MAAM,CAAC;AAErB,cAAM,aAAa,aAAa,KAAK,IAAI;AACzC,cAAM,cAAc,WAEjB,QAAQ,KAAK,MAAM,MAAM,yBAAyB,UAAU,EAC5D,QAAQ,KAAK,MAAM,MAAM,0BAA0B,EAAE;AACxD,cAAM,MAAM,GAAG,GAAG;AAAA,EAAK,UAAU,KAAK;AACtC,eAAO,OAAO,GAAG,IAAI;AAAA,EAAK,WAAW,KAAK;AAI1C,cAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,aAAK,MAAM,MAAM,MAAM;AACvB,aAAK,MAAM,YAAY,aAAa,QAAQ,IAAI;AAChD,aAAK,MAAM,MAAM,MAAM;AAGvB,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AAEA,cAAM,YAAY,OAAO,GAAG,EAAE;AAE9B,YAAI,WAAW,SAAS,QAAQ;AAE9B;AAAA,QACF,WAAW,WAAW,SAAS,cAAc;AAE3C,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,WAAW,OAAO;AACxC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAE5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACpE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,KAAK,MAAM,IAAI,SAAS;AACxE;AAAA,QACF,WAAW,WAAW,SAAS,QAAQ;AAErC,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,KAAK,OAAO;AAClC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAE5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,UAAU,IAAI,MAAM,IAAI,SAAS;AACrE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACvE,kBAAQ,QAAQ,UAAU,OAAO,GAAG,EAAE,EAAG,IAAI,MAAM,EAAE,MAAM,IAAI;AAC/D;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAsC;AACzC,QAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AACvB,YAAM,YAAY,KAAK,SAAS;AAEhC,YAAMC,QAAoB;AAAA,QACxB,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,QACT,OAAO,YAAY,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;AAAA,QACxC,OAAO;AAAA,QACP,OAAO,CAAC;AAAA,MACV;AAEA,aAAO,YAAY,aAAa,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI;AAE5D,UAAI,KAAK,QAAQ,UAAU;AACzB,eAAO,YAAY,OAAO;AAAA,MAC5B;AAGA,YAAM,YAAY,KAAK,MAAM,MAAM,cAAc,IAAI;AACrD,UAAI,oBAAoB;AAExB,aAAO,KAAK;AACV,YAAI,WAAW;AACf,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,YAAI,EAAE,MAAM,UAAU,KAAK,GAAG,IAAI;AAChC;AAAA,QACF;AAEA,YAAI,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;AACjC;AAAA,QACF;AAEA,cAAM,IAAI,CAAC;AACX,cAAM,IAAI,UAAU,IAAI,MAAM;AAE9B,YAAI,OAAO,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,CAAC,MAAc,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC;AACrH,YAAI,WAAW,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACnC,YAAI,YAAY,CAAC,KAAK,KAAK;AAE3B,YAAI,SAAS;AACb,YAAI,KAAK,QAAQ,UAAU;AACzB,mBAAS;AACT,yBAAe,KAAK,UAAU;AAAA,QAChC,WAAW,WAAW;AACpB,mBAAS,IAAI,CAAC,EAAE,SAAS;AAAA,QAC3B,OAAO;AACL,mBAAS,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY;AACpD,mBAAS,SAAS,IAAI,IAAI;AAC1B,yBAAe,KAAK,MAAM,MAAM;AAChC,oBAAU,IAAI,CAAC,EAAE;AAAA,QACnB;AAEA,YAAI,aAAa,KAAK,MAAM,MAAM,UAAU,KAAK,QAAQ,GAAG;AAC1D,iBAAO,WAAW;AAClB,gBAAM,IAAI,UAAU,SAAS,SAAS,CAAC;AACvC,qBAAW;AAAA,QACb;AAEA,YAAI,CAAC,UAAU;AACb,gBAAM,kBAAkB,KAAK,MAAM,MAAM,gBAAgB,MAAM;AAC/D,gBAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/C,gBAAM,mBAAmB,KAAK,MAAM,MAAM,iBAAiB,MAAM;AACjE,gBAAM,oBAAoB,KAAK,MAAM,MAAM,kBAAkB,MAAM;AACnE,gBAAM,iBAAiB,KAAK,MAAM,MAAM,eAAe,MAAM;AAG7D,iBAAO,KAAK;AACV,kBAAM,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACpC,gBAAI;AACJ,uBAAW;AAGX,gBAAI,KAAK,QAAQ,UAAU;AACzB,yBAAW,SAAS,QAAQ,KAAK,MAAM,MAAM,oBAAoB,IAAI;AACrE,oCAAsB;AAAA,YACxB,OAAO;AACL,oCAAsB,SAAS,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM;AAAA,YAC/E;AAGA,gBAAI,iBAAiB,KAAK,QAAQ,GAAG;AACnC;AAAA,YACF;AAGA,gBAAI,kBAAkB,KAAK,QAAQ,GAAG;AACpC;AAAA,YACF;AAGA,gBAAI,eAAe,KAAK,QAAQ,GAAG;AACjC;AAAA,YACF;AAGA,gBAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC;AAAA,YACF;AAGA,gBAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B;AAAA,YACF;AAEA,gBAAI,oBAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,UAAU,CAAC,SAAS,KAAK,GAAG;AAC3F,8BAAgB,OAAO,oBAAoB,MAAM,MAAM;AAAA,YACzD,OAAO;AAEL,kBAAI,WAAW;AACb;AAAA,cACF;AAGA,kBAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AACnG;AAAA,cACF;AACA,kBAAI,iBAAiB,KAAK,IAAI,GAAG;AAC/B;AAAA,cACF;AACA,kBAAI,kBAAkB,KAAK,IAAI,GAAG;AAChC;AAAA,cACF;AACA,kBAAI,QAAQ,KAAK,IAAI,GAAG;AACtB;AAAA,cACF;AAEA,8BAAgB,OAAO;AAAA,YACzB;AAEA,gBAAI,CAAC,aAAa,CAAC,SAAS,KAAK,GAAG;AAClC,0BAAY;AAAA,YACd;AAEA,mBAAO,UAAU;AACjB,kBAAM,IAAI,UAAU,QAAQ,SAAS,CAAC;AACtC,mBAAO,oBAAoB,MAAM,MAAM;AAAA,UACzC;AAAA,QACF;AAEA,YAAI,CAACA,MAAK,OAAO;AAEf,cAAI,mBAAmB;AACrB,YAAAA,MAAK,QAAQ;AAAA,UACf,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,GAAG,GAAG;AACrD,gCAAoB;AAAA,UACtB;AAAA,QACF;AAEA,YAAI,SAAiC;AACrC,YAAI;AAEJ,YAAI,KAAK,QAAQ,KAAK;AACpB,mBAAS,KAAK,MAAM,MAAM,WAAW,KAAK,YAAY;AACtD,cAAI,QAAQ;AACV,wBAAY,OAAO,CAAC,MAAM;AAC1B,2BAAe,aAAa,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE;AAAA,UAC1E;AAAA,QACF;AAEA,QAAAA,MAAK,MAAM,KAAK;AAAA,UACd,MAAM;AAAA,UACN;AAAA,UACA,MAAM,CAAC,CAAC;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,CAAC;AAAA,QACX,CAAC;AAED,QAAAA,MAAK,OAAO;AAAA,MACd;AAGA,YAAM,WAAWA,MAAK,MAAM,GAAG,EAAE;AACjC,UAAI,UAAU;AACZ,iBAAS,MAAM,SAAS,IAAI,QAAQ;AACpC,iBAAS,OAAO,SAAS,KAAK,QAAQ;AAAA,MACxC,OAAO;AAEL;AAAA,MACF;AACA,MAAAA,MAAK,MAAMA,MAAK,IAAI,QAAQ;AAG5B,eAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AAC1C,aAAK,MAAM,MAAM,MAAM;AACvB,QAAAA,MAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,YAAYA,MAAK,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AAEpE,YAAI,CAACA,MAAK,OAAO;AAEf,gBAAM,UAAUA,MAAK,MAAM,CAAC,EAAE,OAAO,OAAO,OAAK,EAAE,SAAS,OAAO;AACnE,gBAAM,wBAAwB,QAAQ,SAAS,KAAK,QAAQ,KAAK,OAAK,KAAK,MAAM,MAAM,QAAQ,KAAK,EAAE,GAAG,CAAC;AAE1G,UAAAA,MAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAGA,UAAIA,MAAK,OAAO;AACd,iBAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AAC1C,UAAAA,MAAK,MAAM,CAAC,EAAE,QAAQ;AAAA,QACxB;AAAA,MACF;AAEA,aAAOA;AAAA,IACT;AAAA,EACF;AAAA,EAEA,KAAK,KAAsC;AACzC,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,QAAqB;AAAA,QACzB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK,IAAI,CAAC;AAAA,QACV,KAAK,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM;AAAA,QAC3D,MAAM,IAAI,CAAC;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,KAAqC;AACvC,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,YAAMC,OAAM,IAAI,CAAC,EAAE,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AAClF,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,cAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAC5H,YAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AACrH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAAA;AAAA,QACA,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,KAAuC;AAC3C,UAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC3C,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,MAAM,MAAM,eAAe,KAAK,IAAI,CAAC,CAAC,GAAG;AAEjD;AAAA,IACF;AAEA,UAAM,UAAU,WAAW,IAAI,CAAC,CAAC;AACjC,UAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,EAAE,MAAM,GAAG;AAC7E,UAAM,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC;AAEpG,UAAM,OAAqB;AAAA,MACzB,MAAM;AAAA,MACN,KAAK,IAAI,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,OAAO,CAAC;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAEA,QAAI,QAAQ,WAAW,OAAO,QAAQ;AAEpC;AAAA,IACF;AAEA,eAAW,SAAS,QAAQ;AAC1B,UAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,KAAK,GAAG;AAChD,aAAK,MAAM,KAAK,OAAO;AAAA,MACzB,WAAW,KAAK,MAAM,MAAM,iBAAiB,KAAK,KAAK,GAAG;AACxD,aAAK,MAAM,KAAK,QAAQ;AAAA,MAC1B,WAAW,KAAK,MAAM,MAAM,eAAe,KAAK,KAAK,GAAG;AACtD,aAAK,MAAM,KAAK,MAAM;AAAA,MACxB,OAAO;AACL,aAAK,MAAM,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AAEA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,WAAK,OAAO,KAAK;AAAA,QACf,MAAM,QAAQ,CAAC;AAAA,QACf,QAAQ,KAAK,MAAM,OAAO,QAAQ,CAAC,CAAC;AAAA,QACpC,QAAQ;AAAA,QACR,OAAO,KAAK,MAAM,CAAC;AAAA,MACrB,CAAC;AAAA,IACH;AAEA,eAAW,OAAO,MAAM;AACtB,WAAK,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,MAAM,MAAM;AAClE,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,UAC9B,QAAQ;AAAA,UACR,OAAO,KAAK,MAAM,CAAC;AAAA,QACrB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,KAAyC;AAChD,UAAM,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AAC9C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI;AAAA,QACtC,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,KAA2C;AACnD,UAAM,MAAM,KAAK,MAAM,MAAM,UAAU,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,OAC9C,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAClB,IAAI,CAAC;AACT,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAsC;AACzC,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,KAAwC;AAC7C,UAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,KAAqC;AACvC,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,UAAI,CAAC,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,UAAU,KAAK,IAAI,CAAC,CAAC,GAAG;AACvE,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B,WAAW,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAG;AAC3E,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B;AACA,UAAI,CAAC,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,GAAG;AACnF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC,WAAW,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAC,GAAG;AACvF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,QAAQ,KAAK,MAAM,MAAM;AAAA,QACzB,YAAY,KAAK,MAAM,MAAM;AAAA,QAC7B,OAAO;AAAA,QACP,MAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAqD;AACxD,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,aAAa,IAAI,CAAC,EAAE,KAAK;AAC/B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,MAAM,MAAM,kBAAkB,KAAK,UAAU,GAAG;AAEjF,YAAI,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAI;AACxD;AAAA,QACF;AAGA,cAAM,aAAa,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAK,WAAW,SAAS,WAAW,UAAU,MAAM,GAAG;AACrD;AAAA,QACF;AAAA,MACF,OAAO;AAEL,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,IAAI;AACtD,YAAI,mBAAmB,IAAI;AAEzB;AAAA,QACF;AAEA,YAAI,iBAAiB,IAAI;AACvB,gBAAM,QAAQ,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI;AAC9C,gBAAM,UAAU,QAAQ,IAAI,CAAC,EAAE,SAAS;AACxC,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAC3C,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,KAAK;AAC3C,cAAI,CAAC,IAAI;AAAA,QACX;AAAA,MACF;AACA,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AAEzB,cAAMJ,QAAO,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI;AAEzD,YAAIA,OAAM;AACR,iBAAOA,MAAK,CAAC;AACb,kBAAQA,MAAK,CAAC;AAAA,QAChB;AAAA,MACF,OAAO;AACL,gBAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,MACzC;AAEA,aAAO,KAAK,KAAK;AACjB,UAAI,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,GAAG;AACjD,YAAI,KAAK,QAAQ,YAAY,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAI;AAEjF,iBAAO,KAAK,MAAM,CAAC;AAAA,QACrB,OAAO;AACL,iBAAO,KAAK,MAAM,GAAG,EAAE;AAAA,QACzB;AAAA,MACF;AACA,aAAO,WAAW,KAAK;AAAA,QACrB,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAAA,QACpE,OAAO,QAAQ,MAAM,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAAA,MACzE,GAAG,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EAEA,QAAQ,KAAa,OAAoE;AACvF,QAAI;AACJ,SAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,GAAG,OACvC,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG,IAAI;AAC/C,YAAM,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AACvF,YAAMA,QAAO,MAAM,WAAW,YAAY,CAAC;AAC3C,UAAI,CAACA,OAAM;AACT,cAAM,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,aAAO,WAAW,KAAKA,OAAM,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,SAAS,KAAa,WAAmB,WAAW,IAA2C;AAC7F,QAAI,QAAQ,KAAK,MAAM,OAAO,eAAe,KAAK,GAAG;AACrD,QAAI,CAAC,MAAO;AAGZ,QAAI,MAAM,CAAC,KAAK,SAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB,EAAG;AAEtE,UAAM,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AAEzC,QAAI,CAAC,YAAY,CAAC,YAAY,KAAK,MAAM,OAAO,YAAY,KAAK,QAAQ,GAAG;AAE1E,YAAM,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS;AACvC,UAAI,QAAQ,SAAS,aAAa,SAAS,gBAAgB;AAE3D,YAAM,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,oBAAoB,KAAK,MAAM,OAAO;AAC7F,aAAO,YAAY;AAGnB,kBAAY,UAAU,MAAM,KAAK,IAAI,SAAS,OAAO;AAErD,cAAQ,QAAQ,OAAO,KAAK,SAAS,MAAM,MAAM;AAC/C,iBAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC;AAE5E,YAAI,CAAC,OAAQ;AAEb,kBAAU,CAAC,GAAG,MAAM,EAAE;AAEtB,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACxB,wBAAc;AACd;AAAA,QACF,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC/B,cAAI,UAAU,KAAK,GAAG,UAAU,WAAW,IAAI;AAC7C,6BAAiB;AACjB;AAAA,UACF;AAAA,QACF;AAEA,sBAAc;AAEd,YAAI,aAAa,EAAG;AAGpB,kBAAU,KAAK,IAAI,SAAS,UAAU,aAAa,aAAa;AAEhE,cAAM,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;AACxC,cAAM,MAAM,IAAI,MAAM,GAAG,UAAU,MAAM,QAAQ,iBAAiB,OAAO;AAGzE,YAAI,KAAK,IAAI,SAAS,OAAO,IAAI,GAAG;AAClC,gBAAMK,QAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA,MAAAA;AAAA,YACA,QAAQ,KAAK,MAAM,aAAaA,KAAI;AAAA,UACtC;AAAA,QACF;AAGA,cAAM,OAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS,KAA0C;AACjD,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,GAAG;AACjE,YAAM,mBAAmB,KAAK,MAAM,MAAM,aAAa,KAAK,IAAI;AAChE,YAAM,0BAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,KAAK,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI;AAC3H,UAAI,oBAAoB,yBAAyB;AAC/C,eAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,GAAG,KAAoC;AACrC,UAAM,MAAM,KAAK,MAAM,OAAO,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,KAAqC;AACvC,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS,KAAsC;AAC7C,UAAM,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;AAAA,MACrB,OAAO;AACL,eAAO,IAAI,CAAC;AACZ,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,KAAsC;AACxC,QAAI;AACJ,QAAI,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG;AACzC,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;AAAA,MACrB,OAAO;AAEL,YAAI;AACJ,WAAG;AACD,wBAAc,IAAI,CAAC;AACnB,cAAI,CAAC,IAAI,KAAK,MAAM,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;AAAA,QAC7D,SAAS,gBAAgB,IAAI,CAAC;AAC9B,eAAO,IAAI,CAAC;AACZ,YAAI,IAAI,CAAC,MAAM,QAAQ;AACrB,iBAAO,YAAY,IAAI,CAAC;AAAA,QAC1B,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,KAAsC;AAC/C,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,UAAU,KAAK,MAAM,MAAM;AACjC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACn2BO,IAAM,SAAN,MAAM,QAAO;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EAMQ;AAAA,EACA;AAAA,EAER,YAAYC,UAAyB;AAEnC,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,QAAQ,uBAAO,OAAO,IAAI;AACtC,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAI,WAAW;AAClE,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAc,CAAC;AACpB,SAAK,QAAQ;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,KAAK;AAAA,IACP;AAEA,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA,OAAO,MAAM;AAAA,MACb,QAAQ,OAAO;AAAA,IACjB;AAEA,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS,OAAO;AAAA,IACxB,WAAW,KAAK,QAAQ,KAAK;AAC3B,YAAM,QAAQ,MAAM;AACpB,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,SAAS,OAAO;AAAA,MACxB,OAAO;AACL,cAAM,SAAS,OAAO;AAAA,MACxB;AAAA,IACF;AACA,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,QAAQ;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,IAAI,KAAaA,UAAyB;AAC/C,UAAMC,SAAQ,IAAI,QAAOD,QAAO;AAChC,WAAOC,OAAM,IAAI,GAAG;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU,KAAaD,UAAyB;AACrD,UAAMC,SAAQ,IAAI,QAAOD,QAAO;AAChC,WAAOC,OAAM,aAAa,GAAG;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAa;AACf,UAAM,IAAI,QAAQ,MAAM,gBAAgB,IAAI;AAE5C,SAAK,YAAY,KAAK,KAAK,MAAM;AAEjC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,YAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,WAAK,aAAa,KAAK,KAAK,KAAK,MAAM;AAAA,IACzC;AACA,SAAK,cAAc,CAAC;AAEpB,WAAO,KAAK;AAAA,EACd;AAAA,EAOA,YAAY,KAAa,SAAkB,CAAC,GAAG,uBAAuB,OAAO;AAC3E,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,IAAI,QAAQ,MAAM,eAAe,MAAM,EAAE,QAAQ,MAAM,WAAW,EAAE;AAAA,IAC5E;AAEA,WAAO,KAAK;AACV,UAAI;AAEJ,UAAI,KAAK,QAAQ,YAAY,OAAO,KAAK,CAAC,iBAAiB;AACzD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AAC3D,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACF;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,IAAI,WAAW,KAAK,cAAc,QAAW;AAGrD,oBAAU,OAAO;AAAA,QACnB,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAE9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AACjE,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAG,MAAM,UAAU;AAAA,QAC3C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,QAAQ,GAAG,GAAG;AACvC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,WAAW,GAAG,GAAG;AAC1C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AACjE,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAG,MAAM,UAAU;AAAA,QAC3C,WAAW,CAAC,KAAK,OAAO,MAAM,MAAM,GAAG,GAAG;AACxC,eAAK,OAAO,MAAM,MAAM,GAAG,IAAI;AAAA,YAC7B,MAAM,MAAM;AAAA,YACZ,OAAO,MAAM;AAAA,UACf;AAAA,QACF;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAIA,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,YAAY;AACvC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,WAAW,QAAQ,CAAC,kBAAkB;AAC5D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACnD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC7C;AAAA,QACF,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,UAAU,UAAU,MAAM,IAAI;AAChE,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,wBAAwB,WAAW,SAAS,aAAa;AAC3D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,GAAG,EAAE,EAAG,MAAM,UAAU;AAAA,QAC3C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA,+BAAuB,OAAO,WAAW,IAAI;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,GAAG,EAAE,EAAG,MAAM,UAAU;AAAA,QAC3C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAEA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAEA,SAAK,MAAM,MAAM;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,KAAa,SAAkB,CAAC,GAAG;AACxC,SAAK,YAAY,KAAK,EAAE,KAAK,OAAO,CAAC;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,KAAa,SAAkB,CAAC,GAAY;AAEvD,QAAI,YAAY;AAChB,QAAI,QAAgC;AAGpC,QAAI,KAAK,OAAO,OAAO;AACrB,YAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK,SAAS,MAAM,MAAM;AAClF,cAAI,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG;AACrE,wBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IACtC,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MACxC,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;AAAA,UACzE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK,SAAS,MAAM,MAAM;AACnF,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS;AAAA,IAC3H;AAGA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK,SAAS,MAAM,MAAM;AAC9E,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;AAAA,IAC7J;AAEA,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,WAAO,KAAK;AACV,UAAI,CAAC,cAAc;AACjB,mBAAW;AAAA,MACb;AACA,qBAAe;AAEf,UAAI;AAGJ,UAAI,KAAK,QAAQ,YAAY,QAAQ,KAAK,CAAC,iBAAiB;AAC1D,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AAC3D,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACF;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AAC1D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,SAAS,UAAU,WAAW,SAAS,QAAQ;AACvD,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,WAAW,QAAQ,GAAG;AAC7D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,MAAM,WAAW,QAAQ,KAAK,UAAU,IAAI,GAAG,IAAI;AAC3D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAIA,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,aAAa;AACxC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,YAAY,QAAQ,CAAC,kBAAkB;AAC7D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACnD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC7C;AAAA,QACF,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,WAAW,MAAM,GAAG;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,MAAM,EAAE,MAAM,KAAK;AAC/B,qBAAW,MAAM,IAAI,MAAM,EAAE;AAAA,QAC/B;AACA,uBAAe;AACf,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAEA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;;;ACxcO,IAAM,YAAN,MAAgB;AAAA,EACrB;AAAA,EACA;AAAA;AAAA,EACA,YAAYC,UAAyB;AACnC,SAAK,UAAUA,YAAW;AAAA,EAC5B;AAAA,EAEA,MAAM,OAA6B;AACjC,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,EAAE,MAAM,MAAM,QAAQ,GAAwB;AACjD,UAAM,cAAc,QAAQ,IAAI,MAAM,MAAM,aAAa,IAAI,CAAC;AAE9D,UAAM,OAAO,KAAK,QAAQ,MAAM,eAAe,EAAE,IAAI;AAErD,QAAI,CAAC,YAAY;AACf,aAAO,iBACF,UAAU,OAAOC,QAAO,MAAM,IAAI,KACnC;AAAA,IACN;AAEA,WAAO,gCACHA,QAAO,UAAU,IACjB,QACC,UAAU,OAAOA,QAAO,MAAM,IAAI,KACnC;AAAA,EACN;AAAA,EAEA,WAAW,EAAE,OAAO,GAA8B;AAChD,UAAM,OAAO,KAAK,OAAO,MAAM,MAAM;AACrC,WAAO;AAAA,EAAiB,IAAI;AAAA;AAAA,EAC9B;AAAA,EAEA,KAAK,EAAE,KAAK,GAAsC;AAChD,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,EAAE,QAAQ,MAAM,GAA2B;AACjD,WAAO,KAAK,KAAK,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,MAAM,KAAK;AAAA;AAAA,EACjE;AAAA,EAEA,GAAG,OAA0B;AAC3B,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,OAA4B;AAC/B,UAAM,UAAU,MAAM;AACtB,UAAM,QAAQ,MAAM;AAEpB,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,YAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,cAAQ,KAAK,SAAS,IAAI;AAAA,IAC5B;AAEA,UAAM,OAAO,UAAU,OAAO;AAC9B,UAAM,YAAa,WAAW,UAAU,IAAM,aAAa,QAAQ,MAAO;AAC1E,WAAO,MAAM,OAAO,YAAY,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC/D;AAAA,EAEA,SAAS,MAA+B;AACtC,QAAI,WAAW;AACf,QAAI,KAAK,MAAM;AACb,YAAM,WAAW,KAAK,SAAS,EAAE,SAAS,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1D,UAAI,KAAK,OAAO;AACd,YAAI,KAAK,OAAO,CAAC,GAAG,SAAS,aAAa;AACxC,eAAK,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE;AACtD,cAAI,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,QAAQ;AACzG,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,MAAMA,QAAO,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI;AACrF,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU;AAAA,UACrC;AAAA,QACF,OAAO;AACL,eAAK,OAAO,QAAQ;AAAA,YAClB,MAAM;AAAA,YACN,KAAK,WAAW;AAAA,YAChB,MAAM,WAAW;AAAA,YACjB,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF;AAEA,gBAAY,KAAK,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,KAAK,KAAK;AAEvD,WAAO,OAAO,QAAQ;AAAA;AAAA,EACxB;AAAA,EAEA,SAAS,EAAE,QAAQ,GAA4B;AAC7C,WAAO,aACF,UAAU,gBAAgB,MAC3B;AAAA,EACN;AAAA,EAEA,UAAU,EAAE,OAAO,GAA6B;AAC9C,WAAO,MAAM,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA;AAAA,EAC9C;AAAA,EAEA,MAAM,OAA6B;AACjC,QAAI,SAAS;AAGb,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC5C,cAAQ,KAAK,UAAU,MAAM,OAAO,CAAC,CAAC;AAAA,IACxC;AACA,cAAU,KAAK,SAAS,EAAE,MAAM,KAAK,CAAC;AAEtC,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AAC1C,YAAM,MAAM,MAAM,KAAK,CAAC;AAExB,aAAO;AACP,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAQ,KAAK,UAAU,IAAI,CAAC,CAAC;AAAA,MAC/B;AAEA,cAAQ,KAAK,SAAS,EAAE,MAAM,KAAK,CAAC;AAAA,IACtC;AACA,QAAI,KAAM,QAAO,UAAU,IAAI;AAE/B,WAAO,uBAEH,SACA,eACA,OACA;AAAA,EACN;AAAA,EAEA,SAAS,EAAE,KAAK,GAA4B;AAC1C,WAAO;AAAA,EAAS,IAAI;AAAA;AAAA,EACtB;AAAA,EAEA,UAAU,OAAiC;AACzC,UAAM,UAAU,KAAK,OAAO,YAAY,MAAM,MAAM;AACpD,UAAM,OAAO,MAAM,SAAS,OAAO;AACnC,UAAMC,OAAM,MAAM,QACd,IAAI,IAAI,WAAW,MAAM,KAAK,OAC9B,IAAI,IAAI;AACZ,WAAOA,OAAM,UAAU,KAAK,IAAI;AAAA;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,EAAE,OAAO,GAA0B;AACxC,WAAO,WAAW,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EACnD;AAAA,EAEA,GAAG,EAAE,OAAO,GAAsB;AAChC,WAAO,OAAO,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EAC/C;AAAA,EAEA,SAAS,EAAE,KAAK,GAA4B;AAC1C,WAAO,SAASD,QAAO,MAAM,IAAI,CAAC;AAAA,EACpC;AAAA,EAEA,GAAG,OAA0B;AAC3B,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,EAAE,OAAO,GAAuB;AAClC,WAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EAChD;AAAA,EAEA,KAAK,EAAE,MAAM,OAAO,OAAO,GAAwB;AACjD,UAAM,OAAO,KAAK,OAAO,YAAY,MAAM;AAC3C,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AACA,WAAO;AACP,QAAI,MAAM,cAAc,OAAO;AAC/B,QAAI,OAAO;AACT,aAAO,aAAcA,QAAO,KAAK,IAAK;AAAA,IACxC;AACA,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,EAAE,MAAM,OAAO,MAAM,OAAO,GAAyB;AACzD,QAAI,QAAQ;AACV,aAAO,KAAK,OAAO,YAAY,QAAQ,KAAK,OAAO,YAAY;AAAA,IACjE;AACA,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACtB,aAAOA,QAAO,IAAI;AAAA,IACpB;AACA,WAAO;AAEP,QAAI,MAAM,aAAa,IAAI,UAAU,IAAI;AACzC,QAAI,OAAO;AACT,aAAO,WAAWA,QAAO,KAAK,CAAC;AAAA,IACjC;AACA,WAAO;AACP,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,OAA6C;AAChD,WAAO,YAAY,SAAS,MAAM,SAC9B,KAAK,OAAO,YAAY,MAAM,MAAM,IACnC,aAAa,SAAS,MAAM,UAAU,MAAM,OAAOA,QAAO,MAAM,IAAI;AAAA,EAC3E;AACF;;;ACpNO,IAAM,gBAAN,MAAoB;AAAA;AAAA,EAEzB,OAAO,EAAE,KAAK,GAAkB;AAC9B,WAAO;AAAA,EACT;AAAA,EAEA,GAAG,EAAE,KAAK,GAAc;AACtB,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,EAAE,KAAK,GAAoB;AAClC,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,EAAE,KAAK,GAAe;AACxB,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,EAAE,KAAK,GAA6B;AACvC,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,EAAE,KAAK,GAA6C;AACvD,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,EAAE,KAAK,GAAgB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,EAAE,KAAK,GAAiB;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,KAAK;AACH,WAAO;AAAA,EACT;AACF;;;AClCO,IAAM,UAAN,MAAM,SAAQ;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAYE,UAAyB;AACnC,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAI,UAAU;AAC/D,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,SAAS,SAAS;AACvB,SAAK,eAAe,IAAI,cAAc;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM,QAAiBA,UAAyB;AACrD,UAAMC,UAAS,IAAI,SAAQD,QAAO;AAClC,WAAOC,QAAO,MAAM,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,YAAY,QAAiBD,UAAyB;AAC3D,UAAMC,UAAS,IAAI,SAAQD,QAAO;AAClC,WAAOC,QAAO,YAAY,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAiB,MAAM,MAAc;AACzC,QAAI,MAAM;AAEV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,WAAW,OAAO,CAAC;AAGzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACvD,cAAM,eAAe;AACrB,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,aAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,YAAY;AACpG,YAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAAS,aAAa,IAAI,GAAG;AAChJ,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AAEA,YAAM,QAAQ;AAEd,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,SAAS;AACZ,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,KAAK,SAAS,GAAG,KAAK;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AACd,iBAAO,KAAK,SAAS,QAAQ,KAAK;AAClC;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;AAAA,QACF;AAAA,QACA,KAAK,cAAc;AACjB,iBAAO,KAAK,SAAS,WAAW,KAAK;AACrC;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,iBAAO,KAAK,SAAS,UAAU,KAAK;AACpC;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,YAAY;AAChB,cAAI,OAAO,KAAK,SAAS,KAAK,SAAS;AACvC,iBAAO,IAAI,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,EAAE,SAAS,QAAQ;AAC7D,wBAAY,OAAO,EAAE,CAAC;AACtB,oBAAQ,OAAO,KAAK,SAAS,KAAK,SAAS;AAAA,UAC7C;AACA,cAAI,KAAK;AACP,mBAAO,KAAK,SAAS,UAAU;AAAA,cAC7B,MAAM;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,YACjE,CAAC;AAAA,UACH,OAAO;AACL,mBAAO;AAAA,UACT;AACA;AAAA,QACF;AAAA,QAEA,SAAS;AACP,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,QAAiB,WAAsC,KAAK,UAAkB;AACxF,QAAI,MAAM;AAEV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,WAAW,OAAO,CAAC;AAGzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACvD,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,SAAS,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,QAAQ;AAC5F,YAAI,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAAS,SAAS,IAAI,GAAG;AAClI,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AAEA,YAAM,QAAQ;AAEd,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,UAAU;AACb,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,iBAAO,SAAS,MAAM,KAAK;AAC3B;AAAA,QACF;AAAA,QACA,KAAK,UAAU;AACb,iBAAO,SAAS,OAAO,KAAK;AAC5B;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,SAAS,GAAG,KAAK;AACxB;AAAA,QACF;AAAA,QACA,KAAK,YAAY;AACf,iBAAO,SAAS,SAAS,KAAK;AAC9B;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,SAAS,GAAG,KAAK;AACxB;AAAA,QACF;AAAA,QACA,KAAK,OAAO;AACV,iBAAO,SAAS,IAAI,KAAK;AACzB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;ACvMO,IAAM,SAAN,MAAa;AAAA,EAClB;AAAA,EACA;AAAA,EAEA,YAAYC,UAAyB;AACnC,SAAK,UAAUA,YAAW;AAAA,EAC5B;AAAA,EAEA,OAAO,mBAAmB,oBAAI,IAAI;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW,UAAkB;AAC3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYC,OAAc;AACxB,WAAOA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,QAA8B;AAC7C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EAC9C;AACF;;;ACtCO,IAAM,SAAN,MAAa;AAAA,EAClB,WAAW,aAAa;AAAA,EACxB,UAAU,KAAK;AAAA,EAEf,QAAQ,KAAK,cAAc,IAAI;AAAA,EAC/B,cAAc,KAAK,cAAc,KAAK;AAAA,EAEtC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EAER,eAAe,MAAyB;AACtC,SAAK,IAAI,GAAG,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,QAA8B,UAA2D;AAClG,QAAI,SAAyB,CAAC;AAC9B,eAAW,SAAS,QAAQ;AAC1B,eAAS,OAAO,OAAO,SAAS,KAAK,MAAM,KAAK,CAAC;AACjD,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,SAAS;AACZ,gBAAM,aAAa;AACnB,qBAAW,QAAQ,WAAW,QAAQ;AACpC,qBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,UAC/D;AACA,qBAAW,OAAO,WAAW,MAAM;AACjC,uBAAW,QAAQ,KAAK;AACtB,uBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/D;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,gBAAM,YAAY;AAClB,mBAAS,OAAO,OAAO,KAAK,WAAW,UAAU,OAAO,QAAQ,CAAC;AACjE;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,eAAe;AACrB,cAAI,KAAK,SAAS,YAAY,cAAc,aAAa,IAAI,GAAG;AAC9D,iBAAK,SAAS,WAAW,YAAY,aAAa,IAAI,EAAE,QAAQ,CAAC,gBAAgB;AAC/E,oBAAMC,UAAS,aAAa,WAAW,EAAE,KAAK,QAAQ;AACtD,uBAAS,OAAO,OAAO,KAAK,WAAWA,SAAQ,QAAQ,CAAC;AAAA,YAC1D,CAAC;AAAA,UACH,WAAW,aAAa,QAAQ;AAC9B,qBAAS,OAAO,OAAO,KAAK,WAAW,aAAa,QAAQ,QAAQ,CAAC;AAAA,UACvE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,MAAyB;AAC9B,UAAM,aAA0C,KAAK,SAAS,cAAc,EAAE,WAAW,CAAC,GAAG,aAAa,CAAC,EAAE;AAE7G,SAAK,QAAQ,CAAC,SAAS;AAErB,YAAM,OAAO,EAAE,GAAG,KAAK;AAGvB,WAAK,QAAQ,KAAK,SAAS,SAAS,KAAK,SAAS;AAGlD,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,QAAQ,CAAC,QAAQ;AAC/B,cAAI,CAAC,IAAI,MAAM;AACb,kBAAM,IAAI,MAAM,yBAAyB;AAAA,UAC3C;AACA,cAAI,cAAc,KAAK;AACrB,kBAAM,eAAe,WAAW,UAAU,IAAI,IAAI;AAClD,gBAAI,cAAc;AAEhB,yBAAW,UAAU,IAAI,IAAI,IAAI,YAAYC,OAAM;AACjD,oBAAI,MAAM,IAAI,SAAS,MAAM,MAAMA,KAAI;AACvC,oBAAI,QAAQ,OAAO;AACjB,wBAAM,aAAa,MAAM,MAAMA,KAAI;AAAA,gBACrC;AACA,uBAAO;AAAA,cACT;AAAA,YACF,OAAO;AACL,yBAAW,UAAU,IAAI,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACF;AACA,cAAI,eAAe,KAAK;AACtB,gBAAI,CAAC,IAAI,SAAU,IAAI,UAAU,WAAW,IAAI,UAAU,UAAW;AACnE,oBAAM,IAAI,MAAM,6CAA6C;AAAA,YAC/D;AACA,kBAAM,WAAW,WAAW,IAAI,KAAK;AACrC,gBAAI,UAAU;AACZ,uBAAS,QAAQ,IAAI,SAAS;AAAA,YAChC,OAAO;AACL,yBAAW,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS;AAAA,YACxC;AACA,gBAAI,IAAI,OAAO;AACb,kBAAI,IAAI,UAAU,SAAS;AACzB,oBAAI,WAAW,YAAY;AACzB,6BAAW,WAAW,KAAK,IAAI,KAAK;AAAA,gBACtC,OAAO;AACL,6BAAW,aAAa,CAAC,IAAI,KAAK;AAAA,gBACpC;AAAA,cACF,WAAW,IAAI,UAAU,UAAU;AACjC,oBAAI,WAAW,aAAa;AAC1B,6BAAW,YAAY,KAAK,IAAI,KAAK;AAAA,gBACvC,OAAO;AACL,6BAAW,cAAc,CAAC,IAAI,KAAK;AAAA,gBACrC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,iBAAiB,OAAO,IAAI,aAAa;AAC3C,uBAAW,YAAY,IAAI,IAAI,IAAI,IAAI;AAAA,UACzC;AAAA,QACF,CAAC;AACD,aAAK,aAAa;AAAA,MACpB;AAGA,UAAI,KAAK,UAAU;AACjB,cAAM,WAAW,KAAK,SAAS,YAAY,IAAI,UAAU,KAAK,QAAQ;AACtE,mBAAW,QAAQ,KAAK,UAAU;AAChC,cAAI,EAAE,QAAQ,WAAW;AACvB,kBAAM,IAAI,MAAM,aAAa,IAAI,kBAAkB;AAAA,UACrD;AACA,cAAI,CAAC,WAAW,QAAQ,EAAE,SAAS,IAAI,GAAG;AAExC;AAAA,UACF;AACA,gBAAM,eAAe;AACrB,gBAAM,eAAe,KAAK,SAAS,YAAY;AAC/C,gBAAM,eAAe,SAAS,YAAY;AAE1C,mBAAS,YAAY,IAAI,IAAIA,UAAoB;AAC/C,gBAAI,MAAM,aAAa,MAAM,UAAUA,KAAI;AAC3C,gBAAI,QAAQ,OAAO;AACjB,oBAAM,aAAa,MAAM,UAAUA,KAAI;AAAA,YACzC;AACA,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AACA,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,KAAK,WAAW;AAClB,cAAM,YAAY,KAAK,SAAS,aAAa,IAAI,WAAW,KAAK,QAAQ;AACzE,mBAAW,QAAQ,KAAK,WAAW;AACjC,cAAI,EAAE,QAAQ,YAAY;AACxB,kBAAM,IAAI,MAAM,cAAc,IAAI,kBAAkB;AAAA,UACtD;AACA,cAAI,CAAC,WAAW,SAAS,OAAO,EAAE,SAAS,IAAI,GAAG;AAEhD;AAAA,UACF;AACA,gBAAM,gBAAgB;AACtB,gBAAM,gBAAgB,KAAK,UAAU,aAAa;AAClD,gBAAM,gBAAgB,UAAU,aAAa;AAG7C,oBAAU,aAAa,IAAI,IAAIA,UAAoB;AACjD,gBAAI,MAAM,cAAc,MAAM,WAAWA,KAAI;AAC7C,gBAAI,QAAQ,OAAO;AACjB,oBAAM,cAAc,MAAM,WAAWA,KAAI;AAAA,YAC3C;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AACA,aAAK,YAAY;AAAA,MACnB;AAGA,UAAI,KAAK,OAAO;AACd,cAAM,QAAQ,KAAK,SAAS,SAAS,IAAI,OAAO;AAChD,mBAAW,QAAQ,KAAK,OAAO;AAC7B,cAAI,EAAE,QAAQ,QAAQ;AACpB,kBAAM,IAAI,MAAM,SAAS,IAAI,kBAAkB;AAAA,UACjD;AACA,cAAI,CAAC,WAAW,OAAO,EAAE,SAAS,IAAI,GAAG;AAEvC;AAAA,UACF;AACA,gBAAM,YAAY;AAClB,gBAAM,YAAY,KAAK,MAAM,SAAS;AACtC,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,OAAO,iBAAiB,IAAI,IAAI,GAAG;AAErC,kBAAM,SAAS,IAAI,CAAC,QAAiB;AACnC,kBAAI,KAAK,SAAS,OAAO;AACvB,uBAAO,QAAQ,QAAQ,UAAU,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,CAAAC,SAAO;AAC7D,yBAAO,SAAS,KAAK,OAAOA,IAAG;AAAA,gBACjC,CAAC;AAAA,cACH;AAEA,oBAAM,MAAM,UAAU,KAAK,OAAO,GAAG;AACrC,qBAAO,SAAS,KAAK,OAAO,GAAG;AAAA,YACjC;AAAA,UACF,OAAO;AAEL,kBAAM,SAAS,IAAI,IAAID,UAAoB;AACzC,kBAAI,MAAM,UAAU,MAAM,OAAOA,KAAI;AACrC,kBAAI,QAAQ,OAAO;AACjB,sBAAM,SAAS,MAAM,OAAOA,KAAI;AAAA,cAClC;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,aAAK,QAAQ;AAAA,MACf;AAGA,UAAI,KAAK,YAAY;AACnB,cAAME,cAAa,KAAK,SAAS;AACjC,cAAM,iBAAiB,KAAK;AAC5B,aAAK,aAAa,SAAS,OAAO;AAChC,cAAI,SAAyB,CAAC;AAC9B,iBAAO,KAAK,eAAe,KAAK,MAAM,KAAK,CAAC;AAC5C,cAAIA,aAAY;AACd,qBAAS,OAAO,OAAOA,YAAW,KAAK,MAAM,KAAK,CAAC;AAAA,UACrD;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,KAAK;AAAA,IAC9C,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,WAAW,KAAoB;AAC7B,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,IAAI;AAC3C,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,KAAaC,UAAyB;AAC1C,WAAO,OAAO,IAAI,KAAKA,YAAW,KAAK,QAAQ;AAAA,EACjD;AAAA,EAEA,OAAO,QAAiBA,UAAyB;AAC/C,WAAO,QAAQ,MAAM,QAAQA,YAAW,KAAK,QAAQ;AAAA,EACvD;AAAA,EAEQ,cAAc,WAAoB;AAQxC,UAAMC,SAAyB,CAAC,KAAaD,aAAwC;AACnF,YAAM,UAAU,EAAE,GAAGA,SAAQ;AAC7B,YAAM,MAAM,EAAE,GAAG,KAAK,UAAU,GAAG,QAAQ;AAE3C,YAAM,aAAa,KAAK,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK;AAGzD,UAAI,KAAK,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AAC3D,eAAO,WAAW,IAAI,MAAM,oIAAoI,CAAC;AAAA,MACnK;AAGA,UAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;AAC9C,eAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;AAAA,MAC/E;AACA,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,WAAW,IAAI,MAAM,0CACxB,OAAO,UAAU,SAAS,KAAK,GAAG,IAAI,mBAAmB,CAAC;AAAA,MAChE;AAEA,UAAI,IAAI,OAAO;AACb,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM,QAAQ;AAAA,MACpB;AAEA,YAAME,SAAQ,IAAI,QAAQ,IAAI,MAAM,aAAa,IAAK,YAAY,OAAO,MAAM,OAAO;AACtF,YAAMC,UAAS,IAAI,QAAQ,IAAI,MAAM,cAAc,IAAK,YAAY,QAAQ,QAAQ,QAAQ;AAE5F,UAAI,IAAI,OAAO;AACb,eAAO,QAAQ,QAAQ,IAAI,QAAQ,IAAI,MAAM,WAAW,GAAG,IAAI,GAAG,EAC/D,KAAK,CAAAC,SAAOF,OAAME,MAAK,GAAG,CAAC,EAC3B,KAAK,YAAU,IAAI,QAAQ,IAAI,MAAM,iBAAiB,MAAM,IAAI,MAAM,EACtE,KAAK,YAAU,IAAI,aAAa,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,MAAM,EAChH,KAAK,YAAUD,QAAO,QAAQ,GAAG,CAAC,EAClC,KAAK,CAAAE,UAAQ,IAAI,QAAQ,IAAI,MAAM,YAAYA,KAAI,IAAIA,KAAI,EAC3D,MAAM,UAAU;AAAA,MACrB;AAEA,UAAI;AACF,YAAI,IAAI,OAAO;AACb,gBAAM,IAAI,MAAM,WAAW,GAAG;AAAA,QAChC;AACA,YAAI,SAASH,OAAM,KAAK,GAAG;AAC3B,YAAI,IAAI,OAAO;AACb,mBAAS,IAAI,MAAM,iBAAiB,MAAM;AAAA,QAC5C;AACA,YAAI,IAAI,YAAY;AAClB,eAAK,WAAW,QAAQ,IAAI,UAAU;AAAA,QACxC;AACA,YAAIG,QAAOF,QAAO,QAAQ,GAAG;AAC7B,YAAI,IAAI,OAAO;AACb,UAAAE,QAAO,IAAI,MAAM,YAAYA,KAAI;AAAA,QACnC;AACA,eAAOA;AAAA,MACT,SAAS,GAAG;AACV,eAAO,WAAW,CAAU;AAAA,MAC9B;AAAA,IACF;AAEA,WAAOJ;AAAA,EACT;AAAA,EAEQ,QAAQ,QAAiB,OAAgB;AAC/C,WAAO,CAAC,MAAuC;AAC7C,QAAE,WAAW;AAEb,UAAI,QAAQ;AACV,cAAM,MAAM,mCACRK,QAAO,EAAE,UAAU,IAAI,IAAI,IAC3B;AACJ,YAAI,OAAO;AACT,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAEA,UAAI,OAAO;AACT,eAAO,QAAQ,OAAO,CAAC;AAAA,MACzB;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACF;;;AVjVA,IAAM,iBAAiB,IAAI,OAAO;AAqB3B,SAAS,OAAO,KAAa,KAAsD;AACxF,SAAO,eAAe,MAAM,KAAK,GAAG;AACtC;AAOA,OAAO,UACP,OAAO,aAAa,SAASC,UAAwB;AACnD,iBAAe,WAAWA,QAAO;AACjC,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACT;AAKA,OAAO,cAAc;AAErB,OAAO,WAAW;AAMlB,OAAO,MAAM,YAAY,MAAyB;AAChD,iBAAe,IAAI,GAAG,IAAI;AAC1B,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACT;AAMA,OAAO,aAAa,SAAS,QAA8B,UAA2D;AACpH,SAAO,eAAe,WAAW,QAAQ,QAAQ;AACnD;AASA,OAAO,cAAc,eAAe;AAKpC,OAAO,SAAS;AAChB,OAAO,SAAS,QAAQ;AACxB,OAAO,WAAW;AAClB,OAAO,eAAe;AACtB,OAAO,QAAQ;AACf,OAAO,QAAQ,OAAO;AACtB,OAAO,YAAY;AACnB,OAAO,QAAQ;AACf,OAAO,QAAQ;AAER,IAAM,UAAU,OAAO;AACvB,IAAM,aAAa,OAAO;AAC1B,IAAM,MAAM,OAAO;AACnB,IAAM,aAAa,OAAO;AAC1B,IAAM,cAAc,OAAO;AAC3B,IAAM,QAAQ;AACd,IAAM,SAAS,QAAQ;AACvB,IAAM,QAAQ,OAAO;", "names": ["escape", "html", "link", "lexer", "options", "list", "tag", "text", "options", "lexer", "options", "escape", "tag", "options", "parser", "options", "html", "tokens", "args", "ret", "walkTokens", "options", "parse", "lexer", "parser", "src", "html", "escape", "options"]}