{"version": 3, "file": "document-validator.js", "sourceRoot": "", "sources": ["../../src/validation/document-validator.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAUhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AACpF,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACpF,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAgC1D,MAAM,OAAO,wBAAwB;IAKjC,YAAY,QAA6B;QACrC,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,UAAU,CAAC,kBAAkB,CAAC;QACjE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAyB,EAAE,UAA6B,EAAE,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QACnH,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,MAAM,WAAW,GAAiB,EAAE,CAAC;QAErC,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAErC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAC5D,IAAI,OAAO,CAAC,qBAAqB,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,CAAA,MAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,MAAK,iBAAiB,CAAC,WAAW,CAAA,EAAA,CAAC,EAAE,CAAC;gBACzG,OAAO,WAAW,CAAC;YACvB,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAC7D,IAAI,OAAO,CAAC,sBAAsB,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,CAAA,MAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,MAAK,iBAAiB,CAAC,YAAY,CAAA,EAAA,CAAC,EAAE,CAAC;gBAC3G,OAAO,WAAW,CAAC;YACvB,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAC1D,IAAI,OAAO,CAAC,sBAAsB,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,CAAA,MAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,MAAK,iBAAiB,CAAC,YAAY,CAAA,EAAA,CAAC,EAAE,CAAC;gBAC3G,OAAO,WAAW,CAAC;YACvB,CAAC;QACL,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC;YACD,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,GAAG,CAAC;YACd,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAErC,OAAO,WAAW,CAAC;IACvB,CAAC;IAES,mBAAmB,CAAC,WAAwB,EAAE,WAAyB,EAAE,QAA2B;;QAC1G,MAAM,gBAAgB,GAAG,CAAC,GAAG,WAAW,CAAC,WAAW,EAAE,GAAG,MAAA,MAAA,WAAW,CAAC,WAAW,0CAAE,WAAW,mCAAI,EAAE,CAAuB,CAAC;QAC3H,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAA,eAAe,CAAC,QAAQ,mCAAI,OAAO,CAAC;YACrD,MAAM,UAAU,GAAe;gBAC3B,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,CAAC;gBACxC,KAAK,EAAE;oBACH,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe,CAAC,IAAK,GAAG,CAAC;wBAC/B,SAAS,EAAE,eAAe,CAAC,MAAO,GAAG,CAAC;qBACzC;oBACD,GAAG,EAAE;wBACD,IAAI,EAAE,eAAe,CAAC,IAAK,GAAG,CAAC;wBAC/B,SAAS,EAAE,eAAe,CAAC,MAAO,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC;qBAClE;iBACJ;gBACD,OAAO,EAAE,eAAe,CAAC,OAAO;gBAChC,IAAI,EAAE,gBAAgB,CAAC,QAAQ,CAAC;gBAChC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;aAC3B,CAAC;YACF,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAES,oBAAoB,CAAC,WAAwB,EAAE,WAAyB,EAAE,QAA2B;QAC3G,KAAK,MAAM,WAAW,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;YACjD,IAAI,KAAK,GAAsB,SAAS,CAAC;YACzC,qDAAqD;YACrD,0EAA0E;YAC1E,kDAAkD;YAClD,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;gBACvC,4DAA4D;gBAC5D,oDAAoD;gBACpD,IAAI,eAAe,IAAI,WAAW,EAAE,CAAC;oBACjC,MAAM,KAAK,GAAI,WAAwC,CAAC,aAAa,CAAC;oBACtE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC5B,MAAM,QAAQ,GAAa,EAAE,IAAI,EAAE,KAAK,CAAC,OAAQ,GAAG,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,SAAU,EAAE,CAAC;wBACrF,KAAK,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAC,CAAC;oBAC9C,CAAC;yBAAM,CAAC;wBACJ,iFAAiF;wBACjF,0BAA0B;wBAC1B,MAAM,QAAQ,GAAa,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;wBACrD,KAAK,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAC,CAAC;oBAC9C,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC;YACD,IAAI,KAAK,EAAE,CAAC;gBACR,MAAM,UAAU,GAAe;oBAC3B,QAAQ,EAAE,oBAAoB,CAAC,OAAO,CAAC;oBACvC,KAAK;oBACL,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,YAAY,CAAC;oBACpD,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;iBAC3B,CAAC;gBACF,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;IACL,CAAC;IAES,oBAAoB,CAAC,QAAyB,EAAE,WAAyB,EAAE,QAA2B;QAC5G,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC1C,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;YACrC,IAAI,YAAY,EAAE,CAAC;gBACf,MAAM,IAAI,GAAoC;oBAC1C,IAAI,EAAE,YAAY,CAAC,SAAS;oBAC5B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,KAAK,EAAE,YAAY,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,iBAAiB,CAAC,YAAY;wBACpC,aAAa,EAAE,YAAY,CAAC,SAAS,CAAC,KAAK;wBAC3C,QAAQ,EAAE,YAAY,CAAC,QAAQ;wBAC/B,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,QAAQ;qBAChB;iBAC/B,CAAC;gBACF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,WAAW,CAAC,QAAiB,EAAE,OAA0B,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAC3G,MAAM,eAAe,GAAiB,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAuB,CAAoB,QAA4B,EAAE,OAAe,EAAE,IAAuB,EAAE,EAAE;YAC/H,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC;QAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACvE,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACtE,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QAEtE,OAAO,eAAe,CAAC;IAC3B,CAAC;IAES,KAAK,CAAC,iBAAiB,CAAC,QAAiB,EAAE,OAA0B,EAAE,QAA4B,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;;QAC/I,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAC1D,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACrC,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,EAAE,EAAE,WAAW,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,QAAiB,EAAE,OAA0B,EAAE,QAA4B,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAC9I,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAK,EAAC,IAAI,EAAC,EAAE;YACnD,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;YACjF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACzB,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,QAAiB,EAAE,OAA0B,EAAE,QAA4B,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;;QAC9I,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC;QACxD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,EAAE,EAAE,WAAW,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAES,YAAY,CAAoB,QAA4B,EAAE,OAAe,EAAE,IAA+B;QACpH,OAAO;YACH,OAAO;YACP,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC;YAC/B,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,CAAC;YACxC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;SAC3B,CAAC;IACN,CAAC;IAES,SAAS;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;IACpC,CAAC;CACJ;AAED,MAAM,UAAU,kBAAkB,CAAoB,IAA+B;IACjF,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IACD,IAAI,OAA4B,CAAC;IACjC,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACpC,OAAO,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACjF,CAAC;SAAM,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC1C,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/E,CAAC;IACD,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,GAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAC;IAC/B,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO;YACH,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;YAChC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;SACjC,CAAC;IACN,CAAC;IACD,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,oBAAoB,CAAC,QAAkC;IACnE,QAAQ,QAAQ,EAAE,CAAC;QACf,KAAK,OAAO;YACR,OAAO,CAA2C,CAAC;QACvD,KAAK,SAAS;YACV,OAAO,CAA6C,CAAC;QACzD,KAAK,MAAM;YACP,OAAO,CAAiD,CAAC;QAC7D,KAAK,MAAM;YACP,OAAO,CAA0C,CAAC;QACtD;YACI,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,QAAQ,CAAC,CAAC;IACpE,CAAC;AACL,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,QAAkC;IAC/D,QAAQ,QAAQ,EAAE,CAAC;QACf,KAAK,OAAO;YACR,OAAO,cAAc,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACzD,KAAK,SAAS;YACV,OAAO,cAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAC3D,KAAK,MAAM;YACP,OAAO,cAAc,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACxD,KAAK,MAAM;YACP,OAAO,cAAc,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACxD;YACI,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,QAAQ,CAAC,CAAC;IACpE,CAAC;AACL,CAAC;AAED,MAAM,KAAW,iBAAiB,CAOjC;AAPD,WAAiB,iBAAiB;IACjB,6BAAW,GAAG,cAAc,CAAC;IAC7B,+BAAa,GAAG,gBAAgB,CAAC;IACjC,4BAAU,GAAG,aAAa,CAAC;IAC3B,4BAAU,GAAG,aAAa,CAAC;IAC3B,8BAAY,GAAG,eAAe,CAAC;IAC/B,8BAAY,GAAG,eAAe,CAAC;AAChD,CAAC,EAPgB,iBAAiB,KAAjB,iBAAiB,QAOjC"}