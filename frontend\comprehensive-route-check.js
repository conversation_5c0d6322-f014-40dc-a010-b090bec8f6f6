/**
 * iFlytek 多模态智能面试系统 - 全面路由和组件检查工具
 * 系统性检查所有404问题并提供修复方案
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 iFlytek 多模态智能面试系统 - 全面路由检查');
console.log('='.repeat(60));

// 检查结果收集器
const checkResults = {
    navigation_links: [],
    route_definitions: [],
    component_files: [],
    empty_components: [],
    missing_components: [],
    route_conflicts: [],
    recommendations: []
};

// 1. 收集所有导航链接
function collectNavigationLinks() {
    console.log('\n📋 1. 收集导航链接...');
    
    const navigationFiles = [
        'src/App.vue',
        'src/views/NewHomePage.vue',
        'src/views/CleanHomePage.vue'
    ];
    
    const foundLinks = new Set();
    
    navigationFiles.forEach(file => {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath, 'utf8');
            
            // 查找 router-link to 属性
            const routerLinkMatches = content.match(/to="([^"]+)"/g) || [];
            routerLinkMatches.forEach(match => {
                const route = match.match(/to="([^"]+)"/)[1];
                foundLinks.add(route);
            });
            
            // 查找 router.push 调用
            const routerPushMatches = content.match(/router\.push\(['"`]([^'"`]+)['"`]\)/g) || [];
            routerPushMatches.forEach(match => {
                const route = match.match(/router\.push\(['"`]([^'"`]+)['"`]\)/)[1];
                foundLinks.add(route);
            });
            
            // 查找 navigateTo 调用
            const navigateToMatches = content.match(/navigateTo\(['"`]([^'"`]+)['"`]\)/g) || [];
            navigateToMatches.forEach(match => {
                const route = match.match(/navigateTo\(['"`]([^'"`]+)['"`]\)/)[1];
                foundLinks.add(route);
            });
            
            console.log(`  ✅ ${file}: 找到 ${routerLinkMatches.length + routerPushMatches.length + navigateToMatches.length} 个链接`);
        } else {
            console.log(`  ❌ ${file}: 文件不存在`);
        }
    });
    
    checkResults.navigation_links = Array.from(foundLinks).sort();
    console.log(`\n📊 总计发现 ${foundLinks.size} 个唯一导航链接:`);
    checkResults.navigation_links.forEach(link => console.log(`    ${link}`));
}

// 2. 收集路由定义
function collectRouteDefinitions() {
    console.log('\n📋 2. 收集路由定义...');
    
    const routerFile = 'src/router/index.js';
    const filePath = path.join(__dirname, routerFile);
    
    if (!fs.existsSync(filePath)) {
        console.log(`❌ 路由文件不存在: ${routerFile}`);
        return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 查找路由定义
    const routeMatches = content.match(/path:\s*['"`]([^'"`]+)['"`]/g) || [];
    const foundRoutes = new Set();
    
    routeMatches.forEach(match => {
        const route = match.match(/path:\s*['"`]([^'"`]+)['"`]/)[1];
        // 排除参数路由
        if (!route.includes(':')) {
            foundRoutes.add(route);
        }
    });
    
    checkResults.route_definitions = Array.from(foundRoutes).sort();
    console.log(`✅ 找到 ${foundRoutes.size} 个路由定义:`);
    checkResults.route_definitions.forEach(route => console.log(`    ${route}`));
}

// 3. 检查组件文件存在性
function checkComponentFiles() {
    console.log('\n📋 3. 检查组件文件...');
    
    const routerFile = 'src/router/index.js';
    const filePath = path.join(__dirname, routerFile);
    
    if (!fs.existsSync(filePath)) {
        console.log(`❌ 路由文件不存在: ${routerFile}`);
        return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 查找组件导入
    const componentMatches = content.match(/component:\s*(?:markRaw\()?([A-Za-z][A-Za-z0-9]*)/g) || [];
    const importMatches = content.match(/import\s+([A-Za-z][A-Za-z0-9]*)\s+from\s+['"`]([^'"`]+)['"`]/g) || [];
    
    console.log(`找到 ${componentMatches.length} 个组件引用`);
    console.log(`找到 ${importMatches.length} 个组件导入`);
    
    // 检查导入的组件文件
    importMatches.forEach(match => {
        const [, componentName, componentPath] = match.match(/import\s+([A-Za-z][A-Za-z0-9]*)\s+from\s+['"`]([^'"`]+)['"`]/);
        const fullPath = path.join(__dirname, componentPath.replace('../', 'src/') + '.vue');
        
        if (fs.existsSync(fullPath)) {
            checkResults.component_files.push({
                name: componentName,
                path: componentPath,
                exists: true,
                fullPath: fullPath
            });
            console.log(`  ✅ ${componentName}: ${componentPath}`);
        } else {
            checkResults.missing_components.push({
                name: componentName,
                path: componentPath,
                fullPath: fullPath
            });
            console.log(`  ❌ ${componentName}: ${componentPath} (文件不存在)`);
        }
    });
}

// 4. 检查空白组件
function checkEmptyComponents() {
    console.log('\n📋 4. 检查空白组件...');
    
    const viewsDir = path.join(__dirname, 'src/views');
    const componentsDir = path.join(__dirname, 'src/components');
    
    function checkDirectory(dir, prefix = '') {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory()) {
                checkDirectory(filePath, prefix + file + '/');
            } else if (file.endsWith('.vue')) {
                const content = fs.readFileSync(filePath, 'utf8');
                
                // 检查是否为空白组件（只有简单标题）
                const isEmptyComponent = (
                    content.length < 200 && 
                    (content.includes('<h1>') || content.includes('页面')) &&
                    !content.includes('export default') &&
                    content.split('\n').length < 5
                );
                
                if (isEmptyComponent) {
                    checkResults.empty_components.push({
                        name: file,
                        path: prefix + file,
                        fullPath: filePath,
                        size: content.length
                    });
                    console.log(`  ❌ 空白组件: ${prefix}${file} (${content.length} 字符)`);
                } else {
                    console.log(`  ✅ 正常组件: ${prefix}${file}`);
                }
            }
        });
    }
    
    checkDirectory(viewsDir, 'views/');
    checkDirectory(componentsDir, 'components/');
}

// 5. 分析路由冲突
function analyzeRouteConflicts() {
    console.log('\n📋 5. 分析路由冲突...');
    
    const missingRoutes = checkResults.navigation_links.filter(
        link => !checkResults.route_definitions.includes(link)
    );
    
    const unusedRoutes = checkResults.route_definitions.filter(
        route => !checkResults.navigation_links.includes(route)
    );
    
    if (missingRoutes.length > 0) {
        console.log(`❌ 缺失路由定义 (${missingRoutes.length} 个):`);
        missingRoutes.forEach(route => {
            console.log(`    ${route}`);
            checkResults.route_conflicts.push({
                type: 'missing_route',
                route: route,
                description: `导航链接 ${route} 没有对应的路由定义`
            });
        });
    }
    
    if (unusedRoutes.length > 0) {
        console.log(`⚠️  未使用路由 (${unusedRoutes.length} 个):`);
        unusedRoutes.forEach(route => {
            console.log(`    ${route}`);
        });
    }
    
    if (missingRoutes.length === 0 && unusedRoutes.length === 0) {
        console.log('✅ 没有发现路由冲突');
    }
}

// 6. 生成修复建议
function generateRecommendations() {
    console.log('\n📋 6. 生成修复建议...');
    
    // 空白组件修复建议
    if (checkResults.empty_components.length > 0) {
        checkResults.recommendations.push({
            type: 'empty_components',
            priority: 'high',
            title: '修复空白组件',
            description: `发现 ${checkResults.empty_components.length} 个空白组件需要修复`,
            actions: checkResults.empty_components.map(comp => 
                `将 ${comp.path} 的内容复制自对应的完整组件或删除未使用的组件`
            )
        });
    }
    
    // 缺失路由修复建议
    if (checkResults.route_conflicts.length > 0) {
        checkResults.recommendations.push({
            type: 'missing_routes',
            priority: 'high',
            title: '添加缺失路由',
            description: `发现 ${checkResults.route_conflicts.length} 个缺失路由需要添加`,
            actions: checkResults.route_conflicts.map(conflict => 
                `为 ${conflict.route} 添加路由定义和对应组件`
            )
        });
    }
    
    // 404错误页面建议
    checkResults.recommendations.push({
        type: '404_handling',
        priority: 'medium',
        title: '完善404错误处理',
        description: '添加通用404错误页面和路由重定向',
        actions: [
            '在路由配置中添加通配符路由指向NotFound组件',
            '为常见错误路径添加重定向规则',
            '优化NotFound页面的用户体验'
        ]
    });
    
    console.log(`生成了 ${checkResults.recommendations.length} 个修复建议`);
}

// 执行检查
async function runComprehensiveCheck() {
    try {
        collectNavigationLinks();
        collectRouteDefinitions();
        checkComponentFiles();
        checkEmptyComponents();
        analyzeRouteConflicts();
        generateRecommendations();
        
        // 输出总结报告
        console.log('\n' + '='.repeat(60));
        console.log('📊 检查总结报告');
        console.log('='.repeat(60));
        
        console.log(`✅ 导航链接: ${checkResults.navigation_links.length} 个`);
        console.log(`✅ 路由定义: ${checkResults.route_definitions.length} 个`);
        console.log(`✅ 正常组件: ${checkResults.component_files.length} 个`);
        console.log(`❌ 空白组件: ${checkResults.empty_components.length} 个`);
        console.log(`❌ 缺失组件: ${checkResults.missing_components.length} 个`);
        console.log(`❌ 路由冲突: ${checkResults.route_conflicts.length} 个`);
        console.log(`💡 修复建议: ${checkResults.recommendations.length} 个`);
        
        // 保存详细报告
        const reportPath = path.join(__dirname, 'route-check-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(checkResults, null, 2));
        console.log(`\n📄 详细报告已保存到: ${reportPath}`);
        
        return checkResults;
        
    } catch (error) {
        console.error('❌ 检查过程中发生错误:', error);
        return null;
    }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    runComprehensiveCheck();
}

export { runComprehensiveCheck, checkResults };
