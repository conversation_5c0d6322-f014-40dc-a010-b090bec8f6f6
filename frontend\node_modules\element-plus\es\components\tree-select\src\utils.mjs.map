{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/tree-select/src/utils.ts"], "sourcesContent": ["import { isArray } from '@element-plus/utils'\n\nimport type { TreeNodeData } from '@element-plus/components/tree/src/tree.type'\n\nexport function isValidValue(val: any) {\n  return val || val === 0\n}\n\nexport function isValidArray(val: any) {\n  return isArray(val) && val.length\n}\n\nexport function toValidArray(val: any) {\n  return isArray(val) ? val : isValidValue(val) ? [val] : []\n}\n\ntype TreeCallback<T extends TreeNodeData, R> = (\n  data: T,\n  index: number,\n  array: T[],\n  parent?: T\n) => R\n\ntype TreeFindCallback<T extends TreeNodeData> = TreeCallback<T, boolean>\n\nexport function treeFind<T extends TreeNodeData>(\n  treeData: T[],\n  findCallback: TreeFindCallback<T>,\n  getChildren: (data: T) => T[]\n): T | undefined\nexport function treeFind<T extends TreeNodeData, R>(\n  treeData: T[],\n  findCallback: TreeFindCallback<T>,\n  getChildren: (data: T) => T[],\n  resultCallback?: TreeCallback<T, R>,\n  parent?: T\n): R | undefined\nexport function treeFind<T extends TreeNodeData, R>(\n  treeData: T[],\n  findCallback: TreeFindCallback<T>,\n  getChildren: (data: T) => T[],\n  resultCallback?: TreeCallback<T, R>,\n  parent?: T\n): T | R | undefined {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i]\n    if (findCallback(data, i, treeData, parent)) {\n      return resultCallback ? resultCallback(data, i, treeData, parent) : data\n    } else {\n      const children = getChildren(data)\n      if (isValidArray(children)) {\n        const find = treeFind(\n          children,\n          findCallback,\n          getChildren,\n          resultCallback,\n          data\n        )\n        if (find) return find\n      }\n    }\n  }\n}\n\nexport function treeEach<T extends TreeNodeData>(\n  treeData: T[],\n  callback: TreeCallback<T, void>,\n  getChildren: (data: T) => T[],\n  parent?: T\n) {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i]\n    callback(data, i, treeData, parent)\n\n    const children = getChildren(data)\n    if (isValidArray(children)) {\n      treeEach(children, callback, getChildren, data)\n    }\n  }\n}\n"], "names": [], "mappings": ";;AACO,SAAS,YAAY,CAAC,GAAG,EAAE;AAClC,EAAE,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;AAC1B,CAAC;AACM,SAAS,YAAY,CAAC,GAAG,EAAE;AAClC,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC;AACpC,CAAC;AACM,SAAS,YAAY,CAAC,GAAG,EAAE;AAClC,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC7D,CAAC;AACM,SAAS,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE;AACtF,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE;AACjD,MAAM,OAAO,cAAc,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;AAC/E,KAAK,MAAM;AACX,MAAM,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACzC,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE;AAClC,QAAQ,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;AACzF,QAAQ,IAAI,IAAI;AAChB,UAAU,OAAO,IAAI,CAAC;AACtB,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACM,SAAS,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE;AAClE,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACvC,IAAI,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE;AAChC,MAAM,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACtD,KAAK;AACL,GAAG;AACH;;;;"}