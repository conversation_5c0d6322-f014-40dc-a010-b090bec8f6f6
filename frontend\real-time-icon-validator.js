#!/usr/bin/env node

/**
 * 实时图标验证工具
 * 生成浏览器控制台代码，实时验证图标修复效果
 */

console.log(`
🎯 多模态面试评估系统 - 实时图标验证工具
=====================================================

请在浏览器中打开 http://localhost:5173，然后：
1. 按F12打开开发者工具
2. 切换到Console标签页
3. 复制并运行以下JavaScript代码：

`);

console.log(`
// ===== 实时图标验证代码 =====
(function() {
    console.log('🔍 开始实时验证图标修复效果...');
    
    // 目标图标配置
    const iconTargets = [
        {
            name: '🔧 技术图标',
            selector: '.ai-tech-icon .el-icon',
            expectedDesktop: '20px',
            expectedMobile: '18px',
            context: '技术展示卡片',
            priority: 'HIGH'
        },
        {
            name: '📋 步骤图标',
            selector: '.ai-step-icon .el-icon', 
            expectedDesktop: '18px',
            expectedMobile: '16px',
            context: '流程步骤展示',
            priority: 'HIGH'
        },
        {
            name: '🎯 CTA选项图标',
            selector: '.ai-cta-option-icon .el-icon',
            expectedDesktop: '18px', 
            expectedMobile: '16px',
            context: '行动号召选项',
            priority: 'HIGH'
        },
        {
            name: '🏷️ 特色标签图标',
            selector: '.feature-tag .el-icon',
            expectedDesktop: '14px',
            expectedMobile: '14px',
            context: '功能特色标签',
            priority: 'MEDIUM'
        },
        {
            name: '📊 统计卡片图标',
            selector: '.stats-icon .el-icon',
            expectedDesktop: '24px',
            expectedMobile: '20px', 
            context: '数据统计展示',
            priority: 'MEDIUM'
        }
    ];
    
    // 检查当前屏幕尺寸
    const isMobile = window.innerWidth <= 768;
    const screenType = isMobile ? 'Mobile' : 'Desktop';
    
    console.log(\`📱 当前屏幕: \${window.innerWidth}px (\${screenType})\`);
    console.log(\`📋 检查模式: \${isMobile ? '移动端' : '桌面端'}\`);
    console.log('='.repeat(50));
    
    let totalChecked = 0;
    let correctSized = 0;
    let issues = [];
    let success = [];
    
    iconTargets.forEach((target, index) => {
        const elements = document.querySelectorAll(target.selector);
        
        if (elements.length === 0) {
            issues.push({
                type: 'MISSING',
                target: target.name,
                selector: target.selector,
                message: '元素未找到'
            });
            console.log(\`❌ \${target.name}: 元素未找到\`);
            return;
        }
        
        const expectedSize = isMobile ? target.expectedMobile : target.expectedDesktop;
        
        elements.forEach((element, elemIndex) => {
            totalChecked++;
            const computedStyle = window.getComputedStyle(element);
            const actualSize = computedStyle.fontSize;
            
            console.log(\`\\n\${index + 1}.\${elemIndex + 1} \${target.name} [\${target.context}]:\`);
            console.log(\`   选择器: \${target.selector}\`);
            console.log(\`   期望尺寸: \${expectedSize}\`);
            console.log(\`   实际尺寸: \${actualSize}\`);
            console.log(\`   优先级: \${target.priority}\`);
            
            if (actualSize === expectedSize) {
                correctSized++;
                success.push(target.name);
                console.log(\`   ✅ 尺寸正确\`);
            } else {
                const actualNum = parseInt(actualSize);
                const expectedNum = parseInt(expectedSize);
                const difference = actualNum - expectedNum;
                const status = difference > 0 ? '过大' : '过小';
                
                issues.push({
                    type: 'SIZE_MISMATCH',
                    target: target.name,
                    selector: target.selector,
                    expected: expectedSize,
                    actual: actualSize,
                    difference: Math.abs(difference),
                    status: status,
                    priority: target.priority
                });
                
                console.log(\`   ❌ 尺寸\${status} (差异: \${difference}px)\`);
                
                // 检查样式来源
                console.log(\`   🔍 样式诊断:\`);
                console.log(\`     - 计算样式: \${actualSize}\`);
                console.log(\`     - 内联样式: \${element.style.fontSize || '无'}\`);
                console.log(\`     - 显示方式: \${computedStyle.display}\`);
                
                // 检查是否有!important样式
                const hasImportant = element.style.cssText.includes('!important');
                console.log(\`     - !important: \${hasImportant ? '是' : '否'}\`);
            }
        });
    });
    
    // 生成详细报告
    console.log(\`\\n📊 验证报告:\`);
    console.log(\`=\`.repeat(40));
    console.log(\`屏幕尺寸: \${window.innerWidth}px (\${screenType})\`);
    console.log(\`总检查数: \${totalChecked}\`);
    console.log(\`正确尺寸: \${correctSized}\`);
    console.log(\`问题数量: \${issues.length}\`);
    console.log(\`修复成功率: \${Math.round(correctSized/totalChecked*100)}%\`);
    
    // 按优先级分类问题
    const highPriorityIssues = issues.filter(i => i.priority === 'HIGH');
    const mediumPriorityIssues = issues.filter(i => i.priority === 'MEDIUM');
    
    if (highPriorityIssues.length > 0) {
        console.log(\`\\n🚨 高优先级问题 (\${highPriorityIssues.length}):\`);
        highPriorityIssues.forEach(issue => {
            console.log(\`- \${issue.target}: \${issue.actual} (应为\${issue.expected})\`);
        });
    }
    
    if (mediumPriorityIssues.length > 0) {
        console.log(\`\\n⚠️ 中优先级问题 (\${mediumPriorityIssues.length}):\`);
        mediumPriorityIssues.forEach(issue => {
            console.log(\`- \${issue.target}: \${issue.actual} (应为\${issue.expected})\`);
        });
    }
    
    if (success.length > 0) {
        console.log(\`\\n✅ 修复成功的图标:\`);
        success.forEach(name => console.log(\`- \${name}\`));
    }
    
    // 响应式测试建议
    console.log(\`\\n📱 响应式测试建议:\`);
    if (isMobile) {
        console.log(\`当前为移动端视图，建议调整窗口到桌面尺寸(>768px)重新测试\`);
    } else {
        console.log(\`当前为桌面端视图，建议调整窗口到移动尺寸(≤768px)重新测试\`);
    }
    
    // 修复建议
    if (issues.length > 0) {
        console.log(\`\\n🔧 修复建议:\`);
        console.log(\`1. 检查CSS样式是否正确应用!important\`);
        console.log(\`2. 验证选择器优先级是否足够高\`);
        console.log(\`3. 确认热重载是否正确更新样式\`);
        console.log(\`4. 检查Element Plus默认样式冲突\`);
        console.log(\`5. 右键检查元素查看样式层叠情况\`);
    } else {
        console.log(\`\\n🎉 所有图标尺寸修复成功！\`);
        console.log(\`✨ 图标与中文文字协调性完美\`);
        console.log(\`📱 响应式设计表现良好\`);
    }
    
    console.log(\`\\n✅ 验证完成！\`);
    
    // 返回结果
    return {
        screenWidth: window.innerWidth,
        screenType,
        totalChecked,
        correctSized,
        issues,
        success,
        successRate: Math.round(correctSized/totalChecked*100)
    };
})();

`);

console.log(`
===== 使用说明 =====

1. 运行上述代码后，查看控制台输出的详细报告
2. 如果发现问题，按照修复建议进行处理
3. 调整浏览器窗口大小测试响应式效果
4. 重新运行代码验证修复结果
5. 确保在不同页面间切换时图标尺寸保持一致

===== 预期结果 =====

修复成功后应该看到：
- ✅ 修复成功率: 100%
- ✅ 所有高优先级图标尺寸正确
- ✅ 响应式样式在不同屏幕尺寸下正确应用
- ✅ 图标与中文文字视觉协调

如果仍有问题，请将控制台输出结果反馈给我！
`);
