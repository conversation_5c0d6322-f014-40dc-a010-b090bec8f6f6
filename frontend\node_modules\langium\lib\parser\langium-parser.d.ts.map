{"version": 3, "file": "langium-parser.d.ts", "sourceRoot": "", "sources": ["../../src/parser/langium-parser.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAGhF,OAAO,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC9J,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,EAAc,UAAU,EAAE,MAAM,+BAA+B,CAAC;AAErG,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,KAAK,EAAE,OAAO,EAA4C,MAAM,mBAAmB,CAAC;AAC3F,OAAO,KAAK,EAAE,KAAK,EAAe,MAAM,YAAY,CAAC;AACrD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAExD,OAAO,EAA8B,qBAAqB,EAAwB,MAAM,YAAY,CAAC;AAMrG,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAEvD,MAAM,MAAM,WAAW,CAAC,CAAC,GAAG,OAAO,IAAI;IACnC,KAAK,EAAE,CAAC,CAAC;IACT,YAAY,EAAE,qBAAqB,EAAE,CAAC;IACtC,WAAW,EAAE,YAAY,EAAE,CAAC;IAC5B,WAAW,CAAC,EAAE,YAAY,CAAA;CAC7B,CAAA;AAED,eAAO,MAAM,cAAc,eAAqB,CAAC;AAcjD,KAAK,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,GAAG,CAAC;AAEtC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAEpC,KAAK,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,GAAG,CAAC;AAOpC;;;;;GAKG;AACH,MAAM,WAAW,UAAU;IACvB;;OAEG;IACH,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,GAAG,UAAU,CAAC;IACnD;;OAEG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,UAAU,GAAG,SAAS,CAAC;IAC9C;;OAEG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAC7D;;OAEG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IAC9D;;OAEG;IACH,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IAC1D;;OAEG;IACH,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IAChE;;;OAGG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI,CAAC;IAC3E;;;;OAIG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;IACtG;;OAEG;IACH,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5C;;OAEG;IACH,SAAS,IAAI,OAAO,CAAC;IACrB;;;;OAIG;IACH,WAAW,IAAI,OAAO,CAAC;IACvB;;OAEG;IACH,IAAI,eAAe,IAAI,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;IAC9C;;;OAGG;IACH,YAAY,IAAI,MAAM,EAAE,CAAC;CAC5B;AAKD,8BAAsB,qBAAsB,YAAW,UAAU;IAE7D,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;IAChC,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,iBAAiB,CAAC;IAC9C,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAgC;IAElF,SAAS,CAAC,QAAQ,0BAAiC;IACnD,SAAS,CAAC,QAAQ,EAAG,UAAU,CAAC;gBAEpB,QAAQ,EAAE,mBAAmB;IAWzC,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAI5D,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI;IAI7D,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI;IAIzD,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI;IAI/D,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,GAAG,UAAU;IAC3D,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI;IACnF,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IAC9G,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IACpD,QAAQ,CAAC,SAAS,IAAI,OAAO;IAE7B,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,UAAU,GAAG,SAAS;IAI7C,WAAW,IAAI,OAAO;IAItB,IAAI,eAAe,IAAI,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAE5C;IAED,YAAY,IAAI,MAAM,EAAE;IAIxB,QAAQ,IAAI,IAAI;CAGnB;AAED,MAAM,WAAW,aAAa;IAC1B,IAAI,CAAC,EAAE,MAAM,CAAA;CAChB;AAED,qBAAa,aAAc,SAAQ,qBAAqB;IACpD,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;IAChC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAiB;IAC3C,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAgB;IAC9C,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAwB;IACpD,OAAO,CAAC,WAAW,CAAC,CAAc;IAClC,OAAO,CAAC,KAAK,CAAa;IAC1B,OAAO,CAAC,aAAa,CAA6D;IAElF,OAAO,KAAK,OAAO,GAElB;gBAEW,QAAQ,EAAE,mBAAmB;IAOzC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,GAAG,UAAU;IAUlD,OAAO,CAAC,eAAe;IAWvB,KAAK,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,GAAE,aAAkB,GAAG,WAAW,CAAC,CAAC,CAAC;IAoB9F,OAAO,CAAC,mBAAmB;IAwB3B,OAAO,CAAC,mBAAmB;IAe3B,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI;IAqB1E;;;;;OAKG;IACH,OAAO,CAAC,YAAY;IAIpB,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IAerG,OAAO,CAAC,wBAAwB;IAsBhC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAiB3C,SAAS,IAAI,OAAO;IAgBpB,OAAO,CAAC,aAAa;IAWrB,OAAO,CAAC,MAAM;IA0Bd,OAAO,CAAC,qBAAqB;IAuB7B,IAAI,gBAAgB,IAAI,sBAAsB,EAAE,CAE/C;CACJ;AAED,MAAM,WAAW,sBAAsB;IACnC,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAA;CACpB;AAED,8BAAsB,kCAAmC,YAAW,2BAA2B;IAE3F,yBAAyB,CAAC,OAAO,EAAE;QAC/B,QAAQ,EAAE,SAAS,CAAA;QACnB,MAAM,EAAE,MAAM,CAAA;QACd,QAAQ,EAAE,MAAM,CAAA;QAChB,QAAQ,EAAE,MAAM,CAAA;KACnB,GAAG,MAAM;IAIV,6BAA6B,CAAC,OAAO,EAAE;QACnC,cAAc,EAAE,MAAM,CAAA;QACtB,QAAQ,EAAE,MAAM,CAAA;KACnB,GAAG,MAAM;IAIV,uBAAuB,CAAC,OAAO,EAAE;QAC7B,mBAAmB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAA;QACpC,MAAM,EAAE,MAAM,EAAE,CAAA;QAChB,QAAQ,EAAE,MAAM,CAAA;QAChB,qBAAqB,EAAE,MAAM,CAAA;QAC7B,QAAQ,EAAE,MAAM,CAAA;KACnB,GAAG,MAAM;IAIV,qBAAqB,CAAC,OAAO,EAAE;QAC3B,sBAAsB,EAAE,SAAS,EAAE,EAAE,CAAA;QACrC,MAAM,EAAE,MAAM,EAAE,CAAA;QAChB,QAAQ,EAAE,MAAM,CAAA;QAChB,qBAAqB,EAAE,MAAM,CAAA;QAC7B,QAAQ,EAAE,MAAM,CAAA;KACnB,GAAG,MAAM;CAIb;AAED,qBAAa,iCAAkC,SAAQ,kCAAkC;IAE5E,yBAAyB,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE;QACrD,QAAQ,EAAE,SAAS,CAAA;QACnB,MAAM,EAAE,MAAM,CAAA;QACd,QAAQ,EAAE,MAAM,CAAA;QAChB,QAAQ,EAAE,MAAM,CAAA;KACnB,GAAG,MAAM;IASD,6BAA6B,CAAC,EAAE,cAAc,EAAE,EAAE;QACvD,cAAc,EAAE,MAAM,CAAA;QACtB,QAAQ,EAAE,MAAM,CAAA;KACnB,GAAG,MAAM;CAGb;AAED,MAAM,WAAW,sBAAsB;IACnC,MAAM,EAAE,MAAM,EAAE,CAAA;IAChB,YAAY,EAAE,eAAe,EAAE,CAAA;IAC/B,UAAU,EAAE,MAAM,CAAA;CACrB;AAED,qBAAa,uBAAwB,SAAQ,qBAAqB;IAC9D,OAAO,CAAC,MAAM,CAAgB;IAE9B,OAAO,CAAC,YAAY,CAAyB;IAC7C,OAAO,CAAC,gBAAgB,CAAyB;IACjD,OAAO,CAAC,cAAc,CAAK;IAC3B,OAAO,CAAC,SAAS,CAAK;IAEtB,MAAM,IAAI,IAAI;IAId,SAAS,IAAI,OAAO;IAKpB,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,sBAAsB;IAc5C,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,GAAG,UAAU;IASlD,OAAO,CAAC,UAAU;IAOlB,OAAO,CAAC,mBAAmB;IAW3B,OAAO,CAAC,wBAAwB;IAIhC,aAAa,IAAI,MAAM;IAMvB,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAKlC,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI;IAQ1E,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IAMrG,MAAM,CAAC,OAAO,EAAE,eAAe,GAAG,IAAI;IAMtC,KAAK,CAAC,OAAO,EAAE,eAAe,GAAG,IAAI;IASrC,IAAI,OAAO,IAAI,MAAM,CAEpB;CACJ;AASD;;;GAGG;AACH,cAAM,iBAAkB,SAAQ,qBAAqB;IAGjD,gBAAgB,EAAE,sBAAsB,EAAE,CAAC;gBAE/B,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,aAAa;IAc1D,IAAI,YAAY,IAAI,OAAO,CAE1B;IAED,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,GAAG,UAAU;IAIrD,gBAAgB,IAAI,IAAI;IAIxB,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,GAAG,MAAM;IAItD,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,GAAG,OAAO;IAM/D,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAItD,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI;IAI/D,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI;IAI7D,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI;CAGtE"}