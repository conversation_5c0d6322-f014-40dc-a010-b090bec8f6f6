#!/usr/bin/env node

/**
 * 检查重复图标导入脚本
 * 检测 Element Plus 图标导入中的重复项
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 检查文件中的重复导入
function checkDuplicateImports(filePath) {
  if (!fs.existsSync(filePath)) {
    return { hasDuplicates: false, duplicates: [] }
  }

  const content = fs.readFileSync(filePath, 'utf8')
  const duplicates = []

  // 匹配 Element Plus 图标导入语句
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@element-plus\/icons-vue['"]/g
  let importMatch

  while ((importMatch = importRegex.exec(content)) !== null) {
    const iconList = importMatch[1]
    const iconNames = iconList
      .split(',')
      .map(name => name.trim())
      .filter(name => name && !name.includes('as'))
      .map(name => name.replace(/\s+as\s+\w+/, '').trim())

    // 检查重复
    const seen = new Set()
    const currentDuplicates = []

    iconNames.forEach(icon => {
      if (seen.has(icon)) {
        currentDuplicates.push(icon)
      } else {
        seen.add(icon)
      }
    })

    if (currentDuplicates.length > 0) {
      duplicates.push({
        line: importMatch.index,
        icons: currentDuplicates,
        fullImport: importMatch[0]
      })
    }
  }

  return {
    hasDuplicates: duplicates.length > 0,
    duplicates
  }
}

// 递归扫描目录
function scanDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const relativePath = path.relative(__dirname, fullPath)
        const result = checkDuplicateImports(fullPath)
        
        if (result.hasDuplicates) {
          results.push({
            file: relativePath,
            duplicates: result.duplicates
          })
        }
      }
    }
  }
  
  scan(dir)
  return results
}

// 主函数
function main() {
  console.log('🔍 检查 Element Plus 图标重复导入...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  if (results.length === 0) {
    console.log('✅ 未发现重复导入的图标')
    return
  }
  
  console.log(`❌ 发现 ${results.length} 个文件存在重复导入:\n`)
  
  results.forEach(({ file, duplicates }) => {
    console.log(`📄 ${file}:`)
    
    duplicates.forEach(({ icons, fullImport }) => {
      console.log(`  ❌ 重复图标: ${icons.join(', ')}`)
      console.log(`  📝 导入语句: ${fullImport.substring(0, 100)}...`)
    })
    
    console.log()
  })
  
  console.log('💡 请手动修复重复导入的图标')
}

main()
