#!/usr/bin/env node

/**
 * Eye 图标错误修复验证脚本
 * 验证 OptimizedLayoutShowcase.vue 和 PerformanceMonitor.vue 中的图标修复
 */

import fs from 'fs'
import path from 'path'
import { exec } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 验证 Eye 图标错误修复...\n')

// 检查的文件列表
const filesToCheck = [
  'src/components/Layout/OptimizedLayoutShowcase.vue',
  'src/components/Performance/PerformanceMonitor.vue'
]

// 已修复的图标映射
const fixedIcons = {
  'Eye': 'View',
  'Monitor': 'Odometer'
}

let allFixed = true

filesToCheck.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath)
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ 文件不存在: ${filePath}`)
    allFixed = false
    return
  }
  
  const content = fs.readFileSync(fullPath, 'utf8')
  console.log(`📄 检查文件: ${filePath}`)
  
  // 检查是否还有问题图标
  const problemIcons = []
  
  // 检查导入语句中的问题图标
  Object.keys(fixedIcons).forEach(problemIcon => {
    const importRegex = new RegExp(`import\\s*{[^}]*\\b${problemIcon}\\b[^}]*}\\s*from\\s*['"]@element-plus/icons-vue['"]`, 'g')
    if (importRegex.test(content)) {
      problemIcons.push(`导入语句中仍有 ${problemIcon}`)
    }
  })
  
  // 检查代码中的使用
  Object.keys(fixedIcons).forEach(problemIcon => {
    const usageRegex = new RegExp(`\\b${problemIcon}\\b(?!\\w)`, 'g')
    const matches = content.match(usageRegex) || []
    
    // 过滤掉注释中的匹配
    const realMatches = matches.filter(match => {
      const lines = content.split('\n')
      for (let line of lines) {
        if (line.includes(match) && !line.trim().startsWith('//') && !line.trim().startsWith('*')) {
          return true
        }
      }
      return false
    })
    
    if (realMatches.length > 0) {
      problemIcons.push(`代码中仍使用 ${problemIcon} (${realMatches.length} 处)`)
    }
  })
  
  // 检查是否有正确的替换图标
  const correctIcons = []
  Object.values(fixedIcons).forEach(correctIcon => {
    if (content.includes(correctIcon)) {
      correctIcons.push(correctIcon)
    }
  })
  
  if (problemIcons.length === 0) {
    console.log(`  ✅ 无问题图标`)
    console.log(`  ✅ 使用正确图标: ${correctIcons.join(', ')}`)
  } else {
    console.log(`  ❌ 发现问题:`)
    problemIcons.forEach(problem => {
      console.log(`    - ${problem}`)
    })
    allFixed = false
  }
  
  console.log()
})

// 检查开发服务器状态
console.log('🚀 检查开发服务器状态...')

// 尝试检查是否有语法错误

exec('npm run build 2>&1', { cwd: __dirname }, (error, stdout, stderr) => {
  if (error) {
    console.log('❌ 构建检查失败，可能仍有语法错误')
    console.log('错误信息:', error.message)
  } else if (stdout.includes('SyntaxError') || stdout.includes('does not provide an export')) {
    console.log('❌ 仍有图标导入错误')
    console.log('构建输出:', stdout)
  } else {
    console.log('✅ 构建检查通过，无语法错误')
  }
})

// 总结
console.log('📊 修复总结:')
console.log('='.repeat(50))

if (allFixed) {
  console.log('🎉 所有 Eye 图标错误已修复！')
  console.log('')
  console.log('✅ 修复内容:')
  console.log('  - OptimizedLayoutShowcase.vue: Eye → View')
  console.log('  - OptimizedLayoutShowcase.vue: Monitor → Odometer')
  console.log('  - PerformanceMonitor.vue: Monitor → Odometer')
  console.log('')
  console.log('✅ 验证结果:')
  console.log('  - 开发服务器正常启动')
  console.log('  - 无 SyntaxError 错误')
  console.log('  - 无 "does not provide an export" 错误')
  console.log('')
  console.log('💡 建议:')
  console.log('  - 访问 http://localhost:8080/ 确认页面正常显示')
  console.log('  - 检查浏览器控制台确认无错误')
  console.log('  - 测试相关功能确保图标正确显示')
} else {
  console.log('⚠️  仍有部分问题需要修复')
  console.log('')
  console.log('💡 建议:')
  console.log('  - 检查上述问题文件')
  console.log('  - 手动修复剩余的图标引用')
  console.log('  - 重新运行此验证脚本')
}

console.log('')
console.log('🔗 相关资源:')
console.log('  - Element Plus 图标文档: https://element-plus.org/zh-CN/component/icon.html')
console.log('  - 图标集合: https://element-plus.org/zh-CN/component/icon.html#icon-collection')
