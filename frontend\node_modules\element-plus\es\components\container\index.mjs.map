{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/container/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\n\nimport Container from './src/container.vue'\nimport Aside from './src/aside.vue'\nimport Footer from './src/footer.vue'\nimport Header from './src/header.vue'\nimport Main from './src/main.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElContainer: SFCWithInstall<typeof Container> & {\n  Aside: typeof Aside\n  Footer: typeof Footer\n  Header: typeof Header\n  Main: typeof Main\n} = withInstall(Container, {\n  Aside,\n  Footer,\n  Header,\n  Main,\n})\n\nexport default ElContainer\nexport const ElAside: SFCWithInstall<typeof Aside> = withNoopInstall(Aside)\nexport const ElFooter: SFCWithInstall<typeof Footer> = withNoopInstall(Footer)\nexport const ElHeader: SFCWithInstall<typeof Header> = withNoopInstall(Header)\nexport const ElMain: SFCWithInstall<typeof Main> = withNoopInstall(Main)\n\nexport type ContainerInstance = InstanceType<typeof Container> & unknown\nexport type AsideInstance = InstanceType<typeof Aside> & unknown\nexport type FooterInstance = InstanceType<typeof Footer> & unknown\nexport type HeaderInstance = InstanceType<typeof Header> & unknown\nexport type MainInstance = InstanceType<typeof Main> & unknown\n"], "names": [], "mappings": ";;;;;;;AAMY,MAAC,WAAW,GAAG,WAAW,CAAC,SAAS,EAAE;AAClD,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,IAAI;AACN,CAAC,EAAE;AAES,MAAC,OAAO,GAAG,eAAe,CAAC,KAAK,EAAE;AAClC,MAAC,QAAQ,GAAG,eAAe,CAAC,MAAM,EAAE;AACpC,MAAC,QAAQ,GAAG,eAAe,CAAC,MAAM,EAAE;AACpC,MAAC,MAAM,GAAG,eAAe,CAAC,IAAI;;;;"}