{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,MAAM,CACpB,KAAoC;IACpC,gBAAoB;SAApB,UAAoB,EAApB,qBAAoB,EAApB,IAAoB;QAApB,+BAAoB;;IAEpB,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAGtE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAC/D,gBAAgB,EAChB,EAAE,CACH,CAAC;IAGF,IAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,GAAG;QAC5C,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACjD,IAAI,OAAO,EAAE;YACX,OAAO,GAAG,CAAC,MAAM,CACf,OAAO,CAAC,GAAG,CAAC,UAAC,KAAK,gBAAK,OAAA,MAAA,MAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,0CAAE,MAAM,mCAAI,CAAC,CAAA,EAAA,CAAC,CAC3D,CAAC;SACH;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAY,EAAE,CAAC,CAAC;IAGjB,IAAI,aAAa,CAAC,MAAM,EAAE;QACxB,IAAM,SAAO,GAAG,IAAI,MAAM,CAAC,aAAW,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,aAAa,OAAI,EAAE,GAAG,CAAC,CAAC;QAE1E,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,OAAO,CAAC,SAAO,EAAE,IAAI,CAAC,EAA1B,CAA0B,CAAC,CAAC;KAC5D;IAGD,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAG9C,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAExB,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,CAAC;QAEtB,IAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QAClD,IAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QACvD,IAAI,aAAa,GAAG,KAAK,CAAA;QAEzB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACrD,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;iBAC1B,KAAK,CAAC,IAAI,CAAC;iBACX,GAAG,CAAC,UAAC,GAAG,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAG,WAAW,GAAG,GAAK,CAAA;YAC/C,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAC;SACf;QAED,MAAM,IAAI,aAAa,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,eAAe,MAAM,CAAC"}