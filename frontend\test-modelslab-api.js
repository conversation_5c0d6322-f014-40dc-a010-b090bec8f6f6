#!/usr/bin/env node

/**
 * 🧪 ModelsLab API测试工具
 * ModelsLab API Testing Tool
 * 
 * 专门测试ModelsLab API的连接性和中文字体生成效果
 * Specifically tests ModelsLab API connectivity and Chinese font generation effects
 */

import fs from 'fs';
import fetch from 'node-fetch';

// ModelsLab API配置
const MODELSLAB_API_KEY = process.env.MODELSLAB_API_KEY || 'B1XaorSAPZrHQWVUesxvMAn9jXPNq8dbcDJCHtipgpwGWnRWCnHsbS6CTXAM';
const MODELSLAB_ENDPOINT = 'https://modelslab.com/api/v7/images/text-to-image';

console.log('🧪 ModelsLab API测试工具');
console.log('ModelsLab API Testing Tool\n');

// 测试用的中文界面提示词
const TEST_PROMPTS = {
    simple: {
        name: '简单测试',
        prompt: 'Professional AI interface with Microsoft YaHei font, Chinese text "科大讯飞Spark系统", blue background, high quality',
        description: '基础中文字体测试'
    },
    enterprise: {
        name: '企业级测试',
        prompt: 'Professional AI interview system interface, Microsoft YaHei font rendering, crystal clear Chinese text "科大讯飞Spark智能面试评估系统" as main title, blue-purple gradient background, modern corporate UI design, white buttons with Chinese labels "开始面试" "多模态分析" "能力评估" "生成报告", enterprise-grade quality, sharp text, 1920x1080 resolution',
        description: '完整企业级界面测试'
    },
    technical: {
        name: '技术架构测试',
        prompt: 'iFlytek Spark LLM technical architecture interface, Microsoft YaHei font, clear Chinese title "AI技术架构", neural network diagrams, technical labels "神经网络" "算法优化" "多模态融合", deep blue background, professional visualization, crisp text',
        description: '技术界面中文标签测试'
    }
};

// 测试API连接性
async function testAPIConnectivity() {
    console.log('🔗 测试ModelsLab API连接性...');
    
    if (!MODELSLAB_API_KEY) {
        console.log('❌ API密钥未设置');
        return false;
    }
    
    console.log(`🔑 API密钥: ${MODELSLAB_API_KEY.substring(0, 10)}...`);
    console.log(`📡 API端点: ${MODELSLAB_ENDPOINT}`);
    
    try {
        // 发送简单的测试请求
        const testData = {
            key: MODELSLAB_API_KEY,
            prompt: 'professional interface design, clean layout, modern UI',
            width: 512,
            height: 512,
            samples: 1,
            num_inference_steps: 20,
            safety_checker: 'yes',
            enhance_prompt: 'no',
            webhook: null,
            track_id: null
        };
        
        console.log('📤 发送测试请求...');
        
        const response = await fetch(MODELSLAB_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        console.log(`📥 响应状态: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.log(`❌ API调用失败: ${errorText}`);
            return false;
        }
        
        const result = await response.json();
        console.log('✅ API连接成功');
        console.log(`📋 响应数据:`, JSON.stringify(result, null, 2));
        
        return true;
        
    } catch (error) {
        console.log(`❌ 连接测试失败: ${error.message}`);
        return false;
    }
}

// 测试中文字体生成
async function testChineseFontGeneration(testPrompt) {
    console.log(`\n🎨 测试: ${testPrompt.name}`);
    console.log(`📝 描述: ${testPrompt.description}`);
    console.log(`💭 提示词: ${testPrompt.prompt.substring(0, 80)}...`);
    
    try {
        const requestData = {
            key: MODELSLAB_API_KEY,
            prompt: testPrompt.prompt,
            negative_prompt: 'blurry text, pixelated fonts, low quality, distorted characters, poor UI design, unclear Chinese text, bad typography',
            width: 1024,
            height: 1024,
            samples: 1,
            num_inference_steps: 30,
            safety_checker: 'yes',
            enhance_prompt: 'yes',
            guidance_scale: 7.5,
            model_id: 'stable-diffusion-xl-base-1.0',
            scheduler: 'K_EULER',
            seed: null,
            webhook: null,
            track_id: null
        };
        
        console.log('🚀 发送生成请求...');
        
        const response = await fetch(MODELSLAB_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API调用失败: ${response.status} - ${errorText}`);
        }
        
        const result = await response.json();
        
        console.log('✅ 生成请求成功');
        console.log(`📋 任务状态: ${result.status || '未知'}`);
        console.log(`🆔 任务ID: ${result.id || '未知'}`);
        
        if (result.output && result.output.length > 0) {
            console.log(`🖼️  生成图片: ${result.output.length} 张`);
            result.output.forEach((url, index) => {
                console.log(`   图片 ${index + 1}: ${url}`);
            });
        } else if (result.eta) {
            console.log(`⏱️  预计完成时间: ${result.eta} 秒`);
        }
        
        return {
            success: true,
            test_name: testPrompt.name,
            task_id: result.id,
            status: result.status,
            output: result.output,
            eta: result.eta,
            result: result
        };
        
    } catch (error) {
        console.log(`❌ 生成失败: ${error.message}`);
        return {
            success: false,
            test_name: testPrompt.name,
            error: error.message
        };
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始ModelsLab API完整测试\n');
    
    const testResults = {
        timestamp: new Date().toISOString(),
        api_connectivity: false,
        font_tests: [],
        summary: {
            total_tests: Object.keys(TEST_PROMPTS).length,
            successful_tests: 0,
            failed_tests: 0
        },
        recommendations: []
    };
    
    try {
        // 1. 测试API连接性
        testResults.api_connectivity = await testAPIConnectivity();
        
        if (!testResults.api_connectivity) {
            console.log('\n❌ API连接失败，无法继续测试');
            testResults.recommendations.push('检查API密钥是否正确');
            testResults.recommendations.push('确认网络连接正常');
            return testResults;
        }
        
        // 2. 测试中文字体生成
        console.log('\n🎨 开始中文字体生成测试...');
        
        for (const [key, testPrompt] of Object.entries(TEST_PROMPTS)) {
            const result = await testChineseFontGeneration(testPrompt);
            testResults.font_tests.push(result);
            
            if (result.success) {
                testResults.summary.successful_tests++;
            } else {
                testResults.summary.failed_tests++;
            }
            
            // 避免API限流
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // 3. 生成测试报告
        console.log('\n📊 测试结果汇总:');
        console.log(`✅ 成功: ${testResults.summary.successful_tests}/${testResults.summary.total_tests}`);
        console.log(`❌ 失败: ${testResults.summary.failed_tests}/${testResults.summary.total_tests}`);
        
        // 4. 生成建议
        if (testResults.summary.successful_tests === testResults.summary.total_tests) {
            testResults.recommendations.push('✅ ModelsLab API工作正常，可以用于生产');
            testResults.recommendations.push('🚀 可以运行: node enhanced-chinese-video-generator.js');
        } else if (testResults.summary.successful_tests > 0) {
            testResults.recommendations.push('⚠️  部分测试成功，建议检查失败的测试');
            testResults.recommendations.push('🔧 可能需要调整提示词或参数');
        } else {
            testResults.recommendations.push('❌ 所有测试失败，请检查API配置');
            testResults.recommendations.push('🛠️  建议联系ModelsLab技术支持');
        }
        
        // 5. 保存测试报告
        fs.writeFileSync('modelslab-test-report.json', JSON.stringify(testResults, null, 2));
        console.log('\n📄 测试报告已保存到 modelslab-test-report.json');
        
        // 6. 显示建议
        console.log('\n💡 建议操作:');
        testResults.recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
        
        return testResults;
        
    } catch (error) {
        console.error('\n❌ 测试过程中发生错误:', error.message);
        testResults.recommendations.push('检查网络连接和API配置');
        return testResults;
    }
}

// 快速测试
async function quickTest() {
    console.log('⚡ ModelsLab API快速测试\n');
    
    console.log(`🔑 API密钥状态: ${MODELSLAB_API_KEY ? '已设置' : '未设置'}`);
    console.log(`📡 API端点: ${MODELSLAB_ENDPOINT}`);
    
    if (!MODELSLAB_API_KEY) {
        console.log('\n❌ 请先设置API密钥:');
        console.log('export MODELSLAB_API_KEY="your_api_key"');
        return;
    }
    
    console.log('\n🚀 运行完整测试: node test-modelslab-api.js');
    console.log('🎨 生成图片: node enhanced-chinese-video-generator.js');
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--quick')) {
    quickTest();
} else {
    runAllTests();
}

export {
    testAPIConnectivity,
    testChineseFontGeneration,
    runAllTests,
    TEST_PROMPTS,
    MODELSLAB_API_KEY,
    MODELSLAB_ENDPOINT
};
