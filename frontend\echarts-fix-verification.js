// ECharts修复验证脚本
// 在浏览器控制台中运行此脚本来验证修复效果

console.log('🔧 开始验证ECharts修复效果...')

// 1. 检查ECharts是否正确加载
function checkEChartsLoading() {
  console.log('\n=== 1. ECharts加载检查 ===')
  
  if (typeof echarts !== 'undefined') {
    console.log('✅ ECharts已正确加载')
    console.log(`📦 ECharts版本: ${echarts.version || '未知'}`)
    return true
  } else {
    console.log('❌ ECharts未加载')
    return false
  }
}

// 2. 检查Vue-ECharts组件
function checkVueECharts() {
  console.log('\n=== 2. Vue-ECharts组件检查 ===')
  
  const app = document.querySelector('#app').__vue_app__
  if (app && app._context.components.VChart) {
    console.log('✅ VChart组件已注册')
    return true
  } else {
    console.log('❌ VChart组件未注册')
    return false
  }
}

// 3. 检查图表容器
function checkChartContainers() {
  console.log('\n=== 3. 图表容器检查 ===')
  
  const containers = document.querySelectorAll('[class*="chart"], [ref*="chart"], [id*="chart"]')
  console.log(`📊 找到 ${containers.length} 个潜在图表容器`)
  
  let validContainers = 0
  containers.forEach((container, index) => {
    const rect = container.getBoundingClientRect()
    const isValid = rect.width > 0 && rect.height > 0
    
    console.log(`容器 ${index + 1}: ${rect.width}x${rect.height} ${isValid ? '✅' : '❌'}`)
    if (isValid) validContainers++
  })
  
  return validContainers > 0
}

// 4. 测试ECharts初始化
function testEChartsInit() {
  console.log('\n=== 4. ECharts初始化测试 ===')
  
  try {
    // 创建测试容器
    const testContainer = document.createElement('div')
    testContainer.style.width = '400px'
    testContainer.style.height = '300px'
    testContainer.style.position = 'absolute'
    testContainer.style.top = '-9999px'
    document.body.appendChild(testContainer)
    
    // 测试初始化
    const testChart = echarts.init(testContainer)
    
    // 测试基本配置
    testChart.setOption({
      title: { text: '测试图表' },
      xAxis: { type: 'category', data: ['A', 'B', 'C'] },
      yAxis: { type: 'value' },
      series: [{ data: [1, 2, 3], type: 'line' }]
    })
    
    console.log('✅ ECharts初始化和配置成功')
    
    // 清理
    testChart.dispose()
    document.body.removeChild(testContainer)
    
    return true
  } catch (error) {
    console.log(`❌ ECharts初始化失败: ${error.message}`)
    return false
  }
}

// 5. 检查控制台错误
function checkConsoleErrors() {
  console.log('\n=== 5. 控制台错误检查 ===')
  
  // 这里我们检查是否有ECharts相关的错误
  const errorPatterns = [
    'registers.registerChartView is not a function',
    'Can\'t get DOM width or height',
    'ECharts',
    'chart'
  ]
  
  // 由于我们无法直接访问控制台历史，我们提示用户检查
  console.log('请检查控制台是否还有以下错误:')
  errorPatterns.forEach(pattern => {
    console.log(`  - ${pattern}`)
  })
  
  console.log('如果没有看到上述错误，说明修复成功 ✅')
  return true
}

// 6. 综合测试报告
function generateTestReport() {
  console.log('\n=== 📋 ECharts修复验证报告 ===')
  
  const results = {
    echartsLoading: checkEChartsLoading(),
    vueECharts: checkVueECharts(),
    containers: checkChartContainers(),
    initialization: testEChartsInit(),
    consoleErrors: checkConsoleErrors()
  }
  
  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！ECharts修复成功！')
  } else {
    console.log('⚠️ 部分测试失败，需要进一步检查')
  }
  
  return results
}

// 执行验证
generateTestReport()

// 导出验证函数供手动调用
window.echartsFixVerification = {
  checkEChartsLoading,
  checkVueECharts,
  checkChartContainers,
  testEChartsInit,
  checkConsoleErrors,
  generateTestReport
}
