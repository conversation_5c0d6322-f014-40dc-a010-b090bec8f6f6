<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="#fff" stroke-width="1"/>
  
  <!-- 面试图标 - 人物头像 -->
  <circle cx="16" cy="12" r="4" fill="#fff" opacity="0.9"/>
  
  <!-- 面试图标 - 身体 -->
  <path d="M10 20 Q10 16 16 16 Q22 16 22 20 L22 24 Q22 26 20 26 L12 26 Q10 26 10 24 Z" fill="#fff" opacity="0.9"/>
  
  <!-- AI智能标识 - 小圆点 -->
  <circle cx="24" cy="8" r="3" fill="#ff6b6b"/>
  <circle cx="24" cy="8" r="1.5" fill="#fff"/>
</svg>
