{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-BFZLARZY.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  GitGraphGeneratedModule,\n  MermaidGeneratedSharedModule,\n  __name\n} from \"./chunk-YAJQ3QCK.mjs\";\n\n// src/language/gitGraph/module.ts\nimport {\n  inject,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  EmptyFileSystem\n} from \"langium\";\n\n// src/language/gitGraph/tokenBuilder.ts\nvar GitGraphTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"GitGraphTokenBuilder\");\n  }\n  constructor() {\n    super([\"gitGraph\"]);\n  }\n};\n\n// src/language/gitGraph/module.ts\nvar GitGraphModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new GitGraphTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createGitGraphServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const GitGraph = inject(\n    createDefaultCoreModule({ shared }),\n    GitGraphGeneratedModule,\n    GitGraphModule\n  );\n  shared.ServiceRegistry.register(GitGraph);\n  return { shared, GitGraph };\n}\n__name(createGitGraphServices, \"createGitGraphServices\");\n\nexport {\n  GitGraphModule,\n  createGitGraphServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,IAAI,uBAAuB,cAAc,4BAA4B;AAAA,EAjBrE,OAiBqE;AAAA;AAAA;AAAA,EACnE,OAAO;AACL,IAAAA,QAAO,MAAM,sBAAsB;AAAA,EACrC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,UAAU,CAAC;AAAA,EACpB;AACF;AAGA,IAAI,iBAAiB;AAAA,EACnB,QAAQ;AAAA,IACN,cAA8B,gBAAAA,QAAO,MAAM,IAAI,qBAAqB,GAAG,cAAc;AAAA,IACrF,gBAAgC,gBAAAA,QAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC3F;AACF;AACA,SAAS,uBAAuB,UAAU,iBAAiB;AACzD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,WAAW;AAAA,IACf,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,QAAQ;AACxC,SAAO,EAAE,QAAQ,SAAS;AAC5B;AAZS;AAaTA,QAAO,wBAAwB,wBAAwB;", "names": ["__name"]}