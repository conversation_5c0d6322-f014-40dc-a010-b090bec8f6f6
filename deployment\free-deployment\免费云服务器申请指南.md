# 🆓 免费云服务器申请指南

## 🌟 推荐方案：Oracle Cloud Always Free

### 为什么选择Oracle Cloud？
- ✅ **真正永久免费**（不是试用）
- ✅ **配置强大**：4核24GB ARM 或 1核1GB x86
- ✅ **流量充足**：10TB/月
- ✅ **存储丰富**：200GB块存储
- ✅ **全球节点**：多个地区可选

## 📋 申请步骤

### 第一步：注册Oracle Cloud账号

1. **访问官网**：https://www.oracle.com/cloud/free/
2. **点击"Start for free"**
3. **填写注册信息**：
   - 国家/地区：选择你的实际位置
   - 姓名：使用真实姓名
   - 邮箱：使用常用邮箱
   - 手机号：用于验证

4. **验证邮箱和手机**
5. **选择Home Region**（重要！）：
   - 推荐：Japan East (Tokyo) - 延迟较低
   - 备选：South Korea Central (Seoul)
   - 避免：美国地区（延迟高）

### 第二步：创建免费虚拟机

1. **登录控制台**
2. **导航到 Compute > Instances**
3. **点击"Create Instance"**

#### 配置选项：

**基础配置**
- Name: `iflytek-interview-system`
- Compartment: 保持默认

**Image and Shape**
- Image: `Ubuntu 20.04`
- Shape: 
  - **推荐**：`VM.Standard.A1.Flex` (ARM, 4核24GB)
  - **备选**：`VM.Standard.E2.1.Micro` (x86, 1核1GB)

**Networking**
- VCN: 使用默认或创建新的
- Subnet: 使用默认公共子网
- **重要**：勾选 "Assign a public IPv4 address"

**SSH Keys**
- 选择"Generate SSH key pair"
- **下载私钥文件**（重要！不要丢失）

**Boot Volume**
- Size: 50GB（免费额度内）

4. **点击"Create"**

### 第三步：配置安全规则

1. **进入VCN详情页**
2. **点击默认安全列表**
3. **添加入站规则**：

```
源类型: CIDR
源CIDR: 0.0.0.0/0
IP协议: TCP
目标端口范围: 80
描述: HTTP访问

源类型: CIDR  
源CIDR: 0.0.0.0/0
IP协议: TCP
目标端口范围: 443
描述: HTTPS访问

源类型: CIDR
源CIDR: 0.0.0.0/0  
IP协议: TCP
目标端口范围: 22
描述: SSH访问
```

## 🔑 连接服务器

### Windows用户
1. **下载PuTTY**：https://www.putty.org/
2. **转换密钥格式**：
   - 打开PuTTYgen
   - 加载下载的私钥文件
   - 保存为.ppk格式
3. **连接服务器**：
   - Host: 你的服务器公网IP
   - Port: 22
   - Connection > SSH > Auth: 加载.ppk文件
   - 用户名: `ubuntu`

### Mac/Linux用户
```bash
# 设置密钥权限
chmod 600 your-private-key.pem

# 连接服务器
ssh -i your-private-key.pem ubuntu@your-server-ip
```

## 🚀 部署系统

连接到服务器后，执行以下命令：

```bash
# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 下载部署文件
wget https://github.com/your-repo/archive/main.zip
unzip main.zip
cd cursor_softcup-main/deployment/free-deployment

# 3. 给脚本执行权限
chmod +x free-deploy.sh

# 4. 运行部署脚本
./free-deploy.sh

# 5. 配置iFlytek API
nano .env
# 修改以下内容：
# IFLYTEK_APP_ID=你的APP_ID
# IFLYTEK_API_KEY=你的API_KEY  
# IFLYTEK_API_SECRET=你的API_SECRET

# 6. 重启服务
docker-compose -f docker-compose-free.yml restart
```

## 🌐 访问系统

部署完成后，访问：`http://你的服务器IP`

## 🔧 其他免费选项

### Google Cloud Platform
1. **访问**：https://cloud.google.com/free
2. **免费额度**：$300试用 + Always Free
3. **Always Free配置**：1核0.6GB
4. **需要**：信用卡验证（不会扣费）

### AWS Free Tier  
1. **访问**：https://aws.amazon.com/free/
2. **免费时长**：12个月
3. **配置**：1核1GB
4. **需要**：信用卡验证

### 阿里云学生机
1. **访问**：https://developer.aliyun.com/plan/student
2. **价格**：9.9元/月
3. **配置**：1核2GB
4. **需要**：学生认证

## ⚠️ 注意事项

### Oracle Cloud注意事项
- **地区选择很重要**：一旦选择无法更改
- **保存好SSH密钥**：丢失后无法找回
- **Always Free有限制**：不要超出免费额度
- **账号可能被暂停**：遵守使用条款

### 性能优化建议
- **使用ARM实例**：性能更好
- **启用swap**：增加虚拟内存
- **定期清理**：删除不必要的文件
- **监控资源**：避免超出限制

## 🆘 常见问题

**Q: 申请被拒绝怎么办？**
A: 尝试更换地区，或等待几天后重试

**Q: 连接不上服务器？**
A: 检查安全组规则，确保开放了22端口

**Q: 系统运行缓慢？**
A: 免费服务器性能有限，属于正常现象

**Q: 如何备份数据？**
A: 定期下载 `/app/data` 目录

---

**🎉 恭喜！现在你有了一个永久免费的云服务器来运行你的多模态面试系统！**
