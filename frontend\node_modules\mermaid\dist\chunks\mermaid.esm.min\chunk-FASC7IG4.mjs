import{a as E}from"./chunk-ZN7TASNU.mjs";import{D as y,E as G,F as x,G as P,H as V,J as B,K as k,L as S,N as yr,P as O,T as A,U as Z,d as N,f as _r,h as Q,i as wr,k as R,n as u,p as I,q as z,s as _,u as br,v as Er,x as q,z as L}from"./chunk-5ZJXQJOJ.mjs";import{O as F,T as Y,z as vr}from"./chunk-6BY5RJGC.mjs";import{a as s}from"./chunk-GTKDMUJJ.mjs";function g(r,e,n,t){var o;do o=A(t);while(r.hasNode(o));return n.dummy=e,r.setNode(o,n),o}s(g,"addDummyNode");function xr(r){var e=new E().setGraph(r.graph());return u(r.nodes(),function(n){e.setNode(n,r.node(n))}),u(r.edges(),function(n){var t=e.edge(n.v,n.w)||{weight:0,minlen:1},o=r.edge(n);e.setEdge(n.v,n.w,{weight:t.weight+o.weight,minlen:Math.max(t.minlen,o.minlen)})}),e}s(xr,"simplify");function X(r){var e=new E({multigraph:r.isMultigraph()}).setGraph(r.graph());return u(r.nodes(),function(n){r.children(n).length||e.setNode(n,r.node(n))}),u(r.edges(),function(n){e.setEdge(n,r.edge(n))}),e}s(X,"asNonCompoundGraph");function $(r,e){var n=r.x,t=r.y,o=e.x-n,a=e.y-t,i=r.width/2,f=r.height/2;if(!o&&!a)throw new Error("Not possible to find intersection inside of the rectangle");var d,c;return Math.abs(a)*i>Math.abs(o)*f?(a<0&&(f=-f),d=f*o/a,c=f):(o<0&&(i=-i),d=i,c=i*a/o),{x:n+d,y:t+c}}s($,"intersectRect");function C(r){var e=_(k(er(r)+1),function(){return[]});return u(r.nodes(),function(n){var t=r.node(n),o=t.rank;y(o)||(e[o][t.order]=n)}),e}s(C,"buildLayerMatrix");function kr(r){var e=P(_(r.nodes(),function(n){return r.node(n).rank}));u(r.nodes(),function(n){var t=r.node(n);q(t,"rank")&&(t.rank-=e)})}s(kr,"normalizeRanks");function gr(r){var e=P(_(r.nodes(),function(a){return r.node(a).rank})),n=[];u(r.nodes(),function(a){var i=r.node(a).rank-e;n[i]||(n[i]=[]),n[i].push(a)});var t=0,o=r.graph().nodeRankFactor;u(n,function(a,i){y(a)&&i%o!==0?--t:t&&u(a,function(f){r.node(f).rank+=t})})}s(gr,"removeEmptyRanks");function rr(r,e,n,t){var o={width:0,height:0};return arguments.length>=4&&(o.rank=n,o.order=t),g(r,"border",o,e)}s(rr,"addBorderNode");function er(r){return x(_(r.nodes(),function(e){var n=r.node(e).rank;if(!y(n))return n}))}s(er,"maxRank");function Nr(r,e){var n={lhs:[],rhs:[]};return u(r,function(t){e(t)?n.lhs.push(t):n.rhs.push(t)}),n}s(Nr,"partition");function Ir(r,e){var n=Q();try{return e()}finally{console.log(r+" time: "+(Q()-n)+"ms")}}s(Ir,"time");function Or(r,e){return e()}s(Or,"notime");function Pr(r){function e(n){var t=r.children(n),o=r.node(n);if(t.length&&u(t,e),Object.prototype.hasOwnProperty.call(o,"minRank")){o.borderLeft=[],o.borderRight=[];for(var a=o.minRank,i=o.maxRank+1;a<i;++a)Lr(r,"borderLeft","_bl",n,o,a),Lr(r,"borderRight","_br",n,o,a)}}s(e,"dfs"),u(r.children(),e)}s(Pr,"addBorderSegments");function Lr(r,e,n,t,o,a){var i={width:0,height:0,rank:a,borderType:e},f=o[e][a-1],d=g(r,"border",i,n);o[e][a]=d,r.setParent(d,t),f&&r.setEdge(f,d,{weight:1})}s(Lr,"addBorderNode");function Tr(r){var e=r.graph().rankdir.toLowerCase();(e==="lr"||e==="rl")&&Rr(r)}s(Tr,"adjust");function jr(r){var e=r.graph().rankdir.toLowerCase();(e==="bt"||e==="rl")&&pe(r),(e==="lr"||e==="rl")&&(me(r),Rr(r))}s(jr,"undo");function Rr(r){u(r.nodes(),function(e){Cr(r.node(e))}),u(r.edges(),function(e){Cr(r.edge(e))})}s(Rr,"swapWidthHeight");function Cr(r){var e=r.width;r.width=r.height,r.height=e}s(Cr,"swapWidthHeightOne");function pe(r){u(r.nodes(),function(e){nr(r.node(e))}),u(r.edges(),function(e){var n=r.edge(e);u(n.points,nr),Object.prototype.hasOwnProperty.call(n,"y")&&nr(n)})}s(pe,"reverseY");function nr(r){r.y=-r.y}s(nr,"reverseYOne");function me(r){u(r.nodes(),function(e){tr(r.node(e))}),u(r.edges(),function(e){var n=r.edge(e);u(n.points,tr),Object.prototype.hasOwnProperty.call(n,"x")&&tr(n)})}s(me,"swapXY");function tr(r){var e=r.x;r.x=r.y,r.y=e}s(tr,"swapXYOne");var H=class{static{s(this,"List")}constructor(){var e={};e._next=e._prev=e,this._sentinel=e}dequeue(){var e=this._sentinel,n=e._prev;if(n!==e)return Sr(n),n}enqueue(e){var n=this._sentinel;e._prev&&e._next&&Sr(e),e._next=n._next,n._next._prev=e,n._next=e,e._prev=n}toString(){for(var e=[],n=this._sentinel,t=n._prev;t!==n;)e.push(JSON.stringify(t,_e)),t=t._prev;return"["+e.join(", ")+"]"}};function Sr(r){r._prev._next=r._next,r._next._prev=r._prev,delete r._next,delete r._prev}s(Sr,"unlink");function _e(r,e){if(r!=="_next"&&r!=="_prev")return e}s(_e,"filterOutLinks");var we=F(1);function Mr(r,e){if(r.nodeCount()<=1)return[];var n=Ee(r,e||we),t=be(n.graph,n.buckets,n.zeroIdx);return N(_(t,function(o){return r.outEdges(o.v,o.w)}))}s(Mr,"greedyFAS");function be(r,e,n){for(var t=[],o=e[e.length-1],a=e[0],i;r.nodeCount();){for(;i=a.dequeue();)or(r,e,n,i);for(;i=o.dequeue();)or(r,e,n,i);if(r.nodeCount()){for(var f=e.length-2;f>0;--f)if(i=e[f].dequeue(),i){t=t.concat(or(r,e,n,i,!0));break}}}return t}s(be,"doGreedyFAS");function or(r,e,n,t,o){var a=o?[]:void 0;return u(r.inEdges(t.v),function(i){var f=r.edge(i),d=r.node(i.v);o&&a.push({v:i.v,w:i.w}),d.out-=f,ar(e,n,d)}),u(r.outEdges(t.v),function(i){var f=r.edge(i),d=i.w,c=r.node(d);c.in-=f,ar(e,n,c)}),r.removeNode(t.v),a}s(or,"removeNode");function Ee(r,e){var n=new E,t=0,o=0;u(r.nodes(),function(f){n.setNode(f,{v:f,in:0,out:0})}),u(r.edges(),function(f){var d=n.edge(f.v,f.w)||0,c=e(f),h=d+c;n.setEdge(f.v,f.w,h),o=Math.max(o,n.node(f.v).out+=c),t=Math.max(t,n.node(f.w).in+=c)});var a=k(o+t+3).map(function(){return new H}),i=t+1;return u(n.nodes(),function(f){ar(a,i,n.node(f))}),{graph:n,buckets:a,zeroIdx:i}}s(Ee,"buildState");function ar(r,e,n){n.out?n.in?r[n.out-n.in+e].enqueue(n):r[r.length-1].enqueue(n):r[0].enqueue(n)}s(ar,"assignBucket");function Fr(r){var e=r.graph().acyclicer==="greedy"?Mr(r,n(r)):ye(r);u(e,function(t){var o=r.edge(t);r.removeEdge(t),o.forwardName=t.name,o.reversed=!0,r.setEdge(t.w,t.v,o,A("rev"))});function n(t){return function(o){return t.edge(o).weight}}s(n,"weightFn")}s(Fr,"run");function ye(r){var e=[],n={},t={};function o(a){Object.prototype.hasOwnProperty.call(t,a)||(t[a]=!0,n[a]=!0,u(r.outEdges(a),function(i){Object.prototype.hasOwnProperty.call(n,i.w)?e.push(i):o(i.w)}),delete n[a])}return s(o,"dfs"),u(r.nodes(),o),e}s(ye,"dfsFAS");function Gr(r){u(r.edges(),function(e){var n=r.edge(e);if(n.reversed){r.removeEdge(e);var t=n.forwardName;delete n.reversed,delete n.forwardName,r.setEdge(e.w,e.v,n,t)}})}s(Gr,"undo");function Br(r){r.graph().dummyChains=[],u(r.edges(),function(e){xe(r,e)})}s(Br,"run");function xe(r,e){var n=e.v,t=r.node(n).rank,o=e.w,a=r.node(o).rank,i=e.name,f=r.edge(e),d=f.labelRank;if(a!==t+1){r.removeEdge(e);var c=void 0,h,l;for(l=0,++t;t<a;++l,++t)f.points=[],c={width:0,height:0,edgeLabel:f,edgeObj:e,rank:t},h=g(r,"edge",c,"_d"),t===d&&(c.width=f.width,c.height=f.height,c.dummy="edge-label",c.labelpos=f.labelpos),r.setEdge(n,h,{weight:f.weight},i),l===0&&r.graph().dummyChains.push(h),n=h;r.setEdge(n,o,{weight:f.weight},i)}}s(xe,"normalizeEdge");function Ar(r){u(r.graph().dummyChains,function(e){var n=r.node(e),t=n.edgeLabel,o;for(r.setEdge(n.edgeObj,t);n.dummy;)o=r.successors(e)[0],r.removeNode(e),t.points.push({x:n.x,y:n.y}),n.dummy==="edge-label"&&(t.x=n.x,t.y=n.y,t.width=n.width,t.height=n.height),e=o,n=r.node(e)})}s(Ar,"undo");function U(r){var e={};function n(t){var o=r.node(t);if(Object.prototype.hasOwnProperty.call(e,t))return o.rank;e[t]=!0;var a=P(_(r.outEdges(t),function(i){return n(i.w)-r.edge(i).minlen}));return(a===Number.POSITIVE_INFINITY||a===void 0||a===null)&&(a=0),o.rank=a}s(n,"dfs"),u(r.sources(),n)}s(U,"longestPath");function M(r,e){return r.node(e.w).rank-r.node(e.v).rank-r.edge(e).minlen}s(M,"slack");function J(r){var e=new E({directed:!1}),n=r.nodes()[0],t=r.nodeCount();e.setNode(n,{});for(var o,a;ke(e,r)<t;)o=ge(e,r),a=e.hasNode(o.v)?M(r,o):-M(r,o),Ne(e,r,a);return e}s(J,"feasibleTree");function ke(r,e){function n(t){u(e.nodeEdges(t),function(o){var a=o.v,i=t===a?o.w:a;!r.hasNode(i)&&!M(e,o)&&(r.setNode(i,{}),r.setEdge(t,i,{}),n(i))})}return s(n,"dfs"),u(r.nodes(),n),r.nodeCount()}s(ke,"tightTree");function ge(r,e){return V(e.edges(),function(n){if(r.hasNode(n.v)!==r.hasNode(n.w))return M(e,n)})}s(ge,"findMinSlackEdge");function Ne(r,e,n){u(r.nodes(),function(t){e.node(t).rank+=n})}s(Ne,"shiftRanks");var tt=F(1);var pt=F(1);ir.CycleException=W;function ir(r){var e={},n={},t=[];function o(a){if(Object.prototype.hasOwnProperty.call(n,a))throw new W;Object.prototype.hasOwnProperty.call(e,a)||(n[a]=!0,e[a]=!0,u(r.predecessors(a),o),delete n[a],t.push(a))}if(s(o,"visit"),u(r.sinks(),o),yr(e)!==r.nodeCount())throw new W;return t}s(ir,"topsort");function W(){}s(W,"CycleException");W.prototype=new Error;function K(r,e,n){vr(e)||(e=[e]);var t=(r.isDirected()?r.successors:r.neighbors).bind(r),o=[],a={};return u(e,function(i){if(!r.hasNode(i))throw new Error("Graph does not have node: "+i);Yr(r,i,n==="post",a,t,o)}),o}s(K,"dfs");function Yr(r,e,n,t,o,a){Object.prototype.hasOwnProperty.call(t,e)||(t[e]=!0,n||a.push(e),u(o(e),function(i){Yr(r,i,n,t,o,a)}),n&&a.push(e))}s(Yr,"doDfs");function sr(r,e){return K(r,e,"post")}s(sr,"postorder");function fr(r,e){return K(r,e,"pre")}s(fr,"preorder");j.initLowLimValues=dr;j.initCutValues=ur;j.calcCutValue=Ur;j.leaveEdge=qr;j.enterEdge=Xr;j.exchangeEdges=Hr;function j(r){r=xr(r),U(r);var e=J(r);dr(e),ur(e,r);for(var n,t;n=qr(e);)t=Xr(e,r,n),Hr(e,r,n,t)}s(j,"networkSimplex");function ur(r,e){var n=sr(r,r.nodes());n=n.slice(0,n.length-1),u(n,function(t){Ce(r,e,t)})}s(ur,"initCutValues");function Ce(r,e,n){var t=r.node(n),o=t.parent;r.edge(n,o).cutvalue=Ur(r,e,n)}s(Ce,"assignCutValue");function Ur(r,e,n){var t=r.node(n),o=t.parent,a=!0,i=e.edge(n,o),f=0;return i||(a=!1,i=e.edge(o,n)),f=i.weight,u(e.nodeEdges(n),function(d){var c=d.v===n,h=c?d.w:d.v;if(h!==o){var l=c===a,p=e.edge(d).weight;if(f+=l?p:-p,je(r,n,h)){var m=r.edge(n,h).cutvalue;f+=l?-m:m}}}),f}s(Ur,"calcCutValue");function dr(r,e){arguments.length<2&&(e=r.nodes()[0]),Wr(r,{},1,e)}s(dr,"initLowLimValues");function Wr(r,e,n,t,o){var a=n,i=r.node(t);return e[t]=!0,u(r.neighbors(t),function(f){Object.prototype.hasOwnProperty.call(e,f)||(n=Wr(r,e,n,f,t))}),i.low=a,i.lim=n++,o?i.parent=o:delete i.parent,n}s(Wr,"dfsAssignLowLim");function qr(r){return z(r.edges(),function(e){return r.edge(e).cutvalue<0})}s(qr,"leaveEdge");function Xr(r,e,n){var t=n.v,o=n.w;e.hasEdge(t,o)||(t=n.w,o=n.v);var a=r.node(t),i=r.node(o),f=a,d=!1;a.lim>i.lim&&(f=i,d=!0);var c=I(e.edges(),function(h){return d===zr(r,r.node(h.v),f)&&d!==zr(r,r.node(h.w),f)});return V(c,function(h){return M(e,h)})}s(Xr,"enterEdge");function Hr(r,e,n,t){var o=n.v,a=n.w;r.removeEdge(o,a),r.setEdge(t.v,t.w,{}),dr(r),ur(r,e),Te(r,e)}s(Hr,"exchangeEdges");function Te(r,e){var n=z(r.nodes(),function(o){return!e.node(o).parent}),t=fr(r,n);t=t.slice(1),u(t,function(o){var a=r.node(o).parent,i=e.edge(o,a),f=!1;i||(i=e.edge(a,o),f=!0),e.node(o).rank=e.node(a).rank+(f?i.minlen:-i.minlen)})}s(Te,"updateRanks");function je(r,e,n){return r.hasEdge(e,n)}s(je,"isTreeEdge");function zr(r,e,n){return n.low<=e.lim&&e.lim<=n.lim}s(zr,"isDescendant");function cr(r){switch(r.graph().ranker){case"network-simplex":Jr(r);break;case"tight-tree":Se(r);break;case"longest-path":Re(r);break;default:Jr(r)}}s(cr,"rank");var Re=U;function Se(r){U(r),J(r)}s(Se,"tightTreeRanker");function Jr(r){j(r)}s(Jr,"networkSimplexRanker");function Kr(r){var e=g(r,"root",{},"_root"),n=Me(r),t=x(L(n))-1,o=2*t+1;r.graph().nestingRoot=e,u(r.edges(),function(i){r.edge(i).minlen*=o});var a=Fe(r)+1;u(r.children(),function(i){Qr(r,e,o,a,t,n,i)}),r.graph().nodeRankFactor=o}s(Kr,"run");function Qr(r,e,n,t,o,a,i){var f=r.children(i);if(!f.length){i!==e&&r.setEdge(e,i,{weight:0,minlen:n});return}var d=rr(r,"_bt"),c=rr(r,"_bb"),h=r.node(i);r.setParent(d,i),h.borderTop=d,r.setParent(c,i),h.borderBottom=c,u(f,function(l){Qr(r,e,n,t,o,a,l);var p=r.node(l),m=p.borderTop?p.borderTop:l,v=p.borderBottom?p.borderBottom:l,b=p.borderTop?t:2*t,D=m!==v?1:o-a[i]+1;r.setEdge(d,m,{weight:b,minlen:D,nestingEdge:!0}),r.setEdge(v,c,{weight:b,minlen:D,nestingEdge:!0})}),r.parent(i)||r.setEdge(e,d,{weight:0,minlen:o+a[i]})}s(Qr,"dfs");function Me(r){var e={};function n(t,o){var a=r.children(t);a&&a.length&&u(a,function(i){n(i,o+1)}),e[t]=o}return s(n,"dfs"),u(r.children(),function(t){n(t,1)}),e}s(Me,"treeDepths");function Fe(r){return S(r.edges(),function(e,n){return e+r.edge(n).weight},0)}s(Fe,"sumWeights");function Zr(r){var e=r.graph();r.removeNode(e.nestingRoot),delete e.nestingRoot,u(r.edges(),function(n){var t=r.edge(n);t.nestingEdge&&r.removeEdge(n)})}s(Zr,"cleanup");function $r(r,e,n){var t={},o;u(n,function(a){for(var i=r.parent(a),f,d;i;){if(f=r.parent(i),f?(d=t[f],t[f]=i):(d=o,o=i),d&&d!==i){e.setEdge(d,i);return}i=f}})}s($r,"addSubgraphConstraints");function re(r,e,n){var t=Ve(r),o=new E({compound:!0}).setGraph({root:t}).setDefaultNodeLabel(function(a){return r.node(a)});return u(r.nodes(),function(a){var i=r.node(a),f=r.parent(a);(i.rank===e||i.minRank<=e&&e<=i.maxRank)&&(o.setNode(a),o.setParent(a,f||t),u(r[n](a),function(d){var c=d.v===a?d.w:d.v,h=o.edge(c,a),l=y(h)?0:h.weight;o.setEdge(c,a,{weight:r.edge(d).weight+l})}),Object.prototype.hasOwnProperty.call(i,"minRank")&&o.setNode(a,{borderLeft:i.borderLeft[e],borderRight:i.borderRight[e]}))}),o}s(re,"buildLayerGraph");function Ve(r){for(var e;r.hasNode(e=A("_root")););return e}s(Ve,"createRootNode");function ee(r,e){for(var n=0,t=1;t<e.length;++t)n+=Be(r,e[t-1],e[t]);return n}s(ee,"crossCount");function Be(r,e,n){for(var t=Z(n,_(n,function(c,h){return h})),o=N(_(e,function(c){return O(_(r.outEdges(c),function(h){return{pos:t[h.w],weight:r.edge(h).weight}}),"pos")})),a=1;a<n.length;)a<<=1;var i=2*a-1;a-=1;var f=_(new Array(i),function(){return 0}),d=0;return u(o.forEach(function(c){var h=c.pos+a;f[h]+=c.weight;for(var l=0;h>0;)h%2&&(l+=f[h+1]),h=h-1>>1,f[h]+=c.weight;d+=c.weight*l})),d}s(Be,"twoLayerCrossCount");function ne(r){var e={},n=I(r.nodes(),function(f){return!r.children(f).length}),t=x(_(n,function(f){return r.node(f).rank})),o=_(k(t+1),function(){return[]});function a(f){if(!q(e,f)){e[f]=!0;var d=r.node(f);o[d.rank].push(f),u(r.successors(f),a)}}s(a,"dfs");var i=O(n,function(f){return r.node(f).rank});return u(i,a),o}s(ne,"initOrder");function te(r,e){return _(e,function(n){var t=r.inEdges(n);if(t.length){var o=S(t,function(a,i){var f=r.edge(i),d=r.node(i.v);return{sum:a.sum+f.weight*d.order,weight:a.weight+f.weight}},{sum:0,weight:0});return{v:n,barycenter:o.sum/o.weight,weight:o.weight}}else return{v:n}})}s(te,"barycenter");function oe(r,e){var n={};u(r,function(o,a){var i=n[o.v]={indegree:0,in:[],out:[],vs:[o.v],i:a};y(o.barycenter)||(i.barycenter=o.barycenter,i.weight=o.weight)}),u(e.edges(),function(o){var a=n[o.v],i=n[o.w];!y(a)&&!y(i)&&(i.indegree++,a.out.push(n[o.w]))});var t=I(n,function(o){return!o.indegree});return Ae(t)}s(oe,"resolveConflicts");function Ae(r){var e=[];function n(a){return function(i){i.merged||(y(i.barycenter)||y(a.barycenter)||i.barycenter>=a.barycenter)&&De(a,i)}}s(n,"handleIn");function t(a){return function(i){i.in.push(a),--i.indegree===0&&r.push(i)}}for(s(t,"handleOut");r.length;){var o=r.pop();e.push(o),u(o.in.reverse(),n(o)),u(o.out,t(o))}return _(I(e,function(a){return!a.merged}),function(a){return B(a,["vs","i","barycenter","weight"])})}s(Ae,"doResolveConflicts");function De(r,e){var n=0,t=0;r.weight&&(n+=r.barycenter*r.weight,t+=r.weight),e.weight&&(n+=e.barycenter*e.weight,t+=e.weight),r.vs=e.vs.concat(r.vs),r.barycenter=n/t,r.weight=t,r.i=Math.min(e.i,r.i),e.merged=!0}s(De,"mergeEntries");function ie(r,e){var n=Nr(r,function(h){return Object.prototype.hasOwnProperty.call(h,"barycenter")}),t=n.lhs,o=O(n.rhs,function(h){return-h.i}),a=[],i=0,f=0,d=0;t.sort(Ye(!!e)),d=ae(a,o,d),u(t,function(h){d+=h.vs.length,a.push(h.vs),i+=h.barycenter*h.weight,f+=h.weight,d=ae(a,o,d)});var c={vs:N(a)};return f&&(c.barycenter=i/f,c.weight=f),c}s(ie,"sort");function ae(r,e,n){for(var t;e.length&&(t=R(e)).i<=n;)e.pop(),r.push(t.vs),n++;return n}s(ae,"consumeUnsortable");function Ye(r){return function(e,n){return e.barycenter<n.barycenter?-1:e.barycenter>n.barycenter?1:r?n.i-e.i:e.i-n.i}}s(Ye,"compareWithBias");function hr(r,e,n,t){var o=r.children(e),a=r.node(e),i=a?a.borderLeft:void 0,f=a?a.borderRight:void 0,d={};i&&(o=I(o,function(v){return v!==i&&v!==f}));var c=te(r,o);u(c,function(v){if(r.children(v.v).length){var b=hr(r,v.v,n,t);d[v.v]=b,Object.prototype.hasOwnProperty.call(b,"barycenter")&&Ue(v,b)}});var h=oe(c,n);ze(h,d);var l=ie(h,t);if(i&&(l.vs=N([i,l.vs,f]),r.predecessors(i).length)){var p=r.node(r.predecessors(i)[0]),m=r.node(r.predecessors(f)[0]);Object.prototype.hasOwnProperty.call(l,"barycenter")||(l.barycenter=0,l.weight=0),l.barycenter=(l.barycenter*l.weight+p.order+m.order)/(l.weight+2),l.weight+=2}return l}s(hr,"sortSubgraph");function ze(r,e){u(r,function(n){n.vs=N(n.vs.map(function(t){return e[t]?e[t].vs:t}))})}s(ze,"expandSubgraphs");function Ue(r,e){y(r.barycenter)?(r.barycenter=e.barycenter,r.weight=e.weight):(r.barycenter=(r.barycenter*r.weight+e.barycenter*e.weight)/(r.weight+e.weight),r.weight+=e.weight)}s(Ue,"mergeBarycenters");function ue(r){var e=er(r),n=se(r,k(1,e+1),"inEdges"),t=se(r,k(e-1,-1,-1),"outEdges"),o=ne(r);fe(r,o);for(var a=Number.POSITIVE_INFINITY,i,f=0,d=0;d<4;++f,++d){We(f%2?n:t,f%4>=2),o=C(r);var c=ee(r,o);c<a&&(d=0,i=_r(o),a=c)}fe(r,i)}s(ue,"order");function se(r,e,n){return _(e,function(t){return re(r,t,n)})}s(se,"buildLayerGraphs");function We(r,e){var n=new E;u(r,function(t){var o=t.graph().root,a=hr(t,o,n,e);u(a.vs,function(i,f){t.node(i).order=f}),$r(t,n,a.vs)})}s(We,"sweepLayerGraphs");function fe(r,e){u(e,function(n){u(n,function(t,o){r.node(t).order=o})})}s(fe,"assignOrder");function de(r){var e=Xe(r);u(r.graph().dummyChains,function(n){for(var t=r.node(n),o=t.edgeObj,a=qe(r,e,o.v,o.w),i=a.path,f=a.lca,d=0,c=i[d],h=!0;n!==o.w;){if(t=r.node(n),h){for(;(c=i[d])!==f&&r.node(c).maxRank<t.rank;)d++;c===f&&(h=!1)}if(!h){for(;d<i.length-1&&r.node(c=i[d+1]).minRank<=t.rank;)d++;c=i[d]}r.setParent(n,c),n=r.successors(n)[0]}})}s(de,"parentDummyChains");function qe(r,e,n,t){var o=[],a=[],i=Math.min(e[n].low,e[t].low),f=Math.max(e[n].lim,e[t].lim),d,c;d=n;do d=r.parent(d),o.push(d);while(d&&(e[d].low>i||f>e[d].lim));for(c=d,d=t;(d=r.parent(d))!==c;)a.push(d);return{path:o.concat(a.reverse()),lca:c}}s(qe,"findPath");function Xe(r){var e={},n=0;function t(o){var a=n;u(r.children(o),t),e[o]={low:a,lim:n++}}return s(t,"dfs"),u(r.children(),t),e}s(Xe,"postorder");function He(r,e){var n={};function t(o,a){var i=0,f=0,d=o.length,c=R(a);return u(a,function(h,l){var p=Ke(r,h),m=p?r.node(p).order:d;(p||h===c)&&(u(a.slice(f,l+1),function(v){u(r.predecessors(v),function(b){var D=r.node(b),mr=D.order;(mr<i||m<mr)&&!(D.dummy&&r.node(v).dummy)&&ce(n,b,v)})}),f=l+1,i=m)}),a}return s(t,"visitLayer"),S(e,t),n}s(He,"findType1Conflicts");function Je(r,e){var n={};function t(a,i,f,d,c){var h;u(k(i,f),function(l){h=a[l],r.node(h).dummy&&u(r.predecessors(h),function(p){var m=r.node(p);m.dummy&&(m.order<d||m.order>c)&&ce(n,p,h)})})}s(t,"scan");function o(a,i){var f=-1,d,c=0;return u(i,function(h,l){if(r.node(h).dummy==="border"){var p=r.predecessors(h);p.length&&(d=r.node(p[0]).order,t(i,c,l,f,d),c=l,f=d)}t(i,c,i.length,d,a.length)}),i}return s(o,"visitLayer"),S(e,o),n}s(Je,"findType2Conflicts");function Ke(r,e){if(r.node(e).dummy)return z(r.predecessors(e),function(n){return r.node(n).dummy})}s(Ke,"findOtherInnerSegmentNode");function ce(r,e,n){if(e>n){var t=e;e=n,n=t}var o=r[e];o||(r[e]=o={}),o[n]=!0}s(ce,"addConflict");function Qe(r,e,n){if(e>n){var t=e;e=n,n=t}return!!r[e]&&Object.prototype.hasOwnProperty.call(r[e],n)}s(Qe,"hasConflict");function Ze(r,e,n,t){var o={},a={},i={};return u(e,function(f){u(f,function(d,c){o[d]=d,a[d]=d,i[d]=c})}),u(e,function(f){var d=-1;u(f,function(c){var h=t(c);if(h.length){h=O(h,function(b){return i[b]});for(var l=(h.length-1)/2,p=Math.floor(l),m=Math.ceil(l);p<=m;++p){var v=h[p];a[c]===c&&d<i[v]&&!Qe(n,c,v)&&(a[v]=c,a[c]=o[c]=o[v],d=i[v])}}})}),{root:o,align:a}}s(Ze,"verticalAlignment");function $e(r,e,n,t,o){var a={},i=rn(r,e,n,o),f=o?"borderLeft":"borderRight";function d(l,p){for(var m=i.nodes(),v=m.pop(),b={};v;)b[v]?l(v):(b[v]=!0,m.push(v),m=m.concat(p(v))),v=m.pop()}s(d,"iterate");function c(l){a[l]=i.inEdges(l).reduce(function(p,m){return Math.max(p,a[m.v]+i.edge(m))},0)}s(c,"pass1");function h(l){var p=i.outEdges(l).reduce(function(v,b){return Math.min(v,a[b.w]-i.edge(b))},Number.POSITIVE_INFINITY),m=r.node(l);p!==Number.POSITIVE_INFINITY&&m.borderType!==f&&(a[l]=Math.max(a[l],p))}return s(h,"pass2"),d(c,i.predecessors.bind(i)),d(h,i.successors.bind(i)),u(t,function(l){a[l]=a[n[l]]}),a}s($e,"horizontalCompaction");function rn(r,e,n,t){var o=new E,a=r.graph(),i=on(a.nodesep,a.edgesep,t);return u(e,function(f){var d;u(f,function(c){var h=n[c];if(o.setNode(h),d){var l=n[d],p=o.edge(l,h);o.setEdge(l,h,Math.max(i(r,c,d),p||0))}d=c})}),o}s(rn,"buildBlockGraph");function en(r,e){return V(L(e),function(n){var t=Number.NEGATIVE_INFINITY,o=Number.POSITIVE_INFINITY;return br(n,function(a,i){var f=an(r,i)/2;t=Math.max(a+f,t),o=Math.min(a-f,o)}),t-o})}s(en,"findSmallestWidthAlignment");function nn(r,e){var n=L(e),t=P(n),o=x(n);u(["u","d"],function(a){u(["l","r"],function(i){var f=a+i,d=r[f],c;if(d!==e){var h=L(d);c=i==="l"?t-P(h):o-x(h),c&&(r[f]=G(d,function(l){return l+c}))}})})}s(nn,"alignCoordinates");function tn(r,e){return G(r.ul,function(n,t){if(e)return r[e.toLowerCase()][t];var o=O(_(r,t));return(o[1]+o[2])/2})}s(tn,"balance");function he(r){var e=C(r),n=Y(He(r,e),Je(r,e)),t={},o;u(["u","d"],function(i){o=i==="u"?e:L(e).reverse(),u(["l","r"],function(f){f==="r"&&(o=_(o,function(l){return L(l).reverse()}));var d=(i==="u"?r.predecessors:r.successors).bind(r),c=Ze(r,o,n,d),h=$e(r,o,c.root,c.align,f==="r");f==="r"&&(h=G(h,function(l){return-l})),t[i+f]=h})});var a=en(r,t);return nn(t,a),tn(t,r.graph().align)}s(he,"positionX");function on(r,e,n){return function(t,o,a){var i=t.node(o),f=t.node(a),d=0,c;if(d+=i.width/2,Object.prototype.hasOwnProperty.call(i,"labelpos"))switch(i.labelpos.toLowerCase()){case"l":c=-i.width/2;break;case"r":c=i.width/2;break}if(c&&(d+=n?c:-c),c=0,d+=(i.dummy?e:r)/2,d+=(f.dummy?e:r)/2,d+=f.width/2,Object.prototype.hasOwnProperty.call(f,"labelpos"))switch(f.labelpos.toLowerCase()){case"l":c=f.width/2;break;case"r":c=-f.width/2;break}return c&&(d+=n?c:-c),c=0,d}}s(on,"sep");function an(r,e){return r.node(e).width}s(an,"width");function le(r){r=X(r),sn(r),Er(he(r),function(e,n){r.node(n).x=e})}s(le,"position");function sn(r){var e=C(r),n=r.graph().ranksep,t=0;u(e,function(o){var a=x(_(o,function(i){return r.node(i).height}));u(o,function(i){r.node(i).y=t+a/2}),t+=a+n})}s(sn,"positionY");function fn(r,e){var n=e&&e.debugTiming?Ir:Or;n("layout",()=>{var t=n("  buildLayoutGraph",()=>bn(r));n("  runLayout",()=>un(t,n)),n("  updateInputGraph",()=>dn(r,t))})}s(fn,"layout");function un(r,e){e("    makeSpaceForEdgeLabels",()=>En(r)),e("    removeSelfEdges",()=>Pn(r)),e("    acyclic",()=>Fr(r)),e("    nestingGraph.run",()=>Kr(r)),e("    rank",()=>cr(X(r))),e("    injectEdgeLabelProxies",()=>yn(r)),e("    removeEmptyRanks",()=>gr(r)),e("    nestingGraph.cleanup",()=>Zr(r)),e("    normalizeRanks",()=>kr(r)),e("    assignRankMinMax",()=>xn(r)),e("    removeEdgeLabelProxies",()=>kn(r)),e("    normalize.run",()=>Br(r)),e("    parentDummyChains",()=>de(r)),e("    addBorderSegments",()=>Pr(r)),e("    order",()=>ue(r)),e("    insertSelfEdges",()=>Cn(r)),e("    adjustCoordinateSystem",()=>Tr(r)),e("    position",()=>le(r)),e("    positionSelfEdges",()=>Tn(r)),e("    removeBorderNodes",()=>Ln(r)),e("    normalize.undo",()=>Ar(r)),e("    fixupEdgeLabelCoords",()=>In(r)),e("    undoCoordinateSystem",()=>jr(r)),e("    translateGraph",()=>gn(r)),e("    assignNodeIntersects",()=>Nn(r)),e("    reversePoints",()=>On(r)),e("    acyclic.undo",()=>Gr(r))}s(un,"runLayout");function dn(r,e){u(r.nodes(),function(n){var t=r.node(n),o=e.node(n);t&&(t.x=o.x,t.y=o.y,e.children(n).length&&(t.width=o.width,t.height=o.height))}),u(r.edges(),function(n){var t=r.edge(n),o=e.edge(n);t.points=o.points,Object.prototype.hasOwnProperty.call(o,"x")&&(t.x=o.x,t.y=o.y)}),r.graph().width=e.graph().width,r.graph().height=e.graph().height}s(dn,"updateInputGraph");var cn=["nodesep","edgesep","ranksep","marginx","marginy"],hn={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},ln=["acyclicer","ranker","rankdir","align"],pn=["width","height"],mn={width:0,height:0},vn=["minlen","weight","width","height","labeloffset"],_n={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},wn=["labelpos"];function bn(r){var e=new E({multigraph:!0,compound:!0}),n=pr(r.graph());return e.setGraph(Y({},hn,lr(n,cn),B(n,ln))),u(r.nodes(),function(t){var o=pr(r.node(t));e.setNode(t,wr(lr(o,pn),mn)),e.setParent(t,r.parent(t))}),u(r.edges(),function(t){var o=pr(r.edge(t));e.setEdge(t,Y({},_n,lr(o,vn),B(o,wn)))}),e}s(bn,"buildLayoutGraph");function En(r){var e=r.graph();e.ranksep/=2,u(r.edges(),function(n){var t=r.edge(n);t.minlen*=2,t.labelpos.toLowerCase()!=="c"&&(e.rankdir==="TB"||e.rankdir==="BT"?t.width+=t.labeloffset:t.height+=t.labeloffset)})}s(En,"makeSpaceForEdgeLabels");function yn(r){u(r.edges(),function(e){var n=r.edge(e);if(n.width&&n.height){var t=r.node(e.v),o=r.node(e.w),a={rank:(o.rank-t.rank)/2+t.rank,e};g(r,"edge-proxy",a,"_ep")}})}s(yn,"injectEdgeLabelProxies");function xn(r){var e=0;u(r.nodes(),function(n){var t=r.node(n);t.borderTop&&(t.minRank=r.node(t.borderTop).rank,t.maxRank=r.node(t.borderBottom).rank,e=x(e,t.maxRank))}),r.graph().maxRank=e}s(xn,"assignRankMinMax");function kn(r){u(r.nodes(),function(e){var n=r.node(e);n.dummy==="edge-proxy"&&(r.edge(n.e).labelRank=n.rank,r.removeNode(e))})}s(kn,"removeEdgeLabelProxies");function gn(r){var e=Number.POSITIVE_INFINITY,n=0,t=Number.POSITIVE_INFINITY,o=0,a=r.graph(),i=a.marginx||0,f=a.marginy||0;function d(c){var h=c.x,l=c.y,p=c.width,m=c.height;e=Math.min(e,h-p/2),n=Math.max(n,h+p/2),t=Math.min(t,l-m/2),o=Math.max(o,l+m/2)}s(d,"getExtremes"),u(r.nodes(),function(c){d(r.node(c))}),u(r.edges(),function(c){var h=r.edge(c);Object.prototype.hasOwnProperty.call(h,"x")&&d(h)}),e-=i,t-=f,u(r.nodes(),function(c){var h=r.node(c);h.x-=e,h.y-=t}),u(r.edges(),function(c){var h=r.edge(c);u(h.points,function(l){l.x-=e,l.y-=t}),Object.prototype.hasOwnProperty.call(h,"x")&&(h.x-=e),Object.prototype.hasOwnProperty.call(h,"y")&&(h.y-=t)}),a.width=n-e+i,a.height=o-t+f}s(gn,"translateGraph");function Nn(r){u(r.edges(),function(e){var n=r.edge(e),t=r.node(e.v),o=r.node(e.w),a,i;n.points?(a=n.points[0],i=n.points[n.points.length-1]):(n.points=[],a=o,i=t),n.points.unshift($(t,a)),n.points.push($(o,i))})}s(Nn,"assignNodeIntersects");function In(r){u(r.edges(),function(e){var n=r.edge(e);if(Object.prototype.hasOwnProperty.call(n,"x"))switch((n.labelpos==="l"||n.labelpos==="r")&&(n.width-=n.labeloffset),n.labelpos){case"l":n.x-=n.width/2+n.labeloffset;break;case"r":n.x+=n.width/2+n.labeloffset;break}})}s(In,"fixupEdgeLabelCoords");function On(r){u(r.edges(),function(e){var n=r.edge(e);n.reversed&&n.points.reverse()})}s(On,"reversePointsForReversedEdges");function Ln(r){u(r.nodes(),function(e){if(r.children(e).length){var n=r.node(e),t=r.node(n.borderTop),o=r.node(n.borderBottom),a=r.node(R(n.borderLeft)),i=r.node(R(n.borderRight));n.width=Math.abs(i.x-a.x),n.height=Math.abs(o.y-t.y),n.x=a.x+n.width/2,n.y=t.y+n.height/2}}),u(r.nodes(),function(e){r.node(e).dummy==="border"&&r.removeNode(e)})}s(Ln,"removeBorderNodes");function Pn(r){u(r.edges(),function(e){if(e.v===e.w){var n=r.node(e.v);n.selfEdges||(n.selfEdges=[]),n.selfEdges.push({e,label:r.edge(e)}),r.removeEdge(e)}})}s(Pn,"removeSelfEdges");function Cn(r){var e=C(r);u(e,function(n){var t=0;u(n,function(o,a){var i=r.node(o);i.order=a+t,u(i.selfEdges,function(f){g(r,"selfedge",{width:f.label.width,height:f.label.height,rank:i.rank,order:a+ ++t,e:f.e,label:f.label},"_se")}),delete i.selfEdges})})}s(Cn,"insertSelfEdges");function Tn(r){u(r.nodes(),function(e){var n=r.node(e);if(n.dummy==="selfedge"){var t=r.node(n.e.v),o=t.x+t.width/2,a=t.y,i=n.x-o,f=t.height/2;r.setEdge(n.e,n.label),r.removeNode(e),n.label.points=[{x:o+2*i/3,y:a-f},{x:o+5*i/6,y:a-f},{x:o+i,y:a},{x:o+5*i/6,y:a+f},{x:o+2*i/3,y:a+f}],n.label.x=n.x,n.label.y=n.y}})}s(Tn,"positionSelfEdges");function lr(r,e){return G(B(r,e),Number)}s(lr,"selectNumberAttrs");function pr(r){var e={};return u(r,function(n,t){e[t.toLowerCase()]=n}),e}s(pr,"canonicalize");export{fn as a};
