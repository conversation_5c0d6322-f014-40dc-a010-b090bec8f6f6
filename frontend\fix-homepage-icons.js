#!/usr/bin/env node

/**
 * 批量修复HomePage.vue中的无效图标
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 图标替换映射
const iconReplacements = {
  'Lightning': 'Star',
  'OfficeBuilding': 'House', 
  'Monitor': 'Grid',
  'MagicStick': 'Setting',
  'Microphone': 'Timer'
};

function fixHomepageIcons() {
    const filePath = path.join(__dirname, 'src/views/HomePage.vue');
    
    if (!fs.existsSync(filePath)) {
        console.error(`❌ 文件不存在: ${filePath}`);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let changeCount = 0;
    
    console.log('🔧 开始修复HomePage.vue中的无效图标...\n');
    
    // 替换图标使用
    Object.entries(iconReplacements).forEach(([oldIcon, newIcon]) => {
        const regex = new RegExp(`<${oldIcon}\\s*/>`, 'g');
        const matches = content.match(regex);
        
        if (matches) {
            console.log(`📝 替换 ${oldIcon} → ${newIcon} (${matches.length} 处)`);
            content = content.replace(regex, `<${newIcon} />`);
            changeCount += matches.length;
        }
    });
    
    // 写回文件
    if (changeCount > 0) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`\n✅ 修复完成！共替换 ${changeCount} 处图标使用`);
    } else {
        console.log('\n✅ 没有发现需要修复的图标');
    }
}

// 添加图标字体协调性样式
function addIconStyles() {
    const filePath = path.join(__dirname, 'src/views/HomePage.vue');
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 查找样式部分
    const styleMatch = content.match(/(<style[^>]*>)([\s\S]*?)(<\/style>)/);
    if (!styleMatch) {
        console.log('❌ 未找到样式部分');
        return;
    }
    
    const iconStyles = `
/* ===== 图标字体协调性标准 ===== */
/* 基础图标尺寸 */
.el-icon {
  vertical-align: middle;
  transition: all 0.3s ease;
}

/* 特色标签图标 */
.feature-tag .el-icon {
  font-size: 14px;
  margin-right: 6px;
}

/* 按钮图标 */
.btn-primary-enhanced .el-icon,
.btn-secondary-enhanced .el-icon,
.btn-tertiary-enhanced .el-icon {
  font-size: 16px;
  margin-right: 8px;
}

/* 统计卡片图标 */
.stats-icon .el-icon {
  font-size: 24px;
}

/* 优势列表图标 */
.advantage-icon {
  font-size: 16px;
  margin-right: 8px;
  color: #52c41a;
}

/* 步骤图标 */
.ai-step-icon .el-icon {
  font-size: 32px;
}

/* 标签页图标 */
.tab-icon .el-icon {
  font-size: 20px;
}

/* 功能演示按钮图标 */
.btn-feature-demo .el-icon {
  font-size: 14px;
  margin-right: 6px;
}

/* 比较表格图标 */
.check-icon,
.cross-icon {
  font-size: 16px;
}

.star-icon {
  font-size: 18px;
  color: #faad14;
}

/* 技术图标 */
.ai-tech-icon .el-icon {
  font-size: 48px;
}

/* CTA选项图标 */
.ai-cta-option-icon .el-icon {
  font-size: 32px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stats-icon .el-icon {
    font-size: 20px;
  }
  
  .ai-step-icon .el-icon {
    font-size: 24px;
  }
  
  .tab-icon .el-icon {
    font-size: 16px;
  }
  
  .ai-tech-icon .el-icon {
    font-size: 32px;
  }
  
  .ai-cta-option-icon .el-icon {
    font-size: 24px;
  }
}

`;
    
    // 在样式开头添加图标样式
    const newStyleContent = styleMatch[1] + iconStyles + styleMatch[2] + styleMatch[3];
    content = content.replace(styleMatch[0], newStyleContent);
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('✅ 已添加图标字体协调性样式');
}

// 主函数
function main() {
    console.log('🎯 HomePage.vue 图标修复工具\n');
    
    try {
        fixHomepageIcons();
        addIconStyles();
        console.log('\n🎉 HomePage.vue 图标修复完成！');
    } catch (error) {
        console.error('❌ 修复过程中出现错误:', error);
    }
}

main();
