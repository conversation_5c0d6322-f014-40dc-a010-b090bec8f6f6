{"version": 3, "file": "language-server.d.ts", "sourceRoot": "", "sources": ["../../src/lsp/language-server.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EACR,gCAAgC,EAChC,gCAAgC,EAChC,iBAAiB,EACjB,UAAU,EACV,UAAU,EACV,KAAK,EACL,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EAQd,oBAAoB,EACpB,sBAAsB,EACtB,2BAA2B,EAC3B,6BAA6B,EAChC,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAsC,OAAO,EAAsD,MAAM,gCAAgC,CAAC;AAEjJ,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAI1D,OAAO,EAAE,aAAa,EAAE,KAAK,eAAe,EAAE,MAAM,2BAA2B,CAAC;AAEhF,OAAO,KAAK,EAAE,qBAAqB,EAAE,yBAAyB,EAAE,MAAM,mBAAmB,CAAC;AAI1F,MAAM,WAAW,cAAc;IAC3B,UAAU,CAAC,MAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAA;IAC/D,WAAW,CAAC,MAAM,EAAE,iBAAiB,GAAG,IAAI,CAAA;IAC5C,YAAY,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,GAAG,UAAU,CAAA;IACtE,aAAa,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,iBAAiB,KAAK,IAAI,GAAG,UAAU,CAAA;CAC3E;AAED;;;;GAIG;AACH,MAAM,MAAM,gCAAgC,GAAG,IAAI,CAAC,mBAAmB,GAAG,yBAAyB,EAAE,QAAQ,CAAC,CAAA;AAE9G,qBAAa,qBAAsB,YAAW,cAAc;IAExD,SAAS,CAAC,mBAAmB,4BAAmC;IAChE,SAAS,CAAC,oBAAoB,6BAAoC;IAElE,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,qBAAqB,CAAC;gBAEvC,QAAQ,EAAE,qBAAqB;IAI3C,IAAI,YAAY,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAE1C;IAED,IAAI,aAAa,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAE5C;IAEK,UAAU,CAAC,MAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAUrE;;;OAGG;IACH,SAAS,CAAC,iBAAiB,IAAI,IAAI;IAKnC,SAAS,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,gCAAgC,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO;IAK3G,SAAS,CAAC,qBAAqB,CAAC,OAAO,EAAE,gBAAgB,GAAG,gBAAgB;IA6F5E,WAAW,CAAC,MAAM,EAAE,iBAAiB,GAAG,IAAI;IAO5C,SAAS,CAAC,+BAA+B,CAAC,MAAM,EAAE,gBAAgB,GAAG,IAAI;IAKzE,SAAS,CAAC,gCAAgC,CAAC,MAAM,EAAE,iBAAiB,GAAG,IAAI;CAgB9E;AAED,wBAAgB,mBAAmB,CAAC,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CA8CzE;AAED;;;GAGG;AACH,wBAAgB,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAwBtG;AAED,wBAAgB,uBAAuB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAuBrG;AAED,wBAAgB,qBAAqB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAkBnG;AAED,wBAAgB,oBAAoB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAQlG;AAED,wBAAgB,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMtG;AAED,wBAAgB,oBAAoB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMlG;AAED,wBAAgB,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMtG;AAED,wBAAgB,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMtG;AAED,wBAAgB,4BAA4B,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAM1G;AAED,wBAAgB,4BAA4B,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,QAMnG;AAED,wBAAgB,yBAAyB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,QAMhG;AAED,wBAAgB,4BAA4B,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAM1G;AAED,wBAAgB,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAM7F;AAED,wBAAgB,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMpG;AAED,wBAAgB,oBAAoB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAgBlG;AAED,wBAAgB,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAW9F;AAED,wBAAgB,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMjG;AAED,wBAAgB,uBAAuB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAiCrG;AACD,wBAAgB,6BAA6B,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAM3G;AAED,wBAAgB,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAWtG;AAED,wBAAgB,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMpG;AAED,wBAAgB,uBAAuB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMrG;AAED,wBAAgB,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAMhG;AAED,wBAAgB,yBAAyB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAwBvG;AAED,wBAAgB,uBAAuB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,CAkCrG;AAED,wBAAgB,uBAAuB,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,qBAAqB,GAAG,IAAI,CAoC3G;AAED,wBAAgB,6BAA6B,CAAC,CAAC,SAAS,6BAA6B,GAAG,2BAA2B,GAAG,gCAAgC,GAAG,gCAAgC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,EACtM,WAAW,EAAE,CAAC,QAAQ,EAAE,gCAAgC,EAAE,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,iBAAiB,KAAK,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3H,cAAc,EAAE,qBAAqB,GACtC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAoBnC;AAED,wBAAgB,0BAA0B,CAAC,CAAC,SAAS;IAAE,YAAY,EAAE,sBAAsB,CAAA;CAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,EAC1G,WAAW,EAAE,CAAC,QAAQ,EAAE,gCAAgC,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,iBAAiB,KAAK,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EACtJ,cAAc,EAAE,qBAAqB,EACrC,WAAW,CAAC,EAAE,aAAa,GAC5B,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAsBnC;AAED,wBAAgB,oBAAoB,CAAC,CAAC,SAAS;IAAE,YAAY,EAAE,sBAAsB,CAAA;CAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,EAChG,WAAW,EAAE,CAAC,QAAQ,EAAE,gCAAgC,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,iBAAiB,KAAK,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EACtJ,cAAc,EAAE,qBAAqB,EACrC,WAAW,CAAC,EAAE,aAAa,GAC5B,cAAc,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAqBhC"}