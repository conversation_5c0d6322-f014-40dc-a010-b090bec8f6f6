{"version": 3, "file": "type-hierarchy-provider.js", "sourceRoot": "", "sources": ["../../src/lsp/type-hierarchy-provider.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAShF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAMnD,OAAO,EAAE,2BAA2B,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,EAAE,GAAG,EAAE,MAAM,uBAAuB,CAAC;AAgB5C,MAAM,OAAgB,6BAA6B;IAM/C,YAAY,QAAyB;QACjC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC;IACrD,CAAC;IAED,oBAAoB,CAAC,QAAyB,EAAE,MAAkC,EAAE,YAAgC;QAChH,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,MAAM,UAAU,GAAG,2BAA2B,CAC1C,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAC/C,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACzE,CAAC;IAES,qBAAqB,CAAC,UAAmB,EAAE,QAAyB;QAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YAC1D,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO;4BAEC,IAAI,EAAE,UAAU,CAAC,KAAK,EACtB,IAAI,EACJ,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,KAAK,EAChC,cAAc,EAAE,QAAQ,CAAC,KAAK,EAC9B,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IACzB,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;SAE/C,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACO,oBAAoB,CAAC,WAAoB;QAC/C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAqC,EAAE,YAAgC;QACpF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACtF,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,MAAM,UAAU,GAAG,2BAA2B,CAC1C,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EACvD,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAOD,KAAK,CAAC,QAAQ,CAAC,MAAmC,EAAE,YAAgC;QAChF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACtF,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,MAAM,UAAU,GAAG,2BAA2B,CAC1C,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EACvD,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;CAMJ"}