export namespace shapes {
    export { rect };
    export { ellipse };
    export { circle };
    export { diamond };
}
export function setShapes(value: any): void;
declare function rect(parent: any, bbox: any, node: any): any;
declare function ellipse(parent: any, bbox: any, node: any): any;
declare function circle(parent: any, bbox: any, node: any): any;
declare function diamond(parent: any, bbox: any, node: any): any;
export {};
