import { Color, RGBColor } from './types.mjs';

/**
 * String to color
 */
declare function stringToColor(value: string): Color | null;
/**
 * Check if colors are identical
 */
declare function compareColors(color1: Color, color2: Color): boolean;
/**
 * Color to hex
 */
declare function colorToHexString(color: RGBColor, canCompact?: boolean): string | null;
/**
 * Convert color to string
 */
declare function colorToString(color: Color): string;

export { colorToHexString, colorToString, compareColors, stringToColor };
