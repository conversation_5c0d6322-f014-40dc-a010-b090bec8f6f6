'use strict';
// https://github.com/tc39/proposal-collection-methods
require('../modules/esnext.map.group-by');
require('../modules/esnext.map.key-by');
require('../modules/esnext.map.delete-all');
require('../modules/esnext.map.every');
require('../modules/esnext.map.filter');
require('../modules/esnext.map.find');
require('../modules/esnext.map.find-key');
require('../modules/esnext.map.includes');
require('../modules/esnext.map.key-of');
require('../modules/esnext.map.map-keys');
require('../modules/esnext.map.map-values');
require('../modules/esnext.map.merge');
require('../modules/esnext.map.reduce');
require('../modules/esnext.map.some');
require('../modules/esnext.map.update');
require('../modules/esnext.set.add-all');
require('../modules/esnext.set.delete-all');
require('../modules/esnext.set.every');
require('../modules/esnext.set.filter');
require('../modules/esnext.set.find');
require('../modules/esnext.set.join');
require('../modules/esnext.set.map');
require('../modules/esnext.set.reduce');
require('../modules/esnext.set.some');
require('../modules/esnext.weak-map.delete-all');
require('../modules/esnext.weak-set.add-all');
require('../modules/esnext.weak-set.delete-all');
