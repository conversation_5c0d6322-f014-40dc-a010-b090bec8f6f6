import{a as ae}from"./chunk-TI4EEUUG.mjs";import{$a as K,Ga as R,Ja as F,Ka as O,La as _,M as v,Ma as k,Na as N,Oa as j,Pa as A,Qa as H,Ra as z,Sa as I,Ta as U,Ua as X,Va as Y,Wa as J,Xa as V,Ya as q,Za as G,_a as Z,b as d,e as m,h as B,ha as D,k as w,t as L}from"./chunk-63ZE7VZ5.mjs";import{T as Q,k as b}from"./chunk-6BY5RJGC.mjs";import{a,e as se}from"./chunk-GTKDMUJJ.mjs";var ne=se(ae(),1);var ce="\u200B",ue={curveBasis:_,curveBasisClosed:k,curveBasisOpen:N,curveBumpX:F,curveBumpY:O,curveBundle:j,curveCardinalClosed:H,curveCardinalOpen:z,curveCardinal:A,curveCatmullRomClosed:U,curveCatmullRomOpen:X,curveCatmullRom:I,curveLinear:R,curveLinearClosed:Y,curveMonotoneX:J,curveMonotoneY:V,curveNatural:q,curveStep:G,curveStepAfter:K,curveStepBefore:Z},le=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,fe=a(function(t,e){let r=re(t,/(?:init\b)|(?:initialize\b)/),n={};if(Array.isArray(r)){let s=r.map(u=>u.args);L(s),n=w(n,[...s])}else n=r.args;if(!n)return;let i=B(t,e),o="config";return n[o]!==void 0&&(i==="flowchart-v2"&&(i="flowchart"),n[i]=n[o],delete n[o]),n},"detectInit"),re=a(function(t,e=null){try{let r=new RegExp(`[%]{2}(?![{]${le.source})(?=[}][%]{2}).*
`,"ig");t=t.trim().replace(r,"").replace(/'/gm,'"'),d.debug(`Detecting diagram directive${e!==null?" type:"+e:""} based on the text:${t}`);let n,i=[];for(;(n=m.exec(t))!==null;)if(n.index===m.lastIndex&&m.lastIndex++,n&&!e||e&&n[1]?.match(e)||e&&n[2]?.match(e)){let o=n[1]?n[1]:n[2],s=n[3]?n[3].trim():n[4]?JSON.parse(n[4].trim()):null;i.push({type:o,args:s})}return i.length===0?{type:t,args:null}:i.length===1?i[0]:i}catch(r){return d.error(`ERROR: ${r.message} - Unable to parse directive type: '${e}' based on the text: '${t}'`),{type:void 0,args:null}}},"detectDirective"),He=a(function(t){return t.replace(m,"")},"removeDirectives"),ge=a(function(t,e){for(let[r,n]of e.entries())if(n.match(t))return r;return-1},"isSubstringInArray");function de(t,e){if(!t)return e;let r=`curve${t.charAt(0).toUpperCase()+t.slice(1)}`;return ue[r]??e}a(de,"interpolateToCurve");function he(t,e){let r=t.trim();if(r)return e.securityLevel!=="loose"?(0,ne.sanitizeUrl)(r):r}a(he,"formatUrl");var me=a((t,...e)=>{let r=t.split("."),n=r.length-1,i=r[n],o=window;for(let s=0;s<n;s++)if(o=o[r[s]],!o){d.error(`Function name: ${t} not found in window`);return}o[i](...e)},"runFunc");function ie(t,e){return!t||!e?0:Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}a(ie,"distance");function pe(t){let e,r=0;t.forEach(i=>{r+=ie(i,e),e=i});let n=r/2;return T(t,n)}a(pe,"traverseEdge");function xe(t){return t.length===1?t[0]:pe(t)}a(xe,"calcLabelPosition");var ee=a((t,e=2)=>{let r=Math.pow(10,e);return Math.round(t*r)/r},"roundNumber"),T=a((t,e)=>{let r,n=e;for(let i of t){if(r){let o=ie(i,r);if(o===0)return r;if(o<n)n-=o;else{let s=n/o;if(s<=0)return r;if(s>=1)return{x:i.x,y:i.y};if(s>0&&s<1)return{x:ee((1-s)*r.x+s*i.x,5),y:ee((1-s)*r.y+s*i.y,5)}}}r=i}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),ye=a((t,e,r)=>{d.info(`our points ${JSON.stringify(e)}`),e[0]!==r&&(e=e.reverse());let i=T(e,25),o=t?10:5,s=Math.atan2(e[0].y-i.y,e[0].x-i.x),u={x:0,y:0};return u.x=Math.sin(s)*o+(e[0].x+i.x)/2,u.y=-Math.cos(s)*o+(e[0].y+i.y)/2,u},"calcCardinalityPosition");function ve(t,e,r){let n=structuredClone(r);d.info("our points",n),e!=="start_left"&&e!=="start_right"&&n.reverse();let i=25+t,o=T(n,i),s=10+t*.5,u=Math.atan2(n[0].y-o.y,n[0].x-o.x),c={x:0,y:0};return e==="start_left"?(c.x=Math.sin(u+Math.PI)*s+(n[0].x+o.x)/2,c.y=-Math.cos(u+Math.PI)*s+(n[0].y+o.y)/2):e==="end_right"?(c.x=Math.sin(u-Math.PI)*s+(n[0].x+o.x)/2-5,c.y=-Math.cos(u-Math.PI)*s+(n[0].y+o.y)/2-5):e==="end_left"?(c.x=Math.sin(u)*s+(n[0].x+o.x)/2-5,c.y=-Math.cos(u)*s+(n[0].y+o.y)/2-5):(c.x=Math.sin(u)*s+(n[0].x+o.x)/2,c.y=-Math.cos(u)*s+(n[0].y+o.y)/2),c}a(ve,"calcTerminalLabelPosition");function be(t){let e="",r="";for(let n of t)n!==void 0&&(n.startsWith("color:")||n.startsWith("text-align:")?r=r+n+";":e=e+n+";");return{style:e,labelStyle:r}}a(be,"getStylesFromArray");var te=0,Ce=a(()=>(te++,"id-"+Math.random().toString(36).substr(2,12)+"-"+te),"generateId");function Me(t){let e="",r="0123456789abcdef",n=r.length;for(let i=0;i<t;i++)e+=r.charAt(Math.floor(Math.random()*n));return e}a(Me,"makeRandomHex");var we=a(t=>Me(t.length),"random"),Pe=a(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),Te=a(function(t,e){let r=e.text.replace(v.lineBreakRegex," "),[,n]=S(e.fontSize),i=t.append("text");i.attr("x",e.x),i.attr("y",e.y),i.style("text-anchor",e.anchor),i.style("font-family",e.fontFamily),i.style("font-size",n),i.style("font-weight",e.fontWeight),i.attr("fill",e.fill),e.class!==void 0&&i.attr("class",e.class);let o=i.append("tspan");return o.attr("x",e.x+e.textMargin*2),o.attr("fill",e.fill),o.text(r),i},"drawSimpleText"),$e=b((t,e,r)=>{if(!t||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),v.lineBreakRegex.test(t)))return t;let n=t.split(" ").filter(Boolean),i=[],o="";return n.forEach((s,u)=>{let c=M(`${s} `,r),l=M(o,r);if(c>e){let{hyphenatedStrings:h,remainingWord:f}=Se(s,e,"-",r);i.push(o,...h),o=f}else l+c>=e?(i.push(o),o=s):o=[o,s].filter(Boolean).join(" ");u+1===n.length&&i.push(o)}),i.filter(s=>s!=="").join(r.joinWith)},(t,e,r)=>`${t}${e}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),Se=b((t,e,r="-",n)=>{n=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},n);let i=[...t],o=[],s="";return i.forEach((u,c)=>{let l=`${s}${u}`;if(M(l,n)>=e){let x=c+1,h=i.length===x,f=`${l}${r}`;o.push(h?l:f),s=""}else s=l}),{hyphenatedStrings:o,remainingWord:s}},(t,e,r="-",n)=>`${t}${e}${r}${n.fontSize}${n.fontWeight}${n.fontFamily}`);function We(t,e){return $(t,e).height}a(We,"calculateTextHeight");function M(t,e){return $(t,e).width}a(M,"calculateTextWidth");var $=b((t,e)=>{let{fontSize:r=12,fontFamily:n="Arial",fontWeight:i=400}=e;if(!t)return{width:0,height:0};let[,o]=S(r),s=["sans-serif",n],u=t.split(v.lineBreakRegex),c=[],l=D("body");if(!l.remove)return{width:0,height:0,lineHeight:0};let p=l.append("svg");for(let h of s){let f=0,g={width:0,height:0,lineHeight:0};for(let oe of u){let W=Pe();W.text=oe||ce;let E=Te(p,W).style("font-size",o).style("font-weight",i).style("font-family",h),y=(E._groups||E)[0][0].getBBox();if(y.width===0&&y.height===0)throw new Error("svg element not in render tree");g.width=Math.round(Math.max(g.width,y.width)),f=Math.round(y.height),g.height+=f,g.lineHeight=Math.round(Math.max(g.lineHeight,f))}c.push(g)}p.remove();let x=isNaN(c[1].height)||isNaN(c[1].width)||isNaN(c[1].lineHeight)||c[0].height>c[1].height&&c[0].width>c[1].width&&c[0].lineHeight>c[1].lineHeight?0:1;return c[x]},(t,e)=>`${t}${e.fontSize}${e.fontWeight}${e.fontFamily}`),P=class{constructor(e=!1,r){this.count=0;this.count=r?r.length:0,this.next=e?()=>this.count++:()=>Date.now()}static{a(this,"InitIDGenerator")}},C,Ee=a(function(t){return C=C||document.createElement("div"),t=escape(t).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),C.innerHTML=t,unescape(C.textContent)},"entityDecode");function ze(t){return"str"in t}a(ze,"isDetailedError");var Be=a((t,e,r,n)=>{if(!n)return;let i=t.node()?.getBBox();i&&t.append("text").text(n).attr("text-anchor","middle").attr("x",i.x+i.width/2).attr("y",-r).attr("class",e)},"insertTitle"),S=a(t=>{if(typeof t=="number")return[t,t+"px"];let e=parseInt(t??"",10);return Number.isNaN(e)?[void 0,void 0]:t===String(e)?[e,t+"px"]:[e,t]},"parseFontSize");function Le(t,e){return Q({},t,e)}a(Le,"cleanAndMerge");var Ie={assignWithDepth:w,wrapLabel:$e,calculateTextHeight:We,calculateTextWidth:M,calculateTextDimensions:$,cleanAndMerge:Le,detectInit:fe,detectDirective:re,isSubstringInArray:ge,interpolateToCurve:de,calcLabelPosition:xe,calcCardinalityPosition:ye,calcTerminalLabelPosition:ve,formatUrl:he,getStylesFromArray:be,generateId:Ce,random:we,runFunc:me,entityDecode:Ee,insertTitle:Be,parseFontSize:S,InitIDGenerator:P},Ue=a(function(t){let e=t;return e=e.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),e=e.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),e=e.replace(/#\w+;/g,function(r){let n=r.substring(1,r.length-1);return/^\+?\d+$/.test(n)?"\uFB02\xB0\xB0"+n+"\xB6\xDF":"\uFB02\xB0"+n+"\xB6\xDF"}),e},"encodeEntities"),Xe=a(function(t){return t.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities");var Ye=a((t,e,{counter:r=0,prefix:n,suffix:i},o)=>o||`${n?`${n}_`:""}${t}_${e}_${r}${i?`_${i}`:""}`,"getEdgeId");function Je(t){return t??null}a(Je,"handleUndefinedAttr");export{ce as a,He as b,de as c,be as d,Ce as e,we as f,$e as g,We as h,M as i,ze as j,S as k,Le as l,Ie as m,Ue as n,Xe as o,Ye as p,Je as q};
