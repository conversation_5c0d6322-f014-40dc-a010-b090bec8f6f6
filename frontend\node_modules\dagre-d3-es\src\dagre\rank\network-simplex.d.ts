export function networkSimplex(g: any): void;
export namespace networkSimplex {
    export { initLowLimValues };
    export { initCutValues };
    export { calcCutValue };
    export { leaveEdge };
    export { enterEdge };
    export { exchangeEdges };
}
declare function initLowLimValues(tree: any, root: any, ...args: any[]): void;
declare function initCutValues(t: any, g: any): void;
declare function calcCutValue(t: any, g: any, child: any): number;
declare function leaveEdge(tree: any): any;
declare function enterEdge(t: any, g: any, edge: any): any;
declare function exchangeEdges(t: any, g: any, e: any, f: any): void;
export {};
