<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="frontendGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#frontendGrad)"/>
  <circle cx="100" cy="80" r="25" fill="rgba(255,255,255,0.2)"/>
  <circle cx="300" cy="220" r="20" fill="rgba(255,255,255,0.15)"/>
  <rect x="50" y="120" width="300" height="80" rx="8" fill="rgba(255,255,255,0.1)"/>
  <text x="200" y="150" text-anchor="middle" dominant-baseline="middle" 
        fill="#ffffff" font-size="24" font-weight="bold" font-family="Microsoft YaHei, Arial, sans-serif">
    前端开发案例
  </text>
  <text x="200" y="180" text-anchor="middle" dominant-baseline="middle" 
        fill="#e0e7ff" font-size="14" font-family="Microsoft YaHei, Arial, sans-serif">
    Vue.js + Element Plus 面试系统
  </text>
  <text x="200" y="250" text-anchor="middle" dominant-baseline="middle" 
        fill="#ffffff" font-size="12" font-family="Microsoft YaHei, Arial, sans-serif">
    iFlytek Spark AI 技术驱动
  </text>
</svg>
