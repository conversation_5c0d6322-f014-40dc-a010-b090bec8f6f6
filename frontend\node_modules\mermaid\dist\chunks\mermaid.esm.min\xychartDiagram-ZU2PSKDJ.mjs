import{a as $t}from"./chunk-ZKOTWRZ5.mjs";import{e as Ut}from"./chunk-TK7VX7YV.mjs";import{l as wt}from"./chunk-CRSA2SMT.mjs";import"./chunk-TI4EEUUG.mjs";import{A as rt,F as Wt,Ha as Dt,N as zt,Q as Ot,R as Ft,S as Nt,T as jt,U as Gt,V as Ht,W as Ct,b as nt,na as At,oa as Tt,q as It,s as Mt}from"./chunk-63ZE7VZ5.mjs";import"./chunk-6BY5RJGC.mjs";import{a as s}from"./chunk-GTKDMUJJ.mjs";var kt=function(){var i=s(function(F,o,h,p){for(h=h||{},p=F.length;p--;h[F[p]]=o);return h},"o"),t=[1,10,12,14,16,18,19,21,23],e=[2,6],a=[1,3],n=[1,5],l=[1,6],x=[1,7],y=[1,5,10,12,14,16,18,19,21,23,34,35,36],A=[1,25],V=[1,26],B=[1,28],_=[1,29],L=[1,30],I=[1,31],M=[1,32],R=[1,33],W=[1,34],z=[1,35],O=[1,36],f=[1,37],P=[1,43],g=[1,42],X=[1,47],v=[1,50],C=[1,10,12,14,16,18,19,21,23,34,35,36],j=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36],c=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36,41,42,43,44,45,46,47,48,49,50],w=[1,64],S={trace:s(function(){},"trace"),yy:{},symbols_:{error:2,start:3,eol:4,XYCHART:5,chartConfig:6,document:7,CHART_ORIENTATION:8,statement:9,title:10,text:11,X_AXIS:12,parseXAxis:13,Y_AXIS:14,parseYAxis:15,LINE:16,plotData:17,BAR:18,acc_title:19,acc_title_value:20,acc_descr:21,acc_descr_value:22,acc_descr_multiline_value:23,SQUARE_BRACES_START:24,commaSeparatedNumbers:25,SQUARE_BRACES_END:26,NUMBER_WITH_DECIMAL:27,COMMA:28,xAxisData:29,bandData:30,ARROW_DELIMITER:31,commaSeparatedTexts:32,yAxisData:33,NEWLINE:34,SEMI:35,EOF:36,alphaNum:37,STR:38,MD_STR:39,alphaNumToken:40,AMP:41,NUM:42,ALPHA:43,PLUS:44,EQUALS:45,MULT:46,DOT:47,BRKT:48,MINUS:49,UNDERSCORE:50,$accept:0,$end:1},terminals_:{2:"error",5:"XYCHART",8:"CHART_ORIENTATION",10:"title",12:"X_AXIS",14:"Y_AXIS",16:"LINE",18:"BAR",19:"acc_title",20:"acc_title_value",21:"acc_descr",22:"acc_descr_value",23:"acc_descr_multiline_value",24:"SQUARE_BRACES_START",26:"SQUARE_BRACES_END",27:"NUMBER_WITH_DECIMAL",28:"COMMA",31:"ARROW_DELIMITER",34:"NEWLINE",35:"SEMI",36:"EOF",38:"STR",39:"MD_STR",41:"AMP",42:"NUM",43:"ALPHA",44:"PLUS",45:"EQUALS",46:"MULT",47:"DOT",48:"BRKT",49:"MINUS",50:"UNDERSCORE"},productions_:[0,[3,2],[3,3],[3,2],[3,1],[6,1],[7,0],[7,2],[9,2],[9,2],[9,2],[9,2],[9,2],[9,3],[9,2],[9,3],[9,2],[9,2],[9,1],[17,3],[25,3],[25,1],[13,1],[13,2],[13,1],[29,1],[29,3],[30,3],[32,3],[32,1],[15,1],[15,2],[15,1],[33,3],[4,1],[4,1],[4,1],[11,1],[11,1],[11,1],[37,1],[37,2],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1]],performAction:s(function(o,h,p,u,b,r,Z){var m=r.length-1;switch(b){case 5:u.setOrientation(r[m]);break;case 9:u.setDiagramTitle(r[m].text.trim());break;case 12:u.setLineData({text:"",type:"text"},r[m]);break;case 13:u.setLineData(r[m-1],r[m]);break;case 14:u.setBarData({text:"",type:"text"},r[m]);break;case 15:u.setBarData(r[m-1],r[m]);break;case 16:this.$=r[m].trim(),u.setAccTitle(this.$);break;case 17:case 18:this.$=r[m].trim(),u.setAccDescription(this.$);break;case 19:this.$=r[m-1];break;case 20:this.$=[Number(r[m-2]),...r[m]];break;case 21:this.$=[Number(r[m])];break;case 22:u.setXAxisTitle(r[m]);break;case 23:u.setXAxisTitle(r[m-1]);break;case 24:u.setXAxisTitle({type:"text",text:""});break;case 25:u.setXAxisBand(r[m]);break;case 26:u.setXAxisRangeData(Number(r[m-2]),Number(r[m]));break;case 27:this.$=r[m-1];break;case 28:this.$=[r[m-2],...r[m]];break;case 29:this.$=[r[m]];break;case 30:u.setYAxisTitle(r[m]);break;case 31:u.setYAxisTitle(r[m-1]);break;case 32:u.setYAxisTitle({type:"text",text:""});break;case 33:u.setYAxisRangeData(Number(r[m-2]),Number(r[m]));break;case 37:this.$={text:r[m],type:"text"};break;case 38:this.$={text:r[m],type:"text"};break;case 39:this.$={text:r[m],type:"markdown"};break;case 40:this.$=r[m];break;case 41:this.$=r[m-1]+""+r[m];break}},"anonymous"),table:[i(t,e,{3:1,4:2,7:4,5:a,34:n,35:l,36:x}),{1:[3]},i(t,e,{4:2,7:4,3:8,5:a,34:n,35:l,36:x}),i(t,e,{4:2,7:4,6:9,3:10,5:a,8:[1,11],34:n,35:l,36:x}),{1:[2,4],9:12,10:[1,13],12:[1,14],14:[1,15],16:[1,16],18:[1,17],19:[1,18],21:[1,19],23:[1,20]},i(y,[2,34]),i(y,[2,35]),i(y,[2,36]),{1:[2,1]},i(t,e,{4:2,7:4,3:21,5:a,34:n,35:l,36:x}),{1:[2,3]},i(y,[2,5]),i(t,[2,7],{4:22,34:n,35:l,36:x}),{11:23,37:24,38:A,39:V,40:27,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f},{11:39,13:38,24:P,27:g,29:40,30:41,37:24,38:A,39:V,40:27,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f},{11:45,15:44,27:X,33:46,37:24,38:A,39:V,40:27,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f},{11:49,17:48,24:v,37:24,38:A,39:V,40:27,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f},{11:52,17:51,24:v,37:24,38:A,39:V,40:27,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f},{20:[1,53]},{22:[1,54]},i(C,[2,18]),{1:[2,2]},i(C,[2,8]),i(C,[2,9]),i(j,[2,37],{40:55,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f}),i(j,[2,38]),i(j,[2,39]),i(c,[2,40]),i(c,[2,42]),i(c,[2,43]),i(c,[2,44]),i(c,[2,45]),i(c,[2,46]),i(c,[2,47]),i(c,[2,48]),i(c,[2,49]),i(c,[2,50]),i(c,[2,51]),i(C,[2,10]),i(C,[2,22],{30:41,29:56,24:P,27:g}),i(C,[2,24]),i(C,[2,25]),{31:[1,57]},{11:59,32:58,37:24,38:A,39:V,40:27,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f},i(C,[2,11]),i(C,[2,30],{33:60,27:X}),i(C,[2,32]),{31:[1,61]},i(C,[2,12]),{17:62,24:v},{25:63,27:w},i(C,[2,14]),{17:65,24:v},i(C,[2,16]),i(C,[2,17]),i(c,[2,41]),i(C,[2,23]),{27:[1,66]},{26:[1,67]},{26:[2,29],28:[1,68]},i(C,[2,31]),{27:[1,69]},i(C,[2,13]),{26:[1,70]},{26:[2,21],28:[1,71]},i(C,[2,15]),i(C,[2,26]),i(C,[2,27]),{11:59,32:72,37:24,38:A,39:V,40:27,41:B,42:_,43:L,44:I,45:M,46:R,47:W,48:z,49:O,50:f},i(C,[2,33]),i(C,[2,19]),{25:73,27:w},{26:[2,28]},{26:[2,20]}],defaultActions:{8:[2,1],10:[2,3],21:[2,2],72:[2,28],73:[2,20]},parseError:s(function(o,h){if(h.recoverable)this.trace(o);else{var p=new Error(o);throw p.hash=h,p}},"parseError"),parse:s(function(o){var h=this,p=[0],u=[],b=[null],r=[],Z=this.table,m="",it=0,Xt=0,Yt=0,oe=2,Vt=1,he=r.slice.call(arguments,1),T=Object.create(this.lexer),G={yy:{}};for(var mt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,mt)&&(G.yy[mt]=this.yy[mt]);T.setInput(o,G.yy),G.yy.lexer=T,G.yy.parser=this,typeof T.yylloc>"u"&&(T.yylloc={});var ft=T.yylloc;r.push(ft);var le=T.options&&T.options.ranges;typeof G.yy.parseError=="function"?this.parseError=G.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Se(E){p.length=p.length-2*E,b.length=b.length-E,r.length=r.length-E}s(Se,"popStack");function ce(){var E;return E=u.pop()||T.lex()||Vt,typeof E!="number"&&(E instanceof Array&&(u=E,E=u.pop()),E=h.symbols_[E]||E),E}s(ce,"lex");for(var k,dt,H,Y,_e,bt,$={},at,N,Bt,st;;){if(H=p[p.length-1],this.defaultActions[H]?Y=this.defaultActions[H]:((k===null||typeof k>"u")&&(k=ce()),Y=Z[H]&&Z[H][k]),typeof Y>"u"||!Y.length||!Y[0]){var yt="";st=[];for(at in Z[H])this.terminals_[at]&&at>oe&&st.push("'"+this.terminals_[at]+"'");T.showPosition?yt="Parse error on line "+(it+1)+`:
`+T.showPosition()+`
Expecting `+st.join(", ")+", got '"+(this.terminals_[k]||k)+"'":yt="Parse error on line "+(it+1)+": Unexpected "+(k==Vt?"end of input":"'"+(this.terminals_[k]||k)+"'"),this.parseError(yt,{text:T.match,token:this.terminals_[k]||k,line:T.yylineno,loc:ft,expected:st})}if(Y[0]instanceof Array&&Y.length>1)throw new Error("Parse Error: multiple actions possible at state: "+H+", token: "+k);switch(Y[0]){case 1:p.push(k),b.push(T.yytext),r.push(T.yylloc),p.push(Y[1]),k=null,dt?(k=dt,dt=null):(Xt=T.yyleng,m=T.yytext,it=T.yylineno,ft=T.yylloc,Yt>0&&Yt--);break;case 2:if(N=this.productions_[Y[1]][1],$.$=b[b.length-N],$._$={first_line:r[r.length-(N||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(N||1)].first_column,last_column:r[r.length-1].last_column},le&&($._$.range=[r[r.length-(N||1)].range[0],r[r.length-1].range[1]]),bt=this.performAction.apply($,[m,Xt,it,G.yy,Y[1],b,r].concat(he)),typeof bt<"u")return bt;N&&(p=p.slice(0,-1*N*2),b=b.slice(0,-1*N),r=r.slice(0,-1*N)),p.push(this.productions_[Y[1]][0]),b.push($.$),r.push($._$),Bt=Z[p[p.length-2]][p[p.length-1]],p.push(Bt);break;case 3:return!0}}return!0},"parse")},D=function(){var F={EOF:1,parseError:s(function(h,p){if(this.yy.parser)this.yy.parser.parseError(h,p);else throw new Error(h)},"parseError"),setInput:s(function(o,h){return this.yy=h||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:s(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var h=o.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:s(function(o){var h=o.length,p=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),p.length-1&&(this.yylineno-=p.length-1);var b=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:p?(p.length===u.length?this.yylloc.first_column:0)+u[u.length-p.length].length-p[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[b[0],b[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:s(function(){return this._more=!0,this},"more"),reject:s(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:s(function(o){this.unput(this.match.slice(o))},"less"),pastInput:s(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:s(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:s(function(){var o=this.pastInput(),h=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:s(function(o,h){var p,u,b;if(this.options.backtrack_lexer&&(b={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(b.yylloc.range=this.yylloc.range.slice(0))),u=o[0].match(/(?:\r\n?|\n).*/g),u&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],p=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),p)return p;if(this._backtrack){for(var r in b)this[r]=b[r];return!1}return!1},"test_match"),next:s(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,h,p,u;this._more||(this.yytext="",this.match="");for(var b=this._currentRules(),r=0;r<b.length;r++)if(p=this._input.match(this.rules[b[r]]),p&&(!h||p[0].length>h[0].length)){if(h=p,u=r,this.options.backtrack_lexer){if(o=this.test_match(p,b[r]),o!==!1)return o;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(o=this.test_match(h,b[u]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:s(function(){var h=this.next();return h||this.lex()},"lex"),begin:s(function(h){this.conditionStack.push(h)},"begin"),popState:s(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:s(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:s(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:s(function(h){this.begin(h)},"pushState"),stateStackSize:s(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:s(function(h,p,u,b){var r=b;switch(u){case 0:break;case 1:break;case 2:return this.popState(),34;break;case 3:return this.popState(),34;break;case 4:return 34;case 5:break;case 6:return 10;case 7:return this.pushState("acc_title"),19;break;case 8:return this.popState(),"acc_title_value";break;case 9:return this.pushState("acc_descr"),21;break;case 10:return this.popState(),"acc_descr_value";break;case 11:this.pushState("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 5;case 15:return 8;case 16:return this.pushState("axis_data"),"X_AXIS";break;case 17:return this.pushState("axis_data"),"Y_AXIS";break;case 18:return this.pushState("axis_band_data"),24;break;case 19:return 31;case 20:return this.pushState("data"),16;break;case 21:return this.pushState("data"),18;break;case 22:return this.pushState("data_inner"),24;break;case 23:return 27;case 24:return this.popState(),26;break;case 25:this.popState();break;case 26:this.pushState("string");break;case 27:this.popState();break;case 28:return"STR";case 29:return 24;case 30:return 26;case 31:return 43;case 32:return"COLON";case 33:return 44;case 34:return 28;case 35:return 45;case 36:return 46;case 37:return 48;case 38:return 50;case 39:return 47;case 40:return 41;case 41:return 49;case 42:return 42;case 43:break;case 44:return 35;case 45:return 36}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:(\r?\n))/i,/^(?:(\r?\n))/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:\{)/i,/^(?:[^\}]*)/i,/^(?:xychart-beta\b)/i,/^(?:(?:vertical|horizontal))/i,/^(?:x-axis\b)/i,/^(?:y-axis\b)/i,/^(?:\[)/i,/^(?:-->)/i,/^(?:line\b)/i,/^(?:bar\b)/i,/^(?:\[)/i,/^(?:[+-]?(?:\d+(?:\.\d+)?|\.\d+))/i,/^(?:\])/i,/^(?:(?:`\)                                    \{ this\.pushState\(md_string\); \}\n<md_string>\(\?:\(\?!`"\)\.\)\+                  \{ return MD_STR; \}\n<md_string>\(\?:`))/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s+)/i,/^(?:;)/i,/^(?:$)/i],conditions:{data_inner:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,23,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},data:{rules:[0,1,3,4,5,6,7,9,11,14,15,16,17,20,21,22,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_band_data:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_data:{rules:[0,1,2,4,5,6,7,9,11,14,15,16,17,18,19,20,21,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},title:{rules:[],inclusive:!1},md_string:{rules:[],inclusive:!1},string:{rules:[27,28],inclusive:!1},INITIAL:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0}}};return F}();S.lexer=D;function U(){this.yy={}}return s(U,"Parser"),U.prototype=S,S.Parser=U,new U}();kt.parser=kt;var qt=kt;function St(i){return i.type==="bar"}s(St,"isBarPlot");function ot(i){return i.type==="band"}s(ot,"isBandAxisData");function q(i){return i.type==="linear"}s(q,"isLinearAxisData");var Q=class{constructor(t){this.parentGroup=t}static{s(this,"TextDimensionCalculatorWithFont")}getMaxDimension(t,e){if(!this.parentGroup)return{width:t.reduce((l,x)=>Math.max(x.length,l),0)*e,height:e};let a={width:0,height:0},n=this.parentGroup.append("g").attr("visibility","hidden").attr("font-size",e);for(let l of t){let x=Ut(n,1,l),y=x?x.width:l.length*e,A=x?x.height:e;a.width=Math.max(a.width,y),a.height=Math.max(a.height,A)}return n.remove(),a}};var K=class{constructor(t,e,a,n){this.axisConfig=t;this.title=e;this.textDimensionCalculator=a;this.axisThemeConfig=n;this.boundingRect={x:0,y:0,width:0,height:0};this.axisPosition="left";this.showTitle=!1;this.showLabel=!1;this.showTick=!1;this.showAxisLine=!1;this.outerPadding=0;this.titleTextHeight=0;this.labelTextHeight=0;this.range=[0,10],this.boundingRect={x:0,y:0,width:0,height:0},this.axisPosition="left"}static{s(this,"BaseAxis")}setRange(t){this.range=t,this.axisPosition==="left"||this.axisPosition==="right"?this.boundingRect.height=t[1]-t[0]:this.boundingRect.width=t[1]-t[0],this.recalculateScale()}getRange(){return[this.range[0]+this.outerPadding,this.range[1]-this.outerPadding]}setAxisPosition(t){this.axisPosition=t,this.setRange(this.range)}getTickDistance(){let t=this.getRange();return Math.abs(t[0]-t[1])/this.getTickValues().length}getAxisOuterPadding(){return this.outerPadding}getLabelDimension(){return this.textDimensionCalculator.getMaxDimension(this.getTickValues().map(t=>t.toString()),this.axisConfig.labelFontSize)}recalculateOuterPaddingToDrawBar(){.7*this.getTickDistance()>this.outerPadding*2&&(this.outerPadding=Math.floor(.7*this.getTickDistance()/2)),this.recalculateScale()}calculateSpaceIfDrawnHorizontally(t){let e=t.height;if(this.axisConfig.showAxisLine&&e>this.axisConfig.axisLineWidth&&(e-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){let a=this.getLabelDimension(),n=.2*t.width;this.outerPadding=Math.min(a.width/2,n);let l=a.height+this.axisConfig.labelPadding*2;this.labelTextHeight=a.height,l<=e&&(e-=l,this.showLabel=!0)}if(this.axisConfig.showTick&&e>=this.axisConfig.tickLength&&(this.showTick=!0,e-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){let a=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),n=a.height+this.axisConfig.titlePadding*2;this.titleTextHeight=a.height,n<=e&&(e-=n,this.showTitle=!0)}this.boundingRect.width=t.width,this.boundingRect.height=t.height-e}calculateSpaceIfDrawnVertical(t){let e=t.width;if(this.axisConfig.showAxisLine&&e>this.axisConfig.axisLineWidth&&(e-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){let a=this.getLabelDimension(),n=.2*t.height;this.outerPadding=Math.min(a.height/2,n);let l=a.width+this.axisConfig.labelPadding*2;l<=e&&(e-=l,this.showLabel=!0)}if(this.axisConfig.showTick&&e>=this.axisConfig.tickLength&&(this.showTick=!0,e-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){let a=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),n=a.height+this.axisConfig.titlePadding*2;this.titleTextHeight=a.height,n<=e&&(e-=n,this.showTitle=!0)}this.boundingRect.width=t.width-e,this.boundingRect.height=t.height}calculateSpace(t){return this.axisPosition==="left"||this.axisPosition==="right"?this.calculateSpaceIfDrawnVertical(t):this.calculateSpaceIfDrawnHorizontally(t),this.recalculateScale(),{width:this.boundingRect.width,height:this.boundingRect.height}}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}getDrawableElementsForLeftAxis(){let t=[];if(this.showAxisLine){let e=this.boundingRect.x+this.boundingRect.width-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["left-axis","axisl-line"],data:[{path:`M ${e},${this.boundingRect.y} L ${e},${this.boundingRect.y+this.boundingRect.height} `,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["left-axis","label"],data:this.getTickValues().map(e=>({text:e.toString(),x:this.boundingRect.x+this.boundingRect.width-(this.showLabel?this.axisConfig.labelPadding:0)-(this.showTick?this.axisConfig.tickLength:0)-(this.showAxisLine?this.axisConfig.axisLineWidth:0),y:this.getScaleValue(e),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"middle",horizontalPos:"right"}))}),this.showTick){let e=this.boundingRect.x+this.boundingRect.width-(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["left-axis","ticks"],data:this.getTickValues().map(a=>({path:`M ${e},${this.getScaleValue(a)} L ${e-this.axisConfig.tickLength},${this.getScaleValue(a)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["left-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.axisConfig.titlePadding,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:270,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForBottomAxis(){let t=[];if(this.showAxisLine){let e=this.boundingRect.y+this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["bottom-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${e} L ${this.boundingRect.x+this.boundingRect.width},${e}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["bottom-axis","label"],data:this.getTickValues().map(e=>({text:e.toString(),x:this.getScaleValue(e),y:this.boundingRect.y+this.axisConfig.labelPadding+(this.showTick?this.axisConfig.tickLength:0)+(this.showAxisLine?this.axisConfig.axisLineWidth:0),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){let e=this.boundingRect.y+(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["bottom-axis","ticks"],data:this.getTickValues().map(a=>({path:`M ${this.getScaleValue(a)},${e} L ${this.getScaleValue(a)},${e+this.axisConfig.tickLength}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["bottom-axis","title"],data:[{text:this.title,x:this.range[0]+(this.range[1]-this.range[0])/2,y:this.boundingRect.y+this.boundingRect.height-this.axisConfig.titlePadding-this.titleTextHeight,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForTopAxis(){let t=[];if(this.showAxisLine){let e=this.boundingRect.y+this.boundingRect.height-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["top-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${e} L ${this.boundingRect.x+this.boundingRect.width},${e}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["top-axis","label"],data:this.getTickValues().map(e=>({text:e.toString(),x:this.getScaleValue(e),y:this.boundingRect.y+(this.showTitle?this.titleTextHeight+this.axisConfig.titlePadding*2:0)+this.axisConfig.labelPadding,fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){let e=this.boundingRect.y;t.push({type:"path",groupTexts:["top-axis","ticks"],data:this.getTickValues().map(a=>({path:`M ${this.getScaleValue(a)},${e+this.boundingRect.height-(this.showAxisLine?this.axisConfig.axisLineWidth:0)} L ${this.getScaleValue(a)},${e+this.boundingRect.height-this.axisConfig.tickLength-(this.showAxisLine?this.axisConfig.axisLineWidth:0)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["top-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.axisConfig.titlePadding,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElements(){if(this.axisPosition==="left")return this.getDrawableElementsForLeftAxis();if(this.axisPosition==="right")throw Error("Drawing of right axis is not implemented");return this.axisPosition==="bottom"?this.getDrawableElementsForBottomAxis():this.axisPosition==="top"?this.getDrawableElementsForTopAxis():[]}};var ht=class extends K{static{s(this,"BandAxis")}constructor(t,e,a,n,l){super(t,n,l,e),this.categories=a,this.scale=At().domain(this.categories).range(this.getRange())}setRange(t){super.setRange(t)}recalculateScale(){this.scale=At().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(.5),nt.trace("BandAxis axis final categories, range: ",this.categories,this.getRange())}getTickValues(){return this.categories}getScaleValue(t){return this.scale(t)??this.getRange()[0]}};var lt=class extends K{static{s(this,"LinearAxis")}constructor(t,e,a,n,l){super(t,n,l,e),this.domain=a,this.scale=Tt().domain(this.domain).range(this.getRange())}getTickValues(){return this.scale.ticks()}recalculateScale(){let t=[...this.domain];this.axisPosition==="left"&&t.reverse(),this.scale=Tt().domain(t).range(this.getRange())}getScaleValue(t){return this.scale(t)}};function _t(i,t,e,a){let n=new Q(a);return ot(i)?new ht(t,e,i.categories,i.title,n):new lt(t,e,[i.min,i.max],i.title,n)}s(_t,"getAxis");var Rt=class{constructor(t,e,a,n){this.textDimensionCalculator=t;this.chartConfig=e;this.chartData=a;this.chartThemeConfig=n;this.boundingRect={x:0,y:0,width:0,height:0},this.showChartTitle=!1}static{s(this,"ChartTitle")}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){let e=this.textDimensionCalculator.getMaxDimension([this.chartData.title],this.chartConfig.titleFontSize),a=Math.max(e.width,t.width),n=e.height+2*this.chartConfig.titlePadding;return e.width<=a&&e.height<=n&&this.chartConfig.showTitle&&this.chartData.title&&(this.boundingRect.width=a,this.boundingRect.height=n,this.showChartTitle=!0),{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){let t=[];return this.showChartTitle&&t.push({groupTexts:["chart-title"],type:"text",data:[{fontSize:this.chartConfig.titleFontSize,text:this.chartData.title,verticalPos:"middle",horizontalPos:"center",x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.chartThemeConfig.titleColor,rotation:0}]}),t}};function Qt(i,t,e,a){let n=new Q(a);return new Rt(n,i,t,e)}s(Qt,"getChartTitleComponent");var ct=class{constructor(t,e,a,n,l){this.plotData=t;this.xAxis=e;this.yAxis=a;this.orientation=n;this.plotIndex=l}static{s(this,"LinePlot")}getDrawableElement(){let t=this.plotData.data.map(a=>[this.xAxis.getScaleValue(a[0]),this.yAxis.getScaleValue(a[1])]),e;return this.orientation==="horizontal"?e=Dt().y(a=>a[0]).x(a=>a[1])(t):e=Dt().x(a=>a[0]).y(a=>a[1])(t),e?[{groupTexts:["plot",`line-plot-${this.plotIndex}`],type:"path",data:[{path:e,strokeFill:this.plotData.strokeFill,strokeWidth:this.plotData.strokeWidth}]}]:[]}};var gt=class{constructor(t,e,a,n,l,x){this.barData=t;this.boundingRect=e;this.xAxis=a;this.yAxis=n;this.orientation=l;this.plotIndex=x}static{s(this,"BarPlot")}getDrawableElement(){let t=this.barData.data.map(l=>[this.xAxis.getScaleValue(l[0]),this.yAxis.getScaleValue(l[1])]),a=Math.min(this.xAxis.getAxisOuterPadding()*2,this.xAxis.getTickDistance())*(1-.05),n=a/2;return this.orientation==="horizontal"?[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(l=>({x:this.boundingRect.x,y:l[0]-n,height:a,width:l[1]-this.boundingRect.x,fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]:[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(l=>({x:l[0]-n,y:l[1],width:a,height:this.boundingRect.y+this.boundingRect.height-l[1],fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]}};var Pt=class{constructor(t,e,a){this.chartConfig=t;this.chartData=e;this.chartThemeConfig=a;this.boundingRect={x:0,y:0,width:0,height:0}}static{s(this,"BasePlot")}setAxes(t,e){this.xAxis=t,this.yAxis=e}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){return this.boundingRect.width=t.width,this.boundingRect.height=t.height,{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){if(!(this.xAxis&&this.yAxis))throw Error("Axes must be passed to render Plots");let t=[];for(let[e,a]of this.chartData.plots.entries())switch(a.type){case"line":{let n=new ct(a,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,e);t.push(...n.getDrawableElement())}break;case"bar":{let n=new gt(a,this.boundingRect,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,e);t.push(...n.getDrawableElement())}break}return t}};function Kt(i,t,e){return new Pt(i,t,e)}s(Kt,"getPlotComponent");var pt=class{constructor(t,e,a,n){this.chartConfig=t;this.chartData=e;this.componentStore={title:Qt(t,e,a,n),plot:Kt(t,e,a),xAxis:_t(e.xAxis,t.xAxis,{titleColor:a.xAxisTitleColor,labelColor:a.xAxisLabelColor,tickColor:a.xAxisTickColor,axisLineColor:a.xAxisLineColor},n),yAxis:_t(e.yAxis,t.yAxis,{titleColor:a.yAxisTitleColor,labelColor:a.yAxisLabelColor,tickColor:a.yAxisTickColor,axisLineColor:a.yAxisLineColor},n)}}static{s(this,"Orchestrator")}calculateVerticalSpace(){let t=this.chartConfig.width,e=this.chartConfig.height,a=0,n=0,l=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),x=Math.floor(e*this.chartConfig.plotReservedSpacePercent/100),y=this.componentStore.plot.calculateSpace({width:l,height:x});t-=y.width,e-=y.height,y=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:e}),n=y.height,e-=y.height,this.componentStore.xAxis.setAxisPosition("bottom"),y=this.componentStore.xAxis.calculateSpace({width:t,height:e}),e-=y.height,this.componentStore.yAxis.setAxisPosition("left"),y=this.componentStore.yAxis.calculateSpace({width:t,height:e}),a=y.width,t-=y.width,t>0&&(l+=t,t=0),e>0&&(x+=e,e=0),this.componentStore.plot.calculateSpace({width:l,height:x}),this.componentStore.plot.setBoundingBoxXY({x:a,y:n}),this.componentStore.xAxis.setRange([a,a+l]),this.componentStore.xAxis.setBoundingBoxXY({x:a,y:n+x}),this.componentStore.yAxis.setRange([n,n+x]),this.componentStore.yAxis.setBoundingBoxXY({x:0,y:n}),this.chartData.plots.some(A=>St(A))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateHorizontalSpace(){let t=this.chartConfig.width,e=this.chartConfig.height,a=0,n=0,l=0,x=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),y=Math.floor(e*this.chartConfig.plotReservedSpacePercent/100),A=this.componentStore.plot.calculateSpace({width:x,height:y});t-=A.width,e-=A.height,A=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:e}),a=A.height,e-=A.height,this.componentStore.xAxis.setAxisPosition("left"),A=this.componentStore.xAxis.calculateSpace({width:t,height:e}),t-=A.width,n=A.width,this.componentStore.yAxis.setAxisPosition("top"),A=this.componentStore.yAxis.calculateSpace({width:t,height:e}),e-=A.height,l=a+A.height,t>0&&(x+=t,t=0),e>0&&(y+=e,e=0),this.componentStore.plot.calculateSpace({width:x,height:y}),this.componentStore.plot.setBoundingBoxXY({x:n,y:l}),this.componentStore.yAxis.setRange([n,n+x]),this.componentStore.yAxis.setBoundingBoxXY({x:n,y:a}),this.componentStore.xAxis.setRange([l,l+y]),this.componentStore.xAxis.setBoundingBoxXY({x:0,y:l}),this.chartData.plots.some(V=>St(V))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateSpace(){this.chartConfig.chartOrientation==="horizontal"?this.calculateHorizontalSpace():this.calculateVerticalSpace()}getDrawableElement(){this.calculateSpace();let t=[];this.componentStore.plot.setAxes(this.componentStore.xAxis,this.componentStore.yAxis);for(let e of Object.values(this.componentStore))t.push(...e.getDrawableElements());return t}};var ut=class{static{s(this,"XYChartBuilder")}static build(t,e,a,n){return new pt(t,e,a,n).getDrawableElement()}};var J=0,Zt,tt=te(),et=Jt(),d=ee(),vt=et.plotColorPalette.split(",").map(i=>i.trim()),xt=!1,Et=!1;function Jt(){let i=It(),t=rt();return wt(i.xyChart,t.themeVariables.xyChart)}s(Jt,"getChartDefaultThemeConfig");function te(){let i=rt();return wt(Mt.xyChart,i.xyChart)}s(te,"getChartDefaultConfig");function ee(){return{yAxis:{type:"linear",title:"",min:1/0,max:-1/0},xAxis:{type:"band",title:"",categories:[]},title:"",plots:[]}}s(ee,"getChartDefaultData");function Lt(i){let t=rt();return Wt(i.trim(),t)}s(Lt,"textSanitizer");function ge(i){Zt=i}s(ge,"setTmpSVGG");function pe(i){i==="horizontal"?tt.chartOrientation="horizontal":tt.chartOrientation="vertical"}s(pe,"setOrientation");function ue(i){d.xAxis.title=Lt(i.text)}s(ue,"setXAxisTitle");function ie(i,t){d.xAxis={type:"linear",title:d.xAxis.title,min:i,max:t},xt=!0}s(ie,"setXAxisRangeData");function xe(i){d.xAxis={type:"band",title:d.xAxis.title,categories:i.map(t=>Lt(t.text))},xt=!0}s(xe,"setXAxisBand");function me(i){d.yAxis.title=Lt(i.text)}s(me,"setYAxisTitle");function fe(i,t){d.yAxis={type:"linear",title:d.yAxis.title,min:i,max:t},Et=!0}s(fe,"setYAxisRangeData");function de(i){let t=Math.min(...i),e=Math.max(...i),a=q(d.yAxis)?d.yAxis.min:1/0,n=q(d.yAxis)?d.yAxis.max:-1/0;d.yAxis={type:"linear",title:d.yAxis.title,min:Math.min(a,t),max:Math.max(n,e)}}s(de,"setYAxisRangeFromPlotData");function ae(i){let t=[];if(i.length===0)return t;if(!xt){let e=q(d.xAxis)?d.xAxis.min:1/0,a=q(d.xAxis)?d.xAxis.max:-1/0;ie(Math.min(e,1),Math.max(a,i.length))}if(Et||de(i),ot(d.xAxis)&&(t=d.xAxis.categories.map((e,a)=>[e,i[a]])),q(d.xAxis)){let e=d.xAxis.min,a=d.xAxis.max,n=(a-e)/(i.length-1),l=[];for(let x=e;x<=a;x+=n)l.push(`${x}`);t=l.map((x,y)=>[x,i[y]])}return t}s(ae,"transformDataWithoutCategory");function se(i){return vt[i===0?0:i%vt.length]}s(se,"getPlotColorFromPalette");function be(i,t){let e=ae(t);d.plots.push({type:"line",strokeFill:se(J),strokeWidth:2,data:e}),J++}s(be,"setLineData");function ye(i,t){let e=ae(t);d.plots.push({type:"bar",fill:se(J),data:e}),J++}s(ye,"setBarData");function Ce(){if(d.plots.length===0)throw Error("No Plot to render, please provide a plot with some data");return d.title=Ct(),ut.build(tt,d,et,Zt)}s(Ce,"getDrawableElem");function Ae(){return et}s(Ae,"getChartThemeConfig");function Te(){return tt}s(Te,"getChartConfig");function De(){return d}s(De,"getXYChartData");var we=s(function(){Ot(),J=0,tt=te(),d=ee(),et=Jt(),vt=et.plotColorPalette.split(",").map(i=>i.trim()),xt=!1,Et=!1},"clear"),ne={getDrawableElem:Ce,clear:we,setAccTitle:Ft,getAccTitle:Nt,setDiagramTitle:Ht,getDiagramTitle:Ct,getAccDescription:Gt,setAccDescription:jt,setOrientation:pe,setXAxisTitle:ue,setXAxisRangeData:ie,setXAxisBand:xe,setYAxisTitle:me,setYAxisRangeData:fe,setLineData:be,setBarData:ye,setTmpSVGG:ge,getChartThemeConfig:Ae,getChartConfig:Te,getXYChartData:De};var ke=s((i,t,e,a)=>{let n=a.db,l=n.getChartThemeConfig(),x=n.getChartConfig(),y=n.getXYChartData().plots[0].data.map(f=>f[1]);function A(f){return f==="top"?"text-before-edge":"middle"}s(A,"getDominantBaseLine");function V(f){return f==="left"?"start":f==="right"?"end":"middle"}s(V,"getTextAnchor");function B(f){return`translate(${f.x}, ${f.y}) rotate(${f.rotation||0})`}s(B,"getTextTransformation"),nt.debug(`Rendering xychart chart
`+i);let _=$t(t),L=_.append("g").attr("class","main"),I=L.append("rect").attr("width",x.width).attr("height",x.height).attr("class","background");zt(_,x.height,x.width,!0),_.attr("viewBox",`0 0 ${x.width} ${x.height}`),I.attr("fill",l.backgroundColor),n.setTmpSVGG(_.append("g").attr("class","mermaid-tmp-group"));let M=n.getDrawableElem(),R={};function W(f){let P=L,g="";for(let[X]of f.entries()){let v=L;X>0&&R[g]&&(v=R[g]),g+=f[X],P=R[g],P||(P=R[g]=v.append("g").attr("class",f[X]))}return P}s(W,"getGroup");for(let f of M){if(f.data.length===0)continue;let P=W(f.groupTexts);switch(f.type){case"rect":if(P.selectAll("rect").data(f.data).enter().append("rect").attr("x",g=>g.x).attr("y",g=>g.y).attr("width",g=>g.width).attr("height",g=>g.height).attr("fill",g=>g.fill).attr("stroke",g=>g.strokeFill).attr("stroke-width",g=>g.strokeWidth),x.showDataLabel)if(x.chartOrientation==="horizontal"){let v=function(c,w){let{data:S,label:D}=c;return w*D.length*.7<=S.width-10};var z=v;s(v,"fitsHorizontally");let g=.7,X=f.data.map((c,w)=>({data:c,label:y[w].toString()})).filter(c=>c.data.width>0&&c.data.height>0),C=X.map(c=>{let{data:w}=c,S=w.height*.7;for(;!v(c,S)&&S>0;)S-=1;return S}),j=Math.floor(Math.min(...C));P.selectAll("text").data(X).enter().append("text").attr("x",c=>c.data.x+c.data.width-10).attr("y",c=>c.data.y+c.data.height/2).attr("text-anchor","end").attr("dominant-baseline","middle").attr("fill","black").attr("font-size",`${j}px`).text(c=>c.label)}else{let v=function(c,w,S){let{data:D,label:U}=c,o=w*U.length*.7,h=D.x+D.width/2,p=h-o/2,u=h+o/2,b=p>=D.x&&u<=D.x+D.width,r=D.y+S+w<=D.y+D.height;return b&&r};var O=v;s(v,"fitsInBar");let g=10,X=f.data.map((c,w)=>({data:c,label:y[w].toString()})).filter(c=>c.data.width>0&&c.data.height>0),C=X.map(c=>{let{data:w,label:S}=c,D=w.width/(S.length*.7);for(;!v(c,D,10)&&D>0;)D-=1;return D}),j=Math.floor(Math.min(...C));P.selectAll("text").data(X).enter().append("text").attr("x",c=>c.data.x+c.data.width/2).attr("y",c=>c.data.y+10).attr("text-anchor","middle").attr("dominant-baseline","hanging").attr("fill","black").attr("font-size",`${j}px`).text(c=>c.label)}break;case"text":P.selectAll("text").data(f.data).enter().append("text").attr("x",0).attr("y",0).attr("fill",g=>g.fill).attr("font-size",g=>g.fontSize).attr("dominant-baseline",g=>A(g.verticalPos)).attr("text-anchor",g=>V(g.horizontalPos)).attr("transform",g=>B(g)).text(g=>g.text);break;case"path":P.selectAll("path").data(f.data).enter().append("path").attr("d",g=>g.path).attr("fill",g=>g.fill?g.fill:"none").attr("stroke",g=>g.strokeFill).attr("stroke-width",g=>g.strokeWidth);break}}},"draw"),re={draw:ke};var Ii={parser:qt,db:ne,renderer:re};export{Ii as diagram};
