/**
 * iFlytek Spark 面试系统 - 快速重叠测试脚本
 * Quick Overlap Test Script for iFlytek Spark Interview System
 * 
 * 在浏览器控制台中运行此脚本来快速验证修复效果
 */

// 快速测试函数
function quickOverlapTest() {
  console.log('🔍 开始快速重叠测试...')
  
  const results = {
    totalChecked: 0,
    passed: 0,
    failed: 0,
    details: []
  }

  // 测试1: 检查导出对话框是否存在重叠
  console.log('📋 测试1: 导出对话框重叠检查')
  const exportDialogs = document.querySelectorAll('.export-format-option')
  exportDialogs.forEach((option, index) => {
    results.totalChecked++
    const radioInput = option.querySelector('.el-radio__input')
    const formatOption = option.querySelector('.format-option')
    
    if (radioInput && formatOption) {
      const inputRect = radioInput.getBoundingClientRect()
      const optionRect = formatOption.getBoundingClientRect()
      
      // 检查单选按钮是否在格式选项的左侧（不重叠）
      const isProperlyPositioned = inputRect.right <= optionRect.left + 50 // 允许50px的重叠区域
      
      if (isProperlyPositioned) {
        results.passed++
        results.details.push(`✅ 导出选项${index + 1}: 单选按钮位置正确`)
      } else {
        results.failed++
        results.details.push(`❌ 导出选项${index + 1}: 单选按钮可能重叠`)
      }
    }
  })

  // 测试2: 检查统计卡片高度一致性
  console.log('📊 测试2: 统计卡片高度检查')
  const statCards = document.querySelectorAll('.stat-card')
  if (statCards.length > 0) {
    results.totalChecked++
    const heights = Array.from(statCards).map(card => card.getBoundingClientRect().height)
    const minHeight = Math.min(...heights)
    const maxHeight = Math.max(...heights)
    const heightDifference = maxHeight - minHeight
    
    if (heightDifference <= 10) { // 允许10px的高度差异
      results.passed++
      results.details.push(`✅ 统计卡片: 高度一致 (差异: ${heightDifference.toFixed(1)}px)`)
    } else {
      results.failed++
      results.details.push(`❌ 统计卡片: 高度不一致 (差异: ${heightDifference.toFixed(1)}px)`)
    }
  }

  // 测试3: 检查面板头部对齐
  console.log('🎛️ 测试3: 面板头部对齐检查')
  const panelHeaders = document.querySelectorAll('.panel-header, .card-header')
  panelHeaders.forEach((header, index) => {
    results.totalChecked++
    const computedStyle = window.getComputedStyle(header)
    const minHeight = parseInt(computedStyle.minHeight)
    const paddingBottom = parseInt(computedStyle.paddingBottom)
    
    if (minHeight >= 40 && paddingBottom >= 4) {
      results.passed++
      results.details.push(`✅ 面板头部${index + 1}: 尺寸设置正确`)
    } else {
      results.failed++
      results.details.push(`❌ 面板头部${index + 1}: 尺寸设置可能不足`)
    }
  })

  // 测试4: 检查Element Plus组件样式
  console.log('🔧 测试4: Element Plus组件样式检查')
  const elButtons = document.querySelectorAll('.el-button')
  if (elButtons.length > 0) {
    results.totalChecked++
    const sampleButton = elButtons[0]
    const computedStyle = window.getComputedStyle(sampleButton)
    const fontFamily = computedStyle.fontFamily
    
    if (fontFamily.includes('Microsoft YaHei') || fontFamily.includes('SimHei')) {
      results.passed++
      results.details.push(`✅ Element Plus按钮: 中文字体设置正确`)
    } else {
      results.failed++
      results.details.push(`❌ Element Plus按钮: 中文字体可能未正确设置`)
    }
  }

  // 测试5: 检查文字溢出
  console.log('📝 测试5: 文字溢出检查')
  const textContainers = document.querySelectorAll('.stat-value, .stat-label, .format-name, .format-desc')
  let overflowCount = 0
  textContainers.forEach((container, index) => {
    results.totalChecked++
    const isOverflowing = container.scrollWidth > container.clientWidth || 
                         container.scrollHeight > container.clientHeight
    
    if (!isOverflowing) {
      results.passed++
    } else {
      results.failed++
      overflowCount++
    }
  })
  
  if (overflowCount === 0) {
    results.details.push(`✅ 文字溢出: 无溢出问题 (检查了${textContainers.length}个容器)`)
  } else {
    results.details.push(`❌ 文字溢出: 发现${overflowCount}个溢出问题`)
  }

  // 生成测试报告
  console.log('\n📋 快速重叠测试报告')
  console.log('=' .repeat(40))
  console.log(`📊 总测试项: ${results.totalChecked}`)
  console.log(`✅ 通过: ${results.passed}`)
  console.log(`❌ 失败: ${results.failed}`)
  console.log(`📈 通过率: ${(results.passed / results.totalChecked * 100).toFixed(1)}%`)
  
  console.log('\n📝 详细结果:')
  results.details.forEach(detail => {
    console.log(`  ${detail}`)
  })

  // 总体评估
  const passRate = results.passed / results.totalChecked
  if (passRate >= 0.9) {
    console.log('\n🎉 总体评估: 优秀 - 重叠问题基本解决')
  } else if (passRate >= 0.7) {
    console.log('\n👍 总体评估: 良好 - 大部分问题已解决')
  } else if (passRate >= 0.5) {
    console.log('\n⚠️ 总体评估: 一般 - 仍有部分问题需要修复')
  } else {
    console.log('\n❌ 总体评估: 需要改进 - 存在较多重叠问题')
  }

  return results
}

// 自动运行测试（延迟3秒等待页面加载）
setTimeout(() => {
  if (document.readyState === 'complete') {
    console.log('🚀 自动运行快速重叠测试...')
    quickOverlapTest()
  }
}, 3000)

// 导出到全局作用域
window.quickOverlapTest = quickOverlapTest

console.log('📋 快速重叠测试脚本已加载')
console.log('💡 使用方法: 在控制台运行 quickOverlapTest()')
