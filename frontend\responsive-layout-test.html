<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek Spark 面试系统 - 响应式布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 32px;
            color: #1890ff;
        }

        .layout-demo {
            display: grid;
            grid-template-columns: 1.2fr 0.8fr;
            gap: 32px;
            min-height: 500px;
        }

        .candidate-section {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(24, 144, 255, 0.1);
        }

        .analysis-section {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(24, 144, 255, 0.1);
        }

        .answer-input-section {
            margin-top: 24px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(24, 144, 255, 0.1);
        }

        .quick-actions-panel {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(24, 144, 255, 0.1);
            margin-top: 16px;
        }

        .actions-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f2f5;
        }

        .actions-header h4 {
            margin: 0;
            color: #1890ff;
            font-size: 16px;
            font-weight: 600;
        }

        .actions-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .question-navigation,
        .interview-control,
        .quick-operations {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 16px;
        }

        .nav-title {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
        }

        .question-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 8px;
            max-width: 100%;
        }

        .question-nav-btn {
            min-width: 40px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .question-nav-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .question-nav-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .control-buttons,
        .operation-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .control-btn {
            flex: 1;
            min-width: 100px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .control-btn.primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .control-btn.danger {
            background: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }

        /* 响应式设计优化 */
        @media (max-width: 1400px) {
            .quick-actions-panel {
                padding: 16px;
            }
            
            .question-buttons {
                grid-template-columns: repeat(auto-fit, minmax(35px, 1fr));
                gap: 6px;
            }
            
            .question-nav-btn {
                min-width: 35px;
                height: 30px;
                font-size: 12px;
            }
        }

        @media (max-width: 1200px) {
            .layout-demo {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .quick-actions-panel {
                margin-top: 12px;
            }
            
            .actions-content {
                gap: 16px;
            }
            
            .question-buttons {
                grid-template-columns: repeat(auto-fit, minmax(32px, 1fr));
                gap: 4px;
            }
            
            .control-buttons,
            .operation-buttons {
                flex-direction: column;
                gap: 6px;
            }
            
            .control-btn {
                min-width: auto;
                width: 100%;
            }
        }

        /* 半屏显示优化 */
        @media (max-width: 1200px) and (min-width: 800px) {
            .actions-content {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 12px;
            }
            
            .question-buttons {
                grid-template-columns: repeat(auto-fit, minmax(30px, 1fr));
                gap: 4px;
                max-height: 80px;
                overflow-y: auto;
            }
            
            .question-nav-btn {
                min-width: 30px;
                height: 28px;
                font-size: 11px;
            }
        }

        @media (max-width: 768px) {
            .quick-actions-panel {
                padding: 12px;
                margin-top: 8px;
            }
            
            .actions-header h4 {
                font-size: 14px;
            }
            
            .question-buttons {
                grid-template-columns: repeat(auto-fit, minmax(28px, 1fr));
                gap: 3px;
            }
            
            .question-nav-btn {
                min-width: 28px;
                height: 28px;
                font-size: 11px;
            }
            
            .nav-title {
                font-size: 12px;
                margin-bottom: 8px;
            }
            
            .question-navigation,
            .interview-control,
            .quick-operations {
                padding: 12px;
            }
        }

        @media (max-width: 480px) {
            .quick-actions-panel {
                padding: 8px;
            }
            
            .actions-content {
                gap: 12px;
            }
            
            .question-buttons {
                grid-template-columns: repeat(5, 1fr);
                gap: 2px;
            }
            
            .question-nav-btn {
                min-width: 24px;
                height: 24px;
                font-size: 10px;
            }
            
            .control-btn {
                font-size: 12px;
                padding: 6px 12px;
            }
        }

        .test-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            color: #1890ff;
        }

        .test-info h3 {
            margin-bottom: 8px;
        }

        .test-info ul {
            margin-left: 20px;
        }

        .test-info li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>iFlytek Spark 面试系统响应式布局测试</h1>
            <p>请调整浏览器窗口大小来测试响应式效果</p>
        </div>

        <div class="test-info">
            <h3>测试说明：</h3>
            <ul>
                <li>全屏模式：双列布局，所有元素正常显示</li>
                <li>半屏模式（800px-1200px）：单列布局，快捷操作面板优化为三列网格</li>
                <li>平板模式（768px以下）：紧凑布局，按钮和文字适当缩小</li>
                <li>手机模式（480px以下）：最小化布局，问题导航固定为5列</li>
            </ul>
        </div>

        <div class="layout-demo">
            <div class="candidate-section">
                <h3>您的回答</h3>
                <div class="answer-input-section">
                    <h4>智能回答输入区域</h4>
                    <p>这里是回答输入框的位置...</p>
                </div>

                <div class="quick-actions-panel">
                    <div class="actions-header">
                        <span>⚙️</span>
                        <h4>快捷操作</h4>
                    </div>
                    <div class="actions-content">
                        <div class="question-navigation">
                            <div class="nav-title">
                                <span>📄</span>
                                问题导航
                            </div>
                            <div class="question-buttons">
                                <button class="question-nav-btn active">1</button>
                                <button class="question-nav-btn">2</button>
                                <button class="question-nav-btn">3</button>
                                <button class="question-nav-btn">4</button>
                                <button class="question-nav-btn">5</button>
                                <button class="question-nav-btn">6</button>
                                <button class="question-nav-btn">7</button>
                                <button class="question-nav-btn">8</button>
                                <button class="question-nav-btn">9</button>
                                <button class="question-nav-btn">10</button>
                            </div>
                        </div>

                        <div class="interview-control">
                            <div class="nav-title">
                                <span>⚙️</span>
                                面试控制
                            </div>
                            <div class="control-buttons">
                                <button class="control-btn">暂停面试</button>
                                <button class="control-btn danger">结束面试</button>
                            </div>
                        </div>

                        <div class="quick-operations">
                            <div class="nav-title">
                                <span>⭐</span>
                                快速操作
                            </div>
                            <div class="operation-buttons">
                                <button class="control-btn primary">提交回答</button>
                                <button class="control-btn">跳过问题</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="analysis-section">
                <h3>AI分析面板</h3>
                <p>这里是AI分析内容的位置...</p>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.question-nav-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.question-nav-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 显示当前窗口尺寸
        function updateWindowSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            document.title = `iFlytek Spark 测试 - ${width}x${height}`;
        }

        window.addEventListener('resize', updateWindowSize);
        updateWindowSize();
    </script>
</body>
</html>
