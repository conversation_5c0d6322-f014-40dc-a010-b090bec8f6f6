{"version": 3, "file": "background-tests.js", "sourceRoot": "", "sources": ["../../../../../src/css/property-descriptors/__tests__/background-tests.ts"], "names": [], "mappings": ";;AAAA,iCAAuC;AACvC,8CAA2C;AAC3C,wDAAoD;AAEpD,2CAAuC;AACvC,2CAAsC;AAEtC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACnC,iDAA8C;AAE9C,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;AAEpC,IAAM,oBAAoB,GAAG,UAAC,OAAgB,EAAE,KAAa;IACzD,OAAA,kCAAe,CAAC,KAAK,CAAC,OAAO,EAAE,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAAzD,CAAyD,CAAC;AAE9D,QAAQ,CAAC,sBAAsB,EAAE;IAC7B,IAAI,OAAgB,CAAC;IACrB,UAAU,CAAC;QACP,8DAA8D;QAC9D,OAAO,GAAG,IAAI,iBAAO,CAAC,EAAS,EAAE,EAAS,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,kBAAkB,EAAE;QACzB,EAAE,CAAC,MAAM,EAAE;YACP,wBAAe,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;YAChC,wBAAe,CACX,oBAAoB,CAAC,OAAO,EAAE,qEAAqE,CAAC,EACpG;gBACI,EAAC,GAAG,EAAE,6BAA6B,EAAE,IAAI,aAAkB,EAAC;gBAC5D,EAAC,GAAG,EAAE,8BAA8B,EAAE,IAAI,aAAkB,EAAC;aAChE,CACJ,CAAC;YACF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,CAAC;YACnF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4GAA4G,EAAE;YAC7G,OAAA,wBAAe,CACX,oBAAoB,CAChB,OAAO,EACP,4GAA4G,CAC/G,EACD;gBACI;oBACI,KAAK,EAAE,WAAG,CAAC,GAAG,CAAC;oBACf,IAAI,yBAA8B;oBAClC,KAAK,EAAE;wBACH,EAAC,KAAK,EAAE,YAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAC;wBAC3C,EAAC,KAAK,EAAE,YAAI,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAC;qBAC5C;iBACJ;gBACD,EAAC,GAAG,EAAE,iCAAiC,EAAE,IAAI,aAAkB,EAAC;aACnE,CACJ;QAhBD,CAgBC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}