/**
 * iFlytek AI面试官意图识别测试
 * 测试新的智能意图分析功能
 */

// 模拟意图分析函数（从Vue组件中提取）
const analyzeUserIntent = (answer) => {
  const answerLower = answer.toLowerCase()

  // 1. 检测明确要求答案的表达
  const requestAnswerPatterns = [
    '请告诉我正确答案', '告诉我答案', '正确答案是什么', '标准答案',
    '请给出答案', '能告诉我', '请直接说', '直接告诉我',
    '答案应该是什么', '请提供答案', '给我答案'
  ]

  // 2. 检测要求引导的表达（扩展和优化）
  const requestGuidancePatterns = [
    '给我一些引导', '可以给我提示', '给我一些思路', '能否指导',
    '给我一些方向', '可以提示一下', '给我一些建议', '能给我启发',
    '我需要引导', '希望得到指导', '可以帮我分析', '得到一些指导',
    '希望得到.*指导', '希望得到.*建议', '给我.*指导', '请给我.*指导',
    '可以指导', '能指导', '指导一下', '建议一下', '提示一下'
  ]

  // 3. 检测一般不知道的表达
  const unknownPatterns = [
    '不知道', '不清楚', '没有经验', '不了解', '不会', '没做过',
    '不太懂', '不确定', '完全不懂', '没有接触过', '不太了解'
  ]

  // 使用正则表达式进行更精确的匹配
  const hasRequestAnswer = requestAnswerPatterns.some(pattern => {
    const regex = new RegExp(pattern, 'i')
    return regex.test(answerLower)
  })

  const hasRequestGuidance = requestGuidancePatterns.some(pattern => {
    const regex = new RegExp(pattern, 'i')
    return regex.test(answerLower)
  })

  const hasUnknownKeywords = unknownPatterns.some(pattern => answerLower.includes(pattern))

  // 优先级判断：明确要求答案 > 要求引导 > 一般不知道
  if (hasRequestAnswer) {
    return 'request_answer'  // 明确要求标准答案
  } else if (hasRequestGuidance) {
    return 'request_guidance'  // 要求思路引导
  } else if (hasUnknownKeywords) {
    // 对于包含"不知道"等词汇的回答，进一步判断
    if (answer.trim().length > 100) {
      // 长回答中包含不知道，可能是部分了解，检查是否有引导需求
      if (hasRequestGuidance) {
        return 'request_guidance'
      } else {
        return 'normal_answer'  // 长回答认为是正常回答
      }
    } else {
      return 'express_unknown'  // 短回答认为是不知道
    }
  } else {
    return 'normal_answer'  // 正常技术回答
  }
}

// 测试用例
const testCases = [
  // 明确要求答案的情况
  {
    input: "我不知道，请告诉我正确答案",
    expected: "request_answer",
    description: "用户明确要求标准答案"
  },
  {
    input: "不清楚，能告诉我答案应该是什么吗？",
    expected: "request_answer", 
    description: "用户询问标准答案"
  },
  {
    input: "请直接告诉我正确答案",
    expected: "request_answer",
    description: "直接要求答案"
  },
  
  // 要求引导的情况
  {
    input: "我不会，可以给我一些引导吗",
    expected: "request_guidance",
    description: "用户要求思路引导"
  },
  {
    input: "不太懂，能否给我一些思路和方向？",
    expected: "request_guidance",
    description: "用户要求思考方向"
  },
  {
    input: "希望得到一些指导和建议",
    expected: "request_guidance",
    description: "用户要求指导"
  },
  
  // 一般性不知道
  {
    input: "不知道",
    expected: "express_unknown",
    description: "简单的不知道"
  },
  {
    input: "我不太了解这个技术",
    expected: "express_unknown",
    description: "表达不了解"
  },
  {
    input: "完全没有接触过",
    expected: "express_unknown",
    description: "没有经验"
  },
  
  // 正常技术回答
  {
    input: "深度学习模型优化主要包括量化、剪枝、知识蒸馏等技术手段，我在项目中使用过INT8量化来减少模型大小...",
    expected: "normal_answer",
    description: "详细的技术回答"
  },
  {
    input: "机器学习包括监督学习、无监督学习和强化学习三大类",
    expected: "normal_answer",
    description: "基础技术回答"
  },
  
  // 边界情况测试
  {
    input: "我不知道具体的实现细节，但我觉得可能需要考虑性能优化和内存管理等方面，请给我一些指导",
    expected: "request_guidance",
    description: "复合表达，优先识别引导需求"
  },
  {
    input: "不太清楚，请告诉我标准答案",
    expected: "request_answer",
    description: "复合表达，优先识别答案需求"
  }
]

// 执行测试
console.log('🧪 iFlytek AI面试官意图识别测试开始\n')

let passedTests = 0
let totalTests = testCases.length

testCases.forEach((testCase, index) => {
  const result = analyzeUserIntent(testCase.input)
  const passed = result === testCase.expected
  
  console.log(`测试 ${index + 1}: ${testCase.description}`)
  console.log(`输入: "${testCase.input}"`)
  console.log(`期望: ${testCase.expected}`)
  console.log(`实际: ${result}`)
  console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`)
  console.log('---')
  
  if (passed) passedTests++
})

console.log(`\n📊 测试结果总结:`)
console.log(`通过: ${passedTests}/${totalTests}`)
console.log(`成功率: ${(passedTests/totalTests*100).toFixed(1)}%`)

if (passedTests === totalTests) {
  console.log('🎉 所有测试通过！意图识别功能工作正常。')
} else {
  console.log('⚠️  部分测试失败，需要进一步优化意图识别逻辑。')
}

// 模拟实际对话场景测试
console.log('\n🎭 模拟实际对话场景测试:')

const dialogueScenarios = [
  {
    question: "请详细说明您在深度学习模型优化方面的实践经验",
    userResponse: "我不知道，请告诉我正确答案",
    expectedIntent: "request_answer",
    expectedResponse: "应该直接提供详细的技术答案"
  },
  {
    question: "描述一下机器学习的基本概念",
    userResponse: "不太懂，可以给我一些思路吗",
    expectedIntent: "request_guidance", 
    expectedResponse: "应该提供思考方向和引导"
  },
  {
    question: "您对人工智能有什么了解？",
    userResponse: "不太了解",
    expectedIntent: "express_unknown",
    expectedResponse: "应该提供鼓励性的学习建议"
  }
]

dialogueScenarios.forEach((scenario, index) => {
  const intent = analyzeUserIntent(scenario.userResponse)
  console.log(`场景 ${index + 1}:`)
  console.log(`问题: ${scenario.question}`)
  console.log(`用户回答: "${scenario.userResponse}"`)
  console.log(`识别意图: ${intent}`)
  console.log(`期望意图: ${scenario.expectedIntent}`)
  console.log(`AI应该: ${scenario.expectedResponse}`)
  console.log(`识别正确: ${intent === scenario.expectedIntent ? '✅' : '❌'}`)
  console.log('---')
})

console.log('\n✨ 意图识别测试完成！')
