#!/usr/bin/env node

/**
 * 提交按钮删除验证脚本
 * 验证"您的回答"输入框右下角的蓝色提交按钮是否被成功删除
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 验证右下角提交按钮删除...\n')

// 检查的文件
const targetFile = 'src/views/TextPrimaryInterviewPage.vue'
const fullPath = path.join(__dirname, targetFile)

console.log(`📄 检查文件: ${targetFile}`)

if (!fs.existsSync(fullPath)) {
  console.log(`❌ 文件不存在: ${targetFile}`)
  process.exit(1)
}

const content = fs.readFileSync(fullPath, 'utf8')
console.log(`✅ 文件存在 (${content.length} 字符)\n`)

// 验证项目
const verificationChecks = [
  {
    name: '右下角提交按钮HTML删除',
    pattern: /class="corner-submit-btn"/,
    description: '检查corner-submit-btn类的按钮是否已删除',
    shouldExist: false
  },
  {
    name: '右下角提交按钮CSS删除',
    pattern: /\.corner-submit-btn\s*{/,
    description: '检查corner-submit-btn的CSS样式是否已删除',
    shouldExist: false
  },
  {
    name: '右下角提交按钮注释删除',
    pattern: /<!-- 右下角提交按钮 -->/,
    description: '检查右下角提交按钮的注释是否已删除',
    shouldExist: false
  },
  {
    name: '底部提交按钮保留',
    pattern: /<el-button[^>]*@click="submitAnswer"[^>]*>\s*<el-icon><Promotion \/><\/el-icon>\s*提交回答\s*<\/el-button>/s,
    description: '确认底部的主要提交按钮仍然存在',
    shouldExist: true
  },
  {
    name: 'submitAnswer函数保留',
    pattern: /const submitAnswer = async \(\) => {/,
    description: '确认submitAnswer函数仍然存在',
    shouldExist: true
  },
  {
    name: 'Ctrl+Enter快捷键保留',
    pattern: /@keydown\.ctrl\.enter="submitAnswer"/,
    description: '确认Ctrl+Enter快捷键功能仍然存在',
    shouldExist: true
  }
]

let allChecksPass = true
let passedChecks = 0

console.log('🔧 执行提交按钮删除验证...\n')

verificationChecks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name}`)
  console.log(`   描述: ${check.description}`)
  
  const result = check.pattern.test(content)
  const passed = check.shouldExist ? result : !result
  
  if (passed) {
    console.log(`   ✅ 通过`)
    passedChecks++
  } else {
    console.log(`   ❌ 失败`)
    if (check.shouldExist) {
      console.log(`   预期: 应该存在，实际: 不存在`)
    } else {
      console.log(`   预期: 不应该存在，实际: 仍然存在`)
    }
    allChecksPass = false
  }
  
  console.log()
})

// 额外检查：查找所有提交相关的元素
console.log('📊 提交功能元素检查:')

// 检查所有提交按钮
const submitButtonMatches = content.match(/<el-button[^>]*@click="submitAnswer"[^>]*>/g) || []
console.log(`   发现 ${submitButtonMatches.length} 个提交按钮`)
submitButtonMatches.forEach((match, index) => {
  const isCornerBtn = match.includes('corner-submit-btn')
  const location = isCornerBtn ? '(右下角)' : '(底部)'
  console.log(`     ${index + 1}. ${location} ${match.substring(0, 50)}...`)
})

// 检查提交相关的CSS类
const submitCssMatches = content.match(/\.[a-zA-Z-]*submit[a-zA-Z-]*\s*{/g) || []
console.log(`   发现 ${submitCssMatches.length} 个提交相关CSS类`)
submitCssMatches.forEach((match, index) => {
  console.log(`     ${index + 1}. ${match}`)
})

// 检查快捷键设置
const shortcutMatches = content.match(/@keydown\.[^=]+="submitAnswer"/g) || []
console.log(`   发现 ${shortcutMatches.length} 个提交快捷键`)
shortcutMatches.forEach((match, index) => {
  console.log(`     ${index + 1}. ${match}`)
})

console.log()

// 检查提交功能的完整性
console.log('📋 提交功能完整性检查:')

const functionalityChecks = [
  { name: 'submitAnswer函数', pattern: /const submitAnswer/, found: false },
  { name: '底部提交按钮', pattern: /提交回答/, found: false },
  { name: 'Ctrl+Enter提示', pattern: /Ctrl \+ Enter/, found: false },
  { name: '输入验证', pattern: /!currentTextInput\.trim\(\)/, found: false },
  { name: '加载状态', pattern: /:loading="isProcessingMultimodal"/, found: false }
]

functionalityChecks.forEach(check => {
  check.found = check.pattern.test(content)
  const status = check.found ? '✅' : '❌'
  console.log(`   ${status} ${check.name}: ${check.found ? '存在' : '缺失'}`)
})

console.log()

// 生成测试报告
console.log('📊 右下角提交按钮删除验证报告')
console.log('='.repeat(50))

if (allChecksPass) {
  console.log('🎉 右下角提交按钮删除成功！')
  console.log('')
  console.log('✅ 完成的删除:')
  console.log('  - 右下角提交按钮HTML元素 (corner-submit-btn)')
  console.log('  - 相关CSS样式 (.corner-submit-btn)')
  console.log('  - 按钮注释和相关代码')
  console.log('')
  console.log('✅ 保留的功能:')
  console.log('  - 底部主要提交按钮 ("提交回答")')
  console.log('  - submitAnswer函数和逻辑')
  console.log('  - Ctrl + Enter 快捷键提交')
  console.log('  - 输入验证和加载状态')
  console.log('  - 所有提交相关的用户提示')
  console.log('')
  console.log('✅ 用户体验改进:')
  console.log('  - 输入框右下角更加简洁')
  console.log('  - 减少重复的提交按钮')
  console.log('  - 保持主要提交功能的可用性')
  console.log('  - 界面布局更加整洁')
} else {
  console.log('⚠️  右下角提交按钮删除验证中发现问题')
  console.log('')
  console.log(`📊 验证结果: ${passedChecks}/${verificationChecks.length} 项通过`)
  console.log('')
  console.log('💡 建议:')
  console.log('  - 检查上述失败的验证项目')
  console.log('  - 确保删除了正确的元素')
  console.log('  - 确保保留了必要的功能')
  console.log('  - 重新运行此验证脚本')
}

console.log('')
console.log('🔗 相关信息:')
console.log('  - 目标文件: src/views/TextPrimaryInterviewPage.vue')
console.log('  - 删除元素: 右下角提交按钮 (corner-submit-btn)')
console.log('  - 保留功能: 底部提交按钮 + 快捷键 + 提交逻辑')
console.log('  - 修改类型: HTML元素和CSS样式删除')

console.log('')
console.log('📞 测试访问:')
console.log('  - 文本面试页面: http://localhost:8080/text-primary-interview')
console.log('  - 产品演示页面: http://localhost:8080/demo')

console.log('')
console.log('🎯 预期效果:')
console.log('  - "您的回答"输入框右下角的蓝色提交按钮消失')
console.log('  - 底部的"提交回答"按钮仍然可用')
console.log('  - Ctrl + Enter 快捷键仍然可用')
console.log('  - 所有提交功能正常工作')

console.log('')
console.log('🛠️ 替代提交方式:')
console.log('  1. 点击底部的"提交回答"按钮')
console.log('  2. 使用 Ctrl + Enter 快捷键')
console.log('  3. 所有提交逻辑和验证保持不变')

export { allChecksPass, passedChecks, verificationChecks }
