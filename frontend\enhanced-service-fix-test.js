/**
 * iFlytek Spark 增强服务修复测试脚本
 * 测试修复后的 EnhancedIflytekSparkService 功能
 */

console.log('🔧 iFlytek Spark 增强服务修复测试');
console.log('='.repeat(60));

// 测试候选人画像数据
const testCandidateProfile = {
  name: '张三',
  position: 'AI工程师',
  experience: '3年机器学习开发经验',
  education: '计算机科学硕士',
  skills: ['Python', 'TensorFlow', 'PyTorch', '机器学习', '深度学习', 'Vue.js'],
  company: '科技公司'
};

// 测试函数
async function testEnhancedService() {
  try {
    console.log('📋 1. 测试服务初始化...');
    
    // 动态导入服务（适配ES模块）
    const { default: EnhancedIflytekSparkService } = await import('./src/services/enhancedIflytekSparkService.js');
    const service = new EnhancedIflytekSparkService();
    
    console.log('✅ 服务初始化成功');
    
    console.log('\n🧠 2. 测试候选人画像分析...');
    
    // 测试候选人画像分析
    try {
      const candidateAnalysis = await service.analyzeCandidateProfile(testCandidateProfile);
      console.log('✅ 候选人画像分析成功');
      console.log('📊 分析结果:', {
        technicalLevel: candidateAnalysis.technicalLevel,
        learningCapacity: candidateAnalysis.learningCapacity,
        strengths: candidateAnalysis.strengths?.slice(0, 3),
        challenges: candidateAnalysis.challenges?.slice(0, 2)
      });
    } catch (error) {
      console.error('❌ 候选人画像分析失败:', error.message);
    }
    
    console.log('\n🎯 3. 测试技术领域适配...');
    
    // 测试技术领域适配
    try {
      const domainConfig = service.adaptTechnicalDomain(testCandidateProfile, 'comprehensive');
      console.log('✅ 技术领域适配成功');
      console.log('📊 适配结果:', {
        primaryDomain: domainConfig.primaryDomain,
        secondaryDomains: domainConfig.secondaryDomains,
        scores: Object.entries(domainConfig.scores)
          .map(([domain, score]) => `${domain}: ${(score * 100).toFixed(1)}%`)
          .slice(0, 3)
      });
    } catch (error) {
      console.error('❌ 技术领域适配失败:', error.message);
    }
    
    console.log('\n🚀 4. 测试面试会话初始化...');
    
    // 测试面试会话初始化
    try {
      const sessionConfig = await service.initializeInterviewSession(testCandidateProfile, 'comprehensive');
      console.log('✅ 面试会话初始化成功');
      console.log('📊 会话配置:', {
        sessionId: sessionConfig.sessionId,
        primaryDomain: sessionConfig.domainConfig?.primaryDomain,
        difficultyLevel: sessionConfig.difficultyConfig?.level,
        questionCount: sessionConfig.questionPlan?.totalQuestions
      });
    } catch (error) {
      console.error('❌ 面试会话初始化失败:', error.message);
    }
    
    console.log('\n🔍 5. 测试辅助方法...');
    
    // 测试学习能力指标获取
    try {
      const learningIndicators = service.getLearningIndicators(testCandidateProfile);
      console.log('✅ 学习能力指标获取成功');
      console.log('📊 学习指标:', learningIndicators);
    } catch (error) {
      console.error('❌ 学习能力指标获取失败:', error.message);
    }
    
    // 测试领域分数计算
    try {
      const aiScore = service.calculateDomainScore(
        testCandidateProfile.skills,
        testCandidateProfile.position,
        ['AI', '机器学习', '深度学习', '算法', 'Python', 'TensorFlow']
      );
      console.log('✅ 领域分数计算成功');
      console.log('📊 AI领域匹配度:', `${(aiScore * 100).toFixed(1)}%`);
    } catch (error) {
      console.error('❌ 领域分数计算失败:', error.message);
    }
    
    // 测试领域适配策略创建
    try {
      const strategy = service.createDomainAdaptationStrategy('ai', {
        ai: 0.9,
        frontend: 0.6,
        backend: 0.3,
        bigdata: 0.2,
        iot: 0.1
      });
      console.log('✅ 领域适配策略创建成功');
      console.log('📊 适配策略:', {
        focus: strategy.focus,
        approach: strategy.approach,
        questionTypes: strategy.questionTypes?.slice(0, 2),
        crossDomainIntegration: strategy.crossDomainIntegration
      });
    } catch (error) {
      console.error('❌ 领域适配策略创建失败:', error.message);
    }
    
    console.log('\n🎉 测试完成！');
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
  }
}

// 浏览器环境测试
function testInBrowser() {
  console.log('🌐 在浏览器环境中测试...');
  
  // 检查服务是否可用
  if (typeof window !== 'undefined' && window.EnhancedIflytekSparkService) {
    const service = new window.EnhancedIflytekSparkService();
    
    console.log('✅ 浏览器环境服务可用');
    
    // 测试基础方法
    try {
      const indicators = service.getLearningIndicators(testCandidateProfile);
      console.log('✅ getLearningIndicators 方法正常:', indicators);
    } catch (error) {
      console.error('❌ getLearningIndicators 方法错误:', error.message);
    }
    
    try {
      const score = service.calculateDomainScore(
        ['Python', 'AI', '机器学习'],
        'AI工程师',
        ['AI', '机器学习', '深度学习']
      );
      console.log('✅ calculateDomainScore 方法正常:', `${(score * 100).toFixed(1)}%`);
    } catch (error) {
      console.error('❌ calculateDomainScore 方法错误:', error.message);
    }
    
    try {
      const strategy = service.createDomainAdaptationStrategy('ai', { ai: 0.9, frontend: 0.5 });
      console.log('✅ createDomainAdaptationStrategy 方法正常:', strategy.focus);
    } catch (error) {
      console.error('❌ createDomainAdaptationStrategy 方法错误:', error.message);
    }
    
  } else {
    console.warn('⚠️ 浏览器环境中服务不可用，请在面试页面中运行此测试');
  }
}

// 修复验证清单
function showFixVerificationChecklist() {
  console.log('\n📋 修复验证清单:');
  console.log('='.repeat(40));
  
  const checklist = [
    '✅ 添加 getLearningIndicators 方法',
    '✅ 添加 calculateDomainScore 方法', 
    '✅ 添加 createDomainAdaptationStrategy 方法',
    '✅ 确保 extractYearsFromText 方法存在',
    '✅ 确保 professionalDomains 属性存在',
    '✅ 修复候选人画像分析功能',
    '✅ 修复技术领域适配功能',
    '✅ 修复面试会话初始化功能'
  ];
  
  checklist.forEach(item => console.log(item));
  
  console.log('\n🎯 使用说明:');
  console.log('1. 在 Node.js 环境中运行: node enhanced-service-fix-test.js');
  console.log('2. 在浏览器控制台中运行: testInBrowser()');
  console.log('3. 在面试页面中检查是否还有错误信息');
}

// 根据环境选择测试方式
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.testEnhancedServiceFix = testInBrowser;
  window.showFixVerificationChecklist = showFixVerificationChecklist;
  testInBrowser();
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  testEnhancedService().then(() => {
    showFixVerificationChecklist();
  });
} else {
  // 其他环境
  showFixVerificationChecklist();
}

console.log('\n💡 提示: 如果仍有错误，请检查:');
console.log('1. 方法名称拼写是否正确');
console.log('2. 方法参数是否匹配');
console.log('3. 依赖的其他方法是否存在');
console.log('4. 浏览器控制台是否还有其他错误信息');
