#!/usr/bin/env node

/**
 * 🔧 简化系统验证工具
 * Simplified System Validation Tool
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 iFlytek Spark系统集成验证');
console.log('=' .repeat(50));

// 检查文件列表
const filesToCheck = [
  // Vue组件
  { path: 'src/components/Demo/iFlytek SparkShowcase.vue', type: 'component', name: 'iFlytek Spark演示组件' },
  { path: 'src/components/Demo/ResponsiveMediaViewer.vue', type: 'component', name: '响应式媒体查看器' },
  { path: 'src/utils/mediaIntegrationTest.js', type: 'utility', name: '媒体集成测试工具' },
  { path: 'src/views/DemoPage.vue', type: 'view', name: '演示页面' },
  
  // 静态资源
  { path: 'public/generated-images/interface-complete-system.png', type: 'image', name: '系统主界面' },
  { path: 'public/generated-images/interface-ai-architecture.png', type: 'image', name: 'AI技术架构' },
  { path: 'public/generated-images/interface-case-analysis.png', type: 'image', name: '案例分析界面' },
  { path: 'public/generated-images/interface-bigdata-analysis.png', type: 'image', name: '大数据分析' },
  { path: 'public/generated-images/interface-iot-systems.png', type: 'image', name: 'IoT物联网' },
  
  { path: 'public/generated-videos/demo-complete.mp4', type: 'video', name: '系统完整演示' },
  { path: 'public/generated-videos/demo-ai-tech.mp4', type: 'video', name: 'AI技术解析' },
  { path: 'public/generated-videos/demo-cases.mp4', type: 'video', name: '案例分析' },
  { path: 'public/generated-videos/demo-bigdata.mp4', type: 'video', name: '大数据专题' },
  { path: 'public/generated-videos/demo-iot.mp4', type: 'video', name: 'IoT物联网专题' }
];

let results = {
  total: filesToCheck.length,
  passed: 0,
  failed: 0,
  components: { total: 0, passed: 0 },
  images: { total: 0, passed: 0 },
  videos: { total: 0, passed: 0 },
  details: []
};

console.log('\n📋 检查文件存在性...\n');

filesToCheck.forEach(file => {
  const exists = fs.existsSync(file.path);
  let size = 0;
  let sizeText = '';
  
  if (exists) {
    const stats = fs.statSync(file.path);
    size = stats.size;
    
    if (file.type === 'image') {
      sizeText = `${Math.round(size / 1024)}KB`;
    } else if (file.type === 'video') {
      sizeText = `${Math.round(size / (1024 * 1024))}MB`;
    } else {
      sizeText = `${Math.round(size / 1024)}KB`;
    }
  }
  
  const status = exists ? '✅' : '❌';
  console.log(`${status} ${file.name} ${exists ? `(${sizeText})` : '- 文件不存在'}`);
  
  // 统计结果
  if (file.type === 'component' || file.type === 'utility' || file.type === 'view') {
    results.components.total++;
    if (exists) results.components.passed++;
  } else if (file.type === 'image') {
    results.images.total++;
    if (exists) results.images.passed++;
  } else if (file.type === 'video') {
    results.videos.total++;
    if (exists) results.videos.passed++;
  }
  
  if (exists) {
    results.passed++;
  } else {
    results.failed++;
  }
  
  results.details.push({
    path: file.path,
    name: file.name,
    type: file.type,
    exists: exists,
    size: size
  });
});

// 计算得分
const score = Math.round((results.passed / results.total) * 100);

console.log('\n' + '=' .repeat(50));
console.log('📊 验证结果汇总');
console.log('=' .repeat(50));
console.log(`🎯 总体得分: ${score}分`);
console.log(`📈 通过率: ${results.passed}/${results.total} (${Math.round(results.passed/results.total*100)}%)`);
console.log('');
console.log('📋 分类统计:');
console.log(`   Vue组件: ${results.components.passed}/${results.components.total}`);
console.log(`   图片资源: ${results.images.passed}/${results.images.total}`);
console.log(`   视频资源: ${results.videos.passed}/${results.videos.total}`);

// 状态评估
let status = '';
if (score >= 95) {
  status = '🎉 优秀 - 系统完全就绪';
} else if (score >= 85) {
  status = '✅ 良好 - 系统基本就绪';
} else if (score >= 70) {
  status = '⚠️  一般 - 需要部分改进';
} else {
  status = '❌ 较差 - 需要大量改进';
}

console.log(`\n🏆 系统状态: ${status}`);

// 生成建议
console.log('\n💡 建议操作:');

if (results.images.passed < results.images.total) {
  console.log('   📸 生成缺失的图片: node backup-image-generator.js');
}

if (results.videos.passed < results.videos.total) {
  console.log('   🎬 生成缺失的视频: node web-video-generator.js');
}

if (results.components.passed < results.components.total) {
  console.log('   🧩 检查Vue组件文件是否正确创建');
}

if (score >= 85) {
  console.log('   🚀 系统可以用于演示和竞赛提交');
  console.log('   🌐 启动开发服务器: npm run dev');
} else {
  console.log('   🔧 完善缺失的文件后重新验证');
}

// 保存简化报告
const report = {
  timestamp: new Date().toISOString(),
  score: score,
  status: status,
  summary: results,
  details: results.details
};

fs.writeFileSync('validation-report.json', JSON.stringify(report, null, 2));
console.log('\n📄 验证报告已保存到 validation-report.json');

console.log('\n🎯 iFlytek Spark多模态演示系统验证完成！');
