#!/usr/bin/env node

/**
 * 🔍 Prompt Quality Validator
 * 提示词质量验证器
 * 
 * Validates that prompts will generate enterprise-grade Chinese interface screenshots
 * 验证提示词能够生成企业级中文界面截图
 */

const fs = require('fs');
const { interfaceImageConfigs } = require('./enhanced-chinese-video-generator');

console.log('🔍 Prompt Quality Validator');
console.log('提示词质量验证器\n');

// Quality criteria for enterprise-grade prompts
const QUALITY_CRITERIA = {
    font_specification: {
        weight: 25,
        keywords: ['Microsoft YaHei', 'SimHei', 'font rendering', 'font'],
        description: 'Explicit Chinese font specification'
    },
    text_clarity: {
        weight: 25,
        keywords: ['crystal clear', 'sharp', 'crisp', 'clear', 'readable'],
        description: 'Text clarity and sharpness requirements'
    },
    contrast_requirements: {
        weight: 20,
        keywords: ['high-contrast', 'contrast', 'white text', 'readable'],
        description: 'Contrast and readability specifications'
    },
    enterprise_quality: {
        weight: 15,
        keywords: ['professional', 'enterprise', 'corporate', 'business'],
        description: 'Enterprise-grade quality indicators'
    },
    technical_specifications: {
        weight: 10,
        keywords: ['1920x1080', 'resolution', 'quality', 'detailed'],
        description: 'Technical quality specifications'
    },
    chinese_content: {
        weight: 5,
        keywords: ['科大讯飞', 'Spark', '智能', '面试', '评估'],
        description: 'Specific Chinese content inclusion'
    }
};

// Analyze individual prompt quality
function analyzePromptQuality(prompt, promptType = 'unknown') {
    console.log(`📝 Analyzing ${promptType} prompt...`);
    
    const analysis = {
        prompt_type: promptType,
        total_score: 0,
        criteria_scores: {},
        strengths: [],
        weaknesses: [],
        recommendations: []
    };
    
    // Score each criteria
    Object.entries(QUALITY_CRITERIA).forEach(([criterion, config]) => {
        const matchCount = config.keywords.filter(keyword => 
            prompt.toLowerCase().includes(keyword.toLowerCase())
        ).length;
        
        const score = Math.min((matchCount / config.keywords.length) * config.weight, config.weight);
        analysis.criteria_scores[criterion] = {
            score: Math.round(score),
            max_score: config.weight,
            matched_keywords: config.keywords.filter(keyword => 
                prompt.toLowerCase().includes(keyword.toLowerCase())
            ),
            description: config.description
        };
        
        analysis.total_score += score;
        
        if (score >= config.weight * 0.7) {
            analysis.strengths.push(config.description);
        } else if (score < config.weight * 0.3) {
            analysis.weaknesses.push(config.description);
        }
    });
    
    analysis.total_score = Math.round(analysis.total_score);
    
    // Generate recommendations
    if (analysis.criteria_scores.font_specification.score < 15) {
        analysis.recommendations.push('Add explicit "Microsoft YaHei font" specification');
    }
    if (analysis.criteria_scores.text_clarity.score < 15) {
        analysis.recommendations.push('Include "crystal clear" or "sharp" text requirements');
    }
    if (analysis.criteria_scores.contrast_requirements.score < 12) {
        analysis.recommendations.push('Specify "high-contrast white text" for readability');
    }
    
    console.log(`   Score: ${analysis.total_score}/100`);
    console.log(`   Strengths: ${analysis.strengths.length}`);
    console.log(`   Weaknesses: ${analysis.weaknesses.length}`);
    
    return analysis;
}

// Test all interface configurations
function testAllInterfacePrompts() {
    console.log('🧪 Testing All Interface Prompts...\n');
    
    const results = {
        total_interfaces: interfaceImageConfigs.length,
        prompt_analyses: [],
        platform_scores: {
            midjourney: [],
            dalle: [],
            stable_diffusion: []
        },
        overall_recommendations: []
    };
    
    interfaceImageConfigs.forEach((config, index) => {
        console.log(`\n${index + 1}. ${config.title} (${config.category})`);
        console.log('=' .repeat(50));
        
        // Test each platform's prompt
        const midjourneyAnalysis = analyzePromptQuality(config.midjourney_prompt, 'Midjourney');
        const dalleAnalysis = analyzePromptQuality(config.dalle_prompt, 'DALL-E');
        const stableAnalysis = analyzePromptQuality(config.stable_diffusion_prompt, 'Stable Diffusion');
        
        results.prompt_analyses.push({
            interface: config.title,
            category: config.category,
            target_video: config.target_video,
            midjourney: midjourneyAnalysis,
            dalle: dalleAnalysis,
            stable_diffusion: stableAnalysis
        });
        
        results.platform_scores.midjourney.push(midjourneyAnalysis.total_score);
        results.platform_scores.dalle.push(dalleAnalysis.total_score);
        results.platform_scores.stable_diffusion.push(stableAnalysis.total_score);
    });
    
    return results;
}

// Generate platform comparison
function generatePlatformComparison(results) {
    console.log('\n📊 Platform Prompt Quality Comparison...');
    
    const platformAverages = {};
    Object.entries(results.platform_scores).forEach(([platform, scores]) => {
        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        platformAverages[platform] = Math.round(average);
        
        console.log(`${platform.toUpperCase()}: ${Math.round(average)}/100 average`);
        console.log(`   Range: ${Math.min(...scores)} - ${Math.max(...scores)}`);
        console.log(`   Consistency: ${Math.max(...scores) - Math.min(...scores) <= 20 ? 'High' : 'Medium'}`);
    });
    
    const bestPlatform = Object.entries(platformAverages).reduce((a, b) => a[1] > b[1] ? a : b);
    console.log(`\n🏆 Best platform for prompts: ${bestPlatform[0].toUpperCase()} (${bestPlatform[1]}/100)`);
    
    return platformAverages;
}

// Generate detailed recommendations
function generateDetailedRecommendations(results) {
    console.log('\n💡 Detailed Recommendations...');
    
    const allRecommendations = new Set();
    const lowScoringInterfaces = [];
    
    results.prompt_analyses.forEach(analysis => {
        const avgScore = (analysis.midjourney.total_score + analysis.dalle.total_score + analysis.stable_diffusion.total_score) / 3;
        
        if (avgScore < 70) {
            lowScoringInterfaces.push({
                interface: analysis.interface,
                score: Math.round(avgScore)
            });
        }
        
        // Collect all recommendations
        [analysis.midjourney, analysis.dalle, analysis.stable_diffusion].forEach(promptAnalysis => {
            promptAnalysis.recommendations.forEach(rec => allRecommendations.add(rec));
        });
    });
    
    console.log('\n🔧 Priority Improvements:');
    if (lowScoringInterfaces.length > 0) {
        console.log('   Low-scoring interfaces that need attention:');
        lowScoringInterfaces.forEach(item => {
            console.log(`   - ${item.interface}: ${item.score}/100`);
        });
    } else {
        console.log('   ✅ All interfaces have good prompt quality');
    }
    
    console.log('\n📝 Common Recommendations:');
    Array.from(allRecommendations).forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
    });
    
    return {
        low_scoring_interfaces: lowScoringInterfaces,
        common_recommendations: Array.from(allRecommendations)
    };
}

// Test specific Chinese font effectiveness
function testChineseFontEffectiveness() {
    console.log('\n🔤 Testing Chinese Font Effectiveness...');
    
    const fontTests = {
        'No font specification': 'Professional AI interface with Chinese text',
        'Generic font mention': 'Professional AI interface with clear font and Chinese text',
        'Microsoft YaHei specified': 'Professional AI interface, Microsoft YaHei font, Chinese text',
        'Full optimization': 'Professional AI interface, Microsoft YaHei font rendering, crystal clear Chinese text "科大讯飞Spark智能面试评估系统", sharp font edges, high-contrast white text'
    };
    
    const fontScores = {};
    
    Object.entries(fontTests).forEach(([testName, prompt]) => {
        const analysis = analyzePromptQuality(prompt, testName);
        fontScores[testName] = analysis.total_score;
        
        console.log(`\n${testName}:`);
        console.log(`   Score: ${analysis.total_score}/100`);
        console.log(`   Font spec: ${analysis.criteria_scores.font_specification.score}/25`);
        console.log(`   Text clarity: ${analysis.criteria_scores.text_clarity.score}/25`);
    });
    
    const bestApproach = Object.entries(fontScores).reduce((a, b) => a[1] > b[1] ? a : b);
    console.log(`\n🏆 Most effective approach: ${bestApproach[0]} (${bestApproach[1]}/100)`);
    
    return fontScores;
}

// Main validation function
async function runPromptQualityValidation() {
    console.log('🚀 Starting Prompt Quality Validation...\n');
    
    try {
        // 1. Test all interface prompts
        const results = testAllInterfacePrompts();
        
        // 2. Generate platform comparison
        const platformAverages = generatePlatformComparison(results);
        
        // 3. Test Chinese font effectiveness
        const fontEffectiveness = testChineseFontEffectiveness();
        
        // 4. Generate detailed recommendations
        const recommendations = generateDetailedRecommendations(results);
        
        // 5. Create comprehensive report
        const report = {
            timestamp: new Date().toISOString(),
            validation_summary: {
                total_interfaces: results.total_interfaces,
                platform_averages: platformAverages,
                best_platform: Object.entries(platformAverages).reduce((a, b) => a[1] > b[1] ? a : b)[0],
                overall_quality: Object.values(platformAverages).reduce((sum, score) => sum + score, 0) / 3
            },
            detailed_results: results,
            font_effectiveness: fontEffectiveness,
            recommendations: recommendations,
            quality_criteria: QUALITY_CRITERIA
        };
        
        // Save report
        fs.writeFileSync('prompt-quality-report.json', JSON.stringify(report, null, 2));
        console.log('\n📄 Validation report saved to prompt-quality-report.json');
        
        // Final assessment
        console.log('\n📊 Final Assessment:');
        const overallQuality = report.validation_summary.overall_quality;
        
        if (overallQuality >= 80) {
            console.log('🎉 Excellent! Prompts are enterprise-ready');
            console.log('✅ Proceed with confidence to image generation');
        } else if (overallQuality >= 65) {
            console.log('✅ Good quality prompts with room for improvement');
            console.log('🔧 Consider implementing recommended enhancements');
        } else {
            console.log('⚠️  Prompts need significant improvement');
            console.log('🛠️  Please address recommendations before proceeding');
        }
        
        console.log(`\n🎯 Overall Quality Score: ${Math.round(overallQuality)}/100`);
        console.log(`🏆 Best Platform: ${report.validation_summary.best_platform.toUpperCase()}`);
        
        return report;
        
    } catch (error) {
        console.error('❌ Validation failed:', error.message);
        return null;
    }
}

// Quick prompt check
function quickPromptCheck() {
    console.log('⚡ Quick Prompt Quality Check\n');
    
    if (!interfaceImageConfigs || interfaceImageConfigs.length === 0) {
        console.log('❌ No interface configurations found');
        console.log('💡 Check enhanced-chinese-video-generator.js file');
        return;
    }
    
    console.log(`📝 Found ${interfaceImageConfigs.length} interface configurations`);
    
    // Quick sample test
    const sampleConfig = interfaceImageConfigs[0];
    const quickAnalysis = analyzePromptQuality(sampleConfig.midjourney_prompt, 'Sample');
    
    console.log(`\n🧪 Sample quality score: ${quickAnalysis.total_score}/100`);
    
    if (quickAnalysis.total_score >= 70) {
        console.log('✅ Prompts look good! Run full validation for details.');
    } else {
        console.log('⚠️  Prompts may need improvement. Run full validation.');
    }
    
    console.log('\n🚀 Run full validation: node test-prompt-quality.js');
}

// Main program
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--quick')) {
        quickPromptCheck();
    } else {
        runPromptQualityValidation();
    }
}

module.exports = {
    analyzePromptQuality,
    testAllInterfacePrompts,
    testChineseFontEffectiveness,
    runPromptQualityValidation,
    QUALITY_CRITERIA
};
