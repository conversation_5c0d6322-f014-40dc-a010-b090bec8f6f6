<template>
  <div class="learning-module-details">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item @click="router.push('/learning-path')">学习路径</el-breadcrumb-item>
            <el-breadcrumb-item @click="router.push(`/learning-path/${pathId}/details`)">{{ pathTitle }}</el-breadcrumb-item>
            <el-breadcrumb-item>{{ moduleDetails.name }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="module-header">
          <div class="module-info">
            <h1>{{ moduleDetails.name }}</h1>
            <div class="module-meta">
              <el-tag :type="getModuleTypeColor(moduleDetails.type)" size="large">
                {{ getModuleTypeName(moduleDetails.type) }}
              </el-tag>
              <span class="duration">学习时长：{{ moduleDetails.duration }}小时</span>
              <el-tag :type="getStatusType(moduleDetails.status)" size="large">
                {{ getStatusText(moduleDetails.status) }}
              </el-tag>
            </div>
            <p class="module-description">{{ moduleDetails.description }}</p>
          </div>
          
          <div class="module-actions">
            <el-button size="large" @click="router.back()">返回</el-button>
            <el-button v-if="moduleDetails.status === 'not_started'" type="primary" size="large" @click="startModule">
              <el-icon><CaretRight /></el-icon>
              开始学习
            </el-button>
            <el-button v-else-if="moduleDetails.status === 'in_progress'" type="warning" size="large" @click="continueModule">
              <el-icon><VideoPlay /></el-icon>
              继续学习
            </el-button>
            <el-button v-else type="success" size="large" @click="reviewModule">
              <el-icon><Refresh /></el-icon>
              复习回顾
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习进度概览 -->
    <div class="progress-overview">
      <el-card class="progress-card">
        <template #header>
          <div class="card-header">
            <el-icon><TrendCharts /></el-icon>
            <span>学习进度</span>
          </div>
        </template>
        
        <div class="progress-content">
          <div class="progress-circle">
            <el-progress 
              type="circle" 
              :percentage="moduleProgress" 
              :width="100"
              :stroke-width="8"
              color="#1890ff"
            >
              <template #default="{ percentage }">
                <span class="progress-text">{{ percentage }}%</span>
              </template>
            </el-progress>
          </div>
          
          <div class="progress-stats">
            <div class="stat-item">
              <span class="stat-number">{{ completedSections }}</span>
              <span class="stat-label">已完成章节</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ totalSections }}</span>
              <span class="stat-label">总章节数</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ studyTime }}</span>
              <span class="stat-label">已学习时长(小时)</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 学习内容 -->
    <div class="learning-content">
      <el-card class="content-card">
        <template #header>
          <div class="card-header">
            <el-icon><Reading /></el-icon>
            <span>学习内容</span>
          </div>
        </template>
        
        <div class="content-sections">
          <div v-for="(section, index) in moduleDetails.sections" :key="section.id" class="section-item">
            <div class="section-header">
              <div class="section-number">{{ index + 1 }}</div>
              <div class="section-info">
                <h4>{{ section.title }}</h4>
                <p>{{ section.description }}</p>
                <div class="section-meta">
                  <span class="section-duration">{{ section.duration }}分钟</span>
                  <el-tag :type="getSectionStatusType(section.status)" size="small">
                    {{ getSectionStatusText(section.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="section-actions">
                <el-button v-if="section.status === 'not_started'" type="primary" size="small" @click="startSection(section.id)">
                  开始
                </el-button>
                <el-button v-else-if="section.status === 'in_progress'" type="warning" size="small" @click="continueSection(section.id)">
                  继续
                </el-button>
                <el-button v-else type="success" size="small" @click="reviewSection(section.id)">
                  复习
                </el-button>
              </div>
            </div>
            
            <!-- 章节内容预览 -->
            <div v-if="section.status !== 'not_started'" class="section-content">
              <div class="content-preview">
                <h5>学习要点</h5>
                <ul class="key-points">
                  <li v-for="point in section.keyPoints" :key="point">{{ point }}</li>
                </ul>
              </div>
              
              <div v-if="section.resources" class="section-resources">
                <h5>相关资源</h5>
                <div class="resource-list">
                  <div v-for="resource in section.resources" :key="resource.id" class="resource-item">
                    <el-icon v-if="resource.type === 'video'"><VideoPlay /></el-icon>
                    <el-icon v-else-if="resource.type === 'document'"><Document /></el-icon>
                    <el-icon v-else><Link /></el-icon>
                    <span>{{ resource.name }}</span>
                    <el-button size="small" text @click="openResource(resource)">查看</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 学习笔记 -->
    <div class="learning-notes">
      <el-card class="notes-card">
        <template #header>
          <div class="card-header">
            <el-icon><EditPen /></el-icon>
            <span>学习笔记</span>
          </div>
        </template>
        
        <div class="notes-content">
          <el-input
            v-model="learningNotes"
            type="textarea"
            :rows="6"
            placeholder="记录您的学习心得和重要知识点..."
            @blur="saveNotes"
          />
          <div class="notes-actions">
            <el-button @click="saveNotes" type="primary">
              <el-icon><Check /></el-icon>
              保存笔记
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  TrendCharts, Reading, CaretRight, VideoPlay, Refresh, 
  Document, Link, EditPen, Check
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 路由参数
const pathId = ref(route.params.pathId)
const moduleId = ref(route.params.moduleId)
const mode = ref(route.params.mode || 'study') // study, review

// 页面数据
const pathTitle = ref('AI算法基础强化路径')
const learningNotes = ref('')

// 模块详情数据
const moduleDetails = ref({
  id: 'm1',
  name: 'Python数据科学基础',
  type: 'theory',
  duration: 40,
  status: 'in_progress',
  description: '掌握Python在数据科学领域的基础应用，包括NumPy、Pandas等核心库的使用',
  sections: [
    {
      id: 's1',
      title: 'Python基础语法回顾',
      description: '复习Python核心语法和数据结构',
      duration: 60,
      status: 'completed',
      keyPoints: [
        '变量和数据类型',
        '控制流语句',
        '函数定义和调用',
        '面向对象编程基础'
      ],
      resources: [
        { id: 'r1', name: 'Python官方文档', type: 'document' },
        { id: 'r2', name: '基础语法视频教程', type: 'video' }
      ]
    },
    {
      id: 's2',
      title: 'NumPy数组操作',
      description: '学习NumPy库的核心功能和数组操作',
      duration: 90,
      status: 'in_progress',
      keyPoints: [
        '数组创建和索引',
        '数组运算和广播',
        '线性代数操作',
        '数组形状变换'
      ],
      resources: [
        { id: 'r3', name: 'NumPy实战练习', type: 'practice' },
        { id: 'r4', name: 'NumPy官方教程', type: 'document' }
      ]
    },
    {
      id: 's3',
      title: 'Pandas数据处理',
      description: '掌握Pandas进行数据清洗和分析',
      duration: 120,
      status: 'not_started',
      keyPoints: [
        'DataFrame和Series',
        '数据读取和写入',
        '数据清洗和转换',
        '数据聚合和分组'
      ],
      resources: [
        { id: 'r5', name: 'Pandas实战项目', type: 'project' },
        { id: 'r6', name: '数据分析案例', type: 'document' }
      ]
    }
  ]
})

// 计算属性
const moduleProgress = computed(() => {
  const completed = moduleDetails.value.sections.filter(s => s.status === 'completed').length
  const inProgress = moduleDetails.value.sections.filter(s => s.status === 'in_progress').length * 0.5
  return Math.round((completed + inProgress) / moduleDetails.value.sections.length * 100)
})

const completedSections = computed(() => {
  return moduleDetails.value.sections.filter(s => s.status === 'completed').length
})

const totalSections = computed(() => {
  return moduleDetails.value.sections.length
})

const studyTime = computed(() => {
  const completed = moduleDetails.value.sections.filter(s => s.status === 'completed')
  const inProgress = moduleDetails.value.sections.filter(s => s.status === 'in_progress')
  return completed.reduce((total, section) => total + section.duration, 0) / 60 + 
         inProgress.reduce((total, section) => total + section.duration * 0.5, 0) / 60
})

// 方法
const getModuleTypeColor = (type) => {
  const colorMap = {
    theory: 'info',
    practice: 'warning',
    project: 'success'
  }
  return colorMap[type] || 'info'
}

const getModuleTypeName = (type) => {
  const nameMap = {
    theory: '理论学习',
    practice: '实践练习',
    project: '项目实战'
  }
  return nameMap[type] || '未知'
}

const getStatusType = (status) => {
  const typeMap = {
    completed: 'success',
    in_progress: 'warning',
    not_started: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    completed: '已完成',
    in_progress: '进行中',
    not_started: '未开始'
  }
  return textMap[status] || '未知'
}

const getSectionStatusType = (status) => {
  return getStatusType(status)
}

const getSectionStatusText = (status) => {
  return getStatusText(status)
}

const startModule = () => {
  moduleDetails.value.status = 'in_progress'
  ElMessage.success('开始学习模块')
  updateModuleStatus()
}

const continueModule = () => {
  ElMessage.info('继续学习模块')
}

const reviewModule = () => {
  ElMessage.info('复习模块内容')
}

const startSection = (sectionId) => {
  const section = moduleDetails.value.sections.find(s => s.id === sectionId)
  if (section) {
    section.status = 'in_progress'
    ElMessage.success(`开始学习：${section.title}`)
    updateModuleStatus()
  }
}

const continueSection = (sectionId) => {
  const section = moduleDetails.value.sections.find(s => s.id === sectionId)
  if (section) {
    ElMessage.info(`继续学习：${section.title}`)
  }
}

const reviewSection = (sectionId) => {
  const section = moduleDetails.value.sections.find(s => s.id === sectionId)
  if (section) {
    ElMessage.info(`复习章节：${section.title}`)
  }
}

const openResource = (resource) => {
  ElMessage.info(`打开资源：${resource.name}`)
  // 这里可以实现具体的资源打开逻辑
}

const saveNotes = () => {
  ElMessage.success('学习笔记已保存')
  // 这里可以实现笔记保存到后端的逻辑
}

const updateModuleStatus = () => {
  // 更新模块状态逻辑
  const sections = moduleDetails.value.sections
  const completedCount = sections.filter(s => s.status === 'completed').length
  const inProgressCount = sections.filter(s => s.status === 'in_progress').length
  
  if (completedCount === sections.length) {
    moduleDetails.value.status = 'completed'
  } else if (inProgressCount > 0 || completedCount > 0) {
    moduleDetails.value.status = 'in_progress'
  } else {
    moduleDetails.value.status = 'not_started'
  }
}

// 生命周期
onMounted(() => {
  console.log('学习模块详情页面已加载:', { pathId: pathId.value, moduleId: moduleId.value, mode: mode.value })
  
  // 根据路由参数加载对应的模块数据
  // 这里可以调用API获取具体的模块数据
})
</script>

<style scoped>
/* 页面整体样式 */
.learning-module-details {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(24, 144, 255, 0.08) 0%,
    rgba(102, 126, 234, 0.06) 25%,
    rgba(0, 102, 204, 0.04) 50%,
    rgba(76, 81, 191, 0.06) 75%,
    rgba(118, 75, 162, 0.08) 100%
  );
  background-attachment: fixed;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.breadcrumb {
  margin-bottom: 24px;
}

.breadcrumb :deep(.el-breadcrumb__item) {
  font-size: 14px;
}

.breadcrumb :deep(.el-breadcrumb__inner) {
  color: #1890ff;
  cursor: pointer;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.module-info h1 {
  font-size: 28px;
  font-weight: 700;
  color: #262626;
  margin: 0 0 12px 0;
}

.module-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.duration {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

.module-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.module-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

/* 卡片通用样式 */
.progress-overview,
.learning-content,
.learning-notes {
  max-width: 1200px;
  margin: 0 auto 32px auto;
}

.progress-card,
.content-card,
.notes-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

/* 进度概览 */
.progress-content {
  display: flex;
  align-items: center;
  gap: 48px;
  padding: 8px 0;
}

.progress-circle {
  flex-shrink: 0;
}

.progress-text {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
}

.progress-stats {
  display: flex;
  gap: 48px;
  flex: 1;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #8c8c8c;
  margin-top: 4px;
}

/* 学习内容 */
.content-sections {
  padding: 8px 0;
}

.section-item {
  margin-bottom: 24px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.section-item:hover {
  border-color: #d9f7be;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #fafafa;
}

.section-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.section-info {
  flex: 1;
}

.section-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.section-info p {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.section-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-duration {
  font-size: 12px;
  color: #8c8c8c;
}

.section-actions {
  flex-shrink: 0;
}

.section-content {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  background: white;
}

.content-preview {
  margin-bottom: 20px;
}

.content-preview h5 {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.key-points {
  margin: 0;
  padding-left: 20px;
}

.key-points li {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 4px;
}

.section-resources h5 {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.resource-item span {
  flex: 1;
  color: #666;
}

/* 学习笔记 */
.notes-content {
  padding: 8px 0;
}

.notes-actions {
  margin-top: 16px;
  text-align: right;
}
</style>
