<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek Spark 系统快速测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            font-size: 2.5rem;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-card h3 {
            margin-top: 0;
            color: #87CEEB;
        }
        .test-link {
            display: inline-block;
            background: #1890ff;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px 5px 5px 0;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #0066cc;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border-left: 4px solid #4CAF50;
        }
        .info {
            background: rgba(33, 150, 243, 0.3);
            border-left: 4px solid #2196F3;
        }
        .warning {
            background: rgba(255, 193, 7, 0.3);
            border-left: 4px solid #FFC107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 iFlytek Spark 智能面试系统</h1>
        
        <div class="status success">
            <strong>✅ 系统状态：</strong> 开发服务器运行正常 (http://localhost:5173/)
        </div>

        <div class="status info">
            <strong>📋 当前配置：</strong> 使用简化路由配置 (clean-routes.js)
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>🏠 核心页面</h3>
                <a href="http://localhost:5173/" class="test-link" target="_blank">首页</a>
                <a href="http://localhost:5173/demo" class="test-link" target="_blank">产品演示</a>
                <a href="http://localhost:5173/interview-selection" class="test-link" target="_blank">面试选择</a>
                <a href="http://localhost:5173/reports" class="test-link" target="_blank">面试报告</a>
            </div>

            <div class="test-card">
                <h3>👥 用户入口</h3>
                <a href="http://localhost:5173/candidate" class="test-link" target="_blank">候选人入口</a>
                <a href="http://localhost:5173/enterprise" class="test-link" target="_blank">企业管理</a>
                <a href="http://localhost:5173/position-management" class="test-link" target="_blank">职位管理</a>
                <a href="http://localhost:5173/enterprise-reports" class="test-link" target="_blank">企业报告</a>
            </div>

            <div class="test-card">
                <h3>🎯 面试功能</h3>
                <a href="http://localhost:5173/interviewing" class="test-link" target="_blank">面试进行</a>
                <a href="http://localhost:5173/interview-result" class="test-link" target="_blank">面试结果</a>
                <a href="http://localhost:5173/learning-path" class="test-link" target="_blank">学习路径</a>
                <a href="http://localhost:5173/question-preview" class="test-link" target="_blank">题目预览</a>
            </div>

            <div class="test-card">
                <h3>🔧 系统工具</h3>
                <a href="http://localhost:5173/debug-404.html" class="test-link" target="_blank">404诊断工具</a>
                <a href="http://localhost:5173/quick-test.html" class="test-link" target="_blank">快速测试页</a>
                <p style="margin-top: 15px; font-size: 0.9em; opacity: 0.8;">
                    如果某个页面显示404，可能是对应的Vue组件文件缺失。
                </p>
            </div>
        </div>

        <div class="status warning">
            <strong>⚠️ 注意事项：</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>当前使用简化路由配置，部分高级功能可能不可用</li>
                <li>如果遇到404错误，请检查对应的Vue组件是否存在</li>
                <li>开发环境下，修改代码后页面会自动刷新</li>
                <li>确保后端服务器也在运行以获得完整功能</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <h3>🎨 技术栈</h3>
            <p>Vue.js 3 + Element Plus + iFlytek Spark + Vite</p>
            <p style="opacity: 0.8; font-size: 0.9em;">
                基于讯飞星火大模型的多模态AI面试解决方案
            </p>
        </div>
    </div>

    <script>
        // 简单的状态检查
        console.log('🚀 iFlytek Spark 系统快速测试页面已加载');
        console.log('📊 当前时间:', new Date().toLocaleString('zh-CN'));
        console.log('🌐 用户代理:', navigator.userAgent);
        
        // 检查Vue应用是否运行
        setTimeout(() => {
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Vue应用响应正常');
                    } else {
                        console.log('⚠️ Vue应用响应异常:', response.status);
                    }
                })
                .catch(error => {
                    console.log('❌ Vue应用连接失败:', error.message);
                });
        }, 1000);
    </script>
</body>
</html>
