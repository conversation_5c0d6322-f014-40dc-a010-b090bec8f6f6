<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI面试官回复优化演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #1890ff;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .before-after {
            border: 1px solid #e8e8e8;
            border-radius: 10px;
            padding: 20px;
        }
        
        .before-after h3 {
            margin-top: 0;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        
        .before h3 {
            background: #ff4d4f;
        }
        
        .after h3 {
            background: #52c41a;
        }
        
        .mock-conversation {
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
            min-height: 300px;
        }
        
        .mock-message {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
        }
        
        .mock-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e6f7ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1890ff;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .mock-content {
            flex: 1;
            background: #e6f7ff;
            border-left: 3px solid #1890ff;
            border-radius: 8px;
            padding: 12px;
        }
        
        .mock-thinking {
            background: #fff7e6;
            border: 1px solid #ffd591;
            color: #fa8c16;
            font-style: italic;
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 6px;
        }
        
        .mock-thinking-process {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 6px;
        }
        
        .collapsible-thinking {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
        }
        
        .thinking-header {
            background: #f0f9ff;
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1890ff;
            font-size: 14px;
            border-bottom: 1px solid #91d5ff;
        }
        
        .thinking-header:hover {
            background: #e6f7ff;
        }
        
        .thinking-content {
            padding: 12px;
            font-family: monospace;
            font-size: 12px;
            color: #1890ff;
            line-height: 1.6;
            display: none;
        }
        
        .thinking-content.expanded {
            display: block;
        }
        
        .typing-effect {
            position: relative;
        }
        
        .typing-cursor {
            animation: blink 1s infinite;
            color: #1890ff;
            font-weight: bold;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .feature-list {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .feature-list h3 {
            color: #52c41a;
            margin-top: 0;
        }
        
        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-item:before {
            content: "✅";
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .code-block {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #333;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .animation-sequence {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .animation-sequence h3 {
            color: #1890ff;
            margin-top: 0;
        }
        
        .sequence-step {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        
        .step-number {
            background: #1890ff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .step-description {
            flex: 1;
        }
        
        .benefits {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .benefits h3 {
            color: #fa8c16;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <h1>🚀 AI面试官回复显示优化</h1>
            <p>iFlytek智能面试系统 - 合并显示与可折叠思考过程</p>
        </div>

        <div class="comparison-section">
            <!-- 优化前 -->
            <div class="before-after before">
                <h3>❌ 优化前：分离显示</h3>
                <div class="mock-conversation">
                    <div class="mock-message">
                        <div class="mock-avatar">🤖</div>
                        <div class="mock-content">
                            <div class="mock-thinking">🤔 AI面试官正在思考...</div>
                        </div>
                    </div>
                    <div class="mock-message">
                        <div class="mock-avatar">🤖</div>
                        <div class="mock-content">
                            <div class="mock-thinking-process">
                                🧠 正在分析您的回答...<br>
                                📝 回答长度: 45字符<br>
                                🔍 检测到关键词: 深度学习<br>
                                📊 技术深度评估: 中等
                            </div>
                        </div>
                    </div>
                    <div class="mock-message">
                        <div class="mock-avatar">🤖</div>
                        <div class="mock-content">
                            您的回答有一定的技术深度，展现了基础的理解能力。建议您可以详细说明具体的实现方法。
                        </div>
                    </div>
                </div>
                <p style="color: #ff4d4f; font-size: 14px; margin-top: 10px;">
                    ⚠️ 问题：三个分离的消息气泡，界面冗余
                </p>
            </div>

            <!-- 优化后 -->
            <div class="before-after after">
                <h3>✅ 优化后：合并显示</h3>
                <div class="mock-conversation">
                    <div class="mock-message">
                        <div class="mock-avatar">🤖</div>
                        <div class="mock-content">
                            <div class="collapsible-thinking">
                                <div class="thinking-header" onclick="toggleThinking(this)">
                                    <span>👁️</span>
                                    <span>💭 查看AI思考过程</span>
                                    <span style="margin-left: auto; font-size: 12px;">展开</span>
                                </div>
                                <div class="thinking-content">
                                    🧠 正在分析您的回答...<br>
                                    📝 回答长度: 45字符<br>
                                    🔍 检测到关键词: 深度学习<br>
                                    📊 技术深度评估: 中等<br>
                                    ⚡ 正在生成个性化反馈...
                                </div>
                            </div>
                            <div class="typing-effect">
                                您的回答有一定的技术深度，展现了基础的理解能力。建议您可以详细说明具体的实现方法。<span class="typing-cursor">|</span>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #52c41a; font-size: 14px; margin-top: 10px;">
                    ✅ 优势：单一消息气泡，思考过程可折叠
                </p>
            </div>
        </div>

        <div class="animation-sequence">
            <h3>🎭 渐进式动画序列</h3>
            <div class="sequence-step">
                <div class="step-number">1</div>
                <div class="step-description">
                    <strong>思考状态指示</strong><br>
                    显示"🤔 AI面试官正在思考..."状态指示器
                </div>
            </div>
            <div class="sequence-step">
                <div class="step-number">2</div>
                <div class="step-description">
                    <strong>思考过程展示</strong><br>
                    逐字打字机效果显示AI思考过程内容
                </div>
            </div>
            <div class="sequence-step">
                <div class="step-number">3</div>
                <div class="step-description">
                    <strong>自动折叠</strong><br>
                    思考过程完成后自动折叠为可展开摘要
                </div>
            </div>
            <div class="sequence-step">
                <div class="step-number">4</div>
                <div class="step-description">
                    <strong>最终回复</strong><br>
                    在同一气泡中逐字显示AI面试官的最终回复
                </div>
            </div>
        </div>

        <div class="feature-list">
            <h3>🔧 核心优化功能</h3>
            <div class="feature-item">
                <strong>合并显示逻辑：</strong>将思考过程和最终回复合并到同一个消息对象中
            </div>
            <div class="feature-item">
                <strong>可折叠思考过程：</strong>使用Element Plus Collapse组件实现展开/折叠功能
            </div>
            <div class="feature-item">
                <strong>打字机效果：</strong>逐字渐进显示内容，提升视觉体验
            </div>
            <div class="feature-item">
                <strong>状态管理：</strong>响应式管理思考过程和回复内容的显示状态
            </div>
            <div class="feature-item">
                <strong>流畅动画：</strong>自然的过渡效果和用户交互体验
            </div>
        </div>

        <div class="code-block">
// 核心实现代码片段
const generateAIInterviewerResponse = async (analysis, userAnswer) => {
  // 创建合并的AI消息对象
  const aiMessage = {
    type: 'ai',
    sender: 'AI面试官',
    content: '🤔 AI面试官正在思考...',
    isThinking: true,
    hasThinkingProcess: true,
    thinkingProcess: '',
    thinkingExpanded: [],
    displayedContent: '',
    isTyping: false
  }
  
  // 渐进式动画序列
  await typewriterEffect(messageIndex, thinkingContent, 'thinking')
  await typewriterEffect(messageIndex, aiResponse, 'response')
}
        </div>

        <div class="benefits">
            <h3>🎯 用户体验提升</h3>
            <div class="feature-item">
                <strong>界面简洁：</strong>减少消息气泡数量，避免界面冗余
            </div>
            <div class="feature-item">
                <strong>信息层次：</strong>思考过程可选查看，主要回复突出显示
            </div>
            <div class="feature-item">
                <strong>交互自然：</strong>打字机效果模拟真实对话体验
            </div>
            <div class="feature-item">
                <strong>功能完整：</strong>保留所有AI分析功能，提升展示方式
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f0f9ff; border-radius: 8px;">
            <h3 style="color: #1890ff; margin: 0;">🎉 优化完成！</h3>
            <p style="margin: 10px 0 0 0; color: #666;">
                AI面试官回复显示已优化，提供更加流畅和专业的交互体验
            </p>
        </div>
    </div>

    <script>
        function toggleThinking(element) {
            const content = element.nextElementSibling;
            const isExpanded = content.classList.contains('expanded');
            
            if (isExpanded) {
                content.classList.remove('expanded');
                element.querySelector('span:last-child').textContent = '展开';
            } else {
                content.classList.add('expanded');
                element.querySelector('span:last-child').textContent = '收起';
            }
        }
    </script>
</body>
</html>
