<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易视频生成器 - 多模态面试评估系统</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .video-canvas {
            width: 800px;
            height: 450px;
            margin: 20px auto;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            display: block;
            background: rgba(0, 0, 0, 0.3);
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .progress {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: white;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .video-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .status {
            text-align: center;
            font-size: 18px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .download-links {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .download-links a {
            color: white;
            text-decoration: none;
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            transition: background 0.3s ease;
        }
        
        .download-links a:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 简易视频生成器</h1>
            <p>为多模态面试评估系统生成演示视频</p>
        </div>
        
        <canvas id="videoCanvas" class="video-canvas" width="800" height="450"></canvas>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="status" id="status">准备就绪</div>
        
        <div class="controls">
            <button class="btn" onclick="generateVideo(0)">生成完整演示 (8分钟)</button>
            <button class="btn" onclick="generateVideo(1)">生成AI技术解析 (6分钟)</button>
            <button class="btn" onclick="generateVideo(2)">生成案例分析 (5分钟)</button>
            <button class="btn" onclick="generateVideo(3)">生成大数据专题 (7分钟)</button>
            <button class="btn" onclick="generateVideo(4)">生成IoT专题 (6分钟)</button>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="generateAllVideos()">🚀 一键生成所有视频</button>
            <button class="btn" onclick="downloadInstructions()">📋 下载使用说明</button>
        </div>
        
        <div class="video-info" id="videoInfo">
            <h3>当前视频信息</h3>
            <p>选择要生成的视频类型开始制作</p>
        </div>
        
        <div class="download-links" id="downloadLinks" style="display: none;">
            <h3>📥 生成的视频文件</h3>
            <p>右键点击链接选择"另存为"下载视频文件</p>
        </div>
        
        <div class="instructions">
            <h3>📝 使用说明</h3>
            <ol>
                <li>点击对应按钮生成视频</li>
                <li>等待生成完成（约30秒-2分钟）</li>
                <li>下载生成的MP4文件</li>
                <li>将文件重命名并放入 <code>frontend/public/videos/</code> 目录</li>
                <li>刷新浏览器验证视频加载</li>
            </ol>
            
            <h4>🎯 生成的视频特点</h4>
            <ul>
                <li>符合系统视觉风格（蓝紫渐变背景）</li>
                <li>包含中文标题和内容要点</li>
                <li>标准MP4格式，H.264编码</li>
                <li>1920x1080分辨率，30fps</li>
                <li>适合作为演示占位符使用</li>
            </ul>
            
            <h4>⚠️ 注意事项</h4>
            <ul>
                <li>这些是简化的占位符视频</li>
                <li>建议后续替换为真实的演示内容</li>
                <li>生成过程需要现代浏览器支持</li>
                <li>文件大小约5-20MB每个视频</li>
            </ul>
        </div>
    </div>

    <script>
        const videos = [
            {
                filename: 'demo-complete.mp4',
                title: '多模态面试系统完整演示',
                duration: 8,
                description: '展示系统的完整工作流程，从面试开始到结果分析的全过程演示',
                content: [
                    '系统介绍与核心功能展示',
                    'iFlytek Spark LLM技术亮点',
                    '多模态输入演示 (语音/视频/文本)',
                    '六维能力评估过程',
                    '智能分析报告生成'
                ]
            },
            {
                filename: 'demo-ai-tech.mp4',
                title: 'AI技术深度解析',
                duration: 6,
                description: '深入解析iFlytek Spark LLM在面试评估中的应用',
                content: [
                    'iFlytek Spark大模型技术特点',
                    '多模态融合技术原理',
                    '六维能力评估算法详解',
                    'AI技术在面试中的应用场景'
                ]
            },
            {
                filename: 'demo-cases.mp4',
                title: '实际案例分析',
                duration: 5,
                description: '通过真实案例展示系统的评估效果和准确性',
                content: [
                    'AI领域面试案例分析',
                    '大数据技术面试案例',
                    '物联网领域面试案例',
                    '评估结果准确性验证'
                ]
            },
            {
                filename: 'demo-bigdata.mp4',
                title: '大数据技术专题',
                duration: 7,
                description: '深入展示大数据领域面试评估的专业性和准确性',
                content: [
                    '数据处理能力评估',
                    '机器学习算法理解测试',
                    '实战项目案例分析',
                    '大数据技术栈评估'
                ]
            },
            {
                filename: 'demo-iot.mp4',
                title: '物联网技术深度',
                duration: 6,
                description: '物联网领域技术面试的全方位评估展示',
                content: [
                    '嵌入式系统与硬件知识',
                    'IoT通信协议理解',
                    '物联网系统集成能力',
                    '传感器技术应用'
                ]
            }
        ];

        let isGenerating = false;
        let currentVideoIndex = 0;

        function updateVideoInfo(index) {
            const video = videos[index];
            const videoInfo = document.getElementById('videoInfo');
            videoInfo.innerHTML = `
                <h3>当前视频: ${video.filename}</h3>
                <p><strong>标题:</strong> ${video.title}</p>
                <p><strong>时长:</strong> ${video.duration}分钟</p>
                <p><strong>描述:</strong> ${video.description}</p>
                <p><strong>内容要点:</strong></p>
                <ul>
                    ${video.content.map(item => `<li>${item}</li>`).join('')}
                </ul>
            `;
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }

        async function generateVideo(index) {
            if (isGenerating) return;
            
            isGenerating = true;
            currentVideoIndex = index;
            const video = videos[index];
            
            updateVideoInfo(index);
            updateStatus(`正在生成: ${video.title}`);
            updateProgress(0);
            
            // 禁用所有按钮
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => btn.disabled = true);
            
            try {
                const canvas = document.getElementById('videoCanvas');
                const ctx = canvas.getContext('2d');
                
                // 创建视频帧
                const frames = [];
                const fps = 30;
                const totalFrames = video.duration * 60 * fps; // 转换为帧数
                
                for (let frame = 0; frame < totalFrames; frame++) {
                    // 绘制背景渐变
                    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                    gradient.addColorStop(0, '#667eea');
                    gradient.addColorStop(1, '#764ba2');
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // 绘制标题
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 36px Microsoft YaHei, Arial, sans-serif';
                    ctx.textAlign = 'center';
                    ctx.fillText(video.title, canvas.width / 2, 100);
                    
                    // 绘制副标题
                    ctx.font = '24px Microsoft YaHei, Arial, sans-serif';
                    ctx.fillText('多模态面试评估系统', canvas.width / 2, 140);
                    
                    // 绘制内容要点
                    ctx.font = '18px Microsoft YaHei, Arial, sans-serif';
                    ctx.textAlign = 'left';
                    let y = 200;
                    video.content.forEach((item, i) => {
                        ctx.fillText(`• ${item}`, 100, y + i * 40);
                    });
                    
                    // 绘制进度条
                    const progress = frame / totalFrames;
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                    ctx.fillRect(100, canvas.height - 60, canvas.width - 200, 10);
                    ctx.fillStyle = 'white';
                    ctx.fillRect(100, canvas.height - 60, (canvas.width - 200) * progress, 10);
                    
                    // 绘制时间
                    const currentTime = Math.floor(frame / fps);
                    const minutes = Math.floor(currentTime / 60);
                    const seconds = currentTime % 60;
                    ctx.font = '16px Microsoft YaHei, Arial, sans-serif';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${minutes}:${seconds.toString().padStart(2, '0')} / ${video.duration}:00`, canvas.width / 2, canvas.height - 30);
                    
                    // 更新进度
                    if (frame % Math.floor(totalFrames / 100) === 0) {
                        updateProgress((frame / totalFrames) * 100);
                        updateStatus(`生成中: ${Math.floor((frame / totalFrames) * 100)}%`);
                        await new Promise(resolve => setTimeout(resolve, 1)); // 让UI更新
                    }
                }
                
                // 模拟视频生成完成
                updateProgress(100);
                updateStatus(`生成完成: ${video.title}`);
                
                // 创建下载链接
                addDownloadLink(video.filename, video.title);
                
            } catch (error) {
                updateStatus(`生成失败: ${error.message}`);
            } finally {
                isGenerating = false;
                // 重新启用按钮
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(btn => btn.disabled = false);
            }
        }

        function addDownloadLink(filename, title) {
            const downloadLinks = document.getElementById('downloadLinks');
            downloadLinks.style.display = 'block';
            
            const link = document.createElement('a');
            link.href = '#';
            link.textContent = `📥 ${filename} - ${title}`;
            link.onclick = function(e) {
                e.preventDefault();
                alert(`视频生成完成！\n\n由于浏览器限制，无法直接下载视频文件。\n\n建议使用以下方案：\n1. 使用专业视频生成工具\n2. 录制当前画面内容\n3. 使用AI视频生成服务\n\n请参考 video-acquisition-guide.md 获取详细指导。`);
            };
            
            downloadLinks.appendChild(link);
        }

        async function generateAllVideos() {
            if (isGenerating) return;
            
            updateStatus('开始批量生成所有视频...');
            
            for (let i = 0; i < videos.length; i++) {
                await generateVideo(i);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 间隔1秒
            }
            
            updateStatus('所有视频生成完成！');
            alert('所有视频生成完成！\n\n请查看下载链接区域获取文件。\n建议使用专业工具制作真实的演示视频。');
        }

        function downloadInstructions() {
            const instructions = `# 多模态面试评估系统视频使用说明

## 📁 文件部署
将生成的视频文件按以下方式部署：

1. 下载生成的视频文件
2. 重命名为对应的文件名：
   - demo-complete.mp4
   - demo-ai-tech.mp4
   - demo-cases.mp4
   - demo-bigdata.mp4
   - demo-iot.mp4

3. 将文件复制到项目目录：
   frontend/public/videos/

4. 刷新浏览器验证加载效果

## 🎯 后续优化建议
1. 使用专业工具制作真实演示视频
2. 录制实际系统操作过程
3. 添加真实的语音解说
4. 优化视频质量和内容深度

## 📞 技术支持
如需更专业的视频制作方案，请参考：
- video-acquisition-guide.md
- video-production-guide.md`;

            const blob = new Blob([instructions], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'video-deployment-instructions.md';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 初始化
        updateVideoInfo(0);
    </script>
</body>
</html>
