# 完整系统演示录制脚本 (8分钟)

## 录制前准备
- [ ] 确保系统运行在 http://localhost:5173
- [ ] 准备清晰的麦克风
- [ ] 设置1920x1080录制分辨率
- [ ] 关闭不必要的通知和弹窗

## 录制步骤

### 第1段: 系统介绍 (0:00-1:00)
**操作步骤:**
1. 打开浏览器，访问系统首页
2. 展示系统Logo和标题
3. 突出显示iFlytek Spark LLM标识

**旁白脚本:**
"欢迎使用多模态智能面试评估系统。本系统基于iFlytek Spark大模型构建，支持语音、视频、文本三种输入模式，提供六维能力评估和智能分析。"

### 第2段: 功能概览 (1:00-3:00)
**操作步骤:**
1. 导航到演示页面 (/demo)
2. 展示系统架构图
3. 介绍六大评估维度
4. 展示AI、大数据、IoT三大技术领域

**旁白脚本:**
"系统提供六大核心能力评估维度：技术深度、问题解决、沟通表达、逻辑思维、学习能力和团队协作。支持人工智能、大数据和物联网三大技术领域的专业评估。"

### 第3段: 多模态输入演示 (3:00-5:00)
**操作步骤:**
1. 演示语音输入功能
2. 展示视频面试界面
3. 显示文本输入和实时分析

**旁白脚本:**
"系统支持多模态输入。候选人可以通过语音回答问题，系统实时进行语音识别和情感分析。同时支持视频面试，分析面部表情和肢体语言。文本输入则提供详细的语义理解和技术能力评估。"

### 第4段: 评估过程展示 (5:00-7:00)
**操作步骤:**
1. 展示实时分析界面
2. 显示iFlytek Spark处理过程
3. 演示六维能力评分更新

**旁白脚本:**
"iFlytek Spark大模型实时分析候选人的回答，从技术深度、逻辑思维等六个维度进行综合评估。系统提供可视化的分析过程，让评估结果更加透明和可信。"

### 第5段: 报告生成 (7:00-8:00)
**操作步骤:**
1. 展示完整评估报告
2. 显示图表和数据可视化
3. 展示改进建议

**旁白脚本:**
"评估完成后，系统生成详细的分析报告，包括各维度得分、优势分析、改进建议等。帮助企业做出更准确的招聘决策，同时为候选人提供有价值的反馈。"

## 录制技巧
- 鼠标移动要平滑，避免快速跳跃
- 重要功能点击前稍作停顿
- 确保每个界面停留足够时间供观众理解
- 语速适中，发音清晰