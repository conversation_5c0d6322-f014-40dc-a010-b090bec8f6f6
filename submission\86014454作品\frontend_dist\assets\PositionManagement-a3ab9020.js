import{_ as ra,S as ua,h as Ot,r as v,a2 as _e,au as bt,o as da,Y as De,am as ca,i as _,j as p,k as y,n as s,p as t,w as a,N as f,A as oe,a3 as ze,a4 as Fe,ap as d,av as va,aw as Bt,Z as wt,a7 as fa,M as r,G as ye,E as xt,a9 as Mt,ag as pa,O as ma,a5 as Ye,z as m,t as Pe,ak as ga,ae as Re,K as _a,aj as kt,m as ya,F as he,x as be,B as D,C as ha,y as b,W as qt,ax as Kt,D as Ct,ah as St,an as It,v as ba,ay as Wt,X as Ht,ao as Yt,az as At,R as Nt,aA as wa,a0 as xa,aB as ka,at as Ne}from"./index-b6a2842e.js";const Ca={class:"position-management-enhanced"},Sa={class:"page-header-enhanced"},Ia={class:"header-content-grid"},Aa={class:"header-left-section"},$a={class:"header-actions-enhanced"},Va={class:"stats-section-enhanced"},Da={class:"stats-grid"},za={class:"stats-content-enhanced"},Fa={class:"stats-icon-enhanced active-icon"},Pa={class:"stats-info-enhanced"},Ra={class:"stats-number-enhanced"},Ea={class:"stats-trend-enhanced positive"},Ta={class:"stats-content-enhanced"},La={class:"stats-icon-enhanced total-icon"},Ua={class:"stats-info-enhanced"},ja={class:"stats-number-enhanced"},Oa={class:"stats-trend-enhanced positive"},Ba={class:"stats-content-enhanced"},Ma={class:"stats-icon-enhanced urgent-icon"},qa={class:"stats-info-enhanced"},Ka={class:"stats-number-enhanced"},Wa={class:"stats-trend-enhanced negative"},Ha={class:"stats-content-enhanced"},Ya={class:"stats-icon-enhanced cycle-icon"},Na={class:"stats-info-enhanced"},Ga={class:"stats-number-enhanced"},Xa={class:"stats-trend-enhanced positive"},Ja={class:"search-section-enhanced"},Za={class:"search-content-enhanced"},Qa={class:"search-row-primary"},es={class:"search-input-enhanced"},ts={class:"search-suffix-actions"},ls={key:0,class:"search-suggestions"},as=["onClick"],ss={class:"suggestion-type"},ns={class:"quick-filters-enhanced"},os={class:"action-buttons-enhanced"},is={class:"ai-recommendations-section"},rs={class:"recommendations-header"},us={key:0,class:"recommendations-list"},ds={key:1,class:"no-recommendations"},cs={class:"toolbar-section-enhanced"},vs={class:"toolbar-content-enhanced"},fs={class:"toolbar-left-enhanced"},ps={class:"toolbar-right-enhanced"},ms={class:"advanced-filter-section-enhanced"},gs={class:"advanced-filter"},_s={class:"filter-actions"},ys={class:"positions-section"},hs={class:"card-header"},bs={class:"header-actions"},ws={key:0},xs={class:"position-name"},ks={key:1,class:"positions-grid"},Cs={class:"position-header"},Ss={class:"position-title"},Is={class:"position-status"},As={class:"position-info"},$s={class:"info-item"},Vs={class:"info-item"},Ds={class:"info-item"},zs={class:"value"},Fs={class:"info-item"},Ps={class:"value"},Rs={class:"position-actions"},Es={class:"pagination-section"},Ts={key:0,class:"position-detail"},Ls={class:"detail-section"},Us={key:1},js={class:"detail-section"},Os={class:"position-description"},Bs={class:"detail-section"},Ms={class:"stats-grid"},qs={class:"stat-item"},Ks={class:"stat-number"},Ws={class:"stat-item"},Hs={class:"stat-number"},Ys={class:"stat-item"},Ns={class:"stat-number"},Gs={class:"detail-section"},Xs={class:"quick-actions"},Js={class:"charts-section"},Zs={class:"card-header"},Qs={key:0,class:"chart-loading"},en={key:1,class:"chart-empty"},tn={key:0,class:"chart-loading"},ln={key:1,class:"chart-empty"},an={class:"card-header"},sn={key:0,class:"chart-loading"},nn={key:1,class:"chart-empty"},on={class:"export-options"},rn={class:"export-info"},un={class:"custom-export-format-group"},dn={class:"format-icon excel",style:{"margin-left":"40px",width:"56px",height:"56px","border-radius":"12px",display:"flex","align-items":"center","justify-content":"center","font-size":"28px",color:"white","flex-shrink":"0",background:"linear-gradient(135deg, #059669, #10b981)"}},cn={class:"format-icon csv",style:{"margin-left":"40px",width:"56px",height:"56px","border-radius":"12px",display:"flex","align-items":"center","justify-content":"center","font-size":"28px",color:"white","flex-shrink":"0",background:"linear-gradient(135deg, #0066cc, #4c51bf)"}},vn={class:"export-settings"},fn={class:"batch-import-content"},pn={key:0,class:"import-step-content"},mn={class:"upload-area"},gn={class:"template-download"},_n={class:"template-section"},yn={key:1,class:"import-step-content"},hn={class:"preview-header"},bn={style:{margin:"8px 0","padding-left":"20px"}},wn={key:0},xn={key:1,style:{"text-align":"center","margin-top":"16px",color:"#666"}},kn={key:2,class:"import-step-content"},Cn={class:"import-result"},Sn={class:"result-stats"},In={key:0,style:{"margin-top":"20px"}},An={class:"dialog-footer"},$n={class:"ai-assistant-content"},Vn={class:"assistant-header"},Dn={class:"assistant-avatar"},zn={class:"assistant-status"},Fn={class:"assistant-features"},Pn={class:"feature-grid"},Rn={key:0,class:"analysis-results"},En={class:"results-header"},Tn={class:"results-content"},Ln={key:0,class:"recommendations"},Un={__name:"PositionManagement",setup(jn){const $t=ua(),T=Ot({activePositions:28,totalPositions:156,urgentPositions:5,totalCandidates:2456,activeGrowth:12.5,totalGrowth:8.3,urgentChange:-5.2,avgRecruitCycle:18,cycleImprovement:15.8,successRate:78.5,successImprovement:6.2,hotDomain:"AI算法",hotDomainCount:15,pendingReview:3,recruitEfficiency:85.2,efficiencyImprovement:9.1}),R=v(""),$=v(""),V=v(""),z=v(""),ee=v(""),te=v(""),L=v(null),q=v(null),le=v(!1),X=v("table"),ie=v(1),we=v(20),K=v([]),Ee=v([{id:1,name:"紧急AI职位",filters:{domain:"ai",urgent:!0}},{id:2,name:"高级职位",filters:{level:"senior"}},{id:3,name:"北京地区",filters:{location:"beijing"}}]),w=v([{id:1,name:"高级前端工程师",domain:"ai",level:"senior",status:"active",urgent:!0,candidates:45,interviews:23,createdAt:"2024-01-15",description:"负责AI产品前端开发"},{id:2,name:"算法工程师",domain:"ai",level:"expert",status:"active",urgent:!1,candidates:32,interviews:18,createdAt:"2024-01-10",description:"负责机器学习算法研发"},{id:3,name:"大数据开发工程师",domain:"bigdata",level:"middle",status:"paused",urgent:!1,candidates:28,interviews:15,createdAt:"2024-01-08",description:"负责大数据平台开发"}]),C=Ot({name:"",domain:"",level:"",status:"active",urgent:!1,description:""}),Gt={name:[{required:!0,message:"请输入职位名称",trigger:"blur"}],domain:[{required:!0,message:"请选择技术领域",trigger:"change"}],level:[{required:!0,message:"请选择职位级别",trigger:"change"}],status:[{required:!0,message:"请选择职位状态",trigger:"change"}]},re=v(!1),Z=v(!1),ue=v(null),x=v(null),Ge=v(!1),Xe=v(),xe=v(!1),U=v("excel"),Je=v(!0),Ze=v(!0),Qe=v(!1),Te=v(!1),I=v(0),de=v(!1),et=v(null),ce=v(null),W=v([]),J=v([]),j=v({success:!1,title:"",message:"",successCount:0,failureCount:0,totalCount:0,failures:[]}),Le=v(!1),F=v("domain"),Q=v("positions"),tt=v(),lt=v(),at=v();let P=null,O=null,H=null;const ae=_e(()=>{let l=w.value;if(R.value&&(l=l.filter(e=>e.name.toLowerCase().includes(R.value.toLowerCase())||e.description.toLowerCase().includes(R.value.toLowerCase()))),$.value&&$.value!=="all"&&(l=l.filter(e=>e.status===$.value)),V.value&&V.value!=="all"&&(l=l.filter(e=>e.domain===V.value)),z.value&&z.value!=="all"&&(l=l.filter(e=>e.level===z.value)),L.value!==null&&(l=l.filter(e=>e.urgent===L.value)),q.value&&q.value.length===2){const e=new Date(q.value[0]),n=new Date(q.value[1]);l=l.filter(i=>{const c=new Date(i.createdAt);return c>=e&&c<=n})}return ee.value,te.value,l}),Vt=_e(()=>{const l=(ie.value-1)*we.value,e=l+we.value;return ae.value.slice(l,e)}),st=(l,e)=>{let n;return function(...c){const g=()=>{clearTimeout(n),l(...c)};clearTimeout(n),n=setTimeout(g,e)}},Xt=_e(()=>I.value===0?ce.value!==null:I.value===1?W.value.length>0&&J.value.length===0:!1),Jt=()=>{$t.go(-1)},Zt=()=>{$t.push("/batch-interview-setup")},Qt=()=>{Te.value=!1,el()},el=()=>{I.value=0,de.value=!1,ce.value=null,W.value=[],J.value=[],Le.value=!1,j.value={success:!1,title:"",message:"",successCount:0,failureCount:0,totalCount:0,failures:[]},et.value&&et.value.clearFiles()},tl=l=>{ce.value=l,console.log("选择文件:",l.name)},ll=l=>{const e=["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","text/csv"].includes(l.type),n=l.size/1024/1024<10;return e?(n||d.error("文件大小不能超过 10MB!"),!1):(d.error("只支持 Excel (.xlsx, .xls) 和 CSV (.csv) 格式的文件!"),!1)},al=()=>{d.warning("只能选择一个文件进行导入")},sl=async()=>{I.value===0?await ol():I.value===1&&await ul()},nl=()=>{I.value>0&&I.value--},ol=async()=>{if(!ce.value){d.error("请先选择文件");return}de.value=!0;try{const l=await il(ce.value.raw),e=await rl(l,ce.value.name);if(W.value=e.data,J.value=e.errors,e.data.length===0){d.error("文件中没有找到有效的职位数据");return}I.value=1,d.success(`成功解析 ${e.data.length} 条记录`)}catch(l){console.error("文件解析失败:",l),d.error("文件解析失败: "+l.message)}finally{de.value=!1}},il=l=>new Promise((e,n)=>{const i=new FileReader;i.onload=c=>e(c.target.result),i.onerror=()=>n(new Error("文件读取失败")),l.name.endsWith(".csv")?i.readAsText(l,"UTF-8"):i.readAsArrayBuffer(l)}),rl=async(l,e)=>{const n=[],i=[];try{if(e.endsWith(".csv")){const c=l.split(`
`).filter(u=>u.trim());if(c.length<2)throw new Error("CSV文件格式不正确，至少需要标题行和一行数据");const g=c[0].split(",").map(u=>u.trim().replace(/"/g,""));for(let u=1;u<c.length;u++){const h=c[u].split(",").map(k=>k.trim().replace(/"/g,""));if(h.length>=6){const k={name:h[0]||"",domain:h[1]||"",level:h[2]||"",location:h[3]||"",salary:h[4]||"",status:h[5]||"active"},B=Dt(k,u+1);B.valid?n.push(k):i.push(...B.errors)}else i.push(`第${u+1}行: 数据列数不足，需要至少6列`)}}else d.warning("Excel文件解析功能需要安装xlsx库，当前使用模拟数据"),[{name:"高级前端工程师",domain:"ai",level:"senior",location:"北京",salary:"20-30K",status:"active"},{name:"算法工程师",domain:"ai",level:"expert",location:"上海",salary:"25-40K",status:"active"},{name:"大数据开发工程师",domain:"bigdata",level:"middle",location:"深圳",salary:"18-25K",status:"active"}].forEach((g,u)=>{const h=Dt(g,u+1);h.valid?n.push(g):i.push(...h.errors)})}catch(c){throw new Error("文件解析失败: "+c.message)}return{data:n,errors:i}},Dt=(l,e)=>{const n=[];(!l.name||l.name.trim()==="")&&n.push(`第${e}行: 职位名称不能为空`);const i=["ai","bigdata","iot"];i.includes(l.domain)||n.push(`第${e}行: 技术领域必须是 ${i.join(", ")} 之一`);const c=["junior","middle","senior","expert"];c.includes(l.level)||n.push(`第${e}行: 职位级别必须是 ${c.join(", ")} 之一`);const g=["active","paused","closed"];return g.includes(l.status)||n.push(`第${e}行: 职位状态必须是 ${g.join(", ")} 之一`),{valid:n.length===0,errors:n}},ul=async()=>{de.value=!0;try{const l=[],e=[];for(let n=0;n<W.value.length;n++){const i=W.value[n];await new Promise(c=>setTimeout(c,100));try{const c={...i,id:Date.now()+n,candidates:0,interviews:0,createdAt:new Date().toISOString().split("T")[0],urgent:!1};w.value.push(c),l.push(c)}catch(c){e.push({row:n+1,name:i.name,error:c.message||"导入失败"})}}j.value={success:e.length===0,title:e.length===0?"导入成功":"部分导入成功",message:`成功导入 ${l.length} 条记录${e.length>0?`，${e.length} 条记录导入失败`:""}`,successCount:l.length,failureCount:e.length,totalCount:W.value.length,failures:e},I.value=2,await updateStats(),d.success(`批量导入完成！成功导入 ${l.length} 条记录`)}catch(l){console.error("导入失败:",l),d.error("导入失败: "+l.message)}finally{de.value=!1}},dl=()=>{const l=["职位名称,技术领域,职位级别,工作地点,薪资范围,职位状态","高级前端工程师,ai,senior,北京,20-30K,active","算法工程师,ai,expert,上海,25-40K,active","大数据开发工程师,bigdata,middle,深圳,18-25K,active","物联网工程师,iot,middle,杭州,15-25K,active"].join(`
`),e=new Blob(["\uFEFF"+l],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a"),i=URL.createObjectURL(e);n.setAttribute("href",i),n.setAttribute("download","iFlytek职位导入模板.csv"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),d.success("模板文件下载成功")},ke=l=>({ai:"AI技术",bigdata:"大数据",iot:"IoT物联网"})[l]||l,nt=l=>({ai:"#0066cc",bigdata:"#059669",iot:"#ea580c"})[l]||"#666",Ce=l=>({junior:"初级",middle:"中级",senior:"高级",expert:"专家"})[l]||l,ot=l=>({junior:"",middle:"success",senior:"warning",expert:"danger"})[l]||"",Se=l=>({active:"招聘中",paused:"暂停",closed:"已关闭"})[l]||l,it=l=>({active:"success",paused:"warning",closed:"info"})[l]||"",Ue=async l=>{try{switch(ne.value=ne.value===l?"":l,l){case"active":$.value="active";break;case"urgent":L.value=!0;break;case"pending":$.value="pending";break;case"hot":V.value=T.hotDomain==="AI算法"?"ai":"bigdata";break;case"success":case"efficiency":case"cycle":await showStatsDetailAnalysis(l);return;default:Bl();return}await vt(),await Ol(l),d.success(`已筛选${Ml(l)}职位`)}catch(e){console.error("统计卡片点击处理失败:",e),d.error("筛选操作失败，请重试")}},rt=st(async l=>{if(!l||l.length<2){ve.value=[];return}try{ft.value=!0,Nl.value=Date.now();const e=await jl(l);if(ve.value=e,A.value){const n=await _l(l);ve.value=[...e,...n]}}catch(e){console.error("搜索建议生成失败:",e)}finally{ft.value=!1}},300),Ie=async()=>{try{const l={domain:V.value,level:z.value,status:$.value,timestamp:Date.now()};Be.value.unshift(l),Be.value.length>10&&(Be.value=Be.value.slice(0,10)),await vt(),await updateStatsWithFilter(),await updateChartsWithCurrentFilter()}catch(l){console.error("筛选条件变化处理失败:",l)}},cl=()=>{le.value=!le.value,le.value&&d.info("高级筛选已展开")},vl=async()=>{try{Oe.value=!0,A.value||await se(),await dt(),d.success("AI助手已启动")}catch(l){console.error("AI助手启动失败:",l),d.error("AI助手暂时不可用")}},fl=async()=>{try{Ft.value=!0,A.value||await se(),d.info("AI筛选助手已启动，请描述您的筛选需求")}catch(l){console.error("AI筛选助手启动失败:",l),d.error("AI筛选助手暂时不可用")}},pl=async()=>{try{if(!R.value){d.warning("请输入搜索关键词");return}A.value||await se();const l=await gl(R.value);await yl(l),d.success("AI搜索完成")}catch(l){console.error("AI搜索失败:",l),d.error("AI搜索暂时不可用，使用普通搜索")}},ml=()=>{try{ue.value=null,Object.keys(C).forEach(l=>{l==="status"?C[l]="active":l==="urgent"?C[l]=!1:C[l]=""}),re.value=!0}catch(l){console.error("创建职位函数执行失败:",l),d.error("打开对话框失败，请重试")}},ut=l=>{ue.value=l,Object.keys(C).forEach(e=>{C[e]=l[e]||""}),re.value=!0},se=async()=>{try{const{default:l}=await va(()=>import("./index-b6a2842e.js").then(e=>e.aF),["assets/index-b6a2842e.js","assets/index-a404e442.css"]);return A.value=l,console.log("iFlytek Spark服务初始化成功"),!0}catch(l){return console.error("iFlytek服务初始化失败:",l),!1}},dt=async()=>{try{if(console.log("🔄 开始生成智能推荐..."),!A.value){console.warn("⚠️ iFlytek服务未初始化");return}const l={dataScope:"position_management",currentFilters:{domain:V.value,level:z.value,status:$.value},positionData:w.value,statsData:T};console.log("📊 分析请求数据:",l);const e=await A.value.generateDataDrivenInsights(l);return console.log("✅ 获取到洞察数据:",e),fe.value=e.recommendations||[],console.log("🎯 智能推荐数据:",fe.value),fe.value.length>0?console.log(`✅ 成功生成 ${fe.value.length} 条智能推荐`):console.warn("⚠️ 没有生成任何推荐内容"),e}catch(l){return console.error("❌ 智能推荐生成失败:",l),null}},gl=async l=>{try{if(!A.value)return null;const e={text:l,context:"position_search",domain:"recruitment",language:"chinese"},n=await A.value.analyzeTextPrimary(e);return{intent:n.searchIntent||"general",suggestedFilters:n.suggestedFilters||{},keywords:n.extractedKeywords||[],confidence:n.confidence||.5}}catch(e){return console.error("搜索意图分析失败:",e),null}},_l=async l=>{try{return A.value?(await A.value.generateRealTimeHint(null,{query:l,type:"search_suggestions",domain:"position_management"})).hints||[]:[]}catch(e){return console.error("AI搜索建议获取失败:",e),[]}},yl=async l=>{try{if(!l||!l.suggestedFilters)return;const e=l.suggestedFilters;e.domain&&(V.value=e.domain),e.level&&(z.value=e.level),e.status&&($.value=e.status),e.urgent!==void 0&&(L.value=e.urgent),await vt(),l.confidence>.7?d.success(`AI理解您的需求：${l.intent}`):d.info("AI已尽力理解您的需求，如结果不准确请手动调整筛选条件")}catch(e){console.error("应用AI筛选条件失败:",e)}},hl=async()=>{try{A.value||await se(),Me.value=!0,d.info("请开始语音输入..."),setTimeout(()=>{Me.value=!1,R.value="高级AI算法工程师",rt(R.value),d.success("语音识别完成")},3e3)}catch(l){console.error("语音搜索启动失败:",l),Me.value=!1,d.error("语音搜索暂时不可用")}},bl=async()=>{if(Xe.value)try{if(await Xe.value.validate(),Ge.value=!0,await new Promise(l=>setTimeout(l,1e3)),ue.value){const l=w.value.findIndex(e=>e.id===ue.value.id);l!==-1&&(w.value[l]={...w.value[l],...C}),d.success("职位更新成功")}else{const l={id:Date.now(),...C,candidates:0,interviews:0,createdAt:new Date().toISOString().split("T")[0]};w.value.unshift(l),d.success("职位创建成功")}re.value=!1}catch(l){console.error("表单验证失败:",l)}finally{Ge.value=!1}},wl=l=>{const{action:e,position:n}=l;switch(e){case"duplicate":d.info(`复制职位: ${n.name}`);break;case"pause":d.info(`暂停招聘: ${n.name}`);break;case"delete":Ne.confirm(`确定要删除职位"${n.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const i=w.value.findIndex(c=>c.id===n.id);i!==-1&&(w.value.splice(i,1),d.success("职位已删除"))}).catch(()=>{});break}},xl=()=>{xe.value=!0},kl=async()=>{if(ae.value.length===0){d.warning("没有可导出的数据");return}Qe.value=!0;try{const l=Bt.service({lock:!0,text:"正在准备导出数据...",background:"rgba(0, 0, 0, 0.7)"});await new Promise(i=>setTimeout(i,1e3));const e=Cl(),n=Sl();U.value==="excel"?await Il(e,n):await Al(e,n),l.close(),xe.value=!1,d.success(`成功导出 ${ae.value.length} 条职位数据`)}catch(l){console.error("导出失败:",l),d.error("导出失败，请重试")}finally{Qe.value=!1}},Cl=()=>ae.value.map(l=>{const e={职位名称:l.name,技术领域:ke(l.domain),职位级别:Ce(l.level),职位状态:Se(l.status),是否紧急:l.urgent?"是":"否",候选人数:l.candidates,面试数量:l.interviews,创建时间:l.createdAt};return Je.value&&l.description&&(e.职位描述=l.description),e}),Sl=()=>{let l="职位列表";if(Ze.value){const n=new Date().toISOString().replace(/[-:]/g,"").replace("T","_").substring(0,15);l+=`_${n}`}return l},Il=async(l,e)=>{try{const n=Object.keys(l[0]||{});let i=`
      <html>
        <head>
          <meta charset="utf-8">
          <style>
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
          </style>
        </head>
        <body>
          <table>
            <thead>
              <tr>
                ${n.map(h=>`<th>${h}</th>`).join("")}
              </tr>
            </thead>
            <tbody>
    `;l.forEach(h=>{i+="<tr>",n.forEach(k=>{const B=h[k]||"";i+=`<td>${String(B).replace(/</g,"&lt;").replace(/>/g,"&gt;")}</td>`}),i+="</tr>"}),i+=`
            </tbody>
          </table>
        </body>
      </html>
    `;const c=new Blob([i],{type:"application/vnd.ms-excel;charset=utf-8"}),g=window.URL.createObjectURL(c),u=document.createElement("a");u.href=g,u.download=`${e}.xls`,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(g)}catch(n){throw console.error("Excel导出失败:",n),new Error("Excel导出失败")}},Al=async(l,e)=>{try{const n=Object.keys(l[0]||{});let i=n.join(",")+`
`;l.forEach(k=>{const B=n.map(M=>{const N=k[M]||"";return typeof N=="string"&&(N.includes(",")||N.includes(`
`)||N.includes('"'))?`"${N.replace(/"/g,'""')}"`:N});i+=B.join(",")+`
`});const c="\uFEFF",g=new Blob([c+i],{type:"text/csv;charset=utf-8"}),u=window.URL.createObjectURL(g),h=document.createElement("a");h.href=u,h.download=`${e}.csv`,document.body.appendChild(h),h.click(),document.body.removeChild(h),window.URL.revokeObjectURL(u)}catch(n){throw console.error("CSV导出失败:",n),new Error("CSV导出失败")}},$l=l=>{we.value=l,ie.value=1},Vl=l=>{ie.value=l},Dl=()=>{R.value="",$.value="",V.value="",z.value="",ee.value="",te.value="",L.value=null,q.value=null,le.value=!1},zl=()=>{Ne.prompt("请输入预设名称","保存筛选条件",{confirmButtonText:"保存",cancelButtonText:"取消",inputPattern:/\S+/,inputErrorMessage:"预设名称不能为空"}).then(({value:l})=>{const e={id:Date.now(),name:l,filters:{status:$.value,domain:V.value,level:z.value,salaryRange:ee.value,location:te.value,urgent:L.value,dateRange:q.value}};Ee.value.push(e),d.success("筛选条件已保存")}).catch(()=>{})},Fl=l=>{const e=Ee.value.find(n=>n.id===l);e&&($.value=e.filters.status||"",V.value=e.filters.domain||"",z.value=e.filters.level||"",ee.value=e.filters.salaryRange||"",te.value=e.filters.location||"",L.value=e.filters.urgent,q.value=e.filters.dateRange,d.success(`已应用筛选条件：${e.name}`))},Pl=l=>{K.value=l.map(e=>e.id)},ct=l=>{x.value=l,Z.value=!0},Rl=l=>{const e={...l,id:Date.now(),name:`${l.name} (副本)`,createdAt:new Date().toISOString().split("T")[0],candidates:0,interviews:0};w.value.unshift(e),Z.value=!1,d.success("职位复制成功")},El=l=>{l.status="paused",Z.value=!1,d.success("职位已暂停")},Tl=l=>{l.status="active",Z.value=!1,d.success("职位已激活")},Ll=l=>{Ne.confirm(`确定要删除职位"${l.name}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=w.value.findIndex(n=>n.id===l.id);e>-1&&(w.value.splice(e,1),Z.value=!1,d.success("职位删除成功"))}).catch(()=>{})},Ul=l=>{if(K.value.length===0){d.warning("请先选择要操作的职位");return}const e={delete:"删除",activate:"激活",pause:"暂停",urgent:"设为紧急"};Ne.confirm(`确定要${e[l]}选中的 ${K.value.length} 个职位吗？`,"批量操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{switch(l){case"delete":w.value=w.value.filter(n=>!K.value.includes(n.id));break;case"activate":w.value.forEach(n=>{K.value.includes(n.id)&&(n.status="active")});break;case"pause":w.value.forEach(n=>{K.value.includes(n.id)&&(n.status="paused")});break;case"urgent":w.value.forEach(n=>{K.value.includes(n.id)&&(n.urgent=!0)});break}K.value=[],d.success(`批量${e[l]}操作完成`)}).catch(()=>{})},jl=async l=>{const e=[];return w.value.forEach(i=>{i.name.toLowerCase().includes(l.toLowerCase())&&e.push({type:"position",text:i.name,icon:"Setting"})}),["JavaScript","Python","Java","React","Vue.js","Node.js","AI","机器学习","大数据"].forEach(i=>{i.toLowerCase().includes(l.toLowerCase())&&e.push({type:"skill",text:i,icon:"Star"})}),e.slice(0,5)},vt=async()=>{try{const l=Bt.service({target:".position-list",text:"正在筛选职位..."});await new Promise(e=>setTimeout(e,500)),l.close(),ie.value=1}catch(l){console.error("筛选应用失败:",l)}},Ol=async l=>{try{switch(l){case"active":F.value="status";break;case"urgent":F.value="priority";break;case"hot":F.value="domain";break;default:F.value="domain"}await De(),qe(),Ke(),We()}catch(e){console.error("图表更新失败:",e)}},Bl=()=>{R.value="",V.value="",z.value="",$.value="",L.value=null,q.value=null,ee.value="",te.value="",ne.value=""},Ml=l=>({active:"活跃",urgent:"紧急",pending:"待审核",hot:"热门领域",success:"高成功率",efficiency:"高效率",cycle:"短周期"})[l]||"相关",ql=l=>{R.value=l.text,ve.value=[],rt(l.text)},Kl=async l=>{try{switch(console.log("应用AI推荐:",l),l.action){case"filter":l.filters&&(Object.keys(l.filters).forEach(e=>{switch(e){case"domain":V.value=l.filters[e];break;case"level":z.value=l.filters[e];break;case"status":$.value=l.filters[e];break;case"urgent":L.value=l.filters[e];break}}),await Ie());break;case"optimize_descriptions":d.info("职位描述优化功能开发中，敬请期待...");break;case"batch_optimize":d.info("批量优化功能开发中，敬请期待...");break;case"view_trends":d.info(`正在分析${l.domain||"技术"}领域趋势...`);break;case"increase_recruitment":d.warning(`建议加强${l.domain||"相关"}领域人才招聘`);break;case"enable_ai_matching":d.success("iFlytek AI智能匹配功能已启用");break;case"view_report":d.info("数据分析报告功能开发中，敬请期待...");break;case"enable_smart_features":d.success("iFlytek智能推荐系统已启用");break;default:d.info(`推荐建议：${l.text}`)}d.success(`已应用AI推荐：${l.text}`)}catch(e){console.error("应用AI推荐失败:",e),d.error("应用推荐失败")}},je=async l=>{try{A.value||await se();const e={type:l,dataScope:"position_management",currentData:{positions:w.value,stats:T,filters:{domain:V.value,level:z.value,status:$.value}}},n=await A.value.generateDataDrivenInsights(e);Ae.value=n,d.success("AI分析完成")}catch(e){console.error("AI分析请求失败:",e),d.error("AI分析暂时不可用")}},Wl=async()=>{try{A.value=null,await se(),d.success("AI服务已刷新")}catch(l){console.error("AI服务刷新失败:",l),d.error("AI服务刷新失败")}},Hl=_e(()=>{const l=w.value,e=l.reduce((c,g)=>{const u=ke(g.domain);return c[u]=(c[u]||0)+1,c},{}),n=l.reduce((c,g)=>{const u=Ce(g.level);return c[u]=(c[u]||0)+1,c},{}),i=l.reduce((c,g)=>{const u=Se(g.status);return c[u]=(c[u]||0)+1,c},{});return{domain:Object.entries(e).map(([c,g])=>({name:c,value:g})),level:Object.entries(n).map(([c,g])=>({name:c,value:g})),status:Object.entries(i).map(([c,g])=>({name:c,value:g}))}}),zt=_e(()=>{const l=w.value.length,e=w.value.reduce((c,g)=>c+g.candidates,0),n=w.value.reduce((c,g)=>c+g.interviews,0),i=Math.round(n*.3);return[{value:l,name:"职位发布"},{value:e,name:"简历投递"},{value:n,name:"面试邀请"},{value:i,name:"成功入职"}]}),Yl=_e(()=>({months:["1月","2月","3月","4月","5月","6月"],positions:[12,18,25,32,28,35],candidates:[120,180,250,320,280,350],interviews:[45,68,95,128,112,140]})),Y=v(!0),E=v(!1),ne=v(""),Oe=v(!1),Ft=v(!1),ve=v([]),ft=v(!1),Be=v([]),Nl=v(0),A=v(null),Ae=v(null),Me=v(!1),fe=v([]);v(!0),v(Date.now()),v(null),v(""),v(null),v(!1),v([{id:1,name:"高级AI工程师",filters:{domain:"ai",level:"senior"}},{id:2,name:"紧急招聘职位",filters:{urgent:!0}},{id:3,name:"北京地区职位",filters:{location:"beijing"}},{id:4,name:"高薪职位",filters:{salaryRange:"25+"}}]);const qe=()=>{try{if(!tt.value){console.warn("饼图容器未找到");return}const l=tt.value;if(l.clientWidth===0||l.clientHeight===0){requestAnimationFrame(()=>{setTimeout(qe,100)});return}P&&!P.isDisposed()&&P.dispose(),P=wt(l,null,{renderer:"canvas",useDirtyRect:!0,width:l.clientWidth,height:l.clientHeight}),pt(),P.on("click",e=>{const n={AI算法:"ai",大数据:"bigdata",物联网:"iot"};n[e.name]?(V.value=n[e.name],Ie(),d.success(`已筛选${e.name}相关职位`)):d.info(`${e.name}: ${e.value}个职位`)}),E.value=!1}catch(l){console.error("饼图初始化失败:",l),E.value=!0,d.error("图表加载失败，请刷新页面重试")}},pt=()=>{try{if(!P)return;const l=Hl.value[F.value];if(!l||l.length===0){P.setOption({title:{text:"暂无数据",left:"center",top:"middle",textStyle:{color:"#999",fontSize:14}}});return}const n={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",textStyle:{fontSize:12}},color:["#1890ff","#52c41a","#faad14","#f5222d","#722ed1"],series:[{name:F.value==="domain"?"技术领域":F.value==="level"?"职位级别":"职位状态",type:"pie",radius:["40%","70%"],center:["60%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:8,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},labelLine:{show:!1},data:l}]};P.setOption(n,!0)}catch(l){console.error("饼图更新失败:",l),d.error("饼图更新失败")}},Ke=()=>{try{if(!lt.value){console.warn("漏斗图容器未找到");return}const l=lt.value;if(l.clientWidth===0||l.clientHeight===0){setTimeout(Ke,100);return}O=wt(l),Pt(),O.on("click",e=>{d.info(`${e.name}: ${e.value}`)})}catch(l){console.error("漏斗图初始化失败:",l),E.value=!0,d.error("漏斗图初始化失败")}},Pt=()=>{try{if(!O)return;const l=zt.value;if(!l||l.length===0){O.setOption({title:{text:"暂无数据",left:"center",top:"middle",textStyle:{color:"#999",fontSize:14}}});return}const e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c}"},color:["#1890ff","#52c41a","#faad14","#f5222d"],series:[{name:"招聘漏斗",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:Math.max(...zt.value.map(n=>n.value)),minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside",formatter:"{b}: {c}",fontSize:12,color:"#fff"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{fontSize:14}},data:l}]};O.setOption(e,!0)}catch(l){console.error("漏斗图更新失败:",l),d.error("漏斗图更新失败")}},We=()=>{try{if(!at.value){console.warn("折线图容器未找到");return}const l=at.value;if(l.clientWidth===0||l.clientHeight===0){setTimeout(We,100);return}H=wt(l),mt(),H.on("datazoom",e=>{console.log("数据缩放:",e)})}catch(l){console.error("折线图初始化失败:",l),E.value=!0,d.error("折线图初始化失败")}},mt=()=>{try{if(!H)return;const l=Yl.value;let e=[],n="";switch(Q.value){case"positions":e=l.positions,n="职位数量";break;case"candidates":e=l.candidates,n="候选人数";break;case"interviews":e=l.interviews,n="面试数量";break}const i={tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},legend:{data:[n],top:10},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,data:l.months,axisLine:{lineStyle:{color:"#d9d9d9"}},axisLabel:{color:"#666"}}],yAxis:[{type:"value",name:n,nameTextStyle:{color:"#666"},axisLine:{lineStyle:{color:"#d9d9d9"}},axisLabel:{color:"#666"},splitLine:{lineStyle:{color:"#f0f0f0"}}}],series:[{name:n,type:"line",stack:"Total",smooth:!0,lineStyle:{width:3,color:"#1890ff"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.3)"},{offset:1,color:"rgba(24, 144, 255, 0.05)"}]}},emphasis:{focus:"series"},data:e,markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}]};i.dataZoom=[{type:"inside",start:0,end:100},{start:0,end:100,handleIcon:"path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",handleSize:"80%",handleStyle:{color:"#fff",shadowBlur:3,shadowColor:"rgba(0, 0, 0, 0.6)",shadowOffsetX:2,shadowOffsetY:2}}],H.setOption(i,!0)}catch(l){console.error("折线图更新失败:",l),d.error("折线图更新失败")}};bt(F,()=>{De(()=>{pt()})}),bt(Q,()=>{De(()=>{mt()})});const Gl=st(()=>{P&&pt(),O&&Pt(),H&&mt()},500);bt(()=>w.value.length,()=>{De(()=>{Gl()})},{deep:!0});const Rt=st(()=>{P&&P.resize(),O&&O.resize(),H&&H.resize()},300),Et=(l,e="未知操作")=>{console.error(`${e}失败:`,l),l.name==="NetworkError"?d.error("网络连接失败，请检查网络设置"):l.name==="TypeError"?d.error("数据格式错误，请刷新页面重试"):d.error(`${e}失败，请重试`)},He={startTime:0,start(l){this.startTime=performance.now(),console.log(`开始执行: ${l}`)},end(l){const e=performance.now()-this.startTime;console.log(`${l} 执行完成，耗时: ${e.toFixed(2)}ms`),e>1e3&&console.warn(`${l} 执行时间较长，建议优化`)}},Tt=async()=>{try{He.start("图表初始化");const l=[new Promise(e=>{try{qe(),e()}catch(n){console.error("饼图初始化失败:",n),e()}}),new Promise(e=>{try{Ke(),e()}catch(n){console.error("漏斗图初始化失败:",n),e()}}),new Promise(e=>{try{We(),e()}catch(n){console.error("折线图初始化失败:",n),e()}})];await Promise.allSettled(l),Y.value=!1,He.end("图表初始化")}catch(l){Et(l,"图表初始化"),Y.value=!1,E.value=!0}};da(async()=>{try{He.start("页面初始化"),console.log("职位管理页面已加载"),De(()=>{setTimeout(()=>{Xl()},200)}),Y.value=!0,window.requestIdleCallback?window.requestIdleCallback(()=>{Tt()}):setTimeout(Tt,100),se().then(async l=>{if(l)try{await dt(),console.log("智能推荐已生成")}catch(e){console.warn("智能推荐生成失败:",e)}}).catch(l=>{console.warn("AI服务初始化失败，将在需要时重试:",l)}),window.addEventListener("resize",Rt),He.end("页面初始化")}catch(l){Et(l,"页面初始化"),Y.value=!1,E.value=!0}});const Xl=()=>{document.querySelectorAll(".export-format-option").forEach((e,n)=>{const i=e.querySelector(".el-radio__input"),c=e.querySelector(".format-option"),g=e.querySelector(".el-radio__label");i&&(i.style.cssText=`
        position: absolute !important;
        top: 30px !important;
        left: 30px !important;
        z-index: 1000 !important;
        margin: 0 !important;
        transform: none !important;
      `),c&&(c.style.cssText=`
        display: flex !important;
        align-items: center !important;
        gap: 20px !important;
        padding: 30px 30px 30px 80px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 12px !important;
        min-height: 100px !important;
        background: #ffffff !important;
        cursor: pointer !important;
      `),g&&(g.style.cssText=`
        padding-left: 0 !important;
        width: 100% !important;
        display: block !important;
      `)})};return ca(()=>{P&&(P.dispose(),P=null),O&&(O.dispose(),O=null),H&&(H.dispose(),H=null),window.removeEventListener("resize",Rt)}),(l,e)=>{var jt;const n=_("el-icon"),i=_("el-button"),c=_("el-card"),g=_("el-input"),u=_("el-option"),h=_("el-select"),k=_("el-tag"),B=_("el-button-group"),M=_("el-dropdown-item"),N=_("el-dropdown-menu"),gt=_("el-dropdown"),Lt=_("el-divider"),G=_("el-col"),Jl=_("el-date-picker"),$e=_("el-row"),S=_("el-table-column"),_t=_("el-table"),Zl=_("el-pagination"),pe=_("el-form-item"),Ql=_("el-switch"),ea=_("el-form"),Ve=_("el-dialog"),me=_("el-descriptions-item"),ta=_("el-descriptions"),Ut=_("el-checkbox"),yt=_("el-step"),la=_("el-steps"),aa=_("el-upload"),sa=_("el-alert"),ht=_("el-statistic"),na=_("el-result"),oa=_("el-collapse-item"),ia=_("el-collapse");return p(),y("div",Ca,[s("div",Sa,[s("div",Ia,[s("div",Aa,[t(i,{onClick:Jt,link:"",class:"back-btn-enhanced"},{default:a(()=>[t(n,null,{default:a(()=>[t(f(fa))]),_:1}),e[54]||(e[54]=r(" 返回 "))]),_:1,__:[54]}),e[55]||(e[55]=s("div",{class:"page-title-enhanced"},[s("h1",null,"职位管理中心"),s("p",null,"基于iFlytek星火大模型的智能招聘管理系统")],-1))]),s("div",$a,[t(i,{onClick:xl,class:"action-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(f(ye))]),_:1}),e[56]||(e[56]=r(" 导出职位 "))]),_:1,__:[56]}),t(i,{onClick:vl,type:"info",class:"action-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(f(xt))]),_:1}),e[57]||(e[57]=r(" AI助手 "))]),_:1,__:[57]}),t(i,{onClick:Zt,type:"success",class:"action-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(f(Mt))]),_:1}),e[58]||(e[58]=r(" 批量面试 "))]),_:1,__:[58]}),t(i,{onClick:e[0]||(e[0]=o=>Te.value=!0),type:"warning",class:"action-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(f(pa))]),_:1}),e[59]||(e[59]=r(" 批量导入 "))]),_:1,__:[59]}),t(i,{type:"primary",icon:f(ma),onClick:ml,disabled:!1,class:"primary-action-btn"},{default:a(()=>e[60]||(e[60]=[r(" 新增职位 ")])),_:1,__:[60]},8,["icon"])])])]),s("div",Va,[s("div",Da,[t(c,{class:oe(["stats-card-enhanced clickable-card",{"active-filter":ne.value==="active"}]),onClick:e[1]||(e[1]=o=>Ue("active")),shadow:"hover"},{default:a(()=>[s("div",za,[s("div",Fa,[t(n,null,{default:a(()=>[t(f(Ye))]),_:1})]),s("div",Pa,[s("div",Ra,m(T.activePositions),1),e[62]||(e[62]=s("div",{class:"stats-label-enhanced"},"活跃职位",-1)),s("div",Ea,[t(n,null,{default:a(()=>[t(f(Pe))]),_:1}),s("span",null,"+"+m(T.activeGrowth)+"%",1),e[61]||(e[61]=s("span",{class:"trend-period-enhanced"},"较上月",-1))])])])]),_:1},8,["class"]),t(c,{class:oe(["stats-card-enhanced clickable-card",{"active-filter":ne.value==="total"}]),onClick:e[2]||(e[2]=o=>Ue("total")),shadow:"hover"},{default:a(()=>[s("div",Ta,[s("div",La,[t(n,null,{default:a(()=>[t(f(ga))]),_:1})]),s("div",Ua,[s("div",ja,m(T.totalPositions),1),e[64]||(e[64]=s("div",{class:"stats-label-enhanced"},"总职位数",-1)),s("div",Oa,[t(n,null,{default:a(()=>[t(f(Pe))]),_:1}),s("span",null,"+"+m(T.totalGrowth)+"%",1),e[63]||(e[63]=s("span",{class:"trend-period-enhanced"},"较上月",-1))])])])]),_:1},8,["class"]),t(c,{class:oe(["stats-card-enhanced clickable-card",{"active-filter":ne.value==="urgent"}]),onClick:e[3]||(e[3]=o=>Ue("urgent")),shadow:"hover"},{default:a(()=>[s("div",Ba,[s("div",Ma,[t(n,null,{default:a(()=>[t(f(Re))]),_:1})]),s("div",qa,[s("div",Ka,m(T.urgentPositions),1),e[66]||(e[66]=s("div",{class:"stats-label-enhanced"},"紧急招聘",-1)),s("div",Wa,[t(n,null,{default:a(()=>[t(f(Pe))]),_:1}),s("span",null,m(T.urgentChange)+"%",1),e[65]||(e[65]=s("span",{class:"trend-period-enhanced"},"较上月",-1))])])])]),_:1},8,["class"]),t(c,{class:oe(["stats-card-enhanced clickable-card",{"active-filter":ne.value==="cycle"}]),onClick:e[4]||(e[4]=o=>Ue("cycle")),shadow:"hover"},{default:a(()=>[s("div",Ha,[s("div",Ya,[t(n,null,{default:a(()=>[t(f(_a))]),_:1})]),s("div",Na,[s("div",Ga,m(T.avgRecruitCycle),1),e[68]||(e[68]=s("div",{class:"stats-label-enhanced"},"平均招聘周期(天)",-1)),s("div",Xa,[t(n,null,{default:a(()=>[t(f(Pe))]),_:1}),s("span",null,"-"+m(T.cycleImprovement)+"%",1),e[67]||(e[67]=s("span",{class:"trend-period-enhanced"},"较上月",-1))])])])]),_:1},8,["class"])])]),s("div",Ja,[t(c,{class:"search-card-enhanced",shadow:"never"},{default:a(()=>[s("div",Za,[s("div",Qa,[s("div",es,[t(g,{modelValue:R.value,"onUpdate:modelValue":e[5]||(e[5]=o=>R.value=o),placeholder:"🔍 智能搜索职位名称、技能要求、工作描述...","prefix-icon":f(kt),clearable:"",size:"large",class:"smart-search-input",onInput:f(rt),loading:ft.value},{suffix:a(()=>[s("div",ts,[t(i,{onClick:hl,link:"",class:oe(["voice-search-btn",{active:Me.value}]),title:"语音搜索"},{default:a(()=>[t(n,null,{default:a(()=>[t(f(ya))]),_:1})]),_:1},8,["class"]),t(i,{onClick:pl,link:"",class:"ai-search-btn",title:"AI智能搜索"},{default:a(()=>[t(n,null,{default:a(()=>[t(f(kt))]),_:1})]),_:1})])]),_:1},8,["modelValue","prefix-icon","onInput","loading"]),ve.value.length>0?(p(),y("div",ls,[(p(!0),y(he,null,be(ve.value,o=>(p(),y("div",{key:o.text,class:"suggestion-item",onClick:ge=>ql(o)},[t(n,null,{default:a(()=>[(p(),D(ha(o.icon)))]),_:2},1024),s("span",null,m(o.text),1),s("span",ss,m(o.type),1)],8,as))),128))])):b("",!0)]),s("div",ns,[t(h,{modelValue:V.value,"onUpdate:modelValue":e[6]||(e[6]=o=>V.value=o),placeholder:"技术领域",clearable:"",class:"filter-select-enhanced",onChange:Ie},{default:a(()=>[t(u,{label:"全部领域",value:""}),t(u,{label:"AI算法",value:"ai"}),t(u,{label:"大数据",value:"bigdata"}),t(u,{label:"物联网",value:"iot"}),t(u,{label:"前端开发",value:"frontend"}),t(u,{label:"后端开发",value:"backend"})]),_:1},8,["modelValue"]),t(h,{modelValue:z.value,"onUpdate:modelValue":e[7]||(e[7]=o=>z.value=o),placeholder:"职位级别",clearable:"",class:"filter-select-enhanced",onChange:Ie},{default:a(()=>[t(u,{label:"全部级别",value:""}),t(u,{label:"实习生",value:"intern"}),t(u,{label:"初级",value:"junior"}),t(u,{label:"中级",value:"middle"}),t(u,{label:"高级",value:"senior"}),t(u,{label:"专家",value:"expert"})]),_:1},8,["modelValue"]),t(h,{modelValue:$.value,"onUpdate:modelValue":e[8]||(e[8]=o=>$.value=o),placeholder:"职位状态",clearable:"",class:"filter-select-enhanced",onChange:Ie},{default:a(()=>[t(u,{label:"全部状态",value:""}),t(u,{label:"活跃招聘",value:"active"}),t(u,{label:"暂停招聘",value:"paused"}),t(u,{label:"已关闭",value:"closed"}),t(u,{label:"待审核",value:"pending"})]),_:1},8,["modelValue"]),s("div",os,[t(i,{icon:f(kt),onClick:cl,type:le.value?"primary":"default",class:"filter-btn-enhanced"},{default:a(()=>e[69]||(e[69]=[r(" 高级筛选 ")])),_:1,__:[69]},8,["icon","type"]),t(i,{icon:f(Ye),onClick:fl,type:"info",class:"ai-filter-btn-enhanced",loading:Ft.value},{default:a(()=>e[70]||(e[70]=[r(" AI筛选 ")])),_:1,__:[70]},8,["icon","loading"])])])]),s("div",is,[s("div",rs,[t(n,null,{default:a(()=>[t(f(qt))]),_:1}),e[72]||(e[72]=s("span",null,"AI智能推荐",-1)),t(i,{size:"small",type:"primary",onClick:dt,style:{"margin-left":"10px"}},{default:a(()=>e[71]||(e[71]=[r(" 刷新推荐 ")])),_:1,__:[71]})]),fe.value.length>0?(p(),y("div",us,[(p(!0),y(he,null,be(fe.value,o=>(p(),D(k,{key:o.id,type:o.type||"info",class:"recommendation-tag",onClick:ge=>Kl(o)},{default:a(()=>[r(m(o.text),1)]),_:2},1032,["type","onClick"]))),128))])):(p(),y("div",ds,e[73]||(e[73]=[s("p",{style:{color:"#999","font-style":"italic"}},'暂无智能推荐，点击"刷新推荐"获取建议',-1)])))])])]),_:1})]),s("div",cs,[t(c,{class:"toolbar-card-enhanced",shadow:"never"},{default:a(()=>[s("div",vs,[s("div",fs,[t(B,{class:"view-toggle-enhanced"},{default:a(()=>[t(i,{type:X.value==="table"?"primary":"default",icon:f(Kt),onClick:e[9]||(e[9]=o=>X.value="table"),class:"view-btn-enhanced"},{default:a(()=>e[74]||(e[74]=[r(" 列表视图 ")])),_:1,__:[74]},8,["type","icon"]),t(i,{type:X.value==="card"?"primary":"default",icon:f(Ct),onClick:e[10]||(e[10]=o=>X.value="card"),class:"view-btn-enhanced"},{default:a(()=>e[75]||(e[75]=[r(" 卡片视图 ")])),_:1,__:[75]},8,["type","icon"])]),_:1})]),s("div",ps,[K.value.length>0?(p(),D(gt,{key:0,onCommand:Ul},{dropdown:a(()=>[t(N,null,{default:a(()=>[t(M,{command:"delete"},{default:a(()=>e[76]||(e[76]=[r("批量删除")])),_:1,__:[76]}),t(M,{command:"activate"},{default:a(()=>e[77]||(e[77]=[r("批量激活")])),_:1,__:[77]}),t(M,{command:"pause"},{default:a(()=>e[78]||(e[78]=[r("批量暂停")])),_:1,__:[78]}),t(M,{command:"urgent"},{default:a(()=>e[79]||(e[79]=[r("设为紧急")])),_:1,__:[79]})]),_:1})]),default:a(()=>[t(i,{type:"warning",class:"batch-btn-enhanced"},{default:a(()=>[r(" 批量操作("+m(K.value.length)+") ",1),t(n,{class:"el-icon--right"},{default:a(()=>[t(f(St))]),_:1})]),_:1})]),_:1})):b("",!0)])])]),_:1})]),ze(s("div",ms,[t(c,{class:"advanced-filter-card-enhanced",shadow:"never"},{default:a(()=>[ze(s("div",gs,[t(Lt),t($e,{gutter:16},{default:a(()=>[t(G,{span:6},{default:a(()=>[t(h,{modelValue:ee.value,"onUpdate:modelValue":e[11]||(e[11]=o=>ee.value=o),placeholder:"薪资范围",clearable:""},{default:a(()=>[t(u,{label:"全部",value:""}),t(u,{label:"5K-10K",value:"5-10"}),t(u,{label:"10K-20K",value:"10-20"}),t(u,{label:"20K-30K",value:"20-30"}),t(u,{label:"30K以上",value:"30+"})]),_:1},8,["modelValue"])]),_:1}),t(G,{span:6},{default:a(()=>[t(h,{modelValue:te.value,"onUpdate:modelValue":e[12]||(e[12]=o=>te.value=o),placeholder:"工作地点",clearable:""},{default:a(()=>[t(u,{label:"全部",value:""}),t(u,{label:"北京",value:"beijing"}),t(u,{label:"上海",value:"shanghai"}),t(u,{label:"深圳",value:"shenzhen"}),t(u,{label:"杭州",value:"hangzhou"}),t(u,{label:"广州",value:"guangzhou"})]),_:1},8,["modelValue"])]),_:1}),t(G,{span:6},{default:a(()=>[t(h,{modelValue:L.value,"onUpdate:modelValue":e[13]||(e[13]=o=>L.value=o),placeholder:"紧急程度",clearable:""},{default:a(()=>[t(u,{label:"全部",value:""}),t(u,{label:"紧急招聘",value:!0}),t(u,{label:"常规招聘",value:!1})]),_:1},8,["modelValue"])]),_:1}),t(G,{span:6},{default:a(()=>[t(Jl,{modelValue:q.value,"onUpdate:modelValue":e[14]||(e[14]=o=>q.value=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),s("div",_s,[t(i,{onClick:Dl,icon:f(It)},{default:a(()=>e[80]||(e[80]=[r("重置筛选")])),_:1,__:[80]},8,["icon"]),t(i,{type:"primary",onClick:zl,icon:f(Ye)},{default:a(()=>e[81]||(e[81]=[r("保存筛选")])),_:1,__:[81]},8,["icon"]),Ee.value.length>0?(p(),D(gt,{key:0,onCommand:Fl},{dropdown:a(()=>[t(N,null,{default:a(()=>[(p(!0),y(he,null,be(Ee.value,o=>(p(),D(M,{key:o.id,command:o.id},{default:a(()=>[r(m(o.name),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:a(()=>[t(i,null,{default:a(()=>[e[82]||(e[82]=r(" 常用筛选 ")),t(n,{class:"el-icon--right"},{default:a(()=>[t(f(St))]),_:1})]),_:1,__:[82]})]),_:1})):b("",!0)])],512),[[Fe,le.value]])]),_:1})],512),[[Fe,le.value]]),s("div",ys,[t(c,null,{header:a(()=>[s("div",hs,[s("span",null,"职位列表 ("+m(ae.value.length)+")",1),s("div",bs,[t(B,null,{default:a(()=>[t(i,{type:X.value==="table"?"primary":"",onClick:e[15]||(e[15]=o=>X.value="table")},{default:a(()=>[t(n,null,{default:a(()=>[t(f(Kt))]),_:1})]),_:1},8,["type"]),t(i,{type:X.value==="card"?"primary":"",onClick:e[16]||(e[16]=o=>X.value="card")},{default:a(()=>[t(n,null,{default:a(()=>[t(f(Ct))]),_:1})]),_:1},8,["type"])]),_:1})])])]),default:a(()=>[X.value==="table"?(p(),y("div",ws,[t(_t,{data:Vt.value,style:{width:"100%"},onSelectionChange:Pl,"row-key":"id"},{default:a(()=>[t(S,{type:"selection",width:"55"}),t(S,{prop:"name",label:"职位名称",width:"200"},{default:a(o=>[s("div",xs,[s("strong",null,m(o.row.name),1),o.row.urgent?(p(),D(k,{key:0,type:"danger",size:"small"},{default:a(()=>e[83]||(e[83]=[r("紧急")])),_:1,__:[83]})):b("",!0)])]),_:1}),t(S,{prop:"domain",label:"技术领域",width:"120"},{default:a(o=>[t(k,{color:nt(o.row.domain)},{default:a(()=>[r(m(ke(o.row.domain)),1)]),_:2},1032,["color"])]),_:1}),t(S,{prop:"level",label:"级别",width:"100"},{default:a(o=>[t(k,{type:ot(o.row.level)},{default:a(()=>[r(m(Ce(o.row.level)),1)]),_:2},1032,["type"])]),_:1}),t(S,{prop:"status",label:"状态",width:"100"},{default:a(o=>[t(k,{type:it(o.row.status)},{default:a(()=>[r(m(Se(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(S,{prop:"candidates",label:"候选人",width:"100"}),t(S,{prop:"interviews",label:"面试数",width:"100"}),t(S,{prop:"createdAt",label:"创建时间",width:"120"}),t(S,{label:"操作",width:"240"},{default:a(o=>[t(i,{size:"small",icon:f(ba),onClick:ge=>ct(o.row)},{default:a(()=>e[84]||(e[84]=[r(" 预览 ")])),_:2,__:[84]},1032,["icon","onClick"]),t(i,{size:"small",icon:f(Wt),onClick:ge=>ut(o.row)},{default:a(()=>e[85]||(e[85]=[r(" 编辑 ")])),_:2,__:[85]},1032,["icon","onClick"]),t(gt,{onCommand:wl},{dropdown:a(()=>[t(N,null,{default:a(()=>[t(M,{command:{action:"duplicate",position:o.row}},{default:a(()=>[t(n,null,{default:a(()=>[t(f(ye))]),_:1}),e[87]||(e[87]=r(" 复制职位 "))]),_:2,__:[87]},1032,["command"]),t(M,{command:{action:"pause",position:o.row}},{default:a(()=>[t(n,null,{default:a(()=>[t(f(Ht))]),_:1}),e[88]||(e[88]=r(" 暂停招聘 "))]),_:2,__:[88]},1032,["command"]),t(M,{command:{action:"urgent",position:o.row}},{default:a(()=>[t(n,null,{default:a(()=>[t(f(Re))]),_:1}),e[89]||(e[89]=r(" 设为紧急 "))]),_:2,__:[89]},1032,["command"]),t(M,{command:{action:"delete",position:o.row},divided:""},{default:a(()=>[t(n,null,{default:a(()=>[t(f(It))]),_:1}),e[90]||(e[90]=r(" 删除职位 "))]),_:2,__:[90]},1032,["command"])]),_:2},1024)]),default:a(()=>[t(i,{size:"small"},{default:a(()=>[e[86]||(e[86]=r(" 更多")),t(n,{class:"el-icon--right"},{default:a(()=>[t(f(St))]),_:1})]),_:1,__:[86]})]),_:2},1024)]),_:1})]),_:1},8,["data"])])):(p(),y("div",ks,[t($e,{gutter:24},{default:a(()=>[(p(!0),y(he,null,be(Vt.value,o=>(p(),D(G,{span:8,key:o.id},{default:a(()=>[t(c,{class:"position-card",onClick:ge=>ct(o)},{default:a(()=>[s("div",Cs,[s("div",Ss,[s("h4",null,m(o.name),1),o.urgent?(p(),D(k,{key:0,type:"danger",size:"small"},{default:a(()=>e[91]||(e[91]=[r("紧急")])),_:1,__:[91]})):b("",!0)]),s("div",Is,[t(k,{type:it(o.status)},{default:a(()=>[r(m(Se(o.status)),1)]),_:2},1032,["type"])])]),s("div",As,[s("div",$s,[e[92]||(e[92]=s("span",{class:"label"},"技术领域：",-1)),t(k,{color:nt(o.domain),size:"small"},{default:a(()=>[r(m(ke(o.domain)),1)]),_:2},1032,["color"])]),s("div",Vs,[e[93]||(e[93]=s("span",{class:"label"},"职位级别：",-1)),t(k,{type:ot(o.level),size:"small"},{default:a(()=>[r(m(Ce(o.level)),1)]),_:2},1032,["type"])]),s("div",Ds,[e[94]||(e[94]=s("span",{class:"label"},"候选人数：",-1)),s("span",zs,m(o.candidates),1)]),s("div",Fs,[e[95]||(e[95]=s("span",{class:"label"},"面试数量：",-1)),s("span",Ps,m(o.interviews),1)])]),s("div",Rs,[t(i,{size:"small",onClick:Yt(ge=>ut(o),["stop"])},{default:a(()=>e[96]||(e[96]=[r("编辑")])),_:2,__:[96]},1032,["onClick"]),t(i,{size:"small",type:"primary",onClick:Yt(ge=>ct(o),["stop"])},{default:a(()=>e[97]||(e[97]=[r(" 查看详情 ")])),_:2,__:[97]},1032,["onClick"])])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})])),s("div",Es,[t(Zl,{"current-page":ie.value,"onUpdate:currentPage":e[17]||(e[17]=o=>ie.value=o),"page-size":we.value,"onUpdate:pageSize":e[18]||(e[18]=o=>we.value=o),"page-sizes":[10,20,50,100],total:ae.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$l,onCurrentChange:Vl},null,8,["current-page","page-size","total"])])]),_:1})]),t(Ve,{modelValue:re.value,"onUpdate:modelValue":e[26]||(e[26]=o=>re.value=o),title:ue.value?"编辑职位":"创建职位",width:"600px"},{footer:a(()=>[t(i,{onClick:e[25]||(e[25]=o=>re.value=!1)},{default:a(()=>e[98]||(e[98]=[r("取消")])),_:1,__:[98]}),t(i,{type:"primary",onClick:bl,loading:Ge.value},{default:a(()=>[r(m(ue.value?"更新":"创建"),1)]),_:1},8,["loading"])]),default:a(()=>[t(ea,{model:C,rules:Gt,ref_key:"positionFormRef",ref:Xe,"label-width":"100px"},{default:a(()=>[t(pe,{label:"职位名称",prop:"name"},{default:a(()=>[t(g,{modelValue:C.name,"onUpdate:modelValue":e[19]||(e[19]=o=>C.name=o),placeholder:"请输入职位名称"},null,8,["modelValue"])]),_:1}),t(pe,{label:"技术领域",prop:"domain"},{default:a(()=>[t(h,{modelValue:C.domain,"onUpdate:modelValue":e[20]||(e[20]=o=>C.domain=o),placeholder:"选择技术领域",style:{width:"100%"}},{default:a(()=>[t(u,{label:"AI技术",value:"ai"}),t(u,{label:"大数据",value:"bigdata"}),t(u,{label:"IoT物联网",value:"iot"})]),_:1},8,["modelValue"])]),_:1}),t(pe,{label:"职位级别",prop:"level"},{default:a(()=>[t(h,{modelValue:C.level,"onUpdate:modelValue":e[21]||(e[21]=o=>C.level=o),placeholder:"选择职位级别",style:{width:"100%"}},{default:a(()=>[t(u,{label:"初级",value:"junior"}),t(u,{label:"中级",value:"middle"}),t(u,{label:"高级",value:"senior"}),t(u,{label:"专家",value:"expert"})]),_:1},8,["modelValue"])]),_:1}),t(pe,{label:"职位状态",prop:"status"},{default:a(()=>[t(h,{modelValue:C.status,"onUpdate:modelValue":e[22]||(e[22]=o=>C.status=o),placeholder:"选择职位状态",style:{width:"100%"}},{default:a(()=>[t(u,{label:"招聘中",value:"active"}),t(u,{label:"暂停",value:"paused"}),t(u,{label:"已关闭",value:"closed"})]),_:1},8,["modelValue"])]),_:1}),t(pe,{label:"紧急招聘"},{default:a(()=>[t(Ql,{modelValue:C.urgent,"onUpdate:modelValue":e[23]||(e[23]=o=>C.urgent=o)},null,8,["modelValue"])]),_:1}),t(pe,{label:"职位描述"},{default:a(()=>[t(g,{modelValue:C.description,"onUpdate:modelValue":e[24]||(e[24]=o=>C.description=o),type:"textarea",rows:4,placeholder:"请输入职位描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(Ve,{modelValue:Z.value,"onUpdate:modelValue":e[33]||(e[33]=o=>Z.value=o),title:((jt=x.value)==null?void 0:jt.name)||"职位详情",width:"800px"},{footer:a(()=>[t(i,{onClick:e[32]||(e[32]=o=>Z.value=!1)},{default:a(()=>e[112]||(e[112]=[r("关闭")])),_:1,__:[112]})]),default:a(()=>[x.value?(p(),y("div",Ts,[t($e,{gutter:24},{default:a(()=>[t(G,{span:16},{default:a(()=>[s("div",Ls,[e[100]||(e[100]=s("h3",null,"基本信息",-1)),t(ta,{column:2,border:""},{default:a(()=>[t(me,{label:"职位名称"},{default:a(()=>[r(m(x.value.name),1)]),_:1}),t(me,{label:"技术领域"},{default:a(()=>[t(k,{color:nt(x.value.domain)},{default:a(()=>[r(m(ke(x.value.domain)),1)]),_:1},8,["color"])]),_:1}),t(me,{label:"职位级别"},{default:a(()=>[t(k,{type:ot(x.value.level)},{default:a(()=>[r(m(Ce(x.value.level)),1)]),_:1},8,["type"])]),_:1}),t(me,{label:"职位状态"},{default:a(()=>[t(k,{type:it(x.value.status)},{default:a(()=>[r(m(Se(x.value.status)),1)]),_:1},8,["type"])]),_:1}),t(me,{label:"紧急程度"},{default:a(()=>[x.value.urgent?(p(),D(k,{key:0,type:"danger"},{default:a(()=>e[99]||(e[99]=[r("紧急招聘")])),_:1,__:[99]})):(p(),y("span",Us,"常规招聘"))]),_:1}),t(me,{label:"创建时间"},{default:a(()=>[r(m(x.value.createdAt),1)]),_:1})]),_:1})]),s("div",js,[e[101]||(e[101]=s("h3",null,"职位描述",-1)),s("div",Os,m(x.value.description||"暂无描述"),1)])]),_:1}),t(G,{span:8},{default:a(()=>[s("div",Bs,[e[105]||(e[105]=s("h3",null,"招聘数据",-1)),s("div",Ms,[s("div",qs,[s("div",Ks,m(x.value.candidates),1),e[102]||(e[102]=s("div",{class:"stat-label"},"候选人数",-1))]),s("div",Ws,[s("div",Hs,m(x.value.interviews),1),e[103]||(e[103]=s("div",{class:"stat-label"},"面试数量",-1))]),s("div",Ys,[s("div",Ns,m(Math.round(x.value.interviews/x.value.candidates*100)||0)+"%",1),e[104]||(e[104]=s("div",{class:"stat-label"},"面试转化率",-1))])])]),s("div",Gs,[e[111]||(e[111]=s("h3",null,"快速操作",-1)),s("div",Xs,[t(i,{type:"primary",icon:f(Wt),onClick:e[27]||(e[27]=o=>ut(x.value))},{default:a(()=>e[106]||(e[106]=[r(" 编辑职位 ")])),_:1,__:[106]},8,["icon"]),t(i,{icon:f(ye),onClick:e[28]||(e[28]=o=>Rl(x.value))},{default:a(()=>e[107]||(e[107]=[r(" 复制职位 ")])),_:1,__:[107]},8,["icon"]),x.value.status==="active"?(p(),D(i,{key:0,icon:f(Ht),onClick:e[29]||(e[29]=o=>El(x.value))},{default:a(()=>e[108]||(e[108]=[r(" 暂停招聘 ")])),_:1,__:[108]},8,["icon"])):x.value.status==="paused"?(p(),D(i,{key:1,icon:f(xt),onClick:e[30]||(e[30]=o=>Tl(x.value))},{default:a(()=>e[109]||(e[109]=[r(" 恢复招聘 ")])),_:1,__:[109]},8,["icon"])):b("",!0),t(i,{type:"danger",icon:f(It),onClick:e[31]||(e[31]=o=>Ll(x.value))},{default:a(()=>e[110]||(e[110]=[r(" 删除职位 ")])),_:1,__:[110]},8,["icon"])])])]),_:1})]),_:1})])):b("",!0)]),_:1},8,["modelValue","title"]),s("div",Js,[t($e,{gutter:24},{default:a(()=>[t(G,{span:12},{default:a(()=>[t(c,null,{header:a(()=>[s("div",Zs,[e[116]||(e[116]=s("span",null,"职位分布统计",-1)),t(B,{size:"small"},{default:a(()=>[t(i,{type:F.value==="domain"?"primary":"",onClick:e[34]||(e[34]=o=>F.value="domain")},{default:a(()=>e[113]||(e[113]=[r(" 技术领域 ")])),_:1,__:[113]},8,["type"]),t(i,{type:F.value==="level"?"primary":"",onClick:e[35]||(e[35]=o=>F.value="level")},{default:a(()=>e[114]||(e[114]=[r(" 职位级别 ")])),_:1,__:[114]},8,["type"]),t(i,{type:F.value==="status"?"primary":"",onClick:e[36]||(e[36]=o=>F.value="status")},{default:a(()=>e[115]||(e[115]=[r(" 职位状态 ")])),_:1,__:[115]},8,["type"])]),_:1})])]),default:a(()=>[ze(s("div",{ref_key:"pieChartRef",ref:tt,class:"chart-container"},null,512),[[Fe,!Y.value&&!E.value]]),Y.value?(p(),y("div",Qs,[t(n,{class:"is-loading"},{default:a(()=>[t(f(At))]),_:1}),e[117]||(e[117]=s("span",{style:{"margin-left":"8px"}},"图表加载中...",-1))])):b("",!0),E.value?(p(),y("div",en,[t(n,null,{default:a(()=>[t(f(Re))]),_:1}),e[119]||(e[119]=s("span",null,"图表加载失败",-1)),t(i,{size:"small",onClick:qe,style:{"margin-top":"8px"}},{default:a(()=>e[118]||(e[118]=[r("重试")])),_:1,__:[118]})])):b("",!0)]),_:1})]),_:1}),t(G,{span:12},{default:a(()=>[t(c,null,{header:a(()=>e[120]||(e[120]=[s("span",null,"招聘漏斗分析",-1)])),default:a(()=>[ze(s("div",{ref_key:"funnelChartRef",ref:lt,class:"chart-container"},null,512),[[Fe,!Y.value&&!E.value]]),Y.value?(p(),y("div",tn,[t(n,{class:"is-loading"},{default:a(()=>[t(f(At))]),_:1}),e[121]||(e[121]=s("span",{style:{"margin-left":"8px"}},"图表加载中...",-1))])):b("",!0),E.value?(p(),y("div",ln,[t(n,null,{default:a(()=>[t(f(Re))]),_:1}),e[123]||(e[123]=s("span",null,"图表加载失败",-1)),t(i,{size:"small",onClick:Ke,style:{"margin-top":"8px"}},{default:a(()=>e[122]||(e[122]=[r("重试")])),_:1,__:[122]})])):b("",!0)]),_:1})]),_:1})]),_:1}),t($e,{gutter:24,style:{"margin-top":"24px"}},{default:a(()=>[t(G,{span:24},{default:a(()=>[t(c,null,{header:a(()=>[s("div",an,[e[127]||(e[127]=s("span",null,"时间趋势分析",-1)),t(B,{size:"small"},{default:a(()=>[t(i,{type:Q.value==="positions"?"primary":"",onClick:e[37]||(e[37]=o=>Q.value="positions")},{default:a(()=>e[124]||(e[124]=[r(" 职位发布 ")])),_:1,__:[124]},8,["type"]),t(i,{type:Q.value==="candidates"?"primary":"",onClick:e[38]||(e[38]=o=>Q.value="candidates")},{default:a(()=>e[125]||(e[125]=[r(" 候选人数 ")])),_:1,__:[125]},8,["type"]),t(i,{type:Q.value==="interviews"?"primary":"",onClick:e[39]||(e[39]=o=>Q.value="interviews")},{default:a(()=>e[126]||(e[126]=[r(" 面试数量 ")])),_:1,__:[126]},8,["type"])]),_:1})])]),default:a(()=>[ze(s("div",{ref_key:"lineChartRef",ref:at,class:"chart-container-large"},null,512),[[Fe,!Y.value&&!E.value]]),Y.value?(p(),y("div",sn,[t(n,{class:"is-loading"},{default:a(()=>[t(f(At))]),_:1}),e[128]||(e[128]=s("span",{style:{"margin-left":"8px"}},"图表加载中...",-1))])):b("",!0),E.value?(p(),y("div",nn,[t(n,null,{default:a(()=>[t(f(Re))]),_:1}),e[130]||(e[130]=s("span",null,"图表加载失败",-1)),t(i,{size:"small",onClick:We,style:{"margin-top":"8px"}},{default:a(()=>e[129]||(e[129]=[r("重试")])),_:1,__:[129]})])):b("",!0)]),_:1})]),_:1})]),_:1})]),t(Ve,{modelValue:xe.value,"onUpdate:modelValue":e[45]||(e[45]=o=>xe.value=o),title:"导出职位数据",width:"400px"},{footer:a(()=>[t(i,{onClick:e[44]||(e[44]=o=>xe.value=!1)},{default:a(()=>e[138]||(e[138]=[r("取消")])),_:1,__:[138]}),t(i,{type:"primary",onClick:kl,loading:Qe.value},{default:a(()=>[t(n,null,{default:a(()=>[t(f(ye))]),_:1}),e[139]||(e[139]=r(" 开始导出 "))]),_:1,__:[139]},8,["loading"])]),default:a(()=>[s("div",on,[s("div",rn,[s("p",null,[e[131]||(e[131]=r("即将导出 ")),s("strong",null,m(ae.value.length),1),e[132]||(e[132]=r(" 条职位数据"))]),e[133]||(e[133]=s("p",{class:"export-note"},"请选择导出格式：",-1))]),s("div",un,[s("div",{class:oe(["custom-format-option",{selected:U.value==="excel"}]),onClick:e[40]||(e[40]=o=>U.value="excel"),style:{position:"relative",display:"flex","align-items":"center",gap:"20px",padding:"24px",border:"2px solid #e5e7eb","border-radius":"12px","min-height":"90px",background:"#ffffff",cursor:"pointer",transition:"all 0.3s ease","margin-bottom":"16px"}},[s("div",{class:"custom-radio-indicator",style:Nt({position:"absolute",top:"24px",left:"24px",width:"16px",height:"16px",border:U.value==="excel"?"2px solid #0066cc":"2px solid #d1d5db",borderRadius:"50%",background:U.value==="excel"?"#0066cc":"#ffffff",boxShadow:U.value==="excel"?"inset 0 0 0 3px #ffffff":"none",transition:"all 0.3s ease",zIndex:"10"})},null,4),s("div",dn,[t(n,null,{default:a(()=>[t(f(ye))]),_:1})]),e[134]||(e[134]=s("div",{class:"format-info",style:{flex:"1","min-width":"0","padding-left":"16px"}},[s("div",{class:"format-name",style:{"font-size":"17px","font-weight":"600",color:"#1f2937","margin-bottom":"8px","line-height":"1.4"}},"Excel 格式"),s("div",{class:"format-desc",style:{"font-size":"14px",color:"#6b7280","line-height":"1.5"}},"适合数据分析和编辑 (.xls)")],-1))],2),s("div",{class:oe(["custom-format-option",{selected:U.value==="csv"}]),onClick:e[41]||(e[41]=o=>U.value="csv"),style:{position:"relative",display:"flex","align-items":"center",gap:"20px",padding:"24px",border:"2px solid #e5e7eb","border-radius":"12px","min-height":"90px",background:"#ffffff",cursor:"pointer",transition:"all 0.3s ease","margin-bottom":"16px"}},[s("div",{class:"custom-radio-indicator",style:Nt({position:"absolute",top:"24px",left:"24px",width:"16px",height:"16px",border:U.value==="csv"?"2px solid #0066cc":"2px solid #d1d5db",borderRadius:"50%",background:U.value==="csv"?"#0066cc":"#ffffff",boxShadow:U.value==="csv"?"inset 0 0 0 3px #ffffff":"none",transition:"all 0.3s ease",zIndex:"10"})},null,4),s("div",cn,[t(n,null,{default:a(()=>[t(f(ye))]),_:1})]),e[135]||(e[135]=s("div",{class:"format-info",style:{flex:"1","min-width":"0","padding-left":"16px"}},[s("div",{class:"format-name",style:{"font-size":"17px","font-weight":"600",color:"#1f2937","margin-bottom":"8px","line-height":"1.4"}},"CSV 格式"),s("div",{class:"format-desc",style:{"font-size":"14px",color:"#6b7280","line-height":"1.5"}},"通用格式，兼容性好 (.csv)")],-1))],2)]),s("div",vn,[t(Ut,{modelValue:Je.value,"onUpdate:modelValue":e[42]||(e[42]=o=>Je.value=o)},{default:a(()=>e[136]||(e[136]=[r("包含职位描述")])),_:1,__:[136]},8,["modelValue"]),t(Ut,{modelValue:Ze.value,"onUpdate:modelValue":e[43]||(e[43]=o=>Ze.value=o)},{default:a(()=>e[137]||(e[137]=[r("文件名包含时间戳")])),_:1,__:[137]},8,["modelValue"])])])]),_:1},8,["modelValue"]),t(Ve,{modelValue:Te.value,"onUpdate:modelValue":e[47]||(e[47]=o=>Te.value=o),title:"批量导入职位",width:"700px","close-on-click-modal":!1},{footer:a(()=>[s("div",An,[I.value>0?(p(),D(i,{key:0,onClick:nl},{default:a(()=>e[147]||(e[147]=[r("上一步")])),_:1,__:[147]})):b("",!0),t(i,{onClick:Qt},{default:a(()=>[r(m(I.value===2?"完成":"取消"),1)]),_:1}),I.value<2?(p(),D(i,{key:1,type:"primary",onClick:sl,disabled:!Xt.value,loading:de.value},{default:a(()=>[r(m(I.value===0?"解析文件":"开始导入"),1)]),_:1},8,["disabled","loading"])):b("",!0)])]),default:a(()=>[s("div",fn,[t(la,{active:I.value,"finish-status":"success","align-center":"",style:{"margin-bottom":"30px"}},{default:a(()=>[t(yt,{title:"选择文件",description:"上传Excel或CSV文件"}),t(yt,{title:"数据预览",description:"确认导入数据"}),t(yt,{title:"导入完成",description:"查看导入结果"})]),_:1},8,["active"]),I.value===0?(p(),y("div",pn,[s("div",mn,[t(aa,{ref_key:"uploadRef",ref:et,class:"upload-dragger",drag:"","auto-upload":!1,"on-change":tl,"before-upload":ll,accept:".xlsx,.xls,.csv",limit:1,"on-exceed":al},{tip:a(()=>e[140]||(e[140]=[s("div",{class:"el-upload__tip"}," 支持 .xlsx、.xls、.csv 格式，文件大小不超过 10MB ",-1)])),default:a(()=>[t(n,{class:"el-icon--upload"},{default:a(()=>[t(f(wa))]),_:1}),e[141]||(e[141]=s("div",{class:"el-upload__text"},[r(" 将文件拖到此处，或"),s("em",null,"点击上传")],-1))]),_:1,__:[141]},512)]),s("div",gn,[t(Lt,null,{default:a(()=>e[142]||(e[142]=[r("或")])),_:1,__:[142]}),s("div",_n,[e[144]||(e[144]=s("h4",null,"下载导入模板",-1)),e[145]||(e[145]=s("p",null,"如果您是首次使用批量导入功能，建议先下载模板文件",-1)),t(i,{type:"primary",plain:"",onClick:dl},{default:a(()=>[t(n,null,{default:a(()=>[t(f(xa))]),_:1}),e[143]||(e[143]=r(" 下载Excel模板 "))]),_:1,__:[143]})])])])):b("",!0),I.value===1?(p(),y("div",yn,[s("div",hn,[s("h4",null,"数据预览 (共 "+m(W.value.length)+" 条记录)",1),J.value.length>0?(p(),D(k,{key:0,type:"warning"},{default:a(()=>[r(" 发现 "+m(J.value.length)+" 个问题 ",1)]),_:1})):b("",!0)]),J.value.length>0?(p(),D(sa,{key:0,title:"数据验证问题",type:"warning",closable:!1,style:{"margin-bottom":"16px"}},{default:a(()=>[s("ul",bn,[(p(!0),y(he,null,be(J.value.slice(0,5),o=>(p(),y("li",{key:o},m(o),1))),128)),J.value.length>5?(p(),y("li",wn," 还有 "+m(J.value.length-5)+" 个问题... ",1)):b("",!0)])]),_:1})):b("",!0),t(_t,{data:W.value.slice(0,10),border:"",style:{width:"100%"},"max-height":"300"},{default:a(()=>[t(S,{prop:"name",label:"职位名称",width:"150"}),t(S,{prop:"domain",label:"技术领域",width:"100"}),t(S,{prop:"level",label:"职位级别",width:"100"}),t(S,{prop:"location",label:"工作地点",width:"120"}),t(S,{prop:"salary",label:"薪资范围",width:"120"}),t(S,{prop:"status",label:"状态",width:"80"})]),_:1},8,["data"]),W.value.length>10?(p(),y("div",xn," 仅显示前10条记录，实际将导入 "+m(W.value.length)+" 条记录 ",1)):b("",!0)])):b("",!0),I.value===2?(p(),y("div",kn,[s("div",Cn,[t(na,{icon:j.value.success?"success":"warning",title:j.value.title,"sub-title":j.value.message},{extra:a(()=>[s("div",Sn,[t(ht,{title:"成功导入",value:j.value.successCount},null,8,["value"]),t(ht,{title:"失败记录",value:j.value.failureCount},null,8,["value"]),t(ht,{title:"总计记录",value:j.value.totalCount},null,8,["value"])]),j.value.failures.length>0?(p(),y("div",In,[t(i,{type:"warning",plain:"",onClick:e[46]||(e[46]=o=>Le.value=!Le.value)},{default:a(()=>e[146]||(e[146]=[r(" 查看失败详情 ")])),_:1,__:[146]})])):b("",!0)]),_:1},8,["icon","title","sub-title"]),Le.value&&j.value.failures.length>0?(p(),D(ia,{key:0,style:{"margin-top":"20px"}},{default:a(()=>[t(oa,{title:"失败记录详情",name:"failures"},{default:a(()=>[t(_t,{data:j.value.failures,border:"",style:{width:"100%"}},{default:a(()=>[t(S,{prop:"row",label:"行号",width:"80"}),t(S,{prop:"name",label:"职位名称",width:"150"}),t(S,{prop:"error",label:"失败原因"})]),_:1},8,["data"])]),_:1})]),_:1})):b("",!0)])])):b("",!0)])]),_:1},8,["modelValue"]),t(Ve,{modelValue:Oe.value,"onUpdate:modelValue":e[53]||(e[53]=o=>Oe.value=o),title:"iFlytek星火AI助手",width:"800px","close-on-click-modal":!1,class:"ai-assistant-dialog"},{footer:a(()=>[t(i,{onClick:e[52]||(e[52]=o=>Oe.value=!1)},{default:a(()=>e[159]||(e[159]=[r("关闭")])),_:1,__:[159]}),t(i,{type:"primary",onClick:Wl},{default:a(()=>[t(n,null,{default:a(()=>[t(f(Ye))]),_:1}),e[160]||(e[160]=r(" 刷新服务 "))]),_:1,__:[160]})]),default:a(()=>[s("div",$n,[s("div",Vn,[s("div",Dn,[t(n,null,{default:a(()=>[t(f(xt))]),_:1})]),e[148]||(e[148]=s("div",{class:"assistant-info"},[s("h3",null,"iFlytek星火大模型"),s("p",null,"智能招聘管理助手，为您提供专业的职位管理建议")],-1)),s("div",zn,[t(k,{type:A.value?"success":"warning"},{default:a(()=>[r(m(A.value?"已连接":"连接中"),1)]),_:1},8,["type"])])]),s("div",Fn,[s("div",Pn,[s("div",{class:"feature-card",onClick:e[48]||(e[48]=o=>je("position_trends"))},[t(n,null,{default:a(()=>[t(f(Pe))]),_:1}),e[149]||(e[149]=s("h4",null,"职位趋势分析",-1)),e[150]||(e[150]=s("p",null,"分析当前职位市场趋势和热门技能需求",-1))]),s("div",{class:"feature-card",onClick:e[49]||(e[49]=o=>je("recruitment_optimization"))},[t(n,null,{default:a(()=>[t(f(ka))]),_:1}),e[151]||(e[151]=s("h4",null,"招聘效率优化",-1)),e[152]||(e[152]=s("p",null,"基于数据分析提供招聘流程优化建议",-1))]),s("div",{class:"feature-card",onClick:e[50]||(e[50]=o=>je("candidate_matching"))},[t(n,null,{default:a(()=>[t(f(Mt))]),_:1}),e[153]||(e[153]=s("h4",null,"候选人匹配",-1)),e[154]||(e[154]=s("p",null,"智能匹配最适合的候选人和职位",-1))]),s("div",{class:"feature-card",onClick:e[51]||(e[51]=o=>je("market_insights"))},[t(n,null,{default:a(()=>[t(f(Ct))]),_:1}),e[155]||(e[155]=s("h4",null,"市场洞察",-1)),e[156]||(e[156]=s("p",null,"获取行业薪资水平和竞争态势分析",-1))])])]),Ae.value?(p(),y("div",Rn,[s("div",En,[t(n,null,{default:a(()=>[t(f(qt))]),_:1}),e[157]||(e[157]=s("span",null,"AI分析结果",-1))]),s("div",Tn,[s("p",null,m(Ae.value.summary),1),Ae.value.recommendations?(p(),y("div",Ln,[e[158]||(e[158]=s("h4",null,"智能推荐",-1)),s("ul",null,[(p(!0),y(he,null,be(Ae.value.recommendations,o=>(p(),y("li",{key:o.id},m(o.text),1))),128))])])):b("",!0)])])):b("",!0)])]),_:1},8,["modelValue"])])}}},Bn=ra(Un,[["__scopeId","data-v-8022691c"]]);export{Bn as default};
