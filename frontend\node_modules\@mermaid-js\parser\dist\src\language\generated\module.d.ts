/******************************************************************************
 * This file was generated by langium-cli 3.3.0.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/
import type { LangiumSharedCoreServices, LangiumCoreServices, LangiumGeneratedCoreServices, LangiumGeneratedSharedCoreServices, Module } from 'langium';
export declare const InfoLanguageMetaData: {
    readonly languageId: "info";
    readonly fileExtensions: readonly [".mmd", ".mermaid"];
    readonly caseInsensitive: false;
    readonly mode: "production";
};
export declare const PacketLanguageMetaData: {
    readonly languageId: "packet";
    readonly fileExtensions: readonly [".mmd", ".mermaid"];
    readonly caseInsensitive: false;
    readonly mode: "production";
};
export declare const PieLanguageMetaData: {
    readonly languageId: "pie";
    readonly fileExtensions: readonly [".mmd", ".mermaid"];
    readonly caseInsensitive: false;
    readonly mode: "production";
};
export declare const ArchitectureLanguageMetaData: {
    readonly languageId: "architecture";
    readonly fileExtensions: readonly [".mmd", ".mermaid"];
    readonly caseInsensitive: false;
    readonly mode: "production";
};
export declare const GitGraphLanguageMetaData: {
    readonly languageId: "gitGraph";
    readonly fileExtensions: readonly [".mmd", ".mermaid"];
    readonly caseInsensitive: false;
    readonly mode: "production";
};
export declare const RadarLanguageMetaData: {
    readonly languageId: "radar";
    readonly fileExtensions: readonly [".mmd", ".mermaid"];
    readonly caseInsensitive: false;
    readonly mode: "production";
};
export declare const TreemapLanguageMetaData: {
    readonly languageId: "treemap";
    readonly fileExtensions: readonly [".mmd", ".mermaid"];
    readonly caseInsensitive: false;
    readonly mode: "production";
};
export declare const MermaidGeneratedSharedModule: Module<LangiumSharedCoreServices, LangiumGeneratedSharedCoreServices>;
export declare const InfoGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
export declare const PacketGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
export declare const PieGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
export declare const ArchitectureGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
export declare const GitGraphGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
export declare const RadarGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
export declare const TreemapGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
