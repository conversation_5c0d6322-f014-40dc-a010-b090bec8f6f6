{"version": 3, "file": "progress2.js", "sources": ["../../../../../../packages/components/progress/src/progress.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.m(type),\n      ns.is(status),\n      {\n        [ns.m('without-text')]: !showText,\n        [ns.m('text-inside')]: textInside,\n      },\n    ]\"\n    role=\"progressbar\"\n    :aria-valuenow=\"percentage\"\n    aria-valuemin=\"0\"\n    aria-valuemax=\"100\"\n  >\n    <div v-if=\"type === 'line'\" :class=\"ns.b('bar')\">\n      <div\n        :class=\"ns.be('bar', 'outer')\"\n        :style=\"{ height: `${strokeWidth}px` }\"\n      >\n        <div\n          :class=\"[\n            ns.be('bar', 'inner'),\n            { [ns.bem('bar', 'inner', 'indeterminate')]: indeterminate },\n            { [ns.bem('bar', 'inner', 'striped')]: striped },\n            { [ns.bem('bar', 'inner', 'striped-flow')]: stripedFlow },\n          ]\"\n          :style=\"barStyle\"\n        >\n          <div\n            v-if=\"(showText || $slots.default) && textInside\"\n            :class=\"ns.be('bar', 'innerText')\"\n          >\n            <slot :percentage=\"percentage\">\n              <span>{{ content }}</span>\n            </slot>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div\n      v-else\n      :class=\"ns.b('circle')\"\n      :style=\"{ height: `${width}px`, width: `${width}px` }\"\n    >\n      <svg viewBox=\"0 0 100 100\">\n        <path\n          :class=\"ns.be('circle', 'track')\"\n          :d=\"trackPath\"\n          :stroke=\"`var(${ns.cssVarName('fill-color-light')}, #e5e9f2)`\"\n          :stroke-linecap=\"strokeLinecap\"\n          :stroke-width=\"relativeStrokeWidth\"\n          fill=\"none\"\n          :style=\"trailPathStyle\"\n        />\n        <path\n          :class=\"ns.be('circle', 'path')\"\n          :d=\"trackPath\"\n          :stroke=\"stroke\"\n          fill=\"none\"\n          :opacity=\"percentage ? 1 : 0\"\n          :stroke-linecap=\"strokeLinecap\"\n          :stroke-width=\"relativeStrokeWidth\"\n          :style=\"circlePathStyle\"\n        />\n      </svg>\n    </div>\n    <div\n      v-if=\"(showText || $slots.default) && !textInside\"\n      :class=\"ns.e('text')\"\n      :style=\"{ fontSize: `${progressTextSize}px` }\"\n    >\n      <slot :percentage=\"percentage\">\n        <span v-if=\"!status\">{{ content }}</span>\n        <el-icon v-else>\n          <component :is=\"statusIcon\" />\n        </el-icon>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  Check,\n  CircleCheck,\n  CircleClose,\n  Close,\n  WarningFilled,\n} from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isString } from '@element-plus/utils'\nimport { progressProps } from './progress'\nimport type { CSSProperties } from 'vue'\nimport type { ProgressColor } from './progress'\n\ndefineOptions({\n  name: 'ElProgress',\n})\n\nconst STATUS_COLOR_MAP: Record<string, string> = {\n  success: '#13ce66',\n  exception: '#ff4949',\n  warning: '#e6a23c',\n  default: '#20a0ff',\n}\n\nconst props = defineProps(progressProps)\n\nconst ns = useNamespace('progress')\n\nconst barStyle = computed<CSSProperties>(() => {\n  const barStyle: CSSProperties = {\n    width: `${props.percentage}%`,\n    animationDuration: `${props.duration}s`,\n  }\n  const color = getCurrentColor(props.percentage)\n  if (color.includes('gradient')) {\n    barStyle.background = color\n  } else {\n    barStyle.backgroundColor = color\n  }\n  return barStyle\n})\n\nconst relativeStrokeWidth = computed(() =>\n  ((props.strokeWidth / props.width) * 100).toFixed(1)\n)\n\nconst radius = computed(() => {\n  if (['circle', 'dashboard'].includes(props.type)) {\n    return Number.parseInt(\n      `${50 - Number.parseFloat(relativeStrokeWidth.value) / 2}`,\n      10\n    )\n  }\n  return 0\n})\n\nconst trackPath = computed(() => {\n  const r = radius.value\n  const isDashboard = props.type === 'dashboard'\n  return `\n          M 50 50\n          m 0 ${isDashboard ? '' : '-'}${r}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? '-' : ''}${r * 2}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? '' : '-'}${r * 2}\n          `\n})\n\nconst perimeter = computed(() => 2 * Math.PI * radius.value)\n\nconst rate = computed(() => (props.type === 'dashboard' ? 0.75 : 1))\n\nconst strokeDashoffset = computed(() => {\n  const offset = (-1 * perimeter.value * (1 - rate.value)) / 2\n  return `${offset}px`\n})\n\nconst trailPathStyle = computed<CSSProperties>(() => ({\n  strokeDasharray: `${perimeter.value * rate.value}px, ${perimeter.value}px`,\n  strokeDashoffset: strokeDashoffset.value,\n}))\n\nconst circlePathStyle = computed<CSSProperties>(() => ({\n  strokeDasharray: `${\n    perimeter.value * rate.value * (props.percentage / 100)\n  }px, ${perimeter.value}px`,\n  strokeDashoffset: strokeDashoffset.value,\n  transition:\n    'stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s',\n}))\n\nconst stroke = computed(() => {\n  let ret: string\n  if (props.color) {\n    ret = getCurrentColor(props.percentage)\n  } else {\n    ret = STATUS_COLOR_MAP[props.status] || STATUS_COLOR_MAP.default\n  }\n  return ret\n})\n\nconst statusIcon = computed(() => {\n  if (props.status === 'warning') {\n    return WarningFilled\n  }\n  if (props.type === 'line') {\n    return props.status === 'success' ? CircleCheck : CircleClose\n  } else {\n    return props.status === 'success' ? Check : Close\n  }\n})\n\nconst progressTextSize = computed(() => {\n  return props.type === 'line'\n    ? 12 + props.strokeWidth * 0.4\n    : props.width * 0.111111 + 2\n})\n\nconst content = computed(() => props.format(props.percentage))\n\nfunction getColors(color: ProgressColor[]) {\n  const span = 100 / color.length\n  const seriesColors = color.map((seriesColor, index) => {\n    if (isString(seriesColor)) {\n      return {\n        color: seriesColor,\n        percentage: (index + 1) * span,\n      }\n    }\n    return seriesColor\n  })\n  return seriesColors.sort((a, b) => a.percentage - b.percentage)\n}\n\nconst getCurrentColor = (percentage: number) => {\n  const { color } = props\n  if (isFunction(color)) {\n    return color(percentage)\n  } else if (isString(color)) {\n    return color\n  } else {\n    const colors = getColors(color)\n    for (const color of colors) {\n      if (color.percentage > percentage) return color.color\n    }\n    return colors[colors.length - 1]?.color\n  }\n}\n</script>\n"], "names": ["useNamespace", "computed", "barStyle", "WarningFilled", "CircleCheck", "CircleClose", "Check", "Close", "isString", "isFunction", "_openBlock", "_createElementBlock", "_normalizeClass"], "mappings": ";;;;;;;;;;;;uCAmGc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAA,MAAM,gBAA2C,GAAA;AAAA,MAC/C,OAAS,EAAA,SAAA;AAAA,MACT,SAAW,EAAA,SAAA;AAAA,MACX,OAAS,EAAA,SAAA;AAAA,MACT,OAAS,EAAA,SAAA;AAAA,KACX,CAAA;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,UAAU,CAAA,CAAA;AAElC,IAAM,MAAA,QAAA,GAAWC,aAAwB,MAAM;AAC7C,MAAA,MAAMC,SAA0B,GAAA;AAAA,QAC9B,KAAA,EAAO,CAAG,EAAA,KAAA,CAAM,UAAU,CAAA,CAAA,CAAA;AAAA,QAC1B,iBAAA,EAAmB,CAAG,EAAA,KAAA,CAAM,QAAQ,CAAA,CAAA,CAAA;AAAA,OACtC,CAAA;AACA,MAAM,MAAA,KAAA,GAAQ,eAAgB,CAAA,KAAA,CAAM,UAAU,CAAA,CAAA;AAC9C,MAAI,IAAA,KAAA,CAAM,QAAS,CAAA,UAAU,CAAG,EAAA;AAC9B,QAAAA,UAAS,UAAa,GAAA,KAAA,CAAA;AAAA,OACjB,MAAA;AACL,QAAAA,UAAS,eAAkB,GAAA,KAAA,CAAA;AAAA,OAC7B;AACA,MAAOA,OAAAA,SAAAA,CAAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAA,MAAM,mBAAsB,GAAAD,YAAA,CAAA,MAAA,CAAA,KAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,GAAA,GAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IAAA,YAClB,GAAAA,YAAA,CAAA;AAA2C,MACrD,IAAA,CAAA,QAAA,EAAA,WAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,EAAA;AAEA,QAAM,OAAA,eAAwB,CAAA,CAAA,EAAA,EAAA,GAAA,MAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA;AAC5B,OAAA;AACE,MAAA,OAAA,CAAA,CAAO;AAAO,KAAA,CAAA,CAAA;AAC4C,IACxD,MAAA,SAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACF,MAAA,CAAA,GAAA,MAAA,CAAA,KAAA,CAAA;AAAA,MACF,MAAA,WAAA,GAAA,KAAA,CAAA,IAAA,KAAA,WAAA,CAAA;AACA,MAAO,OAAA,CAAA;AAAA;AAGT,cAAM,EAAA,gBAA2B,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AAC/B,YAAM,IAAI,CAAO,EAAA,CAAA,CAAA,SAAA,EAAA,WAAA,GAAA,GAAA,GAAA,EAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA;AACjB,YAAM,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,WAAoB,WAAS,GAAA,EAAA,GAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA;AACnC,UAAO,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MAAA,SAEqB,GAAAA,YAAA,CAAA,MAAQ,CAAA,GAAG,IAAC,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAC5B,MAAA,IAAA,eAAM,CAAA,MAAY,eAAoB,WAAK,GAAI,IAAC,GAAA,CAAA,CAAA,CAAA;AAAA,IAChD,MAAA,gBAAM,GAAAA;AAA0C,MAAA,MAAA,MAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,KAAA,IAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,MAE7D,OAAA,CAAA,EAAA,MAAA,CAAA,EAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,cAAgB,GAAAA,oBAAsB;AAE5C,MAAM,eAAA,EAAA,CAAA,EAAA,eAAkC,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,SAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AACtC,MAAA,gBAAqB,EAAA,gBAAmB,CAAA,KAAA;AACxC,KAAA,CAAA,CAAA,CAAA;AAAgB,IAClB,MAAC,eAAA,GAAAA,YAAA,CAAA,OAAA;AAED,MAAM,eAAA,EAAA,CAAA,EAAA,UAAyC,KAAO,GAAA,IAAA,CAAA,KAAA,IAAA,KAAA,CAAA,UAAA,GAAA,GAAA,CAAA,CAAA,IAAA,EAAA,SAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,MACpD,gBAAA,kBAA8B,CAAA;AAAwC,MACtE,gFAAmC;AAAA,KACnC,CAAA,CAAA,CAAA;AAEF,IAAM,MAAA,MAAA,GAAAA,YAAA,CAAkB;AAA+B,MACrD,IAAA,GAAA,CAAA;AAEsB,MACtB;AAAmC,QAEjC,GAAA,GAAA,eAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,OACF,MAAA;AAEF,QAAM,GAAA,GAAA,gBAAwB,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,gBAAA,CAAA,OAAA,CAAA;AAC5B,OAAI;AACJ,MAAA,UAAU,CAAO;AACf,KAAM,CAAA,CAAA;AAAgC,IAAA,MACjC,UAAA,GAAAA,YAAA,CAAA,MAAA;AACL,MAAA,IAAA,KAAuB,CAAA,MAAA,KAAA,SAAA,EAAY;AAAsB,QAC3D,OAAAE,sBAAA,CAAA;AACA,OAAO;AAAA,MACR,IAAA,KAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AAED,QAAM,OAAA,KAAA,CAAA,WAAsB,SAAM,GAAAC,oBAAA,GAAAC,oBAAA,CAAA;AAChC,OAAI,MAAA;AACF,QAAO,OAAA,KAAA,CAAA,MAAA,KAAA,SAAA,GAAAC,cAAA,GAAAC,cAAA,CAAA;AAAA,OACT;AACA,KAAI,CAAA,CAAA;AACF,IAAO,MAAA,gBAAiB,GAAAN,YAAA,CAAA,MAAY;AAAc,MACpD,OAAO,KAAA,CAAA,IAAA,KAAA,MAAA,GAAA,EAAA,GAAA,KAAA,CAAA,WAAA,GAAA,GAAA,GAAA,KAAA,CAAA,KAAA,GAAA,QAAA,GAAA,CAAA,CAAA;AACL,KAAO,CAAA,CAAA;AAAqC,IAC9C,MAAA,OAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,IACF,SAAC,SAAA,CAAA,KAAA,EAAA;AAED,MAAM,MAAA,IAAA,GAAA,GAAA,GAAA,aAA4B;AAChC,MAAO,MAAA,oBACH,CAAA,GAAA,CAAA,CAAA,WAAyB,EAAA,KAAA,KAAA;AACA,QAC9B,IAAAO,eAAA,CAAA,WAAA,CAAA,EAAA;AAED,UAAM;AAEN,YAAA,kBAA2C;AACzC,YAAM,aAAa,KAAM,GAAA,CAAA,IAAA,IAAA;AACzB,WAAA,CAAM;AACJ,SAAI;AACF,QAAO,OAAA,WAAA,CAAA;AAAA,OAAA,CAAA,CAAA;AACE,MACP,OAAA,YAAa,QAAQ,EAAK,CAAA,KAAA,CAAA,CAAA,UAAA,GAAA,CAAA,CAAA,UAAA,CAAA,CAAA;AAAA,KAC5B;AAAA,IACF,MAAA,eAAA,GAAA,CAAA,UAAA,KAAA;AACA,MAAO,IAAA,EAAA,CAAA;AAAA,MACT,MAAC,EAAA,KAAA,EAAA,GAAA,KAAA,CAAA;AACD,MAAO,IAAAC,iBAAA,CAAA,KAAa;AAA0C,QAChE,OAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAEA,OAAM,MAAA,IAAAD,eAAA,CAAA,KAA0C,CAAA,EAAA;AAC9C,QAAM,YAAQ,CAAI;AAClB,OAAI,MAAA;AACF,QAAA,MAAA,SAAuB,SAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACzB,KAAA,MAAoB,MAAA,IAAA,MAAQ,EAAA;AAC1B,UAAO,IAAA,MAAA,CAAA,UAAA,GAAA,UAAA;AAAA,YACF,OAAA,MAAA,CAAA,KAAA,CAAA;AACL,SAAM;AACN,QAAA,OAAA,CAAA,EAAA,gBAA4B,CAAA,MAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAC1B,OAAA;AAAgD,KAClD,CAAA;AACA,IAAA,OAAA,CAAA,IAAc,EAAA,MAAA,KAAO;AAAa,MACpC,OAAAE,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACF,KAAA,EAAAC,kBAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}