#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建符合比赛要求的4个精简版压缩包
移除所有测试、调试、重复文件，只保留核心内容
"""

import os
import shutil
import zipfile
from pathlib import Path

def should_skip_file(file_path):
    """判断文件是否应该跳过（测试、调试、重复文件）"""
    skip_patterns = [
        # 测试和调试文件
        'test-', 'test_', 'debug-', 'debug_', 'fix-', 'fix_',
        'validation-', 'verify-', 'check-', 'diagnostic-',
        'emergency-', 'quick-', 'simple-', 'final-',
        'enhanced-', 'optimized-', 'comprehensive-',
        
        # 重复报告文件
        '_REPORT.md', '_FIX_REPORT.md', '_OPTIMIZATION_REPORT.md',
        '_SUMMARY.md', '_GUIDE.md', '_STATUS.md',
        
        # 临时和缓存文件
        '__pycache__', 'node_modules', '.git', 'dist/', 'build/',
        'logs/', '.log', '.cache', 'venv/', '.bak',
        
        # 开发工具文件
        '.html.bak', '.js.bak', '.py.bak',
        'backup-', 'temp-', 'tmp-'
    ]
    
    file_lower = file_path.lower()
    return any(pattern.lower() in file_lower for pattern in skip_patterns)

def copy_clean_files(source_patterns, dest_dir, description):
    """复制干净的文件（跳过测试调试文件）"""
    print(f"📋 {description}...")
    
    for pattern in source_patterns:
        if os.path.isfile(pattern):
            # 单个文件
            if not should_skip_file(pattern):
                dest_path = os.path.join(dest_dir, pattern)
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                shutil.copy2(pattern, dest_path)
                print(f"✅ {pattern}")
        elif os.path.isdir(pattern):
            # 目录
            for root, dirs, files in os.walk(pattern):
                # 过滤目录
                dirs[:] = [d for d in dirs if not should_skip_file(os.path.join(root, d))]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    if not should_skip_file(file_path):
                        rel_path = os.path.relpath(file_path, '.')
                        dest_path = os.path.join(dest_dir, rel_path)
                        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                        shutil.copy2(file_path, dest_path)

def create_clean_work_package():
    """创建精简版作品安装包"""
    print("🎯 创建精简版作品安装包...")
    
    work_dir = "clean_86014454作品"
    if os.path.exists(work_dir):
        shutil.rmtree(work_dir)
    os.makedirs(work_dir)
    
    # 核心启动文件
    work_files = [
        "start_system.py",
        "quick_system_check.py", 
        "启动服务器.bat",
        "完整启动指南.md",
        "比赛评审运行指南.md",
        "README.md"
    ]
    
    copy_clean_files(work_files, work_dir, "复制启动文件")
    
    # 后端运行文件
    backend_files = [
        "backend/run_server.py",
        "backend/simple_server.py",
        "backend/requirements.txt", 
        "backend/package.json",
        "backend/interview_system.db",
        "backend/app"
    ]
    
    copy_clean_files(backend_files, work_dir, "复制后端运行文件")
    
    # 前端构建文件（如果存在）
    if os.path.exists("frontend/dist"):
        copy_clean_files(["frontend/dist"], work_dir, "复制前端构建文件")
    
    return work_dir

def create_clean_source_package():
    """创建精简版源码包"""
    print("🎯 创建精简版源码包...")
    
    source_dir = "clean_86014454源码"
    if os.path.exists(source_dir):
        shutil.rmtree(source_dir)
    os.makedirs(source_dir)
    
    # 前端源码
    frontend_sources = [
        "frontend/src",
        "frontend/public",
        "frontend/package.json",
        "frontend/vite.config.js",
        "frontend/vue.config.js", 
        "frontend/index.html"
    ]
    
    copy_clean_files(frontend_sources, source_dir, "复制前端源码")
    
    # 后端源码
    backend_sources = [
        "backend/app",
        "backend/requirements.txt",
        "backend/package.json"
    ]
    
    copy_clean_files(backend_sources, source_dir, "复制后端源码")
    
    # 配置文件
    config_files = [
        "README.md",
        "FINAL_PROJECT_STATUS.md"
    ]
    
    copy_clean_files(config_files, source_dir, "复制配置文件")
    
    return source_dir

def create_clean_intro_package():
    """创建精简版介绍文档包"""
    print("🎯 创建精简版介绍文档包...")
    
    intro_dir = "clean_86014454介绍"
    if os.path.exists(intro_dir):
        shutil.rmtree(intro_dir)
    os.makedirs(intro_dir)
    
    # 重要文档
    doc_files = [
        "FINAL_PROJECT_STATUS.md",
        "PROJECT_COMPLETION_REPORT.md",
        "README.md"
    ]
    
    copy_clean_files(doc_files, intro_dir, "复制项目文档")
    
    # docs目录中的重要文档
    docs_files = [
        "docs/presentation.md",
        "docs/technical-documentation.md",
        "docs/system-design-overview.md"
    ]
    
    copy_clean_files(docs_files, intro_dir, "复制技术文档")
    
    # 演示文件
    demo_files = [
        "frontend/multimodal-showcase-demo.html",
        "complete-validation-test.html"
    ]
    
    copy_clean_files(demo_files, intro_dir, "复制演示文件")
    
    # 创建PPT说明
    ppt_note = """# 介绍材料说明

## 📋 当前包含内容
- 项目状态报告
- 技术文档
- 系统设计概述
- 功能演示页面

## ⚠️ 需要手动添加
请在此文件夹中添加：
1. 项目介绍PPT（PowerPoint格式）
2. 演示视频（MP4格式，建议5-10分钟）
3. 系统截图和界面展示

## 🎥 演示视频建议内容
1. 系统启动演示（1分钟）
2. AI面试对话展示（3-4分钟）
3. 多领域功能演示（2分钟）
4. 管理功能展示（1-2分钟）
5. 技术特色说明（1分钟）
"""
    
    with open(os.path.join(intro_dir, "需要添加的材料说明.md"), "w", encoding="utf-8") as f:
        f.write(ppt_note)
    
    return intro_dir

def create_clean_registration_package():
    """创建精简版报名材料包"""
    print("🎯 创建精简版报名材料包...")
    
    reg_dir = "clean_86014454报名"
    if os.path.exists(reg_dir):
        shutil.rmtree(reg_dir)
    os.makedirs(reg_dir)
    
    # 创建报名说明
    reg_content = """# 软件杯比赛报名材料

## 📋 参赛信息
- **参赛编号**: 86014454
- **作品名称**: 基于iFlytek Spark的多模态AI面试评估系统
- **参赛类别**: 软件应用与开发

## 🎯 作品简介
本系统是一个基于iFlytek Spark大语言模型的智能面试评估平台，具有以下特色：

### 核心功能
- 多技术领域面试支持（AI、大数据、物联网）
- 实时智能对话面试体验
- 多模态评估与反馈
- 企业级批量面试管理

### 技术特色
- **前端**: Vue.js 3 + Element Plus
- **后端**: Python Flask + SQLite
- **AI引擎**: iFlytek Spark LLM
- **界面**: 中文本土化设计

### 创新亮点
1. 基于iFlytek Spark的自然语言对话
2. 多技术领域的专业问题库
3. 实时评估算法
4. 个性化学习路径推荐
5. 企业级管理功能

## ⚠️ 需要手动添加的材料
请在此文件夹中添加以下文件：

### 必需材料
1. **正式报名表** (PDF格式)
   - 按比赛要求填写完整
   - 确保信息准确无误

2. **学生证扫描件** (JPG/PNG格式)
   - 清晰可见的学生证正反面
   - 确保个人信息清楚

3. **其他证明材料** (如比赛要求)
   - 学校推荐信
   - 身份证明等

### 文件命名建议
- 报名表: 86014454_报名表.pdf
- 学生证: 86014454_学生证.jpg
- 其他材料: 86014454_[材料名称].[格式]

## 📞 联系信息
如有疑问，请参考比赛官方要求或联系相关负责人。

---
**请确保所有材料真实有效，符合比赛要求。**
"""
    
    with open(os.path.join(reg_dir, "报名材料说明.md"), "w", encoding="utf-8") as f:
        f.write(reg_content)
    
    return reg_dir

def create_zip_packages():
    """创建所有精简版压缩包"""
    print("📦 开始创建精简版压缩包...")
    
    # 创建各个包
    work_dir = create_clean_work_package()
    source_dir = create_clean_source_package() 
    intro_dir = create_clean_intro_package()
    reg_dir = create_clean_registration_package()
    
    # 压缩包配置
    packages = [
        (work_dir, "86014454作品_精简版.zip"),
        (source_dir, "86014454源码_精简版.zip"),
        (intro_dir, "86014454介绍_精简版.zip"),
        (reg_dir, "86014454报名_精简版.zip")
    ]
    
    print("📦 创建压缩文件...")
    for folder, zip_name in packages:
        if os.path.exists(folder):
            with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(folder):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, folder)
                        zipf.write(file_path, arcname)
            print(f"✅ 创建 {zip_name}")
            
            # 清理临时目录
            shutil.rmtree(folder)
    
    print("🎉 所有精简版压缩包创建完成！")

def show_final_summary():
    """显示最终总结"""
    print("\n📋 精简版压缩包说明:")
    print("1. 86014454作品_精简版.zip - 可执行文件和安装包（已清理）")
    print("2. 86014454源码_精简版.zip - 完整源代码（已清理）")
    print("3. 86014454介绍_精简版.zip - PPT、演示视频和文档（需添加PPT和视频）")
    print("4. 86014454报名_精简版.zip - 报名表和学生证（需手动添加）")
    
    print("\n🗑️ 已清理内容:")
    print("- 所有test-、debug-、fix-开头的测试文件")
    print("- 所有重复的报告和日志文件")
    print("- node_modules、__pycache__等缓存目录")
    print("- 数百个验证和调试脚本")
    
    print("\n✅ 保留内容:")
    print("- 核心启动脚本和配置文件")
    print("- 前后端完整源码")
    print("- 重要技术文档")
    print("- 数据库文件")
    print("- 演示页面")
    
    print("\n🎯 推荐使用:")
    print("- 这4个精简版压缩包替代原来的版本")
    print("- 文件更干净，结构更清晰")
    print("- 符合比赛提交要求")

def main():
    """主函数"""
    print("🚀 开始创建符合比赛要求的精简版压缩包...")
    
    create_zip_packages()
    show_final_summary()

if __name__ == "__main__":
    main()
