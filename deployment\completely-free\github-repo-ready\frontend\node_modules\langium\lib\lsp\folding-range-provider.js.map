{"version": 3, "file": "folding-range-provider.js", "sourceRoot": "", "sources": ["../../src/lsp/folding-range-provider.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAQhF,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC1D,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAsBnD,MAAM,OAAO,2BAA2B;IAIpC,YAAY,QAAyB;QACjC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,qBAAqB,CAAC;IAC5E,CAAC;IAED,gBAAgB,CAAC,QAAyB,EAAE,OAA2B,EAAE,YAAgC;QACrG,MAAM,QAAQ,GAAmB,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAyB,CAAC,YAAY,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACxC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAES,cAAc,CAAC,QAAyB,EAAE,QAA8B;;QAC9E,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,WAAW,0CAAE,KAAK,CAAC;QACzC,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACxD,IAAI,MAA+B,CAAC;gBACpC,GAAG,CAAC;oBACA,MAAM,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC;oBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;wBACf,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;wBAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;4BAC3B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;wBACxD,CAAC;wBACD,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;4BACnC,YAAY,CAAC,KAAK,EAAE,CAAC;wBACzB,CAAC;oBACL,CAAC;gBACL,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE;YAC3B,CAAC;YAED,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,aAAa,CAAC,KAAc;QAClC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACO,oBAAoB,CAAC,KAAc;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,oBAAoB,CAAC,QAAyB,EAAE,IAAa,EAAE,QAA8B;QACnG,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC5D,IAAI,YAAY,EAAE,CAAC;gBACf,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC3B,CAAC;QACL,CAAC;IACL,CAAC;IAES,qBAAqB,CAAC,QAAyB,EAAE,IAAa,EAAE,QAA8B;QACpG,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,IAAI,OAAO,EAAE,CAAC;YACV,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClD,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;oBACnF,IAAI,YAAY,EAAE,CAAC;wBACf,QAAQ,CAAC,YAAY,CAAC,CAAC;oBAC3B,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAES,cAAc,CAAC,QAAyB,EAAE,IAAa,EAAE,IAAa;QAC5E,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1B,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QACpB,+DAA+D;QAC/D,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,oEAAoE;QACpE,oEAAoE;QACpE,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC3C,GAAG,GAAG,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACjH,CAAC;QACD,OAAO,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG;IACO,sBAAsB,CAAC,IAAa,EAAE,IAAa;QACzD,IAAI,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CAEJ"}