{"version": 3, "file": "json-serializer.d.ts", "sourceRoot": "", "sources": ["../../src/serializer/json-serializer.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACjC,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,sCAAsC,CAAC;AAC5E,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AACnE,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,KAAK,EAAE,OAAO,EAAW,cAAc,EAAW,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAI9F,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAEpG,MAAM,WAAW,oBAAoB;IACjC,wGAAwG;IACxG,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB,6GAA6G;IAC7G,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,yHAAyH;IACzH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,mJAAmJ;IACnJ,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,iHAAiH;IACjH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,uIAAuI;IACvI,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,KAAK,OAAO,KAAK,OAAO,CAAA;IAC9G,0GAA0G;IAC1G,YAAY,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,KAAK,MAAM,CAAA;CAC5D;AAED,MAAM,WAAW,sBAAsB;IACnC,sGAAsG;IACtG,YAAY,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,GAAG,CAAA;CACtC;AAED;;GAEG;AACH,MAAM,WAAW,qBAAsB,SAAQ,OAAO;IAClD,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW,CAAC,EAAE,4BAA4B,CAAC;CAC9C;AAED;;GAEG;AACH,MAAM,WAAW,kBAAmB,SAAQ,OAAO;IAC/C,QAAQ,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,wBAAgB,oBAAoB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,IAAI,kBAAkB,CAE9E;AAED;;;;GAIG;AACH,MAAM,WAAW,4BAA6B,SAAQ,eAAe;IACjE;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;IAChD;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC3B;;;;OAIG;IACH,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,oBAAoB,GAAG,MAAM,CAAC;IACjE;;OAEG;IACH,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,sBAAsB,GAAG,CAAC,CAAC;CAClG;AAED;;GAEG;AACH,UAAU,qBAAqB;IAC3B,kJAAkJ;IAClJ,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,qFAAqF;IACrF,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,+FAA+F;IAC/F,MAAM,CAAC,EAAE,MAAM,CAAA;CAClB;AAMD,qBAAa,qBAAsB,YAAW,cAAc;IAExD,qEAAqE;IACrE,gBAAgB,cAA6F;IAE7G,0GAA0G;IAC1G,SAAS,CAAC,eAAe,EAAE,eAAe,GAAG,SAAS,CAAC;IAEvD,SAAS,CAAC,QAAQ,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACtD,SAAS,CAAC,QAAQ,CAAC,cAAc,EAAE,cAAc,CAAC;IAClD,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;gBAExC,QAAQ,EAAE,mBAAmB;IAOzC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,oBAAoB,GAAG,MAAM;IAchE,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,sBAAsB,GAAG,CAAC;IAO9F,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,oBAAoB,GAAG,OAAO;IAqD5I,SAAS,CAAC,iCAAiC,CAAC,IAAI,EAAE,qBAAqB;IAwBvE,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,sBAAsB,EAAE,SAAS,CAAC,EAAE,OAAO,EAAE,iBAAiB,CAAC,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,MAAM;IAuBjK,SAAS,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,EAAE,OAAO,EAAE,sBAAsB,GAAG,SAAS,GAAG,SAAS;IAiCxK,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,GAAG,GAAG,OAAO,GAAG,MAAM;CAoC1G"}