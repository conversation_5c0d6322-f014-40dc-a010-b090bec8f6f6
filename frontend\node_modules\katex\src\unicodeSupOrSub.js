// Helpers for Parser.js handling of Unicode (sub|super)script characters.

export const unicodeSubRegEx = /^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/;

export const uSubsAndSups = Object.freeze({
    '₊': '+',
    '₋': '-',
    '₌': '=',
    '₍': '(',
    '₎': ')',
    '₀': '0',
    '₁': '1',
    '₂': '2',
    '₃': '3',
    '₄': '4',
    '₅': '5',
    '₆': '6',
    '₇': '7',
    '₈': '8',
    '₉': '9',
    '\u2090': 'a',
    '\u2091': 'e',
    '\u2095': 'h',
    '\u1D62': 'i',
    '\u2C7C': 'j',
    '\u2096': 'k',
    '\u2097': 'l',
    '\u2098': 'm',
    '\u2099': 'n',
    '\u2092': 'o',
    '\u209A': 'p',
    '\u1D63': 'r',
    '\u209B': 's',
    '\u209C': 't',
    '\u1D64': 'u',
    '\u1D65': 'v',
    '\u2093': 'x',
    '\u1D66': 'β',
    '\u1D67': 'γ',
    '\u1D68': 'ρ',
    '\u1D69': '\u03d5',
    '\u1D6A': 'χ',
    '⁺': '+',
    '⁻': '-',
    '⁼': '=',
    '⁽': '(',
    '⁾': ')',
    '⁰': '0',
    '¹': '1',
    '²': '2',
    '³': '3',
    '⁴': '4',
    '⁵': '5',
    '⁶': '6',
    '⁷': '7',
    '⁸': '8',
    '⁹': '9',
    '\u1D2C': 'A',
    '\u1D2E': 'B',
    '\u1D30': 'D',
    '\u1D31': 'E',
    '\u1D33': 'G',
    '\u1D34': 'H',
    '\u1D35': 'I',
    '\u1D36': 'J',
    '\u1D37': 'K',
    '\u1D38': 'L',
    '\u1D39': 'M',
    '\u1D3A': 'N',
    '\u1D3C': 'O',
    '\u1D3E': 'P',
    '\u1D3F': 'R',
    '\u1D40': 'T',
    '\u1D41': 'U',
    '\u2C7D': 'V',
    '\u1D42': 'W',
    '\u1D43': 'a',
    '\u1D47': 'b',
    '\u1D9C': 'c',
    '\u1D48': 'd',
    '\u1D49': 'e',
    '\u1DA0': 'f',
    '\u1D4D': 'g',
    '\u02B0': 'h',
    '\u2071': 'i',
    '\u02B2': 'j',
    '\u1D4F': 'k',
    '\u02E1': 'l',
    '\u1D50': 'm',
    '\u207F': 'n',
    '\u1D52': 'o',
    '\u1D56': 'p',
    '\u02B3': 'r',
    '\u02E2': 's',
    '\u1D57': 't',
    '\u1D58': 'u',
    '\u1D5B': 'v',
    '\u02B7': 'w',
    '\u02E3': 'x',
    '\u02B8': 'y',
    '\u1DBB': 'z',
    '\u1D5D': 'β',
    '\u1D5E': 'γ',
    '\u1D5F': 'δ',
    '\u1D60': '\u03d5',
    '\u1D61': 'χ',
    '\u1DBF': 'θ',
});
