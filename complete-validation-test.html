<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紫色文字问题完整验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4c51bf;
            background: #f8f9fa;
        }
        .section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(45deg, #4c51bf, #6b21a8);
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-list li {
            counter-increment: step-counter;
            margin: 10px 0;
            padding-left: 30px;
            position: relative;
        }
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: #4c51bf;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 紫色背景文字问题完整验证</h1>
            <p>Vue.js + Element Plus 多模态智能面试系统 - WCAG 2.1 AA 对比度标准验证</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h3>📋 验证步骤</h3>
                <ol class="step-list">
                    <li>确保Vue应用正在运行 (http://localhost:5173)</li>
                    <li>点击"打开Vue应用"在新窗口中打开应用</li>
                    <li>在应用页面按F12打开开发者工具</li>
                    <li>点击"复制验证脚本"并在控制台中粘贴运行</li>
                    <li>查看验证结果，确认所有紫色背景文字都是白色</li>
                </ol>
            </div>
            
            <div class="section">
                <h3>🚀 快速操作</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="openVueApp()">
                        🌐 打开Vue应用
                    </button>
                    <button class="btn btn-success" onclick="copyValidationScript()">
                        📋 复制验证脚本
                    </button>
                    <button class="btn btn-secondary" onclick="showScript()">
                        👁️ 查看脚本内容
                    </button>
                    <button class="btn btn-secondary" onclick="checkAppStatus()">
                        ⚡ 检查应用状态
                    </button>
                </div>
                <div id="status" class="status-box"></div>
            </div>
            
            <div class="section">
                <h3>🎯 预期结果</h3>
                <p><strong>✅ 成功标准：</strong></p>
                <ul>
                    <li>所有紫色背景元素的文字都显示为白色 (#ffffff)</li>
                    <li>文字具有适当的阴影效果以增强可读性</li>
                    <li>对比度比例达到 WCAG 2.1 AA 标准 (≥4.5:1)</li>
                    <li>验证脚本报告"问题元素: 0"</li>
                </ul>
            </div>
            
            <div class="section">
                <h3>🔧 如果仍有问题</h3>
                <p>如果验证脚本仍然发现问题元素，请尝试：</p>
                <ul>
                    <li>按 Ctrl+Shift+R 进行硬刷新</li>
                    <li>清除浏览器缓存</li>
                    <li>重启Vite开发服务器</li>
                    <li>检查浏览器开发者工具中的样式是否正确应用</li>
                </ul>
            </div>
            
            <div id="script-content" style="display: none;">
                <div class="section">
                    <h3>📜 验证脚本内容</h3>
                    <div class="code-block" id="script-code"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 验证脚本内容
        const validationScript = \`// 最终紫色背景文字验证脚本
console.log('🔍 开始最终验证紫色背景文字问题...');

// 等待页面完全加载
setTimeout(() => {
    console.log('📊 正在分析页面中的所有元素...');
    
    // 定义紫色相关的颜色值
    const purpleColors = [
        'rgb(76, 81, 191)',    // #4c51bf
        'rgb(107, 33, 168)',   // #6b21a8
        '#4c51bf',
        '#6b21a8'
    ];
    
    // 检查函数
    function checkElement(element, selector = '') {
        const style = window.getComputedStyle(element);
        const bgColor = style.backgroundColor;
        const bgImage = style.backgroundImage;
        const textColor = style.color;
        
        // 检查是否有紫色背景
        const hasPurpleBackground = 
            purpleColors.some(color => bgColor.includes(color)) ||
            purpleColors.some(color => bgImage.includes(color)) ||
            bgImage.includes('gradient');
            
        if (hasPurpleBackground) {
            const textContent = element.textContent?.trim();
            if (textContent && textContent.length > 0) {
                return {
                    element,
                    selector,
                    tagName: element.tagName,
                    className: element.className,
                    textContent: textContent.substring(0, 30),
                    backgroundColor: bgColor,
                    backgroundImage: bgImage,
                    textColor: textColor,
                    isWhiteText: textColor.includes('rgb(255, 255, 255)') || 
                               textColor.includes('#ffffff') ||
                               textColor.includes('white'),
                    hasTextShadow: style.textShadow !== 'none'
                };
            }
        }
        return null;
    }
    
    // 检查所有相关选择器
    const selectorsToCheck = [
        '.advantage-icon',
        '.feature-icon', 
        '.hero-background',
        '.hero-content',
        '.feature-tag',
        '.connection-line',
        '[style*="#6b21a8"]',
        '[style*="#4c51bf"]',
        '[style*="gradient"]'
    ];
    
    const results = [];
    const problemElements = [];
    
    selectorsToCheck.forEach(selector => {
        try {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                // 检查元素本身
                const result = checkElement(el, selector);
                if (result) {
                    results.push(result);
                    if (!result.isWhiteText) {
                        problemElements.push(result);
                    }
                }
                
                // 检查所有子元素
                const children = el.querySelectorAll('*');
                children.forEach(child => {
                    const childResult = checkElement(child, \\\`\\\${selector} > \\\${child.tagName}\\\`);
                    if (childResult) {
                        results.push(childResult);
                        if (!childResult.isWhiteText) {
                            problemElements.push(childResult);
                        }
                    }
                });
            });
        } catch (e) {
            console.warn(\\\`检查选择器 \\\${selector} 时出错:\\\`, e);
        }
    });
    
    // 输出结果
    console.log(\\\`\\\\n📊 验证结果:\\\`);
    console.log(\\\`   总检查元素: \\\${results.length}\\\`);
    console.log(\\\`   问题元素: \\\${problemElements.length}\\\`);
    console.log(\\\`   正常元素: \\\${results.length - problemElements.length}\\\`);
    
    if (problemElements.length === 0) {
        console.log('\\\\n✅ 恭喜！所有紫色背景元素的文字颜色都已正确设置为白色！');
        console.log('🎉 WCAG 2.1 AA 对比度标准已达成！');
        
        // 显示正常元素的统计
        const normalElements = results.filter(r => r.isWhiteText);
        console.log(\\\`\\\\n📈 正常元素统计:\\\`);
        const groupedNormal = {};
        normalElements.forEach(item => {
            if (!groupedNormal[item.selector]) {
                groupedNormal[item.selector] = 0;
            }
            groupedNormal[item.selector]++;
        });
        
        Object.keys(groupedNormal).forEach(selector => {
            console.log(\\\`   \\\${selector}: \\\${groupedNormal[selector]} 个元素 ✅\\\`);
        });
        
    } else {
        console.log(\\\`\\\\n❌ 发现 \\\${problemElements.length} 个仍需修复的元素:\\\`);
        
        problemElements.forEach((item, index) => {
            console.log(\\\`\\\\n\\\${index + 1}. \\\${item.tagName}.\\\${item.className}\\\`);
            console.log(\\\`   选择器: \\\${item.selector}\\\`);
            console.log(\\\`   文本: "\\\${item.textContent}"\\\`);
            console.log(\\\`   背景: \\\${item.backgroundColor}\\\`);
            console.log(\\\`   文字色: \\\${item.textColor}\\\`);
            console.log(\\\`   文字阴影: \\\${item.hasTextShadow ? '有' : '无'}\\\`);
        });
        
        // 生成修复CSS
        console.log('\\\\n🔧 建议的修复CSS:');
        const uniqueSelectors = [...new Set(problemElements.map(p => p.selector))];
        uniqueSelectors.forEach(selector => {
            console.log(\\\`\\\${selector} * {\\\`);
            console.log(\\\`  color: #ffffff !important;\\\`);
            console.log(\\\`  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6) !important;\\\`);
            console.log(\\\`}\\\`);
        });
    }
    
    console.log('\\\\n✅ 验证完成！');
    
}, 3000); // 等待3秒确保页面完全加载\`;

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = message;
            status.className = \`status-box status-\${type}\`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        function openVueApp() {
            showStatus('🚀 正在新窗口中打开Vue应用...', 'info');
            const timestamp = new Date().getTime();
            window.open(\`http://localhost:5173/?t=\${timestamp}\`, '_blank');
            
            setTimeout(() => {
                showStatus('✅ Vue应用已打开！请在应用页面按F12打开开发者工具，然后运行验证脚本。', 'success');
            }, 1000);
        }

        function copyValidationScript() {
            navigator.clipboard.writeText(validationScript).then(() => {
                showStatus('✅ 验证脚本已复制到剪贴板！<br>请在Vue应用页面的控制台中粘贴并按回车运行。', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                showStatus('❌ 复制失败，请手动复制脚本内容。', 'error');
            });
        }

        function showScript() {
            const scriptContent = document.getElementById('script-content');
            const scriptCode = document.getElementById('script-code');
            
            if (scriptContent.style.display === 'none') {
                scriptCode.textContent = validationScript;
                scriptContent.style.display = 'block';
                showStatus('📜 脚本内容已显示在下方', 'info');
            } else {
                scriptContent.style.display = 'none';
                showStatus('📜 脚本内容已隐藏', 'info');
            }
        }

        function checkAppStatus() {
            showStatus('⚡ 正在检查Vue应用状态...', 'info');
            
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        showStatus('✅ Vue应用运行正常！可以开始验证。', 'success');
                    } else {
                        showStatus('⚠️ Vue应用响应异常，请检查开发服务器。', 'error');
                    }
                })
                .catch(error => {
                    showStatus('❌ 无法连接到Vue应用！<br>请确保开发服务器正在运行：<code>npm run dev</code>', 'error');
                });
        }

        // 页面加载时自动检查应用状态
        window.onload = () => {
            setTimeout(checkAppStatus, 1000);
        };
    </script>
</body>
</html>
