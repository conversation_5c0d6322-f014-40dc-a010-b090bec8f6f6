{"version": 3, "file": "tokens.js", "sourceRoot": "", "sources": ["../../../src/scan/tokens.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,KAAK,EACL,OAAO,EACP,UAAU,EACV,OAAO,EACP,OAAO,EACP,GAAG,EACH,QAAQ,EACR,OAAO,EACP,OAAO,EACP,GAAG,GACJ,MAAM,WAAW,CAAC;AAGnB,MAAM,UAAU,sBAAsB,CACpC,WAAmB,EACnB,cAAyB;IAEzB,MAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IAC9C,IAAI,YAAY,KAAK,cAAc,CAAC,YAAY,EAAE;QAChD,OAAO,IAAI,CAAC;KACb;SAAM;QACL,OAAO,CACL,cAAc,CAAC,QAAQ,KAAK,IAAI;YAChC,cAAc,CAAC,kBAAmB,CAAC,YAAY,CAAC,KAAK,IAAI,CAC1D,CAAC;KACH;AACH,CAAC;AAED,2EAA2E;AAC3E,gGAAgG;AAChG,MAAM,UAAU,kCAAkC,CAChD,KAAa,EACb,OAAkB;IAElB,OAAO,KAAK,CAAC,YAAY,KAAK,OAAO,CAAC,YAAY,CAAC;AACrD,CAAC;AAED,MAAM,CAAC,IAAI,iBAAiB,GAAG,CAAC,CAAC;AACjC,MAAM,CAAC,MAAM,eAAe,GAAsC,EAAE,CAAC;AAErE,MAAM,UAAU,iBAAiB,CAAC,UAAuB;IACvD,0CAA0C;IAC1C,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAE1D,wDAAwD;IACxD,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;IAE9C,8BAA8B;IAC9B,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;IAC9C,0BAA0B,CAAC,oBAAoB,CAAC,CAAC;IAEjD,OAAO,CAAC,oBAAoB,EAAE,CAAC,OAAO,EAAE,EAAE;QACxC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,eAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,UAAuB;IACtD,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;IAE/B,IAAI,UAAU,GAAG,UAAU,CAAC;IAC5B,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,OAAO,SAAS,EAAE;QAChB,UAAU,GAAG,OAAO,CAClB,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAClE,CAAC;QAEF,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAErD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEtC,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE;YAC1B,SAAS,GAAG,KAAK,CAAC;SACnB;aAAM;YACL,UAAU,GAAG,aAAa,CAAC;SAC5B;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,UAAuB;IAC7D,OAAO,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,EAAE;QAClC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE;YACrC,eAAe,CAAC,iBAAiB,CAAC,GAAG,WAAW,CAAC;YAC3C,WAAY,CAAC,YAAY,GAAG,iBAAiB,EAAE,CAAC;SACvD;QAED,wCAAwC;QACxC,IACE,qBAAqB,CAAC,WAAW,CAAC;YAClC,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC;QAChC,KAAK;QACL,+CAA+C;UAC/C;YACA,WAAW,CAAC,UAAU,GAAG,CAAC,WAAW,CAAC,UAAkC,CAAC,CAAC;SAC3E;QAED,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;YACvC,WAAW,CAAC,UAAU,GAAG,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,EAAE;YACjD,WAAW,CAAC,eAAe,GAAG,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,kCAAkC,CAAC,WAAW,CAAC,EAAE;YACpD,WAAW,CAAC,kBAAkB,GAAG,EAAE,CAAC;SACrC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,0BAA0B,CAAC,UAAuB;IAChE,OAAO,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,EAAE;QAClC,qBAAqB;QACrB,WAAW,CAAC,eAAe,GAAG,EAAE,CAAC;QACjC,OAAO,CAAC,WAAW,CAAC,kBAAmB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACpD,WAAW,CAAC,eAAgB,CAAC,IAAI,CAC/B,eAAe,CAAC,GAAwB,CAAC,CAAC,YAAa,CACxD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,UAAuB;IAC7D,OAAO,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,EAAE;QAClC,6BAA6B,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,6BAA6B,CAC3C,IAAiB,EACjB,QAAmB;IAEnB,OAAO,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;QACzB,QAAQ,CAAC,kBAAmB,CAAC,QAAQ,CAAC,YAAa,CAAC,GAAG,IAAI,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,EAAE;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,kDAAkD;QAClD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE;YACpC,6BAA6B,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;SACtD;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,OAAkB;IACpD,OAAO,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,OAAkB;IACtD,OAAO,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,+BAA+B,CAAC,OAAkB;IAChE,OAAO,GAAG,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;AACzC,CAAC;AAED,MAAM,UAAU,kCAAkC,CAChD,OAAkB;IAElB,OAAO,GAAG,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,OAAkB;IAC5C,OAAO,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;AACtC,CAAC"}