{"version": 3, "file": "use-month-range-header.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-month-range-header.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport type { Ref, ToRef } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const useMonthRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate,\n}: {\n  unlinkPanels: ToRef<boolean>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n}) => {\n  const { t } = useLocale()\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(1, 'year')\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(1, 'year')\n    }\n  }\n\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(1, 'year')\n    }\n    rightDate.value = rightDate.value.add(1, 'year')\n  }\n\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(1, 'year')\n  }\n\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(1, 'year')\n  }\n  const leftLabel = computed(() => {\n    return `${leftDate.value.year()} ${t('el.datepicker.year')}`\n  })\n\n  const rightLabel = computed(() => {\n    return `${rightDate.value.year()} ${t('el.datepicker.year')}`\n  })\n\n  const leftYear = computed(() => {\n    return leftDate.value.year()\n  })\n\n  const rightYear = computed(() => {\n    return rightDate.value.year() === leftDate.value.year()\n      ? leftDate.value.year() + 1\n      : rightDate.value.year()\n  })\n\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear,\n  }\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,mBAAmB,GAAG,CAAC;AACpC,EAAE,YAAY;AACd,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC5B,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC7B,MAAM,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC5D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC7B,MAAM,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACrD,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1D,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,OAAO,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;AACjE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;AAClE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACjC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACjH,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,GAAG,CAAC;AACJ;;;;"}