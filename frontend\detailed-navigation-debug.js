// iFlytek 面试系统导航问题详细诊断脚本
// 在浏览器控制台中运行此脚本

console.log('🔍 开始iFlytek面试系统导航问题诊断...');

// 1. 检查Vue应用状态
function checkVueApplication() {
    console.log('\n📱 检查Vue应用状态:');
    
    const app = document.getElementById('app');
    if (!app) {
        console.error('❌ #app 容器不存在');
        return false;
    }
    console.log('✅ #app 容器存在');
    
    if (!app.__vue_app__) {
        console.error('❌ Vue应用实例未挂载');
        return false;
    }
    console.log('✅ Vue应用实例已挂载');
    
    // 检查Vue组件
    const vueComponents = app.querySelectorAll('[data-v-]');
    console.log(`📦 找到 ${vueComponents.length} 个Vue组件`);
    
    return true;
}

// 2. 检查路由系统
function checkRouterSystem() {
    console.log('\n🛣️ 检查路由系统:');
    
    console.log(`📍 当前路径: ${window.location.pathname}`);
    console.log(`🌐 完整URL: ${window.location.href}`);
    
    // 检查History API
    if (window.history && window.history.pushState) {
        console.log('✅ History API 支持正常');
    } else {
        console.error('❌ History API 不支持');
        return false;
    }
    
    // 尝试获取Vue Router实例
    const app = document.getElementById('app');
    if (app && app.__vue_app__) {
        try {
            const vueApp = app.__vue_app__;
            console.log('✅ 可以访问Vue应用实例');
            
            // 检查全局属性
            if (vueApp.config && vueApp.config.globalProperties) {
                console.log('✅ Vue全局属性可访问');
                
                // 尝试访问$router
                if (vueApp.config.globalProperties.$router) {
                    console.log('✅ $router 实例存在');
                } else {
                    console.warn('⚠️ $router 实例未找到');
                }
            }
        } catch (error) {
            console.error('❌ 访问Vue应用实例失败:', error);
        }
    }
    
    return true;
}

// 3. 检查Element Plus组件
function checkElementPlusComponents() {
    console.log('\n🎨 检查Element Plus组件:');
    
    // 检查Element Plus样式
    const elementStyles = Array.from(document.styleSheets).find(sheet => 
        sheet.href && sheet.href.includes('element-plus')
    );
    
    if (elementStyles) {
        console.log('✅ Element Plus 样式已加载');
    } else {
        console.warn('⚠️ Element Plus 样式未检测到');
    }
    
    // 检查Element Plus组件
    const elButtons = document.querySelectorAll('.el-button');
    console.log(`🔘 找到 ${elButtons.length} 个 Element Plus 按钮`);
    
    const elMenus = document.querySelectorAll('.el-menu');
    console.log(`📋 找到 ${elMenus.length} 个 Element Plus 菜单`);
    
    const elMenuItems = document.querySelectorAll('.el-menu-item');
    console.log(`📝 找到 ${elMenuItems.length} 个菜单项`);
    
    return elButtons.length > 0 || elMenus.length > 0;
}

// 4. 检查事件监听器
function checkEventListeners() {
    console.log('\n👂 检查事件监听器:');
    
    // 检查导航菜单项
    const menuItems = document.querySelectorAll('.el-menu-item');
    console.log(`📋 检查 ${menuItems.length} 个菜单项的事件监听器:`);
    
    menuItems.forEach((item, index) => {
        const text = item.textContent.trim();
        console.log(`  ${index + 1}. "${text}"`);
        
        // 检查点击事件
        const hasClickHandler = item.onclick !== null;
        if (hasClickHandler) {
            console.log(`    ✅ 有onclick处理器`);
        } else {
            console.log(`    ⚠️ 无onclick处理器`);
        }
        
        // 检查Vue事件监听器
        if (item.__vueParentComponent) {
            console.log(`    ✅ 有Vue组件绑定`);
        } else {
            console.log(`    ⚠️ 无Vue组件绑定`);
        }
    });
    
    // 检查主要按钮
    const primaryButtons = document.querySelectorAll('.primary-cta, .secondary-cta, .cta-button');
    console.log(`🔘 检查 ${primaryButtons.length} 个主要按钮的事件监听器:`);
    
    primaryButtons.forEach((button, index) => {
        const text = button.textContent.trim();
        console.log(`  ${index + 1}. "${text}"`);
        
        if (button.onclick !== null) {
            console.log(`    ✅ 有onclick处理器`);
        } else {
            console.log(`    ⚠️ 无onclick处理器`);
        }
    });
}

// 5. 检查CSS样式冲突
function checkCSSConflicts() {
    console.log('\n🎨 检查CSS样式冲突:');
    
    // 检查z-index冲突
    const elements = document.querySelectorAll('*');
    const highZIndexElements = [];
    
    elements.forEach(el => {
        const zIndex = window.getComputedStyle(el).zIndex;
        if (zIndex !== 'auto' && parseInt(zIndex) > 1000) {
            highZIndexElements.push({
                element: el,
                tagName: el.tagName,
                className: el.className,
                zIndex: zIndex
            });
        }
    });
    
    if (highZIndexElements.length > 0) {
        console.warn(`⚠️ 发现 ${highZIndexElements.length} 个高z-index元素:`);
        highZIndexElements.slice(0, 5).forEach(item => {
            console.log(`  📌 ${item.tagName}.${item.className.split(' ')[0]}: z-index ${item.zIndex}`);
        });
    } else {
        console.log('✅ 未发现明显的z-index冲突');
    }
    
    // 检查pointer-events
    const disabledElements = document.querySelectorAll('[style*="pointer-events: none"]');
    if (disabledElements.length > 0) {
        console.warn(`⚠️ 发现 ${disabledElements.length} 个禁用指针事件的元素`);
        disabledElements.forEach(el => {
            console.log(`  🚫 ${el.tagName}.${el.className}`);
        });
    } else {
        console.log('✅ 未发现禁用指针事件的元素');
    }
}

// 6. 测试导航功能
function testNavigationFunctionality() {
    console.log('\n🧪 测试导航功能:');
    
    // 测试History API
    const originalPath = window.location.pathname;
    try {
        window.history.pushState({}, '', '/test-navigation');
        console.log('✅ History.pushState 工作正常');
        
        window.history.pushState({}, '', originalPath);
        console.log('✅ 路径恢复成功');
    } catch (error) {
        console.error('❌ History API 测试失败:', error);
    }
    
    // 模拟点击测试
    const firstMenuItem = document.querySelector('.el-menu-item');
    if (firstMenuItem) {
        console.log('🖱️ 尝试模拟点击第一个菜单项...');
        try {
            // 创建点击事件
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            firstMenuItem.dispatchEvent(clickEvent);
            console.log('✅ 点击事件已触发');
        } catch (error) {
            console.error('❌ 模拟点击失败:', error);
        }
    }
}

// 7. 检查JavaScript错误
function checkJavaScriptErrors() {
    console.log('\n🐛 检查JavaScript错误:');
    
    // 设置错误监听器
    const errors = [];
    const originalError = console.error;
    
    console.error = function(...args) {
        errors.push({
            message: args.join(' '),
            timestamp: new Date().toISOString()
        });
        originalError.apply(console, args);
    };
    
    // 检查现有错误
    if (window.collectedErrors && window.collectedErrors.length > 0) {
        console.warn(`⚠️ 发现 ${window.collectedErrors.length} 个之前的错误:`);
        window.collectedErrors.slice(-3).forEach(error => {
            console.error(`  🚫 ${error.message}`);
        });
    } else {
        console.log('✅ 未发现JavaScript错误');
    }
}

// 主诊断函数
function runFullDiagnosis() {
    console.log('🚀 开始完整诊断...\n');
    
    const results = {
        vueApp: checkVueApplication(),
        router: checkRouterSystem(),
        elementPlus: checkElementPlusComponents(),
        events: checkEventListeners(),
        css: checkCSSConflicts(),
        navigation: testNavigationFunctionality(),
        errors: checkJavaScriptErrors()
    };
    
    console.log('\n📊 诊断结果汇总:');
    Object.entries(results).forEach(([key, value]) => {
        const status = value ? '✅' : '❌';
        console.log(`${status} ${key}: ${value ? '正常' : '异常'}`);
    });
    
    console.log('\n🔧 建议的修复步骤:');
    if (!results.vueApp) {
        console.log('1. 检查Vue应用是否正确挂载');
    }
    if (!results.router) {
        console.log('2. 检查Vue Router配置');
    }
    if (!results.elementPlus) {
        console.log('3. 检查Element Plus组件加载');
    }
    
    return results;
}

// 导出函数到全局作用域
window.iflytekDiagnosis = {
    runFullDiagnosis,
    checkVueApplication,
    checkRouterSystem,
    checkElementPlusComponents,
    checkEventListeners,
    checkCSSConflicts,
    testNavigationFunctionality,
    checkJavaScriptErrors
};

console.log('✅ 诊断脚本已加载');
console.log('💡 使用 iflytekDiagnosis.runFullDiagnosis() 运行完整诊断');
console.log('💡 或使用单独的函数进行特定检查');
