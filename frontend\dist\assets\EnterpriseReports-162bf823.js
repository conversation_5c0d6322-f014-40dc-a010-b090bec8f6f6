import{_ as Ve,S as Te,r as f,h as Ne,a2 as $e,o as Ue,Y as Ee,am as Pe,i as p,j as Be,k as Ge,n as l,p as e,w as a,ap as d,N as u,a7 as Fe,M as o,a9 as Q,E as Y,G as F,ah as Z,W as M,z as m,t as N,A as Me,aj as Oe,as as je,Z as $}from"./index-b6a2842e.js";const qe={class:"enterprise-reports"},Le={class:"page-header"},We={class:"header-content"},He={class:"header-left"},Qe={class:"header-actions"},Ye={class:"time-filter-section"},Ze={class:"filter-actions"},Je={class:"metrics-section"},Ke={class:"metric-content"},Xe={class:"metric-icon interviews"},et={class:"metric-info"},tt={class:"metric-number"},at={class:"metric-trend positive"},lt={class:"metric-content"},st={class:"metric-icon candidates"},nt={class:"metric-info"},ot={class:"metric-number"},it={class:"metric-trend positive"},rt={class:"metric-content"},dt={class:"metric-icon pass-rate"},ut={class:"metric-info"},ct={class:"metric-number"},pt={class:"metric-content"},_t={class:"metric-icon avg-score"},ft={class:"metric-info"},mt={class:"metric-number"},vt={class:"metric-trend positive"},gt={class:"charts-section"},ht={class:"card-header"},yt={class:"card-header"},bt={class:"card-header"},wt={class:"card-header"},Ct={class:"data-table-section"},kt={class:"card-header"},xt={class:"header-actions"},It={class:"pagination-section"},Rt={__name:"EnterpriseReports",setup(St){const U=Te(),J=je,S=f([]),y=f("all"),b=f("all"),E=f("comprehensive"),w=f("month"),C=f(""),D=f(1),P=f(20),v=Ne({totalInterviews:1834,interviewGrowth:12.5,totalCandidates:2456,candidateGrowth:8.3,passRate:68.5,passRateChange:2.1,avgScore:85.6,scoreImprovement:3.2}),O=f(),j=f(),q=f(),L=f(),B=f([{candidateName:"张三",position:"前端工程师",domain:"ai",interviewDate:"2024-01-15",interviewer:"李面试官",score:88,status:"passed",duration:"45分钟"},{candidateName:"李四",position:"算法工程师",domain:"ai",interviewDate:"2024-01-14",interviewer:"王面试官",score:92,status:"passed",duration:"60分钟"},{candidateName:"王五",position:"大数据工程师",domain:"bigdata",interviewDate:"2024-01-13",interviewer:"赵面试官",score:65,status:"failed",duration:"40分钟"}]),W=$e(()=>{let s=B.value;return C.value&&(s=s.filter(t=>t.candidateName.includes(C.value)||t.position.includes(C.value))),y.value!=="all"&&(s=s.filter(t=>t.domain===y.value)),b.value!=="all"&&(s=s.filter(t=>t.position.includes(b.value))),s}),K=()=>{U.go(-1)},X=()=>{U.push("/position-management")},ee=()=>{U.push("/batch-interview-setup")},te=s=>({ai:"AI技术",bigdata:"大数据",iot:"IoT物联网"})[s]||s,ae=s=>({ai:"#0066cc",bigdata:"#059669",iot:"#ea580c"})[s]||"#666",le=s=>s>=90?"success":s>=80?"warning":s>=70?"":"danger",se=s=>({passed:"通过",failed:"未通过",pending:"待定"})[s]||s,ne=s=>({passed:"success",failed:"danger",pending:"warning"})[s]||"",oe=()=>{d.info("日期筛选已更新")},ie=()=>{d.info("技术领域筛选已更新")},re=()=>{d.info("职位筛选已更新")},de=()=>{d.info("报表类型已更新")},ue=()=>{S.value=[],y.value="all",b.value="all",E.value="comprehensive",C.value="",d.success("筛选条件已重置")},ce=()=>{d.success("筛选条件已应用")},pe=()=>{d.info("报表导出功能开发中...")},_e=()=>{d.success("数据已刷新")},fe=()=>{d.info("报表生成功能开发中...")},me=async()=>{var s;try{d.info("正在生成AI数据洞察...");const t={dataType:"enterprise_recruitment",metrics:v,tableData:B.value,timeRange:S.value,domain:y.value,position:b.value,analysisDepth:"comprehensive"},i=await J.generateDataDrivenInsights(t);d.success("AI洞察分析完成！"),console.log("🤖 AI数据洞察结果:",i),d({message:`AI洞察：${((s=i.keyInsights)==null?void 0:s[0])||"数据分析完成，建议关注候选人技能匹配度"}`,type:"success",duration:5e3})}catch(t){console.error("❌ AI洞察分析失败:",t),d.error("AI洞察分析暂时不可用，请稍后重试")}},ve=s=>{d.info(`执行操作: ${s}`)},ge=()=>{d.info("面试官详情功能开发中...")},he=()=>{d.info("表格数据导出功能开发中...")},ye=s=>{d.info(`查看 ${s.candidateName} 的面试报告`)},be=s=>{d.info(`查看 ${s.candidateName} 的详细信息`)},we=s=>{P.value=s,D.value=1},Ce=s=>{D.value=s};let k=null,x=null,I=null,R=null;const z=(s,t,i=3)=>{let r=0;const A=()=>{if(!s.value)return;const c=s.value;if(c.clientWidth===0||c.clientHeight===0){r++,r<i?setTimeout(A,100):console.warn("图表容器尺寸异常，跳过初始化");return}try{t(c)}catch(_){console.error("图表初始化失败:",_)}};A()},ke=()=>{z(O,s=>{k=$(s),k.setOption({title:{text:"面试数量趋势"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月"]},yAxis:{type:"value"},series:[{data:[120,200,150,80,70,110],type:"line",smooth:!0,itemStyle:{color:"#0066cc"}}]})}),z(j,s=>{x=$(s),x.setOption({title:{text:"技术领域分布"},tooltip:{trigger:"item"},series:[{type:"pie",radius:"50%",data:[{value:1048,name:"AI技术",itemStyle:{color:"#0066cc"}},{value:735,name:"大数据",itemStyle:{color:"#059669"}},{value:580,name:"IoT物联网",itemStyle:{color:"#ea580c"}}]}]})}),z(q,s=>{I=$(s),I.setOption({title:{text:"评分分布"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:["0-60","60-70","70-80","80-90","90-100"]},yAxis:{type:"value"},series:[{data:[23,45,156,234,178],type:"bar",itemStyle:{color:"#667eea"}}]})}),z(L,s=>{R=$(s),R.setOption({title:{text:"面试官效率"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:["李面试官","王面试官","赵面试官","刘面试官"]},yAxis:{type:"value"},series:[{data:[45,38,52,41],type:"bar",itemStyle:{color:"#4c51bf"}}]})})};return Ue(async()=>{try{console.log("✅ iFlytek Spark服务已就绪")}catch(s){console.error("❌ iFlytek Spark服务初始化失败:",s)}Ee(()=>{setTimeout(()=>{ke()},100)}),console.log("企业报表页面已加载")}),Pe(()=>{k&&(k.dispose(),k=null),x&&(x.dispose(),x=null),I&&(I.dispose(),I=null),R&&(R.dispose(),R=null)}),(s,t)=>{const i=p("el-icon"),r=p("el-button"),A=p("el-date-picker"),c=p("el-col"),_=p("el-option"),G=p("el-select"),V=p("el-row"),g=p("el-card"),xe=p("el-button-group"),H=p("el-dropdown-item"),Ie=p("el-dropdown-menu"),Re=p("el-dropdown"),T=p("el-tag"),Se=p("el-input"),h=p("el-table-column"),De=p("el-table"),ze=p("el-pagination");return Be(),Ge("div",qe,[l("div",Le,[l("div",We,[l("div",He,[e(r,{onClick:K,link:"",class:"back-btn"},{default:a(()=>[e(i,null,{default:a(()=>[e(u(Fe))]),_:1}),t[10]||(t[10]=o(" 返回 "))]),_:1,__:[10]}),t[11]||(t[11]=l("div",{class:"page-title"},[l("h1",null,"数据报表"),l("p",null,"企业招聘数据分析与可视化报表")],-1))]),l("div",Qe,[e(r,{onClick:X,type:"info"},{default:a(()=>[e(i,null,{default:a(()=>[e(u(Q))]),_:1}),t[12]||(t[12]=o(" 职位管理 "))]),_:1,__:[12]}),e(r,{onClick:ee,type:"success"},{default:a(()=>[e(i,null,{default:a(()=>[e(u(Y))]),_:1}),t[13]||(t[13]=o(" 批量面试 "))]),_:1,__:[13]}),e(r,{onClick:pe},{default:a(()=>[e(i,null,{default:a(()=>[e(u(F))]),_:1}),t[14]||(t[14]=o(" 导出报表 "))]),_:1,__:[14]}),e(r,{onClick:_e},{default:a(()=>[e(i,null,{default:a(()=>[e(u(Z))]),_:1}),t[15]||(t[15]=o(" 刷新数据 "))]),_:1,__:[15]}),e(r,{onClick:me,type:"warning"},{default:a(()=>[e(i,null,{default:a(()=>[e(u(M))]),_:1}),t[16]||(t[16]=o(" AI洞察分析 "))]),_:1,__:[16]}),e(r,{type:"primary",onClick:fe},{default:a(()=>[e(i,null,{default:a(()=>[e(u(F))]),_:1}),t[17]||(t[17]=o(" 生成报表 "))]),_:1,__:[17]})])])]),l("div",Ye,[e(g,null,{default:a(()=>[e(V,{gutter:16,align:"middle"},{default:a(()=>[e(c,{span:6},{default:a(()=>[e(A,{modelValue:S.value,"onUpdate:modelValue":t[0]||(t[0]=n=>S.value=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:oe},null,8,["modelValue"])]),_:1}),e(c,{span:4},{default:a(()=>[e(G,{modelValue:y.value,"onUpdate:modelValue":t[1]||(t[1]=n=>y.value=n),placeholder:"技术领域",onChange:ie},{default:a(()=>[e(_,{label:"全部领域",value:"all"}),e(_,{label:"AI技术",value:"ai"}),e(_,{label:"大数据",value:"bigdata"}),e(_,{label:"IoT物联网",value:"iot"})]),_:1},8,["modelValue"])]),_:1}),e(c,{span:4},{default:a(()=>[e(G,{modelValue:b.value,"onUpdate:modelValue":t[2]||(t[2]=n=>b.value=n),placeholder:"职位类型",onChange:re},{default:a(()=>[e(_,{label:"全部职位",value:"all"}),e(_,{label:"前端工程师",value:"frontend"}),e(_,{label:"后端工程师",value:"backend"}),e(_,{label:"算法工程师",value:"algorithm"})]),_:1},8,["modelValue"])]),_:1}),e(c,{span:4},{default:a(()=>[e(G,{modelValue:E.value,"onUpdate:modelValue":t[3]||(t[3]=n=>E.value=n),placeholder:"报表类型",onChange:de},{default:a(()=>[e(_,{label:"综合报表",value:"comprehensive"}),e(_,{label:"面试分析",value:"interview"}),e(_,{label:"候选人分析",value:"candidate"}),e(_,{label:"效率分析",value:"efficiency"})]),_:1},8,["modelValue"])]),_:1}),e(c,{span:6},{default:a(()=>[l("div",Ze,[e(r,{onClick:ue},{default:a(()=>t[18]||(t[18]=[o("重置")])),_:1,__:[18]}),e(r,{type:"primary",onClick:ce},{default:a(()=>t[19]||(t[19]=[o("应用筛选")])),_:1,__:[19]})])]),_:1})]),_:1})]),_:1})]),l("div",Je,[e(V,{gutter:24},{default:a(()=>[e(c,{span:6},{default:a(()=>[e(g,{class:"metric-card"},{default:a(()=>[l("div",Ke,[l("div",Xe,[e(i,null,{default:a(()=>[e(u(Y))]),_:1})]),l("div",et,[l("div",tt,m(v.totalInterviews),1),t[20]||(t[20]=l("div",{class:"metric-label"},"总面试数",-1)),l("div",at,[e(i,null,{default:a(()=>[e(u(N))]),_:1}),o(" +"+m(v.interviewGrowth)+"% ",1)])])])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(g,{class:"metric-card"},{default:a(()=>[l("div",lt,[l("div",st,[e(i,null,{default:a(()=>[e(u(Q))]),_:1})]),l("div",nt,[l("div",ot,m(v.totalCandidates),1),t[21]||(t[21]=l("div",{class:"metric-label"},"候选人数",-1)),l("div",it,[e(i,null,{default:a(()=>[e(u(N))]),_:1}),o(" +"+m(v.candidateGrowth)+"% ",1)])])])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(g,{class:"metric-card"},{default:a(()=>[l("div",rt,[l("div",dt,[e(i,null,{default:a(()=>[e(u(M))]),_:1})]),l("div",ut,[l("div",ct,m(v.passRate)+"%",1),t[22]||(t[22]=l("div",{class:"metric-label"},"通过率",-1)),l("div",{class:Me(["metric-trend",v.passRateChange>=0?"positive":"negative"])},[e(i,null,{default:a(()=>[e(u(N))]),_:1}),o(" "+m(v.passRateChange>=0?"+":"")+m(v.passRateChange)+"% ",1)],2)])])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(g,{class:"metric-card"},{default:a(()=>[l("div",pt,[l("div",_t,[e(i,null,{default:a(()=>[e(u(M))]),_:1})]),l("div",ft,[l("div",mt,m(v.avgScore),1),t[23]||(t[23]=l("div",{class:"metric-label"},"平均分数",-1)),l("div",vt,[e(i,null,{default:a(()=>[e(u(N))]),_:1}),o(" +"+m(v.scoreImprovement),1)])])])]),_:1})]),_:1})]),_:1})]),l("div",gt,[e(V,{gutter:24},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(g,{class:"chart-card"},{header:a(()=>[l("div",ht,[t[27]||(t[27]=l("span",null,"面试趋势分析",-1)),e(xe,null,{default:a(()=>[e(r,{size:"small",type:w.value==="week"?"primary":"",onClick:t[4]||(t[4]=n=>w.value="week")},{default:a(()=>t[24]||(t[24]=[o("周")])),_:1,__:[24]},8,["type"]),e(r,{size:"small",type:w.value==="month"?"primary":"",onClick:t[5]||(t[5]=n=>w.value="month")},{default:a(()=>t[25]||(t[25]=[o("月")])),_:1,__:[25]},8,["type"]),e(r,{size:"small",type:w.value==="quarter"?"primary":"",onClick:t[6]||(t[6]=n=>w.value="quarter")},{default:a(()=>t[26]||(t[26]=[o("季")])),_:1,__:[26]},8,["type"])]),_:1})])]),default:a(()=>[l("div",{class:"chart-container",ref_key:"trendChartRef",ref:O,style:{height:"300px"}},null,512)]),_:1})]),_:1}),e(c,{span:12},{default:a(()=>[e(g,{class:"chart-card"},{header:a(()=>[l("div",yt,[t[31]||(t[31]=l("span",null,"技术领域分布",-1)),e(Re,{onCommand:ve},{dropdown:a(()=>[e(Ie,null,{default:a(()=>[e(H,{command:"export"},{default:a(()=>t[29]||(t[29]=[o("导出数据")])),_:1,__:[29]}),e(H,{command:"detail"},{default:a(()=>t[30]||(t[30]=[o("查看详情")])),_:1,__:[30]})]),_:1})]),default:a(()=>[e(r,{size:"small"},{default:a(()=>[t[28]||(t[28]=o(" 更多")),e(i,{class:"el-icon--right"},{default:a(()=>[e(u(Z))]),_:1})]),_:1,__:[28]})]),_:1})])]),default:a(()=>[l("div",{class:"chart-container",ref_key:"domainChartRef",ref:j,style:{height:"300px"}},null,512)]),_:1})]),_:1})]),_:1}),e(V,{gutter:24,style:{"margin-top":"24px"}},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(g,{class:"chart-card"},{header:a(()=>[l("div",bt,[t[33]||(t[33]=l("span",null,"面试评分分布",-1)),e(T,{type:"info",size:"small"},{default:a(()=>t[32]||(t[32]=[o("最近30天")])),_:1,__:[32]})])]),default:a(()=>[l("div",{class:"chart-container",ref_key:"scoreChartRef",ref:q,style:{height:"300px"}},null,512)]),_:1})]),_:1}),e(c,{span:12},{default:a(()=>[e(g,{class:"chart-card"},{header:a(()=>[l("div",wt,[t[35]||(t[35]=l("span",null,"面试官效率分析",-1)),e(r,{size:"small",onClick:ge},{default:a(()=>t[34]||(t[34]=[o("查看详情")])),_:1,__:[34]})])]),default:a(()=>[l("div",{class:"chart-container",ref_key:"efficiencyChartRef",ref:L,style:{height:"300px"}},null,512)]),_:1})]),_:1})]),_:1})]),l("div",Ct,[e(g,null,{header:a(()=>[l("div",kt,[l("span",null,"详细数据 ("+m(B.value.length)+")",1),l("div",xt,[e(Se,{modelValue:C.value,"onUpdate:modelValue":t[7]||(t[7]=n=>C.value=n),placeholder:"搜索候选人...",style:{width:"200px","margin-right":"12px"},clearable:""},{prefix:a(()=>[e(i,null,{default:a(()=>[e(u(Oe))]),_:1})]),_:1},8,["modelValue"]),e(r,{onClick:he},{default:a(()=>[e(i,null,{default:a(()=>[e(u(F))]),_:1}),t[36]||(t[36]=o(" 导出 "))]),_:1,__:[36]})])])]),default:a(()=>[e(De,{data:W.value,style:{width:"100%"},"max-height":"400"},{default:a(()=>[e(h,{prop:"candidateName",label:"候选人",width:"120"}),e(h,{prop:"position",label:"职位",width:"120"}),e(h,{prop:"domain",label:"技术领域",width:"100"},{default:a(n=>[e(T,{color:ae(n.row.domain),size:"small"},{default:a(()=>[o(m(te(n.row.domain)),1)]),_:2},1032,["color"])]),_:1}),e(h,{prop:"interviewDate",label:"面试日期",width:"120"}),e(h,{prop:"interviewer",label:"面试官",width:"100"}),e(h,{prop:"score",label:"总分",width:"80"},{default:a(n=>[e(T,{type:le(n.row.score)},{default:a(()=>[o(m(n.row.score),1)]),_:2},1032,["type"])]),_:1}),e(h,{prop:"status",label:"状态",width:"100"},{default:a(n=>[e(T,{type:ne(n.row.status)},{default:a(()=>[o(m(se(n.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(h,{prop:"duration",label:"时长",width:"80"}),e(h,{label:"操作",width:"150"},{default:a(n=>[e(r,{size:"small",onClick:Ae=>ye(n.row)},{default:a(()=>t[37]||(t[37]=[o("查看报告")])),_:2,__:[37]},1032,["onClick"]),e(r,{size:"small",onClick:Ae=>be(n.row)},{default:a(()=>t[38]||(t[38]=[o("详情")])),_:2,__:[38]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),l("div",It,[e(ze,{"current-page":D.value,"onUpdate:currentPage":t[8]||(t[8]=n=>D.value=n),"page-size":P.value,"onUpdate:pageSize":t[9]||(t[9]=n=>P.value=n),"page-sizes":[10,20,50,100],total:W.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:we,onCurrentChange:Ce},null,8,["current-page","page-size","total"])])]),_:1})])])}}},zt=Ve(Rt,[["__scopeId","data-v-b774b2a3"]]);export{zt as default};
