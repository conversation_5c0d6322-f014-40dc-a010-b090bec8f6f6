#!/usr/bin/env python3
"""
前后端集成测试脚本
验证多模态面试评估系统的完整功能
"""

import requests
import json
import time
import sys
import os

def test_backend_health():
    """测试后端健康状态"""
    print("=== 后端健康检查 ===")
    
    backend_url = "http://localhost:8000"
    
    try:
        # 测试简单健康检查
        response = requests.get(f"{backend_url}/health", timeout=5)
        if response.status_code == 200:
            print("✓ 后端基础健康检查通过")
        else:
            print(f"✗ 后端基础健康检查失败: {response.status_code}")
            return False
        
        # 测试详细健康检查
        response = requests.get(f"{backend_url}/api/v1/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✓ 详细健康检查通过，状态: {health_data.get('status', 'unknown')}")
        else:
            print(f"⚠ 详细健康检查失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到后端服务，请确保后端服务已启动")
        return False
    except Exception as e:
        print(f"✗ 后端健康检查异常: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n=== API端点测试 ===")
    
    backend_url = "http://localhost:8000"
    
    try:
        # 1. 测试创建面试会话
        session_data = {
            "user_name": "测试用户",
            "domain": "人工智能",
            "position": "AI工程师",
            "difficulty": "中等"
        }
        
        response = requests.post(f"{backend_url}/api/v1/interview/create", 
                               json=session_data, timeout=10)
        
        if response.status_code == 200:
            session_result = response.json()
            session_id = session_result.get("session_id")
            print(f"✓ 创建面试会话成功，会话ID: {session_id}")
            
            # 2. 测试多模态分析
            analysis_data = {
                "session_id": session_id,
                "question_text": "请介绍您在机器学习项目中的经验",
                "text_data": "我在机器学习项目中使用了深度学习技术，特别是卷积神经网络和循环神经网络。在数据预处理阶段，我使用了特征工程和数据增强技术。模型训练过程中，我采用了Adam优化器和学习率调度策略。"
            }
            
            response = requests.post(f"{backend_url}/api/v1/analysis/multimodal",
                                   json=analysis_data, timeout=30)
            
            if response.status_code == 200:
                analysis_result = response.json()
                if analysis_result.get("success"):
                    print("✓ 多模态分析成功")
                    
                    # 检查分析结果结构
                    analysis_data = analysis_result.get("analysis_results", {})
                    if "capability_scores" in analysis_data:
                        scores = analysis_data["capability_scores"]
                        capability_count = len([k for k in scores.keys() if not k.startswith("_")])
                        print(f"✓ 生成了 {capability_count} 个能力指标")
                    else:
                        print("⚠ 分析结果中缺少能力分数")
                else:
                    print("✗ 多模态分析失败")
                    return False
            else:
                print(f"✗ 多模态分析请求失败: {response.status_code}")
                return False
            
            # 3. 测试报告生成
            response = requests.post(f"{backend_url}/api/v1/report/generate/{session_id}",
                                   timeout=15)
            
            if response.status_code == 200:
                report_result = response.json()
                report_id = report_result.get("report_id")
                print(f"✓ 报告生成成功，报告ID: {report_id}")
                
                # 获取报告内容
                response = requests.get(f"{backend_url}/api/v1/report/{report_id}",
                                      timeout=10)
                
                if response.status_code == 200:
                    report_data = response.json()
                    if "overall_score" in report_data:
                        print(f"✓ 报告获取成功，总分: {report_data.get('overall_score', 0)}")
                    else:
                        print("⚠ 报告内容不完整")
                else:
                    print(f"⚠ 报告获取失败: {response.status_code}")
            else:
                print(f"⚠ 报告生成失败: {response.status_code}")
            
            return True
        else:
            print(f"✗ 创建面试会话失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ API端点测试异常: {e}")
        return False

def test_frontend_accessibility():
    """测试前端可访问性"""
    print("\n=== 前端可访问性测试 ===")
    
    frontend_url = "http://localhost:5173"
    
    try:
        response = requests.get(frontend_url, timeout=10)
        if response.status_code == 200:
            print("✓ 前端服务可访问")
            
            # 检查关键页面内容
            content = response.text
            if "多模态面试评估系统" in content or "Vue" in content:
                print("✓ 前端内容正常")
            else:
                print("⚠ 前端内容可能异常")
            
            return True
        else:
            print(f"✗ 前端服务访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到前端服务，请确保前端服务已启动")
        return False
    except Exception as e:
        print(f"✗ 前端可访问性测试异常: {e}")
        return False

def test_system_performance():
    """测试系统性能"""
    print("\n=== 系统性能测试 ===")
    
    backend_url = "http://localhost:8000"
    
    try:
        # 测试响应时间
        start_time = time.time()
        response = requests.get(f"{backend_url}/api/v1/system/performance", timeout=10)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✓ 性能监控端点响应正常，响应时间: {response_time:.2f}秒")
            
            performance_data = response.json()
            if performance_data.get("success"):
                print("✓ 性能数据获取成功")
            else:
                print("⚠ 性能数据获取失败")
            
            return True
        else:
            print(f"✗ 性能监控端点失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 系统性能测试异常: {e}")
        return False

def test_chinese_localization():
    """测试中文本地化"""
    print("\n=== 中文本地化测试 ===")
    
    try:
        # 测试中文文本处理
        test_chinese_text = "我在人工智能领域有丰富的经验，特别是在机器学习和深度学习方面。"
        
        # 简单的中文字符检测
        chinese_char_count = sum(1 for char in test_chinese_text if '\u4e00' <= char <= '\u9fff')
        total_chars = len(test_chinese_text)
        chinese_ratio = chinese_char_count / total_chars if total_chars > 0 else 0
        
        if chinese_ratio > 0.5:
            print(f"✓ 中文文本处理正常，中文字符比例: {chinese_ratio:.2f}")
        else:
            print(f"⚠ 中文文本处理可能有问题，中文字符比例: {chinese_ratio:.2f}")
        
        # 测试中文编码
        encoded_text = test_chinese_text.encode('utf-8')
        decoded_text = encoded_text.decode('utf-8')
        
        if decoded_text == test_chinese_text:
            print("✓ 中文编码解码正常")
        else:
            print("✗ 中文编码解码异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 中文本地化测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 多模态面试评估系统集成测试开始\n")
    
    results = []
    
    # 1. 后端健康检查
    backend_ok = test_backend_health()
    results.append(backend_ok)
    
    if backend_ok:
        # 2. API端点测试
        api_ok = test_api_endpoints()
        results.append(api_ok)
        
        # 3. 系统性能测试
        performance_ok = test_system_performance()
        results.append(performance_ok)
    else:
        print("⚠ 跳过API和性能测试（后端不可用）")
        results.extend([False, False])
    
    # 4. 前端可访问性测试
    frontend_ok = test_frontend_accessibility()
    results.append(frontend_ok)
    
    # 5. 中文本地化测试
    chinese_ok = test_chinese_localization()
    results.append(chinese_ok)
    
    # 总结
    print("\n" + "="*60)
    print("📊 集成测试结果总结")
    print("="*60)
    
    test_names = [
        "后端健康检查",
        "API端点测试",
        "系统性能测试",
        "前端可访问性",
        "中文本地化"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 系统集成测试完全通过！")
        print("\n✅ 系统已完全就绪，可以投入使用")
        print("\n📝 使用说明:")
        print("   - 前端访问: http://localhost:5173")
        print("   - 后端API: http://localhost:8000")
        print("   - API文档: http://localhost:8000/docs")
        return 0
    elif passed >= total * 0.8:
        print("⚠ 系统基本可用，但建议修复部分问题")
        print("\n📝 建议:")
        print("   - 检查失败的测试项目")
        print("   - 确保前后端服务都已正确启动")
        return 1
    else:
        print("❌ 系统存在严重问题，需要修复后再使用")
        print("\n📝 故障排除:")
        print("   - 确保后端服务已启动: python -m uvicorn app.main:app --reload")
        print("   - 确保前端服务已启动: npm run dev")
        print("   - 检查端口是否被占用")
        return 2

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生异常: {e}")
        sys.exit(2)
