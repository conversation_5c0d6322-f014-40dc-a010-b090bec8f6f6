{"version": 3, "sources": ["../../../src/language/radar/module.ts", "../../../src/language/radar/tokenBuilder.ts"], "sourcesContent": ["import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\nimport { CommonValueConverter } from '../common/valueConverter.js';\nimport { MermaidGeneratedSharedModule, RadarGeneratedModule } from '../generated/module.js';\nimport { RadarTokenBuilder } from './tokenBuilder.js';\n\n/**\n * Declaration of `Radar` services.\n */\ninterface RadarAddedServices {\n  parser: {\n    TokenBuilder: RadarTokenBuilder;\n    ValueConverter: CommonValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Radar` services.\n */\nexport type RadarServices = LangiumCoreServices & RadarAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Radar` services.\n */\nexport const RadarModule: Module<RadarServices, PartialLangiumCoreServices & RadarAddedServices> = {\n  parser: {\n    TokenBuilder: () => new RadarTokenBuilder(),\n    ValueConverter: () => new CommonValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createRadarServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Radar: RadarServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Radar: RadarServices = inject(\n    createDefaultCoreModule({ shared }),\n    RadarGeneratedModule,\n    RadarModule\n  );\n  shared.ServiceRegistry.register(Radar);\n  return { shared, Radar };\n}\n", "import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class RadarTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['radar-beta']);\n  }\n}\n"], "mappings": ";;;;;;;;;AAOA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;;;ACVA,IAAM,oBAAN,cAAgC,4BAA4B;AAAA,EAFnE,OAEmE;AAAA;AAAA;AAAA,EAC1D,cAAc;AACnB,UAAM,CAAC,YAAY,CAAC;AAAA,EACtB;AACF;;;AD8BO,IAAM,cAAsF;AAAA,EACjG,QAAQ;AAAA,IACN,cAAc,6BAAM,IAAI,kBAAkB,GAA5B;AAAA,IACd,gBAAgB,6BAAM,IAAI,qBAAqB,GAA/B;AAAA,EAClB;AACF;AAgBO,SAAS,oBAAoB,UAA0C,iBAG5E;AACA,QAAM,SAAoC;AAAA,IACxC,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,QAAuB;AAAA,IAC3B,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,KAAK;AACrC,SAAO,EAAE,QAAQ,MAAM;AACzB;AAfgB;", "names": []}