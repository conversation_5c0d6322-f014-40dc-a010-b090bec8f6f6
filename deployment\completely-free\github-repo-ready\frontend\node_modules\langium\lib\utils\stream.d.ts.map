{"version": 3, "file": "stream.d.ts", "sourceRoot": "", "sources": ["../../src/utils/stream.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF;;;;;;;;;GASG;AACH,MAAM,WAAW,MAAM,CAAC,CAAC,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC;IAE1C;;OAEG;IACH,QAAQ,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAEhC;;OAEG;IACH,OAAO,IAAI,OAAO,CAAC;IAEnB;;OAEG;IACH,KAAK,IAAI,MAAM,CAAC;IAEhB;;OAEG;IACH,OAAO,IAAI,CAAC,EAAE,CAAC;IAEf;;OAEG;IACH,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;IAEhB;;;;;OAKG;IACH,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE3E;;OAEG;IACH,QAAQ,IAAI,MAAM,CAAC;IAEnB;;;;OAIG;IACH,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAEhD;;;;;OAKG;IACH,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IAEhC;;;;;;OAMG;IACH,OAAO,CAAC,aAAa,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAEtD;;;;;;OAMG;IACH,KAAK,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3E,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,OAAO,CAAC;IAEjD;;;;;;OAMG;IACH,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,OAAO,CAAC;IAEhD;;;;OAIG;IACH,OAAO,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI,CAAC;IAE7D;;;;;;;OAOG;IACH,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAE/C;;;;;;;OAOG;IACH,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpE,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEpD;;;OAGG;IACH,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtC;;;;;;;;;;OAUG;IACH,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;IAC5E,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC;IAExF;;;;;;;;;;OAUG;IACH,WAAW,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;IACjF,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC;IAE7F;;;;;;;OAOG;IACH,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;IACtE,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC;IAEtD;;;;;;;OAOG;IACH,SAAS,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,MAAM,CAAC;IAEpD;;;;OAIG;IACH,QAAQ,CAAC,aAAa,EAAE,CAAC,GAAG,OAAO,CAAC;IAEpC;;;;;OAKG;IACH,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEjE;;;;;OAKG;IACH,IAAI,CAAC,CAAC,SAAS,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAExD;;OAEG;IACH,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC;IAEtB;;;;;OAKG;IACH,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEpC;;;;;OAKG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAElC;;;;;;OAMG;IACH,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEvD;;;;;;OAMG;IACH,OAAO,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;CAE9E;AAED,MAAM,MAAM,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI;IAC9C,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAClB,OAAO,EAAE,CAAC,SAAS,QAAQ,CAAC,MAAM,OAAO,CAAC,GACpC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,GACpC,MAAM,CAAC,CAAC,CAAC,CAAA;CAClB,CAAC,KAAK,SAAS,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC;AAEtC,MAAM,MAAM,QAAQ,CAAC,CAAC,SAAS,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAE3H;;;;GAIG;AACH,qBAAa,UAAU,CAAC,CAAC,EAAE,CAAC,CAAE,YAAW,MAAM,CAAC,CAAC,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACpC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,cAAc,CAAC,CAAC,CAAC,CAAC;gBAE/C,OAAO,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC;IAKhF,QAAQ,IAAI,gBAAgB,CAAC,CAAC,CAAC;IAS/B,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC;IAIhC,OAAO,IAAI,OAAO;IAKlB,KAAK,IAAI,MAAM;IAWf,OAAO,IAAI,CAAC,EAAE;IAad,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;IAIf,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAQ1E,QAAQ,IAAI,MAAM;IAIlB,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;IAyB/C,IAAI,CAAC,SAAS,SAAM,GAAG,MAAM;IAkB7B,OAAO,CAAC,aAAa,EAAE,CAAC,EAAE,SAAS,SAAI,GAAG,MAAM;IAyBhD,KAAK,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;IACjF,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,OAAO;IAahD,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,OAAO;IAY/C,OAAO,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IAW5D,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAe9C,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;IAC1E,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;IAiB1D,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAIrC,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,SAAS;IAC3E,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC;IAgBvF,WAAW,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,SAAS;IAChF,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC;IAK5F,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS;IAYlJ,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS;IACrE,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,CAAC,GAAG,SAAS;IAarD,SAAS,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,MAAM;IAcnD,QAAQ,CAAC,aAAa,EAAE,CAAC,GAAG,OAAO;IAYnC,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IA6BhE,IAAI,CAAC,CAAC,SAAS,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;IAmCvD,IAAI,IAAI,CAAC,GAAG,SAAS;IASrB,IAAI,CAAC,SAAS,SAAI,GAAG,MAAM,CAAC,CAAC,CAAC;IAgB9B,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;IAajC,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC;IAoBtD,OAAO,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC;CAW7E;AAqBD;;GAEG;AAEH,eAAO,MAAM,YAAY,EAAE,MAAM,CAAC,GAAG,CAAsE,CAAC;AAE5G;;GAEG;AACH,eAAO,MAAM,WAAW,EAAE,oBAAoB,CAAC,SAAS,CAAmD,CAAC;AAE5G;;GAEG;AACH,wBAAgB,MAAM,CAAC,CAAC,EAAE,GAAG,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CA2DtF;AAED;;GAEG;AACH,MAAM,WAAW,YAAY,CAAC,CAAC,CAAE,SAAQ,gBAAgB,CAAC,CAAC,CAAC;IACxD;;;OAGG;IACH,KAAK,IAAI,IAAI,CAAA;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,UAAU,CAAC,CAAC,CAAE,SAAQ,MAAM,CAAC,CAAC,CAAC;IAC5C,QAAQ,IAAI,YAAY,CAAC,CAAC,CAAC,CAAA;CAC9B;AAED;;;;GAIG;AACH,qBAAa,cAAc,CAAC,CAAC,CACzB,SAAQ,UAAU,CAAC;IAAE,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC,MAAM,EAAE,OAAO,CAAA;CAAE,EAAE,CAAC,CACxE,YAAW,UAAU,CAAC,CAAC,CAAC;gBAEZ,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;QAAE,WAAW,CAAC,EAAE,OAAO,CAAA;KAAE;IA0BnF,QAAQ,IAAI,YAAY,CAAC,CAAC,CAAC;CAWvC;AAED;;GAEG;AACH,yBAAiB,SAAS,CAAC;IAEvB;;OAEG;IACH,SAAgB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAElD;IAED;;OAEG;IACH,SAAgB,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAEtD;IAED;;OAEG;IACH,SAAgB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,SAAS,CAE9D;IAED;;OAEG;IACH,SAAgB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,SAAS,CAE9D;CAEJ"}