{"version": 3, "sources": ["../../../src/rendering-util/rendering-elements/shapes/handDrawnShapeStyles.ts"], "sourcesContent": ["import { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport type { Node } from '../../types.js';\n\n// Striped fill like start or fork nodes in state diagrams\nexport const solidStateFill = (color: string) => {\n  const { handDrawnSeed } = getConfig();\n  return {\n    fill: color,\n    hachureAngle: 120, // angle of hachure,\n    hachureGap: 4,\n    fillWeight: 2,\n    roughness: 0.7,\n    stroke: color,\n    seed: handDrawnSeed,\n  };\n};\n\nexport const compileStyles = (node: Node) => {\n  // node.cssCompiledStyles is an array of strings in the form of 'key: value' where key is the css property and value is the value\n  // the array is the styles of node from the classes it is using\n  // node.cssStyles is an array of styles directly set on the node\n  // concat the arrays and remove duplicates such that the values from node.cssStyles are used if there are duplicates\n  const stylesMap = styles2Map([...(node.cssCompiledStyles || []), ...(node.cssStyles || [])]);\n  return { stylesMap, stylesArray: [...stylesMap] };\n};\n\nexport const styles2Map = (styles: string[]) => {\n  const styleMap = new Map<string, string>();\n  styles.forEach((style) => {\n    const [key, value] = style.split(':');\n    styleMap.set(key.trim(), value?.trim());\n  });\n  return styleMap;\n};\nexport const isLabelStyle = (key: string) => {\n  return (\n    key === 'color' ||\n    key === 'font-size' ||\n    key === 'font-family' ||\n    key === 'font-weight' ||\n    key === 'font-style' ||\n    key === 'text-decoration' ||\n    key === 'text-align' ||\n    key === 'text-transform' ||\n    key === 'line-height' ||\n    key === 'letter-spacing' ||\n    key === 'word-spacing' ||\n    key === 'text-shadow' ||\n    key === 'text-overflow' ||\n    key === 'white-space' ||\n    key === 'word-wrap' ||\n    key === 'word-break' ||\n    key === 'overflow-wrap' ||\n    key === 'hyphens'\n  );\n};\nexport const styles2String = (node: Node) => {\n  const { stylesArray } = compileStyles(node);\n  const labelStyles: string[] = [];\n  const nodeStyles: string[] = [];\n  const borderStyles: string[] = [];\n  const backgroundStyles: string[] = [];\n\n  stylesArray.forEach((style) => {\n    const key = style[0];\n    if (isLabelStyle(key)) {\n      labelStyles.push(style.join(':') + ' !important');\n    } else {\n      nodeStyles.push(style.join(':') + ' !important');\n      if (key.includes('stroke')) {\n        borderStyles.push(style.join(':') + ' !important');\n      }\n      if (key === 'fill') {\n        backgroundStyles.push(style.join(':') + ' !important');\n      }\n    }\n  });\n\n  return {\n    labelStyles: labelStyles.join(';'),\n    nodeStyles: nodeStyles.join(';'),\n    stylesArray,\n    borderStyles,\n    backgroundStyles,\n  };\n};\n\n// Striped fill like start or fork nodes in state diagrams\n// TODO remove any\nexport const userNodeOverrides = (node: Node, options: any) => {\n  const { themeVariables, handDrawnSeed } = getConfig();\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  // index the style array to a map object\n  const result = Object.assign(\n    {\n      roughness: 0.7,\n      fill: stylesMap.get('fill') || mainBkg,\n      fillStyle: 'hachure', // solid fill\n      fillWeight: 4,\n      hachureGap: 5.2,\n      stroke: stylesMap.get('stroke') || nodeBorder,\n      seed: handDrawnSeed,\n      strokeWidth: stylesMap.get('stroke-width')?.replace('px', '') || 1.3,\n      fillLineDash: [0, 0],\n    },\n    options\n  );\n  return result;\n};\n"], "mappings": "kFAIO,IAAMA,EAAiBC,EAACC,GAAkB,CAC/C,GAAM,CAAE,cAAAC,CAAc,EAAIC,EAAU,EACpC,MAAO,CACL,KAAMF,EACN,aAAc,IACd,WAAY,EACZ,WAAY,EACZ,UAAW,GACX,OAAQA,EACR,KAAMC,CACR,CACF,EAX8B,kBAajBE,EAAgBJ,EAACK,GAAe,CAK3C,IAAMC,EAAYC,EAAW,CAAC,GAAIF,EAAK,mBAAqB,CAAC,EAAI,GAAIA,EAAK,WAAa,CAAC,CAAE,CAAC,EAC3F,MAAO,CAAE,UAAAC,EAAW,YAAa,CAAC,GAAGA,CAAS,CAAE,CAClD,EAP6B,iBAShBC,EAAaP,EAACQ,GAAqB,CAC9C,IAAMC,EAAW,IAAI,IACrB,OAAAD,EAAO,QAASE,GAAU,CACxB,GAAM,CAACC,EAAKC,CAAK,EAAIF,EAAM,MAAM,GAAG,EACpCD,EAAS,IAAIE,EAAI,KAAK,EAAGC,GAAO,KAAK,CAAC,CACxC,CAAC,EACMH,CACT,EAP0B,cAQbI,EAAeb,EAACW,GAEzBA,IAAQ,SACRA,IAAQ,aACRA,IAAQ,eACRA,IAAQ,eACRA,IAAQ,cACRA,IAAQ,mBACRA,IAAQ,cACRA,IAAQ,kBACRA,IAAQ,eACRA,IAAQ,kBACRA,IAAQ,gBACRA,IAAQ,eACRA,IAAQ,iBACRA,IAAQ,eACRA,IAAQ,aACRA,IAAQ,cACRA,IAAQ,iBACRA,IAAQ,UAnBgB,gBAsBfG,EAAgBd,EAACK,GAAe,CAC3C,GAAM,CAAE,YAAAU,CAAY,EAAIX,EAAcC,CAAI,EACpCW,EAAwB,CAAC,EACzBC,EAAuB,CAAC,EACxBC,EAAyB,CAAC,EAC1BC,EAA6B,CAAC,EAEpC,OAAAJ,EAAY,QAASL,GAAU,CAC7B,IAAMC,EAAMD,EAAM,CAAC,EACfG,EAAaF,CAAG,EAClBK,EAAY,KAAKN,EAAM,KAAK,GAAG,EAAI,aAAa,GAEhDO,EAAW,KAAKP,EAAM,KAAK,GAAG,EAAI,aAAa,EAC3CC,EAAI,SAAS,QAAQ,GACvBO,EAAa,KAAKR,EAAM,KAAK,GAAG,EAAI,aAAa,EAE/CC,IAAQ,QACVQ,EAAiB,KAAKT,EAAM,KAAK,GAAG,EAAI,aAAa,EAG3D,CAAC,EAEM,CACL,YAAaM,EAAY,KAAK,GAAG,EACjC,WAAYC,EAAW,KAAK,GAAG,EAC/B,YAAAF,EACA,aAAAG,EACA,iBAAAC,CACF,CACF,EA7B6B,iBAiChBC,EAAoBpB,EAAA,CAACK,EAAYgB,IAAiB,CAC7D,GAAM,CAAE,eAAAC,EAAgB,cAAApB,CAAc,EAAIC,EAAU,EAC9C,CAAE,WAAAoB,EAAY,QAAAC,CAAQ,EAAIF,EAC1B,CAAE,UAAAhB,CAAU,EAAIF,EAAcC,CAAI,EAiBxC,OAde,OAAO,OACpB,CACE,UAAW,GACX,KAAMC,EAAU,IAAI,MAAM,GAAKkB,EAC/B,UAAW,UACX,WAAY,EACZ,WAAY,IACZ,OAAQlB,EAAU,IAAI,QAAQ,GAAKiB,EACnC,KAAMrB,EACN,YAAaI,EAAU,IAAI,cAAc,GAAG,QAAQ,KAAM,EAAE,GAAK,IACjE,aAAc,CAAC,EAAG,CAAC,CACrB,EACAe,CACF,CAEF,EArBiC", "names": ["solidStateFill", "__name", "color", "handDrawnSeed", "getConfig", "compileStyles", "node", "stylesMap", "styles2Map", "styles", "styleMap", "style", "key", "value", "isLabelStyle", "styles2String", "stylesArray", "labelStyles", "nodeStyles", "borderStyles", "backgroundStyles", "userNodeOverrides", "options", "themeVariables", "nodeBorder", "mainBkg"]}