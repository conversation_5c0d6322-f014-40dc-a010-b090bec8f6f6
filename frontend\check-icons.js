#!/usr/bin/env node

/**
 * 检查项目中使用的Element Plus图标是否都存在
 * Check if all Element Plus icons used in the project exist
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Element Plus图标列表（常用的）
const validIcons = [
  'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowUp',
  'Back', 'Calendar', 'Check', 'CircleCheck', 'CircleClose', 'Clock', 'Close',
  'Collection', 'Cpu', 'DataAnalysis', 'Document', 'Edit', 'Folder',
  'Grid', 'House', 'InfoFilled', 'Loading', 'Lock', 'Mouse',
  'Odometer', 'Platform', 'Promotion', 'QuestionFilled', 'Reading',
  'Search', 'Setting', 'Star', 'StarFilled', 'SuccessFilled',
  'Target', 'Timer', 'TrendCharts', 'User', 'VideoCamera', 'VideoPlay',
  'View', 'Warning', 'WarningFilled'
]

// 扫描文件中的图标使用
function scanFileForIcons(filePath) {
  if (!fs.existsSync(filePath)) {
    return []
  }

  const content = fs.readFileSync(filePath, 'utf8')
  const icons = []

  // 匹配导入语句中的图标
  const importMatches = content.match(/import\s*{\s*([^}]+)\s*}\s*from\s*['"]@element-plus\/icons-vue['"]/g)
  if (importMatches) {
    importMatches.forEach(match => {
      const iconList = match.match(/{\s*([^}]+)\s*}/)[1]
      const iconNames = iconList.split(',').map(name => name.trim())
      icons.push(...iconNames)
    })
  }

  // 匹配模板中的图标使用
  const templateMatches = content.match(/<el-icon><([A-Z][a-zA-Z]+)\s*\/><\/el-icon>/g)
  if (templateMatches) {
    templateMatches.forEach(match => {
      const iconName = match.match(/<([A-Z][a-zA-Z]+)/)[1]
      icons.push(iconName)
    })
  }

  // 匹配component :is的使用
  const componentMatches = content.match(/<component\s+:is="([A-Z][a-zA-Z]+)"/g)
  if (componentMatches) {
    componentMatches.forEach(match => {
      const iconName = match.match(/:is="([A-Z][a-zA-Z]+)"/)[1]
      icons.push(iconName)
    })
  }

  return [...new Set(icons)] // 去重
}

// 递归扫描目录
function scanDirectory(dir, extensions = ['.vue', '.js', '.ts']) {
  const allIcons = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        const icons = scanFileForIcons(fullPath)
        if (icons.length > 0) {
          allIcons.push({
            file: path.relative(dir, fullPath),
            icons
          })
        }
      }
    })
  }
  
  scan(dir)
  return allIcons
}

// 主函数
function main() {
  console.log('🔍 检查Element Plus图标使用情况...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  const allUsedIcons = new Set()
  const invalidIcons = new Set()
  
  console.log('📋 扫描结果:\n')
  
  results.forEach(({ file, icons }) => {
    console.log(`📄 ${file}:`)
    icons.forEach(icon => {
      allUsedIcons.add(icon)
      const isValid = validIcons.includes(icon)
      const status = isValid ? '✅' : '❌'
      console.log(`   ${status} ${icon}`)
      
      if (!isValid) {
        invalidIcons.add(icon)
      }
    })
    console.log()
  })
  
  console.log('📊 统计信息:')
  console.log(`   总共使用图标: ${allUsedIcons.size}`)
  console.log(`   有效图标: ${allUsedIcons.size - invalidIcons.size}`)
  console.log(`   无效图标: ${invalidIcons.size}`)
  
  if (invalidIcons.size > 0) {
    console.log('\n❌ 发现无效图标:')
    invalidIcons.forEach(icon => {
      console.log(`   - ${icon}`)
    })
    
    console.log('\n💡 建议替换:')
    const suggestions = {
      'Magic': 'Promotion',
      'Guide': 'QuestionFilled',
      'Lightbulb': 'Star',
      'Aim': 'Target'
    }
    
    invalidIcons.forEach(icon => {
      if (suggestions[icon]) {
        console.log(`   ${icon} → ${suggestions[icon]}`)
      } else {
        console.log(`   ${icon} → 需要手动选择替换`)
      }
    })
  } else {
    console.log('\n✅ 所有图标都是有效的!')
  }
  
  console.log('\n🔗 Element Plus图标文档:')
  console.log('   https://element-plus.org/zh-CN/component/icon.html')
}

// 运行检查
main()
