version: '3.8'

services:
  # 后端服务 - 优化内存使用
  backend:
    build:
      context: ../../backend
      dockerfile: ../deployment/free-deployment/Dockerfile.backend-free
    container_name: iflytek-interview-backend-free
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/interview_system.db
      - IFLYTEK_APP_ID=${IFLYTEK_APP_ID}
      - IFLYTEK_API_KEY=${IFLYTEK_API_KEY}
      - IFLYTEK_API_SECRET=${IFLYTEK_API_SECRET}
      - IFLYTEK_SPARK_URL=${IFLYTEK_SPARK_URL}
      - ENVIRONMENT=production
      - WORKERS=1
    volumes:
      - backend_data:/app/data
      - backend_logs:/app/logs
    networks:
      - iflytek_network
    mem_limit: 512m
    cpus: 0.5
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 60s
      timeout: 30s
      retries: 3

  # 前端服务 - 轻量化配置
  frontend:
    build:
      context: ../../frontend
      dockerfile: ../deployment/free-deployment/Dockerfile.frontend-free
    container_name: iflytek-interview-frontend-free
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - iflytek_network
    mem_limit: 256m
    cpus: 0.3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 60s
      timeout: 30s
      retries: 3

# 数据卷
volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local

# 网络
networks:
  iflytek_network:
    driver: bridge
