'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var splitter = require('./splitter.js');
var type = require('./type.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');
var useContainer = require('./hooks/useContainer.js');
var useSize = require('./hooks/useSize.js');
var useResize = require('./hooks/useResize.js');
var index = require('../../../hooks/use-namespace/index.js');
var index$1 = require('../../../hooks/use-ordered-children/index.js');

const __default__ = vue.defineComponent({
  name: "ElSplitter"
});
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...__default__,
  props: splitter.splitterProps,
  emits: ["resizeStart", "resize", "resizeEnd"],
  setup(__props, { emit: emits }) {
    const props = __props;
    const ns = index.useNamespace("splitter");
    const { containerEl, containerSize } = useContainer.useContainer(vue.toRef(props, "layout"));
    const {
      removeChild: unregisterPanel,
      children: panels,
      addChild: sortPanel
    } = index$1.useOrderedChildren(vue.getCurrentInstance(), "ElSplitterPanel");
    vue.watch(panels, () => {
      panels.value.forEach((instance, index) => {
        instance.setIndex(index);
      });
    });
    const { percentSizes, pxSizes } = useSize.useSize(panels, containerSize);
    const { onMoveStart, onMoving, onMoveEnd, onCollapse, movingIndex } = useResize.useResize(panels, containerSize, pxSizes);
    const onResizeStart = (index) => {
      onMoveStart(index);
      emits("resizeStart", index, pxSizes.value);
    };
    const onResize = (index, offset) => {
      onMoving(index, offset);
      emits("resize", index, pxSizes.value);
    };
    const onResizeEnd = (index) => {
      onMoveEnd();
      emits("resizeEnd", index, pxSizes.value);
    };
    vue.provide(type.splitterRootContextKey, vue.reactive({
      panels,
      percentSizes,
      pxSizes,
      layout: props.layout,
      movingIndex,
      containerSize,
      onMoveStart: onResizeStart,
      onMoving: onResize,
      onMoveEnd: onResizeEnd,
      onCollapse,
      registerPanel: (panel) => {
        panels.value.push(panel);
      },
      sortPanel,
      unregisterPanel
    }));
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        ref_key: "containerEl",
        ref: containerEl,
        class: vue.normalizeClass([vue.unref(ns).b(), vue.unref(ns).e(_ctx.layout)])
      }, [
        vue.renderSlot(_ctx.$slots, "default"),
        vue.createCommentVNode(" Prevent iframe touch events from breaking "),
        vue.unref(movingIndex) ? (vue.openBlock(), vue.createElementBlock("div", {
          key: 0,
          class: vue.normalizeClass([vue.unref(ns).e("mask"), vue.unref(ns).e(`mask-${_ctx.layout}`)])
        }, null, 2)) : vue.createCommentVNode("v-if", true)
      ], 2);
    };
  }
});
var Splitter = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "splitter.vue"]]);

exports["default"] = Splitter;
//# sourceMappingURL=splitter2.js.map
