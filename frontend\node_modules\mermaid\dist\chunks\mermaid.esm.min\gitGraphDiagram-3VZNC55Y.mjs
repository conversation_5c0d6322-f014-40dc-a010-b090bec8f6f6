import{a as ee}from"./chunk-4KE642ED.mjs";import{a as te}from"./chunk-VSLJSFIP.mjs";import{a as Q}from"./chunk-ZIX2NOZJ.mjs";import"./chunk-GFOUM62E.mjs";import"./chunk-ACBE5OQD.mjs";import"./chunk-ITREFQHG.mjs";import"./chunk-YLPUNF7Q.mjs";import"./chunk-J4IYBS62.mjs";import{f as Z,l as J,m as X}from"./chunk-CRSA2SMT.mjs";import"./chunk-TI4EEUUG.mjs";import{A as S,M as B,Q as W,R as N,S as _,T as j,U as F,V as z,W as K,Y as U,aa as V,b as $,ha as Y,s as H}from"./chunk-63ZE7VZ5.mjs";import"./chunk-2QAHK7A2.mjs";import"./chunk-7LUIIE75.mjs";import"./chunk-JIBEMXPF.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as d}from"./chunk-GTKDMUJJ.mjs";var u={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4};var he=H.gitGraph,M=d(()=>J({...he,...S().gitGraph}),"getConfig"),i=new te(()=>{let r=M(),e=r.mainBranchName,o=r.mainBranchOrder;return{mainBranchName:e,commits:new Map,head:null,branchConfig:new Map([[e,{name:e,order:o}]]),branches:new Map([[e,null]]),currBranch:e,direction:"LR",seq:0,options:{}}});function q(){return Z({length:7})}d(q,"getID");function ge(r,e){let o=Object.create(null);return r.reduce((a,t)=>{let n=e(t);return o[n]||(o[n]=!0,a.push(t)),a},[])}d(ge,"uniqBy");var fe=d(function(r){i.records.direction=r},"setDirection"),le=d(function(r){$.debug("options str",r),r=r?.trim(),r=r||"{}";try{i.records.options=JSON.parse(r)}catch(e){$.error("error while parsing gitGraph options",e.message)}},"setOptions"),ye=d(function(){return i.records.options},"getOptions"),ue=d(function(r){let e=r.msg,o=r.id,a=r.type,t=r.tags;$.info("commit",e,o,a,t),$.debug("Entering commit:",e,o,a,t);let n=M();o=B.sanitizeText(o,n),e=B.sanitizeText(e,n),t=t?.map(s=>B.sanitizeText(s,n));let m={id:o||i.records.seq+"-"+q(),message:e,seq:i.records.seq++,type:a??u.NORMAL,tags:t??[],parents:i.records.head==null?[]:[i.records.head.id],branch:i.records.currBranch};i.records.head=m,$.info("main branch",n.mainBranchName),i.records.commits.has(m.id)&&$.warn(`Commit ID ${m.id} already exists`),i.records.commits.set(m.id,m),i.records.branches.set(i.records.currBranch,m.id),$.debug("in pushCommit "+m.id)},"commit"),xe=d(function(r){let e=r.name,o=r.order;if(e=B.sanitizeText(e,M()),i.records.branches.has(e))throw new Error(`Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using "checkout ${e}")`);i.records.branches.set(e,i.records.head!=null?i.records.head.id:null),i.records.branchConfig.set(e,{name:e,order:o}),ne(e),$.debug("in createBranch")},"branch"),$e=d(r=>{let e=r.branch,o=r.id,a=r.type,t=r.tags,n=M();e=B.sanitizeText(e,n),o&&(o=B.sanitizeText(o,n));let m=i.records.branches.get(i.records.currBranch),s=i.records.branches.get(e),g=m?i.records.commits.get(m):void 0,p=s?i.records.commits.get(s):void 0;if(g&&p&&g.branch===e)throw new Error(`Cannot merge branch '${e}' into itself.`);if(i.records.currBranch===e){let c=new Error('Incorrect usage of "merge". Cannot merge a branch to itself');throw c.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]},c}if(g===void 0||!g){let c=new Error(`Incorrect usage of "merge". Current branch (${i.records.currBranch})has no commits`);throw c.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["commit"]},c}if(!i.records.branches.has(e)){let c=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") does not exist");throw c.hash={text:`merge ${e}`,token:`merge ${e}`,expected:[`branch ${e}`]},c}if(p===void 0||!p){let c=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") has no commits");throw c.hash={text:`merge ${e}`,token:`merge ${e}`,expected:['"commit"']},c}if(g===p){let c=new Error('Incorrect usage of "merge". Both branches have same head');throw c.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]},c}if(o&&i.records.commits.has(o)){let c=new Error('Incorrect usage of "merge". Commit with id:'+o+" already exists, use different custom id");throw c.hash={text:`merge ${e} ${o} ${a} ${t?.join(" ")}`,token:`merge ${e} ${o} ${a} ${t?.join(" ")}`,expected:[`merge ${e} ${o}_UNIQUE ${a} ${t?.join(" ")}`]},c}let h=s||"",f={id:o||`${i.records.seq}-${q()}`,message:`merged branch ${e} into ${i.records.currBranch}`,seq:i.records.seq++,parents:i.records.head==null?[]:[i.records.head.id,h],branch:i.records.currBranch,type:u.MERGE,customType:a,customId:!!o,tags:t??[]};i.records.head=f,i.records.commits.set(f.id,f),i.records.branches.set(i.records.currBranch,f.id),$.debug(i.records.branches),$.debug("in mergeBranch")},"merge"),be=d(function(r){let e=r.id,o=r.targetId,a=r.tags,t=r.parent;$.debug("Entering cherryPick:",e,o,a);let n=M();if(e=B.sanitizeText(e,n),o=B.sanitizeText(o,n),a=a?.map(g=>B.sanitizeText(g,n)),t=B.sanitizeText(t,n),!e||!i.records.commits.has(e)){let g=new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');throw g.hash={text:`cherryPick ${e} ${o}`,token:`cherryPick ${e} ${o}`,expected:["cherry-pick abc"]},g}let m=i.records.commits.get(e);if(m===void 0||!m)throw new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');if(t&&!(Array.isArray(m.parents)&&m.parents.includes(t)))throw new Error("Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.");let s=m.branch;if(m.type===u.MERGE&&!t)throw new Error("Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.");if(!o||!i.records.commits.has(o)){if(s===i.records.currBranch){let f=new Error('Incorrect usage of "cherryPick". Source commit is already on current branch');throw f.hash={text:`cherryPick ${e} ${o}`,token:`cherryPick ${e} ${o}`,expected:["cherry-pick abc"]},f}let g=i.records.branches.get(i.records.currBranch);if(g===void 0||!g){let f=new Error(`Incorrect usage of "cherry-pick". Current branch (${i.records.currBranch})has no commits`);throw f.hash={text:`cherryPick ${e} ${o}`,token:`cherryPick ${e} ${o}`,expected:["cherry-pick abc"]},f}let p=i.records.commits.get(g);if(p===void 0||!p){let f=new Error(`Incorrect usage of "cherry-pick". Current branch (${i.records.currBranch})has no commits`);throw f.hash={text:`cherryPick ${e} ${o}`,token:`cherryPick ${e} ${o}`,expected:["cherry-pick abc"]},f}let h={id:i.records.seq+"-"+q(),message:`cherry-picked ${m?.message} into ${i.records.currBranch}`,seq:i.records.seq++,parents:i.records.head==null?[]:[i.records.head.id,m.id],branch:i.records.currBranch,type:u.CHERRY_PICK,tags:a?a.filter(Boolean):[`cherry-pick:${m.id}${m.type===u.MERGE?`|parent:${t}`:""}`]};i.records.head=h,i.records.commits.set(h.id,h),i.records.branches.set(i.records.currBranch,h.id),$.debug(i.records.branches),$.debug("in cherryPick")}},"cherryPick"),ne=d(function(r){if(r=B.sanitizeText(r,M()),i.records.branches.has(r)){i.records.currBranch=r;let e=i.records.branches.get(i.records.currBranch);e===void 0||!e?i.records.head=null:i.records.head=i.records.commits.get(e)??null}else{let e=new Error(`Trying to checkout branch which is not yet created. (Help try using "branch ${r}")`);throw e.hash={text:`checkout ${r}`,token:`checkout ${r}`,expected:[`branch ${r}`]},e}},"checkout");function re(r,e,o){let a=r.indexOf(e);a===-1?r.push(o):r.splice(a,1,o)}d(re,"upsert");function oe(r){let e=r.reduce((t,n)=>t.seq>n.seq?t:n,r[0]),o="";r.forEach(function(t){t===e?o+="	*":o+="	|"});let a=[o,e.id,e.seq];for(let t in i.records.branches)i.records.branches.get(t)===e.id&&a.push(t);if($.debug(a.join(" ")),e.parents&&e.parents.length==2&&e.parents[0]&&e.parents[1]){let t=i.records.commits.get(e.parents[0]);re(r,e,t),e.parents[1]&&r.push(i.records.commits.get(e.parents[1]))}else{if(e.parents.length==0)return;if(e.parents[0]){let t=i.records.commits.get(e.parents[0]);re(r,e,t)}}r=ge(r,t=>t.id),oe(r)}d(oe,"prettyPrintCommitHistory");var Ce=d(function(){$.debug(i.records.commits);let r=ae()[0];oe([r])},"prettyPrint"),Be=d(function(){i.reset(),W()},"clear"),we=d(function(){return[...i.records.branchConfig.values()].map((e,o)=>e.order!==null&&e.order!==void 0?e:{...e,order:parseFloat(`0.${o}`)}).sort((e,o)=>(e.order??0)-(o.order??0)).map(({name:e})=>({name:e}))},"getBranchesAsObjArray"),ke=d(function(){return i.records.branches},"getBranches"),Te=d(function(){return i.records.commits},"getCommits"),ae=d(function(){let r=[...i.records.commits.values()];return r.forEach(function(e){$.debug(e.id)}),r.sort((e,o)=>e.seq-o.seq),r},"getCommitsArray"),Ee=d(function(){return i.records.currBranch},"getCurrentBranch"),Pe=d(function(){return i.records.direction},"getDirection"),De=d(function(){return i.records.head},"getHead"),v={commitType:u,getConfig:M,setDirection:fe,setOptions:le,getOptions:ye,commit:ue,branch:xe,merge:$e,cherryPick:be,checkout:ne,prettyPrint:Ce,clear:Be,getBranchesAsObjArray:we,getBranches:ke,getCommits:Te,getCommitsArray:ae,getCurrentBranch:Ee,getDirection:Pe,getHead:De,setAccTitle:N,getAccTitle:_,getAccDescription:F,setAccDescription:j,setDiagramTitle:z,getDiagramTitle:K};var Me=d((r,e)=>{ee(r,e),r.dir&&e.setDirection(r.dir);for(let o of r.statements)Ge(o,e)},"populate"),Ge=d((r,e)=>{let a={Commit:d(t=>e.commit(Le(t)),"Commit"),Branch:d(t=>e.branch(Oe(t)),"Branch"),Merge:d(t=>e.merge(ve(t)),"Merge"),Checkout:d(t=>e.checkout(Re(t)),"Checkout"),CherryPicking:d(t=>e.cherryPick(Ae(t)),"CherryPicking")}[r.$type];a?a(r):$.error(`Unknown statement type: ${r.$type}`)},"parseStatement"),Le=d(r=>({id:r.id,msg:r.message??"",type:r.type!==void 0?u[r.type]:u.NORMAL,tags:r.tags??void 0}),"parseCommit"),Oe=d(r=>({name:r.name,order:r.order??0}),"parseBranch"),ve=d(r=>({branch:r.branch,id:r.id??"",type:r.type!==void 0?u[r.type]:void 0,tags:r.tags??void 0}),"parseMerge"),Re=d(r=>r.branch,"parseCheckout"),Ae=d(r=>({id:r.id,targetId:"",tags:r.tags?.length===0?void 0:r.tags,parent:r.parent}),"parseCherryPicking"),se={parse:d(async r=>{let e=await Q("gitGraph",r);$.debug(e),Me(e,v)},"parse")};var Ie=U(),w=Ie?.gitGraph,P=10,D=40,k=4,T=2,G=8,b=new Map,C=new Map,R=30,L=new Map,A=[],E=0,y="LR",qe=d(()=>{b.clear(),C.clear(),L.clear(),E=0,A=[],y="LR"},"clear"),ce=d(r=>{let e=document.createElementNS("http://www.w3.org/2000/svg","text");return(typeof r=="string"?r.split(/\\n|\n|<br\s*\/?>/gi):r).forEach(a=>{let t=document.createElementNS("http://www.w3.org/2000/svg","tspan");t.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),t.setAttribute("dy","1em"),t.setAttribute("x","0"),t.setAttribute("class","row"),t.textContent=a.trim(),e.appendChild(t)}),e},"drawText"),me=d(r=>{let e,o,a;return y==="BT"?(o=d((t,n)=>t<=n,"comparisonFunc"),a=1/0):(o=d((t,n)=>t>=n,"comparisonFunc"),a=0),r.forEach(t=>{let n=y==="TB"||y=="BT"?C.get(t)?.y:C.get(t)?.x;n!==void 0&&o(n,a)&&(e=t,a=n)}),e},"findClosestParent"),He=d(r=>{let e="",o=1/0;return r.forEach(a=>{let t=C.get(a).y;t<=o&&(e=a,o=t)}),e||void 0},"findClosestParentBT"),Se=d((r,e,o)=>{let a=o,t=o,n=[];r.forEach(m=>{let s=e.get(m);if(!s)throw new Error(`Commit not found for key ${m}`);s.parents.length?(a=Ne(s),t=Math.max(a,t)):n.push(s),_e(s,a)}),a=t,n.forEach(m=>{je(m,a,o)}),r.forEach(m=>{let s=e.get(m);if(s?.parents.length){let g=He(s.parents);a=C.get(g).y-D,a<=t&&(t=a);let p=b.get(s.branch).pos,h=a-P;C.set(s.id,{x:p,y:h})}})},"setParallelBTPos"),We=d(r=>{let e=me(r.parents.filter(a=>a!==null));if(!e)throw new Error(`Closest parent not found for commit ${r.id}`);let o=C.get(e)?.y;if(o===void 0)throw new Error(`Closest parent position not found for commit ${r.id}`);return o},"findClosestParentPos"),Ne=d(r=>We(r)+D,"calculateCommitPosition"),_e=d((r,e)=>{let o=b.get(r.branch);if(!o)throw new Error(`Branch not found for commit ${r.id}`);let a=o.pos,t=e+P;return C.set(r.id,{x:a,y:t}),{x:a,y:t}},"setCommitPosition"),je=d((r,e,o)=>{let a=b.get(r.branch);if(!a)throw new Error(`Branch not found for commit ${r.id}`);let t=e+o,n=a.pos;C.set(r.id,{x:n,y:t})},"setRootPosition"),Fe=d((r,e,o,a,t,n)=>{if(n===u.HIGHLIGHT)r.append("rect").attr("x",o.x-10).attr("y",o.y-10).attr("width",20).attr("height",20).attr("class",`commit ${e.id} commit-highlight${t%G} ${a}-outer`),r.append("rect").attr("x",o.x-6).attr("y",o.y-6).attr("width",12).attr("height",12).attr("class",`commit ${e.id} commit${t%G} ${a}-inner`);else if(n===u.CHERRY_PICK)r.append("circle").attr("cx",o.x).attr("cy",o.y).attr("r",10).attr("class",`commit ${e.id} ${a}`),r.append("circle").attr("cx",o.x-3).attr("cy",o.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${a}`),r.append("circle").attr("cx",o.x+3).attr("cy",o.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${a}`),r.append("line").attr("x1",o.x+3).attr("y1",o.y+1).attr("x2",o.x).attr("y2",o.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${a}`),r.append("line").attr("x1",o.x-3).attr("y1",o.y+1).attr("x2",o.x).attr("y2",o.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${a}`);else{let m=r.append("circle");if(m.attr("cx",o.x),m.attr("cy",o.y),m.attr("r",e.type===u.MERGE?9:10),m.attr("class",`commit ${e.id} commit${t%G}`),n===u.MERGE){let s=r.append("circle");s.attr("cx",o.x),s.attr("cy",o.y),s.attr("r",6),s.attr("class",`commit ${a} ${e.id} commit${t%G}`)}n===u.REVERSE&&r.append("path").attr("d",`M ${o.x-5},${o.y-5}L${o.x+5},${o.y+5}M${o.x-5},${o.y+5}L${o.x+5},${o.y-5}`).attr("class",`commit ${a} ${e.id} commit${t%G}`)}},"drawCommitBullet"),ze=d((r,e,o,a)=>{if(e.type!==u.CHERRY_PICK&&(e.customId&&e.type===u.MERGE||e.type!==u.MERGE)&&w?.showCommitLabel){let t=r.append("g"),n=t.insert("rect").attr("class","commit-label-bkg"),m=t.append("text").attr("x",a).attr("y",o.y+25).attr("class","commit-label").text(e.id),s=m.node()?.getBBox();if(s&&(n.attr("x",o.posWithOffset-s.width/2-T).attr("y",o.y+13.5).attr("width",s.width+2*T).attr("height",s.height+2*T),y==="TB"||y==="BT"?(n.attr("x",o.x-(s.width+4*k+5)).attr("y",o.y-12),m.attr("x",o.x-(s.width+4*k)).attr("y",o.y+s.height-12)):m.attr("x",o.posWithOffset-s.width/2),w.rotateCommitLabel))if(y==="TB"||y==="BT")m.attr("transform","rotate(-45, "+o.x+", "+o.y+")"),n.attr("transform","rotate(-45, "+o.x+", "+o.y+")");else{let g=-7.5-(s.width+10)/25*9.5,p=10+s.width/25*8.5;t.attr("transform","translate("+g+", "+p+") rotate(-45, "+a+", "+o.y+")")}}},"drawCommitLabel"),Ke=d((r,e,o,a)=>{if(e.tags.length>0){let t=0,n=0,m=0,s=[];for(let g of e.tags.reverse()){let p=r.insert("polygon"),h=r.append("circle"),f=r.append("text").attr("y",o.y-16-t).attr("class","tag-label").text(g),c=f.node()?.getBBox();if(!c)throw new Error("Tag bbox not found");n=Math.max(n,c.width),m=Math.max(m,c.height),f.attr("x",o.posWithOffset-c.width/2),s.push({tag:f,hole:h,rect:p,yOffset:t}),t+=20}for(let{tag:g,hole:p,rect:h,yOffset:f}of s){let c=m/2,l=o.y-19.2-f;if(h.attr("class","tag-label-bkg").attr("points",`
      ${a-n/2-k/2},${l+T}  
      ${a-n/2-k/2},${l-T}
      ${o.posWithOffset-n/2-k},${l-c-T}
      ${o.posWithOffset+n/2+k},${l-c-T}
      ${o.posWithOffset+n/2+k},${l+c+T}
      ${o.posWithOffset-n/2-k},${l+c+T}`),p.attr("cy",l).attr("cx",a-n/2+k/2).attr("r",1.5).attr("class","tag-hole"),y==="TB"||y==="BT"){let x=a+f;h.attr("class","tag-label-bkg").attr("points",`
        ${o.x},${x+2}
        ${o.x},${x-2}
        ${o.x+P},${x-c-2}
        ${o.x+P+n+4},${x-c-2}
        ${o.x+P+n+4},${x+c+2}
        ${o.x+P},${x+c+2}`).attr("transform","translate(12,12) rotate(45, "+o.x+","+a+")"),p.attr("cx",o.x+k/2).attr("cy",x).attr("transform","translate(12,12) rotate(45, "+o.x+","+a+")"),g.attr("x",o.x+5).attr("y",x+3).attr("transform","translate(14,14) rotate(45, "+o.x+","+a+")")}}}},"drawCommitTags"),Ue=d(r=>{switch(r.customType??r.type){case u.NORMAL:return"commit-normal";case u.REVERSE:return"commit-reverse";case u.HIGHLIGHT:return"commit-highlight";case u.MERGE:return"commit-merge";case u.CHERRY_PICK:return"commit-cherry-pick";default:return"commit-normal"}},"getCommitClassType"),Ve=d((r,e,o,a)=>{let t={x:0,y:0};if(r.parents.length>0){let n=me(r.parents);if(n){let m=a.get(n)??t;return e==="TB"?m.y+D:e==="BT"?(a.get(r.id)??t).y-D:m.x+D}}else return e==="TB"?R:e==="BT"?(a.get(r.id)??t).y-D:0;return 0},"calculatePosition"),Ye=d((r,e,o)=>{let a=y==="BT"&&o?e:e+P,t=y==="TB"||y==="BT"?a:b.get(r.branch)?.pos,n=y==="TB"||y==="BT"?b.get(r.branch)?.pos:a;if(n===void 0||t===void 0)throw new Error(`Position were undefined for commit ${r.id}`);return{x:n,y:t,posWithOffset:a}},"getCommitPosition"),ie=d((r,e,o)=>{if(!w)throw new Error("GitGraph config not found");let a=r.append("g").attr("class","commit-bullets"),t=r.append("g").attr("class","commit-labels"),n=y==="TB"||y==="BT"?R:0,m=[...e.keys()],s=w?.parallelCommits??!1,g=d((h,f)=>{let c=e.get(h)?.seq,l=e.get(f)?.seq;return c!==void 0&&l!==void 0?c-l:0},"sortKeys"),p=m.sort(g);y==="BT"&&(s&&Se(p,e,n),p=p.reverse()),p.forEach(h=>{let f=e.get(h);if(!f)throw new Error(`Commit not found for key ${h}`);s&&(n=Ve(f,y,n,C));let c=Ye(f,n,s);if(o){let l=Ue(f),x=f.customType??f.type,I=b.get(f.branch)?.index??0;Fe(a,f,c,l,I,x),ze(t,f,c,n),Ke(t,f,c,n)}y==="TB"||y==="BT"?C.set(f.id,{x:c.x,y:c.posWithOffset}):C.set(f.id,{x:c.posWithOffset,y:c.y}),n=y==="BT"&&s?n+D:n+D+P,n>E&&(E=n)})},"drawCommits"),Ze=d((r,e,o,a,t)=>{let m=(y==="TB"||y==="BT"?o.x<a.x:o.y<a.y)?e.branch:r.branch,s=d(p=>p.branch===m,"isOnBranchToGetCurve"),g=d(p=>p.seq>r.seq&&p.seq<e.seq,"isBetweenCommits");return[...t.values()].some(p=>g(p)&&s(p))},"shouldRerouteArrow"),O=d((r,e,o=0)=>{let a=r+Math.abs(r-e)/2;if(o>5)return a;if(A.every(m=>Math.abs(m-a)>=10))return A.push(a),a;let n=Math.abs(r-e);return O(r,e-n/5,o+1)},"findLane"),Je=d((r,e,o,a)=>{let t=C.get(e.id),n=C.get(o.id);if(t===void 0||n===void 0)throw new Error(`Commit positions not found for commits ${e.id} and ${o.id}`);let m=Ze(e,o,t,n,a),s="",g="",p=0,h=0,f=b.get(o.branch)?.index;o.type===u.MERGE&&e.id!==o.parents[0]&&(f=b.get(e.branch)?.index);let c;if(m){s="A 10 10, 0, 0, 0,",g="A 10 10, 0, 0, 1,",p=10,h=10;let l=t.y<n.y?O(t.y,n.y):O(n.y,t.y),x=t.x<n.x?O(t.x,n.x):O(n.x,t.x);y==="TB"?t.x<n.x?c=`M ${t.x} ${t.y} L ${x-p} ${t.y} ${g} ${x} ${t.y+h} L ${x} ${n.y-p} ${s} ${x+h} ${n.y} L ${n.x} ${n.y}`:(f=b.get(e.branch)?.index,c=`M ${t.x} ${t.y} L ${x+p} ${t.y} ${s} ${x} ${t.y+h} L ${x} ${n.y-p} ${g} ${x-h} ${n.y} L ${n.x} ${n.y}`):y==="BT"?t.x<n.x?c=`M ${t.x} ${t.y} L ${x-p} ${t.y} ${s} ${x} ${t.y-h} L ${x} ${n.y+p} ${g} ${x+h} ${n.y} L ${n.x} ${n.y}`:(f=b.get(e.branch)?.index,c=`M ${t.x} ${t.y} L ${x+p} ${t.y} ${g} ${x} ${t.y-h} L ${x} ${n.y+p} ${s} ${x-h} ${n.y} L ${n.x} ${n.y}`):t.y<n.y?c=`M ${t.x} ${t.y} L ${t.x} ${l-p} ${s} ${t.x+h} ${l} L ${n.x-p} ${l} ${g} ${n.x} ${l+h} L ${n.x} ${n.y}`:(f=b.get(e.branch)?.index,c=`M ${t.x} ${t.y} L ${t.x} ${l+p} ${g} ${t.x+h} ${l} L ${n.x-p} ${l} ${s} ${n.x} ${l-h} L ${n.x} ${n.y}`)}else s="A 20 20, 0, 0, 0,",g="A 20 20, 0, 0, 1,",p=20,h=20,y==="TB"?(t.x<n.x&&(o.type===u.MERGE&&e.id!==o.parents[0]?c=`M ${t.x} ${t.y} L ${t.x} ${n.y-p} ${s} ${t.x+h} ${n.y} L ${n.x} ${n.y}`:c=`M ${t.x} ${t.y} L ${n.x-p} ${t.y} ${g} ${n.x} ${t.y+h} L ${n.x} ${n.y}`),t.x>n.x&&(s="A 20 20, 0, 0, 0,",g="A 20 20, 0, 0, 1,",p=20,h=20,o.type===u.MERGE&&e.id!==o.parents[0]?c=`M ${t.x} ${t.y} L ${t.x} ${n.y-p} ${g} ${t.x-h} ${n.y} L ${n.x} ${n.y}`:c=`M ${t.x} ${t.y} L ${n.x+p} ${t.y} ${s} ${n.x} ${t.y+h} L ${n.x} ${n.y}`),t.x===n.x&&(c=`M ${t.x} ${t.y} L ${n.x} ${n.y}`)):y==="BT"?(t.x<n.x&&(o.type===u.MERGE&&e.id!==o.parents[0]?c=`M ${t.x} ${t.y} L ${t.x} ${n.y+p} ${g} ${t.x+h} ${n.y} L ${n.x} ${n.y}`:c=`M ${t.x} ${t.y} L ${n.x-p} ${t.y} ${s} ${n.x} ${t.y-h} L ${n.x} ${n.y}`),t.x>n.x&&(s="A 20 20, 0, 0, 0,",g="A 20 20, 0, 0, 1,",p=20,h=20,o.type===u.MERGE&&e.id!==o.parents[0]?c=`M ${t.x} ${t.y} L ${t.x} ${n.y+p} ${s} ${t.x-h} ${n.y} L ${n.x} ${n.y}`:c=`M ${t.x} ${t.y} L ${n.x-p} ${t.y} ${s} ${n.x} ${t.y-h} L ${n.x} ${n.y}`),t.x===n.x&&(c=`M ${t.x} ${t.y} L ${n.x} ${n.y}`)):(t.y<n.y&&(o.type===u.MERGE&&e.id!==o.parents[0]?c=`M ${t.x} ${t.y} L ${n.x-p} ${t.y} ${g} ${n.x} ${t.y+h} L ${n.x} ${n.y}`:c=`M ${t.x} ${t.y} L ${t.x} ${n.y-p} ${s} ${t.x+h} ${n.y} L ${n.x} ${n.y}`),t.y>n.y&&(o.type===u.MERGE&&e.id!==o.parents[0]?c=`M ${t.x} ${t.y} L ${n.x-p} ${t.y} ${s} ${n.x} ${t.y-h} L ${n.x} ${n.y}`:c=`M ${t.x} ${t.y} L ${t.x} ${n.y+p} ${g} ${t.x+h} ${n.y} L ${n.x} ${n.y}`),t.y===n.y&&(c=`M ${t.x} ${t.y} L ${n.x} ${n.y}`));if(c===void 0)throw new Error("Line definition not found");r.append("path").attr("d",c).attr("class","arrow arrow"+f%G)},"drawArrow"),Xe=d((r,e)=>{let o=r.append("g").attr("class","commit-arrows");[...e.keys()].forEach(a=>{let t=e.get(a);t.parents&&t.parents.length>0&&t.parents.forEach(n=>{Je(o,e.get(n),t,e)})})},"drawArrows"),Qe=d((r,e)=>{let o=r.append("g");e.forEach((a,t)=>{let n=t%G,m=b.get(a.name)?.pos;if(m===void 0)throw new Error(`Position not found for branch ${a.name}`);let s=o.append("line");s.attr("x1",0),s.attr("y1",m),s.attr("x2",E),s.attr("y2",m),s.attr("class","branch branch"+n),y==="TB"?(s.attr("y1",R),s.attr("x1",m),s.attr("y2",E),s.attr("x2",m)):y==="BT"&&(s.attr("y1",E),s.attr("x1",m),s.attr("y2",R),s.attr("x2",m)),A.push(m);let g=a.name,p=ce(g),h=o.insert("rect"),c=o.insert("g").attr("class","branchLabel").insert("g").attr("class","label branch-label"+n);c.node().appendChild(p);let l=p.getBBox();h.attr("class","branchLabelBkg label"+n).attr("rx",4).attr("ry",4).attr("x",-l.width-4-(w?.rotateCommitLabel===!0?30:0)).attr("y",-l.height/2+8).attr("width",l.width+18).attr("height",l.height+4),c.attr("transform","translate("+(-l.width-14-(w?.rotateCommitLabel===!0?30:0))+", "+(m-l.height/2-1)+")"),y==="TB"?(h.attr("x",m-l.width/2-10).attr("y",0),c.attr("transform","translate("+(m-l.width/2-5)+", 0)")):y==="BT"?(h.attr("x",m-l.width/2-10).attr("y",E),c.attr("transform","translate("+(m-l.width/2-5)+", "+E+")")):h.attr("transform","translate(-19, "+(m-l.height/2)+")")})},"drawBranches"),et=d(function(r,e,o,a,t){return b.set(r,{pos:e,index:o}),e+=50+(t?40:0)+(y==="TB"||y==="BT"?a.width/2:0),e},"setBranchPosition"),tt=d(function(r,e,o,a){if(qe(),$.debug("in gitgraph renderer",r+`
`,"id:",e,o),!w)throw new Error("GitGraph config not found");let t=w.rotateCommitLabel??!1,n=a.db;L=n.getCommits();let m=n.getBranchesAsObjArray();y=n.getDirection();let s=Y(`[id="${e}"]`),g=0;m.forEach((p,h)=>{let f=ce(p.name),c=s.append("g"),l=c.insert("g").attr("class","branchLabel"),x=l.insert("g").attr("class","label branch-label");x.node()?.appendChild(f);let I=f.getBBox();g=et(p.name,g,h,I,t),x.remove(),l.remove(),c.remove()}),ie(s,L,!1),w.showBranches&&Qe(s,m),Xe(s,L),ie(s,L,!0),X.insertTitle(s,"gitTitleText",w.titleTopMargin??0,n.getDiagramTitle()),V(void 0,s,w.diagramPadding,w.useMaxWidth)},"draw"),de={draw:tt};var rt=d(r=>`
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: lightgrey;
    color: lightgrey;
    font-family: 'trebuchet ms', verdana, arial, sans-serif;
    font-family: var(--mermaid-font-family);
  }
  ${[0,1,2,3,4,5,6,7].map(e=>`
        .branch-label${e} { fill: ${r["gitBranchLabel"+e]}; }
        .commit${e} { stroke: ${r["git"+e]}; fill: ${r["git"+e]}; }
        .commit-highlight${e} { stroke: ${r["gitInv"+e]}; fill: ${r["gitInv"+e]}; }
        .label${e}  { fill: ${r["git"+e]}; }
        .arrow${e} { stroke: ${r["git"+e]}; }
        `).join(`
`)}

  .branch {
    stroke-width: 1;
    stroke: ${r.lineColor};
    stroke-dasharray: 2;
  }
  .commit-label { font-size: ${r.commitLabelFontSize}; fill: ${r.commitLabelColor};}
  .commit-label-bkg { font-size: ${r.commitLabelFontSize}; fill: ${r.commitLabelBackground}; opacity: 0.5; }
  .tag-label { font-size: ${r.tagLabelFontSize}; fill: ${r.tagLabelColor};}
  .tag-label-bkg { fill: ${r.tagLabelBackground}; stroke: ${r.tagLabelBorder}; }
  .tag-hole { fill: ${r.textColor}; }

  .commit-merge {
    stroke: ${r.primaryColor};
    fill: ${r.primaryColor};
  }
  .commit-reverse {
    stroke: ${r.primaryColor};
    fill: ${r.primaryColor};
    stroke-width: 3;
  }
  .commit-highlight-outer {
  }
  .commit-highlight-inner {
    stroke: ${r.primaryColor};
    fill: ${r.primaryColor};
  }

  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}
  .gitTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${r.textColor};
  }
`,"getStyles"),pe=rt;var Rt={parser:se,db:v,renderer:de,styles:pe};export{Rt as diagram};
