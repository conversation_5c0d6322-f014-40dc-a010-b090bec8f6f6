{"name": "match-at", "version": "0.1.1", "description": "Relocatable regular expressions.", "repository": "sophiebits/match-at", "main": "lib/matchAt.js", "files": ["lib/"], "devDependencies": {"babel-cli": "^6.26.0", "babel-jest": "^21.0.0", "babel-plugin-syntax-flow": "^6.18.0", "babel-plugin-transform-flow-strip-types": "^6.22.0", "jest": "^21.0.1"}, "scripts": {"prepublish": "babel --no-babelrc --plugins syntax-flow -d lib/ src/ && mv lib/matchAt.js lib/matchAt.js.flow && babel -d lib/ src/", "test": "jest"}, "babel": {"plugins": ["transform-flow-strip-types"]}}