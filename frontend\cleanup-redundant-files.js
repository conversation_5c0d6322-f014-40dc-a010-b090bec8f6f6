/**
 * iFlytek 系统简化 - 冗余文件清理脚本
 * 移除不必要的样式文件和工具文件
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要移除的冗余样式文件
const redundantStyleFiles = [
  'src/styles/ui-emergency-fix.css',
  'src/styles/final-ui-polish.css',
  'src/styles/background-force-fix.css',
  'src/styles/chinese-font-background-fix.css',
  'src/styles/chinese-text-alignment.css',
  'src/styles/competitor-inspired-ui.css',
  'src/styles/comprehensive-purple-text-fix.css',
  'src/styles/critical-alignment-fixes.css',
  'src/styles/enhanced-chart-system.css',
  'src/styles/enhanced-color-system.css',
  'src/styles/enterprise-dashboard-fixes.css',
  'src/styles/force-icon-alignment.css',
  'src/styles/force-overlap-fix.css',
  'src/styles/high-contrast-ui-fix.css',
  'src/styles/homepage-display-fix.css',
  'src/styles/homepage-purple-text-fix.css',
  'src/styles/intelligent-dynamic-effects.css',
  'src/styles/interview-wcag-optimization.css',
  'src/styles/interviewing-page-color-optimization.css',
  'src/styles/layout-consistency.css',
  'src/styles/layout-display-fix.css',
  'src/styles/module-themes.css',
  'src/styles/multimodal-contrast-optimization.css',
  'src/styles/precise-alignment-fixes.css',
  'src/styles/report-list-optimization.css',
  'src/styles/text-container-overlap-fix.css',
  'src/styles/text-visibility-fixes.css',
  'src/styles/ui-alignment-fixes.css',
  'src/styles/ultimate-button-fix.css',
  'src/styles/wcag-contrast-fix.css',
  'src/styles/wcag-contrast-variables.css',
  'src/styles/wcag-optimized-colors.css',
  'src/styles/design-system.css.backup'
]

// 需要移除的冗余工具文件
const redundantUtilFiles = [
  'src/utils/alignmentChecker.js',
  'src/utils/color-contrast-validator.js',
  'src/utils/color-optimization-tool.js',
  'src/utils/contrast-validation-tool.js',
  'src/utils/contrastValidator.js',
  'src/utils/directAlignmentFix.js',
  'src/utils/echartsErrorDetector.js',
  'src/utils/enterpriseFunctionTest.js',
  'src/utils/enterpriseRouteValidator.js',
  'src/utils/exportFunctionTest.js',
  'src/utils/icon-validation.js',
  'src/utils/iconEnhancer.js',
  'src/utils/iflytekBrandConsistency.js',
  'src/utils/iflytekSparkTest.js',
  'src/utils/interviewFunctionTest.js',
  'src/utils/performance-optimizer.js',
  'src/utils/quickServiceTest.js',
  'src/utils/resize-observer-fix.js',
  'src/utils/resize-observer-monitor.js',
  'src/utils/sparkServiceValidator.js',
  'src/utils/systemCompatibilityChecker.js',
  'src/utils/systemOptimization.js',
  'src/utils/systemOptimizer.js',
  'src/utils/video-automation-tool.js'
]

// 需要移除的冗余服务文件
const redundantServiceFiles = [
  'src/services/competitorAnalysisOptimizer.js',
  'src/services/comprehensiveTestRunner.js',
  'src/services/enhancedAIInsightsService.js',
  'src/services/enhancedDataExportService.js',
  'src/services/enhancedLearningPathService.js',
  'src/services/enhancedMediaService.js',
  'src/services/enhancedTimelineService.js',
  'src/services/frontendPerformanceService.js',
  'src/services/integrationTestService.js',
  'src/services/multimodalDemoContentService.js',
  'src/services/systemPerformanceOptimizer.js',
  'src/services/systemTestService.js'
]

// 需要移除的冗余测试页面
const redundantTestPages = [
  'src/views/AIResponseTestPage.vue',
  'src/views/ChartDemoPage.vue',
  'src/views/CompetitorIntegrationDemo.vue',
  'src/views/ComponentTest.vue',
  'src/views/EChartsTestPage.vue',
  'src/views/EnhancedBranchDiagramDemo.vue',
  'src/views/EnhancedInteractiveDemoPage.vue',
  'src/views/HomepageDisplayTestPage.vue',
  'src/views/IconTestPage.vue',
  'src/views/IntegrationTestPage.vue',
  'src/views/LayoutFixVerificationPage.vue',
  'src/views/OptimizedBranchDiagramDemo.vue',
  'src/views/PositionManagementTest.vue',
  'src/views/ResizeObserverTestPage.vue',
  'src/views/RouteTestPage.vue',
  'src/views/SparkServiceTest.vue',
  'src/views/SparkTestPage.vue',
  'src/views/StatusCheck.vue',
  'src/views/TestEnterprise.vue',
  'src/views/UIAlignmentTestPage.vue'
]

// 需要移除的根目录文件
const redundantRootFiles = [
  'emergency-text-visibility-fix.css',
  'ui-fix-verification.js',
  'ui-fix-test.html',
  'UI_FIX_COMPLETION_REPORT.md',
  'color-contrast-analyzer.js',
  'comprehensive-icon-check.js',
  'check-duplicate-imports.js',
  'check-icons.js',
  'brand-consistency-checker.js',
  'icon-size-analyzer.js',
  'final-icon-check.js',
  'UI_COLOR_CONTRAST_OPTIMIZATION.md',
  'UI_OPTIMIZATION_TROUBLESHOOTING_REPORT.md'
]

function removeFile(filePath) {
  const fullPath = path.join(__dirname, filePath)
  try {
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath)
      console.log(`✅ 已删除: ${filePath}`)
      return true
    } else {
      console.log(`⚠️ 文件不存在: ${filePath}`)
      return false
    }
  } catch (error) {
    console.error(`❌ 删除失败: ${filePath} - ${error.message}`)
    return false
  }
}

function cleanupFiles() {
  console.log('🧹 开始清理冗余文件...\n')
  
  let removedCount = 0
  let totalCount = 0
  
  console.log('📁 清理样式文件:')
  redundantStyleFiles.forEach(file => {
    totalCount++
    if (removeFile(file)) removedCount++
  })
  
  console.log('\n🔧 清理工具文件:')
  redundantUtilFiles.forEach(file => {
    totalCount++
    if (removeFile(file)) removedCount++
  })
  
  console.log('\n⚙️ 清理服务文件:')
  redundantServiceFiles.forEach(file => {
    totalCount++
    if (removeFile(file)) removedCount++
  })
  
  console.log('\n🧪 清理测试页面:')
  redundantTestPages.forEach(file => {
    totalCount++
    if (removeFile(file)) removedCount++
  })
  
  console.log('\n📄 清理根目录文件:')
  redundantRootFiles.forEach(file => {
    totalCount++
    if (removeFile(file)) removedCount++
  })
  
  console.log(`\n📊 清理完成:`)
  console.log(`- 总计文件: ${totalCount}`)
  console.log(`- 成功删除: ${removedCount}`)
  console.log(`- 清理率: ${Math.round(removedCount / totalCount * 100)}%`)
  
  return { total: totalCount, removed: removedCount }
}

// 执行清理
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanupFiles()
}

export { cleanupFiles }
