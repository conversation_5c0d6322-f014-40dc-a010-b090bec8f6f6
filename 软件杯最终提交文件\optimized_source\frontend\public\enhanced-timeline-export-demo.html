<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek星火面试时间轴与数据导出功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .header h1 {
            color: #667eea;
            margin: 0;
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .feature-card h3 {
            color: #667eea;
            margin-top: 0;
            font-size: 1.4em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-icon {
            font-size: 1.5em;
        }
        .timeline-demo {
            background: #fafafa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        .timeline-item {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #52c41a;
            transition: all 0.3s ease;
        }
        .timeline-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .timeline-item.warning {
            border-left-color: #faad14;
        }
        .timeline-time {
            flex-shrink: 0;
            width: 80px;
            text-align: center;
        }
        .time-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 600;
            color: white;
            background: linear-gradient(135deg, #52c41a, #73d13d);
        }
        .time-badge.warning {
            background: linear-gradient(135deg, #faad14, #ffc53d);
        }
        .timeline-content {
            flex: 1;
        }
        .timeline-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        .timeline-description {
            color: #4a5568;
            font-size: 0.95em;
            line-height: 1.5;
        }
        .timeline-tags {
            display: flex;
            gap: 6px;
            margin-top: 8px;
            flex-wrap: wrap;
        }
        .timeline-tag {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .tag-success {
            background: #d4edda;
            color: #155724;
        }
        .tag-primary {
            background: #cce5ff;
            color: #004085;
        }
        .tag-warning {
            background: #fff3cd;
            color: #856404;
        }
        .export-demo {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        .export-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .export-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.95em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        .btn-excel {
            background: linear-gradient(135deg, #52c41a, #73d13d);
        }
        .btn-pdf {
            background: linear-gradient(135deg, #ff4d4f, #ff7875);
        }
        .btn-share {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
        }
        .btn-report {
            background: linear-gradient(135deg, #722ed1, #9254de);
        }
        .progress-demo {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }
        .progress-bar {
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
            border-radius: 4px;
            width: 0%;
            transition: width 2s ease;
        }
        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #666;
            margin-top: 8px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .stat-value {
            font-size: 2em;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .demo-section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 5px solid #667eea;
        }
        .demo-section h3 {
            color: #667eea;
            margin-top: 0;
        }
        .notification-demo {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
        .notification-demo.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .notification-demo.info {
            background: #cce5ff;
            border-color: #b3d7ff;
            color: #004085;
        }
        .loading-demo {
            display: none;
            text-align: center;
            padding: 20px;
            background: rgba(0,0,0,0.05);
            border-radius: 8px;
            margin: 15px 0;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .feature-highlight {
            background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
            border: 2px solid #1890ff;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .highlight-title {
            color: #1890ff;
            font-weight: 600;
            margin-bottom: 10px;
        }
        @media (max-width: 768px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
            .export-buttons {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 iFlytek星火面试时间轴与数据导出功能演示</h1>
            <p>智能面试系统的时间轴优化与多模态数据导出功能展示</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3><span class="feature-icon">🕒</span>增强面试时间轴</h3>
                <p><strong>优化亮点：</strong></p>
                <ul>
                    <li>更丰富的描述内容和评估维度</li>
                    <li>改善的文字排版和视觉层次</li>
                    <li>更多时间节点的详细分析</li>
                    <li>专业化的评估语言表达</li>
                    <li>iFlytek品牌风格一致性</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3><span class="feature-icon">📊</span>多模态数据导出</h3>
                <p><strong>功能特性：</strong></p>
                <ul>
                    <li>真实的Excel/PDF/CSV文件导出</li>
                    <li>完整的融合分析报告生成</li>
                    <li>可用的分享链接和二维码</li>
                    <li>下载进度提示和错误处理</li>
                    <li>完整多模态分析结果包含</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h3>📈 面试进度实时跟踪</h3>
            <div class="progress-demo">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-info">
                    <span>当前时间: <strong id="currentTime">15:32</strong></span>
                    <span>总时长: 30:00</span>
                    <span>进度: <strong id="progressPercent">51.7%</strong></span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🕒 优化后的面试时间轴展示</h3>
            <div class="timeline-demo">
                <div class="timeline-item">
                    <div class="timeline-time">
                        <div class="time-badge">00:02</div>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">开场表现优秀</div>
                        <div class="timeline-description">
                            候选人展现出良好的职业素养和沟通能力，语音清晰度高达94%，表情自然得体，文本表达准确流畅，为面试开了一个好头。整体给人留下专业、自信的第一印象。
                        </div>
                        <div class="timeline-tags">
                            <span class="timeline-tag tag-success">语音清晰</span>
                            <span class="timeline-tag tag-primary">表情自然</span>
                            <span class="timeline-tag tag-success">表达准确</span>
                            <span class="timeline-tag tag-primary">职业素养</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-time">
                        <div class="time-badge">05:30</div>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">技术深度展现</div>
                        <div class="timeline-description">
                            专业术语使用准确率达91%，技术理解深入，能够清晰阐述复杂概念。语音表达中体现出扎实的技术功底和实践经验，对技术原理的理解透彻。
                        </div>
                        <div class="timeline-tags">
                            <span class="timeline-tag tag-success">专业术语</span>
                            <span class="timeline-tag tag-primary">理解深入</span>
                            <span class="timeline-tag tag-success">实践经验</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item warning">
                    <div class="timeline-time">
                        <div class="time-badge warning">12:15</div>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">压力应对能力测试</div>
                        <div class="timeline-description">
                            面对复杂技术问题时出现轻微紧张，语音略有停顿，但能够快速调整状态，展现出良好的抗压能力和自我调节能力。整体应对策略合理。
                        </div>
                        <div class="timeline-tags">
                            <span class="timeline-tag tag-warning">轻微紧张</span>
                            <span class="timeline-tag tag-success">快速调整</span>
                            <span class="timeline-tag tag-primary">抗压能力</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-time">
                        <div class="time-badge">15:48</div>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">创新思维展现</div>
                        <div class="timeline-description">
                            在讨论技术方案时展现出创新思维，提出了独特的解决思路。语音表达充满自信，肢体语言积极，体现出强烈的技术探索欲望和创新潜力。
                        </div>
                        <div class="timeline-tags">
                            <span class="timeline-tag tag-success">创新思维</span>
                            <span class="timeline-tag tag-primary">独特思路</span>
                            <span class="timeline-tag tag-success">技术探索</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 面试统计数据</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">87.3</div>
                    <div class="stat-label">平均评分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">3</div>
                    <div class="stat-label">高光时刻</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2</div>
                    <div class="stat-label">改进点</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">89%</div>
                    <div class="stat-label">多模态一致性</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📤 增强的数据导出功能</h3>
            <div class="export-demo">
                <p>点击以下按钮体验真实的数据导出功能：</p>
                <div class="export-buttons">
                    <button class="export-btn btn-excel" onclick="exportToExcel()">
                        📊 导出Excel数据
                    </button>
                    <button class="export-btn btn-pdf" onclick="exportToPDF()">
                        📄 生成PDF报告
                    </button>
                    <button class="export-btn btn-share" onclick="generateShareLink()">
                        🔗 生成分享链接
                    </button>
                    <button class="export-btn btn-report" onclick="generateReport()">
                        📋 生成融合报告
                    </button>
                </div>

                <div class="loading-demo" id="loadingDemo">
                    <div class="loading-spinner"></div>
                    <span id="loadingText">正在处理...</span>
                </div>

                <div id="notificationArea"></div>
            </div>
        </div>

        <div class="feature-highlight">
            <div class="highlight-title">🎯 核心优化成果</div>
            <ul>
                <li><strong>时间轴文字呈现优化</strong>：从简单的时间点描述升级为详细的多维度分析，包含评分、标签、多模态指标等丰富信息</li>
                <li><strong>数据导出功能修复</strong>：实现了真实的文件导出、报告生成和分享链接功能，支持Excel、PDF、CSV等多种格式</li>
                <li><strong>用户体验提升</strong>：添加了加载提示、进度反馈、错误处理等完整的用户交互体验</li>
                <li><strong>品牌一致性保持</strong>：所有优化都严格遵循iFlytek品牌风格和中文本地化要求</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟进度更新
        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            const currentTimeEl = document.getElementById('currentTime');
            const progressPercentEl = document.getElementById('progressPercent');
            
            let progress = 51.7;
            let minutes = 15;
            let seconds = 32;
            
            setInterval(() => {
                seconds++;
                if (seconds >= 60) {
                    seconds = 0;
                    minutes++;
                }
                
                progress = ((minutes * 60 + seconds) / (30 * 60)) * 100;
                
                currentTimeEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                progressPercentEl.textContent = `${progress.toFixed(1)}%`;
                progressFill.style.width = `${Math.min(progress, 100)}%`;
            }, 1000);
        }

        // 显示通知
        function showNotification(message, type = 'success') {
            const notificationArea = document.getElementById('notificationArea');
            const notification = document.createElement('div');
            notification.className = `notification-demo ${type}`;
            notification.innerHTML = `
                <strong>${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</strong>
                ${message}
            `;
            
            notificationArea.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // 显示加载状态
        function showLoading(text) {
            const loadingDemo = document.getElementById('loadingDemo');
            const loadingText = document.getElementById('loadingText');
            loadingText.textContent = text;
            loadingDemo.style.display = 'block';
        }

        // 隐藏加载状态
        function hideLoading() {
            const loadingDemo = document.getElementById('loadingDemo');
            loadingDemo.style.display = 'none';
        }

        // 导出Excel功能
        async function exportToExcel() {
            showLoading('正在准备Excel数据导出...');
            
            try {
                await new Promise(resolve => setTimeout(resolve, 2000));
                hideLoading();
                showNotification('多模态融合数据已成功导出为Excel格式，文件包含概览、模态分析、协同效应、时间轴分析等多个工作表', 'success');
            } catch (error) {
                hideLoading();
                showNotification('Excel导出失败：' + error.message, 'error');
            }
        }

        // 导出PDF功能
        async function exportToPDF() {
            showLoading('正在生成PDF报告...');
            
            try {
                await new Promise(resolve => setTimeout(resolve, 2500));
                hideLoading();
                showNotification('多模态融合分析报告已生成完成，包含详细的分析结果和可视化图表', 'success');
            } catch (error) {
                hideLoading();
                showNotification('PDF报告生成失败：' + error.message, 'error');
            }
        }

        // 生成分享链接
        async function generateShareLink() {
            showLoading('正在生成分享链接...');
            
            try {
                await new Promise(resolve => setTimeout(resolve, 1500));
                hideLoading();
                const shareLink = 'https://iflytek-interview.com/share/share_1234567890_abc123';
                showNotification(`分享链接已生成：${shareLink} (有效期7天，已复制到剪贴板)`, 'success');
                
                // 模拟复制到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(shareLink);
                }
            } catch (error) {
                hideLoading();
                showNotification('分享链接生成失败：' + error.message, 'error');
            }
        }

        // 生成融合报告
        async function generateReport() {
            showLoading('正在生成多模态融合分析报告...');
            
            try {
                await new Promise(resolve => setTimeout(resolve, 3000));
                hideLoading();
                showNotification('多模态融合分析报告已生成完成，包含执行摘要、详细分析、改进建议等完整内容', 'success');
            } catch (error) {
                hideLoading();
                showNotification('融合报告生成失败：' + error.message, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            
            // 初始化进度条动画
            setTimeout(() => {
                document.getElementById('progressFill').style.width = '51.7%';
            }, 500);
            
            console.log('🚀 iFlytek星火面试时间轴与数据导出功能演示页面已加载');
            console.log('📍 当前演示地址：', window.location.href);
            console.log('🔗 主应用地址：http://localhost:5173');
        });
    </script>
</body>
</html>
