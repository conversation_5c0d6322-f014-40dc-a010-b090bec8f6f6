#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软件杯比赛提交文件整理脚本
按照比赛要求分类整理文件到4个压缩包
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_submission_structure():
    """创建提交文件结构"""
    
    # 1. 86014454作品.zip - 可执行文件/安装包
    print("📦 整理作品安装包...")
    work_dir = "submission/86014454作品"
    
    # 复制可执行文件和启动脚本
    files_to_copy = [
        "启动服务器.bat",
        "完整启动指南.md", 
        "比赛评审运行指南.md",
        "start_system.py",
        "quick_system_check.py"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, work_dir)
    
    # 复制前端构建文件
    if os.path.exists("frontend/dist"):
        shutil.copytree("frontend/dist", f"{work_dir}/frontend_dist", dirs_exist_ok=True)
    
    # 复制后端运行文件
    backend_files = [
        "backend/run_server.py",
        "backend/simple_server.py", 
        "backend/requirements.txt",
        "backend/package.json"
    ]
    
    os.makedirs(f"{work_dir}/backend", exist_ok=True)
    for file in backend_files:
        if os.path.exists(file):
            shutil.copy2(file, f"{work_dir}/backend/")
    
    print("✅ 作品安装包整理完成")

def organize_source_code():
    """整理源码文件"""
    print("📦 整理源码文件...")
    source_dir = "submission/86014454源码"
    
    # 复制完整源码
    dirs_to_copy = [
        "frontend/src",
        "frontend/public", 
        "backend/app",
        "docs"
    ]
    
    for dir_path in dirs_to_copy:
        if os.path.exists(dir_path):
            dest_path = f"{source_dir}/{dir_path}"
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copytree(dir_path, dest_path, dirs_exist_ok=True)
    
    # 复制配置文件
    config_files = [
        "frontend/package.json",
        "frontend/vite.config.js",
        "frontend/vue.config.js",
        "backend/requirements.txt",
        "README.md"
    ]
    
    for file in config_files:
        if os.path.exists(file):
            dest_path = f"{source_dir}/{file}"
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(file, dest_path)
    
    print("✅ 源码文件整理完成")

def organize_presentation():
    """整理介绍材料"""
    print("📦 整理介绍材料...")
    intro_dir = "submission/86014454介绍"
    
    # 复制文档
    doc_files = [
        "docs/presentation.md",
        "docs/demo-video-script.md", 
        "docs/enhanced-ppt-outline.md",
        "docs/technical-documentation.md",
        "docs/system-design-overview.md",
        "FINAL_PROJECT_STATUS.md",
        "PROJECT_COMPLETION_REPORT.md"
    ]
    
    for file in doc_files:
        if os.path.exists(file):
            shutil.copy2(file, intro_dir)
    
    # 复制演示文件
    demo_files = [
        "frontend/multimodal-showcase-demo.html",
        "complete-validation-test.html"
    ]
    
    for file in demo_files:
        if os.path.exists(file):
            shutil.copy2(file, intro_dir)
    
    print("✅ 介绍材料整理完成")

def create_registration_template():
    """创建报名材料模板"""
    print("📦 创建报名材料模板...")
    reg_dir = "submission/86014454报名"
    
    # 创建报名表模板
    registration_content = """
# 软件杯比赛报名材料

## 参赛信息
- 参赛编号: 86014454
- 作品名称: 基于iFlytek Spark的多模态AI面试评估系统
- 参赛类别: 软件应用与开发

## 作品简介
本系统是一个基于iFlytek Spark大语言模型的智能面试评估平台，支持：
- 多技术领域面试（AI、大数据、物联网）
- 实时对话式面试体验
- 智能评估与反馈
- 企业级管理功能

## 技术栈
- 前端: Vue.js 3 + Element Plus
- 后端: Python Flask
- AI引擎: iFlytek Spark LLM
- 数据库: SQLite

## 创新点
1. 基于iFlytek Spark的智能对话面试
2. 多模态评估能力
3. 个性化学习路径推荐
4. 企业级批量面试管理

请在此文件夹中添加：
1. 正式报名表（PDF格式）
2. 学生证扫描件
3. 其他要求的证明材料
"""
    
    with open(f"{reg_dir}/报名材料说明.md", "w", encoding="utf-8") as f:
        f.write(registration_content)
    
    print("✅ 报名材料模板创建完成")

def create_zip_files():
    """创建压缩文件"""
    print("📦 创建压缩文件...")
    
    zip_configs = [
        ("submission/86014454作品", "86014454作品.zip"),
        ("submission/86014454源码", "86014454源码.zip"), 
        ("submission/86014454介绍", "86014454介绍.zip"),
        ("submission/86014454报名", "86014454报名.zip")
    ]
    
    for folder, zip_name in zip_configs:
        if os.path.exists(folder):
            with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(folder):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, folder)
                        zipf.write(file_path, arcname)
            print(f"✅ 创建 {zip_name}")
    
    print("🎉 所有压缩文件创建完成！")

def main():
    """主函数"""
    print("🚀 开始整理软件杯比赛提交文件...")
    
    # 创建目录结构
    os.makedirs("submission/86014454作品", exist_ok=True)
    os.makedirs("submission/86014454源码", exist_ok=True) 
    os.makedirs("submission/86014454介绍", exist_ok=True)
    os.makedirs("submission/86014454报名", exist_ok=True)
    
    # 整理各类文件
    create_submission_structure()
    organize_source_code()
    organize_presentation()
    create_registration_template()
    
    # 创建压缩文件
    create_zip_files()
    
    print("\n📋 提交文件清单:")
    print("1. 86014454作品.zip - 可执行文件和安装包")
    print("2. 86014454源码.zip - 完整源代码")
    print("3. 86014454介绍.zip - PPT、演示视频和文档")
    print("4. 86014454报名.zip - 报名表和学生证（需手动添加）")
    
    print("\n⚠️  请注意:")
    print("- 86014454报名.zip 中需要手动添加报名表和学生证")
    print("- 86014454介绍.zip 中需要添加PPT和演示视频")
    print("- 所有文件已按比赛要求命名和分类")

if __name__ == "__main__":
    main()
