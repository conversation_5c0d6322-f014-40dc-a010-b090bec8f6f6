<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="dataGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#722ed1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#eb2f96;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#dataGrad)"/>
  <circle cx="90" cy="70" r="12" fill="rgba(255,255,255,0.25)"/>
  <circle cx="310" cy="230" r="16" fill="rgba(255,255,255,0.2)"/>
  <circle cx="350" cy="80" r="10" fill="rgba(255,255,255,0.3)"/>
  <rect x="70" y="115" width="260" height="85" rx="10" fill="rgba(255,255,255,0.12)"/>
  <text x="200" y="145" text-anchor="middle" dominant-baseline="middle" 
        fill="#ffffff" font-size="24" font-weight="bold" font-family="Microsoft YaHei, Arial, sans-serif">
    数据分析案例
  </text>
  <text x="200" y="175" text-anchor="middle" dominant-baseline="middle" 
        fill="#f9f0ff" font-size="14" font-family="Microsoft YaHei, Arial, sans-serif">
    大数据面试评估分析
  </text>
  <text x="200" y="255" text-anchor="middle" dominant-baseline="middle" 
        fill="#ffffff" font-size="12" font-family="Microsoft YaHei, Arial, sans-serif">
    六维能力数据建模
  </text>
</svg>
