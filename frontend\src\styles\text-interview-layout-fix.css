/**
 * iFlytek Spark 文本面试页面布局修复
 * 专门解决 /text-interview 路径的拥挤问题
 */

/* 页面整体布局优化 */
.text-interview-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* 头部区域优化 */
.text-interview-page .interview-header {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  padding: 12px 0;
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 100;
  min-height: 60px;
}

.text-interview-page .header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  min-height: 48px;
}

.text-interview-page .interview-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
}

.text-interview-page .interview-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #666;
}

.text-interview-page .meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.text-interview-page .interview-status {
  display: flex;
  gap: 12px;
}

.text-interview-page .status-item {
  text-align: center;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid rgba(24, 144, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  min-width: 80px;
}

.text-interview-page .status-label {
  display: block;
  font-size: 11px;
  color: #999;
  margin-bottom: 2px;
}

.text-interview-page .status-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

/* 主内容区域优化 */
.text-interview-page .interview-main {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.text-interview-page .interview-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1.4fr 1fr;
  gap: 28px;
  min-height: calc(100vh - 100px);
  align-items: start;
}

/* 对话区域优化 */
.text-interview-page .chat-section {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  min-height: 550px;
  max-height: 650px;
}

.text-interview-page .chat-history {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  min-height: 350px;
  max-height: 450px;
}

.text-interview-page .message-item {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.text-interview-page .message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 14px;
  color: white;
}

.text-interview-page .message-content {
  flex: 1;
  background: white;
  padding: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
}

.text-interview-page .input-area {
  padding: 16px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.text-interview-page .answer-input .el-textarea__inner {
  min-height: 80px !important;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
}

.text-interview-page .input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.text-interview-page .action-buttons {
  display: flex;
  gap: 8px;
}

/* 分析区域优化 - 修复重叠问题 */
.text-interview-page .analysis-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 180px);
  padding: 0;
}

.text-interview-page .text-analysis-panel,
.text-interview-page .realtime-score,
.text-interview-page .interview-controls,
.text-interview-page .dialogue-stats {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  padding: 18px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(24, 144, 255, 0.1);
  min-height: 160px;
  max-height: 240px;
  overflow-y: auto;
  flex-shrink: 0;
}

.text-interview-page .text-analysis-panel h3,
.text-interview-page .realtime-score h3,
.text-interview-page .interview-controls h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-interview-page .analysis-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.text-interview-page .analysis-item .label {
  min-width: 80px;
  font-size: 13px;
  color: #666;
}

.text-interview-page .analysis-item .score {
  min-width: 40px;
  font-size: 13px;
  font-weight: 600;
  color: #1890ff;
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .text-interview-page .interview-container {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 20px;
  }

  .text-interview-page .analysis-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    max-height: none;
  }

  .text-interview-page .chat-section {
    min-height: 450px;
    max-height: 550px;
  }

  .text-interview-page .text-analysis-panel,
  .text-interview-page .realtime-score,
  .text-interview-page .interview-controls {
    min-height: 150px;
    max-height: 200px;
  }
}

@media (max-width: 768px) {
  .text-interview-page .interview-header {
    padding: 8px 0;
  }

  .text-interview-page .header-container {
    flex-direction: column;
    gap: 12px;
    padding: 0 16px;
    min-height: auto;
  }

  .text-interview-page .interview-meta {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .text-interview-page .interview-status {
    justify-content: center;
    gap: 8px;
  }

  .text-interview-page .interview-container {
    padding: 0 16px;
    gap: 16px;
  }

  .text-interview-page .chat-section {
    min-height: 400px;
    max-height: 500px;
  }

  .text-interview-page .chat-history {
    min-height: 250px;
    max-height: 350px;
    padding: 12px;
  }

  .text-interview-page .analysis-section {
    grid-template-columns: 1fr;
  }

  .text-interview-page .text-analysis-panel,
  .text-interview-page .realtime-score,
  .text-interview-page .interview-controls {
    padding: 12px;
    min-height: 120px;
    max-height: 180px;
  }
}

@media (max-width: 480px) {
  .text-interview-page .interview-title {
    font-size: 18px;
  }

  .text-interview-page .header-container {
    padding: 0 12px;
  }

  .text-interview-page .interview-container {
    padding: 0 12px;
  }

  .text-interview-page .status-item {
    padding: 6px 8px;
    min-width: 60px;
  }

  .text-interview-page .status-value {
    font-size: 12px;
  }
}
