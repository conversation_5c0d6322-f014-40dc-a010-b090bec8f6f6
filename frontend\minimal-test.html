<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .success {
            color: #52c41a;
            font-weight: bold;
        }
        .error {
            color: #ff4d4f;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-box">
        <h1>🔍 前端调试测试页面</h1>
        <p>这是一个静态HTML页面，用于确认服务器基本功能</p>
        
        <div id="server-test">
            <h3>服务器状态测试</h3>
            <p id="server-status">检测中...</p>
        </div>
        
        <div id="vue-test">
            <h3>Vue应用测试</h3>
            <p>请在浏览器开发者工具中查看以下信息：</p>
            <ul>
                <li>Console标签页是否有JavaScript错误</li>
                <li>Network标签页是否有加载失败的资源</li>
                <li>Elements标签页中#app元素是否为空</li>
            </ul>
        </div>
        
        <div id="links-test">
            <h3>快速测试链接</h3>
            <p><a href="/" target="_blank">打开Vue主应用</a></p>
            <p><a href="/src/main.js" target="_blank">检查main.js是否能加载</a></p>
            <p><a href="/src/App_simple.vue" target="_blank">检查App组件是否能加载</a></p>
        </div>
        
        <div id="debug-info">
            <h3>调试信息</h3>
            <p>当前时间: <span id="current-time"></span></p>
            <p>页面URL: <span id="page-url"></span></p>
            <p>用户代理: <span id="user-agent"></span></p>
        </div>
    </div>
    
    <script>
        // 更新调试信息
        document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        document.getElementById('page-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        
        // 测试服务器连接
        const serverStatus = document.getElementById('server-status');
        
        // 测试主应用
        fetch('/')
            .then(response => {
                if (response.ok) {
                    return response.text();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(html => {
                if (html.includes('<div id="app">')) {
                    serverStatus.innerHTML = '<span class="success">✅ 服务器正常，HTML结构正确</span>';
                } else {
                    serverStatus.innerHTML = '<span class="error">⚠️ 服务器响应异常，HTML结构不正确</span>';
                }
            })
            .catch(error => {
                serverStatus.innerHTML = `<span class="error">❌ 服务器连接失败: ${error.message}</span>`;
            });
            
        // 测试main.js
        fetch('/src/main.js')
            .then(response => {
                if (response.ok) {
                    console.log('✅ main.js 可以正常加载');
                } else {
                    console.error('❌ main.js 加载失败');
                }
            })
            .catch(error => {
                console.error('❌ main.js 请求失败:', error);
            });
    </script>
</body>
</html>
