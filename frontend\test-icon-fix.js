// 图标修复验证脚本
// 在浏览器控制台中运行此脚本来检查图标尺寸

console.log('🔍 开始检查图标尺寸...');

// 检查所有 el-icon 元素
const allIcons = document.querySelectorAll('.el-icon, [class*="el-icon"], svg.el-icon');
console.log(`📊 找到 ${allIcons.length} 个图标元素`);

let oversizedIcons = [];
let fixedIcons = [];

allIcons.forEach((icon, index) => {
    const rect = icon.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(icon);
    
    const info = {
        index: index + 1,
        element: icon,
        width: rect.width,
        height: rect.height,
        fontSize: computedStyle.fontSize,
        className: icon.className,
        parent: icon.parentElement?.className || 'unknown'
    };
    
    // 检查是否过大（超过40px被认为是过大）
    if (rect.width > 40 || rect.height > 40) {
        oversizedIcons.push(info);
        console.warn(`⚠️ 发现过大图标 #${index + 1}:`, info);
    } else {
        fixedIcons.push(info);
    }
});

console.log(`✅ 正常尺寸图标: ${fixedIcons.length} 个`);
console.log(`❌ 过大图标: ${oversizedIcons.length} 个`);

if (oversizedIcons.length > 0) {
    console.log('\n🔧 需要修复的过大图标:');
    oversizedIcons.forEach(icon => {
        console.log(`- 图标 #${icon.index}: ${icon.width}x${icon.height}px, 父容器: ${icon.parent}`);
        
        // 尝试自动修复
        icon.element.style.width = '24px';
        icon.element.style.height = '24px';
        icon.element.style.fontSize = '24px';
        icon.element.style.maxWidth = '24px';
        icon.element.style.maxHeight = '24px';
        
        console.log(`  ✅ 已应用临时修复`);
    });
} else {
    console.log('🎉 所有图标尺寸正常！');
}

// 特别检查 Setting 图标
const settingIcons = document.querySelectorAll('[class*="setting"], [class*="Setting"]');
console.log(`\n⚙️ 检查 Setting 图标: ${settingIcons.length} 个`);

settingIcons.forEach((icon, index) => {
    const rect = icon.getBoundingClientRect();
    console.log(`Setting 图标 #${index + 1}: ${rect.width}x${rect.height}px`);
    
    if (rect.width > 32 || rect.height > 32) {
        console.warn(`⚠️ Setting 图标过大，应用修复...`);
        icon.style.width = '24px';
        icon.style.height = '24px';
        icon.style.fontSize = '24px';
    }
});

// 检查技术图标容器
const techIcons = document.querySelectorAll('.ai-tech-icon');
console.log(`\n🔬 检查技术图标容器: ${techIcons.length} 个`);

techIcons.forEach((container, index) => {
    const icon = container.querySelector('.el-icon, svg, i');
    if (icon) {
        const rect = icon.getBoundingClientRect();
        console.log(`技术图标 #${index + 1}: ${rect.width}x${rect.height}px`);
        
        if (rect.width > 30 || rect.height > 30) {
            console.warn(`⚠️ 技术图标过大，应用修复...`);
            icon.style.width = '24px';
            icon.style.height = '24px';
            icon.style.fontSize = '24px';
        }
    }
});

console.log('\n📋 修复总结:');
console.log(`- 总图标数量: ${allIcons.length}`);
console.log(`- 正常图标: ${fixedIcons.length}`);
console.log(`- 修复图标: ${oversizedIcons.length}`);
console.log(`- Setting图标: ${settingIcons.length}`);
console.log(`- 技术图标: ${techIcons.length}`);

// 返回检查结果
return {
    total: allIcons.length,
    normal: fixedIcons.length,
    oversized: oversizedIcons.length,
    settingIcons: settingIcons.length,
    techIcons: techIcons.length,
    success: oversizedIcons.length === 0
};
