<svg width="100" height="32" viewBox="0 0 100 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="iflytekGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1890ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#722ed1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="100" height="32" rx="6" fill="url(#iflytekGradient)" opacity="0.1"/>
  
  <!-- Logo Icon -->
  <g transform="translate(4, 4)">
    <!-- Stylized "i" -->
    <circle cx="4" cy="4" r="2" fill="url(#iflytekGradient)"/>
    <rect x="3" y="8" width="2" height="16" rx="1" fill="url(#iflytekGradient)"/>
    
    <!-- Flying elements -->
    <path d="M10 8 L16 6 L14 10 L18 12 L12 14 Z" fill="url(#iflytekGradient)" opacity="0.8"/>
    <path d="M12 16 L18 14 L16 18 L20 20 L14 22 Z" fill="url(#iflytekGradient)" opacity="0.6"/>
  </g>
  
  <!-- Text -->
  <text x="28" y="12" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="url(#iflytekGradient)">
    iFlytek
  </text>
  <text x="28" y="22" font-family="Arial, sans-serif" font-size="6" fill="#666">
    科大讯飞
  </text>
</svg>
