# base64-arraybuffer

![CI](https://github.com/niklasvh/base64-arraybuffer/workflows/CI/badge.svg?branch=master)
[![NPM Downloads](https://img.shields.io/npm/dm/base64-arraybuffer.svg)](https://www.npmjs.org/package/base64-arraybuffer)
[![NPM Version](https://img.shields.io/npm/v/base64-arraybuffer.svg)](https://www.npmjs.org/package/base64-arraybuffer)

Encode/decode base64 data into ArrayBuffers

### Installing
You can install the module via npm:

    npm install base64-arraybuffer
  
## API
The library encodes and decodes base64 to and from ArrayBuffers

 - __encode(buffer)__ - Encodes `<PERSON>rrayBuffer` into base64 string
 - __decode(str)__ - Decodes base64 string to `ArrayBuffer`

### Testing
You can run the test suite with:

    npm test

## License
Copyright (c) 2012 <PERSON><PERSON>
Licensed under the MIT license.
