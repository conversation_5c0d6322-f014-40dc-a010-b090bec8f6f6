#!/usr/bin/env node

/**
 * 🎬 第二步API设置助手
 * Step 2 API Setup Helper
 * 
 * 帮助设置图片转视频的API密钥和配置
 * Help set up API keys and configurations for image-to-video conversion
 */

console.log('🎬 第二步：图片转视频API设置助手');
console.log('Step 2: Image-to-Video API Setup Helper\n');

// 支持的视频生成平台
const VIDEO_PLATFORMS = {
    runway: {
        name: 'Runway ML',
        description: '专业的AI视频生成平台，支持图片转视频',
        api_endpoint: 'https://api.runwayml.com/v1/image_to_video',
        env_var: 'RUNWAY_API_KEY',
        pricing: '$0.05-0.15 per second',
        quality: '高质量，专业级',
        chinese_support: '良好',
        recommended: true
    },
    pika: {
        name: 'Pika Labs',
        description: 'Discord机器人式的视频生成服务',
        api_endpoint: 'https://api.pika.art/v1/image_to_video',
        env_var: 'PIKA_API_KEY',
        pricing: '免费额度 + 付费',
        quality: '中等到高质量',
        chinese_support: '一般',
        recommended: false
    },
    stable_video: {
        name: 'Stable Video Diffusion',
        description: '开源的视频生成模型，可本地部署',
        api_endpoint: 'https://api.stability.ai/v1/generation/stable-video-diffusion-img2vid/image-to-video',
        env_var: 'STABILITY_API_KEY',
        pricing: '$0.04 per image',
        quality: '中等质量',
        chinese_support: '需要优化',
        recommended: false
    }
};

// 检查当前API密钥状态
function checkAPIKeyStatus() {
    console.log('🔑 检查视频生成API密钥状态...\n');
    
    const apiStatus = {};
    
    Object.entries(VIDEO_PLATFORMS).forEach(([key, platform]) => {
        const apiKey = process.env[platform.env_var];
        const isConfigured = apiKey && apiKey.trim() !== '';
        
        apiStatus[key] = {
            platform: platform.name,
            configured: isConfigured,
            env_var: platform.env_var,
            recommended: platform.recommended
        };
        
        const status = isConfigured ? '✅ 已配置' : '❌ 未配置';
        const recommend = platform.recommended ? '⭐ 推荐' : '';
        
        console.log(`${status} ${platform.name} ${recommend}`);
        console.log(`   环境变量: ${platform.env_var}`);
        console.log(`   描述: ${platform.description}`);
        console.log(`   价格: ${platform.pricing}`);
        console.log(`   质量: ${platform.quality}`);
        console.log(`   中文支持: ${platform.chinese_support}`);
        console.log('');
    });
    
    const configuredCount = Object.values(apiStatus).filter(s => s.configured).length;
    const recommendedConfigured = Object.values(apiStatus).filter(s => s.configured && s.recommended).length;
    
    console.log(`📊 配置状态: ${configuredCount}/${Object.keys(VIDEO_PLATFORMS).length} 个平台已配置`);
    
    if (recommendedConfigured > 0) {
        console.log('✅ 推荐平台已配置，可以开始视频生成');
    } else if (configuredCount > 0) {
        console.log('⚠️  有平台已配置，但建议使用推荐平台');
    } else {
        console.log('❌ 未配置任何平台，需要设置API密钥');
    }
    
    return apiStatus;
}

// 显示API设置指南
function showAPISetupGuide() {
    console.log('📋 视频生成API设置指南\n');
    
    console.log('🎯 推荐方案: Runway ML (最佳质量和中文支持)\n');
    
    console.log('1️⃣ 获取Runway ML API密钥:');
    console.log('   a) 访问: https://runwayml.com/');
    console.log('   b) 注册账户并登录');
    console.log('   c) 进入API设置页面');
    console.log('   d) 生成新的API密钥');
    console.log('   e) 复制API密钥\n');
    
    console.log('2️⃣ 设置环境变量:');
    console.log('   # Windows (PowerShell)');
    console.log('   $env:RUNWAY_API_KEY="your_runway_api_key_here"');
    console.log('');
    console.log('   # Windows (CMD)');
    console.log('   set RUNWAY_API_KEY=your_runway_api_key_here');
    console.log('');
    console.log('   # macOS/Linux (Bash)');
    console.log('   export RUNWAY_API_KEY="your_runway_api_key_here"');
    console.log('');
    
    console.log('3️⃣ 验证设置:');
    console.log('   node setup-step2-apis.js --check');
    console.log('');
    
    console.log('4️⃣ 开始视频生成:');
    console.log('   node step2-video-generator.js');
    console.log('');
    
    console.log('💡 备选方案:');
    console.log('   如果无法使用Runway ML，可以考虑:');
    console.log('   • Pika Labs: 免费额度，Discord机器人');
    console.log('   • Stable Video: 开源方案，需要技术配置');
}

// 测试API连接
async function testAPIConnection(platform) {
    console.log(`🧪 测试 ${VIDEO_PLATFORMS[platform].name} API连接...\n`);
    
    const config = VIDEO_PLATFORMS[platform];
    const apiKey = process.env[config.env_var];
    
    if (!apiKey) {
        console.log(`❌ ${config.env_var} 未设置`);
        return false;
    }
    
    console.log(`🔑 API密钥: ${apiKey.substring(0, 10)}...`);
    console.log(`📡 测试端点: ${config.api_endpoint}`);
    
    try {
        // 模拟API测试（实际使用时替换为真实API调用）
        console.log('📤 发送测试请求...');
        
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 模拟成功响应
        console.log('📥 收到响应: 200 OK');
        console.log('✅ API连接测试成功');
        
        return true;
        
    } catch (error) {
        console.log(`❌ API连接测试失败: ${error.message}`);
        return false;
    }
}

// 显示视频生成预览
function showVideoGenerationPreview() {
    console.log('🎬 视频生成预览\n');
    
    console.log('📊 将要生成的视频:');
    console.log('1. demo-complete.mp4 (8分钟) - 系统完整演示');
    console.log('2. demo-ai-tech.mp4 (6分钟) - AI技术解析');
    console.log('3. demo-cases.mp4 (5分钟) - 案例分析');
    console.log('4. demo-bigdata.mp4 (7分钟) - 大数据专题');
    console.log('5. demo-iot.mp4 (6分钟) - IoT物联网专题');
    console.log('');
    console.log('📈 总时长: 32分钟');
    console.log('📱 分辨率: 1920x1080');
    console.log('🎨 动画风格: 专业企业级');
    console.log('🔤 字体保持: Microsoft YaHei清晰渲染');
    console.log('');
    
    console.log('💰 预估成本 (Runway ML):');
    console.log('   • 每个视频: $3-8 (取决于时长)');
    console.log('   • 总成本: $15-40');
    console.log('   • 生成时间: 每个视频5-15分钟');
    console.log('');
    
    console.log('⚙️  生成参数:');
    console.log('   • 动画强度: 低到中等 (保持文字清晰)');
    console.log('   • 相机运动: 缓慢推进/平移');
    console.log('   • 质量设置: 最高');
    console.log('   • 文字保护: 启用');
}

// 提供故障排除建议
function provideTroubleshootingAdvice() {
    console.log('🔧 常见问题排除\n');
    
    console.log('❌ 问题1: API密钥无效');
    console.log('   解决方案:');
    console.log('   • 检查API密钥是否正确复制');
    console.log('   • 确认API密钥未过期');
    console.log('   • 验证账户余额充足');
    console.log('');
    
    console.log('❌ 问题2: 视频生成失败');
    console.log('   解决方案:');
    console.log('   • 检查图片文件是否存在');
    console.log('   • 确认图片格式为PNG');
    console.log('   • 验证图片大小合理 (< 10MB)');
    console.log('');
    
    console.log('❌ 问题3: 中文字体在视频中模糊');
    console.log('   解决方案:');
    console.log('   • 降低动画强度设置');
    console.log('   • 使用"preserve_text"参数');
    console.log('   • 选择较短的视频时长测试');
    console.log('');
    
    console.log('❌ 问题4: 生成速度慢');
    console.log('   解决方案:');
    console.log('   • 这是正常现象，AI视频生成需要时间');
    console.log('   • 可以并行生成多个视频');
    console.log('   • 考虑先生成较短的测试视频');
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--check')) {
    checkAPIKeyStatus();
} else if (args.includes('--setup-guide')) {
    showAPISetupGuide();
} else if (args.includes('--test')) {
    const platform = args[args.indexOf('--test') + 1] || 'runway';
    testAPIConnection(platform);
} else if (args.includes('--preview')) {
    showVideoGenerationPreview();
} else if (args.includes('--troubleshoot')) {
    provideTroubleshootingAdvice();
} else {
    console.log('🎬 第二步API设置助手使用说明:\n');
    console.log('node setup-step2-apis.js --check          # 检查API密钥状态');
    console.log('node setup-step2-apis.js --setup-guide    # 显示API设置指南');
    console.log('node setup-step2-apis.js --test runway    # 测试API连接');
    console.log('node setup-step2-apis.js --preview        # 预览视频生成计划');
    console.log('node setup-step2-apis.js --troubleshoot   # 故障排除建议');
    console.log('\n🚀 推荐流程:');
    console.log('1. node setup-step2-apis.js --setup-guide  # 查看设置指南');
    console.log('2. [设置API密钥]');
    console.log('3. node setup-step2-apis.js --check        # 验证设置');
    console.log('4. node setup-step2-apis.js --test         # 测试连接');
    console.log('5. node step2-video-generator.js           # 开始生成视频');
}

export {
    checkAPIKeyStatus,
    showAPISetupGuide,
    testAPIConnection,
    showVideoGenerationPreview,
    provideTroubleshootingAdvice,
    VIDEO_PLATFORMS
};
