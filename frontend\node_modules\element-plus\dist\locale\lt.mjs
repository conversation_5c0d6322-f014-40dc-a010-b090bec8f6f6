/*! Element Plus v2.10.2 */

var lt = {
  name: "lt",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "<PERSON>yt<PERSON>"
    },
    datepicker: {
      now: "Dabar",
      today: "\u0160iandien",
      cancel: "At\u0161aukti",
      clear: "Valyt<PERSON>",
      confirm: "OK",
      selectDate: "Pasirink dat\u0105",
      selectTime: "Pasirink laik\u0105",
      startDate: "Data nuo",
      startTime: "Laikas nuo",
      endDate: "Data iki",
      endTime: "Laikas iki",
      prevYear: "Metai atgal",
      nextYear: "Metai \u012F priek\u012F",
      prevMonth: "M\u0117n. atgal",
      nextMonth: "M\u0117n. \u012F priek\u012F",
      year: "",
      month1: "Sausis",
      month2: "Vasa<PERSON>",
      month3: "<PERSON><PERSON>",
      month4: "<PERSON><PERSON><PERSON>",
      month5: "Gegu\u017E\u0117",
      month6: "Bir\u017Eelis",
      month7: "Lie<PERSON>",
      month8: "Rugpj\u016Btis",
      month9: "Rugs\u0117jis",
      month10: "Spalis",
      month11: "Lapkritis",
      month12: "Gruodis",
      weeks: {
        sun: "S.",
        mon: "Pr.",
        tue: "A.",
        wed: "T.",
        thu: "K.",
        fri: "Pn.",
        sat: "\u0160."
      },
      months: {
        jan: "Sau",
        feb: "Vas",
        mar: "Kov",
        apr: "Bal",
        may: "Geg",
        jun: "Bir",
        jul: "Lie",
        aug: "Rugp",
        sep: "Rugs",
        oct: "Spa",
        nov: "Lap",
        dec: "Gruo"
      }
    },
    select: {
      loading: "Kraunasi",
      noMatch: "Duomen\u0173 nerasta",
      noData: "N\u0117ra duomen\u0173",
      placeholder: "Pasirink"
    },
    mention: {
      loading: "Kraunasi"
    },
    cascader: {
      noMatch: "Duomen\u0173 nerasta",
      loading: "Kraunasi",
      placeholder: "Pasirink",
      noData: "N\u0117ra duomen\u0173"
    },
    pagination: {
      goto: "Eiti \u012F",
      pagesize: "/p",
      total: "Viso {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "\u017Dinut\u0117",
      confirm: "OK",
      cancel: "At\u0161aukti",
      error: "Klaida \u012Fvestuose duomenyse"
    },
    upload: {
      deleteTip: 'spauskite "Trinti" nor\u0117dami pa\u0161alinti',
      delete: "Trinti",
      preview: "Per\u017Ei\u016Br\u0117ti",
      continue: "Toliau"
    },
    table: {
      emptyText: "Duomen\u0173 nerasta",
      confirmFilter: "Patvirtinti",
      resetFilter: "Atstatyti",
      clearFilter: "I\u0161valyti",
      sumText: "Suma"
    },
    tour: {
      next: "Kitas",
      previous: "Ankstesnis",
      finish: "Baigti"
    },
    tree: {
      emptyText: "N\u0117ra duomen\u0173"
    },
    transfer: {
      noMatch: "Duomen\u0173 nerasta",
      noData: "N\u0117ra duomen\u0173",
      titles: ["S\u0105ra\u0161as 1", "S\u0105ra\u0161as 2"],
      filterPlaceholder: "\u012Evesk rakta\u017Eod\u012F",
      noCheckedFormat: "Viso: {total}",
      hasCheckedFormat: "Pa\u017Eym\u0117ta {checked} i\u0161 {total}"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { lt as default };
