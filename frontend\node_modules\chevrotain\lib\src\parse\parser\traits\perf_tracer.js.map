{"version": 3, "file": "perf_tracer.js", "sourceRoot": "", "sources": ["../../../../../src/parse/parser/traits/perf_tracer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,GAAG,EAAE,MAAM,WAAW,CAAC;AAChC,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAE1C,OAAO,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAErD;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAK5B,qBAAqB,CAAC,MAAqB;QACzC,IAAI,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE;YAChC,MAAM,iBAAiB,GAAG,MAAM,CAAC,aAAa,CAAC;YAC/C,MAAM,aAAa,GAAG,OAAO,iBAAiB,KAAK,QAAQ,CAAC;YAC5D,IAAI,CAAC,iBAAiB,GAAG,aAAa;gBACpC,CAAC,CAAS,iBAAiB;gBAC3B,CAAC,CAAC,QAAQ,CAAC;YACb,IAAI,CAAC,aAAa,GAAG,aAAa;gBAChC,CAAC,CAAC,iBAAiB,GAAG,CAAC;gBACvB,CAAC,CAAE,iBAA6B,CAAC,CAAC,0DAA0D;SAC/F;aAAM;YACL,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,qBAAqB,CAAC,aAAa,CAAC;SAC1D;QAED,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED,UAAU,CAAyB,SAAiB,EAAE,SAAkB;QACtE,sDAAsD;QACtD,oCAAoC;QACpC,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,EAAE;gBACjD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,QAAQ,SAAS,GAAG,CAAC,CAAC;aAC5C;YACD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YACzC,kGAAkG;YAClG,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;YAC3D,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,EAAE;gBACjD,WAAW,CAAC,GAAG,MAAM,QAAQ,SAAS,WAAW,IAAI,IAAI,CAAC,CAAC;aAC5D;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;SACd;aAAM;YACL,OAAO,SAAS,EAAE,CAAC;SACpB;IACH,CAAC;CACF"}