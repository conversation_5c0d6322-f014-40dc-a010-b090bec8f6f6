# Render.com 部署配置
services:
  - type: web
    name: iflytek-interview-backend
    env: python
    buildCommand: |
      cd backend
      pip install -r requirements.txt
    startCommand: |
      cd backend
      python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: PYTHONPATH
        value: /opt/render/project/src/backend
      - key: IFLYTEK_APP_ID
        sync: false
      - key: IFLYTEK_API_KEY
        sync: false
      - key: IFLYTEK_API_SECRET
        sync: false
      - key: IFLYTEK_SPARK_URL
        value: wss://spark-api.xf-yun.com/v3.5/chat
