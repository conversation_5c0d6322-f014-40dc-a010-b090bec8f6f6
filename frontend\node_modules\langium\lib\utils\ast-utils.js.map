{"version": 3, "file": "ast-utils.js", "sourceRoot": "", "sources": ["../../src/utils/ast-utils.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAMhF,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC9E,OAAO,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AAEzC;;;GAGG;AACH,MAAM,UAAU,sBAAsB,CAAC,IAAa;IAChD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBAC1B,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;wBACjB,IAAyB,CAAC,UAAU,GAAG,IAAI,CAAC;wBAC5C,IAAyB,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBACpD,IAAyB,CAAC,eAAe,GAAG,KAAK,CAAC;oBACvD,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;iBAAM,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,KAA0B,CAAC,UAAU,GAAG,IAAI,CAAC;gBAC7C,KAA0B,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC1D,CAAC;QACL,CAAC;IACL,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,kBAAkB,CAAoB,IAAyB,EAAE,aAAqC;IAClH,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,OAAO,IAAI,EAAE,CAAC;QACV,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAAyB,EAAE,SAAkC;IAC5F,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,OAAO,IAAI,EAAE,CAAC;QACV,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAA8B,IAAa;IAClE,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC;IAClC,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjD,CAAC;IACD,OAAO,MAA4B,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,IAAa;IACtC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;QACrB,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AASD;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,IAAa,EAAE,OAA0B;IACpE,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IACD,MAAM,KAAK,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC;IAE7B,OAAO,IAAI,UAAU,CAAiB,GAAG,EAAE,CAAC,CAAC;QACzC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACvB,QAAQ,EAAE,CAAC;QACX,UAAU,EAAE,CAAC;KAChB,CAAC,EAAE,KAAK,CAAC,EAAE;QACR,OAAO,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,KAAK,GAAI,IAAuB,CAAC,QAAQ,CAAC,CAAC;gBACjD,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBACnB,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACjB,IAAI,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC;wBACjC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;oBAClC,CAAC;gBACL,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9B,OAAO,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;wBACrC,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;wBACjC,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;wBAC7B,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;4BACzD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;wBAC3C,CAAC;oBACL,CAAC;oBACD,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;gBACzB,CAAC;YACL,CAAC;YACD,KAAK,CAAC,QAAQ,EAAE,CAAC;QACrB,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAa,EAAE,OAA0B;IACvE,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACrD,CAAC;IACD,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AAC3E,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,IAAa,EAAE,OAA0B;IAC/D,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACrD,CAAC;SAAM,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,KAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAClE,yDAAyD;QACzD,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;AAClG,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAgB,EAAE,KAAa;;IACrD,IAAI,CAAC,KAAK,EAAE,CAAC;QACT,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,MAAM,SAAS,GAAG,MAAA,OAAO,CAAC,QAAQ,0CAAE,KAAK,CAAC;IAC1C,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,OAAO,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACrC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,IAAa;IAE1C,OAAO,IAAI,UAAU,CAAuB,GAAG,EAAE,CAAC,CAAC;QAC/C,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACvB,QAAQ,EAAE,CAAC;QACX,UAAU,EAAE,CAAC;KAChB,CAAC,EAAE,KAAK,CAAC,EAAE;QACR,OAAO,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,KAAK,GAAI,IAAuB,CAAC,QAAQ,CAAC,CAAC;gBACjD,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrB,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACjB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC;gBACnF,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9B,OAAO,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;wBACrC,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;wBACjC,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;wBAC7B,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;4BACvB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC;wBAC5F,CAAC;oBACL,CAAC;oBACD,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;gBACzB,CAAC;YACL,CAAC;YACD,KAAK,CAAC,QAAQ,EAAE,CAAC;QACrB,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,UAAmB,EAAE,MAAM,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,KAAK;IACvG,MAAM,IAAI,GAAgB,EAAE,CAAC;IAC7B,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC7B,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACrC,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;gBACvC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;AACxB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,yBAAyB,CAAC,UAAyB,EAAE,IAAa;IAC9E,MAAM,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5D,MAAM,WAAW,GAAG,IAAsB,CAAC;IAC3C,KAAK,MAAM,QAAQ,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;QAC7C,sFAAsF;QACtF,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;YAClF,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;AACL,CAAC;AAED,SAAS,gBAAgB,CAAC,YAA0B;IAChD,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACnD,CAAC;SAAM,CAAC;QACJ,OAAO,YAAY,CAAC;IACxB,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAA8B,IAAO,EAAE,cAAsH;IACpL,MAAM,IAAI,GAAmB,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;IAEnD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;YACpD,CAAC;iBAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,cAAc,CACvB,IAAI,EACJ,IAAI,EACJ,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,QAAQ,CACjB,CAAC;YACN,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,WAAW,GAAc,EAAE,CAAC;gBAClC,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;oBAC1B,IAAI,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;wBACrB,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;oBAC3D,CAAC;yBAAM,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC9B,WAAW,CAAC,IAAI,CACZ,cAAc,CACV,IAAI,EACJ,IAAI,EACJ,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,QAAQ,CACnB,CACJ,CAAC;oBACN,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC9B,CAAC;gBACL,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACvB,CAAC;QACL,CAAC;IACL,CAAC;IAED,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC7B,OAAO,IAAoB,CAAC;AAChC,CAAC"}