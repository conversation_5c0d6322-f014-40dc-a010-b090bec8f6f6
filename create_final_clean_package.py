#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建最终的精简版压缩包
只保留最核心的文件，去除所有测试和调试文件
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_final_clean_package():
    """创建最终精简版压缩包"""
    
    print("🚀 创建最终精简版压缩包...")
    
    # 创建临时目录
    temp_dir = "final_clean_package"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # 核心启动文件
    core_files = [
        "start_system.py",
        "quick_system_check.py",
        "启动服务器.bat", 
        "完整启动指南.md",
        "比赛评审运行指南.md",
        "README.md",
        "FINAL_PROJECT_STATUS.md",
        "PROJECT_COMPLETION_REPORT.md"
    ]
    
    print("📋 复制核心启动文件...")
    for file in core_files:
        if os.path.exists(file):
            shutil.copy2(file, temp_dir)
            print(f"✅ {file}")
    
    # 前端核心文件
    print("📋 复制前端核心文件...")
    frontend_dirs = [
        "frontend/src",
        "frontend/public"
    ]
    
    for dir_path in frontend_dirs:
        if os.path.exists(dir_path):
            dest_path = os.path.join(temp_dir, dir_path)
            shutil.copytree(dir_path, dest_path, dirs_exist_ok=True)
            print(f"✅ {dir_path}")
    
    frontend_files = [
        "frontend/package.json",
        "frontend/vite.config.js", 
        "frontend/vue.config.js",
        "frontend/index.html"
    ]
    
    for file in frontend_files:
        if os.path.exists(file):
            dest_path = os.path.join(temp_dir, file)
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(file, dest_path)
            print(f"✅ {file}")
    
    # 后端核心文件
    print("📋 复制后端核心文件...")
    backend_dirs = [
        "backend/app"
    ]
    
    for dir_path in backend_dirs:
        if os.path.exists(dir_path):
            dest_path = os.path.join(temp_dir, dir_path)
            shutil.copytree(dir_path, dest_path, dirs_exist_ok=True)
            print(f"✅ {dir_path}")
    
    backend_files = [
        "backend/run_server.py",
        "backend/simple_server.py",
        "backend/requirements.txt",
        "backend/package.json",
        "backend/interview_system.db"
    ]
    
    for file in backend_files:
        if os.path.exists(file):
            dest_path = os.path.join(temp_dir, file)
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(file, dest_path)
            print(f"✅ {file}")
    
    # 重要文档
    print("📋 复制重要文档...")
    docs_files = [
        "docs/presentation.md",
        "docs/technical-documentation.md",
        "docs/system-design-overview.md"
    ]
    
    for file in docs_files:
        if os.path.exists(file):
            dest_path = os.path.join(temp_dir, file)
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(file, dest_path)
            print(f"✅ {file}")
    
    # 演示文件
    demo_files = [
        "frontend/multimodal-showcase-demo.html",
        "complete-validation-test.html"
    ]
    
    for file in demo_files:
        if os.path.exists(file):
            dest_path = os.path.join(temp_dir, file)
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(file, dest_path)
            print(f"✅ {file}")
    
    # 创建最终压缩包
    print("📦 创建最终压缩包...")
    with zipfile.ZipFile("86014454最终精简版.zip", 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, temp_dir)
                zipf.write(file_path, arcname)
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print("🎉 最终精简版创建完成！")
    
    # 显示文件大小对比
    original_files = [f for f in os.listdir('.') if f.endswith('.zip')]
    print("\n📊 压缩包大小对比:")
    for zip_file in sorted(original_files):
        if os.path.exists(zip_file):
            size = os.path.getsize(zip_file) / (1024 * 1024)  # MB
            print(f"  {zip_file}: {size:.2f} MB")

def create_readme():
    """创建使用说明"""
    
    readme_content = """# 软件杯比赛 - 最终精简版

## 📦 包含内容

### 🚀 启动文件
- start_system.py - Python启动脚本
- quick_system_check.py - 系统检查脚本  
- 启动服务器.bat - Windows一键启动
- 完整启动指南.md - 详细启动说明
- 比赛评审运行指南.md - 评审专用指南

### 💻 前端核心
- frontend/src/ - Vue.js源码
- frontend/public/ - 静态资源
- frontend/package.json - 项目配置
- frontend/vite.config.js - 构建配置

### 🔧 后端核心  
- backend/app/ - Python Flask应用
- backend/run_server.py - 服务器启动脚本
- backend/requirements.txt - Python依赖
- backend/interview_system.db - 数据库文件

### 📚 重要文档
- docs/presentation.md - 项目介绍
- docs/technical-documentation.md - 技术文档
- FINAL_PROJECT_STATUS.md - 项目状态

### 🎯 演示文件
- frontend/multimodal-showcase-demo.html - 功能演示
- complete-validation-test.html - 完整验证

## 🚀 快速启动

### Windows用户
1. 双击 `启动服务器.bat`
2. 浏览器访问 http://localhost:8080

### 其他系统
1. 安装Python依赖: `pip install -r backend/requirements.txt`
2. 运行: `python start_system.py`
3. 浏览器访问 http://localhost:8080

## ✨ 核心功能
- 基于iFlytek Spark的AI面试官
- 多技术领域支持(AI/大数据/物联网)
- 实时对话评估
- 企业级管理功能

## 🏆 技术特色
- Vue.js 3 + Element Plus前端
- Python Flask后端
- iFlytek Spark LLM集成
- 多模态评估能力

---
**此版本已移除所有测试、调试和重复文件，只保留核心功能。**
"""
    
    with open("86014454最终精简版说明.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("📝 使用说明已创建: 86014454最终精简版说明.md")

def main():
    """主函数"""
    create_final_clean_package()
    create_readme()
    
    print("\n🎯 推荐使用:")
    print("  86014454最终精简版.zip - 最干净的完整版本")
    print("  86014454最终精简版说明.md - 详细使用说明")
    
    print("\n🗑️ 已清理:")
    print("  - 所有test-、debug-、fix-开头的文件")
    print("  - 所有报告和日志文件") 
    print("  - node_modules、__pycache__等缓存")
    print("  - 重复的验证和测试脚本")
    
    print("\n✅ 只保留:")
    print("  - 核心启动脚本")
    print("  - 前后端源码")
    print("  - 重要配置文件")
    print("  - 关键文档")
    print("  - 演示页面")

if __name__ == "__main__":
    main()
