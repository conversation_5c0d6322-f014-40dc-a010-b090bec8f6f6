import * as util from '../../../../util/index.mjs';

import coords from './coords.mjs';
import edgeArrows from './edge-arrows.mjs';
import edgeControlPoints from './edge-control-points.mjs';
import edgeEndpoints from './edge-endpoints.mjs';
import edgeProjection from './edge-projection.mjs';
import labels from './labels.mjs';
import nodes from './nodes.mjs';
import renderedStyle from './rendered-style.mjs';
import zOrdering from './z-ordering.mjs';

var BRp = {};

[
  coords,
  edgeArrows,
  edgeControlPoints,
  edgeEndpoints,
  edgeProjection,
  labels,
  nodes,
  renderedStyle,
  zOrdering
].forEach(function( props ){
  util.extend( BRp, props );
});

export default BRp;
