{"version": 3, "file": "langium-test.d.ts", "sourceRoot": "", "sources": ["../../src/test/langium-test.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAGhF,OAAO,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,KAAK,EAAE,eAAe,EAAwB,kBAAkB,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,eAAe,EAAE,MAAM,gCAAgC,CAAC;AACzR,OAAO,EAAE,kBAAkB,EAAiB,MAAM,6BAA6B,CAAC;AAEhF,OAAO,KAAK,EAAE,eAAe,EAAE,wBAAwB,EAAE,MAAM,wBAAwB,CAAC;AACxF,OAAO,EAAE,qBAAqB,EAAE,MAAM,mCAAmC,CAAC;AAC1E,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,KAAK,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AACrF,OAAO,KAAK,EAAE,OAAO,EAAW,UAAU,EAAE,MAAM,mBAAmB,CAAC;AACtE,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAMpD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,YAAY,EAAE,KAAK,eAAe,EAAE,MAAM,2BAA2B,CAAC;AAE/E,MAAM,WAAW,kBAAmB,SAAQ,YAAY;IACpD;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,aAAa,CAAC,EAAE,aAAa,CAAA;CAChC;AAID,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,QAAQ,EAAE,mBAAmB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,kBAAkB,KAAK,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAUpK;AAED,MAAM,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC;AAM5F;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,SAAS,EAAE,cAAc,GAAG,IAAI,CAE9D;AAED,MAAM,WAAW,YAAY;IACzB;;;OAGG;IACH,IAAI,EAAE,MAAM,CAAA;IACZ;;OAEG;IACH,YAAY,CAAC,EAAE,kBAAkB,CAAA;IACjC;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB;;OAEG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAA;IACzB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAA;IACvB;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED,MAAM,WAAW,iBAAkB,SAAQ,YAAY;IACnD,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,UAAU,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;CACjC;AAED;;;;GAIG;AACH,wBAAgB,eAAe,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,KAAK,EAAE,iBAAiB,KAAK,OAAO,CAAC,eAAe,CAAC,CA4CjH;AAED,MAAM,WAAW,mBAAoB,SAAQ,YAAY;IACrD,eAAe,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAA;IAC/C,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,MAAM,CAAA;CACpD;AACD,MAAM,WAAW,uBAAwB,SAAQ,YAAY;IACzD,MAAM,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,IAAI,CAAC;CAC/C;AACD,MAAM,MAAM,eAAe,GAAG,mBAAmB,GAAG,uBAAuB,CAAC;AAE5E,wBAAgB,aAAa,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,KAAK,EAAE,eAAe,KAAK,OAAO,CAAC,eAAe,CAAC,CAiC7G;AAED,MAAM,WAAW,4BAA4B;IACzC,KAAK,CAAC,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,4BAA6B,SAAQ,4BAA4B;IAC9E,eAAe,EAAE,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,CAAC;IACjD,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,eAAe,KAAK,MAAM,CAAC;CACtD;AAED,MAAM,WAAW,gCAAiC,SAAQ,4BAA4B;IAClF,MAAM,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,IAAI,CAAC;CAChD;AAED,MAAM,MAAM,wBAAwB,GAAG,4BAA4B,GAAG,gCAAgC,CAAC;AAEvG,wBAAgB,sBAAsB,CAAC,QAAQ,EAAE,wBAAwB,GAAG,CAAC,KAAK,EAAE,wBAAwB,KAAK,OAAO,CAAC,IAAI,CAAC,CA8B7H;AAED,MAAM,WAAW,gBAAiB,SAAQ,YAAY;IAClD,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC;CAClF;AAED,wBAAgB,cAAc,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,KAAK,EAAE,gBAAgB,KAAK,OAAO,CAAC,eAAe,CAAC,CA2B/G;AAED,wBAAgB,kBAAkB,CAAC,QAAQ,EAAE,eAAe,GAAG;IAAE,YAAY,EAAE,sBAAsB,CAAA;CAAE,CAEtG;AAED,MAAM,WAAW,uBAAwB,SAAQ,YAAY;IACzD,KAAK,EAAE,MAAM,CAAA;IACb,aAAa,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAA;IAC7C,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,MAAM,CAAA;CAClD;AACD,MAAM,WAAW,0BAA2B,SAAQ,YAAY;IAC5D,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,CAAC,WAAW,EAAE,cAAc,KAAK,IAAI,CAAC;CACjD;AACD,MAAM,MAAM,kBAAkB,GAAG,uBAAuB,GAAG,0BAA0B,CAAC;AAEtF,wBAAgB,gBAAgB,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,kBAAkB,EAAE,kBAAkB,KAAK,OAAO,CAAC,eAAe,CAAC,CAqChI;AAED,MAAM,WAAW,sBAAuB,SAAQ,YAAY;IACxD,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;CAChC;AAED,wBAAgB,oBAAoB,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,sBAAsB,EAAE,sBAAsB,KAAK,OAAO,CAAC,eAAe,CAAC,CAgC5I;AAED,MAAM,WAAW,sBAAuB,SAAQ,YAAY;IACxD,kBAAkB,EAAE,OAAO,CAAA;CAC9B;AAED,wBAAgB,oBAAoB,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,sBAAsB,EAAE,sBAAsB,KAAK,OAAO,CAAC,eAAe,CAAC,CAwB5I;AAED,wBAAgB,eAAe,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAO,GAAG,eAAe,CAMvH;AACD,MAAM,WAAW,aAAc,SAAQ,YAAY;IAC/C,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;CAC1B;AAED,wBAAgB,WAAW,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,aAAa,EAAE,aAAa,KAAK,OAAO,CAAC,eAAe,CAAC,CAuBjH;AAED,MAAM,WAAW,gBAAgB;IAC7B;;OAEG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;;OAGG;IACH,KAAK,EAAE,MAAM,CAAA;IACb;;OAEG;IACH,YAAY,CAAC,EAAE,kBAAkB,CAAA;IACjC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAA;IACb;;;;;;;;OAQG;IACH,OAAO,CAAC,EAAE,iBAAiB,CAAA;IAC3B;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED,wBAAgB,gBAAgB,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,kBAAkB,EAAE,gBAAgB,KAAK,OAAO,CAAC,eAAe,CAAC,CAyB9H;AAED,wBAAgB,0BAA0B,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,GAAG,0BAA0B,CAEhH;AAED,wBAAsB,aAAa,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,kBAAkB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAMxK;AAED,wBAAgB,cAAc,CAAC,IAAI,EAAE,YAAY,GAAG;IAAE,MAAM,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,MAAM,EAAE,CAAC;IAAC,MAAM,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;CAAE,CAoCzH;AAED,MAAM,WAAW,gBAAgB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,CAAE,SAAQ,eAAe;IAClF,WAAW,EAAE,UAAU,EAAE,CAAC;IAC1B,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;CAChC;AAED,MAAM,MAAM,uBAAuB,GAAG,kBAAkB,GAAG;IAAE,mBAAmB,CAAC,EAAE,OAAO,CAAA;CAAE,CAAC;AAE7F,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,QAAQ,EAAE,mBAAmB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,uBAAuB,KAAK,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAsB/K;AAED,MAAM,MAAM,qCAAqC,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,IAAI,oBAAoB,GAAG,oBAAoB,GAAG,CAAC,0BAA0B,CAAC,CAAC,CAAC,GAAG,4BAA4B,GAAG,6BAA6B,CAAC,CAAC;AAC9N,MAAM,MAAM,uBAAuB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,IAAI,uBAAuB,GAAG,qCAAqC,CAAC,CAAC,CAAC,CAAC;AAEtI,MAAM,WAAW,uBAAuB;IACpC,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACzB,QAAQ,CAAC,EAAE,kBAAkB,CAAA;CAChC;AAED,MAAM,WAAW,oBAAoB;IACjC,IAAI,CAAC,EAAE,MAAM,CAAA;CAChB;AAED,MAAM,WAAW,oBAAoB;IACjC,IAAI,CAAC,EAAE,OAAO,CAAA;CACjB;AAED,MAAM,WAAW,0BAA0B,CAAC,CAAC,SAAS,OAAO;IACzD,IAAI,CAAC,EAAE,CAAC,CAAA;IACR,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG;QAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAE,CAAA;CACrE;AAED,MAAM,WAAW,4BAA4B;IACzC,KAAK,EAAE,KAAK,CAAA;CACf;AAED,MAAM,WAAW,6BAA6B;IAC1C,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,MAAM,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,OAAO,CAAC;AAE/C,wBAAgB,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,GAAG,OAAO,CAczE;AAED,wBAAgB,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,GAAG,OAAO,CAK5D;AAED,wBAAgB,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,MAAM,CAElD;AAED,wBAAgB,eAAe,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,uBAAuB,CAAC,CAAC,CAAC,gBA0CnK;AAED,wBAAgB,cAAc,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,uBAAuB,CAAC,CAAC,CAAC,GAAG,IAAI,CAGhL;AAED,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,uBAAuB,CAAC,CAAC,CAAC,GAAG,IAAI,CAG7K;AAED,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,EAAE,aAAa,EAAE,qCAAqC,CAAC,CAAC,CAAC,GAAG,IAAI,CASpN;AACD,wBAAgB,aAAa,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,EAAE,aAAa,EAAE,qCAAqC,CAAC,CAAC,CAAC,GAAG,IAAI,CAStN;AAED,wBAAgB,gBAAgB,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,SAAS,GAAG,MAAM,CAE9E;AAED;;;;GAIG;AACH,wBAAgB,eAAe,CAAC,QAAQ,EAAE,eAAe,GAAG,wBAAwB,EAAE,QAAQ,EAAE,YAAY,GAAG,UAAU,CAOxH;AAED,wBAAgB,cAAc,CAAC,QAAQ,EAAE,mBAAmB,GAAG,yBAAyB,EAAE,SAAS,CAAC,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAItI;AAED,MAAM,WAAW,+BAA+B;IAC5C,MAAM,EAAE,qBAAqB,CAAC,oBAAoB,EAAE,CAAC;IACrD,MAAM,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;CACnC;AAED,wBAAgB,eAAe,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,QAAQ,EAAE,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,kBAAkB,KAAK,OAAO,CAAC,+BAA+B,CAAC,CAejL;AAED,MAAM,WAAW,mBAAmB;IAChC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,kBAAkB,CAAC;CACjC;AAED,wBAAgB,mBAAmB,CAAC,gBAAgB,EAAE,+BAA+B,EAAE,OAAO,EAAE,mBAAmB,GAAG,IAAI,CAMzH"}