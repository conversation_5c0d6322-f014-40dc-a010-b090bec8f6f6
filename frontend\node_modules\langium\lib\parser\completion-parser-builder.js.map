{"version": 3, "file": "completion-parser-builder.js", "sourceRoot": "", "sources": ["../../src/parser/completion-parser-builder.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAGhF,OAAO,EAAE,uBAAuB,EAAE,MAAM,qBAAqB,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,MAAM,UAAU,sBAAsB,CAAC,QAA6B;IAChE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;IACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;IACpC,MAAM,MAAM,GAAG,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IACrD,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;IAChD,MAAM,CAAC,QAAQ,EAAE,CAAC;IAClB,OAAO,MAAM,CAAC;AAClB,CAAC"}