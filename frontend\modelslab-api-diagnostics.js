#!/usr/bin/env node

/**
 * 🔧 ModelsLab API深度诊断工具
 * ModelsLab API Deep Diagnostics Tool
 * 
 * 深入分析ModelsLab API问题并提供解决方案
 * Deep analysis of ModelsLab API issues and provide solutions
 */

import fetch from 'node-fetch';

console.log('🔧 ModelsLab API深度诊断工具');
console.log('ModelsLab API Deep Diagnostics Tool\n');

const MODELSLAB_API_KEY = process.env.MODELSLAB_API_KEY || 'B1XaorSAPZrHQWVUesxvMAn9jXPNq8dbcDJCHtipgpwGWnRWCnHsbS6CTXAM';
const MODELSLAB_ENDPOINT = 'https://modelslab.com/api/v7/images/text-to-image';

// 不同的API配置测试
const TEST_CONFIGURATIONS = [
    {
        name: '基础配置',
        config: {
            key: MODELSLAB_API_KEY,
            prompt: 'professional interface design',
            width: 512,
            height: 512,
            samples: 1,
            num_inference_steps: 20,
            safety_checker: 'yes',
            enhance_prompt: 'no'
        }
    },
    {
        name: '标准SD配置',
        config: {
            key: MODELSLAB_API_KEY,
            prompt: 'professional interface design',
            width: 1024,
            height: 1024,
            samples: 1,
            num_inference_steps: 30,
            safety_checker: 'yes',
            enhance_prompt: 'yes',
            guidance_scale: 7.5,
            model_id: 'stable-diffusion-xl-base-1.0'
        }
    },
    {
        name: '简化配置',
        config: {
            key: MODELSLAB_API_KEY,
            prompt: 'simple test image',
            width: 512,
            height: 512
        }
    },
    {
        name: '中文测试配置',
        config: {
            key: MODELSLAB_API_KEY,
            prompt: 'Professional interface with Chinese text "测试"',
            width: 1024,
            height: 1024,
            samples: 1,
            num_inference_steps: 20,
            safety_checker: 'yes',
            enhance_prompt: 'yes'
        }
    }
];

// 测试单个配置
async function testConfiguration(testConfig) {
    console.log(`🧪 测试配置: ${testConfig.name}`);
    console.log(`📝 参数: ${JSON.stringify(testConfig.config, null, 2)}`);
    
    try {
        const response = await fetch(MODELSLAB_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testConfig.config)
        });
        
        console.log(`📥 响应状态: ${response.status} ${response.statusText}`);
        
        const responseText = await response.text();
        console.log(`📄 响应内容: ${responseText}`);
        
        let result;
        try {
            result = JSON.parse(responseText);
        } catch (e) {
            result = { raw_response: responseText };
        }
        
        if (response.ok) {
            console.log('✅ 配置测试成功');
            if (result.status === 'success' || result.output) {
                console.log('🎉 图片生成成功！');
                return { success: true, config: testConfig.name, result };
            } else if (result.status === 'processing') {
                console.log('⏳ 图片生成中...');
                return { success: true, config: testConfig.name, result, processing: true };
            }
        } else {
            console.log('❌ 配置测试失败');
        }
        
        return { success: false, config: testConfig.name, result, status: response.status };
        
    } catch (error) {
        console.log(`❌ 网络错误: ${error.message}`);
        return { success: false, config: testConfig.name, error: error.message };
    } finally {
        console.log('─'.repeat(60) + '\n');
    }
}

// 检查API密钥有效性
async function checkAPIKeyValidity() {
    console.log('🔑 检查API密钥有效性...\n');
    
    if (!MODELSLAB_API_KEY) {
        console.log('❌ API密钥未设置');
        return false;
    }
    
    console.log(`🔑 API密钥: ${MODELSLAB_API_KEY.substring(0, 10)}...${MODELSLAB_API_KEY.substring(-5)}`);
    console.log(`📡 API端点: ${MODELSLAB_ENDPOINT}`);
    
    // 尝试最简单的请求
    try {
        const response = await fetch(MODELSLAB_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                key: MODELSLAB_API_KEY,
                prompt: 'test'
            })
        });
        
        console.log(`📥 基础连接状态: ${response.status} ${response.statusText}`);
        
        if (response.status === 401) {
            console.log('❌ API密钥无效或已过期');
            return false;
        } else if (response.status === 403) {
            console.log('❌ API密钥权限不足');
            return false;
        } else if (response.status === 200) {
            console.log('✅ API密钥有效');
            return true;
        } else {
            console.log('⚠️  API密钥状态未知，但连接成功');
            return true;
        }
        
    } catch (error) {
        console.log(`❌ 连接失败: ${error.message}`);
        return false;
    }
}

// 检查ModelsLab服务状态
async function checkServiceStatus() {
    console.log('🌐 检查ModelsLab服务状态...\n');
    
    try {
        // 检查主页是否可访问
        const response = await fetch('https://modelslab.com/', {
            method: 'GET',
            timeout: 10000
        });
        
        console.log(`🌐 ModelsLab主页状态: ${response.status}`);
        
        if (response.ok) {
            console.log('✅ ModelsLab服务正常运行');
            return true;
        } else {
            console.log('⚠️  ModelsLab服务可能有问题');
            return false;
        }
        
    } catch (error) {
        console.log(`❌ 无法访问ModelsLab: ${error.message}`);
        return false;
    }
}

// 生成诊断报告
async function generateDiagnosticReport() {
    console.log('📊 生成ModelsLab API诊断报告...\n');
    
    const report = {
        timestamp: new Date().toISOString(),
        api_key_status: null,
        service_status: null,
        configuration_tests: [],
        recommendations: [],
        alternative_solutions: []
    };
    
    // 1. 检查服务状态
    report.service_status = await checkServiceStatus();
    
    // 2. 检查API密钥
    report.api_key_status = await checkAPIKeyValidity();
    
    // 3. 测试不同配置
    console.log('🧪 开始配置测试...\n');
    
    for (const testConfig of TEST_CONFIGURATIONS) {
        const result = await testConfiguration(testConfig);
        report.configuration_tests.push(result);
        
        // 避免API限流
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 4. 分析结果并生成建议
    const successfulTests = report.configuration_tests.filter(t => t.success);
    const failedTests = report.configuration_tests.filter(t => !t.success);
    
    console.log('📋 诊断结果分析:');
    console.log(`✅ 成功配置: ${successfulTests.length}/${report.configuration_tests.length}`);
    console.log(`❌ 失败配置: ${failedTests.length}/${report.configuration_tests.length}`);
    
    // 生成建议
    if (!report.api_key_status) {
        report.recommendations.push('检查API密钥是否正确和有效');
        report.recommendations.push('确认账户余额充足');
        report.recommendations.push('联系ModelsLab技术支持');
    }
    
    if (!report.service_status) {
        report.recommendations.push('ModelsLab服务可能暂时不可用');
        report.recommendations.push('稍后重试或使用备用API');
    }
    
    if (successfulTests.length === 0) {
        report.recommendations.push('所有配置测试失败，建议使用备用图片生成方案');
        report.alternative_solutions.push('使用Hugging Face Stable Diffusion API');
        report.alternative_solutions.push('使用OpenAI DALL-E API');
        report.alternative_solutions.push('使用本地Stable Diffusion');
        report.alternative_solutions.push('使用高质量模拟图片');
    } else if (successfulTests.length < report.configuration_tests.length) {
        report.recommendations.push('部分配置成功，建议使用成功的配置');
        const workingConfigs = successfulTests.map(t => t.config);
        report.recommendations.push(`推荐使用: ${workingConfigs.join(', ')}`);
    } else {
        report.recommendations.push('所有配置测试成功，API工作正常');
        report.recommendations.push('可以继续使用ModelsLab生成图片');
    }
    
    // 保存报告
    const fs = await import('fs');
    fs.default.writeFileSync('modelslab-diagnostic-report.json', JSON.stringify(report, null, 2));
    
    console.log('\n📄 详细诊断报告已保存到 modelslab-diagnostic-report.json');
    
    // 显示建议
    console.log('\n💡 建议操作:');
    report.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
    });
    
    if (report.alternative_solutions.length > 0) {
        console.log('\n🔄 备用方案:');
        report.alternative_solutions.forEach((sol, index) => {
            console.log(`   ${index + 1}. ${sol}`);
        });
    }
    
    return report;
}

// 提供快速修复建议
function provideQuickFixes() {
    console.log('⚡ ModelsLab API快速修复建议\n');
    
    console.log('🔧 立即尝试的解决方案:');
    console.log('1. 等待5-10分钟后重试（服务器可能临时过载）');
    console.log('2. 检查账户余额和API配额');
    console.log('3. 尝试更简单的提示词');
    console.log('4. 降低图片分辨率（512x512）');
    console.log('5. 减少推理步数（10-20步）');
    console.log('');
    
    console.log('🔄 备用方案:');
    console.log('1. 使用免费的Hugging Face API');
    console.log('2. 使用OpenAI DALL-E（付费但稳定）');
    console.log('3. 使用高质量模拟图片继续开发');
    console.log('4. 本地部署Stable Diffusion');
    console.log('');
    
    console.log('📞 获取帮助:');
    console.log('1. 访问ModelsLab文档: https://modelslab.com/docs');
    console.log('2. 联系ModelsLab技术支持');
    console.log('3. 检查ModelsLab状态页面');
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--quick-fix')) {
    provideQuickFixes();
} else if (args.includes('--key-check')) {
    checkAPIKeyValidity();
} else if (args.includes('--service-check')) {
    checkServiceStatus();
} else {
    generateDiagnosticReport();
}

export {
    generateDiagnosticReport,
    checkAPIKeyValidity,
    checkServiceStatus,
    testConfiguration,
    provideQuickFixes
};
