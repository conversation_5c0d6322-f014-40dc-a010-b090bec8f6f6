#!/usr/bin/env node

/**
 * 综合图标修复脚本
 * 自动检测和修复所有 Element Plus 图标问题
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 已知的图标替换映射
const ICON_REPLACEMENTS = {
  'Lightbulb': 'Star',
  'LineChart': 'TrendCharts',
  'Brain': 'Cpu',
  'DataAnalysis': 'Grid',
  'Shield': 'Lock',
  'PlayCircle': 'VideoPlay',
  'Play': 'VideoPlay',
  'CloudUpload': 'Upload',
  'PlayArrow': 'VideoPlay',
  'Iphone': 'Phone',
  'Cellphone': 'Phone',
  'Smartphone': 'Phone',
  'Monitor': 'TrendCharts',
  'Dashboard': 'DataBoard',
  'Analytics': 'TrendCharts',
  'Intelligence': 'Cpu',
  'Smart': 'Cpu',
  'Radar': 'Grid',
  'Book': 'Document',
  'Target': 'Trophy'
}

// 修复文件中的图标
function fixIconsInFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return { fixed: false, changes: [] }
  }

  let content = fs.readFileSync(filePath, 'utf8')
  let originalContent = content
  const changes = []

  // 修复导入语句
  Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
    // 修复导入语句中的图标
    const importRegex = new RegExp(`(import\\s*{[^}]*?)\\b${oldIcon}\\b([^}]*}\\s*from\\s*['"]@element-plus/icons-vue['"])`, 'g')
    if (importRegex.test(content)) {
      content = content.replace(importRegex, `$1${newIcon}$2`)
      changes.push({
        type: 'import',
        from: oldIcon,
        to: newIcon
      })
    }

    // 修复模板中的使用
    const templateRegex = new RegExp(`<el-icon><${oldIcon}\\s*/?></el-icon>`, 'g')
    if (templateRegex.test(content)) {
      content = content.replace(templateRegex, `<el-icon><${newIcon} /></el-icon>`)
      changes.push({
        type: 'template',
        from: oldIcon,
        to: newIcon
      })
    }

    // 修复字符串引用
    const stringRegex = new RegExp(`(['"\`])${oldIcon}\\1`, 'g')
    if (stringRegex.test(content)) {
      content = content.replace(stringRegex, `$1${newIcon}$1`)
      changes.push({
        type: 'string',
        from: oldIcon,
        to: newIcon
      })
    }

    // 修复 as 重命名的情况
    const asRegex = new RegExp(`\\b${oldIcon}\\s+as\\s+(\\w+)`, 'g')
    if (asRegex.test(content)) {
      content = content.replace(asRegex, `${newIcon} as $1`)
      changes.push({
        type: 'alias',
        from: oldIcon,
        to: newIcon
      })
    }
  })

  // 如果有变化，写入文件
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    return { fixed: true, changes }
  }

  return { fixed: false, changes: [] }
}

// 递归扫描目录
function scanAndFixDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const result = fixIconsInFile(fullPath)
        if (result.fixed) {
          results.push({
            file: path.relative(dir, fullPath),
            changes: result.changes
          })
        }
      }
    })
  }
  
  scan(dir)
  return results
}

// 验证 Element Plus 图标
async function verifyElementPlusIcons() {
  try {
    const icons = await import('@element-plus/icons-vue')
    const availableIcons = Object.keys(icons)
    
    console.log('📦 Element Plus 图标库验证:')
    console.log(`   总图标数量: ${availableIcons.length}`)
    
    // 检查替换映射中的图标是否存在
    Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
      const hasOld = availableIcons.includes(oldIcon)
      const hasNew = availableIcons.includes(newIcon)
      
      console.log(`   ${oldIcon} → ${newIcon}: ${hasOld ? '❌' : '✅'} → ${hasNew ? '✅' : '❌'}`)
    })
    
    return { availableIcons }
  } catch (error) {
    console.log('❌ 无法验证 Element Plus 图标库:', error.message)
    return { availableIcons: [] }
  }
}

// 主函数
async function main() {
  console.log('🔧 开始综合图标修复...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanAndFixDirectory(srcDir)
  
  console.log('📋 修复结果:')
  
  if (results.length === 0) {
    console.log('✅ 未发现需要修复的图标问题！')
  } else {
    console.log(`🔧 修复了 ${results.length} 个文件:\n`)
    
    results.forEach(({ file, changes }) => {
      console.log(`📄 ${file}:`)
      changes.forEach(change => {
        console.log(`   ${change.type}: ${change.from} → ${change.to}`)
      })
      console.log()
    })
  }
  
  // 验证图标库
  console.log('\n' + '='.repeat(50))
  await verifyElementPlusIcons()
  
  // 总结
  console.log('\n' + '='.repeat(50))
  console.log('📊 修复总结:')
  
  if (results.length === 0) {
    console.log('🎉 所有图标都正常！')
    console.log('✅ 应用现在可以正常运行，无图标错误')
  } else {
    console.log(`🔧 成功修复 ${results.length} 个文件`)
    console.log('✅ 所有问题图标已替换为可用图标')
    console.log('✅ 应用现在可以正常运行')
  }
  
  console.log('\n🔗 相关文档:')
  console.log('   Element Plus 图标: https://element-plus.org/zh-CN/component/icon.html')
}

// 运行修复
main().catch(console.error)
