import{_ as y,S as h,r as k,i as w,j as i,k as p,n as t,p as a,w as l,F as x,x as C,M as r,z as d}from"./index-b6a2842e.js";const z={class:"route-test-page"},R={class:"test-buttons"},T={class:"test-log"},B={class:"log-content"},L={class:"log-time"},P={class:"log-message"},D={__name:"RouteTestPage",setup(E){const o=h(),c=k([]),s=_=>{c.value.push({time:new Date().toLocaleTimeString(),message:_})},g=()=>{s("点击批量面试按钮，准备跳转到 /batch-interview-setup"),console.log("🧪 测试批量面试路由跳转"),o.push("/batch-interview-setup")},m=()=>{s("点击职位管理按钮，准备跳转到 /position-management"),console.log("🧪 测试职位管理路由跳转"),o.push("/position-management")},v=()=>{s("点击企业报表按钮，准备跳转到 /enterprise-reports"),console.log("🧪 测试企业报表路由跳转"),o.push("/enterprise-reports")},f=()=>{s("返回企业端仪表板 /enterprise"),console.log("🧪 返回企业端仪表板"),o.push("/enterprise")};return s("路由测试页面已加载"),(_,e)=>{const n=w("el-button");return i(),p("div",z,[e[5]||(e[5]=t("div",{class:"test-header"},[t("h1",null,"路由测试页面"),t("p",null,"测试企业端功能模块路由跳转")],-1)),t("div",R,[a(n,{type:"primary",size:"large",onClick:g,class:"test-btn"},{default:l(()=>e[0]||(e[0]=[r(" 测试批量面试路由 ")])),_:1,__:[0]}),a(n,{type:"success",size:"large",onClick:m,class:"test-btn"},{default:l(()=>e[1]||(e[1]=[r(" 测试职位管理路由 ")])),_:1,__:[1]}),a(n,{type:"warning",size:"large",onClick:v,class:"test-btn"},{default:l(()=>e[2]||(e[2]=[r(" 测试企业报表路由 ")])),_:1,__:[2]}),a(n,{type:"info",size:"large",onClick:f,class:"test-btn"},{default:l(()=>e[3]||(e[3]=[r(" 返回企业端仪表板 ")])),_:1,__:[3]})]),t("div",T,[e[4]||(e[4]=t("h3",null,"测试日志",-1)),t("div",B,[(i(!0),p(x,null,C(c.value,(u,b)=>(i(),p("div",{key:b,class:"log-item"},[t("span",L,d(u.time),1),t("span",P,d(u.message),1)]))),128))])])])}}},S=y(D,[["__scopeId","data-v-c02c4ff8"]]);export{S as default};
