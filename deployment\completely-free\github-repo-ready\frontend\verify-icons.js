/**
 * iFlytek面试系统图标验证脚本
 * 验证所有组件中使用的Element Plus图标是否存在
 */

import * as icons from '@element-plus/icons-vue'

// 定义各组件使用的图标
const componentIcons = {
  'SystemTestPage.vue': [
    'VideoPlay', 'Download', 'Delete', 'MagicStick', 'Star', 
    'Document', 'Share', 'Plus', 'View', 'List', 'CircleCheck', 'CircleClose'
  ],
  'ShareConfigDialog.vue': [
    'Setting', 'Lock', 'View', 'Download', 'Printer', 'Key', 'Share'
  ],
  'ExportProgressDialog.vue': [
    'Loading', 'Document', 'CircleCheck', 'CircleClose', 'Refresh', 'Check'
  ],
  'TextPrimaryInterviewPage.vue': [
    'Guide', 'List', 'Star', 'Close', 'User', 'Timer', 'Document', 
    'ArrowRight', 'Setting', 'InfoFilled', 'Tools', 'Promotion',
    'Plus', 'Delete', 'Right', 'QuestionFilled', 'Operation', 
    'VideoPause', 'VideoPlay', 'SwitchButton'
  ]
}

console.log('🔍 iFlytek面试系统图标验证报告')
console.log('=' .repeat(50))

let totalIcons = 0
let validIcons = 0
let invalidIcons = []

// 验证每个组件的图标
Object.entries(componentIcons).forEach(([component, iconList]) => {
  console.log(`\n📄 ${component}:`)
  
  iconList.forEach(iconName => {
    totalIcons++
    const exists = iconName in icons
    
    if (exists) {
      validIcons++
      console.log(`  ✅ ${iconName}`)
    } else {
      console.log(`  ❌ ${iconName} - 不存在`)
      invalidIcons.push({ component, icon: iconName })
    }
  })
})

// 生成总结报告
console.log('\n📊 验证总结:')
console.log('=' .repeat(30))
console.log(`总图标数: ${totalIcons}`)
console.log(`有效图标: ${validIcons}`)
console.log(`无效图标: ${totalIcons - validIcons}`)
console.log(`验证通过率: ${((validIcons / totalIcons) * 100).toFixed(1)}%`)

if (invalidIcons.length > 0) {
  console.log('\n❌ 需要修复的图标:')
  invalidIcons.forEach(({ component, icon }) => {
    console.log(`  ${component}: ${icon}`)
  })
  
  // 提供替代建议
  console.log('\n💡 建议的替代图标:')
  const suggestions = {
    'PlayArrow': 'VideoPlay',
    'BrainStorm': 'MagicStick',
    'Guide': 'QuestionFilled',
    'List': 'Menu'
  }
  
  invalidIcons.forEach(({ icon }) => {
    if (suggestions[icon]) {
      console.log(`  ${icon} → ${suggestions[icon]}`)
    }
  })
} else {
  console.log('\n🎉 所有图标验证通过！系统可以正常运行。')
}

console.log('\n🔗 访问地址:')
console.log('  - 系统测试: http://localhost:8080/system-test')
console.log('  - 面试页面: http://localhost:8080/text-interview')
console.log('  - 报告中心: http://localhost:8080/report-center')
