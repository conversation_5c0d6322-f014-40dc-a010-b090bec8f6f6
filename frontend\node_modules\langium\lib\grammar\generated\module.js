/******************************************************************************
 * This file was generated by langium-cli 3.3.0.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/
import { LangiumGrammarAstReflection } from '../../languages/generated/ast.js';
import { LangiumGrammarGrammar } from './grammar.js';
export const LangiumGrammarLanguageMetaData = {
    languageId: 'langium',
    fileExtensions: ['.langium'],
    caseInsensitive: false,
    mode: 'development'
};
export const LangiumGrammarParserConfig = {
    maxLookahead: 3,
};
export const LangiumGrammarGeneratedSharedModule = {
    AstReflection: () => new LangiumGrammarAstReflection()
};
export const LangiumGrammarGeneratedModule = {
    Grammar: () => LangiumGrammarGrammar(),
    LanguageMetaData: () => LangiumGrammarLanguageMetaData,
    parser: {
        ParserConfig: () => LangiumGrammarParserConfig
    }
};
//# sourceMappingURL=module.js.map