#!/usr/bin/env node

/**
 * 图片资源验证器
 * 检查所有图片文件的存在性、格式正确性和引用一致性
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 图片文件扩展名和对应的MIME类型
const imageExtensions = {
  '.jpg': ['image/jpeg', 'JPEG'],
  '.jpeg': ['image/jpeg', 'JPEG'],
  '.png': ['image/png', 'PNG'],
  '.svg': ['image/svg+xml', 'SVG'],
  '.gif': ['image/gif', 'GIF'],
  '.webp': ['image/webp', 'WebP']
};

// 检查文件是否存在
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

// 检查文件格式是否与扩展名匹配
function checkFileFormat(filePath) {
  if (!fs.existsSync(filePath)) {
    return { valid: false, reason: '文件不存在' };
  }

  const ext = path.extname(filePath).toLowerCase();
  const expectedFormat = imageExtensions[ext];
  
  if (!expectedFormat) {
    return { valid: false, reason: '不支持的图片格式' };
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查SVG文件
    if (ext === '.svg') {
      if (content.trim().startsWith('<svg')) {
        return { valid: true, format: 'SVG', size: content.length };
      } else {
        return { valid: false, reason: 'SVG文件格式不正确' };
      }
    }
    
    // 检查其他格式（简单检查是否为文本内容）
    if (content.includes('<svg') && (ext === '.jpg' || ext === '.jpeg' || ext === '.png')) {
      return { valid: false, reason: `文件内容是SVG但扩展名是${ext}` };
    }
    
    return { valid: true, format: expectedFormat[1], size: content.length };
  } catch (error) {
    // 二进制文件读取会出错，这是正常的
    try {
      const stats = fs.statSync(filePath);
      return { valid: true, format: expectedFormat[1], size: stats.size };
    } catch (e) {
      return { valid: false, reason: '无法读取文件' };
    }
  }
}

// 扫描目录中的图片文件
function scanImageFiles(dir) {
  const images = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.')) {
        scan(fullPath);
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase();
        if (imageExtensions[ext]) {
          const relativePath = path.relative(dir, fullPath);
          const formatCheck = checkFileFormat(fullPath);
          
          images.push({
            path: relativePath,
            fullPath,
            extension: ext,
            formatCheck,
            size: stat.size
          });
        }
      }
    });
  }
  
  scan(dir);
  return images;
}

// 扫描代码中的图片引用
function scanImageReferences(srcDir) {
  const references = [];
  
  function scanFile(filePath) {
    if (!fs.existsSync(filePath)) return [];
    
    const content = fs.readFileSync(filePath, 'utf8');
    const refs = [];
    
    // 匹配各种图片引用模式
    const patterns = [
      /['"`]\/images\/([^'"`]+\.(jpg|jpeg|png|svg|gif|webp))['"`]/gi,
      /src\s*=\s*['"`]\/images\/([^'"`]+\.(jpg|jpeg|png|svg|gif|webp))['"`]/gi,
      /avatar\s*:\s*['"`]\/images\/([^'"`]+\.(jpg|jpeg|png|svg|gif|webp))['"`]/gi
    ];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        refs.push({
          file: path.relative(srcDir, filePath),
          reference: `/images/${match[1]}`,
          line: content.substring(0, match.index).split('\n').length
        });
      }
    });
    
    return refs;
  }
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath);
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        references.push(...scanFile(fullPath));
      }
    });
  }
  
  scan(srcDir);
  return references;
}

// 主验证函数
function validateImageResources() {
  colorLog('cyan', '\n🔍 图片资源验证器');
  colorLog('cyan', '='.repeat(50));
  
  const publicDir = path.join(__dirname, 'public');
  const srcDir = path.join(__dirname, 'src');
  const imagesDir = path.join(publicDir, 'images');
  
  // 1. 扫描图片文件
  colorLog('blue', '\n📁 扫描图片文件...');
  const imageFiles = scanImageFiles(imagesDir);
  
  let validFiles = 0;
  let invalidFiles = 0;
  
  imageFiles.forEach(img => {
    const status = img.formatCheck.valid ? '✅' : '❌';
    const sizeKB = (img.size / 1024).toFixed(1);
    
    if (img.formatCheck.valid) {
      validFiles++;
      colorLog('green', `  ${status} ${img.path} (${img.formatCheck.format}, ${sizeKB}KB)`);
    } else {
      invalidFiles++;
      colorLog('red', `  ${status} ${img.path} - ${img.formatCheck.reason}`);
    }
  });
  
  // 2. 扫描代码引用
  colorLog('blue', '\n🔗 扫描代码引用...');
  const references = scanImageReferences(srcDir);
  
  let validRefs = 0;
  let invalidRefs = 0;
  
  references.forEach(ref => {
    const imagePath = path.join(publicDir, ref.reference);
    const exists = fs.existsSync(imagePath);
    const status = exists ? '✅' : '❌';
    
    if (exists) {
      validRefs++;
      colorLog('green', `  ${status} ${ref.file}:${ref.line} -> ${ref.reference}`);
    } else {
      invalidRefs++;
      colorLog('red', `  ${status} ${ref.file}:${ref.line} -> ${ref.reference} (文件不存在)`);
    }
  });
  
  // 3. 汇总报告
  colorLog('cyan', '\n📊 验证结果汇总');
  colorLog('cyan', '-'.repeat(30));
  
  console.log(`图片文件: ${validFiles} 有效, ${invalidFiles} 无效`);
  console.log(`代码引用: ${validRefs} 有效, ${invalidRefs} 无效`);
  
  const totalIssues = invalidFiles + invalidRefs;
  if (totalIssues === 0) {
    colorLog('green', '\n🎉 所有图片资源验证通过！');
    return true;
  } else {
    colorLog('red', `\n⚠️  发现 ${totalIssues} 个问题需要修复`);
    return false;
  }
}

// 运行验证
if (require.main === module) {
  const success = validateImageResources();
  process.exit(success ? 0 : 1);
}

module.exports = { validateImageResources };
