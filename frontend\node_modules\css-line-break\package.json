{"name": "css-line-break", "version": "2.1.0", "description": "", "main": "dist/css-line-break.umd.js", "module": "dist/css-line-break.es5.js", "typings": "dist/types/index.d.ts", "scripts": {"prebuild": "rimraf dist/", "build": "tsc --module commonjs && rollup -c rollup.config.ts", "format": "prettier --write \"{src,scripts}/**/*.ts\"", "lint": "tslint -c tslint.json --project tsconfig.json -t codeFrame src/**/*.ts tests/**/*.ts scripts/**/*.ts", "generate-trie": "ts-node scripts/generate_line_break_trie.ts", "generate-tests": "ts-node scripts/generate_line_break_tests.ts", "mocha": "mocha --require ts-node/register tests/*.ts", "test": "npm run lint && npm run mocha", "release": "standard-version"}, "repository": {"type": "git", "url": "git+ssh://**************/niklasvh/css-line-break.git"}, "keywords": ["white-space", "line-break", "word-break", "word-wrap", "overflow-wrap"], "dependencies": {"utrie": "^1.0.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/mocha": "^8.2.2", "@types/node": "^16.0.0", "mocha": "9.0.2", "prettier": "^2.3.2", "rimraf": "3.0.2", "rollup": "^2.52.7", "rollup-plugin-json": "^4.0.0", "rollup-plugin-sourcemaps": "^0.6.3", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.3.5"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://hertzen.com"}, "license": "MIT", "bugs": {"url": "https://github.com/niklasvh/css-line-break/issues"}, "homepage": "https://github.com/niklasvh/css-line-break#readme"}