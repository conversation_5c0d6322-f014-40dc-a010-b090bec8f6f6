{"version": 3, "file": "document-builder.js", "sourceRoot": "", "sources": ["../../src/workspace/document-builder.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AASpD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACxG,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sCAAsC,CAAC;AAC1E,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAmG/C,MAAM,OAAO,sBAAsB;IAqB/B,YAAY,QAAmC;QAnB/C,uBAAkB,GAAiB;YAC/B,sHAAsH;YACtH,UAAU,EAAE;gBACR,UAAU,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC;aACnC;SACJ,CAAC;QAOiB,oBAAe,GAA6B,EAAE,CAAC;QAC/C,wBAAmB,GAAG,IAAI,QAAQ,EAAwC,CAAC;QAC3E,2BAAsB,GAAG,IAAI,QAAQ,EAAwC,CAAC;QAC9E,eAAU,GAAG,IAAI,GAAG,EAA8B,CAAC;QACnD,yBAAoB,GAAG,IAAI,GAAG,EAA0B,CAAC;QAClE,iBAAY,GAAG,aAAa,CAAC,OAAO,CAAC;QAG3C,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC5D,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC,SAAS,CAAC,sBAAsB,CAAC;QACxE,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC;QACpD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,KAAK,CAAoB,SAAoC,EAAE,UAAwB,EAAE,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;;QACjI,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,QAAQ,CAAC,KAAK,KAAK,aAAa,CAAC,SAAS,EAAE,CAAC;gBAC7C,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,SAAS,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;oBAChE,yCAAyC;oBACzC,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC,iBAAiB,CAAC;oBACjD,QAAQ,CAAC,WAAW,GAAG,SAAS,CAAC;oBACjC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAChC,CAAC;qBAAM,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;oBAChD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAC5C,MAAM,kBAAkB,GAAG,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,0CAAE,gBAAgB,CAAC;oBAChE,IAAI,kBAAkB,EAAE,CAAC;wBACrB,wGAAwG;wBACxG,uFAAuF;wBACvF,MAAM,aAAa,GAAG,MAAA,OAAO,CAAC,UAAU,CAAC,UAAU,mCAAI,kBAAkB,CAAC,GAA2B,CAAC;wBACtG,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9E,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACxB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE;gCACrB,SAAS,EAAE,KAAK;gCAChB,OAAO,EAAE;oCACL,UAAU,kCACH,OAAO,CAAC,UAAU,KACrB,UAAU,GACb;iCACJ;gCACD,MAAM,EAAE,UAAU,CAAC,MAAM;6BAC5B,CAAC,CAAC;4BACH,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC,iBAAiB,CAAC;wBACrD,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,6CAA6C;gBAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC;QAC1C,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACrD,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAAc,EAAE,OAAc,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAC7E,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC;QAC1C,gEAAgE;QAChE,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACzC,CAAC;QACD,qFAAqF;QACrF,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;YAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACzE,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,2CAA2C;gBAC3C,4EAA4E;gBAC5E,sEAAsE;gBACtE,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC,CAAC;gBAC5F,WAAW,CAAC,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC;gBAC1C,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACnD,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClD,CAAC;QACD,oGAAoG;QACpG,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAC1F,IAAI,CAAC,gBAAgB,CAAC,GAAG;aACpB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;aAChG,OAAO,CAAC,GAAG,CAAC,EAAE;YACX,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;YAC3E,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACnB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC;YAC9D,GAAG,CAAC,WAAW,GAAG,SAAS,CAAC;QAChC,CAAC,CAAC,CAAC;QACP,iCAAiC;QACjC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxC,yEAAyE;QACzE,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAErC,wDAAwD;QACxD,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CACvC,IAAI,CAAC,gBAAgB,CAAC,GAAG;aACpB,MAAM,CAAC,GAAG,CAAC,EAAE;;YACV,6FAA6F;YAC7F,OAAA,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM;gBAChC,oEAAoE;mBACjE,CAAC,CAAA,MAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,0CAAE,SAAS,CAAA,CAAA;SAAA,CACzD;aACA,OAAO,EAAE,CACjB,CAAC;QACF,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACtF,CAAC;IAES,KAAK,CAAC,UAAU,CAAC,OAAc,EAAE,OAAc;QACrD,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;IAED;;;;;;OAMG;IACO,aAAa,CAAC,SAA4B;QAChD,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAEjC,OAAO,IAAI,GAAG,KAAK,EAAE,CAAC;YAClB,OAAO,IAAI,GAAG,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACtE,IAAI,EAAE,CAAC;YACX,CAAC;YAED,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3D,KAAK,EAAE,CAAC;YACZ,CAAC;YAED,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;gBACf,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,eAAe,CAAC,GAAoB;;QACxC,OAAO,OAAO,CAAC,MAAA,IAAI,CAAC,aAAa,0CAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,QAAyB,EAAE,WAAwB;QACtE,qFAAqF;QACrF,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,oEAAoE;QACpE,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC/D,CAAC;IAED,QAAQ,CAAC,QAAgC;QACrC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACO,KAAK,CAAC,cAAc,CAAC,SAA4B,EAAE,OAAqB,EAAE,WAA8B;QAC9G,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,mBAAmB;QACnB,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,EAAE,CACzE,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC,CACvD,CAAC;QACF,mBAAmB;QACnB,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,cAAc,EAAE,WAAW,EAAE,GAAG,CAAC,EAAE,CACjF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG,EAAE,WAAW,CAAC,CACpD,CAAC;QACF,oBAAoB;QACpB,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,cAAc,EAAE,WAAW,EAAE,KAAK,EAAC,GAAG,EAAC,EAAE;YACvF,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC;YAC/F,GAAG,CAAC,iBAAiB,GAAG,MAAM,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QACH,aAAa;QACb,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,EAAE;YACzE,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;YAC3E,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QACH,sBAAsB;QACtB,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,iBAAiB,EAAE,WAAW,EAAE,GAAG,CAAC,EAAE,CACpF,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,WAAW,CAAC,CACvD,CAAC;QACF,gBAAgB;QAChB,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;QACxE,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,SAAS,EAAE,WAAW,EAAE,GAAG,CAAC,EAAE,CAChF,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,CAClC,CAAC;QAEF,oGAAoG;QACpG,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtD,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;YAC3B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACO,YAAY,CAAC,SAA4B,EAAE,OAAqB;QACtE,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC1B,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACvC,iGAAiG;YACjG,4FAA4F;YAC5F,uCAAuC;YACvC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE;oBACrB,SAAS,EAAE,KAAK;oBAChB,OAAO;oBACP,MAAM,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM;iBACxB,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACO,KAAK,CAAC,aAAa,CAAC,SAA4B,EAAE,WAA0B,EAAE,WAA8B,EAClH,QAA8D;QAC9D,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC;QAClE,KAAK,MAAM,QAAQ,IAAI,QAAQ,EAAE,CAAC;YAC9B,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzB,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC;YAC7B,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACvE,CAAC;QAED,gHAAgH;QAChH,sGAAsG;QACtG,mEAAmE;QACnE,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC;QAC3E,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACvE,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAED,YAAY,CAAC,WAA0B,EAAE,QAA+B;QACpE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAED,eAAe,CAAC,WAA0B,EAAE,QAA+B;QACvE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QACvD,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE;YAC1B,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACP,CAAC;IAID,SAAS,CAAC,KAAoB,EAAE,UAAoC,EAAE,WAA+B;QACjG,IAAI,GAAG,GAAoB,SAAS,CAAC;QACrC,IAAI,UAAU,IAAI,MAAM,IAAI,UAAU,EAAE,CAAC;YACrC,GAAG,GAAG,UAAU,CAAC;QACrB,CAAC;aAAM,CAAC;YACJ,WAAW,GAAG,UAAU,CAAC;QAC7B,CAAC;QACD,WAAW,aAAX,WAAW,cAAX,WAAW,IAAX,WAAW,GAAK,iBAAiB,CAAC,IAAI,EAAC;QACvC,IAAI,GAAG,EAAE,CAAC;YACN,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC;gBACrC,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,EAAE,CAAC;YAC7B,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,WAAW,CAAC,uBAAuB,EAAE,CAAC;YAC7C,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE;gBAClD,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC1B,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC3B,IAAI,GAAG,EAAE,CAAC;oBACN,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;oBACxD,OAAO,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,GAAG,CAAC,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,SAAS,CAAC,CAAC;gBACvB,CAAC;YACL,CAAC,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,WAAY,CAAC,uBAAuB,CAAC,GAAG,EAAE;gBAC/D,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC1B,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAES,KAAK,CAAC,mBAAmB,CAAC,QAAyB,EAAE,KAAoB,EAAE,WAA8B;QAC/G,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;QACxC,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC;gBACD,MAAM,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC1C,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,6BAA6B;gBAC7B,kDAAkD;gBAClD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC7B,MAAM,GAAG,CAAC;gBACd,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,SAA4B,EAAE,KAAoB,EAAE,WAA8B;QAC/G,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,mDAAmD;YACnD,OAAO;QACX,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;QACxC,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;YACnC,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,cAAc,CAAC,QAAyB;QAC9C,OAAO,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,QAAQ,CAAC,QAAyB,EAAE,WAA8B;;QAC9E,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC;QAC9F,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC;QACpE,MAAM,OAAO,GAAG,OAAO,iBAAiB,KAAK,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC;QACtF,MAAM,WAAW,GAAG,MAAM,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QACrF,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACvB,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACJ,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;QACvC,CAAC;QAED,qEAAqE;QACrE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE,CAAC;YACR,MAAA,KAAK,CAAC,MAAM,oCAAZ,KAAK,CAAC,MAAM,GAAK,EAAE,EAAC;YACpB,MAAM,aAAa,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,mCAAI,kBAAkB,CAAC,GAAG,CAAC;YACpE,IAAI,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAChC,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;YACvD,CAAC;QACL,CAAC;IACL,CAAC;IAES,eAAe,CAAC,QAAyB;;QAC/C,OAAO,MAAA,MAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,0CAAE,OAAO,mCAAI,EAAE,CAAC;IACvE,CAAC;CAEJ"}