// 最终ECharts修复验证脚本
// 在浏览器控制台中运行此脚本来验证所有修复是否生效

console.log('🔧 开始最终ECharts修复验证...')

// 1. 检查全局ECharts状态
function checkGlobalEChartsStatus() {
  console.log('\n=== 1. 全局ECharts状态检查 ===')
  
  const results = {
    echartsLoaded: typeof echarts !== 'undefined',
    echartsReady: window.ECHARTS_READY === true,
    managerAvailable: false,
    vchartRegistered: false
  }
  
  // 检查ECharts管理器
  try {
    const app = document.querySelector('#app').__vue_app__
    if (app && app._context.provides && app._context.provides.echartsManager) {
      results.managerAvailable = true
    }
  } catch (error) {
    console.warn('无法检查ECharts管理器状态')
  }
  
  // 检查VChart组件
  try {
    const app = document.querySelector('#app').__vue_app__
    if (app && app._context.components && app._context.components.VChart) {
      results.vchartRegistered = true
    }
  } catch (error) {
    console.warn('无法检查VChart组件状态')
  }
  
  console.log(`ECharts加载: ${results.echartsLoaded ? '✅' : '❌'}`)
  console.log(`ECharts准备就绪: ${results.echartsReady ? '✅' : '❌'}`)
  console.log(`ECharts管理器: ${results.managerAvailable ? '✅' : '⚠️'}`)
  console.log(`VChart组件: ${results.vchartRegistered ? '✅' : '❌'}`)
  
  return results
}

// 2. 检查图表容器状态
function checkChartContainers() {
  console.log('\n=== 2. 图表容器状态检查 ===')
  
  const selectors = [
    '[ref*="chart"]',
    '.chart-container',
    '[id*="chart"]',
    '.echarts'
  ]
  
  const containers = []
  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    elements.forEach(el => containers.push(el))
  })
  
  console.log(`找到 ${containers.length} 个潜在图表容器`)
  
  const results = containers.map((container, index) => {
    const rect = container.getBoundingClientRect()
    const result = {
      index: index + 1,
      element: container.tagName.toLowerCase(),
      className: container.className,
      width: rect.width,
      height: rect.height,
      visible: rect.width > 0 && rect.height > 0,
      hasEChartsInstance: !!container._echarts_instance_
    }
    
    const status = result.visible ? '✅' : '❌'
    console.log(`容器 ${result.index}: ${result.element} ${result.width}x${result.height} ${status}`)
    
    return result
  })
  
  const validContainers = results.filter(r => r.visible).length
  console.log(`有效容器: ${validContainers}/${containers.length}`)
  
  return results
}

// 3. 测试ECharts功能
function testEChartsFunction() {
  console.log('\n=== 3. ECharts功能测试 ===')
  
  const results = {
    canCreateChart: false,
    canSetOption: false,
    canDispose: false,
    error: null
  }
  
  try {
    // 创建测试容器
    const testContainer = document.createElement('div')
    testContainer.style.width = '400px'
    testContainer.style.height = '300px'
    testContainer.style.position = 'absolute'
    testContainer.style.top = '-9999px'
    testContainer.style.left = '-9999px'
    document.body.appendChild(testContainer)
    
    // 测试图表创建
    const testChart = echarts.init(testContainer)
    results.canCreateChart = true
    console.log('✅ 图表创建成功')
    
    // 测试配置设置
    testChart.setOption({
      title: { text: '测试图表' },
      xAxis: { type: 'category', data: ['A', 'B', 'C'] },
      yAxis: { type: 'value' },
      series: [{ data: [1, 2, 3], type: 'line' }]
    })
    results.canSetOption = true
    console.log('✅ 图表配置成功')
    
    // 测试销毁
    testChart.dispose()
    results.canDispose = true
    console.log('✅ 图表销毁成功')
    
    // 清理
    document.body.removeChild(testContainer)
    
  } catch (error) {
    results.error = error.message
    console.log(`❌ ECharts功能测试失败: ${error.message}`)
  }
  
  return results
}

// 4. 检查控制台错误
function checkConsoleErrors() {
  console.log('\n=== 4. 控制台错误检查 ===')
  
  const errorPatterns = [
    'registers.registerChartView is not a function',
    'Can\'t get DOM width or height',
    'registerSeriesModel',
    'registerChartView'
  ]
  
  console.log('请检查控制台是否还有以下ECharts相关错误:')
  errorPatterns.forEach(pattern => {
    console.log(`  - ${pattern}`)
  })
  
  console.log('\n如果没有看到上述错误，说明核心问题已解决 ✅')
  
  // 检查是否有ECharts错误检测器的报告
  if (window.echartsErrorDetector) {
    console.log('\n📊 ECharts错误检测器状态:')
    const report = window.echartsErrorDetector.generateDiagnosticReport()
    console.log(`错误数量: ${report.summary.totalErrors}`)
    console.log(`警告数量: ${report.summary.totalWarnings}`)
    console.log(`配置状态: ${report.summary.configurationOK ? '✅' : '❌'}`)
  }
  
  return true
}

// 5. 性能检查
function checkPerformance() {
  console.log('\n=== 5. 性能检查 ===')
  
  const results = {
    pageLoadTime: performance.now(),
    memoryUsage: null,
    chartCount: 0
  }
  
  // 检查内存使用（如果可用）
  if (performance.memory) {
    results.memoryUsage = {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
    }
    console.log(`内存使用: ${results.memoryUsage.used}MB / ${results.memoryUsage.total}MB`)
  }
  
  // 统计图表数量
  const chartElements = document.querySelectorAll('[class*="chart"], [ref*="chart"]')
  results.chartCount = chartElements.length
  console.log(`页面图表数量: ${results.chartCount}`)
  
  console.log(`页面加载时间: ${Math.round(results.pageLoadTime)}ms`)
  
  return results
}

// 6. 生成最终报告
function generateFinalReport() {
  console.log('\n=== 📋 最终ECharts修复验证报告 ===')
  
  const globalStatus = checkGlobalEChartsStatus()
  const containers = checkChartContainers()
  const functionality = testEChartsFunction()
  const consoleCheck = checkConsoleErrors()
  const performance = checkPerformance()
  
  const report = {
    timestamp: new Date().toISOString(),
    globalStatus,
    containers,
    functionality,
    performance,
    summary: {
      echartsWorking: globalStatus.echartsLoaded && globalStatus.echartsReady,
      containersValid: containers.filter(c => c.visible).length > 0,
      functionalityOK: functionality.canCreateChart && functionality.canSetOption && functionality.canDispose,
      overallStatus: 'unknown'
    }
  }
  
  // 计算总体状态
  const checks = [
    report.summary.echartsWorking,
    report.summary.containersValid,
    report.summary.functionalityOK
  ]
  
  const passedChecks = checks.filter(Boolean).length
  const totalChecks = checks.length
  
  if (passedChecks === totalChecks) {
    report.summary.overallStatus = 'success'
    console.log('\n🎉 所有检查通过！ECharts修复成功！')
    console.log('✅ ECharts已正确加载和配置')
    console.log('✅ 图表容器状态正常')
    console.log('✅ 核心功能工作正常')
  } else if (passedChecks > 0) {
    report.summary.overallStatus = 'partial'
    console.log(`\n⚠️ 部分检查通过 (${passedChecks}/${totalChecks})`)
    console.log('需要进一步检查和修复')
  } else {
    report.summary.overallStatus = 'failed'
    console.log('\n❌ 检查失败，需要重新修复')
  }
  
  console.log(`\n📊 检查结果: ${passedChecks}/${totalChecks} 通过`)
  console.log(`🕐 检查时间: ${new Date().toLocaleString()}`)
  
  // 存储报告到全局变量
  window.echartsVerificationReport = report
  
  return report
}

// 执行验证
const finalReport = generateFinalReport()

// 导出验证函数
window.echartsVerification = {
  checkGlobalEChartsStatus,
  checkChartContainers,
  testEChartsFunction,
  checkConsoleErrors,
  checkPerformance,
  generateFinalReport,
  getLastReport: () => window.echartsVerificationReport
}

console.log('\n💡 提示: 可以通过 window.echartsVerification 访问所有验证函数')
console.log('💡 最新报告保存在 window.echartsVerificationReport 中')
