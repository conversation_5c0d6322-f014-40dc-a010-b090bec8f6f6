{"version": 3, "file": "declared-types.js", "sourceRoot": "", "sources": ["../../../../src/grammar/type-system/type-collector/declared-types.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAGhF,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,qCAAqC,CAAC;AAEvF,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAC;AAC9G,OAAO,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC;AACjG,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAE9D,MAAM,UAAU,oBAAoB,CAAC,UAAuB,EAAE,MAAc;IACxE,MAAM,aAAa,GAAkB,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;IAEpE,iBAAiB;IACjB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;QAC5B,MAAM,UAAU,GAAoB,EAAE,CAAC;QACvC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAkB;gBAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,QAAQ,EAAE,SAAS,CAAC,UAAU;gBAC9B,QAAQ,EAAE,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;gBAC9B,IAAI,EAAE,4BAA4B,CAAC,SAAS,CAAC,IAAI,CAAC;aACrD,CAAC;YACF,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;gBACzB,QAAQ,CAAC,YAAY,GAAG,sBAAsB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC3E,CAAC;YACD,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QACrC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACtC,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;gBAChB,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/C,CAAC;QACL,CAAC;QACD,MAAM,aAAa,GAAmB;YAClC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,UAAU;YACtB,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,IAAI,GAAG,EAAE;SACtB,CAAC;QACF,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED,YAAY;IACZ,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QACzB,MAAM,SAAS,GAAe;YAC1B,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,4BAA4B,CAAC,KAAK,CAAC,IAAI,CAAC;YAC9C,UAAU,EAAE,IAAI,GAAG,EAAE;YACrB,QAAQ,EAAE,IAAI,GAAG,EAAE;SACtB,CAAC;QACF,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAED,OAAO,aAAa,CAAC;AACzB,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAqB;IACjD,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,OAAO,OAAO,CAAC,IAAI,CAAC;IACxB,CAAC;SAAM,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;QACjC,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACxD,CAAC;SAAM,CAAC;QACJ,OAAO,OAAO,CAAC,KAAK,CAAC;IACzB,CAAC;AACL,CAAC;AAED,MAAM,UAAU,4BAA4B,CAAC,IAAoB;IAC7D,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QACpB,OAAO;YACH,WAAW,EAAE,4BAA4B,CAAC,IAAI,CAAC,WAAW,CAAC;SAC9D,CAAC;IACN,CAAC;SAAM,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,OAAO;YACH,aAAa,EAAE,4BAA4B,CAAC,IAAI,CAAC,aAAa,CAAC;SAClE,CAAC;IACN,CAAC;SAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,4BAA4B,CAAC;SACtD,CAAC;IACN,CAAC;SAAM,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,IAAI,KAAyB,CAAC;QAC9B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;YAC3B,OAAO;gBACH,SAAS,EAAE,KAAK;aACnB,CAAC;QACN,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACzB,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;YACxB,OAAO;gBACH,MAAM,EAAE,KAAK;aAChB,CAAC;QACN,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;YAC7B,MAAM,KAAK,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChC,OAAO;wBACH,SAAS,EAAE,KAAK;qBACnB,CAAC;gBACN,CAAC;qBAAM,CAAC;oBACJ,OAAO;wBACH,KAAK;qBACR,CAAC;gBACN,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IACD,OAAO;QACH,SAAS,EAAE,SAAS;KACvB,CAAC;AACN,CAAC"}