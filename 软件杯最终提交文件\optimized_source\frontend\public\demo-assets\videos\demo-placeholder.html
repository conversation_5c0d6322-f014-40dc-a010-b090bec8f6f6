<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示视频占位符</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .video-placeholder {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            border: 2px dashed #ccc;
        }
        .play-icon {
            font-size: 60px;
            color: #667eea;
            margin-bottom: 20px;
        }
        .video-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .video-desc {
            font-size: 16px;
            color: #666;
            text-align: center;
            max-width: 500px;
        }
        .info-box {
            background: #f8f9ff;
            border: 1px solid #e0e6ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .file-list {
            list-style: none;
            padding: 0;
        }
        .file-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            color: #666;
        }
        .file-list li:last-child {
            border-bottom: none;
        }
        .status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 多模态面试系统演示视频</h1>
        
        <div class="video-placeholder">
            <div class="play-icon">▶️</div>
            <div class="video-title">演示视频占位符</div>
            <div class="video-desc">
                这里将显示多模态面试评估系统的完整功能演示，
                包括AI面试官交互、实时评估、报告生成等核心功能。
            </div>
        </div>
        
        <div class="status">
            <strong>📝 说明：</strong> 当前为演示占位符，实际部署时请替换为真实的演示视频文件。
        </div>
        
        <div class="info-box">
            <div class="info-title">📁 需要的视频文件：</div>
            <ul class="file-list">
                <li>demo-complete.mp4 - 系统完整演示视频</li>
                <li>ai-interview-demo.mp4 - AI领域面试演示</li>
                <li>bigdata-interview-demo.mp4 - 大数据领域面试演示</li>
                <li>iot-interview-demo.mp4 - 物联网领域面试演示</li>
                <li>feature-showcase.mp4 - 功能特性展示</li>
                <li>technical-architecture.mp4 - 技术架构介绍</li>
            </ul>
        </div>
        
        <div class="info-box">
            <div class="info-title">🎯 视频内容建议：</div>
            <ul class="file-list">
                <li>系统登录和界面导航演示</li>
                <li>AI面试官智能对话展示</li>
                <li>多模态数据采集过程</li>
                <li>实时评估算法工作原理</li>
                <li>6大核心能力评估展示</li>
                <li>可视化报告生成过程</li>
                <li>iFlytek星火大模型集成亮点</li>
                <li>不同技术领域面试场景</li>
            </ul>
        </div>
        
        <div class="info-box">
            <div class="info-title">⚙️ 技术规格建议：</div>
            <ul class="file-list">
                <li>分辨率：1920x1080 (Full HD)</li>
                <li>帧率：30fps</li>
                <li>格式：MP4 (H.264编码)</li>
                <li>音频：AAC编码，48kHz</li>
                <li>时长：每个演示视频5-15分钟</li>
                <li>文件大小：建议控制在50MB以内</li>
            </ul>
        </div>
    </div>
</body>
</html>
