<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用状态检查</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .status-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #10b981;
            background: #f0f9ff;
            color: #065f46;
        }
        .status-error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .iframe-container {
            margin-top: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            height: 500px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="status-container">
        <h1>🔍 iFlytek Spark 应用状态检查</h1>
        
        <div>
            <button class="test-button" onclick="checkMainApp()">检查主应用</button>
            <button class="test-button" onclick="loadMainApp()">加载主应用</button>
            <button class="test-button" onclick="checkConsoleErrors()">检查控制台错误</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>

        <div id="status-results"></div>

        <div class="iframe-container" id="iframe-container" style="display: none;">
            <iframe id="main-app-frame" src=""></iframe>
        </div>
    </div>

    <script>
        const resultsContainer = document.getElementById('status-results');
        let errorCount = 0;

        function addResult(message, isError = false) {
            const div = document.createElement('div');
            div.className = `status-item ${isError ? 'status-error' : ''}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsContainer.appendChild(div);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        function clearResults() {
            resultsContainer.innerHTML = '';
            errorCount = 0;
        }

        function checkMainApp() {
            addResult('🔍 开始检查主应用状态...');
            
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        addResult(`✅ 主应用响应正常 (状态码: ${response.status})`);
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(html => {
                    // 检查HTML内容
                    if (html.includes('<div id="app">')) {
                        addResult('✅ HTML包含app容器');
                    } else {
                        addResult('❌ HTML缺少app容器', true);
                    }
                    
                    if (html.includes('main.js')) {
                        addResult('✅ HTML包含main.js引用');
                    } else {
                        addResult('❌ HTML缺少main.js引用', true);
                    }
                    
                    if (html.includes('iFlytek')) {
                        addResult('✅ HTML包含iFlytek品牌信息');
                    } else {
                        addResult('⚠️ HTML可能缺少品牌信息');
                    }
                })
                .catch(error => {
                    addResult(`❌ 主应用检查失败: ${error.message}`, true);
                });
        }

        function loadMainApp() {
            addResult('🔍 在iframe中加载主应用...');
            
            const iframe = document.getElementById('main-app-frame');
            const container = document.getElementById('iframe-container');
            
            iframe.src = 'http://localhost:5173/';
            container.style.display = 'block';
            
            iframe.onload = function() {
                addResult('✅ 主应用在iframe中加载成功');
                
                // 延迟检查iframe内容
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (iframeDoc) {
                            const appElement = iframeDoc.getElementById('app');
                            if (appElement) {
                                addResult('✅ 在iframe中找到app元素');
                                
                                // 检查app元素是否有内容
                                if (appElement.innerHTML.trim()) {
                                    addResult('✅ app元素包含内容');
                                } else {
                                    addResult('⚠️ app元素为空，可能Vue应用未正确挂载', true);
                                }
                            } else {
                                addResult('❌ 在iframe中未找到app元素', true);
                            }
                        }
                    } catch (e) {
                        addResult('⚠️ 无法访问iframe内容（可能是跨域限制）');
                    }
                }, 2000);
            };
            
            iframe.onerror = function() {
                addResult('❌ 主应用在iframe中加载失败', true);
            };
            
            // 设置超时
            setTimeout(() => {
                if (!iframe.contentDocument && !iframe.contentWindow) {
                    addResult('⚠️ iframe加载超时', true);
                }
            }, 10000);
        }

        function checkConsoleErrors() {
            addResult('🔍 开始监控控制台错误...');
            
            // 重置错误计数
            errorCount = 0;
            
            // 捕获JavaScript错误
            const originalError = console.error;
            console.error = function(...args) {
                errorCount++;
                addResult(`❌ 控制台错误 #${errorCount}: ${args.join(' ')}`, true);
                originalError.apply(console, args);
            };
            
            // 捕获未处理的错误
            window.addEventListener('error', (event) => {
                errorCount++;
                addResult(`❌ JavaScript错误 #${errorCount}: ${event.message} (${event.filename}:${event.lineno})`, true);
            });
            
            // 捕获Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                errorCount++;
                addResult(`❌ 未处理的Promise拒绝 #${errorCount}: ${event.reason}`, true);
            });
            
            addResult('✅ 错误监控已启动');
            
            // 5秒后报告结果
            setTimeout(() => {
                if (errorCount === 0) {
                    addResult('✅ 未发现控制台错误');
                } else {
                    addResult(`⚠️ 发现 ${errorCount} 个错误，请检查上述错误信息`, true);
                }
            }, 5000);
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            addResult('🚀 应用状态检查工具已启动');
            setTimeout(checkMainApp, 1000);
        });

        // 全局错误捕获
        window.addEventListener('error', (event) => {
            addResult(`❌ 页面错误: ${event.message}`, true);
        });

        window.addEventListener('unhandledrejection', (event) => {
            addResult(`❌ 未处理的Promise: ${event.reason}`, true);
        });
    </script>
</body>
</html>
