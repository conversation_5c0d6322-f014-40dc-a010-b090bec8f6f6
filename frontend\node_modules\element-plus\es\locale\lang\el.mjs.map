{"version": 3, "file": "el.mjs", "sources": ["../../../../../packages/locale/lang/el.ts"], "sourcesContent": ["export default {\n  name: 'el',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Εντάξει',\n      clear: 'Καθαρισμός',\n    },\n    datepicker: {\n      now: 'Τώρα',\n      today: 'Σήμερα',\n      cancel: 'Ακύρωση',\n      clear: 'Καθαρισμός',\n      confirm: 'Εντάξει',\n      selectDate: 'Επιλέξτε ημέρα',\n      selectTime: 'Επιλέξτε ώρα',\n      startDate: 'Ημερομηνία Έναρξης',\n      startTime: 'Ωρα Έναρξης',\n      endDate: 'Ημερομηνία Λήξης',\n      endTime: 'Ωρα Λήξης',\n      prevYear: 'Προηγούμενο Έτος',\n      nextYear: 'Επόμενο Έτος',\n      prevMonth: 'Προηγούμενος <PERSON>',\n      nextMonth: 'Επό<PERSON>ενος <PERSON>',\n      year: 'Έτος',\n      month1: 'Ιανουάριος',\n      month2: 'Φεβρουάριος',\n      month3: 'Μάρτιος',\n      month4: 'Απρίλιος',\n      month5: 'Μάιος',\n      month6: 'Ιούνιος',\n      month7: 'Ιούλιος',\n      month8: 'Αύγουστος',\n      month9: 'Σεπτέμβριος',\n      month10: 'Οκτώβριος',\n      month11: 'Νοέμβριος',\n      month12: 'Δεκέμβριος',\n      // week: 'εβδομάδα',\n      weeks: {\n        sun: 'Κυρ',\n        mon: 'Δευ',\n        tue: 'Τρι',\n        wed: 'Τετ',\n        thu: 'Πεμ',\n        fri: 'Παρ',\n        sat: 'Σαβ',\n      },\n      months: {\n        jan: 'Ιαν',\n        feb: 'Φεβ',\n        mar: 'Μαρ',\n        apr: 'Απρ',\n        may: 'Μαϊ',\n        jun: 'Ιουν',\n        jul: 'Ιουλ',\n        aug: 'Αυγ',\n        sep: 'Σεπ',\n        oct: 'Οκτ',\n        nov: 'Νοε',\n        dec: 'Δεκ',\n      },\n    },\n    select: {\n      loading: 'Φόρτωση',\n      noMatch: 'Δεν βρέθηκαν αποτελέσματα',\n      noData: 'Χωρίς δεδομένα',\n      placeholder: 'Επιλογή',\n    },\n    mention: {\n      loading: 'Φόρτωση',\n    },\n    cascader: {\n      noMatch: 'Δεν βρέθηκαν αποτελέσματα',\n      loading: 'Φόρτωση',\n      placeholder: 'Επιλογή',\n      noData: 'Χωρίς δεδομένα',\n    },\n    pagination: {\n      goto: 'Μετάβαση σε',\n      pagesize: '/σελίδα',\n      total: 'Σύνολο {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Μήνυμα',\n      confirm: 'Εντάξει',\n      cancel: 'Ακύρωση',\n      error: 'Άκυρη εισαγωγή',\n    },\n    upload: {\n      deleteTip: 'Πάτησε Διαγραφή για αφαίρεση',\n      delete: 'Διαγραφή',\n      preview: 'Προεπισκόπηση',\n      continue: 'Συνέχεια',\n    },\n    table: {\n      emptyText: 'Χωρίς Δεδομένα',\n      confirmFilter: 'Επιβεβαίωση',\n      resetFilter: 'Επαναφορά',\n      clearFilter: 'Όλα',\n      sumText: 'Σύνολο',\n    },\n    tree: {\n      emptyText: 'Χωρίς Δεδομένα',\n    },\n    transfer: {\n      noMatch: 'Δεν βρέθηκαν αποτελέσματα',\n      noData: 'Χωρίς δεδομένα',\n      titles: ['Λίστα 1', 'Λίστα 2'],\n      filterPlaceholder: 'Αναζήτηση',\n      noCheckedFormat: '{total} Αντικείμενα',\n      hasCheckedFormat: '{checked}/{total} επιλεγμένα',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,KAAK,EAAE,8DAA8D;AAC3E,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,0BAA0B;AACrC,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,8DAA8D;AAC3E,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,UAAU,EAAE,iFAAiF;AACnG,MAAM,UAAU,EAAE,qEAAqE;AACvF,MAAM,SAAS,EAAE,yGAAyG;AAC1H,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,OAAO,EAAE,6FAA6F;AAC5G,MAAM,OAAO,EAAE,mDAAmD;AAClE,MAAM,QAAQ,EAAE,6FAA6F;AAC7G,MAAM,QAAQ,EAAE,qEAAqE;AACrF,MAAM,SAAS,EAAE,yGAAyG;AAC1H,MAAM,SAAS,EAAE,iFAAiF;AAClG,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,MAAM,EAAE,oEAAoE;AAClF,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,wDAAwD;AACtE,MAAM,MAAM,EAAE,oEAAoE;AAClF,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,8IAA8I;AAC7J,MAAM,MAAM,EAAE,iFAAiF;AAC/F,MAAM,WAAW,EAAE,4CAA4C;AAC/D,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,4CAA4C;AAC3D,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,8IAA8I;AAC7J,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,MAAM,EAAE,iFAAiF;AAC/F,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,+DAA+D;AAC3E,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,KAAK,EAAE,8CAA8C;AAC3D,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,iFAAiF;AAC9F,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,2JAA2J;AAC5K,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,QAAQ,EAAE,kDAAkD;AAClE,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,iFAAiF;AAClG,MAAM,aAAa,EAAE,oEAAoE;AACzF,MAAM,WAAW,EAAE,wDAAwD;AAC3E,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,iFAAiF;AAClG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,8IAA8I;AAC7J,MAAM,MAAM,EAAE,iFAAiF;AAC/F,MAAM,MAAM,EAAE,CAAC,kCAAkC,EAAE,kCAAkC,CAAC;AACtF,MAAM,iBAAiB,EAAE,wDAAwD;AACjF,MAAM,eAAe,EAAE,4EAA4E;AACnG,MAAM,gBAAgB,EAAE,gFAAgF;AACxG,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}