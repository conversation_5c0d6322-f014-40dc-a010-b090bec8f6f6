# iFlytek 多模态面试系统 - 前端 Dockerfile (免费版优化)
# 多阶段构建：构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖 - 只安装生产依赖
RUN npm ci --only=production --no-audit

# 复制源代码
COPY . .

# 构建生产版本 - 优化构建
RUN npm run build

# 生产阶段：使用nginx服务静态文件
FROM nginx:alpine

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY deployment/free-deployment/nginx-free.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 健康检查 - 减少频率
HEALTHCHECK --interval=60s --timeout=30s --start-period=10s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
