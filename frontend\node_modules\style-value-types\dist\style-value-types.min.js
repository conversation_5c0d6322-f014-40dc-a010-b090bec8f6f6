!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).valueTypes={})}(this,(function(t){"use strict";const e=(t,e)=>s=>Math.max(Math.min(s,e),t),s=t=>t%1?Number(t.toFixed(5)):t,r=/(-)?([\d]*\.?[\d])+/g,n=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,a=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function o(t){return"string"==typeof t}const l={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},u=Object.assign(Object.assign({},l),{transform:e(0,1)}),i=Object.assign(Object.assign({},l),{default:1}),p=t=>({test:e=>o(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),c=p("deg"),f=p("%"),h=p("px"),m=p("vh"),d=p("vw"),b=Object.assign(Object.assign({},f),{parse:t=>f.parse(t)/100,transform:t=>f.transform(100*t)}),g=(t,e)=>s=>Boolean(o(s)&&a.test(s)&&s.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(s,e)),v=(t,e,s)=>n=>{if(!o(n))return n;const[a,l,u,i]=n.match(r);return{[t]:parseFloat(a),[e]:parseFloat(l),[s]:parseFloat(u),alpha:void 0!==i?parseFloat(i):1}},j={test:g("hsl","hue"),parse:v("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:n=1})=>"hsla("+Math.round(t)+", "+f.transform(s(e))+", "+f.transform(s(r))+", "+s(u.transform(n))+")"},y=e(0,255),O=Object.assign(Object.assign({},l),{transform:t=>Math.round(y(t))}),x={test:g("rgb","red"),parse:v("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:n=1})=>"rgba("+O.transform(t)+", "+O.transform(e)+", "+O.transform(r)+", "+s(u.transform(n))+")"};const F={test:g("#"),parse:function(t){let e="",s="",r="",n="";return t.length>5?(e=t.substr(1,2),s=t.substr(3,2),r=t.substr(5,2),n=t.substr(7,2)):(e=t.substr(1,1),s=t.substr(2,1),r=t.substr(3,1),n=t.substr(4,1),e+=e,s+=s,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(s,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:x.transform},w={test:t=>x.test(t)||F.test(t)||j.test(t),parse:t=>x.test(t)?x.parse(t):j.test(t)?j.parse(t):F.parse(t),transform:t=>o(t)?t:t.hasOwnProperty("red")?x.transform(t):j.transform(t)},$="${c}",M="${n}";function N(t){"number"==typeof t&&(t=`${t}`);const e=[];let s=0;const a=t.match(n);a&&(s=a.length,t=t.replace(n,$),e.push(...a.map(w.parse)));const o=t.match(r);return o&&(t=t.replace(r,M),e.push(...o.map(l.parse))),{values:e,numColors:s,tokenised:t}}function I(t){return N(t).values}function P(t){const{values:e,numColors:r,tokenised:n}=N(t),a=e.length;return t=>{let e=n;for(let n=0;n<a;n++)e=e.replace(n<r?$:M,n<r?w.transform(t[n]):s(t[n]));return e}}const T=t=>"number"==typeof t?0:t;const k={test:function(t){var e,s,a,l;return isNaN(t)&&o(t)&&(null!==(s=null===(e=t.match(r))||void 0===e?void 0:e.length)&&void 0!==s?s:0)+(null!==(l=null===(a=t.match(n))||void 0===a?void 0:a.length)&&void 0!==l?l:0)>0},parse:I,createTransformer:P,getAnimatableNone:function(t){const e=I(t);return P(t)(e.map(T))}},A=new Set(["brightness","contrast","saturate","opacity"]);function C(t){let[e,s]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[n]=s.match(r)||[];if(!n)return t;const a=s.replace(n,"");let o=A.has(e)?1:0;return n!==s&&(o*=100),e+"("+o+a+")"}const W=/([a-z-]*)\(.*?\)/g,_=Object.assign(Object.assign({},k),{getAnimatableNone:t=>{const e=t.match(W);return e?e.map(C).join(" "):t}});t.alpha=u,t.color=w,t.complex=k,t.degrees=c,t.filter=_,t.hex=F,t.hsla=j,t.number=l,t.percent=f,t.progressPercentage=b,t.px=h,t.rgbUnit=O,t.rgba=x,t.scale=i,t.vh=m,t.vw=d,Object.defineProperty(t,"__esModule",{value:!0})}));
