/******************************************************************************
 * Copyright 2021 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/
import type { LangiumCoreServices } from '../services.js';
export interface GrammarConfig {
    /**
     * Lists all rule names which are classified as multiline comment rules
     */
    multilineCommentRules: string[];
    /**
     * A regular expression which matches characters of names
     */
    nameRegexp: RegExp;
}
/**
 * Create the default grammar configuration (used by `createDefaultModule`). This can be overridden in a
 * language-specific module.
 */
export declare function createGrammarConfig(services: LangiumCoreServices): GrammarConfig;
//# sourceMappingURL=grammar-config.d.ts.map