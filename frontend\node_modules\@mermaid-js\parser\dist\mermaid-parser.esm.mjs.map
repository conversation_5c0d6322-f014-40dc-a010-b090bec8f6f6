{"version": 3, "sources": ["../src/parse.ts"], "sourcesContent": ["import type { LangiumParser, ParseResult } from 'langium';\n\nimport type { Info, Packet, Pie, Architecture, GitGraph, Radar, Treemap } from './index.js';\n\nexport type DiagramAST = Info | Packet | Pie | Architecture | GitGraph | Radar;\n\nconst parsers: Record<string, LangiumParser> = {};\nconst initializers = {\n  info: async () => {\n    const { createInfoServices } = await import('./language/info/index.js');\n    const parser = createInfoServices().Info.parser.LangiumParser;\n    parsers.info = parser;\n  },\n  packet: async () => {\n    const { createPacketServices } = await import('./language/packet/index.js');\n    const parser = createPacketServices().Packet.parser.LangiumParser;\n    parsers.packet = parser;\n  },\n  pie: async () => {\n    const { createPieServices } = await import('./language/pie/index.js');\n    const parser = createPieServices().Pie.parser.LangiumParser;\n    parsers.pie = parser;\n  },\n  architecture: async () => {\n    const { createArchitectureServices } = await import('./language/architecture/index.js');\n    const parser = createArchitectureServices().Architecture.parser.LangiumParser;\n    parsers.architecture = parser;\n  },\n  gitGraph: async () => {\n    const { createGitGraphServices } = await import('./language/gitGraph/index.js');\n    const parser = createGitGraphServices().GitGraph.parser.LangiumParser;\n    parsers.gitGraph = parser;\n  },\n  radar: async () => {\n    const { createRadarServices } = await import('./language/radar/index.js');\n    const parser = createRadarServices().Radar.parser.LangiumParser;\n    parsers.radar = parser;\n  },\n  treemap: async () => {\n    const { createTreemapServices } = await import('./language/treemap/index.js');\n    const parser = createTreemapServices().Treemap.parser.LangiumParser;\n    parsers.treemap = parser;\n  },\n} as const;\n\nexport async function parse(diagramType: 'info', text: string): Promise<Info>;\nexport async function parse(diagramType: 'packet', text: string): Promise<Packet>;\nexport async function parse(diagramType: 'pie', text: string): Promise<Pie>;\nexport async function parse(diagramType: 'architecture', text: string): Promise<Architecture>;\nexport async function parse(diagramType: 'gitGraph', text: string): Promise<GitGraph>;\nexport async function parse(diagramType: 'radar', text: string): Promise<Radar>;\nexport async function parse(diagramType: 'treemap', text: string): Promise<Treemap>;\n\nexport async function parse<T extends DiagramAST>(\n  diagramType: keyof typeof initializers,\n  text: string\n): Promise<T> {\n  const initializer = initializers[diagramType];\n  if (!initializer) {\n    throw new Error(`Unknown diagram type: ${diagramType}`);\n  }\n  if (!parsers[diagramType]) {\n    await initializer();\n  }\n  const parser: LangiumParser = parsers[diagramType];\n  const result: ParseResult<T> = parser.parse<T>(text);\n  if (result.lexerErrors.length > 0 || result.parserErrors.length > 0) {\n    throw new MermaidParseError(result);\n  }\n  return result.value;\n}\n\nexport class MermaidParseError extends Error {\n  constructor(public result: ParseResult<DiagramAST>) {\n    const lexerErrors: string = result.lexerErrors.map((err) => err.message).join('\\n');\n    const parserErrors: string = result.parserErrors.map((err) => err.message).join('\\n');\n    super(`Parsing failed: ${lexerErrors} ${parserErrors}`);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,UAAyC,CAAC;AAChD,IAAM,eAAe;AAAA,EACnB,MAAM,mCAAY;AAChB,UAAM,EAAE,oBAAAA,oBAAmB,IAAI,MAAM,OAAO,+CAA0B;AACtE,UAAM,SAASA,oBAAmB,EAAE,KAAK,OAAO;AAChD,YAAQ,OAAO;AAAA,EACjB,GAJM;AAAA,EAKN,QAAQ,mCAAY;AAClB,UAAM,EAAE,sBAAAC,sBAAqB,IAAI,MAAM,OAAO,iDAA4B;AAC1E,UAAM,SAASA,sBAAqB,EAAE,OAAO,OAAO;AACpD,YAAQ,SAAS;AAAA,EACnB,GAJQ;AAAA,EAKR,KAAK,mCAAY;AACf,UAAM,EAAE,mBAAAC,mBAAkB,IAAI,MAAM,OAAO,8CAAyB;AACpE,UAAM,SAASA,mBAAkB,EAAE,IAAI,OAAO;AAC9C,YAAQ,MAAM;AAAA,EAChB,GAJK;AAAA,EAKL,cAAc,mCAAY;AACxB,UAAM,EAAE,4BAAAC,4BAA2B,IAAI,MAAM,OAAO,uDAAkC;AACtF,UAAM,SAASA,4BAA2B,EAAE,aAAa,OAAO;AAChE,YAAQ,eAAe;AAAA,EACzB,GAJc;AAAA,EAKd,UAAU,mCAAY;AACpB,UAAM,EAAE,wBAAAC,wBAAuB,IAAI,MAAM,OAAO,mDAA8B;AAC9E,UAAM,SAASA,wBAAuB,EAAE,SAAS,OAAO;AACxD,YAAQ,WAAW;AAAA,EACrB,GAJU;AAAA,EAKV,OAAO,mCAAY;AACjB,UAAM,EAAE,qBAAAC,qBAAoB,IAAI,MAAM,OAAO,gDAA2B;AACxE,UAAM,SAASA,qBAAoB,EAAE,MAAM,OAAO;AAClD,YAAQ,QAAQ;AAAA,EAClB,GAJO;AAAA,EAKP,SAAS,mCAAY;AACnB,UAAM,EAAE,uBAAAC,uBAAsB,IAAI,MAAM,OAAO,kDAA6B;AAC5E,UAAM,SAASA,uBAAsB,EAAE,QAAQ,OAAO;AACtD,YAAQ,UAAU;AAAA,EACpB,GAJS;AAKX;AAUA,eAAsB,MACpB,aACA,MACY;AACZ,QAAM,cAAc,aAAa,WAAW;AAC5C,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,yBAAyB,WAAW,EAAE;AAAA,EACxD;AACA,MAAI,CAAC,QAAQ,WAAW,GAAG;AACzB,UAAM,YAAY;AAAA,EACpB;AACA,QAAM,SAAwB,QAAQ,WAAW;AACjD,QAAM,SAAyB,OAAO,MAAS,IAAI;AACnD,MAAI,OAAO,YAAY,SAAS,KAAK,OAAO,aAAa,SAAS,GAAG;AACnE,UAAM,IAAI,kBAAkB,MAAM;AAAA,EACpC;AACA,SAAO,OAAO;AAChB;AAjBsB;AAmBf,IAAM,oBAAN,cAAgC,MAAM;AAAA,EAC3C,YAAmB,QAAiC;AAClD,UAAM,cAAsB,OAAO,YAAY,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,KAAK,IAAI;AAClF,UAAM,eAAuB,OAAO,aAAa,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,KAAK,IAAI;AACpF,UAAM,mBAAmB,WAAW,IAAI,YAAY,EAAE;AAHrC;AAAA,EAInB;AAAA,EA7EF,OAwE6C;AAAA;AAAA;AAM7C;", "names": ["createInfoServices", "createPacketServices", "createPieServices", "createArchitectureServices", "createGitGraphServices", "createRadarServices", "createTreemapServices"]}