{"version": 3, "file": "model.js", "sourceRoot": "", "sources": ["../../src/model.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAS7E,iFAAiF;AACjF,SAAS,UAAU,CAAC,OAAkB;IACpC,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,OAAO,CAAC,KAAK,CAAC;KACtB;SAAM;QACL,OAAO,OAAO,CAAC,IAAI,CAAC;KACrB;AACH,CAAC;AAED,iFAAiF;AACjF,SAAS,aAAa,CACpB,GAAc;IAEd,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,KAAK,KAAK,EAAE,CAAC;AACjD,CAAC;AAED,MAAM,OAAgB,kBAAkB;IAGtC,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACD,IAAW,UAAU,CAAC,KAAU;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,YAAsB,WAAgB;QAAhB,gBAAW,GAAX,WAAW,CAAK;IAAG,CAAC;IAE1C,MAAM,CAAC,OAAqB;QAC1B,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpB,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,OAAO,WACX,SAAQ,kBAAkB;IAQ1B,YAAY,OAKX;QACC,KAAK,CAAC,EAAE,CAAC,CAAC;QARL,QAAG,GAAW,CAAC,CAAC;QASrB,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;IAED,IAAI,UAAU,CAAC,UAAyB;QACtC,YAAY;IACd,CAAC;IAED,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;SACvC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,CAAC,OAAqB;QAC1B,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpB,qFAAqF;IACvF,CAAC;CACF;AAED,MAAM,OAAO,IAAK,SAAQ,kBAAkB;IAI1C,YAAY,OAIX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAPrB,YAAO,GAAW,EAAE,CAAC;QAQ1B,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,WAAY,SAAQ,kBAAkB;IAGjD,YAAY,OAGX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QANrB,sBAAiB,GAAY,KAAK,CAAC;QAOxC,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,MACX,SAAQ,kBAAkB;IAM1B,YAAY,OAIX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QARrB,QAAG,GAAW,CAAC,CAAC;QASrB,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,mBACX,SAAQ,kBAAkB;IAM1B,YAAY,OAIX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QARrB,QAAG,GAAW,CAAC,CAAC;QASrB,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,gCACX,SAAQ,kBAAkB;IAO1B,YAAY,OAIX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QARrB,QAAG,GAAW,CAAC,CAAC;QASrB,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,UACX,SAAQ,kBAAkB;IAO1B,YAAY,OAIX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QARrB,QAAG,GAAW,CAAC,CAAC;QASrB,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,uBACX,SAAQ,kBAAkB;IAO1B,YAAY,OAIX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QARrB,QAAG,GAAW,CAAC,CAAC;QASrB,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,WACX,SAAQ,kBAA+B;IAQvC,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACD,IAAW,UAAU,CAAC,KAAoB;QACxC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,YAAY,OAMX;QACC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAnBrB,QAAG,GAAW,CAAC,CAAC;QAChB,sBAAiB,GAAY,KAAK,CAAC;QACnC,kBAAa,GAAY,KAAK,CAAC;QAkBpC,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,QAAQ;IAKnB,YAAY,OAIX;QANM,QAAG,GAAW,CAAC,CAAC;QAOrB,MAAM,CACJ,IAAI,EACJ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAqB;QAC1B,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;CACF;AA+CD,MAAM,UAAU,gBAAgB,CAAC,QAAgB;IAC/C,OAAO,GAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,IAAiB;IACnD,SAAS,iBAAiB,CAAC,UAAyB;QAClD,OAAO,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;IAC9C,CAAC;IACD,0BAA0B;IAC1B,IAAI,IAAI,YAAY,WAAW,EAAE;QAC/B,MAAM,qBAAqB,GAA2B;YACpD,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,IAAI,CAAC,eAAe;YAC1B,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC;QAEF,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxB,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAC1C;QAED,OAAO,qBAAqB,CAAC;KAC9B;SAAM,IAAI,IAAI,YAAY,WAAW,EAAE;QACtC,OAAyB;YACvB,IAAI,EAAE,aAAa;YACnB,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;SAAM,IAAI,IAAI,YAAY,MAAM,EAAE;QACjC,OAAyB;YACvB,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;SAAM,IAAI,IAAI,YAAY,mBAAmB,EAAE;QAC9C,OAAyB;YACvB,IAAI,EAAE,qBAAqB;YAC3B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;SAAM,IAAI,IAAI,YAAY,gCAAgC,EAAE;QAC3D,OAAyC;YACvC,IAAI,EAAE,kCAAkC;YACxC,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,SAAS,EAAuB,CAC9B,mBAAmB,CAAC,IAAI,QAAQ,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CACpE;YACD,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;SAAM,IAAI,IAAI,YAAY,uBAAuB,EAAE;QAClD,OAAyC;YACvC,IAAI,EAAE,yBAAyB;YAC/B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,SAAS,EAAuB,CAC9B,mBAAmB,CAAC,IAAI,QAAQ,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CACpE;YACD,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;SAAM,IAAI,IAAI,YAAY,UAAU,EAAE;QACrC,OAAyB;YACvB,IAAI,EAAE,YAAY;YAClB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;SAAM,IAAI,IAAI,YAAY,WAAW,EAAE;QACtC,OAAyB;YACvB,IAAI,EAAE,aAAa;YACnB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;SAAM,IAAI,IAAI,YAAY,QAAQ,EAAE;QACnC,MAAM,kBAAkB,GAAwB;YAC9C,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YAC5B,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC;YACpC,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC;QAEF,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxB,kBAAkB,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;SAC/C;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;QAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YAC7B,kBAAkB,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBAC5C,CAAC,CAAO,OAAQ,CAAC,MAAM;gBACvB,CAAC,CAAC,OAAO,CAAC;SACb;QAED,OAAO,kBAAkB,CAAC;KAC3B;SAAM,IAAI,IAAI,YAAY,IAAI,EAAE;QAC/B,OAA4B;YAC1B,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C,CAAC;QACF,sBAAsB;KACvB;SAAM;QACL,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACrC;AACH,CAAC"}