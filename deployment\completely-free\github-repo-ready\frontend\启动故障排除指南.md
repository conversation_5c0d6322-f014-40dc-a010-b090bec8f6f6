# iFlytek面试系统启动故障排除指南

## 🚀 快速启动方法

### 方法1：使用启动脚本（推荐）
```bash
# Windows用户
双击运行：启动iFlytek面试系统.bat

# 或者在命令行中运行
node start-iflytek-system.js
```

### 方法2：使用npm命令
```bash
# 进入frontend目录
cd frontend

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm run dev
```

## ❌ 常见问题及解决方案

### 1. ERR_CONNECTION_REFUSED（连接被拒绝）

**原因**：开发服务器未启动或端口被占用

**解决方案**：
```bash
# 1. 确保在frontend目录中
cd frontend

# 2. 检查Node.js是否安装
node --version

# 3. 安装依赖
npm install

# 4. 启动开发服务器
npm run dev
```

### 2. 端口8080被占用

**现象**：显示端口已被占用的警告

**解决方案**：
- Vite会自动选择其他可用端口（如8081、8082等）
- 查看控制台输出的实际访问地址
- 或手动指定端口：`npm run dev -- --port 3000`

### 3. 依赖安装失败

**现象**：npm install报错

**解决方案**：
```bash
# 1. 清除npm缓存
npm cache clean --force

# 2. 删除node_modules和package-lock.json
rm -rf node_modules package-lock.json

# 3. 重新安装
npm install

# 4. 如果仍然失败，尝试使用yarn
npm install -g yarn
yarn install
```

### 4. Node.js版本过低

**现象**：提示Node.js版本不兼容

**解决方案**：
- 下载并安装Node.js 16+：https://nodejs.org/
- 或使用nvm管理Node.js版本：
```bash
# 安装nvm后
nvm install 18
nvm use 18
```

### 5. 模块找不到错误

**现象**：Cannot find module 'xxx'

**解决方案**：
```bash
# 1. 重新安装依赖
npm install

# 2. 检查package.json中的依赖
npm list

# 3. 手动安装缺失的模块
npm install [模块名]
```

### 6. Vite启动失败

**现象**：Vite相关错误

**解决方案**：
```bash
# 1. 清除Vite缓存
rm -rf node_modules/.vite

# 2. 重新启动
npm run dev

# 3. 如果仍然失败，检查vite.config.js配置
```

### 7. 浏览器白屏或404

**现象**：页面无法正常显示

**解决方案**：
1. 检查控制台是否有JavaScript错误
2. 确认访问的URL是否正确
3. 清除浏览器缓存：Ctrl+Shift+R（强制刷新）
4. 检查路由配置是否正确

## 🔧 环境检查清单

### 必需环境
- ✅ Node.js 16.0.0+
- ✅ npm 7.0.0+（通常随Node.js安装）
- ✅ 现代浏览器（Chrome 90+、Firefox 88+、Safari 14+）

### 检查命令
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查项目依赖
npm list --depth=0

# 检查端口占用（Windows）
netstat -an | findstr :8080

# 检查端口占用（macOS/Linux）
lsof -i :8080
```

## 📍 访问地址

启动成功后，可以访问以下页面：

### 主要页面
- **主页**：http://localhost:8080
- **面试页面**：http://localhost:8080/text-interview
- **报告中心**：http://localhost:8080/report-center
- **报告详情**：http://localhost:8080/report/1

### 测试页面
- **系统测试**：http://localhost:8080/system-test
- **功能演示**：http://localhost:8080/demo

### 新功能测试
- **AI智能提示**：在面试页面中测试
- **报告导出**：在报告中心测试Excel/CSV导出
- **企业分享**：在报告详情页测试分享功能

## 🧪 功能验证步骤

### 1. AI智能提示系统测试
1. 访问面试页面：http://localhost:8080/text-interview
2. 输入问题和回答
3. 点击"AI提示"按钮
4. 验证是否生成个性化提示

### 2. 报告导出功能测试
1. 访问报告中心：http://localhost:8080/report-center
2. 点击"下载"或"批量导出"按钮
3. 选择Excel或CSV格式
4. 验证文件是否正确下载

### 3. 企业分享功能测试
1. 访问报告详情：http://localhost:8080/report/1
2. 点击"分享报告"按钮
3. 配置分享设置
4. 验证分享链接是否生成

### 4. 系统集成测试
1. 访问测试页面：http://localhost:8080/system-test
2. 点击"开始完整测试"
3. 查看测试结果和通过率
4. 验证各功能模块是否正常

## 📞 获取帮助

如果以上方法都无法解决问题，请：

1. **查看详细错误信息**：复制完整的错误消息
2. **检查系统环境**：提供Node.js版本、操作系统信息
3. **查看功能文档**：阅读FEATURE_VALIDATION.md了解功能详情
4. **运行系统测试**：访问/system-test页面获取详细诊断信息

## 🎯 成功启动标志

当看到以下信息时，说明系统启动成功：

```
✅ Node.js环境检查通过
✅ 项目依赖已存在
✅ 端口8080可用
🚀 正在启动iFlytek面试系统...

  VITE v4.4.5  ready in 1234 ms

  ➜  Local:   http://localhost:8080/
  ➜  Network: use --host to expose
```

此时可以在浏览器中访问 http://localhost:8080 开始使用系统。
