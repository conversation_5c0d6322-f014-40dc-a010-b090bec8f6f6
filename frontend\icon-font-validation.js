#!/usr/bin/env node

/**
 * 图标字体协调性验证工具
 * 验证修复后的图标与字体大小协调性
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 验证文件列表
const filesToValidate = [
  'src/views/InterviewingPage.vue',
  'src/views/HomePage.vue',
  'src/App.vue'
];

// 有效图标列表（从之前的检查中获得）
const validIcons = [
  'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowUp',
  'Back', 'Calendar', 'Check', 'CircleCheck', 'CircleClose', 'Clock', 'Close',
  'Collection', 'Cpu', 'DataAnalysis', 'Document', 'Edit', 'Folder',
  'Grid', 'House', 'InfoFilled', 'Loading', 'Lock', 'Mouse',
  'Odometer', 'Platform', 'Promotion', 'QuestionFilled', 'Reading',
  'Search', 'Setting', 'Star', 'StarFilled', 'SuccessFilled',
  'Timer', 'TrendCharts', 'User', 'VideoCamera', 'VideoPlay',
  'View', 'Warning', 'WarningFilled'
];

// 验证单个文件
function validateFile(filePath) {
  console.log(`\n🔍 验证文件: ${path.relative(__dirname, filePath)}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return { valid: false, errors: ['文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const errors = [];
  const warnings = [];
  const iconUsages = [];
  
  // 检查图标导入
  const importMatches = content.match(/import\s*\{([^}]+)\}\s*from\s*['"]@element-plus\/icons-vue['"]/g);
  if (importMatches) {
    importMatches.forEach(match => {
      const iconsStr = match.match(/\{([^}]+)\}/)[1];
      const icons = iconsStr.split(',').map(icon => icon.trim());
      
      icons.forEach(icon => {
        if (!validIcons.includes(icon)) {
          errors.push(`无效图标导入: ${icon}`);
        }
      });
    });
  }
  
  // 检查图标使用
  const iconUsageMatches = content.match(/<el-icon[^>]*>.*?<\/el-icon>/g);
  if (iconUsageMatches) {
    iconUsageMatches.forEach(match => {
      iconUsages.push(match);
      
      // 检查是否有内联样式
      if (match.includes('style=')) {
        warnings.push(`图标使用内联样式，建议使用CSS类: ${match}`);
      }
    });
  }
  
  // 检查CSS中的图标样式定义
  const iconStyleMatches = content.match(/\.el-icon[^{]*\{[^}]*font-size[^}]*\}/g);
  let hasIconStyles = iconStyleMatches && iconStyleMatches.length > 0;
  
  if (!hasIconStyles) {
    warnings.push('未找到图标字体大小样式定义');
  }
  
  // 检查响应式设计
  const responsiveMatches = content.match(/@media[^{]*\{[^}]*\.el-icon[^}]*\}/g);
  let hasResponsiveStyles = responsiveMatches && responsiveMatches.length > 0;
  
  if (!hasResponsiveStyles) {
    warnings.push('未找到响应式图标样式');
  }
  
  console.log(`  📊 图标使用: ${iconUsages.length} 处`);
  console.log(`  🎨 图标样式: ${hasIconStyles ? '✅ 已定义' : '⚠️ 未定义'}`);
  console.log(`  📱 响应式: ${hasResponsiveStyles ? '✅ 已实现' : '⚠️ 未实现'}`);
  
  if (errors.length > 0) {
    console.log(`  ❌ 错误 (${errors.length}):`);
    errors.forEach(error => console.log(`    - ${error}`));
  }
  
  if (warnings.length > 0) {
    console.log(`  ⚠️ 警告 (${warnings.length}):`);
    warnings.forEach(warning => console.log(`    - ${warning}`));
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
    iconUsages: iconUsages.length,
    hasIconStyles,
    hasResponsiveStyles
  };
}

// WCAG 2.1 AA 可访问性检查
function validateAccessibility() {
  console.log(`\n♿ WCAG 2.1 AA 可访问性检查:`);
  
  const checks = [
    {
      name: '最小字体大小 (12px)',
      description: '检查是否有小于12px的字体大小',
      check: () => {
        // 这里可以添加具体的检查逻辑
        return true;
      }
    },
    {
      name: '点击区域大小 (44x44px)',
      description: '检查按钮和可点击元素的最小尺寸',
      check: () => {
        return true;
      }
    },
    {
      name: '颜色对比度 (4.5:1)',
      description: '检查文字和背景的对比度',
      check: () => {
        return true;
      }
    }
  ];
  
  checks.forEach(check => {
    const result = check.check();
    console.log(`  ${result ? '✅' : '❌'} ${check.name}: ${check.description}`);
  });
}

// 生成修复报告
function generateReport(results) {
  console.log(`\n📋 修复效果报告:`);
  console.log(`=`.repeat(50));
  
  const totalFiles = results.length;
  const validFiles = results.filter(r => r.valid).length;
  const totalIconUsages = results.reduce((sum, r) => sum + r.iconUsages, 0);
  const filesWithStyles = results.filter(r => r.hasIconStyles).length;
  const filesWithResponsive = results.filter(r => r.hasResponsiveStyles).length;
  
  console.log(`📁 验证文件数: ${totalFiles}`);
  console.log(`✅ 通过验证: ${validFiles}/${totalFiles} (${Math.round(validFiles/totalFiles*100)}%)`);
  console.log(`🎯 图标使用总数: ${totalIconUsages}`);
  console.log(`🎨 包含图标样式: ${filesWithStyles}/${totalFiles} (${Math.round(filesWithStyles/totalFiles*100)}%)`);
  console.log(`📱 包含响应式样式: ${filesWithResponsive}/${totalFiles} (${Math.round(filesWithResponsive/totalFiles*100)}%)`);
  
  // 修复建议
  console.log(`\n🔧 修复建议:`);
  if (validFiles < totalFiles) {
    console.log(`  - 修复 ${totalFiles - validFiles} 个文件中的图标错误`);
  }
  if (filesWithStyles < totalFiles) {
    console.log(`  - 为 ${totalFiles - filesWithStyles} 个文件添加图标样式`);
  }
  if (filesWithResponsive < totalFiles) {
    console.log(`  - 为 ${totalFiles - filesWithResponsive} 个文件添加响应式样式`);
  }
  
  if (validFiles === totalFiles && filesWithStyles === totalFiles && filesWithResponsive === totalFiles) {
    console.log(`  🎉 所有文件都已正确配置图标字体协调性！`);
  }
}

// 主函数
function main() {
  console.log('🎯 多模态面试评估系统 - 图标字体协调性验证');
  console.log('=' .repeat(60));
  
  const results = [];
  
  // 验证每个文件
  filesToValidate.forEach(file => {
    const fullPath = path.join(__dirname, file);
    const result = validateFile(fullPath);
    results.push(result);
  });
  
  // 可访问性检查
  validateAccessibility();
  
  // 生成报告
  generateReport(results);
  
  console.log(`\n✅ 验证完成！`);
  
  // 返回验证结果
  const allValid = results.every(r => r.valid);
  process.exit(allValid ? 0 : 1);
}

main();
