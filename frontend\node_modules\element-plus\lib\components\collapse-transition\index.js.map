{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/collapse-transition/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport CollapseTransition from './src/collapse-transition.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCollapseTransition: SFCWithInstall<typeof CollapseTransition> =\n  withInstall(CollapseTransition)\n\nexport default ElCollapseTransition\n"], "names": ["withInstall", "CollapseTransition"], "mappings": ";;;;;;;AAEY,MAAC,oBAAoB,GAAGA,mBAAW,CAACC,6BAAkB;;;;;"}