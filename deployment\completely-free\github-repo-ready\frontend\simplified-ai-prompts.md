# 🎬 AI视频生成简化提示词 - 多模态面试评估系统

## 📋 使用说明
- 直接复制英文提示词到AI视频生成平台
- 建议平台：Runway ML, Pika Labs, Stable Video Diffusion
- 每个提示词已优化为简洁格式，便于AI理解

---

## 🎯 视频1: demo-complete.mp4 (8分钟)

### 📝 英文提示词 (复制使用)
```
Professional AI interview system demo, iFlytek Spark LLM interface, blue-purple gradient UI, multimodal input (voice/video/text), six capability assessment dashboard, Chinese interface, modern corporate design, smooth transitions, 1920x1080, 8 minutes
```

### 🇨🇳 中文描述
专业AI面试系统完整演示，展示iFlytek Spark大模型界面，蓝紫渐变UI设计，多模态输入功能，六维能力评估仪表板，中文界面，现代企业风格

### ⚙️ 参数设置建议
- **时长**: 8分钟
- **分辨率**: 1920x1080
- **风格**: Corporate, Professional
- **运动**: Smooth transitions
- **色调**: Blue-purple gradient

---

## 🤖 视频2: demo-ai-tech.mp4 (6分钟)

### 📝 英文提示词 (复制使用)
```
iFlytek Spark LLM technical architecture, neural network diagrams, AI algorithm visualization, multimodal fusion technology, blue gradient background, Chinese tech presentation, professional diagrams, 6 minutes
```

### 🇨🇳 中文描述
iFlytek Spark大模型技术架构展示，神经网络图表，AI算法可视化，多模态融合技术原理，蓝色渐变背景，中文技术演示

### ⚙️ 参数设置建议
- **时长**: 6分钟
- **分辨率**: 1920x1080
- **风格**: Technical, Diagrams
- **运动**: Data flow animations
- **色调**: Blue gradient, Tech aesthetic

---

## 📊 视频3: demo-cases.mp4 (5分钟)

### 📝 英文提示词 (复制使用)
```
Interview case studies, AI engineer assessment, big data analyst evaluation, IoT developer interview, split-screen candidate analysis, professional interview room, Chinese subtitles, evaluation metrics, 5 minutes
```

### 🇨🇳 中文描述
面试案例研究，AI工程师评估，大数据分析师评价，IoT开发者面试，分屏候选人分析，专业面试室环境，中文字幕，评估指标展示

### ⚙️ 参数设置建议
- **时长**: 5分钟
- **分辨率**: 1920x1080
- **风格**: Professional interview
- **运动**: Split-screen transitions
- **色调**: Corporate blue-purple

---

## 📈 视频4: demo-bigdata.mp4 (7分钟)

### 📝 英文提示词 (复制使用)
```
Big data assessment interface, data processing visualization, machine learning algorithms, technical skill evaluation, data science dashboard, Chinese interface, professional analytics, chart animations, 7 minutes
```

### 🇨🇳 中文描述
大数据评估界面，数据处理可视化，机器学习算法展示，技术技能评估，数据科学仪表板，中文界面，专业分析图表动画

### ⚙️ 参数设置建议
- **时长**: 7分钟
- **分辨率**: 1920x1080
- **风格**: Data visualization
- **运动**: Chart animations
- **色调**: Blue data theme

---

## 🔌 视频5: demo-iot.mp4 (6分钟)

### 📝 英文提示词 (复制使用)
```
IoT technology assessment, embedded systems interface, sensor network visualization, communication protocols, hardware-software integration, Chinese tech interface, professional IoT dashboard, 6 minutes
```

### 🇨🇳 中文描述
物联网技术评估，嵌入式系统界面，传感器网络可视化，通信协议展示，硬件软件集成，中文技术界面，专业IoT仪表板

### ⚙️ 参数设置建议
- **时长**: 6分钟
- **分辨率**: 1920x1080
- **风格**: IoT technical
- **运动**: Network animations
- **色调**: Tech blue-purple

---

## 🎨 通用风格指导

### 核心视觉元素
- **主色调**: Blue-purple gradient (#667eea → #764ba2)
- **品牌**: iFlytek Spark LLM prominently featured
- **界面**: Chinese interface with modern design
- **风格**: Professional, corporate, clean

### 技术规格
- **格式**: MP4, H.264 encoding
- **分辨率**: 1920x1080 (推荐) 或 1280x720
- **帧率**: 30fps
- **宽高比**: 16:9

---

## 🚀 平台使用指南

### Runway ML 使用步骤
1. 访问 https://runwayml.com
2. 选择 "Gen-2" 或最新视频生成工具
3. 复制对应的英文提示词
4. 设置时长和分辨率参数
5. 点击生成并等待处理

### Pika Labs 使用步骤
1. 访问 https://pika.art
2. 加入Discord服务器
3. 使用命令: `/create [提示词] --duration [时长] --aspect 16:9`
4. 等待生成完成

### 提示词优化技巧
- **关键词前置**: 将最重要的元素放在提示词开头
- **具体描述**: 使用具体的技术术语而非抽象概念
- **视觉细节**: 包含颜色、布局、动画等视觉元素
- **时长控制**: 明确指定视频时长要求

---

## 💡 生成后处理建议

### 质量检查
- [ ] 视频清晰度是否达到1080p
- [ ] 是否包含iFlytek Spark元素
- [ ] 色调是否符合蓝紫渐变主题
- [ ] 时长是否符合要求
- [ ] 内容是否与系统功能匹配

### 后期优化
1. **添加中文字幕**: 使用剪映或Premiere Pro
2. **音频处理**: 添加背景音乐或旁白
3. **格式转换**: 确保输出为H.264编码的MP4
4. **文件命名**: 按照系统要求重命名文件

### 部署步骤
1. 下载生成的视频文件
2. 重命名为对应的文件名 (demo-complete.mp4等)
3. 替换 `frontend/public/videos/` 目录中的占位符文件
4. 刷新浏览器验证效果

---

## 📞 技术支持

如果生成的视频不符合预期，可以尝试：
- 调整提示词中的关键词顺序
- 增加或减少描述细节
- 使用不同的AI生成平台
- 参考 `video-acquisition-guide.md` 获取更多方案

**祝您生成成功！** 🎉
