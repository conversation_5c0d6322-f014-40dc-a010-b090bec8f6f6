/******************************************************************************
 * This file was generated by langium-cli 3.3.0.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/
import type { AstNode, Reference, ReferenceInfo, TypeMetaData } from '../../syntax-tree.js';
import { AbstractAstReflection } from '../../syntax-tree.js';
export declare const LangiumGrammarTerminals: {
    ID: RegExp;
    STRING: RegExp;
    NUMBER: RegExp;
    RegexLiteral: RegExp;
    WS: RegExp;
    ML_COMMENT: RegExp;
    SL_COMMENT: RegExp;
};
export type LangiumGrammarTerminalNames = keyof typeof LangiumGrammarTerminals;
export type LangiumGrammarKeywordNames = "!" | "&" | "(" | ")" | "*" | "+" | "+=" | "," | "->" | "." | ".." | ":" | ";" | "<" | "=" | "=>" | ">" | "?" | "?!" | "?<!" | "?<=" | "?=" | "@" | "Date" | "EOF" | "[" | "]" | "bigint" | "boolean" | "current" | "entry" | "extends" | "false" | "fragment" | "grammar" | "hidden" | "import" | "infer" | "infers" | "interface" | "number" | "returns" | "string" | "terminal" | "true" | "type" | "with" | "{" | "|" | "}";
export type LangiumGrammarTokenNames = LangiumGrammarTerminalNames | LangiumGrammarKeywordNames;
export type AbstractRule = ParserRule | TerminalRule;
export declare const AbstractRule = "AbstractRule";
export declare function isAbstractRule(item: unknown): item is AbstractRule;
export type AbstractType = InferredType | Interface | ParserRule | Type;
export declare const AbstractType = "AbstractType";
export declare function isAbstractType(item: unknown): item is AbstractType;
export type Condition = BooleanLiteral | Conjunction | Disjunction | Negation | ParameterReference;
export declare const Condition = "Condition";
export declare function isCondition(item: unknown): item is Condition;
export type FeatureName = 'current' | 'entry' | 'extends' | 'false' | 'fragment' | 'grammar' | 'hidden' | 'import' | 'infer' | 'infers' | 'interface' | 'returns' | 'terminal' | 'true' | 'type' | 'with' | PrimitiveType | string;
export declare function isFeatureName(item: unknown): item is FeatureName;
export type PrimitiveType = 'Date' | 'bigint' | 'boolean' | 'number' | 'string';
export declare function isPrimitiveType(item: unknown): item is PrimitiveType;
export type TypeDefinition = ArrayType | ReferenceType | SimpleType | UnionType;
export declare const TypeDefinition = "TypeDefinition";
export declare function isTypeDefinition(item: unknown): item is TypeDefinition;
export type ValueLiteral = ArrayLiteral | BooleanLiteral | NumberLiteral | StringLiteral;
export declare const ValueLiteral = "ValueLiteral";
export declare function isValueLiteral(item: unknown): item is ValueLiteral;
export interface AbstractElement extends AstNode {
    readonly $type: 'AbstractElement' | 'Action' | 'Alternatives' | 'Assignment' | 'CharacterRange' | 'CrossReference' | 'EndOfFile' | 'Group' | 'Keyword' | 'NegatedToken' | 'RegexToken' | 'RuleCall' | 'TerminalAlternatives' | 'TerminalGroup' | 'TerminalRuleCall' | 'UnorderedGroup' | 'UntilToken' | 'Wildcard';
    cardinality?: '*' | '+' | '?';
    lookahead?: '?!' | '?<!' | '?<=' | '?=';
}
export declare const AbstractElement = "AbstractElement";
export declare function isAbstractElement(item: unknown): item is AbstractElement;
export interface ArrayLiteral extends AstNode {
    readonly $container: ArrayLiteral | TypeAttribute;
    readonly $type: 'ArrayLiteral';
    elements: Array<ValueLiteral>;
}
export declare const ArrayLiteral = "ArrayLiteral";
export declare function isArrayLiteral(item: unknown): item is ArrayLiteral;
export interface ArrayType extends AstNode {
    readonly $container: ArrayType | ReferenceType | Type | TypeAttribute | UnionType;
    readonly $type: 'ArrayType';
    elementType: TypeDefinition;
}
export declare const ArrayType = "ArrayType";
export declare function isArrayType(item: unknown): item is ArrayType;
export interface BooleanLiteral extends AstNode {
    readonly $container: ArrayLiteral | Conjunction | Disjunction | Group | NamedArgument | Negation | TypeAttribute;
    readonly $type: 'BooleanLiteral';
    true: boolean;
}
export declare const BooleanLiteral = "BooleanLiteral";
export declare function isBooleanLiteral(item: unknown): item is BooleanLiteral;
export interface Conjunction extends AstNode {
    readonly $container: Conjunction | Disjunction | Group | NamedArgument | Negation;
    readonly $type: 'Conjunction';
    left: Condition;
    right: Condition;
}
export declare const Conjunction = "Conjunction";
export declare function isConjunction(item: unknown): item is Conjunction;
export interface Disjunction extends AstNode {
    readonly $container: Conjunction | Disjunction | Group | NamedArgument | Negation;
    readonly $type: 'Disjunction';
    left: Condition;
    right: Condition;
}
export declare const Disjunction = "Disjunction";
export declare function isDisjunction(item: unknown): item is Disjunction;
export interface Grammar extends AstNode {
    readonly $type: 'Grammar';
    definesHiddenTokens: boolean;
    hiddenTokens: Array<Reference<AbstractRule>>;
    imports: Array<GrammarImport>;
    interfaces: Array<Interface>;
    isDeclared: boolean;
    name?: string;
    rules: Array<AbstractRule>;
    types: Array<Type>;
    usedGrammars: Array<Reference<Grammar>>;
}
export declare const Grammar = "Grammar";
export declare function isGrammar(item: unknown): item is Grammar;
export interface GrammarImport extends AstNode {
    readonly $container: Grammar;
    readonly $type: 'GrammarImport';
    path: string;
}
export declare const GrammarImport = "GrammarImport";
export declare function isGrammarImport(item: unknown): item is GrammarImport;
export interface InferredType extends AstNode {
    readonly $container: Action | ParserRule;
    readonly $type: 'InferredType';
    name: string;
}
export declare const InferredType = "InferredType";
export declare function isInferredType(item: unknown): item is InferredType;
export interface Interface extends AstNode {
    readonly $container: Grammar;
    readonly $type: 'Interface';
    attributes: Array<TypeAttribute>;
    name: string;
    superTypes: Array<Reference<AbstractType>>;
}
export declare const Interface = "Interface";
export declare function isInterface(item: unknown): item is Interface;
export interface NamedArgument extends AstNode {
    readonly $container: RuleCall;
    readonly $type: 'NamedArgument';
    calledByName: boolean;
    parameter?: Reference<Parameter>;
    value: Condition;
}
export declare const NamedArgument = "NamedArgument";
export declare function isNamedArgument(item: unknown): item is NamedArgument;
export interface Negation extends AstNode {
    readonly $container: Conjunction | Disjunction | Group | NamedArgument | Negation;
    readonly $type: 'Negation';
    value: Condition;
}
export declare const Negation = "Negation";
export declare function isNegation(item: unknown): item is Negation;
export interface NumberLiteral extends AstNode {
    readonly $container: ArrayLiteral | TypeAttribute;
    readonly $type: 'NumberLiteral';
    value: number;
}
export declare const NumberLiteral = "NumberLiteral";
export declare function isNumberLiteral(item: unknown): item is NumberLiteral;
export interface Parameter extends AstNode {
    readonly $container: ParserRule;
    readonly $type: 'Parameter';
    name: string;
}
export declare const Parameter = "Parameter";
export declare function isParameter(item: unknown): item is Parameter;
export interface ParameterReference extends AstNode {
    readonly $container: Conjunction | Disjunction | Group | NamedArgument | Negation;
    readonly $type: 'ParameterReference';
    parameter: Reference<Parameter>;
}
export declare const ParameterReference = "ParameterReference";
export declare function isParameterReference(item: unknown): item is ParameterReference;
export interface ParserRule extends AstNode {
    readonly $container: Grammar;
    readonly $type: 'ParserRule';
    dataType?: PrimitiveType;
    definesHiddenTokens: boolean;
    definition: AbstractElement;
    entry: boolean;
    fragment: boolean;
    hiddenTokens: Array<Reference<AbstractRule>>;
    inferredType?: InferredType;
    name: string;
    parameters: Array<Parameter>;
    returnType?: Reference<AbstractType>;
    wildcard: boolean;
}
export declare const ParserRule = "ParserRule";
export declare function isParserRule(item: unknown): item is ParserRule;
export interface ReferenceType extends AstNode {
    readonly $container: ArrayType | ReferenceType | Type | TypeAttribute | UnionType;
    readonly $type: 'ReferenceType';
    referenceType: TypeDefinition;
}
export declare const ReferenceType = "ReferenceType";
export declare function isReferenceType(item: unknown): item is ReferenceType;
export interface ReturnType extends AstNode {
    readonly $container: TerminalRule;
    readonly $type: 'ReturnType';
    name: PrimitiveType | string;
}
export declare const ReturnType = "ReturnType";
export declare function isReturnType(item: unknown): item is ReturnType;
export interface SimpleType extends AstNode {
    readonly $container: ArrayType | ReferenceType | Type | TypeAttribute | UnionType;
    readonly $type: 'SimpleType';
    primitiveType?: PrimitiveType;
    stringType?: string;
    typeRef?: Reference<AbstractType>;
}
export declare const SimpleType = "SimpleType";
export declare function isSimpleType(item: unknown): item is SimpleType;
export interface StringLiteral extends AstNode {
    readonly $container: ArrayLiteral | TypeAttribute;
    readonly $type: 'StringLiteral';
    value: string;
}
export declare const StringLiteral = "StringLiteral";
export declare function isStringLiteral(item: unknown): item is StringLiteral;
export interface TerminalRule extends AstNode {
    readonly $container: Grammar;
    readonly $type: 'TerminalRule';
    definition: AbstractElement;
    fragment: boolean;
    hidden: boolean;
    name: string;
    type?: ReturnType;
}
export declare const TerminalRule = "TerminalRule";
export declare function isTerminalRule(item: unknown): item is TerminalRule;
export interface Type extends AstNode {
    readonly $container: Grammar;
    readonly $type: 'Type';
    name: string;
    type: TypeDefinition;
}
export declare const Type = "Type";
export declare function isType(item: unknown): item is Type;
export interface TypeAttribute extends AstNode {
    readonly $container: Interface;
    readonly $type: 'TypeAttribute';
    defaultValue?: ValueLiteral;
    isOptional: boolean;
    name: FeatureName;
    type: TypeDefinition;
}
export declare const TypeAttribute = "TypeAttribute";
export declare function isTypeAttribute(item: unknown): item is TypeAttribute;
export interface UnionType extends AstNode {
    readonly $container: ArrayType | ReferenceType | Type | TypeAttribute | UnionType;
    readonly $type: 'UnionType';
    types: Array<TypeDefinition>;
}
export declare const UnionType = "UnionType";
export declare function isUnionType(item: unknown): item is UnionType;
export interface Action extends AbstractElement {
    readonly $type: 'Action';
    feature?: FeatureName;
    inferredType?: InferredType;
    operator?: '+=' | '=';
    type?: Reference<AbstractType>;
}
export declare const Action = "Action";
export declare function isAction(item: unknown): item is Action;
export interface Alternatives extends AbstractElement {
    readonly $type: 'Alternatives';
    elements: Array<AbstractElement>;
}
export declare const Alternatives = "Alternatives";
export declare function isAlternatives(item: unknown): item is Alternatives;
export interface Assignment extends AbstractElement {
    readonly $type: 'Assignment';
    feature: FeatureName;
    operator: '+=' | '=' | '?=';
    terminal: AbstractElement;
}
export declare const Assignment = "Assignment";
export declare function isAssignment(item: unknown): item is Assignment;
export interface CharacterRange extends AbstractElement {
    readonly $type: 'CharacterRange';
    left: Keyword;
    right?: Keyword;
}
export declare const CharacterRange = "CharacterRange";
export declare function isCharacterRange(item: unknown): item is CharacterRange;
export interface CrossReference extends AbstractElement {
    readonly $type: 'CrossReference';
    deprecatedSyntax: boolean;
    terminal?: AbstractElement;
    type: Reference<AbstractType>;
}
export declare const CrossReference = "CrossReference";
export declare function isCrossReference(item: unknown): item is CrossReference;
export interface EndOfFile extends AbstractElement {
    readonly $type: 'EndOfFile';
}
export declare const EndOfFile = "EndOfFile";
export declare function isEndOfFile(item: unknown): item is EndOfFile;
export interface Group extends AbstractElement {
    readonly $type: 'Group';
    elements: Array<AbstractElement>;
    guardCondition?: Condition;
}
export declare const Group = "Group";
export declare function isGroup(item: unknown): item is Group;
export interface Keyword extends AbstractElement {
    readonly $container: CharacterRange;
    readonly $type: 'Keyword';
    value: string;
}
export declare const Keyword = "Keyword";
export declare function isKeyword(item: unknown): item is Keyword;
export interface NegatedToken extends AbstractElement {
    readonly $type: 'NegatedToken';
    terminal: AbstractElement;
}
export declare const NegatedToken = "NegatedToken";
export declare function isNegatedToken(item: unknown): item is NegatedToken;
export interface RegexToken extends AbstractElement {
    readonly $type: 'RegexToken';
    regex: string;
}
export declare const RegexToken = "RegexToken";
export declare function isRegexToken(item: unknown): item is RegexToken;
export interface RuleCall extends AbstractElement {
    readonly $type: 'RuleCall';
    arguments: Array<NamedArgument>;
    rule: Reference<AbstractRule>;
}
export declare const RuleCall = "RuleCall";
export declare function isRuleCall(item: unknown): item is RuleCall;
export interface TerminalAlternatives extends AbstractElement {
    readonly $type: 'TerminalAlternatives';
    elements: Array<AbstractElement>;
}
export declare const TerminalAlternatives = "TerminalAlternatives";
export declare function isTerminalAlternatives(item: unknown): item is TerminalAlternatives;
export interface TerminalGroup extends AbstractElement {
    readonly $type: 'TerminalGroup';
    elements: Array<AbstractElement>;
}
export declare const TerminalGroup = "TerminalGroup";
export declare function isTerminalGroup(item: unknown): item is TerminalGroup;
export interface TerminalRuleCall extends AbstractElement {
    readonly $type: 'TerminalRuleCall';
    rule: Reference<TerminalRule>;
}
export declare const TerminalRuleCall = "TerminalRuleCall";
export declare function isTerminalRuleCall(item: unknown): item is TerminalRuleCall;
export interface UnorderedGroup extends AbstractElement {
    readonly $type: 'UnorderedGroup';
    elements: Array<AbstractElement>;
}
export declare const UnorderedGroup = "UnorderedGroup";
export declare function isUnorderedGroup(item: unknown): item is UnorderedGroup;
export interface UntilToken extends AbstractElement {
    readonly $type: 'UntilToken';
    terminal: AbstractElement;
}
export declare const UntilToken = "UntilToken";
export declare function isUntilToken(item: unknown): item is UntilToken;
export interface Wildcard extends AbstractElement {
    readonly $type: 'Wildcard';
}
export declare const Wildcard = "Wildcard";
export declare function isWildcard(item: unknown): item is Wildcard;
export type LangiumGrammarAstType = {
    AbstractElement: AbstractElement;
    AbstractRule: AbstractRule;
    AbstractType: AbstractType;
    Action: Action;
    Alternatives: Alternatives;
    ArrayLiteral: ArrayLiteral;
    ArrayType: ArrayType;
    Assignment: Assignment;
    BooleanLiteral: BooleanLiteral;
    CharacterRange: CharacterRange;
    Condition: Condition;
    Conjunction: Conjunction;
    CrossReference: CrossReference;
    Disjunction: Disjunction;
    EndOfFile: EndOfFile;
    Grammar: Grammar;
    GrammarImport: GrammarImport;
    Group: Group;
    InferredType: InferredType;
    Interface: Interface;
    Keyword: Keyword;
    NamedArgument: NamedArgument;
    NegatedToken: NegatedToken;
    Negation: Negation;
    NumberLiteral: NumberLiteral;
    Parameter: Parameter;
    ParameterReference: ParameterReference;
    ParserRule: ParserRule;
    ReferenceType: ReferenceType;
    RegexToken: RegexToken;
    ReturnType: ReturnType;
    RuleCall: RuleCall;
    SimpleType: SimpleType;
    StringLiteral: StringLiteral;
    TerminalAlternatives: TerminalAlternatives;
    TerminalGroup: TerminalGroup;
    TerminalRule: TerminalRule;
    TerminalRuleCall: TerminalRuleCall;
    Type: Type;
    TypeAttribute: TypeAttribute;
    TypeDefinition: TypeDefinition;
    UnionType: UnionType;
    UnorderedGroup: UnorderedGroup;
    UntilToken: UntilToken;
    ValueLiteral: ValueLiteral;
    Wildcard: Wildcard;
};
export declare class LangiumGrammarAstReflection extends AbstractAstReflection {
    getAllTypes(): string[];
    protected computeIsSubtype(subtype: string, supertype: string): boolean;
    getReferenceType(refInfo: ReferenceInfo): string;
    getTypeMetaData(type: string): TypeMetaData;
}
export declare const reflection: LangiumGrammarAstReflection;
//# sourceMappingURL=ast.d.ts.map