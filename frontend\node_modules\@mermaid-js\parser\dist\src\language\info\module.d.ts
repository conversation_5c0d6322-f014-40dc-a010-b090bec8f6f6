import type { DefaultSharedCoreModuleContext, LangiumCoreServices, LangiumSharedCoreServices, Module, PartialLangiumCoreServices } from 'langium';
import { CommonValueConverter } from '../common/index.js';
import { InfoTokenBuilder } from './tokenBuilder.js';
/**
 * Declaration of `Info` services.
 */
interface InfoAddedServices {
    parser: {
        TokenBuilder: InfoTokenBuilder;
        ValueConverter: CommonValueConverter;
    };
}
/**
 * Union of Langium default services and `Info` services.
 */
export type InfoServices = LangiumCoreServices & InfoAddedServices;
/**
 * Dependency injection module that overrides Langium default services and
 * contributes the declared `Info` services.
 */
export declare const InfoModule: Module<InfoServices, PartialLangiumCoreServices & InfoAddedServices>;
/**
 * Create the full set of services required by Langium.
 *
 * First inject the shared services by merging two modules:
 *  - Langium default shared services
 *  - Services generated by langium-cli
 *
 * Then inject the language-specific services by merging three modules:
 *  - Langium default language-specific services
 *  - Services generated by langium-cli
 *  - Services specified in this file
 * @param context - Optional module context with the LSP connection
 * @returns An object wrapping the shared services and the language-specific services
 */
export declare function createInfoServices(context?: DefaultSharedCoreModuleContext): {
    shared: LangiumSharedCoreServices;
    Info: InfoServices;
};
export {};
