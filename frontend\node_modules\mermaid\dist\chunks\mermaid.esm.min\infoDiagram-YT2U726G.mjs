import{a as s}from"./chunk-ZIX2NOZJ.mjs";import"./chunk-GFOUM62E.mjs";import"./chunk-ACBE5OQD.mjs";import"./chunk-ITREFQHG.mjs";import"./chunk-YLPUNF7Q.mjs";import"./chunk-J4IYBS62.mjs";import{a as p}from"./chunk-2VWVV462.mjs";import{a}from"./chunk-ZKOTWRZ5.mjs";import{N as n,b as o}from"./chunk-63ZE7VZ5.mjs";import"./chunk-2QAHK7A2.mjs";import"./chunk-7LUIIE75.mjs";import"./chunk-JIBEMXPF.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as r}from"./chunk-GTKDMUJJ.mjs";var m={parse:r(async t=>{let e=await s("info",t);o.debug(e)},"parse")};var c={version:p.version+""},y=r(()=>c.version,"getVersion"),f={getVersion:y};var D=r((t,e,d)=>{o.debug(`rendering info diagram
`+t);let i=a(e);n(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${d}`)},"draw"),g={draw:D};var L={parser:m,db:f,renderer:g};export{L as diagram};
