/******************************************************************************
 * This file was generated by langium-cli 3.3.0.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/
import { AbstractAstReflection } from '../../syntax-tree.js';
export const LangiumGrammarTerminals = {
    ID: /\^?[_a-zA-Z][\w_]*/,
    STRING: /"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,
    NUMBER: /NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,
    RegexLiteral: /\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,
    WS: /\s+/,
    ML_COMMENT: /\/\*[\s\S]*?\*\//,
    SL_COMMENT: /\/\/[^\n\r]*/,
};
export const AbstractRule = 'AbstractRule';
export function isAbstractRule(item) {
    return reflection.isInstance(item, AbstractRule);
}
export const AbstractType = 'AbstractType';
export function isAbstractType(item) {
    return reflection.isInstance(item, AbstractType);
}
export const Condition = 'Condition';
export function isCondition(item) {
    return reflection.isInstance(item, Condition);
}
export function isFeatureName(item) {
    return isPrimitiveType(item) || item === 'current' || item === 'entry' || item === 'extends' || item === 'false' || item === 'fragment' || item === 'grammar' || item === 'hidden' || item === 'import' || item === 'interface' || item === 'returns' || item === 'terminal' || item === 'true' || item === 'type' || item === 'infer' || item === 'infers' || item === 'with' || (typeof item === 'string' && (/\^?[_a-zA-Z][\w_]*/.test(item)));
}
export function isPrimitiveType(item) {
    return item === 'string' || item === 'number' || item === 'boolean' || item === 'Date' || item === 'bigint';
}
export const TypeDefinition = 'TypeDefinition';
export function isTypeDefinition(item) {
    return reflection.isInstance(item, TypeDefinition);
}
export const ValueLiteral = 'ValueLiteral';
export function isValueLiteral(item) {
    return reflection.isInstance(item, ValueLiteral);
}
export const AbstractElement = 'AbstractElement';
export function isAbstractElement(item) {
    return reflection.isInstance(item, AbstractElement);
}
export const ArrayLiteral = 'ArrayLiteral';
export function isArrayLiteral(item) {
    return reflection.isInstance(item, ArrayLiteral);
}
export const ArrayType = 'ArrayType';
export function isArrayType(item) {
    return reflection.isInstance(item, ArrayType);
}
export const BooleanLiteral = 'BooleanLiteral';
export function isBooleanLiteral(item) {
    return reflection.isInstance(item, BooleanLiteral);
}
export const Conjunction = 'Conjunction';
export function isConjunction(item) {
    return reflection.isInstance(item, Conjunction);
}
export const Disjunction = 'Disjunction';
export function isDisjunction(item) {
    return reflection.isInstance(item, Disjunction);
}
export const Grammar = 'Grammar';
export function isGrammar(item) {
    return reflection.isInstance(item, Grammar);
}
export const GrammarImport = 'GrammarImport';
export function isGrammarImport(item) {
    return reflection.isInstance(item, GrammarImport);
}
export const InferredType = 'InferredType';
export function isInferredType(item) {
    return reflection.isInstance(item, InferredType);
}
export const Interface = 'Interface';
export function isInterface(item) {
    return reflection.isInstance(item, Interface);
}
export const NamedArgument = 'NamedArgument';
export function isNamedArgument(item) {
    return reflection.isInstance(item, NamedArgument);
}
export const Negation = 'Negation';
export function isNegation(item) {
    return reflection.isInstance(item, Negation);
}
export const NumberLiteral = 'NumberLiteral';
export function isNumberLiteral(item) {
    return reflection.isInstance(item, NumberLiteral);
}
export const Parameter = 'Parameter';
export function isParameter(item) {
    return reflection.isInstance(item, Parameter);
}
export const ParameterReference = 'ParameterReference';
export function isParameterReference(item) {
    return reflection.isInstance(item, ParameterReference);
}
export const ParserRule = 'ParserRule';
export function isParserRule(item) {
    return reflection.isInstance(item, ParserRule);
}
export const ReferenceType = 'ReferenceType';
export function isReferenceType(item) {
    return reflection.isInstance(item, ReferenceType);
}
export const ReturnType = 'ReturnType';
export function isReturnType(item) {
    return reflection.isInstance(item, ReturnType);
}
export const SimpleType = 'SimpleType';
export function isSimpleType(item) {
    return reflection.isInstance(item, SimpleType);
}
export const StringLiteral = 'StringLiteral';
export function isStringLiteral(item) {
    return reflection.isInstance(item, StringLiteral);
}
export const TerminalRule = 'TerminalRule';
export function isTerminalRule(item) {
    return reflection.isInstance(item, TerminalRule);
}
export const Type = 'Type';
export function isType(item) {
    return reflection.isInstance(item, Type);
}
export const TypeAttribute = 'TypeAttribute';
export function isTypeAttribute(item) {
    return reflection.isInstance(item, TypeAttribute);
}
export const UnionType = 'UnionType';
export function isUnionType(item) {
    return reflection.isInstance(item, UnionType);
}
export const Action = 'Action';
export function isAction(item) {
    return reflection.isInstance(item, Action);
}
export const Alternatives = 'Alternatives';
export function isAlternatives(item) {
    return reflection.isInstance(item, Alternatives);
}
export const Assignment = 'Assignment';
export function isAssignment(item) {
    return reflection.isInstance(item, Assignment);
}
export const CharacterRange = 'CharacterRange';
export function isCharacterRange(item) {
    return reflection.isInstance(item, CharacterRange);
}
export const CrossReference = 'CrossReference';
export function isCrossReference(item) {
    return reflection.isInstance(item, CrossReference);
}
export const EndOfFile = 'EndOfFile';
export function isEndOfFile(item) {
    return reflection.isInstance(item, EndOfFile);
}
export const Group = 'Group';
export function isGroup(item) {
    return reflection.isInstance(item, Group);
}
export const Keyword = 'Keyword';
export function isKeyword(item) {
    return reflection.isInstance(item, Keyword);
}
export const NegatedToken = 'NegatedToken';
export function isNegatedToken(item) {
    return reflection.isInstance(item, NegatedToken);
}
export const RegexToken = 'RegexToken';
export function isRegexToken(item) {
    return reflection.isInstance(item, RegexToken);
}
export const RuleCall = 'RuleCall';
export function isRuleCall(item) {
    return reflection.isInstance(item, RuleCall);
}
export const TerminalAlternatives = 'TerminalAlternatives';
export function isTerminalAlternatives(item) {
    return reflection.isInstance(item, TerminalAlternatives);
}
export const TerminalGroup = 'TerminalGroup';
export function isTerminalGroup(item) {
    return reflection.isInstance(item, TerminalGroup);
}
export const TerminalRuleCall = 'TerminalRuleCall';
export function isTerminalRuleCall(item) {
    return reflection.isInstance(item, TerminalRuleCall);
}
export const UnorderedGroup = 'UnorderedGroup';
export function isUnorderedGroup(item) {
    return reflection.isInstance(item, UnorderedGroup);
}
export const UntilToken = 'UntilToken';
export function isUntilToken(item) {
    return reflection.isInstance(item, UntilToken);
}
export const Wildcard = 'Wildcard';
export function isWildcard(item) {
    return reflection.isInstance(item, Wildcard);
}
export class LangiumGrammarAstReflection extends AbstractAstReflection {
    getAllTypes() {
        return [AbstractElement, AbstractRule, AbstractType, Action, Alternatives, ArrayLiteral, ArrayType, Assignment, BooleanLiteral, CharacterRange, Condition, Conjunction, CrossReference, Disjunction, EndOfFile, Grammar, GrammarImport, Group, InferredType, Interface, Keyword, NamedArgument, NegatedToken, Negation, NumberLiteral, Parameter, ParameterReference, ParserRule, ReferenceType, RegexToken, ReturnType, RuleCall, SimpleType, StringLiteral, TerminalAlternatives, TerminalGroup, TerminalRule, TerminalRuleCall, Type, TypeAttribute, TypeDefinition, UnionType, UnorderedGroup, UntilToken, ValueLiteral, Wildcard];
    }
    computeIsSubtype(subtype, supertype) {
        switch (subtype) {
            case Action:
            case Alternatives:
            case Assignment:
            case CharacterRange:
            case CrossReference:
            case EndOfFile:
            case Group:
            case Keyword:
            case NegatedToken:
            case RegexToken:
            case RuleCall:
            case TerminalAlternatives:
            case TerminalGroup:
            case TerminalRuleCall:
            case UnorderedGroup:
            case UntilToken:
            case Wildcard: {
                return this.isSubtype(AbstractElement, supertype);
            }
            case ArrayLiteral:
            case NumberLiteral:
            case StringLiteral: {
                return this.isSubtype(ValueLiteral, supertype);
            }
            case ArrayType:
            case ReferenceType:
            case SimpleType:
            case UnionType: {
                return this.isSubtype(TypeDefinition, supertype);
            }
            case BooleanLiteral: {
                return this.isSubtype(Condition, supertype) || this.isSubtype(ValueLiteral, supertype);
            }
            case Conjunction:
            case Disjunction:
            case Negation:
            case ParameterReference: {
                return this.isSubtype(Condition, supertype);
            }
            case InferredType:
            case Interface:
            case Type: {
                return this.isSubtype(AbstractType, supertype);
            }
            case ParserRule: {
                return this.isSubtype(AbstractRule, supertype) || this.isSubtype(AbstractType, supertype);
            }
            case TerminalRule: {
                return this.isSubtype(AbstractRule, supertype);
            }
            default: {
                return false;
            }
        }
    }
    getReferenceType(refInfo) {
        const referenceId = `${refInfo.container.$type}:${refInfo.property}`;
        switch (referenceId) {
            case 'Action:type':
            case 'CrossReference:type':
            case 'Interface:superTypes':
            case 'ParserRule:returnType':
            case 'SimpleType:typeRef': {
                return AbstractType;
            }
            case 'Grammar:hiddenTokens':
            case 'ParserRule:hiddenTokens':
            case 'RuleCall:rule': {
                return AbstractRule;
            }
            case 'Grammar:usedGrammars': {
                return Grammar;
            }
            case 'NamedArgument:parameter':
            case 'ParameterReference:parameter': {
                return Parameter;
            }
            case 'TerminalRuleCall:rule': {
                return TerminalRule;
            }
            default: {
                throw new Error(`${referenceId} is not a valid reference id.`);
            }
        }
    }
    getTypeMetaData(type) {
        switch (type) {
            case AbstractElement: {
                return {
                    name: AbstractElement,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' }
                    ]
                };
            }
            case ArrayLiteral: {
                return {
                    name: ArrayLiteral,
                    properties: [
                        { name: 'elements', defaultValue: [] }
                    ]
                };
            }
            case ArrayType: {
                return {
                    name: ArrayType,
                    properties: [
                        { name: 'elementType' }
                    ]
                };
            }
            case BooleanLiteral: {
                return {
                    name: BooleanLiteral,
                    properties: [
                        { name: 'true', defaultValue: false }
                    ]
                };
            }
            case Conjunction: {
                return {
                    name: Conjunction,
                    properties: [
                        { name: 'left' },
                        { name: 'right' }
                    ]
                };
            }
            case Disjunction: {
                return {
                    name: Disjunction,
                    properties: [
                        { name: 'left' },
                        { name: 'right' }
                    ]
                };
            }
            case Grammar: {
                return {
                    name: Grammar,
                    properties: [
                        { name: 'definesHiddenTokens', defaultValue: false },
                        { name: 'hiddenTokens', defaultValue: [] },
                        { name: 'imports', defaultValue: [] },
                        { name: 'interfaces', defaultValue: [] },
                        { name: 'isDeclared', defaultValue: false },
                        { name: 'name' },
                        { name: 'rules', defaultValue: [] },
                        { name: 'types', defaultValue: [] },
                        { name: 'usedGrammars', defaultValue: [] }
                    ]
                };
            }
            case GrammarImport: {
                return {
                    name: GrammarImport,
                    properties: [
                        { name: 'path' }
                    ]
                };
            }
            case InferredType: {
                return {
                    name: InferredType,
                    properties: [
                        { name: 'name' }
                    ]
                };
            }
            case Interface: {
                return {
                    name: Interface,
                    properties: [
                        { name: 'attributes', defaultValue: [] },
                        { name: 'name' },
                        { name: 'superTypes', defaultValue: [] }
                    ]
                };
            }
            case NamedArgument: {
                return {
                    name: NamedArgument,
                    properties: [
                        { name: 'calledByName', defaultValue: false },
                        { name: 'parameter' },
                        { name: 'value' }
                    ]
                };
            }
            case Negation: {
                return {
                    name: Negation,
                    properties: [
                        { name: 'value' }
                    ]
                };
            }
            case NumberLiteral: {
                return {
                    name: NumberLiteral,
                    properties: [
                        { name: 'value' }
                    ]
                };
            }
            case Parameter: {
                return {
                    name: Parameter,
                    properties: [
                        { name: 'name' }
                    ]
                };
            }
            case ParameterReference: {
                return {
                    name: ParameterReference,
                    properties: [
                        { name: 'parameter' }
                    ]
                };
            }
            case ParserRule: {
                return {
                    name: ParserRule,
                    properties: [
                        { name: 'dataType' },
                        { name: 'definesHiddenTokens', defaultValue: false },
                        { name: 'definition' },
                        { name: 'entry', defaultValue: false },
                        { name: 'fragment', defaultValue: false },
                        { name: 'hiddenTokens', defaultValue: [] },
                        { name: 'inferredType' },
                        { name: 'name' },
                        { name: 'parameters', defaultValue: [] },
                        { name: 'returnType' },
                        { name: 'wildcard', defaultValue: false }
                    ]
                };
            }
            case ReferenceType: {
                return {
                    name: ReferenceType,
                    properties: [
                        { name: 'referenceType' }
                    ]
                };
            }
            case ReturnType: {
                return {
                    name: ReturnType,
                    properties: [
                        { name: 'name' }
                    ]
                };
            }
            case SimpleType: {
                return {
                    name: SimpleType,
                    properties: [
                        { name: 'primitiveType' },
                        { name: 'stringType' },
                        { name: 'typeRef' }
                    ]
                };
            }
            case StringLiteral: {
                return {
                    name: StringLiteral,
                    properties: [
                        { name: 'value' }
                    ]
                };
            }
            case TerminalRule: {
                return {
                    name: TerminalRule,
                    properties: [
                        { name: 'definition' },
                        { name: 'fragment', defaultValue: false },
                        { name: 'hidden', defaultValue: false },
                        { name: 'name' },
                        { name: 'type' }
                    ]
                };
            }
            case Type: {
                return {
                    name: Type,
                    properties: [
                        { name: 'name' },
                        { name: 'type' }
                    ]
                };
            }
            case TypeAttribute: {
                return {
                    name: TypeAttribute,
                    properties: [
                        { name: 'defaultValue' },
                        { name: 'isOptional', defaultValue: false },
                        { name: 'name' },
                        { name: 'type' }
                    ]
                };
            }
            case UnionType: {
                return {
                    name: UnionType,
                    properties: [
                        { name: 'types', defaultValue: [] }
                    ]
                };
            }
            case Action: {
                return {
                    name: Action,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'feature' },
                        { name: 'inferredType' },
                        { name: 'lookahead' },
                        { name: 'operator' },
                        { name: 'type' }
                    ]
                };
            }
            case Alternatives: {
                return {
                    name: Alternatives,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'elements', defaultValue: [] },
                        { name: 'lookahead' }
                    ]
                };
            }
            case Assignment: {
                return {
                    name: Assignment,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'feature' },
                        { name: 'lookahead' },
                        { name: 'operator' },
                        { name: 'terminal' }
                    ]
                };
            }
            case CharacterRange: {
                return {
                    name: CharacterRange,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'left' },
                        { name: 'lookahead' },
                        { name: 'right' }
                    ]
                };
            }
            case CrossReference: {
                return {
                    name: CrossReference,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'deprecatedSyntax', defaultValue: false },
                        { name: 'lookahead' },
                        { name: 'terminal' },
                        { name: 'type' }
                    ]
                };
            }
            case EndOfFile: {
                return {
                    name: EndOfFile,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' }
                    ]
                };
            }
            case Group: {
                return {
                    name: Group,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'elements', defaultValue: [] },
                        { name: 'guardCondition' },
                        { name: 'lookahead' }
                    ]
                };
            }
            case Keyword: {
                return {
                    name: Keyword,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' },
                        { name: 'value' }
                    ]
                };
            }
            case NegatedToken: {
                return {
                    name: NegatedToken,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' },
                        { name: 'terminal' }
                    ]
                };
            }
            case RegexToken: {
                return {
                    name: RegexToken,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' },
                        { name: 'regex' }
                    ]
                };
            }
            case RuleCall: {
                return {
                    name: RuleCall,
                    properties: [
                        { name: 'arguments', defaultValue: [] },
                        { name: 'cardinality' },
                        { name: 'lookahead' },
                        { name: 'rule' }
                    ]
                };
            }
            case TerminalAlternatives: {
                return {
                    name: TerminalAlternatives,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'elements', defaultValue: [] },
                        { name: 'lookahead' }
                    ]
                };
            }
            case TerminalGroup: {
                return {
                    name: TerminalGroup,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'elements', defaultValue: [] },
                        { name: 'lookahead' }
                    ]
                };
            }
            case TerminalRuleCall: {
                return {
                    name: TerminalRuleCall,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' },
                        { name: 'rule' }
                    ]
                };
            }
            case UnorderedGroup: {
                return {
                    name: UnorderedGroup,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'elements', defaultValue: [] },
                        { name: 'lookahead' }
                    ]
                };
            }
            case UntilToken: {
                return {
                    name: UntilToken,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' },
                        { name: 'terminal' }
                    ]
                };
            }
            case Wildcard: {
                return {
                    name: Wildcard,
                    properties: [
                        { name: 'cardinality' },
                        { name: 'lookahead' }
                    ]
                };
            }
            default: {
                return {
                    name: type,
                    properties: []
                };
            }
        }
    }
}
export const reflection = new LangiumGrammarAstReflection();
//# sourceMappingURL=ast.js.map