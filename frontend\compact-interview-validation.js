/**
 * iFlytek 星火大模型智能面试系统 - 紧凑布局验证
 * Compact Interview Layout Validation for iFlytek Spark Interview System
 * 
 * 验证目标：
 * - 检查界面紧凑度是否达到预期
 * - 验证空间利用率是否提升
 * - 确认用户体验是否改善
 * - 检查响应式布局是否正常
 */

console.log('🔍 开始验证面试界面紧凑化效果...')
console.log('=' .repeat(60))

// 验证结果对象
const compactValidationResults = {
  spacingOptimization: {
    passed: 0,
    failed: 0,
    issues: []
  },
  contentDensity: {
    passed: 0,
    failed: 0,
    issues: []
  },
  visualHierarchy: {
    passed: 0,
    failed: 0,
    issues: []
  },
  userExperience: {
    passed: 0,
    failed: 0,
    issues: []
  },
  summary: {
    totalChecks: 0,
    passedChecks: 0,
    failedChecks: 0,
    compactScore: 0
  }
}

// 1. 检查间距优化
function validateSpacingOptimization() {
  console.log('\n📏 1. 验证间距优化')
  console.log('-'.repeat(40))
  
  const spacingChecks = [
    {
      selector: '.header-container',
      property: 'min-height',
      expected: 56,
      tolerance: 8,
      name: '头部容器高度'
    },
    {
      selector: '.ai-card',
      property: 'padding',
      expected: 20,
      tolerance: 4,
      name: 'AI卡片内边距'
    },
    {
      selector: '.candidate-section',
      property: 'padding',
      expected: 20,
      tolerance: 4,
      name: '候选人区域内边距'
    },
    {
      selector: '.main-container',
      property: 'gap',
      expected: 20,
      tolerance: 4,
      name: '主容器间距'
    }
  ]
  
  spacingChecks.forEach(check => {
    const elements = document.querySelectorAll(check.selector)
    
    if (elements.length === 0) {
      compactValidationResults.spacingOptimization.failed++
      compactValidationResults.spacingOptimization.issues.push(
        `${check.name}: 未找到元素 ${check.selector}`
      )
      console.log(`❌ ${check.name}: 未找到元素`)
      return
    }
    
    elements.forEach((element, index) => {
      const computedStyle = window.getComputedStyle(element)
      let actualValue
      
      if (check.property === 'padding') {
        actualValue = parseInt(computedStyle.paddingTop) || parseInt(computedStyle.padding)
      } else if (check.property === 'gap') {
        actualValue = parseInt(computedStyle.gap) || parseInt(computedStyle.gridGap)
      } else {
        actualValue = parseInt(computedStyle[check.property])
      }
      
      const difference = Math.abs(actualValue - check.expected)
      
      if (difference <= check.tolerance) {
        compactValidationResults.spacingOptimization.passed++
        console.log(`✅ ${check.name}[${index}]: ${actualValue}px (目标: ${check.expected}px)`)
      } else {
        compactValidationResults.spacingOptimization.failed++
        compactValidationResults.spacingOptimization.issues.push(
          `${check.name}[${index}]: 实际 ${actualValue}px，期望 ${check.expected}px (±${check.tolerance}px)`
        )
        console.log(`❌ ${check.name}[${index}]: ${actualValue}px (期望: ${check.expected}px)`)
      }
    })
  })
}

// 2. 检查内容密度
function validateContentDensity() {
  console.log('\n📊 2. 验证内容密度')
  console.log('-'.repeat(40))
  
  const viewportHeight = window.innerHeight
  const viewportWidth = window.innerWidth
  
  // 检查可视区域利用率
  const mainContainer = document.querySelector('.main-container')
  if (mainContainer) {
    const containerRect = mainContainer.getBoundingClientRect()
    const containerArea = containerRect.width * containerRect.height
    const viewportArea = viewportWidth * viewportHeight
    const utilizationRate = (containerArea / viewportArea) * 100
    
    if (utilizationRate >= 60) {
      compactValidationResults.contentDensity.passed++
      console.log(`✅ 屏幕利用率: ${utilizationRate.toFixed(1)}%`)
    } else {
      compactValidationResults.contentDensity.failed++
      compactValidationResults.contentDensity.issues.push(
        `屏幕利用率过低: ${utilizationRate.toFixed(1)}% (期望 ≥60%)`
      )
      console.log(`❌ 屏幕利用率: ${utilizationRate.toFixed(1)}% (期望 ≥60%)`)
    }
  }
  
  // 检查内容区域密度
  const contentElements = document.querySelectorAll('.ai-card, .candidate-section')
  let totalContentHeight = 0
  let totalContainerHeight = 0
  
  contentElements.forEach(element => {
    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)
    const padding = parseInt(style.paddingTop) + parseInt(style.paddingBottom)
    
    totalContentHeight += rect.height - padding
    totalContainerHeight += rect.height
  })
  
  if (totalContainerHeight > 0) {
    const contentDensity = (totalContentHeight / totalContainerHeight) * 100
    
    if (contentDensity >= 75) {
      compactValidationResults.contentDensity.passed++
      console.log(`✅ 内容密度: ${contentDensity.toFixed(1)}%`)
    } else {
      compactValidationResults.contentDensity.failed++
      compactValidationResults.contentDensity.issues.push(
        `内容密度过低: ${contentDensity.toFixed(1)}% (期望 ≥75%)`
      )
      console.log(`❌ 内容密度: ${contentDensity.toFixed(1)}% (期望 ≥75%)`)
    }
  }
}

// 3. 检查视觉层次
function validateVisualHierarchy() {
  console.log('\n👁️  3. 验证视觉层次')
  console.log('-'.repeat(40))
  
  const hierarchyChecks = [
    {
      selector: '.interview-title h1',
      property: 'font-size',
      expected: 18, // 1.125rem ≈ 18px
      tolerance: 2,
      name: '主标题字体大小'
    },
    {
      selector: '.question-text',
      property: 'font-size',
      expected: 14,
      tolerance: 1,
      name: '问题文字大小'
    },
    {
      selector: '.ai-info h3',
      property: 'font-size',
      expected: 16, // 1rem = 16px
      tolerance: 1,
      name: 'AI信息标题大小'
    }
  ]
  
  hierarchyChecks.forEach(check => {
    const elements = document.querySelectorAll(check.selector)
    
    if (elements.length === 0) {
      compactValidationResults.visualHierarchy.failed++
      compactValidationResults.visualHierarchy.issues.push(
        `${check.name}: 未找到元素 ${check.selector}`
      )
      console.log(`❌ ${check.name}: 未找到元素`)
      return
    }
    
    elements.forEach((element, index) => {
      const computedStyle = window.getComputedStyle(element)
      const actualValue = parseInt(computedStyle.fontSize)
      const difference = Math.abs(actualValue - check.expected)
      
      if (difference <= check.tolerance) {
        compactValidationResults.visualHierarchy.passed++
        console.log(`✅ ${check.name}[${index}]: ${actualValue}px`)
      } else {
        compactValidationResults.visualHierarchy.failed++
        compactValidationResults.visualHierarchy.issues.push(
          `${check.name}[${index}]: 实际 ${actualValue}px，期望 ${check.expected}px`
        )
        console.log(`❌ ${check.name}[${index}]: ${actualValue}px (期望: ${check.expected}px)`)
      }
    })
  })
}

// 4. 检查用户体验
function validateUserExperience() {
  console.log('\n🎯 4. 验证用户体验')
  console.log('-'.repeat(40))
  
  // 检查可点击区域大小
  const clickableElements = document.querySelectorAll('button, .el-button')
  let adequateClickTargets = 0
  let totalClickTargets = clickableElements.length
  
  clickableElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect()
    const minSize = 32 // 最小点击目标尺寸
    
    if (rect.width >= minSize && rect.height >= minSize) {
      adequateClickTargets++
    }
  })
  
  if (totalClickTargets > 0) {
    const clickTargetRate = (adequateClickTargets / totalClickTargets) * 100
    
    if (clickTargetRate >= 90) {
      compactValidationResults.userExperience.passed++
      console.log(`✅ 点击目标适当性: ${clickTargetRate.toFixed(1)}%`)
    } else {
      compactValidationResults.userExperience.failed++
      compactValidationResults.userExperience.issues.push(
        `点击目标过小: ${clickTargetRate.toFixed(1)}% 符合要求 (期望 ≥90%)`
      )
      console.log(`❌ 点击目标适当性: ${clickTargetRate.toFixed(1)}%`)
    }
  }
  
  // 检查文本可读性
  const textElements = document.querySelectorAll('p, span, div:not(:empty)')
  let readableTextCount = 0
  let totalTextCount = 0
  
  textElements.forEach(element => {
    const style = window.getComputedStyle(element)
    const fontSize = parseInt(style.fontSize)
    const lineHeight = parseFloat(style.lineHeight)
    
    if (element.textContent.trim().length > 0) {
      totalTextCount++
      
      if (fontSize >= 12 && (lineHeight >= 1.4 || lineHeight === 0)) {
        readableTextCount++
      }
    }
  })
  
  if (totalTextCount > 0) {
    const readabilityRate = (readableTextCount / totalTextCount) * 100
    
    if (readabilityRate >= 85) {
      compactValidationResults.userExperience.passed++
      console.log(`✅ 文本可读性: ${readabilityRate.toFixed(1)}%`)
    } else {
      compactValidationResults.userExperience.failed++
      compactValidationResults.userExperience.issues.push(
        `文本可读性不足: ${readabilityRate.toFixed(1)}% (期望 ≥85%)`
      )
      console.log(`❌ 文本可读性: ${readabilityRate.toFixed(1)}%`)
    }
  }
}

// 5. 生成紧凑化验证报告
function generateCompactValidationReport() {
  console.log('\n📊 紧凑化验证报告')
  console.log('=' .repeat(60))
  
  // 计算总体统计
  const totalPassed = compactValidationResults.spacingOptimization.passed + 
                     compactValidationResults.contentDensity.passed + 
                     compactValidationResults.visualHierarchy.passed + 
                     compactValidationResults.userExperience.passed
  
  const totalFailed = compactValidationResults.spacingOptimization.failed + 
                     compactValidationResults.contentDensity.failed + 
                     compactValidationResults.visualHierarchy.failed + 
                     compactValidationResults.userExperience.failed
  
  const totalChecks = totalPassed + totalFailed
  const compactScore = totalChecks > 0 ? ((totalPassed / totalChecks) * 100) : 0
  
  compactValidationResults.summary = {
    totalChecks,
    passedChecks: totalPassed,
    failedChecks: totalFailed,
    compactScore: parseFloat(compactScore.toFixed(1))
  }
  
  console.log(`📈 紧凑化评分: ${compactScore.toFixed(1)}分 (${totalPassed}/${totalChecks})`)
  console.log(`✅ 通过检查: ${totalPassed}`)
  console.log(`❌ 失败检查: ${totalFailed}`)
  
  // 评级
  let grade = 'F'
  if (compactScore >= 90) grade = 'A+'
  else if (compactScore >= 80) grade = 'A'
  else if (compactScore >= 70) grade = 'B'
  else if (compactScore >= 60) grade = 'C'
  else if (compactScore >= 50) grade = 'D'
  
  console.log(`🏆 紧凑化等级: ${grade}`)
  
  // 详细报告
  console.log('\n📋 详细检查结果:')
  console.log(`📏 间距优化: ${compactValidationResults.spacingOptimization.passed}✅ ${compactValidationResults.spacingOptimization.failed}❌`)
  console.log(`📊 内容密度: ${compactValidationResults.contentDensity.passed}✅ ${compactValidationResults.contentDensity.failed}❌`)
  console.log(`👁️  视觉层次: ${compactValidationResults.visualHierarchy.passed}✅ ${compactValidationResults.visualHierarchy.failed}❌`)
  console.log(`🎯 用户体验: ${compactValidationResults.userExperience.passed}✅ ${compactValidationResults.userExperience.failed}❌`)
  
  // 问题列表
  const allIssues = [
    ...compactValidationResults.spacingOptimization.issues,
    ...compactValidationResults.contentDensity.issues,
    ...compactValidationResults.visualHierarchy.issues,
    ...compactValidationResults.userExperience.issues
  ]
  
  if (allIssues.length > 0) {
    console.log('\n⚠️  发现的问题:')
    allIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`)
    })
  } else {
    console.log('\n🎉 界面紧凑化完美！')
  }
  
  return compactValidationResults
}

// 执行验证
function runCompactValidation() {
  try {
    validateSpacingOptimization()
    validateContentDensity()
    validateVisualHierarchy()
    validateUserExperience()
    
    const results = generateCompactValidationReport()
    
    console.log('\n🏁 面试界面紧凑化验证完成')
    console.log('=' .repeat(60))
    
    return results
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error)
    return null
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runCompactValidation)
  } else {
    runCompactValidation()
  }
  
  // 导出到全局作用域以便手动调用
  window.validateCompactInterview = runCompactValidation
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runCompactValidation }
}
