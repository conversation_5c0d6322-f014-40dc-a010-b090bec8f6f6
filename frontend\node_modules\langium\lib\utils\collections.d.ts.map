{"version": 3, "file": "collections.d.ts", "sourceRoot": "", "sources": ["../../src/utils/collections.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAG1C;;GAEG;AACH,qBAAa,QAAQ,CAAC,CAAC,EAAE,CAAC;IAEtB,OAAO,CAAC,GAAG,CAAqB;;gBAGpB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IASnC;;OAEG;IACH,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED;;OAEG;IACH,KAAK,IAAI,IAAI;IAIb;;;;;;;OAOG;IACH,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO;IAoBlC;;;;;;OAMG;IACH,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE;IAIzB;;;;OAIG;IACH,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO;IAY/B;;OAEG;IACH,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI;IAS3B;;OAEG;IACH,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;IASzC;;OAEG;IACH,OAAO,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,KAAK,IAAI,GAAG,IAAI;IAMhE;;OAEG;IACH,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAIrC;;OAEG;IACH,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAKzB;;OAEG;IACH,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC;IAIjB;;OAEG;IACH,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC;IAInB;;OAEG;IACH,mBAAmB,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAI1C;AAED,qBAAa,KAAK,CAAC,CAAC,EAAE,CAAC;IAEnB,OAAO,CAAC,GAAG,CAAmB;IAC9B,OAAO,CAAC,OAAO,CAAmB;IAElC,IAAI,IAAI,IAAI,MAAM,CAEjB;;gBAGW,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IASnC,KAAK,IAAI,IAAI;IAKb,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI;IAM3B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,SAAS;IAI1B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,SAAS;IAI/B,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;CAS1B"}