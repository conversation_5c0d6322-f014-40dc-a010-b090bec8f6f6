/**
 * @license
 * web-streams-polyfill v3.3.3
 * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";function t(){}function r(e){return"object"==typeof e&&null!==e||"function"==typeof e}const o=t;function n(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}const a=Promise,i=Promise.prototype.then,l=Promise.reject.bind(a);function s(e){return new a(e)}function u(e){return s((t=>t(e)))}function c(e){return l(e)}function d(e,t,r){return i.call(e,t,r)}function f(e,t,r){d(d(e,t,r),void 0,o)}function b(e,t){f(e,t)}function m(e,t){f(e,void 0,t)}function h(e,t,r){return d(e,t,r)}function _(e){d(e,void 0,o)}let p=e=>{if("function"==typeof queueMicrotask)p=queueMicrotask;else{const e=u(void 0);p=t=>d(e,t)}return p(e)};function y(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function S(e,t,r){try{return u(y(e,t,r))}catch(e){return c(e)}}class g{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const v=Symbol("[[AbortSteps]]"),w=Symbol("[[ErrorSteps]]"),R=Symbol("[[CancelSteps]]"),T=Symbol("[[PullSteps]]"),C=Symbol("[[ReleaseSteps]]");function P(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?B(e):"closed"===t._state?function(e){B(e),k(e)}(e):O(e,t._storedError)}function q(e,t){return kr(e._ownerReadableStream,t)}function E(e){const t=e._ownerReadableStream;"readable"===t._state?j(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){O(e,t)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[C](),t._reader=void 0,e._ownerReadableStream=void 0}function W(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function B(e){e._closedPromise=s(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function O(e,t){B(e),j(e,t)}function j(e,t){void 0!==e._closedPromise_reject&&(_(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function k(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const A=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function z(e,t){if(void 0!==e&&("object"!=typeof(r=e)&&"function"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function I(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function L(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function F(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function $(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function M(e){return Number(e)}function Y(e){return 0===e?0:e}function x(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=Y(o),!A(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return Y(D(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return A(o)&&0!==o?o:0}function Q(e,t){if(!Or(e))throw new TypeError(`${t} is not a ReadableStream.`)}function N(e){return new ReadableStreamDefaultReader(e)}function H(e,t){e._reader._readRequests.push(t)}function V(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function U(e){return e._reader._readRequests.length}function G(e){const t=e._reader;return void 0!==t&&!!X(t)}class ReadableStreamDefaultReader{constructor(e){if(F(e,1,"ReadableStreamDefaultReader"),Q(e,"First parameter"),jr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");P(this,e),this._readRequests=new g}get closed(){return X(this)?this._closedPromise:c(Z("closed"))}cancel(e=void 0){return X(this)?void 0===this._ownerReadableStream?c(W("cancel")):q(this,e):c(Z("cancel"))}read(){if(!X(this))return c(Z("read"));if(void 0===this._ownerReadableStream)return c(W("read from"));let e,t;const r=s(((r,o)=>{e=r,t=o}));return J(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!X(this))throw Z("releaseLock");void 0!==this._ownerReadableStream&&function(e){E(e);const t=new TypeError("Reader was released");K(e,t)}(this)}}function X(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof ReadableStreamDefaultReader)}function J(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[T](t)}function K(e,t){const r=e._readRequests;e._readRequests=new g,r.forEach((e=>{e._errorSteps(t)}))}function Z(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}function ee(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],o=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function te(e){return this instanceof te?(this.v=e,this):new te(e)}function re(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,n=r.apply(e,t||[]),a=[];return o={},i("next"),i("throw"),i("return"),o[Symbol.asyncIterator]=function(){return this},o;function i(e){n[e]&&(o[e]=function(t){return new Promise((function(r,o){a.push([e,t,r,o])>1||l(e,t)}))})}function l(e,t){try{(r=n[e](t)).value instanceof te?Promise.resolve(r.value.v).then(s,u):c(a[0][2],r)}catch(e){c(a[0][3],e)}var r}function s(e){l("next",e)}function u(e){l("throw",e)}function c(e,t){e(t),a.shift(),a.length&&l(a[0][0],a[0][1])}}function oe(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=ee(e),t={},o("next"),o("throw"),o("return"),t[Symbol.asyncIterator]=function(){return this},t);function o(r){t[r]=e[r]&&function(t){return new Promise((function(o,n){(function(e,t,r,o){Promise.resolve(o).then((function(t){e({value:t,done:r})}),t)})(o,n,(t=e[r](t)).done,t.value)}))}}}var ne,ae,ie;function le(e){return e.slice()}function se(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamDefaultReader.prototype.cancel,"cancel"),n(ReadableStreamDefaultReader.prototype.read,"read"),n(ReadableStreamDefaultReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0}),"function"==typeof SuppressedError&&SuppressedError;let ue=e=>(ue="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e,ue(e)),ce=e=>(ce="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength,ce(e));function de(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return se(n,0,e,t,o),n}function fe(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(`${String(t)} is not a function`);return r}}function be(e){const t={[Symbol.iterator]:()=>e.iterator},r=function(){return re(this,arguments,(function*(){return yield te(yield te(yield*function(e){var t,r;return t={},o("next"),o("throw",(function(e){throw e})),o("return"),t[Symbol.iterator]=function(){return this},t;function o(o,n){t[o]=e[o]?function(t){return(r=!r)?{value:te(e[o](t)),done:!1}:n?n(t):t}:n}}(oe(t))))}))}();return{iterator:r,nextMethod:r.next,done:!1}}const me=null!==(ie=null!==(ne=Symbol.asyncIterator)&&void 0!==ne?ne:null===(ae=Symbol.for)||void 0===ae?void 0:ae.call(Symbol,"Symbol.asyncIterator"))&&void 0!==ie?ie:"@@asyncIterator";function he(e,t="sync",o){if(void 0===o)if("async"===t){if(void 0===(o=fe(e,me))){return be(he(e,"sync",fe(e,Symbol.iterator)))}}else o=fe(e,Symbol.iterator);if(void 0===o)throw new TypeError("The object is not iterable");const n=y(o,e,[]);if(!r(n))throw new TypeError("The iterator method must return an object");return{iterator:n,nextMethod:n.next,done:!1}}const _e={[me](){return this}};Object.defineProperty(_e,me,{enumerable:!1});class pe{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?h(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?h(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;let t,r;const o=s(((e,o)=>{t=e,r=o}));return J(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,p((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,E(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,E(e),r(t)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(!this._preventCancel){const r=q(t,e);return E(t),h(r,(()=>({value:e,done:!0})))}return E(t),u({value:e,done:!0})}}const ye={next(){return Se(this)?this._asyncIteratorImpl.next():c(ge("next"))},return(e){return Se(this)?this._asyncIteratorImpl.return(e):c(ge("return"))}};function Se(e){if(!r(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof pe}catch(e){return!1}}function ge(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.setPrototypeOf(ye,_e);const ve=Number.isNaN||function(e){return e!=e};function we(e){const t=de(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function Re(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Te(e,t,r){if("number"!=typeof(o=r)||ve(o)||o<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function Ce(e){e._queue=new g,e._queueTotalSize=0}function Pe(e){return e===DataView}class ReadableStreamBYOBRequest{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Ee(this))throw et("view");return this._view}respond(e){if(!Ee(this))throw et("respond");if(F(e,1,"respond"),e=x(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(ce(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Je(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!Ee(this))throw et("respondWithNewView");if(F(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(ce(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Ke(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),n(ReadableStreamBYOBRequest.prototype.respond,"respond"),n(ReadableStreamBYOBRequest.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!qe(this))throw tt("byobRequest");return Ge(this)}get desiredSize(){if(!qe(this))throw tt("desiredSize");return Xe(this)}close(){if(!qe(this))throw tt("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);Ne(this)}enqueue(e){if(!qe(this))throw tt("enqueue");if(F(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);He(this,e)}error(e=void 0){if(!qe(this))throw tt("error");Ve(this,e)}[R](e){Be(this),Ce(this);const t=this._cancelAlgorithm(e);return Qe(this),t}[T](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void Ue(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}H(t,e),We(this)}[C](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new g,this._pendingPullIntos.push(e)}}}function qe(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ReadableByteStreamController)}function Ee(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ReadableStreamBYOBRequest)}function We(e){const t=function(e){const t=e._controlledReadableByteStream;if("readable"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(G(t)&&U(t)>0)return!0;if(it(t)&&at(t)>0)return!0;const r=Xe(e);if(r>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;f(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,We(e)),null)),(t=>(Ve(e,t),null)))}function Be(e){Fe(e),e._pendingPullIntos=new g}function Oe(e,t){let r=!1;"closed"===e._state&&(r=!0);const o=je(t);"default"===t.readerType?V(e,o,r):function(e,t,r){const o=e._reader,n=o._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,o,r)}function je(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function ke(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function Ae(e,t,r,o){let n;try{n=de(t,r,r+o)}catch(t){throw Ve(e,t),t}ke(e,n,0,o)}function De(e,t){t.bytesFilled>0&&Ae(e,t.buffer,t.byteOffset,t.bytesFilled),xe(e)}function ze(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+r;let n=r,a=!1;const i=o-o%t.elementSize;i>=t.minimumFill&&(n=i-t.bytesFilled,a=!0);const l=e._queue;for(;n>0;){const r=l.peek(),o=Math.min(n,r.byteLength),a=t.byteOffset+t.bytesFilled;se(t.buffer,a,r.buffer,r.byteOffset,o),r.byteLength===o?l.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,Ie(e,o,t),n-=o}return a}function Ie(e,t,r){r.bytesFilled+=t}function Le(e){0===e._queueTotalSize&&e._closeRequested?(Qe(e),Ar(e._controlledReadableByteStream)):We(e)}function Fe(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function $e(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();ze(e,t)&&(xe(e),Oe(e._controlledReadableByteStream,t))}}function Me(e,t,r,o){const n=e._controlledReadableByteStream,a=t.constructor,i=function(e){return Pe(e)?1:e.BYTES_PER_ELEMENT}(a),{byteOffset:l,byteLength:s}=t,u=r*i;let c;try{c=ue(t.buffer)}catch(e){return void o._errorSteps(e)}const d={buffer:c,bufferByteLength:c.byteLength,byteOffset:l,byteLength:s,bytesFilled:0,minimumFill:u,elementSize:i,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(d),void nt(n,o);if("closed"!==n._state){if(e._queueTotalSize>0){if(ze(e,d)){const t=je(d);return Le(e),void o._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return Ve(e,t),void o._errorSteps(t)}}e._pendingPullIntos.push(d),nt(n,o),We(e)}else{const e=new a(d.buffer,d.byteOffset,0);o._closeSteps(e)}}function Ye(e,t){const r=e._pendingPullIntos.peek();Fe(e);"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&xe(e);const r=e._controlledReadableByteStream;if(it(r))for(;at(r)>0;)Oe(r,xe(e))}(e,r):function(e,t,r){if(Ie(0,t,r),"none"===r.readerType)return De(e,r),void $e(e);if(r.bytesFilled<r.minimumFill)return;xe(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;Ae(e,r.buffer,t-o,o)}r.bytesFilled-=o,Oe(e._controlledReadableByteStream,r),$e(e)}(e,t,r),We(e)}function xe(e){return e._pendingPullIntos.shift()}function Qe(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Ne(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Ve(e,t),t}}Qe(e),Ar(t)}}function He(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const{buffer:o,byteOffset:n,byteLength:a}=t;if(ce(o))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const i=ue(o);if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(ce(t.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Fe(e),t.buffer=ue(t.buffer),"none"===t.readerType&&De(e,t)}if(G(r))if(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;Ue(e,t._readRequests.shift())}}(e),0===U(r))ke(e,i,n,a);else{e._pendingPullIntos.length>0&&xe(e);V(r,new Uint8Array(i,n,a),!1)}else it(r)?(ke(e,i,n,a),$e(e)):ke(e,i,n,a);We(e)}function Ve(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(Be(e),Ce(e),Qe(e),Dr(r,t))}function Ue(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,Le(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function Ge(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}function Xe(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Je(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=ue(r.buffer),Ye(e,t)}function Ke(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const o=t.byteLength;r.buffer=ue(t.buffer),Ye(e,o)}function Ze(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Ce(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new g,e._readableStreamController=t;f(u(r()),(()=>(t._started=!0,We(t),null)),(e=>(Ve(t,e),null)))}function et(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function tt(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function rt(e,t){if("byob"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function ot(e){return new ReadableStreamBYOBReader(e)}function nt(e,t){e._reader._readIntoRequests.push(t)}function at(e){return e._reader._readIntoRequests.length}function it(e){const t=e._reader;return void 0!==t&&!!lt(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableByteStreamController.prototype.close,"close"),n(ReadableByteStreamController.prototype.enqueue,"enqueue"),n(ReadableByteStreamController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if(F(e,1,"ReadableStreamBYOBReader"),Q(e,"First parameter"),jr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!qe(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");P(this,e),this._readIntoRequests=new g}get closed(){return lt(this)?this._closedPromise:c(ct("closed"))}cancel(e=void 0){return lt(this)?void 0===this._ownerReadableStream?c(W("cancel")):q(this,e):c(ct("cancel"))}read(e,t={}){if(!lt(this))return c(ct("read"));if(!ArrayBuffer.isView(e))return c(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return c(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return c(new TypeError("view's buffer must have non-zero byteLength"));if(ce(e.buffer))return c(new TypeError("view's buffer has been detached"));let r;try{r=function(e,t){var r;return z(e,t),{min:x(null!==(r=null==e?void 0:e.min)&&void 0!==r?r:1,`${t} has member 'min' that`)}}(t,"options")}catch(e){return c(e)}const o=r.min;if(0===o)return c(new TypeError("options.min must be greater than 0"));if(function(e){return Pe(e.constructor)}(e)){if(o>e.byteLength)return c(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(o>e.length)return c(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return c(W("read from"));let n,a;const i=s(((e,t)=>{n=e,a=t}));return st(this,e,o,{_chunkSteps:e=>n({value:e,done:!1}),_closeSteps:e=>n({value:e,done:!0}),_errorSteps:e=>a(e)}),i}releaseLock(){if(!lt(this))throw ct("releaseLock");void 0!==this._ownerReadableStream&&function(e){E(e);const t=new TypeError("Reader was released");ut(e,t)}(this)}}function lt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof ReadableStreamBYOBReader)}function st(e,t,r,o){const n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?o._errorSteps(n._storedError):Me(n._readableStreamController,t,r,o)}function ut(e,t){const r=e._readIntoRequests;e._readIntoRequests=new g,r.forEach((e=>{e._errorSteps(t)}))}function ct(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function dt(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(ve(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function ft(e){const{size:t}=e;return t||(()=>1)}function bt(e,t){z(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:M(r),size:void 0===o?void 0:mt(o,`${t} has member 'size' that`)}}function mt(e,t){return I(e,t),t=>M(e(t))}function ht(e,t,r){return I(e,r),r=>S(e,t,[r])}function _t(e,t,r){return I(e,r),()=>S(e,t,[])}function pt(e,t,r){return I(e,r),r=>y(e,t,[r])}function yt(e,t,r){return I(e,r),(r,o)=>S(e,t,[r,o])}function St(e,t){if(!Rt(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamBYOBReader.prototype.cancel,"cancel"),n(ReadableStreamBYOBReader.prototype.read,"read"),n(ReadableStreamBYOBReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});const gt="function"==typeof AbortController;class WritableStream{constructor(e={},t={}){void 0===e?e=null:L(e,"First parameter");const r=bt(t,"Second parameter"),o=function(e,t){z(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:ht(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:_t(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:pt(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:yt(i,e,`${t} has member 'write' that`),type:a}}(e,"First parameter");wt(this);if(void 0!==o.type)throw new RangeError("Invalid type is specified");const n=ft(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a,i,l,s;a=void 0!==t.start?()=>t.start(n):()=>{};i=void 0!==t.write?e=>t.write(e,n):()=>u(void 0);l=void 0!==t.close?()=>t.close():()=>u(void 0);s=void 0!==t.abort?e=>t.abort(e):()=>u(void 0);Mt(e,n,a,i,l,s,r,o)}(this,o,dt(r,1),n)}get locked(){if(!Rt(this))throw Ut("locked");return Tt(this)}abort(e=void 0){return Rt(this)?Tt(this)?c(new TypeError("Cannot abort a stream that already has a writer")):Ct(this,e):c(Ut("abort"))}close(){return Rt(this)?Tt(this)?c(new TypeError("Cannot close a stream that already has a writer")):Bt(this)?c(new TypeError("Cannot close an already-closing stream")):Pt(this):c(Ut("close"))}getWriter(){if(!Rt(this))throw Ut("getWriter");return vt(this)}}function vt(e){return new WritableStreamDefaultWriter(e)}function wt(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new g,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function Rt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof WritableStream)}function Tt(e){return void 0!==e._writer}function Ct(e,t){var r;if("closed"===e._state||"errored"===e._state)return u(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if("closed"===o||"errored"===o)return u(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);const a=s(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||Et(e,t),a}function Pt(e){const t=e._state;if("closed"===t||"errored"===t)return c(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=s(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&"writable"===t&&ir(o),Te(n=e._writableStreamController,Ft,0),Qt(n),r}function qt(e,t){"writable"!==e._state?Wt(e):Et(e,t)}function Et(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const o=e._writer;void 0!==o&&zt(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&Wt(e)}function Wt(e){e._state="errored",e._writableStreamController[w]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new g,void 0===e._pendingAbortRequest)return void Ot(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void Ot(e);f(e._writableStreamController[v](r._reason),(()=>(r._resolve(),Ot(e),null)),(t=>(r._reject(t),Ot(e),null)))}function Bt(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Ot(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&er(t,e._storedError)}function jt(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){rr(e)}(r):ir(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),n(WritableStream.prototype.abort,"abort"),n(WritableStream.prototype.close,"close"),n(WritableStream.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStream.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if(F(e,1,"WritableStreamDefaultWriter"),St(e,"First parameter"),Tt(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!Bt(e)&&e._backpressure?rr(this):nr(this),Kt(this);else if("erroring"===t)or(this,e._storedError),Kt(this);else if("closed"===t)nr(this),Kt(r=this),tr(r);else{const t=e._storedError;or(this,t),Zt(this,t)}var r}get closed(){return kt(this)?this._closedPromise:c(Xt("closed"))}get desiredSize(){if(!kt(this))throw Xt("desiredSize");if(void 0===this._ownerWritableStream)throw Jt("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;if("errored"===r||"erroring"===r)return null;if("closed"===r)return 0;return xt(t._writableStreamController)}(this)}get ready(){return kt(this)?this._readyPromise:c(Xt("ready"))}abort(e=void 0){return kt(this)?void 0===this._ownerWritableStream?c(Jt("abort")):function(e,t){return Ct(e._ownerWritableStream,t)}(this,e):c(Xt("abort"))}close(){if(!kt(this))return c(Xt("close"));const e=this._ownerWritableStream;return void 0===e?c(Jt("close")):Bt(e)?c(new TypeError("Cannot close an already-closing stream")):At(this)}releaseLock(){if(!kt(this))throw Xt("releaseLock");void 0!==this._ownerWritableStream&&It(this)}write(e=void 0){return kt(this)?void 0===this._ownerWritableStream?c(Jt("write to")):Lt(this,e):c(Xt("write"))}}function kt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof WritableStreamDefaultWriter)}function At(e){return Pt(e._ownerWritableStream)}function Dt(e,t){"pending"===e._closedPromiseState?er(e,t):function(e,t){Zt(e,t)}(e,t)}function zt(e,t){"pending"===e._readyPromiseState?ar(e,t):function(e,t){or(e,t)}(e,t)}function It(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");zt(e,r),Dt(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function Lt(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return Nt(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return c(Jt("write to"));const a=r._state;if("errored"===a)return c(r._storedError);if(Bt(r)||"closed"===a)return c(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return c(r._storedError);const i=function(e){return s(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{Te(e,t,r)}catch(t){return void Nt(e,t)}const o=e._controlledWritableStream;if(!Bt(o)&&"writable"===o._state){jt(o,Ht(e))}Qt(e)}(o,t,n),i}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),n(WritableStreamDefaultWriter.prototype.abort,"abort"),n(WritableStreamDefaultWriter.prototype.close,"close"),n(WritableStreamDefaultWriter.prototype.releaseLock,"releaseLock"),n(WritableStreamDefaultWriter.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const Ft={};class WritableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!$t(this))throw Gt("abortReason");return this._abortReason}get signal(){if(!$t(this))throw Gt("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e=void 0){if(!$t(this))throw Gt("error");"writable"===this._controlledWritableStream._state&&Vt(this,e)}[v](e){const t=this._abortAlgorithm(e);return Yt(this),t}[w](){Ce(this)}}function $t(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof WritableStreamDefaultController)}function Mt(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Ce(t),t._abortReason=void 0,t._abortController=function(){if(gt)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=Ht(t);jt(e,s);f(u(r()),(()=>(t._started=!0,Qt(t),null)),(r=>(t._started=!0,qt(e,r),null)))}function Yt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function xt(e){return e._strategyHWM-e._queueTotalSize}function Qt(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void Wt(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===Ft?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),Re(e);const r=e._closeAlgorithm();Yt(e),f(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&tr(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),qt(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);const o=e._writeAlgorithm(t);f(o,(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(Re(e),!Bt(r)&&"writable"===t){const t=Ht(e);jt(r,t)}return Qt(e),null}),(t=>("writable"===r._state&&Yt(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,qt(e,t)}(r,t),null)))}(e,r)}function Nt(e,t){"writable"===e._controlledWritableStream._state&&Vt(e,t)}function Ht(e){return xt(e)<=0}function Vt(e,t){const r=e._controlledWritableStream;Yt(e),Et(r,t)}function Ut(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Gt(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Xt(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function Jt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Kt(e){e._closedPromise=s(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function Zt(e,t){Kt(e),er(e,t)}function er(e,t){void 0!==e._closedPromise_reject&&(_(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function tr(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function rr(e){e._readyPromise=s(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function or(e,t){rr(e),ar(e,t)}function nr(e){rr(e),ir(e)}function ar(e,t){void 0!==e._readyPromise_reject&&(_(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function ir(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const lr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0;const sr=function(){const e=null==lr?void 0:lr.DOMException;return function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?e:void 0}()||function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return n(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function ur(e,r,o,n,a,i){const l=N(e),h=vt(r);e._disturbed=!0;let p=!1,y=u(void 0);return s(((S,g)=>{let v;if(void 0!==i){if(v=()=>{const t=void 0!==i.reason?i.reason:new sr("Aborted","AbortError"),o=[];n||o.push((()=>"writable"===r._state?Ct(r,t):u(void 0))),a||o.push((()=>"readable"===e._state?kr(e,t):u(void 0))),q((()=>Promise.all(o.map((e=>e())))),!0,t)},i.aborted)return void v();i.addEventListener("abort",v)}var w,R,T;if(P(e,l._closedPromise,(e=>(n?W(!0,e):q((()=>Ct(r,e)),!0,e),null))),P(r,h._closedPromise,(t=>(a?W(!0,t):q((()=>kr(e,t)),!0,t),null))),w=e,R=l._closedPromise,T=()=>(o?W():q((()=>function(e){const t=e._ownerWritableStream,r=t._state;return Bt(t)||"closed"===r?u(void 0):"errored"===r?c(t._storedError):At(e)}(h))),null),"closed"===w._state?T():b(R,T),Bt(r)||"closed"===r._state){const t=new TypeError("the destination writable stream closed before all data could be piped to it");a?W(!0,t):q((()=>kr(e,t)),!0,t)}function C(){const e=y;return d(y,(()=>e!==y?C():void 0))}function P(e,t,r){"errored"===e._state?r(e._storedError):m(t,r)}function q(e,t,o){function n(){return f(e(),(()=>B(t,o)),(e=>B(!0,e))),null}p||(p=!0,"writable"!==r._state||Bt(r)?n():b(C(),n))}function W(e,t){p||(p=!0,"writable"!==r._state||Bt(r)?B(e,t):b(C(),(()=>B(e,t))))}function B(e,t){return It(h),E(l),void 0!==i&&i.removeEventListener("abort",v),e?g(t):S(void 0),null}_(s(((e,r)=>{!function o(n){n?e():d(p?u(!0):d(h._readyPromise,(()=>s(((e,r)=>{J(l,{_chunkSteps:r=>{y=d(Lt(h,r),void 0,t),e(!1)},_closeSteps:()=>e(!0),_errorSteps:r})})))),o,r)}(!1)})))}))}class ReadableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!cr(this))throw gr("desiredSize");return pr(this)}close(){if(!cr(this))throw gr("close");if(!yr(this))throw new TypeError("The stream is not in a state that permits close");mr(this)}enqueue(e=void 0){if(!cr(this))throw gr("enqueue");if(!yr(this))throw new TypeError("The stream is not in a state that permits enqueue");return hr(this,e)}error(e=void 0){if(!cr(this))throw gr("error");_r(this,e)}[R](e){Ce(this);const t=this._cancelAlgorithm(e);return br(this),t}[T](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=Re(this);this._closeRequested&&0===this._queue.length?(br(this),Ar(t)):dr(this),e._chunkSteps(r)}else H(t,e),dr(this)}[C](){}}function cr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof ReadableStreamDefaultController)}function dr(e){if(!fr(e))return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;f(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,dr(e)),null)),(t=>(_r(e,t),null)))}function fr(e){const t=e._controlledReadableStream;if(!yr(e))return!1;if(!e._started)return!1;if(jr(t)&&U(t)>0)return!0;return pr(e)>0}function br(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function mr(e){if(!yr(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(br(e),Ar(t))}function hr(e,t){if(!yr(e))return;const r=e._controlledReadableStream;if(jr(r)&&U(r)>0)V(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw _r(e,t),t}try{Te(e,t,r)}catch(t){throw _r(e,t),t}}dr(e)}function _r(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(Ce(e),br(e),Dr(r,t))}function pr(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function yr(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function Sr(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Ce(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t;f(u(r()),(()=>(t._started=!0,dr(t),null)),(e=>(_r(t,e),null)))}function gr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function vr(e,t){return qe(e._readableStreamController)?function(e){let t,r,o,n,a,i=N(e),l=!1,c=!1,d=!1,f=!1,b=!1;const h=s((e=>{a=e}));function _(e){m(e._closedPromise,(t=>(e!==i||(Ve(o._readableStreamController,t),Ve(n._readableStreamController,t),f&&b||a(void 0)),null)))}function y(){lt(i)&&(E(i),i=N(e),_(i));J(i,{_chunkSteps:t=>{p((()=>{c=!1,d=!1;const r=t;let i=t;if(!f&&!b)try{i=we(t)}catch(t){return Ve(o._readableStreamController,t),Ve(n._readableStreamController,t),void a(kr(e,t))}f||He(o._readableStreamController,r),b||He(n._readableStreamController,i),l=!1,c?g():d&&v()}))},_closeSteps:()=>{l=!1,f||Ne(o._readableStreamController),b||Ne(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&Je(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&Je(n._readableStreamController,0),f&&b||a(void 0)},_errorSteps:()=>{l=!1}})}function S(t,r){X(i)&&(E(i),i=ot(e),_(i));const s=r?n:o,u=r?o:n;st(i,t,1,{_chunkSteps:t=>{p((()=>{c=!1,d=!1;const o=r?b:f;if(r?f:b)o||Ke(s._readableStreamController,t);else{let r;try{r=we(t)}catch(t){return Ve(s._readableStreamController,t),Ve(u._readableStreamController,t),void a(kr(e,t))}o||Ke(s._readableStreamController,t),He(u._readableStreamController,r)}l=!1,c?g():d&&v()}))},_closeSteps:e=>{l=!1;const t=r?b:f,o=r?f:b;t||Ne(s._readableStreamController),o||Ne(u._readableStreamController),void 0!==e&&(t||Ke(s._readableStreamController,e),!o&&u._readableStreamController._pendingPullIntos.length>0&&Je(u._readableStreamController,0)),t&&o||a(void 0)},_errorSteps:()=>{l=!1}})}function g(){if(l)return c=!0,u(void 0);l=!0;const e=Ge(o._readableStreamController);return null===e?y():S(e._view,!1),u(void 0)}function v(){if(l)return d=!0,u(void 0);l=!0;const e=Ge(n._readableStreamController);return null===e?y():S(e._view,!0),u(void 0)}function w(o){if(f=!0,t=o,b){const o=le([t,r]),n=kr(e,o);a(n)}return h}function R(o){if(b=!0,r=o,f){const o=le([t,r]),n=kr(e,o);a(n)}return h}function T(){}return o=Wr(T,g,w),n=Wr(T,v,R),_(i),[o,n]}(e):function(e,t){const r=N(e);let o,n,a,i,l,c=!1,d=!1,f=!1,b=!1;const h=s((e=>{l=e}));function _(){if(c)return d=!0,u(void 0);c=!0;return J(r,{_chunkSteps:e=>{p((()=>{d=!1;const t=e,r=e;f||hr(a._readableStreamController,t),b||hr(i._readableStreamController,r),c=!1,d&&_()}))},_closeSteps:()=>{c=!1,f||mr(a._readableStreamController),b||mr(i._readableStreamController),f&&b||l(void 0)},_errorSteps:()=>{c=!1}}),u(void 0)}function y(t){if(f=!0,o=t,b){const t=le([o,n]),r=kr(e,t);l(r)}return h}function S(t){if(b=!0,n=t,f){const t=le([o,n]),r=kr(e,t);l(r)}return h}function g(){}return a=Er(g,_,y),i=Er(g,_,S),m(r._closedPromise,(e=>(_r(a._readableStreamController,e),_r(i._readableStreamController,e),f&&b||l(void 0),null))),[a,i]}(e)}function wr(e){return r(o=e)&&void 0!==o.getReader?function(e){let o;function n(){let t;try{t=e.read()}catch(e){return c(e)}return h(t,(e=>{if(!r(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)mr(o._readableStreamController);else{const t=e.value;hr(o._readableStreamController,t)}}))}function a(t){try{return u(e.cancel(t))}catch(e){return c(e)}}return o=Er(t,n,a,0),o}(e.getReader()):function(e){let o;const n=he(e,"async");function a(){let e;try{e=function(e){const t=y(e.nextMethod,e.iterator,[]);if(!r(t))throw new TypeError("The iterator.next() method must return an object");return t}(n)}catch(e){return c(e)}return h(u(e),(e=>{if(!r(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");const t=function(e){return Boolean(e.done)}(e);if(t)mr(o._readableStreamController);else{const t=function(e){return e.value}(e);hr(o._readableStreamController,t)}}))}function i(e){const t=n.iterator;let o,a;try{o=fe(t,"return")}catch(e){return c(e)}if(void 0===o)return u(void 0);try{a=y(o,t,[e])}catch(e){return c(e)}return h(u(a),(e=>{if(!r(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}return o=Er(t,a,i,0),o}(e);var o}function Rr(e,t,r){return I(e,r),r=>S(e,t,[r])}function Tr(e,t,r){return I(e,r),r=>S(e,t,[r])}function Cr(e,t,r){return I(e,r),r=>y(e,t,[r])}function Pr(e,t){if("bytes"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function qr(e,t){z(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableStreamDefaultController.prototype.close,"close"),n(ReadableStreamDefaultController.prototype.enqueue,"enqueue"),n(ReadableStreamDefaultController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:L(e,"First parameter");const r=bt(t,"Second parameter"),o=function(e,t){z(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:x(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:Rr(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:Tr(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:Cr(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:Pr(l,`${t} has member 'type' that`)}}(e,"First parameter");if(Br(this),"bytes"===o.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n,a,i;n=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>u(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0);const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");Ze(e,o,n,a,i,r,l)}(this,o,dt(r,0))}else{const e=ft(r);!function(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a,i,l;a=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>u(void 0),l=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0),Sr(e,n,a,i,l,r,o)}(this,o,dt(r,1),e)}}get locked(){if(!Or(this))throw zr("locked");return jr(this)}cancel(e=void 0){return Or(this)?jr(this)?c(new TypeError("Cannot cancel a stream that already has a reader")):kr(this,e):c(zr("cancel"))}getReader(e=void 0){if(!Or(this))throw zr("getReader");return void 0===function(e,t){z(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:rt(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?N(this):ot(this)}pipeThrough(e,t={}){if(!Or(this))throw zr("pipeThrough");F(e,1,"pipeThrough");const r=function(e,t){z(e,t);const r=null==e?void 0:e.readable;$(r,"readable","ReadableWritablePair"),Q(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return $(o,"writable","ReadableWritablePair"),St(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=qr(t,"Second parameter");if(jr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Tt(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return _(ur(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!Or(this))return c(zr("pipeTo"));if(void 0===e)return c("Parameter 1 is required in 'pipeTo'.");if(!Rt(e))return c(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=qr(t,"Second parameter")}catch(e){return c(e)}return jr(this)?c(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Tt(e)?c(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):ur(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!Or(this))throw zr("tee");return le(vr(this))}values(e=void 0){if(!Or(this))throw zr("values");return function(e,t){const r=N(e),o=new pe(r,t),n=Object.create(ye);return n._asyncIteratorImpl=o,n}(this,function(e,t){z(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,"First parameter").preventCancel)}[me](e){return this.values(e)}static from(e){return wr(e)}}function Er(e,t,r,o=1,n=(()=>1)){const a=Object.create(ReadableStream.prototype);Br(a);return Sr(a,Object.create(ReadableStreamDefaultController.prototype),e,t,r,o,n),a}function Wr(e,t,r){const o=Object.create(ReadableStream.prototype);Br(o);return Ze(o,Object.create(ReadableByteStreamController.prototype),e,t,r,0,void 0),o}function Br(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Or(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof ReadableStream)}function jr(e){return void 0!==e._reader}function kr(e,r){if(e._disturbed=!0,"closed"===e._state)return u(void 0);if("errored"===e._state)return c(e._storedError);Ar(e);const o=e._reader;if(void 0!==o&&lt(o)){const e=o._readIntoRequests;o._readIntoRequests=new g,e.forEach((e=>{e._closeSteps(void 0)}))}return h(e._readableStreamController[R](r),t)}function Ar(e){e._state="closed";const t=e._reader;if(void 0!==t&&(k(t),X(t))){const e=t._readRequests;t._readRequests=new g,e.forEach((e=>{e._closeSteps()}))}}function Dr(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(j(r,t),X(r)?K(r,t):ut(r,t))}function zr(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Ir(e,t){z(e,t);const r=null==e?void 0:e.highWaterMark;return $(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:M(r)}}Object.defineProperties(ReadableStream,{from:{enumerable:!0}}),Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),n(ReadableStream.from,"from"),n(ReadableStream.prototype.cancel,"cancel"),n(ReadableStream.prototype.getReader,"getReader"),n(ReadableStream.prototype.pipeThrough,"pipeThrough"),n(ReadableStream.prototype.pipeTo,"pipeTo"),n(ReadableStream.prototype.tee,"tee"),n(ReadableStream.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStream.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(ReadableStream.prototype,me,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const Lr=e=>e.byteLength;n(Lr,"size");class ByteLengthQueuingStrategy{constructor(e){F(e,1,"ByteLengthQueuingStrategy"),e=Ir(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!$r(this))throw Fr("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!$r(this))throw Fr("size");return Lr}}function Fr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function $r(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const Mr=()=>1;n(Mr,"size");class CountQueuingStrategy{constructor(e){F(e,1,"CountQueuingStrategy"),e=Ir(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!xr(this))throw Yr("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!xr(this))throw Yr("size");return Mr}}function Yr(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function xr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof CountQueuingStrategy)}function Qr(e,t,r){return I(e,r),r=>S(e,t,[r])}function Nr(e,t,r){return I(e,r),r=>y(e,t,[r])}function Hr(e,t,r){return I(e,r),(r,o)=>S(e,t,[r,o])}function Vr(e,t,r){return I(e,r),r=>S(e,t,[r])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=bt(t,"Second parameter"),n=bt(r,"Third parameter"),a=function(e,t){z(e,t);const r=null==e?void 0:e.cancel,o=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,a=null==e?void 0:e.start,i=null==e?void 0:e.transform,l=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:Vr(r,e,`${t} has member 'cancel' that`),flush:void 0===o?void 0:Qr(o,e,`${t} has member 'flush' that`),readableType:n,start:void 0===a?void 0:Nr(a,e,`${t} has member 'start' that`),transform:void 0===i?void 0:Hr(i,e,`${t} has member 'transform' that`),writableType:l}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");const i=dt(n,0),l=ft(n),d=dt(o,1),b=ft(o);let m;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return h(e._backpressureChangePromise,(()=>{const o=e._writable;if("erroring"===o._state)throw o._storedError;return ro(r,t)}))}return ro(r,t)}(e,t)}function u(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._readable;r._finishPromise=s(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return eo(r),f(n,(()=>("errored"===o._state?ao(r,o._storedError):(_r(o._readableStreamController,t),no(r)),null)),(e=>(_r(o._readableStreamController,e),ao(r,e),null))),r._finishPromise}(e,t)}function c(){return function(e){const t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;const r=e._readable;t._finishPromise=s(((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r}));const o=t._flushAlgorithm();return eo(t),f(o,(()=>("errored"===r._state?ao(t,r._storedError):(mr(r._readableStreamController),no(t)),null)),(e=>(_r(r._readableStreamController,e),ao(t,e),null))),t._finishPromise}(e)}function d(){return function(e){return Kr(e,!1),e._backpressureChangePromise}(e)}function b(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._writable;r._finishPromise=s(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return eo(r),f(n,(()=>("errored"===o._state?ao(r,o._storedError):(Nt(o._writableStreamController,t),Jr(e),no(r)),null)),(t=>(Nt(o._writableStreamController,t),Jr(e),ao(r,t),null))),r._finishPromise}(e,t)}e._writable=function(e,t,r,o,n=1,a=(()=>1)){const i=Object.create(WritableStream.prototype);return wt(i),Mt(i,Object.create(WritableStreamDefaultController.prototype),e,t,r,o,n,a),i}(i,l,c,u,r,o),e._readable=Er(i,d,b,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Kr(e,!0),e._transformStreamController=void 0}(this,s((e=>{m=e})),d,b,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o,n,a;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return to(r,e),u(void 0)}catch(e){return c(e)}};n=void 0!==t.flush?()=>t.flush(r):()=>u(void 0);a=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0);!function(e,t,r,o,n){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o,t._cancelAlgorithm=n,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}(e,r,o,n,a)}(this,a),void 0!==a.start?m(a.start(this._transformStreamController)):m(void 0)}get readable(){if(!Ur(this))throw io("readable");return this._readable}get writable(){if(!Ur(this))throw io("writable");return this._writable}}function Ur(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof TransformStream)}function Gr(e,t){_r(e._readable._readableStreamController,t),Xr(e,t)}function Xr(e,t){eo(e._transformStreamController),Nt(e._writable._writableStreamController,t),Jr(e)}function Jr(e){e._backpressure&&Kr(e,!1)}function Kr(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=s((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStream.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Zr(this))throw oo("desiredSize");return pr(this._controlledTransformStream._readable._readableStreamController)}enqueue(e=void 0){if(!Zr(this))throw oo("enqueue");to(this,e)}error(e=void 0){if(!Zr(this))throw oo("error");var t;t=e,Gr(this._controlledTransformStream,t)}terminate(){if(!Zr(this))throw oo("terminate");!function(e){const t=e._controlledTransformStream;mr(t._readable._readableStreamController);const r=new TypeError("TransformStream terminated");Xr(t,r)}(this)}}function Zr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof TransformStreamDefaultController)}function eo(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function to(e,t){const r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!yr(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{hr(o,t)}catch(e){throw Xr(r,e),r._readable._storedError}const n=function(e){return!fr(e)}(o);n!==r._backpressure&&Kr(r,!0)}function ro(e,t){return h(e._transformAlgorithm(t),void 0,(t=>{throw Gr(e._controlledTransformStream,t),t}))}function oo(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function no(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function ao(e,t){void 0!==e._finishPromise_reject&&(_(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function io(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),n(TransformStreamDefaultController.prototype.enqueue,"enqueue"),n(TransformStreamDefaultController.prototype.error,"error"),n(TransformStreamDefaultController.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});const lo={ReadableStream:ReadableStream,ReadableStreamDefaultController:ReadableStreamDefaultController,ReadableByteStreamController:ReadableByteStreamController,ReadableStreamBYOBRequest:ReadableStreamBYOBRequest,ReadableStreamDefaultReader:ReadableStreamDefaultReader,ReadableStreamBYOBReader:ReadableStreamBYOBReader,WritableStream:WritableStream,WritableStreamDefaultController:WritableStreamDefaultController,WritableStreamDefaultWriter:WritableStreamDefaultWriter,ByteLengthQueuingStrategy:ByteLengthQueuingStrategy,CountQueuingStrategy:CountQueuingStrategy,TransformStream:TransformStream,TransformStreamDefaultController:TransformStreamDefaultController};if(void 0!==lr)for(const e in lo)Object.prototype.hasOwnProperty.call(lo,e)&&Object.defineProperty(lr,e,{value:lo[e],writable:!0,configurable:!0});e.ByteLengthQueuingStrategy=ByteLengthQueuingStrategy,e.CountQueuingStrategy=CountQueuingStrategy,e.ReadableByteStreamController=ReadableByteStreamController,e.ReadableStream=ReadableStream,e.ReadableStreamBYOBReader=ReadableStreamBYOBReader,e.ReadableStreamBYOBRequest=ReadableStreamBYOBRequest,e.ReadableStreamDefaultController=ReadableStreamDefaultController,e.ReadableStreamDefaultReader=ReadableStreamDefaultReader,e.TransformStream=TransformStream,e.TransformStreamDefaultController=TransformStreamDefaultController,e.WritableStream=WritableStream,e.WritableStreamDefaultController=WritableStreamDefaultController,e.WritableStreamDefaultWriter=WritableStreamDefaultWriter}));
//# sourceMappingURL=polyfill.es6.min.js.map
