#!/usr/bin/env node

/**
 * 修复重复导入的 Element Plus 图标脚本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 修复文件中的重复导入
function fixDuplicateImports(filePath) {
  if (!fs.existsSync(filePath)) {
    return { fixed: false, changes: [] }
  }

  let content = fs.readFileSync(filePath, 'utf8')
  let originalContent = content
  const changes = []

  // 匹配 Element Plus 图标导入语句
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@element-plus\/icons-vue['"]/g
  let match

  while ((match = importRegex.exec(content)) !== null) {
    const fullImport = match[0]
    const iconList = match[1]
    
    // 解析图标列表
    const icons = iconList
      .split(',')
      .map(icon => icon.trim())
      .filter(icon => icon && icon.length > 0)
      .map(icon => {
        // 处理 "Icon as Alias" 的情况
        if (icon.includes(' as ')) {
          return icon.trim()
        }
        return icon.trim()
      })

    // 去除重复的图标
    const uniqueIcons = []
    const seen = new Set()
    
    icons.forEach(icon => {
      // 提取图标名称（忽略 as 别名）
      const iconName = icon.includes(' as ') ? icon.split(' as ')[0].trim() : icon
      
      if (!seen.has(iconName)) {
        seen.add(iconName)
        uniqueIcons.push(icon)
      } else {
        changes.push({
          type: 'duplicate_removed',
          icon: iconName,
          full: icon
        })
      }
    })

    // 如果有重复，重新构建导入语句
    if (uniqueIcons.length !== icons.length) {
      const newImport = `import {\n  ${uniqueIcons.join(', ')}\n} from '@element-plus/icons-vue'`
      content = content.replace(fullImport, newImport)
    }
  }

  // 如果有变化，写入文件
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    return { fixed: true, changes }
  }

  return { fixed: false, changes: [] }
}

// 递归扫描目录
function scanAndFixDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const result = fixDuplicateImports(fullPath)
        if (result.fixed) {
          results.push({
            file: path.relative(dir, fullPath),
            changes: result.changes
          })
        }
      }
    })
  }
  
  scan(dir)
  return results
}

// 主函数
function main() {
  console.log('🔧 开始修复重复导入...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanAndFixDirectory(srcDir)
  
  console.log('📋 修复结果:')
  
  if (results.length === 0) {
    console.log('✅ 未发现重复导入问题！')
  } else {
    console.log(`🔧 修复了 ${results.length} 个文件:\n`)
    
    results.forEach(({ file, changes }) => {
      console.log(`📄 ${file}:`)
      changes.forEach(change => {
        console.log(`   移除重复: ${change.icon}`)
      })
      console.log()
    })
  }
  
  console.log('\n📊 修复总结:')
  
  if (results.length === 0) {
    console.log('🎉 所有导入都正常！')
  } else {
    console.log(`🔧 成功修复 ${results.length} 个文件的重复导入`)
    console.log('✅ 所有重复的图标导入已清理')
    console.log('✅ 应用现在可以正常编译')
  }
}

// 运行修复
main()
