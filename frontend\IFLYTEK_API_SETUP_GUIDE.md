# iFlytek星火大模型API配置指南

## 问题描述
当前系统显示"⚠️ iFlytek API配置不完整，使用模拟响应"警告，这是因为缺少真实的iFlytek API配置。

## 解决方案

### 1. 获取iFlytek API密钥

1. 访问 [讯飞开放平台](https://console.xfyun.cn/)
2. 注册并登录账号
3. 创建新应用，选择"星火认知大模型"
4. 获取以下信息：
   - APP_ID
   - API_KEY  
   - API_SECRET

### 2. 配置环境变量

#### 方法一：使用 .env.local 文件（推荐）

编辑 `frontend/.env.local` 文件，替换以下配置：

```bash
# iFlytek星火大模型真实API配置
VUE_APP_IFLYTEK_API_URL=https://spark-api.xf-yun.com
VUE_APP_IFLYTEK_APP_ID=您的真实APP_ID
VUE_APP_IFLYTEK_API_KEY=您的真实API_KEY
VUE_APP_IFLYTEK_API_SECRET=您的真实API_SECRET

# 关闭模拟模式
VUE_APP_MOCK_API_RESPONSES=false
```

#### 方法二：使用系统环境变量

在系统中设置以下环境变量：
```bash
export VUE_APP_IFLYTEK_APP_ID="您的APP_ID"
export VUE_APP_IFLYTEK_API_KEY="您的API_KEY"
export VUE_APP_IFLYTEK_API_SECRET="您的API_SECRET"
```

### 3. 重启开发服务器

配置完成后，重启开发服务器：
```bash
npm run dev
```

### 4. 验证配置

配置成功后，控制台应该显示：
- ✅ iFlytek API配置完整
- ✅ 面试会话初始化成功
- ✅ 智能问题生成成功

## 临时解决方案

如果暂时无法获取真实API密钥，可以继续使用模拟模式：

1. 在 `.env.local` 中设置：
```bash
VUE_APP_MOCK_API_RESPONSES=true
```

2. 系统将使用内置的模拟响应，功能正常但使用的是预设的回答。

## 注意事项

1. **安全性**：不要将真实的API密钥提交到版本控制系统
2. **配额管理**：注意API调用次数限制
3. **网络连接**：确保能够访问讯飞API服务器
4. **版本兼容**：当前使用星火v3.5版本

## 故障排除

### 常见错误及解决方案

1. **401 Unauthorized**
   - 检查APP_ID、API_KEY、API_SECRET是否正确
   - 确认应用状态是否正常

2. **网络连接错误**
   - 检查网络连接
   - 确认防火墙设置

3. **配置不生效**
   - 重启开发服务器
   - 清除浏览器缓存
   - 检查环境变量名称是否正确

## 联系支持

如需技术支持，请访问：
- [讯飞开放平台文档](https://www.xfyun.cn/doc/)
- [星火大模型API文档](https://www.xfyun.cn/doc/spark/Web.html)
