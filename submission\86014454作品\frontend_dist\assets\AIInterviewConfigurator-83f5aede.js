import{_ as Q,r as g,a2 as W,o as X,i as a,j as n,k as c,n as l,p as t,w as i,M as m,q as ee,a3 as x,a4 as C,F as I,x as U,z as p,B as w,y as N,Y as le,Z as te,N as b,f as se,R as F,C as q,a5 as ie,G as oe,a6 as ae,a7 as ne,P as de}from"./index-b6a2842e.js";const ue={class:"ai-interview-configurator"},re={class:"configurator-header"},me={class:"header-content"},ce={class:"header-info"},pe={class:"configurator-title"},ve={class:"configuration-steps"},_e={class:"configuration-content"},fe={class:"config-panel"},ge={class:"config-grid"},be={class:"config-section"},ye={class:"config-section"},Ve={class:"difficulty-config"},we={class:"difficulty-item"},ke={class:"difficulty-item"},he={class:"difficulty-item"},xe={class:"config-panel"},Ce={class:"ai-capabilities-grid"},Ie={class:"capability-header"},Ue={class:"capability-info"},Ae={class:"capability-title"},Se={class:"capability-description"},ze={key:0,class:"capability-settings"},Ne={class:"setting-label"},De={class:"model-tuning-section"},Me={class:"section-title"},Be={class:"tuning-grid"},Fe={class:"tuning-item"},qe={class:"tuning-item"},Te={class:"tuning-item"},je={class:"config-panel"},Ee={class:"evaluation-config"},Ge={class:"weight-config"},Le={class:"weight-items"},Oe={class:"dimension-info"},Pe={class:"dimension-details"},Re={class:"dimension-name"},Ye={class:"dimension-desc"},Ze={class:"weight-control"},$e={class:"scoring-preview"},He={class:"preview-chart"},Je={class:"config-panel"},Ke={class:"preview-content"},Qe={class:"config-summary"},We={class:"summary-section"},Xe={class:"summary-items"},el={class:"summary-item"},ll={class:"item-value"},tl={class:"summary-item"},sl={class:"item-value"},il={class:"summary-item"},ol={class:"item-value"},al={class:"summary-section"},nl={class:"enabled-capabilities"},dl={class:"preview-actions"},ul={class:"configurator-footer"},rl={class:"footer-actions"},ml={__name:"AIInterviewConfigurator",setup(cl){const u=g(0),o=g({position:"AI算法工程师",domain:"ai",experience:"middle",duration:30,questionCount:10,difficulty:{basic:3,advanced:5,expert:2}}),D=g([{id:1,title:"智能语音分析",description:"基于iFlytek语音识别技术，分析语音质量、语速、停顿等",icon:"Microphone",gradient:"linear-gradient(135deg, #1890ff 0%, #0066cc 100%)",color:"#1890ff",enabled:!0,settings:[{key:"sensitivity",label:"识别敏感度",value:80,min:0,max:100,step:10},{key:"analysis_depth",label:"分析深度",value:70,min:0,max:100,step:10}]},{id:2,title:"实时情感识别",description:"通过语音语调分析候选人的情感状态和紧张程度",icon:"Cpu",gradient:"linear-gradient(135deg, #52c41a 0%, #389e0d 100%)",color:"#52c41a",enabled:!0,settings:[{key:"emotion_weight",label:"情感权重",value:60,min:0,max:100,step:10}]},{id:3,title:"智能内容分析",description:"基于Spark大模型分析回答内容的逻辑性和专业性",icon:"Grid",gradient:"linear-gradient(135deg, #722ed1 0%, #531dab 100%)",color:"#722ed1",enabled:!0,settings:[{key:"content_depth",label:"内容深度分析",value:90,min:0,max:100,step:10},{key:"logic_weight",label:"逻辑性权重",value:85,min:0,max:100,step:10}]}]),_=g({creativity:.7,depth:.8,friendliness:.6}),M=g([{key:"professional",name:"专业知识",description:"技术能力和专业素养",weight:30,color:"#1890ff",icon:"Star"},{key:"communication",name:"沟通表达",description:"语言表达和沟通能力",weight:25,color:"#52c41a",icon:"Microphone"},{key:"logic",name:"逻辑思维",description:"问题分析和逻辑推理",weight:25,color:"#722ed1",icon:"Cpu"},{key:"innovation",name:"创新能力",description:"创新思维和解决方案",weight:20,color:"#fa8c16",icon:"TrendCharts"}]),T=W(()=>D.value.filter(v=>v.enabled)),j=()=>{u.value<3&&u.value++},E=()=>{u.value>0&&u.value--},G=v=>({ai:"人工智能",bigdata:"大数据",iot:"物联网"})[v]||v,L=()=>{console.log("保存草稿")},O=()=>{console.log("发布面试")},A=g(null),P=()=>{le(()=>{if(A.value){const v=te(A.value),e={tooltip:{trigger:"item"},series:[{type:"pie",radius:["40%","70%"],data:M.value.map(d=>({value:d.weight,name:d.name,itemStyle:{color:d.color}})),label:{show:!0,formatter:"{b}: {c}%"}}]};v.setOption(e)}})};return X(()=>{P()}),(v,e)=>{const d=a("el-icon"),k=a("el-step"),R=a("el-steps"),Y=a("el-input"),f=a("el-form-item"),S=a("el-option"),Z=a("el-select"),z=a("el-radio"),$=a("el-radio-group"),B=a("el-form"),y=a("el-slider"),V=a("el-input-number"),H=a("el-switch"),J=a("el-tag"),h=a("el-button");return n(),c("div",ue,[l("div",re,[l("div",me,[l("div",ce,[l("h1",pe,[t(d,null,{default:i(()=>[t(b(se))]),_:1}),e[11]||(e[11]=m(" iFlytek Spark AI面试配置 "))]),e[12]||(e[12]=l("p",{class:"configurator-subtitle"},"基于讯飞星火大模型，打造个性化智能面试体验",-1))]),e[13]||(e[13]=ee('<div class="ai-status" data-v-96fe2657><div class="status-indicator online" data-v-96fe2657><div class="status-dot" data-v-96fe2657></div><span data-v-96fe2657>AI引擎在线</span></div><div class="model-info" data-v-96fe2657><span class="model-name" data-v-96fe2657>Spark V3.5</span><span class="model-version" data-v-96fe2657>最新版本</span></div></div>',1))])]),l("div",ve,[t(R,{active:u.value,"align-center":"",class:"config-steps"},{default:i(()=>[t(k,{title:"基础设置",description:"职位信息与面试类型"}),t(k,{title:"AI能力配置",description:"多模态分析设置"}),t(k,{title:"评估标准",description:"评分权重与标准"}),t(k,{title:"预览确认",description:"配置预览与发布"})]),_:1},8,["active"])]),l("div",_e,[x(l("div",fe,[e[22]||(e[22]=l("div",{class:"panel-header"},[l("h3",{class:"panel-title"},"基础面试设置"),l("p",{class:"panel-subtitle"},"配置职位信息和面试基本参数")],-1)),l("div",ge,[l("div",be,[e[17]||(e[17]=l("h4",{class:"section-title"},"职位信息",-1)),t(B,{model:o.value,"label-width":"120px",class:"config-form"},{default:i(()=>[t(f,{label:"职位名称"},{default:i(()=>[t(Y,{modelValue:o.value.position,"onUpdate:modelValue":e[0]||(e[0]=s=>o.value.position=s),placeholder:"请输入职位名称"},null,8,["modelValue"])]),_:1}),t(f,{label:"技术领域"},{default:i(()=>[t(Z,{modelValue:o.value.domain,"onUpdate:modelValue":e[1]||(e[1]=s=>o.value.domain=s),placeholder:"选择技术领域"},{default:i(()=>[t(S,{label:"人工智能",value:"ai"}),t(S,{label:"大数据",value:"bigdata"}),t(S,{label:"物联网",value:"iot"})]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"经验要求"},{default:i(()=>[t($,{modelValue:o.value.experience,"onUpdate:modelValue":e[2]||(e[2]=s=>o.value.experience=s)},{default:i(()=>[t(z,{label:"junior"},{default:i(()=>e[14]||(e[14]=[m("初级 (0-2年)")])),_:1,__:[14]}),t(z,{label:"middle"},{default:i(()=>e[15]||(e[15]=[m("中级 (3-5年)")])),_:1,__:[15]}),t(z,{label:"senior"},{default:i(()=>e[16]||(e[16]=[m("高级 (5年以上)")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),l("div",ye,[e[21]||(e[21]=l("h4",{class:"section-title"},"面试参数",-1)),t(B,{model:o.value,"label-width":"120px",class:"config-form"},{default:i(()=>[t(f,{label:"面试时长"},{default:i(()=>[t(y,{modelValue:o.value.duration,"onUpdate:modelValue":e[3]||(e[3]=s=>o.value.duration=s),min:15,max:60,step:5,"show-input":"","format-tooltip":s=>`${s}分钟`},null,8,["modelValue","format-tooltip"])]),_:1}),t(f,{label:"问题数量"},{default:i(()=>[t(V,{modelValue:o.value.questionCount,"onUpdate:modelValue":e[4]||(e[4]=s=>o.value.questionCount=s),min:5,max:20},null,8,["modelValue"])]),_:1}),t(f,{label:"难度分布"},{default:i(()=>[l("div",Ve,[l("div",we,[e[18]||(e[18]=l("span",null,"基础题",-1)),t(V,{modelValue:o.value.difficulty.basic,"onUpdate:modelValue":e[5]||(e[5]=s=>o.value.difficulty.basic=s),min:0,max:10,size:"small"},null,8,["modelValue"])]),l("div",ke,[e[19]||(e[19]=l("span",null,"进阶题",-1)),t(V,{modelValue:o.value.difficulty.advanced,"onUpdate:modelValue":e[6]||(e[6]=s=>o.value.difficulty.advanced=s),min:0,max:10,size:"small"},null,8,["modelValue"])]),l("div",he,[e[20]||(e[20]=l("span",null,"挑战题",-1)),t(V,{modelValue:o.value.difficulty.expert,"onUpdate:modelValue":e[7]||(e[7]=s=>o.value.difficulty.expert=s),min:0,max:10,size:"small"},null,8,["modelValue"])])])]),_:1})]),_:1},8,["model"])])])],512),[[C,u.value===0]]),x(l("div",xe,[e[30]||(e[30]=l("div",{class:"panel-header"},[l("h3",{class:"panel-title"},"iFlytek Spark AI能力配置"),l("p",{class:"panel-subtitle"},"配置多模态分析能力和AI交互参数")],-1)),l("div",Ce,[(n(!0),c(I,null,U(D.value,s=>(n(),c("div",{class:"capability-card",key:s.id},[l("div",Ie,[l("div",{class:"capability-icon",style:F({background:s.gradient})},[t(d,{size:24},{default:i(()=>[(n(),w(q(s.icon)))]),_:2},1024)],4),l("div",Ue,[l("h4",Ae,p(s.title),1),l("p",Se,p(s.description),1)]),t(H,{modelValue:s.enabled,"onUpdate:modelValue":r=>s.enabled=r,size:"large","active-color":s.color},null,8,["modelValue","onUpdate:modelValue","active-color"])]),s.enabled?(n(),c("div",ze,[(n(!0),c(I,null,U(s.settings,r=>(n(),c("div",{class:"setting-item",key:r.key},[l("label",Ne,p(r.label),1),t(y,{modelValue:r.value,"onUpdate:modelValue":K=>r.value=K,min:r.min,max:r.max,step:r.step,"show-input":"",size:"small"},null,8,["modelValue","onUpdate:modelValue","min","max","step"])]))),128))])):N("",!0)]))),128))]),l("div",De,[l("h4",Me,[t(d,null,{default:i(()=>[t(b(ie))]),_:1}),e[23]||(e[23]=m(" Spark模型参数调优 "))]),l("div",Be,[l("div",Fe,[e[24]||(e[24]=l("label",null,"创造性水平",-1)),t(y,{modelValue:_.value.creativity,"onUpdate:modelValue":e[8]||(e[8]=s=>_.value.creativity=s),min:0,max:1,step:.1,"show-input":""},null,8,["modelValue"]),e[25]||(e[25]=l("span",{class:"tuning-hint"},"控制AI回答的创新程度",-1))]),l("div",qe,[e[26]||(e[26]=l("label",null,"专业深度",-1)),t(y,{modelValue:_.value.depth,"onUpdate:modelValue":e[9]||(e[9]=s=>_.value.depth=s),min:0,max:1,step:.1,"show-input":""},null,8,["modelValue"]),e[27]||(e[27]=l("span",{class:"tuning-hint"},"调整技术问题的深度",-1))]),l("div",Te,[e[28]||(e[28]=l("label",null,"交互友好度",-1)),t(y,{modelValue:_.value.friendliness,"onUpdate:modelValue":e[10]||(e[10]=s=>_.value.friendliness=s),min:0,max:1,step:.1,"show-input":""},null,8,["modelValue"]),e[29]||(e[29]=l("span",{class:"tuning-hint"},"设置AI面试官的亲和力",-1))])])])],512),[[C,u.value===1]]),x(l("div",je,[e[34]||(e[34]=l("div",{class:"panel-header"},[l("h3",{class:"panel-title"},"智能评估标准配置"),l("p",{class:"panel-subtitle"},"设置多维度评估权重和评分标准")],-1)),l("div",Ee,[l("div",Ge,[e[32]||(e[32]=l("h4",{class:"section-title"},"评估维度权重",-1)),l("div",Le,[(n(!0),c(I,null,U(M.value,s=>(n(),c("div",{class:"weight-item",key:s.key},[l("div",Oe,[l("div",{class:"dimension-icon",style:F({background:s.color})},[t(d,null,{default:i(()=>[(n(),w(q(s.icon)))]),_:2},1024)],4),l("div",Pe,[l("span",Re,p(s.name),1),l("span",Ye,p(s.description),1)])]),l("div",Ze,[t(V,{modelValue:s.weight,"onUpdate:modelValue":r=>s.weight=r,min:0,max:100,step:5,size:"small"},null,8,["modelValue","onUpdate:modelValue"]),e[31]||(e[31]=l("span",{class:"weight-unit"},"%",-1))])]))),128))])]),l("div",$e,[e[33]||(e[33]=l("h4",{class:"section-title"},"评分预览",-1)),l("div",He,[l("div",{ref_key:"evaluationChart",ref:A,class:"chart-container"},null,512)])])])],512),[[C,u.value===2]]),x(l("div",Je,[e[42]||(e[42]=l("div",{class:"panel-header"},[l("h3",{class:"panel-title"},"配置预览与确认"),l("p",{class:"panel-subtitle"},"检查配置信息并发布AI面试")],-1)),l("div",Ke,[l("div",Qe,[l("div",We,[e[38]||(e[38]=l("h4",null,"基础信息",-1)),l("div",Xe,[l("div",el,[e[35]||(e[35]=l("span",{class:"item-label"},"职位名称:",-1)),l("span",ll,p(o.value.position),1)]),l("div",tl,[e[36]||(e[36]=l("span",{class:"item-label"},"技术领域:",-1)),l("span",sl,p(G(o.value.domain)),1)]),l("div",il,[e[37]||(e[37]=l("span",{class:"item-label"},"面试时长:",-1)),l("span",ol,p(o.value.duration)+"分钟",1)])])]),l("div",al,[e[39]||(e[39]=l("h4",null,"AI能力配置",-1)),l("div",nl,[(n(!0),c(I,null,U(T.value,s=>(n(),w(J,{key:s.id,color:s.color,class:"capability-tag"},{default:i(()=>[m(p(s.title),1)]),_:2},1032,["color"]))),128))])])]),l("div",dl,[t(h,{size:"large",onClick:L},{default:i(()=>[t(d,null,{default:i(()=>[t(b(oe))]),_:1}),e[40]||(e[40]=m(" 保存草稿 "))]),_:1,__:[40]}),t(h,{type:"primary",size:"large",onClick:O},{default:i(()=>[t(d,null,{default:i(()=>[t(b(ae))]),_:1}),e[41]||(e[41]=m(" 发布面试 "))]),_:1,__:[41]})])])],512),[[C,u.value===3]])]),l("div",ul,[l("div",rl,[u.value>0?(n(),w(h,{key:0,onClick:E},{default:i(()=>[t(d,null,{default:i(()=>[t(b(ne))]),_:1}),e[43]||(e[43]=m(" 上一步 "))]),_:1,__:[43]})):N("",!0),u.value<3?(n(),w(h,{key:1,type:"primary",onClick:j},{default:i(()=>[e[44]||(e[44]=m(" 下一步 ")),t(d,null,{default:i(()=>[t(b(de))]),_:1})]),_:1,__:[44]})):N("",!0)])])])}}},vl=Q(ml,[["__scopeId","data-v-96fe2657"]]);export{vl as default};
