#!/usr/bin/env python3
"""
API端点测试脚本
测试多模态面试评估系统的所有关键API端点
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_api_endpoint(method, endpoint, data=None, description=""):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    print(f"\n🔍 测试: {description}")
    print(f"📍 {method} {endpoint}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return False
            
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            # 只显示响应的前200个字符
            response_text = response.text
            if len(response_text) > 200:
                response_text = response_text[:200] + "..."
            print(f"📄 响应: {response_text}")
            return True
        else:
            print(f"❌ 失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 多模态面试评估系统 API 测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 基础健康检查
    test_results.append(test_api_endpoint(
        "GET", "/health", 
        description="健康检查"
    ))
    
    # 2. 获取技术领域
    test_results.append(test_api_endpoint(
        "GET", "/api/v1/domains", 
        description="获取技术领域列表"
    ))
    
    # 3. 获取岗位列表
    test_results.append(test_api_endpoint(
        "GET", "/api/v1/domains/人工智能/positions", 
        description="获取人工智能领域岗位列表"
    ))
    
    # 4. 获取面试题目
    test_results.append(test_api_endpoint(
        "POST", "/api/v1/questions",
        data={"domain": "人工智能", "position": "技术岗"},
        description="获取面试题目"
    ))
    
    # 5. 高级AI面试官开始
    test_results.append(test_api_endpoint(
        "POST", "/api/v1/interview/advanced-start",
        data={"domain": "人工智能", "position": "运维测试岗"},
        description="高级AI面试官开始面试"
    ))
    
    # 6. 高级AI面试官智能引导
    test_results.append(test_api_endpoint(
        "POST", "/api/v1/interview/advanced-next",
        data={
            "messages": [
                {"role": "assistant", "content": "在Docker环境中部署实时推理模型时，你会关注哪些版本控制指标？"},
                {"role": "user", "content": "不知道，请给出正确答案"}
            ],
            "domain": "人工智能",
            "position": "运维测试岗"
        },
        description="高级AI面试官智能引导"
    ))
    
    # 7. 增强版面试开始
    test_results.append(test_api_endpoint(
        "POST", "/api/v1/interview/enhanced-start",
        data={"domain": "人工智能", "position": "技术岗"},
        description="增强版面试开始"
    ))
    
    # 8. 增强版面试继续
    test_results.append(test_api_endpoint(
        "POST", "/api/v1/interview/enhanced-next",
        data={
            "messages": [
                {"role": "assistant", "content": "请描述一个您解决过的复杂技术问题"},
                {"role": "user", "content": "我不知道"}
            ],
            "domain": "人工智能",
            "position": "技术岗"
        },
        description="增强版面试继续"
    ))
    
    # 9. 基础面试开始
    test_results.append(test_api_endpoint(
        "POST", "/api/v1/interview/start",
        data={"domain": "人工智能", "position": "技术岗"},
        description="基础面试开始"
    ))
    
    # 10. 基础面试继续
    test_results.append(test_api_endpoint(
        "POST", "/api/v1/interview/next",
        data={
            "messages": [
                {"role": "assistant", "content": "请介绍一下您的技术背景"},
                {"role": "user", "content": "我有3年的Python开发经验"}
            ]
        },
        description="基础面试继续"
    ))
    
    # 测试结果统计
    print("\n" + "=" * 50)
    print("📊 测试结果统计")
    print("=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    print(f"📈 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有API端点测试通过！系统运行正常！")
        return 0
    else:
        print(f"\n⚠️  有 {total - passed} 个API端点测试失败，请检查系统状态")
        return 1

if __name__ == "__main__":
    sys.exit(main())
