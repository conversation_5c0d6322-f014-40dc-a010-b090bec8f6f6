/**
 * 布局显示修复 - iFlytek 多模态面试评估系统
 * 解决白屏、组件重叠、响应式设计等布局问题
 */

/* ===== 全局布局修复 ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: #ffffff;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== 防止白屏问题 ===== */
.enterprise-homepage {
  min-height: 100vh;
  background: #f8fafc;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease-in-out;
}

.enterprise-homepage.loading {
  opacity: 0;
}

/* ===== 容器布局修复 ===== */
.container,
.section-container,
.hero-container,
.header-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding-left: 24px;
  padding-right: 24px;
}

/* ===== 网格布局修复 ===== */
.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  min-height: 400px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  margin-top: 48px;
}

.tech-advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-top: 48px;
}

/* ===== Flexbox 布局修复 ===== */
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  flex-wrap: nowrap;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.hero-actions {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-top: 32px;
}

.hero-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

/* ===== 卡片布局修复 ===== */
.feature-card,
.product-card,
.tech-card {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card:hover,
.product-card:hover,
.tech-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* ===== 浮动元素修复 ===== */
.floating-cards {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.floating-cards .feature-card {
  position: absolute;
  width: auto;
  min-width: 200px;
  max-width: 280px;
}

.ai-card {
  top: 50px;
  right: 100px;
  animation: float 6s ease-in-out infinite;
  animation-delay: 0s;
}

.voice-card {
  top: 180px;
  right: 50px;
  animation: float 6s ease-in-out infinite;
  animation-delay: 2s;
}

.data-card {
  top: 300px;
  right: 120px;
  animation: float 6s ease-in-out infinite;
  animation-delay: 4s;
}

/* ===== Z-index 层级管理 ===== */
.enterprise-header {
  z-index: 1000;
}

.hero-section {
  z-index: 1;
}

.hero-section::before {
  z-index: 2;
}

.hero-container {
  z-index: 3;
}

.floating-cards {
  z-index: 2;
}

.feature-card {
  z-index: 3;
}

/* ===== 响应式断点修复 ===== */
@media (max-width: 1200px) {
  .container,
  .section-container,
  .hero-container,
  .header-container {
    max-width: 1140px;
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .hero-content {
    gap: 60px;
  }
  
  .floating-cards .feature-card {
    position: static;
    margin-bottom: 16px;
  }
  
  .floating-cards {
    height: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
  }
  
  .tech-advantages-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .container,
  .section-container,
  .hero-container,
  .header-container {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .header-container {
    height: 64px;
    flex-wrap: wrap;
  }
  
  .nav-menu {
    order: 3;
    width: 100%;
    margin-top: 8px;
  }
  
  .hero-section {
    padding: 60px 0 80px;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
  
  .stat-item {
    width: 100%;
    max-width: 200px;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  
  .primary-cta,
  .secondary-cta {
    width: 100%;
    max-width: 280px;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .tech-advantages-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .container,
  .section-container,
  .hero-container,
  .header-container {
    padding-left: 12px;
    padding-right: 12px;
  }
  
  .feature-card,
  .product-card,
  .tech-card {
    padding: 20px;
  }
  
  .hero-section {
    padding: 40px 0 60px;
  }
  
  .hero-stats {
    gap: 12px;
  }
  
  .hero-actions {
    gap: 12px;
  }
}

/* ===== 动画性能优化 ===== */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateZ(0);
  }
  50% {
    transform: translateY(-10px) translateZ(0);
  }
}

.feature-card {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== 滚动优化 ===== */
.enterprise-homepage {
  scroll-behavior: smooth;
}

/* ===== 防止内容溢出 ===== */
.hero-title,
.hero-subtitle,
.section-title,
.section-subtitle {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* ===== 图片响应式 ===== */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* ===== 表格响应式 ===== */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* ===== 可访问性增强 ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== 焦点状态 ===== */
button:focus,
a:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* ===== 减少动画（用户偏好） ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== 全局布局问题修复 ===== */

/* 修复面试页面特定布局问题 */
.modern-interview-page {
  overflow-x: hidden !important;
}

.interview-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
  width: 100% !important;
}

.interview-main {
  min-height: 0 !important;
  flex: 1 !important;
}

/* 修复报告页面特定布局问题 */
.modern-report-page {
  overflow-x: hidden !important;
}

.candidate-card {
  overflow: hidden !important;
}

.score-circle {
  flex-shrink: 0 !important;
}

/* 修复企业管理页面特定布局问题 */
.enterprise-dashboard {
  overflow-x: hidden !important;
}

.enterprise-header {
  width: 100% !important;
}

/* 修复候选人门户特定布局问题 */
.candidate-portal {
  overflow-x: hidden !important;
}

.candidate-header {
  width: 100% !important;
}

/* 通用网格布局修复 */
.stats-grid,
.module-grid,
.metrics-grid,
.insights-grid {
  width: 100% !important;
  overflow: hidden !important;
}

/* 通用卡片布局修复 */
.stat-card,
.module-card,
.metric-card,
.insight-card {
  overflow: hidden !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* 通用按钮布局修复 */
.el-button {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 通用文字容器修复 */
.section-title,
.section-subtitle,
.card-title,
.card-description {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
}

/* 修复图标与文字对齐问题 */
.el-icon + span,
.el-icon + p {
  vertical-align: middle !important;
  margin-left: 8px !important;
}
