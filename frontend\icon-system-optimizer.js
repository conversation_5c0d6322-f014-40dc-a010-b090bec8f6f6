/**
 * iFlytek 星火大模型智能面试系统 - 图标系统优化工具
 * Icon System Optimizer for iFlytek Spark Interview System
 * 
 * 功能：
 * 1. 批量替换无效图标
 * 2. 优化图标语义匹配
 * 3. 统一图标尺寸和样式
 * 4. 生成图标使用报告
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 无效图标到有效图标的映射
const iconReplacements = {
  // 数据可视化图标
  'PieChart': 'TrendCharts',
  'Histogram': 'TrendCharts',
  'DataLine': 'TrendCharts',
  'Chart': 'TrendCharts',
  'Graph': 'TrendCharts',
  
  // 用户和人员图标
  'UserFilled': 'User',
  'Users': 'User',
  'Medal': 'Trophy',
  
  // 操作和功能图标
  'Plus': 'CirclePlus',
  'Minus': 'Remove',
  'Delete': 'Delete',
  'Filter': 'Search',
  'Sort': 'Sort',
  'List': 'Menu',
  
  // 通信和消息图标
  'Message': 'ChatDotRound',
  'ChatLineRound': 'ChatDotRound',
  'Bell': 'Bell',
  'Notification': 'Bell',
  
  // 工具和设置图标
  'Tools': 'Setting',
  'MagicStick': 'Star',
  'Guide': 'QuestionFilled',
  'Pointer': 'Mouse',
  
  // 文档和内容图标
  'Notebook': 'Document',
  'Tickets': 'Document',
  'Reading': 'Reading',
  
  // 商业和办公图标
  'Briefcase': 'OfficeBuilding',
  'Money': 'Coin',
  'Rank': 'TrendCharts',
  
  // 安全和系统图标
  'Shield': 'Lock',
  'Lightning': 'Promotion',
  
  // 导航和方向图标
  'Bottom': 'ArrowDown',
  'MoreFilled': 'More',
  
  // 设备和媒体图标
  'Printer': 'Printer',
  'Bulb': 'Star'
}

// 面试系统专用图标语义映射
const interviewIconMapping = {
  // 面试流程图标
  'interview-start': 'VideoPlay',
  'interview-pause': 'VideoPause',
  'interview-end': 'Close',
  'interview-next': 'ArrowRight',
  'interview-previous': 'ArrowLeft',
  
  // 评估和分析图标
  'analysis-voice': 'Microphone',
  'analysis-video': 'VideoCamera',
  'analysis-text': 'Document',
  'analysis-data': 'TrendCharts',
  'analysis-ai': 'Cpu',
  
  // 候选人和用户图标
  'candidate': 'User',
  'interviewer': 'User',
  'enterprise': 'OfficeBuilding',
  'position': 'OfficeBuilding',
  
  // 技能和能力图标
  'skill-ai': 'Cpu',
  'skill-bigdata': 'DataBoard',
  'skill-iot': 'Connection',
  'skill-cloud': 'Monitor',
  
  // 状态和进度图标
  'status-active': 'Star',
  'status-completed': 'CircleCheck',
  'status-pending': 'Clock',
  'status-warning': 'WarningFilled',
  'status-error': 'Close',
  
  // 报告和结果图标
  'report-generate': 'Document',
  'report-download': 'Download',
  'report-share': 'Share',
  'report-print': 'Printer'
}

// 扫描文件中的图标使用
function scanFileForIcons(filePath) {
  if (!fs.existsSync(filePath)) {
    return { imports: [], usages: [], replacements: [] }
  }

  const content = fs.readFileSync(filePath, 'utf8')
  const imports = []
  const usages = []
  const replacements = []

  // 匹配导入语句中的图标
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@element-plus\/icons-vue['"]/g
  let importMatch
  while ((importMatch = importRegex.exec(content)) !== null) {
    const iconList = importMatch[1]
    const iconNames = iconList
      .split(',')
      .map(name => name.trim())
      .filter(name => name && !name.includes('as'))
    
    imports.push(...iconNames)
  }

  // 匹配模板中的图标使用
  const templateRegex = /<el-icon[^>]*>\s*<([A-Z][a-zA-Z]*)\s*\/?\s*>\s*<\/el-icon>/g
  let templateMatch
  while ((templateMatch = templateRegex.exec(content)) !== null) {
    usages.push(templateMatch[1])
  }

  // 检查需要替换的图标
  [...imports, ...usages].forEach(icon => {
    if (iconReplacements[icon]) {
      replacements.push({
        original: icon,
        replacement: iconReplacements[icon],
        type: imports.includes(icon) ? 'import' : 'usage'
      })
    }
  })

  return { imports, usages, replacements }
}

// 执行图标替换
function replaceIconsInFile(filePath, replacements) {
  if (!fs.existsSync(filePath) || replacements.length === 0) {
    return false
  }

  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false

  replacements.forEach(({ original, replacement }) => {
    // 替换导入语句中的图标
    const importRegex = new RegExp(`\\b${original}\\b(?=.*from\\s*['"]@element-plus\\/icons-vue['"])`, 'g')
    if (importRegex.test(content)) {
      content = content.replace(importRegex, replacement)
      modified = true
    }

    // 替换模板中的图标使用
    const templateRegex = new RegExp(`<${original}\\s*\\/?>`, 'g')
    if (templateRegex.test(content)) {
      content = content.replace(templateRegex, `<${replacement} />`)
      modified = true
    }
  })

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8')
    return true
  }

  return false
}

// 递归扫描目录
function scanDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const relativePath = path.relative(__dirname, fullPath)
        const analysis = scanFileForIcons(fullPath)
        
        if (analysis.imports.length > 0 || analysis.usages.length > 0 || analysis.replacements.length > 0) {
          results.push({
            file: relativePath,
            ...analysis
          })
        }
      }
    }
  }
  
  scan(dir)
  return results
}

// 生成优化报告
function generateOptimizationReport(results) {
  let totalReplacements = 0
  let filesModified = 0
  const replacementSummary = {}

  console.log('🎯 iFlytek 星火面试系统 - 图标系统优化报告')
  console.log('=' .repeat(60))
  
  results.forEach(({ file, replacements }) => {
    if (replacements.length > 0) {
      console.log(`\n📄 ${file}:`)
      replacements.forEach(({ original, replacement, type }) => {
        console.log(`   ${original} → ${replacement} (${type})`)
        totalReplacements++
        
        if (!replacementSummary[original]) {
          replacementSummary[original] = { count: 0, replacement }
        }
        replacementSummary[original].count++
      })
      filesModified++
    }
  })

  console.log('\n📊 优化统计:')
  console.log(`   修改文件数: ${filesModified}`)
  console.log(`   替换图标数: ${totalReplacements}`)
  
  console.log('\n🔄 替换汇总:')
  Object.entries(replacementSummary).forEach(([original, { count, replacement }]) => {
    console.log(`   ${original} → ${replacement} (${count} 次)`)
  })

  return { totalReplacements, filesModified, replacementSummary }
}

// 主函数
function main() {
  console.log('🚀 启动 iFlytek 图标系统优化工具...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  // 生成优化报告
  const report = generateOptimizationReport(results)
  
  // 询问是否执行替换
  console.log('\n❓ 是否执行图标替换？(y/n)')
  
  // 在实际使用中，这里应该有用户输入处理
  // 为了演示，我们直接执行替换
  const shouldReplace = true
  
  if (shouldReplace) {
    console.log('\n🔧 执行图标替换...')
    let successCount = 0
    
    results.forEach(({ file, replacements }) => {
      const fullPath = path.join(__dirname, file)
      if (replaceIconsInFile(fullPath, replacements)) {
        console.log(`✅ 已更新: ${file}`)
        successCount++
      }
    })
    
    console.log(`\n✨ 优化完成！成功更新 ${successCount} 个文件`)
  }
  
  console.log('\n📚 建议后续操作:')
  console.log('   1. 运行 npm run dev 检查应用是否正常')
  console.log('   2. 测试图标在不同屏幕尺寸下的显示效果')
  console.log('   3. 验证图标语义是否符合功能预期')
  console.log('   4. 检查 iFlytek 品牌一致性')
}

// 运行优化工具
main()

export {
  iconReplacements,
  interviewIconMapping,
  scanFileForIcons,
  replaceIconsInFile,
  scanDirectory,
  generateOptimizationReport
}
