{"version": 3, "file": "basic-month-table.js", "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-month-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const basicMonthTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault('month'),\n})\n\nexport type BasicMonthTableProps = ExtractPropTypes<typeof basicMonthTableProps>\n"], "names": ["buildProps", "datePickerSharedProps", "selectionModeWithDefault"], "mappings": ";;;;;;;AAEY,MAAC,oBAAoB,GAAGA,kBAAU,CAAC;AAC/C,EAAE,GAAGC,4BAAqB;AAC1B,EAAE,aAAa,EAAEC,+BAAwB,CAAC,OAAO,CAAC;AAClD,CAAC;;;;"}