/**
 * iFlytek Spark面试AI系统 - API连接测试
 * 验证前后端连接状态
 */

console.log('🔗 iFlytek Spark面试AI系统 - API连接测试');
console.log('='.repeat(50));
console.log(`测试时间: ${new Date().toLocaleString()}`);

// 测试API连接
async function testApiConnection() {
  const apiBaseUrl = 'http://localhost:8000';
  
  console.log('\n🌐 API连接状态检查:');
  console.log(`API基础地址: ${apiBaseUrl}`);
  
  try {
    // 测试健康检查端点
    console.log('\n1. 测试健康检查端点...');
    const healthResponse = await fetch(`${apiBaseUrl}/health`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ 健康检查成功');
      console.log(`   状态: ${healthData.status || '正常'}`);
      console.log(`   时间戳: ${healthData.timestamp || '未知'}`);
    } else {
      console.log(`❌ 健康检查失败: ${healthResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ 健康检查连接失败: ${error.message}`);
  }
  
  try {
    // 测试技术领域API
    console.log('\n2. 测试技术领域API...');
    const domainsResponse = await fetch(`${apiBaseUrl}/api/v1/domains`);
    
    if (domainsResponse.ok) {
      const domainsData = await domainsResponse.json();
      console.log('✅ 技术领域API成功');
      console.log(`   领域数量: ${domainsData.length || 0}`);
      if (domainsData.length > 0) {
        console.log(`   领域列表: ${domainsData.slice(0, 3).join(', ')}${domainsData.length > 3 ? '...' : ''}`);
      }
    } else {
      console.log(`❌ 技术领域API失败: ${domainsResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ 技术领域API连接失败: ${error.message}`);
  }
  
  try {
    // 测试系统信息API
    console.log('\n3. 测试系统信息API...');
    const infoResponse = await fetch(`${apiBaseUrl}/api/v1/system/info`);
    
    if (infoResponse.ok) {
      const infoData = await infoResponse.json();
      console.log('✅ 系统信息API成功');
      console.log(`   系统名称: ${infoData.name || '未知'}`);
      console.log(`   版本: ${infoData.version || '未知'}`);
    } else {
      console.log(`❌ 系统信息API失败: ${infoResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ 系统信息API连接失败: ${error.message}`);
  }
}

// 检查服务器状态
async function checkServerStatus() {
  console.log('\n🖥️ 服务器状态检查:');
  
  try {
    const response = await fetch('http://localhost:8000');
    if (response.ok) {
      console.log('✅ 后端服务器 (端口8000): 运行中');
    } else {
      console.log(`⚠️ 后端服务器响应异常: ${response.status}`);
    }
  } catch (error) {
    console.log('❌ 后端服务器 (端口8000): 连接失败');
    console.log(`   错误: ${error.message}`);
  }
  
  try {
    const response = await fetch('http://localhost:5173');
    if (response.ok) {
      console.log('✅ 前端服务器 (端口5173): 运行中');
    } else {
      console.log(`⚠️ 前端服务器响应异常: ${response.status}`);
    }
  } catch (error) {
    console.log('❌ 前端服务器 (端口5173): 连接失败');
    console.log(`   错误: ${error.message}`);
  }
}

// 生成连接状态报告
async function generateConnectionReport() {
  console.log('\n' + '='.repeat(50));
  console.log('📊 API连接状态报告');
  console.log('='.repeat(50));
  
  await checkServerStatus();
  await testApiConnection();
  
  console.log('\n💡 问题解决建议:');
  console.log('如果API连接失败:');
  console.log('1. 确认后端服务器正在运行 (端口8000)');
  console.log('2. 检查防火墙设置');
  console.log('3. 验证API端点路径是否正确');
  console.log('4. 查看后端服务器日志');
  
  console.log('\n🔧 Element Plus组件问题:');
  console.log('✅ el-loading-spinner 已替换为 el-icon + Loading');
  console.log('✅ 导入了 Loading 图标组件');
  console.log('✅ 修复了组件解析错误');
  
  console.log('\n🎯 下一步操作:');
  console.log('1. 访问 http://localhost:5173/demo 测试前端');
  console.log('2. 访问 http://localhost:8000/docs 查看API文档');
  console.log('3. 测试API调用功能');
  
  console.log('\n✨ 测试完成！');
}

// 执行测试
generateConnectionReport().catch(console.error);
