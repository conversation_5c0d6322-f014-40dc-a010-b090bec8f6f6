AI视频生成提示词 - 直接复制使用版本
===========================================

视频1: demo-complete.mp4 (8分钟) - 系统完整演示
------------------------------------------------
英文提示词:
Professional AI interview system demo, iFlytek Spark LLM interface, blue-purple gradient UI, multimodal input (voice/video/text), six capability assessment dashboard, Chinese interface, modern corporate design, smooth transitions, 1920x1080, 8 minutes

中文说明: 专业AI面试系统完整演示，iFlytek Spark界面，蓝紫渐变UI，多模态输入，六维评估仪表板


视频2: demo-ai-tech.mp4 (6分钟) - AI技术解析
--------------------------------------------
英文提示词:
iFlytek Spark LLM technical architecture, neural network diagrams, AI algorithm visualization, multimodal fusion technology, blue gradient background, Chinese tech presentation, professional diagrams, 6 minutes

中文说明: iFlytek Spark技术架构，神经网络图表，AI算法可视化，多模态融合技术


视频3: demo-cases.mp4 (5分钟) - 案例分析
---------------------------------------
英文提示词:
Interview case studies, AI engineer assessment, big data analyst evaluation, IoT developer interview, split-screen candidate analysis, professional interview room, Chinese subtitles, evaluation metrics, 5 minutes

中文说明: 面试案例研究，AI/大数据/IoT工程师评估，分屏分析，专业面试环境


视频4: demo-bigdata.mp4 (7分钟) - 大数据专题
-------------------------------------------
英文提示词:
Big data assessment interface, data processing visualization, machine learning algorithms, technical skill evaluation, data science dashboard, Chinese interface, professional analytics, chart animations, 7 minutes

中文说明: 大数据评估界面，数据处理可视化，机器学习算法，数据科学仪表板


视频5: demo-iot.mp4 (6分钟) - IoT专题
------------------------------------
英文提示词:
IoT technology assessment, embedded systems interface, sensor network visualization, communication protocols, hardware-software integration, Chinese tech interface, professional IoT dashboard, 6 minutes

中文说明: 物联网技术评估，嵌入式系统界面，传感器网络，通信协议，IoT仪表板


通用参数设置建议:
================
- 分辨率: 1920x1080
- 帧率: 30fps
- 格式: MP4
- 风格: Professional, Corporate
- 色调: Blue-purple gradient
- 界面: Chinese interface


Runway ML 使用方法:
==================
1. 访问 runwayml.com
2. 选择 Gen-2 视频生成
3. 复制上述英文提示词
4. 设置对应时长
5. 点击生成


Pika Labs 使用方法:
==================
1. 访问 pika.art Discord
2. 使用命令格式:
   /create [复制的英文提示词] --duration [时长] --aspect 16:9

例如:
/create Professional AI interview system demo, iFlytek Spark LLM interface, blue-purple gradient UI, multimodal input, six capability assessment dashboard, Chinese interface, modern corporate design, smooth transitions, 1920x1080 --duration 8 minutes --aspect 16:9


备用关键词 (如果需要调整):
=========================
- iFlytek Spark LLM
- multimodal AI system
- interview assessment
- blue-purple gradient
- Chinese interface
- professional dashboard
- corporate design
- technical visualization
- data analytics
- neural networks
- IoT sensors
- embedded systems


文件部署步骤:
============
1. 下载生成的视频
2. 重命名为: demo-complete.mp4, demo-ai-tech.mp4, demo-cases.mp4, demo-bigdata.mp4, demo-iot.mp4
3. 放入: frontend/public/videos/ 目录
4. 刷新浏览器验证
