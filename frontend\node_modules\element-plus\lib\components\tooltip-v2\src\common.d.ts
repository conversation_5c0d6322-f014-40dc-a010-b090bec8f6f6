import type { ExtractPropTypes } from 'vue';
/**
 * TODO: make this under constants or tokens
 */
export declare const tooltipV2CommonProps: {
    readonly nowrap: BooleanConstructor;
};
export type TooltipV2CommonProps = ExtractPropTypes<typeof tooltipV2CommonProps>;
export declare enum TooltipV2Sides {
    top = "top",
    bottom = "bottom",
    left = "left",
    right = "right"
}
export declare const tooltipV2Sides: TooltipV2Sides[];
export declare const tooltipV2OppositeSide: {
    readonly top: TooltipV2Sides.bottom;
    readonly bottom: TooltipV2Sides.top;
    readonly left: TooltipV2Sides.right;
    readonly right: TooltipV2Sides.left;
};
export declare const tooltipV2ArrowBorders: {
    readonly top: readonly [TooltipV2Sides.left, TooltipV2Sides.top];
    readonly bottom: readonly [TooltipV2Sides.bottom, TooltipV2Sides.right];
    readonly left: readonly [TooltipV2Sides.bottom, TooltipV2Sides.left];
    readonly right: readonly [TooltipV2Sides.top, TooltipV2Sides.right];
};
