# RC9

<!-- automd:badges color=yellow codecov bundlejs -->

[![npm version](https://img.shields.io/npm/v/rc9?color=yellow)](https://npmjs.com/package/rc9)
[![npm downloads](https://img.shields.io/npm/dm/rc9?color=yellow)](https://npmjs.com/package/rc9)
[![bundle size](https://img.shields.io/bundlejs/size/rc9?color=yellow)](https://bundlejs.com/?q=rc9)
[![codecov](https://img.shields.io/codecov/c/gh/unjs/rc9?color=yellow)](https://codecov.io/gh/unjs/rc9)

<!-- /automd -->

Read/Write RC configs couldn't be easier!

## Install

Install dependencies:

<!-- automd:pm-i -->

```sh
# ✨ Auto-detect
npx nypm install rc9

# npm
npm install rc9

# yarn
yarn add rc9

# pnpm
pnpm install rc9

# bun
bun install rc9
```

<!-- /automd -->

Import utils:

<!-- automd:jsimport cjs src="./src/index.ts"-->

**ESM** (Node.js, Bun)

```js
import {
  defaults,
  parse,
  parseFile,
  read,
  readUser,
  serialize,
  write,
  writeUser,
  update,
  updateUser,
} from "rc9";
```

**CommonJS** (Legacy Node.js)

```js
const {
  defaults,
  parse,
  parseFile,
  read,
  readUser,
  serialize,
  write,
  writeUser,
  update,
  updateUser,
} = require("rc9");
```

<!-- /automd -->


## Usage

`.conf`:

```ini
db.username=username
db.password=multi word password
db.enabled=true
```

**Update config:**

```ts
update({ 'db.enabled': false }) // or update(..., { name: '.conf' })
```

Push to an array:

```ts
update({ 'modules[]': 'test' })
```

**Read/Write config:**

```ts
const config = read() // or read('.conf')

// config = {
//   db: {
//     username: 'username',
//     password: 'multi word password',
//     enabled: true
//   }
// }

config.enabled = false
write(config) // or write(config, '.conf')
```

**User Config:**

It is common to keep config in user home directory (MacOS: `/Users/<USER>/home/<USER>