{"version": 3, "file": "mention2.js", "sources": ["../../../../../../packages/components/mention/src/mention.vue"], "sourcesContent": ["<template>\n  <div ref=\"wrapperRef\" :class=\"ns.b()\">\n    <el-input\n      v-bind=\"mergeProps(passInputProps, $attrs)\"\n      ref=\"elInputRef\"\n      :model-value=\"modelValue\"\n      :disabled=\"disabled\"\n      :role=\"dropdownVisible ? 'combobox' : undefined\"\n      :aria-activedescendant=\"dropdownVisible ? hoveringId || '' : undefined\"\n      :aria-controls=\"dropdownVisible ? contentId : undefined\"\n      :aria-expanded=\"dropdownVisible || undefined\"\n      :aria-label=\"ariaLabel\"\n      :aria-autocomplete=\"dropdownVisible ? 'none' : undefined\"\n      :aria-haspopup=\"dropdownVisible ? 'listbox' : undefined\"\n      @input=\"handleInputChange\"\n      @keydown=\"handleInputKeyDown\"\n      @mousedown=\"handleInputMouseDown\"\n    >\n      <template v-for=\"(_, name) in $slots\" #[name]=\"slotProps\">\n        <slot :name=\"name\" v-bind=\"slotProps\" />\n      </template>\n    </el-input>\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownVisible\"\n      :popper-class=\"[ns.e('popper'), popperClass]\"\n      :popper-options=\"popperOptions\"\n      :placement=\"computedPlacement\"\n      :fallback-placements=\"computedFallbackPlacements\"\n      effect=\"light\"\n      pure\n      :offset=\"offset\"\n      :show-arrow=\"showArrow\"\n    >\n      <template #default>\n        <div :style=\"cursorStyle\" />\n      </template>\n      <template #content>\n        <el-mention-dropdown\n          ref=\"dropdownRef\"\n          :options=\"filteredOptions\"\n          :disabled=\"disabled\"\n          :loading=\"loading\"\n          :content-id=\"contentId\"\n          :aria-label=\"ariaLabel\"\n          @select=\"handleSelect\"\n          @click.stop=\"elInputRef?.focus\"\n        >\n          <template v-for=\"(_, name) in $slots\" #[name]=\"slotProps\">\n            <slot :name=\"name\" v-bind=\"slotProps\" />\n          </template>\n        </el-mention-dropdown>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, mergeProps, nextTick, ref } from 'vue'\nimport { pick } from 'lodash-unified'\nimport { useFocusController, useId, useNamespace } from '@element-plus/hooks'\nimport ElInput, { inputProps } from '@element-plus/components/input'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport {\n  EVENT_CODE,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { isFunction } from '@element-plus/utils'\nimport { mentionEmits, mentionProps } from './mention'\nimport { getCursorPosition, getMentionCtx } from './helper'\nimport ElMentionDropdown from './mention-dropdown.vue'\n\nimport type { Placement } from '@popperjs/core'\nimport type { CSSProperties } from 'vue'\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { MentionCtx, MentionOption } from './types'\n\ndefineOptions({\n  name: 'ElMention',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(mentionProps)\nconst emit = defineEmits(mentionEmits)\n\nconst passInputProps = computed(() => pick(props, Object.keys(inputProps)))\n\nconst ns = useNamespace('mention')\nconst disabled = useFormDisabled()\nconst contentId = useId()\n\nconst elInputRef = ref<InputInstance>()\nconst tooltipRef = ref<TooltipInstance>()\nconst dropdownRef = ref<InstanceType<typeof ElMentionDropdown>>()\n\nconst visible = ref(false)\nconst cursorStyle = ref<CSSProperties>()\nconst mentionCtx = ref<MentionCtx>()\n\nconst computedPlacement = computed<Placement>(() =>\n  props.showArrow ? props.placement : `${props.placement}-start`\n)\n\nconst computedFallbackPlacements = computed<Placement[]>(() =>\n  props.showArrow ? ['bottom', 'top'] : ['bottom-start', 'top-start']\n)\n\nconst filteredOptions = computed(() => {\n  const { filterOption, options } = props\n  if (!mentionCtx.value || !filterOption) return options\n  return options.filter((option) =>\n    filterOption(mentionCtx.value!.pattern, option)\n  )\n})\n\nconst dropdownVisible = computed(() => {\n  return visible.value && (!!filteredOptions.value.length || props.loading)\n})\n\nconst hoveringId = computed(() => {\n  return `${contentId.value}-${dropdownRef.value?.hoveringIndex}`\n})\n\nconst handleInputChange = (value: string) => {\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(INPUT_EVENT, value)\n  syncAfterCursorMove()\n}\n\nconst handleInputKeyDown = (event: KeyboardEvent | Event) => {\n  if (!('code' in event) || elInputRef.value?.isComposing) return\n\n  switch (event.code) {\n    case EVENT_CODE.left:\n    case EVENT_CODE.right:\n      syncAfterCursorMove()\n      break\n    case EVENT_CODE.up:\n    case EVENT_CODE.down:\n      if (!visible.value) return\n      event.preventDefault()\n      dropdownRef.value?.navigateOptions(\n        event.code === EVENT_CODE.up ? 'prev' : 'next'\n      )\n      break\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      if (!visible.value) return\n      event.preventDefault()\n      if (dropdownRef.value?.hoverOption) {\n        dropdownRef.value?.selectHoverOption()\n      } else {\n        visible.value = false\n      }\n      break\n    case EVENT_CODE.esc:\n      if (!visible.value) return\n      event.preventDefault()\n      visible.value = false\n      break\n    case EVENT_CODE.backspace:\n      if (props.whole && mentionCtx.value) {\n        const { splitIndex, selectionEnd, pattern, prefixIndex, prefix } =\n          mentionCtx.value\n        const inputEl = getInputEl()\n        if (!inputEl) return\n        const inputValue = inputEl.value\n        const matchOption = props.options.find((item) => item.value === pattern)\n        const isWhole = isFunction(props.checkIsWhole)\n          ? props.checkIsWhole(pattern, prefix)\n          : matchOption\n        if (isWhole && splitIndex !== -1 && splitIndex + 1 === selectionEnd) {\n          event.preventDefault()\n          const newValue =\n            inputValue.slice(0, prefixIndex) + inputValue.slice(splitIndex + 1)\n          emit(UPDATE_MODEL_EVENT, newValue)\n          emit(INPUT_EVENT, newValue)\n\n          const newSelectionEnd = prefixIndex\n          nextTick(() => {\n            // input value is updated\n            inputEl.selectionStart = newSelectionEnd\n            inputEl.selectionEnd = newSelectionEnd\n            syncDropdownVisible()\n          })\n        }\n      }\n  }\n}\n\nconst { wrapperRef } = useFocusController(elInputRef, {\n  beforeFocus() {\n    return disabled.value\n  },\n  afterFocus() {\n    syncAfterCursorMove()\n  },\n  beforeBlur(event) {\n    return tooltipRef.value?.isFocusInsideContent(event)\n  },\n  afterBlur() {\n    visible.value = false\n  },\n})\n\nconst handleInputMouseDown = () => {\n  syncAfterCursorMove()\n}\n\nconst handleSelect = (item: MentionOption) => {\n  if (!mentionCtx.value) return\n  const inputEl = getInputEl()\n  if (!inputEl) return\n  const inputValue = inputEl.value\n  const { split } = props\n\n  const newEndPart = inputValue.slice(mentionCtx.value.end)\n  const alreadySeparated = newEndPart.startsWith(split)\n  const newMiddlePart = `${item.value}${alreadySeparated ? '' : split}`\n\n  const newValue =\n    inputValue.slice(0, mentionCtx.value.start) + newMiddlePart + newEndPart\n\n  emit(UPDATE_MODEL_EVENT, newValue)\n  emit(INPUT_EVENT, newValue)\n  emit('select', item, mentionCtx.value.prefix)\n\n  const newSelectionEnd =\n    mentionCtx.value.start + newMiddlePart.length + (alreadySeparated ? 1 : 0)\n\n  nextTick(() => {\n    // input value is updated\n    inputEl.selectionStart = newSelectionEnd\n    inputEl.selectionEnd = newSelectionEnd\n    inputEl.focus()\n    syncDropdownVisible()\n  })\n}\n\nconst getInputEl = () =>\n  props.type === 'textarea'\n    ? elInputRef.value?.textarea\n    : elInputRef.value?.input\n\nconst syncAfterCursorMove = () => {\n  // can't use nextTick(), get cursor position will be wrong\n  setTimeout(() => {\n    syncCursor()\n    syncDropdownVisible()\n    nextTick(() => tooltipRef.value?.updatePopper())\n  }, 0)\n}\n\nconst syncCursor = () => {\n  const inputEl = getInputEl()\n  if (!inputEl) return\n\n  const caretPosition = getCursorPosition(inputEl)\n  const inputRect = inputEl.getBoundingClientRect()\n  const elInputRect = elInputRef.value!.$el.getBoundingClientRect()\n\n  cursorStyle.value = {\n    position: 'absolute',\n    width: 0,\n    height: `${caretPosition.height}px`,\n    left: `${caretPosition.left + inputRect.left - elInputRect.left}px`,\n    top: `${caretPosition.top + inputRect.top - elInputRect.top}px`,\n  }\n}\n\nconst syncDropdownVisible = () => {\n  const inputEl = getInputEl()\n  if (document.activeElement !== inputEl) {\n    visible.value = false\n    return\n  }\n  const { prefix, split } = props\n  mentionCtx.value = getMentionCtx(inputEl, prefix, split)\n  if (mentionCtx.value && mentionCtx.value.splitIndex === -1) {\n    visible.value = true\n    emit('search', mentionCtx.value.pattern, mentionCtx.value.prefix)\n    return\n  }\n  visible.value = false\n}\n\ndefineExpose({\n  input: elInputRef,\n  tooltip: tooltipRef,\n  dropdownVisible,\n})\n</script>\n"], "names": ["computed", "pick", "inputProps", "useNamespace", "useFormDisabled", "useId", "ref", "UPDATE_MODEL_EVENT", "INPUT_EVENT", "event", "EVENT_CODE", "isFunction", "nextTick", "useFocusController", "getCursorPosition", "getMentionCtx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;uCAgFc,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,cAAA,GAAiBA,aAAS,MAAMC,kBAAA,CAAK,OAAO,MAAO,CAAA,IAAA,CAAKC,gBAAU,CAAC,CAAC,CAAA,CAAA;AAE1E,IAAM,MAAA,EAAA,GAAKC,mBAAa,SAAS,CAAA,CAAA;AACjC,IAAA,MAAM,WAAWC,kCAAgB,EAAA,CAAA;AACjC,IAAA,MAAM,YAAYC,aAAM,EAAA,CAAA;AAExB,IAAA,MAAM,aAAaC,OAAmB,EAAA,CAAA;AACtC,IAAA,MAAM,aAAaA,OAAqB,EAAA,CAAA;AACxC,IAAA,MAAM,cAAcA,OAA4C,EAAA,CAAA;AAEhE,IAAM,MAAA,OAAA,GAAUA,QAAI,KAAK,CAAA,CAAA;AACzB,IAAA,MAAM,cAAcA,OAAmB,EAAA,CAAA;AACvC,IAAA,MAAM,aAAaA,OAAgB,EAAA,CAAA;AAEnC,IAAA,MAAM,iBAAoB,GAAAN,YAAA,CAAA,MAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,SAAA,GAAA,CAAA,EAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IAAA,gCACA,GAAYA,YAAA,CAAA,WAAkB,CAAA,SAAA,GAAA,CAAA,QAAA,EAAA,KAAA,CAAA,GAAA,CAAA,cAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AAAA,IACxD,MAAA,eAAA,GAAAA,YAAA,CAAA,MAAA;AAEA,MAAA,MAAmC,EAAA,YAAA,EAAA,OAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MAAsB,IAAA,CAAA,UACrC,CAAA,KAAA,IAAC,aAAe;AAAgC,QACpE,OAAA,OAAA,CAAA;AAEA,MAAM,OAAA,OAAA,CAAA,MAAA,CAAkB,YAAe,YAAA,CAAA,UAAA,CAAA,KAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACrC,KAAM,CAAA,CAAA;AACN,IAAA,MAAI,eAAqB,GAAAA;AACzB,MAAA,OAAO,OAAQ,CAAA,KAAA,KAAA,CAAA,CAAA,eAAA,CAAA,KAAA,CAAA,MAAA,IAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACiC,IAChD,MAAA,UAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACD,IAAA,EAAA,CAAA;AAED,MAAM,OAAA,CAAA,EAAA,SAAA,CAAA,YAAiC,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAAA,CAAA,CAAA,CAAA;AACrC,KAAA,CAAA,CAAA;AAAiE,IACnE,MAAC,iBAAA,GAAA,CAAA,KAAA,KAAA;AAED,MAAM,IAAA,CAAAO,+BAA4B,CAAA,CAAA;AAChC,MAAA,IAAA,CAAAC,iBAAoB,EAAA,KAAA,CAAA,CAAK;AAAoC,MAC9D,mBAAA,EAAA,CAAA;AAED,KAAM,CAAA;AACJ,IAAA,MAAA,qBAAyB,CAAKC,OAAA,KAAA;AAC9B,MAAA,IAAA,cAAkB,CAAK;AACvB,MAAoB,IAAA,EAAA,MAAA,IAAAA,OAAA,CAAA,KAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,CAAA;AAAA,QACtB,OAAA;AAEA,MAAM,QAAAA,OAAA,CAAA,IAAA;AACJ,QAAA,KAAMC,eAAoB,CAAA,IAAA,CAAA;AAE1B,QAAA,KAAAA,eAAoB,CAAA,KAAA;AAAA,6BACF,EAAA,CAAA;AAAA,gBACA;AACd,QAAoB,KAAAA,eAAA,CAAA,EAAA,CAAA;AACpB,QAAA,KAAAA,eAAA,CAAA,IAAA;AAAA,cACc,CAAA,OAAA,CAAA,KAAA;AAAA,mBACA;AACd,UAAID,OAAC,eAAe,EAAA,CAAA;AACpB,UAAA,CAAA,EAAA,GAAM,WAAe,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAAA,OAAA,CAAA,IAAA,KAAAC,eAAA,CAAA,EAAA,GAAA,MAAA,GAAA,MAAA,CAAA,CAAA;AACrB,UAAA,MAAA;AAAmB,QAAA,KACXA,eAAA,CAAA,KAAoB,CAAA;AAAc,QAC1C,KAAAA,eAAA,CAAA,WAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,KAAA;AAAA,mBACc;AAAA,iBACA,CAAA,cAAA,EAAA,CAAA;AACd,UAAI,IAAA,CAAC,gBAAe,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,EAAA;AACpB,YAAA,CAAA,EAAA,GAAqB,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,EAAA,CAAA;AACrB,WAAI,MAAA;AACF,YAAA,OAAA,CAAA,aAAqC,CAAA;AAAA,WAChC;AACL,UAAA,MAAA;AAAgB,QAClB,KAAAA,eAAA,CAAA,GAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,KAAA;AAAA,mBACc;AACd,UAAID,OAAC,eAAe,EAAA,CAAA;AACpB,UAAA,OAAqB,CAAA,KAAA,GAAA,KAAA,CAAA;AACrB,UAAA,MAAA;AACA,QAAA,KAAAC,eAAA,CAAA,SAAA;AAAA,cACc,KAAA,CAAA,KAAA,IAAA,UAAA,CAAA,KAAA,EAAA;AACd,YAAI,MAAA,EAAM,UAAS,EAAA,YAAkB,EAAA,OAAA,EAAA,WAAA,EAAA,MAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AACnC,YAAA,MAAM,OAAc,GAAA,UAAA,EAAA,CAAA;AAEpB,YAAA,IAAA,CAAA;AACA,cAAA,OAAc;AACd,YAAA,MAAM,aAAa,OAAQ,CAAA,KAAA,CAAA;AAC3B,YAAM,MAAA,WAAA,GAAc,MAAM,OAAQ,CAAA,IAAA,CAAK,CAAC,IAAS,KAAA,IAAA,CAAK,UAAU,OAAO,CAAA,CAAA;AACvE,YAAM,MAAA,OAAA,GAAUC,kBAAW,KAAM,CAAA,YAAY,IACzC,KAAM,CAAA,YAAA,CAAa,OAAS,EAAA,MAAM,CAClC,GAAA,WAAA,CAAA;AACJ,YAAA,IAAI,OAAW,IAAA,UAAA,KAAe,CAAM,CAAA,IAAA,UAAA,GAAa,MAAM,YAAc,EAAA;AACnE,cAAAF,OAAA,CAAM,cAAe,EAAA,CAAA;AACrB,cAAM,MAAA,QAAA,GACJ,WAAW,KAAM,CAAA,CAAA,EAAG,WAAW,CAAI,GAAA,UAAA,CAAW,KAAM,CAAA,UAAA,GAAa,CAAC,CAAA,CAAA;AACpE,cAAA,IAAA,CAAKF,0BAAoB,QAAQ,CAAA,CAAA;AACjC,cAAA,IAAA,CAAKC,mBAAa,QAAQ,CAAA,CAAA;AAE1B,cAAA,MAAM,eAAkB,GAAA,WAAA,CAAA;AACxB,cAAAI,YAAA,CAAS,MAAM;AAEb,gBAAA,OAAA,CAAQ,cAAiB,GAAA,eAAA,CAAA;AACzB,gBAAA,OAAA,CAAQ,YAAe,GAAA,eAAA,CAAA;AACvB,gBAAoB,mBAAA,EAAA,CAAA;AAAA,eACrB,CAAA,CAAA;AAAA,aACH;AAAA,WACF;AAAA,OACJ;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,EAAE,UAAA,EAAe,GAAAC,0BAAA,CAAmB,UAAY,EAAA;AAAA,MACpD,WAAc,GAAA;AACZ,QAAA,OAAO,QAAS,CAAA,KAAA,CAAA;AAAA,OAClB;AAAA,MACA,UAAa,GAAA;AACX,QAAoB,mBAAA,EAAA,CAAA;AAAA,OACtB;AAAA,MACA,WAAW,KAAO,EAAA;AAChB,QAAO,IAAA,EAAA,CAAA;AAA4C,QACrD,OAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACY;AACV,MAAA,SAAA,GAAgB;AAAA,QAClB,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,OACD;AAED,KAAA,CAAA,CAAA;AACE,IAAoB,MAAA,oBAAA,GAAA,MAAA;AAAA,MACtB,mBAAA,EAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,YAAY,GAAO,CAAA,IAAA,KAAA;AACvB,MAAA,IAAA,CAAA,WAAgB,KAAW;AAC3B,QAAA,OAAc;AACd,MAAA,MAAM,oBAAqB,EAAA,CAAA;AAC3B,MAAM,IAAA,CAAA;AAEN,QAAA,OAAmB;AACnB,MAAM,MAAA,UAAA,GAAA,OAA8B,CAAA,KAAA,CAAA;AACpC,MAAA,MAAM,iBAAmB,CAAA;AAEzB,MAAM,MAAA,UAAA,aACa,CAAA,KAAA,CAAA,gBAAoB,CAAA,GAAA,CAAA,CAAA;AAEvC,MAAA,yBAAyB,UAAQ,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AACjC,MAAA,mBAA0B,GAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,EAAA,gBAAA,GAAA,EAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AAC1B,MAAA,MAAe,QAAA,GAAA,UAAiB,CAAA,KAAA,CAAA,CAAA,EAAA,UAAY,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,aAAA,GAAA,UAAA,CAAA;AAE5C,MAAA,IAAA,CAAAN,0BACa,QAAA,CAAA,CAAA;AAEb,MAAA,IAAA,CAAAC,iBAAe,EAAA,QAAA,CAAA,CAAA;AAEb,MAAA,IAAA,CAAA,QAAyB,EAAA,IAAA,EAAA,UAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACzB,MAAA,MAAA,eAAuB,GAAA,UAAA,CAAA,KAAA,CAAA,KAAA,GAAA,aAAA,CAAA,MAAA,IAAA,gBAAA,GAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACvB,MAAAI,YAAA,CAAA,MAAc;AACd,QAAoB,OAAA,CAAA,cAAA,GAAA,eAAA,CAAA;AAAA,QACrB,OAAA,CAAA,YAAA,GAAA,eAAA,CAAA;AAAA,QACH,OAAA,CAAA,KAAA,EAAA,CAAA;AAEA,QAAM,qBACJ,CAAM;AAIR,OAAA,CAAA,CAAA;AAEE,KAAA,CAAA;AACE,IAAW,MAAA,UAAA,GAAA,MAAA;AACX,MAAoB,IAAA,EAAA,EAAA,EAAA,CAAA;AACpB,MAAA,OAAA,KAAe,CAAA,IAAA,KAAA,UAAkB,GAAA,CAAA,EAAA,GAAA,UAAc,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,KAAA,CACjD;AAAI,IACN,MAAA,mBAAA,GAAA,MAAA;AAEA,MAAA,iBAAmB;AACjB,QAAA;AACA,QAAA,mBAAc,EAAA,CAAA;AAEd,QAAMA,YAAA,CAAA,MAAA;AACN,UAAM,IAAA,EAAA,CAAA;AACN,UAAA,OAAoB,CAAA,EAAA,GAAA,UAAA,CAAA,KAAkB,KAAA,IAA0B,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AAEhE,SAAA,CAAA,CAAA;AAAoB,OAAA,EACR,CAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACH,IACP,MAAA,UAAW,GAAA,MAAA;AAAoB,MAAA,aACtB,GAAA;AAAsD,MAAA,YACvD;AAAmD,QAC7D,OAAA;AAAA,MACF,MAAA,aAAA,GAAAE,wBAAA,CAAA,OAAA,CAAA,CAAA;AAEA,MAAA,0BAA4B,qBAAM,EAAA,CAAA;AAChC,MAAA,MAAM,WAAqB,GAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,CAAA;AAC3B,MAAI,WAAA,CAAA;AACF,QAAA,QAAQ,EAAQ,UAAA;AAChB,QAAA,KAAA,EAAA,CAAA;AAAA,QACF,MAAA,EAAA,CAAA,EAAA,aAAA,CAAA,MAAA,CAAA,EAAA,CAAA;AACA,QAAM,IAAA,EAAE,CAAQ,EAAA,aAAU,CAAA,IAAA,GAAA,SAAA,CAAA,IAAA,GAAA,WAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AAC1B,QAAA,GAAA,EAAA,CAAA,EAAA,aAAmB,CAAA,GAAA,GAAA,SAAuB,CAAA,GAAA,GAAA,WAAa,CAAA,GAAA,CAAA,EAAA,CAAA;AACvD,OAAA,CAAA;AACE,KAAA,CAAA;AACA,IAAA,MAAA,mBAA0B,GAAA,MAAA;AAC1B,MAAA,MAAA,OAAA,GAAA,UAAA,EAAA,CAAA;AAAA,MACF,IAAA,QAAA,CAAA,aAAA,KAAA,OAAA,EAAA;AACA,QAAA,OAAgB,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,QAClB,OAAA;AAEA,OAAa;AAAA,MACX,MAAO,EAAA,MAAA,EAAA,KAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MACP,UAAS,CAAA,KAAA,GAAAC,oBAAA,CAAA,OAAA,EAAA,MAAA,EAAA,KAAA,CAAA,CAAA;AAAA,MACT,IAAA,UAAA,CAAA,KAAA,IAAA,UAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,CAAA,EAAA;AAAA,QACD,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}