{"version": 3, "file": "follow.js", "sourceRoot": "", "sources": ["../../../../src/parse/grammar/follow.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACvC,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAC5C,OAAO,EAAE,EAAE,EAAE,MAAM,iBAAiB,CAAC;AACrC,OAAO,EAAE,WAAW,EAA+B,MAAM,kBAAkB,CAAC;AAG5E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,OAAO,mBAAoB,SAAQ,UAAU;IAGjD,YAAoB,OAAa;QAC/B,KAAK,EAAE,CAAC;QADU,YAAO,GAAP,OAAO,CAAM;QAF1B,YAAO,GAAgC,EAAE,CAAC;IAIjD,CAAC;IAED,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,YAAY,CACV,QAAkB,EAClB,QAAuB,EACvB,QAAuB;QAEvB,yDAAyD;IAC3D,CAAC;IAED,WAAW,CACT,OAAoB,EACpB,QAAuB,EACvB,QAAuB;QAEvB,MAAM,UAAU,GACd,6BAA6B,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC;YAClE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACpB,MAAM,QAAQ,GAAkB,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,IAAI,WAAW,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC3D,MAAM,oBAAoB,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,oBAAoB,CAAC;IAClD,CAAC;CACF;AAED,MAAM,UAAU,sBAAsB,CACpC,cAAsB;IAEtB,MAAM,aAAa,GAAG,EAAE,CAAC;IAEzB,OAAO,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE;QAClC,MAAM,cAAc,GAAG,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC;QACvE,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IACH,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,MAAM,UAAU,6BAA6B,CAC3C,KAAW,EACX,iBAAyB;IAEzB,OAAO,KAAK,CAAC,IAAI,GAAG,iBAAiB,GAAG,EAAE,CAAC;AAC7C,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,QAAkB;IACxD,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC;IAChD,OAAO,YAAY,GAAG,QAAQ,CAAC,GAAG,GAAG,EAAE,CAAC;AAC1C,CAAC"}