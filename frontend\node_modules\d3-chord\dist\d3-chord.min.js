// https://d3js.org/d3-chord/ v3.0.1 Copyright 2010-2021 Mike <PERSON>
!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("d3-path")):"function"==typeof define&&define.amd?define(["exports","d3-path"],t):t((n="undefined"!=typeof globalThis?globalThis:n||self).d3=n.d3||{},n.d3)}(this,(function(n,t){"use strict";var e=Math.abs,r=Math.cos,u=Math.sin,o=Math.PI,l=o/2,i=2*o,a=Math.max,c=1e-12;function f(n,t){return Array.from({length:t-n},((t,e)=>n+e))}function s(n){return function(t,e){return n(t.source.value+t.target.value,e.source.value+e.target.value)}}function g(n,t){var e=0,r=null,u=null,o=null;function l(l){var c,s=l.length,g=new Array(s),d=f(0,s),p=new Array(s*s),h=new Array(s),y=0;l=Float64Array.from({length:s*s},t?(n,t)=>l[t%s][t/s|0]:(n,t)=>l[t/s|0][t%s]);for(let t=0;t<s;++t){let e=0;for(let r=0;r<s;++r)e+=l[t*s+r]+n*l[r*s+t];y+=g[t]=e}c=(y=a(0,i-e*s)/y)?e:i/s;{let t=0;r&&d.sort(((n,t)=>r(g[n],g[t])));for(const e of d){const r=t;if(n){const n=f(1+~s,s).filter((n=>n<0?l[~n*s+e]:l[e*s+n]));u&&n.sort(((n,t)=>u(n<0?-l[~n*s+e]:l[e*s+n],t<0?-l[~t*s+e]:l[e*s+t])));for(const r of n)if(r<0){(p[~r*s+e]||(p[~r*s+e]={source:null,target:null})).target={index:e,startAngle:t,endAngle:t+=l[~r*s+e]*y,value:l[~r*s+e]}}else{(p[e*s+r]||(p[e*s+r]={source:null,target:null})).source={index:e,startAngle:t,endAngle:t+=l[e*s+r]*y,value:l[e*s+r]}}h[e]={index:e,startAngle:r,endAngle:t,value:g[e]}}else{const n=f(0,s).filter((n=>l[e*s+n]||l[n*s+e]));u&&n.sort(((n,t)=>u(l[e*s+n],l[e*s+t])));for(const r of n){let n;if(e<r?(n=p[e*s+r]||(p[e*s+r]={source:null,target:null}),n.source={index:e,startAngle:t,endAngle:t+=l[e*s+r]*y,value:l[e*s+r]}):(n=p[r*s+e]||(p[r*s+e]={source:null,target:null}),n.target={index:e,startAngle:t,endAngle:t+=l[e*s+r]*y,value:l[e*s+r]},e===r&&(n.source=n.target)),n.source&&n.target&&n.source.value<n.target.value){const t=n.source;n.source=n.target,n.target=t}}h[e]={index:e,startAngle:r,endAngle:t,value:g[e]}}t+=c}}return(p=Object.values(p)).groups=h,o?p.sort(o):p}return l.padAngle=function(n){return arguments.length?(e=a(0,n),l):e},l.sortGroups=function(n){return arguments.length?(r=n,l):r},l.sortSubgroups=function(n){return arguments.length?(u=n,l):u},l.sortChords=function(n){return arguments.length?(null==n?o=null:(o=s(n))._=n,l):o&&o._},l}var d=Array.prototype.slice;function p(n){return function(){return n}}function h(n){return n.source}function y(n){return n.target}function v(n){return n.radius}function A(n){return n.startAngle}function b(n){return n.endAngle}function x(){return 0}function T(){return 10}function m(n){var o=h,i=y,a=v,f=v,s=A,g=b,T=x,m=null;function M(){var p,h=o.apply(this,arguments),y=i.apply(this,arguments),v=T.apply(this,arguments)/2,A=d.call(arguments),b=+a.apply(this,(A[0]=h,A)),x=s.apply(this,A)-l,M=g.apply(this,A)-l,q=+f.apply(this,(A[0]=y,A)),w=s.apply(this,A)-l,C=g.apply(this,A)-l;if(m||(m=p=t.path()),v>c&&(e(M-x)>2*v+c?M>x?(x+=v,M-=v):(x-=v,M+=v):x=M=(x+M)/2,e(C-w)>2*v+c?C>w?(w+=v,C-=v):(w-=v,C+=v):w=C=(w+C)/2),m.moveTo(b*r(x),b*u(x)),m.arc(0,0,b,x,M),x!==w||M!==C)if(n){var _=+n.apply(this,arguments),j=q-_,P=(w+C)/2;m.quadraticCurveTo(0,0,j*r(w),j*u(w)),m.lineTo(q*r(P),q*u(P)),m.lineTo(j*r(C),j*u(C))}else m.quadraticCurveTo(0,0,q*r(w),q*u(w)),m.arc(0,0,q,w,C);if(m.quadraticCurveTo(0,0,b*r(x),b*u(x)),m.closePath(),p)return m=null,p+""||null}return n&&(M.headRadius=function(t){return arguments.length?(n="function"==typeof t?t:p(+t),M):n}),M.radius=function(n){return arguments.length?(a=f="function"==typeof n?n:p(+n),M):a},M.sourceRadius=function(n){return arguments.length?(a="function"==typeof n?n:p(+n),M):a},M.targetRadius=function(n){return arguments.length?(f="function"==typeof n?n:p(+n),M):f},M.startAngle=function(n){return arguments.length?(s="function"==typeof n?n:p(+n),M):s},M.endAngle=function(n){return arguments.length?(g="function"==typeof n?n:p(+n),M):g},M.padAngle=function(n){return arguments.length?(T="function"==typeof n?n:p(+n),M):T},M.source=function(n){return arguments.length?(o=n,M):o},M.target=function(n){return arguments.length?(i=n,M):i},M.context=function(n){return arguments.length?(m=null==n?null:n,M):m},M}n.chord=function(){return g(!1,!1)},n.chordDirected=function(){return g(!0,!1)},n.chordTranspose=function(){return g(!1,!0)},n.ribbon=function(){return m()},n.ribbonArrow=function(){return m(T)},Object.defineProperty(n,"__esModule",{value:!0})}));
