<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek Spark颜色强制更新工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .title {
            color: #4c51bf;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .section h3 {
            color: #6b21a8;
            margin-top: 0;
        }
        .button {
            background: linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .color-demo {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .color-box {
            width: 100px;
            height: 100px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }
        .old-color {
            background: #667eea;
        }
        .new-color {
            background: #4c51bf;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎨 iFlytek Spark颜色强制更新工具</h1>
        
        <div class="section">
            <h3>🎯 颜色对比</h3>
            <div class="color-demo">
                <div class="color-box old-color">
                    旧颜色<br>#667eea
                </div>
                <div class="color-box new-color">
                    新颜色<br>#4c51bf
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🔍 1. 检查当前CSS变量值</h3>
            <button class="button" onclick="checkCurrentColors()">检查当前颜色</button>
            <div id="colorCheck" class="result"></div>
        </div>

        <div class="section">
            <h3>🔧 2. 强制更新CSS变量</h3>
            <button class="button" onclick="forceUpdateColors()">强制更新颜色</button>
            <div id="updateResult" class="result"></div>
        </div>

        <div class="section">
            <h3>🌐 3. 检查样式表加载</h3>
            <button class="button" onclick="checkStyleSheets()">检查样式表</button>
            <div id="styleCheck" class="result"></div>
        </div>

        <div class="section">
            <h3>🔍 4. 查找旧颜色元素</h3>
            <button class="button" onclick="findOldColorElements()">查找旧颜色</button>
            <div id="oldColorCheck" class="result"></div>
        </div>

        <div class="section">
            <h3>🏷️ 5. 修复标签文字颜色</h3>
            <button class="button" onclick="fixTagTextColors()">修复标签文字颜色</button>
            <div id="tagFixResult" class="result"></div>
        </div>

        <div class="section">
            <h3>🚀 6. 一键修复</h3>
            <button class="button" onclick="oneClickFix()" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);">
                一键强制修复所有颜色
            </button>
            <div id="fixResult" class="result"></div>
        </div>

        <div class="section">
            <h3>📋 操作指南</h3>
            <div class="status warning">
                <strong>使用说明：</strong><br>
                1. 首先点击"检查当前颜色"查看CSS变量状态<br>
                2. 如果显示旧颜色，点击"强制更新颜色"<br>
                3. 如果标签文字不清晰，点击"修复标签文字颜色"<br>
                4. 如果问题仍然存在，点击"一键强制修复所有颜色"<br>
                5. 在主应用页面刷新查看效果
            </div>
            <div class="status success">
                <strong>💡 标签文字颜色问题：</strong><br>
                如果您在智能学习路径页面看到紫色背景上的文字不清晰，<br>
                请使用"修复标签文字颜色"功能，它会将所有标签文字设置为白色。
            </div>
        </div>
    </div>

    <script>
        // 检查当前CSS变量值
        function checkCurrentColors() {
            const root = getComputedStyle(document.documentElement);
            const currentPrimary = root.getPropertyValue('--iflytek-primary').trim();
            const currentSecondary = root.getPropertyValue('--iflytek-secondary').trim();
            
            const result = `当前CSS变量值:
--iflytek-primary: "${currentPrimary}"
--iflytek-secondary: "${currentSecondary}"

期望值:
--iflytek-primary: "#4c51bf"
--iflytek-secondary: "#6b21a8"

状态: ${currentPrimary === '#4c51bf' && currentSecondary === '#6b21a8' ? '✅ 正确' : '❌ 需要更新'}`;
            
            document.getElementById('colorCheck').textContent = result;
            
            // 显示状态
            const statusDiv = document.createElement('div');
            statusDiv.className = currentPrimary === '#4c51bf' && currentSecondary === '#6b21a8' ? 'status success' : 'status error';
            statusDiv.textContent = currentPrimary === '#4c51bf' && currentSecondary === '#6b21a8' ? 
                '✅ CSS变量已正确设置' : '❌ CSS变量需要更新';
            document.getElementById('colorCheck').appendChild(statusDiv);
        }

        // 强制更新CSS变量
        function forceUpdateColors() {
            try {
                document.documentElement.style.setProperty('--iflytek-primary', '#4c51bf');
                document.documentElement.style.setProperty('--iflytek-secondary', '#6b21a8');
                document.documentElement.style.setProperty('--iflytek-gradient', 'linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%)');
                document.documentElement.style.setProperty('--text-on-iflytek-primary', '#ffffff');
                document.documentElement.style.setProperty('--text-on-iflytek-secondary', '#ffffff');
                
                const result = `✅ CSS变量强制更新成功！

已更新的变量:
--iflytek-primary: #4c51bf
--iflytek-secondary: #6b21a8
--iflytek-gradient: linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%)
--text-on-iflytek-primary: #ffffff
--text-on-iflytek-secondary: #ffffff

请在主应用页面刷新查看效果。`;
                
                document.getElementById('updateResult').textContent = result;
                
                const statusDiv = document.createElement('div');
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 颜色变量已强制更新，请刷新主页面查看效果';
                document.getElementById('updateResult').appendChild(statusDiv);
                
            } catch (error) {
                const result = `❌ 更新失败: ${error.message}`;
                document.getElementById('updateResult').textContent = result;
            }
        }

        // 检查样式表加载
        function checkStyleSheets() {
            const sheets = Array.from(document.styleSheets);
            let result = `加载的样式表 (${sheets.length} 个):\n\n`;
            
            sheets.forEach((sheet, index) => {
                const href = sheet.href || '内联样式';
                const isIflytekCSS = href.includes('design-system') || 
                                   href.includes('wcag-compliant') || 
                                   href.includes('iflytek-brand');
                result += `${index + 1}. ${href} ${isIflytekCSS ? '✅' : ''}\n`;
            });
            
            document.getElementById('styleCheck').textContent = result;
        }

        // 查找使用旧颜色的元素
        function findOldColorElements() {
            const allElements = Array.from(document.querySelectorAll('*'));
            const oldColorElements = allElements.filter(el => {
                const style = getComputedStyle(el);
                return style.backgroundColor.includes('102, 126, 234') || 
                       style.color.includes('102, 126, 234') ||
                       style.borderColor.includes('102, 126, 234');
            });
            
            const result = `查找结果:
找到 ${oldColorElements.length} 个使用旧颜色的元素

${oldColorElements.length > 0 ? 
    '元素列表:\n' + oldColorElements.slice(0, 5).map((el, i) => 
        `${i + 1}. ${el.tagName.toLowerCase()}${el.className ? '.' + el.className.split(' ')[0] : ''}`
    ).join('\n') + 
    (oldColorElements.length > 5 ? `\n... 还有 ${oldColorElements.length - 5} 个元素` : '')
    : '✅ 未发现使用旧颜色的元素'}`;
            
            document.getElementById('oldColorCheck').textContent = result;
            
            // 高亮显示找到的元素
            oldColorElements.forEach(el => {
                el.style.outline = '2px solid red';
                setTimeout(() => {
                    el.style.outline = '';
                }, 3000);
            });
        }

        // 修复标签文字颜色
        function fixTagTextColors() {
            const result = document.getElementById('tagFixResult');
            result.textContent = '正在修复标签文字颜色...\n\n';

            // 查找所有标签元素
            const tagSelectors = ['.el-tag', '.career-tag', '.career-goal-tag', '.weak-area-tag', '.concept-tag'];
            let totalFixed = 0;

            tagSelectors.forEach(selector => {
                const tags = document.querySelectorAll(selector);
                let selectorFixed = 0;

                tags.forEach(tag => {
                    // 强制设置白色文字
                    tag.style.setProperty('color', '#ffffff', 'important');
                    tag.style.setProperty('font-weight', '500', 'important');
                    tag.style.setProperty('text-shadow', '0 1px 2px rgba(0, 0, 0, 0.2)', 'important');

                    // 确保背景是高对比度渐变
                    const currentBg = getComputedStyle(tag).backgroundColor;
                    if (!currentBg.includes('76, 81, 191') && !currentBg.includes('#4c51bf')) {
                        tag.style.setProperty('background', 'linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%)', 'important');
                    }

                    selectorFixed++;
                });

                if (selectorFixed > 0) {
                    result.textContent += `✅ ${selector}: 修复了 ${selectorFixed} 个标签\n`;
                    totalFixed += selectorFixed;
                }
            });

            if (totalFixed === 0) {
                result.textContent += 'ℹ️ 当前页面未找到需要修复的标签元素\n';
                result.textContent += '💡 请在智能学习路径页面使用此功能\n';
            } else {
                result.textContent += `\n🎉 总共修复了 ${totalFixed} 个标签的文字颜色！\n`;
            }

            const statusDiv = document.createElement('div');
            statusDiv.className = totalFixed > 0 ? 'status success' : 'status warning';
            statusDiv.innerHTML = totalFixed > 0 ?
                '<strong>✅ 标签文字颜色修复完成！</strong><br>所有标签现在都显示白色文字' :
                '<strong>ℹ️ 未找到标签元素</strong><br>请在包含标签的页面使用此功能';
            result.appendChild(statusDiv);
        }

        // 一键修复
        function oneClickFix() {
            const result = document.getElementById('fixResult');
            result.textContent = '正在执行一键修复...\n\n';

            // 1. 强制更新CSS变量
            document.documentElement.style.setProperty('--iflytek-primary', '#4c51bf');
            document.documentElement.style.setProperty('--iflytek-secondary', '#6b21a8');
            document.documentElement.style.setProperty('--iflytek-gradient', 'linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%)');
            result.textContent += '✅ 步骤1: CSS变量已更新\n';

            // 2. 修复标签文字颜色
            const tagSelectors = ['.el-tag', '.career-tag', '.career-goal-tag', '.weak-area-tag', '.concept-tag'];
            let tagFixed = 0;

            tagSelectors.forEach(selector => {
                const tags = document.querySelectorAll(selector);
                tags.forEach(tag => {
                    tag.style.setProperty('color', '#ffffff', 'important');
                    tag.style.setProperty('font-weight', '500', 'important');
                    tag.style.setProperty('text-shadow', '0 1px 2px rgba(0, 0, 0, 0.2)', 'important');
                    tag.style.setProperty('background', 'linear-gradient(135deg, #4c51bf 0%, #6b21a8 100%)', 'important');
                    tagFixed++;
                });
            });

            result.textContent += `✅ 步骤2: 修复了 ${tagFixed} 个标签的文字颜色\n`;

            // 3. 查找并修复使用旧颜色的元素
            const allElements = Array.from(document.querySelectorAll('*'));
            let fixedCount = 0;

            allElements.forEach(el => {
                const style = getComputedStyle(el);
                if (style.backgroundColor.includes('102, 126, 234')) {
                    el.style.backgroundColor = '#4c51bf';
                    fixedCount++;
                }
                if (style.color.includes('102, 126, 234')) {
                    el.style.color = '#4c51bf';
                    fixedCount++;
                }
                if (style.borderColor.includes('102, 126, 234')) {
                    el.style.borderColor = '#4c51bf';
                    fixedCount++;
                }
            });

            result.textContent += `✅ 步骤3: 修复了 ${fixedCount} 个元素的颜色\n`;

            // 4. 强制刷新样式
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            links.forEach(link => {
                const href = link.href;
                link.href = href + (href.includes('?') ? '&' : '?') + 'v=' + Date.now();
            });

            result.textContent += '✅ 步骤4: 强制刷新样式表\n';
            result.textContent += '\n🎉 一键修复完成！请在主应用页面刷新查看效果。';

            const statusDiv = document.createElement('div');
            statusDiv.className = 'status success';
            statusDiv.innerHTML = '<strong>🎉 修复完成！</strong><br>请返回主应用页面并刷新 (Ctrl+F5) 查看效果';
            result.appendChild(statusDiv);
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkCurrentColors();
        };
    </script>
</body>
</html>
