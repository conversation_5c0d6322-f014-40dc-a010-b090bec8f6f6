/**
 * iFlytek 星火大模型智能面试系统 - 文本面试页面紧凑布局
 * Text Interview Compact Layout for iFlytek Spark Interview System
 * 
 * 仅针对文本面试页面的紧凑化优化，不影响其他页面
 */

/* 仅对文本面试页面生效 */
.text-primary-interview-page {
  /* 确保页面内容在紫色背景内 */
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 头部区域紧凑化 */
.text-primary-interview-page .interview-header {
  padding: 12px 0;
}

.text-primary-interview-page .header-content {
  max-width: min(1200px, calc(100vw - 48px));
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  box-sizing: border-box;
}

/* 主要内容区域紧凑化 */
.text-primary-interview-page .interview-main {
  padding: 16px 0;
}

.text-primary-interview-page .interview-layout {
  max-width: min(1200px, calc(100vw - 48px));
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 20px;
  box-sizing: border-box;
}

/* 对话区域优化 */
.text-primary-interview-page .conversation-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

/* 分析区域优化 */
.text-primary-interview-page .analysis-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.text-primary-interview-page .analysis-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 输入区域紧凑化 */
.text-primary-interview-page .input-section {
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 消息列表优化 */
.text-primary-interview-page .messages-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 12px 0;
}

.text-primary-interview-page .message-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  background: #f8fafc;
}

/* 候选人信息卡片紧凑化 */
.text-primary-interview-page .candidate-info-card {
  padding: 16px;
  margin-bottom: 16px;
}

.text-primary-interview-page .candidate-avatar {
  width: 48px;
  height: 48px;
}

/* 实时分析结果紧凑化 */
.text-primary-interview-page .analysis-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin: 16px 0;
}

.text-primary-interview-page .metric-item {
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  text-align: center;
}

.text-primary-interview-page .metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.text-primary-interview-page .metric-label {
  font-size: 0.875rem;
  color: #64748b;
}

/* 按钮和控件优化 */
.text-primary-interview-page .el-button {
  padding: 8px 16px;
  font-size: 14px;
}

.text-primary-interview-page .el-button--large {
  padding: 10px 20px;
  font-size: 16px;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .text-primary-interview-page .interview-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .text-primary-interview-page .header-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .text-primary-interview-page .header-content,
  .text-primary-interview-page .interview-layout {
    max-width: calc(100vw - 32px);
    padding: 0 16px;
  }
  
  .text-primary-interview-page .conversation-container,
  .text-primary-interview-page .analysis-card,
  .text-primary-interview-page .input-section {
    padding: 16px;
    border-radius: 8px;
  }
  
  .text-primary-interview-page .analysis-metrics {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

/* 确保所有内容都在容器内 */
.text-primary-interview-page * {
  box-sizing: border-box;
  max-width: 100%;
}

/* 防止水平滚动 */
.text-primary-interview-page {
  overflow-x: hidden;
}

.text-primary-interview-page .interview-layout,
.text-primary-interview-page .header-content {
  overflow: hidden;
}
