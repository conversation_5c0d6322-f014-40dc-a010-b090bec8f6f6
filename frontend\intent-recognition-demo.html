<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek AI面试官意图识别演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1890ff 0%, #0066cc 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .demo-section {
            padding: 30px;
        }
        
        .input-area {
            margin-bottom: 30px;
        }
        
        .input-area label {
            display: block;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .input-area textarea {
            width: 100%;
            height: 100px;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s;
        }
        
        .input-area textarea:focus {
            outline: none;
            border-color: #1890ff;
        }
        
        .analyze-btn {
            background: linear-gradient(135deg, #1890ff 0%, #0066cc 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .analyze-btn:hover {
            transform: translateY(-2px);
        }
        
        .result-area {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        
        .intent-result {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .intent-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .intent-request-answer {
            background: #ff4d4f;
            color: white;
        }
        
        .intent-request-guidance {
            background: #52c41a;
            color: white;
        }
        
        .intent-express-unknown {
            background: #faad14;
            color: white;
        }
        
        .intent-normal-answer {
            background: #1890ff;
            color: white;
        }
        
        .ai-response {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            margin-top: 15px;
        }
        
        .ai-response h4 {
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .example-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .example-card:hover {
            border-color: #1890ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24,144,255,0.15);
        }
        
        .example-card h4 {
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .example-text {
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }
        
        .example-intent {
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 iFlytek AI面试官意图识别演示</h1>
            <p>智能识别候选人的真实意图，提供差异化的回复策略</p>
        </div>
        
        <div class="demo-section">
            <div class="input-area">
                <label for="userInput">请输入候选人的回答：</label>
                <textarea id="userInput" placeholder="例如：我不知道，请告诉我正确答案"></textarea>
                <br><br>
                <button class="analyze-btn" onclick="analyzeIntent()">🔍 分析意图</button>
            </div>
            
            <div id="resultArea" class="result-area" style="display: none;">
                <div class="intent-result">
                    <span id="intentBadge" class="intent-badge"></span>
                    <span id="intentDescription"></span>
                </div>
                <div class="ai-response">
                    <h4>🤖 AI面试官回复：</h4>
                    <div id="aiResponse"></div>
                </div>
            </div>
            
            <div class="examples">
                <div class="example-card" onclick="useExample('我不知道，请告诉我正确答案')">
                    <h4>📋 要求标准答案</h4>
                    <div class="example-text">"我不知道，请告诉我正确答案"</div>
                    <div class="example-intent intent-request-answer">直接提供详细答案</div>
                </div>
                
                <div class="example-card" onclick="useExample('我不会，可以给我一些引导吗')">
                    <h4>🧭 要求思路引导</h4>
                    <div class="example-text">"我不会，可以给我一些引导吗"</div>
                    <div class="example-intent intent-request-guidance">提供思考方向</div>
                </div>
                
                <div class="example-card" onclick="useExample('不太了解')">
                    <h4>💡 一般性不知道</h4>
                    <div class="example-text">"不太了解"</div>
                    <div class="example-intent intent-express-unknown">鼓励性学习建议</div>
                </div>
                
                <div class="example-card" onclick="useExample('深度学习模型优化主要包括量化、剪枝、知识蒸馏等技术手段，我在项目中使用过INT8量化来减少模型大小')">
                    <h4>🎯 正常技术回答</h4>
                    <div class="example-text">"深度学习模型优化主要包括量化、剪枝..."</div>
                    <div class="example-intent intent-normal-answer">技术深度分析</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 意图分析函数
        function analyzeUserIntent(answer) {
            const answerLower = answer.toLowerCase()
            
            const requestAnswerPatterns = [
                '请告诉我正确答案', '告诉我答案', '正确答案是什么', '标准答案',
                '请给出答案', '能告诉我', '请直接说', '直接告诉我',
                '答案应该是什么', '请提供答案', '给我答案'
            ]
            
            const requestGuidancePatterns = [
                '给我一些引导', '可以给我提示', '给我一些思路', '能否指导',
                '给我一些方向', '可以提示一下', '给我一些建议', '能给我启发',
                '我需要引导', '希望得到指导', '可以帮我分析', '得到一些指导',
                '希望得到.*指导', '希望得到.*建议', '给我.*指导', '请给我.*指导',
                '可以指导', '能指导', '指导一下', '建议一下', '提示一下'
            ]
            
            const unknownPatterns = [
                '不知道', '不清楚', '没有经验', '不了解', '不会', '没做过',
                '不太懂', '不确定', '完全不懂', '没有接触过', '不太了解'
            ]
            
            const hasRequestAnswer = requestAnswerPatterns.some(pattern => {
                const regex = new RegExp(pattern, 'i')
                return regex.test(answerLower)
            })
            
            const hasRequestGuidance = requestGuidancePatterns.some(pattern => {
                const regex = new RegExp(pattern, 'i')
                return regex.test(answerLower)
            })
            
            const hasUnknownKeywords = unknownPatterns.some(pattern => answerLower.includes(pattern))
            
            if (hasRequestAnswer) {
                return 'request_answer'
            } else if (hasRequestGuidance) {
                return 'request_guidance'
            } else if (hasUnknownKeywords) {
                if (answer.trim().length > 100) {
                    if (hasRequestGuidance) {
                        return 'request_guidance'
                    } else {
                        return 'normal_answer'
                    }
                } else {
                    return 'express_unknown'
                }
            } else {
                return 'normal_answer'
            }
        }

        // 生成AI回复
        function generateAIResponse(intent, userAnswer) {
            switch(intent) {
                case 'request_answer':
                    return `好的，我来为您提供这个问题的详细答案：

📋 **标准答案解析**

**深度学习模型优化的核心技术：**

**1. 模型架构优化**
• **量化技术**：INT8/FP16量化减少模型大小和推理时间
• **模型剪枝**：移除不重要的连接和神经元
• **知识蒸馏**：用大模型训练小模型，保持性能的同时减少计算量

**2. 训练优化**
• **混合精度训练**：使用FP16加速训练，关键部分保持FP32
• **梯度累积**：在有限显存下模拟大批次训练
• **学习率调度**：余弦退火、warmup等策略

**3. 推理优化**
• **批处理优化**：动态批处理和KV-Cache管理
• **算子融合**：CUDA kernel融合减少访存开销
• **投机解码**：使用小模型预测，大模型验证

希望这个详细的答案对您有帮助。在实际面试中，建议您结合自己的项目经验来回答会更加出色。`

                case 'request_guidance':
                    return `当然可以！让我为您提供一些思考的方向和引导：

🧭 **思考引导**

💡 建议从基本概念开始，逐步深入到具体实现

**可以从这些角度思考：**
• **基础概念**：这个技术的核心原理是什么？
• **技术实现**：通常采用什么方法或工具来实现？
• **应用场景**：在什么情况下会使用这个技术？
• **优势特点**：相比其他方案有什么优势？

**回答建议：**
即使不完全了解，也可以分享您听说过的相关概念，或者表达您的学习思路。面试官更看重您的思考过程和学习能力。`

                case 'express_unknown':
                    return `没关系，诚实地表达不了解是很好的态度！让我为您提供一些思路：

💡 **学习提示**

这个问题涉及的核心概念包括技术原理、实现方法和应用场景。

**您可以尝试：**
• 分享您在相关领域的任何了解，哪怕是基础概念
• 表达您对这个技术的学习兴趣和计划
• 谈谈您认为可能的解决思路
• 提及您在类似场景中的经验

**记住：** 面试不仅是考察现有知识，更重要的是看到您的学习能力和思考方式。诚实和积极的学习态度往往比强行回答更有价值。`

                case 'normal_answer':
                    return `您的回答有一定的技术深度，思路也比较清晰。

为了更好地了解您的实践能力，能否详细说明一下具体的技术实现细节？比如：
• 使用了哪些具体的工具或框架？
• 在实际项目中遇到了什么挑战？
• 是如何解决性能瓶颈的？
• 有什么经验总结和改进建议？

请选择其中一个方面详细展开，我会根据您的回答提供更深入的技术讨论。`

                default:
                    return '感谢您的回答，让我们继续下一个问题。'
            }
        }

        // 分析意图
        function analyzeIntent() {
            const userInput = document.getElementById('userInput').value.trim()
            if (!userInput) {
                alert('请输入候选人的回答')
                return
            }

            const intent = analyzeUserIntent(userInput)
            const aiResponse = generateAIResponse(intent, userInput)

            // 显示结果
            const resultArea = document.getElementById('resultArea')
            const intentBadge = document.getElementById('intentBadge')
            const intentDescription = document.getElementById('intentDescription')
            const aiResponseDiv = document.getElementById('aiResponse')

            // 设置意图标签
            intentBadge.className = `intent-badge intent-${intent.replace('_', '-')}`
            
            const intentTexts = {
                'request_answer': '要求标准答案',
                'request_guidance': '要求思路引导', 
                'express_unknown': '一般性不知道',
                'normal_answer': '正常技术回答'
            }
            
            const intentDescriptions = {
                'request_answer': '用户明确要求提供标准答案，AI应直接给出详细的技术解答',
                'request_guidance': '用户希望得到思考方向和引导，AI应提供思路和方法',
                'express_unknown': '用户表达不了解，AI应给予鼓励性的学习建议',
                'normal_answer': '用户提供了技术回答，AI应进行深度分析和追问'
            }

            intentBadge.textContent = intentTexts[intent]
            intentDescription.textContent = intentDescriptions[intent]
            aiResponseDiv.innerHTML = aiResponse.replace(/\n/g, '<br>')

            resultArea.style.display = 'block'
        }

        // 使用示例
        function useExample(text) {
            document.getElementById('userInput').value = text
            analyzeIntent()
        }
    </script>
</body>
</html>
