#!/usr/bin/env node

/**
 * Element Plus 图标错误修复工具
 * 自动检测和修复不存在的图标导入
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Element Plus 中确实存在的图标列表
const VALID_ICONS = [
  // 基础图标
  'Plus', 'Minus', 'Close', 'Check', 'Search', 'Edit', 'Delete', 'Refresh',
  'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
  
  // 媒体图标
  'VideoPlay', 'VideoPause', 'VideoCamera', 'Microphone', 'Upload', 'Download',
  'Picture', 'Files', 'Document', 'Folder', 'FolderOpened',
  
  // 数据和图表图标
  'TrendCharts', 'DataBoard', 'Grid', 'Histogram', 'PieChart',
  
  // 系统图标
  'Cpu', 'Monitor', 'Setting', 'Tools', 'Connection', 'Wifi',
  
  // 状态图标
  'Star', 'Warning', 'InfoFilled', 'SuccessFilled', 'CircleCheck',
  'WarningFilled', 'CircleClose', 'Loading',
  
  // 用户和界面图标
  'User', 'UserFilled', 'Avatar', 'Phone', 'Message', 'ChatDotRound',
  'Location', 'OfficeBuilding', 'School', 'House',
  
  // 其他常用图标
  'Lock', 'Unlock', 'View', 'Hide', 'More', 'MoreFilled',
  'Timer', 'Clock', 'Calendar', 'Date'
];

// 常见的错误图标及其替换建议
const ICON_REPLACEMENTS = {
  'BrainFilled': 'Cpu',
  'DataAnalysis': 'TrendCharts',
  'CloudUpload': 'Upload',
  'PlayArrow': 'VideoPlay',
  'Iphone': 'Phone',
  'Cellphone': 'Phone',
  'Smartphone': 'Phone',
  'Monitor': 'TrendCharts',
  'Dashboard': 'DataBoard',
  'Analytics': 'TrendCharts',
  'Brain': 'Cpu',
  'Intelligence': 'Cpu',
  'Smart': 'Cpu'
};

// 扫描文件中的图标使用
function scanFileForIcons(filePath) {
  if (!fs.existsSync(filePath)) {
    return { imports: [], issues: [] };
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const imports = [];
  const issues = [];

  // 匹配导入语句
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@element-plus\/icons-vue['"]/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    const iconList = match[1];
    const iconNames = iconList
      .split(',')
      .map(name => name.trim())
      .filter(name => name && !name.includes('//'));

    iconNames.forEach(iconName => {
      imports.push(iconName);
      
      if (!VALID_ICONS.includes(iconName)) {
        const replacement = ICON_REPLACEMENTS[iconName];
        issues.push({
          type: 'invalid_import',
          icon: iconName,
          replacement: replacement || 'Unknown',
          line: content.substring(0, match.index).split('\n').length
        });
      }
    });
  }

  return { imports, issues };
}

// 修复文件中的图标问题
function fixIconsInFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // 替换导入语句中的错误图标
  Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
    const importRegex = new RegExp(`(import\\s*{[^}]*?)\\b${oldIcon}\\b([^}]*}\\s*from\\s*['"]@element-plus\\/icons-vue['"])`, 'g');
    const templateRegex = new RegExp(`<${oldIcon}\\s*\\/>`, 'g');
    const componentRegex = new RegExp(`<${oldIcon}\\s*>`, 'g');
    
    if (importRegex.test(content)) {
      content = content.replace(importRegex, `$1${newIcon}$2`);
      hasChanges = true;
      console.log(`  ✅ 替换导入: ${oldIcon} → ${newIcon}`);
    }
    
    if (templateRegex.test(content)) {
      content = content.replace(templateRegex, `<${newIcon} />`);
      hasChanges = true;
      console.log(`  ✅ 替换模板: ${oldIcon} → ${newIcon}`);
    }
    
    if (componentRegex.test(content)) {
      content = content.replace(componentRegex, `<${newIcon}>`);
      hasChanges = true;
      console.log(`  ✅ 替换组件: ${oldIcon} → ${newIcon}`);
    }
  });

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }

  return false;
}

// 递归扫描目录
function scanDirectory(dir) {
  const results = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath);
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const relativePath = path.relative(__dirname, fullPath);
        const { imports, issues } = scanFileForIcons(fullPath);
        
        if (issues.length > 0) {
          results.push({
            file: relativePath,
            imports,
            issues
          });
        }
      }
    }
  }
  
  scan(dir);
  return results;
}

// 主函数
function main() {
  console.log('🔧 Element Plus 图标错误修复工具');
  console.log('=' .repeat(50));
  
  const srcDir = path.join(__dirname, 'src');
  const results = scanDirectory(srcDir);
  
  if (results.length === 0) {
    console.log('✅ 未发现图标问题！');
    return;
  }
  
  console.log(`\n🔍 发现 ${results.length} 个文件存在图标问题:\n`);
  
  let totalFixed = 0;
  
  results.forEach(({ file, issues }) => {
    console.log(`📄 ${file}:`);
    
    issues.forEach(issue => {
      console.log(`  ❌ 第${issue.line}行: ${issue.icon} (${issue.type})`);
      if (issue.replacement && issue.replacement !== 'Unknown') {
        console.log(`     建议替换为: ${issue.replacement}`);
      }
    });
    
    // 尝试自动修复
    const fullPath = path.join(__dirname, file);
    if (fixIconsInFile(fullPath)) {
      totalFixed++;
      console.log(`  🔧 已自动修复`);
    } else {
      console.log(`  ⚠️  需要手动修复`);
    }
    
    console.log();
  });
  
  console.log(`\n📊 修复统计:`);
  console.log(`  总问题文件: ${results.length}`);
  console.log(`  自动修复: ${totalFixed}`);
  console.log(`  需手动修复: ${results.length - totalFixed}`);
  
  if (totalFixed > 0) {
    console.log(`\n✅ 已自动修复 ${totalFixed} 个文件的图标问题`);
    console.log('💡 建议重启开发服务器以确保修复生效');
  }
}

// 运行修复工具
main();

export { scanFileForIcons, fixIconsInFile, VALID_ICONS, ICON_REPLACEMENTS };
