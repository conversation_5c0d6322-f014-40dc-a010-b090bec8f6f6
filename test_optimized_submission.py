#!/usr/bin/env python3
"""
测试精简版提交文件的可运行性
"""

import os
import sys
import subprocess
import time
import requests
import zipfile
import tempfile
import shutil
from pathlib import Path

def test_zip_extraction_and_startup():
    """测试ZIP文件解压和启动"""
    print("🔍 测试精简版提交文件的可运行性")
    print("=" * 60)
    
    # 测试目录
    test_dir = Path("test_submission_temp")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    try:
        # 1. 解压作品包
        print("\n📦 解压作品包...")
        work_zip = Path("软件杯最终提交文件/86014454作品.zip")
        if not work_zip.exists():
            print(f"❌ 作品包不存在: {work_zip}")
            return False
        
        work_dir = test_dir / "work"
        work_dir.mkdir()
        
        with zipfile.ZipFile(work_zip, 'r') as zip_ref:
            zip_ref.extractall(work_dir)
        print(f"✅ 作品包解压完成: {work_dir}")
        
        # 2. 检查关键文件
        print("\n🔍 检查关键文件...")
        required_files = [
            "快速启动.bat",
            "backend/simple_start.py",
            "backend/requirements.txt",
            "frontend_dist/index.html",
            "system_config.json"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = work_dir / file_path
            if full_path.exists():
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}")
                missing_files.append(file_path)
        
        if missing_files:
            print(f"\n❌ 缺失关键文件: {missing_files}")
            return False
        
        # 3. 检查Python依赖
        print("\n🐍 检查Python依赖...")
        requirements_file = work_dir / "backend" / "requirements.txt"
        
        try:
            # 尝试导入关键模块
            import fastapi
            import uvicorn
            print("✅ FastAPI 和 Uvicorn 已安装")
        except ImportError as e:
            print(f"⚠️ 缺少依赖: {e}")
            print("正在尝试安装依赖...")
            
            # 安装依赖
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], capture_output=True, text=True, cwd=work_dir / "backend")
            
            if result.returncode != 0:
                print(f"❌ 依赖安装失败: {result.stderr}")
                return False
            else:
                print("✅ 依赖安装成功")
        
        # 4. 测试后端启动
        print("\n🚀 测试后端启动...")
        backend_dir = work_dir / "backend"
        
        # 启动后端服务器
        backend_process = subprocess.Popen([
            sys.executable, "simple_start.py"
        ], cwd=backend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        print("等待后端服务器启动...")
        time.sleep(10)
        
        # 检查后端是否启动成功
        try:
            response = requests.get("http://localhost:8000/docs", timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务器启动成功")
                backend_ok = True
            else:
                print(f"⚠️ 后端响应异常: {response.status_code}")
                backend_ok = False
        except requests.exceptions.RequestException as e:
            print(f"❌ 后端连接失败: {e}")
            backend_ok = False
        
        # 停止后端服务器
        backend_process.terminate()
        backend_process.wait()
        
        # 5. 测试前端文件
        print("\n🌐 测试前端文件...")
        frontend_dir = work_dir / "frontend_dist"
        index_file = frontend_dir / "index.html"
        
        if index_file.exists():
            with open(index_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'assets/index-' in content and '.js' in content:
                    print("✅ 前端构建文件完整")
                    frontend_ok = True
                else:
                    print("❌ 前端构建文件不完整")
                    frontend_ok = False
        else:
            print("❌ 前端index.html不存在")
            frontend_ok = False
        
        # 检查assets目录
        assets_dir = frontend_dir / "assets"
        if assets_dir.exists():
            js_files = list(assets_dir.glob("*.js"))
            css_files = list(assets_dir.glob("*.css"))
            print(f"✅ 前端资源: {len(js_files)} JS文件, {len(css_files)} CSS文件")
        else:
            print("❌ 前端assets目录不存在")
            frontend_ok = False
        
        # 6. 测试启动脚本
        print("\n📜 测试启动脚本...")
        startup_script = work_dir / "快速启动.bat"
        
        if startup_script.exists():
            with open(startup_script, 'r', encoding='utf-8') as f:
                script_content = f.read()
                if 'python simple_start.py' in script_content and 'python -m http.server 8080' in script_content:
                    print("✅ 启动脚本逻辑正确")
                    script_ok = True
                else:
                    print("❌ 启动脚本逻辑有问题")
                    script_ok = False
        else:
            print("❌ 启动脚本不存在")
            script_ok = False
        
        # 7. 总结测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"  文件完整性: {'✅ 通过' if not missing_files else '❌ 失败'}")
        print(f"  后端启动: {'✅ 通过' if backend_ok else '❌ 失败'}")
        print(f"  前端文件: {'✅ 通过' if frontend_ok else '❌ 失败'}")
        print(f"  启动脚本: {'✅ 通过' if script_ok else '❌ 失败'}")
        
        overall_success = not missing_files and backend_ok and frontend_ok and script_ok
        
        if overall_success:
            print("\n🎉 精简版提交文件可运行性测试通过！")
            print("✅ 系统可以正常启动和运行")
            print("✅ 核心功能完整保留")
            print("✅ 文件结构正确")
        else:
            print("\n❌ 精简版提交文件存在问题！")
            print("需要修复上述问题后再提交")
        
        return overall_success
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return False
    
    finally:
        # 清理测试目录
        if test_dir.exists():
            shutil.rmtree(test_dir)
        print(f"\n🧹 清理测试目录: {test_dir}")

def test_source_package():
    """测试源码包"""
    print("\n📦 测试源码包...")
    
    source_zip = Path("软件杯最终提交文件/86014454源码.zip")
    if not source_zip.exists():
        print(f"❌ 源码包不存在: {source_zip}")
        return False
    
    # 检查源码包内容
    with zipfile.ZipFile(source_zip, 'r') as zip_ref:
        files = zip_ref.namelist()
        
        # 检查关键源码文件
        required_source_files = [
            "frontend/package.json",
            "frontend/src/App.vue",
            "frontend/src/main.js",
            "backend/app/main.py",
            "backend/requirements.txt"
        ]
        
        missing_source_files = []
        for file_path in required_source_files:
            if file_path not in files:
                missing_source_files.append(file_path)
            else:
                print(f"✅ {file_path}")
        
        if missing_source_files:
            print(f"❌ 源码包缺失文件: {missing_source_files}")
            return False
        else:
            print("✅ 源码包文件完整")
            return True

if __name__ == "__main__":
    print("🧪 开始测试精简版提交文件...")
    
    # 测试作品包
    work_package_ok = test_zip_extraction_and_startup()
    
    # 测试源码包
    source_package_ok = test_source_package()
    
    # 最终结果
    print("\n" + "=" * 60)
    print("🏆 最终测试结果:")
    print(f"  作品包: {'✅ 可运行' if work_package_ok else '❌ 有问题'}")
    print(f"  源码包: {'✅ 完整' if source_package_ok else '❌ 有问题'}")
    
    if work_package_ok and source_package_ok:
        print("\n🎉 精简版提交文件完全可用！")
        print("✅ 可以安全提交到比赛平台")
    else:
        print("\n⚠️ 精简版提交文件需要修复！")
        print("❌ 建议修复问题后再提交")
    
    exit(0 if (work_package_ok and source_package_ok) else 1)
