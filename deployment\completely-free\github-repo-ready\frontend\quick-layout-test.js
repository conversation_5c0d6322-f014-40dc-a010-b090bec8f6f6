/**
 * iFlytek Spark 面试系统 - 快速布局测试脚本
 * 用于验证布局优化效果
 */

console.log('🎯 iFlytek Spark 面试系统 - 快速布局测试');
console.log('='.repeat(60));

// 布局测试配置
const layoutTests = {
    gridLayout: {
        name: '主布局网格比例',
        selector: '.interview-layout.spark-layout',
        property: 'gridTemplateColumns',
        expected: '1.2fr 0.8fr',
        description: '左右区域比例应为 1.2:0.8'
    },
    containerWidth: {
        name: '容器最大宽度',
        selector: '.interview-main.spark-main',
        property: 'maxWidth',
        expected: '1400px',
        description: '主容器最大宽度应为 1400px'
    },
    headerHeight: {
        name: '头部最小高度',
        selector: '.interview-header.iflytek-style',
        property: 'minHeight',
        expected: '80px',
        description: '头部最小高度应为 80px'
    },
    layoutGap: {
        name: '布局间距',
        selector: '.interview-layout.spark-layout',
        property: 'gap',
        expected: '32px',
        description: '主布局间距应为 32px'
    },
    headerGap: {
        name: '头部内容间距',
        selector: '.interview-header-content',
        property: 'gap',
        expected: '24px',
        description: '头部内容间距应为 24px'
    }
};

// 响应式测试断点
const breakpoints = {
    desktop: { width: 1400, name: '大屏幕' },
    laptop: { width: 1200, name: '笔记本' },
    tablet: { width: 768, name: '平板' },
    mobile: { width: 480, name: '手机' }
};

/**
 * 执行布局测试
 */
function runLayoutTests() {
    console.log('\n📐 开始布局测试...');
    
    const results = {};
    let passedTests = 0;
    let totalTests = 0;
    
    Object.entries(layoutTests).forEach(([testKey, test]) => {
        totalTests++;
        const element = document.querySelector(test.selector);
        
        if (!element) {
            console.log(`❌ ${test.name}: 元素未找到 (${test.selector})`);
            results[testKey] = { passed: false, reason: '元素未找到' };
            return;
        }
        
        const computedStyle = window.getComputedStyle(element);
        const actualValue = computedStyle[test.property];
        
        // 特殊处理网格布局
        if (test.property === 'gridTemplateColumns') {
            const passed = actualValue.includes('1.2fr') && actualValue.includes('0.8fr');
            if (passed) {
                console.log(`✅ ${test.name}: ${actualValue}`);
                passedTests++;
                results[testKey] = { passed: true, value: actualValue };
            } else {
                console.log(`❌ ${test.name}: ${actualValue} (期望包含 1.2fr 和 0.8fr)`);
                results[testKey] = { passed: false, value: actualValue, expected: test.expected };
            }
        } else {
            const passed = actualValue === test.expected;
            if (passed) {
                console.log(`✅ ${test.name}: ${actualValue}`);
                passedTests++;
                results[testKey] = { passed: true, value: actualValue };
            } else {
                console.log(`❌ ${test.name}: ${actualValue} (期望: ${test.expected})`);
                results[testKey] = { passed: false, value: actualValue, expected: test.expected };
            }
        }
    });
    
    console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过 (${Math.round(passedTests/totalTests*100)}%)`);
    return results;
}

/**
 * 检查响应式设计
 */
function checkResponsiveDesign() {
    console.log('\n📱 检查响应式设计...');
    
    const currentWidth = window.innerWidth;
    console.log(`当前窗口宽度: ${currentWidth}px`);
    
    // 确定当前断点
    let currentBreakpoint = 'desktop';
    if (currentWidth <= 480) currentBreakpoint = 'mobile';
    else if (currentWidth <= 768) currentBreakpoint = 'tablet';
    else if (currentWidth <= 1200) currentBreakpoint = 'laptop';
    
    console.log(`当前断点: ${breakpoints[currentBreakpoint].name}`);
    
    // 检查布局是否正确响应
    const layoutElement = document.querySelector('.interview-layout.spark-layout');
    if (layoutElement) {
        const computedStyle = window.getComputedStyle(layoutElement);
        const gridColumns = computedStyle.gridTemplateColumns;
        
        if (currentWidth <= 1200) {
            const isSingleColumn = gridColumns === 'none' || gridColumns === '1fr' || !gridColumns.includes('fr');
            if (isSingleColumn) {
                console.log('✅ 小屏幕下正确切换为单列布局');
            } else {
                console.log(`❌ 小屏幕下应为单列布局，当前: ${gridColumns}`);
            }
        } else {
            const isDoubleColumn = gridColumns.includes('1.2fr') && gridColumns.includes('0.8fr');
            if (isDoubleColumn) {
                console.log('✅ 大屏幕下正确使用双列布局');
            } else {
                console.log(`❌ 大屏幕下应为双列布局，当前: ${gridColumns}`);
            }
        }
    }
}

/**
 * 检查滚动容器
 */
function checkScrollContainers() {
    console.log('\n📜 检查滚动容器...');
    
    const scrollContainers = [
        { selector: '.analysis-section.spark-analysis', name: 'AI分析区域' },
        { selector: '.candidate-info-card.spark-card', name: '候选人信息卡片' },
        { selector: '.ai-interviewer-panel.spark-panel', name: 'AI面试官面板' }
    ];
    
    scrollContainers.forEach(container => {
        const element = document.querySelector(container.selector);
        if (element) {
            const computedStyle = window.getComputedStyle(element);
            const overflowY = computedStyle.overflowY;
            const maxHeight = computedStyle.maxHeight;
            
            if (overflowY === 'auto' || overflowY === 'scroll') {
                console.log(`✅ ${container.name}: 滚动设置正确 (${overflowY})`);
                if (maxHeight && maxHeight !== 'none') {
                    console.log(`   最大高度: ${maxHeight}`);
                }
            } else {
                console.log(`⚠️ ${container.name}: 可能需要滚动设置 (${overflowY})`);
            }
        } else {
            console.log(`❌ ${container.name}: 元素未找到`);
        }
    });
}

/**
 * 检查视觉层次
 */
function checkVisualHierarchy() {
    console.log('\n🎨 检查视觉层次...');
    
    const elements = [
        { selector: '.interview-header.iflytek-style', name: '头部', expectedZIndex: 100 },
        { selector: '.interview-main.spark-main', name: '主内容区域' },
        { selector: '.candidate-section.spark-candidate', name: '候选人区域' },
        { selector: '.analysis-section.spark-analysis', name: 'AI分析区域' }
    ];
    
    elements.forEach(element => {
        const el = document.querySelector(element.selector);
        if (el) {
            const computedStyle = window.getComputedStyle(el);
            const zIndex = computedStyle.zIndex;
            const position = computedStyle.position;
            
            console.log(`📍 ${element.name}:`);
            console.log(`   位置: ${position}`);
            if (zIndex !== 'auto') {
                console.log(`   层级: ${zIndex}`);
            }
            
            if (element.expectedZIndex && zIndex == element.expectedZIndex) {
                console.log(`   ✅ 层级设置正确`);
            }
        }
    });
}

/**
 * 生成布局报告
 */
function generateLayoutReport() {
    console.log('\n📋 生成布局优化报告...');
    
    const report = {
        timestamp: new Date().toLocaleString('zh-CN'),
        windowSize: `${window.innerWidth}x${window.innerHeight}`,
        userAgent: navigator.userAgent,
        tests: runLayoutTests(),
        responsive: checkResponsiveDesign(),
        scrollContainers: checkScrollContainers(),
        visualHierarchy: checkVisualHierarchy()
    };
    
    console.log('\n📄 报告生成完成！');
    console.log('可以将此报告保存用于进一步分析。');
    
    return report;
}

/**
 * 主函数
 */
function main() {
    console.log('🚀 开始执行布局测试...');
    
    // 等待页面完全加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(generateLayoutReport, 1000);
        });
    } else {
        setTimeout(generateLayoutReport, 1000);
    }
}

// 导出函数供外部调用
if (typeof window !== 'undefined') {
    window.layoutTest = {
        runLayoutTests,
        checkResponsiveDesign,
        checkScrollContainers,
        checkVisualHierarchy,
        generateLayoutReport
    };
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined' && window.location) {
    main();
}

console.log('\n💡 使用说明:');
console.log('- 在浏览器控制台中运行: layoutTest.generateLayoutReport()');
console.log('- 单独测试布局: layoutTest.runLayoutTests()');
console.log('- 检查响应式: layoutTest.checkResponsiveDesign()');
console.log('- 检查滚动: layoutTest.checkScrollContainers()');
