# 🏆 比赛评审运行指南

## 📋 系统概述
**多模态智能模拟面试评测智能体** - 中国软件杯参赛项目
- 基于讯飞星火大模型的AI面试评估系统
- 支持文本、语音、视频多模态分析
- 6个核心能力指标智能评估
- Vue.js + FastAPI 现代化技术栈

## 🚀 一键启动 (推荐)

### 环境要求
- **Python 3.8+** (必需)
- **Node.js 16+** (必需)
- **Windows/Linux/macOS** 均支持

### 启动步骤

1. **打开命令行/终端**，进入项目目录：
```bash
cd cursor_softcup
```

2. **一键启动系统**：
```bash
python start_system.py
```

3. **等待启动完成**（约1-2分钟）：
   - 系统会自动安装所需依赖
   - 自动启动后端和前端服务
   - 显示访问地址

4. **访问系统**：
   - **前端界面**：http://localhost:5173
   - **后端API**：http://localhost:8000
   - **API文档**：http://localhost:8000/docs

### 启动成功标志
看到以下信息表示启动成功：
```
🌟 多模态面试评估系统已启动
====================================
📱 前端界面: http://localhost:5173
🔧 后端API: http://localhost:8000
📚 API文档: http://localhost:8000/docs
====================================
```

## 🎯 系统演示功能

### 主要页面
1. **首页** - 系统介绍和功能概览
2. **演示页面** - 核心功能展示和交互演示
3. **面试页面** - 模拟面试流程
4. **报告页面** - 智能分析结果展示

### 核心功能演示
- **多模态输入**：文本、语音、视频分析
- **AI智能评估**：6个核心能力指标
- **实时反馈**：即时分析和建议
- **可视化报告**：图表展示分析结果
- **技术领域专业化**：AI、大数据、物联网

## 🛠️ 备用启动方式

如果一键启动失败，可使用手动启动：

### 后端启动
```bash
cd backend
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 前端启动 (新终端)
```bash
cd frontend
npm install
npm run dev
```

## 🔧 故障排除

### 常见问题
1. **端口占用**：确保8000和5173端口未被占用
2. **依赖安装失败**：检查网络连接，尝试使用国内镜像
3. **Python版本**：确保Python版本3.8+
4. **Node.js版本**：确保Node.js版本16+

### 快速检查
```bash
python --version  # 应显示3.8+
node --version    # 应显示16+
```

## 📞 技术支持

如遇问题，请检查：
1. 控制台错误信息
2. 浏览器开发者工具
3. 网络连接状态
4. 端口占用情况

## 🎉 评审要点

### 技术亮点
- **讯飞星火大模型集成**
- **多模态AI分析**
- **现代化前端界面**
- **完整的评估体系**
- **丰富的演示功能**

### 功能完整性
- ✅ 多模态输入支持
- ✅ AI智能分析
- ✅ 可视化报告
- ✅ 用户交互体验
- ✅ 系统稳定性

---
**祝评审顺利！** 🎊
