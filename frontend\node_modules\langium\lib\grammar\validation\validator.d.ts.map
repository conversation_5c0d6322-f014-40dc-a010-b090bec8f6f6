{"version": 3, "file": "validator.d.ts", "sourceRoot": "", "sources": ["../../../src/grammar/validation/validator.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAIhF,OAAO,KAAK,GAAG,MAAM,kCAAkC,CAAC;AAExD,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,gCAAgC,CAAC;AACjE,OAAO,KAAK,EAAuB,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAO3E,OAAO,KAAK,EAAkB,kBAAkB,EAAoB,MAAM,yCAAyC,CAAC;AAEpH,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,qCAAqC,CAAC;AAC1E,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAErE,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAI3E,wBAAgB,wBAAwB,CAAC,QAAQ,EAAE,sBAAsB,GAAG,IAAI,CAyE/E;AAED,yBAAiB,UAAU,CAAC;IACjB,MAAM,oBAAoB,2BAA2B,CAAC;IACtD,MAAM,iBAAiB,wBAAwB,CAAC;IAChD,MAAM,mBAAmB,0BAA0B,CAAC;IACpD,MAAM,cAAc,qBAAqB,CAAC;IAC1C,MAAM,oBAAoB,4BAA4B,CAAC;IACvD,MAAM,mBAAmB,2BAA2B,CAAC;IACrD,MAAM,wBAAwB,+BAA+B,CAAC;IAC9D,MAAM,cAAc,oBAAoB,CAAC;IACzC,MAAM,aAAa,mBAAmB,CAAC;IACvC,MAAM,YAAY,kBAAkB,CAAC;IACrC,MAAM,cAAc,oBAAoB,CAAC;IACzC,MAAM,gBAAgB,sBAAsB,CAAC;IAC7C,MAAM,sBAAsB,6BAA6B,CAAC;IAC1D,MAAM,gBAAgB,uBAAuB,CAAC;CACxD;AAED,qBAAa,uBAAuB;IAEhC,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC;IAC1C,SAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,cAAc,CAAC;IAC/C,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,CAAC;gBAEnC,QAAQ,EAAE,sBAAsB;IAM5C,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAaxE,qBAAqB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IA0B7E;;OAEG;IACH,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAK3E;;OAEG;IACH,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAK3E,OAAO,CAAC,eAAe;IA0BvB,6BAA6B,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAmBrF;;OAEG;IACH,wBAAwB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IA4BhF,OAAO,CAAC,yBAAyB;IAiBjC,qBAAqB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAsG7E,OAAO,CAAC,aAAa;IASrB,wBAAwB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAUhF,uBAAuB,CAAC,YAAY,EAAE,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAMzF,sBAAsB,CAAC,YAAY,EAAE,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAYxF,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAyClF,sBAAsB,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAqC/E,2BAA2B,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAepF,OAAO,CAAC,YAAY;IAiBpB,2BAA2B,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAuB5G,6BAA6B,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAUvF,yBAAyB,CAAC,QAAQ,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAUzF,kBAAkB,CAAC,GAAG,EAAE,GAAG,CAAC,aAAa,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAa5E,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAqBvF,0BAA0B,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAiBlF,0BAA0B,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IA6FlF,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAavE,qHAAqH;IACrH,8BAA8B,CAAC,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAmCpF,qBAAqB,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAI9H,2BAA2B,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAItG,2BAA2B,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAMnF,OAAO,CAAC,iBAAiB;IAUzB,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAYpE,mBAAmB,CAAC,cAAc,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAWzF,uBAAuB,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAe/E,uBAAuB,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAa/E,6BAA6B,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAS3F,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IA0BlF;sDACkD;IAClD,8CAA8C,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAOtG,OAAO,CAAC,yDAAyD;IAoBjE,OAAO,CAAC,oDAAoD;IA2E5D,2BAA2B,CAAC,aAAa,EAAE,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IA4B3F,SAAS,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM;IAI/C,2BAA2B,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAMrF,uBAAuB,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAajF,2BAA2B,CAAC,SAAS,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAM5F,yBAAyB,CAAC,SAAS,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAc1F,iBAAiB,CAAC,SAAS,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAOlF,8BAA8B,CAAC,SAAS,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAS/F,qBAAqB,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAM7E,uBAAuB,CAAC,IAAI,EAAE,GAAG,CAAC,aAAa,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;IAMlF,SAAS,CAAC,8BAA8B,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,MAAM,GAAG,SAAS;IAU/F,8BAA8B,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,kBAAkB,GAAG,IAAI;CAK/F"}