{"version": 3, "sources": ["../../../src/diagrams/mindmap/parser/mindmap.jison", "../../../src/diagrams/mindmap/mindmapDb.ts", "../../../src/diagrams/mindmap/mindmapRenderer.ts", "../../../src/diagrams/mindmap/svgDraw.ts", "../../../src/diagrams/mindmap/styles.ts", "../../../src/diagrams/mindmap/mindmap-definition.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,4],$V1=[1,13],$V2=[1,12],$V3=[1,15],$V4=[1,16],$V5=[1,20],$V6=[1,19],$V7=[6,7,8],$V8=[1,26],$V9=[1,24],$Va=[1,25],$Vb=[6,7,11],$Vc=[1,6,13,15,16,19,22],$Vd=[1,33],$Ve=[1,34],$Vf=[1,6,7,11,13,15,16,19,22];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"mindMap\":4,\"spaceLines\":5,\"SPACELINE\":6,\"NL\":7,\"MINDMAP\":8,\"document\":9,\"stop\":10,\"EOF\":11,\"statement\":12,\"SPACELIST\":13,\"node\":14,\"ICON\":15,\"CLASS\":16,\"nodeWithId\":17,\"nodeWithoutId\":18,\"NODE_DSTART\":19,\"NODE_DESCR\":20,\"NODE_DEND\":21,\"NODE_ID\":22,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",6:\"SPACELINE\",7:\"NL\",8:\"MINDMAP\",11:\"EOF\",13:\"SPACELIST\",15:\"ICON\",16:\"CLASS\",19:\"NODE_DSTART\",20:\"NODE_DESCR\",21:\"NODE_DEND\",22:\"NODE_ID\"},\nproductions_: [0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 6: case 7:\n return yy; \nbreak;\ncase 8:\nyy.getLogger().trace('Stop NL ');\nbreak;\ncase 9:\nyy.getLogger().trace('Stop EOF ');\nbreak;\ncase 11:\nyy.getLogger().trace('Stop NL2 ');\nbreak;\ncase 12:\nyy.getLogger().trace('Stop EOF2 ');\nbreak;\ncase 15:\n yy.getLogger().info('Node: ',$$[$0].id);yy.addNode($$[$0-1].length, $$[$0].id, $$[$0].descr, $$[$0].type);  \nbreak;\ncase 16:\n yy.getLogger().trace('Icon: ',$$[$0]);yy.decorateNode({icon: $$[$0]}); \nbreak;\ncase 17: case 21:\n yy.decorateNode({class: $$[$0]}); \nbreak;\ncase 18:\n yy.getLogger().trace('SPACELIST');\nbreak;\ncase 19:\n yy.getLogger().trace('Node: ',$$[$0].id);yy.addNode(0, $$[$0].id, $$[$0].descr, $$[$0].type);  \nbreak;\ncase 20:\n yy.decorateNode({icon: $$[$0]}); \nbreak;\ncase 25:\n yy.getLogger().trace(\"node found ..\", $$[$0-2]); this.$ = { id: $$[$0-1], descr: $$[$0-1], type: yy.getType($$[$0-2], $$[$0]) }; \nbreak;\ncase 26:\n this.$ = { id: $$[$0], descr: $$[$0], type: yy.nodeType.DEFAULT }; \nbreak;\ncase 27:\n yy.getLogger().trace(\"node found ..\", $$[$0-3]); this.$ = { id: $$[$0-3], descr: $$[$0-1], type: yy.getType($$[$0-2], $$[$0]) }; \nbreak;\n}\n},\ntable: [{3:1,4:2,5:3,6:[1,5],8:$V0},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:$V0},{6:$V1,7:[1,10],9:9,12:11,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},o($V7,[2,3]),{1:[2,2]},o($V7,[2,4]),o($V7,[2,5]),{1:[2,6],6:$V1,12:21,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},{6:$V1,9:22,12:11,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},{6:$V8,7:$V9,10:23,11:$Va},o($Vb,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:$V5,22:$V6}),o($Vb,[2,18]),o($Vb,[2,19]),o($Vb,[2,20]),o($Vb,[2,21]),o($Vb,[2,23]),o($Vb,[2,24]),o($Vb,[2,26],{19:[1,30]}),{20:[1,31]},{6:$V8,7:$V9,10:32,11:$Va},{1:[2,7],6:$V1,12:21,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},o($Vc,[2,14],{7:$Vd,11:$Ve}),o($Vf,[2,8]),o($Vf,[2,9]),o($Vf,[2,10]),o($Vb,[2,15]),o($Vb,[2,16]),o($Vb,[2,17]),{20:[1,35]},{21:[1,36]},o($Vc,[2,13],{7:$Vd,11:$Ve}),o($Vf,[2,11]),o($Vf,[2,12]),{21:[1,37]},o($Vb,[2,25]),o($Vb,[2,27])],\ndefaultActions: {2:[2,1],6:[2,2]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\n\t// Pre-lexer code can go here\n\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:yy.getLogger().trace('Found comment',yy_.yytext); return 6;\nbreak;\ncase 1:return 8;\nbreak;\ncase 2: this.begin('CLASS'); \nbreak;\ncase 3: this.popState();return 16; \nbreak;\ncase 4: this.popState();\nbreak;\ncase 5: yy.getLogger().trace('Begin icon');this.begin('ICON'); \nbreak;\ncase 6:yy.getLogger().trace('SPACELINE');return 6                 /* skip all whitespace */    ;\nbreak;\ncase 7:return 7;\nbreak;\ncase 8: return 15; \nbreak;\ncase 9:yy.getLogger().trace('end icon');this.popState();\nbreak;\ncase 10: yy.getLogger().trace('Exploding node'); this.begin('NODE');return 19; \nbreak;\ncase 11: yy.getLogger().trace('Cloud'); this.begin('NODE');return 19; \nbreak;\ncase 12: yy.getLogger().trace('Explosion Bang'); this.begin('NODE');return 19; \nbreak;\ncase 13: yy.getLogger().trace('Cloud Bang'); this.begin('NODE');return 19; \nbreak;\ncase 14: this.begin('NODE');return 19; \nbreak;\ncase 15: this.begin('NODE');return 19; \nbreak;\ncase 16: this.begin('NODE');return 19; \nbreak;\ncase 17: this.begin('NODE');return 19; \nbreak;\ncase 18:return 13                 /* skip all whitespace */    ;\nbreak;\ncase 19:return 22;\nbreak;\ncase 20:return 11;\nbreak;\ncase 21: this.begin(\"NSTR2\");\nbreak;\ncase 22: return \"NODE_DESCR\";\nbreak;\ncase 23: this.popState();\nbreak;\ncase 24: yy.getLogger().trace('Starting NSTR');this.begin(\"NSTR\");\nbreak;\ncase 25: yy.getLogger().trace('description:', yy_.yytext); return \"NODE_DESCR\";\nbreak;\ncase 26:this.popState();\nbreak;\ncase 27:this.popState();yy.getLogger().trace('node end ))');return \"NODE_DEND\";\nbreak;\ncase 28:this.popState();yy.getLogger().trace('node end )');return \"NODE_DEND\";\nbreak;\ncase 29:this.popState();yy.getLogger().trace('node end ...',yy_.yytext);return \"NODE_DEND\";\nbreak;\ncase 30:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 31:this.popState();yy.getLogger().trace('node end (-');return \"NODE_DEND\";\nbreak;\ncase 32:this.popState();yy.getLogger().trace('node end (-');return \"NODE_DEND\";\nbreak;\ncase 33:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 34:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 35: yy.getLogger().trace('Long description:', yy_.yytext);   return 20;\nbreak;\ncase 36: yy.getLogger().trace('Long description:', yy_.yytext);   return 20;\nbreak;\n}\n},\nrules: [/^(?:\\s*%%.*)/i,/^(?:mindmap\\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\\n)/i,/^(?:::icon\\()/i,/^(?:[\\s]+[\\n])/i,/^(?:[\\n]+)/i,/^(?:[^\\)]+)/i,/^(?:\\))/i,/^(?:-\\))/i,/^(?:\\(-)/i,/^(?:\\)\\))/i,/^(?:\\))/i,/^(?:\\(\\()/i,/^(?:\\{\\{)/i,/^(?:\\()/i,/^(?:\\[)/i,/^(?:[\\s]+)/i,/^(?:[^\\(\\[\\n\\)\\{\\}]+)/i,/^(?:$)/i,/^(?:[\"][`])/i,/^(?:[^`\"]+)/i,/^(?:[`][\"])/i,/^(?:[\"])/i,/^(?:[^\"]+)/i,/^(?:[\"])/i,/^(?:[\\)]\\))/i,/^(?:[\\)])/i,/^(?:[\\]])/i,/^(?:\\}\\})/i,/^(?:\\(-)/i,/^(?:-\\))/i,/^(?:\\(\\()/i,/^(?:\\()/i,/^(?:[^\\)\\]\\(\\}]+)/i,/^(?:.+(?!\\(\\())/i],\nconditions: {\"CLASS\":{\"rules\":[3,4],\"inclusive\":false},\"ICON\":{\"rules\":[8,9],\"inclusive\":false},\"NSTR2\":{\"rules\":[22,23],\"inclusive\":false},\"NSTR\":{\"rules\":[25,26],\"inclusive\":false},\"NODE\":{\"rules\":[21,24,27,28,29,30,31,32,33,34,35,36],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { D3Element } from '../../types.js';\nimport { sanitizeText } from '../../diagrams/common/common.js';\nimport { log } from '../../logger.js';\nimport type { MindmapNode } from './mindmapTypes.js';\nimport defaultConfig from '../../defaultConfig.js';\n\nlet nodes: MindmapNode[] = [];\nlet cnt = 0;\nlet elements: Record<number, D3Element> = {};\n\nconst clear = () => {\n  nodes = [];\n  cnt = 0;\n  elements = {};\n};\n\nconst getParent = function (level: number) {\n  for (let i = nodes.length - 1; i >= 0; i--) {\n    if (nodes[i].level < level) {\n      return nodes[i];\n    }\n  }\n  // No parent found\n  return null;\n};\n\nconst getMindmap = () => {\n  return nodes.length > 0 ? nodes[0] : null;\n};\n\nconst addNode = (level: number, id: string, descr: string, type: number) => {\n  log.info('addNode', level, id, descr, type);\n  const conf = getConfig();\n  let padding: number = conf.mindmap?.padding ?? defaultConfig.mindmap.padding;\n  switch (type) {\n    case nodeType.ROUNDED_RECT:\n    case nodeType.RECT:\n    case nodeType.HEXAGON:\n      padding *= 2;\n  }\n\n  const node = {\n    id: cnt++,\n    nodeId: sanitizeText(id, conf),\n    level,\n    descr: sanitizeText(descr, conf),\n    type,\n    children: [],\n    width: conf.mindmap?.maxNodeWidth ?? defaultConfig.mindmap.maxNodeWidth,\n    padding,\n  } satisfies MindmapNode;\n\n  const parent = getParent(level);\n  if (parent) {\n    parent.children.push(node);\n    // Keep all nodes in the list\n    nodes.push(node);\n  } else {\n    if (nodes.length === 0) {\n      // First node, the root\n      nodes.push(node);\n    } else {\n      // Syntax error ... there can only bee one root\n      throw new Error(\n        'There can be only one root. No parent could be found for (\"' + node.descr + '\")'\n      );\n    }\n  }\n};\n\nconst nodeType = {\n  DEFAULT: 0,\n  NO_BORDER: 0,\n  ROUNDED_RECT: 1,\n  RECT: 2,\n  CIRCLE: 3,\n  CLOUD: 4,\n  BANG: 5,\n  HEXAGON: 6,\n};\n\nconst getType = (startStr: string, endStr: string): number => {\n  log.debug('In get type', startStr, endStr);\n  switch (startStr) {\n    case '[':\n      return nodeType.RECT;\n    case '(':\n      return endStr === ')' ? nodeType.ROUNDED_RECT : nodeType.CLOUD;\n    case '((':\n      return nodeType.CIRCLE;\n    case ')':\n      return nodeType.CLOUD;\n    case '))':\n      return nodeType.BANG;\n    case '{{':\n      return nodeType.HEXAGON;\n    default:\n      return nodeType.DEFAULT;\n  }\n};\n\nconst setElementForId = (id: number, element: D3Element) => {\n  elements[id] = element;\n};\n\nconst decorateNode = (decoration?: { class?: string; icon?: string }) => {\n  if (!decoration) {\n    return;\n  }\n  const config = getConfig();\n  const node = nodes[nodes.length - 1];\n  if (decoration.icon) {\n    node.icon = sanitizeText(decoration.icon, config);\n  }\n  if (decoration.class) {\n    node.class = sanitizeText(decoration.class, config);\n  }\n};\n\nconst type2Str = (type: number) => {\n  switch (type) {\n    case nodeType.DEFAULT:\n      return 'no-border';\n    case nodeType.RECT:\n      return 'rect';\n    case nodeType.ROUNDED_RECT:\n      return 'rounded-rect';\n    case nodeType.CIRCLE:\n      return 'circle';\n    case nodeType.CLOUD:\n      return 'cloud';\n    case nodeType.BANG:\n      return 'bang';\n    case nodeType.HEXAGON:\n      return 'hexgon'; // cspell: disable-line\n    default:\n      return 'no-border';\n  }\n};\n\n// Expose logger to grammar\nconst getLogger = () => log;\nconst getElementById = (id: number) => elements[id];\n\nconst db = {\n  clear,\n  addNode,\n  getMindmap,\n  nodeType,\n  getType,\n  setElementForId,\n  decorateNode,\n  type2Str,\n  getLogger,\n  getElementById,\n} as const;\n\nexport default db;\n", "import cytoscape from 'cytoscape';\n// @ts-expect-error No types available\nimport coseBilkent from 'cytoscape-cose-bilkent';\nimport { select } from 'd3';\nimport type { MermaidConfig } from '../../config.type.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DrawDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport type { D3Element } from '../../types.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport type { FilledMindMapNode, MindmapDB, MindmapNode } from './mindmapTypes.js';\nimport { drawNode, positionNode } from './svgDraw.js';\nimport defaultConfig from '../../defaultConfig.js';\n\n// Inject the layout algorithm into cytoscape\ncytoscape.use(coseBilkent);\n\nasync function drawNodes(\n  db: MindmapDB,\n  svg: D3Element,\n  mindmap: FilledMindMapNode,\n  section: number,\n  conf: MermaidConfig\n) {\n  await drawNode(db, svg, mindmap, section, conf);\n  if (mindmap.children) {\n    await Promise.all(\n      mindmap.children.map((child, index) =>\n        drawNodes(db, svg, child, section < 0 ? index : section, conf)\n      )\n    );\n  }\n}\n\ndeclare module 'cytoscape' {\n  interface EdgeSingular {\n    _private: {\n      bodyBounds: unknown;\n      rscratch: {\n        startX: number;\n        startY: number;\n        midX: number;\n        midY: number;\n        endX: number;\n        endY: number;\n      };\n    };\n  }\n}\n\nfunction drawEdges(edgesEl: D3Element, cy: cytoscape.Core) {\n  cy.edges().map((edge, id) => {\n    const data = edge.data();\n    if (edge[0]._private.bodyBounds) {\n      const bounds = edge[0]._private.rscratch;\n      log.trace('Edge: ', id, data);\n      edgesEl\n        .insert('path')\n        .attr(\n          'd',\n          `M ${bounds.startX},${bounds.startY} L ${bounds.midX},${bounds.midY} L${bounds.endX},${bounds.endY} `\n        )\n        .attr('class', 'edge section-edge-' + data.section + ' edge-depth-' + data.depth);\n    }\n  });\n}\n\nfunction addNodes(mindmap: MindmapNode, cy: cytoscape.Core, conf: MermaidConfig, level: number) {\n  cy.add({\n    group: 'nodes',\n    data: {\n      id: mindmap.id.toString(),\n      labelText: mindmap.descr,\n      height: mindmap.height,\n      width: mindmap.width,\n      level: level,\n      nodeId: mindmap.id,\n      padding: mindmap.padding,\n      type: mindmap.type,\n    },\n    position: {\n      x: mindmap.x!,\n      y: mindmap.y!,\n    },\n  });\n  if (mindmap.children) {\n    mindmap.children.forEach((child) => {\n      addNodes(child, cy, conf, level + 1);\n      cy.add({\n        group: 'edges',\n        data: {\n          id: `${mindmap.id}_${child.id}`,\n          source: mindmap.id,\n          target: child.id,\n          depth: level,\n          section: child.section,\n        },\n      });\n    });\n  }\n}\n\nfunction layoutMindmap(node: MindmapNode, conf: MermaidConfig): Promise<cytoscape.Core> {\n  return new Promise((resolve) => {\n    // Add temporary render element\n    const renderEl = select('body').append('div').attr('id', 'cy').attr('style', 'display:none');\n    const cy = cytoscape({\n      container: document.getElementById('cy'), // container to render in\n      style: [\n        {\n          selector: 'edge',\n          style: {\n            'curve-style': 'bezier',\n          },\n        },\n      ],\n    });\n    // Remove element after layout\n    renderEl.remove();\n    addNodes(node, cy, conf, 0);\n\n    // Make cytoscape care about the dimensions of the nodes\n    cy.nodes().forEach(function (n) {\n      n.layoutDimensions = () => {\n        const data = n.data();\n        return { w: data.width, h: data.height };\n      };\n    });\n\n    cy.layout({\n      name: 'cose-bilkent',\n      // @ts-ignore Types for cose-bilkent are not correct?\n      quality: 'proof',\n      styleEnabled: false,\n      animate: false,\n    }).run();\n    cy.ready((e) => {\n      log.info('Ready', e);\n      resolve(cy);\n    });\n  });\n}\n\nfunction positionNodes(db: MindmapDB, cy: cytoscape.Core) {\n  cy.nodes().map((node, id) => {\n    const data = node.data();\n    data.x = node.position().x;\n    data.y = node.position().y;\n    positionNode(db, data);\n    const el = db.getElementById(data.nodeId);\n    log.info('id:', id, 'Position: (', node.position().x, ', ', node.position().y, ')', data);\n    el.attr(\n      'transform',\n      `translate(${node.position().x - data.width / 2}, ${node.position().y - data.height / 2})`\n    );\n    el.attr('attr', `apa-${id})`);\n  });\n}\n\nexport const draw: DrawDefinition = async (text, id, _version, diagObj) => {\n  log.debug('Rendering mindmap diagram\\n' + text);\n\n  const db = diagObj.db as MindmapDB;\n  const mm = db.getMindmap();\n  if (!mm) {\n    return;\n  }\n\n  const conf = getConfig();\n  conf.htmlLabels = false;\n\n  const svg = selectSvgElement(id);\n\n  // Draw the graph and start with drawing the nodes without proper position\n  // this gives us the size of the nodes and we can set the positions later\n\n  const edgesElem = svg.append('g');\n  edgesElem.attr('class', 'mindmap-edges');\n  const nodesElem = svg.append('g');\n  nodesElem.attr('class', 'mindmap-nodes');\n  await drawNodes(db, nodesElem, mm as FilledMindMapNode, -1, conf);\n\n  // Next step is to layout the mindmap, giving each node a position\n\n  const cy = await layoutMindmap(mm, conf);\n\n  // After this we can draw, first the edges and the then nodes with the correct position\n  drawEdges(edgesElem, cy);\n  positionNodes(db, cy);\n\n  // Setup the view box and size of the svg element\n  setupGraphViewbox(\n    undefined,\n    svg,\n    conf.mindmap?.padding ?? defaultConfig.mindmap.padding,\n    conf.mindmap?.useMaxWidth ?? defaultConfig.mindmap.useMaxWidth\n  );\n};\n\nexport default {\n  draw,\n};\n", "import { createText } from '../../rendering-util/createText.js';\nimport type { FilledMindMapNode, MindmapDB } from './mindmapTypes.js';\nimport type { Point, D3Element } from '../../types.js';\nimport { parseFontSize } from '../../utils.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\nconst MAX_SECTIONS = 12;\n\ntype ShapeFunction = (\n  db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode,\n  section?: number\n) => void;\n\nconst defaultBkg: ShapeFunction = function (db, elem, node, section) {\n  const rd = 5;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${\n        node.width - 2 * rd\n      } q5,0 5,5 v${node.height - rd} H0 Z`\n    );\n\n  elem\n    .append('line')\n    .attr('class', 'node-line-' + section)\n    .attr('x1', 0)\n    .attr('y1', node.height)\n    .attr('x2', node.width)\n    .attr('y2', node.height);\n};\n\nconst rectBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('rect')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('height', node.height)\n    .attr('width', node.width);\n};\n\nconst cloudBkg: ShapeFunction = function (db, elem, node) {\n  const w = node.width;\n  const h = node.height;\n  const r1 = 0.15 * w;\n  const r2 = 0.25 * w;\n  const r3 = 0.35 * w;\n  const r4 = 0.2 * w;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 0 a${r1},${r1} 0 0,1 ${w * 0.25},${-1 * w * 0.1}\n      a${r3},${r3} 1 0,1 ${w * 0.4},${-1 * w * 0.1}\n      a${r2},${r2} 1 0,1 ${w * 0.35},${1 * w * 0.2}\n\n      a${r1},${r1} 1 0,1 ${w * 0.15},${1 * h * 0.35}\n      a${r4},${r4} 1 0,1 ${-1 * w * 0.15},${1 * h * 0.65}\n\n      a${r2},${r1} 1 0,1 ${-1 * w * 0.25},${w * 0.15}\n      a${r3},${r3} 1 0,1 ${-1 * w * 0.5},${0}\n      a${r1},${r1} 1 0,1 ${-1 * w * 0.25},${-1 * w * 0.15}\n\n      a${r1},${r1} 1 0,1 ${-1 * w * 0.1},${-1 * h * 0.35}\n      a${r4},${r4} 1 0,1 ${w * 0.1},${-1 * h * 0.65}\n\n    H0 V0 Z`\n    );\n};\n\nconst bangBkg: ShapeFunction = function (db, elem, node) {\n  const w = node.width;\n  const h = node.height;\n  const r = 0.15 * w;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 0 a${r},${r} 1 0,0 ${w * 0.25},${-1 * h * 0.1}\n      a${r},${r} 1 0,0 ${w * 0.25},${0}\n      a${r},${r} 1 0,0 ${w * 0.25},${0}\n      a${r},${r} 1 0,0 ${w * 0.25},${1 * h * 0.1}\n\n      a${r},${r} 1 0,0 ${w * 0.15},${1 * h * 0.33}\n      a${r * 0.8},${r * 0.8} 1 0,0 ${0},${1 * h * 0.34}\n      a${r},${r} 1 0,0 ${-1 * w * 0.15},${1 * h * 0.33}\n\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${h * 0.15}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${0}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${0}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${-1 * h * 0.15}\n\n      a${r},${r} 1 0,0 ${-1 * w * 0.1},${-1 * h * 0.33}\n      a${r * 0.8},${r * 0.8} 1 0,0 ${0},${-1 * h * 0.34}\n      a${r},${r} 1 0,0 ${w * 0.1},${-1 * h * 0.33}\n\n    H0 V0 Z`\n    );\n};\n\nconst circleBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('circle')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('r', node.width / 2);\n};\n\nfunction insertPolygonShape(\n  parent: D3Element,\n  w: number,\n  h: number,\n  points: Point[],\n  node: FilledMindMapNode\n) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('transform', 'translate(' + (node.width - w) / 2 + ', ' + h + ')');\n}\n\nconst hexagonBkg: ShapeFunction = function (\n  _db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode\n) {\n  const h = node.height;\n  const f = 4;\n  const m = h / f;\n  const w = node.width - node.padding + 2 * m;\n  const points: Point[] = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n  insertPolygonShape(elem, w, h, points, node);\n};\n\nconst roundedRectBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('rect')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('height', node.height)\n    .attr('rx', node.padding)\n    .attr('ry', node.padding)\n    .attr('width', node.width);\n};\n\n/**\n * @param db - The database\n * @param elem - The D3 dom element in which the node is to be added\n * @param node - The node to be added\n * @param fullSection - ?\n * @param conf - The configuration object\n * @returns The height nodes dom element\n */\nexport const drawNode = async function (\n  db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode,\n  fullSection: number,\n  conf: MermaidConfig\n): Promise<number> {\n  const htmlLabels = conf.htmlLabels;\n  const section = fullSection % (MAX_SECTIONS - 1);\n  const nodeElem = elem.append('g');\n  node.section = section;\n  let sectionClass = 'section-' + section;\n  if (section < 0) {\n    sectionClass += ' section-root';\n  }\n  nodeElem.attr('class', (node.class ? node.class + ' ' : '') + 'mindmap-node ' + sectionClass);\n  const bkgElem = nodeElem.append('g');\n\n  // Create the wrapped text element\n  const textElem = nodeElem.append('g');\n  const description = node.descr.replace(/(<br\\/*>)/g, '\\n');\n  await createText(\n    textElem,\n    description,\n    {\n      useHtmlLabels: htmlLabels,\n      width: node.width,\n      classes: 'mindmap-node-label',\n    },\n    conf\n  );\n\n  if (!htmlLabels) {\n    textElem\n      .attr('dy', '1em')\n      .attr('alignment-baseline', 'middle')\n      .attr('dominant-baseline', 'middle')\n      .attr('text-anchor', 'middle');\n  }\n  const bbox = textElem.node().getBBox();\n  const [fontSize] = parseFontSize(conf.fontSize);\n  node.height = bbox.height + fontSize! * 1.1 * 0.5 + node.padding;\n  node.width = bbox.width + 2 * node.padding;\n  if (node.icon) {\n    if (node.type === db.nodeType.CIRCLE) {\n      node.height += 50;\n      node.width += 50;\n      const icon = nodeElem\n        .append('foreignObject')\n        .attr('height', '50px')\n        .attr('width', node.width)\n        .attr('style', 'text-align: center;');\n      icon\n        .append('div')\n        .attr('class', 'icon-container')\n        .append('i')\n        .attr('class', 'node-icon-' + section + ' ' + node.icon);\n      textElem.attr(\n        'transform',\n        'translate(' + node.width / 2 + ', ' + (node.height / 2 - 1.5 * node.padding) + ')'\n      );\n    } else {\n      node.width += 50;\n      const orgHeight = node.height;\n      node.height = Math.max(orgHeight, 60);\n      const heightDiff = Math.abs(node.height - orgHeight);\n      const icon = nodeElem\n        .append('foreignObject')\n        .attr('width', '60px')\n        .attr('height', node.height)\n        .attr('style', 'text-align: center;margin-top:' + heightDiff / 2 + 'px;');\n\n      icon\n        .append('div')\n        .attr('class', 'icon-container')\n        .append('i')\n        .attr('class', 'node-icon-' + section + ' ' + node.icon);\n      textElem.attr(\n        'transform',\n        'translate(' + (25 + node.width / 2) + ', ' + (heightDiff / 2 + node.padding / 2) + ')'\n      );\n    }\n  } else {\n    if (!htmlLabels) {\n      const dx = node.width / 2;\n      const dy = node.padding / 2;\n      textElem.attr('transform', 'translate(' + dx + ', ' + dy + ')');\n      // textElem.attr('transform', 'translate(' + node.width / 2 + ', ' + node.padding / 2 + ')');\n    } else {\n      const dx = (node.width - bbox.width) / 2;\n      const dy = (node.height - bbox.height) / 2;\n      textElem.attr('transform', 'translate(' + dx + ', ' + dy + ')');\n    }\n  }\n\n  switch (node.type) {\n    case db.nodeType.DEFAULT:\n      defaultBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.ROUNDED_RECT:\n      roundedRectBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.RECT:\n      rectBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.CIRCLE:\n      bkgElem.attr('transform', 'translate(' + node.width / 2 + ', ' + +node.height / 2 + ')');\n      circleBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.CLOUD:\n      cloudBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.BANG:\n      bangBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.HEXAGON:\n      hexagonBkg(db, bkgElem, node, section);\n      break;\n  }\n\n  db.setElementForId(node.id, nodeElem);\n  return node.height;\n};\n\nexport const positionNode = function (db: MindmapDB, node: FilledMindMapNode) {\n  const nodeElem = db.getElementById(node.id);\n\n  const x = node.x || 0;\n  const y = node.y || 0;\n  // Position the node to its coordinate\n  nodeElem.attr('transform', 'translate(' + x + ',' + y + ')');\n};\n", "// @ts-expect-error Incorrect khroma types\nimport { darken, lighten, isDark } from 'khroma';\nimport type { DiagramStylesProvider } from '../../diagram-api/types.js';\n\nconst genSections: DiagramStylesProvider = (options) => {\n  let sections = '';\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options['lineColor' + i] = options['lineColor' + i] || options['cScaleInv' + i];\n    if (isDark(options['lineColor' + i])) {\n      options['lineColor' + i] = lighten(options['lineColor' + i], 20);\n    } else {\n      options['lineColor' + i] = darken(options['lineColor' + i], 20);\n    }\n  }\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = '' + (17 - 3 * i);\n    sections += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${\n      i - 1\n    } polygon, .section-${i - 1} path  {\n      fill: ${options['cScale' + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options['cScaleLabel' + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options['cScaleLabel' + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options['cScale' + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options['cScaleInv' + i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections;\n};\n\n// TODO: These options seem incorrect.\nconst getStyles: DiagramStylesProvider = (options) =>\n  `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .mindmap-node-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`;\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport parser from './parser/mindmap.jison';\nimport db from './mindmapDb.js';\nimport renderer from './mindmapRenderer.js';\nimport styles from './styles.js';\nimport type { DiagramDefinition } from '../../diagram-api/types.js';\n\nexport const diagram: DiagramDefinition = {\n  db,\n  renderer,\n  parser,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AACzR,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,WAAU,GAAE,cAAa,GAAE,aAAY,GAAE,MAAK,GAAE,WAAU,GAAE,YAAW,GAAE,QAAO,IAAG,OAAM,IAAG,aAAY,IAAG,aAAY,IAAG,QAAO,IAAG,QAAO,IAAG,SAAQ,IAAG,cAAa,IAAG,iBAAgB,IAAG,eAAc,IAAG,cAAa,IAAG,aAAY,IAAG,WAAU,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IAC5S,YAAY,EAAC,GAAE,SAAQ,GAAE,aAAY,GAAE,MAAK,GAAE,WAAU,IAAG,OAAM,IAAG,aAAY,IAAG,QAAO,IAAG,SAAQ,IAAG,eAAc,IAAG,cAAa,IAAG,aAAY,IAAG,UAAS;AAAA,IACjK,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACpM,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AAAA,QAAG,KAAK;AACZ,iBAAO;AACR;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,UAAU;AAC/B;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,WAAW;AAChC;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,WAAW;AAChC;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,YAAY;AACjC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,KAAK,UAAS,GAAG,EAAE,EAAE,EAAE;AAAE,aAAG,QAAQ,GAAG,KAAG,CAAC,EAAE,QAAQ,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI;AACzG;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,UAAS,GAAG,EAAE,CAAC;AAAE,aAAG,aAAa,EAAC,MAAM,GAAG,EAAE,EAAC,CAAC;AACrE;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,aAAG,aAAa,EAAC,OAAO,GAAG,EAAE,EAAC,CAAC;AAChC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,WAAW;AACjC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,UAAS,GAAG,EAAE,EAAE,EAAE;AAAE,aAAG,QAAQ,GAAG,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI;AAC5F;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,EAAC,MAAM,GAAG,EAAE,EAAC,CAAC;AAC/B;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,iBAAiB,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAI,EAAE,IAAI,GAAG,KAAG,CAAC,GAAG,OAAO,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAC/H;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAE,IAAI,GAAG,EAAE,GAAG,OAAO,GAAG,EAAE,GAAG,MAAM,GAAG,SAAS,QAAQ;AACjE;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,iBAAiB,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAI,EAAE,IAAI,GAAG,KAAG,CAAC,GAAG,OAAO,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAC/H;AAAA,MACA;AAAA,IACA,GAhDe;AAAA,IAiDf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,IAAG,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,IAAG,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAC54B,gBAAgB,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,EAAC;AAAA,IAChC,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAG7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAE,eAAG,UAAU,EAAE,MAAM,iBAAgB,IAAI,MAAM;AAAG,mBAAO;AAChE;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,OAAO;AAC1B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,mBAAO;AAC/B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,eAAG,UAAU,EAAE,MAAM,YAAY;AAAE,iBAAK,MAAM,MAAM;AAC5D;AAAA,UACA,KAAK;AAAE,eAAG,UAAU,EAAE,MAAM,WAAW;AAAE,mBAAO;AAChD;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAE,eAAG,UAAU,EAAE,MAAM,UAAU;AAAE,iBAAK,SAAS;AACtD;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,gBAAgB;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AAC3E;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,OAAO;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AAClE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,gBAAgB;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AAC3E;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,YAAY;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACvE;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAC3B;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,eAAe;AAAE,iBAAK,MAAM,MAAM;AAChE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,gBAAgB,IAAI,MAAM;AAAG,mBAAO;AAClE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,YAAY;AAAE,mBAAO;AAClE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,gBAAe,IAAI,MAAM;AAAE,mBAAO;AAC/E;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,qBAAqB,IAAI,MAAM;AAAK,mBAAO;AACzE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,qBAAqB,IAAI,MAAM;AAAK,mBAAO;AACzE;AAAA,QACA;AAAA,MACA,GAhFe;AAAA,MAiFf,OAAO,CAAC,iBAAgB,mBAAkB,aAAY,YAAW,YAAW,kBAAiB,mBAAkB,eAAc,gBAAe,YAAW,aAAY,aAAY,cAAa,YAAW,cAAa,cAAa,YAAW,YAAW,eAAc,0BAAyB,WAAU,gBAAe,gBAAe,gBAAe,aAAY,eAAc,aAAY,gBAAe,cAAa,cAAa,cAAa,aAAY,aAAY,cAAa,YAAW,sBAAqB,kBAAkB;AAAA,MACxgB,YAAY,EAAC,SAAQ,EAAC,SAAQ,CAAC,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IACnV;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,kBAAQ;;;ACtrBhB,IAAI,QAAuB,CAAC;AAC5B,IAAI,MAAM;AACV,IAAI,WAAsC,CAAC;AAE3C,IAAM,QAAQ,6BAAM;AAClB,UAAQ,CAAC;AACT,QAAM;AACN,aAAW,CAAC;AACd,GAJc;AAMd,IAAM,YAAY,gCAAU,OAAe;AACzC,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,QAAI,MAAM,CAAC,EAAE,QAAQ,OAAO;AAC1B,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT,GARkB;AAUlB,IAAM,aAAa,6BAAM;AACvB,SAAO,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AACvC,GAFmB;AAInB,IAAM,UAAU,wBAAC,OAAe,IAAY,OAAe,SAAiB;AAC1E,MAAI,KAAK,WAAW,OAAO,IAAI,OAAO,IAAI;AAC1C,QAAM,OAAO,UAAU;AACvB,MAAI,UAAkB,KAAK,SAAS,WAAW,sBAAc,QAAQ;AACrE,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AAAA,IACd,KAAK,SAAS;AAAA,IACd,KAAK,SAAS;AACZ,iBAAW;AAAA,EACf;AAEA,QAAM,OAAO;AAAA,IACX,IAAI;AAAA,IACJ,QAAQ,aAAa,IAAI,IAAI;AAAA,IAC7B;AAAA,IACA,OAAO,aAAa,OAAO,IAAI;AAAA,IAC/B;AAAA,IACA,UAAU,CAAC;AAAA,IACX,OAAO,KAAK,SAAS,gBAAgB,sBAAc,QAAQ;AAAA,IAC3D;AAAA,EACF;AAEA,QAAM,SAAS,UAAU,KAAK;AAC9B,MAAI,QAAQ;AACV,WAAO,SAAS,KAAK,IAAI;AAEzB,UAAM,KAAK,IAAI;AAAA,EACjB,OAAO;AACL,QAAI,MAAM,WAAW,GAAG;AAEtB,YAAM,KAAK,IAAI;AAAA,IACjB,OAAO;AAEL,YAAM,IAAI;AAAA,QACR,gEAAgE,KAAK,QAAQ;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AACF,GAtCgB;AAwChB,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AACX;AAEA,IAAM,UAAU,wBAAC,UAAkB,WAA2B;AAC5D,MAAI,MAAM,eAAe,UAAU,MAAM;AACzC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,WAAW,MAAM,SAAS,eAAe,SAAS;AAAA,IAC3D,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB;AACE,aAAO,SAAS;AAAA,EACpB;AACF,GAlBgB;AAoBhB,IAAM,kBAAkB,wBAAC,IAAY,YAAuB;AAC1D,WAAS,EAAE,IAAI;AACjB,GAFwB;AAIxB,IAAM,eAAe,wBAAC,eAAmD;AACvE,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AACA,QAAM,SAAS,UAAU;AACzB,QAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AACnC,MAAI,WAAW,MAAM;AACnB,SAAK,OAAO,aAAa,WAAW,MAAM,MAAM;AAAA,EAClD;AACA,MAAI,WAAW,OAAO;AACpB,SAAK,QAAQ,aAAa,WAAW,OAAO,MAAM;AAAA,EACpD;AACF,GAZqB;AAcrB,IAAM,WAAW,wBAAC,SAAiB;AACjC,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF,GAnBiB;AAsBjB,IAAM,YAAY,6BAAM,KAAN;AAClB,IAAM,iBAAiB,wBAAC,OAAe,SAAS,EAAE,GAA3B;AAEvB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,oBAAQ;;;AC9Jf,OAAO,eAAe;AAEtB,OAAO,iBAAiB;AACxB,SAAS,cAAc;;;ACGvB,IAAM,eAAe;AASrB,IAAM,aAA4B,gCAAUE,KAAI,MAAM,MAAM,SAAS;AACnE,QAAM,KAAK;AACX,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD;AAAA,IACC;AAAA,IACA,MAAM,KAAK,SAAS,EAAE,KAAK,CAAC,KAAK,SAAS,IAAI,EAAE,gBAC9C,KAAK,QAAQ,IAAI,EACnB,cAAc,KAAK,SAAS,EAAE;AAAA,EAChC;AAEF,OACG,OAAO,MAAM,EACb,KAAK,SAAS,eAAe,OAAO,EACpC,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,KAAK,MAAM,EACtB,KAAK,MAAM,KAAK,KAAK,EACrB,KAAK,MAAM,KAAK,MAAM;AAC3B,GApBkC;AAsBlC,IAAM,UAAyB,gCAAUA,KAAI,MAAM,MAAM;AACvD,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD,KAAK,UAAU,KAAK,MAAM,EAC1B,KAAK,SAAS,KAAK,KAAK;AAC7B,GAP+B;AAS/B,IAAM,WAA0B,gCAAUA,KAAI,MAAM,MAAM;AACxD,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,MAAM;AACjB,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD;AAAA,IACC;AAAA,IACA,SAAS,EAAE,IAAI,EAAE,UAAU,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,SAChD,EAAE,IAAI,EAAE,UAAU,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,SACzC,EAAE,IAAI,EAAE,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA;AAAA,SAEzC,EAAE,IAAI,EAAE,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SAC1C,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA;AAAA,SAE/C,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SAC3C,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,GAAG,IAAI,CAAC;AAAA,SACnC,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA,SAEhD,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA,SAC/C,EAAE,IAAI,EAAE,UAAU,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA;AAAA,EAG/C;AACJ,GA7BgC;AA+BhC,IAAM,UAAyB,gCAAUA,KAAI,MAAM,MAAM;AACvD,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,OAAO;AACjB,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD;AAAA,IACC;AAAA,IACA,SAAS,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,SAC9C,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,SAC7B,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,SAC7B,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA;AAAA,SAEvC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SACxC,IAAI,GAAG,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI,IAAI,IAAI;AAAA,SAC7C,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA;AAAA,SAE7C,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SACzC,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,SAClC,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,SAClC,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA,SAE9C,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA,SAC7C,IAAI,GAAG,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,KAAK,IAAI,IAAI;AAAA,SAC9C,CAAC,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA;AAAA,EAG7C;AACJ,GA9B+B;AAgC/B,IAAM,YAA2B,gCAAUA,KAAI,MAAM,MAAM;AACzD,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD,KAAK,KAAK,KAAK,QAAQ,CAAC;AAC7B,GANiC;AAQjC,SAAS,mBACP,QACA,GACA,GACA,QACA,MACA;AACA,SAAO,OACJ,OAAO,WAAW,cAAc,EAChC;AAAA,IACC;AAAA,IACA,OACG,IAAI,SAAU,GAAG;AAChB,aAAO,EAAE,IAAI,MAAM,EAAE;AAAA,IACvB,CAAC,EACA,KAAK,GAAG;AAAA,EACb,EACC,KAAK,aAAa,gBAAgB,KAAK,QAAQ,KAAK,IAAI,OAAO,IAAI,GAAG;AAC3E;AAlBS;AAoBT,IAAM,aAA4B,gCAChC,KACA,MACA,MACA;AACA,QAAM,IAAI,KAAK;AACf,QAAM,IAAI;AACV,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,QAAQ,KAAK,UAAU,IAAI;AAC1C,QAAM,SAAkB;AAAA,IACtB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,EACpB;AACA,qBAAmB,MAAM,GAAG,GAAG,QAAQ,IAAI;AAC7C,GAlBkC;AAoBlC,IAAM,iBAAgC,gCAAUA,KAAI,MAAM,MAAM;AAC9D,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD,KAAK,UAAU,KAAK,MAAM,EAC1B,KAAK,MAAM,KAAK,OAAO,EACvB,KAAK,MAAM,KAAK,OAAO,EACvB,KAAK,SAAS,KAAK,KAAK;AAC7B,GATsC;AAmB/B,IAAM,WAAW,sCACtBA,KACA,MACA,MACA,aACA,MACiB;AACjB,QAAM,aAAa,KAAK;AACxB,QAAM,UAAU,eAAe,eAAe;AAC9C,QAAM,WAAW,KAAK,OAAO,GAAG;AAChC,OAAK,UAAU;AACf,MAAI,eAAe,aAAa;AAChC,MAAI,UAAU,GAAG;AACf,oBAAgB;AAAA,EAClB;AACA,WAAS,KAAK,UAAU,KAAK,QAAQ,KAAK,QAAQ,MAAM,MAAM,kBAAkB,YAAY;AAC5F,QAAM,UAAU,SAAS,OAAO,GAAG;AAGnC,QAAM,WAAW,SAAS,OAAO,GAAG;AACpC,QAAM,cAAc,KAAK,MAAM,QAAQ,cAAc,IAAI;AACzD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,MACE,eAAe;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAEA,MAAI,CAAC,YAAY;AACf,aACG,KAAK,MAAM,KAAK,EAChB,KAAK,sBAAsB,QAAQ,EACnC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ;AAAA,EACjC;AACA,QAAM,OAAO,SAAS,KAAK,EAAE,QAAQ;AACrC,QAAM,CAAC,QAAQ,IAAI,cAAc,KAAK,QAAQ;AAC9C,OAAK,SAAS,KAAK,SAAS,WAAY,MAAM,MAAM,KAAK;AACzD,OAAK,QAAQ,KAAK,QAAQ,IAAI,KAAK;AACnC,MAAI,KAAK,MAAM;AACb,QAAI,KAAK,SAASA,IAAG,SAAS,QAAQ;AACpC,WAAK,UAAU;AACf,WAAK,SAAS;AACd,YAAM,OAAO,SACV,OAAO,eAAe,EACtB,KAAK,UAAU,MAAM,EACrB,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,SAAS,qBAAqB;AACtC,WACG,OAAO,KAAK,EACZ,KAAK,SAAS,gBAAgB,EAC9B,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,UAAU,MAAM,KAAK,IAAI;AACzD,eAAS;AAAA,QACP;AAAA,QACA,eAAe,KAAK,QAAQ,IAAI,QAAQ,KAAK,SAAS,IAAI,MAAM,KAAK,WAAW;AAAA,MAClF;AAAA,IACF,OAAO;AACL,WAAK,SAAS;AACd,YAAM,YAAY,KAAK;AACvB,WAAK,SAAS,KAAK,IAAI,WAAW,EAAE;AACpC,YAAM,aAAa,KAAK,IAAI,KAAK,SAAS,SAAS;AACnD,YAAM,OAAO,SACV,OAAO,eAAe,EACtB,KAAK,SAAS,MAAM,EACpB,KAAK,UAAU,KAAK,MAAM,EAC1B,KAAK,SAAS,mCAAmC,aAAa,IAAI,KAAK;AAE1E,WACG,OAAO,KAAK,EACZ,KAAK,SAAS,gBAAgB,EAC9B,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,UAAU,MAAM,KAAK,IAAI;AACzD,eAAS;AAAA,QACP;AAAA,QACA,gBAAgB,KAAK,KAAK,QAAQ,KAAK,QAAQ,aAAa,IAAI,KAAK,UAAU,KAAK;AAAA,MACtF;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,CAAC,YAAY;AACf,YAAM,KAAK,KAAK,QAAQ;AACxB,YAAM,KAAK,KAAK,UAAU;AAC1B,eAAS,KAAK,aAAa,eAAe,KAAK,OAAO,KAAK,GAAG;AAAA,IAEhE,OAAO;AACL,YAAM,MAAM,KAAK,QAAQ,KAAK,SAAS;AACvC,YAAM,MAAM,KAAK,SAAS,KAAK,UAAU;AACzC,eAAS,KAAK,aAAa,eAAe,KAAK,OAAO,KAAK,GAAG;AAAA,IAChE;AAAA,EACF;AAEA,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAKA,IAAG,SAAS;AACf,iBAAWA,KAAI,SAAS,MAAM,OAAO;AACrC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,qBAAeA,KAAI,SAAS,MAAM,OAAO;AACzC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,cAAQA,KAAI,SAAS,MAAM,OAAO;AAClC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,cAAQ,KAAK,aAAa,eAAe,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AACvF,gBAAUA,KAAI,SAAS,MAAM,OAAO;AACpC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,eAASA,KAAI,SAAS,MAAM,OAAO;AACnC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,cAAQA,KAAI,SAAS,MAAM,OAAO;AAClC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,iBAAWA,KAAI,SAAS,MAAM,OAAO;AACrC;AAAA,EACJ;AAEA,EAAAA,IAAG,gBAAgB,KAAK,IAAI,QAAQ;AACpC,SAAO,KAAK;AACd,GA1HwB;AA4HjB,IAAM,eAAe,gCAAUA,KAAe,MAAyB;AAC5E,QAAM,WAAWA,IAAG,eAAe,KAAK,EAAE;AAE1C,QAAM,IAAI,KAAK,KAAK;AACpB,QAAM,IAAI,KAAK,KAAK;AAEpB,WAAS,KAAK,aAAa,eAAe,IAAI,MAAM,IAAI,GAAG;AAC7D,GAP4B;;;AD5R5B,UAAU,IAAI,WAAW;AAEzB,eAAe,UACbC,KACA,KACA,SACA,SACA,MACA;AACA,QAAM,SAASA,KAAI,KAAK,SAAS,SAAS,IAAI;AAC9C,MAAI,QAAQ,UAAU;AACpB,UAAM,QAAQ;AAAA,MACZ,QAAQ,SAAS;AAAA,QAAI,CAAC,OAAO,UAC3B,UAAUA,KAAI,KAAK,OAAO,UAAU,IAAI,QAAQ,SAAS,IAAI;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACF;AAfe;AAiCf,SAAS,UAAU,SAAoB,IAAoB;AACzD,KAAG,MAAM,EAAE,IAAI,CAAC,MAAM,OAAO;AAC3B,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,KAAK,CAAC,EAAE,SAAS,YAAY;AAC/B,YAAM,SAAS,KAAK,CAAC,EAAE,SAAS;AAChC,UAAI,MAAM,UAAU,IAAI,IAAI;AAC5B,cACG,OAAO,MAAM,EACb;AAAA,QACC;AAAA,QACA,KAAK,OAAO,MAAM,IAAI,OAAO,MAAM,MAAM,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MACpG,EACC,KAAK,SAAS,uBAAuB,KAAK,UAAU,iBAAiB,KAAK,KAAK;AAAA,IACpF;AAAA,EACF,CAAC;AACH;AAfS;AAiBT,SAAS,SAAS,SAAsB,IAAoB,MAAqB,OAAe;AAC9F,KAAG,IAAI;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,MACJ,IAAI,QAAQ,GAAG,SAAS;AAAA,MACxB,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,QAAQ,QAAQ;AAAA,MAChB,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ;AAAA,IAChB;AAAA,IACA,UAAU;AAAA,MACR,GAAG,QAAQ;AAAA,MACX,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,UAAU;AACpB,YAAQ,SAAS,QAAQ,CAAC,UAAU;AAClC,eAAS,OAAO,IAAI,MAAM,QAAQ,CAAC;AACnC,SAAG,IAAI;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,IAAI,GAAG,QAAQ,EAAE,IAAI,MAAM,EAAE;AAAA,UAC7B,QAAQ,QAAQ;AAAA,UAChB,QAAQ,MAAM;AAAA,UACd,OAAO;AAAA,UACP,SAAS,MAAM;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAjCS;AAmCT,SAAS,cAAc,MAAmB,MAA8C;AACtF,SAAO,IAAI,QAAQ,CAAC,YAAY;AAE9B,UAAM,WAAW,OAAO,MAAM,EAAE,OAAO,KAAK,EAAE,KAAK,MAAM,IAAI,EAAE,KAAK,SAAS,cAAc;AAC3F,UAAM,KAAK,UAAU;AAAA,MACnB,WAAW,SAAS,eAAe,IAAI;AAAA;AAAA,MACvC,OAAO;AAAA,QACL;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA,YACL,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,aAAS,OAAO;AAChB,aAAS,MAAM,IAAI,MAAM,CAAC;AAG1B,OAAG,MAAM,EAAE,QAAQ,SAAU,GAAG;AAC9B,QAAE,mBAAmB,MAAM;AACzB,cAAM,OAAO,EAAE,KAAK;AACpB,eAAO,EAAE,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO;AAAA,MACzC;AAAA,IACF,CAAC;AAED,OAAG,OAAO;AAAA,MACR,MAAM;AAAA;AAAA,MAEN,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,IACX,CAAC,EAAE,IAAI;AACP,OAAG,MAAM,CAAC,MAAM;AACd,UAAI,KAAK,SAAS,CAAC;AACnB,cAAQ,EAAE;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAvCS;AAyCT,SAAS,cAAcA,KAAe,IAAoB;AACxD,KAAG,MAAM,EAAE,IAAI,CAAC,MAAM,OAAO;AAC3B,UAAM,OAAO,KAAK,KAAK;AACvB,SAAK,IAAI,KAAK,SAAS,EAAE;AACzB,SAAK,IAAI,KAAK,SAAS,EAAE;AACzB,iBAAaA,KAAI,IAAI;AACrB,UAAM,KAAKA,IAAG,eAAe,KAAK,MAAM;AACxC,QAAI,KAAK,OAAO,IAAI,eAAe,KAAK,SAAS,EAAE,GAAG,MAAM,KAAK,SAAS,EAAE,GAAG,KAAK,IAAI;AACxF,OAAG;AAAA,MACD;AAAA,MACA,aAAa,KAAK,SAAS,EAAE,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE,IAAI,KAAK,SAAS,CAAC;AAAA,IACzF;AACA,OAAG,KAAK,QAAQ,OAAO,EAAE,GAAG;AAAA,EAC9B,CAAC;AACH;AAdS;AAgBF,IAAM,OAAuB,8BAAO,MAAM,IAAI,UAAU,YAAY;AACzE,MAAI,MAAM,gCAAgC,IAAI;AAE9C,QAAMA,MAAK,QAAQ;AACnB,QAAM,KAAKA,IAAG,WAAW;AACzB,MAAI,CAAC,IAAI;AACP;AAAA,EACF;AAEA,QAAM,OAAO,UAAU;AACvB,OAAK,aAAa;AAElB,QAAM,MAAM,iBAAiB,EAAE;AAK/B,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,YAAU,KAAK,SAAS,eAAe;AACvC,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,YAAU,KAAK,SAAS,eAAe;AACvC,QAAM,UAAUA,KAAI,WAAW,IAAyB,IAAI,IAAI;AAIhE,QAAM,KAAK,MAAM,cAAc,IAAI,IAAI;AAGvC,YAAU,WAAW,EAAE;AACvB,gBAAcA,KAAI,EAAE;AAGpB;AAAA,IACE;AAAA,IACA;AAAA,IACA,KAAK,SAAS,WAAW,sBAAc,QAAQ;AAAA,IAC/C,KAAK,SAAS,eAAe,sBAAc,QAAQ;AAAA,EACrD;AACF,GAtCoC;AAwCpC,IAAO,0BAAQ;AAAA,EACb;AACF;;;AEzMA,SAAS,QAAQ,SAAS,cAAc;AAGxC,IAAM,cAAqC,wBAAC,YAAY;AACtD,MAAI,WAAW;AAEf,WAAS,IAAI,GAAG,IAAI,QAAQ,mBAAmB,KAAK;AAClD,YAAQ,cAAc,CAAC,IAAI,QAAQ,cAAc,CAAC,KAAK,QAAQ,cAAc,CAAC;AAC9E,QAAI,OAAO,QAAQ,cAAc,CAAC,CAAC,GAAG;AACpC,cAAQ,cAAc,CAAC,IAAI,QAAQ,QAAQ,cAAc,CAAC,GAAG,EAAE;AAAA,IACjE,OAAO;AACL,cAAQ,cAAc,CAAC,IAAI,OAAO,QAAQ,cAAc,CAAC,GAAG,EAAE;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,QAAQ,mBAAmB,KAAK;AAClD,UAAM,KAAK,MAAM,KAAK,IAAI;AAC1B,gBAAY;AAAA,eACD,IAAI,CAAC,mBAAmB,IAAI,CAAC,mBAAmB,IAAI,CAAC,qBAC9D,IAAI,CACN,sBAAsB,IAAI,CAAC;AAAA,cACjB,QAAQ,WAAW,CAAC,CAAC;AAAA;AAAA,eAEpB,IAAI,CAAC;AAAA,aACP,QAAQ,gBAAgB,CAAC,CAAC;AAAA;AAAA,iBAEtB,IAAI,CAAC;AAAA;AAAA,eAEP,QAAQ,gBAAgB,CAAC,CAAC;AAAA;AAAA,oBAErB,IAAI,CAAC;AAAA,gBACT,QAAQ,WAAW,CAAC,CAAC;AAAA;AAAA,kBAEnB,IAAI,CAAC;AAAA,sBACD,EAAE;AAAA;AAAA,eAET,IAAI,CAAC;AAAA,gBACJ,QAAQ,cAAc,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtC;AACA,SAAO;AACT,GA/C2C;AAkD3C,IAAM,YAAmC,wBAAC,YACxC;AAAA;AAAA;AAAA;AAAA,IAIE,YAAY,OAAO,CAAC;AAAA;AAAA,YAEZ,QAAQ,IAAI;AAAA;AAAA;AAAA,YAGZ,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAVM;AA6BzC,IAAO,iBAAQ;;;AC5ER,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["o", "parser", "lexer", "db", "db"]}