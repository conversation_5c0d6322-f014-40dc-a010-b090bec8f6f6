#!/usr/bin/env node

/**
 * 批量修复 Element Plus 图标脚本
 * 自动替换所有 Brain 和 DataAnalysis 图标
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 图标替换映射
const ICON_REPLACEMENTS = {
  'Brain': 'Cpu',
  'DataAnalysis': 'Grid',
  'PlayCircle': 'VideoPlay',
  'Shield': 'Lock',
  'Play': 'VideoPlay',
  'Lightbulb': 'Star'
}

// 修复文件中的图标
function fixIconsInFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return { fixed: false, changes: [] }
  }

  let content = fs.readFileSync(filePath, 'utf8')
  let originalContent = content
  const changes = []

  // 修复导入语句中的图标
  Object.entries(ICON_REPLACEMENTS).forEach(([oldIcon, newIcon]) => {
    // 匹配导入语句中的图标
    const importRegex = new RegExp(`(import\\s*{[^}]*?)\\b${oldIcon}\\b([^}]*}\\s*from\\s*['"]@element-plus/icons-vue['"])`, 'g')
    const newContent = content.replace(importRegex, (match, before, after) => {
      changes.push(`导入语句: ${oldIcon} → ${newIcon}`)
      return before + newIcon + after
    })
    content = newContent

    // 修复模板中的图标使用
    const templateRegex = new RegExp(`(<el-icon[^>]*>\\s*<)${oldIcon}(\\s*/?\\s*>\\s*</el-icon>)`, 'g')
    content = content.replace(templateRegex, (match, before, after) => {
      changes.push(`模板使用: ${oldIcon} → ${newIcon}`)
      return before + newIcon + after
    })

    // 修复字符串中的图标引用
    const stringRegex = new RegExp(`(['"])${oldIcon}(['"])`, 'g')
    content = content.replace(stringRegex, (match, quote1, quote2) => {
      changes.push(`字符串引用: ${oldIcon} → ${newIcon}`)
      return quote1 + newIcon + quote2
    })
  })

  // 如果有修改，写入文件
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8')
    return { fixed: true, changes }
  }

  return { fixed: false, changes: [] }
}

// 递归处理目录
function processDirectory(dir) {
  const results = []
  
  function process(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        process(fullPath)
      } else if (stat.isFile() && (item.endsWith('.vue') || item.endsWith('.js') || item.endsWith('.ts'))) {
        const relativePath = path.relative(__dirname, fullPath)
        const result = fixIconsInFile(fullPath)
        
        if (result.fixed) {
          results.push({
            file: relativePath,
            changes: result.changes
          })
        }
      }
    }
  }
  
  process(dir)
  return results
}

// 主函数
function main() {
  console.log('🔧 开始批量修复 Element Plus 图标...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = processDirectory(srcDir)
  
  if (results.length === 0) {
    console.log('✅ 没有发现需要修复的图标')
    return
  }
  
  console.log(`🎉 成功修复 ${results.length} 个文件:\n`)
  
  results.forEach(({ file, changes }) => {
    console.log(`📄 ${file}:`)
    changes.forEach(change => {
      console.log(`  ✅ ${change}`)
    })
    console.log()
  })
  
  console.log('🎯 修复完成！所有无效图标已替换为有效图标。')
}

main()
