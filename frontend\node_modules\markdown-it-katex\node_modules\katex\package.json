{"name": "katex", "version": "0.6.0", "description": "Fast math typesetting for the web.", "main": "katex.js", "repository": {"type": "git", "url": "git://github.com/Khan/KaTeX.git"}, "files": ["katex.js", "cli.js", "src/", "dist/"], "license": "MIT", "devDependencies": {"browserify": "^10.2.4", "clean-css": "~2.2.15", "eslint": "^1.10.2", "express": "~3.3.3", "glob": "^5.0.15", "jasmine": "^2.3.2", "jasmine-core": "^2.3.4", "js-yaml": "^3.3.1", "jspngopt": "^0.1.0", "less": "~1.7.5", "nomnom": "^1.8.1", "pako": "0.2.7", "selenium-webdriver": "^2.46.1", "uglify-js": "~2.4.15"}, "bin": "cli.js", "scripts": {"test": "make lint test", "prepublish": "make dist"}, "dependencies": {"match-at": "^0.1.0"}}