{"version": 3, "file": "token-builder.js", "sourceRoot": "", "sources": ["../../src/parser/token-builder.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAKhF,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AACxF,OAAO,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAChF,OAAO,EAAE,yBAAyB,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AACnG,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AA8B5C,MAAM,OAAO,mBAAmB;IAAhC;QACI;;WAEG;QACO,gBAAW,GAAuB,EAAE,CAAC;IAmHnD,CAAC;IAjHG,WAAW,CAAC,OAAgB,EAAE,OAA6B;QACvD,MAAM,cAAc,GAAG,MAAM,CAAC,oBAAoB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACpE,MAAM,cAAc,GAAgB,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;QAC7E,MAAM,MAAM,GAAgB,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAE7F,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YACnC,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;YACtC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvF,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC,CAAC,CAAC;QACH,iDAAiD;QACjD,gEAAgE;QAChE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,6DAA6D;IAC7D,iBAAiB,CAAC,IAAY;QAC1B,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;IAClD,CAAC;IAES,cAAc;QACpB,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,OAAO,WAAW,CAAC;IACvB,CAAC;IAES,mBAAmB,CAAC,KAA2B;QACrD,OAAO,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;aACvD,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACtE,CAAC;IAES,kBAAkB,CAAC,QAAsB;QAC/C,MAAM,KAAK,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC7F,MAAM,SAAS,GAAc;YACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,OAAO,EAAE,OAAO;SACnB,CAAC;QACF,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;YAChC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,sDAAsD;YACtD,SAAS,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;QACrE,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,qBAAqB,CAAC,KAAa;QACzC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACzD,8DAA8D;YAC9D,OAAO,IAAI,CAAC;QAChB,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACtE,wEAAwE;YACxE,OAAO,IAAI,CAAC;QAChB,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAES,oBAAoB,CAAC,KAAa;QACxC,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;QACzD,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACpB,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC;YAC/B,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC;IACN,CAAC;IAES,kBAAkB,CAAC,KAA2B,EAAE,cAA2B,EAAE,OAA6B;QAChH,OAAO,KAAK;YACR,mHAAmH;aAClH,MAAM,CAAC,YAAY,CAAC;aACpB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC1D,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE;YACjC,qCAAqC;aACpC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;aAC/C,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IAC5G,CAAC;IAES,iBAAiB,CAAC,OAAgB,EAAE,cAA2B,EAAE,eAAwB;QAC/F,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAc;YACzB,IAAI,EAAE,OAAO,CAAC,KAAK;YACnB,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,cAAc,CAAC;SAC1D,CAAC;QAEF,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE,CAAC;YACvC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,mBAAmB,CAAC,OAAgB,EAAE,eAAwB;QACpE,OAAO,eAAe,CAAC,CAAC;YACpB,IAAI,MAAM,CAAC,yBAAyB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACtD,OAAO,CAAC,KAAK,CAAC;IACtB,CAAC;IAES,aAAa,CAAC,OAAgB,EAAE,cAA2B;QACjE,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,UAAuB,EAAE,KAAK,EAAE,EAAE;YAC5D,MAAM,OAAO,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAiB,CAAC;YACzC,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAI,cAAc,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/E,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YACD,OAAO,UAAU,CAAC;QACtB,CAAC,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;CACJ"}