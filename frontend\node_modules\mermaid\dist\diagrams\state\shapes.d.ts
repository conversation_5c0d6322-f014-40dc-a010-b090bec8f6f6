export function drawStartState(g: any): any;
export function drawDivider(g: any): any;
export function drawSimpleState(g: any, stateDef: any): any;
export function drawDescrState(g: any, stateDef: any): any;
export function addTitleAndBox(g: any, stateDef: any, altBkg: any): any;
export function drawText(elem: any, textData: any): any;
export function drawNote(text: any, g: any): any;
export function drawState(elem: any, stateDef: any): {
    id: any;
    label: any;
    width: number;
    height: number;
};
export function drawEdge(elem: any, path: any, relation: any): void;
