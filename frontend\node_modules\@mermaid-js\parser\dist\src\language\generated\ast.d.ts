/******************************************************************************
 * This file was generated by langium-cli 3.3.0.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/
import type { AstNode, Reference, ReferenceInfo, TypeMetaData } from 'langium';
import { AbstractAstReflection } from 'langium';
export declare const MermaidTerminals: {
    ACC_DESCR: RegExp;
    ACC_TITLE: RegExp;
    TITLE: RegExp;
    NEWLINE: RegExp;
    WHITESPACE: RegExp;
    YAML: RegExp;
    DIRECTIVE: RegExp;
    SINGLE_LINE_COMMENT: RegExp;
    INT: RegExp;
    STRING: RegExp;
    NUMBER: RegExp;
    ARROW_DIRECTION: RegExp;
    ARROW_GROUP: RegExp;
    ARROW_INTO: RegExp;
    ID: RegExp;
    ARCH_ICON: RegExp;
    ARCH_TITLE: RegExp;
    REFERENCE: RegExp;
    GRATICULE: RegExp;
    BOOLEAN: RegExp;
    TREEMAP_KEYWORD: RegExp;
    CLASS_DEF: RegExp;
    STYLE_SEPARATOR: RegExp;
    SEPARATOR: RegExp;
    COMMA: RegExp;
    WS: RegExp;
    ML_COMMENT: RegExp;
    NL: RegExp;
    INDENTATION: RegExp;
    ID2: RegExp;
    NUMBER2: RegExp;
    STRING2: RegExp;
};
export type MermaidTerminalNames = keyof typeof MermaidTerminals;
export type MermaidKeywordNames = "+" | "," | "-" | "--" | ":" | "BT" | "HIGHLIGHT" | "LR" | "NORMAL" | "REVERSE" | "TB" | "[" | "]" | "architecture-beta" | "axis" | "branch" | "checkout" | "cherry-pick" | "commit" | "curve" | "gitGraph" | "gitGraph:" | "graticule" | "group" | "id:" | "in" | "info" | "junction" | "max" | "merge" | "min" | "msg:" | "order:" | "packet-beta" | "parent:" | "pie" | "radar-beta" | "radar-beta:" | "service" | "showData" | "showInfo" | "showLegend" | "switch" | "tag:" | "ticks" | "type:" | "{" | "}";
export type MermaidTokenNames = MermaidTerminalNames | MermaidKeywordNames;
export type ClassDef = string;
export declare function isClassDef(item: unknown): item is ClassDef;
export type MyNumber = number;
export declare function isMyNumber(item: unknown): item is MyNumber;
export type Statement = Branch | Checkout | CherryPicking | Commit | Merge;
export declare const Statement = "Statement";
export declare function isStatement(item: unknown): item is Statement;
export interface Architecture extends AstNode {
    readonly $type: 'Architecture';
    accDescr?: string;
    accTitle?: string;
    edges: Array<Edge>;
    groups: Array<Group>;
    junctions: Array<Junction>;
    services: Array<Service>;
    title?: string;
}
export declare const Architecture = "Architecture";
export declare function isArchitecture(item: unknown): item is Architecture;
export interface Axis extends AstNode {
    readonly $container: Radar;
    readonly $type: 'Axis';
    label?: string;
    name: string;
}
export declare const Axis = "Axis";
export declare function isAxis(item: unknown): item is Axis;
export interface Branch extends AstNode {
    readonly $container: GitGraph;
    readonly $type: 'Branch';
    name: string;
    order?: number;
}
export declare const Branch = "Branch";
export declare function isBranch(item: unknown): item is Branch;
export interface Checkout extends AstNode {
    readonly $container: GitGraph;
    readonly $type: 'Checkout';
    branch: string;
}
export declare const Checkout = "Checkout";
export declare function isCheckout(item: unknown): item is Checkout;
export interface CherryPicking extends AstNode {
    readonly $container: GitGraph;
    readonly $type: 'CherryPicking';
    id?: string;
    parent?: string;
    tags: Array<string>;
}
export declare const CherryPicking = "CherryPicking";
export declare function isCherryPicking(item: unknown): item is CherryPicking;
export interface ClassDefStatement extends AstNode {
    readonly $type: 'ClassDefStatement';
    className: string;
    styleText: string;
}
export declare const ClassDefStatement = "ClassDefStatement";
export declare function isClassDefStatement(item: unknown): item is ClassDefStatement;
export interface Commit extends AstNode {
    readonly $container: GitGraph;
    readonly $type: 'Commit';
    id?: string;
    message?: string;
    tags: Array<string>;
    type?: 'HIGHLIGHT' | 'NORMAL' | 'REVERSE';
}
export declare const Commit = "Commit";
export declare function isCommit(item: unknown): item is Commit;
export interface Curve extends AstNode {
    readonly $container: Radar;
    readonly $type: 'Curve';
    entries: Array<Entry>;
    label?: string;
    name: string;
}
export declare const Curve = "Curve";
export declare function isCurve(item: unknown): item is Curve;
export interface Edge extends AstNode {
    readonly $container: Architecture;
    readonly $type: 'Edge';
    lhsDir: string;
    lhsGroup: boolean;
    lhsId: string;
    lhsInto: boolean;
    rhsDir: string;
    rhsGroup: boolean;
    rhsId: string;
    rhsInto: boolean;
    title?: string;
}
export declare const Edge = "Edge";
export declare function isEdge(item: unknown): item is Edge;
export interface Entry extends AstNode {
    readonly $container: Curve;
    readonly $type: 'Entry';
    axis?: Reference<Axis>;
    value: number;
}
export declare const Entry = "Entry";
export declare function isEntry(item: unknown): item is Entry;
export interface GitGraph extends AstNode {
    readonly $type: 'Direction' | 'GitGraph';
    accDescr?: string;
    accTitle?: string;
    statements: Array<Statement>;
    title?: string;
}
export declare const GitGraph = "GitGraph";
export declare function isGitGraph(item: unknown): item is GitGraph;
export interface Group extends AstNode {
    readonly $container: Architecture;
    readonly $type: 'Group';
    icon?: string;
    id: string;
    in?: string;
    title?: string;
}
export declare const Group = "Group";
export declare function isGroup(item: unknown): item is Group;
export interface Info extends AstNode {
    readonly $type: 'Info';
    accDescr?: string;
    accTitle?: string;
    title?: string;
}
export declare const Info = "Info";
export declare function isInfo(item: unknown): item is Info;
export interface Item extends AstNode {
    readonly $type: 'Item' | 'Leaf' | 'Section';
    classSelector?: string;
    name: string;
}
export declare const Item = "Item";
export declare function isItem(item: unknown): item is Item;
export interface Junction extends AstNode {
    readonly $container: Architecture;
    readonly $type: 'Junction';
    id: string;
    in?: string;
}
export declare const Junction = "Junction";
export declare function isJunction(item: unknown): item is Junction;
export interface Merge extends AstNode {
    readonly $container: GitGraph;
    readonly $type: 'Merge';
    branch: string;
    id?: string;
    tags: Array<string>;
    type?: 'HIGHLIGHT' | 'NORMAL' | 'REVERSE';
}
export declare const Merge = "Merge";
export declare function isMerge(item: unknown): item is Merge;
export interface Option extends AstNode {
    readonly $container: Radar;
    readonly $type: 'Option';
    name: 'graticule' | 'max' | 'min' | 'showLegend' | 'ticks';
    value: boolean | number | string;
}
export declare const Option = "Option";
export declare function isOption(item: unknown): item is Option;
export interface Packet extends AstNode {
    readonly $type: 'Packet';
    accDescr?: string;
    accTitle?: string;
    blocks: Array<PacketBlock>;
    title?: string;
}
export declare const Packet = "Packet";
export declare function isPacket(item: unknown): item is Packet;
export interface PacketBlock extends AstNode {
    readonly $container: Packet;
    readonly $type: 'PacketBlock';
    bits?: number;
    end?: number;
    label: string;
    start?: number;
}
export declare const PacketBlock = "PacketBlock";
export declare function isPacketBlock(item: unknown): item is PacketBlock;
export interface Pie extends AstNode {
    readonly $type: 'Pie';
    accDescr?: string;
    accTitle?: string;
    sections: Array<PieSection>;
    showData: boolean;
    title?: string;
}
export declare const Pie = "Pie";
export declare function isPie(item: unknown): item is Pie;
export interface PieSection extends AstNode {
    readonly $container: Pie;
    readonly $type: 'PieSection';
    label: string;
    value: number;
}
export declare const PieSection = "PieSection";
export declare function isPieSection(item: unknown): item is PieSection;
export interface Radar extends AstNode {
    readonly $type: 'Radar';
    accDescr?: string;
    accTitle?: string;
    axes: Array<Axis>;
    curves: Array<Curve>;
    options: Array<Option>;
    title?: string;
}
export declare const Radar = "Radar";
export declare function isRadar(item: unknown): item is Radar;
export interface Service extends AstNode {
    readonly $container: Architecture;
    readonly $type: 'Service';
    icon?: string;
    iconText?: string;
    id: string;
    in?: string;
    title?: string;
}
export declare const Service = "Service";
export declare function isService(item: unknown): item is Service;
export interface Treemap extends AstNode {
    readonly $type: 'Treemap';
    accDescr?: string;
    accTitle?: string;
    title?: string;
    TreemapRows: Array<TreemapRow>;
}
export declare const Treemap = "Treemap";
export declare function isTreemap(item: unknown): item is Treemap;
export interface TreemapRow extends AstNode {
    readonly $container: Treemap;
    readonly $type: 'ClassDef' | 'TreemapRow';
    indent?: string;
    item: Item;
}
export declare const TreemapRow = "TreemapRow";
export declare function isTreemapRow(item: unknown): item is TreemapRow;
export interface Direction extends GitGraph {
    readonly $type: 'Direction';
    dir: 'BT' | 'LR' | 'TB';
}
export declare const Direction = "Direction";
export declare function isDirection(item: unknown): item is Direction;
export interface Leaf extends Item {
    readonly $type: 'Leaf';
    value: number;
}
export declare const Leaf = "Leaf";
export declare function isLeaf(item: unknown): item is Leaf;
export interface Section extends Item {
    readonly $type: 'Section';
}
export declare const Section = "Section";
export declare function isSection(item: unknown): item is Section;
export type MermaidAstType = {
    Architecture: Architecture;
    Axis: Axis;
    Branch: Branch;
    Checkout: Checkout;
    CherryPicking: CherryPicking;
    ClassDefStatement: ClassDefStatement;
    Commit: Commit;
    Curve: Curve;
    Direction: Direction;
    Edge: Edge;
    Entry: Entry;
    GitGraph: GitGraph;
    Group: Group;
    Info: Info;
    Item: Item;
    Junction: Junction;
    Leaf: Leaf;
    Merge: Merge;
    Option: Option;
    Packet: Packet;
    PacketBlock: PacketBlock;
    Pie: Pie;
    PieSection: PieSection;
    Radar: Radar;
    Section: Section;
    Service: Service;
    Statement: Statement;
    Treemap: Treemap;
    TreemapRow: TreemapRow;
};
export declare class MermaidAstReflection extends AbstractAstReflection {
    getAllTypes(): string[];
    protected computeIsSubtype(subtype: string, supertype: string): boolean;
    getReferenceType(refInfo: ReferenceInfo): string;
    getTypeMetaData(type: string): TypeMetaData;
}
export declare const reflection: MermaidAstReflection;
