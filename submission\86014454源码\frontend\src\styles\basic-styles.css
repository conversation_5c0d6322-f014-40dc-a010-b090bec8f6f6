/* 基础样式系统 - 临时替代方案 */

:root {
  /* iFlytek 品牌色彩 */
  --iflytek-primary: #1890ff;
  --iflytek-secondary: #667eea;
  --iflytek-success: #52c41a;
  --iflytek-warning: #faad14;
  --iflytek-error: #ff4d4f;
  
  /* 背景色 */
  --iflytek-bg-primary: #ffffff;
  --iflytek-bg-secondary: #f5f5f5;
  --iflytek-bg-tertiary: #fafafa;
  
  /* 文字色 */
  --iflytek-text-primary: #262626;
  --iflytek-text-secondary: #595959;
  --iflytek-text-tertiary: #8c8c8c;
  
  /* 边框色 */
  --iflytek-border-primary: #d9d9d9;
  --iflytek-border-secondary: #f0f0f0;
  --iflytek-border-tertiary: #fafafa;
  
  /* 阴影 */
  --iflytek-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --iflytek-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --iflytek-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* 渐变 */
  --iflytek-gradient-primary: linear-gradient(135deg, #1890ff 0%, #667eea 100%);
  --iflytek-gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 字体 */
  --font-family-chinese-base: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  
  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--font-family-chinese-base);
  background: var(--iflytek-bg-primary);
  color: var(--iflytek-text-primary);
  line-height: 1.6;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 基础动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 基础按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background: var(--iflytek-gradient-primary);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--iflytek-shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--iflytek-primary);
  border: 2px solid var(--iflytek-primary);
}

.btn-secondary:hover {
  background: var(--iflytek-primary);
  color: white;
}

/* 基础卡片样式 */
.card {
  background: var(--iflytek-bg-primary);
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--iflytek-shadow-sm);
  border: 1px solid var(--iflytek-border-secondary);
}

.card:hover {
  box-shadow: var(--iflytek-shadow-md);
}

/* 基础容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 基础网格 */
.grid {
  display: grid;
  gap: 20px;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式 */
@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 0 16px;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--iflytek-primary);
}

.text-secondary {
  color: var(--iflytek-text-secondary);
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.p-4 {
  padding: 16px;
}

.hidden {
  display: none;
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--iflytek-border-primary);
  border-radius: 50%;
  border-top-color: var(--iflytek-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
