#!/usr/bin/env node

/**
 * 面板大小调整验证脚本
 * 验证实时分析状态和文本分析结果面板的高度调整，确保所有6个项目都能显示
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 验证面板大小调整...\n')

// 检查的文件
const targetFile = 'src/views/TextPrimaryInterviewPage.vue'
const fullPath = path.join(__dirname, targetFile)

console.log(`📄 检查文件: ${targetFile}`)

if (!fs.existsSync(fullPath)) {
  console.log(`❌ 文件不存在: ${targetFile}`)
  process.exit(1)
}

const content = fs.readFileSync(fullPath, 'utf8')
console.log(`✅ 文件存在 (${content.length} 字符)\n`)

// 验证项目
const verificationChecks = [
  {
    name: '桌面端状态面板高度调整',
    pattern: /\.status-panel-external\s*{[^}]*height:\s*280px[^}]*}/s,
    description: '检查桌面端状态面板高度是否调整为280px'
  },
  {
    name: '桌面端结果面板高度调整',
    pattern: /\.results-panel-external\s*{[^}]*height:\s*280px[^}]*}/s,
    description: '检查桌面端结果面板高度是否调整为280px'
  },
  {
    name: '桌面端面板overflow设置',
    pattern: /overflow:\s*visible[^}]*\/\*[^*]*确保所有内容显示/,
    description: '检查桌面端面板是否设置为overflow: visible'
  },
  {
    name: '状态面板网格布局调整',
    pattern: /\.status-panel-external\s+\.processing-stats\s*{[^}]*grid-template-rows:\s*1fr\s+1fr\s+1fr[^}]*}/s,
    description: '检查状态面板是否调整为3行网格布局'
  },
  {
    name: '结果面板网格布局调整',
    pattern: /\.results-panel-external\s+\.score-breakdown\s*{[^}]*grid-template-rows:\s*1fr\s+1fr\s+1fr[^}]*}/s,
    description: '检查结果面板是否调整为3行网格布局'
  },
  {
    name: '项目最小高度调整',
    pattern: /min-height:\s*42px[^}]*\/\*[^*]*减少最小高度以适应3行布局/,
    description: '检查项目最小高度是否调整为42px'
  },
  {
    name: '中等屏幕面板高度调整',
    pattern: /height:\s*260px[^}]*\/\*[^*]*中等屏幕下增加高度以容纳6个项目/,
    description: '检查中等屏幕下面板高度是否调整为260px'
  },
  {
    name: '移动端面板高度调整',
    pattern: /height:\s*220px[^}]*\/\*[^*]*移动端增加高度以容纳6个项目/,
    description: '检查移动端面板高度是否调整为220px'
  }
]

let allChecksPass = true
let passedChecks = 0

console.log('🔧 执行面板大小调整验证...\n')

verificationChecks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name}`)
  console.log(`   描述: ${check.description}`)
  
  const result = check.pattern.test(content)
  
  if (result) {
    console.log(`   ✅ 通过`)
    passedChecks++
  } else {
    console.log(`   ❌ 失败`)
    allChecksPass = false
  }
  
  console.log()
})

// 额外检查：提取实际的高度值
console.log('📊 实际高度值检查:')

// 检查桌面端高度
const desktopHeightMatch = content.match(/\.status-panel-external\s*{[^}]*height:\s*([^;]+);/s)
if (desktopHeightMatch) {
  console.log(`   桌面端面板高度: ${desktopHeightMatch[1].trim()}`)
} else {
  console.log(`   桌面端面板高度: 未找到`)
}

// 检查网格行设置
const gridRowsMatches = content.match(/grid-template-rows:\s*([^;]+);/g) || []
console.log(`   发现 ${gridRowsMatches.length} 个网格行设置`)
gridRowsMatches.forEach((match, index) => {
  console.log(`     ${index + 1}. ${match}`)
})

// 检查overflow设置
const overflowMatches = content.match(/overflow:\s*visible[^}]*\/\*[^*]*确保所有内容显示/g) || []
console.log(`   发现 ${overflowMatches.length} 个overflow: visible设置`)

console.log()

// 检查面板内容项目数量
console.log('📋 面板内容项目检查:')

// 检查状态面板项目
const statusItems = [
  '已处理消息', '分析耗时', '沟通技巧', '表达能力', '模型版本', '响应时间'
]
const statusItemsFound = statusItems.filter(item => content.includes(item))
console.log(`   状态面板项目: ${statusItemsFound.length}/6 个`)
statusItemsFound.forEach((item, index) => {
  console.log(`     ${index + 1}. ${item}`)
})

// 检查结果面板项目
const resultItems = [
  '技术能力', '逻辑思维', '沟通技巧', '表达能力', '内容质量', '学习能力'
]
const resultItemsFound = resultItems.filter(item => content.includes(item))
console.log(`   结果面板项目: ${resultItemsFound.length}/6 个`)
resultItemsFound.forEach((item, index) => {
  console.log(`     ${index + 1}. ${item}`)
})

console.log()

// 生成测试报告
console.log('📊 面板大小调整验证报告')
console.log('='.repeat(50))

if (allChecksPass) {
  console.log('🎉 面板大小调整成功！')
  console.log('')
  console.log('✅ 完成的调整:')
  console.log('  - 桌面端面板高度: 220px → 280px (增加60px)')
  console.log('  - 中等屏幕面板高度: 200px → 260px (增加60px)')
  console.log('  - 移动端面板高度: 160px → 220px (增加60px)')
  console.log('  - 网格布局: 2x2 → 2x3 (增加一行)')
  console.log('  - 项目高度: 50px → 42px (优化空间利用)')
  console.log('  - overflow设置: hidden → visible (确保内容显示)')
  console.log('')
  console.log('✅ 现在可以显示的内容:')
  console.log('  实时分析状态面板 (6项):')
  console.log('    - 已处理消息、分析耗时')
  console.log('    - 沟通技巧、表达能力')
  console.log('    - 模型版本、响应时间 ← 新显示')
  console.log('')
  console.log('  文本分析结果面板 (6项):')
  console.log('    - 技术能力、逻辑思维')
  console.log('    - 沟通技巧、表达能力')
  console.log('    - 内容质量、学习能力 ← 新显示')
  console.log('')
  console.log('✅ 响应式支持:')
  console.log('  - 桌面端、中等屏幕、移动端全覆盖')
  console.log('  - 保持iFlytek品牌一致性')
  console.log('  - 维护良好的视觉层次')
} else {
  console.log('⚠️  面板大小调整验证中发现问题')
  console.log('')
  console.log(`📊 验证结果: ${passedChecks}/${verificationChecks.length} 项通过`)
  console.log('')
  console.log('💡 建议:')
  console.log('  - 检查上述失败的验证项目')
  console.log('  - 确保CSS修改正确应用')
  console.log('  - 重新运行此验证脚本')
}

console.log('')
console.log('🔗 相关信息:')
console.log('  - 目标文件: src/views/TextPrimaryInterviewPage.vue')
console.log('  - 调整类型: 面板高度和布局优化')
console.log('  - 主要目标: 显示所有6个项目内容')
console.log('  - 布局变化: 2x2网格 → 2x3网格')

console.log('')
console.log('📞 测试访问:')
console.log('  - 文本面试页面: http://localhost:8080/text-primary-interview')
console.log('  - 产品演示页面: http://localhost:8080/demo')

console.log('')
console.log('🎯 预期效果:')
console.log('  - 所有6个状态项目完整显示')
console.log('  - 所有6个得分项目完整显示')
console.log('  - 面板高度适当增加，布局美观')
console.log('  - 响应式设计在所有设备上正常工作')

export { allChecksPass, passedChecks, verificationChecks }
