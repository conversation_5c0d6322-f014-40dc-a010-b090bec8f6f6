{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/radio/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Radio from './src/radio.vue'\nimport RadioButton from './src/radio-button.vue'\nimport RadioGroup from './src/radio-group.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElRadio: SFCWithInstall<typeof Radio> & {\n  RadioButton: typeof RadioButton\n  RadioGroup: typeof RadioGroup\n} = withInstall(Radio, {\n  RadioButton,\n  RadioGroup,\n})\nexport default ElRadio\nexport const ElRadioGroup: SFCWithInstall<typeof RadioGroup> =\n  withNoopInstall(RadioGroup)\nexport const ElRadioButton: SFCWithInstall<typeof RadioButton> =\n  withNoopInstall(RadioButton)\n\nexport * from './src/radio'\nexport * from './src/radio-group'\nexport * from './src/radio-button'\nexport * from './src/constants'\n"], "names": ["withInstall", "Radio", "RadioButton", "RadioGroup", "withNoopInstall"], "mappings": ";;;;;;;;;;;;;AAIY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK,EAAE;AAC1C,eAAEC,wBAAW;AACb,cAAEC,uBAAU;AACZ,CAAC,EAAE;AAES,MAAC,YAAY,GAAGC,uBAAe,CAACD,uBAAU,EAAE;AAC5C,MAAC,aAAa,GAAGC,uBAAe,CAACF,wBAAW;;;;;;;;;;;;;;"}