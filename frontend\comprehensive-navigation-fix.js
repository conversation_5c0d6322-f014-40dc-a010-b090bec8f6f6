// iFlytek 面试系统综合导航修复脚本
// 识别并修复任何剩余的导航问题

console.log('🔧 开始iFlytek面试系统综合导航修复...');

// 修复结果记录器
const fixResults = {
    appliedFixes: [],
    skippedFixes: [],
    errors: []
};

// 1. 强制修复导航菜单项事件绑定
function forceFixMenuItemEvents() {
    console.log('📋 强制修复导航菜单项事件绑定...');
    
    const menuItemConfigs = [
        { text: '首页', path: '/' },
        { text: '产品演示', path: '/demo' },
        { text: '开始面试', path: '/interview-selection' },
        { text: '面试报告', path: '/reports' },
        { text: '数据洞察', path: '/intelligent-dashboard' }
    ];
    
    const menuItems = document.querySelectorAll('.el-menu-item');
    let fixedCount = 0;
    
    menuItems.forEach(item => {
        const text = item.textContent.trim();
        const config = menuItemConfigs.find(c => c.text === text);
        
        if (config) {
            // 移除所有现有事件监听器
            const newItem = item.cloneNode(true);
            item.parentNode.replaceChild(newItem, item);
            
            // 添加强制点击事件
            newItem.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                console.log(`🖱️ 导航到: ${config.path}`);
                
                // 多种导航方式确保成功
                try {
                    // 方式1: 使用History API
                    window.history.pushState({}, '', config.path);
                    
                    // 方式2: 触发popstate事件
                    window.dispatchEvent(new PopStateEvent('popstate', { state: {} }));
                    
                    // 方式3: 如果Vue Router存在，尝试使用
                    const app = document.getElementById('app');
                    if (app && app.__vue_app__) {
                        try {
                            const vueApp = app.__vue_app__;
                            if (vueApp.config && vueApp.config.globalProperties && vueApp.config.globalProperties.$router) {
                                vueApp.config.globalProperties.$router.push(config.path);
                            }
                        } catch (routerError) {
                            console.warn('Vue Router导航失败，使用备用方案');
                        }
                    }
                    
                    // 方式4: 备用方案 - 直接重新加载
                    setTimeout(() => {
                        if (window.location.pathname !== config.path) {
                            window.location.href = config.path;
                        }
                    }, 200);
                    
                } catch (error) {
                    console.error(`导航失败: ${error.message}`);
                    // 最终备用方案
                    window.location.href = config.path;
                }
            });
            
            // 添加视觉反馈
            newItem.style.cursor = 'pointer';
            newItem.style.transition = 'all 0.3s ease';
            
            newItem.addEventListener('mouseenter', () => {
                newItem.style.backgroundColor = 'rgba(24, 144, 255, 0.1)';
            });
            
            newItem.addEventListener('mouseleave', () => {
                newItem.style.backgroundColor = '';
            });
            
            fixedCount++;
            fixResults.appliedFixes.push(`菜单项 "${text}" 事件绑定已修复`);
        }
    });
    
    console.log(`✅ 修复了 ${fixedCount} 个菜单项的事件绑定`);
    return fixedCount;
}

// 2. 强制修复按钮事件绑定
function forceFixButtonEvents() {
    console.log('🔘 强制修复按钮事件绑定...');
    
    const buttonConfigs = [
        { selector: '.primary-cta', path: '/interview-selection', name: '立即开始面试' },
        { selector: '.secondary-cta', path: '/demo', name: '观看产品演示' },
        { selector: '.cta-button', path: '/enterprise-home', name: '企业版体验' },
        { selector: '.secondary-btn', path: '/candidate-portal', name: '候选人入口' },
        { selector: '.start-btn', path: '/interview-selection', name: '开始AI面试' },
        { selector: '.demo-btn', path: '/demo', name: '观看演示' },
        { selector: '.report-btn', path: '/reports', name: '查看报告' }
    ];
    
    let fixedCount = 0;
    
    buttonConfigs.forEach(config => {
        const buttons = document.querySelectorAll(config.selector);
        
        buttons.forEach(button => {
            // 移除现有事件监听器
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // 添加强制点击事件
            newButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                console.log(`🖱️ 按钮点击: ${config.name} -> ${config.path}`);
                
                // 多重导航策略
                try {
                    window.history.pushState({}, '', config.path);
                    window.dispatchEvent(new PopStateEvent('popstate', { state: {} }));
                    
                    setTimeout(() => {
                        if (window.location.pathname !== config.path) {
                            window.location.href = config.path;
                        }
                    }, 200);
                    
                } catch (error) {
                    console.error(`按钮导航失败: ${error.message}`);
                    window.location.href = config.path;
                }
            });
            
            // 添加视觉反馈
            newButton.style.cursor = 'pointer';
            newButton.style.transition = 'all 0.3s ease';
            
            newButton.addEventListener('mouseenter', () => {
                newButton.style.transform = 'translateY(-2px)';
                newButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            });
            
            newButton.addEventListener('mouseleave', () => {
                newButton.style.transform = 'translateY(0)';
                newButton.style.boxShadow = '';
            });
            
            fixedCount++;
            fixResults.appliedFixes.push(`按钮 "${config.name}" 事件绑定已修复`);
        });
    });
    
    console.log(`✅ 修复了 ${fixedCount} 个按钮的事件绑定`);
    return fixedCount;
}

// 3. 修复产品卡片点击事件
function fixProductCardEvents() {
    console.log('🃏 修复产品卡片点击事件...');
    
    const productCards = document.querySelectorAll('.product-card');
    let fixedCount = 0;
    
    productCards.forEach((card, index) => {
        // 确定目标路径
        const title = card.querySelector('.product-title');
        let targetPath = '/demo'; // 默认路径
        
        if (title) {
            const titleText = title.textContent.trim();
            switch (titleText) {
                case 'AI智能面试官':
                    targetPath = '/interview-selection';
                    break;
                case '多模态分析引擎':
                    targetPath = '/demo';
                    break;
                case '智能招聘管理':
                    targetPath = '/intelligent-dashboard';
                    break;
            }
        }
        
        // 移除现有事件监听器
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);
        
        // 添加点击事件
        newCard.addEventListener('click', (e) => {
            e.preventDefault();
            console.log(`🖱️ 产品卡片点击 -> ${targetPath}`);
            
            try {
                window.history.pushState({}, '', targetPath);
                window.dispatchEvent(new PopStateEvent('popstate', { state: {} }));
                
                setTimeout(() => {
                    if (window.location.pathname !== targetPath) {
                        window.location.href = targetPath;
                    }
                }, 200);
                
            } catch (error) {
                console.error(`产品卡片导航失败: ${error.message}`);
                window.location.href = targetPath;
            }
        });
        
        // 添加视觉效果
        newCard.style.cursor = 'pointer';
        newCard.style.transition = 'all 0.3s ease';
        
        newCard.addEventListener('mouseenter', () => {
            newCard.style.transform = 'translateY(-4px)';
            newCard.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
        });
        
        newCard.addEventListener('mouseleave', () => {
            newCard.style.transform = 'translateY(0)';
            newCard.style.boxShadow = '';
        });
        
        fixedCount++;
        fixResults.appliedFixes.push(`产品卡片 ${index + 1} 点击事件已修复`);
    });
    
    console.log(`✅ 修复了 ${fixedCount} 个产品卡片的点击事件`);
    return fixedCount;
}

// 4. 修复Vue Router集成
function fixVueRouterIntegration() {
    console.log('🛣️ 修复Vue Router集成...');
    
    try {
        // 设置全局路由监听器
        window.addEventListener('popstate', (e) => {
            console.log('📍 路径变化检测:', window.location.pathname);
            
            // 尝试通知Vue应用路径变化
            const app = document.getElementById('app');
            if (app && app.__vue_app__) {
                try {
                    const vueApp = app.__vue_app__;
                    
                    // 尝试强制更新Vue应用
                    if (vueApp._instance && vueApp._instance.proxy) {
                        if (vueApp._instance.proxy.$forceUpdate) {
                            vueApp._instance.proxy.$forceUpdate();
                        }
                        
                        // 尝试触发路由更新
                        if (vueApp._instance.proxy.$router) {
                            vueApp._instance.proxy.$router.replace(window.location.pathname);
                        }
                    }
                    
                } catch (vueError) {
                    console.warn('Vue Router更新失败，使用页面重新加载');
                    // 如果Vue Router更新失败，重新加载页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 100);
                }
            }
        });
        
        fixResults.appliedFixes.push('Vue Router集成已修复');
        console.log('✅ Vue Router集成修复完成');
        return true;
        
    } catch (error) {
        console.error('Vue Router集成修复失败:', error);
        fixResults.errors.push(`Vue Router集成: ${error.message}`);
        return false;
    }
}

// 5. 应用CSS修复
function applyCSSFixes() {
    console.log('🎨 应用CSS修复...');
    
    try {
        // 创建修复样式
        const fixStyles = `
            /* 确保所有可点击元素有正确的指针样式 */
            .el-menu-item,
            .el-button,
            .product-card,
            .primary-cta,
            .secondary-cta,
            .cta-button,
            .secondary-btn {
                cursor: pointer !important;
                pointer-events: auto !important;
            }
            
            /* 防止事件冲突 */
            .el-menu-item * {
                pointer-events: none;
            }
            
            .el-button * {
                pointer-events: none;
            }
            
            /* 确保悬停效果 */
            .el-menu-item:hover {
                background-color: rgba(24, 144, 255, 0.1) !important;
            }
            
            .el-button:hover {
                transform: translateY(-1px) !important;
            }
            
            /* 确保z-index不冲突 */
            .enterprise-header {
                z-index: 1000 !important;
            }
            
            .el-menu {
                z-index: 999 !important;
            }
        `;
        
        // 添加样式到页面
        const styleElement = document.createElement('style');
        styleElement.textContent = fixStyles;
        document.head.appendChild(styleElement);
        
        fixResults.appliedFixes.push('CSS修复样式已应用');
        console.log('✅ CSS修复完成');
        return true;
        
    } catch (error) {
        console.error('CSS修复失败:', error);
        fixResults.errors.push(`CSS修复: ${error.message}`);
        return false;
    }
}

// 6. 主修复函数
function runComprehensiveNavigationFix() {
    console.log('🚀 开始综合导航修复...\n');
    
    // 重置修复结果
    fixResults.appliedFixes = [];
    fixResults.skippedFixes = [];
    fixResults.errors = [];
    
    try {
        // 执行所有修复
        const menuFixes = forceFixMenuItemEvents();
        const buttonFixes = forceFixButtonEvents();
        const cardFixes = fixProductCardEvents();
        const routerFix = fixVueRouterIntegration();
        const cssFix = applyCSSFixes();
        
        // 生成修复报告
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                menuItemsFixed: menuFixes,
                buttonsFixed: buttonFixes,
                productCardsFixed: cardFixes,
                routerFixed: routerFix,
                cssFixed: cssFix,
                totalFixes: fixResults.appliedFixes.length,
                totalErrors: fixResults.errors.length
            },
            details: fixResults
        };
        
        console.log('\n📊 修复结果摘要:');
        console.log(`  菜单项修复: ${menuFixes} 个`);
        console.log(`  按钮修复: ${buttonFixes} 个`);
        console.log(`  产品卡片修复: ${cardFixes} 个`);
        console.log(`  路由器修复: ${routerFix ? '成功' : '失败'}`);
        console.log(`  CSS修复: ${cssFix ? '成功' : '失败'}`);
        console.log(`  总修复项: ${report.summary.totalFixes} 个`);
        console.log(`  错误数: ${report.summary.totalErrors} 个`);
        
        if (report.summary.totalErrors > 0) {
            console.log('\n❌ 修复过程中的错误:');
            fixResults.errors.forEach(error => console.error(`  - ${error}`));
        }
        
        // 保存报告
        window.iflytekNavigationFixReport = report;
        
        console.log('\n✅ 综合导航修复完成!');
        console.log('💡 修复报告已保存到 window.iflytekNavigationFixReport');
        console.log('🎯 现在可以测试导航功能');
        
        return report;
        
    } catch (error) {
        console.error('综合修复失败:', error);
        fixResults.errors.push(`综合修复: ${error.message}`);
        return null;
    }
}

// 导出到全局作用域
window.iflytekComprehensiveFix = {
    runComprehensiveNavigationFix,
    forceFixMenuItemEvents,
    forceFixButtonEvents,
    fixProductCardEvents,
    fixVueRouterIntegration,
    applyCSSFixes,
    getResults: () => fixResults
};

console.log('✅ 综合导航修复脚本已加载');
console.log('💡 使用 iflytekComprehensiveFix.runComprehensiveNavigationFix() 开始修复');
console.log('💡 使用 window.iflytekNavigationFixReport 查看修复报告');
