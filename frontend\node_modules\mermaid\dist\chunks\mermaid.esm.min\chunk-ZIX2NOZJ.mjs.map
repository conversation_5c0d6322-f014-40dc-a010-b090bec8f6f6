{"version": 3, "sources": ["../../../../parser/dist/mermaid-parser.core.mjs"], "sourcesContent": ["import {\n  GitGraphModule,\n  createGitGraphServices\n} from \"./chunks/mermaid-parser.core/chunk-BFZLARZY.mjs\";\nimport {\n  InfoModule,\n  createInfoServices\n} from \"./chunks/mermaid-parser.core/chunk-FHLWH6W2.mjs\";\nimport {\n  PacketModule,\n  createPacketServices\n} from \"./chunks/mermaid-parser.core/chunk-DI7F6ITA.mjs\";\nimport {\n  PieModule,\n  createPieServices\n} from \"./chunks/mermaid-parser.core/chunk-4D64QAKS.mjs\";\nimport {\n  ArchitectureModule,\n  createArchitectureServices\n} from \"./chunks/mermaid-parser.core/chunk-6MM43HOH.mjs\";\nimport {\n  RadarModule,\n  createRadarServices\n} from \"./chunks/mermaid-parser.core/chunk-PYI2724P.mjs\";\nimport {\n  TreemapModule,\n  createTreemapServices\n} from \"./chunks/mermaid-parser.core/chunk-VX7I4HLL.mjs\";\nimport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  Info,\n  InfoGeneratedModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  Pie,\n  PieGeneratedModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  Statement,\n  Treemap,\n  TreemapGeneratedModule,\n  __name,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection,\n  isTreemap\n} from \"./chunks/mermaid-parser.core/chunk-YAJQ3QCK.mjs\";\n\n// src/parse.ts\nvar parsers = {};\nvar initializers = {\n  info: /* @__PURE__ */ __name(async () => {\n    const { createInfoServices: createInfoServices2 } = await import(\"./chunks/mermaid-parser.core/info-W3HD63GC.mjs\");\n    const parser = createInfoServices2().Info.parser.LangiumParser;\n    parsers.info = parser;\n  }, \"info\"),\n  packet: /* @__PURE__ */ __name(async () => {\n    const { createPacketServices: createPacketServices2 } = await import(\"./chunks/mermaid-parser.core/packet-RYMULSSO.mjs\");\n    const parser = createPacketServices2().Packet.parser.LangiumParser;\n    parsers.packet = parser;\n  }, \"packet\"),\n  pie: /* @__PURE__ */ __name(async () => {\n    const { createPieServices: createPieServices2 } = await import(\"./chunks/mermaid-parser.core/pie-KUMW6FLJ.mjs\");\n    const parser = createPieServices2().Pie.parser.LangiumParser;\n    parsers.pie = parser;\n  }, \"pie\"),\n  architecture: /* @__PURE__ */ __name(async () => {\n    const { createArchitectureServices: createArchitectureServices2 } = await import(\"./chunks/mermaid-parser.core/architecture-W74WOHHR.mjs\");\n    const parser = createArchitectureServices2().Architecture.parser.LangiumParser;\n    parsers.architecture = parser;\n  }, \"architecture\"),\n  gitGraph: /* @__PURE__ */ __name(async () => {\n    const { createGitGraphServices: createGitGraphServices2 } = await import(\"./chunks/mermaid-parser.core/gitGraph-7HJAHWGR.mjs\");\n    const parser = createGitGraphServices2().GitGraph.parser.LangiumParser;\n    parsers.gitGraph = parser;\n  }, \"gitGraph\"),\n  radar: /* @__PURE__ */ __name(async () => {\n    const { createRadarServices: createRadarServices2 } = await import(\"./chunks/mermaid-parser.core/radar-KEIDH4CJ.mjs\");\n    const parser = createRadarServices2().Radar.parser.LangiumParser;\n    parsers.radar = parser;\n  }, \"radar\"),\n  treemap: /* @__PURE__ */ __name(async () => {\n    const { createTreemapServices: createTreemapServices2 } = await import(\"./chunks/mermaid-parser.core/treemap-6Y5VK53G.mjs\");\n    const parser = createTreemapServices2().Treemap.parser.LangiumParser;\n    parsers.treemap = parser;\n  }, \"treemap\")\n};\nasync function parse(diagramType, text) {\n  const initializer = initializers[diagramType];\n  if (!initializer) {\n    throw new Error(`Unknown diagram type: ${diagramType}`);\n  }\n  if (!parsers[diagramType]) {\n    await initializer();\n  }\n  const parser = parsers[diagramType];\n  const result = parser.parse(text);\n  if (result.lexerErrors.length > 0 || result.parserErrors.length > 0) {\n    throw new MermaidParseError(result);\n  }\n  return result.value;\n}\n__name(parse, \"parse\");\nvar MermaidParseError = class extends Error {\n  constructor(result) {\n    const lexerErrors = result.lexerErrors.map((err) => err.message).join(\"\\n\");\n    const parserErrors = result.parserErrors.map((err) => err.message).join(\"\\n\");\n    super(`Parsing failed: ${lexerErrors} ${parserErrors}`);\n    this.result = result;\n  }\n  static {\n    __name(this, \"MermaidParseError\");\n  }\n};\nexport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  ArchitectureModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  GitGraphModule,\n  Info,\n  InfoGeneratedModule,\n  InfoModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  MermaidParseError,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  PacketModule,\n  Pie,\n  PieGeneratedModule,\n  PieModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  RadarModule,\n  Statement,\n  Treemap,\n  TreemapGeneratedModule,\n  TreemapModule,\n  createArchitectureServices,\n  createGitGraphServices,\n  createInfoServices,\n  createPacketServices,\n  createPieServices,\n  createRadarServices,\n  createTreemapServices,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection,\n  isTreemap,\n  parse\n};\n"], "mappings": "kFAqEA,IAAIA,EAAU,CAAC,EACXC,EAAe,CACjB,KAAsBC,EAAO,SAAY,CACvC,GAAM,CAAE,mBAAoBC,CAAoB,EAAI,KAAM,QAAO,8BAAgD,EAC3GC,EAASD,EAAoB,EAAE,KAAK,OAAO,cACjDH,EAAQ,KAAOI,CACjB,EAAG,MAAM,EACT,OAAwBF,EAAO,SAAY,CACzC,GAAM,CAAE,qBAAsBG,CAAsB,EAAI,KAAM,QAAO,gCAAkD,EACjHD,EAASC,EAAsB,EAAE,OAAO,OAAO,cACrDL,EAAQ,OAASI,CACnB,EAAG,QAAQ,EACX,IAAqBF,EAAO,SAAY,CACtC,GAAM,CAAE,kBAAmBI,CAAmB,EAAI,KAAM,QAAO,6BAA+C,EACxGF,EAASE,EAAmB,EAAE,IAAI,OAAO,cAC/CN,EAAQ,IAAMI,CAChB,EAAG,KAAK,EACR,aAA8BF,EAAO,SAAY,CAC/C,GAAM,CAAE,2BAA4BK,CAA4B,EAAI,KAAM,QAAO,sCAAwD,EACnIH,EAASG,EAA4B,EAAE,aAAa,OAAO,cACjEP,EAAQ,aAAeI,CACzB,EAAG,cAAc,EACjB,SAA0BF,EAAO,SAAY,CAC3C,GAAM,CAAE,uBAAwBM,CAAwB,EAAI,KAAM,QAAO,kCAAoD,EACvHJ,EAASI,EAAwB,EAAE,SAAS,OAAO,cACzDR,EAAQ,SAAWI,CACrB,EAAG,UAAU,EACb,MAAuBF,EAAO,SAAY,CACxC,GAAM,CAAE,oBAAqBO,CAAqB,EAAI,KAAM,QAAO,+BAAiD,EAC9GL,EAASK,EAAqB,EAAE,MAAM,OAAO,cACnDT,EAAQ,MAAQI,CAClB,EAAG,OAAO,EACV,QAAyBF,EAAO,SAAY,CAC1C,GAAM,CAAE,sBAAuBQ,CAAuB,EAAI,KAAM,QAAO,iCAAmD,EACpHN,EAASM,EAAuB,EAAE,QAAQ,OAAO,cACvDV,EAAQ,QAAUI,CACpB,EAAG,SAAS,CACd,EACA,eAAeO,EAAMC,EAAaC,EAAM,CACtC,IAAMC,EAAcb,EAAaW,CAAW,EAC5C,GAAI,CAACE,EACH,MAAM,IAAI,MAAM,yBAAyBF,CAAW,EAAE,EAEnDZ,EAAQY,CAAW,GACtB,MAAME,EAAY,EAGpB,IAAMC,EADSf,EAAQY,CAAW,EACZ,MAAMC,CAAI,EAChC,GAAIE,EAAO,YAAY,OAAS,GAAKA,EAAO,aAAa,OAAS,EAChE,MAAM,IAAIC,EAAkBD,CAAM,EAEpC,OAAOA,EAAO,KAChB,CAdeb,EAAAS,EAAA,SAefT,EAAOS,EAAO,OAAO,EACrB,IAAIK,EAAoB,cAAc,KAAM,CA3H5C,MA2H4C,CAAAd,EAAA,0BAC1C,YAAYa,EAAQ,CAClB,IAAME,EAAcF,EAAO,YAAY,IAAKG,GAAQA,EAAI,OAAO,EAAE,KAAK;AAAA,CAAI,EACpEC,EAAeJ,EAAO,aAAa,IAAKG,GAAQA,EAAI,OAAO,EAAE,KAAK;AAAA,CAAI,EAC5E,MAAM,mBAAmBD,CAAW,IAAIE,CAAY,EAAE,EACtD,KAAK,OAASJ,CAChB,CACA,MAAO,CACLb,EAAO,KAAM,mBAAmB,CAClC,CACF", "names": ["parsers", "initializers", "__name", "createInfoServices2", "parser", "createPacketServices2", "createPieServices2", "createArchitectureServices2", "createGitGraphServices2", "createRadarServices2", "createTreemapServices2", "parse", "diagramType", "text", "initializer", "result", "MermaidParseError", "lexerErrors", "err", "parserErrors"]}