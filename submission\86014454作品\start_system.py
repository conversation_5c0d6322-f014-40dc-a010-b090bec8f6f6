#!/usr/bin/env python3
"""
多模态面试评估系统启动脚本
一键启动前后端服务
"""

import subprocess
import sys
import os
import time
import signal
import threading
from pathlib import Path

class SystemStarter:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        
    def check_dependencies(self):
        """检查依赖环境"""
        print("🔍 检查系统依赖...")
        
        # 检查Python
        try:
            python_version = sys.version_info
            if python_version.major >= 3 and python_version.minor >= 8:
                print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
            else:
                print(f"✗ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
                return False
        except Exception as e:
            print(f"✗ Python检查失败: {e}")
            return False
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                node_version = result.stdout.strip()
                print(f"✓ Node.js版本: {node_version}")
            else:
                print("✗ Node.js未安装或不可用")
                return False
        except Exception as e:
            print(f"✗ Node.js检查失败: {e}")
            return False
        
        # 检查项目目录
        if not self.backend_dir.exists():
            print(f"✗ 后端目录不存在: {self.backend_dir}")
            return False
        
        if not self.frontend_dir.exists():
            print(f"✗ 前端目录不存在: {self.frontend_dir}")
            return False
        
        print("✓ 依赖检查通过")
        return True
    
    def install_backend_dependencies(self):
        """安装后端依赖"""
        print("\n📦 安装后端依赖...")
        
        requirements_file = self.backend_dir / "requirements.txt"
        if not requirements_file.exists():
            print("⚠ requirements.txt不存在，跳过依赖安装")
            return True
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
            ], cwd=self.backend_dir, timeout=300)
            
            if result.returncode == 0:
                print("✓ 后端依赖安装成功")
                return True
            else:
                print("✗ 后端依赖安装失败")
                return False
        except Exception as e:
            print(f"✗ 后端依赖安装异常: {e}")
            return False
    
    def install_frontend_dependencies(self):
        """安装前端依赖"""
        print("\n📦 安装前端依赖...")
        
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print("⚠ package.json不存在，跳过依赖安装")
            return True
        
        try:
            result = subprocess.run(['npm', 'install'], 
                                  cwd=self.frontend_dir, timeout=300)
            
            if result.returncode == 0:
                print("✓ 前端依赖安装成功")
                return True
            else:
                print("✗ 前端依赖安装失败")
                return False
        except Exception as e:
            print(f"✗ 前端依赖安装异常: {e}")
            return False
    
    def start_backend(self):
        """启动后端服务"""
        print("\n🚀 启动后端服务...")
        
        try:
            # 启动FastAPI服务
            self.backend_process = subprocess.Popen([
                sys.executable, '-m', 'uvicorn', 'app.main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=self.backend_dir)
            
            print("✓ 后端服务启动中...")
            print("   - 服务地址: http://localhost:8000")
            print("   - API文档: http://localhost:8000/docs")
            
            # 等待服务启动
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("✓ 后端服务启动成功")
                return True
            else:
                print("✗ 后端服务启动失败")
                return False
                
        except Exception as e:
            print(f"✗ 后端服务启动异常: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("\n🚀 启动前端服务...")
        
        try:
            # 启动Vue开发服务器
            self.frontend_process = subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=self.frontend_dir)
            
            print("✓ 前端服务启动中...")
            print("   - 服务地址: http://localhost:5173")
            
            # 等待服务启动
            time.sleep(5)
            
            if self.frontend_process.poll() is None:
                print("✓ 前端服务启动成功")
                return True
            else:
                print("✗ 前端服务启动失败")
                return False
                
        except Exception as e:
            print(f"✗ 前端服务启动异常: {e}")
            return False
    
    def wait_for_services(self):
        """等待服务运行"""
        print("\n⏳ 系统运行中...")
        print("="*60)
        print("🌟 多模态面试评估系统已启动")
        print("="*60)
        print("📱 前端界面: http://localhost:5173")
        print("🔧 后端API: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("="*60)
        print("💡 使用说明:")
        print("   1. 打开浏览器访问前端界面")
        print("   2. 选择技术领域和职位")
        print("   3. 开始面试评估")
        print("   4. 查看分析报告")
        print("="*60)
        print("⚠️  按 Ctrl+C 停止系统")
        print("="*60)
        
        try:
            # 监控服务状态
            while True:
                time.sleep(5)
                
                # 检查后端服务
                if self.backend_process and self.backend_process.poll() is not None:
                    print("⚠️ 后端服务意外停止")
                    break
                
                # 检查前端服务
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("⚠️ 前端服务意外停止")
                    break
                    
        except KeyboardInterrupt:
            print("\n🛑 用户请求停止系统")
    
    def stop_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止服务...")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=10)
                print("✓ 后端服务已停止")
            except Exception as e:
                print(f"⚠️ 停止后端服务时出错: {e}")
                try:
                    self.backend_process.kill()
                except:
                    pass
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=10)
                print("✓ 前端服务已停止")
            except Exception as e:
                print(f"⚠️ 停止前端服务时出错: {e}")
                try:
                    self.frontend_process.kill()
                except:
                    pass
        
        print("✅ 系统已完全停止")
    
    def run(self, install_deps=True):
        """运行系统"""
        try:
            print("🚀 多模态面试评估系统启动器")
            print("="*50)
            
            # 1. 检查依赖
            if not self.check_dependencies():
                return 1
            
            # 2. 安装依赖（可选）
            if install_deps:
                if not self.install_backend_dependencies():
                    return 1
                if not self.install_frontend_dependencies():
                    return 1
            
            # 3. 启动后端
            if not self.start_backend():
                return 1
            
            # 4. 启动前端
            if not self.start_frontend():
                self.stop_services()
                return 1
            
            # 5. 等待运行
            self.wait_for_services()
            
            return 0
            
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            return 1
        finally:
            self.stop_services()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="多模态面试评估系统启动器")
    parser.add_argument("--no-install", action="store_true", 
                       help="跳过依赖安装")
    
    args = parser.parse_args()
    
    starter = SystemStarter()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print("\n🛑 接收到停止信号")
        starter.stop_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行系统
    exit_code = starter.run(install_deps=not args.no_install)
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
