/**
 * iFlytek 星火大模型智能面试系统 - 布局一致性标准化
 * Layout Consistency Standards for iFlytek Spark Interview System
 *
 * 版本: 1.0
 * 更新: 2025-07-20
 *
 * 功能特性:
 * - 统一组件间距和边距
 * - Element Plus组件样式一致性
 * - 响应式设计标准化
 * - iFlytek品牌视觉规范
 */

/* ===== 全局布局标准 ===== */

/* 容器标准化 */
.container,
.demo-container,
.section-container,
.interview-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
}

/* 页面间距标准化 */
.page {
  padding: 40px 0;
  min-height: calc(100vh - 120px);
}

.section {
  padding: 60px 0;
  margin-bottom: 40px;
}

.section:last-child {
  margin-bottom: 0;
}

/* ===== 卡片组件一致性 ===== */

/* Element Plus 卡片标准化 */
.el-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.el-card__header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
  background: rgba(24, 144, 255, 0.02);
}

.el-card__body {
  padding: 24px;
}

/* 自定义卡片样式 */
.feature-card,
.tech-item,
.candidate-info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 24px;
}

.feature-card:hover,
.tech-item:hover,
.candidate-info-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* ===== 按钮组件一致性 ===== */

/* Element Plus 按钮标准化 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.el-button--primary {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-color: #1890ff;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #40a9ff, #69c0ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.el-button--secondary {
  background: white;
  color: #1890ff;
  border-color: #1890ff;
}

.el-button--secondary:hover {
  background: #1890ff;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 按钮尺寸标准化 */
.el-button--large {
  padding: 12px 24px;
  font-size: 16px;
  min-height: 48px;
}

.el-button--default {
  padding: 8px 16px;
  font-size: 14px;
  min-height: 40px;
}

.el-button--small {
  padding: 6px 12px;
  font-size: 12px;
  min-height: 32px;
}

/* ===== 表单组件一致性 ===== */

/* Element Plus 表单标准化 */
.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.el-input,
.el-select,
.el-textarea {
  border-radius: 8px;
}

.el-input__inner,
.el-select__inner,
.el-textarea__inner {
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.el-input__inner:focus,
.el-select__inner:focus,
.el-textarea__inner:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* ===== 网格布局一致性 ===== */

/* 技术特性网格 */
.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

/* 产品功能网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
  margin-top: 48px;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

/* ===== 间距标准化 ===== */

/* 标题间距 */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 16px 0;
}

h1 {
  margin-bottom: 24px;
}

h2 {
  margin-bottom: 20px;
}

/* 段落间距 */
p {
  margin: 0 0 16px 0;
  line-height: 1.6;
}

p:last-child {
  margin-bottom: 0;
}

/* 列表间距 */
ul, ol {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* ===== 响应式布局标准 ===== */

/* 平板端适配 */
@media (max-width: 768px) {
  .container,
  .demo-container,
  .section-container,
  .interview-container {
    padding: 0 16px;
  }
  
  .page {
    padding: 24px 0;
  }
  
  .section {
    padding: 40px 0;
    margin-bottom: 24px;
  }
  
  .el-card__header,
  .el-card__body {
    padding: 16px 20px;
  }
  
  .feature-card,
  .tech-item,
  .candidate-info-card {
    padding: 20px;
    margin-bottom: 16px;
  }
  
  .tech-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    margin-top: 24px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-top: 32px;
  }
}

/* 手机端适配 */
@media (max-width: 480px) {
  .container,
  .demo-container,
  .section-container,
  .interview-container {
    padding: 0 12px;
  }
  
  .page {
    padding: 16px 0;
  }
  
  .section {
    padding: 24px 0;
    margin-bottom: 16px;
  }
  
  .el-card__header,
  .el-card__body {
    padding: 12px 16px;
  }
  
  .feature-card,
  .tech-item,
  .candidate-info-card {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-top: 16px;
  }
  
  .features-grid {
    gap: 16px;
    margin-top: 24px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-top: 16px;
  }
  
  /* 按钮在移动端的适配 */
  .el-button--large {
    padding: 10px 20px;
    font-size: 14px;
    min-height: 44px;
  }
  
  .el-button--default {
    padding: 8px 14px;
    font-size: 13px;
    min-height: 36px;
  }
  
  .el-button--small {
    padding: 6px 10px;
    font-size: 11px;
    min-height: 28px;
  }
}

/* ===== 特殊组件布局 ===== */

/* 面试页面布局 */
.interview-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 32px;
  min-height: calc(100vh - 200px);
}

@media (max-width: 1024px) {
  .interview-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

/* 仪表板布局 */
.dashboard-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 24px;
  min-height: calc(100vh - 120px);
}

@media (max-width: 768px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
