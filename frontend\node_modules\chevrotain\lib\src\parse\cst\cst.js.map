{"version": 3, "file": "cst.js", "sourceRoot": "", "sources": ["../../../../src/parse/cst/cst.ts"], "names": [], "mappings": "AAEA;;;;;;GAMG;AACH,MAAM,UAAU,yBAAyB,CACvC,gBAAiC,EACjC,eAAoE;IAEpE,yCAAyC;IACzC,IAAI,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;QAChD,0EAA0E;QAC1E,qEAAqE;QACrE,kDAAkD;QAClD,gBAAgB,CAAC,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;QAC3D,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;KACxD;IACD,oFAAoF;IACpF,qDAAqD;IACrD,kFAAkF;IAClF,2FAA2F;SACtF,IAAI,gBAAgB,CAAC,SAAU,GAAG,eAAe,CAAC,SAAS,KAAK,IAAI,EAAE;QACzE,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;KACxD;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,mBAAmB,CACjC,gBAAiC,EACjC,eAAgC;IAEhC,yCAAyC;IACzC,IAAI,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;QAChD,0EAA0E;QAC1E,qEAAqE;QACrE,+CAA+C;QAC/C,gBAAgB,CAAC,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;QAC3D,gBAAgB,CAAC,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;QAC3D,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;QACvD,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;QACvD,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;QACvD,gBAAgB,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;KACpD;IACD,oFAAoF;IACpF,qDAAqD;IACrD,kFAAkF;IAClF,2FAA2F;SACtF,IAAI,gBAAgB,CAAC,SAAU,GAAG,eAAe,CAAC,SAAU,KAAK,IAAI,EAAE;QAC1E,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;QACvD,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;QACvD,gBAAgB,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;KACpD;AACH,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,IAAa,EACb,KAAa,EACb,aAAqB;IAErB,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,SAAS,EAAE;QAC9C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KACxC;SAAM;QACL,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC1C;AACH,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,IAAa,EACb,QAAgB,EAChB,UAAe;IAEf,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE;QACzC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACxC;SAAM;QACL,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC1C;AACH,CAAC"}