/******************************************************************************
 * Copyright 2022 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/
import type { Interface, Type, TypeDefinition } from '../../../languages/generated/ast.js';
import type { PlainAstTypes, PlainPropertyType } from './plain-types.js';
export declare function collectDeclaredTypes(interfaces: Interface[], unions: Type[]): PlainAstTypes;
export declare function typeDefinitionToPropertyType(type: TypeDefinition): PlainPropertyType;
//# sourceMappingURL=declared-types.d.ts.map