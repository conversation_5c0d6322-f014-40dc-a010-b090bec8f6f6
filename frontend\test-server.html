<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .test-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .test-title {
            color: #1890ff;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .test-status {
            background: #f6ffed;
            border: 2px solid #52c41a;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .test-status h3 {
            color: #52c41a;
            margin-bottom: 15px;
        }

        .test-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .test-link {
            display: inline-block;
            padding: 12px 24px;
            background: #1890ff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-link:hover {
            background: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .test-link.enhanced {
            background: #52c41a;
        }

        .test-link.enhanced:hover {
            background: #73d13d;
        }

        .server-info {
            background: #f0f0f0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🚀 服务器测试页面</h1>
        
        <div class="test-status">
            <h3>✅ 服务器状态检测</h3>
            <p>如果您能看到这个页面，说明服务器正在正常运行！</p>
        </div>

        <div class="server-info">
            <strong>当前访问地址：</strong><br>
            <span id="current-url"></span><br><br>
            <strong>测试时间：</strong><br>
            <span id="current-time"></span>
        </div>

        <div class="test-links">
            <a href="http://localhost:5173/" class="test-link">
                🏠 返回首页
            </a>
            <a href="http://localhost:5173/enhanced-demo" class="test-link enhanced">
                🎯 测试增强演示
            </a>
            <a href="http://localhost:5173/demo" class="test-link">
                📱 产品演示
            </a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #f0f0f0;">
            <h4>🔧 故障排除步骤</h4>
            <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
                <li>确认开发服务器已启动 (npm run dev)</li>
                <li>检查端口5173是否被占用</li>
                <li>清除浏览器缓存</li>
                <li>检查控制台错误信息</li>
                <li>重启开发服务器</li>
            </ol>
        </div>
    </div>

    <script>
        // 显示当前URL和时间
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');

        // 测试链接点击
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('点击测试链接:', this.href);
                // 不阻止默认行为，让链接正常跳转
            });
        });

        console.log('🎉 服务器测试页面加载成功！');
        console.log('当前时间:', new Date().toLocaleString('zh-CN'));
        console.log('当前URL:', window.location.href);
    </script>
</body>
</html>
