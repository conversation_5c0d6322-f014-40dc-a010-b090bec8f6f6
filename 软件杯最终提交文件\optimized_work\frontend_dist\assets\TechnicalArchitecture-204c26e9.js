import{_ as x,f as M,a as I,D as A,m as B,E as S,G as w,H as D,I as L,t as F,J as V,K as z,l as $,e as E,g as G,i as f,j as s,k as a,n as e,F as u,x as _,p as n,w as r,B as h,C as v,z as o,A as p}from"./index-b6a2842e.js";const N={name:"TechnicalArchitecture",components:{Cpu:M,Monitor:I,Grid:A,Microphone:B,VideoCamera:S,Document:w,Check:D,Close:L,TrendCharts:F,Bottom:V,Timer:z,Lock:$,Connection:E,Tools:G},setup(){return{techLayers:[{id:1,name:"应用层",icon:"Document",components:[{id:1,name:"面试管理",description:"面试流程管理和调度",icon:"Timer",status:"active"},{id:2,name:"实时交互",description:"音视频实时通信",icon:"VideoCamera",status:"active"},{id:3,name:"报告生成",description:"智能评估报告",icon:"DataBoard",status:"active"}]},{id:2,name:"AI服务层",icon:"Cpu",components:[{id:1,name:"Spark大模型",description:"iFlytek核心AI引擎",icon:"Cpu",status:"active"},{id:2,name:"语音识别",description:"实时语音转文字",icon:"Microphone",status:"active"},{id:3,name:"视频分析",description:"表情行为分析",icon:"VideoCamera",status:"active"}]},{id:3,name:"基础设施层",icon:"Monitor",components:[{id:1,name:"云计算平台",description:"弹性计算资源",icon:"Monitor",status:"stable"},{id:2,name:"安全防护",description:"数据安全保障",icon:"Lock",status:"stable"},{id:3,name:"网络通信",description:"高速网络连接",icon:"Connection",status:"stable"}]}],coreTechnologies:[{id:1,name:"iFlytek Spark 大模型",icon:"Cpu",description:"基于科大讯飞星火认知大模型，具备强大的语言理解和生成能力",metrics:[{label:"参数规模",value:"千亿级"},{label:"响应时间",value:"<100ms"},{label:"准确率",value:"98.5%"}],features:["多轮对话理解","专业领域知识","实时推理能力","中文优化处理"]},{id:2,name:"多模态融合分析",icon:"DataBoard",description:"融合语音、视频、文本多维度信息，提供全面的候选人评估",metrics:[{label:"模态数量",value:"3+"},{label:"融合精度",value:"96.8%"},{label:"处理速度",value:"实时"}],features:["语音情感分析","视频行为识别","文本语义理解","综合能力评估"]},{id:3,name:"智能防作弊系统",icon:"Lock",description:"基于AI的智能监控系统，确保面试过程的公平性和真实性",metrics:[{label:"检测准确率",value:"99.2%"},{label:"误报率",value:"<0.5%"},{label:"响应时间",value:"<50ms"}],features:["人脸识别验证","行为异常检测","环境监控分析","实时预警提醒"]}],comparisonItems:[{feature:"语音识别准确率",traditional:"85-90%",iflytek:"98.5%+"},{feature:"实时处理能力",traditional:"延迟明显",iflytek:"<100ms响应"},{feature:"多模态融合",traditional:"单一维度",iflytek:"3+维度融合"},{feature:"中文优化",traditional:"基础支持",iflytek:"深度优化"},{feature:"防作弊机制",traditional:"人工监控",iflytek:"AI智能检测"}],performanceMetrics:[{id:1,label:"系统可用性",value:"99.9%",icon:"Monitor",trend:"up",trendText:"持续稳定"},{id:2,label:"响应时间",value:"<100ms",icon:"Timer",trend:"up",trendText:"极速响应"},{id:3,label:"并发处理",value:"10000+",icon:"Connection",trend:"up",trendText:"高并发"},{id:4,label:"准确率",value:"98.5%",icon:"TrendCharts",trend:"up",trendText:"行业领先"}],getStatusText:k=>({active:"运行中",stable:"稳定",warning:"警告",error:"错误"})[k]||"未知"}}},j={class:"technical-architecture"},H={class:"architecture-overview"},J={class:"container"},K={class:"architecture-diagram"},q={class:"layer-header"},O={class:"layer-icon"},P={class:"layer-title"},Q={class:"layer-components"},R={class:"component-icon"},U={class:"component-name"},W={class:"component-desc"},X={class:"component-status"},Y={class:"status-text"},Z={class:"core-technologies"},ee={class:"container"},te={class:"tech-grid"},se={class:"tech-header"},ae={class:"tech-icon professional-glow"},oe={class:"tech-title"},ie={class:"tech-content"},ce={class:"tech-description"},ne={class:"tech-metrics"},le={class:"metric-value"},re={class:"metric-label"},de={class:"tech-features"},ue={class:"tech-comparison"},_e={class:"container"},me={class:"comparison-table"},pe={class:"feature-cell"},he={class:"competitor-cell"},ve={class:"iflytek-cell"},fe={class:"performance-metrics"},ke={class:"container"},ye={class:"metrics-grid"},be={class:"metric-icon data-visualization-glow"},Ce={class:"metric-value"},Te={class:"metric-label"},ge={class:"metric-trend"},xe={class:"trend-text"};function Me(b,c,C,m,T,k){const l=f("el-icon"),y=f("Check"),g=f("Close");return s(),a("div",j,[e("section",H,[e("div",J,[c[0]||(c[0]=e("div",{class:"section-header"},[e("h2",{class:"section-title"},"iFlytek Spark 技术架构"),e("p",{class:"section-subtitle"},"基于科大讯飞星火大模型的多模态AI面试系统")],-1)),e("div",K,[(s(!0),a(u,null,_(m.techLayers,(t,d)=>(s(),a("div",{class:"tech-layer",key:t.id},[e("div",q,[e("div",O,[n(l,null,{default:r(()=>[(s(),h(v(t.icon)))]),_:2},1024)]),e("h3",P,o(t.name),1)]),e("div",Q,[(s(!0),a(u,null,_(t.components,i=>(s(),a("div",{key:i.id,class:p(["component-card card-modern-hover",`delay-${d*100+i.id*50}`])},[e("div",R,[n(l,null,{default:r(()=>[(s(),h(v(i.icon)))]),_:2},1024)]),e("h4",U,o(i.name),1),e("p",W,o(i.description),1),e("div",X,[e("span",{class:p(["status-dot",i.status])},null,2),e("span",Y,o(m.getStatusText(i.status)),1)])],2))),128))])]))),128))])])]),e("section",Z,[e("div",ee,[c[1]||(c[1]=e("h2",{class:"section-title"},"核心技术优势",-1)),e("div",te,[(s(!0),a(u,null,_(m.coreTechnologies,(t,d)=>(s(),a("div",{key:t.id,class:p(["tech-card enterprise-fade-scale",`delay-${d*100}`])},[e("div",se,[e("div",ae,[n(l,null,{default:r(()=>[(s(),h(v(t.icon)))]),_:2},1024)]),e("h3",oe,o(t.name),1)]),e("div",ie,[e("p",ce,o(t.description),1),e("div",ne,[(s(!0),a(u,null,_(t.metrics,i=>(s(),a("div",{class:"metric",key:i.label},[e("span",le,o(i.value),1),e("span",re,o(i.label),1)]))),128))]),e("div",de,[(s(!0),a(u,null,_(t.features,i=>(s(),a("div",{class:"feature-item",key:i},[n(l,null,{default:r(()=>[n(y)]),_:1}),e("span",null,o(i),1)]))),128))])])],2))),128))])])]),e("section",ue,[e("div",_e,[c[3]||(c[3]=e("h2",{class:"section-title"},"技术对比优势",-1)),e("div",me,[c[2]||(c[2]=e("div",{class:"comparison-header"},[e("div",{class:"feature-column"},"功能特性"),e("div",{class:"competitor-column"},"传统方案"),e("div",{class:"iflytek-column"},"iFlytek Spark")],-1)),(s(!0),a(u,null,_(m.comparisonItems,(t,d)=>(s(),a("div",{key:t.feature,class:p(["comparison-row card-modern-entrance",`delay-${d*100}`])},[e("div",pe,o(t.feature),1),e("div",he,[n(l,{class:"status-icon error"},{default:r(()=>[n(g)]),_:1}),e("span",null,o(t.traditional),1)]),e("div",ve,[n(l,{class:"status-icon success"},{default:r(()=>[n(y)]),_:1}),e("span",null,o(t.iflytek),1)])],2))),128))])])]),e("section",fe,[e("div",ke,[c[4]||(c[4]=e("h2",{class:"section-title"},"性能指标",-1)),e("div",ye,[(s(!0),a(u,null,_(m.performanceMetrics,(t,d)=>(s(),a("div",{key:t.id,class:p(["metric-card stats-card-hina-style",`delay-${d*150}`])},[e("div",be,[n(l,null,{default:r(()=>[(s(),h(v(t.icon)))]),_:2},1024)]),e("div",Ce,o(t.value),1),e("div",Te,o(t.label),1),e("div",ge,[n(l,{class:p(["trend-icon",t.trend])},{default:r(()=>[(s(),h(v(t.trend==="up"?"TrendCharts":"Bottom")))]),_:2},1032,["class"]),e("span",xe,o(t.trendText),1)])],2))),128))])])])])}const Ae=x(N,[["render",Me],["__scopeId","data-v-58a0474f"]]);export{Ae as default};
