import{aC as Qe,aD as Ha,aE as Lo}from"./index-b6a2842e.js";var ht=function(a){return a&&a.Math===Math&&a},_=ht(typeof globalThis=="object"&&globalThis)||ht(typeof window=="object"&&window)||ht(typeof self=="object"&&self)||ht(typeof Qe=="object"&&Qe)||ht(typeof Qe=="object"&&Qe)||function(){return this}()||Function("return this")(),Et={},V=function(a){try{return!!a()}catch{return!0}},Pl=V,he=!Pl(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!==7}),Rl=V,br=!Rl(function(){var a=(function(){}).bind();return typeof a!="function"||a.hasOwnProperty("prototype")}),Nl=br,Ft=Function.prototype.call,Y=Nl?Ft.bind(Ft):function(){return Ft.apply(Ft,arguments)},ko={},Bo={}.propertyIsEnumerable,jo=Object.getOwnPropertyDescriptor,Il=jo&&!Bo.call({1:2},1);ko.f=Il?function(e){var t=jo(this,e);return!!t&&t.enumerable}:Bo;var Oi=function(a,e){return{enumerable:!(a&1),configurable:!(a&2),writable:!(a&4),value:e}},Fo=br,Uo=Function.prototype,Ya=Uo.call,Ml=Fo&&Uo.bind.bind(Ya,Ya),L=Fo?Ml:function(a){return function(){return Ya.apply(a,arguments)}},Go=L,_l=Go({}.toString),Dl=Go("".slice),je=function(a){return Dl(_l(a),8,-1)},Vl=L,Ll=V,kl=je,Zr=Object,Bl=Vl("".split),zo=Ll(function(){return!Zr("z").propertyIsEnumerable(0)})?function(a){return kl(a)==="String"?Bl(a,""):Zr(a)}:Zr,xr=function(a){return a==null},jl=xr,Fl=TypeError,ve=function(a){if(jl(a))throw new Fl("Can't call method on "+a);return a},Ul=zo,Gl=ve,$t=function(a){return Ul(Gl(a))},Jr=typeof document=="object"&&document.all,B=typeof Jr>"u"&&Jr!==void 0?function(a){return typeof a=="function"||a===Jr}:function(a){return typeof a=="function"},zl=B,ae=function(a){return typeof a=="object"?a!==null:zl(a)},ea=_,Hl=B,Yl=function(a){return Hl(a)?a:void 0},Fe=function(a,e){return arguments.length<2?Yl(ea[a]):ea[a]&&ea[a][e]},Xl=L,Tr=Xl({}.isPrototypeOf),Wl=_,yn=Wl.navigator,mn=yn&&yn.userAgent,wt=mn?String(mn):"",Ho=_,ta=wt,bn=Ho.process,xn=Ho.Deno,Tn=bn&&bn.versions||xn&&xn.version,On=Tn&&Tn.v8,le,cr;On&&(le=On.split("."),cr=le[0]>0&&le[0]<4?1:+(le[0]+le[1]));!cr&&ta&&(le=ta.match(/Edge\/(\d+)/),(!le||le[1]>=74)&&(le=ta.match(/Chrome\/(\d+)/),le&&(cr=+le[1])));var Si=cr,Sn=Si,ql=V,Ql=_,Kl=Ql.String,Yo=!!Object.getOwnPropertySymbols&&!ql(function(){var a=Symbol("symbol detection");return!Kl(a)||!(Object(a)instanceof Symbol)||!Symbol.sham&&Sn&&Sn<41}),Zl=Yo,Xo=Zl&&!Symbol.sham&&typeof Symbol.iterator=="symbol",Jl=Fe,eh=B,th=Tr,rh=Xo,ah=Object,Wo=rh?function(a){return typeof a=="symbol"}:function(a){var e=Jl("Symbol");return eh(e)&&th(e.prototype,ah(a))},ih=String,Or=function(a){try{return ih(a)}catch{return"Object"}},nh=B,sh=Or,oh=TypeError,Te=function(a){if(nh(a))return a;throw new oh(sh(a)+" is not a function")},uh=Te,lh=xr,st=function(a,e){var t=a[e];return lh(t)?void 0:uh(t)},ra=Y,aa=B,ia=ae,hh=TypeError,vh=function(a,e){var t,r;if(e==="string"&&aa(t=a.toString)&&!ia(r=ra(t,a))||aa(t=a.valueOf)&&!ia(r=ra(t,a))||e!=="string"&&aa(t=a.toString)&&!ia(r=ra(t,a)))return r;throw new hh("Can't convert object to primitive value")},qo={exports:{}},En=_,fh=Object.defineProperty,Ei=function(a,e){try{fh(En,a,{value:e,configurable:!0,writable:!0})}catch{En[a]=e}return e},ch=_,gh=Ei,$n="__core-js_shared__",wn=qo.exports=ch[$n]||gh($n,{});(wn.versions||(wn.versions=[])).push({version:"3.44.0",mode:"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"});var $i=qo.exports,Cn=$i,wi=function(a,e){return Cn[a]||(Cn[a]=e||{})},dh=ve,ph=Object,Sr=function(a){return ph(dh(a))},yh=L,mh=Sr,bh=yh({}.hasOwnProperty),fe=Object.hasOwn||function(e,t){return bh(mh(e),t)},xh=L,Th=0,Oh=Math.random(),Sh=xh(1.1.toString),Qo=function(a){return"Symbol("+(a===void 0?"":a)+")_"+Sh(++Th+Oh,36)},Eh=_,$h=wi,An=fe,wh=Qo,Ch=Yo,Ah=Xo,Ke=Eh.Symbol,na=$h("wks"),Ph=Ah?Ke.for||Ke:Ke&&Ke.withoutSetter||wh,z=function(a){return An(na,a)||(na[a]=Ch&&An(Ke,a)?Ke[a]:Ph("Symbol."+a)),na[a]},Rh=Y,Pn=ae,Rn=Wo,Nh=st,Ih=vh,Mh=z,_h=TypeError,Dh=Mh("toPrimitive"),Vh=function(a,e){if(!Pn(a)||Rn(a))return a;var t=Nh(a,Dh),r;if(t){if(e===void 0&&(e="default"),r=Rh(t,a,e),!Pn(r)||Rn(r))return r;throw new _h("Can't convert object to primitive value")}return e===void 0&&(e="number"),Ih(a,e)},Lh=Vh,kh=Wo,Ko=function(a){var e=Lh(a,"string");return kh(e)?e:e+""},Bh=_,Nn=ae,Xa=Bh.document,jh=Nn(Xa)&&Nn(Xa.createElement),Er=function(a){return jh?Xa.createElement(a):{}},Fh=he,Uh=V,Gh=Er,Zo=!Fh&&!Uh(function(){return Object.defineProperty(Gh("div"),"a",{get:function(){return 7}}).a!==7}),zh=he,Hh=Y,Yh=ko,Xh=Oi,Wh=$t,qh=Ko,Qh=fe,Kh=Zo,In=Object.getOwnPropertyDescriptor;Et.f=zh?In:function(e,t){if(e=Wh(e),t=qh(t),Kh)try{return In(e,t)}catch{}if(Qh(e,t))return Xh(!Hh(Yh.f,e,t),e[t])};var Oe={},Zh=he,Jh=V,Jo=Zh&&Jh(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!==42}),ev=ae,tv=String,rv=TypeError,J=function(a){if(ev(a))return a;throw new rv(tv(a)+" is not an object")},av=he,iv=Zo,nv=Jo,Ut=J,Mn=Ko,sv=TypeError,sa=Object.defineProperty,ov=Object.getOwnPropertyDescriptor,oa="enumerable",ua="configurable",la="writable";Oe.f=av?nv?function(e,t,r){if(Ut(e),t=Mn(t),Ut(r),typeof e=="function"&&t==="prototype"&&"value"in r&&la in r&&!r[la]){var i=ov(e,t);i&&i[la]&&(e[t]=r.value,r={configurable:ua in r?r[ua]:i[ua],enumerable:oa in r?r[oa]:i[oa],writable:!1})}return sa(e,t,r)}:sa:function(e,t,r){if(Ut(e),t=Mn(t),Ut(r),iv)try{return sa(e,t,r)}catch{}if("get"in r||"set"in r)throw new sv("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var uv=he,lv=Oe,hv=Oi,Ct=uv?function(a,e,t){return lv.f(a,e,hv(1,t))}:function(a,e,t){return a[e]=t,a},eu={exports:{}},Wa=he,vv=fe,tu=Function.prototype,fv=Wa&&Object.getOwnPropertyDescriptor,Ci=vv(tu,"name"),cv=Ci&&(function(){}).name==="something",gv=Ci&&(!Wa||Wa&&fv(tu,"name").configurable),$r={EXISTS:Ci,PROPER:cv,CONFIGURABLE:gv},dv=L,pv=B,qa=$i,yv=dv(Function.toString);pv(qa.inspectSource)||(qa.inspectSource=function(a){return yv(a)});var Ai=qa.inspectSource,mv=_,bv=B,_n=mv.WeakMap,xv=bv(_n)&&/native code/.test(String(_n)),Tv=wi,Ov=Qo,Dn=Tv("keys"),Pi=function(a){return Dn[a]||(Dn[a]=Ov(a))},Ri={},Sv=xv,ru=_,Ev=ae,$v=Ct,ha=fe,va=$i,wv=Pi,Cv=Ri,Vn="Object already initialized",Qa=ru.TypeError,Av=ru.WeakMap,gr,Tt,dr,Pv=function(a){return dr(a)?Tt(a):gr(a,{})},Rv=function(a){return function(e){var t;if(!Ev(e)||(t=Tt(e)).type!==a)throw new Qa("Incompatible receiver, "+a+" required");return t}};if(Sv||va.state){var de=va.state||(va.state=new Av);de.get=de.get,de.has=de.has,de.set=de.set,gr=function(a,e){if(de.has(a))throw new Qa(Vn);return e.facade=a,de.set(a,e),e},Tt=function(a){return de.get(a)||{}},dr=function(a){return de.has(a)}}else{var Ye=wv("state");Cv[Ye]=!0,gr=function(a,e){if(ha(a,Ye))throw new Qa(Vn);return e.facade=a,$v(a,Ye,e),e},Tt=function(a){return ha(a,Ye)?a[Ye]:{}},dr=function(a){return ha(a,Ye)}}var wr={set:gr,get:Tt,has:dr,enforce:Pv,getterFor:Rv},Ni=L,Nv=V,Iv=B,Gt=fe,Ka=he,Mv=$r.CONFIGURABLE,_v=Ai,au=wr,Dv=au.enforce,Vv=au.get,Ln=String,or=Object.defineProperty,Lv=Ni("".slice),kv=Ni("".replace),Bv=Ni([].join),jv=Ka&&!Nv(function(){return or(function(){},"length",{value:8}).length!==8}),Fv=String(String).split("String"),Uv=eu.exports=function(a,e,t){Lv(Ln(e),0,7)==="Symbol("&&(e="["+kv(Ln(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),t&&t.getter&&(e="get "+e),t&&t.setter&&(e="set "+e),(!Gt(a,"name")||Mv&&a.name!==e)&&(Ka?or(a,"name",{value:e,configurable:!0}):a.name=e),jv&&t&&Gt(t,"arity")&&a.length!==t.arity&&or(a,"length",{value:t.arity});try{t&&Gt(t,"constructor")&&t.constructor?Ka&&or(a,"prototype",{writable:!1}):a.prototype&&(a.prototype=void 0)}catch{}var r=Dv(a);return Gt(r,"source")||(r.source=Bv(Fv,typeof e=="string"?e:"")),a};Function.prototype.toString=Uv(function(){return Iv(this)&&Vv(this).source||_v(this)},"toString");var iu=eu.exports,Gv=B,zv=Oe,Hv=iu,Yv=Ei,Ue=function(a,e,t,r){r||(r={});var i=r.enumerable,n=r.name!==void 0?r.name:e;if(Gv(t)&&Hv(t,n,r),r.global)i?a[e]=t:Yv(e,t);else{try{r.unsafe?a[e]&&(i=!0):delete a[e]}catch{}i?a[e]=t:zv.f(a,e,{value:t,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return a},nu={},Xv=Math.ceil,Wv=Math.floor,qv=Math.trunc||function(e){var t=+e;return(t>0?Wv:Xv)(t)},Qv=qv,Cr=function(a){var e=+a;return e!==e||e===0?0:Qv(e)},Kv=Cr,Zv=Math.max,Jv=Math.min,ef=function(a,e){var t=Kv(a);return t<0?Zv(t+e,0):Jv(t,e)},tf=Cr,rf=Math.min,ot=function(a){var e=tf(a);return e>0?rf(e,9007199254740991):0},af=ot,Ii=function(a){return af(a.length)},nf=$t,sf=ef,of=Ii,kn=function(a){return function(e,t,r){var i=nf(e),n=of(i);if(n===0)return!a&&-1;var o=sf(r,n),s;if(a&&t!==t){for(;n>o;)if(s=i[o++],s!==s)return!0}else for(;n>o;o++)if((a||o in i)&&i[o]===t)return a||o||0;return!a&&-1}},su={includes:kn(!0),indexOf:kn(!1)},uf=L,fa=fe,lf=$t,hf=su.indexOf,vf=Ri,Bn=uf([].push),ou=function(a,e){var t=lf(a),r=0,i=[],n;for(n in t)!fa(vf,n)&&fa(t,n)&&Bn(i,n);for(;e.length>r;)fa(t,n=e[r++])&&(~hf(i,n)||Bn(i,n));return i},Mi=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ff=ou,cf=Mi,gf=cf.concat("length","prototype");nu.f=Object.getOwnPropertyNames||function(e){return ff(e,gf)};var uu={};uu.f=Object.getOwnPropertySymbols;var df=Fe,pf=L,yf=nu,mf=uu,bf=J,xf=pf([].concat),Tf=df("Reflect","ownKeys")||function(e){var t=yf.f(bf(e)),r=mf.f;return r?xf(t,r(e)):t},jn=fe,Of=Tf,Sf=Et,Ef=Oe,$f=function(a,e,t){for(var r=Of(e),i=Ef.f,n=Sf.f,o=0;o<r.length;o++){var s=r[o];!jn(a,s)&&!(t&&jn(t,s))&&i(a,s,n(e,s))}},wf=V,Cf=B,Af=/#|\.prototype\./,At=function(a,e){var t=Rf[Pf(a)];return t===If?!0:t===Nf?!1:Cf(e)?wf(e):!!e},Pf=At.normalize=function(a){return String(a).replace(Af,".").toLowerCase()},Rf=At.data={},Nf=At.NATIVE="N",If=At.POLYFILL="P",lu=At,zt=_,Mf=Et.f,_f=Ct,Df=Ue,Vf=Ei,Lf=$f,kf=lu,ee=function(a,e){var t=a.target,r=a.global,i=a.stat,n,o,s,u,l,h;if(r?o=zt:i?o=zt[t]||Vf(t,{}):o=zt[t]&&zt[t].prototype,o)for(s in e){if(l=e[s],a.dontCallGetSet?(h=Mf(o,s),u=h&&h.value):u=o[s],n=kf(r?s:t+(i?".":"#")+s,a.forced),!n&&u!==void 0){if(typeof l==typeof u)continue;Lf(l,u)}(a.sham||u&&u.sham)&&_f(l,"sham",!0),Df(o,s,l,a)}},vt=_,Bf=wt,jf=je,Ht=function(a){return Bf.slice(0,a.length)===a},hu=function(){return Ht("Bun/")?"BUN":Ht("Cloudflare-Workers")?"CLOUDFLARE":Ht("Deno/")?"DENO":Ht("Node.js/")?"NODE":vt.Bun&&typeof Bun.version=="string"?"BUN":vt.Deno&&typeof Deno.version=="object"?"DENO":jf(vt.process)==="process"?"NODE":vt.window&&vt.document?"BROWSER":"REST"}(),Ff=hu,Ar=Ff==="NODE",Uf=_,Gf=Uf,zf=L,Hf=Te,Yf=function(a,e,t){try{return zf(Hf(Object.getOwnPropertyDescriptor(a,e)[t]))}catch{}},Xf=ae,Wf=function(a){return Xf(a)||a===null},qf=Wf,Qf=String,Kf=TypeError,Zf=function(a){if(qf(a))return a;throw new Kf("Can't set "+Qf(a)+" as a prototype")},Jf=Yf,ec=ae,tc=ve,rc=Zf,vu=Object.setPrototypeOf||("__proto__"in{}?function(){var a=!1,e={},t;try{t=Jf(Object.prototype,"__proto__","set"),t(e,[]),a=e instanceof Array}catch{}return function(i,n){return tc(i),rc(n),ec(i)&&(a?t(i,n):i.__proto__=n),i}}():void 0),ac=Oe.f,ic=fe,nc=z,Fn=nc("toStringTag"),Pr=function(a,e,t){a&&!t&&(a=a.prototype),a&&!ic(a,Fn)&&ac(a,Fn,{configurable:!0,value:e})},Un=iu,sc=Oe,oc=function(a,e,t){return t.get&&Un(t.get,e,{getter:!0}),t.set&&Un(t.set,e,{setter:!0}),sc.f(a,e,t)},uc=Fe,lc=oc,hc=z,vc=he,Gn=hc("species"),fc=function(a){var e=uc(a);vc&&e&&!e[Gn]&&lc(e,Gn,{configurable:!0,get:function(){return this}})},cc=Tr,gc=TypeError,dc=function(a,e){if(cc(e,a))return a;throw new gc("Incorrect invocation")},pc=z,yc=pc("toStringTag"),fu={};fu[yc]="z";var mc=String(fu)==="[object z]",bc=mc,xc=B,ur=je,Tc=z,Oc=Tc("toStringTag"),Sc=Object,Ec=ur(function(){return arguments}())==="Arguments",$c=function(a,e){try{return a[e]}catch{}},_i=bc?ur:function(a){var e,t,r;return a===void 0?"Undefined":a===null?"Null":typeof(t=$c(e=Sc(a),Oc))=="string"?t:Ec?ur(e):(r=ur(e))==="Object"&&xc(e.callee)?"Arguments":r},wc=L,Cc=V,cu=B,Ac=_i,Pc=Fe,Rc=Ai,gu=function(){},du=Pc("Reflect","construct"),Di=/^\s*(?:class|function)\b/,Nc=wc(Di.exec),Ic=!Di.test(gu),ft=function(e){if(!cu(e))return!1;try{return du(gu,[],e),!0}catch{return!1}},pu=function(e){if(!cu(e))return!1;switch(Ac(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Ic||!!Nc(Di,Rc(e))}catch{return!0}};pu.sham=!0;var Mc=!du||Cc(function(){var a;return ft(ft.call)||!ft(Object)||!ft(function(){a=!0})||a})?pu:ft,_c=Mc,Dc=Or,Vc=TypeError,Lc=function(a){if(_c(a))return a;throw new Vc(Dc(a)+" is not a constructor")},zn=J,kc=Lc,Bc=xr,jc=z,Fc=jc("species"),yu=function(a,e){var t=zn(a).constructor,r;return t===void 0||Bc(r=zn(t)[Fc])?e:kc(r)},Uc=br,mu=Function.prototype,Hn=mu.apply,Yn=mu.call,bu=typeof Reflect=="object"&&Reflect.apply||(Uc?Yn.bind(Hn):function(){return Yn.apply(Hn,arguments)}),Gc=je,zc=L,Rr=function(a){if(Gc(a)==="Function")return zc(a)},Xn=Rr,Hc=Te,Yc=br,Xc=Xn(Xn.bind),Vi=function(a,e){return Hc(a),e===void 0?a:Yc?Xc(a,e):function(){return a.apply(e,arguments)}},Wc=Fe,xu=Wc("document","documentElement"),qc=L,Qc=qc([].slice),Kc=TypeError,Zc=function(a,e){if(a<e)throw new Kc("Not enough arguments");return a},Jc=wt,Tu=/(?:ipad|iphone|ipod).*applewebkit/i.test(Jc),ie=_,eg=bu,tg=Vi,Wn=B,rg=fe,Ou=V,qn=xu,ag=Qc,Qn=Er,ig=Zc,ng=Tu,sg=Ar,Za=ie.setImmediate,Ja=ie.clearImmediate,og=ie.process,ca=ie.Dispatch,ug=ie.Function,Kn=ie.MessageChannel,lg=ie.String,ga=0,bt={},Zn="onreadystatechange",Ot,Ie,da,pa;Ou(function(){Ot=ie.location});var Li=function(a){if(rg(bt,a)){var e=bt[a];delete bt[a],e()}},ya=function(a){return function(){Li(a)}},Jn=function(a){Li(a.data)},es=function(a){ie.postMessage(lg(a),Ot.protocol+"//"+Ot.host)};(!Za||!Ja)&&(Za=function(e){ig(arguments.length,1);var t=Wn(e)?e:ug(e),r=ag(arguments,1);return bt[++ga]=function(){eg(t,void 0,r)},Ie(ga),ga},Ja=function(e){delete bt[e]},sg?Ie=function(a){og.nextTick(ya(a))}:ca&&ca.now?Ie=function(a){ca.now(ya(a))}:Kn&&!ng?(da=new Kn,pa=da.port2,da.port1.onmessage=Jn,Ie=tg(pa.postMessage,pa)):ie.addEventListener&&Wn(ie.postMessage)&&!ie.importScripts&&Ot&&Ot.protocol!=="file:"&&!Ou(es)?(Ie=es,ie.addEventListener("message",Jn,!1)):Zn in Qn("script")?Ie=function(a){qn.appendChild(Qn("script"))[Zn]=function(){qn.removeChild(this),Li(a)}}:Ie=function(a){setTimeout(ya(a),0)});var Su={set:Za,clear:Ja},ts=_,hg=he,vg=Object.getOwnPropertyDescriptor,fg=function(a){if(!hg)return ts[a];var e=vg(ts,a);return e&&e.value},Eu=function(){this.head=null,this.tail=null};Eu.prototype={add:function(a){var e={item:a,next:null},t=this.tail;t?t.next=e:this.head=e,this.tail=e},get:function(){var a=this.head;if(a){var e=this.head=a.next;return e===null&&(this.tail=null),a.item}}};var $u=Eu,cg=wt,gg=/ipad|iphone|ipod/i.test(cg)&&typeof Pebble<"u",dg=wt,pg=/web0s(?!.*chrome)/i.test(dg),at=_,yg=fg,rs=Vi,ma=Su.set,mg=$u,bg=Tu,xg=gg,Tg=pg,ba=Ar,as=at.MutationObserver||at.WebKitMutationObserver,is=at.document,ns=at.process,Yt=at.Promise,ei=yg("queueMicrotask"),Xe,xa,Ta,Xt,ss;if(!ei){var Wt=new mg,qt=function(){var a,e;for(ba&&(a=ns.domain)&&a.exit();e=Wt.get();)try{e()}catch(t){throw Wt.head&&Xe(),t}a&&a.enter()};!bg&&!ba&&!Tg&&as&&is?(xa=!0,Ta=is.createTextNode(""),new as(qt).observe(Ta,{characterData:!0}),Xe=function(){Ta.data=xa=!xa}):!xg&&Yt&&Yt.resolve?(Xt=Yt.resolve(void 0),Xt.constructor=Yt,ss=rs(Xt.then,Xt),Xe=function(){ss(qt)}):ba?Xe=function(){ns.nextTick(qt)}:(ma=rs(ma,at),Xe=function(){ma(qt)}),ei=function(a){Wt.head||Xe(),Wt.add(a)}}var Og=ei,Sg=function(a,e){try{arguments.length===1?console.error(a):console.error(a,e)}catch{}},ki=function(a){try{return{error:!1,value:a()}}catch(e){return{error:!0,value:e}}},Eg=_,Nr=Eg.Promise,$g=_,xt=Nr,wg=B,Cg=lu,Ag=Ai,Pg=z,os=hu,Oa=Si;xt&&xt.prototype;var Rg=Pg("species"),ti=!1,wu=wg($g.PromiseRejectionEvent),Ng=Cg("Promise",function(){var a=Ag(xt),e=a!==String(xt);if(!e&&Oa===66)return!0;if(!Oa||Oa<51||!/native code/.test(a)){var t=new xt(function(n){n(1)}),r=function(n){n(function(){},function(){})},i=t.constructor={};if(i[Rg]=r,ti=t.then(function(){})instanceof r,!ti)return!0}return!e&&(os==="BROWSER"||os==="DENO")&&!wu}),Pt={CONSTRUCTOR:Ng,REJECTION_EVENT:wu,SUBCLASSING:ti},ut={},us=Te,Ig=TypeError,Mg=function(a){var e,t;this.promise=new a(function(r,i){if(e!==void 0||t!==void 0)throw new Ig("Bad Promise constructor");e=r,t=i}),this.resolve=us(e),this.reject=us(t)};ut.f=function(a){return new Mg(a)};var _g=ee,pr=Ar,we=_,Dg=Gf,it=Y,ls=Ue,hs=vu,Vg=Pr,Lg=fc,kg=Te,lr=B,Bg=ae,jg=dc,Fg=yu,Cu=Su.set,Bi=Og,Ug=Sg,Gg=ki,zg=$u,Au=wr,yr=Nr,ji=Pt,Pu=ut,Ir="Promise",Ru=ji.CONSTRUCTOR,Hg=ji.REJECTION_EVENT,Yg=ji.SUBCLASSING,Sa=Au.getterFor(Ir),Xg=Au.set,qe=yr&&yr.prototype,Le=yr,Qt=qe,Nu=we.TypeError,ri=we.document,Fi=we.process,ai=Pu.f,Wg=ai,qg=!!(ri&&ri.createEvent&&we.dispatchEvent),Iu="unhandledrejection",Qg="rejectionhandled",vs=0,Mu=1,Kg=2,Ui=1,_u=2,Kt,fs,Du,cs,Vu=function(a){var e;return Bg(a)&&lr(e=a.then)?e:!1},Lu=function(a,e){var t=e.value,r=e.state===Mu,i=r?a.ok:a.fail,n=a.resolve,o=a.reject,s=a.domain,u,l,h;try{i?(r||(e.rejection===_u&&Jg(e),e.rejection=Ui),i===!0?u=t:(s&&s.enter(),u=i(t),s&&(s.exit(),h=!0)),u===a.promise?o(new Nu("Promise-chain cycle")):(l=Vu(u))?it(l,u,n,o):n(u)):o(t)}catch(f){s&&!h&&s.exit(),o(f)}},ku=function(a,e){a.notified||(a.notified=!0,Bi(function(){for(var t=a.reactions,r;r=t.get();)Lu(r,a);a.notified=!1,e&&!a.rejection&&Zg(a)}))},Bu=function(a,e,t){var r,i;qg?(r=ri.createEvent("Event"),r.promise=e,r.reason=t,r.initEvent(a,!1,!0),we.dispatchEvent(r)):r={promise:e,reason:t},!Hg&&(i=we["on"+a])?i(r):a===Iu&&Ug("Unhandled promise rejection",t)},Zg=function(a){it(Cu,we,function(){var e=a.facade,t=a.value,r=gs(a),i;if(r&&(i=Gg(function(){pr?Fi.emit("unhandledRejection",t,e):Bu(Iu,e,t)}),a.rejection=pr||gs(a)?_u:Ui,i.error))throw i.value})},gs=function(a){return a.rejection!==Ui&&!a.parent},Jg=function(a){it(Cu,we,function(){var e=a.facade;pr?Fi.emit("rejectionHandled",e):Bu(Qg,e,a.value)})},Ze=function(a,e,t){return function(r){a(e,r,t)}},et=function(a,e,t){a.done||(a.done=!0,t&&(a=t),a.value=e,a.state=Kg,ku(a,!0))},ii=function(a,e,t){if(!a.done){a.done=!0,t&&(a=t);try{if(a.facade===e)throw new Nu("Promise can't be resolved itself");var r=Vu(e);r?Bi(function(){var i={done:!1};try{it(r,e,Ze(ii,i,a),Ze(et,i,a))}catch(n){et(i,n,a)}}):(a.value=e,a.state=Mu,ku(a,!1))}catch(i){et({done:!1},i,a)}}};if(Ru&&(Le=function(e){jg(this,Qt),kg(e),it(Kt,this);var t=Sa(this);try{e(Ze(ii,t),Ze(et,t))}catch(r){et(t,r)}},Qt=Le.prototype,Kt=function(e){Xg(this,{type:Ir,done:!1,notified:!1,parent:!1,reactions:new zg,rejection:!1,state:vs,value:null})},Kt.prototype=ls(Qt,"then",function(e,t){var r=Sa(this),i=ai(Fg(this,Le));return r.parent=!0,i.ok=lr(e)?e:!0,i.fail=lr(t)&&t,i.domain=pr?Fi.domain:void 0,r.state===vs?r.reactions.add(i):Bi(function(){Lu(i,r)}),i.promise}),fs=function(){var a=new Kt,e=Sa(a);this.promise=a,this.resolve=Ze(ii,e),this.reject=Ze(et,e)},Pu.f=ai=function(a){return a===Le||a===Du?new fs(a):Wg(a)},lr(yr)&&qe!==Object.prototype)){cs=qe.then,Yg||ls(qe,"then",function(e,t){var r=this;return new Le(function(i,n){it(cs,r,i,n)}).then(e,t)},{unsafe:!0});try{delete qe.constructor}catch{}hs&&hs(qe,Qt)}_g({global:!0,constructor:!0,wrap:!0,forced:Ru},{Promise:Le});Du=Dg.Promise;Vg(Le,Ir,!1);Lg(Ir);var Rt={},ed=z,td=Rt,rd=ed("iterator"),ad=Array.prototype,id=function(a){return a!==void 0&&(td.Array===a||ad[rd]===a)},nd=_i,ds=st,sd=xr,od=Rt,ud=z,ld=ud("iterator"),ju=function(a){if(!sd(a))return ds(a,ld)||ds(a,"@@iterator")||od[nd(a)]},hd=Y,vd=Te,fd=J,cd=Or,gd=ju,dd=TypeError,pd=function(a,e){var t=arguments.length<2?gd(a):e;if(vd(t))return fd(hd(t,a));throw new dd(cd(a)+" is not iterable")},yd=Y,ps=J,md=st,bd=function(a,e,t){var r,i;ps(a);try{if(r=md(a,"return"),!r){if(e==="throw")throw t;return t}r=yd(r,a)}catch(n){i=!0,r=n}if(e==="throw")throw t;if(i)throw r;return ps(r),t},xd=Vi,Td=Y,Od=J,Sd=Or,Ed=id,$d=Ii,ys=Tr,wd=pd,Cd=ju,ms=bd,Ad=TypeError,hr=function(a,e){this.stopped=a,this.result=e},bs=hr.prototype,Fu=function(a,e,t){var r=t&&t.that,i=!!(t&&t.AS_ENTRIES),n=!!(t&&t.IS_RECORD),o=!!(t&&t.IS_ITERATOR),s=!!(t&&t.INTERRUPTED),u=xd(e,r),l,h,f,c,v,g,d,p=function(x){return l&&ms(l,"normal"),new hr(!0,x)},y=function(x){return i?(Od(x),s?u(x[0],x[1],p):u(x[0],x[1])):s?u(x,p):u(x)};if(n)l=a.iterator;else if(o)l=a;else{if(h=Cd(a),!h)throw new Ad(Sd(a)+" is not iterable");if(Ed(h)){for(f=0,c=$d(a);c>f;f++)if(v=y(a[f]),v&&ys(bs,v))return v;return new hr(!1)}l=wd(a,h)}for(g=n?a.next:l.next;!(d=Td(g,l)).done;){try{v=y(d.value)}catch(x){ms(l,"throw",x)}if(typeof v=="object"&&v&&ys(bs,v))return v}return new hr(!1)},Pd=z,Uu=Pd("iterator"),Gu=!1;try{var Rd=0,xs={next:function(){return{done:!!Rd++}},return:function(){Gu=!0}};xs[Uu]=function(){return this},Array.from(xs,function(){throw 2})}catch{}var Nd=function(a,e){try{if(!e&&!Gu)return!1}catch{return!1}var t=!1;try{var r={};r[Uu]=function(){return{next:function(){return{done:t=!0}}}},a(r)}catch{}return t},Id=Nr,Md=Nd,_d=Pt.CONSTRUCTOR,zu=_d||!Md(function(a){Id.all(a).then(void 0,function(){})}),Dd=ee,Vd=Y,Ld=Te,kd=ut,Bd=ki,jd=Fu,Fd=zu;Dd({target:"Promise",stat:!0,forced:Fd},{all:function(e){var t=this,r=kd.f(t),i=r.resolve,n=r.reject,o=Bd(function(){var s=Ld(t.resolve),u=[],l=0,h=1;jd(e,function(f){var c=l++,v=!1;h++,Vd(s,t,f).then(function(g){v||(v=!0,u[c]=g,--h||i(u))},n)}),--h||i(u)});return o.error&&n(o.value),r.promise}});var Ud=ee,Gd=Pt.CONSTRUCTOR,ni=Nr,zd=Fe,Hd=B,Yd=Ue,Ts=ni&&ni.prototype;Ud({target:"Promise",proto:!0,forced:Gd,real:!0},{catch:function(a){return this.then(void 0,a)}});if(Hd(ni)){var Os=zd("Promise").prototype.catch;Ts.catch!==Os&&Yd(Ts,"catch",Os,{unsafe:!0})}var Xd=ee,Wd=Y,qd=Te,Qd=ut,Kd=ki,Zd=Fu,Jd=zu;Xd({target:"Promise",stat:!0,forced:Jd},{race:function(e){var t=this,r=Qd.f(t),i=r.reject,n=Kd(function(){var o=qd(t.resolve);Zd(e,function(s){Wd(o,t,s).then(r.resolve,i)})});return n.error&&i(n.value),r.promise}});var ep=ee,tp=ut,rp=Pt.CONSTRUCTOR;ep({target:"Promise",stat:!0,forced:rp},{reject:function(e){var t=tp.f(this),r=t.reject;return r(e),t.promise}});var ap=J,ip=ae,np=ut,sp=function(a,e){if(ap(a),ip(e)&&e.constructor===a)return e;var t=np.f(a),r=t.resolve;return r(e),t.promise},op=ee,up=Fe,lp=Pt.CONSTRUCTOR,hp=sp;up("Promise");op({target:"Promise",stat:!0,forced:lp},{resolve:function(e){return hp(this,e)}});function Ss(a,e,t,r,i,n,o){try{var s=a[n](o),u=s.value}catch(l){return void t(l)}s.done?e(u):Promise.resolve(u).then(r,i)}function xe(a){return function(){var e=this,t=arguments;return new Promise(function(r,i){var n=a.apply(e,t);function o(u){Ss(n,r,i,o,s,"next",u)}function s(u){Ss(n,r,i,o,s,"throw",u)}o(void 0)})}}var vp=_i,fp=String,pe=function(a){if(vp(a)==="Symbol")throw new TypeError("Cannot convert a Symbol value to a string");return fp(a)},cp=J,Hu=function(){var a=cp(this),e="";return a.hasIndices&&(e+="d"),a.global&&(e+="g"),a.ignoreCase&&(e+="i"),a.multiline&&(e+="m"),a.dotAll&&(e+="s"),a.unicode&&(e+="u"),a.unicodeSets&&(e+="v"),a.sticky&&(e+="y"),e},Gi=V,gp=_,zi=gp.RegExp,Hi=Gi(function(){var a=zi("a","y");return a.lastIndex=2,a.exec("abcd")!==null}),dp=Hi||Gi(function(){return!zi("a","y").sticky}),pp=Hi||Gi(function(){var a=zi("^r","gy");return a.lastIndex=2,a.exec("str")!==null}),Yu={BROKEN_CARET:pp,MISSED_STICKY:dp,UNSUPPORTED_Y:Hi},Xu={},yp=ou,mp=Mi,bp=Object.keys||function(e){return yp(e,mp)},xp=he,Tp=Jo,Op=Oe,Sp=J,Ep=$t,$p=bp;Xu.f=xp&&!Tp?Object.defineProperties:function(e,t){Sp(e);for(var r=Ep(t),i=$p(t),n=i.length,o=0,s;n>o;)Op.f(e,s=i[o++],r[s]);return e};var wp=J,Cp=Xu,Es=Mi,Ap=Ri,Pp=xu,Rp=Er,Np=Pi,$s=">",ws="<",si="prototype",oi="script",Wu=Np("IE_PROTO"),Ea=function(){},qu=function(a){return ws+oi+$s+a+ws+"/"+oi+$s},Cs=function(a){a.write(qu("")),a.close();var e=a.parentWindow.Object;return a=null,e},Ip=function(){var a=Rp("iframe"),e="java"+oi+":",t;return a.style.display="none",Pp.appendChild(a),a.src=String(e),t=a.contentWindow.document,t.open(),t.write(qu("document.F=Object")),t.close(),t.F},Zt,vr=function(){try{Zt=new ActiveXObject("htmlfile")}catch{}vr=typeof document<"u"?document.domain&&Zt?Cs(Zt):Ip():Cs(Zt);for(var a=Es.length;a--;)delete vr[si][Es[a]];return vr()};Ap[Wu]=!0;var Yi=Object.create||function(e,t){var r;return e!==null?(Ea[si]=wp(e),r=new Ea,Ea[si]=null,r[Wu]=e):r=vr(),t===void 0?r:Cp.f(r,t)},Mp=V,_p=_,Dp=_p.RegExp,Vp=Mp(function(){var a=Dp(".","s");return!(a.dotAll&&a.test(`
`)&&a.flags==="s")}),Lp=V,kp=_,Bp=kp.RegExp,jp=Lp(function(){var a=Bp("(?<a>b)","g");return a.exec("b").groups.a!=="b"||"b".replace(a,"$<a>c")!=="bc"}),Je=Y,Mr=L,Fp=pe,Up=Hu,Gp=Yu,zp=wi,Hp=Yi,Yp=wr.get,Xp=Vp,Wp=jp,qp=zp("native-string-replace",String.prototype.replace),mr=RegExp.prototype.exec,ui=mr,Qp=Mr("".charAt),Kp=Mr("".indexOf),Zp=Mr("".replace),$a=Mr("".slice),li=function(){var a=/a/,e=/b*/g;return Je(mr,a,"a"),Je(mr,e,"a"),a.lastIndex!==0||e.lastIndex!==0}(),Qu=Gp.BROKEN_CARET,hi=/()??/.exec("")[1]!==void 0,Jp=li||hi||Qu||Xp||Wp;Jp&&(ui=function(e){var t=this,r=Yp(t),i=Fp(e),n=r.raw,o,s,u,l,h,f,c;if(n)return n.lastIndex=t.lastIndex,o=Je(ui,n,i),t.lastIndex=n.lastIndex,o;var v=r.groups,g=Qu&&t.sticky,d=Je(Up,t),p=t.source,y=0,x=i;if(g&&(d=Zp(d,"y",""),Kp(d,"g")===-1&&(d+="g"),x=$a(i,t.lastIndex),t.lastIndex>0&&(!t.multiline||t.multiline&&Qp(i,t.lastIndex-1)!==`
`)&&(p="(?: "+p+")",x=" "+x,y++),s=new RegExp("^(?:"+p+")",d)),hi&&(s=new RegExp("^"+p+"$(?!\\s)",d)),li&&(u=t.lastIndex),l=Je(mr,g?s:t,x),g?l?(l.input=$a(l.input,y),l[0]=$a(l[0],y),l.index=t.lastIndex,t.lastIndex+=l[0].length):t.lastIndex=0:li&&l&&(t.lastIndex=t.global?l.index+l[0].length:u),hi&&l&&l.length>1&&Je(qp,l[0],s,function(){for(h=1;h<arguments.length-2;h++)arguments[h]===void 0&&(l[h]=void 0)}),l&&v)for(l.groups=f=Hp(null),h=0;h<v.length;h++)c=v[h],f[c[0]]=l[c[1]];return l});var Xi=ui,ey=ee,As=Xi;ey({target:"RegExp",proto:!0,forced:/./.exec!==As},{exec:As});var Ps=Y,Rs=Ue,ty=Xi,Ns=V,Ku=z,ry=Ct,ay=Ku("species"),wa=RegExp.prototype,Wi=function(a,e,t,r){var i=Ku(a),n=!Ns(function(){var l={};return l[i]=function(){return 7},""[a](l)!==7}),o=n&&!Ns(function(){var l=!1,h=/a/;return a==="split"&&(h={},h.constructor={},h.constructor[ay]=function(){return h},h.flags="",h[i]=/./[i]),h.exec=function(){return l=!0,null},h[i](""),!l});if(!n||!o||t){var s=/./[i],u=e(i,""[a],function(l,h,f,c,v){var g=h.exec;return g===ty||g===wa.exec?n&&!v?{done:!0,value:Ps(s,h,f,c)}:{done:!0,value:Ps(l,f,h,c)}:{done:!1}});Rs(String.prototype,a,u[0]),Rs(wa,i,u[1])}r&&ry(wa[i],"sham",!0)},qi=L,iy=Cr,ny=pe,sy=ve,oy=qi("".charAt),Is=qi("".charCodeAt),uy=qi("".slice),Ms=function(a){return function(e,t){var r=ny(sy(e)),i=iy(t),n=r.length,o,s;return i<0||i>=n?a?"":void 0:(o=Is(r,i),o<55296||o>56319||i+1===n||(s=Is(r,i+1))<56320||s>57343?a?oy(r,i):o:a?uy(r,i,i+2):(o-55296<<10)+(s-56320)+65536)}},ly={codeAt:Ms(!1),charAt:Ms(!0)},hy=ly.charAt,Qi=function(a,e,t){return e+(t?hy(a,e).length:1)},vy=_,fy=V,_s=vy.RegExp,cy=!fy(function(){var a=!0;try{_s(".","d")}catch{a=!1}var e={},t="",r=a?"dgimsy":"gimsy",i=function(u,l){Object.defineProperty(e,u,{get:function(){return t+=l,!0}})},n={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};a&&(n.hasIndices="d");for(var o in n)i(o,n[o]);var s=Object.getOwnPropertyDescriptor(_s.prototype,"flags").get.call(e);return s!==r||t!==r}),gy={correct:cy},dy=Y,py=fe,yy=Tr,Ds=gy,my=Hu,by=RegExp.prototype,Ki=Ds.correct?function(a){return a.flags}:function(a){return!Ds.correct&&yy(by,a)&&!py(a,"flags")?dy(my,a):a.flags},Vs=Y,xy=J,Ty=B,Oy=je,Sy=Xi,Ey=TypeError,Zi=function(a,e){var t=a.exec;if(Ty(t)){var r=Vs(t,a,e);return r!==null&&xy(r),r}if(Oy(a)==="RegExp")return Vs(Sy,a,e);throw new Ey("RegExp#exec called on incompatible receiver")},$y=Y,wy=L,Cy=Wi,Ay=J,Py=ae,Ry=ot,Jt=pe,Ny=ve,Iy=st,My=Qi,_y=Ki,Ls=Zi,ks=wy("".indexOf);Cy("match",function(a,e,t){return[function(i){var n=Ny(this),o=Py(i)?Iy(i,a):void 0;return o?$y(o,i,n):new RegExp(i)[a](Jt(n))},function(r){var i=Ay(this),n=Jt(r),o=t(e,i,n);if(o.done)return o.value;var s=Jt(_y(i));if(ks(s,"g")===-1)return Ls(i,n);var u=ks(s,"u")!==-1;i.lastIndex=0;for(var l=[],h=0,f;(f=Ls(i,n))!==null;){var c=Jt(f[0]);l[h]=c,c===""&&(i.lastIndex=My(n,Ry(i.lastIndex),u)),h++}return h===0?null:l}]});var Ji=L,Dy=Sr,Vy=Math.floor,Ca=Ji("".charAt),Ly=Ji("".replace),Aa=Ji("".slice),ky=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,By=/\$([$&'`]|\d{1,2})/g,jy=function(a,e,t,r,i,n){var o=t+a.length,s=r.length,u=By;return i!==void 0&&(i=Dy(i),u=ky),Ly(n,u,function(l,h){var f;switch(Ca(h,0)){case"$":return"$";case"&":return a;case"`":return Aa(e,0,t);case"'":return Aa(e,o);case"<":f=i[Aa(h,1,-1)];break;default:var c=+h;if(c===0)return l;if(c>s){var v=Vy(c/10);return v===0?l:v<=s?r[v-1]===void 0?Ca(h,1):r[v-1]+Ca(h,1):l}f=r[c-1]}return f===void 0?"":f})},Fy=bu,Bs=Y,_r=L,Uy=Wi,Gy=V,zy=J,Hy=B,Yy=ae,Xy=Cr,Wy=ot,Me=pe,qy=ve,Qy=Qi,Ky=st,Zy=jy,Jy=Ki,em=Zi,tm=z,vi=tm("replace"),rm=Math.max,am=Math.min,im=_r([].concat),Pa=_r([].push),er=_r("".indexOf),js=_r("".slice),nm=function(a){return a===void 0?a:String(a)},sm=function(){return"a".replace(/./,"$0")==="$0"}(),Fs=function(){return/./[vi]?/./[vi]("a","$0")==="":!1}(),om=!Gy(function(){var a=/./;return a.exec=function(){var e=[];return e.groups={a:"7"},e},"".replace(a,"$<a>")!=="7"});Uy("replace",function(a,e,t){var r=Fs?"$":"$0";return[function(n,o){var s=qy(this),u=Yy(n)?Ky(n,vi):void 0;return u?Bs(u,n,s,o):Bs(e,Me(s),n,o)},function(i,n){var o=zy(this),s=Me(i);if(typeof n=="string"&&er(n,r)===-1&&er(n,"$<")===-1){var u=t(e,o,s,n);if(u.done)return u.value}var l=Hy(n);l||(n=Me(n));var h=Me(Jy(o)),f=er(h,"g")!==-1,c;f&&(c=er(h,"u")!==-1,o.lastIndex=0);for(var v=[],g;g=em(o,s),!(g===null||(Pa(v,g),!f));){var d=Me(g[0]);d===""&&(o.lastIndex=Qy(s,Wy(o.lastIndex),c))}for(var p="",y=0,x=0;x<v.length;x++){g=v[x];for(var b=Me(g[0]),T=rm(am(Xy(g.index),s.length),0),$=[],E,O=1;O<g.length;O++)Pa($,nm(g[O]));var C=g.groups;if(l){var P=im([b],$,T,s);C!==void 0&&Pa(P,C),E=Me(Fy(n,void 0,P))}else E=Zy(b,s,T,$,C,n);T>=y&&(p+=js(s,y,T)+E,y=T+b.length)}return p+js(s,y)}]},!om||!sm||Fs);var um=ae,lm=je,hm=z,vm=hm("match"),fm=function(a){var e;return um(a)&&((e=a[vm])!==void 0?!!e:lm(a)==="RegExp")},cm=fm,gm=TypeError,en=function(a){if(cm(a))throw new gm("The method doesn't accept regular expressions");return a},dm=z,pm=dm("match"),tn=function(a){var e=/./;try{"/./"[a](e)}catch{try{return e[pm]=!1,"/./"[a](e)}catch{}}return!1},ym=ee,mm=Rr,bm=Et.f,xm=ot,Us=pe,Tm=en,Om=ve,Sm=tn,Em=mm("".slice),$m=Math.min,Zu=Sm("startsWith"),wm=!Zu&&!!function(){var a=bm(String.prototype,"startsWith");return a&&!a.writable}();ym({target:"String",proto:!0,forced:!wm&&!Zu},{startsWith:function(e){var t=Us(Om(this));Tm(e);var r=xm($m(arguments.length>1?arguments[1]:void 0,t.length)),i=Us(e);return Em(t,r,r+i.length)===i}});var Cm=z,Am=Yi,Pm=Oe.f,fi=Cm("unscopables"),ci=Array.prototype;ci[fi]===void 0&&Pm(ci,fi,{configurable:!0,value:Am(null)});var Rm=function(a){ci[fi][a]=!0},Nm=V,Im=!Nm(function(){function a(){}return a.prototype.constructor=null,Object.getPrototypeOf(new a)!==a.prototype}),Mm=fe,_m=B,Dm=Sr,Vm=Pi,Lm=Im,Gs=Vm("IE_PROTO"),gi=Object,km=gi.prototype,Ju=Lm?gi.getPrototypeOf:function(a){var e=Dm(a);if(Mm(e,Gs))return e[Gs];var t=e.constructor;return _m(t)&&e instanceof t?t.prototype:e instanceof gi?km:null},Bm=V,jm=B,Fm=ae,zs=Ju,Um=Ue,Gm=z,di=Gm("iterator"),el=!1,ke,Ra,Na;[].keys&&(Na=[].keys(),"next"in Na?(Ra=zs(zs(Na)),Ra!==Object.prototype&&(ke=Ra)):el=!0);var zm=!Fm(ke)||Bm(function(){var a={};return ke[di].call(a)!==a});zm&&(ke={});jm(ke[di])||Um(ke,di,function(){return this});var tl={IteratorPrototype:ke,BUGGY_SAFARI_ITERATORS:el},Hm=tl.IteratorPrototype,Ym=Yi,Xm=Oi,Wm=Pr,qm=Rt,Qm=function(){return this},Km=function(a,e,t,r){var i=e+" Iterator";return a.prototype=Ym(Hm,{next:Xm(+!r,t)}),Wm(a,i,!1),qm[i]=Qm,a},Zm=ee,Jm=Y,rl=$r,e0=B,t0=Km,Hs=Ju,Ys=vu,r0=Pr,a0=Ct,Ia=Ue,i0=z,n0=Rt,al=tl,s0=rl.PROPER,o0=rl.CONFIGURABLE,Xs=al.IteratorPrototype,tr=al.BUGGY_SAFARI_ITERATORS,ct=i0("iterator"),Ws="keys",gt="values",qs="entries",u0=function(){return this},l0=function(a,e,t,r,i,n,o){t0(t,e,r);var s=function(y){if(y===i&&c)return c;if(!tr&&y&&y in h)return h[y];switch(y){case Ws:return function(){return new t(this,y)};case gt:return function(){return new t(this,y)};case qs:return function(){return new t(this,y)}}return function(){return new t(this)}},u=e+" Iterator",l=!1,h=a.prototype,f=h[ct]||h["@@iterator"]||i&&h[i],c=!tr&&f||s(i),v=e==="Array"&&h.entries||f,g,d,p;if(v&&(g=Hs(v.call(new a)),g!==Object.prototype&&g.next&&(Hs(g)!==Xs&&(Ys?Ys(g,Xs):e0(g[ct])||Ia(g,ct,u0)),r0(g,u,!0))),s0&&i===gt&&f&&f.name!==gt&&(o0?a0(h,"name",gt):(l=!0,c=function(){return Jm(f,this)})),i)if(d={values:s(gt),keys:n?c:s(Ws),entries:s(qs)},o)for(p in d)(tr||l||!(p in h))&&Ia(h,p,d[p]);else Zm({target:e,proto:!0,forced:tr||l},d);return h[ct]!==c&&Ia(h,ct,c,{name:i}),n0[e]=c,d},h0=function(a,e){return{value:a,done:e}},v0=$t,rn=Rm,Qs=Rt,il=wr,f0=Oe.f,c0=l0,rr=h0,g0=he,nl="Array Iterator",d0=il.set,p0=il.getterFor(nl),y0=c0(Array,"Array",function(a,e){d0(this,{type:nl,target:v0(a),index:0,kind:e})},function(){var a=p0(this),e=a.target,t=a.index++;if(!e||t>=e.length)return a.target=null,rr(void 0,!0);switch(a.kind){case"keys":return rr(t,!1);case"values":return rr(e[t],!1)}return rr([t,e[t]],!1)},"values"),Ks=Qs.Arguments=Qs.Array;rn("keys");rn("values");rn("entries");if(g0&&Ks.name!=="values")try{f0(Ks,"name",{value:"values"})}catch{}var m0={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},b0=Er,Ma=b0("span").classList,Zs=Ma&&Ma.constructor&&Ma.constructor.prototype,x0=Zs===Object.prototype?void 0:Zs,Js=_,sl=m0,T0=x0,pt=y0,eo=Ct,O0=Pr,S0=z,_a=S0("iterator"),Da=pt.values,ol=function(a,e){if(a){if(a[_a]!==Da)try{eo(a,_a,Da)}catch{a[_a]=Da}if(O0(a,e,!0),sl[e]){for(var t in pt)if(a[t]!==pt[t])try{eo(a,t,pt[t])}catch{a[t]=pt[t]}}}};for(var Va in sl)ol(Js[Va]&&Js[Va].prototype,Va);ol(T0,"DOMTokenList");function E0(a,e){if(Ha(a)!="object"||!a)return a;var t=a[Symbol.toPrimitive];if(t!==void 0){var r=t.call(a,e||"default");if(Ha(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(a)}function $0(a){var e=E0(a,"string");return Ha(e)=="symbol"?e:e+""}function an(a,e,t){return(e=$0(e))in a?Object.defineProperty(a,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[e]=t,a}var w0=Te,C0=Sr,A0=zo,P0=Ii,to=TypeError,ro="Reduce of empty array with no initial value",ao=function(a){return function(e,t,r,i){var n=C0(e),o=A0(n),s=P0(n);if(w0(t),s===0&&r<2)throw new to(ro);var u=a?s-1:0,l=a?-1:1;if(r<2)for(;;){if(u in o){i=o[u],u+=l;break}if(u+=l,a?u<0:s<=u)throw new to(ro)}for(;a?u>=0:s>u;u+=l)u in o&&(i=t(i,o[u],u,n));return i}},R0={left:ao(!1),right:ao(!0)},N0=V,ul=function(a,e){var t=[][a];return!!t&&N0(function(){t.call(null,e||function(){return 1},1)})},I0=ee,M0=R0.left,_0=ul,io=Si,D0=Ar,V0=!D0&&io>79&&io<83,L0=V0||!_0("reduce");I0({target:"Array",proto:!0,forced:L0},{reduce:function(e){var t=arguments.length;return M0(this,e,t,t>1?arguments[1]:void 0)}});var k0=ee,B0=Rr,j0=Et.f,F0=ot,no=pe,U0=en,G0=ve,z0=tn,H0=B0("".slice),Y0=Math.min,ll=z0("endsWith"),X0=!ll&&!!function(){var a=j0(String.prototype,"endsWith");return a&&!a.writable}();k0({target:"String",proto:!0,forced:!X0&&!ll},{endsWith:function(e){var t=no(G0(this));U0(e);var r=arguments.length>1?arguments[1]:void 0,i=t.length,n=r===void 0?i:Y0(F0(r),i),o=no(e);return H0(t,n-o.length,n)===o}});var La=Y,hl=L,W0=Wi,q0=J,Q0=ae,K0=ve,Z0=yu,J0=Qi,eb=ot,so=pe,tb=st,oo=Zi,rb=Yu,ab=V,We=rb.UNSUPPORTED_Y,ib=4294967295,nb=Math.min,ka=hl([].push),Ba=hl("".slice),sb=!ab(function(){var a=/(?:)/,e=a.exec;a.exec=function(){return e.apply(this,arguments)};var t="ab".split(a);return t.length!==2||t[0]!=="a"||t[1]!=="b"}),uo="abbc".split(/(b)*/)[1]==="c"||"test".split(/(?:)/,-1).length!==4||"ab".split(/(?:ab)*/).length!==2||".".split(/(.?)(.?)/).length!==4||".".split(/()()/).length>1||"".split(/.?/).length;W0("split",function(a,e,t){var r="0".split(void 0,0).length?function(i,n){return i===void 0&&n===0?[]:La(e,this,i,n)}:e;return[function(n,o){var s=K0(this),u=Q0(n)?tb(n,a):void 0;return u?La(u,n,s,o):La(r,so(s),n,o)},function(i,n){var o=q0(this),s=so(i);if(!uo){var u=t(r,o,s,n,r!==e);if(u.done)return u.value}var l=Z0(o,RegExp),h=o.unicode,f=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(We?"g":"y"),c=new l(We?"^(?:"+o.source+")":o,f),v=n===void 0?ib:n>>>0;if(v===0)return[];if(s.length===0)return oo(c,s)===null?[s]:[];for(var g=0,d=0,p=[];d<s.length;){c.lastIndex=We?0:d;var y=oo(c,We?Ba(s,d):s),x;if(y===null||(x=nb(eb(c.lastIndex+(We?d:0)),s.length))===g)d=J0(s,d,h);else{if(ka(p,Ba(s,g,d)),p.length===v)return p;for(var b=1;b<=y.length-1;b++)if(ka(p,y[b]),p.length===v)return p;d=g=x}}return ka(p,Ba(s,g)),p}]},uo||!sb,We);var Dr={exports:{}},yt={exports:{}};(function(){var a,e,t,r,i,n;typeof performance<"u"&&performance!==null&&performance.now?yt.exports=function(){return performance.now()}:typeof process<"u"&&process!==null&&process.hrtime?(yt.exports=function(){return(a()-i)/1e6},e=process.hrtime,a=function(){var o;return o=e(),o[0]*1e9+o[1]},r=a(),n=process.uptime()*1e9,i=r-n):Date.now?(yt.exports=function(){return Date.now()-t},t=Date.now()):(yt.exports=function(){return new Date().getTime()-t},t=new Date().getTime())}).call(Qe);var ob=yt.exports,ub=ob,be=typeof window>"u"?Qe:window,ar=["moz","webkit"],tt="AnimationFrame",nt=be["request"+tt],St=be["cancel"+tt]||be["cancelRequest"+tt];for(var dt=0;!nt&&dt<ar.length;dt++)nt=be[ar[dt]+"Request"+tt],St=be[ar[dt]+"Cancel"+tt]||be[ar[dt]+"CancelRequest"+tt];if(!nt||!St){var ja=0,lo=0,_e=[],lb=1e3/60;nt=function(a){if(_e.length===0){var e=ub(),t=Math.max(0,lb-(e-ja));ja=t+e,setTimeout(function(){var r=_e.slice(0);_e.length=0;for(var i=0;i<r.length;i++)if(!r[i].cancelled)try{r[i].callback(ja)}catch(n){setTimeout(function(){throw n},0)}},Math.round(t))}return _e.push({handle:++lo,callback:a,cancelled:!1}),lo},St=function(a){for(var e=0;e<_e.length;e++)_e[e].handle===a&&(_e[e].cancelled=!0)}}Dr.exports=function(a){return nt.call(be,a)};Dr.exports.cancel=function(){St.apply(be,arguments)};Dr.exports.polyfill=function(a){a||(a=be),a.requestAnimationFrame=nt,a.cancelAnimationFrame=St};var hb=Dr.exports;const Fa=Lo(hb);var vl=`	
\v\f\r                　\u2028\u2029\uFEFF`,vb=L,fb=ve,cb=pe,pi=vl,ho=vb("".replace),gb=RegExp("^["+pi+"]+"),db=RegExp("(^|[^"+pi+"])["+pi+"]+$"),Ua=function(a){return function(e){var t=cb(fb(e));return a&1&&(t=ho(t,gb,"")),a&2&&(t=ho(t,db,"$1")),t}},pb={start:Ua(1),end:Ua(2),trim:Ua(3)},yb=$r.PROPER,mb=V,vo=vl,fo="​᠎",bb=function(a){return mb(function(){return!!vo[a]()||fo[a]()!==fo||yb&&vo[a].name!==a})},xb=ee,Tb=pb.trim,Ob=bb;xb({target:"String",proto:!0,forced:Ob("trim")},{trim:function(){return Tb(this)}});var Sb=function(a){this.ok=!1,this.alpha=1,a.charAt(0)=="#"&&(a=a.substr(1,6)),a=a.replace(/ /g,""),a=a.toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};a=e[a]||a;for(var t=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3]),parseFloat(u[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(u){return[parseInt(u[1],16),parseInt(u[2],16),parseInt(u[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(u){return[parseInt(u[1]+u[1],16),parseInt(u[2]+u[2],16),parseInt(u[3]+u[3],16)]}}],r=0;r<t.length;r++){var i=t[r].re,n=t[r].process,o=i.exec(a);if(o){var s=n(o);this.r=s[0],this.g=s[1],this.b=s[2],s.length>3&&(this.alpha=s[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var u=this.r.toString(16),l=this.g.toString(16),h=this.b.toString(16);return u.length==1&&(u="0"+u),l.length==1&&(l="0"+l),h.length==1&&(h="0"+h),"#"+u+l+h},this.getHelpXML=function(){for(var u=new Array,l=0;l<t.length;l++)for(var h=t[l].example,f=0;f<h.length;f++)u[u.length]=h[f];for(var c in e)u[u.length]=c;var v=document.createElement("ul");v.setAttribute("id","rgbcolor-examples");for(var l=0;l<u.length;l++)try{var g=document.createElement("li"),d=new RGBColor(u[l]),p=document.createElement("div");p.style.cssText="margin: 3px; border: 1px solid black; background:"+d.toHex()+"; color:"+d.toHex(),p.appendChild(document.createTextNode("test"));var y=document.createTextNode(" "+u[l]+" -> "+d.toRGB()+" -> "+d.toHex());g.appendChild(p),g.appendChild(y),v.appendChild(g)}catch{}return v}};const yi=Lo(Sb);var Eb=ee,$b=Rr,wb=su.indexOf,Cb=ul,mi=$b([].indexOf),fl=!!mi&&1/mi([1],1,-0)<0,Ab=fl||!Cb("indexOf");Eb({target:"Array",proto:!0,forced:Ab},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return fl?mi(this,e,t)||0:wb(this,e,t)}});var Pb=ee,Rb=L,Nb=en,Ib=ve,co=pe,Mb=tn,_b=Rb("".indexOf);Pb({target:"String",proto:!0,forced:!Mb("includes")},{includes:function(e){return!!~_b(co(Ib(this)),co(Nb(e)),arguments.length>1?arguments[1]:void 0)}});var Db=je,Vb=Array.isArray||function(e){return Db(e)==="Array"},Lb=ee,kb=L,Bb=Vb,jb=kb([].reverse),go=[1,2];Lb({target:"Array",proto:!0,forced:String(go)===String(go.reverse())},{reverse:function(){return Bb(this)&&(this.length=this.length),jb(this)}});/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var cl=function(a,e){return(cl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])})(a,e)};function gl(a,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=a}cl(a,e),a.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}function Fb(a){var e="";Array.isArray(a)||(a=[a]);for(var t=0;t<a.length;t++){var r=a[t];if(r.type===m.CLOSE_PATH)e+="z";else if(r.type===m.HORIZ_LINE_TO)e+=(r.relative?"h":"H")+r.x;else if(r.type===m.VERT_LINE_TO)e+=(r.relative?"v":"V")+r.y;else if(r.type===m.MOVE_TO)e+=(r.relative?"m":"M")+r.x+" "+r.y;else if(r.type===m.LINE_TO)e+=(r.relative?"l":"L")+r.x+" "+r.y;else if(r.type===m.CURVE_TO)e+=(r.relative?"c":"C")+r.x1+" "+r.y1+" "+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===m.SMOOTH_CURVE_TO)e+=(r.relative?"s":"S")+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===m.QUAD_TO)e+=(r.relative?"q":"Q")+r.x1+" "+r.y1+" "+r.x+" "+r.y;else if(r.type===m.SMOOTH_QUAD_TO)e+=(r.relative?"t":"T")+r.x+" "+r.y;else{if(r.type!==m.ARC)throw new Error('Unexpected command type "'+r.type+'" at index '+t+".");e+=(r.relative?"a":"A")+r.rX+" "+r.rY+" "+r.xRot+" "+ +r.lArcFlag+" "+ +r.sweepFlag+" "+r.x+" "+r.y}}return e}function bi(a,e){var t=a[0],r=a[1];return[t*Math.cos(e)-r*Math.sin(e),t*Math.sin(e)+r*Math.cos(e)]}function ue(){for(var a=[],e=0;e<arguments.length;e++)a[e]=arguments[e];for(var t=0;t<a.length;t++)if(typeof a[t]!="number")throw new Error("assertNumbers arguments["+t+"] is not a number. "+typeof a[t]+" == typeof "+a[t]);return!0}var Ee=Math.PI;function Ga(a,e,t){a.lArcFlag=a.lArcFlag===0?0:1,a.sweepFlag=a.sweepFlag===0?0:1;var r=a.rX,i=a.rY,n=a.x,o=a.y;r=Math.abs(a.rX),i=Math.abs(a.rY);var s=bi([(e-n)/2,(t-o)/2],-a.xRot/180*Ee),u=s[0],l=s[1],h=Math.pow(u,2)/Math.pow(r,2)+Math.pow(l,2)/Math.pow(i,2);1<h&&(r*=Math.sqrt(h),i*=Math.sqrt(h)),a.rX=r,a.rY=i;var f=Math.pow(r,2)*Math.pow(l,2)+Math.pow(i,2)*Math.pow(u,2),c=(a.lArcFlag!==a.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(r,2)*Math.pow(i,2)-f)/f)),v=r*l/i*c,g=-i*u/r*c,d=bi([v,g],a.xRot/180*Ee);a.cX=d[0]+(e+n)/2,a.cY=d[1]+(t+o)/2,a.phi1=Math.atan2((l-g)/i,(u-v)/r),a.phi2=Math.atan2((-l-g)/i,(-u-v)/r),a.sweepFlag===0&&a.phi2>a.phi1&&(a.phi2-=2*Ee),a.sweepFlag===1&&a.phi2<a.phi1&&(a.phi2+=2*Ee),a.phi1*=180/Ee,a.phi2*=180/Ee}function po(a,e,t){ue(a,e,t);var r=a*a+e*e-t*t;if(0>r)return[];if(r===0)return[[a*t/(a*a+e*e),e*t/(a*a+e*e)]];var i=Math.sqrt(r);return[[(a*t+e*i)/(a*a+e*e),(e*t-a*i)/(a*a+e*e)],[(a*t-e*i)/(a*a+e*e),(e*t+a*i)/(a*a+e*e)]]}var U,ye=Math.PI/180;function yo(a,e,t){return(1-t)*a+t*e}function mo(a,e,t,r){return a+Math.cos(r/180*Ee)*e+Math.sin(r/180*Ee)*t}function bo(a,e,t,r){var i=1e-6,n=e-a,o=t-e,s=3*n+3*(r-t)-6*o,u=6*(o-n),l=3*n;return Math.abs(s)<i?[-l/u]:function(h,f,c){c===void 0&&(c=1e-6);var v=h*h/4-f;if(v<-c)return[];if(v<=c)return[-h/2];var g=Math.sqrt(v);return[-h/2-g,-h/2+g]}(u/s,l/s,i)}function xo(a,e,t,r,i){var n=1-i;return a*(n*n*n)+e*(3*n*n*i)+t*(3*n*i*i)+r*(i*i*i)}(function(a){function e(){return i(function(s,u,l){return s.relative&&(s.x1!==void 0&&(s.x1+=u),s.y1!==void 0&&(s.y1+=l),s.x2!==void 0&&(s.x2+=u),s.y2!==void 0&&(s.y2+=l),s.x!==void 0&&(s.x+=u),s.y!==void 0&&(s.y+=l),s.relative=!1),s})}function t(){var s=NaN,u=NaN,l=NaN,h=NaN;return i(function(f,c,v){return f.type&m.SMOOTH_CURVE_TO&&(f.type=m.CURVE_TO,s=isNaN(s)?c:s,u=isNaN(u)?v:u,f.x1=f.relative?c-s:2*c-s,f.y1=f.relative?v-u:2*v-u),f.type&m.CURVE_TO?(s=f.relative?c+f.x2:f.x2,u=f.relative?v+f.y2:f.y2):(s=NaN,u=NaN),f.type&m.SMOOTH_QUAD_TO&&(f.type=m.QUAD_TO,l=isNaN(l)?c:l,h=isNaN(h)?v:h,f.x1=f.relative?c-l:2*c-l,f.y1=f.relative?v-h:2*v-h),f.type&m.QUAD_TO?(l=f.relative?c+f.x1:f.x1,h=f.relative?v+f.y1:f.y1):(l=NaN,h=NaN),f})}function r(){var s=NaN,u=NaN;return i(function(l,h,f){if(l.type&m.SMOOTH_QUAD_TO&&(l.type=m.QUAD_TO,s=isNaN(s)?h:s,u=isNaN(u)?f:u,l.x1=l.relative?h-s:2*h-s,l.y1=l.relative?f-u:2*f-u),l.type&m.QUAD_TO){s=l.relative?h+l.x1:l.x1,u=l.relative?f+l.y1:l.y1;var c=l.x1,v=l.y1;l.type=m.CURVE_TO,l.x1=((l.relative?0:h)+2*c)/3,l.y1=((l.relative?0:f)+2*v)/3,l.x2=(l.x+2*c)/3,l.y2=(l.y+2*v)/3}else s=NaN,u=NaN;return l})}function i(s){var u=0,l=0,h=NaN,f=NaN;return function(c){if(isNaN(h)&&!(c.type&m.MOVE_TO))throw new Error("path must start with moveto");var v=s(c,u,l,h,f);return c.type&m.CLOSE_PATH&&(u=h,l=f),c.x!==void 0&&(u=c.relative?u+c.x:c.x),c.y!==void 0&&(l=c.relative?l+c.y:c.y),c.type&m.MOVE_TO&&(h=u,f=l),v}}function n(s,u,l,h,f,c){return ue(s,u,l,h,f,c),i(function(v,g,d,p){var y=v.x1,x=v.x2,b=v.relative&&!isNaN(p),T=v.x!==void 0?v.x:b?0:g,$=v.y!==void 0?v.y:b?0:d;function E(se){return se*se}v.type&m.HORIZ_LINE_TO&&u!==0&&(v.type=m.LINE_TO,v.y=v.relative?0:d),v.type&m.VERT_LINE_TO&&l!==0&&(v.type=m.LINE_TO,v.x=v.relative?0:g),v.x!==void 0&&(v.x=v.x*s+$*l+(b?0:f)),v.y!==void 0&&(v.y=T*u+v.y*h+(b?0:c)),v.x1!==void 0&&(v.x1=v.x1*s+v.y1*l+(b?0:f)),v.y1!==void 0&&(v.y1=y*u+v.y1*h+(b?0:c)),v.x2!==void 0&&(v.x2=v.x2*s+v.y2*l+(b?0:f)),v.y2!==void 0&&(v.y2=x*u+v.y2*h+(b?0:c));var O=s*h-u*l;if(v.xRot!==void 0&&(s!==1||u!==0||l!==0||h!==1))if(O===0)delete v.rX,delete v.rY,delete v.xRot,delete v.lArcFlag,delete v.sweepFlag,v.type=m.LINE_TO;else{var C=v.xRot*Math.PI/180,P=Math.sin(C),D=Math.cos(C),j=1/E(v.rX),R=1/E(v.rY),X=E(D)*j+E(P)*R,W=2*P*D*(j-R),G=E(P)*j+E(D)*R,q=X*h*h-W*u*h+G*u*u,H=W*(s*h+u*l)-2*(X*l*h+G*s*u),Q=X*l*l-W*s*l+G*s*s,N=(Math.atan2(H,q-Q)+Math.PI)%Math.PI/2,M=Math.sin(N),K=Math.cos(N);v.rX=Math.abs(O)/Math.sqrt(q*E(K)+H*M*K+Q*E(M)),v.rY=Math.abs(O)/Math.sqrt(q*E(M)-H*M*K+Q*E(K)),v.xRot=180*N/Math.PI}return v.sweepFlag!==void 0&&0>O&&(v.sweepFlag=+!v.sweepFlag),v})}function o(){return function(s){var u={};for(var l in s)u[l]=s[l];return u}}a.ROUND=function(s){function u(l){return Math.round(l*s)/s}return s===void 0&&(s=1e13),ue(s),function(l){return l.x1!==void 0&&(l.x1=u(l.x1)),l.y1!==void 0&&(l.y1=u(l.y1)),l.x2!==void 0&&(l.x2=u(l.x2)),l.y2!==void 0&&(l.y2=u(l.y2)),l.x!==void 0&&(l.x=u(l.x)),l.y!==void 0&&(l.y=u(l.y)),l.rX!==void 0&&(l.rX=u(l.rX)),l.rY!==void 0&&(l.rY=u(l.rY)),l}},a.TO_ABS=e,a.TO_REL=function(){return i(function(s,u,l){return s.relative||(s.x1!==void 0&&(s.x1-=u),s.y1!==void 0&&(s.y1-=l),s.x2!==void 0&&(s.x2-=u),s.y2!==void 0&&(s.y2-=l),s.x!==void 0&&(s.x-=u),s.y!==void 0&&(s.y-=l),s.relative=!0),s})},a.NORMALIZE_HVZ=function(s,u,l){return s===void 0&&(s=!0),u===void 0&&(u=!0),l===void 0&&(l=!0),i(function(h,f,c,v,g){if(isNaN(v)&&!(h.type&m.MOVE_TO))throw new Error("path must start with moveto");return u&&h.type&m.HORIZ_LINE_TO&&(h.type=m.LINE_TO,h.y=h.relative?0:c),l&&h.type&m.VERT_LINE_TO&&(h.type=m.LINE_TO,h.x=h.relative?0:f),s&&h.type&m.CLOSE_PATH&&(h.type=m.LINE_TO,h.x=h.relative?v-f:v,h.y=h.relative?g-c:g),h.type&m.ARC&&(h.rX===0||h.rY===0)&&(h.type=m.LINE_TO,delete h.rX,delete h.rY,delete h.xRot,delete h.lArcFlag,delete h.sweepFlag),h})},a.NORMALIZE_ST=t,a.QT_TO_C=r,a.INFO=i,a.SANITIZE=function(s){s===void 0&&(s=0),ue(s);var u=NaN,l=NaN,h=NaN,f=NaN;return i(function(c,v,g,d,p){var y=Math.abs,x=!1,b=0,T=0;if(c.type&m.SMOOTH_CURVE_TO&&(b=isNaN(u)?0:v-u,T=isNaN(l)?0:g-l),c.type&(m.CURVE_TO|m.SMOOTH_CURVE_TO)?(u=c.relative?v+c.x2:c.x2,l=c.relative?g+c.y2:c.y2):(u=NaN,l=NaN),c.type&m.SMOOTH_QUAD_TO?(h=isNaN(h)?v:2*v-h,f=isNaN(f)?g:2*g-f):c.type&m.QUAD_TO?(h=c.relative?v+c.x1:c.x1,f=c.relative?g+c.y1:c.y2):(h=NaN,f=NaN),c.type&m.LINE_COMMANDS||c.type&m.ARC&&(c.rX===0||c.rY===0||!c.lArcFlag)||c.type&m.CURVE_TO||c.type&m.SMOOTH_CURVE_TO||c.type&m.QUAD_TO||c.type&m.SMOOTH_QUAD_TO){var $=c.x===void 0?0:c.relative?c.x:c.x-v,E=c.y===void 0?0:c.relative?c.y:c.y-g;b=isNaN(h)?c.x1===void 0?b:c.relative?c.x:c.x1-v:h-v,T=isNaN(f)?c.y1===void 0?T:c.relative?c.y:c.y1-g:f-g;var O=c.x2===void 0?0:c.relative?c.x:c.x2-v,C=c.y2===void 0?0:c.relative?c.y:c.y2-g;y($)<=s&&y(E)<=s&&y(b)<=s&&y(T)<=s&&y(O)<=s&&y(C)<=s&&(x=!0)}return c.type&m.CLOSE_PATH&&y(v-d)<=s&&y(g-p)<=s&&(x=!0),x?[]:c})},a.MATRIX=n,a.ROTATE=function(s,u,l){u===void 0&&(u=0),l===void 0&&(l=0),ue(s,u,l);var h=Math.sin(s),f=Math.cos(s);return n(f,h,-h,f,u-u*f+l*h,l-u*h-l*f)},a.TRANSLATE=function(s,u){return u===void 0&&(u=0),ue(s,u),n(1,0,0,1,s,u)},a.SCALE=function(s,u){return u===void 0&&(u=s),ue(s,u),n(s,0,0,u,0,0)},a.SKEW_X=function(s){return ue(s),n(1,0,Math.atan(s),1,0,0)},a.SKEW_Y=function(s){return ue(s),n(1,Math.atan(s),0,1,0,0)},a.X_AXIS_SYMMETRY=function(s){return s===void 0&&(s=0),ue(s),n(-1,0,0,1,s,0)},a.Y_AXIS_SYMMETRY=function(s){return s===void 0&&(s=0),ue(s),n(1,0,0,-1,0,s)},a.A_TO_C=function(){return i(function(s,u,l){return m.ARC===s.type?function(h,f,c){var v,g,d,p;h.cX||Ga(h,f,c);for(var y=Math.min(h.phi1,h.phi2),x=Math.max(h.phi1,h.phi2)-y,b=Math.ceil(x/90),T=new Array(b),$=f,E=c,O=0;O<b;O++){var C=yo(h.phi1,h.phi2,O/b),P=yo(h.phi1,h.phi2,(O+1)/b),D=P-C,j=4/3*Math.tan(D*ye/4),R=[Math.cos(C*ye)-j*Math.sin(C*ye),Math.sin(C*ye)+j*Math.cos(C*ye)],X=R[0],W=R[1],G=[Math.cos(P*ye),Math.sin(P*ye)],q=G[0],H=G[1],Q=[q+j*Math.sin(P*ye),H-j*Math.cos(P*ye)],N=Q[0],M=Q[1];T[O]={relative:h.relative,type:m.CURVE_TO};var K=function(se,ge){var Se=bi([se*h.rX,ge*h.rY],h.xRot),ze=Se[0],It=Se[1];return[h.cX+ze,h.cY+It]};v=K(X,W),T[O].x1=v[0],T[O].y1=v[1],g=K(N,M),T[O].x2=g[0],T[O].y2=g[1],d=K(q,H),T[O].x=d[0],T[O].y=d[1],h.relative&&(T[O].x1-=$,T[O].y1-=E,T[O].x2-=$,T[O].y2-=E,T[O].x-=$,T[O].y-=E),$=(p=[T[O].x,T[O].y])[0],E=p[1]}return T}(s,s.relative?0:u,s.relative?0:l):s})},a.ANNOTATE_ARCS=function(){return i(function(s,u,l){return s.relative&&(u=0,l=0),m.ARC===s.type&&Ga(s,u,l),s})},a.CLONE=o,a.CALCULATE_BOUNDS=function(){var s=function(c){var v={};for(var g in c)v[g]=c[g];return v},u=e(),l=r(),h=t(),f=i(function(c,v,g){var d=h(l(u(s(c))));function p(M){M>f.maxX&&(f.maxX=M),M<f.minX&&(f.minX=M)}function y(M){M>f.maxY&&(f.maxY=M),M<f.minY&&(f.minY=M)}if(d.type&m.DRAWING_COMMANDS&&(p(v),y(g)),d.type&m.HORIZ_LINE_TO&&p(d.x),d.type&m.VERT_LINE_TO&&y(d.y),d.type&m.LINE_TO&&(p(d.x),y(d.y)),d.type&m.CURVE_TO){p(d.x),y(d.y);for(var x=0,b=bo(v,d.x1,d.x2,d.x);x<b.length;x++)0<(N=b[x])&&1>N&&p(xo(v,d.x1,d.x2,d.x,N));for(var T=0,$=bo(g,d.y1,d.y2,d.y);T<$.length;T++)0<(N=$[T])&&1>N&&y(xo(g,d.y1,d.y2,d.y,N))}if(d.type&m.ARC){p(d.x),y(d.y),Ga(d,v,g);for(var E=d.xRot/180*Math.PI,O=Math.cos(E)*d.rX,C=Math.sin(E)*d.rX,P=-Math.sin(E)*d.rY,D=Math.cos(E)*d.rY,j=d.phi1<d.phi2?[d.phi1,d.phi2]:-180>d.phi2?[d.phi2+360,d.phi1+360]:[d.phi2,d.phi1],R=j[0],X=j[1],W=function(M){var K=M[0],se=M[1],ge=180*Math.atan2(se,K)/Math.PI;return ge<R?ge+360:ge},G=0,q=po(P,-O,0).map(W);G<q.length;G++)(N=q[G])>R&&N<X&&p(mo(d.cX,O,P,N));for(var H=0,Q=po(D,-C,0).map(W);H<Q.length;H++){var N;(N=Q[H])>R&&N<X&&y(mo(d.cY,C,D,N))}}return c});return f.minX=1/0,f.maxX=-1/0,f.minY=1/0,f.maxY=-1/0,f}})(U||(U={}));var oe,dl=function(){function a(){}return a.prototype.round=function(e){return this.transform(U.ROUND(e))},a.prototype.toAbs=function(){return this.transform(U.TO_ABS())},a.prototype.toRel=function(){return this.transform(U.TO_REL())},a.prototype.normalizeHVZ=function(e,t,r){return this.transform(U.NORMALIZE_HVZ(e,t,r))},a.prototype.normalizeST=function(){return this.transform(U.NORMALIZE_ST())},a.prototype.qtToC=function(){return this.transform(U.QT_TO_C())},a.prototype.aToC=function(){return this.transform(U.A_TO_C())},a.prototype.sanitize=function(e){return this.transform(U.SANITIZE(e))},a.prototype.translate=function(e,t){return this.transform(U.TRANSLATE(e,t))},a.prototype.scale=function(e,t){return this.transform(U.SCALE(e,t))},a.prototype.rotate=function(e,t,r){return this.transform(U.ROTATE(e,t,r))},a.prototype.matrix=function(e,t,r,i,n,o){return this.transform(U.MATRIX(e,t,r,i,n,o))},a.prototype.skewX=function(e){return this.transform(U.SKEW_X(e))},a.prototype.skewY=function(e){return this.transform(U.SKEW_Y(e))},a.prototype.xSymmetry=function(e){return this.transform(U.X_AXIS_SYMMETRY(e))},a.prototype.ySymmetry=function(e){return this.transform(U.Y_AXIS_SYMMETRY(e))},a.prototype.annotateArcs=function(){return this.transform(U.ANNOTATE_ARCS())},a}(),Ub=function(a){return a===" "||a==="	"||a==="\r"||a===`
`},To=function(a){return"0".charCodeAt(0)<=a.charCodeAt(0)&&a.charCodeAt(0)<="9".charCodeAt(0)},Gb=function(a){function e(){var t=a.call(this)||this;return t.curNumber="",t.curCommandType=-1,t.curCommandRelative=!1,t.canParseCommandOrComma=!0,t.curNumberHasExp=!1,t.curNumberHasExpDigits=!1,t.curNumberHasDecimal=!1,t.curArgs=[],t}return gl(e,a),e.prototype.finish=function(t){if(t===void 0&&(t=[]),this.parse(" ",t),this.curArgs.length!==0||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,r){var i=this;r===void 0&&(r=[]);for(var n=function(f){r.push(f),i.curArgs.length=0,i.canParseCommandOrComma=!0},o=0;o<t.length;o++){var s=t[o],u=!(this.curCommandType!==m.ARC||this.curArgs.length!==3&&this.curArgs.length!==4||this.curNumber.length!==1||this.curNumber!=="0"&&this.curNumber!=="1"),l=To(s)&&(this.curNumber==="0"&&s==="0"||u);if(!To(s)||l)if(s!=="e"&&s!=="E")if(s!=="-"&&s!=="+"||!this.curNumberHasExp||this.curNumberHasExpDigits)if(s!=="."||this.curNumberHasExp||this.curNumberHasDecimal||u){if(this.curNumber&&this.curCommandType!==-1){var h=Number(this.curNumber);if(isNaN(h))throw new SyntaxError("Invalid number ending at "+o);if(this.curCommandType===m.ARC){if(this.curArgs.length===0||this.curArgs.length===1){if(0>h)throw new SyntaxError('Expected positive number, got "'+h+'" at index "'+o+'"')}else if((this.curArgs.length===3||this.curArgs.length===4)&&this.curNumber!=="0"&&this.curNumber!=="1")throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+o+'"')}this.curArgs.push(h),this.curArgs.length===zb[this.curCommandType]&&(m.HORIZ_LINE_TO===this.curCommandType?n({type:m.HORIZ_LINE_TO,relative:this.curCommandRelative,x:h}):m.VERT_LINE_TO===this.curCommandType?n({type:m.VERT_LINE_TO,relative:this.curCommandRelative,y:h}):this.curCommandType===m.MOVE_TO||this.curCommandType===m.LINE_TO||this.curCommandType===m.SMOOTH_QUAD_TO?(n({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),m.MOVE_TO===this.curCommandType&&(this.curCommandType=m.LINE_TO)):this.curCommandType===m.CURVE_TO?n({type:m.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===m.SMOOTH_CURVE_TO?n({type:m.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===m.QUAD_TO?n({type:m.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===m.ARC&&n({type:m.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!Ub(s))if(s===","&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if(s!=="+"&&s!=="-"&&s!==".")if(l)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(this.curArgs.length!==0)throw new SyntaxError("Unterminated command at index "+o+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+s+'" at index '+o+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,s!=="z"&&s!=="Z")if(s==="h"||s==="H")this.curCommandType=m.HORIZ_LINE_TO,this.curCommandRelative=s==="h";else if(s==="v"||s==="V")this.curCommandType=m.VERT_LINE_TO,this.curCommandRelative=s==="v";else if(s==="m"||s==="M")this.curCommandType=m.MOVE_TO,this.curCommandRelative=s==="m";else if(s==="l"||s==="L")this.curCommandType=m.LINE_TO,this.curCommandRelative=s==="l";else if(s==="c"||s==="C")this.curCommandType=m.CURVE_TO,this.curCommandRelative=s==="c";else if(s==="s"||s==="S")this.curCommandType=m.SMOOTH_CURVE_TO,this.curCommandRelative=s==="s";else if(s==="q"||s==="Q")this.curCommandType=m.QUAD_TO,this.curCommandRelative=s==="q";else if(s==="t"||s==="T")this.curCommandType=m.SMOOTH_QUAD_TO,this.curCommandRelative=s==="t";else{if(s!=="a"&&s!=="A")throw new SyntaxError('Unexpected character "'+s+'" at index '+o+".");this.curCommandType=m.ARC,this.curCommandRelative=s==="a"}else r.push({type:m.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=s,this.curNumberHasDecimal=s==="."}else this.curNumber+=s,this.curNumberHasDecimal=!0;else this.curNumber+=s;else this.curNumber+=s,this.curNumberHasExp=!0;else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return r},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(r,i){i===void 0&&(i=[]);for(var n=0,o=Object.getPrototypeOf(this).parse.call(this,r);n<o.length;n++){var s=o[n],u=t(s);Array.isArray(u)?i.push.apply(i,u):i.push(u)}return i}}})},e}(dl),m=function(a){function e(t){var r=a.call(this)||this;return r.commands=typeof t=="string"?e.parse(t):t,r}return gl(e,a),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=U.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var r=[],i=0,n=this.commands;i<n.length;i++){var o=t(n[i]);Array.isArray(o)?r.push.apply(r,o):r.push(o)}return this.commands=r,this},e.encode=function(t){return Fb(t)},e.parse=function(t){var r=new Gb,i=[];return r.parse(t,i),r.finish(i),i},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(dl),zb=((oe={})[m.MOVE_TO]=2,oe[m.LINE_TO]=2,oe[m.HORIZ_LINE_TO]=1,oe[m.VERT_LINE_TO]=1,oe[m.CLOSE_PATH]=0,oe[m.QUAD_TO]=4,oe[m.SMOOTH_QUAD_TO]=2,oe[m.CURVE_TO]=6,oe[m.SMOOTH_CURVE_TO]=4,oe[m.ARC]=7,oe),Hb=$r.PROPER,Yb=Ue,Xb=J,Oo=pe,Wb=V,qb=Ki,nn="toString",pl=RegExp.prototype,yl=pl[nn],Qb=Wb(function(){return yl.call({source:"a",flags:"b"})!=="/a/b"}),Kb=Hb&&yl.name!==nn;(Qb||Kb)&&Yb(pl,nn,function(){var e=Xb(this),t=Oo(e.source),r=Oo(qb(e));return"/"+t+"/"+r},{unsafe:!0});function fr(a){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?fr=function(e){return typeof e}:fr=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fr(a)}function Zb(a,e){if(!(a instanceof e))throw new TypeError("Cannot call a class as a function")}var Jb=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],e1=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function t1(a,e,t,r,i){if(typeof a=="string"&&(a=document.getElementById(a)),!a||fr(a)!=="object"||!("getContext"in a))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var n=a.getContext("2d");try{return n.getImageData(e,t,r,i)}catch(o){throw new Error("unable to access image data: "+o)}}function r1(a,e,t,r,i,n){if(!(isNaN(n)||n<1)){n|=0;var o=t1(a,e,t,r,i);o=a1(o,e,t,r,i,n),a.getContext("2d").putImageData(o,e,t)}}function a1(a,e,t,r,i,n){for(var o=a.data,s=2*n+1,u=r-1,l=i-1,h=n+1,f=h*(h+1)/2,c=new So,v=c,g,d=1;d<s;d++)v=v.next=new So,d===h&&(g=v);v.next=c;for(var p=null,y=null,x=0,b=0,T=Jb[n],$=e1[n],E=0;E<i;E++){v=c;for(var O=o[b],C=o[b+1],P=o[b+2],D=o[b+3],j=0;j<h;j++)v.r=O,v.g=C,v.b=P,v.a=D,v=v.next;for(var R=0,X=0,W=0,G=0,q=h*O,H=h*C,Q=h*P,N=h*D,M=f*O,K=f*C,se=f*P,ge=f*D,Se=1;Se<h;Se++){var ze=b+((u<Se?u:Se)<<2),It=o[ze],un=o[ze+1],ln=o[ze+2],hn=o[ze+3],Mt=h-Se;M+=(v.r=It)*Mt,K+=(v.g=un)*Mt,se+=(v.b=ln)*Mt,ge+=(v.a=hn)*Mt,R+=It,X+=un,W+=ln,G+=hn,v=v.next}p=c,y=g;for(var jr=0;jr<r;jr++){var Fr=ge*T>>>$;if(o[b+3]=Fr,Fr!==0){var Ur=255/Fr;o[b]=(M*T>>>$)*Ur,o[b+1]=(K*T>>>$)*Ur,o[b+2]=(se*T>>>$)*Ur}else o[b]=o[b+1]=o[b+2]=0;M-=q,K-=H,se-=Q,ge-=N,q-=p.r,H-=p.g,Q-=p.b,N-=p.a;var Ae=jr+n+1;Ae=x+(Ae<u?Ae:u)<<2,R+=p.r=o[Ae],X+=p.g=o[Ae+1],W+=p.b=o[Ae+2],G+=p.a=o[Ae+3],M+=R,K+=X,se+=W,ge+=G,p=p.next;var _t=y,vn=_t.r,fn=_t.g,cn=_t.b,gn=_t.a;q+=vn,H+=fn,Q+=cn,N+=gn,R-=vn,X-=fn,W-=cn,G-=gn,y=y.next,b+=4}x+=r}for(var He=0;He<r;He++){b=He<<2;var Pe=o[b],Re=o[b+1],Ne=o[b+2],te=o[b+3],Gr=h*Pe,zr=h*Re,Hr=h*Ne,Yr=h*te,Dt=f*Pe,Vt=f*Re,Lt=f*Ne,kt=f*te;v=c;for(var dn=0;dn<h;dn++)v.r=Pe,v.g=Re,v.b=Ne,v.a=te,v=v.next;for(var pn=r,Xr=0,Wr=0,qr=0,Qr=0,Bt=1;Bt<=n;Bt++){b=pn+He<<2;var jt=h-Bt;Dt+=(v.r=Pe=o[b])*jt,Vt+=(v.g=Re=o[b+1])*jt,Lt+=(v.b=Ne=o[b+2])*jt,kt+=(v.a=te=o[b+3])*jt,Qr+=Pe,Xr+=Re,Wr+=Ne,qr+=te,v=v.next,Bt<l&&(pn+=r)}b=He,p=c,y=g;for(var Kr=0;Kr<i;Kr++){var re=b<<2;o[re+3]=te=kt*T>>>$,te>0?(te=255/te,o[re]=(Dt*T>>>$)*te,o[re+1]=(Vt*T>>>$)*te,o[re+2]=(Lt*T>>>$)*te):o[re]=o[re+1]=o[re+2]=0,Dt-=Gr,Vt-=zr,Lt-=Hr,kt-=Yr,Gr-=p.r,zr-=p.g,Hr-=p.b,Yr-=p.a,re=He+((re=Kr+h)<l?re:l)*r<<2,Dt+=Qr+=p.r=o[re],Vt+=Xr+=p.g=o[re+1],Lt+=Wr+=p.b=o[re+2],kt+=qr+=p.a=o[re+3],p=p.next,Gr+=Pe=y.r,zr+=Re=y.g,Hr+=Ne=y.b,Yr+=te=y.a,Qr-=Pe,Xr-=Re,Wr-=Ne,qr-=te,y=y.next,b+=r}}return a}var So=function a(){Zb(this,a),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};function i1(){var{DOMParser:a}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:a,createCanvas(t,r){return new OffscreenCanvas(t,r)},createImage(t){return xe(function*(){var r=yield fetch(t),i=yield r.blob(),n=yield createImageBitmap(i);return n})()}};return(typeof DOMParser<"u"||typeof a>"u")&&Reflect.deleteProperty(e,"DOMParser"),e}function n1(a){var{DOMParser:e,canvas:t,fetch:r}=a;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:r,createCanvas:t.createCanvas,createImage:t.loadImage}}var y2=Object.freeze({__proto__:null,offscreen:i1,node:n1});function lt(a){return a.replace(/(?!\u3000)\s+/gm," ")}function s1(a){return a.replace(/^[\n \t]+/,"")}function o1(a){return a.replace(/[\n \t]+$/,"")}function ne(a){var e=(a||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[];return e.map(parseFloat)}var u1=/^[A-Z-]+$/;function l1(a){return u1.test(a)?a.toLowerCase():a}function ml(a){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(a)||[];return e[2]||e[3]||e[4]}function h1(a){if(!a.startsWith("rgb"))return a;var e=3,t=a.replace(/\d+(\.\d+)?/g,(r,i)=>e--&&i?String(Math.round(parseFloat(r))):r);return t}var v1=/(\[[^\]]+\])/g,f1=/(#[^\s+>~.[:]+)/g,c1=/(\.[^\s+>~.[:]+)/g,g1=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,d1=/(:[\w-]+\([^)]*\))/gi,p1=/(:[^\s+>~.[:]+)/g,y1=/([^\s+>~.[:]+)/g;function De(a,e){var t=e.exec(a);return t?[a.replace(e," "),t.length]:[a,0]}function m1(a){var e=[0,0,0],t=a.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),r=0;return[t,r]=De(t,v1),e[1]+=r,[t,r]=De(t,f1),e[0]+=r,[t,r]=De(t,c1),e[1]+=r,[t,r]=De(t,g1),e[2]+=r,[t,r]=De(t,d1),e[1]+=r,[t,r]=De(t,p1),e[1]+=r,t=t.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[t,r]=De(t,y1),e[2]+=r,e.join("")}var rt=1e-8;function Eo(a){return Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2))}function xi(a,e){return(a[0]*e[0]+a[1]*e[1])/(Eo(a)*Eo(e))}function $o(a,e){return(a[0]*e[1]<a[1]*e[0]?-1:1)*Math.acos(xi(a,e))}function wo(a){return a*a*a}function Co(a){return 3*a*a*(1-a)}function Ao(a){return 3*a*(1-a)*(1-a)}function Po(a){return(1-a)*(1-a)*(1-a)}function Ro(a){return a*a}function No(a){return 2*a*(1-a)}function Io(a){return(1-a)*(1-a)}class S{constructor(e,t,r){this.document=e,this.name=t,this.value=r,this.isNormalizedColor=!1}static empty(e){return new S(e,"EMPTY","")}split(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:" ",{document:t,name:r}=this;return lt(this.getString()).trim().split(e).map(i=>new S(t,r,i))}hasValue(e){var{value:t}=this;return t!==null&&t!==""&&(e||t!==0)&&typeof t<"u"}isString(e){var{value:t}=this,r=typeof t=="string";return!r||!e?r:e.test(t)}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var e=this.getString();switch(!0){case e.endsWith("px"):case/^[0-9]+$/.test(e):return!0;default:return!1}}setValue(e){return this.value=e,this}getValue(e){return typeof e>"u"||this.hasValue()?this.value:e}getNumber(e){if(!this.hasValue())return typeof e>"u"?0:parseFloat(e);var{value:t}=this,r=parseFloat(t);return this.isString(/%$/)&&(r/=100),r}getString(e){return typeof e>"u"||this.hasValue()?typeof this.value>"u"?"":String(this.value):String(e)}getColor(e){var t=this.getString(e);return this.isNormalizedColor||(this.isNormalizedColor=!0,t=h1(t),this.value=t),t}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!this.hasValue())return 0;var[r,i]=typeof e=="boolean"?[void 0,e]:[e],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return this.getNumber()*15;case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case(this.isString(/%$/)&&i):return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:{var o=this.getNumber();return t&&o<1?o*n.computeSize(r):o}}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():this.getNumber()*1e3:0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var e=this.getString(),t=/#([^)'"]+)/.exec(e);return t&&(t=t[1]),t||(t=e),this.document.definitions[t]}getFillStyleDefinition(e,t){var r=this.getDefinition();if(!r)return null;if(typeof r.createGradient=="function")return r.createGradient(this.document.ctx,e,t);if(typeof r.createPattern=="function"){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,e,t)}return null}getTextBaseline(){return this.hasValue()?S.textBaselineMapping[this.getString()]:null}addOpacity(e){for(var t=this.getColor(),r=t.length,i=0,n=0;n<r&&(t[n]===","&&i++,i!==3);n++);if(e.hasValue()&&this.isString()&&i!==3){var o=new yi(t);o.ok&&(o.alpha=e.getNumber(),t=o.toRGBA())}return new S(this.document,this.name,t)}}S.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class b1{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(e,t){this.viewPorts.push({width:e,height:t})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:e}=this;return e[e.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(e){return typeof e=="number"?e:e==="x"?this.width:e==="y"?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class k{constructor(e,t){this.x=e,this.y=t}static parse(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,[r=t,i=t]=ne(e);return new k(r,i)}static parseScale(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,[r=t,i=r]=ne(e);return new k(r,i)}static parsePath(e){for(var t=ne(e),r=t.length,i=[],n=0;n<r;n+=2)i.push(new k(t[n],t[n+1]));return i}angleTo(e){return Math.atan2(e.y-this.y,e.x-this.x)}applyTransform(e){var{x:t,y:r}=this,i=t*e[0]+r*e[2]+e[4],n=t*e[1]+r*e[3]+e[5];this.x=i,this.y=n}}class x1{constructor(e){this.screen=e,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:e,onClick:t,onMouseMove:r}=this,i=e.ctx.canvas;i.onclick=t,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var e=this.screen.ctx.canvas;this.working=!1,e.onclick=null,e.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:e,events:t,eventElements:r}=this,{style:i}=e.ctx.canvas;i&&(i.cursor=""),t.forEach((n,o)=>{for(var{run:s}=n,u=r[o];u;)s(u),u=u.parent}),this.events=[],this.eventElements=[]}}checkPath(e,t){if(!(!this.working||!t)){var{events:r,eventElements:i}=this;r.forEach((n,o)=>{var{x:s,y:u}=n;!i[o]&&t.isPointInPath&&t.isPointInPath(s,u)&&(i[o]=e)})}}checkBoundingBox(e,t){if(!(!this.working||!t)){var{events:r,eventElements:i}=this;r.forEach((n,o)=>{var{x:s,y:u}=n;!i[o]&&t.isPointInBox(s,u)&&(i[o]=e)})}}mapXY(e,t){for(var{window:r,ctx:i}=this.screen,n=new k(e,t),o=i.canvas;o;)n.x-=o.offsetLeft,n.y-=o.offsetTop,o=o.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(e){var{x:t,y:r}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onclick",x:t,y:r,run(i){i.onClick&&i.onClick()}})}onMouseMove(e){var{x:t,y:r}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onmousemove",x:t,y:r,run(i){i.onMouseMove&&i.onMouseMove()}})}}var bl=typeof window<"u"?window:null,xl=typeof fetch<"u"?fetch.bind(void 0):null;class Vr{constructor(e){var{fetch:t=xl,window:r=bl}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.ctx=e,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new b1,this.mouse=new x1(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=t}wait(e){this.waits.push(e)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var e=this.waits.every(t=>t());return e&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=e,e}setDefaults(e){e.strokeStyle="rgba(0,0,0,0)",e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4}setViewBox(e){var{document:t,ctx:r,aspectRatio:i,width:n,desiredWidth:o,height:s,desiredHeight:u,minX:l=0,minY:h=0,refX:f,refY:c,clip:v=!1,clipX:g=0,clipY:d=0}=e,p=lt(i).replace(/^defer\s/,""),[y,x]=p.split(" "),b=y||"xMidYMid",T=x||"meet",$=n/o,E=s/u,O=Math.min($,E),C=Math.max($,E),P=o,D=u;T==="meet"&&(P*=O,D*=O),T==="slice"&&(P*=C,D*=C);var j=new S(t,"refX",f),R=new S(t,"refY",c),X=j.hasValue()&&R.hasValue();if(X&&r.translate(-O*j.getPixels("x"),-O*R.getPixels("y")),v){var W=O*g,G=O*d;r.beginPath(),r.moveTo(W,G),r.lineTo(n,G),r.lineTo(n,s),r.lineTo(W,s),r.closePath(),r.clip()}if(!X){var q=T==="meet"&&O===E,H=T==="slice"&&C===E,Q=T==="meet"&&O===$,N=T==="slice"&&C===$;b.startsWith("xMid")&&(q||H)&&r.translate(n/2-P/2,0),b.endsWith("YMid")&&(Q||N)&&r.translate(0,s/2-D/2),b.startsWith("xMax")&&(q||H)&&r.translate(n-P,0),b.endsWith("YMax")&&(Q||N)&&r.translate(0,s-D)}switch(!0){case b==="none":r.scale($,E);break;case T==="meet":r.scale(O,O);break;case T==="slice":r.scale(C,C);break}r.translate(-l,-h)}start(e){var{enableRedraw:t=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:o=!1,forceRedraw:s,scaleWidth:u,scaleHeight:l,offsetX:h,offsetY:f}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{FRAMERATE:c,mouse:v}=this,g=1e3/c;if(this.frameDuration=g,this.readyPromise=new Promise(b=>{this.resolveReady=b}),this.isReady()&&this.render(e,n,o,u,l,h,f),!!t){var d=Date.now(),p=d,y=0,x=()=>{d=Date.now(),y=d-p,y>=g&&(p=d-y%g,this.shouldUpdate(i,s)&&(this.render(e,n,o,u,l,h,f),v.runEvents())),this.intervalId=Fa(x)};r||v.start(),this.intervalId=Fa(x)}}stop(){this.intervalId&&(Fa.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(e,t){if(!e){var{frameDuration:r}=this,i=this.animations.reduce((n,o)=>o.update(r)||n,!1);if(i)return!0}return!!(typeof t=="function"&&t()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(e,t,r,i,n,o,s){var{CLIENT_WIDTH:u,CLIENT_HEIGHT:l,viewPort:h,ctx:f,isFirstRender:c}=this,v=f.canvas;h.clear(),v.width&&v.height?h.setCurrent(v.width,v.height):h.setCurrent(u,l);var g=e.getStyle("width"),d=e.getStyle("height");!t&&(c||typeof i!="number"&&typeof n!="number")&&(g.hasValue()&&(v.width=g.getPixels("x"),v.style&&(v.style.width="".concat(v.width,"px"))),d.hasValue()&&(v.height=d.getPixels("y"),v.style&&(v.style.height="".concat(v.height,"px"))));var p=v.clientWidth||v.width,y=v.clientHeight||v.height;if(t&&g.hasValue()&&d.hasValue()&&(p=g.getPixels("x"),y=d.getPixels("y")),h.setCurrent(p,y),typeof o=="number"&&e.getAttribute("x",!0).setValue(o),typeof s=="number"&&e.getAttribute("y",!0).setValue(s),typeof i=="number"||typeof n=="number"){var x=ne(e.getAttribute("viewBox").getString()),b=0,T=0;if(typeof i=="number"){var $=e.getStyle("width");$.hasValue()?b=$.getPixels("x")/i:isNaN(x[2])||(b=x[2]/i)}if(typeof n=="number"){var E=e.getStyle("height");E.hasValue()?T=E.getPixels("y")/n:isNaN(x[3])||(T=x[3]/n)}b||(b=T),T||(T=b),e.getAttribute("width",!0).setValue(i),e.getAttribute("height",!0).setValue(n);var O=e.getStyle("transform",!0,!0);O.setValue("".concat(O.getString()," scale(").concat(1/b,", ").concat(1/T,")"))}r||f.clearRect(0,0,p,y),e.render(f),c&&(this.isFirstRender=!1)}}Vr.defaultWindow=bl;Vr.defaultFetch=xl;var{defaultFetch:T1}=Vr,O1=typeof DOMParser<"u"?DOMParser:null;class za{constructor(){var{fetch:e=T1,DOMParser:t=O1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.fetch=e,this.DOMParser=t}parse(e){var t=this;return xe(function*(){return e.startsWith("<")?t.parseFromString(e):t.load(e)})()}parseFromString(e){var t=new this.DOMParser;try{return this.checkDocument(t.parseFromString(e,"image/svg+xml"))}catch{return this.checkDocument(t.parseFromString(e,"text/xml"))}}checkDocument(e){var t=e.getElementsByTagName("parsererror")[0];if(t)throw new Error(t.textContent);return e}load(e){var t=this;return xe(function*(){var r=yield t.fetch(e),i=yield r.text();return t.parseFromString(i)})()}}class S1{constructor(e,t){this.type="translate",this.point=null,this.point=k.parse(t)}apply(e){var{x:t,y:r}=this.point;e.translate(t||0,r||0)}unapply(e){var{x:t,y:r}=this.point;e.translate(-1*t||0,-1*r||0)}applyToPoint(e){var{x:t,y:r}=this.point;e.applyTransform([1,0,0,1,t||0,r||0])}}class E1{constructor(e,t,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=ne(t);this.angle=new S(e,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(e){var{cx:t,cy:r,originX:i,originY:n,angle:o}=this,s=t+i.getPixels("x"),u=r+n.getPixels("y");e.translate(s,u),e.rotate(o.getRadians()),e.translate(-s,-u)}unapply(e){var{cx:t,cy:r,originX:i,originY:n,angle:o}=this,s=t+i.getPixels("x"),u=r+n.getPixels("y");e.translate(s,u),e.rotate(-1*o.getRadians()),e.translate(-s,-u)}applyToPoint(e){var{cx:t,cy:r,angle:i}=this,n=i.getRadians();e.applyTransform([1,0,0,1,t||0,r||0]),e.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),e.applyTransform([1,0,0,1,-t||0,-r||0])}}class $1{constructor(e,t,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=k.parseScale(t);(i.x===0||i.y===0)&&(i.x=rt,i.y=rt),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(e){var{scale:{x:t,y:r},originX:i,originY:n}=this,o=i.getPixels("x"),s=n.getPixels("y");e.translate(o,s),e.scale(t,r||t),e.translate(-o,-s)}unapply(e){var{scale:{x:t,y:r},originX:i,originY:n}=this,o=i.getPixels("x"),s=n.getPixels("y");e.translate(o,s),e.scale(1/t,1/r||t),e.translate(-o,-s)}applyToPoint(e){var{x:t,y:r}=this.scale;e.applyTransform([t||0,0,0,r||0,0,0])}}class Tl{constructor(e,t,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=ne(t),this.originX=r[0],this.originY=r[1]}apply(e){var{originX:t,originY:r,matrix:i}=this,n=t.getPixels("x"),o=r.getPixels("y");e.translate(n,o),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),e.translate(-n,-o)}unapply(e){var{originX:t,originY:r,matrix:i}=this,n=i[0],o=i[2],s=i[4],u=i[1],l=i[3],h=i[5],f=0,c=0,v=1,g=1/(n*(l*v-h*c)-o*(u*v-h*f)+s*(u*c-l*f)),d=t.getPixels("x"),p=r.getPixels("y");e.translate(d,p),e.transform(g*(l*v-h*c),g*(h*f-u*v),g*(s*c-o*v),g*(n*v-s*f),g*(o*h-s*l),g*(s*u-n*h)),e.translate(-d,-p)}applyToPoint(e){e.applyTransform(this.matrix)}}class Ol extends Tl{constructor(e,t,r){super(e,t,r),this.type="skew",this.angle=null,this.angle=new S(e,"angle",t)}}class w1 extends Ol{constructor(e,t,r){super(e,t,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class C1 extends Ol{constructor(e,t,r){super(e,t,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}function A1(a){return lt(a).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}function P1(a){var[e,t]=a.split("(");return[e.trim(),t.trim().replace(")","")]}class Be{constructor(e,t,r){this.document=e,this.transforms=[];var i=A1(t);i.forEach(n=>{if(n!=="none"){var[o,s]=P1(n),u=Be.transformTypes[o];typeof u<"u"&&this.transforms.push(new u(this.document,s,r))}})}static fromElement(e,t){var r=t.getStyle("transform",!1,!0),[i,n=i]=t.getStyle("transform-origin",!1,!0).split(),o=[i,n];return r.hasValue()?new Be(e,r.getString(),o):null}apply(e){for(var{transforms:t}=this,r=t.length,i=0;i<r;i++)t[i].apply(e)}unapply(e){for(var{transforms:t}=this,r=t.length,i=r-1;i>=0;i--)t[i].unapply(e)}applyToPoint(e){for(var{transforms:t}=this,r=t.length,i=0;i<r;i++)t[i].applyToPoint(e)}}Be.transformTypes={translate:S1,rotate:E1,scale:$1,matrix:Tl,skewX:w1,skewY:C1};class I{constructor(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(this.document=e,this.node=t,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!(!t||t.nodeType!==1)){if(Array.from(t.attributes).forEach(s=>{var u=l1(s.nodeName);this.attributes[u]=new S(e,u,s.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var i=this.getAttribute("style").getString().split(";").map(s=>s.trim());i.forEach(s=>{if(s){var[u,l]=s.split(":").map(h=>h.trim());this.styles[u]=new S(e,u,l)}})}var{definitions:n}=e,o=this.getAttribute("id");o.hasValue()&&(n[o.getString()]||(n[o.getString()]=this)),Array.from(t.childNodes).forEach(s=>{if(s.nodeType===1)this.addChild(s);else if(r&&(s.nodeType===3||s.nodeType===4)){var u=e.createTextNode(s);u.getText().length>0&&this.addChild(u)}})}}getAttribute(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=this.attributes[e];if(!r&&t){var i=new S(this.document,e,"");return this.attributes[e]=i,i}return r||S.empty(this.document)}getHrefAttribute(){for(var e in this.attributes)if(e==="href"||e.endsWith(":href"))return this.attributes[e];return S.empty(this.document)}getStyle(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=this.styles[e];if(i)return i;var n=this.getAttribute(e);if(n!=null&&n.hasValue())return this.styles[e]=n,n;if(!r){var{parent:o}=this;if(o){var s=o.getStyle(e);if(s!=null&&s.hasValue())return s}}if(t){var u=new S(this.document,e,"");return this.styles[e]=u,u}return i||S.empty(this.document)}render(e){if(!(this.getStyle("display").getString()==="none"||this.getStyle("visibility").getString()==="hidden")){if(e.save(),this.getStyle("mask").hasValue()){var t=this.getStyle("mask").getDefinition();t&&(this.applyEffects(e),t.apply(e,this))}else if(this.getStyle("filter").getValue("none")!=="none"){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(e),r.apply(e,this))}else this.setContext(e),this.renderChildren(e),this.clearContext(e);e.restore()}}setContext(e){}applyEffects(e){var t=Be.fromElement(this.document,this);t&&t.apply(e);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(e)}}clearContext(e){}renderChildren(e){this.children.forEach(t=>{t.render(e)})}addChild(e){var t=e instanceof I?e:this.document.createElement(e);t.parent=this,I.ignoreChildTypes.includes(t.type)||this.children.push(t)}matchesSelector(e){var t,{node:r}=this;if(typeof r.matches=="function")return r.matches(e);var i=(t=r.getAttribute)===null||t===void 0?void 0:t.call(r,"class");return!i||i===""?!1:i.split(" ").some(n=>".".concat(n)===e)}addStylesFromStyleDefinition(){var{styles:e,stylesSpecificity:t}=this.document;for(var r in e)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=e[r],n=t[r];if(i)for(var o in i){var s=this.stylesSpecificity[o];typeof s>"u"&&(s="000"),n>=s&&(this.styles[o]=i[o],this.stylesSpecificity[o]=n)}}}removeStyles(e,t){var r=t.reduce((i,n)=>{var o=e.getStyle(n);if(!o.hasValue())return i;var s=o.getString();return o.setValue(""),[...i,[n,s]]},[]);return r}restoreStyles(e,t){t.forEach(r=>{var[i,n]=r;e.getStyle(i,!0).setValue(n)})}isFirstChild(){var e;return((e=this.parent)===null||e===void 0?void 0:e.children.indexOf(this))===0}}I.ignoreChildTypes=["title"];class R1 extends I{constructor(e,t,r){super(e,t,r)}}function N1(a){var e=a.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function I1(a){return typeof process>"u"?a:a.trim().split(",").map(N1).join(",")}function M1(a){if(!a)return"";var e=a.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function _1(a){if(!a)return"";var e=a.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class Z{constructor(e,t,r,i,n,o){var s=o?typeof o=="string"?Z.parse(o):o:{};this.fontFamily=n||s.fontFamily,this.fontSize=i||s.fontSize,this.fontStyle=e||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=t||s.fontVariant}static parse(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0,r="",i="",n="",o="",s="",u=lt(e).trim().split(" "),l={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return u.forEach(h=>{switch(!0){case(!l.fontStyle&&Z.styles.includes(h)):h!=="inherit"&&(r=h),l.fontStyle=!0;break;case(!l.fontVariant&&Z.variants.includes(h)):h!=="inherit"&&(i=h),l.fontStyle=!0,l.fontVariant=!0;break;case(!l.fontWeight&&Z.weights.includes(h)):h!=="inherit"&&(n=h),l.fontStyle=!0,l.fontVariant=!0,l.fontWeight=!0;break;case!l.fontSize:h!=="inherit"&&([o]=h.split("/")),l.fontStyle=!0,l.fontVariant=!0,l.fontWeight=!0,l.fontSize=!0;break;default:h!=="inherit"&&(s+=h)}}),new Z(r,i,n,o,s,t)}toString(){return[M1(this.fontStyle),this.fontVariant,_1(this.fontWeight),this.fontSize,I1(this.fontFamily)].join(" ").trim()}}Z.styles="normal|italic|oblique|inherit";Z.variants="normal|small-caps|inherit";Z.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class ce{constructor(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Number.NaN,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.NaN,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Number.NaN,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Number.NaN;this.x1=e,this.y1=t,this.x2=r,this.y2=i,this.addPoint(e,t),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(e,t){typeof e<"u"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),typeof t<"u"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=t,this.y2=t),t<this.y1&&(this.y1=t),t>this.y2&&(this.y2=t))}addX(e){this.addPoint(e,null)}addY(e){this.addPoint(null,e)}addBoundingBox(e){if(e){var{x1:t,y1:r,x2:i,y2:n}=e;this.addPoint(t,r),this.addPoint(i,n)}}sumCubic(e,t,r,i,n){return Math.pow(1-e,3)*t+3*Math.pow(1-e,2)*e*r+3*(1-e)*Math.pow(e,2)*i+Math.pow(e,3)*n}bezierCurveAdd(e,t,r,i,n){var o=6*t-12*r+6*i,s=-3*t+9*r-9*i+3*n,u=3*r-3*t;if(s===0){if(o===0)return;var l=-u/o;0<l&&l<1&&(e?this.addX(this.sumCubic(l,t,r,i,n)):this.addY(this.sumCubic(l,t,r,i,n)));return}var h=Math.pow(o,2)-4*u*s;if(!(h<0)){var f=(-o+Math.sqrt(h))/(2*s);0<f&&f<1&&(e?this.addX(this.sumCubic(f,t,r,i,n)):this.addY(this.sumCubic(f,t,r,i,n)));var c=(-o-Math.sqrt(h))/(2*s);0<c&&c<1&&(e?this.addX(this.sumCubic(c,t,r,i,n)):this.addY(this.sumCubic(c,t,r,i,n)))}}addBezierCurve(e,t,r,i,n,o,s,u){this.addPoint(e,t),this.addPoint(s,u),this.bezierCurveAdd(!0,e,r,n,s),this.bezierCurveAdd(!1,t,i,o,u)}addQuadraticCurve(e,t,r,i,n,o){var s=e+.6666666666666666*(r-e),u=t+2/3*(i-t),l=s+1/3*(n-e),h=u+1/3*(o-t);this.addBezierCurve(e,t,s,l,u,h,n,o)}isPointInBox(e,t){var{x1:r,y1:i,x2:n,y2:o}=this;return r<=e&&e<=n&&i<=t&&t<=o}}class w extends m{constructor(e){super(e.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new k(0,0),this.control=new k(0,0),this.current=new k(0,0),this.points=[],this.angles=[]}isEnd(){var{i:e,commands:t}=this;return e>=t.length-1}next(){var e=this.commands[++this.i];return this.previousCommand=this.command,this.command=e,e}getPoint(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"x",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"y",r=new k(this.command[e],this.command[t]);return this.makeAbsolute(r)}getAsControlPoint(e,t){var r=this.getPoint(e,t);return this.control=r,r}getAsCurrentPoint(e,t){var r=this.getPoint(e,t);return this.current=r,r}getReflectedControlPoint(){var e=this.previousCommand.type;if(e!==m.CURVE_TO&&e!==m.SMOOTH_CURVE_TO&&e!==m.QUAD_TO&&e!==m.SMOOTH_QUAD_TO)return this.current;var{current:{x:t,y:r},control:{x:i,y:n}}=this,o=new k(2*t-i,2*r-n);return o}makeAbsolute(e){if(this.command.relative){var{x:t,y:r}=this.current;e.x+=t,e.y+=r}return e}addMarker(e,t,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(e,t?t.angleTo(e):null)}addMarkerAngle(e,t){this.points.push(e),this.angles.push(t)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:e}=this,t=e.length,r=0;r<t;r++)if(!e[r]){for(var i=r+1;i<t;i++)if(e[i]){e[r]=e[i];break}}return e}}class Ge extends I{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var e=1,t=this;t;){var r=t.getStyle("opacity",!1,!0);r.hasValue(!0)&&(e*=r.getNumber()),t=t.parent}return e}setContext(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!t){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),o=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,i);s&&(e.fillStyle=s)}else if(r.hasValue()){r.getString()==="currentColor"&&r.setValue(this.getStyle("color").getColor());var u=r.getColor();u!=="inherit"&&(e.fillStyle=u==="none"?"rgba(0,0,0,0)":u)}if(i.hasValue()){var l=new S(this.document,"fill",e.fillStyle).addOpacity(i).getColor();e.fillStyle=l}if(n.isUrlDefinition()){var h=n.getFillStyleDefinition(this,o);h&&(e.strokeStyle=h)}else if(n.hasValue()){n.getString()==="currentColor"&&n.setValue(this.getStyle("color").getColor());var f=n.getString();f!=="inherit"&&(e.strokeStyle=f==="none"?"rgba(0,0,0,0)":f)}if(o.hasValue()){var c=new S(this.document,"stroke",e.strokeStyle).addOpacity(o).getString();e.strokeStyle=c}var v=this.getStyle("stroke-width");if(v.hasValue()){var g=v.getPixels();e.lineWidth=g||rt}var d=this.getStyle("stroke-linecap"),p=this.getStyle("stroke-linejoin"),y=this.getStyle("stroke-miterlimit"),x=this.getStyle("stroke-dasharray"),b=this.getStyle("stroke-dashoffset");if(d.hasValue()&&(e.lineCap=d.getString()),p.hasValue()&&(e.lineJoin=p.getString()),y.hasValue()&&(e.miterLimit=y.getNumber()),x.hasValue()&&x.getString()!=="none"){var T=ne(x.getString());typeof e.setLineDash<"u"?e.setLineDash(T):typeof e.webkitLineDash<"u"?e.webkitLineDash=T:typeof e.mozDash<"u"&&!(T.length===1&&T[0]===0)&&(e.mozDash=T);var $=b.getPixels();typeof e.lineDashOffset<"u"?e.lineDashOffset=$:typeof e.webkitLineDashOffset<"u"?e.webkitLineDashOffset=$:typeof e.mozDashOffset<"u"&&(e.mozDashOffset=$)}}if(this.modifiedEmSizeStack=!1,typeof e.font<"u"){var E=this.getStyle("font"),O=this.getStyle("font-style"),C=this.getStyle("font-variant"),P=this.getStyle("font-weight"),D=this.getStyle("font-size"),j=this.getStyle("font-family"),R=new Z(O.getString(),C.getString(),P.getString(),D.hasValue()?"".concat(D.getPixels(!0),"px"):"",j.getString(),Z.parse(E.getString(),e.font));O.setValue(R.fontStyle),C.setValue(R.fontVariant),P.setValue(R.fontWeight),D.setValue(R.fontSize),j.setValue(R.fontFamily),e.font=R.toString(),D.isPixels()&&(this.document.emSize=D.getPixels(),this.modifiedEmSizeStack=!0)}t||(this.applyEffects(e),e.globalAlpha=this.calculateOpacity())}clearContext(e){super.clearContext(e),this.modifiedEmSizeStack&&this.document.popEmSize()}}class A extends Ge{constructor(e,t,r){super(e,t,r),this.type="path",this.pathParser=null,this.pathParser=new w(this.getAttribute("d").getString())}path(e){var{pathParser:t}=this,r=new ce;for(t.reset(),e&&e.beginPath();!t.isEnd();)switch(t.next().type){case w.MOVE_TO:this.pathM(e,r);break;case w.LINE_TO:this.pathL(e,r);break;case w.HORIZ_LINE_TO:this.pathH(e,r);break;case w.VERT_LINE_TO:this.pathV(e,r);break;case w.CURVE_TO:this.pathC(e,r);break;case w.SMOOTH_CURVE_TO:this.pathS(e,r);break;case w.QUAD_TO:this.pathQ(e,r);break;case w.SMOOTH_QUAD_TO:this.pathT(e,r);break;case w.ARC:this.pathA(e,r);break;case w.CLOSE_PATH:this.pathZ(e,r);break}return r}getBoundingBox(e){return this.path()}getMarkers(){var{pathParser:e}=this,t=e.getMarkerPoints(),r=e.getMarkerAngles(),i=t.map((n,o)=>[n,r[o]]);return i}renderChildren(e){this.path(e),this.document.screen.mouse.checkPath(this,e);var t=this.getStyle("fill-rule");e.fillStyle!==""&&(t.getString("inherit")!=="inherit"?e.fill(t.getString()):e.fill()),e.strokeStyle!==""&&(this.getAttribute("vector-effect").getString()==="non-scaling-stroke"?(e.save(),e.setTransform(1,0,0,1,0,0),e.stroke(),e.restore()):e.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),o=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(n.isUrlDefinition()){var u=n.getDefinition(),[l,h]=r[0];u.render(e,l,h)}if(o.isUrlDefinition())for(var f=o.getDefinition(),c=1;c<i;c++){var[v,g]=r[c];f.render(e,v,g)}if(s.isUrlDefinition()){var d=s.getDefinition(),[p,y]=r[i];d.render(e,p,y)}}}static pathM(e){var t=e.getAsCurrentPoint();return e.start=e.current,{point:t}}pathM(e,t){var{pathParser:r}=this,{point:i}=A.pathM(r),{x:n,y:o}=i;r.addMarker(i),t.addPoint(n,o),e&&e.moveTo(n,o)}static pathL(e){var{current:t}=e,r=e.getAsCurrentPoint();return{current:t,point:r}}pathL(e,t){var{pathParser:r}=this,{current:i,point:n}=A.pathL(r),{x:o,y:s}=n;r.addMarker(n,i),t.addPoint(o,s),e&&e.lineTo(o,s)}static pathH(e){var{current:t,command:r}=e,i=new k((r.relative?t.x:0)+r.x,t.y);return e.current=i,{current:t,point:i}}pathH(e,t){var{pathParser:r}=this,{current:i,point:n}=A.pathH(r),{x:o,y:s}=n;r.addMarker(n,i),t.addPoint(o,s),e&&e.lineTo(o,s)}static pathV(e){var{current:t,command:r}=e,i=new k(t.x,(r.relative?t.y:0)+r.y);return e.current=i,{current:t,point:i}}pathV(e,t){var{pathParser:r}=this,{current:i,point:n}=A.pathV(r),{x:o,y:s}=n;r.addMarker(n,i),t.addPoint(o,s),e&&e.lineTo(o,s)}static pathC(e){var{current:t}=e,r=e.getPoint("x1","y1"),i=e.getAsControlPoint("x2","y2"),n=e.getAsCurrentPoint();return{current:t,point:r,controlPoint:i,currentPoint:n}}pathC(e,t){var{pathParser:r}=this,{current:i,point:n,controlPoint:o,currentPoint:s}=A.pathC(r);r.addMarker(s,o,n),t.addBezierCurve(i.x,i.y,n.x,n.y,o.x,o.y,s.x,s.y),e&&e.bezierCurveTo(n.x,n.y,o.x,o.y,s.x,s.y)}static pathS(e){var{current:t}=e,r=e.getReflectedControlPoint(),i=e.getAsControlPoint("x2","y2"),n=e.getAsCurrentPoint();return{current:t,point:r,controlPoint:i,currentPoint:n}}pathS(e,t){var{pathParser:r}=this,{current:i,point:n,controlPoint:o,currentPoint:s}=A.pathS(r);r.addMarker(s,o,n),t.addBezierCurve(i.x,i.y,n.x,n.y,o.x,o.y,s.x,s.y),e&&e.bezierCurveTo(n.x,n.y,o.x,o.y,s.x,s.y)}static pathQ(e){var{current:t}=e,r=e.getAsControlPoint("x1","y1"),i=e.getAsCurrentPoint();return{current:t,controlPoint:r,currentPoint:i}}pathQ(e,t){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:o}=A.pathQ(r);r.addMarker(o,n,n),t.addQuadraticCurve(i.x,i.y,n.x,n.y,o.x,o.y),e&&e.quadraticCurveTo(n.x,n.y,o.x,o.y)}static pathT(e){var{current:t}=e,r=e.getReflectedControlPoint();e.control=r;var i=e.getAsCurrentPoint();return{current:t,controlPoint:r,currentPoint:i}}pathT(e,t){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:o}=A.pathT(r);r.addMarker(o,n,n),t.addQuadraticCurve(i.x,i.y,n.x,n.y,o.x,o.y),e&&e.quadraticCurveTo(n.x,n.y,o.x,o.y)}static pathA(e){var{current:t,command:r}=e,{rX:i,rY:n,xRot:o,lArcFlag:s,sweepFlag:u}=r,l=o*(Math.PI/180),h=e.getAsCurrentPoint(),f=new k(Math.cos(l)*(t.x-h.x)/2+Math.sin(l)*(t.y-h.y)/2,-Math.sin(l)*(t.x-h.x)/2+Math.cos(l)*(t.y-h.y)/2),c=Math.pow(f.x,2)/Math.pow(i,2)+Math.pow(f.y,2)/Math.pow(n,2);c>1&&(i*=Math.sqrt(c),n*=Math.sqrt(c));var v=(s===u?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(f.y,2)-Math.pow(n,2)*Math.pow(f.x,2))/(Math.pow(i,2)*Math.pow(f.y,2)+Math.pow(n,2)*Math.pow(f.x,2)));isNaN(v)&&(v=0);var g=new k(v*i*f.y/n,v*-n*f.x/i),d=new k((t.x+h.x)/2+Math.cos(l)*g.x-Math.sin(l)*g.y,(t.y+h.y)/2+Math.sin(l)*g.x+Math.cos(l)*g.y),p=$o([1,0],[(f.x-g.x)/i,(f.y-g.y)/n]),y=[(f.x-g.x)/i,(f.y-g.y)/n],x=[(-f.x-g.x)/i,(-f.y-g.y)/n],b=$o(y,x);return xi(y,x)<=-1&&(b=Math.PI),xi(y,x)>=1&&(b=0),{currentPoint:h,rX:i,rY:n,sweepFlag:u,xAxisRotation:l,centp:d,a1:p,ad:b}}pathA(e,t){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:o,sweepFlag:s,xAxisRotation:u,centp:l,a1:h,ad:f}=A.pathA(r),c=1-s?1:-1,v=h+c*(f/2),g=new k(l.x+n*Math.cos(v),l.y+o*Math.sin(v));if(r.addMarkerAngle(g,v-c*Math.PI/2),r.addMarkerAngle(i,v-c*Math.PI),t.addPoint(i.x,i.y),e&&!isNaN(h)&&!isNaN(f)){var d=n>o?n:o,p=n>o?1:n/o,y=n>o?o/n:1;e.translate(l.x,l.y),e.rotate(u),e.scale(p,y),e.arc(0,0,d,h,h+f,!!(1-s)),e.scale(1/p,1/y),e.rotate(-u),e.translate(-l.x,-l.y)}}static pathZ(e){e.current=e.start}pathZ(e,t){A.pathZ(this.pathParser),e&&t.x1!==t.x2&&t.y1!==t.y2&&e.closePath()}}class Sl extends A{constructor(e,t,r){super(e,t,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class Ce extends Ge{constructor(e,t,r){super(e,t,new.target===Ce?!0:r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;super.setContext(e,t);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(e.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(e){if(this.type!=="text")return this.getTElementBoundingBox(e);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e);var t=null;return this.children.forEach((r,i)=>{var n=this.getChildBoundingBox(e,this,this,i);t?t.addBoundingBox(n):t=n}),t}getFontSize(){var{document:e,parent:t}=this,r=Z.parse(e.ctx.font).fontSize,i=t.getStyle("font-size").getNumber(r);return i}getTElementBoundingBox(e){var t=this.getFontSize();return new ce(this.x,this.y-t,this.x+this.measureText(e),this.y)}getGlyph(e,t,r){var i=t[r],n=null;if(e.isArabic){var o=t.length,s=t[r-1],u=t[r+1],l="isolated";if((r===0||s===" ")&&r<o-1&&u!==" "&&(l="terminal"),r>0&&s!==" "&&r<o-1&&u!==" "&&(l="medial"),r>0&&s!==" "&&(r===o-1||u===" ")&&(l="initial"),typeof e.glyphs[i]<"u"){var h=e.glyphs[i];n=h instanceof Sl?h:h[l]}}else n=e.glyphs[i];return n||(n=e.missingGlyph),n}getText(){return""}getTextFromNode(e){var t=e||this.node,r=Array.from(t.parentNode.childNodes),i=r.indexOf(t),n=r.length-1,o=lt(t.textContent||"");return i===0&&(o=s1(o)),i===n&&(o=o1(o)),o}renderChildren(e){if(this.type!=="text"){this.renderTElementChildren(e);return}this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e),this.children.forEach((r,i)=>{this.renderChild(e,this,this,i)});var{mouse:t}=this.document.screen;t.isWorking()&&t.checkBoundingBox(this,this.getBoundingBox(e))}renderTElementChildren(e){var{document:t,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n){for(var{unitsPerEm:o}=n.fontFace,s=Z.parse(t.ctx.font),u=r.getStyle("font-size").getNumber(s.fontSize),l=r.getStyle("font-style").getString(s.fontStyle),h=u/o,f=n.isRTL?i.split("").reverse().join(""):i,c=ne(r.getAttribute("dx").getString()),v=f.length,g=0;g<v;g++){var d=this.getGlyph(n,f,g);e.translate(this.x,this.y),e.scale(h,-h);var p=e.lineWidth;e.lineWidth=e.lineWidth*o/u,l==="italic"&&e.transform(1,0,.4,1,0,0),d.render(e),l==="italic"&&e.transform(1,0,-.4,1,0,0),e.lineWidth=p,e.scale(1/h,-1/h),e.translate(-this.x,-this.y),this.x+=u*(d.horizAdvX||n.horizAdvX)/o,typeof c[g]<"u"&&!isNaN(c[g])&&(this.x+=c[g])}return}var{x:y,y:x}=this;e.fillStyle&&e.fillText(i,y,x),e.strokeStyle&&e.strokeText(i,y,x)}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var e=this.leafTexts[this.textChunkStart],t=e.getStyle("text-anchor").getString("start"),r=!1,i=0;t==="start"&&!r||t==="end"&&r?i=e.x-this.minX:t==="end"&&!r||t==="start"&&r?i=e.x-this.maxX:i=e.x-(this.minX+this.maxX)/2;for(var n=this.textChunkStart;n<this.leafTexts.length;n++)this.leafTexts[n].x+=i;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(e){this.children.forEach((t,r)=>{this.adjustChildCoordinatesRecursiveCore(e,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(e,t,r,i){var n=r.children[i];n.children.length>0?n.children.forEach((o,s)=>{t.adjustChildCoordinatesRecursiveCore(e,t,n,s)}):this.adjustChildCoordinates(e,t,r,i)}adjustChildCoordinates(e,t,r,i){var n=r.children[i];if(typeof n.measureText!="function")return n;e.save(),n.setContext(e,!0);var o=n.getAttribute("x"),s=n.getAttribute("y"),u=n.getAttribute("dx"),l=n.getAttribute("dy"),h=n.getStyle("font-family").getDefinition(),f=!!h&&h.isRTL;i===0&&(o.hasValue()||o.setValue(n.getInheritedAttribute("x")),s.hasValue()||s.setValue(n.getInheritedAttribute("y")),u.hasValue()||u.setValue(n.getInheritedAttribute("dx")),l.hasValue()||l.setValue(n.getInheritedAttribute("dy")));var c=n.measureText(e);return f&&(t.x-=c),o.hasValue()?(t.applyAnchoring(),n.x=o.getPixels("x"),u.hasValue()&&(n.x+=u.getPixels("x"))):(u.hasValue()&&(t.x+=u.getPixels("x")),n.x=t.x),t.x=n.x,f||(t.x+=c),s.hasValue()?(n.y=s.getPixels("y"),l.hasValue()&&(n.y+=l.getPixels("y"))):(l.hasValue()&&(t.y+=l.getPixels("y")),n.y=t.y),t.y=n.y,t.leafTexts.push(n),t.minX=Math.min(t.minX,n.x,n.x+c),t.maxX=Math.max(t.maxX,n.x,n.x+c),n.clearContext(e),e.restore(),n}getChildBoundingBox(e,t,r,i){var n=r.children[i];if(typeof n.getBoundingBox!="function")return null;var o=n.getBoundingBox(e);return o?(n.children.forEach((s,u)=>{var l=t.getChildBoundingBox(e,t,n,u);o.addBoundingBox(l)}),o):null}renderChild(e,t,r,i){var n=r.children[i];n.render(e),n.children.forEach((o,s)=>{t.renderChild(e,t,n,s)})}measureText(e){var{measureCache:t}=this;if(~t)return t;var r=this.getText(),i=this.measureTargetText(e,r);return this.measureCache=i,i}measureTargetText(e,t){if(!t.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),o=i.isRTL?t.split("").reverse().join(""):t,s=ne(r.getAttribute("dx").getString()),u=o.length,l=0,h=0;h<u;h++){var f=this.getGlyph(i,o,h);l+=(f.horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,typeof s[h]<"u"&&!isNaN(s[h])&&(l+=s[h])}return l}if(!e.measureText)return t.length*10;e.save(),this.setContext(e,!0);var{width:c}=e.measureText(t);return this.clearContext(e),e.restore(),c}getInheritedAttribute(e){for(var t=this;t instanceof Ce&&t.isFirstChild();){var r=t.parent.getAttribute(e);if(r.hasValue(!0))return r.getValue("0");t=t.parent}return null}}class Lr extends Ce{constructor(e,t,r){super(e,t,new.target===Lr?!0:r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class D1 extends Lr{constructor(){super(...arguments),this.type="textNode"}}class Nt extends Ge{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(e){var t,{document:r}=this,{screen:i,window:n}=r,o=e.canvas;if(i.setDefaults(e),o.style&&typeof e.font<"u"&&n&&typeof n.getComputedStyle<"u"){e.font=n.getComputedStyle(o).getPropertyValue("font");var s=new S(r,"fontSize",Z.parse(e.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:u,height:l}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),f=this.getAttribute("refY"),c=this.getAttribute("viewBox"),v=c.hasValue()?ne(c.getString()):null,g=!this.root&&this.getStyle("overflow").getValue("hidden")!=="visible",d=0,p=0,y=0,x=0;v&&(d=v[0],p=v[1]),this.root||(u=this.getStyle("width").getPixels("x"),l=this.getStyle("height").getPixels("y"),this.type==="marker"&&(y=d,x=p,d=0,p=0)),i.viewPort.setCurrent(u,l),this.node&&(!this.parent||((t=this.node.parentNode)===null||t===void 0?void 0:t.nodeName)==="foreignObject")&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(e),e.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),v&&(u=v[2],l=v[3]),r.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:u,height:i.viewPort.height,desiredHeight:l,minX:d,minY:p,refX:h.getValue(),refY:f.getValue(),clip:g,clipX:y,clipY:x}),v&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(u,l))}clearContext(e){super.clearContext(e),this.document.screen.viewPort.removeCurrent()}resize(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),o=this.getAttribute("viewBox"),s=this.getAttribute("style"),u=i.getNumber(0),l=n.getNumber(0);if(r)if(typeof r=="string")this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(e),n.setValue(t),o.hasValue()||o.setValue("0 0 ".concat(u||e," ").concat(l||t)),s.hasValue()){var f=this.getStyle("width"),c=this.getStyle("height");f.hasValue()&&f.setValue("".concat(e,"px")),c.hasValue()&&c.setValue("".concat(t,"px"))}}}class El extends A{constructor(){super(...arguments),this.type="rect"}path(e){var t=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),o=this.getAttribute("rx"),s=this.getAttribute("ry"),u=o.getPixels("x"),l=s.getPixels("y");if(o.hasValue()&&!s.hasValue()&&(l=u),s.hasValue()&&!o.hasValue()&&(u=l),u=Math.min(u,i/2),l=Math.min(l,n/2),e){var h=4*((Math.sqrt(2)-1)/3);e.beginPath(),n>0&&i>0&&(e.moveTo(t+u,r),e.lineTo(t+i-u,r),e.bezierCurveTo(t+i-u+h*u,r,t+i,r+l-h*l,t+i,r+l),e.lineTo(t+i,r+n-l),e.bezierCurveTo(t+i,r+n-l+h*l,t+i-u+h*u,r+n,t+i-u,r+n),e.lineTo(t+u,r+n),e.bezierCurveTo(t+u-h*u,r+n,t,r+n-l+h*l,t,r+n-l),e.lineTo(t,r+l),e.bezierCurveTo(t,r+l-h*l,t+u-h*u,r,t+u,r),e.closePath())}return new ce(t,r,t+i,r+n)}getMarkers(){return null}}class V1 extends A{constructor(){super(...arguments),this.type="circle"}path(e){var t=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return e&&i>0&&(e.beginPath(),e.arc(t,r,i,0,Math.PI*2,!1),e.closePath()),new ce(t-i,r-i,t+i,r+i)}getMarkers(){return null}}class L1 extends A{constructor(){super(...arguments),this.type="ellipse"}path(e){var t=4*((Math.sqrt(2)-1)/3),r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),o=this.getAttribute("cy").getPixels("y");return e&&r>0&&i>0&&(e.beginPath(),e.moveTo(n+r,o),e.bezierCurveTo(n+r,o+t*i,n+t*r,o+i,n,o+i),e.bezierCurveTo(n-t*r,o+i,n-r,o+t*i,n-r,o),e.bezierCurveTo(n-r,o-t*i,n-t*r,o-i,n,o-i),e.bezierCurveTo(n+t*r,o-i,n+r,o-t*i,n+r,o),e.closePath()),new ce(n-r,o-i,n+r,o+i)}getMarkers(){return null}}class k1 extends A{constructor(){super(...arguments),this.type="line"}getPoints(){return[new k(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new k(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(e){var[{x:t,y:r},{x:i,y:n}]=this.getPoints();return e&&(e.beginPath(),e.moveTo(t,r),e.lineTo(i,n)),new ce(t,r,i,n)}getMarkers(){var[e,t]=this.getPoints(),r=e.angleTo(t);return[[e,r],[t,r]]}}class $l extends A{constructor(e,t,r){super(e,t,r),this.type="polyline",this.points=[],this.points=k.parsePath(this.getAttribute("points").getString())}path(e){var{points:t}=this,[{x:r,y:i}]=t,n=new ce(r,i);return e&&(e.beginPath(),e.moveTo(r,i)),t.forEach(o=>{var{x:s,y:u}=o;n.addPoint(s,u),e&&e.lineTo(s,u)}),n}getMarkers(){var{points:e}=this,t=e.length-1,r=[];return e.forEach((i,n)=>{n!==t&&r.push([i,i.angleTo(e[n+1])])}),r.length>0&&r.push([e[e.length-1],r[r.length-1][1]]),r}}class B1 extends $l{constructor(){super(...arguments),this.type="polygon"}path(e){var t=super.path(e),[{x:r,y:i}]=this.points;return e&&(e.lineTo(r,i),e.closePath()),t}}class j1 extends I{constructor(){super(...arguments),this.type="pattern"}createPattern(e,t,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),o=new Nt(this.document,null);o.attributes.viewBox=new S(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.width=new S(this.document,"width","".concat(i,"px")),o.attributes.height=new S(this.document,"height","".concat(n,"px")),o.attributes.transform=new S(this.document,"transform",this.getAttribute("patternTransform").getValue()),o.children=this.children;var s=this.document.createCanvas(i,n),u=s.getContext("2d"),l=this.getAttribute("x"),h=this.getAttribute("y");l.hasValue()&&h.hasValue()&&u.translate(l.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var f=-1;f<=1;f++)for(var c=-1;c<=1;c++)u.save(),o.attributes.x=new S(this.document,"x",f*s.width),o.attributes.y=new S(this.document,"y",c*s.height),o.render(u),u.restore();var v=e.createPattern(s,"repeat");return v}}class F1 extends I{constructor(){super(...arguments),this.type="marker"}render(e,t,r){if(t){var{x:i,y:n}=t,o=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");e.translate(i,n),o==="auto"&&e.rotate(r),s==="strokeWidth"&&e.scale(e.lineWidth,e.lineWidth),e.save();var u=new Nt(this.document,null);u.type=this.type,u.attributes.viewBox=new S(this.document,"viewBox",this.getAttribute("viewBox").getValue()),u.attributes.refX=new S(this.document,"refX",this.getAttribute("refX").getValue()),u.attributes.refY=new S(this.document,"refY",this.getAttribute("refY").getValue()),u.attributes.width=new S(this.document,"width",this.getAttribute("markerWidth").getValue()),u.attributes.height=new S(this.document,"height",this.getAttribute("markerHeight").getValue()),u.attributes.overflow=new S(this.document,"overflow",this.getAttribute("overflow").getValue()),u.attributes.fill=new S(this.document,"fill",this.getAttribute("fill").getColor("black")),u.attributes.stroke=new S(this.document,"stroke",this.getAttribute("stroke").getValue("none")),u.children=this.children,u.render(e),e.restore(),s==="strokeWidth"&&e.scale(1/e.lineWidth,1/e.lineWidth),o==="auto"&&e.rotate(-r),e.translate(-i,-n)}}}class U1 extends I{constructor(){super(...arguments),this.type="defs"}render(){}}class sn extends Ge{constructor(){super(...arguments),this.type="g"}getBoundingBox(e){var t=new ce;return this.children.forEach(r=>{t.addBoundingBox(r.getBoundingBox(e))}),t}}class wl extends I{constructor(e,t,r){super(e,t,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach(o=>{o.type==="stop"&&i.push(o)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(e,t,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,o=this.getGradient(e,t);if(!o)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach(p=>{o.addColorStop(p.offset,this.addParentOpacity(r,p.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:u,viewPort:l}=s.screen,[h]=l.viewPorts,f=new El(s,null);f.attributes.x=new S(s,"x",-u/3),f.attributes.y=new S(s,"y",-u/3),f.attributes.width=new S(s,"width",u),f.attributes.height=new S(s,"height",u);var c=new sn(s,null);c.attributes.transform=new S(s,"transform",this.getAttribute("gradientTransform").getValue()),c.children=[f];var v=new Nt(s,null);v.attributes.x=new S(s,"x",0),v.attributes.y=new S(s,"y",0),v.attributes.width=new S(s,"width",h.width),v.attributes.height=new S(s,"height",h.height),v.children=[c];var g=s.createCanvas(h.width,h.height),d=g.getContext("2d");return d.fillStyle=o,v.render(d),d.createPattern(g,"no-repeat")}return o}inheritStopContainer(e){this.attributesToInherit.forEach(t=>{!this.getAttribute(t).hasValue()&&e.getAttribute(t).hasValue()&&this.getAttribute(t,!0).setValue(e.getAttribute(t).getValue())})}addParentOpacity(e,t){if(e.hasValue()){var r=new S(this.document,"color",t);return r.addOpacity(e).getColor()}return t}}class G1 extends wl{constructor(e,t,r){super(e,t,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(e,t){var r=this.getGradientUnits()==="objectBoundingBox",i=r?t.getBoundingBox(e):null;if(r&&!i)return null;!this.getAttribute("x1").hasValue()&&!this.getAttribute("y1").hasValue()&&!this.getAttribute("x2").hasValue()&&!this.getAttribute("y2").hasValue()&&(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),u=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===s&&o===u?null:e.createLinearGradient(n,o,s,u)}}class z1 extends wl{constructor(e,t,r){super(e,t,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(e,t){var r=this.getGradientUnits()==="objectBoundingBox",i=t.getBoundingBox(e);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),o=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=n,u=o;this.getAttribute("fx").hasValue()&&(s=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(u=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var l=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return e.createRadialGradient(s,u,h,n,o,l)}}class H1 extends I{constructor(e,t,r){super(e,t,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),o=this.getStyle("stop-color",!0);o.getString()===""&&o.setValue("#000"),n.hasValue()&&(o=o.addOpacity(n)),this.offset=i,this.color=o.getColor()}}class on extends I{constructor(e,t,r){super(e,t,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,e.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new S(e,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var e=this.getAttribute("attributeType").getString(),t=this.getAttribute("attributeName").getString();return e==="CSS"?this.parent.getStyle(t,!0):this.parent.getAttribute(t,!0)}calcValue(){var{initialUnits:e}=this,{progress:t,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*t;return e==="%"&&(n*=100),"".concat(n).concat(e)}update(e){var{parent:t}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if(this.getAttribute("repeatCount").getString()==="indefinite"||this.getAttribute("repeatDur").getString()==="indefinite")this.duration=0;else if(i==="freeze"&&!this.frozen)this.frozen=!0,t.animationFrozen=!0,t.animationFrozenValue=r.getString();else if(i==="remove"&&!this.removed)return this.removed=!0,r.setValue(t.animationFrozen?t.animationFrozenValue:this.initialValue),!0;return!1}this.duration+=e;var n=!1;if(this.begin<this.duration){var o=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var u=s.getString();o="".concat(u,"(").concat(o,")")}r.setValue(o),n=!0}return n}getProgress(){var{document:e,values:t}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(t.hasValue()){var i=r.progress*(t.getValue().length-1),n=Math.floor(i),o=Math.ceil(i);r.from=new S(e,"from",parseFloat(t.getValue()[n])),r.to=new S(e,"to",parseFloat(t.getValue()[o])),r.progress=(i-n)/(o-n)}else r.from=this.from,r.to=this.to;return r}}class Y1 extends on{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:e,from:t,to:r}=this.getProgress(),i=new yi(t.getColor()),n=new yi(r.getColor());if(i.ok&&n.ok){var o=i.r+(n.r-i.r)*e,s=i.g+(n.g-i.g)*e,u=i.b+(n.b-i.b)*e;return"rgb(".concat(Math.floor(o),", ").concat(Math.floor(s),", ").concat(Math.floor(u),")")}return this.getAttribute("from").getColor()}}class X1 extends on{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:e,from:t,to:r}=this.getProgress(),i=ne(t.getString()),n=ne(r.getString()),o=i.map((s,u)=>{var l=n[u];return s+(l-s)*e}).join(" ");return o}}class W1 extends I{constructor(e,t,r){super(e,t,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=e,{children:n}=this;for(var o of n)switch(o.type){case"font-face":{this.fontFace=o;var s=o.getStyle("font-family");s.hasValue()&&(i[s.getString()]=this);break}case"missing-glyph":this.missingGlyph=o;break;case"glyph":{var u=o;u.arabicForm?(this.isRTL=!0,this.isArabic=!0,typeof this.glyphs[u.unicode]>"u"&&(this.glyphs[u.unicode]=Object.create(null)),this.glyphs[u.unicode][u.arabicForm]=u):this.glyphs[u.unicode]=u;break}}}render(){}}class q1 extends I{constructor(e,t,r){super(e,t,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class Q1 extends A{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class K1 extends Ce{constructor(){super(...arguments),this.type="tref"}getText(){var e=this.getHrefAttribute().getDefinition();if(e){var t=e.children[0];if(t)return t.getText()}return""}}class Z1 extends Ce{constructor(e,t,r){super(e,t,r),this.type="a";var{childNodes:i}=t,n=i[0],o=i.length>0&&Array.from(i).every(s=>s.nodeType===3);this.hasText=o,this.text=o?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(e){if(this.hasText){super.renderChildren(e);var{document:t,x:r,y:i}=this,{mouse:n}=t.screen,o=new S(t,"fontSize",Z.parse(t.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new ce(r,i-o.getPixels("y"),r+this.measureText(e),i))}else if(this.children.length>0){var s=new sn(this.document,null);s.children=this.children,s.parent=this,s.render(e)}}onClick(){var{window:e}=this.document;e&&e.open(this.getHrefAttribute().getString())}onMouseMove(){var e=this.document.ctx;e.canvas.style.cursor="pointer"}}function Mo(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(a);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),t.push.apply(t,r)}return t}function ir(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Mo(Object(t),!0).forEach(function(r){an(a,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):Mo(Object(t)).forEach(function(r){Object.defineProperty(a,r,Object.getOwnPropertyDescriptor(t,r))})}return a}class J1 extends Ce{constructor(e,t,r){super(e,t,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(e){var{dataArray:t}=this;e&&e.beginPath(),t.forEach(r=>{var{type:i,points:n}=r;switch(i){case w.LINE_TO:e&&e.lineTo(n[0],n[1]);break;case w.MOVE_TO:e&&e.moveTo(n[0],n[1]);break;case w.CURVE_TO:e&&e.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case w.QUAD_TO:e&&e.quadraticCurveTo(n[0],n[1],n[2],n[3]);break;case w.ARC:{var[o,s,u,l,h,f,c,v]=n,g=u>l?u:l,d=u>l?1:u/l,p=u>l?l/u:1;e&&(e.translate(o,s),e.rotate(c),e.scale(d,p),e.arc(0,0,g,h,h+f,!!(1-v)),e.scale(1/d,1/p),e.rotate(-c),e.translate(-o,-s));break}case w.CLOSE_PATH:e&&e.closePath();break}})}renderChildren(e){this.setTextData(e),e.save();var t=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=e.fillStyle;t==="underline"&&e.beginPath(),i.forEach((o,s)=>{var{p0:u,p1:l,rotation:h,text:f}=o;e.save(),e.translate(u.x,u.y),e.rotate(h),e.fillStyle&&e.fillText(f,0,0),e.strokeStyle&&e.strokeText(f,0,0),e.restore(),t==="underline"&&(s===0&&e.moveTo(u.x,u.y+r/8),e.lineTo(l.x,l.y+r/5))}),t==="underline"&&(e.lineWidth=r/20,e.strokeStyle=n,e.stroke(),e.closePath()),e.restore()}getLetterSpacingAt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return this.letterSpacingCache[e]||0}findSegmentToFitChar(e,t,r,i,n,o,s,u,l){var h=o,f=this.measureText(e,u);u===" "&&t==="justify"&&r<i&&(f+=(i-r)/n),l>-1&&(h+=this.getLetterSpacingAt(l));var c=this.textHeight/20,v=this.getEquidistantPointOnPath(h,c,0),g=this.getEquidistantPointOnPath(h+f,c,0),d={p0:v,p1:g},p=v&&g?Math.atan2(g.y-v.y,g.x-v.x):0;if(s){var y=Math.cos(Math.PI/2+p)*s,x=Math.cos(-p)*s;d.p0=ir(ir({},v),{},{x:v.x+y,y:v.y+x}),d.p1=ir(ir({},g),{},{x:g.x+y,y:g.y+x})}return h+=f,{offset:h,segment:d,rotation:p}}measureText(e,t){var{measuresCache:r}=this,i=t||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(e,i);return r.set(i,n),n}setTextData(e){if(!this.glyphInfo){var t=this.getText(),r=t.split(""),i=t.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(T=>T.getPixels("x")),o=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),u=this.getStyle("letter-spacing"),l=this.parent.getStyle("letter-spacing"),h=0;!u.hasValue()||u.getValue()==="inherit"?h=l.getPixels():u.hasValue()&&u.getValue()!=="initial"&&u.getValue()!=="unset"&&(h=u.getPixels());var f=[],c=t.length;this.letterSpacingCache=f;for(var v=0;v<c;v++)f.push(typeof n[v]<"u"?n[v]:h);var g=f.reduce((T,$,E)=>E===0?0:T+$||0,0),d=this.measureText(e),p=Math.max(d+g,0);this.textWidth=d,this.textHeight=this.getFontSize(),this.glyphInfo=[];var y=this.getPathLength(),x=this.getStyle("startOffset").getNumber(0)*y,b=0;(s==="middle"||s==="center")&&(b=-p/2),(s==="end"||s==="right")&&(b=-p),b+=x,r.forEach((T,$)=>{var{offset:E,segment:O,rotation:C}=this.findSegmentToFitChar(e,s,p,y,i,b,o,T,$);b=E,!(!O.p0||!O.p1)&&this.glyphInfo.push({text:r[$],p0:O.p0,p1:O.p1,rotation:C})})}}parsePathData(e){if(this.pathLength=-1,!e)return[];var t=[],{pathParser:r}=e;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,o=i?i.y:0,s=r.next(),u=s.type,l=[];switch(s.type){case w.MOVE_TO:this.pathM(r,l);break;case w.LINE_TO:u=this.pathL(r,l);break;case w.HORIZ_LINE_TO:u=this.pathH(r,l);break;case w.VERT_LINE_TO:u=this.pathV(r,l);break;case w.CURVE_TO:this.pathC(r,l);break;case w.SMOOTH_CURVE_TO:u=this.pathS(r,l);break;case w.QUAD_TO:this.pathQ(r,l);break;case w.SMOOTH_QUAD_TO:u=this.pathT(r,l);break;case w.ARC:l=this.pathA(r);break;case w.CLOSE_PATH:A.pathZ(r);break}s.type!==w.CLOSE_PATH?t.push({type:u,points:l,start:{x:n,y:o},pathLength:this.calcLength(n,o,u,l)}):t.push({type:w.CLOSE_PATH,points:[],pathLength:0})}return t}pathM(e,t){var{x:r,y:i}=A.pathM(e).point;t.push(r,i)}pathL(e,t){var{x:r,y:i}=A.pathL(e).point;return t.push(r,i),w.LINE_TO}pathH(e,t){var{x:r,y:i}=A.pathH(e).point;return t.push(r,i),w.LINE_TO}pathV(e,t){var{x:r,y:i}=A.pathV(e).point;return t.push(r,i),w.LINE_TO}pathC(e,t){var{point:r,controlPoint:i,currentPoint:n}=A.pathC(e);t.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(e,t){var{point:r,controlPoint:i,currentPoint:n}=A.pathS(e);return t.push(r.x,r.y,i.x,i.y,n.x,n.y),w.CURVE_TO}pathQ(e,t){var{controlPoint:r,currentPoint:i}=A.pathQ(e);t.push(r.x,r.y,i.x,i.y)}pathT(e,t){var{controlPoint:r,currentPoint:i}=A.pathT(e);return t.push(r.x,r.y,i.x,i.y),w.QUAD_TO}pathA(e){var{rX:t,rY:r,sweepFlag:i,xAxisRotation:n,centp:o,a1:s,ad:u}=A.pathA(e);return i===0&&u>0&&(u-=2*Math.PI),i===1&&u<0&&(u+=2*Math.PI),[o.x,o.y,t,r,s,u,n,i]}calcLength(e,t,r,i){var n=0,o=null,s=null,u=0;switch(r){case w.LINE_TO:return this.getLineLength(e,t,i[0],i[1]);case w.CURVE_TO:for(n=0,o=this.getPointOnCubicBezier(0,e,t,i[0],i[1],i[2],i[3],i[4],i[5]),u=.01;u<=1;u+=.01)s=this.getPointOnCubicBezier(u,e,t,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return n;case w.QUAD_TO:for(n=0,o=this.getPointOnQuadraticBezier(0,e,t,i[0],i[1],i[2],i[3]),u=.01;u<=1;u+=.01)s=this.getPointOnQuadraticBezier(u,e,t,i[0],i[1],i[2],i[3]),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return n;case w.ARC:{n=0;var l=i[4],h=i[5],f=i[4]+h,c=Math.PI/180;if(Math.abs(l-f)<c&&(c=Math.abs(l-f)),o=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],l,0),h<0)for(u=l-c;u>f;u-=c)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;else for(u=l+c;u<f;u+=c)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],f,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),n}}return 0}getPointOnLine(e,t,r,i,n){var o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:t,s=arguments.length>6&&arguments[6]!==void 0?arguments[6]:r,u=(n-r)/(i-t+rt),l=Math.sqrt(e*e/(1+u*u));i<t&&(l*=-1);var h=u*l,f=null;if(i===t)f={x:o,y:s+h};else if((s-r)/(o-t+rt)===u)f={x:o+l,y:s+h};else{var c=0,v=0,g=this.getLineLength(t,r,i,n);if(g<rt)return null;var d=(o-t)*(i-t)+(s-r)*(n-r);d/=g*g,c=t+d*(i-t),v=r+d*(n-r);var p=this.getLineLength(o,s,c,v),y=Math.sqrt(e*e-p*p);l=Math.sqrt(y*y/(1+u*u)),i<t&&(l*=-1),h=u*l,f={x:c+l,y:v+h}}return f}getPointOnPath(e){var t=this.getPathLength(),r=0,i=null;if(e<-5e-5||e-5e-5>t)return null;var{dataArray:n}=this;for(var o of n){if(o&&(o.pathLength<5e-5||r+o.pathLength+5e-5<e)){r+=o.pathLength;continue}var s=e-r,u=0;switch(o.type){case w.LINE_TO:i=this.getPointOnLine(s,o.start.x,o.start.y,o.points[0],o.points[1],o.start.x,o.start.y);break;case w.ARC:{var l=o.points[4],h=o.points[5],f=o.points[4]+h;if(u=l+s/o.pathLength*h,h<0&&u<f||h>=0&&u>f)break;i=this.getPointOnEllipticalArc(o.points[0],o.points[1],o.points[2],o.points[3],u,o.points[6]);break}case w.CURVE_TO:u=s/o.pathLength,u>1&&(u=1),i=this.getPointOnCubicBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3],o.points[4],o.points[5]);break;case w.QUAD_TO:u=s/o.pathLength,u>1&&(u=1),i=this.getPointOnQuadraticBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3]);break}if(i)return i;break}return null}getLineLength(e,t,r,i){return Math.sqrt((r-e)*(r-e)+(i-t)*(i-t))}getPathLength(){return this.pathLength===-1&&(this.pathLength=this.dataArray.reduce((e,t)=>t.pathLength>0?e+t.pathLength:e,0)),this.pathLength}getPointOnCubicBezier(e,t,r,i,n,o,s,u,l){var h=u*wo(e)+o*Co(e)+i*Ao(e)+t*Po(e),f=l*wo(e)+s*Co(e)+n*Ao(e)+r*Po(e);return{x:h,y:f}}getPointOnQuadraticBezier(e,t,r,i,n,o,s){var u=o*Ro(e)+i*No(e)+t*Io(e),l=s*Ro(e)+n*No(e)+r*Io(e);return{x:u,y:l}}getPointOnEllipticalArc(e,t,r,i,n,o){var s=Math.cos(o),u=Math.sin(o),l={x:r*Math.cos(n),y:i*Math.sin(n)};return{x:e+(l.x*s-l.y*u),y:t+(l.x*u+l.y*s)}}buildEquidistantCache(e,t){var r=this.getPathLength(),i=t||.25,n=e||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var o=0,s=0;s<=r;s+=i){var u=this.getPointOnPath(s),l=this.getPointOnPath(s+i);!u||!l||(o+=this.getLineLength(u.x,u.y,l.x,l.y),o>=n&&(this.equidistantCache.points.push({x:u.x,y:u.y,distance:s}),o-=n))}}}getEquidistantPointOnPath(e,t,r){if(this.buildEquidistantCache(t,r),e<0||e-this.getPathLength()>5e-5)return null;var i=Math.round(e/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var e2=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class t2 extends Ge{constructor(e,t,r){super(e,t,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);e.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(e){var t=this;return xe(function*(){try{var r=yield t.document.createImage(e);t.image=r}catch(i){console.error('Error while loading image "'.concat(e,'":'),i)}t.loaded=!0})()}loadSvg(e){var t=this;return xe(function*(){var r=e2.exec(e);if(r){var i=r[5];r[4]==="base64"?t.image=atob(i):t.image=decodeURIComponent(i)}else try{var n=yield t.document.fetch(e),o=yield n.text();t.image=o}catch(s){console.error('Error while loading image "'.concat(e,'":'),s)}t.loaded=!0})()}renderChildren(e){var{document:t,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!(!i||!r||!s||!u)){if(e.save(),e.translate(n,o),this.isSvg){var l=t.canvg.forkString(e,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:u});l.document.documentElement.parent=this,l.render()}else{var h=this.image;t.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:u,desiredHeight:h.height}),this.loaded&&(typeof h.complete>"u"||h.complete)&&e.drawImage(h,0,0)}e.restore()}}getBoundingBox(){var e=this.getAttribute("x").getPixels("x"),t=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new ce(e,t,e+r,t+i)}}class r2 extends Ge{constructor(){super(...arguments),this.type="symbol"}render(e){}}class a2{constructor(e){this.document=e,this.loaded=!1,e.fonts.push(this)}load(e,t){var r=this;return xe(function*(){try{var{document:i}=r,n=yield i.canvg.parser.load(t),o=n.getElementsByTagName("font");Array.from(o).forEach(s=>{var u=i.createElement(s);i.definitions[e]=u})}catch(s){console.error('Error while loading font "'.concat(t,'":'),s)}r.loaded=!0})()}}class Cl extends I{constructor(e,t,r){super(e,t,r),this.type="style";var i=lt(Array.from(t.childNodes).map(o=>o.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")),n=i.split("}");n.forEach(o=>{var s=o.trim();if(s){var u=s.split("{"),l=u[0].split(","),h=u[1].split(";");l.forEach(f=>{var c=f.trim();if(c){var v=e.styles[c]||{};if(h.forEach(p=>{var y=p.indexOf(":"),x=p.substr(0,y).trim(),b=p.substr(y+1,p.length-y).trim();x&&b&&(v[x]=new S(e,x,b))}),e.styles[c]=v,e.stylesSpecificity[c]=m1(c),c==="@font-face"){var g=v["font-family"].getString().replace(/"|'/g,""),d=v.src.getString().split(",");d.forEach(p=>{if(p.indexOf('format("svg")')>0){var y=ml(p);y&&new a2(e).load(g,y)}})}}})}})}}Cl.parseExternalUrl=ml;class i2 extends Ge{constructor(){super(...arguments),this.type="use"}setContext(e){super.setContext(e);var t=this.getAttribute("x"),r=this.getAttribute("y");t.hasValue()&&e.translate(t.getPixels("x"),0),r.hasValue()&&e.translate(0,r.getPixels("y"))}path(e){var{element:t}=this;t&&t.path(e)}renderChildren(e){var{document:t,element:r}=this;if(r){var i=r;if(r.type==="symbol"&&(i=new Nt(t,null),i.attributes.viewBox=new S(t,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new S(t,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new S(t,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new S(t,"opacity",this.calculateOpacity())),i.type==="svg"){var n=this.getStyle("width",!1,!0),o=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new S(t,"width",n.getString())),o.hasValue()&&(i.attributes.height=new S(t,"height",o.getString()))}var s=i.parent;i.parent=this,i.render(e),i.parent=s}}getBoundingBox(e){var{element:t}=this;return t?t.getBoundingBox(e):null}elementTransform(){var{document:e,element:t}=this;return Be.fromElement(e,t)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function nr(a,e,t,r,i,n){return a[t*r*4+e*4+n]}function sr(a,e,t,r,i,n,o){a[t*r*4+e*4+n]=o}function F(a,e,t){var r=a[e];return r*t}function me(a,e,t,r){return e+Math.cos(a)*t+Math.sin(a)*r}class Al extends I{constructor(e,t,r){super(e,t,r),this.type="feColorMatrix";var i=ne(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":{var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break}case"hueRotate":{var o=i[0]*Math.PI/180;i=[me(o,.213,.787,-.213),me(o,.715,-.715,-.715),me(o,.072,-.072,.928),0,0,me(o,.213,-.213,.143),me(o,.715,.285,.14),me(o,.072,-.072,-.283),0,0,me(o,.213,-.213,-.787),me(o,.715,-.715,.715),me(o,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break}case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1];break}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(e,t,r,i,n){for(var{includeOpacity:o,matrix:s}=this,u=e.getImageData(0,0,i,n),l=0;l<n;l++)for(var h=0;h<i;h++){var f=nr(u.data,h,l,i,n,0),c=nr(u.data,h,l,i,n,1),v=nr(u.data,h,l,i,n,2),g=nr(u.data,h,l,i,n,3),d=F(s,0,f)+F(s,1,c)+F(s,2,v)+F(s,3,g)+F(s,4,1),p=F(s,5,f)+F(s,6,c)+F(s,7,v)+F(s,8,g)+F(s,9,1),y=F(s,10,f)+F(s,11,c)+F(s,12,v)+F(s,13,g)+F(s,14,1),x=F(s,15,f)+F(s,16,c)+F(s,17,v)+F(s,18,g)+F(s,19,1);o&&(d=0,p=0,y=0,x*=g/255),sr(u.data,h,l,i,n,0,d),sr(u.data,h,l,i,n,1,p),sr(u.data,h,l,i,n,2,y),sr(u.data,h,l,i,n,3,x)}e.clearRect(0,0,i,n),e.putImageData(u,0,0)}}class kr extends I{constructor(){super(...arguments),this.type="mask"}apply(e,t){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),o=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!o&&!s){var u=new ce;this.children.forEach(g=>{u.addBoundingBox(g.getBoundingBox(e))}),i=Math.floor(u.x1),n=Math.floor(u.y1),o=Math.floor(u.width),s=Math.floor(u.height)}var l=this.removeStyles(t,kr.ignoreStyles),h=r.createCanvas(i+o,n+s),f=h.getContext("2d");r.screen.setDefaults(f),this.renderChildren(f),new Al(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(f,0,0,i+o,n+s);var c=r.createCanvas(i+o,n+s),v=c.getContext("2d");r.screen.setDefaults(v),t.render(v),v.globalCompositeOperation="destination-in",v.fillStyle=f.createPattern(h,"no-repeat"),v.fillRect(0,0,i+o,n+s),e.fillStyle=v.createPattern(c,"no-repeat"),e.fillRect(0,0,i+o,n+s),this.restoreStyles(t,l)}render(e){}}kr.ignoreStyles=["mask","transform","clip-path"];var _o=()=>{};class n2 extends I{constructor(){super(...arguments),this.type="clipPath"}apply(e){var{document:t}=this,r=Reflect.getPrototypeOf(e),{beginPath:i,closePath:n}=e;r&&(r.beginPath=_o,r.closePath=_o),Reflect.apply(i,e,[]),this.children.forEach(o=>{if(!(typeof o.path>"u")){var s=typeof o.elementTransform<"u"?o.elementTransform():null;s||(s=Be.fromElement(t,o)),s&&s.apply(e),o.path(e),r&&(r.closePath=n),s&&s.unapply(e)}}),Reflect.apply(n,e,[]),e.clip(),r&&(r.beginPath=i,r.closePath=n)}render(e){}}class Br extends I{constructor(){super(...arguments),this.type="filter"}apply(e,t){var{document:r,children:i}=this,n=t.getBoundingBox(e);if(n){var o=0,s=0;i.forEach(y=>{var x=y.extraFilterDistance||0;o=Math.max(o,x),s=Math.max(s,x)});var u=Math.floor(n.width),l=Math.floor(n.height),h=u+2*o,f=l+2*s;if(!(h<1||f<1)){var c=Math.floor(n.x),v=Math.floor(n.y),g=this.removeStyles(t,Br.ignoreStyles),d=r.createCanvas(h,f),p=d.getContext("2d");r.screen.setDefaults(p),p.translate(-c+o,-v+s),t.render(p),i.forEach(y=>{typeof y.apply=="function"&&y.apply(p,0,0,h,f)}),e.drawImage(d,0,0,h,f,c-o,v-s,h,f),this.restoreStyles(t,g)}}}render(e){}}Br.ignoreStyles=["filter","transform","clip-path"];class s2 extends I{constructor(e,t,r){super(e,t,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(e,t,r,i,n){}}class o2 extends I{constructor(){super(...arguments),this.type="feMorphology"}apply(e,t,r,i,n){}}class u2 extends I{constructor(){super(...arguments),this.type="feComposite"}apply(e,t,r,i,n){}}class l2 extends I{constructor(e,t,r){super(e,t,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(e,t,r,i,n){var{document:o,blurRadius:s}=this,u=o.window?o.window.document.body:null,l=e.canvas;l.id=o.getUniqueId(),u&&(l.style.display="none",u.appendChild(l)),r1(l,t,r,i,n,s),u&&u.removeChild(l)}}class h2 extends I{constructor(){super(...arguments),this.type="title"}}class v2 extends I{constructor(){super(...arguments),this.type="desc"}}var f2={svg:Nt,rect:El,circle:V1,ellipse:L1,line:k1,polyline:$l,polygon:B1,path:A,pattern:j1,marker:F1,defs:U1,linearGradient:G1,radialGradient:z1,stop:H1,animate:on,animateColor:Y1,animateTransform:X1,font:W1,"font-face":q1,"missing-glyph":Q1,glyph:Sl,text:Ce,tspan:Lr,tref:K1,a:Z1,textPath:J1,image:t2,g:sn,symbol:r2,style:Cl,use:i2,mask:kr,clipPath:n2,filter:Br,feDropShadow:s2,feMorphology:o2,feComposite:u2,feColorMatrix:Al,feGaussianBlur:l2,title:h2,desc:v2};function Do(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(a);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),t.push.apply(t,r)}return t}function c2(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Do(Object(t),!0).forEach(function(r){an(a,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):Do(Object(t)).forEach(function(r){Object.defineProperty(a,r,Object.getOwnPropertyDescriptor(t,r))})}return a}function g2(a,e){var t=document.createElement("canvas");return t.width=a,t.height=e,t}function d2(a){return Ti.apply(this,arguments)}function Ti(){return Ti=xe(function*(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=document.createElement("img");return e&&(t.crossOrigin="Anonymous"),new Promise((r,i)=>{t.onload=()=>{r(t)},t.onerror=(n,o,s,u,l)=>{i(l)},t.src=a})}),Ti.apply(this,arguments)}class $e{constructor(e){var{rootEmSize:t=12,emSize:r=12,createCanvas:i=$e.createCanvas,createImage:n=$e.createImage,anonymousCrossOrigin:o}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.canvg=e,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=e.screen,this.rootEmSize=t,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,o),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(e,t){return typeof t=="boolean"?(r,i)=>e(r,typeof i=="boolean"?i:t):e}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:e}=this;return e[e.length-1]}set emSize(e){var{emSizeStack:t}=this;t.push(e)}popEmSize(){var{emSizeStack:e}=this;e.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(e=>e.loaded)}isFontsLoaded(){return this.fonts.every(e=>e.loaded)}createDocumentElement(e){var t=this.createElement(e.documentElement);return t.root=!0,t.addStylesFromStyleDefinition(),this.documentElement=t,t}createElement(e){var t=e.nodeName.replace(/^[^:]+:/,""),r=$e.elementTypes[t];return typeof r<"u"?new r(this,e):new R1(this,e)}createTextNode(e){return new D1(this,e)}setViewBox(e){this.screen.setViewBox(c2({document:this},e))}}$e.createCanvas=g2;$e.createImage=d2;$e.elementTypes=f2;function Vo(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(a);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),t.push.apply(t,r)}return t}function Ve(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Vo(Object(t),!0).forEach(function(r){an(a,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):Vo(Object(t)).forEach(function(r){Object.defineProperty(a,r,Object.getOwnPropertyDescriptor(t,r))})}return a}class mt{constructor(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.parser=new za(r),this.screen=new Vr(e,r),this.options=r;var i=new $e(this,r),n=i.createDocumentElement(t);this.document=i,this.documentElement=n}static from(e,t){var r=arguments;return xe(function*(){var i=r.length>2&&r[2]!==void 0?r[2]:{},n=new za(i),o=yield n.parse(t);return new mt(e,o,i)})()}static fromString(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=new za(r),n=i.parseFromString(t);return new mt(e,n,r)}fork(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return mt.from(e,t,Ve(Ve({},this.options),r))}forkString(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return mt.fromString(e,t,Ve(Ve({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var e=arguments,t=this;return xe(function*(){var r=e.length>0&&e[0]!==void 0?e[0]:{};t.start(Ve({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield t.ready(),t.stop()})()}start(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{documentElement:t,screen:r,options:i}=this;r.start(t,Ve(Ve({enableRedraw:!0},i),e))}stop(){this.screen.stop()}resize(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.documentElement.resize(e,t,r)}}export{Z1 as AElement,Y1 as AnimateColorElement,on as AnimateElement,X1 as AnimateTransformElement,ce as BoundingBox,wo as CB1,Co as CB2,Ao as CB3,Po as CB4,mt as Canvg,V1 as CircleElement,n2 as ClipPathElement,U1 as DefsElement,v2 as DescElement,$e as Document,I as Element,L1 as EllipseElement,Al as FeColorMatrixElement,u2 as FeCompositeElement,s2 as FeDropShadowElement,l2 as FeGaussianBlurElement,o2 as FeMorphologyElement,Br as FilterElement,Z as Font,W1 as FontElement,q1 as FontFaceElement,sn as GElement,Sl as GlyphElement,wl as GradientElement,t2 as ImageElement,k1 as LineElement,G1 as LinearGradientElement,F1 as MarkerElement,kr as MaskElement,Tl as Matrix,Q1 as MissingGlyphElement,x1 as Mouse,rt as PSEUDO_ZERO,za as Parser,A as PathElement,w as PathParser,j1 as PatternElement,k as Point,B1 as PolygonElement,$l as PolylineElement,S as Property,Ro as QB1,No as QB2,Io as QB3,z1 as RadialGradientElement,El as RectElement,Ge as RenderedElement,E1 as Rotate,Nt as SVGElement,a2 as SVGFontLoader,$1 as Scale,Vr as Screen,Ol as Skew,w1 as SkewX,C1 as SkewY,H1 as StopElement,Cl as StyleElement,r2 as SymbolElement,K1 as TRefElement,Lr as TSpanElement,Ce as TextElement,J1 as TextPathElement,h2 as TitleElement,Be as Transform,S1 as Translate,R1 as UnknownElement,i2 as UseElement,b1 as ViewPort,lt as compressSpaces,mt as default,m1 as getSelectorSpecificity,l1 as normalizeAttributeName,h1 as normalizeColor,ml as parseExternalUrl,y2 as presets,ne as toNumbers,s1 as trimLeft,o1 as trimRight,Eo as vectorMagnitude,$o as vectorsAngle,xi as vectorsRatio};
