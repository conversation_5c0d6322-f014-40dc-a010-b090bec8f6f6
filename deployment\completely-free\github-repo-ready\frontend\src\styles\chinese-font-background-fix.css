/**
 * 中文字体与背景协调修复 - iFlytek 多模态面试评估系统
 * 确保中文字体在各种背景下都能正确显示和渲染
 */

/* ===== 中文字体系统定义 ===== */
@font-face {
  font-family: 'Microsoft YaHei';
  src: local('Microsoft YaHei'), local('微软雅黑');
  font-display: swap;
}

@font-face {
  font-family: 'PingFang SC';
  src: local('PingFang SC'), local('苹方');
  font-display: swap;
}

@font-face {
  font-family: 'Hiragino Sans GB';
  src: local('Hiragino Sans GB'), local('冬青黑体简体中文');
  font-display: swap;
}

/* ===== 中文字体变量系统 ===== */
:root {
  /* 中文字体堆栈 - 优先级从高到低 */
  --font-family-chinese-primary: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimHei', 'STHeiti', 'WenQuanYi Micro Hei', sans-serif;
  --font-family-chinese-title: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimHei', 'STHeiti', sans-serif;
  --font-family-chinese-body: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimSun', 'STSong', sans-serif;
  
  /* 字体渲染优化 */
  --font-smoothing: antialiased;
  --text-rendering: optimizeLegibility;
}

/* ===== 全局中文字体应用 ===== */
* {
  font-family: var(--font-family-chinese-primary) !important;
  -webkit-font-smoothing: var(--font-smoothing) !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: var(--text-rendering) !important;
}

/* ===== 首页中文字体与背景协调 ===== */
.enterprise-homepage {
  font-family: var(--font-family-chinese-primary) !important;
  /* 确保字体在浅色背景上清晰显示 */
  color: #262626 !important;
  text-shadow: none !important;
}

/* ===== 英雄区域中文字体优化 ===== */
.hero-section {
  /* 确保中文字体在深色渐变背景上清晰显示 */
  color: #ffffff !important;
}

.hero-title {
  font-family: var(--font-family-chinese-title) !important;
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
  /* 中文字体粗细优化 */
  font-weight: 700 !important;
  letter-spacing: 0.02em !important;
}

.hero-subtitle {
  font-family: var(--font-family-chinese-body) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4) !important;
  /* 中文行高优化 */
  line-height: 1.8 !important;
  letter-spacing: 0.01em !important;
}

.hero-brand {
  font-family: var(--font-family-chinese-title) !important;
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
}

/* ===== 统计数据中文字体优化 ===== */
.stat-number {
  font-family: var(--font-family-chinese-title) !important;
  color: #ffffff !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4) !important;
  /* 数字字体优化 */
  font-variant-numeric: tabular-nums !important;
}

.stat-label {
  font-family: var(--font-family-chinese-body) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4) !important;
}

/* ===== 导航栏中文字体优化 ===== */
.brand-text {
  font-family: var(--font-family-chinese-title) !important;
  color: #1890ff !important;
  font-weight: 600 !important;
  letter-spacing: 0.02em !important;
}

.enterprise-nav .el-menu-item {
  font-family: var(--font-family-chinese-body) !important;
  color: #262626 !important;
  font-weight: 500 !important;
  letter-spacing: 0.01em !important;
}

/* ===== 产品卡片中文字体优化 ===== */
.section-title {
  font-family: var(--font-family-chinese-title) !important;
  color: #262626 !important;
  font-weight: 700 !important;
  letter-spacing: 0.02em !important;
}

.section-subtitle {
  font-family: var(--font-family-chinese-body) !important;
  color: #595959 !important;
  line-height: 1.8 !important;
  letter-spacing: 0.01em !important;
}

.product-title {
  font-family: var(--font-family-chinese-title) !important;
  color: #262626 !important;
  font-weight: 600 !important;
  letter-spacing: 0.01em !important;
}

.product-description {
  font-family: var(--font-family-chinese-body) !important;
  color: #595959 !important;
  line-height: 1.8 !important;
  letter-spacing: 0.005em !important;
}

/* ===== 技术优势区域中文字体优化 ===== */
.advantage-title {
  font-family: var(--font-family-chinese-title) !important;
  color: #262626 !important;
  font-weight: 600 !important;
  letter-spacing: 0.01em !important;
}

.advantage-description {
  font-family: var(--font-family-chinese-body) !important;
  color: #595959 !important;
  line-height: 1.8 !important;
  letter-spacing: 0.005em !important;
}

.metric-value {
  font-family: var(--font-family-chinese-title) !important;
  color: #1890ff !important;
  font-weight: 700 !important;
  font-variant-numeric: tabular-nums !important;
}

.metric-label {
  font-family: var(--font-family-chinese-body) !important;
  color: #595959 !important;
  letter-spacing: 0.01em !important;
}

/* ===== 快速开始区域中文字体优化 ===== */
.quick-start-title {
  font-family: var(--font-family-chinese-title) !important;
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
  font-weight: 700 !important;
  letter-spacing: 0.02em !important;
}

.quick-start-subtitle {
  font-family: var(--font-family-chinese-body) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4) !important;
  line-height: 1.8 !important;
  letter-spacing: 0.01em !important;
}

/* ===== 按钮中文字体优化 ===== */
.primary-cta,
.secondary-cta,
.learn-more-btn,
.start-btn,
.demo-btn,
.report-btn,
.cta-button {
  font-family: var(--font-family-chinese-body) !important;
  font-weight: 600 !important;
  letter-spacing: 0.02em !important;
}

/* ===== 浮动卡片中文字体优化 ===== */
.feature-card span {
  font-family: var(--font-family-chinese-body) !important;
  color: #262626 !important;
  font-weight: 500 !important;
  letter-spacing: 0.01em !important;
}

/* ===== 列表项中文字体优化 ===== */
.feature-list li {
  font-family: var(--font-family-chinese-body) !important;
  color: #595959 !important;
  line-height: 1.8 !important;
  letter-spacing: 0.005em !important;
}

/* ===== 响应式中文字体优化 ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem !important;
    line-height: 1.3 !important;
    letter-spacing: 0.01em !important;
  }
  
  .hero-subtitle {
    font-size: 1rem !important;
    line-height: 1.6 !important;
  }
  
  .section-title {
    font-size: 1.8rem !important;
    line-height: 1.4 !important;
  }
  
  .product-title {
    font-size: 1.2rem !important;
    line-height: 1.5 !important;
  }
}

/* ===== 高对比度模式中文字体优化 ===== */
@media (prefers-contrast: high) {
  .hero-title,
  .hero-subtitle,
  .stat-number,
  .stat-label {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8) !important;
  }
  
  .section-title,
  .product-title,
  .advantage-title {
    color: #000000 !important;
    font-weight: 800 !important;
  }
}

/* ===== 打印样式中文字体优化 ===== */
@media print {
  * {
    font-family: 'SimSun', '宋体', serif !important;
    color: black !important;
    text-shadow: none !important;
  }
}

/* ===== 字体加载失败回退 ===== */
@supports not (font-display: swap) {
  * {
    font-family: 'SimHei', '黑体', sans-serif !important;
  }
}

/* ===== 确保字体渲染质量 ===== */
.enterprise-homepage * {
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}
