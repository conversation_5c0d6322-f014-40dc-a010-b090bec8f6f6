import{Fa as Q,O as at,Q as ot,X as lt,Y as ct,b as S,ha as W,n as rt,o as it,p as st}from"./chunk-63ZE7VZ5.mjs";import{a as s,c as Nt}from"./chunk-GTKDMUJJ.mjs";var X=function(){var n=s(function(m,i,a,c){for(a=a||{},c=m.length;c--;a[m[c]]=i);return a},"o"),t=[6,8,10,11,12,14,16,17,20,21],e=[1,9],o=[1,10],r=[1,11],u=[1,12],h=[1,13],f=[1,16],g=[1,17],p={trace:s(function(){},"trace"),yy:{},symbols_:{error:2,start:3,timeline:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,title:11,acc_title:12,acc_title_value:13,acc_descr:14,acc_descr_value:15,acc_descr_multiline_value:16,section:17,period_statement:18,event_statement:19,period:20,event:21,$accept:0,$end:1},terminals_:{2:"error",4:"timeline",6:"EOF",8:"SPACE",10:"NEWLINE",11:"title",12:"acc_title",13:"acc_title_value",14:"acc_descr",15:"acc_descr_value",16:"acc_descr_multiline_value",17:"section",20:"period",21:"event"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],performAction:s(function(i,a,c,d,y,l,E){var k=l.length-1;switch(y){case 1:return l[k-1];case 2:this.$=[];break;case 3:l[k-1].push(l[k]),this.$=l[k-1];break;case 4:case 5:this.$=l[k];break;case 6:case 7:this.$=[];break;case 8:d.getCommonDb().setDiagramTitle(l[k].substr(6)),this.$=l[k].substr(6);break;case 9:this.$=l[k].trim(),d.getCommonDb().setAccTitle(this.$);break;case 10:case 11:this.$=l[k].trim(),d.getCommonDb().setAccDescription(this.$);break;case 12:d.addSection(l[k].substr(8)),this.$=l[k].substr(8);break;case 15:d.addTask(l[k],0,""),this.$=l[k];break;case 16:d.addEvent(l[k].substr(2)),this.$=l[k];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},n(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:e,12:o,14:r,16:u,17:h,18:14,19:15,20:f,21:g},n(t,[2,7],{1:[2,1]}),n(t,[2,3]),{9:18,11:e,12:o,14:r,16:u,17:h,18:14,19:15,20:f,21:g},n(t,[2,5]),n(t,[2,6]),n(t,[2,8]),{13:[1,19]},{15:[1,20]},n(t,[2,11]),n(t,[2,12]),n(t,[2,13]),n(t,[2,14]),n(t,[2,15]),n(t,[2,16]),n(t,[2,4]),n(t,[2,9]),n(t,[2,10])],defaultActions:{},parseError:s(function(i,a){if(a.recoverable)this.trace(i);else{var c=new Error(i);throw c.hash=a,c}},"parseError"),parse:s(function(i){var a=this,c=[0],d=[],y=[null],l=[],E=this.table,k="",N=0,C=0,V=0,et=2,L=1,v=l.slice.call(arguments,1),x=Object.create(this.lexer),T={yy:{}};for(var $ in this.yy)Object.prototype.hasOwnProperty.call(this.yy,$)&&(T.yy[$]=this.yy[$]);x.setInput(i,T.yy),T.yy.lexer=x,T.yy.parser=this,typeof x.yylloc>"u"&&(x.yylloc={});var P=x.yylloc;l.push(P);var U=x.options&&x.options.ranges;typeof T.yy.parseError=="function"?this.parseError=T.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Zt(I){c.length=c.length-2*I,y.length=y.length-I,l.length=l.length-I}s(Zt,"popStack");function Mt(){var I;return I=d.pop()||x.lex()||L,typeof I!="number"&&(I instanceof Array&&(d=I,I=d.pop()),I=a.symbols_[I]||I),I}s(Mt,"lex");for(var w,Z,B,M,Jt,J,R={},O,H,nt,j;;){if(B=c[c.length-1],this.defaultActions[B]?M=this.defaultActions[B]:((w===null||typeof w>"u")&&(w=Mt()),M=E[B]&&E[B][w]),typeof M>"u"||!M.length||!M[0]){var K="";j=[];for(O in E[B])this.terminals_[O]&&O>et&&j.push("'"+this.terminals_[O]+"'");x.showPosition?K="Parse error on line "+(N+1)+`:
`+x.showPosition()+`
Expecting `+j.join(", ")+", got '"+(this.terminals_[w]||w)+"'":K="Parse error on line "+(N+1)+": Unexpected "+(w==L?"end of input":"'"+(this.terminals_[w]||w)+"'"),this.parseError(K,{text:x.match,token:this.terminals_[w]||w,line:x.yylineno,loc:P,expected:j})}if(M[0]instanceof Array&&M.length>1)throw new Error("Parse Error: multiple actions possible at state: "+B+", token: "+w);switch(M[0]){case 1:c.push(w),y.push(x.yytext),l.push(x.yylloc),c.push(M[1]),w=null,Z?(w=Z,Z=null):(C=x.yyleng,k=x.yytext,N=x.yylineno,P=x.yylloc,V>0&&V--);break;case 2:if(H=this.productions_[M[1]][1],R.$=y[y.length-H],R._$={first_line:l[l.length-(H||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(H||1)].first_column,last_column:l[l.length-1].last_column},U&&(R._$.range=[l[l.length-(H||1)].range[0],l[l.length-1].range[1]]),J=this.performAction.apply(R,[k,C,N,T.yy,M[1],y,l].concat(v)),typeof J<"u")return J;H&&(c=c.slice(0,-1*H*2),y=y.slice(0,-1*H),l=l.slice(0,-1*H)),c.push(this.productions_[M[1]][0]),y.push(R.$),l.push(R._$),nt=E[c[c.length-2]][c[c.length-1]],c.push(nt);break;case 3:return!0}}return!0},"parse")},b=function(){var m={EOF:1,parseError:s(function(a,c){if(this.yy.parser)this.yy.parser.parseError(a,c);else throw new Error(a)},"parseError"),setInput:s(function(i,a){return this.yy=a||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:s(function(){var i=this._input[0];this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i;var a=i.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:s(function(i){var a=i.length,c=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var y=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===d.length?this.yylloc.first_column:0)+d[d.length-c.length].length-c[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[y[0],y[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:s(function(){return this._more=!0,this},"more"),reject:s(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:s(function(i){this.unput(this.match.slice(i))},"less"),pastInput:s(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:s(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:s(function(){var i=this.pastInput(),a=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:s(function(i,a){var c,d,y;if(this.options.backtrack_lexer&&(y={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(y.yylloc.range=this.yylloc.range.slice(0))),d=i[0].match(/(?:\r\n?|\n).*/g),d&&(this.yylineno+=d.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:d?d[d.length-1].length-d[d.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],c=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var l in y)this[l]=y[l];return!1}return!1},"test_match"),next:s(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var i,a,c,d;this._more||(this.yytext="",this.match="");for(var y=this._currentRules(),l=0;l<y.length;l++)if(c=this._input.match(this.rules[y[l]]),c&&(!a||c[0].length>a[0].length)){if(a=c,d=l,this.options.backtrack_lexer){if(i=this.test_match(c,y[l]),i!==!1)return i;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(i=this.test_match(a,y[d]),i!==!1?i:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:s(function(){var a=this.next();return a||this.lex()},"lex"),begin:s(function(a){this.conditionStack.push(a)},"begin"),popState:s(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:s(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:s(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:s(function(a){this.begin(a)},"pushState"),stateStackSize:s(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:s(function(a,c,d,y){var l=y;switch(d){case 0:break;case 1:break;case 2:return 10;case 3:break;case 4:break;case 5:return 4;case 6:return 11;case 7:return this.begin("acc_title"),12;break;case 8:return this.popState(),"acc_title_value";break;case 9:return this.begin("acc_descr"),14;break;case 10:return this.popState(),"acc_descr_value";break;case 11:this.begin("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 17;case 15:return 21;case 16:return 20;case 17:return 6;case 18:return"INVALID"}},"anonymous"),rules:[/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:timeline\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:section\s[^:\n]+)/i,/^(?::\s(?:[^:\n]|:(?!\s))+)/i,/^(?:[^#:\n]+)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],inclusive:!0}}};return m}();p.lexer=b;function _(){this.yy={}}return s(_,"Parser"),_.prototype=p,p.Parser=_,new _}();X.parser=X;var ht=X;var D={};Nt(D,{addEvent:()=>bt,addSection:()=>ft,addTask:()=>xt,addTaskOrg:()=>kt,clear:()=>yt,default:()=>Lt,getCommonDb:()=>pt,getSections:()=>gt,getTasks:()=>mt});var z="",dt=0,Y=[],G=[],F=[],pt=s(()=>lt,"getCommonDb"),yt=s(function(){Y.length=0,G.length=0,z="",F.length=0,ot()},"clear"),ft=s(function(n){z=n,Y.push(n)},"addSection"),gt=s(function(){return Y},"getSections"),mt=s(function(){let n=ut(),t=100,e=0;for(;!n&&e<t;)n=ut(),e++;return G.push(...F),G},"getTasks"),xt=s(function(n,t,e){let o={id:dt++,section:z,type:z,task:n,score:t||0,events:e?[e]:[]};F.push(o)},"addTask"),bt=s(function(n){F.find(e=>e.id===dt-1).events.push(n)},"addEvent"),kt=s(function(n){let t={section:z,type:z,description:n,task:n,classes:[]};G.push(t)},"addTaskOrg"),ut=s(function(){let n=s(function(e){return F[e].processed},"compileTask"),t=!0;for(let[e,o]of F.entries())n(e),t=t&&o.processed;return t},"compileTasks"),Lt={clear:yt,getCommonDb:pt,addSection:ft,getSections:gt,getTasks:mt,addTask:xt,addTaskOrg:kt,addEvent:bt};var $t=12,q=s(function(n,t){let e=n.append("rect");return e.attr("x",t.x),e.attr("y",t.y),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("width",t.width),e.attr("height",t.height),e.attr("rx",t.rx),e.attr("ry",t.ry),t.class!==void 0&&e.attr("class",t.class),e},"drawRect"),Ht=s(function(n,t){let o=n.append("circle").attr("cx",t.cx).attr("cy",t.cy).attr("class","face").attr("r",15).attr("stroke-width",2).attr("overflow","visible"),r=n.append("g");r.append("circle").attr("cx",t.cx-15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666"),r.append("circle").attr("cx",t.cx+15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666");function u(g){let p=Q().startAngle(Math.PI/2).endAngle(3*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);g.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+2)+")")}s(u,"smile");function h(g){let p=Q().startAngle(3*Math.PI/2).endAngle(5*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);g.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+7)+")")}s(h,"sad");function f(g){g.append("line").attr("class","mouth").attr("stroke",2).attr("x1",t.cx-5).attr("y1",t.cy+7).attr("x2",t.cx+5).attr("y2",t.cy+7).attr("class","mouth").attr("stroke-width","1px").attr("stroke","#666")}return s(f,"ambivalent"),t.score>3?u(r):t.score<3?h(r):f(r),o},"drawFace"),At=s(function(n,t){let e=n.append("circle");return e.attr("cx",t.cx),e.attr("cy",t.cy),e.attr("class","actor-"+t.pos),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("r",t.r),e.class!==void 0&&e.attr("class",e.class),t.title!==void 0&&e.append("title").text(t.title),e},"drawCircle"),vt=s(function(n,t){let e=t.text.replace(/<br\s*\/?>/gi," "),o=n.append("text");o.attr("x",t.x),o.attr("y",t.y),o.attr("class","legend"),o.style("text-anchor",t.anchor),t.class!==void 0&&o.attr("class",t.class);let r=o.append("tspan");return r.attr("x",t.x+t.textMargin*2),r.text(e),o},"drawText"),Ct=s(function(n,t){function e(r,u,h,f,g){return r+","+u+" "+(r+h)+","+u+" "+(r+h)+","+(u+f-g)+" "+(r+h-g*1.2)+","+(u+f)+" "+r+","+(u+f)}s(e,"genPoints");let o=n.append("polygon");o.attr("points",e(t.x,t.y,50,20,7)),o.attr("class","labelBox"),t.y=t.y+t.labelMargin,t.x=t.x+.5*t.labelMargin,vt(n,t)},"drawLabel"),Pt=s(function(n,t,e){let o=n.append("g"),r=tt();r.x=t.x,r.y=t.y,r.fill=t.fill,r.width=e.width,r.height=e.height,r.class="journey-section section-type-"+t.num,r.rx=3,r.ry=3,q(o,r),wt(e)(t.text,o,r.x,r.y,r.width,r.height,{class:"journey-section section-type-"+t.num},e,t.colour)},"drawSection"),_t=-1,Bt=s(function(n,t,e){let o=t.x+e.width/2,r=n.append("g");_t++;let u=300+5*30;r.append("line").attr("id","task"+_t).attr("x1",o).attr("y1",t.y).attr("x2",o).attr("y2",u).attr("class","task-line").attr("stroke-width","1px").attr("stroke-dasharray","4 2").attr("stroke","#666"),Ht(r,{cx:o,cy:300+(5-t.score)*30,score:t.score});let h=tt();h.x=t.x,h.y=t.y,h.fill=t.fill,h.width=e.width,h.height=e.height,h.class="task task-type-"+t.num,h.rx=3,h.ry=3,q(r,h),wt(e)(t.task,r,h.x,h.y,h.width,h.height,{class:"task"},e,t.colour)},"drawTask"),Vt=s(function(n,t){q(n,{x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,class:"rect"}).lower()},"drawBackgroundRect"),Rt=s(function(){return{x:0,y:0,fill:void 0,"text-anchor":"start",width:100,height:100,textMargin:0,rx:0,ry:0}},"getTextObj"),tt=s(function(){return{x:0,y:0,width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),wt=function(){function n(r,u,h,f,g,p,b,_){let m=u.append("text").attr("x",h+g/2).attr("y",f+p/2+5).style("font-color",_).style("text-anchor","middle").text(r);o(m,b)}s(n,"byText");function t(r,u,h,f,g,p,b,_,m){let{taskFontSize:i,taskFontFamily:a}=_,c=r.split(/<br\s*\/?>/gi);for(let d=0;d<c.length;d++){let y=d*i-i*(c.length-1)/2,l=u.append("text").attr("x",h+g/2).attr("y",f).attr("fill",m).style("text-anchor","middle").style("font-size",i).style("font-family",a);l.append("tspan").attr("x",h+g/2).attr("dy",y).text(c[d]),l.attr("y",f+p/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),o(l,b)}}s(t,"byTspan");function e(r,u,h,f,g,p,b,_){let m=u.append("switch"),a=m.append("foreignObject").attr("x",h).attr("y",f).attr("width",g).attr("height",p).attr("position","fixed").append("xhtml:div").style("display","table").style("height","100%").style("width","100%");a.append("div").attr("class","label").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(r),t(r,m,h,f,g,p,b,_),o(a,b)}s(e,"byFo");function o(r,u){for(let h in u)h in u&&r.attr(h,u[h])}return s(o,"_setTextAttrs"),function(r){return r.textPlacement==="fo"?e:r.textPlacement==="old"?n:t}}(),Wt=s(function(n){n.append("defs").append("marker").attr("id","arrowhead").attr("refX",5).attr("refY",2).attr("markerWidth",6).attr("markerHeight",4).attr("orient","auto").append("path").attr("d","M 0,0 V 4 L6,2 Z")},"initGraphics");function St(n,t){n.each(function(){var e=W(this),o=e.text().split(/(\s+|<br>)/).reverse(),r,u=[],h=1.1,f=e.attr("y"),g=parseFloat(e.attr("dy")),p=e.text(null).append("tspan").attr("x",0).attr("y",f).attr("dy",g+"em");for(let b=0;b<o.length;b++)r=o[o.length-1-b],u.push(r),p.text(u.join(" ").trim()),(p.node().getComputedTextLength()>t||r==="<br>")&&(u.pop(),p.text(u.join(" ").trim()),r==="<br>"?u=[""]:u=[r],p=e.append("tspan").attr("x",0).attr("y",f).attr("dy",h+"em").text(r))})}s(St,"wrap");var zt=s(function(n,t,e,o){let r=e%$t-1,u=n.append("g");t.section=r,u.attr("class",(t.class?t.class+" ":"")+"timeline-node "+("section-"+r));let h=u.append("g"),f=u.append("g"),p=f.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(St,t.width).node().getBBox(),b=o.fontSize?.replace?o.fontSize.replace("px",""):o.fontSize;return t.height=p.height+b*1.1*.5+t.padding,t.height=Math.max(t.height,t.maxHeight),t.width=t.width+2*t.padding,f.attr("transform","translate("+t.width/2+", "+t.padding/2+")"),Ot(h,t,r,o),t},"drawNode"),Ft=s(function(n,t,e){let o=n.append("g"),u=o.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(St,t.width).node().getBBox(),h=e.fontSize?.replace?e.fontSize.replace("px",""):e.fontSize;return o.remove(),u.height+h*1.1*.5+t.padding},"getVirtualNodeHeight"),Ot=s(function(n,t,e){n.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+t.type).attr("d",`M0 ${t.height-5} v${-t.height+2*5} q0,-5 5,-5 h${t.width-2*5} q5,0 5,5 v${t.height-5} H0 Z`),n.append("line").attr("class","node-line-"+e).attr("x1",0).attr("y1",t.height).attr("x2",t.width).attr("y2",t.height)},"defaultBkg"),A={drawRect:q,drawCircle:At,drawSection:Pt,drawText:vt,drawLabel:Ct,drawTask:Bt,drawBackgroundRect:Vt,getTextObj:Rt,getNoteRect:tt,initGraphics:Wt,drawNode:zt,getVirtualNodeHeight:Ft};var jt=s(function(n,t,e,o){let r=ct(),u=r.leftMargin??50;S.debug("timeline",o.db);let h=r.securityLevel,f;h==="sandbox"&&(f=W("#i"+t));let p=(h==="sandbox"?W(f.nodes()[0].contentDocument.body):W("body")).select("#"+t);p.append("g");let b=o.db.getTasks(),_=o.db.getCommonDb().getDiagramTitle();S.debug("task",b),A.initGraphics(p);let m=o.db.getSections();S.debug("sections",m);let i=0,a=0,c=0,d=0,y=50+u,l=50;d=50;let E=0,k=!0;m.forEach(function(L){let v={number:E,descr:L,section:E,width:150,padding:20,maxHeight:i},x=A.getVirtualNodeHeight(p,v,r);S.debug("sectionHeight before draw",x),i=Math.max(i,x+20)});let N=0,C=0;S.debug("tasks.length",b.length);for(let[L,v]of b.entries()){let x={number:L,descr:v,section:v.section,width:150,padding:20,maxHeight:a},T=A.getVirtualNodeHeight(p,x,r);S.debug("taskHeight before draw",T),a=Math.max(a,T+20),N=Math.max(N,v.events.length);let $=0;for(let P of v.events){let U={descr:P,section:v.section,number:v.section,width:150,padding:20,maxHeight:50};$+=A.getVirtualNodeHeight(p,U,r)}v.events.length>0&&($+=(v.events.length-1)*10),C=Math.max(C,$)}S.debug("maxSectionHeight before draw",i),S.debug("maxTaskHeight before draw",a),m&&m.length>0?m.forEach(L=>{let v=b.filter(P=>P.section===L),x={number:E,descr:L,section:E,width:200*Math.max(v.length,1)-50,padding:20,maxHeight:i};S.debug("sectionNode",x);let T=p.append("g"),$=A.drawNode(T,x,E,r);S.debug("sectionNode output",$),T.attr("transform",`translate(${y}, ${d})`),l+=i+50,v.length>0&&Et(p,v,E,y,l,a,r,N,C,i,!1),y+=200*Math.max(v.length,1),l=d,E++}):(k=!1,Et(p,b,E,y,l,a,r,N,C,i,!0));let V=p.node().getBBox();S.debug("bounds",V),_&&p.append("text").text(_).attr("x",V.width/2-u).attr("font-size","4ex").attr("font-weight","bold").attr("y",20),c=k?i+a+150:a+100,p.append("g").attr("class","lineWrapper").append("line").attr("x1",u).attr("y1",c).attr("x2",V.width+3*u).attr("y2",c).attr("stroke-width",4).attr("stroke","black").attr("marker-end","url(#arrowhead)"),at(void 0,p,r.timeline?.padding??50,r.timeline?.useMaxWidth??!1)},"draw"),Et=s(function(n,t,e,o,r,u,h,f,g,p,b){for(let _ of t){let m={descr:_.task,section:e,number:e,width:150,padding:20,maxHeight:u};S.debug("taskNode",m);let i=n.append("g").attr("class","taskWrapper"),c=A.drawNode(i,m,e,h).height;if(S.debug("taskHeight after draw",c),i.attr("transform",`translate(${o}, ${r})`),u=Math.max(u,c),_.events){let d=n.append("g").attr("class","lineWrapper"),y=u;r+=100,y=y+Gt(n,_.events,e,o,r,h),r-=100,d.append("line").attr("x1",o+190/2).attr("y1",r+u).attr("x2",o+190/2).attr("y2",r+u+100+g+100).attr("stroke-width",2).attr("stroke","black").attr("marker-end","url(#arrowhead)").attr("stroke-dasharray","5,5")}o=o+200,b&&!h.timeline?.disableMulticolor&&e++}r=r-10},"drawTasks"),Gt=s(function(n,t,e,o,r,u){let h=0,f=r;r=r+100;for(let g of t){let p={descr:g,section:e,number:e,width:150,padding:20,maxHeight:50};S.debug("eventNode",p);let b=n.append("g").attr("class","eventWrapper"),m=A.drawNode(b,p,e,u).height;h=h+m,b.attr("transform",`translate(${o}, ${r})`),r=r+10+m}return r=f,h},"drawEvents"),Tt={setConf:s(()=>{},"setConf"),draw:jt};var qt=s(n=>{let t="";for(let e=0;e<n.THEME_COLOR_LIMIT;e++)n["lineColor"+e]=n["lineColor"+e]||n["cScaleInv"+e],rt(n["lineColor"+e])?n["lineColor"+e]=it(n["lineColor"+e],20):n["lineColor"+e]=st(n["lineColor"+e],20);for(let e=0;e<n.THEME_COLOR_LIMIT;e++){let o=""+(17-3*e);t+=`
    .section-${e-1} rect, .section-${e-1} path, .section-${e-1} circle, .section-${e-1} path  {
      fill: ${n["cScale"+e]};
    }
    .section-${e-1} text {
     fill: ${n["cScaleLabel"+e]};
    }
    .node-icon-${e-1} {
      font-size: 40px;
      color: ${n["cScaleLabel"+e]};
    }
    .section-edge-${e-1}{
      stroke: ${n["cScale"+e]};
    }
    .edge-depth-${e-1}{
      stroke-width: ${o};
    }
    .section-${e-1} line {
      stroke: ${n["cScaleInv"+e]} ;
      stroke-width: 3;
    }

    .lineWrapper line{
      stroke: ${n["cScaleLabel"+e]} ;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return t},"genSections"),Ut=s(n=>`
  .edge {
    stroke-width: 3;
  }
  ${qt(n)}
  .section-root rect, .section-root path, .section-root circle  {
    fill: ${n.git0};
  }
  .section-root text {
    fill: ${n.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .eventWrapper  {
   filter: brightness(120%);
  }
`,"getStyles"),It=Ut;var ye={db:D,renderer:Tt,parser:ht,styles:It};export{ye as diagram};
