<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f0f9ff;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#722ed1;stop-opacity:0.1" />
        </linearGradient>
    </defs>
    <rect width="400" height="300" fill="url(#bg-gradient)" stroke="#722ed1" stroke-width="2" rx="12"/>
    
    <!-- 背景装饰 -->
    <circle cx="350" cy="50" r="30" fill="#722ed1" opacity="0.1"/>
    <circle cx="50" cy="250" r="20" fill="#722ed1" opacity="0.15"/>
    
    <!-- 主图标区域 -->
    <g transform="translate(180, 80)">
        <circle cx="20" cy="20" r="35" fill="#722ed1" opacity="0.2"/>
        <g transform="translate(8, 8)" stroke-width="2">
            <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="#722ed1" stroke-width="2" fill="none"/>
                      <polygon points="10,8 16,12 10,16" fill="#722ed1"/>
        </g>
    </g>
    
    <!-- 标题文字 -->
    <text x="200" y="180" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#722ed1" font-weight="700">多模态面试 提升体验</text>
    
    <!-- 副标题 -->
    <text x="200" y="210" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#722ed1" opacity="0.8">iFlytek Spark AI 驱动</text>
    
    <!-- 装饰线条 -->
    <line x1="120" y1="230" x2="280" y2="230" stroke="#722ed1" stroke-width="2" opacity="0.3"/>
    
    <!-- 底部标识 -->
    <g transform="translate(170, 250)">
        <rect x="0" y="0" width="60" height="20" fill="#722ed1" opacity="0.1" rx="10"/>
        <text x="30" y="14" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="10" fill="#722ed1" font-weight="600">iFlytek</text>
    </g>
</svg>