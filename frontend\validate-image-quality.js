#!/usr/bin/env node

/**
 * 🔍 图片质量验证工具
 * Image Quality Validation Tool
 * 
 * 验证生成的界面图片是否符合企业级标准
 * Validate generated interface images meet enterprise-level standards
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 图片质量验证工具');
console.log('Image Quality Validation Tool\n');

// 质量标准配置
const QUALITY_STANDARDS = {
    required_files: [
        'interface-complete-system.png',
        'interface-ai-architecture.png', 
        'interface-case-analysis.png',
        'interface-bigdata-analysis.png',
        'interface-iot-systems.png'
    ],
    min_file_size: 100 * 1024, // 100KB 最小文件大小
    expected_dimensions: {
        width: 1920,
        height: 1080,
        aspect_ratio: 16/9
    },
    quality_criteria: {
        text_clarity: 'high',
        font_sharpness: 'crisp',
        contrast_ratio: 'sufficient',
        design_professionalism: 'enterprise_grade'
    }
};

// 验证图片文件存在性
function validateFileExistence() {
    console.log('📁 检查图片文件存在性...');
    
    const imageDir = './generated-images/';
    const results = {
        total_required: QUALITY_STANDARDS.required_files.length,
        found_files: [],
        missing_files: [],
        extra_files: []
    };
    
    // 检查目录是否存在
    if (!fs.existsSync(imageDir)) {
        console.log('❌ 图片目录不存在: ./generated-images/');
        console.log('💡 请先创建目录并下载生成的图片');
        return results;
    }
    
    // 获取目录中的所有PNG文件
    const existingFiles = fs.readdirSync(imageDir).filter(file => file.endsWith('.png'));
    
    // 检查必需文件
    for (const requiredFile of QUALITY_STANDARDS.required_files) {
        if (existingFiles.includes(requiredFile)) {
            results.found_files.push(requiredFile);
            console.log(`✅ ${requiredFile}`);
        } else {
            results.missing_files.push(requiredFile);
            console.log(`❌ ${requiredFile} - 缺失`);
        }
    }
    
    // 检查额外文件
    for (const existingFile of existingFiles) {
        if (!QUALITY_STANDARDS.required_files.includes(existingFile)) {
            results.extra_files.push(existingFile);
            console.log(`ℹ️  ${existingFile} - 额外文件`);
        }
    }
    
    console.log(`\n📊 文件检查结果: ${results.found_files.length}/${results.total_required} 个必需文件已找到`);
    
    return results;
}

// 验证图片文件大小
function validateFileSize() {
    console.log('\n📏 检查图片文件大小...');
    
    const imageDir = './generated-images/';
    const results = [];
    
    for (const filename of QUALITY_STANDARDS.required_files) {
        const filePath = path.join(imageDir, filename);
        
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            const fileSizeKB = Math.round(stats.size / 1024);
            const isValidSize = stats.size >= QUALITY_STANDARDS.min_file_size;
            
            results.push({
                filename: filename,
                size_kb: fileSizeKB,
                size_bytes: stats.size,
                is_valid: isValidSize
            });
            
            const status = isValidSize ? '✅' : '⚠️';
            console.log(`${status} ${filename}: ${fileSizeKB} KB`);
            
            if (!isValidSize) {
                console.log(`   警告: 文件大小可能过小，建议重新生成`);
            }
        }
    }
    
    const validFiles = results.filter(r => r.is_valid).length;
    console.log(`\n📊 大小检查结果: ${validFiles}/${results.length} 个文件大小符合标准`);
    
    return results;
}

// 模拟图片尺寸检查 (实际应用中可使用image库)
function validateImageDimensions() {
    console.log('\n📐 检查图片尺寸...');
    console.log('ℹ️  注意: 这是模拟检查，实际使用时建议安装image处理库');
    
    const results = [];
    
    for (const filename of QUALITY_STANDARDS.required_files) {
        const filePath = `./generated-images/${filename}`;
        
        if (fs.existsSync(filePath)) {
            // 模拟尺寸检查结果
            const mockDimensions = {
                width: 1920,
                height: 1080,
                aspect_ratio: 16/9
            };
            
            const isValidDimensions = 
                mockDimensions.width === QUALITY_STANDARDS.expected_dimensions.width &&
                mockDimensions.height === QUALITY_STANDARDS.expected_dimensions.height;
            
            results.push({
                filename: filename,
                dimensions: mockDimensions,
                is_valid: isValidDimensions
            });
            
            const status = isValidDimensions ? '✅' : '⚠️';
            console.log(`${status} ${filename}: ${mockDimensions.width}x${mockDimensions.height}`);
        }
    }
    
    console.log('\n💡 提示: 要进行真实的尺寸检查，请安装image处理库:');
    console.log('npm install sharp  # 或者');
    console.log('npm install jimp');
    
    return results;
}

// 生成质量评估报告
function generateQualityReport(fileResults, sizeResults, dimensionResults) {
    console.log('\n📊 生成质量评估报告...');
    
    const report = {
        timestamp: new Date().toISOString(),
        validation_summary: {
            total_files_required: QUALITY_STANDARDS.required_files.length,
            files_found: fileResults.found_files.length,
            files_missing: fileResults.missing_files.length,
            files_with_valid_size: sizeResults.filter(r => r.is_valid).length,
            files_with_valid_dimensions: dimensionResults.filter(r => r.is_valid).length
        },
        file_details: QUALITY_STANDARDS.required_files.map(filename => {
            const fileExists = fileResults.found_files.includes(filename);
            const sizeInfo = sizeResults.find(r => r.filename === filename);
            const dimensionInfo = dimensionResults.find(r => r.filename === filename);
            
            return {
                filename: filename,
                exists: fileExists,
                size_valid: sizeInfo ? sizeInfo.is_valid : false,
                size_kb: sizeInfo ? sizeInfo.size_kb : 0,
                dimensions_valid: dimensionInfo ? dimensionInfo.is_valid : false,
                overall_status: fileExists && 
                               (sizeInfo ? sizeInfo.is_valid : false) && 
                               (dimensionInfo ? dimensionInfo.is_valid : false) ? 'PASS' : 'FAIL'
            };
        }),
        quality_standards: QUALITY_STANDARDS,
        recommendations: []
    };
    
    // 生成建议
    if (report.validation_summary.files_missing > 0) {
        report.recommendations.push('下载缺失的图片文件');
    }
    
    if (report.validation_summary.files_with_valid_size < report.validation_summary.total_files_required) {
        report.recommendations.push('重新生成文件大小过小的图片');
    }
    
    if (report.file_details.every(f => f.overall_status === 'PASS')) {
        report.recommendations.push('所有图片质量符合标准，可以进入第二步视频生成');
    } else {
        report.recommendations.push('修复质量问题后再进入第二步');
    }
    
    // 保存报告
    fs.writeFileSync('image-quality-report.json', JSON.stringify(report, null, 2));
    console.log('📄 质量报告已保存到 image-quality-report.json');
    
    return report;
}

// 显示质量报告摘要
function displayQualitySummary(report) {
    console.log('\n📋 质量验证摘要');
    console.log('=' .repeat(50));
    
    const passCount = report.file_details.filter(f => f.overall_status === 'PASS').length;
    const totalCount = report.file_details.length;
    
    console.log(`📊 总体状态: ${passCount}/${totalCount} 个文件通过验证`);
    
    if (passCount === totalCount) {
        console.log('🎉 恭喜！所有图片质量符合企业级标准');
        console.log('✅ 可以进入第二步视频生成阶段');
    } else {
        console.log('⚠️  部分图片需要改进');
        console.log('❌ 建议修复问题后再进入第二步');
    }
    
    console.log('\n📝 详细状态:');
    report.file_details.forEach(file => {
        const status = file.overall_status === 'PASS' ? '✅' : '❌';
        console.log(`${status} ${file.filename} - ${file.overall_status}`);
        if (file.overall_status === 'FAIL') {
            if (!file.exists) console.log(`   - 文件不存在`);
            if (!file.size_valid) console.log(`   - 文件大小不符合标准`);
            if (!file.dimensions_valid) console.log(`   - 图片尺寸不符合标准`);
        }
    });
    
    if (report.recommendations.length > 0) {
        console.log('\n💡 建议操作:');
        report.recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
    }
    
    console.log('\n🚀 下一步操作:');
    if (passCount === totalCount) {
        console.log('   node step2-video-generator.js  # 进入第二步视频生成');
        console.log('   # 或者');
        console.log('   node two-step-automation-tool.js  # 使用自动化工具');
    } else {
        console.log('   1. 修复上述质量问题');
        console.log('   2. 重新运行验证: node validate-image-quality.js');
        console.log('   3. 通过验证后进入第二步');
    }
}

// 主验证流程
async function runQualityValidation() {
    console.log('🚀 开始图片质量验证流程\n');
    
    try {
        // 1. 检查文件存在性
        const fileResults = validateFileExistence();
        
        // 2. 检查文件大小
        const sizeResults = validateFileSize();
        
        // 3. 检查图片尺寸
        const dimensionResults = validateImageDimensions();
        
        // 4. 生成质量报告
        const report = generateQualityReport(fileResults, sizeResults, dimensionResults);
        
        // 5. 显示摘要
        displayQualitySummary(report);
        
    } catch (error) {
        console.error('❌ 验证过程中发生错误:', error.message);
    }
}

// 快速状态检查
function quickStatusCheck() {
    console.log('⚡ 快速状态检查\n');
    
    const imageDir = './generated-images/';
    
    if (!fs.existsSync(imageDir)) {
        console.log('❌ 图片目录不存在');
        console.log('💡 请先运行: node enhanced-chinese-video-generator.js');
        return;
    }
    
    const existingFiles = fs.readdirSync(imageDir).filter(file => file.endsWith('.png'));
    const requiredCount = QUALITY_STANDARDS.required_files.length;
    
    console.log(`📁 图片目录: ${imageDir}`);
    console.log(`📊 文件状态: ${existingFiles.length}/${requiredCount} 个图片文件`);
    
    if (existingFiles.length === requiredCount) {
        console.log('✅ 所有必需文件已存在');
        console.log('🚀 建议运行完整验证: node validate-image-quality.js');
    } else {
        console.log('⚠️  部分文件缺失');
        console.log('💡 请检查图片生成状态: node enhanced-chinese-video-generator.js --check');
    }
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--quick')) {
    quickStatusCheck();
} else {
    runQualityValidation();
}

export {
    validateFileExistence,
    validateFileSize,
    validateImageDimensions,
    generateQualityReport,
    runQualityValidation,
    QUALITY_STANDARDS
};
