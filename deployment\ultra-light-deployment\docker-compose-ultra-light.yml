version: '3.8'

services:
  # 后端服务 - 超轻量配置
  backend:
    build:
      context: ../../backend
      dockerfile: ../deployment/ultra-light-deployment/Dockerfile.backend-ultra-light
    container_name: iflytek-interview-backend-ultra
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/interview_system.db
      - IFLYTEK_APP_ID=${IFLYTEK_APP_ID}
      - IFLYTEK_API_KEY=${IFLYTEK_API_KEY}
      - IFLYTEK_API_SECRET=${IFLYTEK_API_SECRET}
      - IFLYTEK_SPARK_URL=${IFLYTEK_SPARK_URL}
      - ENVIRONMENT=production
      - WORKERS=1
    volumes:
      - backend_data:/app/data
      - backend_logs:/app/logs
    networks:
      - iflytek_network
    mem_limit: 400m
    cpus: 0.7
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 120s
      timeout: 30s
      retries: 2

  # 前端服务 - 超轻量配置
  frontend:
    build:
      context: ../../frontend
      dockerfile: ../deployment/ultra-light-deployment/Dockerfile.frontend-ultra-light
    container_name: iflytek-interview-frontend-ultra
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - iflytek_network
    mem_limit: 200m
    cpus: 0.3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 120s
      timeout: 30s
      retries: 2

# 数据卷
volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local

# 网络
networks:
  iflytek_network:
    driver: bridge
