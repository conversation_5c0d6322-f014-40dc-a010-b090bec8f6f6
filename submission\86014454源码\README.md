# 多模态智能模拟面试评测智能体

本项目是"中国软件杯"大学生软件设计大赛的参赛项目，旨在开发一个面向高校学生的多模态智能模拟面试评测智能体。

## 项目简介

本智能体通过模拟真实面试场景，利用人工智能和多模态分析技术，对学生的面试表现（语音、视频、文本）进行智能评测，并生成详细的反馈报告，帮助学生提升面试技巧，增强就业竞争力。

## 技术栈

- **前端**: Vue 3 + Vite + Element Plus
- **后端**: Python + FastAPI
- **AI模型**: 讯飞星火大模型及相关AI服务
- **数据库**: SQLite (开发阶段)

## 目录结构

```
.
├── backend/            # 后端 (FastAPI)
│   ├── app/
│   │   ├── api/        # API 路由
│   │   ├── core/       # 配置、核心功能
│   │   ├── services/   # AI 服务集成
│   │   ├── models/     # 数据模型
│   │   └── main.py     # 应用入口
│   └── requirements.txt
├── frontend/           # 前端 (Vue)
│   ├── public/
│   └── src/
│       ├── components/
│       ├── views/
│       ├── router/
│       ├── services/
│       └── main.js
├── docs/               # 文档
│   ├── requirement_specification.md
│   └── design_specification.md
└── README.md
```

## 🌟 系统特性

### 核心功能
- **多模态分析**：支持文本、语音、视频三种输入模式的综合分析
- **智能评估**：基于6个核心能力指标的精准评估算法
- **实时反馈**：即时分析结果和个性化建议
- **技术领域专业化**：针对AI、大数据、物联网等领域的专业评估
- **增强用户体验**：现代化UI设计，丰富的交互式演示功能
- **系统监控**：完善的健康检查、性能监控和错误处理机制

### 6个核心能力指标
1. **专业知识水平** (Professional Knowledge) - 25%权重
2. **技能匹配度** (Skill Matching) - 20%权重
3. **语言表达能力** (Language Expression) - 15%权重
4. **逻辑思维能力** (Logical Thinking) - 15%权重
5. **创新能力** (Innovation Ability) - 15%权重
6. **应变抗压能力** (Stress Resistance) - 10%权重

## 🚀 快速开始

### 环境要求
- Node.js 16+
- Python 3.8+
- iFlytek开发者账号（获取API密钥）

### 🎯 **比赛评审一键启动** (推荐)

**最简单的启动方式 - 适合比赛评审：**

```bash
# 进入项目目录
cd cursor_softcup

# 一键启动系统（自动安装依赖并启动前后端）
python start_system.py
```

启动后访问：
- **前端界面**：http://localhost:5173
- **后端API**：http://localhost:8000
- **API文档**：http://localhost:8000/docs

### 📋 **手动启动方式** (可选)

如果需要手动控制启动过程：

#### 1. 后端启动
```bash
cd backend
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 2. 前端启动 (新终端窗口)
```bash
cd frontend
npm install
npm run dev
```

### 🔧 环境配置 (可选)

系统已包含默认配置，如需自定义可创建 `backend/软件杯.env` 文件：
```env
# iFlytek配置 (可选，系统有默认配置)
IFLYTEK_APP_ID=your_app_id
IFLYTEK_API_KEY=your_api_key
IFLYTEK_API_SECRET=your_api_secret

# 数据库配置
DATABASE_URL=sqlite:///./interview_system.db

# 系统配置
DEBUG=False
LOG_LEVEL=INFO
```

## 📖 使用指南

### 基本使用流程

1. **创建面试会话**
   - 选择技术领域（AI/大数据/物联网）
   - 设置职位和难度级别
   - 输入候选人基本信息

2. **进行面试评估**
   - 支持文本输入回答（增强版编辑器，模板插入）
   - 支持语音录制回答（实时质量监控）
   - 支持视频录制回答（表情和姿态分析）
   - 实时获取分析反馈和智能建议

3. **查看评估报告**
   - 6个核心能力指标详细分析
   - 多模态数据融合结果
   - 个性化改进建议
   - 学习路径推荐

### 演示功能

系统提供丰富的演示功能：

- **功能演示**：核心功能的详细展示
- **视频教程**：使用方法和最佳实践
- **交互体验**：实时AI对话演示、场景筛选、实时演示控制台
- **技术架构**：系统架构图、技术栈详情、性能监控面板

## 🧪 测试

### 运行后端测试
```bash
cd backend
pytest tests/ -v
```

### 运行前端测试
```bash
cd frontend
npm run test
```

### 系统集成测试
```bash
cd backend
python -m pytest tests/test_system_integration.py -v
```

## 📊 监控和维护

### 健康检查
- 简单检查：`GET /health`
- 详细检查：`GET /api/v1/health`
- 系统统计：`GET /api/v1/system/stats`
- 性能指标：`GET /api/v1/system/performance`

### 日志管理
日志文件位置：`logs/app.log`

日志级别配置：
```env
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
```

## 🔒 安全考虑

- API密钥安全存储
- 用户数据加密
- 输入数据验证
- CORS配置
- 访问日志记录

## 📄 许可证

本项目采用MIT许可证

---

**注意**：本系统需要有效的iFlytek API密钥才能正常运行。请确保在使用前正确配置相关参数。