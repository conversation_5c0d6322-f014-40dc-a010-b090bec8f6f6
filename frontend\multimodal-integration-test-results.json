{"componentIntegration": {"MultimodalAIShowcase": {"fileExists": true, "vueStructure": {"template": true, "script": true, "style": true}}, "EnterpriseDashboard": {"fileExists": true, "vueStructure": {"template": true, "script": true, "style": true}}, "CandidatePortal": {"fileExists": true, "vueStructure": {"template": true, "script": true, "style": true}}}, "featureImplementation": {"multimodalShowcase": {}, "enterpriseDashboard": {}, "candidatePortal": {}}, "brandConsistency": {"MultimodalAIShowcase": {"--iflytek-primary": true, "--iflytek-gradient-primary": true, "--iflytek-gradient-secondary": true, "--iflytek-gradient-accent": true}, "EnterpriseDashboard": {"--iflytek-primary": true, "--iflytek-gradient-primary": true, "--iflytek-gradient-secondary": true, "--iflytek-gradient-accent": true}, "CandidatePortal": {"--iflytek-primary": true, "--iflytek-gradient-primary": true, "--iflytek-gradient-secondary": true, "--iflytek-gradient-accent": true}}, "chineseLocalization": {"MultimodalAIShowcase": {"语音识别": true, "面试助手": false, "智能评估": true}, "EnterpriseDashboard": {"语音识别": true, "面试助手": false, "智能评估": true}, "CandidatePortal": {"语音识别": true, "面试助手": true, "智能评估": false}}, "performance": {"MultimodalAIShowcase": {"animations": true, "vForCount": 5, "timerCount": 8}, "EnterpriseDashboard": {"animations": true, "vForCount": 6, "timerCount": 3}, "CandidatePortal": {"animations": true, "vForCount": 8, "timerCount": 4}}, "overall": {"passed": 32, "failed": 3, "warnings": 7}}