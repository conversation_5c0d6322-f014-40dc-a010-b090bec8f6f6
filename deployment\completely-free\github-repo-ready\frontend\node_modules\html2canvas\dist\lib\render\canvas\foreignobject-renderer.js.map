{"version": 3, "file": "foreignobject-renderer.js", "sourceRoot": "", "sources": ["../../../../src/render/canvas/foreignobject-renderer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,gDAA2D;AAC3D,+CAA+C;AAC/C,wCAAqC;AAGrC;IAA2C,yCAAQ;IAK/C,+BAAY,OAAgB,EAAE,OAA6B;QAA3D,YACI,kBAAM,OAAO,EAAE,OAAO,CAAC,SAc1B;QAbG,KAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjF,KAAI,CAAC,GAAG,GAAG,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAA6B,CAAC;QACpE,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,KAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9D,KAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAChE,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAM,OAAO,CAAC,KAAK,OAAI,CAAC;QAC/C,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAM,OAAO,CAAC,MAAM,OAAI,CAAC;QAEjD,KAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CACrB,sDAAoD,OAAO,CAAC,KAAK,SAAI,OAAO,CAAC,MAAM,YAAO,OAAO,CAAC,CAAC,SAAI,OAAO,CAAC,CAAC,qBAAgB,OAAO,CAAC,KAAO,CAClJ,CAAC;;IACN,CAAC;IAEK,sCAAM,GAAZ,UAAa,OAAoB;;;;;;wBACvB,GAAG,GAAG,iCAAsB,CAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EACvC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EACxC,IAAI,CAAC,OAAO,CAAC,KAAK,EAClB,IAAI,CAAC,OAAO,CAAC,KAAK,EAClB,OAAO,CACV,CAAC;wBAEU,qBAAM,yBAAiB,CAAC,GAAG,CAAC,EAAA;;wBAAlC,GAAG,GAAG,SAA4B;wBAExC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;4BAC9B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,gBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;4BAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;yBAC9G;wBAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAEpG,sBAAO,IAAI,CAAC,MAAM,EAAC;;;;KACtB;IACL,4BAAC;AAAD,CAAC,AA1CD,CAA2C,mBAAQ,GA0ClD;AA1CY,sDAAqB;AA4C3B,IAAM,iBAAiB,GAAG,UAAC,GAAS;IACvC,OAAA,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QACxB,IAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;QACxB,GAAG,CAAC,MAAM,GAAG;YACT,OAAO,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC;QACF,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;QAErB,GAAG,CAAC,GAAG,GAAG,sCAAoC,kBAAkB,CAAC,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAG,CAAC;IACnH,CAAC,CAAC;AARF,CAQE,CAAC;AATM,QAAA,iBAAiB,qBASvB"}