/******************************************************************************
 * Copyright 2022 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/
import type { ParserRule } from '../../../languages/generated/ast.js';
import type { PlainAstTypes } from './plain-types.js';
export declare function collectInferredTypes(parserRules: ParserRule[], datatypeRules: ParserRule[], declared: PlainAstTypes): PlainAstTypes;
//# sourceMappingURL=inferred-types.d.ts.map