{"version": 3, "sources": ["../../../src/diagrams/packet/db.ts", "../../../src/diagrams/packet/parser.ts", "../../../src/diagrams/packet/renderer.ts", "../../../src/diagrams/packet/styles.ts", "../../../src/diagrams/packet/diagram.ts"], "sourcesContent": ["import { getConfig as commonGetConfig } from '../../config.js';\nimport type { PacketDiagramConfig } from '../../config.type.js';\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport type { PacketDB, PacketData, PacketWord } from './types.js';\n\nconst defaultPacketData: PacketData = {\n  packet: [],\n};\n\nlet data: PacketData = structuredClone(defaultPacketData);\n\nconst DEFAULT_PACKET_CONFIG: Required<PacketDiagramConfig> = DEFAULT_CONFIG.packet;\n\nconst getConfig = (): Required<PacketDiagramConfig> => {\n  const config = cleanAndMerge({\n    ...DEFAULT_PACKET_CONFIG,\n    ...commonGetConfig().packet,\n  });\n  if (config.showBits) {\n    config.paddingY += 10;\n  }\n  return config;\n};\n\nconst getPacket = (): PacketWord[] => data.packet;\n\nconst pushWord = (word: PacketWord) => {\n  if (word.length > 0) {\n    data.packet.push(word);\n  }\n};\n\nconst clear = () => {\n  commonClear();\n  data = structuredClone(defaultPacketData);\n};\n\nexport const db: PacketDB = {\n  pushWord,\n  getPacket,\n  getConfig,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n};\n", "import type { Packet } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport { db } from './db.js';\nimport type { PacketBlock, PacketWord } from './types.js';\n\nconst maxPacketSize = 10_000;\n\nconst populate = (ast: Packet) => {\n  populateCommonDb(ast, db);\n  let lastBit = -1;\n  let word: PacketWord = [];\n  let row = 1;\n  const { bitsPerRow } = db.getConfig();\n\n  for (let { start, end, bits, label } of ast.blocks) {\n    if (start !== undefined && end !== undefined && end < start) {\n      throw new Error(`Packet block ${start} - ${end} is invalid. End must be greater than start.`);\n    }\n    start ??= lastBit + 1;\n    if (start !== lastBit + 1) {\n      throw new Error(\n        `Packet block ${start} - ${end ?? start} is not contiguous. It should start from ${\n          lastBit + 1\n        }.`\n      );\n    }\n    if (bits === 0) {\n      throw new Error(`Packet block ${start} is invalid. Cannot have a zero bit field.`);\n    }\n    end ??= start + (bits ?? 1) - 1;\n    bits ??= end - start + 1;\n    lastBit = end;\n    log.debug(`Packet block ${start} - ${lastBit} with label ${label}`);\n\n    while (word.length <= bitsPerRow + 1 && db.getPacket().length < maxPacketSize) {\n      const [block, nextBlock] = getNextFittingBlock({ start, end, bits, label }, row, bitsPerRow);\n      word.push(block);\n      if (block.end + 1 === row * bitsPerRow) {\n        db.pushWord(word);\n        word = [];\n        row++;\n      }\n      if (!nextBlock) {\n        break;\n      }\n      ({ start, end, bits, label } = nextBlock);\n    }\n  }\n  db.pushWord(word);\n};\n\nconst getNextFittingBlock = (\n  block: PacketBlock,\n  row: number,\n  bitsPerRow: number\n): [Required<PacketBlock>, PacketBlock | undefined] => {\n  if (block.start === undefined) {\n    throw new Error('start should have been set during first phase');\n  }\n  if (block.end === undefined) {\n    throw new Error('end should have been set during first phase');\n  }\n\n  if (block.start > block.end) {\n    throw new Error(`Block start ${block.start} is greater than block end ${block.end}.`);\n  }\n\n  if (block.end + 1 <= row * bitsPerRow) {\n    return [block as Required<PacketBlock>, undefined];\n  }\n\n  const rowEnd = row * bitsPerRow - 1;\n  const rowStart = row * bitsPerRow;\n  return [\n    {\n      start: block.start,\n      end: rowEnd,\n      label: block.label,\n      bits: rowEnd - block.start,\n    },\n    {\n      start: rowStart,\n      end: block.end,\n      label: block.label,\n      bits: block.end - rowStart,\n    },\n  ];\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: Packet = await parse('packet', input);\n    log.debug(ast);\n    populate(ast);\n  },\n};\n", "import type { Diagram } from '../../Diagram.js';\nimport type { PacketDiagramConfig } from '../../config.type.js';\nimport type { DiagramRenderer, DrawDefinition, SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { PacketDB, PacketWord } from './types.js';\n\nconst draw: DrawDefinition = (_text, id, _version, diagram: Diagram) => {\n  const db = diagram.db as PacketDB;\n  const config = db.getConfig();\n  const { rowHeight, paddingY, bitWidth, bitsPerRow } = config;\n  const words = db.getPacket();\n  const title = db.getDiagramTitle();\n  const totalRowHeight = rowHeight + paddingY;\n  const svgHeight = totalRowHeight * (words.length + 1) - (title ? 0 : rowHeight);\n  const svgWidth = bitWidth * bitsPerRow + 2;\n  const svg: SVG = selectSvgElement(id);\n\n  svg.attr('viewbox', `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n\n  for (const [word, packet] of words.entries()) {\n    drawWord(svg, packet, word, config);\n  }\n\n  svg\n    .append('text')\n    .text(title)\n    .attr('x', svgWidth / 2)\n    .attr('y', svgHeight - totalRowHeight / 2)\n    .attr('dominant-baseline', 'middle')\n    .attr('text-anchor', 'middle')\n    .attr('class', 'packetTitle');\n};\n\nconst drawWord = (\n  svg: SVG,\n  word: PacketWord,\n  rowNumber: number,\n  { rowHeight, paddingX, paddingY, bitWidth, bitsPerRow, showBits }: Required<PacketDiagramConfig>\n) => {\n  const group: SVGGroup = svg.append('g');\n  const wordY = rowNumber * (rowHeight + paddingY) + paddingY;\n  for (const block of word) {\n    const blockX = (block.start % bitsPerRow) * bitWidth + 1;\n    const width = (block.end - block.start + 1) * bitWidth - paddingX;\n    // Block rectangle\n    group\n      .append('rect')\n      .attr('x', blockX)\n      .attr('y', wordY)\n      .attr('width', width)\n      .attr('height', rowHeight)\n      .attr('class', 'packetBlock');\n\n    // Block label\n    group\n      .append('text')\n      .attr('x', blockX + width / 2)\n      .attr('y', wordY + rowHeight / 2)\n      .attr('class', 'packetLabel')\n      .attr('dominant-baseline', 'middle')\n      .attr('text-anchor', 'middle')\n      .text(block.label);\n\n    if (!showBits) {\n      continue;\n    }\n    // Start byte count\n    const isSingleBlock = block.end === block.start;\n    const bitNumberY = wordY - 2;\n    group\n      .append('text')\n      .attr('x', blockX + (isSingleBlock ? width / 2 : 0))\n      .attr('y', bitNumberY)\n      .attr('class', 'packetByte start')\n      .attr('dominant-baseline', 'auto')\n      .attr('text-anchor', isSingleBlock ? 'middle' : 'start')\n      .text(block.start);\n\n    // Draw end byte count if it is not the same as start byte count\n    if (!isSingleBlock) {\n      group\n        .append('text')\n        .attr('x', blockX + width)\n        .attr('y', bitNumberY)\n        .attr('class', 'packetByte end')\n        .attr('dominant-baseline', 'auto')\n        .attr('text-anchor', 'end')\n        .text(block.end);\n    }\n  }\n};\nexport const renderer: DiagramRenderer = { draw };\n", "import type { DiagramStylesProvider } from '../../diagram-api/types.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport type { PacketStyleOptions } from './types.js';\n\nconst defaultPacketStyleOptions: PacketStyleOptions = {\n  byteFontSize: '10px',\n  startByteColor: 'black',\n  endByteColor: 'black',\n  labelColor: 'black',\n  labelFontSize: '12px',\n  titleColor: 'black',\n  titleFontSize: '14px',\n  blockStrokeColor: 'black',\n  blockStrokeWidth: '1',\n  blockFillColor: '#efefef',\n};\n\nexport const styles: DiagramStylesProvider = ({ packet }: { packet?: PacketStyleOptions } = {}) => {\n  const options = cleanAndMerge(defaultPacketStyleOptions, packet);\n\n  return `\n\t.packetByte {\n\t\tfont-size: ${options.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${options.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${options.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${options.labelColor};\n\t\tfont-size: ${options.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${options.titleColor};\n\t\tfont-size: ${options.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${options.blockStrokeColor};\n\t\tstroke-width: ${options.blockStrokeWidth};\n\t\tfill: ${options.blockFillColor};\n\t}\n\t`;\n};\n\nexport default styles;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { db } from './db.js';\nimport { parser } from './parser.js';\nimport { renderer } from './renderer.js';\nimport { styles } from './styles.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,oBAAgC;AAAA,EACpC,QAAQ,CAAC;AACX;AAEA,IAAI,OAAmB,gBAAgB,iBAAiB;AAExD,IAAM,wBAAuD,sBAAe;AAE5E,IAAMA,aAAY,6BAAqC;AACrD,QAAM,SAAS,cAAc;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,UAAgB,EAAE;AAAA,EACvB,CAAC;AACD,MAAI,OAAO,UAAU;AACnB,WAAO,YAAY;AAAA,EACrB;AACA,SAAO;AACT,GATkB;AAWlB,IAAM,YAAY,6BAAoB,KAAK,QAAzB;AAElB,IAAM,WAAW,wBAAC,SAAqB;AACrC,MAAI,KAAK,SAAS,GAAG;AACnB,SAAK,OAAO,KAAK,IAAI;AAAA,EACvB;AACF,GAJiB;AAMjB,IAAMC,SAAQ,6BAAM;AAClB,QAAY;AACZ,SAAO,gBAAgB,iBAAiB;AAC1C,GAHc;AAKP,IAAM,KAAe;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,WAAAD;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACzDA,SAAS,aAAa;AAOtB,IAAM,gBAAgB;AAEtB,IAAM,WAAW,wBAAC,QAAgB;AAChC,mBAAiB,KAAK,EAAE;AACxB,MAAI,UAAU;AACd,MAAI,OAAmB,CAAC;AACxB,MAAI,MAAM;AACV,QAAM,EAAE,WAAW,IAAI,GAAG,UAAU;AAEpC,WAAS,EAAE,OAAO,KAAK,MAAM,MAAM,KAAK,IAAI,QAAQ;AAClD,QAAI,UAAU,UAAa,QAAQ,UAAa,MAAM,OAAO;AAC3D,YAAM,IAAI,MAAM,gBAAgB,KAAK,MAAM,GAAG,8CAA8C;AAAA,IAC9F;AACA,cAAU,UAAU;AACpB,QAAI,UAAU,UAAU,GAAG;AACzB,YAAM,IAAI;AAAA,QACR,gBAAgB,KAAK,MAAM,OAAO,KAAK,4CACrC,UAAU,CACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,GAAG;AACd,YAAM,IAAI,MAAM,gBAAgB,KAAK,4CAA4C;AAAA,IACnF;AACA,YAAQ,SAAS,QAAQ,KAAK;AAC9B,aAAS,MAAM,QAAQ;AACvB,cAAU;AACV,QAAI,MAAM,gBAAgB,KAAK,MAAM,OAAO,eAAe,KAAK,EAAE;AAElE,WAAO,KAAK,UAAU,aAAa,KAAK,GAAG,UAAU,EAAE,SAAS,eAAe;AAC7E,YAAM,CAAC,OAAO,SAAS,IAAI,oBAAoB,EAAE,OAAO,KAAK,MAAM,MAAM,GAAG,KAAK,UAAU;AAC3F,WAAK,KAAK,KAAK;AACf,UAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AACtC,WAAG,SAAS,IAAI;AAChB,eAAO,CAAC;AACR;AAAA,MACF;AACA,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,OAAC,EAAE,OAAO,KAAK,MAAM,MAAM,IAAI;AAAA,IACjC;AAAA,EACF;AACA,KAAG,SAAS,IAAI;AAClB,GA1CiB;AA4CjB,IAAM,sBAAsB,wBAC1B,OACA,KACA,eACqD;AACrD,MAAI,MAAM,UAAU,QAAW;AAC7B,UAAM,IAAI,MAAM,+CAA+C;AAAA,EACjE;AACA,MAAI,MAAM,QAAQ,QAAW;AAC3B,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC/D;AAEA,MAAI,MAAM,QAAQ,MAAM,KAAK;AAC3B,UAAM,IAAI,MAAM,eAAe,MAAM,KAAK,8BAA8B,MAAM,GAAG,GAAG;AAAA,EACtF;AAEA,MAAI,MAAM,MAAM,KAAK,MAAM,YAAY;AACrC,WAAO,CAAC,OAAgC,MAAS;AAAA,EACnD;AAEA,QAAM,SAAS,MAAM,aAAa;AAClC,QAAM,WAAW,MAAM;AACvB,SAAO;AAAA,IACL;AAAA,MACE,OAAO,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,MAAM;AAAA,MACb,MAAM,SAAS,MAAM;AAAA,IACvB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,KAAK,MAAM;AAAA,MACX,OAAO,MAAM;AAAA,MACb,MAAM,MAAM,MAAM;AAAA,IACpB;AAAA,EACF;AACF,GApC4B;AAsCrB,IAAM,SAA2B;AAAA,EACtC,OAAO,8BAAO,UAAiC;AAC7C,UAAM,MAAc,MAAM,MAAM,UAAU,KAAK;AAC/C,QAAI,MAAM,GAAG;AACb,aAAS,GAAG;AAAA,EACd,GAJO;AAKT;;;AC3FA,IAAM,OAAuB,wBAAC,OAAO,IAAI,UAAUC,aAAqB;AACtE,QAAMC,MAAKD,SAAQ;AACnB,QAAM,SAASC,IAAG,UAAU;AAC5B,QAAM,EAAE,WAAW,UAAU,UAAU,WAAW,IAAI;AACtD,QAAM,QAAQA,IAAG,UAAU;AAC3B,QAAM,QAAQA,IAAG,gBAAgB;AACjC,QAAM,iBAAiB,YAAY;AACnC,QAAM,YAAY,kBAAkB,MAAM,SAAS,MAAM,QAAQ,IAAI;AACrE,QAAM,WAAW,WAAW,aAAa;AACzC,QAAM,MAAW,iBAAiB,EAAE;AAEpC,MAAI,KAAK,WAAW,OAAO,QAAQ,IAAI,SAAS,EAAE;AAClD,mBAAiB,KAAK,WAAW,UAAU,OAAO,WAAW;AAE7D,aAAW,CAAC,MAAM,MAAM,KAAK,MAAM,QAAQ,GAAG;AAC5C,aAAS,KAAK,QAAQ,MAAM,MAAM;AAAA,EACpC;AAEA,MACG,OAAO,MAAM,EACb,KAAK,KAAK,EACV,KAAK,KAAK,WAAW,CAAC,EACtB,KAAK,KAAK,YAAY,iBAAiB,CAAC,EACxC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ,EAC5B,KAAK,SAAS,aAAa;AAChC,GA1B6B;AA4B7B,IAAM,WAAW,wBACf,KACA,MACA,WACA,EAAE,WAAW,UAAU,UAAU,UAAU,YAAY,SAAS,MAC7D;AACH,QAAM,QAAkB,IAAI,OAAO,GAAG;AACtC,QAAM,QAAQ,aAAa,YAAY,YAAY;AACnD,aAAW,SAAS,MAAM;AACxB,UAAM,SAAU,MAAM,QAAQ,aAAc,WAAW;AACvD,UAAM,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK,WAAW;AAEzD,UACG,OAAO,MAAM,EACb,KAAK,KAAK,MAAM,EAChB,KAAK,KAAK,KAAK,EACf,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,SAAS,EACxB,KAAK,SAAS,aAAa;AAG9B,UACG,OAAO,MAAM,EACb,KAAK,KAAK,SAAS,QAAQ,CAAC,EAC5B,KAAK,KAAK,QAAQ,YAAY,CAAC,EAC/B,KAAK,SAAS,aAAa,EAC3B,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ,EAC5B,KAAK,MAAM,KAAK;AAEnB,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AAEA,UAAM,gBAAgB,MAAM,QAAQ,MAAM;AAC1C,UAAM,aAAa,QAAQ;AAC3B,UACG,OAAO,MAAM,EACb,KAAK,KAAK,UAAU,gBAAgB,QAAQ,IAAI,EAAE,EAClD,KAAK,KAAK,UAAU,EACpB,KAAK,SAAS,kBAAkB,EAChC,KAAK,qBAAqB,MAAM,EAChC,KAAK,eAAe,gBAAgB,WAAW,OAAO,EACtD,KAAK,MAAM,KAAK;AAGnB,QAAI,CAAC,eAAe;AAClB,YACG,OAAO,MAAM,EACb,KAAK,KAAK,SAAS,KAAK,EACxB,KAAK,KAAK,UAAU,EACpB,KAAK,SAAS,gBAAgB,EAC9B,KAAK,qBAAqB,MAAM,EAChC,KAAK,eAAe,KAAK,EACzB,KAAK,MAAM,GAAG;AAAA,IACnB;AAAA,EACF;AACF,GAzDiB;AA0DV,IAAM,WAA4B,EAAE,KAAK;;;ACzFhD,IAAM,4BAAgD;AAAA,EACpD,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,gBAAgB;AAClB;AAEO,IAAM,SAAgC,wBAAC,EAAE,OAAO,IAAqC,CAAC,MAAM;AACjG,QAAM,UAAU,cAAc,2BAA2B,MAAM;AAE/D,SAAO;AAAA;AAAA,eAEM,QAAQ,YAAY;AAAA;AAAA;AAAA,UAGzB,QAAQ,cAAc;AAAA;AAAA;AAAA,UAGtB,QAAQ,YAAY;AAAA;AAAA;AAAA,UAGpB,QAAQ,UAAU;AAAA,eACb,QAAQ,aAAa;AAAA;AAAA;AAAA,UAG1B,QAAQ,UAAU;AAAA,eACb,QAAQ,aAAa;AAAA;AAAA;AAAA,YAGxB,QAAQ,gBAAgB;AAAA,kBAClB,QAAQ,gBAAgB;AAAA,UAChC,QAAQ,cAAc;AAAA;AAAA;AAGhC,GA3B6C;;;ACXtC,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["getConfig", "clear", "diagram", "db"]}