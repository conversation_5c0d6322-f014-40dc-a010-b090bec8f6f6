/**
 * 分支图WCAG 2.1 AA对比度验证工具
 * 验证iFlytek品牌色彩是否符合无障碍标准
 */

// WCAG 2.1 AA标准要求对比度≥4.5:1
const WCAG_AA_RATIO = 4.5;

// iFlytek品牌色彩配置
const IFLYTEK_COLORS = {
  primary: '#1890ff',      // iFlytek主蓝色
  secondary: '#667eea',    // iFlytek辅助紫色
  tertiary: '#764ba2',     // iFlytek深紫色
  white: '#ffffff',        // 白色文字
  black: '#000000',        // 黑色文字
  gray: '#333333',         // 深灰色文字
  lightGray: '#f8f9fa',    // 浅灰色背景
  success: '#67C23A',      // 成功色
  warning: '#E6A23C',      // 警告色
  danger: '#F56C6C'        // 危险色
};

/**
 * 将十六进制颜色转换为RGB
 */
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * 计算相对亮度
 */
function getRelativeLuminance(rgb) {
  const { r, g, b } = rgb;
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * 计算对比度
 */
function getContrastRatio(color1, color2) {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const l1 = getRelativeLuminance(rgb1);
  const l2 = getRelativeLuminance(rgb2);
  
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * 验证颜色组合是否符合WCAG 2.1 AA标准
 */
function validateColorCombination(backgroundColor, textColor, description) {
  const ratio = getContrastRatio(backgroundColor, textColor);
  const isValid = ratio >= WCAG_AA_RATIO;
  
  return {
    backgroundColor,
    textColor,
    description,
    ratio: ratio.toFixed(2),
    isValid,
    status: isValid ? '✅ 通过' : '❌ 不通过',
    recommendation: isValid ? '符合WCAG 2.1 AA标准' : `对比度不足，需要≥${WCAG_AA_RATIO}:1`
  };
}

/**
 * 验证分支图颜色配置
 */
function validateBranchDiagramColors() {
  console.log('🎨 分支图WCAG 2.1 AA对比度验证');
  console.log('='.repeat(60));
  
  const validations = [
    // 主要品牌色组合
    validateColorCombination(IFLYTEK_COLORS.primary, IFLYTEK_COLORS.white, 'iFlytek主蓝色 + 白色文字'),
    validateColorCombination(IFLYTEK_COLORS.secondary, IFLYTEK_COLORS.white, 'iFlytek辅助紫色 + 白色文字'),
    validateColorCombination(IFLYTEK_COLORS.tertiary, IFLYTEK_COLORS.white, 'iFlytek深紫色 + 白色文字'),
    
    // 节点样式组合
    validateColorCombination(IFLYTEK_COLORS.lightGray, IFLYTEK_COLORS.gray, '浅灰色背景 + 深灰色文字'),
    validateColorCombination(IFLYTEK_COLORS.white, IFLYTEK_COLORS.gray, '白色背景 + 深灰色文字'),
    
    // 状态色组合
    validateColorCombination(IFLYTEK_COLORS.success, IFLYTEK_COLORS.white, '成功色 + 白色文字'),
    validateColorCombination(IFLYTEK_COLORS.warning, IFLYTEK_COLORS.white, '警告色 + 白色文字'),
    validateColorCombination(IFLYTEK_COLORS.danger, IFLYTEK_COLORS.white, '危险色 + 白色文字'),
  ];
  
  let passCount = 0;
  let totalCount = validations.length;
  
  validations.forEach((validation, index) => {
    console.log(`${index + 1}. ${validation.description}`);
    console.log(`   背景色: ${validation.backgroundColor}`);
    console.log(`   文字色: ${validation.textColor}`);
    console.log(`   对比度: ${validation.ratio}:1`);
    console.log(`   状态: ${validation.status}`);
    console.log(`   建议: ${validation.recommendation}`);
    console.log('');
    
    if (validation.isValid) passCount++;
  });
  
  console.log('📊 验证结果汇总');
  console.log('-'.repeat(40));
  console.log(`✅ 通过: ${passCount}/${totalCount} (${(passCount/totalCount*100).toFixed(1)}%)`);
  console.log(`❌ 不通过: ${totalCount - passCount}/${totalCount}`);
  console.log(`🎯 WCAG 2.1 AA合规率: ${passCount === totalCount ? '100% 完全合规' : '需要优化'}`);
  
  return {
    validations,
    passCount,
    totalCount,
    isFullyCompliant: passCount === totalCount
  };
}

/**
 * 生成CSS变量配置
 */
function generateCSSVariables() {
  console.log('\n🎨 推荐的CSS变量配置');
  console.log('='.repeat(60));
  
  const cssVars = `
:root {
  /* iFlytek品牌色彩 - WCAG 2.1 AA合规 */
  --iflytek-primary: ${IFLYTEK_COLORS.primary};
  --iflytek-secondary: ${IFLYTEK_COLORS.secondary};
  --iflytek-tertiary: ${IFLYTEK_COLORS.tertiary};
  
  /* 文字颜色 - 高对比度 */
  --text-on-primary: ${IFLYTEK_COLORS.white};
  --text-on-secondary: ${IFLYTEK_COLORS.white};
  --text-on-tertiary: ${IFLYTEK_COLORS.white};
  --text-on-light: ${IFLYTEK_COLORS.gray};
  
  /* 背景颜色 */
  --bg-light: ${IFLYTEK_COLORS.lightGray};
  --bg-white: ${IFLYTEK_COLORS.white};
  
  /* 状态颜色 */
  --color-success: ${IFLYTEK_COLORS.success};
  --color-warning: ${IFLYTEK_COLORS.warning};
  --color-danger: ${IFLYTEK_COLORS.danger};
}

/* 分支图专用样式类 */
.branch-diagram-primary {
  background-color: var(--iflytek-primary);
  color: var(--text-on-primary);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.branch-diagram-secondary {
  background-color: var(--iflytek-secondary);
  color: var(--text-on-secondary);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.branch-diagram-tertiary {
  background-color: var(--iflytek-tertiary);
  color: var(--text-on-tertiary);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}
`;
  
  console.log(cssVars);
  return cssVars;
}

// 执行验证
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.validateBranchDiagramColors = validateBranchDiagramColors;
  window.generateCSSVariables = generateCSSVariables;
  window.IFLYTEK_COLORS = IFLYTEK_COLORS;
}

// 自动执行验证
console.log('🚀 启动分支图WCAG验证...');
const result = validateBranchDiagramColors();
generateCSSVariables();

console.log('\n💡 使用建议:');
console.log('1. 在浏览器控制台运行: validateBranchDiagramColors()');
console.log('2. 获取CSS配置: generateCSSVariables()');
console.log('3. 检查特定颜色组合: validateColorCombination(bgColor, textColor, description)');
