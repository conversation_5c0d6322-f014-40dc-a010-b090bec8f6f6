{"version": 3, "file": "gast_recorder.js", "sourceRoot": "", "sources": ["../../../../../src/parse/parser/traits/gast_recorder.ts"], "names": [], "mappings": "AAgBA,OAAO,EACL,OAAO,EACP,GAAG,EACH,OAAO,EACP,UAAU,EACV,IAAI,IAAI,IAAI,EACZ,IAAI,GACL,MAAM,WAAW,CAAC;AAEnB,OAAO,EACL,WAAW,EACX,WAAW,EACX,WAAW,EACX,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,gCAAgC,EAChC,uBAAuB,EACvB,IAAI,EACJ,QAAQ,GACT,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,KAAK,EAAE,MAAM,+BAA+B,CAAC;AACtD,OAAO,EACL,iBAAiB,EACjB,mBAAmB,GACpB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,WAAW,EACX,mBAAmB,GACpB,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAIhE,MAAM,qBAAqB,GAAG;IAC5B,WAAW,EAAE,4DAA4D;CAC1E,CAAC;AACF,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAErC,MAAM,gBAAgB,GAAG,IAAI,CAAC;AAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC,GAAG,CAAC,CAAC;AAEhE,MAAM,GAAG,GAAG,WAAW,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9E,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzB,MAAM,qBAAqB,GAAG,mBAAmB,CAC/C,GAAG,EACH,4DAA4D;IAC1D,EAAE;IACF,oFAAoF;AACtF,mFAAmF;AACnF,sGAAsG;AACtG,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,CACH,CAAC;AACF,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAErC,MAAM,uBAAuB,GAAY;IACvC,IAAI,EACF,6DAA6D;QAC7D,oFAAoF;IACtF,QAAQ,EAAE,EAAE;CACb,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,YAAY;IAIvB,gBAAgB,CAAsB,MAAqB;QACzD,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED,eAAe;QACb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,GAAG,EAAE;YACvC;;;;;;;;eAQG;YACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC3B,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,UAAU,GAAG,EAAe,CAAC,GAAG,UAAU,IAAI,EAAE,IAAI;oBACvD,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;gBACnD,CAAC,CAAC;gBACF,IAAI,CAAC,UAAU,GAAG,EAAe,CAAC,GAAG,UAAU,IAAI,EAAE,IAAI;oBACvD,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAQ,CAAC;gBAC1D,CAAC,CAAC;gBACF,IAAI,CAAC,SAAS,GAAG,EAAc,CAAC,GAAG,UAAU,IAAI;oBAC/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC;gBACF,IAAI,CAAC,KAAK,GAAG,EAAU,CAAC,GAAG,UAAU,IAAI;oBACvC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC;gBACF,IAAI,CAAC,OAAO,GAAG,EAAY,CAAC,GAAG,UAAU,IAAI;oBAC3C,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBACnC,CAAC,CAAC;gBACF,IAAI,CAAC,WAAW,GAAG,EAAgB,CAAC,GAAG,UAAU,IAAI;oBACnD,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC3C,CAAC,CAAC;gBACF,IAAI,CAAC,eAAe,GAAG,EAAoB,CAAC,GAAG,UAAU,IAAI;oBAC3D,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBACzC,CAAC,CAAC;gBACF,IAAI,CAAC,mBAAmB,GAAG,EAAwB,CAAC,GAAG,UAAU,IAAI;oBACnE,IAAI,CAAC,gCAAgC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBACjD,CAAC,CAAC;aACH;YAED,kDAAkD;YAClD,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU,GAAG,EAAE,IAAI,EAAE,IAAI;gBACzC,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;YACrD,CAAC,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU,GAAG,EAAE,IAAI,EAAE,IAAI;gBACzC,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAQ,CAAC;YAC5D,CAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAU,GAAG,EAAE,IAAI;gBAClC,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC9C,CAAC,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,EAAE,IAAI;gBAC9B,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU,GAAG,EAAE,IAAI;gBAChC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACrC,CAAC,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,GAAG,UAAU,GAAG,EAAE,IAAI;gBACtC,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC3C,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACvC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;QACd,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,mEAAmE;QACnE,uEAAuE;QACvE,mEAAmE;QACnE,iCAAiC;QACjC,IAAI,CAAC,UAAU,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACjD,MAAM,IAAI,GAAQ,IAAI,CAAC;YAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC3B,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC;gBAC7B,OAAO,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC;gBAC7B,OAAO,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC;gBAC5B,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;gBACxB,OAAO,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;gBAC1B,OAAO,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;gBAC9B,OAAO,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC,CAAC;gBAClC,OAAO,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC,CAAC;aACvC;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1B,OAAO,IAAI,CAAC,MAAM,CAAC;YACnB,OAAO,IAAI,CAAC,SAAS,CAAC;YACtB,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gDAAgD;IAChD,oFAAoF;IACpF,wCAAwC;IACxC,aAAa,CAAyB,IAAa;QACjD,yBAAyB;IAC3B,CAAC;IAED,0EAA0E;IAC1E,gBAAgB,CACd,WAAkC,EAClC,IAAY;QAEZ,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC;IACpB,CAAC;IAED,4EAA4E;IAC5E,oEAAoE;IACpE,SAAS,CAAC,OAAe;QACvB,uEAAuE;QACvE,0EAA0E;QAC1E,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,kBAAkB,CAAC,IAAY,EAAE,GAAa;QAC5C,IAAI;YACF,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YACjE,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9C,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACf,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC;YAC9B,OAAO,eAAe,CAAC;SACxB;QAAC,OAAO,aAAa,EAAE;YACtB,IAAI,aAAa,CAAC,oBAAoB,KAAK,IAAI,EAAE;gBAC/C,IAAI;oBACF,aAAa,CAAC,OAAO;wBACnB,aAAa,CAAC,OAAO;4BACrB,wFAAwF;4BACxF,mEAAmE,CAAC;iBACvE;gBAAC,OAAO,eAAe,EAAE;oBACxB,yDAAyD;oBACzD,MAAM,aAAa,CAAC;iBACrB;aACF;YACD,MAAM,aAAa,CAAC;SACrB;IACH,CAAC;IAED,gCAAgC;IAChC,oBAAoB,CAElB,iBAA0D,EAC1D,UAAkB;QAElB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;IACtE,CAAC;IAED,wBAAwB,CAEtB,UAAkB,EAClB,iBAAiE;QAEjE,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;IAC5E,CAAC;IAED,gCAAgC,CAE9B,UAAkB,EAClB,OAAqC;QAErC,UAAU,CAAC,IAAI,CACb,IAAI,EACJ,gCAAgC,EAChC,OAAO,EACP,UAAU,EACV,gBAAgB,CACjB,CAAC;IACJ,CAAC;IAED,kBAAkB,CAEhB,UAAkB,EAClB,iBAA0D;QAE1D,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;IACnE,CAAC;IAED,0BAA0B,CAExB,UAAkB,EAClB,OAA+B;QAE/B,UAAU,CAAC,IAAI,CACb,IAAI,EACJ,uBAAuB,EACvB,OAAO,EACP,UAAU,EACV,gBAAgB,CACjB,CAAC;IACJ,CAAC;IAED,gBAAgB,CAEd,UAAiD,EACjD,UAAkB;QAElB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,qBAAqB,CAEnB,UAAyC,EACzC,UAAkB,EAClB,OAAiC;QAEjC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,KAAK,KAAK,EAAE;YACxD,MAAM,KAAK,GAAQ,IAAI,KAAK,CAC1B,WAAW,YAAY,CAAC,UAAU,CAAC,uBAAuB;gBACxD,kDAAkD,IAAI,CAAC,SAAS,CAC9D,UAAU,CACX,GAAG;gBACJ,8BACS,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAE,CAAC,IACrC,GAAG,CACN,CAAC;YACF,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YAClC,MAAM,KAAK,CAAC;SACb;QAED,MAAM,QAAQ,GAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACpD,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QACrC,MAAM,eAAe,GAAG,IAAI,WAAW,CAAC;YACtC,GAAG,EAAE,UAAU;YACf,eAAe,EAAE,QAAQ;YACzB,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK;YACrB,0GAA0G;YAC1G,cAAc,EAAE,SAAS;SAC1B,CAAC,CAAC;QACH,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE1C,OAAO,IAAI,CAAC,SAAS;YACnB,CAAC,CAAC,uBAAuB;YACzB,CAAC,CAAM,qBAAqB,CAAC;IACjC,CAAC;IAED,qBAAqB,CAEnB,OAAkB,EAClB,UAAkB,EAClB,OAA2B;QAE3B,sBAAsB,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;YACjC,MAAM,KAAK,GAAQ,IAAI,KAAK,CAC1B,WAAW,YAAY,CAAC,UAAU,CAAC,uBAAuB;gBACxD,8CAA8C,IAAI,CAAC,SAAS,CAC1D,OAAO,CACR,GAAG;gBACJ,8BACS,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAE,CAAC,IACrC,GAAG,CACN,CAAC;YACF,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YAClC,MAAM,KAAK,CAAC;SACb;QACD,MAAM,QAAQ,GAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC;YACnC,GAAG,EAAE,UAAU;YACf,YAAY,EAAE,OAAO;YACrB,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK;SACtB,CAAC,CAAC;QACH,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE1C,OAAO,qBAAqB,CAAC;IAC/B,CAAC;CACF;AAED,SAAS,UAAU,CACjB,eAAoB,EACpB,WAAgB,EAChB,UAAkB,EAClB,YAAqB,KAAK;IAE1B,sBAAsB,CAAC,UAAU,CAAC,CAAC;IACnC,MAAM,QAAQ,GAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACpD,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC;IAE9E,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAC;IACzE,IAAI,SAAS,EAAE;QACb,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC;KACrC;IACD,IAAI,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,EAAE;QACrC,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC;KAClD;IAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACtC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC;IAE9B,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AAED,SAAS,YAAY,CAAC,WAAgB,EAAE,UAAkB;IACxD,sBAAsB,CAAC,UAAU,CAAC,CAAC;IACnC,MAAM,QAAQ,GAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACpD,gCAAgC;IAChC,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC;IAClD,MAAM,IAAI,GACR,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC;IAEvD,MAAM,SAAS,GAAG,IAAI,WAAW,CAAC;QAChC,UAAU,EAAE,EAAE;QACd,GAAG,EAAE,UAAU;QACf,iBAAiB,EAAE,UAAU,IAAI,WAAW,CAAC,kBAAkB,KAAK,IAAI;KACzE,CAAC,CAAC;IACH,IAAI,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,EAAE;QACrC,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC;KACpD;IAED,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,OAAY,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7E,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC;IAExC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEpC,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;QACxB,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;QACxD,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,IAAI,GAAG,CAAC,OAAO,EAAE,oBAAoB,CAAC,EAAE;YACtC,WAAW,CAAC,iBAAiB,GAAG,OAAO,CAAC,kBAA6B,CAAC,CAAC,0DAA0D;SAClI;QACD,sDAAsD;aACjD,IAAI,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;YAC7B,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACtC;QACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IACH,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;AACnC,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAW;IACzC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,cAAc,EAAE;QACnC,MAAM,KAAK,GAAQ,IAAI,KAAK;QAC1B,sDAAsD;QACtD,kCAAkC,GAAG,OAAO;YAC1C,wDACE,cAAc,GAAG,CACnB,EAAE,CACL,CAAC;QACF,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAClC,MAAM,KAAK,CAAC;KACb;AACH,CAAC"}