{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-FHLWH6W2.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  InfoGeneratedModule,\n  MermaidGeneratedSharedModule,\n  __name\n} from \"./chunk-YAJQ3QCK.mjs\";\n\n// src/language/info/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/info/tokenBuilder.ts\nvar InfoTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"InfoTokenBuilder\");\n  }\n  constructor() {\n    super([\"info\", \"showInfo\"]);\n  }\n};\n\n// src/language/info/module.ts\nvar InfoModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new InfoTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createInfoServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Info = inject(\n    createDefaultCoreModule({ shared }),\n    InfoGeneratedModule,\n    InfoModule\n  );\n  shared.ServiceRegistry.register(Info);\n  return { shared, Info };\n}\n__name(createInfoServices, \"createInfoServices\");\n\nexport {\n  InfoModule,\n  createInfoServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,IAAI,mBAAmB,cAAc,4BAA4B;AAAA,EAjBjE,OAiBiE;AAAA;AAAA;AAAA,EAC/D,OAAO;AACL,IAAAA,QAAO,MAAM,kBAAkB;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,QAAQ,UAAU,CAAC;AAAA,EAC5B;AACF;AAGA,IAAI,aAAa;AAAA,EACf,QAAQ;AAAA,IACN,cAA8B,gBAAAA,QAAO,MAAM,IAAI,iBAAiB,GAAG,cAAc;AAAA,IACjF,gBAAgC,gBAAAA,QAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC3F;AACF;AACA,SAAS,mBAAmB,UAAU,iBAAiB;AACrD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,IAAI;AACpC,SAAO,EAAE,QAAQ,KAAK;AACxB;AAZS;AAaTA,QAAO,oBAAoB,oBAAoB;", "names": ["__name"]}