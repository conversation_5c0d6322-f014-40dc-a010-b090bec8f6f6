// 创建主页缺失的图片资源
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 确保images目录存在
const imagesDir = path.join(__dirname, 'public', 'images');
if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
}

// 创建SVG占位符图片的函数
function createSVGPlaceholder(filename, title, color = '#1890ff', bgColor = '#f0f9ff', icon = 'default') {
    let iconSVG = '';
    
    // 根据类型创建不同的图标
    switch(icon) {
        case 'filter':
            iconSVG = `<path d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z M19.5 10.5a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Z" stroke="${color}" stroke-width="2" fill="none"/>`;
            break;
        case 'target':
            iconSVG = `<circle cx="12" cy="12" r="10" stroke="${color}" stroke-width="2" fill="none"/>
                      <circle cx="12" cy="12" r="6" stroke="${color}" stroke-width="2" fill="none"/>
                      <circle cx="12" cy="12" r="2" fill="${color}"/>`;
            break;
        case 'video':
            iconSVG = `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="${color}" stroke-width="2" fill="none"/>
                      <polygon points="10,8 16,12 10,16" fill="${color}"/>`;
            break;
        case 'chart':
            iconSVG = `<path d="M3 3v18h18" stroke="${color}" stroke-width="2" fill="none"/>
                      <path d="m19 9-5 5-4-4-5 5" stroke="${color}" stroke-width="2" fill="none"/>`;
            break;
        case 'school':
            iconSVG = `<path d="M22 10v6M2 10l10-5 10 5-10 5z" stroke="${color}" stroke-width="2" fill="none"/>
                      <path d="M6 12v5c3 3 9 3 12 0v-5" stroke="${color}" stroke-width="2" fill="none"/>`;
            break;
        case 'users':
            iconSVG = `<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="${color}" stroke-width="2" fill="none"/>
                      <circle cx="9" cy="7" r="4" stroke="${color}" stroke-width="2" fill="none"/>
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87" stroke="${color}" stroke-width="2" fill="none"/>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="${color}" stroke-width="2" fill="none"/>`;
            break;
        case 'crown':
            iconSVG = `<path d="M2 18h20l-2-6-4 2-4-4-4 4-4-2z" stroke="${color}" stroke-width="2" fill="none"/>
                      <circle cx="12" cy="9" r="1" fill="${color}"/>`;
            break;
        default:
            iconSVG = `<circle cx="12" cy="12" r="3" fill="${color}"/>`;
    }
    
    const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${bgColor};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${color};stop-opacity:0.1" />
        </linearGradient>
    </defs>
    <rect width="400" height="300" fill="url(#bg-gradient)" stroke="${color}" stroke-width="2" rx="12"/>
    
    <!-- 背景装饰 -->
    <circle cx="350" cy="50" r="30" fill="${color}" opacity="0.1"/>
    <circle cx="50" cy="250" r="20" fill="${color}" opacity="0.15"/>
    
    <!-- 主图标区域 -->
    <g transform="translate(180, 80)">
        <circle cx="20" cy="20" r="35" fill="${color}" opacity="0.2"/>
        <g transform="translate(8, 8)" stroke-width="2">
            ${iconSVG}
        </g>
    </g>
    
    <!-- 标题文字 -->
    <text x="200" y="180" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="${color}" font-weight="700">${title}</text>
    
    <!-- 副标题 -->
    <text x="200" y="210" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="${color}" opacity="0.8">iFlytek Spark AI 驱动</text>
    
    <!-- 装饰线条 -->
    <line x1="120" y1="230" x2="280" y2="230" stroke="${color}" stroke-width="2" opacity="0.3"/>
    
    <!-- 底部标识 -->
    <g transform="translate(170, 250)">
        <rect x="0" y="0" width="60" height="20" fill="${color}" opacity="0.1" rx="10"/>
        <text x="30" y="14" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="10" fill="${color}" font-weight="600">iFlytek</text>
    </g>
</svg>`;
    
    const filepath = path.join(imagesDir, filename);
    fs.writeFileSync(filepath, svg);
    console.log(`✅ 创建图片: ${filename}`);
}

console.log('🎨 开始创建主页缺失的图片资源...');

// 创建流程图片
const processImages = [
    { filename: 'process-1.png', title: '智能筛选 自动邀约', color: '#1890ff', icon: 'filter' },
    { filename: 'process-2.png', title: '因岗设题 精准考核', color: '#52c41a', icon: 'target' },
    { filename: 'process-3.png', title: '多模态面试 提升体验', color: '#722ed1', icon: 'video' },
    { filename: 'process-4.png', title: '智能评估 综合分析', color: '#fa8c16', icon: 'chart' }
];

// 创建场景图片
const scenarioImages = [
    { filename: 'scenario-1.png', title: '校园招聘面试', color: '#1890ff', icon: 'school' },
    { filename: 'scenario-2.png', title: '大批量人才筛选', color: '#52c41a', icon: 'users' },
    { filename: 'scenario-3.png', title: '高端人才面试', color: '#722ed1', icon: 'crown' }
];

// 创建流程图片
processImages.forEach(img => {
    createSVGPlaceholder(img.filename, img.title, img.color, '#f0f9ff', img.icon);
});

// 创建场景图片
scenarioImages.forEach(img => {
    createSVGPlaceholder(img.filename, img.title, img.color, '#f0f5ff', img.icon);
});

console.log('🎉 主页图片资源创建完成！');
console.log('📁 图片保存位置: public/images/');
console.log('🔄 请刷新浏览器查看效果');
