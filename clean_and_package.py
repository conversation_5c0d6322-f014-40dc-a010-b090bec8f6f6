#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理项目并创建精简版压缩包
只保留最重要和最必要的文件
"""

import os
import shutil
import zipfile
from pathlib import Path

def should_keep_file(file_path):
    """判断文件是否应该保留"""
    
    # 必须保留的核心文件
    keep_patterns = [
        # 启动和配置文件
        'start_system.py',
        'quick_system_check.py', 
        '启动服务器.bat',
        '完整启动指南.md',
        '比赛评审运行指南.md',
        'README.md',
        
        # 前端核心文件
        'frontend/src/',
        'frontend/public/',
        'frontend/package.json',
        'frontend/vite.config.js',
        'frontend/vue.config.js',
        'frontend/index.html',
        
        # 后端核心文件
        'backend/app/',
        'backend/run_server.py',
        'backend/simple_server.py',
        'backend/requirements.txt',
        'backend/package.json',
        'backend/interview_system.db',
        
        # 重要文档
        'docs/presentation.md',
        'docs/technical-documentation.md',
        'docs/system-design-overview.md',
        'FINAL_PROJECT_STATUS.md',
        'PROJECT_COMPLETION_REPORT.md',
        
        # 演示文件
        'frontend/multimodal-showcase-demo.html',
        'complete-validation-test.html'
    ]
    
    # 检查是否匹配保留模式
    for pattern in keep_patterns:
        if pattern in file_path:
            return True
    
    return False

def should_skip_file(file_path):
    """判断文件是否应该跳过"""
    
    skip_patterns = [
        # 测试和调试文件
        'test-', 'test_', 'debug-', 'debug_',
        'validation-', 'verify-', 'check-',
        'fix-', 'diagnostic-', 'emergency-',
        
        # 临时和缓存文件
        '__pycache__', 'node_modules', '.git',
        'dist/', 'build/', 'logs/',
        '.log', '.cache', 'venv/',
        
        # 重复的报告文件
        '_REPORT.md', '_FIX_REPORT.md',
        '_OPTIMIZATION_REPORT.md',
        '_SUMMARY.md', '_GUIDE.md',
        
        # 开发工具文件
        '.js.bak', '.html.bak',
        'simple-', 'quick-', 'final-',
        'enhanced-', 'optimized-',
        
        # 提交目录（避免重复）
        'submission/'
    ]
    
    # 检查是否匹配跳过模式
    for pattern in skip_patterns:
        if pattern in file_path.lower():
            return True
    
    return False

def create_clean_structure():
    """创建清理后的项目结构"""
    
    print("🧹 开始清理项目文件...")
    
    # 创建清理后的目录
    clean_dir = "clean_project"
    if os.path.exists(clean_dir):
        shutil.rmtree(clean_dir)
    os.makedirs(clean_dir)
    
    # 遍历项目文件
    for root, dirs, files in os.walk("."):
        # 跳过不需要的目录
        dirs[:] = [d for d in dirs if not should_skip_file(os.path.join(root, d))]
        
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, ".")
            
            # 跳过不需要的文件
            if should_skip_file(rel_path):
                continue
                
            # 只保留核心文件
            if should_keep_file(rel_path):
                dest_path = os.path.join(clean_dir, rel_path)
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                shutil.copy2(file_path, dest_path)
                print(f"✅ 保留: {rel_path}")
    
    print(f"🎉 清理完成！清理后的文件在 {clean_dir} 目录")
    return clean_dir

def create_essential_packages():
    """创建精简版压缩包"""
    
    print("📦 创建精简版压缩包...")
    
    clean_dir = create_clean_structure()
    
    # 1. 完整精简版
    print("创建完整精简版...")
    with zipfile.ZipFile("86014454完整精简版.zip", 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(clean_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, clean_dir)
                zipf.write(file_path, arcname)
    
    # 2. 可执行版本
    print("创建可执行版本...")
    executable_files = [
        "start_system.py",
        "quick_system_check.py", 
        "启动服务器.bat",
        "完整启动指南.md",
        "比赛评审运行指南.md",
        "backend/run_server.py",
        "backend/requirements.txt",
        "backend/app/",
        "backend/interview_system.db"
    ]
    
    with zipfile.ZipFile("86014454可执行版.zip", 'w', zipfile.ZIP_DEFLATED) as zipf:
        for pattern in executable_files:
            for root, dirs, files in os.walk(clean_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, clean_dir)
                    if pattern in rel_path:
                        zipf.write(file_path, rel_path)
    
    # 3. 源码版本
    print("创建源码版本...")
    source_files = [
        "frontend/src/",
        "frontend/public/",
        "frontend/package.json",
        "frontend/vite.config.js",
        "backend/app/",
        "backend/requirements.txt",
        "README.md"
    ]
    
    with zipfile.ZipFile("86014454源码版.zip", 'w', zipfile.ZIP_DEFLATED) as zipf:
        for pattern in source_files:
            for root, dirs, files in os.walk(clean_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, clean_dir)
                    if pattern in rel_path:
                        zipf.write(file_path, rel_path)
    
    # 4. 文档版本
    print("创建文档版本...")
    doc_files = [
        "docs/",
        "FINAL_PROJECT_STATUS.md",
        "PROJECT_COMPLETION_REPORT.md",
        "README.md",
        "frontend/multimodal-showcase-demo.html"
    ]
    
    with zipfile.ZipFile("86014454文档版.zip", 'w', zipfile.ZIP_DEFLATED) as zipf:
        for pattern in doc_files:
            for root, dirs, files in os.walk(clean_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, clean_dir)
                    if pattern in rel_path:
                        zipf.write(file_path, rel_path)
    
    print("🎉 所有精简版压缩包创建完成！")

def show_cleanup_summary():
    """显示清理总结"""
    
    print("\n📋 精简版压缩包说明:")
    print("1. 86014454完整精简版.zip - 包含所有核心文件的完整版本")
    print("2. 86014454可执行版.zip - 只包含运行必需的文件")
    print("3. 86014454源码版.zip - 只包含源代码文件")
    print("4. 86014454文档版.zip - 只包含文档和说明")
    
    print("\n🗑️ 已清理的文件类型:")
    print("- 所有测试和调试文件")
    print("- 重复的报告和日志文件")
    print("- 临时文件和缓存目录")
    print("- 开发工具和验证脚本")
    print("- node_modules 和 __pycache__")
    
    print("\n✅ 保留的核心文件:")
    print("- 启动脚本和配置文件")
    print("- 前端源码 (src/, public/)")
    print("- 后端源码 (app/)")
    print("- 重要文档和说明")
    print("- 数据库文件")
    print("- 演示页面")

def main():
    """主函数"""
    print("🚀 开始创建精简版项目包...")
    
    create_essential_packages()
    show_cleanup_summary()
    
    print("\n⚠️ 建议:")
    print("- 使用 86014454完整精简版.zip 作为主要提交版本")
    print("- 其他版本可根据具体要求选择使用")
    print("- 原始文件未被删除，仍在原目录中")

if __name__ == "__main__":
    main()
