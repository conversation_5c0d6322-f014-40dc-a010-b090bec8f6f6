{"version": 3, "file": "completion-provider.d.ts", "sourceRoot": "", "sources": ["../../../src/lsp/completion/completion-provider.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EAAE,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AACjG,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,gCAAgC,CAAC;AAC9E,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AACtE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAC1D,OAAO,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAC/G,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,KAAK,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,8BAA8B,CAAC;AAClF,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,+CAA+C,CAAC;AAE3F,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACrF,OAAO,KAAK,GAAG,MAAM,kCAAkC,CAAC;AAIxD,OAAO,EAAU,KAAK,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAG5D,MAAM,MAAM,kBAAkB,GAAG,CAAC,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,mBAAmB,KAAK,IAAI,CAAA;AAEjG,MAAM,MAAM,mBAAmB,GAAG,CAAC;IAC/B,KAAK,CAAC,EAAE,MAAM,CAAA;CACjB,GAAG;IACA,IAAI,EAAE,OAAO,CAAA;CAChB,GAAG;IACA,eAAe,EAAE,kBAAkB,CAAA;CACtC,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE7B,MAAM,WAAW,iBAAiB;IAC9B,IAAI,CAAC,EAAE,OAAO,CAAA;IACd,QAAQ,EAAE,eAAe,CAAA;IACzB,YAAY,EAAE,YAAY,CAAA;IAC1B,QAAQ,EAAE,WAAW,EAAE,CAAA;IACvB;;;OAGG;IACH,WAAW,EAAE,MAAM,CAAA;IACnB;;;;OAIG;IACH,cAAc,EAAE,MAAM,CAAA;IACtB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAA;IACd,QAAQ,EAAE,QAAQ,CAAA;CACrB;AAED,MAAM,WAAW,yBAAyB;IACtC;;;;;;;;;OASG;IACH,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC7B;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;CAClC;AAED,MAAM,WAAW,iCAAiC;IAC9C,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;CACxB;AAED,wBAAgB,8BAA8B,CAAC,OAAO,EAAE,KAAK,CAAC,yBAAyB,GAAG,SAAS,CAAC,GAAG,yBAAyB,CAO/H;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IAC/B;;;;;;;;;OASG;IACH,aAAa,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,gBAAgB,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,YAAY,CAAC,cAAc,GAAG,SAAS,CAAC,CAAA;IAC7I;;;;OAIG;IACH,QAAQ,CAAC,iBAAiB,CAAC,EAAE,yBAAyB,CAAC;CAC1D;AAED,qBAAa,yBAA0B,YAAW,kBAAkB;IAEhE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;IAC7D,SAAS,CAAC,QAAQ,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAChE,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC;IAChD,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC;IACxC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;IAChC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACtD,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC;IAChD,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC;IAChD,QAAQ,CAAC,iBAAiB,CAAC,EAAE,yBAAyB,CAAC;gBAE3C,QAAQ,EAAE,eAAe;IAa/B,aAAa,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,cAAc,GAAG,SAAS,CAAC;IAsC/I;;;;;OAKG;IACH,SAAS,CAAC,gBAAgB,CAAC,KAAK,EAAE,cAAc,EAAE,GAAG,cAAc,EAAE;IAIrE,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,GAAG,WAAW,EAAE;IA4B/E,SAAS,CAAE,aAAa,CAAC,QAAQ,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,GAAG,gBAAgB,CAAC,iBAAiB,CAAC;IA2F5G,SAAS,CAAC,0BAA0B,CAAC,QAAQ,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO;IAQrH,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,SAAS;IAiB3F;;;;OAIG;IACH,SAAS,CAAC,kBAAkB,CAAC,KAAK,EAAE,cAAc,EAAE,GAAG,OAAO;IAI9D;;;;;;;;OAQG;IACH,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,iCAAiC;IA2C9F,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAAC;IAWxH,SAAS,CAAC,2BAA2B,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAAC;IAgC1J;;;;;;;;OAQG;IACH,SAAS,CAAC,sBAAsB,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,iBAAiB,GAAG,MAAM,CAAC,kBAAkB,CAAC;IAIjH;;;;;;;;OAQG;IACH,SAAS,CAAC,6BAA6B,CAAC,eAAe,EAAE,kBAAkB,GAAG,mBAAmB;IAYjG,SAAS,CAAC,yBAAyB,CAAC,eAAe,EAAE,kBAAkB,GAAG,aAAa,GAAG,MAAM,GAAG,SAAS;IAW5G,SAAS,CAAC,oBAAoB,CAAC,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAAC;IAYlI,SAAS,CAAC,4BAA4B,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAO,GAAG,kBAAkB;IAIjF,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,GAAG,OAAO;IAKlF,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,EAAE,mBAAmB,GAAG,cAAc,GAAG,SAAS;IAmD/G,SAAS,CAAC,uBAAuB,CAAC,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS;CAiBtH"}