<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iFlytek文字面试功能演示 - 多模态面试评估系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #333;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #1890ff;
        }
        
        .demo-title {
            color: #1890ff;
            font-size: 32px;
            margin: 0 0 10px 0;
            font-weight: 600;
        }
        
        .demo-subtitle {
            color: #666;
            font-size: 16px;
            margin: 0;
        }
        
        .feature-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #1890ff;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-title {
            color: #333;
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 10px 0;
            text-align: center;
        }
        
        .feature-description {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .feature-highlights {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-highlights li {
            padding: 5px 0;
            font-size: 13px;
            color: #555;
            position: relative;
            padding-left: 20px;
        }
        
        .feature-highlights li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #52c41a;
            font-weight: bold;
        }
        
        .workflow-section {
            background: linear-gradient(135deg, #1890ff 0%, #667eea 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .workflow-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 20px 0;
            text-align: center;
        }
        
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .workflow-step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .step-number {
            background: white;
            color: #1890ff;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 15px auto;
        }
        
        .step-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }
        
        .step-description {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }
        
        .demo-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e8e8e8;
            flex-wrap: wrap;
        }
        
        .demo-button {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            min-width: 180px;
            justify-content: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1890ff, #667eea);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #52c41a, #73d13d);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(82, 196, 26, 0.3);
        }
        
        .advantages-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .advantages-title {
            color: #333;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 20px 0;
            text-align: center;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .advantage-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e8e8e8;
        }
        
        .advantage-icon {
            font-size: 36px;
            margin-bottom: 15px;
            color: #1890ff;
        }
        
        .advantage-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .advantage-description {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .demo-title {
                font-size: 24px;
            }
            
            .feature-showcase {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .workflow-steps {
                grid-template-columns: 1fr;
            }
            
            .demo-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-button {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">💬 iFlytek文字面试功能演示</h1>
            <p class="demo-subtitle">基于iFlytek Spark大模型的智能文字对话面试系统</p>
        </div>
        
        <div class="workflow-section">
            <h2 class="workflow-title">🚀 文字面试完整流程</h2>
            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-title">选择面试模式</div>
                    <div class="step-description">在主页选择"文字对话面试"模式</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-title">进入对话界面</div>
                    <div class="step-description">AI面试官发起问候和第一个问题</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-title">文字交流</div>
                    <div class="step-description">通过文字回答问题，获得AI实时分析</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-title">智能评估</div>
                    <div class="step-description">系统进行实时文本分析和评分</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">5</div>
                    <div class="step-title">查看结果</div>
                    <div class="step-description">获得详细的面试报告和改进建议</div>
                </div>
            </div>
        </div>
        
        <div class="feature-showcase">
            <div class="feature-card">
                <div class="feature-icon">💬</div>
                <h3 class="feature-title">智能对话交互</h3>
                <p class="feature-description">基于iFlytek Spark大模型的自然语言对话，支持深度技术交流</p>
                <ul class="feature-highlights">
                    <li>实时文字对话</li>
                    <li>智能问题生成</li>
                    <li>上下文理解</li>
                    <li>多轮对话支持</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3 class="feature-title">实时文本分析</h3>
                <p class="feature-description">AI智能分析回答内容，提供多维度评估指标</p>
                <ul class="feature-highlights">
                    <li>关键词匹配度</li>
                    <li>逻辑结构分析</li>
                    <li>专业术语识别</li>
                    <li>回答完整性评估</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">智能评分系统</h3>
                <p class="feature-description">多维度实时评分，客观评估候选人技术能力</p>
                <ul class="feature-highlights">
                    <li>技术能力评分</li>
                    <li>表达能力评估</li>
                    <li>逻辑思维分析</li>
                    <li>综合能力评价</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3 class="feature-title">AI智能助手</h3>
                <p class="feature-description">提供智能提示和引导，帮助候选人更好地表达</p>
                <ul class="feature-highlights">
                    <li>智能提示功能</li>
                    <li>思路引导</li>
                    <li>实时反馈</li>
                    <li>个性化建议</li>
                </ul>
            </div>
        </div>
        
        <div class="advantages-section">
            <h2 class="advantages-title">🌟 文字面试模式优势</h2>
            <div class="advantages-grid">
                <div class="advantage-item">
                    <div class="advantage-icon">⚡</div>
                    <h3 class="advantage-title">高效专注</h3>
                    <p class="advantage-description">专注于技术内容交流，避免外在因素干扰，提高面试效率</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">🎯</div>
                    <h3 class="advantage-title">深度交流</h3>
                    <p class="advantage-description">文字表达更利于深度技术讨论，展现逻辑思维能力</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">📱</div>
                    <h3 class="advantage-title">设备简单</h3>
                    <p class="advantage-description">只需键盘输入，无需摄像头和麦克风，降低技术门槛</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">🌐</div>
                    <h3 class="advantage-title">网络友好</h3>
                    <p class="advantage-description">低带宽要求，适合各种网络环境，确保面试稳定进行</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">📝</div>
                    <h3 class="advantage-title">记录完整</h3>
                    <p class="advantage-description">完整保存对话记录，便于后续回顾和分析</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">🔍</div>
                    <h3 class="advantage-title">分析精准</h3>
                    <p class="advantage-description">专注文本分析，评估更加客观和精准</p>
                </div>
            </div>
        </div>
        
        <div class="demo-actions">
            <a href="http://localhost:5173" class="demo-button btn-secondary">
                🏠 返回主页
            </a>
            <a href="http://localhost:5173/select-interview-mode" class="demo-button btn-primary">
                🎯 选择面试模式
            </a>
            <a href="http://localhost:5173/text-based-interview" class="demo-button btn-success">
                💬 直接体验文字面试
            </a>
        </div>
    </div>
    
    <script>
        // 页面加载完成后的演示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ iFlytek文字面试功能演示页面加载完成');
            console.log('🎯 核心功能：');
            console.log('   - 智能对话交互');
            console.log('   - 实时文本分析');
            console.log('   - 智能评分系统');
            console.log('   - AI智能助手');
            console.log('🌟 主要优势：');
            console.log('   - 高效专注的技术交流');
            console.log('   - 低设备和网络要求');
            console.log('   - 精准的文本分析评估');
            console.log('   - 完整的对话记录');
        });
        
        // 添加点击测试功能
        document.querySelectorAll('.demo-button').forEach(button => {
            button.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href.includes('localhost')) {
                    console.log(`🔗 正在跳转到: ${href}`);
                }
            });
        });
        
        // 添加卡片悬停效果
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.borderLeftColor = '#52c41a';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.borderLeftColor = '#1890ff';
            });
        });
    </script>
</body>
</html>
