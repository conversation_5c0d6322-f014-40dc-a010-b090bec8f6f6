---
name: Bug report
about: Let us know about a bug
title: ''
labels: bug
assignees: ''

---

**Before you post**

A request for help or a request for a how-to should be directed to [Phind]([url](https://www.phind.com/search?c=I%27m%20using%20the%20Cytoscape.js%20graph%20theory%20JS%20library.&q=How%20do%20I%20create%20a%20graph%20in%20my%20HTML%20page)).

If your issue pertains to an extension, your issue should be filed in that extension's repository -- not here.



**Environment info**

- Cytoscape.js version :
- Browser/Node.js & version :



**Current (buggy) behaviour**

_What does the bug do?_



**Desired behaviour**

_What do you expect Cytoscape.js to do instead?_



**Minimum steps to reproduce**

_What do you need to do to reproduce the issue?_

_Fork/clone this JSBin demo and reproduce your issue so that your issue can be addressed quickly and effectively:_

1. Go to our base JSBin: http://jsbin.com/fiqugiq
2. Click the 'Edit in JSBin' button in the top-right corner.
3. Click File > Clone to make your own copy of the demo (with its own unique URL) that you can edit.
4. Click the yellow 'Login or Register' button at the top-right corner so that your JSBin persists longer-term.
5. Log in with your GitHub account.
6. Edit the JSBin to reproduce your issue.
7. When you're finished editing, click 'File > Save snapshot` (command + s) to save your edits.
8. Post the link to your JSBin here.



**For reviewers**

_Reviewers should ensure that the following tasks are carried out for incorporated issues:_

- [ ] Ensure that the reporter has included a reproducible demo.  They can easily fork this JSBin demo: http://jsbin.com/fiqugiq
- [ ] The issue has been associated with a corresponding [milestone](https://github.com/cytoscape/cytoscape.js/milestones).
- [ ] The commits have been incorporated into the corresponding branches.  Bug-fix patches go on
    - [ ] `master` and
    - [ ] `unstable`.
- [ ] The issue has been labelled as a [`bug`](https://github.com/cytoscape/cytoscape.js/labels/bug), if necessary.

