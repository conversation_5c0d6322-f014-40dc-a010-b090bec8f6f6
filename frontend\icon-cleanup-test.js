#!/usr/bin/env node

/**
 * 大图标清理验证脚本
 * 检查HomePage.vue文件中是否还有任何可能导致布局问题的大图标
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始验证大图标清理结果...\n');

// 读取HomePage.vue文件
const homePagePath = path.join(__dirname, 'src/views/HomePage.vue');
let content = '';

try {
    content = fs.readFileSync(homePagePath, 'utf8');
} catch (error) {
    console.error('❌ 无法读取HomePage.vue文件:', error.message);
    process.exit(1);
}

// 检查项目配置
const checks = [
    {
        name: '超大图标容器检查',
        pattern: /width:\s*[8-9][0-9]px|width:\s*[1-9][0-9][0-9]px|height:\s*[8-9][0-9]px|height:\s*[1-9][0-9][0-9]px/g,
        description: '检查是否还有80px以上的图标容器'
    },
    {
        name: '超大字体尺寸检查',
        pattern: /font-size:\s*[3-9][0-9]px|font-size:\s*[1-9][0-9][0-9]px/g,
        description: '检查是否还有30px以上的字体尺寸'
    },
    {
        name: '超大rem尺寸检查',
        pattern: /font-size:\s*[3-9](\.\d+)?rem|font-size:\s*[1-9][0-9](\.\d+)?rem/g,
        description: '检查是否还有3rem以上的字体尺寸'
    },
    {
        name: '危险transform缩放检查',
        pattern: /transform:\s*scale\([2-9]|transform:\s*scale\(1\.[6-9]/g,
        description: '检查是否有过大的缩放变换'
    },
    {
        name: '内联样式大尺寸检查',
        pattern: /style="[^"]*(?:font-size:\s*[3-9][0-9]px|width:\s*[8-9][0-9]px|height:\s*[8-9][0-9]px)/g,
        description: '检查内联样式中的大尺寸设置'
    }
];

let totalIssues = 0;
const results = [];

// 执行检查
checks.forEach(check => {
    const matches = content.match(check.pattern);
    const issueCount = matches ? matches.length : 0;
    totalIssues += issueCount;
    
    results.push({
        name: check.name,
        description: check.description,
        issues: issueCount,
        matches: matches || []
    });
    
    if (issueCount > 0) {
        console.log(`❌ ${check.name}: 发现 ${issueCount} 个问题`);
        matches.forEach((match, index) => {
            console.log(`   ${index + 1}. ${match}`);
        });
    } else {
        console.log(`✅ ${check.name}: 通过`);
    }
    console.log(`   ${check.description}\n`);
});

// 检查已优化的图标容器
const optimizedContainers = [
    { name: 'ai-step-icon', expectedSize: '40px' },
    { name: 'ai-tech-icon', expectedSize: '40px' },
    { name: 'ai-feature-icon', expectedSize: '40px' },
    { name: 'ai-cta-option-icon', expectedSize: '40px' }
];

console.log('📊 已优化图标容器验证:');
optimizedContainers.forEach(container => {
    const pattern = new RegExp(`\\.${container.name}\\s*{[^}]*width:\\s*${container.expectedSize}[^}]*height:\\s*${container.expectedSize}`, 'g');
    const found = content.match(pattern);
    
    if (found) {
        console.log(`✅ ${container.name}: 已优化到 ${container.expectedSize}`);
    } else {
        console.log(`⚠️ ${container.name}: 可能需要进一步检查`);
    }
});

// 检查响应式优化
console.log('\n📱 响应式优化验证:');
const mobileOptimizations = [
    'ai-tech-icon.*36px',
    'ai-step-icon.*36px', 
    'ai-cta-option-icon.*32px',
    'ai-scenario-icon.*1\\.5rem'
];

mobileOptimizations.forEach(optimization => {
    const pattern = new RegExp(optimization, 'g');
    const found = content.match(pattern);
    
    if (found) {
        console.log(`✅ 移动端优化: ${optimization.replace('.*', ' → ')} 已应用`);
    } else {
        console.log(`⚠️ 移动端优化: ${optimization} 可能需要检查`);
    }
});

// 生成总结报告
console.log('\n' + '='.repeat(60));
console.log('📋 大图标清理验证总结');
console.log('='.repeat(60));

if (totalIssues === 0) {
    console.log('🎉 恭喜！所有大图标问题已成功解决！');
    console.log('✅ 没有发现任何可能导致布局问题的大图标');
    console.log('✅ 所有图标容器都已优化到合理尺寸');
    console.log('✅ 响应式适配已正确实现');
    console.log('✅ 页面布局现在完全正常');
} else {
    console.log(`⚠️ 发现 ${totalIssues} 个潜在问题需要进一步处理`);
    console.log('建议检查上述标记的问题并进行相应优化');
}

// 生成优化建议
console.log('\n💡 优化建议:');
console.log('1. 定期运行此脚本验证图标尺寸');
console.log('2. 新增图标时确保容器尺寸不超过40px');
console.log('3. 字体尺寸保持在32px以下');
console.log('4. 使用CSS变量统一管理图标尺寸');
console.log('5. 在不同设备上测试响应式效果');

console.log('\n🚀 下一步操作:');
console.log('1. 刷新浏览器查看优化效果');
console.log('2. 在移动设备上测试响应式布局');
console.log('3. 检查页面加载性能是否有改善');
console.log('4. 确认用户体验是否更加流畅');

// 输出统计信息
const stats = {
    totalChecks: checks.length,
    passedChecks: checks.filter(c => results.find(r => r.name === c.name && r.issues === 0)).length,
    totalIssues: totalIssues,
    optimizedContainers: optimizedContainers.length
};

console.log('\n📊 统计信息:');
console.log(`检查项目: ${stats.totalChecks}`);
console.log(`通过检查: ${stats.passedChecks}`);
console.log(`发现问题: ${stats.totalIssues}`);
console.log(`优化容器: ${stats.optimizedContainers}`);

// 如果没有问题，输出成功信息
if (totalIssues === 0) {
    console.log('\n🎊 大图标清理任务完成！');
    console.log('HomePage.vue 现在已经完全优化，不再有任何大图标布局问题。');
}

process.exit(totalIssues > 0 ? 1 : 0);
