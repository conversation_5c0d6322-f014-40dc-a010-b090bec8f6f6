/**
 * AI响应功能快速测试脚本
 * 在浏览器控制台中运行此脚本来测试AI响应功能
 */

// 测试配置
const TEST_CONFIG = {
  testAnswers: [
    '我在机器学习项目中使用了TensorFlow框架，实现了一个深度神经网络模型。',
    '我不太清楚这个问题',
    '我有3年的Python开发经验，熟悉Django和Flask框架。',
    '不知道',
    '我参与过一个推荐系统项目，使用协同过滤算法实现个性化推荐。'
  ],
  domains: ['ai', 'bigdata', 'iot'],
  sessionId: 'test_session_' + Date.now()
}

// 测试结果存储
let testResults = []

/**
 * 运行AI响应功能测试
 */
async function runAIResponseTest() {
  console.log('🚀 开始AI响应功能测试...')
  console.log('=' .repeat(60))
  
  try {
    // 导入服务
    const { default: enhancedIflytekSparkService } = await import('./src/services/enhancedIflytekSparkService.js')
    
    // 测试1: 会话初始化
    console.log('🧪 测试1: 会话初始化')
    await testSessionInitialization(enhancedIflytekSparkService)
    
    // 测试2: 文本分析
    console.log('\n🧪 测试2: 文本分析')
    await testTextAnalysis(enhancedIflytekSparkService)
    
    // 测试3: AI提示生成
    console.log('\n🧪 测试3: AI提示生成')
    await testHintGeneration(enhancedIflytekSparkService)
    
    // 测试4: 完整对话流程
    console.log('\n🧪 测试4: 完整对话流程')
    await testCompleteConversation(enhancedIflytekSparkService)
    
    // 生成测试报告
    generateTestReport()
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
}

/**
 * 测试会话初始化
 */
async function testSessionInitialization(service) {
  const startTime = Date.now()
  
  try {
    const candidateProfile = {
      name: '测试候选人',
      position: 'AI工程师',
      experience: '3年'
    }
    
    const session = await service.initializeInterviewSession(candidateProfile, 'technical')
    const duration = Date.now() - startTime
    
    testResults.push({
      test: '会话初始化',
      status: 'success',
      duration: duration,
      details: `会话ID: ${session.sessionId}`
    })
    
    console.log(`✅ 会话初始化成功 - 耗时: ${duration}ms`)
    console.log(`📋 会话ID: ${session.sessionId}`)
    
    TEST_CONFIG.sessionId = session.sessionId
    
  } catch (error) {
    const duration = Date.now() - startTime
    testResults.push({
      test: '会话初始化',
      status: 'error',
      duration: duration,
      error: error.message
    })
    console.error(`❌ 会话初始化失败 - 耗时: ${duration}ms`)
  }
}

/**
 * 测试文本分析
 */
async function testTextAnalysis(service) {
  for (let i = 0; i < TEST_CONFIG.testAnswers.length; i++) {
    const answer = TEST_CONFIG.testAnswers[i]
    const startTime = Date.now()
    
    try {
      console.log(`\n📝 测试回答 ${i + 1}: ${answer.substring(0, 50)}...`)
      
      const analysisData = {
        text: answer,
        domain: TEST_CONFIG.domains[i % TEST_CONFIG.domains.length]
      }
      
      const analysis = await service.analyzeTextPrimaryInput(TEST_CONFIG.sessionId, analysisData)
      const duration = Date.now() - startTime
      
      testResults.push({
        test: `文本分析-${i + 1}`,
        status: 'success',
        duration: duration,
        details: `评分: ${analysis.overallScore}, 关键词: ${analysis.textAnalysis?.keywords?.slice(0, 3).join(', ') || '无'}`
      })
      
      console.log(`✅ 分析完成 - 耗时: ${duration}ms`)
      console.log(`📊 评分: ${analysis.overallScore}`)
      console.log(`🔑 关键词: ${analysis.textAnalysis?.keywords?.slice(0, 3).join(', ') || '无'}`)
      
      // 模拟AI回复生成
      await simulateAIResponse(analysis, answer)
      
    } catch (error) {
      const duration = Date.now() - startTime
      testResults.push({
        test: `文本分析-${i + 1}`,
        status: 'error',
        duration: duration,
        error: error.message
      })
      console.error(`❌ 分析失败 - 耗时: ${duration}ms, 错误: ${error.message}`)
    }
    
    // 添加延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

/**
 * 模拟AI回复生成
 */
async function simulateAIResponse(analysis, userAnswer) {
  const score = analysis.overallScore || 0
  let aiResponse = ''
  
  if (score >= 85) {
    aiResponse = `很棒的回答！您展现了扎实的技术功底。让我们深入探讨一下相关的技术细节。`
  } else if (score >= 70) {
    aiResponse = `您的回答有一定的技术深度，不过还有提升空间。能否详细说明一下具体的实现方案？`
  } else if (userAnswer.includes('不知道') || userAnswer.includes('不清楚')) {
    aiResponse = `没关系，这是一个学习的机会。让我为您提供一些思路和指导。`
  } else {
    aiResponse = `感谢您的回答。我看到您对这个问题有一定的理解。您能否举个具体的例子来说明？`
  }
  
  console.log(`🤖 AI回复: ${aiResponse}`)
}

/**
 * 测试AI提示生成
 */
async function testHintGeneration(service) {
  const testContexts = [
    {
      question: '请介绍您对机器学习的理解',
      candidateResponse: '我不太清楚',
      questionNumber: 1
    },
    {
      question: '描述一下您的项目经验',
      candidateResponse: '',
      questionNumber: 2
    }
  ]
  
  for (let i = 0; i < testContexts.length; i++) {
    const context = testContexts[i]
    const startTime = Date.now()
    
    try {
      console.log(`\n💡 测试提示生成 ${i + 1}`)
      
      const hint = await service.generateRealTimeHint(TEST_CONFIG.sessionId, context)
      const duration = Date.now() - startTime
      
      testResults.push({
        test: `AI提示-${i + 1}`,
        status: 'success',
        duration: duration,
        details: hint.hint?.substring(0, 100) + '...'
      })
      
      console.log(`✅ 提示生成成功 - 耗时: ${duration}ms`)
      console.log(`💡 提示内容: ${hint.hint}`)
      
    } catch (error) {
      const duration = Date.now() - startTime
      testResults.push({
        test: `AI提示-${i + 1}`,
        status: 'error',
        duration: duration,
        error: error.message
      })
      console.error(`❌ 提示生成失败 - 耗时: ${duration}ms`)
    }
    
    await new Promise(resolve => setTimeout(resolve, 500))
  }
}

/**
 * 测试完整对话流程
 */
async function testCompleteConversation(service) {
  console.log('🔄 模拟完整面试对话流程...')
  
  try {
    // 1. 生成问题
    const questionContext = {
      domain: 'ai',
      difficulty: 'medium',
      questionNumber: 1
    }
    
    const question = await service.generateInterviewQuestion(TEST_CONFIG.sessionId, questionContext)
    console.log(`❓ 生成问题: ${question.question?.substring(0, 100)}...`)
    
    // 2. 模拟用户回答
    const userAnswer = TEST_CONFIG.testAnswers[0]
    console.log(`👤 用户回答: ${userAnswer}`)
    
    // 3. 分析回答
    const analysis = await service.analyzeTextPrimaryInput(TEST_CONFIG.sessionId, {
      text: userAnswer,
      domain: 'ai'
    })
    console.log(`📊 分析结果: 评分 ${analysis.overallScore}`)
    
    // 4. 生成AI回复
    await simulateAIResponse(analysis, userAnswer)
    
    console.log('✅ 完整对话流程测试成功')
    
  } catch (error) {
    console.error('❌ 完整对话流程测试失败:', error.message)
  }
}

/**
 * 生成测试报告
 */
function generateTestReport() {
  console.log('\n📋 AI响应功能测试报告')
  console.log('=' .repeat(60))
  
  const successCount = testResults.filter(r => r.status === 'success').length
  const errorCount = testResults.filter(r => r.status === 'error').length
  const totalTests = testResults.length
  
  console.log(`📊 测试统计:`)
  console.log(`   总测试数: ${totalTests}`)
  console.log(`   成功: ${successCount} (${Math.round(successCount/totalTests*100)}%)`)
  console.log(`   失败: ${errorCount} (${Math.round(errorCount/totalTests*100)}%)`)
  
  console.log('\n⏱️ 性能分析:')
  testResults.forEach(result => {
    const status = result.status === 'success' ? '✅' : '❌'
    console.log(`   ${status} ${result.test}: ${result.duration}ms`)
    if (result.details) {
      console.log(`      ${result.details}`)
    }
  })
  
  if (errorCount > 0) {
    console.log('\n🚨 错误详情:')
    testResults
      .filter(r => r.status === 'error')
      .forEach(result => {
        console.log(`   ❌ ${result.test}: ${result.error}`)
      })
  }
  
  console.log('\n💡 测试结论:')
  if (errorCount === 0) {
    console.log('   🎉 所有测试通过！AI响应功能正常工作。')
  } else if (errorCount < totalTests / 2) {
    console.log('   ⚠️ 部分测试失败，但核心功能可用。建议检查失败项目。')
  } else {
    console.log('   🚨 多数测试失败，需要检查API配置和网络连接。')
  }
  
  console.log('=' .repeat(60))
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.runAIResponseTest = runAIResponseTest
  window.TEST_CONFIG = TEST_CONFIG
  window.testResults = testResults
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAIResponseTest,
    TEST_CONFIG,
    testResults
  }
}

console.log('🔧 AI响应测试脚本已加载')
console.log('💡 在浏览器控制台中运行: runAIResponseTest()')
