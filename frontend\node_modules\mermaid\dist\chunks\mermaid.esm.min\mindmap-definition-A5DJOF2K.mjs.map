{"version": 3, "sources": ["../../../../../node_modules/.pnpm/layout-base@1.0.2/node_modules/layout-base/layout-base.js", "../../../../../node_modules/.pnpm/cose-base@1.0.3/node_modules/cose-base/cose-base.js", "../../../../../node_modules/.pnpm/cytoscape-cose-bilkent@4.1.0_cytoscape@3.31.0/node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js", "../../../src/diagrams/mindmap/parser/mindmap.jison", "../../../src/diagrams/mindmap/mindmapDb.ts", "../../../src/diagrams/mindmap/mindmapRenderer.ts", "../../../src/diagrams/mindmap/svgDraw.ts", "../../../src/diagrams/mindmap/styles.ts", "../../../src/diagrams/mindmap/mindmap-definition.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"layoutBase\"] = factory();\n\telse\n\t\troot[\"layoutBase\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 26);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LayoutConstants() {}\n\n/**\r\n * Layout Quality: 0:draft, 1:default, 2:proof\r\n */\nLayoutConstants.QUALITY = 1;\n\n/**\r\n * Default parameters\r\n */\nLayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\nLayoutConstants.DEFAULT_INCREMENTAL = false;\nLayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\nLayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\nLayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\nLayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n// -----------------------------------------------------------------------------\n// Section: General other constants\n// -----------------------------------------------------------------------------\n/*\r\n * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n * assume margins on all four sides to be uniform.\r\n */\nLayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n/*\r\n * Whether to consider labels in node dimensions or not\r\n */\nLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n/*\r\n * Empty compound node size. When a compound node is empty, its both\r\n * dimensions should be of this value.\r\n */\nLayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n/*\r\n * Minimum length that an edge should take during layout\r\n */\nLayoutConstants.MIN_EDGE_LENGTH = 1;\n\n/*\r\n * World boundaries that layout operates on\r\n */\nLayoutConstants.WORLD_BOUNDARY = 1000000;\n\n/*\r\n * World boundaries that random positioning can be performed with\r\n */\nLayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n/*\r\n * Coordinates of the world center\r\n */\nLayoutConstants.WORLD_CENTER_X = 1200;\nLayoutConstants.WORLD_CENTER_Y = 900;\n\nmodule.exports = LayoutConstants;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction LEdge(source, target, vEdge) {\n  LGraphObject.call(this, vEdge);\n\n  this.isOverlapingSourceAndTarget = false;\n  this.vGraphObject = vEdge;\n  this.bendpoints = [];\n  this.source = source;\n  this.target = target;\n}\n\nLEdge.prototype = Object.create(LGraphObject.prototype);\n\nfor (var prop in LGraphObject) {\n  LEdge[prop] = LGraphObject[prop];\n}\n\nLEdge.prototype.getSource = function () {\n  return this.source;\n};\n\nLEdge.prototype.getTarget = function () {\n  return this.target;\n};\n\nLEdge.prototype.isInterGraph = function () {\n  return this.isInterGraph;\n};\n\nLEdge.prototype.getLength = function () {\n  return this.length;\n};\n\nLEdge.prototype.isOverlapingSourceAndTarget = function () {\n  return this.isOverlapingSourceAndTarget;\n};\n\nLEdge.prototype.getBendpoints = function () {\n  return this.bendpoints;\n};\n\nLEdge.prototype.getLca = function () {\n  return this.lca;\n};\n\nLEdge.prototype.getSourceInLca = function () {\n  return this.sourceInLca;\n};\n\nLEdge.prototype.getTargetInLca = function () {\n  return this.targetInLca;\n};\n\nLEdge.prototype.getOtherEnd = function (node) {\n  if (this.source === node) {\n    return this.target;\n  } else if (this.target === node) {\n    return this.source;\n  } else {\n    throw \"Node is not incident with this edge\";\n  }\n};\n\nLEdge.prototype.getOtherEndInGraph = function (node, graph) {\n  var otherEnd = this.getOtherEnd(node);\n  var root = graph.getGraphManager().getRoot();\n\n  while (true) {\n    if (otherEnd.getOwner() == graph) {\n      return otherEnd;\n    }\n\n    if (otherEnd.getOwner() == root) {\n      break;\n    }\n\n    otherEnd = otherEnd.getOwner().getParent();\n  }\n\n  return null;\n};\n\nLEdge.prototype.updateLength = function () {\n  var clipPointCoordinates = new Array(4);\n\n  this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n\n  if (!this.isOverlapingSourceAndTarget) {\n    this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n    this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n\n    if (Math.abs(this.lengthX) < 1.0) {\n      this.lengthX = IMath.sign(this.lengthX);\n    }\n\n    if (Math.abs(this.lengthY) < 1.0) {\n      this.lengthY = IMath.sign(this.lengthY);\n    }\n\n    this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n  }\n};\n\nLEdge.prototype.updateLengthSimple = function () {\n  this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n  this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n\n  if (Math.abs(this.lengthX) < 1.0) {\n    this.lengthX = IMath.sign(this.lengthX);\n  }\n\n  if (Math.abs(this.lengthY) < 1.0) {\n    this.lengthY = IMath.sign(this.lengthY);\n  }\n\n  this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n};\n\nmodule.exports = LEdge;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LGraphObject(vGraphObject) {\n  this.vGraphObject = vGraphObject;\n}\n\nmodule.exports = LGraphObject;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar RectangleD = __webpack_require__(13);\nvar LayoutConstants = __webpack_require__(0);\nvar RandomSeed = __webpack_require__(16);\nvar PointD = __webpack_require__(4);\n\nfunction LNode(gm, loc, size, vNode) {\n  //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n  if (size == null && vNode == null) {\n    vNode = loc;\n  }\n\n  LGraphObject.call(this, vNode);\n\n  //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n  if (gm.graphManager != null) gm = gm.graphManager;\n\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.inclusionTreeDepth = Integer.MAX_VALUE;\n  this.vGraphObject = vNode;\n  this.edges = [];\n  this.graphManager = gm;\n\n  if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n}\n\nLNode.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LNode[prop] = LGraphObject[prop];\n}\n\nLNode.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLNode.prototype.getChild = function () {\n  return this.child;\n};\n\nLNode.prototype.getOwner = function () {\n  //  if (this.owner != null) {\n  //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n  //      throw \"assert failed\";\n  //    }\n  //  }\n\n  return this.owner;\n};\n\nLNode.prototype.getWidth = function () {\n  return this.rect.width;\n};\n\nLNode.prototype.setWidth = function (width) {\n  this.rect.width = width;\n};\n\nLNode.prototype.getHeight = function () {\n  return this.rect.height;\n};\n\nLNode.prototype.setHeight = function (height) {\n  this.rect.height = height;\n};\n\nLNode.prototype.getCenterX = function () {\n  return this.rect.x + this.rect.width / 2;\n};\n\nLNode.prototype.getCenterY = function () {\n  return this.rect.y + this.rect.height / 2;\n};\n\nLNode.prototype.getCenter = function () {\n  return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n};\n\nLNode.prototype.getLocation = function () {\n  return new PointD(this.rect.x, this.rect.y);\n};\n\nLNode.prototype.getRect = function () {\n  return this.rect;\n};\n\nLNode.prototype.getDiagonal = function () {\n  return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n};\n\n/**\n * This method returns half the diagonal length of this node.\n */\nLNode.prototype.getHalfTheDiagonal = function () {\n  return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n};\n\nLNode.prototype.setRect = function (upperLeft, dimension) {\n  this.rect.x = upperLeft.x;\n  this.rect.y = upperLeft.y;\n  this.rect.width = dimension.width;\n  this.rect.height = dimension.height;\n};\n\nLNode.prototype.setCenter = function (cx, cy) {\n  this.rect.x = cx - this.rect.width / 2;\n  this.rect.y = cy - this.rect.height / 2;\n};\n\nLNode.prototype.setLocation = function (x, y) {\n  this.rect.x = x;\n  this.rect.y = y;\n};\n\nLNode.prototype.moveBy = function (dx, dy) {\n  this.rect.x += dx;\n  this.rect.y += dy;\n};\n\nLNode.prototype.getEdgeListToNode = function (to) {\n  var edgeList = [];\n  var edge;\n  var self = this;\n\n  self.edges.forEach(function (edge) {\n\n    if (edge.target == to) {\n      if (edge.source != self) throw \"Incorrect edge source!\";\n\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getEdgesBetween = function (other) {\n  var edgeList = [];\n  var edge;\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n\n    if (edge.target == other || edge.source == other) {\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getNeighborsList = function () {\n  var neighbors = new Set();\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (edge.source == self) {\n      neighbors.add(edge.target);\n    } else {\n      if (edge.target != self) {\n        throw \"Incorrect incidency!\";\n      }\n\n      neighbors.add(edge.source);\n    }\n  });\n\n  return neighbors;\n};\n\nLNode.prototype.withChildren = function () {\n  var withNeighborsList = new Set();\n  var childNode;\n  var children;\n\n  withNeighborsList.add(this);\n\n  if (this.child != null) {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n      children = childNode.withChildren();\n      children.forEach(function (node) {\n        withNeighborsList.add(node);\n      });\n    }\n  }\n\n  return withNeighborsList;\n};\n\nLNode.prototype.getNoOfChildren = function () {\n  var noOfChildren = 0;\n  var childNode;\n\n  if (this.child == null) {\n    noOfChildren = 1;\n  } else {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n\n      noOfChildren += childNode.getNoOfChildren();\n    }\n  }\n\n  if (noOfChildren == 0) {\n    noOfChildren = 1;\n  }\n  return noOfChildren;\n};\n\nLNode.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLNode.prototype.calcEstimatedSize = function () {\n  if (this.child == null) {\n    return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n  } else {\n    this.estimatedSize = this.child.calcEstimatedSize();\n    this.rect.width = this.estimatedSize;\n    this.rect.height = this.estimatedSize;\n\n    return this.estimatedSize;\n  }\n};\n\nLNode.prototype.scatter = function () {\n  var randomCenterX;\n  var randomCenterY;\n\n  var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n\n  var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n\n  this.rect.x = randomCenterX;\n  this.rect.y = randomCenterY;\n};\n\nLNode.prototype.updateBounds = function () {\n  if (this.getChild() == null) {\n    throw \"assert failed\";\n  }\n  if (this.getChild().getNodes().length != 0) {\n    // wrap the children nodes by re-arranging the boundaries\n    var childGraph = this.getChild();\n    childGraph.updateBounds(true);\n\n    this.rect.x = childGraph.getLeft();\n    this.rect.y = childGraph.getTop();\n\n    this.setWidth(childGraph.getRight() - childGraph.getLeft());\n    this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n    // Update compound bounds considering its label properties    \n    if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = childGraph.getRight() - childGraph.getLeft();\n      var height = childGraph.getBottom() - childGraph.getTop();\n\n      if (this.labelWidth > width) {\n        this.rect.x -= (this.labelWidth - width) / 2;\n        this.setWidth(this.labelWidth);\n      }\n\n      if (this.labelHeight > height) {\n        if (this.labelPos == \"center\") {\n          this.rect.y -= (this.labelHeight - height) / 2;\n        } else if (this.labelPos == \"top\") {\n          this.rect.y -= this.labelHeight - height;\n        }\n        this.setHeight(this.labelHeight);\n      }\n    }\n  }\n};\n\nLNode.prototype.getInclusionTreeDepth = function () {\n  if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.inclusionTreeDepth;\n};\n\nLNode.prototype.transform = function (trans) {\n  var left = this.rect.x;\n\n  if (left > LayoutConstants.WORLD_BOUNDARY) {\n    left = LayoutConstants.WORLD_BOUNDARY;\n  } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n    left = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var top = this.rect.y;\n\n  if (top > LayoutConstants.WORLD_BOUNDARY) {\n    top = LayoutConstants.WORLD_BOUNDARY;\n  } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n    top = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var leftTop = new PointD(left, top);\n  var vLeftTop = trans.inverseTransformPoint(leftTop);\n\n  this.setLocation(vLeftTop.x, vLeftTop.y);\n};\n\nLNode.prototype.getLeft = function () {\n  return this.rect.x;\n};\n\nLNode.prototype.getRight = function () {\n  return this.rect.x + this.rect.width;\n};\n\nLNode.prototype.getTop = function () {\n  return this.rect.y;\n};\n\nLNode.prototype.getBottom = function () {\n  return this.rect.y + this.rect.height;\n};\n\nLNode.prototype.getParent = function () {\n  if (this.owner == null) {\n    return null;\n  }\n\n  return this.owner.getParent();\n};\n\nmodule.exports = LNode;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction PointD(x, y) {\n  if (x == null && y == null) {\n    this.x = 0;\n    this.y = 0;\n  } else {\n    this.x = x;\n    this.y = y;\n  }\n}\n\nPointD.prototype.getX = function () {\n  return this.x;\n};\n\nPointD.prototype.getY = function () {\n  return this.y;\n};\n\nPointD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nPointD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nPointD.prototype.getDifference = function (pt) {\n  return new DimensionD(this.x - pt.x, this.y - pt.y);\n};\n\nPointD.prototype.getCopy = function () {\n  return new PointD(this.x, this.y);\n};\n\nPointD.prototype.translate = function (dim) {\n  this.x += dim.width;\n  this.y += dim.height;\n  return this;\n};\n\nmodule.exports = PointD;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(6);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar RectangleD = __webpack_require__(13);\nvar Point = __webpack_require__(12);\nvar LinkedList = __webpack_require__(11);\n\nfunction LGraph(parent, obj2, vGraph) {\n  LGraphObject.call(this, vGraph);\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n  this.edges = [];\n  this.nodes = [];\n  this.isConnected = false;\n  this.parent = parent;\n\n  if (obj2 != null && obj2 instanceof LGraphManager) {\n    this.graphManager = obj2;\n  } else if (obj2 != null && obj2 instanceof Layout) {\n    this.graphManager = obj2.graphManager;\n  }\n}\n\nLGraph.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LGraph[prop] = LGraphObject[prop];\n}\n\nLGraph.prototype.getNodes = function () {\n  return this.nodes;\n};\n\nLGraph.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLGraph.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLGraph.prototype.getParent = function () {\n  return this.parent;\n};\n\nLGraph.prototype.getLeft = function () {\n  return this.left;\n};\n\nLGraph.prototype.getRight = function () {\n  return this.right;\n};\n\nLGraph.prototype.getTop = function () {\n  return this.top;\n};\n\nLGraph.prototype.getBottom = function () {\n  return this.bottom;\n};\n\nLGraph.prototype.isConnected = function () {\n  return this.isConnected;\n};\n\nLGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n  if (sourceNode == null && targetNode == null) {\n    var newNode = obj1;\n    if (this.graphManager == null) {\n      throw \"Graph has no graph mgr!\";\n    }\n    if (this.getNodes().indexOf(newNode) > -1) {\n      throw \"Node already in graph!\";\n    }\n    newNode.owner = this;\n    this.getNodes().push(newNode);\n\n    return newNode;\n  } else {\n    var newEdge = obj1;\n    if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n      throw \"Source or target not in graph!\";\n    }\n\n    if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n      throw \"Both owners must be this graph!\";\n    }\n\n    if (sourceNode.owner != targetNode.owner) {\n      return null;\n    }\n\n    // set source and target\n    newEdge.source = sourceNode;\n    newEdge.target = targetNode;\n\n    // set as intra-graph edge\n    newEdge.isInterGraph = false;\n\n    // add to graph edge list\n    this.getEdges().push(newEdge);\n\n    // add to incidency lists\n    sourceNode.edges.push(newEdge);\n\n    if (targetNode != sourceNode) {\n      targetNode.edges.push(newEdge);\n    }\n\n    return newEdge;\n  }\n};\n\nLGraph.prototype.remove = function (obj) {\n  var node = obj;\n  if (obj instanceof LNode) {\n    if (node == null) {\n      throw \"Node is null!\";\n    }\n    if (!(node.owner != null && node.owner == this)) {\n      throw \"Owner graph is invalid!\";\n    }\n    if (this.graphManager == null) {\n      throw \"Owner graph manager is invalid!\";\n    }\n    // remove incident edges first (make a copy to do it safely)\n    var edgesToBeRemoved = node.edges.slice();\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n\n      if (edge.isInterGraph) {\n        this.graphManager.remove(edge);\n      } else {\n        edge.source.owner.remove(edge);\n      }\n    }\n\n    // now the node itself\n    var index = this.nodes.indexOf(node);\n    if (index == -1) {\n      throw \"Node not in owner node list!\";\n    }\n\n    this.nodes.splice(index, 1);\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n    if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n      throw \"Source and/or target owner is invalid!\";\n    }\n\n    var sourceIndex = edge.source.edges.indexOf(edge);\n    var targetIndex = edge.target.edges.indexOf(edge);\n    if (!(sourceIndex > -1 && targetIndex > -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    edge.source.edges.splice(sourceIndex, 1);\n\n    if (edge.target != edge.source) {\n      edge.target.edges.splice(targetIndex, 1);\n    }\n\n    var index = edge.source.owner.getEdges().indexOf(edge);\n    if (index == -1) {\n      throw \"Not in owner's edge list!\";\n    }\n\n    edge.source.owner.getEdges().splice(index, 1);\n  }\n};\n\nLGraph.prototype.updateLeftTop = function () {\n  var top = Integer.MAX_VALUE;\n  var left = Integer.MAX_VALUE;\n  var nodeTop;\n  var nodeLeft;\n  var margin;\n\n  var nodes = this.getNodes();\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeTop = lNode.getTop();\n    nodeLeft = lNode.getLeft();\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n  }\n\n  // Do we have any nodes in this graph?\n  if (top == Integer.MAX_VALUE) {\n    return null;\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = left - margin;\n  this.top = top - margin;\n\n  // Apply the margins and return the result\n  return new Point(this.left, this.top);\n};\n\nLGraph.prototype.updateBounds = function (recursive) {\n  // calculate bounds\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n  var margin;\n\n  var nodes = this.nodes;\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n\n    if (recursive && lNode.child != null) {\n      lNode.updateBounds();\n    }\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n  if (left == Integer.MAX_VALUE) {\n    this.left = this.parent.getLeft();\n    this.right = this.parent.getRight();\n    this.top = this.parent.getTop();\n    this.bottom = this.parent.getBottom();\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = boundingRect.x - margin;\n  this.right = boundingRect.x + boundingRect.width + margin;\n  this.top = boundingRect.y - margin;\n  this.bottom = boundingRect.y + boundingRect.height + margin;\n};\n\nLGraph.calculateBounds = function (nodes) {\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n\n  return boundingRect;\n};\n\nLGraph.prototype.getInclusionTreeDepth = function () {\n  if (this == this.graphManager.getRoot()) {\n    return 1;\n  } else {\n    return this.parent.getInclusionTreeDepth();\n  }\n};\n\nLGraph.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLGraph.prototype.calcEstimatedSize = function () {\n  var size = 0;\n  var nodes = this.nodes;\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    size += lNode.calcEstimatedSize();\n  }\n\n  if (size == 0) {\n    this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n  } else {\n    this.estimatedSize = size / Math.sqrt(this.nodes.length);\n  }\n\n  return this.estimatedSize;\n};\n\nLGraph.prototype.updateConnected = function () {\n  var self = this;\n  if (this.nodes.length == 0) {\n    this.isConnected = true;\n    return;\n  }\n\n  var queue = new LinkedList();\n  var visited = new Set();\n  var currentNode = this.nodes[0];\n  var neighborEdges;\n  var currentNeighbor;\n  var childrenOfNode = currentNode.withChildren();\n  childrenOfNode.forEach(function (node) {\n    queue.push(node);\n    visited.add(node);\n  });\n\n  while (queue.length !== 0) {\n    currentNode = queue.shift();\n\n    // Traverse all neighbors of this node\n    neighborEdges = currentNode.getEdges();\n    var size = neighborEdges.length;\n    for (var i = 0; i < size; i++) {\n      var neighborEdge = neighborEdges[i];\n      currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n      // Add unvisited neighbors to the list to visit\n      if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n        var childrenOfNeighbor = currentNeighbor.withChildren();\n\n        childrenOfNeighbor.forEach(function (node) {\n          queue.push(node);\n          visited.add(node);\n        });\n      }\n    }\n  }\n\n  this.isConnected = false;\n\n  if (visited.size >= this.nodes.length) {\n    var noOfVisitedInThisGraph = 0;\n\n    visited.forEach(function (visitedNode) {\n      if (visitedNode.owner == self) {\n        noOfVisitedInThisGraph++;\n      }\n    });\n\n    if (noOfVisitedInThisGraph == this.nodes.length) {\n      this.isConnected = true;\n    }\n  }\n};\n\nmodule.exports = LGraph;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraph;\nvar LEdge = __webpack_require__(1);\n\nfunction LGraphManager(layout) {\n  LGraph = __webpack_require__(5); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n  this.layout = layout;\n\n  this.graphs = [];\n  this.edges = [];\n}\n\nLGraphManager.prototype.addRoot = function () {\n  var ngraph = this.layout.newGraph();\n  var nnode = this.layout.newNode(null);\n  var root = this.add(ngraph, nnode);\n  this.setRootGraph(root);\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n  //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n  if (newEdge == null && sourceNode == null && targetNode == null) {\n    if (newGraph == null) {\n      throw \"Graph is null!\";\n    }\n    if (parentNode == null) {\n      throw \"Parent node is null!\";\n    }\n    if (this.graphs.indexOf(newGraph) > -1) {\n      throw \"Graph already in this graph mgr!\";\n    }\n\n    this.graphs.push(newGraph);\n\n    if (newGraph.parent != null) {\n      throw \"Already has a parent!\";\n    }\n    if (parentNode.child != null) {\n      throw \"Already has a child!\";\n    }\n\n    newGraph.parent = parentNode;\n    parentNode.child = newGraph;\n\n    return newGraph;\n  } else {\n    //change the order of the parameters\n    targetNode = newEdge;\n    sourceNode = parentNode;\n    newEdge = newGraph;\n    var sourceGraph = sourceNode.getOwner();\n    var targetGraph = targetNode.getOwner();\n\n    if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n      throw \"Source not in this graph mgr!\";\n    }\n    if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n      throw \"Target not in this graph mgr!\";\n    }\n\n    if (sourceGraph == targetGraph) {\n      newEdge.isInterGraph = false;\n      return sourceGraph.add(newEdge, sourceNode, targetNode);\n    } else {\n      newEdge.isInterGraph = true;\n\n      // set source and target\n      newEdge.source = sourceNode;\n      newEdge.target = targetNode;\n\n      // add edge to inter-graph edge list\n      if (this.edges.indexOf(newEdge) > -1) {\n        throw \"Edge already in inter-graph edge list!\";\n      }\n\n      this.edges.push(newEdge);\n\n      // add edge to source and target incidency lists\n      if (!(newEdge.source != null && newEdge.target != null)) {\n        throw \"Edge source and/or target is null!\";\n      }\n\n      if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n        throw \"Edge already in source and/or target incidency list!\";\n      }\n\n      newEdge.source.edges.push(newEdge);\n      newEdge.target.edges.push(newEdge);\n\n      return newEdge;\n    }\n  }\n};\n\nLGraphManager.prototype.remove = function (lObj) {\n  if (lObj instanceof LGraph) {\n    var graph = lObj;\n    if (graph.getGraphManager() != this) {\n      throw \"Graph not in this graph mgr\";\n    }\n    if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n      throw \"Invalid parent node!\";\n    }\n\n    // first the edges (make a copy to do it safely)\n    var edgesToBeRemoved = [];\n\n    edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n      graph.remove(edge);\n    }\n\n    // then the nodes (make a copy to do it safely)\n    var nodesToBeRemoved = [];\n\n    nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n\n    var node;\n    s = nodesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      node = nodesToBeRemoved[i];\n      graph.remove(node);\n    }\n\n    // check if graph is the root\n    if (graph == this.rootGraph) {\n      this.setRootGraph(null);\n    }\n\n    // now remove the graph itself\n    var index = this.graphs.indexOf(graph);\n    this.graphs.splice(index, 1);\n\n    // also reset the parent of the graph\n    graph.parent = null;\n  } else if (lObj instanceof LEdge) {\n    edge = lObj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!edge.isInterGraph) {\n      throw \"Not an inter-graph edge!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n\n    // remove edge from source and target nodes' incidency lists\n\n    if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    var index = edge.source.edges.indexOf(edge);\n    edge.source.edges.splice(index, 1);\n    index = edge.target.edges.indexOf(edge);\n    edge.target.edges.splice(index, 1);\n\n    // remove edge from owner graph manager's inter-graph edge list\n\n    if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n      throw \"Edge owner graph or owner graph manager is null!\";\n    }\n    if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n      throw \"Not in owner graph manager's edge list!\";\n    }\n\n    var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n    edge.source.owner.getGraphManager().edges.splice(index, 1);\n  }\n};\n\nLGraphManager.prototype.updateBounds = function () {\n  this.rootGraph.updateBounds(true);\n};\n\nLGraphManager.prototype.getGraphs = function () {\n  return this.graphs;\n};\n\nLGraphManager.prototype.getAllNodes = function () {\n  if (this.allNodes == null) {\n    var nodeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < s; i++) {\n      nodeList = nodeList.concat(graphs[i].getNodes());\n    }\n    this.allNodes = nodeList;\n  }\n  return this.allNodes;\n};\n\nLGraphManager.prototype.resetAllNodes = function () {\n  this.allNodes = null;\n};\n\nLGraphManager.prototype.resetAllEdges = function () {\n  this.allEdges = null;\n};\n\nLGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n  this.allNodesToApplyGravitation = null;\n};\n\nLGraphManager.prototype.getAllEdges = function () {\n  if (this.allEdges == null) {\n    var edgeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < graphs.length; i++) {\n      edgeList = edgeList.concat(graphs[i].getEdges());\n    }\n\n    edgeList = edgeList.concat(this.edges);\n\n    this.allEdges = edgeList;\n  }\n  return this.allEdges;\n};\n\nLGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n  return this.allNodesToApplyGravitation;\n};\n\nLGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n  if (this.allNodesToApplyGravitation != null) {\n    throw \"assert failed\";\n  }\n\n  this.allNodesToApplyGravitation = nodeList;\n};\n\nLGraphManager.prototype.getRoot = function () {\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.setRootGraph = function (graph) {\n  if (graph.getGraphManager() != this) {\n    throw \"Root not in this graph mgr!\";\n  }\n\n  this.rootGraph = graph;\n  // root graph must have a root node associated with it for convenience\n  if (graph.parent == null) {\n    graph.parent = this.layout.newNode(\"Root node\");\n  }\n};\n\nLGraphManager.prototype.getLayout = function () {\n  return this.layout;\n};\n\nLGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n  if (!(firstNode != null && secondNode != null)) {\n    throw \"assert failed\";\n  }\n\n  if (firstNode == secondNode) {\n    return true;\n  }\n  // Is second node an ancestor of the first one?\n  var ownerGraph = firstNode.getOwner();\n  var parentNode;\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == secondNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n  // Is first node an ancestor of the second one?\n  ownerGraph = secondNode.getOwner();\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == firstNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n\n  return false;\n};\n\nLGraphManager.prototype.calcLowestCommonAncestors = function () {\n  var edge;\n  var sourceNode;\n  var targetNode;\n  var sourceAncestorGraph;\n  var targetAncestorGraph;\n\n  var edges = this.getAllEdges();\n  var s = edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = edges[i];\n\n    sourceNode = edge.source;\n    targetNode = edge.target;\n    edge.lca = null;\n    edge.sourceInLca = sourceNode;\n    edge.targetInLca = targetNode;\n\n    if (sourceNode == targetNode) {\n      edge.lca = sourceNode.getOwner();\n      continue;\n    }\n\n    sourceAncestorGraph = sourceNode.getOwner();\n\n    while (edge.lca == null) {\n      edge.targetInLca = targetNode;\n      targetAncestorGraph = targetNode.getOwner();\n\n      while (edge.lca == null) {\n        if (targetAncestorGraph == sourceAncestorGraph) {\n          edge.lca = targetAncestorGraph;\n          break;\n        }\n\n        if (targetAncestorGraph == this.rootGraph) {\n          break;\n        }\n\n        if (edge.lca != null) {\n          throw \"assert failed\";\n        }\n        edge.targetInLca = targetAncestorGraph.getParent();\n        targetAncestorGraph = edge.targetInLca.getOwner();\n      }\n\n      if (sourceAncestorGraph == this.rootGraph) {\n        break;\n      }\n\n      if (edge.lca == null) {\n        edge.sourceInLca = sourceAncestorGraph.getParent();\n        sourceAncestorGraph = edge.sourceInLca.getOwner();\n      }\n    }\n\n    if (edge.lca == null) {\n      throw \"assert failed\";\n    }\n  }\n};\n\nLGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n  if (firstNode == secondNode) {\n    return firstNode.getOwner();\n  }\n  var firstOwnerGraph = firstNode.getOwner();\n\n  do {\n    if (firstOwnerGraph == null) {\n      break;\n    }\n    var secondOwnerGraph = secondNode.getOwner();\n\n    do {\n      if (secondOwnerGraph == null) {\n        break;\n      }\n\n      if (secondOwnerGraph == firstOwnerGraph) {\n        return secondOwnerGraph;\n      }\n      secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n    } while (true);\n\n    firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n  } while (true);\n\n  return firstOwnerGraph;\n};\n\nLGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n  if (graph == null && depth == null) {\n    graph = this.rootGraph;\n    depth = 1;\n  }\n  var node;\n\n  var nodes = graph.getNodes();\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    node = nodes[i];\n    node.inclusionTreeDepth = depth;\n\n    if (node.child != null) {\n      this.calcInclusionTreeDepths(node.child, depth + 1);\n    }\n  }\n};\n\nLGraphManager.prototype.includesInvalidEdge = function () {\n  var edge;\n\n  var s = this.edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = this.edges[i];\n\n    if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nmodule.exports = LGraphManager;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __webpack_require__(0);\n\nfunction FDLayoutConstants() {}\n\n//FDLayoutConstants inherits static props in LayoutConstants\nfor (var prop in LayoutConstants) {\n  FDLayoutConstants[prop] = LayoutConstants[prop];\n}\n\nFDLayoutConstants.MAX_ITERATIONS = 2500;\n\nFDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\nFDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\nFDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\nFDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\nFDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\nFDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\nFDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\nFDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\nFDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\nFDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\nFDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\nFDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\nFDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\nFDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\nFDLayoutConstants.MIN_EDGE_LENGTH = 1;\nFDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n\nmodule.exports = FDLayoutConstants;\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/**\n * This class maintains a list of static geometry related utility methods.\n *\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar Point = __webpack_require__(12);\n\nfunction IGeometry() {}\n\n/**\n * This method calculates *half* the amount in x and y directions of the two\n * input rectangles needed to separate them keeping their respective\n * positioning, and returns the result in the input array. An input\n * separation buffer added to the amount in both directions. We assume that\n * the two rectangles do intersect.\n */\nIGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n  if (!rectA.intersects(rectB)) {\n    throw \"assert failed\";\n  }\n\n  var directions = new Array(2);\n\n  this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n\n  overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n  overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n  // update the overlapping amounts for the following cases:\n  if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n    /* Case x.1:\n    *\n    * rectA\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectB\n    */\n    overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n  } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n    /* Case x.2:\n    *\n    * rectB\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectA\n    */\n    overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n  }\n  if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n    /* Case y.1:\n     *          ________ rectA\n     *         |\n     *         |\n     *   ______|____  rectB\n     *         |    |\n     *         |    |\n     *   ______|____|\n     *         |\n     *         |\n     *         |________\n     *\n     */\n    overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n  } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n    /* Case y.2:\n    *          ________ rectB\n    *         |\n    *         |\n    *   ______|____  rectA\n    *         |    |\n    *         |    |\n    *   ______|____|\n    *         |\n    *         |\n    *         |________\n    *\n    */\n    overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n  }\n\n  // find slope of the line passes two centers\n  var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n  // if centers are overlapped\n  if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n    // assume the slope is 1 (45 degree)\n    slope = 1.0;\n  }\n\n  var moveByY = slope * overlapAmount[0];\n  var moveByX = overlapAmount[1] / slope;\n  if (overlapAmount[0] < moveByX) {\n    moveByX = overlapAmount[0];\n  } else {\n    moveByY = overlapAmount[1];\n  }\n  // return half the amount so that if each rectangle is moved by these\n  // amounts in opposite directions, overlap will be resolved\n  overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n  overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n};\n\n/**\n * This method decides the separation direction of overlapping nodes\n *\n * if directions[0] = -1, then rectA goes left\n * if directions[0] = 1,  then rectA goes right\n * if directions[1] = -1, then rectA goes up\n * if directions[1] = 1,  then rectA goes down\n */\nIGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n  if (rectA.getCenterX() < rectB.getCenterX()) {\n    directions[0] = -1;\n  } else {\n    directions[0] = 1;\n  }\n\n  if (rectA.getCenterY() < rectB.getCenterY()) {\n    directions[1] = -1;\n  } else {\n    directions[1] = 1;\n  }\n};\n\n/**\n * This method calculates the intersection (clipping) points of the two\n * input rectangles with line segment defined by the centers of these two\n * rectangles. The clipping points are saved in the input double array and\n * whether or not the two rectangles overlap is returned.\n */\nIGeometry.getIntersection2 = function (rectA, rectB, result) {\n  //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n  var p1x = rectA.getCenterX();\n  var p1y = rectA.getCenterY();\n  var p2x = rectB.getCenterX();\n  var p2y = rectB.getCenterY();\n\n  //if two rectangles intersect, then clipping points are centers\n  if (rectA.intersects(rectB)) {\n    result[0] = p1x;\n    result[1] = p1y;\n    result[2] = p2x;\n    result[3] = p2y;\n    return true;\n  }\n  //variables for rectA\n  var topLeftAx = rectA.getX();\n  var topLeftAy = rectA.getY();\n  var topRightAx = rectA.getRight();\n  var bottomLeftAx = rectA.getX();\n  var bottomLeftAy = rectA.getBottom();\n  var bottomRightAx = rectA.getRight();\n  var halfWidthA = rectA.getWidthHalf();\n  var halfHeightA = rectA.getHeightHalf();\n  //variables for rectB\n  var topLeftBx = rectB.getX();\n  var topLeftBy = rectB.getY();\n  var topRightBx = rectB.getRight();\n  var bottomLeftBx = rectB.getX();\n  var bottomLeftBy = rectB.getBottom();\n  var bottomRightBx = rectB.getRight();\n  var halfWidthB = rectB.getWidthHalf();\n  var halfHeightB = rectB.getHeightHalf();\n\n  //flag whether clipping points are found\n  var clipPointAFound = false;\n  var clipPointBFound = false;\n\n  // line is vertical\n  if (p1x === p2x) {\n    if (p1y > p2y) {\n      result[0] = p1x;\n      result[1] = topLeftAy;\n      result[2] = p2x;\n      result[3] = bottomLeftBy;\n      return false;\n    } else if (p1y < p2y) {\n      result[0] = p1x;\n      result[1] = bottomLeftAy;\n      result[2] = p2x;\n      result[3] = topLeftBy;\n      return false;\n    } else {\n      //not line, return null;\n    }\n  }\n  // line is horizontal\n  else if (p1y === p2y) {\n      if (p1x > p2x) {\n        result[0] = topLeftAx;\n        result[1] = p1y;\n        result[2] = topRightBx;\n        result[3] = p2y;\n        return false;\n      } else if (p1x < p2x) {\n        result[0] = topRightAx;\n        result[1] = p1y;\n        result[2] = topLeftBx;\n        result[3] = p2y;\n        return false;\n      } else {\n        //not valid line, return null;\n      }\n    } else {\n      //slopes of rectA's and rectB's diagonals\n      var slopeA = rectA.height / rectA.width;\n      var slopeB = rectB.height / rectB.width;\n\n      //slope of line between center of rectA and center of rectB\n      var slopePrime = (p2y - p1y) / (p2x - p1x);\n      var cardinalDirectionA = void 0;\n      var cardinalDirectionB = void 0;\n      var tempPointAx = void 0;\n      var tempPointAy = void 0;\n      var tempPointBx = void 0;\n      var tempPointBy = void 0;\n\n      //determine whether clipping point is the corner of nodeA\n      if (-slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = bottomLeftAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = topRightAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        }\n      } else if (slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = bottomRightAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        }\n      }\n\n      //determine whether clipping point is the corner of nodeB\n      if (-slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = bottomLeftBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = topRightBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        }\n      } else if (slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = topLeftBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = bottomRightBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        }\n      }\n\n      //if both clipping points are corners\n      if (clipPointAFound && clipPointBFound) {\n        return false;\n      }\n\n      //determine Cardinal Direction of rectangles\n      if (p1x > p2x) {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n        }\n      } else {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n        }\n      }\n      //calculate clipping Point if it is not found before\n      if (!clipPointAFound) {\n        switch (cardinalDirectionA) {\n          case 1:\n            tempPointAy = topLeftAy;\n            tempPointAx = p1x + -halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 2:\n            tempPointAx = bottomRightAx;\n            tempPointAy = p1y + halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 3:\n            tempPointAy = bottomLeftAy;\n            tempPointAx = p1x + halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 4:\n            tempPointAx = bottomLeftAx;\n            tempPointAy = p1y + -halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n        }\n      }\n      if (!clipPointBFound) {\n        switch (cardinalDirectionB) {\n          case 1:\n            tempPointBy = topLeftBy;\n            tempPointBx = p2x + -halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 2:\n            tempPointBx = bottomRightBx;\n            tempPointBy = p2y + halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 3:\n            tempPointBy = bottomLeftBy;\n            tempPointBx = p2x + halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 4:\n            tempPointBx = bottomLeftBx;\n            tempPointBy = p2y + -halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n        }\n      }\n    }\n  return false;\n};\n\n/**\n * This method returns in which cardinal direction does input point stays\n * 1: North\n * 2: East\n * 3: South\n * 4: West\n */\nIGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n  if (slope > slopePrime) {\n    return line;\n  } else {\n    return 1 + line % 4;\n  }\n};\n\n/**\n * This method calculates the intersection of the two lines defined by\n * point pairs (s1,s2) and (f1,f2).\n */\nIGeometry.getIntersection = function (s1, s2, f1, f2) {\n  if (f2 == null) {\n    return this.getIntersection2(s1, s2, f1);\n  }\n\n  var x1 = s1.x;\n  var y1 = s1.y;\n  var x2 = s2.x;\n  var y2 = s2.y;\n  var x3 = f1.x;\n  var y3 = f1.y;\n  var x4 = f2.x;\n  var y4 = f2.y;\n  var x = void 0,\n      y = void 0; // intersection point\n  var a1 = void 0,\n      a2 = void 0,\n      b1 = void 0,\n      b2 = void 0,\n      c1 = void 0,\n      c2 = void 0; // coefficients of line eqns.\n  var denom = void 0;\n\n  a1 = y2 - y1;\n  b1 = x1 - x2;\n  c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n  a2 = y4 - y3;\n  b2 = x3 - x4;\n  c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n  denom = a1 * b2 - a2 * b1;\n\n  if (denom === 0) {\n    return null;\n  }\n\n  x = (b1 * c2 - b2 * c1) / denom;\n  y = (a2 * c1 - a1 * c2) / denom;\n\n  return new Point(x, y);\n};\n\n/**\n * This method finds and returns the angle of the vector from the + x-axis\n * in clockwise direction (compatible w/ Java coordinate system!).\n */\nIGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n  var C_angle = void 0;\n\n  if (Cx !== Nx) {\n    C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n\n    if (Nx < Cx) {\n      C_angle += Math.PI;\n    } else if (Ny < Cy) {\n      C_angle += this.TWO_PI;\n    }\n  } else if (Ny < Cy) {\n    C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n  } else {\n    C_angle = this.HALF_PI; // 90 degrees\n  }\n\n  return C_angle;\n};\n\n/**\n * This method checks whether the given two line segments (one with point\n * p1 and p2, the other with point p3 and p4) intersect at a point other\n * than these points.\n */\nIGeometry.doIntersect = function (p1, p2, p3, p4) {\n  var a = p1.x;\n  var b = p1.y;\n  var c = p2.x;\n  var d = p2.y;\n  var p = p3.x;\n  var q = p3.y;\n  var r = p4.x;\n  var s = p4.y;\n  var det = (c - a) * (s - q) - (r - p) * (d - b);\n\n  if (det === 0) {\n    return false;\n  } else {\n    var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n    var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n    return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: Class Constants\n// -----------------------------------------------------------------------------\n/**\n * Some useful pre-calculated constants\n */\nIGeometry.HALF_PI = 0.5 * Math.PI;\nIGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\nIGeometry.TWO_PI = 2.0 * Math.PI;\nIGeometry.THREE_PI = 3.0 * Math.PI;\n\nmodule.exports = IGeometry;\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction IMath() {}\n\n/**\n * This method returns the sign of the input value.\n */\nIMath.sign = function (value) {\n  if (value > 0) {\n    return 1;\n  } else if (value < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\nIMath.floor = function (value) {\n  return value < 0 ? Math.ceil(value) : Math.floor(value);\n};\n\nIMath.ceil = function (value) {\n  return value < 0 ? Math.floor(value) : Math.ceil(value);\n};\n\nmodule.exports = IMath;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Integer() {}\n\nInteger.MAX_VALUE = 2147483647;\nInteger.MIN_VALUE = -2147483648;\n\nmodule.exports = Integer;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar nodeFrom = function nodeFrom(value) {\n  return { value: value, next: null, prev: null };\n};\n\nvar add = function add(prev, node, next, list) {\n  if (prev !== null) {\n    prev.next = node;\n  } else {\n    list.head = node;\n  }\n\n  if (next !== null) {\n    next.prev = node;\n  } else {\n    list.tail = node;\n  }\n\n  node.prev = prev;\n  node.next = next;\n\n  list.length++;\n\n  return node;\n};\n\nvar _remove = function _remove(node, list) {\n  var prev = node.prev,\n      next = node.next;\n\n\n  if (prev !== null) {\n    prev.next = next;\n  } else {\n    list.head = next;\n  }\n\n  if (next !== null) {\n    next.prev = prev;\n  } else {\n    list.tail = prev;\n  }\n\n  node.prev = node.next = null;\n\n  list.length--;\n\n  return node;\n};\n\nvar LinkedList = function () {\n  function LinkedList(vals) {\n    var _this = this;\n\n    _classCallCheck(this, LinkedList);\n\n    this.length = 0;\n    this.head = null;\n    this.tail = null;\n\n    if (vals != null) {\n      vals.forEach(function (v) {\n        return _this.push(v);\n      });\n    }\n  }\n\n  _createClass(LinkedList, [{\n    key: \"size\",\n    value: function size() {\n      return this.length;\n    }\n  }, {\n    key: \"insertBefore\",\n    value: function insertBefore(val, otherNode) {\n      return add(otherNode.prev, nodeFrom(val), otherNode, this);\n    }\n  }, {\n    key: \"insertAfter\",\n    value: function insertAfter(val, otherNode) {\n      return add(otherNode, nodeFrom(val), otherNode.next, this);\n    }\n  }, {\n    key: \"insertNodeBefore\",\n    value: function insertNodeBefore(newNode, otherNode) {\n      return add(otherNode.prev, newNode, otherNode, this);\n    }\n  }, {\n    key: \"insertNodeAfter\",\n    value: function insertNodeAfter(newNode, otherNode) {\n      return add(otherNode, newNode, otherNode.next, this);\n    }\n  }, {\n    key: \"push\",\n    value: function push(val) {\n      return add(this.tail, nodeFrom(val), null, this);\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(val) {\n      return add(null, nodeFrom(val), this.head, this);\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(node) {\n      return _remove(node, this);\n    }\n  }, {\n    key: \"pop\",\n    value: function pop() {\n      return _remove(this.tail, this).value;\n    }\n  }, {\n    key: \"popNode\",\n    value: function popNode() {\n      return _remove(this.tail, this);\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      return _remove(this.head, this).value;\n    }\n  }, {\n    key: \"shiftNode\",\n    value: function shiftNode() {\n      return _remove(this.head, this);\n    }\n  }, {\n    key: \"get_object_at\",\n    value: function get_object_at(index) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        return current.value;\n      }\n    }\n  }, {\n    key: \"set_object_at\",\n    value: function set_object_at(index, value) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        current.value = value;\n      }\n    }\n  }]);\n\n  return LinkedList;\n}();\n\nmodule.exports = LinkedList;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/*\r\n *This class is the javascript implementation of the Point.java class in jdk\r\n */\nfunction Point(x, y, p) {\n  this.x = null;\n  this.y = null;\n  if (x == null && y == null && p == null) {\n    this.x = 0;\n    this.y = 0;\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    this.x = x;\n    this.y = y;\n  } else if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.x = p.x;\n    this.y = p.y;\n  }\n}\n\nPoint.prototype.getX = function () {\n  return this.x;\n};\n\nPoint.prototype.getY = function () {\n  return this.y;\n};\n\nPoint.prototype.getLocation = function () {\n  return new Point(this.x, this.y);\n};\n\nPoint.prototype.setLocation = function (x, y, p) {\n  if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.setLocation(p.x, p.y);\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    //if both parameters are integer just move (x,y) location\n    if (parseInt(x) == x && parseInt(y) == y) {\n      this.move(x, y);\n    } else {\n      this.x = Math.floor(x + 0.5);\n      this.y = Math.floor(y + 0.5);\n    }\n  }\n};\n\nPoint.prototype.move = function (x, y) {\n  this.x = x;\n  this.y = y;\n};\n\nPoint.prototype.translate = function (dx, dy) {\n  this.x += dx;\n  this.y += dy;\n};\n\nPoint.prototype.equals = function (obj) {\n  if (obj.constructor.name == \"Point\") {\n    var pt = obj;\n    return this.x == pt.x && this.y == pt.y;\n  }\n  return this == obj;\n};\n\nPoint.prototype.toString = function () {\n  return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n};\n\nmodule.exports = Point;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RectangleD(x, y, width, height) {\n  this.x = 0;\n  this.y = 0;\n  this.width = 0;\n  this.height = 0;\n\n  if (x != null && y != null && width != null && height != null) {\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n}\n\nRectangleD.prototype.getX = function () {\n  return this.x;\n};\n\nRectangleD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nRectangleD.prototype.getY = function () {\n  return this.y;\n};\n\nRectangleD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nRectangleD.prototype.getWidth = function () {\n  return this.width;\n};\n\nRectangleD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nRectangleD.prototype.getHeight = function () {\n  return this.height;\n};\n\nRectangleD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nRectangleD.prototype.getRight = function () {\n  return this.x + this.width;\n};\n\nRectangleD.prototype.getBottom = function () {\n  return this.y + this.height;\n};\n\nRectangleD.prototype.intersects = function (a) {\n  if (this.getRight() < a.x) {\n    return false;\n  }\n\n  if (this.getBottom() < a.y) {\n    return false;\n  }\n\n  if (a.getRight() < this.x) {\n    return false;\n  }\n\n  if (a.getBottom() < this.y) {\n    return false;\n  }\n\n  return true;\n};\n\nRectangleD.prototype.getCenterX = function () {\n  return this.x + this.width / 2;\n};\n\nRectangleD.prototype.getMinX = function () {\n  return this.getX();\n};\n\nRectangleD.prototype.getMaxX = function () {\n  return this.getX() + this.width;\n};\n\nRectangleD.prototype.getCenterY = function () {\n  return this.y + this.height / 2;\n};\n\nRectangleD.prototype.getMinY = function () {\n  return this.getY();\n};\n\nRectangleD.prototype.getMaxY = function () {\n  return this.getY() + this.height;\n};\n\nRectangleD.prototype.getWidthHalf = function () {\n  return this.width / 2;\n};\n\nRectangleD.prototype.getHeightHalf = function () {\n  return this.height / 2;\n};\n\nmodule.exports = RectangleD;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction UniqueIDGeneretor() {}\n\nUniqueIDGeneretor.lastID = 0;\n\nUniqueIDGeneretor.createID = function (obj) {\n  if (UniqueIDGeneretor.isPrimitive(obj)) {\n    return obj;\n  }\n  if (obj.uniqueID != null) {\n    return obj.uniqueID;\n  }\n  obj.uniqueID = UniqueIDGeneretor.getString();\n  UniqueIDGeneretor.lastID++;\n  return obj.uniqueID;\n};\n\nUniqueIDGeneretor.getString = function (id) {\n  if (id == null) id = UniqueIDGeneretor.lastID;\n  return \"Object#\" + id + \"\";\n};\n\nUniqueIDGeneretor.isPrimitive = function (arg) {\n  var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n  return arg == null || type != \"object\" && type != \"function\";\n};\n\nmodule.exports = UniqueIDGeneretor;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(6);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar LGraph = __webpack_require__(5);\nvar PointD = __webpack_require__(4);\nvar Transform = __webpack_require__(17);\nvar Emitter = __webpack_require__(27);\n\nfunction Layout(isRemoteUse) {\n  Emitter.call(this);\n\n  //Layout Quality: 0:draft, 1:default, 2:proof\n  this.layoutQuality = LayoutConstants.QUALITY;\n  //Whether layout should create bendpoints as needed or not\n  this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  //Whether layout should be incremental or not\n  this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n  //Whether we animate from before to after layout node positions\n  this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n  //Whether we animate the layout process or not\n  this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n  //Number iterations that should be done between two successive animations\n  this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n  /**\r\n   * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n   * they are, both spring and repulsion forces between two leaf nodes can be\r\n   * calculated without the expensive clipping point calculations, resulting\r\n   * in major speed-up.\r\n   */\n  this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  /**\r\n   * This is used for creation of bendpoints by using dummy nodes and edges.\r\n   * Maps an LEdge to its dummy bendpoint path.\r\n   */\n  this.edgeToDummyNodes = new Map();\n  this.graphManager = new LGraphManager(this);\n  this.isLayoutFinished = false;\n  this.isSubLayout = false;\n  this.isRemoteUse = false;\n\n  if (isRemoteUse != null) {\n    this.isRemoteUse = isRemoteUse;\n  }\n}\n\nLayout.RANDOM_SEED = 1;\n\nLayout.prototype = Object.create(Emitter.prototype);\n\nLayout.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLayout.prototype.getAllNodes = function () {\n  return this.graphManager.getAllNodes();\n};\n\nLayout.prototype.getAllEdges = function () {\n  return this.graphManager.getAllEdges();\n};\n\nLayout.prototype.getAllNodesToApplyGravitation = function () {\n  return this.graphManager.getAllNodesToApplyGravitation();\n};\n\nLayout.prototype.newGraphManager = function () {\n  var gm = new LGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nLayout.prototype.newGraph = function (vGraph) {\n  return new LGraph(null, this.graphManager, vGraph);\n};\n\nLayout.prototype.newNode = function (vNode) {\n  return new LNode(this.graphManager, vNode);\n};\n\nLayout.prototype.newEdge = function (vEdge) {\n  return new LEdge(null, null, vEdge);\n};\n\nLayout.prototype.checkLayoutSuccess = function () {\n  return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n};\n\nLayout.prototype.runLayout = function () {\n  this.isLayoutFinished = false;\n\n  if (this.tilingPreLayout) {\n    this.tilingPreLayout();\n  }\n\n  this.initParameters();\n  var isLayoutSuccessfull;\n\n  if (this.checkLayoutSuccess()) {\n    isLayoutSuccessfull = false;\n  } else {\n    isLayoutSuccessfull = this.layout();\n  }\n\n  if (LayoutConstants.ANIMATE === 'during') {\n    // If this is a 'during' layout animation. Layout is not finished yet. \n    // We need to perform these in index.js when layout is really finished.\n    return false;\n  }\n\n  if (isLayoutSuccessfull) {\n    if (!this.isSubLayout) {\n      this.doPostLayout();\n    }\n  }\n\n  if (this.tilingPostLayout) {\n    this.tilingPostLayout();\n  }\n\n  this.isLayoutFinished = true;\n\n  return isLayoutSuccessfull;\n};\n\n/**\r\n * This method performs the operations required after layout.\r\n */\nLayout.prototype.doPostLayout = function () {\n  //assert !isSubLayout : \"Should not be called on sub-layout!\";\n  // Propagate geometric changes to v-level objects\n  if (!this.incremental) {\n    this.transform();\n  }\n  this.update();\n};\n\n/**\r\n * This method updates the geometry of the target graph according to\r\n * calculated layout.\r\n */\nLayout.prototype.update2 = function () {\n  // update bend points\n  if (this.createBendsAsNeeded) {\n    this.createBendpointsFromDummyNodes();\n\n    // reset all edges, since the topology has changed\n    this.graphManager.resetAllEdges();\n  }\n\n  // perform edge, node and root updates if layout is not called\n  // remotely\n  if (!this.isRemoteUse) {\n    // update all edges\n    var edge;\n    var allEdges = this.graphManager.getAllEdges();\n    for (var i = 0; i < allEdges.length; i++) {\n      edge = allEdges[i];\n      //      this.update(edge);\n    }\n\n    // recursively update nodes\n    var node;\n    var nodes = this.graphManager.getRoot().getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      //      this.update(node);\n    }\n\n    // update root graph\n    this.update(this.graphManager.getRoot());\n  }\n};\n\nLayout.prototype.update = function (obj) {\n  if (obj == null) {\n    this.update2();\n  } else if (obj instanceof LNode) {\n    var node = obj;\n    if (node.getChild() != null) {\n      // since node is compound, recursively update child nodes\n      var nodes = node.getChild().getNodes();\n      for (var i = 0; i < nodes.length; i++) {\n        update(nodes[i]);\n      }\n    }\n\n    // if the l-level node is associated with a v-level graph object,\n    // then it is assumed that the v-level node implements the\n    // interface Updatable.\n    if (node.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vNode = node.vGraphObject;\n\n      // call the update method of the interface\n      vNode.update(node);\n    }\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    // if the l-level edge is associated with a v-level graph object,\n    // then it is assumed that the v-level edge implements the\n    // interface Updatable.\n\n    if (edge.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vEdge = edge.vGraphObject;\n\n      // call the update method of the interface\n      vEdge.update(edge);\n    }\n  } else if (obj instanceof LGraph) {\n    var graph = obj;\n    // if the l-level graph is associated with a v-level graph object,\n    // then it is assumed that the v-level object implements the\n    // interface Updatable.\n\n    if (graph.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vGraph = graph.vGraphObject;\n\n      // call the update method of the interface\n      vGraph.update(graph);\n    }\n  }\n};\n\n/**\r\n * This method is used to set all layout parameters to default values\r\n * determined at compile time.\r\n */\nLayout.prototype.initParameters = function () {\n  if (!this.isSubLayout) {\n    this.layoutQuality = LayoutConstants.QUALITY;\n    this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n    this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n    this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n    this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n    this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n    this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  }\n\n  if (this.animationDuringLayout) {\n    this.animationOnLayout = false;\n  }\n};\n\nLayout.prototype.transform = function (newLeftTop) {\n  if (newLeftTop == undefined) {\n    this.transform(new PointD(0, 0));\n  } else {\n    // create a transformation object (from Eclipse to layout). When an\n    // inverse transform is applied, we get upper-left coordinate of the\n    // drawing or the root graph at given input coordinate (some margins\n    // already included in calculation of left-top).\n\n    var trans = new Transform();\n    var leftTop = this.graphManager.getRoot().updateLeftTop();\n\n    if (leftTop != null) {\n      trans.setWorldOrgX(newLeftTop.x);\n      trans.setWorldOrgY(newLeftTop.y);\n\n      trans.setDeviceOrgX(leftTop.x);\n      trans.setDeviceOrgY(leftTop.y);\n\n      var nodes = this.getAllNodes();\n      var node;\n\n      for (var i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.transform(trans);\n      }\n    }\n  }\n};\n\nLayout.prototype.positionNodesRandomly = function (graph) {\n\n  if (graph == undefined) {\n    //assert !this.incremental;\n    this.positionNodesRandomly(this.getGraphManager().getRoot());\n    this.getGraphManager().getRoot().updateBounds(true);\n  } else {\n    var lNode;\n    var childGraph;\n\n    var nodes = graph.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      lNode = nodes[i];\n      childGraph = lNode.getChild();\n\n      if (childGraph == null) {\n        lNode.scatter();\n      } else if (childGraph.getNodes().length == 0) {\n        lNode.scatter();\n      } else {\n        this.positionNodesRandomly(childGraph);\n        lNode.updateBounds();\n      }\n    }\n  }\n};\n\n/**\r\n * This method returns a list of trees where each tree is represented as a\r\n * list of l-nodes. The method returns a list of size 0 when:\r\n * - The graph is not flat or\r\n * - One of the component(s) of the graph is not a tree.\r\n */\nLayout.prototype.getFlatForest = function () {\n  var flatForest = [];\n  var isForest = true;\n\n  // Quick reference for all nodes in the graph manager associated with\n  // this layout. The list should not be changed.\n  var allNodes = this.graphManager.getRoot().getNodes();\n\n  // First be sure that the graph is flat\n  var isFlat = true;\n\n  for (var i = 0; i < allNodes.length; i++) {\n    if (allNodes[i].getChild() != null) {\n      isFlat = false;\n    }\n  }\n\n  // Return empty forest if the graph is not flat.\n  if (!isFlat) {\n    return flatForest;\n  }\n\n  // Run BFS for each component of the graph.\n\n  var visited = new Set();\n  var toBeVisited = [];\n  var parents = new Map();\n  var unProcessedNodes = [];\n\n  unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n  // Each iteration of this loop finds a component of the graph and\n  // decides whether it is a tree or not. If it is a tree, adds it to the\n  // forest and continued with the next component.\n\n  while (unProcessedNodes.length > 0 && isForest) {\n    toBeVisited.push(unProcessedNodes[0]);\n\n    // Start the BFS. Each iteration of this loop visits a node in a\n    // BFS manner.\n    while (toBeVisited.length > 0 && isForest) {\n      //pool operation\n      var currentNode = toBeVisited[0];\n      toBeVisited.splice(0, 1);\n      visited.add(currentNode);\n\n      // Traverse all neighbors of this node\n      var neighborEdges = currentNode.getEdges();\n\n      for (var i = 0; i < neighborEdges.length; i++) {\n        var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n        // If BFS is not growing from this neighbor.\n        if (parents.get(currentNode) != currentNeighbor) {\n          // We haven't previously visited this neighbor.\n          if (!visited.has(currentNeighbor)) {\n            toBeVisited.push(currentNeighbor);\n            parents.set(currentNeighbor, currentNode);\n          }\n          // Since we have previously visited this neighbor and\n          // this neighbor is not parent of currentNode, given\n          // graph contains a component that is not tree, hence\n          // it is not a forest.\n          else {\n              isForest = false;\n              break;\n            }\n        }\n      }\n    }\n\n    // The graph contains a component that is not a tree. Empty\n    // previously found trees. The method will end.\n    if (!isForest) {\n      flatForest = [];\n    }\n    // Save currently visited nodes as a tree in our forest. Reset\n    // visited and parents lists. Continue with the next component of\n    // the graph, if any.\n    else {\n        var temp = [].concat(_toConsumableArray(visited));\n        flatForest.push(temp);\n        //flatForest = flatForest.concat(temp);\n        //unProcessedNodes.removeAll(visited);\n        for (var i = 0; i < temp.length; i++) {\n          var value = temp[i];\n          var index = unProcessedNodes.indexOf(value);\n          if (index > -1) {\n            unProcessedNodes.splice(index, 1);\n          }\n        }\n        visited = new Set();\n        parents = new Map();\n      }\n  }\n\n  return flatForest;\n};\n\n/**\r\n * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n * for the given edge (one per bendpoint). The existing l-level structure\r\n * is updated accordingly.\r\n */\nLayout.prototype.createDummyNodesForBendpoints = function (edge) {\n  var dummyNodes = [];\n  var prev = edge.source;\n\n  var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n\n  for (var i = 0; i < edge.bendpoints.length; i++) {\n    // create new dummy node\n    var dummyNode = this.newNode(null);\n    dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n\n    graph.add(dummyNode);\n\n    // create new dummy edge between prev and dummy node\n    var dummyEdge = this.newEdge(null);\n    this.graphManager.add(dummyEdge, prev, dummyNode);\n\n    dummyNodes.add(dummyNode);\n    prev = dummyNode;\n  }\n\n  var dummyEdge = this.newEdge(null);\n  this.graphManager.add(dummyEdge, prev, edge.target);\n\n  this.edgeToDummyNodes.set(edge, dummyNodes);\n\n  // remove real edge from graph manager if it is inter-graph\n  if (edge.isInterGraph()) {\n    this.graphManager.remove(edge);\n  }\n  // else, remove the edge from the current graph\n  else {\n      graph.remove(edge);\n    }\n\n  return dummyNodes;\n};\n\n/**\r\n * This method creates bendpoints for edges from the dummy nodes\r\n * at l-level.\r\n */\nLayout.prototype.createBendpointsFromDummyNodes = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n\n  for (var k = 0; k < edges.length; k++) {\n    var lEdge = edges[k];\n\n    if (lEdge.bendpoints.length > 0) {\n      var path = this.edgeToDummyNodes.get(lEdge);\n\n      for (var i = 0; i < path.length; i++) {\n        var dummyNode = path[i];\n        var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n        // update bendpoint's location according to dummy node\n        var ebp = lEdge.bendpoints.get(i);\n        ebp.x = p.x;\n        ebp.y = p.y;\n\n        // remove the dummy node, dummy edges incident with this\n        // dummy node is also removed (within the remove method)\n        dummyNode.getOwner().remove(dummyNode);\n      }\n\n      // add the real edge to graph\n      this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n    }\n  }\n};\n\nLayout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n  if (minDiv != undefined && maxMul != undefined) {\n    var value = defaultValue;\n\n    if (sliderValue <= 50) {\n      var minValue = defaultValue / minDiv;\n      value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n    } else {\n      var maxValue = defaultValue * maxMul;\n      value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n    }\n\n    return value;\n  } else {\n    var a, b;\n\n    if (sliderValue <= 50) {\n      a = 9.0 * defaultValue / 500.0;\n      b = defaultValue / 10.0;\n    } else {\n      a = 9.0 * defaultValue / 50.0;\n      b = -8 * defaultValue;\n    }\n\n    return a * sliderValue + b;\n  }\n};\n\n/**\r\n * This method finds and returns the center of the given nodes, assuming\r\n * that the given nodes form a tree in themselves.\r\n */\nLayout.findCenterOfTree = function (nodes) {\n  var list = [];\n  list = list.concat(nodes);\n\n  var removedNodes = [];\n  var remainingDegrees = new Map();\n  var foundCenter = false;\n  var centerNode = null;\n\n  if (list.length == 1 || list.length == 2) {\n    foundCenter = true;\n    centerNode = list[0];\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    var node = list[i];\n    var degree = node.getNeighborsList().size;\n    remainingDegrees.set(node, node.getNeighborsList().size);\n\n    if (degree == 1) {\n      removedNodes.push(node);\n    }\n  }\n\n  var tempList = [];\n  tempList = tempList.concat(removedNodes);\n\n  while (!foundCenter) {\n    var tempList2 = [];\n    tempList2 = tempList2.concat(tempList);\n    tempList = [];\n\n    for (var i = 0; i < list.length; i++) {\n      var node = list[i];\n\n      var index = list.indexOf(node);\n      if (index >= 0) {\n        list.splice(index, 1);\n      }\n\n      var neighbours = node.getNeighborsList();\n\n      neighbours.forEach(function (neighbour) {\n        if (removedNodes.indexOf(neighbour) < 0) {\n          var otherDegree = remainingDegrees.get(neighbour);\n          var newDegree = otherDegree - 1;\n\n          if (newDegree == 1) {\n            tempList.push(neighbour);\n          }\n\n          remainingDegrees.set(neighbour, newDegree);\n        }\n      });\n    }\n\n    removedNodes = removedNodes.concat(tempList);\n\n    if (list.length == 1 || list.length == 2) {\n      foundCenter = true;\n      centerNode = list[0];\n    }\n  }\n\n  return centerNode;\n};\n\n/**\r\n * During the coarsening process, this layout may be referenced by two graph managers\r\n * this setter function grants access to change the currently being used graph manager\r\n */\nLayout.prototype.setGraphManager = function (gm) {\n  this.graphManager = gm;\n};\n\nmodule.exports = Layout;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RandomSeed() {}\n// adapted from: https://stackoverflow.com/a/19303725\nRandomSeed.seed = 1;\nRandomSeed.x = 0;\n\nRandomSeed.nextDouble = function () {\n  RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n  return RandomSeed.x - Math.floor(RandomSeed.x);\n};\n\nmodule.exports = RandomSeed;\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar PointD = __webpack_require__(4);\n\nfunction Transform(x, y) {\n  this.lworldOrgX = 0.0;\n  this.lworldOrgY = 0.0;\n  this.ldeviceOrgX = 0.0;\n  this.ldeviceOrgY = 0.0;\n  this.lworldExtX = 1.0;\n  this.lworldExtY = 1.0;\n  this.ldeviceExtX = 1.0;\n  this.ldeviceExtY = 1.0;\n}\n\nTransform.prototype.getWorldOrgX = function () {\n  return this.lworldOrgX;\n};\n\nTransform.prototype.setWorldOrgX = function (wox) {\n  this.lworldOrgX = wox;\n};\n\nTransform.prototype.getWorldOrgY = function () {\n  return this.lworldOrgY;\n};\n\nTransform.prototype.setWorldOrgY = function (woy) {\n  this.lworldOrgY = woy;\n};\n\nTransform.prototype.getWorldExtX = function () {\n  return this.lworldExtX;\n};\n\nTransform.prototype.setWorldExtX = function (wex) {\n  this.lworldExtX = wex;\n};\n\nTransform.prototype.getWorldExtY = function () {\n  return this.lworldExtY;\n};\n\nTransform.prototype.setWorldExtY = function (wey) {\n  this.lworldExtY = wey;\n};\n\n/* Device related */\n\nTransform.prototype.getDeviceOrgX = function () {\n  return this.ldeviceOrgX;\n};\n\nTransform.prototype.setDeviceOrgX = function (dox) {\n  this.ldeviceOrgX = dox;\n};\n\nTransform.prototype.getDeviceOrgY = function () {\n  return this.ldeviceOrgY;\n};\n\nTransform.prototype.setDeviceOrgY = function (doy) {\n  this.ldeviceOrgY = doy;\n};\n\nTransform.prototype.getDeviceExtX = function () {\n  return this.ldeviceExtX;\n};\n\nTransform.prototype.setDeviceExtX = function (dex) {\n  this.ldeviceExtX = dex;\n};\n\nTransform.prototype.getDeviceExtY = function () {\n  return this.ldeviceExtY;\n};\n\nTransform.prototype.setDeviceExtY = function (dey) {\n  this.ldeviceExtY = dey;\n};\n\nTransform.prototype.transformX = function (x) {\n  var xDevice = 0.0;\n  var worldExtX = this.lworldExtX;\n  if (worldExtX != 0.0) {\n    xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n  }\n\n  return xDevice;\n};\n\nTransform.prototype.transformY = function (y) {\n  var yDevice = 0.0;\n  var worldExtY = this.lworldExtY;\n  if (worldExtY != 0.0) {\n    yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n  }\n\n  return yDevice;\n};\n\nTransform.prototype.inverseTransformX = function (x) {\n  var xWorld = 0.0;\n  var deviceExtX = this.ldeviceExtX;\n  if (deviceExtX != 0.0) {\n    xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n  }\n\n  return xWorld;\n};\n\nTransform.prototype.inverseTransformY = function (y) {\n  var yWorld = 0.0;\n  var deviceExtY = this.ldeviceExtY;\n  if (deviceExtY != 0.0) {\n    yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n  }\n  return yWorld;\n};\n\nTransform.prototype.inverseTransformPoint = function (inPoint) {\n  var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n  return outPoint;\n};\n\nmodule.exports = Transform;\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar Layout = __webpack_require__(15);\nvar FDLayoutConstants = __webpack_require__(7);\nvar LayoutConstants = __webpack_require__(0);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction FDLayout() {\n  Layout.call(this);\n\n  this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n  this.idealEdgeLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n  this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n  this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n  this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n  this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n  this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n  this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.totalDisplacement = 0.0;\n  this.oldTotalDisplacement = 0.0;\n  this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n}\n\nFDLayout.prototype = Object.create(Layout.prototype);\n\nfor (var prop in Layout) {\n  FDLayout[prop] = Layout[prop];\n}\n\nFDLayout.prototype.initParameters = function () {\n  Layout.prototype.initParameters.call(this, arguments);\n\n  this.totalIterations = 0;\n  this.notAnimatedIterations = 0;\n\n  this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n\n  this.grid = [];\n};\n\nFDLayout.prototype.calcIdealEdgeLengths = function () {\n  var edge;\n  var lcaDepth;\n  var source;\n  var target;\n  var sizeOfSourceInLca;\n  var sizeOfTargetInLca;\n\n  var allEdges = this.getGraphManager().getAllEdges();\n  for (var i = 0; i < allEdges.length; i++) {\n    edge = allEdges[i];\n\n    edge.idealLength = this.idealEdgeLength;\n\n    if (edge.isInterGraph) {\n      source = edge.getSource();\n      target = edge.getTarget();\n\n      sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n      sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n\n      if (this.useSmartIdealEdgeLengthCalculation) {\n        edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n      }\n\n      lcaDepth = edge.getLca().getInclusionTreeDepth();\n\n      edge.idealLength += FDLayoutConstants.DEFAULT_EDGE_LENGTH * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n    }\n  }\n};\n\nFDLayout.prototype.initSpringEmbedder = function () {\n\n  var s = this.getAllNodes().length;\n  if (this.incremental) {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    }\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n  } else {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    } else {\n      this.coolingFactor = 1.0;\n    }\n    this.initialCoolingFactor = this.coolingFactor;\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n  }\n\n  this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n\n  this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n\n  this.repulsionRange = this.calcRepulsionRange();\n};\n\nFDLayout.prototype.calcSpringForces = function () {\n  var lEdges = this.getAllEdges();\n  var edge;\n\n  for (var i = 0; i < lEdges.length; i++) {\n    edge = lEdges[i];\n\n    this.calcSpringForce(edge, edge.idealLength);\n  }\n};\n\nFDLayout.prototype.calcRepulsionForces = function () {\n  var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var i, j;\n  var nodeA, nodeB;\n  var lNodes = this.getAllNodes();\n  var processedNodeSet;\n\n  if (this.useFRGridVariant) {\n    if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n      this.updateGrid();\n    }\n\n    processedNodeSet = new Set();\n\n    // calculate repulsion forces between each nodes and its surrounding\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n      this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n      processedNodeSet.add(nodeA);\n    }\n  } else {\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n\n      for (j = i + 1; j < lNodes.length; j++) {\n        nodeB = lNodes[j];\n\n        // If both nodes are not members of the same graph, skip.\n        if (nodeA.getOwner() != nodeB.getOwner()) {\n          continue;\n        }\n\n        this.calcRepulsionForce(nodeA, nodeB);\n      }\n    }\n  }\n};\n\nFDLayout.prototype.calcGravitationalForces = function () {\n  var node;\n  var lNodes = this.getAllNodesToApplyGravitation();\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    this.calcGravitationalForce(node);\n  }\n};\n\nFDLayout.prototype.moveNodes = function () {\n  var lNodes = this.getAllNodes();\n  var node;\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.move();\n  }\n};\n\nFDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n  var sourceNode = edge.getSource();\n  var targetNode = edge.getTarget();\n\n  var length;\n  var springForce;\n  var springForceX;\n  var springForceY;\n\n  // Update edge length\n  if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n    edge.updateLengthSimple();\n  } else {\n    edge.updateLength();\n\n    if (edge.isOverlapingSourceAndTarget) {\n      return;\n    }\n  }\n\n  length = edge.getLength();\n\n  if (length == 0) return;\n\n  // Calculate spring forces\n  springForce = this.springConstant * (length - idealLength);\n\n  // Project force onto x and y axes\n  springForceX = springForce * (edge.lengthX / length);\n  springForceY = springForce * (edge.lengthY / length);\n\n  // Apply forces on the end nodes\n  sourceNode.springForceX += springForceX;\n  sourceNode.springForceY += springForceY;\n  targetNode.springForceX -= springForceX;\n  targetNode.springForceY -= springForceY;\n};\n\nFDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n  var rectA = nodeA.getRect();\n  var rectB = nodeB.getRect();\n  var overlapAmount = new Array(2);\n  var clipPoints = new Array(4);\n  var distanceX;\n  var distanceY;\n  var distanceSquared;\n  var distance;\n  var repulsionForce;\n  var repulsionForceX;\n  var repulsionForceY;\n\n  if (rectA.intersects(rectB)) // two nodes overlap\n    {\n      // calculate separation amount in x and y directions\n      IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n\n      repulsionForceX = 2 * overlapAmount[0];\n      repulsionForceY = 2 * overlapAmount[1];\n\n      var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n      // Apply forces on the two nodes\n      nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n      nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n      nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n      nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n    } else // no overlap\n    {\n      // calculate distance\n\n      if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null) // simply base repulsion on distance of node centers\n        {\n          distanceX = rectB.getCenterX() - rectA.getCenterX();\n          distanceY = rectB.getCenterY() - rectA.getCenterY();\n        } else // use clipping points\n        {\n          IGeometry.getIntersection(rectA, rectB, clipPoints);\n\n          distanceX = clipPoints[2] - clipPoints[0];\n          distanceY = clipPoints[3] - clipPoints[1];\n        }\n\n      // No repulsion range. FR grid variant should take care of this.\n      if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      distanceSquared = distanceX * distanceX + distanceY * distanceY;\n      distance = Math.sqrt(distanceSquared);\n\n      repulsionForce = this.repulsionConstant * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n      // Project force onto x and y axes\n      repulsionForceX = repulsionForce * distanceX / distance;\n      repulsionForceY = repulsionForce * distanceY / distance;\n\n      // Apply forces on the two nodes    \n      nodeA.repulsionForceX -= repulsionForceX;\n      nodeA.repulsionForceY -= repulsionForceY;\n      nodeB.repulsionForceX += repulsionForceX;\n      nodeB.repulsionForceY += repulsionForceY;\n    }\n};\n\nFDLayout.prototype.calcGravitationalForce = function (node) {\n  var ownerGraph;\n  var ownerCenterX;\n  var ownerCenterY;\n  var distanceX;\n  var distanceY;\n  var absDistanceX;\n  var absDistanceY;\n  var estimatedSize;\n  ownerGraph = node.getOwner();\n\n  ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n  ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n  distanceX = node.getCenterX() - ownerCenterX;\n  distanceY = node.getCenterY() - ownerCenterY;\n  absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n  absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n\n  if (node.getOwner() == this.graphManager.getRoot()) // in the root graph\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX;\n        node.gravitationForceY = -this.gravityConstant * distanceY;\n      }\n    } else // inside a compound\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n        node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n      }\n    }\n};\n\nFDLayout.prototype.isConverged = function () {\n  var converged;\n  var oscilating = false;\n\n  if (this.totalIterations > this.maxIterations / 3) {\n    oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n  }\n\n  converged = this.totalDisplacement < this.totalDisplacementThreshold;\n\n  this.oldTotalDisplacement = this.totalDisplacement;\n\n  return converged || oscilating;\n};\n\nFDLayout.prototype.animate = function () {\n  if (this.animationDuringLayout && !this.isSubLayout) {\n    if (this.notAnimatedIterations == this.animationPeriod) {\n      this.update();\n      this.notAnimatedIterations = 0;\n    } else {\n      this.notAnimatedIterations++;\n    }\n  }\n};\n\n//This method calculates the number of children (weight) for all nodes\nFDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n  var node;\n  var allNodes = this.graphManager.getAllNodes();\n\n  for (var i = 0; i < allNodes.length; i++) {\n    node = allNodes[i];\n    node.noOfChildren = node.getNoOfChildren();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: FR-Grid Variant Repulsion Force Calculation\n// -----------------------------------------------------------------------------\n\nFDLayout.prototype.calcGrid = function (graph) {\n\n  var sizeX = 0;\n  var sizeY = 0;\n\n  sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n  sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n\n  var grid = new Array(sizeX);\n\n  for (var i = 0; i < sizeX; i++) {\n    grid[i] = new Array(sizeY);\n  }\n\n  for (var i = 0; i < sizeX; i++) {\n    for (var j = 0; j < sizeY; j++) {\n      grid[i][j] = new Array();\n    }\n  }\n\n  return grid;\n};\n\nFDLayout.prototype.addNodeToGrid = function (v, left, top) {\n\n  var startX = 0;\n  var finishX = 0;\n  var startY = 0;\n  var finishY = 0;\n\n  startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n  finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n  startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n  finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n\n  for (var i = startX; i <= finishX; i++) {\n    for (var j = startY; j <= finishY; j++) {\n      this.grid[i][j].push(v);\n      v.setGridCoordinates(startX, finishX, startY, finishY);\n    }\n  }\n};\n\nFDLayout.prototype.updateGrid = function () {\n  var i;\n  var nodeA;\n  var lNodes = this.getAllNodes();\n\n  this.grid = this.calcGrid(this.graphManager.getRoot());\n\n  // put all nodes to proper grid cells\n  for (i = 0; i < lNodes.length; i++) {\n    nodeA = lNodes[i];\n    this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n  }\n};\n\nFDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n\n  if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n    var surrounding = new Set();\n    nodeA.surrounding = new Array();\n    var nodeB;\n    var grid = this.grid;\n\n    for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n      for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n        if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n          for (var k = 0; k < grid[i][j].length; k++) {\n            nodeB = grid[i][j][k];\n\n            // If both nodes are not members of the same graph, \n            // or both nodes are the same, skip.\n            if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n              continue;\n            }\n\n            // check if the repulsion force between\n            // nodeA and nodeB has already been calculated\n            if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n              var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n              var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n              // if the distance between nodeA and nodeB \n              // is less then calculation range\n              if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                //then add nodeB to surrounding of nodeA\n                surrounding.add(nodeB);\n              }\n            }\n          }\n        }\n      }\n    }\n\n    nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n  }\n  for (i = 0; i < nodeA.surrounding.length; i++) {\n    this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n  }\n};\n\nFDLayout.prototype.calcRepulsionRange = function () {\n  return 0.0;\n};\n\nmodule.exports = FDLayout;\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LEdge = __webpack_require__(1);\nvar FDLayoutConstants = __webpack_require__(7);\n\nfunction FDLayoutEdge(source, target, vEdge) {\n  LEdge.call(this, source, target, vEdge);\n  this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n}\n\nFDLayoutEdge.prototype = Object.create(LEdge.prototype);\n\nfor (var prop in LEdge) {\n  FDLayoutEdge[prop] = LEdge[prop];\n}\n\nmodule.exports = FDLayoutEdge;\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LNode = __webpack_require__(3);\n\nfunction FDLayoutNode(gm, loc, size, vNode) {\n  // alternative constructor is handled inside LNode\n  LNode.call(this, gm, loc, size, vNode);\n  //Spring, repulsion and gravitational forces acting on this node\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  //Amount by which this node is to be moved in this iteration\n  this.displacementX = 0;\n  this.displacementY = 0;\n\n  //Start and finish grid coordinates that this node is fallen into\n  this.startX = 0;\n  this.finishX = 0;\n  this.startY = 0;\n  this.finishY = 0;\n\n  //Geometric neighbors of this node\n  this.surrounding = [];\n}\n\nFDLayoutNode.prototype = Object.create(LNode.prototype);\n\nfor (var prop in LNode) {\n  FDLayoutNode[prop] = LNode[prop];\n}\n\nFDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n  this.startX = _startX;\n  this.finishX = _finishX;\n  this.startY = _startY;\n  this.finishY = _finishY;\n};\n\nmodule.exports = FDLayoutNode;\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction DimensionD(width, height) {\n  this.width = 0;\n  this.height = 0;\n  if (width !== null && height !== null) {\n    this.height = height;\n    this.width = width;\n  }\n}\n\nDimensionD.prototype.getWidth = function () {\n  return this.width;\n};\n\nDimensionD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nDimensionD.prototype.getHeight = function () {\n  return this.height;\n};\n\nDimensionD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nmodule.exports = DimensionD;\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashMap() {\n  this.map = {};\n  this.keys = [];\n}\n\nHashMap.prototype.put = function (key, value) {\n  var theId = UniqueIDGeneretor.createID(key);\n  if (!this.contains(theId)) {\n    this.map[theId] = value;\n    this.keys.push(key);\n  }\n};\n\nHashMap.prototype.contains = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[key] != null;\n};\n\nHashMap.prototype.get = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[theId];\n};\n\nHashMap.prototype.keySet = function () {\n  return this.keys;\n};\n\nmodule.exports = HashMap;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashSet() {\n  this.set = {};\n}\n;\n\nHashSet.prototype.add = function (obj) {\n  var theId = UniqueIDGeneretor.createID(obj);\n  if (!this.contains(theId)) this.set[theId] = obj;\n};\n\nHashSet.prototype.remove = function (obj) {\n  delete this.set[UniqueIDGeneretor.createID(obj)];\n};\n\nHashSet.prototype.clear = function () {\n  this.set = {};\n};\n\nHashSet.prototype.contains = function (obj) {\n  return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n};\n\nHashSet.prototype.isEmpty = function () {\n  return this.size() === 0;\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\n//concats this.set to the given list\nHashSet.prototype.addAllTo = function (list) {\n  var keys = Object.keys(this.set);\n  var length = keys.length;\n  for (var i = 0; i < length; i++) {\n    list.push(this.set[keys[i]]);\n  }\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\nHashSet.prototype.addAll = function (list) {\n  var s = list.length;\n  for (var i = 0; i < s; i++) {\n    var v = list[i];\n    this.add(v);\n  }\n};\n\nmodule.exports = HashSet;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * A classic Quicksort algorithm with Hoare's partition\n * - Works also on LinkedList objects\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar LinkedList = __webpack_require__(11);\n\nvar Quicksort = function () {\n    function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n\n        this._quicksort(A, 0, length - 1);\n    }\n\n    _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n            if (p < r) {\n                var q = this._partition(A, p, r);\n                this._quicksort(A, p, q);\n                this._quicksort(A, q + 1, r);\n            }\n        }\n    }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n            var x = this._get(A, p);\n            var i = p;\n            var j = r;\n            while (true) {\n                while (this.compareFunction(x, this._get(A, j))) {\n                    j--;\n                }while (this.compareFunction(this._get(A, i), x)) {\n                    i++;\n                }if (i < j) {\n                    this._swap(A, i, j);\n                    i++;\n                    j--;\n                } else return j;\n            }\n        }\n    }, {\n        key: '_get',\n        value: function _get(object, index) {\n            if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n    }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n            if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n    }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n            var temp = this._get(A, i);\n            this._set(A, i, this._get(A, j));\n            this._set(A, j, temp);\n        }\n    }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n            return b > a;\n        }\n    }]);\n\n    return Quicksort;\n}();\n\nmodule.exports = Quicksort;\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n *\n *   Aside from the inputs, you can assign the scores for,\n *   - Match: The two characters at the current index are same.\n *   - Mismatch: The two characters at the current index are different.\n *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n */\n\nvar NeedlemanWunsch = function () {\n    function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n\n        _classCallCheck(this, NeedlemanWunsch);\n\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n            this.grid[i] = new Array(this.jMax);\n\n            for (var j = 0; j < this.jMax; j++) {\n                this.grid[i][j] = 0;\n            }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n            this.tracebackGrid[_i] = new Array(this.jMax);\n\n            for (var _j = 0; _j < this.jMax; _j++) {\n                this.tracebackGrid[_i][_j] = [null, null, null];\n            }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n    }\n\n    _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n            return this.score;\n        }\n    }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n            return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n\n    }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n            // Fill in the first row\n            for (var j = 1; j < this.jMax; j++) {\n                this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n                this.tracebackGrid[0][j] = [false, false, true];\n            }\n\n            // Fill in the first column\n            for (var i = 1; i < this.iMax; i++) {\n                this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n                this.tracebackGrid[i][0] = [false, true, false];\n            }\n\n            // Fill the rest of the grid\n            for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n                for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n                    // Find the max score(s) among [`Diag`, `Up`, `Left`]\n                    var diag = void 0;\n                    if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n\n                    var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n                    var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n                    // If there exists multiple max values, capture them for multiple paths\n                    var maxOf = [diag, up, left];\n                    var indices = this.arrayAllMaxIndexes(maxOf);\n\n                    // Update Grids\n                    this.grid[_i2][_j2] = maxOf[indices[0]];\n                    this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n                }\n            }\n\n            // Update alignment score\n            this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n\n    }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n            var inProcessAlignments = [];\n\n            inProcessAlignments.push({ pos: [this.sequence1.length, this.sequence2.length],\n                seq1: \"\",\n                seq2: \"\"\n            });\n\n            while (inProcessAlignments[0]) {\n                var current = inProcessAlignments[0];\n                var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n\n                if (directions[0]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1] - 1],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n                if (directions[1]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1]],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: '-' + current.seq2\n                    });\n                }\n                if (directions[2]) {\n                    inProcessAlignments.push({ pos: [current.pos[0], current.pos[1] - 1],\n                        seq1: '-' + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n\n                if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({ sequence1: current.seq1,\n                    sequence2: current.seq2\n                });\n\n                inProcessAlignments.shift();\n            }\n\n            return this.alignments;\n        }\n\n        // Helper Functions\n\n    }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n            var indexes = [],\n                i = -1;\n            while ((i = arr.indexOf(val, i + 1)) !== -1) {\n                indexes.push(i);\n            }\n            return indexes;\n        }\n    }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n            return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n    }]);\n\n    return NeedlemanWunsch;\n}();\n\nmodule.exports = NeedlemanWunsch;\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar layoutBase = function layoutBase() {\n  return;\n};\n\nlayoutBase.FDLayout = __webpack_require__(18);\nlayoutBase.FDLayoutConstants = __webpack_require__(7);\nlayoutBase.FDLayoutEdge = __webpack_require__(19);\nlayoutBase.FDLayoutNode = __webpack_require__(20);\nlayoutBase.DimensionD = __webpack_require__(21);\nlayoutBase.HashMap = __webpack_require__(22);\nlayoutBase.HashSet = __webpack_require__(23);\nlayoutBase.IGeometry = __webpack_require__(8);\nlayoutBase.IMath = __webpack_require__(9);\nlayoutBase.Integer = __webpack_require__(10);\nlayoutBase.Point = __webpack_require__(12);\nlayoutBase.PointD = __webpack_require__(4);\nlayoutBase.RandomSeed = __webpack_require__(16);\nlayoutBase.RectangleD = __webpack_require__(13);\nlayoutBase.Transform = __webpack_require__(17);\nlayoutBase.UniqueIDGeneretor = __webpack_require__(14);\nlayoutBase.Quicksort = __webpack_require__(24);\nlayoutBase.LinkedList = __webpack_require__(11);\nlayoutBase.LGraphObject = __webpack_require__(2);\nlayoutBase.LGraph = __webpack_require__(5);\nlayoutBase.LEdge = __webpack_require__(1);\nlayoutBase.LGraphManager = __webpack_require__(6);\nlayoutBase.LNode = __webpack_require__(3);\nlayoutBase.Layout = __webpack_require__(15);\nlayoutBase.LayoutConstants = __webpack_require__(0);\nlayoutBase.NeedlemanWunsch = __webpack_require__(25);\n\nmodule.exports = layoutBase;\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Emitter() {\n  this.listeners = [];\n}\n\nvar p = Emitter.prototype;\n\np.addListener = function (event, callback) {\n  this.listeners.push({\n    event: event,\n    callback: callback\n  });\n};\n\np.removeListener = function (event, callback) {\n  for (var i = this.listeners.length; i >= 0; i--) {\n    var l = this.listeners[i];\n\n    if (l.event === event && l.callback === callback) {\n      this.listeners.splice(i, 1);\n    }\n  }\n};\n\np.emit = function (event, data) {\n  for (var i = 0; i < this.listeners.length; i++) {\n    var l = this.listeners[i];\n\n    if (event === l.event) {\n      l.callback(data);\n    }\n  }\n};\n\nmodule.exports = Emitter;\n\n/***/ })\n/******/ ]);\n});", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"layout-base\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"layout-base\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"coseBase\"] = factory(require(\"layout-base\"));\n\telse\n\t\troot[\"coseBase\"] = factory(root[\"layoutBase\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_0__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 7);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_0__;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayoutConstants = __webpack_require__(0).FDLayoutConstants;\n\nfunction CoSEConstants() {}\n\n//CoSEConstants inherits static props in FDLayoutConstants\nfor (var prop in FDLayoutConstants) {\n  CoSEConstants[prop] = FDLayoutConstants[prop];\n}\n\nCoSEConstants.DEFAULT_USE_MULTI_LEVEL_SCALING = false;\nCoSEConstants.DEFAULT_RADIAL_SEPARATION = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\nCoSEConstants.DEFAULT_COMPONENT_SEPERATION = 60;\nCoSEConstants.TILE = true;\nCoSEConstants.TILING_PADDING_VERTICAL = 10;\nCoSEConstants.TILING_PADDING_HORIZONTAL = 10;\nCoSEConstants.TREE_REDUCTION_ON_INCREMENTAL = false; // make this true when cose is used incrementally as a part of other non-incremental layout\n\nmodule.exports = CoSEConstants;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayoutEdge = __webpack_require__(0).FDLayoutEdge;\n\nfunction CoSEEdge(source, target, vEdge) {\n  FDLayoutEdge.call(this, source, target, vEdge);\n}\n\nCoSEEdge.prototype = Object.create(FDLayoutEdge.prototype);\nfor (var prop in FDLayoutEdge) {\n  CoSEEdge[prop] = FDLayoutEdge[prop];\n}\n\nmodule.exports = CoSEEdge;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraph = __webpack_require__(0).LGraph;\n\nfunction CoSEGraph(parent, graphMgr, vGraph) {\n  LGraph.call(this, parent, graphMgr, vGraph);\n}\n\nCoSEGraph.prototype = Object.create(LGraph.prototype);\nfor (var prop in LGraph) {\n  CoSEGraph[prop] = LGraph[prop];\n}\n\nmodule.exports = CoSEGraph;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphManager = __webpack_require__(0).LGraphManager;\n\nfunction CoSEGraphManager(layout) {\n  LGraphManager.call(this, layout);\n}\n\nCoSEGraphManager.prototype = Object.create(LGraphManager.prototype);\nfor (var prop in LGraphManager) {\n  CoSEGraphManager[prop] = LGraphManager[prop];\n}\n\nmodule.exports = CoSEGraphManager;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayoutNode = __webpack_require__(0).FDLayoutNode;\nvar IMath = __webpack_require__(0).IMath;\n\nfunction CoSENode(gm, loc, size, vNode) {\n  FDLayoutNode.call(this, gm, loc, size, vNode);\n}\n\nCoSENode.prototype = Object.create(FDLayoutNode.prototype);\nfor (var prop in FDLayoutNode) {\n  CoSENode[prop] = FDLayoutNode[prop];\n}\n\nCoSENode.prototype.move = function () {\n  var layout = this.graphManager.getLayout();\n  this.displacementX = layout.coolingFactor * (this.springForceX + this.repulsionForceX + this.gravitationForceX) / this.noOfChildren;\n  this.displacementY = layout.coolingFactor * (this.springForceY + this.repulsionForceY + this.gravitationForceY) / this.noOfChildren;\n\n  if (Math.abs(this.displacementX) > layout.coolingFactor * layout.maxNodeDisplacement) {\n    this.displacementX = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementX);\n  }\n\n  if (Math.abs(this.displacementY) > layout.coolingFactor * layout.maxNodeDisplacement) {\n    this.displacementY = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementY);\n  }\n\n  // a simple node, just move it\n  if (this.child == null) {\n    this.moveBy(this.displacementX, this.displacementY);\n  }\n  // an empty compound node, again just move it\n  else if (this.child.getNodes().length == 0) {\n      this.moveBy(this.displacementX, this.displacementY);\n    }\n    // non-empty compound node, propogate movement to children as well\n    else {\n        this.propogateDisplacementToChildren(this.displacementX, this.displacementY);\n      }\n\n  layout.totalDisplacement += Math.abs(this.displacementX) + Math.abs(this.displacementY);\n\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  this.displacementX = 0;\n  this.displacementY = 0;\n};\n\nCoSENode.prototype.propogateDisplacementToChildren = function (dX, dY) {\n  var nodes = this.getChild().getNodes();\n  var node;\n  for (var i = 0; i < nodes.length; i++) {\n    node = nodes[i];\n    if (node.getChild() == null) {\n      node.moveBy(dX, dY);\n      node.displacementX += dX;\n      node.displacementY += dY;\n    } else {\n      node.propogateDisplacementToChildren(dX, dY);\n    }\n  }\n};\n\nCoSENode.prototype.setPred1 = function (pred1) {\n  this.pred1 = pred1;\n};\n\nCoSENode.prototype.getPred1 = function () {\n  return pred1;\n};\n\nCoSENode.prototype.getPred2 = function () {\n  return pred2;\n};\n\nCoSENode.prototype.setNext = function (next) {\n  this.next = next;\n};\n\nCoSENode.prototype.getNext = function () {\n  return next;\n};\n\nCoSENode.prototype.setProcessed = function (processed) {\n  this.processed = processed;\n};\n\nCoSENode.prototype.isProcessed = function () {\n  return processed;\n};\n\nmodule.exports = CoSENode;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayout = __webpack_require__(0).FDLayout;\nvar CoSEGraphManager = __webpack_require__(4);\nvar CoSEGraph = __webpack_require__(3);\nvar CoSENode = __webpack_require__(5);\nvar CoSEEdge = __webpack_require__(2);\nvar CoSEConstants = __webpack_require__(1);\nvar FDLayoutConstants = __webpack_require__(0).FDLayoutConstants;\nvar LayoutConstants = __webpack_require__(0).LayoutConstants;\nvar Point = __webpack_require__(0).Point;\nvar PointD = __webpack_require__(0).PointD;\nvar Layout = __webpack_require__(0).Layout;\nvar Integer = __webpack_require__(0).Integer;\nvar IGeometry = __webpack_require__(0).IGeometry;\nvar LGraph = __webpack_require__(0).LGraph;\nvar Transform = __webpack_require__(0).Transform;\n\nfunction CoSELayout() {\n  FDLayout.call(this);\n\n  this.toBeTiled = {}; // Memorize if a node is to be tiled or is tiled\n}\n\nCoSELayout.prototype = Object.create(FDLayout.prototype);\n\nfor (var prop in FDLayout) {\n  CoSELayout[prop] = FDLayout[prop];\n}\n\nCoSELayout.prototype.newGraphManager = function () {\n  var gm = new CoSEGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nCoSELayout.prototype.newGraph = function (vGraph) {\n  return new CoSEGraph(null, this.graphManager, vGraph);\n};\n\nCoSELayout.prototype.newNode = function (vNode) {\n  return new CoSENode(this.graphManager, vNode);\n};\n\nCoSELayout.prototype.newEdge = function (vEdge) {\n  return new CoSEEdge(null, null, vEdge);\n};\n\nCoSELayout.prototype.initParameters = function () {\n  FDLayout.prototype.initParameters.call(this, arguments);\n  if (!this.isSubLayout) {\n    if (CoSEConstants.DEFAULT_EDGE_LENGTH < 10) {\n      this.idealEdgeLength = 10;\n    } else {\n      this.idealEdgeLength = CoSEConstants.DEFAULT_EDGE_LENGTH;\n    }\n\n    this.useSmartIdealEdgeLengthCalculation = CoSEConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n    this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n    this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n    this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n    this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n    this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n    this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n\n    // variables for tree reduction support\n    this.prunedNodesAll = [];\n    this.growTreeIterations = 0;\n    this.afterGrowthIterations = 0;\n    this.isTreeGrowing = false;\n    this.isGrowthFinished = false;\n\n    // variables for cooling\n    this.coolingCycle = 0;\n    this.maxCoolingCycle = this.maxIterations / FDLayoutConstants.CONVERGENCE_CHECK_PERIOD;\n    this.finalTemperature = FDLayoutConstants.CONVERGENCE_CHECK_PERIOD / this.maxIterations;\n    this.coolingAdjuster = 1;\n  }\n};\n\nCoSELayout.prototype.layout = function () {\n  var createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  if (createBendsAsNeeded) {\n    this.createBendpoints();\n    this.graphManager.resetAllEdges();\n  }\n\n  this.level = 0;\n  return this.classicLayout();\n};\n\nCoSELayout.prototype.classicLayout = function () {\n  this.nodesWithGravity = this.calculateNodesToApplyGravitationTo();\n  this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity);\n  this.calcNoOfChildrenForAllNodes();\n  this.graphManager.calcLowestCommonAncestors();\n  this.graphManager.calcInclusionTreeDepths();\n  this.graphManager.getRoot().calcEstimatedSize();\n  this.calcIdealEdgeLengths();\n\n  if (!this.incremental) {\n    var forest = this.getFlatForest();\n\n    // The graph associated with this layout is flat and a forest\n    if (forest.length > 0) {\n      this.positionNodesRadially(forest);\n    }\n    // The graph associated with this layout is not flat or a forest\n    else {\n        // Reduce the trees when incremental mode is not enabled and graph is not a forest \n        this.reduceTrees();\n        // Update nodes that gravity will be applied\n        this.graphManager.resetAllNodesToApplyGravitation();\n        var allNodes = new Set(this.getAllNodes());\n        var intersection = this.nodesWithGravity.filter(function (x) {\n          return allNodes.has(x);\n        });\n        this.graphManager.setAllNodesToApplyGravitation(intersection);\n\n        this.positionNodesRandomly();\n      }\n  } else {\n    if (CoSEConstants.TREE_REDUCTION_ON_INCREMENTAL) {\n      // Reduce the trees in incremental mode if only this constant is set to true \n      this.reduceTrees();\n      // Update nodes that gravity will be applied\n      this.graphManager.resetAllNodesToApplyGravitation();\n      var allNodes = new Set(this.getAllNodes());\n      var intersection = this.nodesWithGravity.filter(function (x) {\n        return allNodes.has(x);\n      });\n      this.graphManager.setAllNodesToApplyGravitation(intersection);\n    }\n  }\n\n  this.initSpringEmbedder();\n  this.runSpringEmbedder();\n\n  return true;\n};\n\nCoSELayout.prototype.tick = function () {\n  this.totalIterations++;\n\n  if (this.totalIterations === this.maxIterations && !this.isTreeGrowing && !this.isGrowthFinished) {\n    if (this.prunedNodesAll.length > 0) {\n      this.isTreeGrowing = true;\n    } else {\n      return true;\n    }\n  }\n\n  if (this.totalIterations % FDLayoutConstants.CONVERGENCE_CHECK_PERIOD == 0 && !this.isTreeGrowing && !this.isGrowthFinished) {\n    if (this.isConverged()) {\n      if (this.prunedNodesAll.length > 0) {\n        this.isTreeGrowing = true;\n      } else {\n        return true;\n      }\n    }\n\n    this.coolingCycle++;\n\n    if (this.layoutQuality == 0) {\n      // quality - \"draft\"\n      this.coolingAdjuster = this.coolingCycle;\n    } else if (this.layoutQuality == 1) {\n      // quality - \"default\"\n      this.coolingAdjuster = this.coolingCycle / 3;\n    }\n\n    // cooling schedule is based on http://www.btluke.com/simanf1.html -> cooling schedule 3\n    this.coolingFactor = Math.max(this.initialCoolingFactor - Math.pow(this.coolingCycle, Math.log(100 * (this.initialCoolingFactor - this.finalTemperature)) / Math.log(this.maxCoolingCycle)) / 100 * this.coolingAdjuster, this.finalTemperature);\n    this.animationPeriod = Math.ceil(this.initialAnimationPeriod * Math.sqrt(this.coolingFactor));\n  }\n  // Operations while tree is growing again \n  if (this.isTreeGrowing) {\n    if (this.growTreeIterations % 10 == 0) {\n      if (this.prunedNodesAll.length > 0) {\n        this.graphManager.updateBounds();\n        this.updateGrid();\n        this.growTree(this.prunedNodesAll);\n        // Update nodes that gravity will be applied\n        this.graphManager.resetAllNodesToApplyGravitation();\n        var allNodes = new Set(this.getAllNodes());\n        var intersection = this.nodesWithGravity.filter(function (x) {\n          return allNodes.has(x);\n        });\n        this.graphManager.setAllNodesToApplyGravitation(intersection);\n\n        this.graphManager.updateBounds();\n        this.updateGrid();\n        this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n      } else {\n        this.isTreeGrowing = false;\n        this.isGrowthFinished = true;\n      }\n    }\n    this.growTreeIterations++;\n  }\n  // Operations after growth is finished\n  if (this.isGrowthFinished) {\n    if (this.isConverged()) {\n      return true;\n    }\n    if (this.afterGrowthIterations % 10 == 0) {\n      this.graphManager.updateBounds();\n      this.updateGrid();\n    }\n    this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL * ((100 - this.afterGrowthIterations) / 100);\n    this.afterGrowthIterations++;\n  }\n\n  var gridUpdateAllowed = !this.isTreeGrowing && !this.isGrowthFinished;\n  var forceToNodeSurroundingUpdate = this.growTreeIterations % 10 == 1 && this.isTreeGrowing || this.afterGrowthIterations % 10 == 1 && this.isGrowthFinished;\n\n  this.totalDisplacement = 0;\n  this.graphManager.updateBounds();\n  this.calcSpringForces();\n  this.calcRepulsionForces(gridUpdateAllowed, forceToNodeSurroundingUpdate);\n  this.calcGravitationalForces();\n  this.moveNodes();\n  this.animate();\n\n  return false; // Layout is not ended yet return false\n};\n\nCoSELayout.prototype.getPositionsData = function () {\n  var allNodes = this.graphManager.getAllNodes();\n  var pData = {};\n  for (var i = 0; i < allNodes.length; i++) {\n    var rect = allNodes[i].rect;\n    var id = allNodes[i].id;\n    pData[id] = {\n      id: id,\n      x: rect.getCenterX(),\n      y: rect.getCenterY(),\n      w: rect.width,\n      h: rect.height\n    };\n  }\n\n  return pData;\n};\n\nCoSELayout.prototype.runSpringEmbedder = function () {\n  this.initialAnimationPeriod = 25;\n  this.animationPeriod = this.initialAnimationPeriod;\n  var layoutEnded = false;\n\n  // If aminate option is 'during' signal that layout is supposed to start iterating\n  if (FDLayoutConstants.ANIMATE === 'during') {\n    this.emit('layoutstarted');\n  } else {\n    // If aminate option is 'during' tick() function will be called on index.js\n    while (!layoutEnded) {\n      layoutEnded = this.tick();\n    }\n\n    this.graphManager.updateBounds();\n  }\n};\n\nCoSELayout.prototype.calculateNodesToApplyGravitationTo = function () {\n  var nodeList = [];\n  var graph;\n\n  var graphs = this.graphManager.getGraphs();\n  var size = graphs.length;\n  var i;\n  for (i = 0; i < size; i++) {\n    graph = graphs[i];\n\n    graph.updateConnected();\n\n    if (!graph.isConnected) {\n      nodeList = nodeList.concat(graph.getNodes());\n    }\n  }\n\n  return nodeList;\n};\n\nCoSELayout.prototype.createBendpoints = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  var visited = new Set();\n  var i;\n  for (i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n\n    if (!visited.has(edge)) {\n      var source = edge.getSource();\n      var target = edge.getTarget();\n\n      if (source == target) {\n        edge.getBendpoints().push(new PointD());\n        edge.getBendpoints().push(new PointD());\n        this.createDummyNodesForBendpoints(edge);\n        visited.add(edge);\n      } else {\n        var edgeList = [];\n\n        edgeList = edgeList.concat(source.getEdgeListToNode(target));\n        edgeList = edgeList.concat(target.getEdgeListToNode(source));\n\n        if (!visited.has(edgeList[0])) {\n          if (edgeList.length > 1) {\n            var k;\n            for (k = 0; k < edgeList.length; k++) {\n              var multiEdge = edgeList[k];\n              multiEdge.getBendpoints().push(new PointD());\n              this.createDummyNodesForBendpoints(multiEdge);\n            }\n          }\n          edgeList.forEach(function (edge) {\n            visited.add(edge);\n          });\n        }\n      }\n    }\n\n    if (visited.size == edges.length) {\n      break;\n    }\n  }\n};\n\nCoSELayout.prototype.positionNodesRadially = function (forest) {\n  // We tile the trees to a grid row by row; first tree starts at (0,0)\n  var currentStartingPoint = new Point(0, 0);\n  var numberOfColumns = Math.ceil(Math.sqrt(forest.length));\n  var height = 0;\n  var currentY = 0;\n  var currentX = 0;\n  var point = new PointD(0, 0);\n\n  for (var i = 0; i < forest.length; i++) {\n    if (i % numberOfColumns == 0) {\n      // Start of a new row, make the x coordinate 0, increment the\n      // y coordinate with the max height of the previous row\n      currentX = 0;\n      currentY = height;\n\n      if (i != 0) {\n        currentY += CoSEConstants.DEFAULT_COMPONENT_SEPERATION;\n      }\n\n      height = 0;\n    }\n\n    var tree = forest[i];\n\n    // Find the center of the tree\n    var centerNode = Layout.findCenterOfTree(tree);\n\n    // Set the staring point of the next tree\n    currentStartingPoint.x = currentX;\n    currentStartingPoint.y = currentY;\n\n    // Do a radial layout starting with the center\n    point = CoSELayout.radialLayout(tree, centerNode, currentStartingPoint);\n\n    if (point.y > height) {\n      height = Math.floor(point.y);\n    }\n\n    currentX = Math.floor(point.x + CoSEConstants.DEFAULT_COMPONENT_SEPERATION);\n  }\n\n  this.transform(new PointD(LayoutConstants.WORLD_CENTER_X - point.x / 2, LayoutConstants.WORLD_CENTER_Y - point.y / 2));\n};\n\nCoSELayout.radialLayout = function (tree, centerNode, startingPoint) {\n  var radialSep = Math.max(this.maxDiagonalInTree(tree), CoSEConstants.DEFAULT_RADIAL_SEPARATION);\n  CoSELayout.branchRadialLayout(centerNode, null, 0, 359, 0, radialSep);\n  var bounds = LGraph.calculateBounds(tree);\n\n  var transform = new Transform();\n  transform.setDeviceOrgX(bounds.getMinX());\n  transform.setDeviceOrgY(bounds.getMinY());\n  transform.setWorldOrgX(startingPoint.x);\n  transform.setWorldOrgY(startingPoint.y);\n\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    node.transform(transform);\n  }\n\n  var bottomRight = new PointD(bounds.getMaxX(), bounds.getMaxY());\n\n  return transform.inverseTransformPoint(bottomRight);\n};\n\nCoSELayout.branchRadialLayout = function (node, parentOfNode, startAngle, endAngle, distance, radialSeparation) {\n  // First, position this node by finding its angle.\n  var halfInterval = (endAngle - startAngle + 1) / 2;\n\n  if (halfInterval < 0) {\n    halfInterval += 180;\n  }\n\n  var nodeAngle = (halfInterval + startAngle) % 360;\n  var teta = nodeAngle * IGeometry.TWO_PI / 360;\n\n  // Make polar to java cordinate conversion.\n  var cos_teta = Math.cos(teta);\n  var x_ = distance * Math.cos(teta);\n  var y_ = distance * Math.sin(teta);\n\n  node.setCenter(x_, y_);\n\n  // Traverse all neighbors of this node and recursively call this\n  // function.\n  var neighborEdges = [];\n  neighborEdges = neighborEdges.concat(node.getEdges());\n  var childCount = neighborEdges.length;\n\n  if (parentOfNode != null) {\n    childCount--;\n  }\n\n  var branchCount = 0;\n\n  var incEdgesCount = neighborEdges.length;\n  var startIndex;\n\n  var edges = node.getEdgesBetween(parentOfNode);\n\n  // If there are multiple edges, prune them until there remains only one\n  // edge.\n  while (edges.length > 1) {\n    //neighborEdges.remove(edges.remove(0));\n    var temp = edges[0];\n    edges.splice(0, 1);\n    var index = neighborEdges.indexOf(temp);\n    if (index >= 0) {\n      neighborEdges.splice(index, 1);\n    }\n    incEdgesCount--;\n    childCount--;\n  }\n\n  if (parentOfNode != null) {\n    //assert edges.length == 1;\n    startIndex = (neighborEdges.indexOf(edges[0]) + 1) % incEdgesCount;\n  } else {\n    startIndex = 0;\n  }\n\n  var stepAngle = Math.abs(endAngle - startAngle) / childCount;\n\n  for (var i = startIndex; branchCount != childCount; i = ++i % incEdgesCount) {\n    var currentNeighbor = neighborEdges[i].getOtherEnd(node);\n\n    // Don't back traverse to root node in current tree.\n    if (currentNeighbor == parentOfNode) {\n      continue;\n    }\n\n    var childStartAngle = (startAngle + branchCount * stepAngle) % 360;\n    var childEndAngle = (childStartAngle + stepAngle) % 360;\n\n    CoSELayout.branchRadialLayout(currentNeighbor, node, childStartAngle, childEndAngle, distance + radialSeparation, radialSeparation);\n\n    branchCount++;\n  }\n};\n\nCoSELayout.maxDiagonalInTree = function (tree) {\n  var maxDiagonal = Integer.MIN_VALUE;\n\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    var diagonal = node.getDiagonal();\n\n    if (diagonal > maxDiagonal) {\n      maxDiagonal = diagonal;\n    }\n  }\n\n  return maxDiagonal;\n};\n\nCoSELayout.prototype.calcRepulsionRange = function () {\n  // formula is 2 x (level + 1) x idealEdgeLength\n  return 2 * (this.level + 1) * this.idealEdgeLength;\n};\n\n// Tiling methods\n\n// Group zero degree members whose parents are not to be tiled, create dummy parents where needed and fill memberGroups by their dummp parent id's\nCoSELayout.prototype.groupZeroDegreeMembers = function () {\n  var self = this;\n  // array of [parent_id x oneDegreeNode_id]\n  var tempMemberGroups = {}; // A temporary map of parent node and its zero degree members\n  this.memberGroups = {}; // A map of dummy parent node and its zero degree members whose parents are not to be tiled\n  this.idToDummyNode = {}; // A map of id to dummy node \n\n  var zeroDegree = []; // List of zero degree nodes whose parents are not to be tiled\n  var allNodes = this.graphManager.getAllNodes();\n\n  // Fill zero degree list\n  for (var i = 0; i < allNodes.length; i++) {\n    var node = allNodes[i];\n    var parent = node.getParent();\n    // If a node has zero degree and its parent is not to be tiled if exists add that node to zeroDegres list\n    if (this.getNodeDegreeWithChildren(node) === 0 && (parent.id == undefined || !this.getToBeTiled(parent))) {\n      zeroDegree.push(node);\n    }\n  }\n\n  // Create a map of parent node and its zero degree members\n  for (var i = 0; i < zeroDegree.length; i++) {\n    var node = zeroDegree[i]; // Zero degree node itself\n    var p_id = node.getParent().id; // Parent id\n\n    if (typeof tempMemberGroups[p_id] === \"undefined\") tempMemberGroups[p_id] = [];\n\n    tempMemberGroups[p_id] = tempMemberGroups[p_id].concat(node); // Push node to the list belongs to its parent in tempMemberGroups\n  }\n\n  // If there are at least two nodes at a level, create a dummy compound for them\n  Object.keys(tempMemberGroups).forEach(function (p_id) {\n    if (tempMemberGroups[p_id].length > 1) {\n      var dummyCompoundId = \"DummyCompound_\" + p_id; // The id of dummy compound which will be created soon\n      self.memberGroups[dummyCompoundId] = tempMemberGroups[p_id]; // Add dummy compound to memberGroups\n\n      var parent = tempMemberGroups[p_id][0].getParent(); // The parent of zero degree nodes will be the parent of new dummy compound\n\n      // Create a dummy compound with calculated id\n      var dummyCompound = new CoSENode(self.graphManager);\n      dummyCompound.id = dummyCompoundId;\n      dummyCompound.paddingLeft = parent.paddingLeft || 0;\n      dummyCompound.paddingRight = parent.paddingRight || 0;\n      dummyCompound.paddingBottom = parent.paddingBottom || 0;\n      dummyCompound.paddingTop = parent.paddingTop || 0;\n\n      self.idToDummyNode[dummyCompoundId] = dummyCompound;\n\n      var dummyParentGraph = self.getGraphManager().add(self.newGraph(), dummyCompound);\n      var parentGraph = parent.getChild();\n\n      // Add dummy compound to parent the graph\n      parentGraph.add(dummyCompound);\n\n      // For each zero degree node in this level remove it from its parent graph and add it to the graph of dummy parent\n      for (var i = 0; i < tempMemberGroups[p_id].length; i++) {\n        var node = tempMemberGroups[p_id][i];\n\n        parentGraph.remove(node);\n        dummyParentGraph.add(node);\n      }\n    }\n  });\n};\n\nCoSELayout.prototype.clearCompounds = function () {\n  var childGraphMap = {};\n  var idToNode = {};\n\n  // Get compound ordering by finding the inner one first\n  this.performDFSOnCompounds();\n\n  for (var i = 0; i < this.compoundOrder.length; i++) {\n\n    idToNode[this.compoundOrder[i].id] = this.compoundOrder[i];\n    childGraphMap[this.compoundOrder[i].id] = [].concat(this.compoundOrder[i].getChild().getNodes());\n\n    // Remove children of compounds\n    this.graphManager.remove(this.compoundOrder[i].getChild());\n    this.compoundOrder[i].child = null;\n  }\n\n  this.graphManager.resetAllNodes();\n\n  // Tile the removed children\n  this.tileCompoundMembers(childGraphMap, idToNode);\n};\n\nCoSELayout.prototype.clearZeroDegreeMembers = function () {\n  var self = this;\n  var tiledZeroDegreePack = this.tiledZeroDegreePack = [];\n\n  Object.keys(this.memberGroups).forEach(function (id) {\n    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound\n\n    tiledZeroDegreePack[id] = self.tileNodes(self.memberGroups[id], compoundNode.paddingLeft + compoundNode.paddingRight);\n\n    // Set the width and height of the dummy compound as calculated\n    compoundNode.rect.width = tiledZeroDegreePack[id].width;\n    compoundNode.rect.height = tiledZeroDegreePack[id].height;\n  });\n};\n\nCoSELayout.prototype.repopulateCompounds = function () {\n  for (var i = this.compoundOrder.length - 1; i >= 0; i--) {\n    var lCompoundNode = this.compoundOrder[i];\n    var id = lCompoundNode.id;\n    var horizontalMargin = lCompoundNode.paddingLeft;\n    var verticalMargin = lCompoundNode.paddingTop;\n\n    this.adjustLocations(this.tiledMemberPack[id], lCompoundNode.rect.x, lCompoundNode.rect.y, horizontalMargin, verticalMargin);\n  }\n};\n\nCoSELayout.prototype.repopulateZeroDegreeMembers = function () {\n  var self = this;\n  var tiledPack = this.tiledZeroDegreePack;\n\n  Object.keys(tiledPack).forEach(function (id) {\n    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound by its id\n    var horizontalMargin = compoundNode.paddingLeft;\n    var verticalMargin = compoundNode.paddingTop;\n\n    // Adjust the positions of nodes wrt its compound\n    self.adjustLocations(tiledPack[id], compoundNode.rect.x, compoundNode.rect.y, horizontalMargin, verticalMargin);\n  });\n};\n\nCoSELayout.prototype.getToBeTiled = function (node) {\n  var id = node.id;\n  //firstly check the previous results\n  if (this.toBeTiled[id] != null) {\n    return this.toBeTiled[id];\n  }\n\n  //only compound nodes are to be tiled\n  var childGraph = node.getChild();\n  if (childGraph == null) {\n    this.toBeTiled[id] = false;\n    return false;\n  }\n\n  var children = childGraph.getNodes(); // Get the children nodes\n\n  //a compound node is not to be tiled if all of its compound children are not to be tiled\n  for (var i = 0; i < children.length; i++) {\n    var theChild = children[i];\n\n    if (this.getNodeDegree(theChild) > 0) {\n      this.toBeTiled[id] = false;\n      return false;\n    }\n\n    //pass the children not having the compound structure\n    if (theChild.getChild() == null) {\n      this.toBeTiled[theChild.id] = false;\n      continue;\n    }\n\n    if (!this.getToBeTiled(theChild)) {\n      this.toBeTiled[id] = false;\n      return false;\n    }\n  }\n  this.toBeTiled[id] = true;\n  return true;\n};\n\n// Get degree of a node depending of its edges and independent of its children\nCoSELayout.prototype.getNodeDegree = function (node) {\n  var id = node.id;\n  var edges = node.getEdges();\n  var degree = 0;\n\n  // For the edges connected\n  for (var i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n    if (edge.getSource().id !== edge.getTarget().id) {\n      degree = degree + 1;\n    }\n  }\n  return degree;\n};\n\n// Get degree of a node with its children\nCoSELayout.prototype.getNodeDegreeWithChildren = function (node) {\n  var degree = this.getNodeDegree(node);\n  if (node.getChild() == null) {\n    return degree;\n  }\n  var children = node.getChild().getNodes();\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    degree += this.getNodeDegreeWithChildren(child);\n  }\n  return degree;\n};\n\nCoSELayout.prototype.performDFSOnCompounds = function () {\n  this.compoundOrder = [];\n  this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes());\n};\n\nCoSELayout.prototype.fillCompexOrderByDFS = function (children) {\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    if (child.getChild() != null) {\n      this.fillCompexOrderByDFS(child.getChild().getNodes());\n    }\n    if (this.getToBeTiled(child)) {\n      this.compoundOrder.push(child);\n    }\n  }\n};\n\n/**\n* This method places each zero degree member wrt given (x,y) coordinates (top left).\n*/\nCoSELayout.prototype.adjustLocations = function (organization, x, y, compoundHorizontalMargin, compoundVerticalMargin) {\n  x += compoundHorizontalMargin;\n  y += compoundVerticalMargin;\n\n  var left = x;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n    var row = organization.rows[i];\n    x = left;\n    var maxHeight = 0;\n\n    for (var j = 0; j < row.length; j++) {\n      var lnode = row[j];\n\n      lnode.rect.x = x; // + lnode.rect.width / 2;\n      lnode.rect.y = y; // + lnode.rect.height / 2;\n\n      x += lnode.rect.width + organization.horizontalPadding;\n\n      if (lnode.rect.height > maxHeight) maxHeight = lnode.rect.height;\n    }\n\n    y += maxHeight + organization.verticalPadding;\n  }\n};\n\nCoSELayout.prototype.tileCompoundMembers = function (childGraphMap, idToNode) {\n  var self = this;\n  this.tiledMemberPack = [];\n\n  Object.keys(childGraphMap).forEach(function (id) {\n    // Get the compound node\n    var compoundNode = idToNode[id];\n\n    self.tiledMemberPack[id] = self.tileNodes(childGraphMap[id], compoundNode.paddingLeft + compoundNode.paddingRight);\n\n    compoundNode.rect.width = self.tiledMemberPack[id].width;\n    compoundNode.rect.height = self.tiledMemberPack[id].height;\n  });\n};\n\nCoSELayout.prototype.tileNodes = function (nodes, minWidth) {\n  var verticalPadding = CoSEConstants.TILING_PADDING_VERTICAL;\n  var horizontalPadding = CoSEConstants.TILING_PADDING_HORIZONTAL;\n  var organization = {\n    rows: [],\n    rowWidth: [],\n    rowHeight: [],\n    width: 0,\n    height: minWidth, // assume minHeight equals to minWidth\n    verticalPadding: verticalPadding,\n    horizontalPadding: horizontalPadding\n  };\n\n  // Sort the nodes in ascending order of their areas\n  nodes.sort(function (n1, n2) {\n    if (n1.rect.width * n1.rect.height > n2.rect.width * n2.rect.height) return -1;\n    if (n1.rect.width * n1.rect.height < n2.rect.width * n2.rect.height) return 1;\n    return 0;\n  });\n\n  // Create the organization -> tile members\n  for (var i = 0; i < nodes.length; i++) {\n    var lNode = nodes[i];\n\n    if (organization.rows.length == 0) {\n      this.insertNodeToRow(organization, lNode, 0, minWidth);\n    } else if (this.canAddHorizontal(organization, lNode.rect.width, lNode.rect.height)) {\n      this.insertNodeToRow(organization, lNode, this.getShortestRowIndex(organization), minWidth);\n    } else {\n      this.insertNodeToRow(organization, lNode, organization.rows.length, minWidth);\n    }\n\n    this.shiftToLastRow(organization);\n  }\n\n  return organization;\n};\n\nCoSELayout.prototype.insertNodeToRow = function (organization, node, rowIndex, minWidth) {\n  var minCompoundSize = minWidth;\n\n  // Add new row if needed\n  if (rowIndex == organization.rows.length) {\n    var secondDimension = [];\n\n    organization.rows.push(secondDimension);\n    organization.rowWidth.push(minCompoundSize);\n    organization.rowHeight.push(0);\n  }\n\n  // Update row width\n  var w = organization.rowWidth[rowIndex] + node.rect.width;\n\n  if (organization.rows[rowIndex].length > 0) {\n    w += organization.horizontalPadding;\n  }\n\n  organization.rowWidth[rowIndex] = w;\n  // Update compound width\n  if (organization.width < w) {\n    organization.width = w;\n  }\n\n  // Update height\n  var h = node.rect.height;\n  if (rowIndex > 0) h += organization.verticalPadding;\n\n  var extraHeight = 0;\n  if (h > organization.rowHeight[rowIndex]) {\n    extraHeight = organization.rowHeight[rowIndex];\n    organization.rowHeight[rowIndex] = h;\n    extraHeight = organization.rowHeight[rowIndex] - extraHeight;\n  }\n\n  organization.height += extraHeight;\n\n  // Insert node\n  organization.rows[rowIndex].push(node);\n};\n\n//Scans the rows of an organization and returns the one with the min width\nCoSELayout.prototype.getShortestRowIndex = function (organization) {\n  var r = -1;\n  var min = Number.MAX_VALUE;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n    if (organization.rowWidth[i] < min) {\n      r = i;\n      min = organization.rowWidth[i];\n    }\n  }\n  return r;\n};\n\n//Scans the rows of an organization and returns the one with the max width\nCoSELayout.prototype.getLongestRowIndex = function (organization) {\n  var r = -1;\n  var max = Number.MIN_VALUE;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n\n    if (organization.rowWidth[i] > max) {\n      r = i;\n      max = organization.rowWidth[i];\n    }\n  }\n\n  return r;\n};\n\n/**\n* This method checks whether adding extra width to the organization violates\n* the aspect ratio(1) or not.\n*/\nCoSELayout.prototype.canAddHorizontal = function (organization, extraWidth, extraHeight) {\n\n  var sri = this.getShortestRowIndex(organization);\n\n  if (sri < 0) {\n    return true;\n  }\n\n  var min = organization.rowWidth[sri];\n\n  if (min + organization.horizontalPadding + extraWidth <= organization.width) return true;\n\n  var hDiff = 0;\n\n  // Adding to an existing row\n  if (organization.rowHeight[sri] < extraHeight) {\n    if (sri > 0) hDiff = extraHeight + organization.verticalPadding - organization.rowHeight[sri];\n  }\n\n  var add_to_row_ratio;\n  if (organization.width - min >= extraWidth + organization.horizontalPadding) {\n    add_to_row_ratio = (organization.height + hDiff) / (min + extraWidth + organization.horizontalPadding);\n  } else {\n    add_to_row_ratio = (organization.height + hDiff) / organization.width;\n  }\n\n  // Adding a new row for this node\n  hDiff = extraHeight + organization.verticalPadding;\n  var add_new_row_ratio;\n  if (organization.width < extraWidth) {\n    add_new_row_ratio = (organization.height + hDiff) / extraWidth;\n  } else {\n    add_new_row_ratio = (organization.height + hDiff) / organization.width;\n  }\n\n  if (add_new_row_ratio < 1) add_new_row_ratio = 1 / add_new_row_ratio;\n\n  if (add_to_row_ratio < 1) add_to_row_ratio = 1 / add_to_row_ratio;\n\n  return add_to_row_ratio < add_new_row_ratio;\n};\n\n//If moving the last node from the longest row and adding it to the last\n//row makes the bounding box smaller, do it.\nCoSELayout.prototype.shiftToLastRow = function (organization) {\n  var longest = this.getLongestRowIndex(organization);\n  var last = organization.rowWidth.length - 1;\n  var row = organization.rows[longest];\n  var node = row[row.length - 1];\n\n  var diff = node.width + organization.horizontalPadding;\n\n  // Check if there is enough space on the last row\n  if (organization.width - organization.rowWidth[last] > diff && longest != last) {\n    // Remove the last element of the longest row\n    row.splice(-1, 1);\n\n    // Push it to the last row\n    organization.rows[last].push(node);\n\n    organization.rowWidth[longest] = organization.rowWidth[longest] - diff;\n    organization.rowWidth[last] = organization.rowWidth[last] + diff;\n    organization.width = organization.rowWidth[instance.getLongestRowIndex(organization)];\n\n    // Update heights of the organization\n    var maxHeight = Number.MIN_VALUE;\n    for (var i = 0; i < row.length; i++) {\n      if (row[i].height > maxHeight) maxHeight = row[i].height;\n    }\n    if (longest > 0) maxHeight += organization.verticalPadding;\n\n    var prevTotal = organization.rowHeight[longest] + organization.rowHeight[last];\n\n    organization.rowHeight[longest] = maxHeight;\n    if (organization.rowHeight[last] < node.height + organization.verticalPadding) organization.rowHeight[last] = node.height + organization.verticalPadding;\n\n    var finalTotal = organization.rowHeight[longest] + organization.rowHeight[last];\n    organization.height += finalTotal - prevTotal;\n\n    this.shiftToLastRow(organization);\n  }\n};\n\nCoSELayout.prototype.tilingPreLayout = function () {\n  if (CoSEConstants.TILE) {\n    // Find zero degree nodes and create a compound for each level\n    this.groupZeroDegreeMembers();\n    // Tile and clear children of each compound\n    this.clearCompounds();\n    // Separately tile and clear zero degree nodes for each level\n    this.clearZeroDegreeMembers();\n  }\n};\n\nCoSELayout.prototype.tilingPostLayout = function () {\n  if (CoSEConstants.TILE) {\n    this.repopulateZeroDegreeMembers();\n    this.repopulateCompounds();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: Tree Reduction methods\n// -----------------------------------------------------------------------------\n// Reduce trees \nCoSELayout.prototype.reduceTrees = function () {\n  var prunedNodesAll = [];\n  var containsLeaf = true;\n  var node;\n\n  while (containsLeaf) {\n    var allNodes = this.graphManager.getAllNodes();\n    var prunedNodesInStepTemp = [];\n    containsLeaf = false;\n\n    for (var i = 0; i < allNodes.length; i++) {\n      node = allNodes[i];\n      if (node.getEdges().length == 1 && !node.getEdges()[0].isInterGraph && node.getChild() == null) {\n        prunedNodesInStepTemp.push([node, node.getEdges()[0], node.getOwner()]);\n        containsLeaf = true;\n      }\n    }\n    if (containsLeaf == true) {\n      var prunedNodesInStep = [];\n      for (var j = 0; j < prunedNodesInStepTemp.length; j++) {\n        if (prunedNodesInStepTemp[j][0].getEdges().length == 1) {\n          prunedNodesInStep.push(prunedNodesInStepTemp[j]);\n          prunedNodesInStepTemp[j][0].getOwner().remove(prunedNodesInStepTemp[j][0]);\n        }\n      }\n      prunedNodesAll.push(prunedNodesInStep);\n      this.graphManager.resetAllNodes();\n      this.graphManager.resetAllEdges();\n    }\n  }\n  this.prunedNodesAll = prunedNodesAll;\n};\n\n// Grow tree one step \nCoSELayout.prototype.growTree = function (prunedNodesAll) {\n  var lengthOfPrunedNodesInStep = prunedNodesAll.length;\n  var prunedNodesInStep = prunedNodesAll[lengthOfPrunedNodesInStep - 1];\n\n  var nodeData;\n  for (var i = 0; i < prunedNodesInStep.length; i++) {\n    nodeData = prunedNodesInStep[i];\n\n    this.findPlaceforPrunedNode(nodeData);\n\n    nodeData[2].add(nodeData[0]);\n    nodeData[2].add(nodeData[1], nodeData[1].source, nodeData[1].target);\n  }\n\n  prunedNodesAll.splice(prunedNodesAll.length - 1, 1);\n  this.graphManager.resetAllNodes();\n  this.graphManager.resetAllEdges();\n};\n\n// Find an appropriate position to replace pruned node, this method can be improved\nCoSELayout.prototype.findPlaceforPrunedNode = function (nodeData) {\n\n  var gridForPrunedNode;\n  var nodeToConnect;\n  var prunedNode = nodeData[0];\n  if (prunedNode == nodeData[1].source) {\n    nodeToConnect = nodeData[1].target;\n  } else {\n    nodeToConnect = nodeData[1].source;\n  }\n  var startGridX = nodeToConnect.startX;\n  var finishGridX = nodeToConnect.finishX;\n  var startGridY = nodeToConnect.startY;\n  var finishGridY = nodeToConnect.finishY;\n\n  var upNodeCount = 0;\n  var downNodeCount = 0;\n  var rightNodeCount = 0;\n  var leftNodeCount = 0;\n  var controlRegions = [upNodeCount, rightNodeCount, downNodeCount, leftNodeCount];\n\n  if (startGridY > 0) {\n    for (var i = startGridX; i <= finishGridX; i++) {\n      controlRegions[0] += this.grid[i][startGridY - 1].length + this.grid[i][startGridY].length - 1;\n    }\n  }\n  if (finishGridX < this.grid.length - 1) {\n    for (var i = startGridY; i <= finishGridY; i++) {\n      controlRegions[1] += this.grid[finishGridX + 1][i].length + this.grid[finishGridX][i].length - 1;\n    }\n  }\n  if (finishGridY < this.grid[0].length - 1) {\n    for (var i = startGridX; i <= finishGridX; i++) {\n      controlRegions[2] += this.grid[i][finishGridY + 1].length + this.grid[i][finishGridY].length - 1;\n    }\n  }\n  if (startGridX > 0) {\n    for (var i = startGridY; i <= finishGridY; i++) {\n      controlRegions[3] += this.grid[startGridX - 1][i].length + this.grid[startGridX][i].length - 1;\n    }\n  }\n  var min = Integer.MAX_VALUE;\n  var minCount;\n  var minIndex;\n  for (var j = 0; j < controlRegions.length; j++) {\n    if (controlRegions[j] < min) {\n      min = controlRegions[j];\n      minCount = 1;\n      minIndex = j;\n    } else if (controlRegions[j] == min) {\n      minCount++;\n    }\n  }\n\n  if (minCount == 3 && min == 0) {\n    if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[2] == 0) {\n      gridForPrunedNode = 1;\n    } else if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[3] == 0) {\n      gridForPrunedNode = 0;\n    } else if (controlRegions[0] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {\n      gridForPrunedNode = 3;\n    } else if (controlRegions[1] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {\n      gridForPrunedNode = 2;\n    }\n  } else if (minCount == 2 && min == 0) {\n    var random = Math.floor(Math.random() * 2);\n    if (controlRegions[0] == 0 && controlRegions[1] == 0) {\n      ;\n      if (random == 0) {\n        gridForPrunedNode = 0;\n      } else {\n        gridForPrunedNode = 1;\n      }\n    } else if (controlRegions[0] == 0 && controlRegions[2] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 0;\n      } else {\n        gridForPrunedNode = 2;\n      }\n    } else if (controlRegions[0] == 0 && controlRegions[3] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 0;\n      } else {\n        gridForPrunedNode = 3;\n      }\n    } else if (controlRegions[1] == 0 && controlRegions[2] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 1;\n      } else {\n        gridForPrunedNode = 2;\n      }\n    } else if (controlRegions[1] == 0 && controlRegions[3] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 1;\n      } else {\n        gridForPrunedNode = 3;\n      }\n    } else {\n      if (random == 0) {\n        gridForPrunedNode = 2;\n      } else {\n        gridForPrunedNode = 3;\n      }\n    }\n  } else if (minCount == 4 && min == 0) {\n    var random = Math.floor(Math.random() * 4);\n    gridForPrunedNode = random;\n  } else {\n    gridForPrunedNode = minIndex;\n  }\n\n  if (gridForPrunedNode == 0) {\n    prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() - nodeToConnect.getHeight() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getHeight() / 2);\n  } else if (gridForPrunedNode == 1) {\n    prunedNode.setCenter(nodeToConnect.getCenterX() + nodeToConnect.getWidth() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getWidth() / 2, nodeToConnect.getCenterY());\n  } else if (gridForPrunedNode == 2) {\n    prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() + nodeToConnect.getHeight() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getHeight() / 2);\n  } else {\n    prunedNode.setCenter(nodeToConnect.getCenterX() - nodeToConnect.getWidth() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getWidth() / 2, nodeToConnect.getCenterY());\n  }\n};\n\nmodule.exports = CoSELayout;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar coseBase = {};\n\ncoseBase.layoutBase = __webpack_require__(0);\ncoseBase.CoSEConstants = __webpack_require__(1);\ncoseBase.CoSEEdge = __webpack_require__(2);\ncoseBase.CoSEGraph = __webpack_require__(3);\ncoseBase.CoSEGraphManager = __webpack_require__(4);\ncoseBase.CoSELayout = __webpack_require__(6);\ncoseBase.CoSENode = __webpack_require__(5);\n\nmodule.exports = coseBase;\n\n/***/ })\n/******/ ]);\n});", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"cose-base\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"cose-base\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"cytoscapeCoseBilkent\"] = factory(require(\"cose-base\"));\n\telse\n\t\troot[\"cytoscapeCoseBilkent\"] = factory(root[\"coseBase\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_0__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 1);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_0__;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __webpack_require__(0).layoutBase.LayoutConstants;\nvar FDLayoutConstants = __webpack_require__(0).layoutBase.FDLayoutConstants;\nvar CoSEConstants = __webpack_require__(0).CoSEConstants;\nvar CoSELayout = __webpack_require__(0).CoSELayout;\nvar CoSENode = __webpack_require__(0).CoSENode;\nvar PointD = __webpack_require__(0).layoutBase.PointD;\nvar DimensionD = __webpack_require__(0).layoutBase.DimensionD;\n\nvar defaults = {\n  // Called on `layoutready`\n  ready: function ready() {},\n  // Called on `layoutstop`\n  stop: function stop() {},\n  // 'draft', 'default' or 'proof\" \n  // - 'draft' fast cooling rate \n  // - 'default' moderate cooling rate \n  // - \"proof\" slow cooling rate\n  quality: 'default',\n  // include labels in node dimensions\n  nodeDimensionsIncludeLabels: false,\n  // number of ticks per frame; higher is faster but more jerky\n  refresh: 30,\n  // Whether to fit the network view after when done\n  fit: true,\n  // Padding on fit\n  padding: 10,\n  // Whether to enable incremental mode\n  randomize: true,\n  // Node repulsion (non overlapping) multiplier\n  nodeRepulsion: 4500,\n  // Ideal edge (non nested) length\n  idealEdgeLength: 50,\n  // Divisor to compute edge forces\n  edgeElasticity: 0.45,\n  // Nesting factor (multiplier) to compute ideal edge length for nested edges\n  nestingFactor: 0.1,\n  // Gravity force (constant)\n  gravity: 0.25,\n  // Maximum number of iterations to perform\n  numIter: 2500,\n  // For enabling tiling\n  tile: true,\n  // Type of layout animation. The option set is {'during', 'end', false}\n  animate: 'end',\n  // Duration for animate:end\n  animationDuration: 500,\n  // Represents the amount of the vertical space to put between the zero degree members during the tiling operation(can also be a function)\n  tilingPaddingVertical: 10,\n  // Represents the amount of the horizontal space to put between the zero degree members during the tiling operation(can also be a function)\n  tilingPaddingHorizontal: 10,\n  // Gravity range (constant) for compounds\n  gravityRangeCompound: 1.5,\n  // Gravity force (constant) for compounds\n  gravityCompound: 1.0,\n  // Gravity range (constant)\n  gravityRange: 3.8,\n  // Initial cooling factor for incremental layout\n  initialEnergyOnIncremental: 0.5\n};\n\nfunction extend(defaults, options) {\n  var obj = {};\n\n  for (var i in defaults) {\n    obj[i] = defaults[i];\n  }\n\n  for (var i in options) {\n    obj[i] = options[i];\n  }\n\n  return obj;\n};\n\nfunction _CoSELayout(_options) {\n  this.options = extend(defaults, _options);\n  getUserOptions(this.options);\n}\n\nvar getUserOptions = function getUserOptions(options) {\n  if (options.nodeRepulsion != null) CoSEConstants.DEFAULT_REPULSION_STRENGTH = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH = options.nodeRepulsion;\n  if (options.idealEdgeLength != null) CoSEConstants.DEFAULT_EDGE_LENGTH = FDLayoutConstants.DEFAULT_EDGE_LENGTH = options.idealEdgeLength;\n  if (options.edgeElasticity != null) CoSEConstants.DEFAULT_SPRING_STRENGTH = FDLayoutConstants.DEFAULT_SPRING_STRENGTH = options.edgeElasticity;\n  if (options.nestingFactor != null) CoSEConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = options.nestingFactor;\n  if (options.gravity != null) CoSEConstants.DEFAULT_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = options.gravity;\n  if (options.numIter != null) CoSEConstants.MAX_ITERATIONS = FDLayoutConstants.MAX_ITERATIONS = options.numIter;\n  if (options.gravityRange != null) CoSEConstants.DEFAULT_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = options.gravityRange;\n  if (options.gravityCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = options.gravityCompound;\n  if (options.gravityRangeCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = options.gravityRangeCompound;\n  if (options.initialEnergyOnIncremental != null) CoSEConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = options.initialEnergyOnIncremental;\n\n  if (options.quality == 'draft') LayoutConstants.QUALITY = 0;else if (options.quality == 'proof') LayoutConstants.QUALITY = 2;else LayoutConstants.QUALITY = 1;\n\n  CoSEConstants.NODE_DIMENSIONS_INCLUDE_LABELS = FDLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = options.nodeDimensionsIncludeLabels;\n  CoSEConstants.DEFAULT_INCREMENTAL = FDLayoutConstants.DEFAULT_INCREMENTAL = LayoutConstants.DEFAULT_INCREMENTAL = !options.randomize;\n  CoSEConstants.ANIMATE = FDLayoutConstants.ANIMATE = LayoutConstants.ANIMATE = options.animate;\n  CoSEConstants.TILE = options.tile;\n  CoSEConstants.TILING_PADDING_VERTICAL = typeof options.tilingPaddingVertical === 'function' ? options.tilingPaddingVertical.call() : options.tilingPaddingVertical;\n  CoSEConstants.TILING_PADDING_HORIZONTAL = typeof options.tilingPaddingHorizontal === 'function' ? options.tilingPaddingHorizontal.call() : options.tilingPaddingHorizontal;\n};\n\n_CoSELayout.prototype.run = function () {\n  var ready;\n  var frameId;\n  var options = this.options;\n  var idToLNode = this.idToLNode = {};\n  var layout = this.layout = new CoSELayout();\n  var self = this;\n\n  self.stopped = false;\n\n  this.cy = this.options.cy;\n\n  this.cy.trigger({ type: 'layoutstart', layout: this });\n\n  var gm = layout.newGraphManager();\n  this.gm = gm;\n\n  var nodes = this.options.eles.nodes();\n  var edges = this.options.eles.edges();\n\n  this.root = gm.addRoot();\n  this.processChildrenList(this.root, this.getTopMostNodes(nodes), layout);\n\n  for (var i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n    var sourceNode = this.idToLNode[edge.data(\"source\")];\n    var targetNode = this.idToLNode[edge.data(\"target\")];\n    if (sourceNode !== targetNode && sourceNode.getEdgesBetween(targetNode).length == 0) {\n      var e1 = gm.add(layout.newEdge(), sourceNode, targetNode);\n      e1.id = edge.id();\n    }\n  }\n\n  var getPositions = function getPositions(ele, i) {\n    if (typeof ele === \"number\") {\n      ele = i;\n    }\n    var theId = ele.data('id');\n    var lNode = self.idToLNode[theId];\n\n    return {\n      x: lNode.getRect().getCenterX(),\n      y: lNode.getRect().getCenterY()\n    };\n  };\n\n  /*\n   * Reposition nodes in iterations animatedly\n   */\n  var iterateAnimated = function iterateAnimated() {\n    // Thigs to perform after nodes are repositioned on screen\n    var afterReposition = function afterReposition() {\n      if (options.fit) {\n        options.cy.fit(options.eles, options.padding);\n      }\n\n      if (!ready) {\n        ready = true;\n        self.cy.one('layoutready', options.ready);\n        self.cy.trigger({ type: 'layoutready', layout: self });\n      }\n    };\n\n    var ticksPerFrame = self.options.refresh;\n    var isDone;\n\n    for (var i = 0; i < ticksPerFrame && !isDone; i++) {\n      isDone = self.stopped || self.layout.tick();\n    }\n\n    // If layout is done\n    if (isDone) {\n      // If the layout is not a sublayout and it is successful perform post layout.\n      if (layout.checkLayoutSuccess() && !layout.isSubLayout) {\n        layout.doPostLayout();\n      }\n\n      // If layout has a tilingPostLayout function property call it.\n      if (layout.tilingPostLayout) {\n        layout.tilingPostLayout();\n      }\n\n      layout.isLayoutFinished = true;\n\n      self.options.eles.nodes().positions(getPositions);\n\n      afterReposition();\n\n      // trigger layoutstop when the layout stops (e.g. finishes)\n      self.cy.one('layoutstop', self.options.stop);\n      self.cy.trigger({ type: 'layoutstop', layout: self });\n\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n\n      ready = false;\n      return;\n    }\n\n    var animationData = self.layout.getPositionsData(); // Get positions of layout nodes note that all nodes may not be layout nodes because of tiling\n\n    // Position nodes, for the nodes whose id does not included in data (because they are removed from their parents and included in dummy compounds)\n    // use position of their ancestors or dummy ancestors\n    options.eles.nodes().positions(function (ele, i) {\n      if (typeof ele === \"number\") {\n        ele = i;\n      }\n      // If ele is a compound node, then its position will be defined by its children\n      if (!ele.isParent()) {\n        var theId = ele.id();\n        var pNode = animationData[theId];\n        var temp = ele;\n        // If pNode is undefined search until finding position data of its first ancestor (It may be dummy as well)\n        while (pNode == null) {\n          pNode = animationData[temp.data('parent')] || animationData['DummyCompound_' + temp.data('parent')];\n          animationData[theId] = pNode;\n          temp = temp.parent()[0];\n          if (temp == undefined) {\n            break;\n          }\n        }\n        if (pNode != null) {\n          return {\n            x: pNode.x,\n            y: pNode.y\n          };\n        } else {\n          return {\n            x: ele.position('x'),\n            y: ele.position('y')\n          };\n        }\n      }\n    });\n\n    afterReposition();\n\n    frameId = requestAnimationFrame(iterateAnimated);\n  };\n\n  /*\n  * Listen 'layoutstarted' event and start animated iteration if animate option is 'during'\n  */\n  layout.addListener('layoutstarted', function () {\n    if (self.options.animate === 'during') {\n      frameId = requestAnimationFrame(iterateAnimated);\n    }\n  });\n\n  layout.runLayout(); // Run cose layout\n\n  /*\n   * If animate option is not 'during' ('end' or false) perform these here (If it is 'during' similar things are already performed)\n   */\n  if (this.options.animate !== \"during\") {\n    self.options.eles.nodes().not(\":parent\").layoutPositions(self, self.options, getPositions); // Use layout positions to reposition the nodes it considers the options parameter\n    ready = false;\n  }\n\n  return this; // chaining\n};\n\n//Get the top most ones of a list of nodes\n_CoSELayout.prototype.getTopMostNodes = function (nodes) {\n  var nodesMap = {};\n  for (var i = 0; i < nodes.length; i++) {\n    nodesMap[nodes[i].id()] = true;\n  }\n  var roots = nodes.filter(function (ele, i) {\n    if (typeof ele === \"number\") {\n      ele = i;\n    }\n    var parent = ele.parent()[0];\n    while (parent != null) {\n      if (nodesMap[parent.id()]) {\n        return false;\n      }\n      parent = parent.parent()[0];\n    }\n    return true;\n  });\n\n  return roots;\n};\n\n_CoSELayout.prototype.processChildrenList = function (parent, children, layout) {\n  var size = children.length;\n  for (var i = 0; i < size; i++) {\n    var theChild = children[i];\n    var children_of_children = theChild.children();\n    var theNode;\n\n    var dimensions = theChild.layoutDimensions({\n      nodeDimensionsIncludeLabels: this.options.nodeDimensionsIncludeLabels\n    });\n\n    if (theChild.outerWidth() != null && theChild.outerHeight() != null) {\n      theNode = parent.add(new CoSENode(layout.graphManager, new PointD(theChild.position('x') - dimensions.w / 2, theChild.position('y') - dimensions.h / 2), new DimensionD(parseFloat(dimensions.w), parseFloat(dimensions.h))));\n    } else {\n      theNode = parent.add(new CoSENode(this.graphManager));\n    }\n    // Attach id to the layout node\n    theNode.id = theChild.data(\"id\");\n    // Attach the paddings of cy node to layout node\n    theNode.paddingLeft = parseInt(theChild.css('padding'));\n    theNode.paddingTop = parseInt(theChild.css('padding'));\n    theNode.paddingRight = parseInt(theChild.css('padding'));\n    theNode.paddingBottom = parseInt(theChild.css('padding'));\n\n    //Attach the label properties to compound if labels will be included in node dimensions  \n    if (this.options.nodeDimensionsIncludeLabels) {\n      if (theChild.isParent()) {\n        var labelWidth = theChild.boundingBox({ includeLabels: true, includeNodes: false }).w;\n        var labelHeight = theChild.boundingBox({ includeLabels: true, includeNodes: false }).h;\n        var labelPos = theChild.css(\"text-halign\");\n        theNode.labelWidth = labelWidth;\n        theNode.labelHeight = labelHeight;\n        theNode.labelPos = labelPos;\n      }\n    }\n\n    // Map the layout node\n    this.idToLNode[theChild.data(\"id\")] = theNode;\n\n    if (isNaN(theNode.rect.x)) {\n      theNode.rect.x = 0;\n    }\n\n    if (isNaN(theNode.rect.y)) {\n      theNode.rect.y = 0;\n    }\n\n    if (children_of_children != null && children_of_children.length > 0) {\n      var theNewGraph;\n      theNewGraph = layout.getGraphManager().add(layout.newGraph(), theNode);\n      this.processChildrenList(theNewGraph, children_of_children, layout);\n    }\n  }\n};\n\n/**\n * @brief : called on continuous layouts to stop them before they finish\n */\n_CoSELayout.prototype.stop = function () {\n  this.stopped = true;\n\n  return this; // chaining\n};\n\nvar register = function register(cytoscape) {\n  //  var Layout = getLayout( cytoscape );\n\n  cytoscape('layout', 'cose-bilkent', _CoSELayout);\n};\n\n// auto reg for globals\nif (typeof cytoscape !== 'undefined') {\n  register(cytoscape);\n}\n\nmodule.exports = register;\n\n/***/ })\n/******/ ]);\n});", "/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,4],$V1=[1,13],$V2=[1,12],$V3=[1,15],$V4=[1,16],$V5=[1,20],$V6=[1,19],$V7=[6,7,8],$V8=[1,26],$V9=[1,24],$Va=[1,25],$Vb=[6,7,11],$Vc=[1,6,13,15,16,19,22],$Vd=[1,33],$Ve=[1,34],$Vf=[1,6,7,11,13,15,16,19,22];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"mindMap\":4,\"spaceLines\":5,\"SPACELINE\":6,\"NL\":7,\"MINDMAP\":8,\"document\":9,\"stop\":10,\"EOF\":11,\"statement\":12,\"SPACELIST\":13,\"node\":14,\"ICON\":15,\"CLASS\":16,\"nodeWithId\":17,\"nodeWithoutId\":18,\"NODE_DSTART\":19,\"NODE_DESCR\":20,\"NODE_DEND\":21,\"NODE_ID\":22,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",6:\"SPACELINE\",7:\"NL\",8:\"MINDMAP\",11:\"EOF\",13:\"SPACELIST\",15:\"ICON\",16:\"CLASS\",19:\"NODE_DSTART\",20:\"NODE_DESCR\",21:\"NODE_DEND\",22:\"NODE_ID\"},\nproductions_: [0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 6: case 7:\n return yy; \nbreak;\ncase 8:\nyy.getLogger().trace('Stop NL ');\nbreak;\ncase 9:\nyy.getLogger().trace('Stop EOF ');\nbreak;\ncase 11:\nyy.getLogger().trace('Stop NL2 ');\nbreak;\ncase 12:\nyy.getLogger().trace('Stop EOF2 ');\nbreak;\ncase 15:\n yy.getLogger().info('Node: ',$$[$0].id);yy.addNode($$[$0-1].length, $$[$0].id, $$[$0].descr, $$[$0].type);  \nbreak;\ncase 16:\n yy.getLogger().trace('Icon: ',$$[$0]);yy.decorateNode({icon: $$[$0]}); \nbreak;\ncase 17: case 21:\n yy.decorateNode({class: $$[$0]}); \nbreak;\ncase 18:\n yy.getLogger().trace('SPACELIST');\nbreak;\ncase 19:\n yy.getLogger().trace('Node: ',$$[$0].id);yy.addNode(0, $$[$0].id, $$[$0].descr, $$[$0].type);  \nbreak;\ncase 20:\n yy.decorateNode({icon: $$[$0]}); \nbreak;\ncase 25:\n yy.getLogger().trace(\"node found ..\", $$[$0-2]); this.$ = { id: $$[$0-1], descr: $$[$0-1], type: yy.getType($$[$0-2], $$[$0]) }; \nbreak;\ncase 26:\n this.$ = { id: $$[$0], descr: $$[$0], type: yy.nodeType.DEFAULT }; \nbreak;\ncase 27:\n yy.getLogger().trace(\"node found ..\", $$[$0-3]); this.$ = { id: $$[$0-3], descr: $$[$0-1], type: yy.getType($$[$0-2], $$[$0]) }; \nbreak;\n}\n},\ntable: [{3:1,4:2,5:3,6:[1,5],8:$V0},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:$V0},{6:$V1,7:[1,10],9:9,12:11,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},o($V7,[2,3]),{1:[2,2]},o($V7,[2,4]),o($V7,[2,5]),{1:[2,6],6:$V1,12:21,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},{6:$V1,9:22,12:11,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},{6:$V8,7:$V9,10:23,11:$Va},o($Vb,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:$V5,22:$V6}),o($Vb,[2,18]),o($Vb,[2,19]),o($Vb,[2,20]),o($Vb,[2,21]),o($Vb,[2,23]),o($Vb,[2,24]),o($Vb,[2,26],{19:[1,30]}),{20:[1,31]},{6:$V8,7:$V9,10:32,11:$Va},{1:[2,7],6:$V1,12:21,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},o($Vc,[2,14],{7:$Vd,11:$Ve}),o($Vf,[2,8]),o($Vf,[2,9]),o($Vf,[2,10]),o($Vb,[2,15]),o($Vb,[2,16]),o($Vb,[2,17]),{20:[1,35]},{21:[1,36]},o($Vc,[2,13],{7:$Vd,11:$Ve}),o($Vf,[2,11]),o($Vf,[2,12]),{21:[1,37]},o($Vb,[2,25]),o($Vb,[2,27])],\ndefaultActions: {2:[2,1],6:[2,2]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\n\t// Pre-lexer code can go here\n\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:yy.getLogger().trace('Found comment',yy_.yytext); return 6;\nbreak;\ncase 1:return 8;\nbreak;\ncase 2: this.begin('CLASS'); \nbreak;\ncase 3: this.popState();return 16; \nbreak;\ncase 4: this.popState();\nbreak;\ncase 5: yy.getLogger().trace('Begin icon');this.begin('ICON'); \nbreak;\ncase 6:yy.getLogger().trace('SPACELINE');return 6                 /* skip all whitespace */    ;\nbreak;\ncase 7:return 7;\nbreak;\ncase 8: return 15; \nbreak;\ncase 9:yy.getLogger().trace('end icon');this.popState();\nbreak;\ncase 10: yy.getLogger().trace('Exploding node'); this.begin('NODE');return 19; \nbreak;\ncase 11: yy.getLogger().trace('Cloud'); this.begin('NODE');return 19; \nbreak;\ncase 12: yy.getLogger().trace('Explosion Bang'); this.begin('NODE');return 19; \nbreak;\ncase 13: yy.getLogger().trace('Cloud Bang'); this.begin('NODE');return 19; \nbreak;\ncase 14: this.begin('NODE');return 19; \nbreak;\ncase 15: this.begin('NODE');return 19; \nbreak;\ncase 16: this.begin('NODE');return 19; \nbreak;\ncase 17: this.begin('NODE');return 19; \nbreak;\ncase 18:return 13                 /* skip all whitespace */    ;\nbreak;\ncase 19:return 22;\nbreak;\ncase 20:return 11;\nbreak;\ncase 21: this.begin(\"NSTR2\");\nbreak;\ncase 22: return \"NODE_DESCR\";\nbreak;\ncase 23: this.popState();\nbreak;\ncase 24: yy.getLogger().trace('Starting NSTR');this.begin(\"NSTR\");\nbreak;\ncase 25: yy.getLogger().trace('description:', yy_.yytext); return \"NODE_DESCR\";\nbreak;\ncase 26:this.popState();\nbreak;\ncase 27:this.popState();yy.getLogger().trace('node end ))');return \"NODE_DEND\";\nbreak;\ncase 28:this.popState();yy.getLogger().trace('node end )');return \"NODE_DEND\";\nbreak;\ncase 29:this.popState();yy.getLogger().trace('node end ...',yy_.yytext);return \"NODE_DEND\";\nbreak;\ncase 30:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 31:this.popState();yy.getLogger().trace('node end (-');return \"NODE_DEND\";\nbreak;\ncase 32:this.popState();yy.getLogger().trace('node end (-');return \"NODE_DEND\";\nbreak;\ncase 33:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 34:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 35: yy.getLogger().trace('Long description:', yy_.yytext);   return 20;\nbreak;\ncase 36: yy.getLogger().trace('Long description:', yy_.yytext);   return 20;\nbreak;\n}\n},\nrules: [/^(?:\\s*%%.*)/i,/^(?:mindmap\\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\\n)/i,/^(?:::icon\\()/i,/^(?:[\\s]+[\\n])/i,/^(?:[\\n]+)/i,/^(?:[^\\)]+)/i,/^(?:\\))/i,/^(?:-\\))/i,/^(?:\\(-)/i,/^(?:\\)\\))/i,/^(?:\\))/i,/^(?:\\(\\()/i,/^(?:\\{\\{)/i,/^(?:\\()/i,/^(?:\\[)/i,/^(?:[\\s]+)/i,/^(?:[^\\(\\[\\n\\)\\{\\}]+)/i,/^(?:$)/i,/^(?:[\"][`])/i,/^(?:[^`\"]+)/i,/^(?:[`][\"])/i,/^(?:[\"])/i,/^(?:[^\"]+)/i,/^(?:[\"])/i,/^(?:[\\)]\\))/i,/^(?:[\\)])/i,/^(?:[\\]])/i,/^(?:\\}\\})/i,/^(?:\\(-)/i,/^(?:-\\))/i,/^(?:\\(\\()/i,/^(?:\\()/i,/^(?:[^\\)\\]\\(\\}]+)/i,/^(?:.+(?!\\(\\())/i],\nconditions: {\"CLASS\":{\"rules\":[3,4],\"inclusive\":false},\"ICON\":{\"rules\":[8,9],\"inclusive\":false},\"NSTR2\":{\"rules\":[22,23],\"inclusive\":false},\"NSTR\":{\"rules\":[25,26],\"inclusive\":false},\"NODE\":{\"rules\":[21,24,27,28,29,30,31,32,33,34,35,36],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { D3Element } from '../../types.js';\nimport { sanitizeText } from '../../diagrams/common/common.js';\nimport { log } from '../../logger.js';\nimport type { MindmapNode } from './mindmapTypes.js';\nimport defaultConfig from '../../defaultConfig.js';\n\nlet nodes: MindmapNode[] = [];\nlet cnt = 0;\nlet elements: Record<number, D3Element> = {};\n\nconst clear = () => {\n  nodes = [];\n  cnt = 0;\n  elements = {};\n};\n\nconst getParent = function (level: number) {\n  for (let i = nodes.length - 1; i >= 0; i--) {\n    if (nodes[i].level < level) {\n      return nodes[i];\n    }\n  }\n  // No parent found\n  return null;\n};\n\nconst getMindmap = () => {\n  return nodes.length > 0 ? nodes[0] : null;\n};\n\nconst addNode = (level: number, id: string, descr: string, type: number) => {\n  log.info('addNode', level, id, descr, type);\n  const conf = getConfig();\n  let padding: number = conf.mindmap?.padding ?? defaultConfig.mindmap.padding;\n  switch (type) {\n    case nodeType.ROUNDED_RECT:\n    case nodeType.RECT:\n    case nodeType.HEXAGON:\n      padding *= 2;\n  }\n\n  const node = {\n    id: cnt++,\n    nodeId: sanitizeText(id, conf),\n    level,\n    descr: sanitizeText(descr, conf),\n    type,\n    children: [],\n    width: conf.mindmap?.maxNodeWidth ?? defaultConfig.mindmap.maxNodeWidth,\n    padding,\n  } satisfies MindmapNode;\n\n  const parent = getParent(level);\n  if (parent) {\n    parent.children.push(node);\n    // Keep all nodes in the list\n    nodes.push(node);\n  } else {\n    if (nodes.length === 0) {\n      // First node, the root\n      nodes.push(node);\n    } else {\n      // Syntax error ... there can only bee one root\n      throw new Error(\n        'There can be only one root. No parent could be found for (\"' + node.descr + '\")'\n      );\n    }\n  }\n};\n\nconst nodeType = {\n  DEFAULT: 0,\n  NO_BORDER: 0,\n  ROUNDED_RECT: 1,\n  RECT: 2,\n  CIRCLE: 3,\n  CLOUD: 4,\n  BANG: 5,\n  HEXAGON: 6,\n};\n\nconst getType = (startStr: string, endStr: string): number => {\n  log.debug('In get type', startStr, endStr);\n  switch (startStr) {\n    case '[':\n      return nodeType.RECT;\n    case '(':\n      return endStr === ')' ? nodeType.ROUNDED_RECT : nodeType.CLOUD;\n    case '((':\n      return nodeType.CIRCLE;\n    case ')':\n      return nodeType.CLOUD;\n    case '))':\n      return nodeType.BANG;\n    case '{{':\n      return nodeType.HEXAGON;\n    default:\n      return nodeType.DEFAULT;\n  }\n};\n\nconst setElementForId = (id: number, element: D3Element) => {\n  elements[id] = element;\n};\n\nconst decorateNode = (decoration?: { class?: string; icon?: string }) => {\n  if (!decoration) {\n    return;\n  }\n  const config = getConfig();\n  const node = nodes[nodes.length - 1];\n  if (decoration.icon) {\n    node.icon = sanitizeText(decoration.icon, config);\n  }\n  if (decoration.class) {\n    node.class = sanitizeText(decoration.class, config);\n  }\n};\n\nconst type2Str = (type: number) => {\n  switch (type) {\n    case nodeType.DEFAULT:\n      return 'no-border';\n    case nodeType.RECT:\n      return 'rect';\n    case nodeType.ROUNDED_RECT:\n      return 'rounded-rect';\n    case nodeType.CIRCLE:\n      return 'circle';\n    case nodeType.CLOUD:\n      return 'cloud';\n    case nodeType.BANG:\n      return 'bang';\n    case nodeType.HEXAGON:\n      return 'hexgon'; // cspell: disable-line\n    default:\n      return 'no-border';\n  }\n};\n\n// Expose logger to grammar\nconst getLogger = () => log;\nconst getElementById = (id: number) => elements[id];\n\nconst db = {\n  clear,\n  addNode,\n  getMindmap,\n  nodeType,\n  getType,\n  setElementForId,\n  decorateNode,\n  type2Str,\n  getLogger,\n  getElementById,\n} as const;\n\nexport default db;\n", "import cytoscape from 'cytoscape';\n// @ts-expect-error No types available\nimport coseBilkent from 'cytoscape-cose-bilkent';\nimport { select } from 'd3';\nimport type { MermaidConfig } from '../../config.type.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DrawDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport type { D3Element } from '../../types.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport type { FilledMindMapNode, MindmapDB, MindmapNode } from './mindmapTypes.js';\nimport { drawNode, positionNode } from './svgDraw.js';\nimport defaultConfig from '../../defaultConfig.js';\n\n// Inject the layout algorithm into cytoscape\ncytoscape.use(coseBilkent);\n\nasync function drawNodes(\n  db: MindmapDB,\n  svg: D3Element,\n  mindmap: FilledMindMapNode,\n  section: number,\n  conf: MermaidConfig\n) {\n  await drawNode(db, svg, mindmap, section, conf);\n  if (mindmap.children) {\n    await Promise.all(\n      mindmap.children.map((child, index) =>\n        drawNodes(db, svg, child, section < 0 ? index : section, conf)\n      )\n    );\n  }\n}\n\ndeclare module 'cytoscape' {\n  interface EdgeSingular {\n    _private: {\n      bodyBounds: unknown;\n      rscratch: {\n        startX: number;\n        startY: number;\n        midX: number;\n        midY: number;\n        endX: number;\n        endY: number;\n      };\n    };\n  }\n}\n\nfunction drawEdges(edgesEl: D3Element, cy: cytoscape.Core) {\n  cy.edges().map((edge, id) => {\n    const data = edge.data();\n    if (edge[0]._private.bodyBounds) {\n      const bounds = edge[0]._private.rscratch;\n      log.trace('Edge: ', id, data);\n      edgesEl\n        .insert('path')\n        .attr(\n          'd',\n          `M ${bounds.startX},${bounds.startY} L ${bounds.midX},${bounds.midY} L${bounds.endX},${bounds.endY} `\n        )\n        .attr('class', 'edge section-edge-' + data.section + ' edge-depth-' + data.depth);\n    }\n  });\n}\n\nfunction addNodes(mindmap: MindmapNode, cy: cytoscape.Core, conf: MermaidConfig, level: number) {\n  cy.add({\n    group: 'nodes',\n    data: {\n      id: mindmap.id.toString(),\n      labelText: mindmap.descr,\n      height: mindmap.height,\n      width: mindmap.width,\n      level: level,\n      nodeId: mindmap.id,\n      padding: mindmap.padding,\n      type: mindmap.type,\n    },\n    position: {\n      x: mindmap.x!,\n      y: mindmap.y!,\n    },\n  });\n  if (mindmap.children) {\n    mindmap.children.forEach((child) => {\n      addNodes(child, cy, conf, level + 1);\n      cy.add({\n        group: 'edges',\n        data: {\n          id: `${mindmap.id}_${child.id}`,\n          source: mindmap.id,\n          target: child.id,\n          depth: level,\n          section: child.section,\n        },\n      });\n    });\n  }\n}\n\nfunction layoutMindmap(node: MindmapNode, conf: MermaidConfig): Promise<cytoscape.Core> {\n  return new Promise((resolve) => {\n    // Add temporary render element\n    const renderEl = select('body').append('div').attr('id', 'cy').attr('style', 'display:none');\n    const cy = cytoscape({\n      container: document.getElementById('cy'), // container to render in\n      style: [\n        {\n          selector: 'edge',\n          style: {\n            'curve-style': 'bezier',\n          },\n        },\n      ],\n    });\n    // Remove element after layout\n    renderEl.remove();\n    addNodes(node, cy, conf, 0);\n\n    // Make cytoscape care about the dimensions of the nodes\n    cy.nodes().forEach(function (n) {\n      n.layoutDimensions = () => {\n        const data = n.data();\n        return { w: data.width, h: data.height };\n      };\n    });\n\n    cy.layout({\n      name: 'cose-bilkent',\n      // @ts-ignore Types for cose-bilkent are not correct?\n      quality: 'proof',\n      styleEnabled: false,\n      animate: false,\n    }).run();\n    cy.ready((e) => {\n      log.info('Ready', e);\n      resolve(cy);\n    });\n  });\n}\n\nfunction positionNodes(db: MindmapDB, cy: cytoscape.Core) {\n  cy.nodes().map((node, id) => {\n    const data = node.data();\n    data.x = node.position().x;\n    data.y = node.position().y;\n    positionNode(db, data);\n    const el = db.getElementById(data.nodeId);\n    log.info('id:', id, 'Position: (', node.position().x, ', ', node.position().y, ')', data);\n    el.attr(\n      'transform',\n      `translate(${node.position().x - data.width / 2}, ${node.position().y - data.height / 2})`\n    );\n    el.attr('attr', `apa-${id})`);\n  });\n}\n\nexport const draw: DrawDefinition = async (text, id, _version, diagObj) => {\n  log.debug('Rendering mindmap diagram\\n' + text);\n\n  const db = diagObj.db as MindmapDB;\n  const mm = db.getMindmap();\n  if (!mm) {\n    return;\n  }\n\n  const conf = getConfig();\n  conf.htmlLabels = false;\n\n  const svg = selectSvgElement(id);\n\n  // Draw the graph and start with drawing the nodes without proper position\n  // this gives us the size of the nodes and we can set the positions later\n\n  const edgesElem = svg.append('g');\n  edgesElem.attr('class', 'mindmap-edges');\n  const nodesElem = svg.append('g');\n  nodesElem.attr('class', 'mindmap-nodes');\n  await drawNodes(db, nodesElem, mm as FilledMindMapNode, -1, conf);\n\n  // Next step is to layout the mindmap, giving each node a position\n\n  const cy = await layoutMindmap(mm, conf);\n\n  // After this we can draw, first the edges and the then nodes with the correct position\n  drawEdges(edgesElem, cy);\n  positionNodes(db, cy);\n\n  // Setup the view box and size of the svg element\n  setupGraphViewbox(\n    undefined,\n    svg,\n    conf.mindmap?.padding ?? defaultConfig.mindmap.padding,\n    conf.mindmap?.useMaxWidth ?? defaultConfig.mindmap.useMaxWidth\n  );\n};\n\nexport default {\n  draw,\n};\n", "import { createText } from '../../rendering-util/createText.js';\nimport type { FilledMindMapNode, MindmapDB } from './mindmapTypes.js';\nimport type { Point, D3Element } from '../../types.js';\nimport { parseFontSize } from '../../utils.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\nconst MAX_SECTIONS = 12;\n\ntype ShapeFunction = (\n  db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode,\n  section?: number\n) => void;\n\nconst defaultBkg: ShapeFunction = function (db, elem, node, section) {\n  const rd = 5;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${\n        node.width - 2 * rd\n      } q5,0 5,5 v${node.height - rd} H0 Z`\n    );\n\n  elem\n    .append('line')\n    .attr('class', 'node-line-' + section)\n    .attr('x1', 0)\n    .attr('y1', node.height)\n    .attr('x2', node.width)\n    .attr('y2', node.height);\n};\n\nconst rectBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('rect')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('height', node.height)\n    .attr('width', node.width);\n};\n\nconst cloudBkg: ShapeFunction = function (db, elem, node) {\n  const w = node.width;\n  const h = node.height;\n  const r1 = 0.15 * w;\n  const r2 = 0.25 * w;\n  const r3 = 0.35 * w;\n  const r4 = 0.2 * w;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 0 a${r1},${r1} 0 0,1 ${w * 0.25},${-1 * w * 0.1}\n      a${r3},${r3} 1 0,1 ${w * 0.4},${-1 * w * 0.1}\n      a${r2},${r2} 1 0,1 ${w * 0.35},${1 * w * 0.2}\n\n      a${r1},${r1} 1 0,1 ${w * 0.15},${1 * h * 0.35}\n      a${r4},${r4} 1 0,1 ${-1 * w * 0.15},${1 * h * 0.65}\n\n      a${r2},${r1} 1 0,1 ${-1 * w * 0.25},${w * 0.15}\n      a${r3},${r3} 1 0,1 ${-1 * w * 0.5},${0}\n      a${r1},${r1} 1 0,1 ${-1 * w * 0.25},${-1 * w * 0.15}\n\n      a${r1},${r1} 1 0,1 ${-1 * w * 0.1},${-1 * h * 0.35}\n      a${r4},${r4} 1 0,1 ${w * 0.1},${-1 * h * 0.65}\n\n    H0 V0 Z`\n    );\n};\n\nconst bangBkg: ShapeFunction = function (db, elem, node) {\n  const w = node.width;\n  const h = node.height;\n  const r = 0.15 * w;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 0 a${r},${r} 1 0,0 ${w * 0.25},${-1 * h * 0.1}\n      a${r},${r} 1 0,0 ${w * 0.25},${0}\n      a${r},${r} 1 0,0 ${w * 0.25},${0}\n      a${r},${r} 1 0,0 ${w * 0.25},${1 * h * 0.1}\n\n      a${r},${r} 1 0,0 ${w * 0.15},${1 * h * 0.33}\n      a${r * 0.8},${r * 0.8} 1 0,0 ${0},${1 * h * 0.34}\n      a${r},${r} 1 0,0 ${-1 * w * 0.15},${1 * h * 0.33}\n\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${h * 0.15}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${0}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${0}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${-1 * h * 0.15}\n\n      a${r},${r} 1 0,0 ${-1 * w * 0.1},${-1 * h * 0.33}\n      a${r * 0.8},${r * 0.8} 1 0,0 ${0},${-1 * h * 0.34}\n      a${r},${r} 1 0,0 ${w * 0.1},${-1 * h * 0.33}\n\n    H0 V0 Z`\n    );\n};\n\nconst circleBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('circle')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('r', node.width / 2);\n};\n\nfunction insertPolygonShape(\n  parent: D3Element,\n  w: number,\n  h: number,\n  points: Point[],\n  node: FilledMindMapNode\n) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('transform', 'translate(' + (node.width - w) / 2 + ', ' + h + ')');\n}\n\nconst hexagonBkg: ShapeFunction = function (\n  _db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode\n) {\n  const h = node.height;\n  const f = 4;\n  const m = h / f;\n  const w = node.width - node.padding + 2 * m;\n  const points: Point[] = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n  insertPolygonShape(elem, w, h, points, node);\n};\n\nconst roundedRectBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('rect')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('height', node.height)\n    .attr('rx', node.padding)\n    .attr('ry', node.padding)\n    .attr('width', node.width);\n};\n\n/**\n * @param db - The database\n * @param elem - The D3 dom element in which the node is to be added\n * @param node - The node to be added\n * @param fullSection - ?\n * @param conf - The configuration object\n * @returns The height nodes dom element\n */\nexport const drawNode = async function (\n  db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode,\n  fullSection: number,\n  conf: MermaidConfig\n): Promise<number> {\n  const htmlLabels = conf.htmlLabels;\n  const section = fullSection % (MAX_SECTIONS - 1);\n  const nodeElem = elem.append('g');\n  node.section = section;\n  let sectionClass = 'section-' + section;\n  if (section < 0) {\n    sectionClass += ' section-root';\n  }\n  nodeElem.attr('class', (node.class ? node.class + ' ' : '') + 'mindmap-node ' + sectionClass);\n  const bkgElem = nodeElem.append('g');\n\n  // Create the wrapped text element\n  const textElem = nodeElem.append('g');\n  const description = node.descr.replace(/(<br\\/*>)/g, '\\n');\n  await createText(\n    textElem,\n    description,\n    {\n      useHtmlLabels: htmlLabels,\n      width: node.width,\n      classes: 'mindmap-node-label',\n    },\n    conf\n  );\n\n  if (!htmlLabels) {\n    textElem\n      .attr('dy', '1em')\n      .attr('alignment-baseline', 'middle')\n      .attr('dominant-baseline', 'middle')\n      .attr('text-anchor', 'middle');\n  }\n  const bbox = textElem.node().getBBox();\n  const [fontSize] = parseFontSize(conf.fontSize);\n  node.height = bbox.height + fontSize! * 1.1 * 0.5 + node.padding;\n  node.width = bbox.width + 2 * node.padding;\n  if (node.icon) {\n    if (node.type === db.nodeType.CIRCLE) {\n      node.height += 50;\n      node.width += 50;\n      const icon = nodeElem\n        .append('foreignObject')\n        .attr('height', '50px')\n        .attr('width', node.width)\n        .attr('style', 'text-align: center;');\n      icon\n        .append('div')\n        .attr('class', 'icon-container')\n        .append('i')\n        .attr('class', 'node-icon-' + section + ' ' + node.icon);\n      textElem.attr(\n        'transform',\n        'translate(' + node.width / 2 + ', ' + (node.height / 2 - 1.5 * node.padding) + ')'\n      );\n    } else {\n      node.width += 50;\n      const orgHeight = node.height;\n      node.height = Math.max(orgHeight, 60);\n      const heightDiff = Math.abs(node.height - orgHeight);\n      const icon = nodeElem\n        .append('foreignObject')\n        .attr('width', '60px')\n        .attr('height', node.height)\n        .attr('style', 'text-align: center;margin-top:' + heightDiff / 2 + 'px;');\n\n      icon\n        .append('div')\n        .attr('class', 'icon-container')\n        .append('i')\n        .attr('class', 'node-icon-' + section + ' ' + node.icon);\n      textElem.attr(\n        'transform',\n        'translate(' + (25 + node.width / 2) + ', ' + (heightDiff / 2 + node.padding / 2) + ')'\n      );\n    }\n  } else {\n    if (!htmlLabels) {\n      const dx = node.width / 2;\n      const dy = node.padding / 2;\n      textElem.attr('transform', 'translate(' + dx + ', ' + dy + ')');\n      // textElem.attr('transform', 'translate(' + node.width / 2 + ', ' + node.padding / 2 + ')');\n    } else {\n      const dx = (node.width - bbox.width) / 2;\n      const dy = (node.height - bbox.height) / 2;\n      textElem.attr('transform', 'translate(' + dx + ', ' + dy + ')');\n    }\n  }\n\n  switch (node.type) {\n    case db.nodeType.DEFAULT:\n      defaultBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.ROUNDED_RECT:\n      roundedRectBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.RECT:\n      rectBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.CIRCLE:\n      bkgElem.attr('transform', 'translate(' + node.width / 2 + ', ' + +node.height / 2 + ')');\n      circleBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.CLOUD:\n      cloudBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.BANG:\n      bangBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.HEXAGON:\n      hexagonBkg(db, bkgElem, node, section);\n      break;\n  }\n\n  db.setElementForId(node.id, nodeElem);\n  return node.height;\n};\n\nexport const positionNode = function (db: MindmapDB, node: FilledMindMapNode) {\n  const nodeElem = db.getElementById(node.id);\n\n  const x = node.x || 0;\n  const y = node.y || 0;\n  // Position the node to its coordinate\n  nodeElem.attr('transform', 'translate(' + x + ',' + y + ')');\n};\n", "// @ts-expect-error Incorrect khroma types\nimport { darken, lighten, isDark } from 'khroma';\nimport type { DiagramStylesProvider } from '../../diagram-api/types.js';\n\nconst genSections: DiagramStylesProvider = (options) => {\n  let sections = '';\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options['lineColor' + i] = options['lineColor' + i] || options['cScaleInv' + i];\n    if (isDark(options['lineColor' + i])) {\n      options['lineColor' + i] = lighten(options['lineColor' + i], 20);\n    } else {\n      options['lineColor' + i] = darken(options['lineColor' + i], 20);\n    }\n  }\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = '' + (17 - 3 * i);\n    sections += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${\n      i - 1\n    } polygon, .section-${i - 1} path  {\n      fill: ${options['cScale' + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options['cScaleLabel' + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options['cScaleLabel' + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options['cScale' + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options['cScaleInv' + i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections;\n};\n\n// TODO: These options seem incorrect.\nconst getStyles: DiagramStylesProvider = (options) =>\n  `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .mindmap-node-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`;\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport parser from './parser/mindmap.jison';\nimport db from './mindmapDb.js';\nimport renderer from './mindmapRenderer.js';\nimport styles from './styles.js';\nimport type { DiagramDefinition } from '../../diagram-api/types.js';\n\nexport const diagram: DiagramDefinition = {\n  db,\n  renderer,\n  parser,\n  styles,\n};\n"], "mappings": "oYAAA,IAAAA,GAAAC,GAAA,CAAAC,GAAAC,KAAA,cAACC,EAAA,SAA0CC,EAAMC,EAAS,CACtD,OAAOJ,IAAY,UAAY,OAAOC,IAAW,SACnDA,GAAO,QAAUG,EAAQ,EAClB,OAAO,QAAW,YAAc,OAAO,IAC9C,OAAO,CAAC,EAAGA,CAAO,EACX,OAAOJ,IAAY,SAC1BA,GAAQ,WAAgBI,EAAQ,EAEhCD,EAAK,WAAgBC,EAAQ,CAC/B,EATC,oCASEJ,GAAM,UAAW,CACpB,OAAiB,SAASK,EAAS,CAEzB,IAAIC,EAAmB,CAAC,EAGxB,SAASC,EAAoBC,EAAU,CAGtC,GAAGF,EAAiBE,CAAQ,EAC3B,OAAOF,EAAiBE,CAAQ,EAAE,QAGnC,IAAIP,EAASK,EAAiBE,CAAQ,EAAI,CACzCA,EACA,EAAG,GACH,QAAS,CAAC,CACX,EAGA,OAAAH,EAAQG,CAAQ,EAAE,KAAKP,EAAO,QAASA,EAAQA,EAAO,QAASM,CAAmB,EAGlFN,EAAO,EAAI,GAGJA,EAAO,OACf,CArBS,OAAAC,EAAAK,EAAA,uBAyBTA,EAAoB,EAAIF,EAGxBE,EAAoB,EAAID,EAGxBC,EAAoB,EAAI,SAASE,EAAO,CAAE,OAAOA,CAAO,EAGxDF,EAAoB,EAAI,SAASP,EAASU,EAAMC,EAAQ,CACnDJ,EAAoB,EAAEP,EAASU,CAAI,GACtC,OAAO,eAAeV,EAASU,EAAM,CACpC,aAAc,GACd,WAAY,GACZ,IAAKC,CACN,CAAC,CAEH,EAGAJ,EAAoB,EAAI,SAASN,EAAQ,CACxC,IAAIU,EAASV,GAAUA,EAAO,WAC7BC,EAAA,UAAsB,CAAE,OAAOD,EAAO,OAAY,EAAlD,cACAC,EAAA,UAA4B,CAAE,OAAOD,CAAQ,EAA7C,oBACD,OAAAM,EAAoB,EAAEI,EAAQ,IAAKA,CAAM,EAClCA,CACR,EAGAJ,EAAoB,EAAI,SAASK,EAAQC,EAAU,CAAE,OAAO,OAAO,UAAU,eAAe,KAAKD,EAAQC,CAAQ,CAAG,EAGpHN,EAAoB,EAAI,GAGjBA,EAAoBA,EAAoB,EAAI,EAAE,CACtD,EAEC,CAEH,SAASN,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASO,GAAkB,CAAC,CAAnBZ,EAAAY,EAAA,mBAKTA,EAAgB,QAAU,EAK1BA,EAAgB,+BAAiC,GACjDA,EAAgB,oBAAsB,GACtCA,EAAgB,4BAA8B,GAC9CA,EAAgB,gCAAkC,GAClDA,EAAgB,yBAA2B,GAC3CA,EAAgB,gCAAkC,GASlDA,EAAgB,qBAAuB,GAKvCA,EAAgB,+BAAiC,GAKjDA,EAAgB,iBAAmB,GAKnCA,EAAgB,sBAAwBA,EAAgB,iBAAmB,EAM3EA,EAAgB,yBAA2B,GAK3CA,EAAgB,gBAAkB,EAKlCA,EAAgB,eAAiB,IAKjCA,EAAgB,uBAAyBA,EAAgB,eAAiB,IAK1EA,EAAgB,eAAiB,KACjCA,EAAgB,eAAiB,IAEjCb,EAAO,QAAUa,CAEX,EAEC,SAASb,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIQ,EAAeR,EAAoB,CAAC,EACpCS,EAAYT,EAAoB,CAAC,EACjCU,EAAQV,EAAoB,CAAC,EAEjC,SAASW,EAAMC,EAAQC,EAAQC,EAAO,CACpCN,EAAa,KAAK,KAAMM,CAAK,EAE7B,KAAK,4BAA8B,GACnC,KAAK,aAAeA,EACpB,KAAK,WAAa,CAAC,EACnB,KAAK,OAASF,EACd,KAAK,OAASC,CAChB,CARSlB,EAAAgB,EAAA,SAUTA,EAAM,UAAY,OAAO,OAAOH,EAAa,SAAS,EAEtD,QAASO,KAAQP,EACfG,EAAMI,CAAI,EAAIP,EAAaO,CAAI,EAGjCJ,EAAM,UAAU,UAAY,UAAY,CACtC,OAAO,KAAK,MACd,EAEAA,EAAM,UAAU,UAAY,UAAY,CACtC,OAAO,KAAK,MACd,EAEAA,EAAM,UAAU,aAAe,UAAY,CACzC,OAAO,KAAK,YACd,EAEAA,EAAM,UAAU,UAAY,UAAY,CACtC,OAAO,KAAK,MACd,EAEAA,EAAM,UAAU,4BAA8B,UAAY,CACxD,OAAO,KAAK,2BACd,EAEAA,EAAM,UAAU,cAAgB,UAAY,CAC1C,OAAO,KAAK,UACd,EAEAA,EAAM,UAAU,OAAS,UAAY,CACnC,OAAO,KAAK,GACd,EAEAA,EAAM,UAAU,eAAiB,UAAY,CAC3C,OAAO,KAAK,WACd,EAEAA,EAAM,UAAU,eAAiB,UAAY,CAC3C,OAAO,KAAK,WACd,EAEAA,EAAM,UAAU,YAAc,SAAUK,EAAM,CAC5C,GAAI,KAAK,SAAWA,EAClB,OAAO,KAAK,OACP,GAAI,KAAK,SAAWA,EACzB,OAAO,KAAK,OAEZ,KAAM,qCAEV,EAEAL,EAAM,UAAU,mBAAqB,SAAUK,EAAMC,EAAO,CAI1D,QAHIC,EAAW,KAAK,YAAYF,CAAI,EAChCpB,EAAOqB,EAAM,gBAAgB,EAAE,QAAQ,IAE9B,CACX,GAAIC,EAAS,SAAS,GAAKD,EACzB,OAAOC,EAGT,GAAIA,EAAS,SAAS,GAAKtB,EACzB,MAGFsB,EAAWA,EAAS,SAAS,EAAE,UAAU,CAC3C,CAEA,OAAO,IACT,EAEAP,EAAM,UAAU,aAAe,UAAY,CACzC,IAAIQ,EAAuB,IAAI,MAAM,CAAC,EAEtC,KAAK,4BAA8BV,EAAU,gBAAgB,KAAK,OAAO,QAAQ,EAAG,KAAK,OAAO,QAAQ,EAAGU,CAAoB,EAE1H,KAAK,8BACR,KAAK,QAAUA,EAAqB,CAAC,EAAIA,EAAqB,CAAC,EAC/D,KAAK,QAAUA,EAAqB,CAAC,EAAIA,EAAqB,CAAC,EAE3D,KAAK,IAAI,KAAK,OAAO,EAAI,IAC3B,KAAK,QAAUT,EAAM,KAAK,KAAK,OAAO,GAGpC,KAAK,IAAI,KAAK,OAAO,EAAI,IAC3B,KAAK,QAAUA,EAAM,KAAK,KAAK,OAAO,GAGxC,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,QAAU,KAAK,QAAU,KAAK,OAAO,EAErF,EAEAC,EAAM,UAAU,mBAAqB,UAAY,CAC/C,KAAK,QAAU,KAAK,OAAO,WAAW,EAAI,KAAK,OAAO,WAAW,EACjE,KAAK,QAAU,KAAK,OAAO,WAAW,EAAI,KAAK,OAAO,WAAW,EAE7D,KAAK,IAAI,KAAK,OAAO,EAAI,IAC3B,KAAK,QAAUD,EAAM,KAAK,KAAK,OAAO,GAGpC,KAAK,IAAI,KAAK,OAAO,EAAI,IAC3B,KAAK,QAAUA,EAAM,KAAK,KAAK,OAAO,GAGxC,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,QAAU,KAAK,QAAU,KAAK,OAAO,CACnF,EAEAhB,EAAO,QAAUiB,CAEX,EAEC,SAASjB,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASQ,EAAaY,EAAc,CAClC,KAAK,aAAeA,CACtB,CAFSzB,EAAAa,EAAA,gBAITd,EAAO,QAAUc,CAEX,EAEC,SAASd,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIQ,EAAeR,EAAoB,CAAC,EACpCqB,EAAUrB,EAAoB,EAAE,EAChCsB,EAAatB,EAAoB,EAAE,EACnCO,EAAkBP,EAAoB,CAAC,EACvCuB,EAAavB,EAAoB,EAAE,EACnCwB,EAASxB,EAAoB,CAAC,EAElC,SAASyB,EAAMC,EAAIC,EAAKC,EAAMC,EAAO,CAE/BD,GAAQ,MAAQC,GAAS,OAC3BA,EAAQF,GAGVnB,EAAa,KAAK,KAAMqB,CAAK,EAGzBH,EAAG,cAAgB,OAAMA,EAAKA,EAAG,cAErC,KAAK,cAAgBL,EAAQ,UAC7B,KAAK,mBAAqBA,EAAQ,UAClC,KAAK,aAAeQ,EACpB,KAAK,MAAQ,CAAC,EACd,KAAK,aAAeH,EAEhBE,GAAQ,MAAQD,GAAO,KAAM,KAAK,KAAO,IAAIL,EAAWK,EAAI,EAAGA,EAAI,EAAGC,EAAK,MAAOA,EAAK,MAAM,EAAO,KAAK,KAAO,IAAIN,CAC1H,CAlBS3B,EAAA8B,EAAA,SAoBTA,EAAM,UAAY,OAAO,OAAOjB,EAAa,SAAS,EACtD,QAASO,KAAQP,EACfiB,EAAMV,CAAI,EAAIP,EAAaO,CAAI,EAGjCU,EAAM,UAAU,SAAW,UAAY,CACrC,OAAO,KAAK,KACd,EAEAA,EAAM,UAAU,SAAW,UAAY,CACrC,OAAO,KAAK,KACd,EAEAA,EAAM,UAAU,SAAW,UAAY,CAOrC,OAAO,KAAK,KACd,EAEAA,EAAM,UAAU,SAAW,UAAY,CACrC,OAAO,KAAK,KAAK,KACnB,EAEAA,EAAM,UAAU,SAAW,SAAUK,EAAO,CAC1C,KAAK,KAAK,MAAQA,CACpB,EAEAL,EAAM,UAAU,UAAY,UAAY,CACtC,OAAO,KAAK,KAAK,MACnB,EAEAA,EAAM,UAAU,UAAY,SAAUM,EAAQ,CAC5C,KAAK,KAAK,OAASA,CACrB,EAEAN,EAAM,UAAU,WAAa,UAAY,CACvC,OAAO,KAAK,KAAK,EAAI,KAAK,KAAK,MAAQ,CACzC,EAEAA,EAAM,UAAU,WAAa,UAAY,CACvC,OAAO,KAAK,KAAK,EAAI,KAAK,KAAK,OAAS,CAC1C,EAEAA,EAAM,UAAU,UAAY,UAAY,CACtC,OAAO,IAAID,EAAO,KAAK,KAAK,EAAI,KAAK,KAAK,MAAQ,EAAG,KAAK,KAAK,EAAI,KAAK,KAAK,OAAS,CAAC,CACzF,EAEAC,EAAM,UAAU,YAAc,UAAY,CACxC,OAAO,IAAID,EAAO,KAAK,KAAK,EAAG,KAAK,KAAK,CAAC,CAC5C,EAEAC,EAAM,UAAU,QAAU,UAAY,CACpC,OAAO,KAAK,IACd,EAEAA,EAAM,UAAU,YAAc,UAAY,CACxC,OAAO,KAAK,KAAK,KAAK,KAAK,MAAQ,KAAK,KAAK,MAAQ,KAAK,KAAK,OAAS,KAAK,KAAK,MAAM,CAC1F,EAKAA,EAAM,UAAU,mBAAqB,UAAY,CAC/C,OAAO,KAAK,KAAK,KAAK,KAAK,OAAS,KAAK,KAAK,OAAS,KAAK,KAAK,MAAQ,KAAK,KAAK,KAAK,EAAI,CAC9F,EAEAA,EAAM,UAAU,QAAU,SAAUO,EAAWC,EAAW,CACxD,KAAK,KAAK,EAAID,EAAU,EACxB,KAAK,KAAK,EAAIA,EAAU,EACxB,KAAK,KAAK,MAAQC,EAAU,MAC5B,KAAK,KAAK,OAASA,EAAU,MAC/B,EAEAR,EAAM,UAAU,UAAY,SAAUS,EAAIC,EAAI,CAC5C,KAAK,KAAK,EAAID,EAAK,KAAK,KAAK,MAAQ,EACrC,KAAK,KAAK,EAAIC,EAAK,KAAK,KAAK,OAAS,CACxC,EAEAV,EAAM,UAAU,YAAc,SAAUW,EAAGC,EAAG,CAC5C,KAAK,KAAK,EAAID,EACd,KAAK,KAAK,EAAIC,CAChB,EAEAZ,EAAM,UAAU,OAAS,SAAUa,EAAIC,EAAI,CACzC,KAAK,KAAK,GAAKD,EACf,KAAK,KAAK,GAAKC,CACjB,EAEAd,EAAM,UAAU,kBAAoB,SAAUe,EAAI,CAChD,IAAIC,EAAW,CAAC,EACZC,EACAC,EAAO,KAEX,OAAAA,EAAK,MAAM,QAAQ,SAAUD,EAAM,CAEjC,GAAIA,EAAK,QAAUF,EAAI,CACrB,GAAIE,EAAK,QAAUC,EAAM,KAAM,yBAE/BF,EAAS,KAAKC,CAAI,CACpB,CACF,CAAC,EAEMD,CACT,EAEAhB,EAAM,UAAU,gBAAkB,SAAUmB,EAAO,CACjD,IAAIH,EAAW,CAAC,EACZC,EAEAC,EAAO,KACX,OAAAA,EAAK,MAAM,QAAQ,SAAUD,EAAM,CAEjC,GAAI,EAAEA,EAAK,QAAUC,GAAQD,EAAK,QAAUC,GAAO,KAAM,uCAErDD,EAAK,QAAUE,GAASF,EAAK,QAAUE,IACzCH,EAAS,KAAKC,CAAI,CAEtB,CAAC,EAEMD,CACT,EAEAhB,EAAM,UAAU,iBAAmB,UAAY,CAC7C,IAAIoB,EAAY,IAAI,IAEhBF,EAAO,KACX,OAAAA,EAAK,MAAM,QAAQ,SAAUD,EAAM,CAEjC,GAAIA,EAAK,QAAUC,EACjBE,EAAU,IAAIH,EAAK,MAAM,MACpB,CACL,GAAIA,EAAK,QAAUC,EACjB,KAAM,uBAGRE,EAAU,IAAIH,EAAK,MAAM,CAC3B,CACF,CAAC,EAEMG,CACT,EAEApB,EAAM,UAAU,aAAe,UAAY,CACzC,IAAIqB,EAAoB,IAAI,IACxBC,EACAC,EAIJ,GAFAF,EAAkB,IAAI,IAAI,EAEtB,KAAK,OAAS,KAEhB,QADIG,EAAQ,KAAK,MAAM,SAAS,EACvBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChCH,EAAYE,EAAMC,CAAC,EACnBF,EAAWD,EAAU,aAAa,EAClCC,EAAS,QAAQ,SAAUhC,EAAM,CAC/B8B,EAAkB,IAAI9B,CAAI,CAC5B,CAAC,EAIL,OAAO8B,CACT,EAEArB,EAAM,UAAU,gBAAkB,UAAY,CAC5C,IAAI0B,EAAe,EACfJ,EAEJ,GAAI,KAAK,OAAS,KAChBI,EAAe,MAGf,SADIF,EAAQ,KAAK,MAAM,SAAS,EACvBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChCH,EAAYE,EAAMC,CAAC,EAEnBC,GAAgBJ,EAAU,gBAAgB,EAI9C,OAAII,GAAgB,IAClBA,EAAe,GAEVA,CACT,EAEA1B,EAAM,UAAU,iBAAmB,UAAY,CAC7C,GAAI,KAAK,eAAiBJ,EAAQ,UAChC,KAAM,gBAER,OAAO,KAAK,aACd,EAEAI,EAAM,UAAU,kBAAoB,UAAY,CAC9C,OAAI,KAAK,OAAS,KACT,KAAK,eAAiB,KAAK,KAAK,MAAQ,KAAK,KAAK,QAAU,GAEnE,KAAK,cAAgB,KAAK,MAAM,kBAAkB,EAClD,KAAK,KAAK,MAAQ,KAAK,cACvB,KAAK,KAAK,OAAS,KAAK,cAEjB,KAAK,cAEhB,EAEAA,EAAM,UAAU,QAAU,UAAY,CACpC,IAAI2B,EACAC,EAEAC,EAAO,CAAC/C,EAAgB,uBACxBgD,EAAOhD,EAAgB,uBAC3B6C,EAAgB7C,EAAgB,eAAiBgB,EAAW,WAAW,GAAKgC,EAAOD,GAAQA,EAE3F,IAAIE,EAAO,CAACjD,EAAgB,uBACxBkD,EAAOlD,EAAgB,uBAC3B8C,EAAgB9C,EAAgB,eAAiBgB,EAAW,WAAW,GAAKkC,EAAOD,GAAQA,EAE3F,KAAK,KAAK,EAAIJ,EACd,KAAK,KAAK,EAAIC,CAChB,EAEA5B,EAAM,UAAU,aAAe,UAAY,CACzC,GAAI,KAAK,SAAS,GAAK,KACrB,KAAM,gBAER,GAAI,KAAK,SAAS,EAAE,SAAS,EAAE,QAAU,EAAG,CAE1C,IAAIiC,EAAa,KAAK,SAAS,EAU/B,GATAA,EAAW,aAAa,EAAI,EAE5B,KAAK,KAAK,EAAIA,EAAW,QAAQ,EACjC,KAAK,KAAK,EAAIA,EAAW,OAAO,EAEhC,KAAK,SAASA,EAAW,SAAS,EAAIA,EAAW,QAAQ,CAAC,EAC1D,KAAK,UAAUA,EAAW,UAAU,EAAIA,EAAW,OAAO,CAAC,EAGvDnD,EAAgB,+BAAgC,CAElD,IAAIuB,EAAQ4B,EAAW,SAAS,EAAIA,EAAW,QAAQ,EACnD3B,EAAS2B,EAAW,UAAU,EAAIA,EAAW,OAAO,EAEpD,KAAK,WAAa5B,IACpB,KAAK,KAAK,IAAM,KAAK,WAAaA,GAAS,EAC3C,KAAK,SAAS,KAAK,UAAU,GAG3B,KAAK,YAAcC,IACjB,KAAK,UAAY,SACnB,KAAK,KAAK,IAAM,KAAK,YAAcA,GAAU,EACpC,KAAK,UAAY,QAC1B,KAAK,KAAK,GAAK,KAAK,YAAcA,GAEpC,KAAK,UAAU,KAAK,WAAW,EAEnC,CACF,CACF,EAEAN,EAAM,UAAU,sBAAwB,UAAY,CAClD,GAAI,KAAK,oBAAsBJ,EAAQ,UACrC,KAAM,gBAER,OAAO,KAAK,kBACd,EAEAI,EAAM,UAAU,UAAY,SAAUkC,EAAO,CAC3C,IAAIC,EAAO,KAAK,KAAK,EAEjBA,EAAOrD,EAAgB,eACzBqD,EAAOrD,EAAgB,eACdqD,EAAO,CAACrD,EAAgB,iBACjCqD,EAAO,CAACrD,EAAgB,gBAG1B,IAAIsD,EAAM,KAAK,KAAK,EAEhBA,EAAMtD,EAAgB,eACxBsD,EAAMtD,EAAgB,eACbsD,EAAM,CAACtD,EAAgB,iBAChCsD,EAAM,CAACtD,EAAgB,gBAGzB,IAAIuD,EAAU,IAAItC,EAAOoC,EAAMC,CAAG,EAC9BE,EAAWJ,EAAM,sBAAsBG,CAAO,EAElD,KAAK,YAAYC,EAAS,EAAGA,EAAS,CAAC,CACzC,EAEAtC,EAAM,UAAU,QAAU,UAAY,CACpC,OAAO,KAAK,KAAK,CACnB,EAEAA,EAAM,UAAU,SAAW,UAAY,CACrC,OAAO,KAAK,KAAK,EAAI,KAAK,KAAK,KACjC,EAEAA,EAAM,UAAU,OAAS,UAAY,CACnC,OAAO,KAAK,KAAK,CACnB,EAEAA,EAAM,UAAU,UAAY,UAAY,CACtC,OAAO,KAAK,KAAK,EAAI,KAAK,KAAK,MACjC,EAEAA,EAAM,UAAU,UAAY,UAAY,CACtC,OAAI,KAAK,OAAS,KACT,KAGF,KAAK,MAAM,UAAU,CAC9B,EAEA/B,EAAO,QAAU+B,CAEX,EAEC,SAAS/B,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASwB,EAAOY,EAAGC,EAAG,CAChBD,GAAK,MAAQC,GAAK,MACpB,KAAK,EAAI,EACT,KAAK,EAAI,IAET,KAAK,EAAID,EACT,KAAK,EAAIC,EAEb,CARS1C,EAAA6B,EAAA,UAUTA,EAAO,UAAU,KAAO,UAAY,CAClC,OAAO,KAAK,CACd,EAEAA,EAAO,UAAU,KAAO,UAAY,CAClC,OAAO,KAAK,CACd,EAEAA,EAAO,UAAU,KAAO,SAAUY,EAAG,CACnC,KAAK,EAAIA,CACX,EAEAZ,EAAO,UAAU,KAAO,SAAUa,EAAG,CACnC,KAAK,EAAIA,CACX,EAEAb,EAAO,UAAU,cAAgB,SAAUwC,EAAI,CAC7C,OAAO,IAAI,WAAW,KAAK,EAAIA,EAAG,EAAG,KAAK,EAAIA,EAAG,CAAC,CACpD,EAEAxC,EAAO,UAAU,QAAU,UAAY,CACrC,OAAO,IAAIA,EAAO,KAAK,EAAG,KAAK,CAAC,CAClC,EAEAA,EAAO,UAAU,UAAY,SAAUyC,EAAK,CAC1C,YAAK,GAAKA,EAAI,MACd,KAAK,GAAKA,EAAI,OACP,IACT,EAEAvE,EAAO,QAAU8B,CAEX,EAEC,SAAS9B,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIQ,EAAeR,EAAoB,CAAC,EACpCqB,EAAUrB,EAAoB,EAAE,EAChCO,EAAkBP,EAAoB,CAAC,EACvCkE,EAAgBlE,EAAoB,CAAC,EACrCyB,EAAQzB,EAAoB,CAAC,EAC7BW,EAAQX,EAAoB,CAAC,EAC7BsB,EAAatB,EAAoB,EAAE,EACnCmE,EAAQnE,EAAoB,EAAE,EAC9BoE,EAAapE,EAAoB,EAAE,EAEvC,SAASqE,EAAOC,EAAQC,EAAMC,EAAQ,CACpChE,EAAa,KAAK,KAAMgE,CAAM,EAC9B,KAAK,cAAgBnD,EAAQ,UAC7B,KAAK,OAASd,EAAgB,qBAC9B,KAAK,MAAQ,CAAC,EACd,KAAK,MAAQ,CAAC,EACd,KAAK,YAAc,GACnB,KAAK,OAAS+D,EAEVC,GAAQ,MAAQA,aAAgBL,EAClC,KAAK,aAAeK,EACXA,GAAQ,MAAQA,aAAgB,SACzC,KAAK,aAAeA,EAAK,aAE7B,CAdS5E,EAAA0E,EAAA,UAgBTA,EAAO,UAAY,OAAO,OAAO7D,EAAa,SAAS,EACvD,QAASO,KAAQP,EACf6D,EAAOtD,CAAI,EAAIP,EAAaO,CAAI,EAGlCsD,EAAO,UAAU,SAAW,UAAY,CACtC,OAAO,KAAK,KACd,EAEAA,EAAO,UAAU,SAAW,UAAY,CACtC,OAAO,KAAK,KACd,EAEAA,EAAO,UAAU,gBAAkB,UAAY,CAC7C,OAAO,KAAK,YACd,EAEAA,EAAO,UAAU,UAAY,UAAY,CACvC,OAAO,KAAK,MACd,EAEAA,EAAO,UAAU,QAAU,UAAY,CACrC,OAAO,KAAK,IACd,EAEAA,EAAO,UAAU,SAAW,UAAY,CACtC,OAAO,KAAK,KACd,EAEAA,EAAO,UAAU,OAAS,UAAY,CACpC,OAAO,KAAK,GACd,EAEAA,EAAO,UAAU,UAAY,UAAY,CACvC,OAAO,KAAK,MACd,EAEAA,EAAO,UAAU,YAAc,UAAY,CACzC,OAAO,KAAK,WACd,EAEAA,EAAO,UAAU,IAAM,SAAUI,EAAMC,EAAYC,EAAY,CAC7D,GAAID,GAAc,MAAQC,GAAc,KAAM,CAC5C,IAAIC,EAAUH,EACd,GAAI,KAAK,cAAgB,KACvB,KAAM,0BAER,GAAI,KAAK,SAAS,EAAE,QAAQG,CAAO,EAAI,GACrC,KAAM,yBAER,OAAAA,EAAQ,MAAQ,KAChB,KAAK,SAAS,EAAE,KAAKA,CAAO,EAErBA,CACT,KAAO,CACL,IAAIC,EAAUJ,EACd,GAAI,EAAE,KAAK,SAAS,EAAE,QAAQC,CAAU,EAAI,IAAM,KAAK,SAAS,EAAE,QAAQC,CAAU,EAAI,IACtF,KAAM,iCAGR,GAAI,EAAED,EAAW,OAASC,EAAW,OAASD,EAAW,OAAS,MAChE,KAAM,kCAGR,OAAIA,EAAW,OAASC,EAAW,MAC1B,MAITE,EAAQ,OAASH,EACjBG,EAAQ,OAASF,EAGjBE,EAAQ,aAAe,GAGvB,KAAK,SAAS,EAAE,KAAKA,CAAO,EAG5BH,EAAW,MAAM,KAAKG,CAAO,EAEzBF,GAAcD,GAChBC,EAAW,MAAM,KAAKE,CAAO,EAGxBA,EACT,CACF,EAEAR,EAAO,UAAU,OAAS,SAAUS,EAAK,CACvC,IAAI9D,EAAO8D,EACX,GAAIA,aAAerD,EAAO,CACxB,GAAIT,GAAQ,KACV,KAAM,gBAER,GAAI,EAAEA,EAAK,OAAS,MAAQA,EAAK,OAAS,MACxC,KAAM,0BAER,GAAI,KAAK,cAAgB,KACvB,KAAM,kCAMR,QAHI+D,EAAmB/D,EAAK,MAAM,MAAM,EACpC0B,EACAsC,EAAID,EAAiB,OAChB7B,EAAI,EAAGA,EAAI8B,EAAG9B,IACrBR,EAAOqC,EAAiB7B,CAAC,EAErBR,EAAK,aACP,KAAK,aAAa,OAAOA,CAAI,EAE7BA,EAAK,OAAO,MAAM,OAAOA,CAAI,EAKjC,IAAIuC,EAAQ,KAAK,MAAM,QAAQjE,CAAI,EACnC,GAAIiE,GAAS,GACX,KAAM,+BAGR,KAAK,MAAM,OAAOA,EAAO,CAAC,CAC5B,SAAWH,aAAenE,EAAO,CAC/B,IAAI+B,EAAOoC,EACX,GAAIpC,GAAQ,KACV,KAAM,gBAER,GAAI,EAAEA,EAAK,QAAU,MAAQA,EAAK,QAAU,MAC1C,KAAM,gCAER,GAAI,EAAEA,EAAK,OAAO,OAAS,MAAQA,EAAK,OAAO,OAAS,MAAQA,EAAK,OAAO,OAAS,MAAQA,EAAK,OAAO,OAAS,MAChH,KAAM,yCAGR,IAAIwC,EAAcxC,EAAK,OAAO,MAAM,QAAQA,CAAI,EAC5CyC,EAAczC,EAAK,OAAO,MAAM,QAAQA,CAAI,EAChD,GAAI,EAAEwC,EAAc,IAAMC,EAAc,IACtC,KAAM,+CAGRzC,EAAK,OAAO,MAAM,OAAOwC,EAAa,CAAC,EAEnCxC,EAAK,QAAUA,EAAK,QACtBA,EAAK,OAAO,MAAM,OAAOyC,EAAa,CAAC,EAGzC,IAAIF,EAAQvC,EAAK,OAAO,MAAM,SAAS,EAAE,QAAQA,CAAI,EACrD,GAAIuC,GAAS,GACX,KAAM,4BAGRvC,EAAK,OAAO,MAAM,SAAS,EAAE,OAAOuC,EAAO,CAAC,CAC9C,CACF,EAEAZ,EAAO,UAAU,cAAgB,UAAY,CAU3C,QATIR,EAAMxC,EAAQ,UACduC,EAAOvC,EAAQ,UACf+D,EACAC,EACAC,EAEArC,EAAQ,KAAK,SAAS,EACtB+B,EAAI/B,EAAM,OAELC,EAAI,EAAGA,EAAI8B,EAAG9B,IAAK,CAC1B,IAAIqC,EAAQtC,EAAMC,CAAC,EACnBkC,EAAUG,EAAM,OAAO,EACvBF,EAAWE,EAAM,QAAQ,EAErB1B,EAAMuB,IACRvB,EAAMuB,GAGJxB,EAAOyB,IACTzB,EAAOyB,EAEX,CAGA,OAAIxB,GAAOxC,EAAQ,UACV,MAGL4B,EAAM,CAAC,EAAE,UAAU,EAAE,aAAe,KACtCqC,EAASrC,EAAM,CAAC,EAAE,UAAU,EAAE,YAE9BqC,EAAS,KAAK,OAGhB,KAAK,KAAO1B,EAAO0B,EACnB,KAAK,IAAMzB,EAAMyB,EAGV,IAAInB,EAAM,KAAK,KAAM,KAAK,GAAG,EACtC,EAEAE,EAAO,UAAU,aAAe,SAAUmB,EAAW,CAcnD,QAZI5B,EAAOvC,EAAQ,UACfoE,EAAQ,CAACpE,EAAQ,UACjBwC,EAAMxC,EAAQ,UACdqE,EAAS,CAACrE,EAAQ,UAClBgE,EACAM,EACAP,EACAQ,EACAN,EAEArC,EAAQ,KAAK,MACb+B,EAAI/B,EAAM,OACLC,EAAI,EAAGA,EAAI8B,EAAG9B,IAAK,CAC1B,IAAIqC,EAAQtC,EAAMC,CAAC,EAEfsC,GAAaD,EAAM,OAAS,MAC9BA,EAAM,aAAa,EAErBF,EAAWE,EAAM,QAAQ,EACzBI,EAAYJ,EAAM,SAAS,EAC3BH,EAAUG,EAAM,OAAO,EACvBK,EAAaL,EAAM,UAAU,EAEzB3B,EAAOyB,IACTzB,EAAOyB,GAGLI,EAAQE,IACVF,EAAQE,GAGN9B,EAAMuB,IACRvB,EAAMuB,GAGJM,EAASE,IACXF,EAASE,EAEb,CAEA,IAAIC,EAAe,IAAIvE,EAAWsC,EAAMC,EAAK4B,EAAQ7B,EAAM8B,EAAS7B,CAAG,EACnED,GAAQvC,EAAQ,YAClB,KAAK,KAAO,KAAK,OAAO,QAAQ,EAChC,KAAK,MAAQ,KAAK,OAAO,SAAS,EAClC,KAAK,IAAM,KAAK,OAAO,OAAO,EAC9B,KAAK,OAAS,KAAK,OAAO,UAAU,GAGlC4B,EAAM,CAAC,EAAE,UAAU,EAAE,aAAe,KACtCqC,EAASrC,EAAM,CAAC,EAAE,UAAU,EAAE,YAE9BqC,EAAS,KAAK,OAGhB,KAAK,KAAOO,EAAa,EAAIP,EAC7B,KAAK,MAAQO,EAAa,EAAIA,EAAa,MAAQP,EACnD,KAAK,IAAMO,EAAa,EAAIP,EAC5B,KAAK,OAASO,EAAa,EAAIA,EAAa,OAASP,CACvD,EAEAjB,EAAO,gBAAkB,SAAUpB,EAAO,CAYxC,QAXIW,EAAOvC,EAAQ,UACfoE,EAAQ,CAACpE,EAAQ,UACjBwC,EAAMxC,EAAQ,UACdqE,EAAS,CAACrE,EAAQ,UAClBgE,EACAM,EACAP,EACAQ,EAEAZ,EAAI/B,EAAM,OAELC,EAAI,EAAGA,EAAI8B,EAAG9B,IAAK,CAC1B,IAAIqC,EAAQtC,EAAMC,CAAC,EACnBmC,EAAWE,EAAM,QAAQ,EACzBI,EAAYJ,EAAM,SAAS,EAC3BH,EAAUG,EAAM,OAAO,EACvBK,EAAaL,EAAM,UAAU,EAEzB3B,EAAOyB,IACTzB,EAAOyB,GAGLI,EAAQE,IACVF,EAAQE,GAGN9B,EAAMuB,IACRvB,EAAMuB,GAGJM,EAASE,IACXF,EAASE,EAEb,CAEA,IAAIC,EAAe,IAAIvE,EAAWsC,EAAMC,EAAK4B,EAAQ7B,EAAM8B,EAAS7B,CAAG,EAEvE,OAAOgC,CACT,EAEAxB,EAAO,UAAU,sBAAwB,UAAY,CACnD,OAAI,MAAQ,KAAK,aAAa,QAAQ,EAC7B,EAEA,KAAK,OAAO,sBAAsB,CAE7C,EAEAA,EAAO,UAAU,iBAAmB,UAAY,CAC9C,GAAI,KAAK,eAAiBhD,EAAQ,UAChC,KAAM,gBAER,OAAO,KAAK,aACd,EAEAgD,EAAO,UAAU,kBAAoB,UAAY,CAK/C,QAJIzC,EAAO,EACPqB,EAAQ,KAAK,MACb+B,EAAI/B,EAAM,OAELC,EAAI,EAAGA,EAAI8B,EAAG9B,IAAK,CAC1B,IAAIqC,EAAQtC,EAAMC,CAAC,EACnBtB,GAAQ2D,EAAM,kBAAkB,CAClC,CAEA,OAAI3D,GAAQ,EACV,KAAK,cAAgBrB,EAAgB,yBAErC,KAAK,cAAgBqB,EAAO,KAAK,KAAK,KAAK,MAAM,MAAM,EAGlD,KAAK,aACd,EAEAyC,EAAO,UAAU,gBAAkB,UAAY,CAC7C,IAAI1B,EAAO,KACX,GAAI,KAAK,MAAM,QAAU,EAAG,CAC1B,KAAK,YAAc,GACnB,MACF,CAEA,IAAImD,EAAQ,IAAI1B,EACZ2B,EAAU,IAAI,IACdC,EAAc,KAAK,MAAM,CAAC,EAC1BC,EACAC,EACAC,EAAiBH,EAAY,aAAa,EAM9C,IALAG,EAAe,QAAQ,SAAUnF,EAAM,CACrC8E,EAAM,KAAK9E,CAAI,EACf+E,EAAQ,IAAI/E,CAAI,CAClB,CAAC,EAEM8E,EAAM,SAAW,GAAG,CACzBE,EAAcF,EAAM,MAAM,EAG1BG,EAAgBD,EAAY,SAAS,EAErC,QADIpE,EAAOqE,EAAc,OAChB/C,EAAI,EAAGA,EAAItB,EAAMsB,IAAK,CAC7B,IAAIkD,EAAeH,EAAc/C,CAAC,EAIlC,GAHAgD,EAAkBE,EAAa,mBAAmBJ,EAAa,IAAI,EAG/DE,GAAmB,MAAQ,CAACH,EAAQ,IAAIG,CAAe,EAAG,CAC5D,IAAIG,EAAqBH,EAAgB,aAAa,EAEtDG,EAAmB,QAAQ,SAAUrF,EAAM,CACzC8E,EAAM,KAAK9E,CAAI,EACf+E,EAAQ,IAAI/E,CAAI,CAClB,CAAC,CACH,CACF,CACF,CAIA,GAFA,KAAK,YAAc,GAEf+E,EAAQ,MAAQ,KAAK,MAAM,OAAQ,CACrC,IAAIO,EAAyB,EAE7BP,EAAQ,QAAQ,SAAUQ,EAAa,CACjCA,EAAY,OAAS5D,GACvB2D,GAEJ,CAAC,EAEGA,GAA0B,KAAK,MAAM,SACvC,KAAK,YAAc,GAEvB,CACF,EAEA5G,EAAO,QAAU2E,CAEX,EAEC,SAAS3E,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIqE,EACA1D,EAAQX,EAAoB,CAAC,EAEjC,SAASkE,EAAcsC,EAAQ,CAC7BnC,EAASrE,EAAoB,CAAC,EAC9B,KAAK,OAASwG,EAEd,KAAK,OAAS,CAAC,EACf,KAAK,MAAQ,CAAC,CAChB,CANS7G,EAAAuE,EAAA,iBAQTA,EAAc,UAAU,QAAU,UAAY,CAC5C,IAAIuC,EAAS,KAAK,OAAO,SAAS,EAC9BC,EAAQ,KAAK,OAAO,QAAQ,IAAI,EAChC9G,EAAO,KAAK,IAAI6G,EAAQC,CAAK,EACjC,YAAK,aAAa9G,CAAI,EACf,KAAK,SACd,EAEAsE,EAAc,UAAU,IAAM,SAAUyC,EAAUC,EAAY/B,EAASH,EAAYC,EAAY,CAE7F,GAAIE,GAAW,MAAQH,GAAc,MAAQC,GAAc,KAAM,CAC/D,GAAIgC,GAAY,KACd,KAAM,iBAER,GAAIC,GAAc,KAChB,KAAM,uBAER,GAAI,KAAK,OAAO,QAAQD,CAAQ,EAAI,GAClC,KAAM,mCAKR,GAFA,KAAK,OAAO,KAAKA,CAAQ,EAErBA,EAAS,QAAU,KACrB,KAAM,wBAER,GAAIC,EAAW,OAAS,KACtB,KAAM,uBAGR,OAAAD,EAAS,OAASC,EAClBA,EAAW,MAAQD,EAEZA,CACT,KAAO,CAELhC,EAAaE,EACbH,EAAakC,EACb/B,EAAU8B,EACV,IAAIE,EAAcnC,EAAW,SAAS,EAClCoC,EAAcnC,EAAW,SAAS,EAEtC,GAAI,EAAEkC,GAAe,MAAQA,EAAY,gBAAgB,GAAK,MAC5D,KAAM,gCAER,GAAI,EAAEC,GAAe,MAAQA,EAAY,gBAAgB,GAAK,MAC5D,KAAM,gCAGR,GAAID,GAAeC,EACjB,OAAAjC,EAAQ,aAAe,GAChBgC,EAAY,IAAIhC,EAASH,EAAYC,CAAU,EAStD,GAPAE,EAAQ,aAAe,GAGvBA,EAAQ,OAASH,EACjBG,EAAQ,OAASF,EAGb,KAAK,MAAM,QAAQE,CAAO,EAAI,GAChC,KAAM,yCAMR,GAHA,KAAK,MAAM,KAAKA,CAAO,EAGnB,EAAEA,EAAQ,QAAU,MAAQA,EAAQ,QAAU,MAChD,KAAM,qCAGR,GAAI,EAAEA,EAAQ,OAAO,MAAM,QAAQA,CAAO,GAAK,IAAMA,EAAQ,OAAO,MAAM,QAAQA,CAAO,GAAK,IAC5F,KAAM,uDAGR,OAAAA,EAAQ,OAAO,MAAM,KAAKA,CAAO,EACjCA,EAAQ,OAAO,MAAM,KAAKA,CAAO,EAE1BA,CAEX,CACF,EAEAX,EAAc,UAAU,OAAS,SAAU6C,EAAM,CAC/C,GAAIA,aAAgB1C,EAAQ,CAC1B,IAAIpD,EAAQ8F,EACZ,GAAI9F,EAAM,gBAAgB,GAAK,KAC7B,KAAM,8BAER,GAAI,EAAEA,GAAS,KAAK,WAAaA,EAAM,QAAU,MAAQA,EAAM,OAAO,cAAgB,MACpF,KAAM,uBAIR,IAAI8D,EAAmB,CAAC,EAExBA,EAAmBA,EAAiB,OAAO9D,EAAM,SAAS,CAAC,EAI3D,QAFIyB,EACAsC,EAAID,EAAiB,OAChB7B,EAAI,EAAGA,EAAI8B,EAAG9B,IACrBR,EAAOqC,EAAiB7B,CAAC,EACzBjC,EAAM,OAAOyB,CAAI,EAInB,IAAIsE,EAAmB,CAAC,EAExBA,EAAmBA,EAAiB,OAAO/F,EAAM,SAAS,CAAC,EAE3D,IAAID,EACJgE,EAAIgC,EAAiB,OACrB,QAAS9D,EAAI,EAAGA,EAAI8B,EAAG9B,IACrBlC,EAAOgG,EAAiB9D,CAAC,EACzBjC,EAAM,OAAOD,CAAI,EAIfC,GAAS,KAAK,WAChB,KAAK,aAAa,IAAI,EAIxB,IAAIgE,EAAQ,KAAK,OAAO,QAAQhE,CAAK,EACrC,KAAK,OAAO,OAAOgE,EAAO,CAAC,EAG3BhE,EAAM,OAAS,IACjB,SAAW8F,aAAgBpG,EAAO,CAEhC,GADA+B,EAAOqE,EACHrE,GAAQ,KACV,KAAM,gBAER,GAAI,CAACA,EAAK,aACR,KAAM,2BAER,GAAI,EAAEA,EAAK,QAAU,MAAQA,EAAK,QAAU,MAC1C,KAAM,gCAKR,GAAI,EAAEA,EAAK,OAAO,MAAM,QAAQA,CAAI,GAAK,IAAMA,EAAK,OAAO,MAAM,QAAQA,CAAI,GAAK,IAChF,KAAM,+CAGR,IAAIuC,EAAQvC,EAAK,OAAO,MAAM,QAAQA,CAAI,EAO1C,GANAA,EAAK,OAAO,MAAM,OAAOuC,EAAO,CAAC,EACjCA,EAAQvC,EAAK,OAAO,MAAM,QAAQA,CAAI,EACtCA,EAAK,OAAO,MAAM,OAAOuC,EAAO,CAAC,EAI7B,EAAEvC,EAAK,OAAO,OAAS,MAAQA,EAAK,OAAO,MAAM,gBAAgB,GAAK,MACxE,KAAM,mDAER,GAAIA,EAAK,OAAO,MAAM,gBAAgB,EAAE,MAAM,QAAQA,CAAI,GAAK,GAC7D,KAAM,0CAGR,IAAIuC,EAAQvC,EAAK,OAAO,MAAM,gBAAgB,EAAE,MAAM,QAAQA,CAAI,EAClEA,EAAK,OAAO,MAAM,gBAAgB,EAAE,MAAM,OAAOuC,EAAO,CAAC,CAC3D,CACF,EAEAf,EAAc,UAAU,aAAe,UAAY,CACjD,KAAK,UAAU,aAAa,EAAI,CAClC,EAEAA,EAAc,UAAU,UAAY,UAAY,CAC9C,OAAO,KAAK,MACd,EAEAA,EAAc,UAAU,YAAc,UAAY,CAChD,GAAI,KAAK,UAAY,KAAM,CAIzB,QAHI+C,EAAW,CAAC,EACZC,EAAS,KAAK,UAAU,EACxBlC,EAAIkC,EAAO,OACNhE,EAAI,EAAGA,EAAI8B,EAAG9B,IACrB+D,EAAWA,EAAS,OAAOC,EAAOhE,CAAC,EAAE,SAAS,CAAC,EAEjD,KAAK,SAAW+D,CAClB,CACA,OAAO,KAAK,QACd,EAEA/C,EAAc,UAAU,cAAgB,UAAY,CAClD,KAAK,SAAW,IAClB,EAEAA,EAAc,UAAU,cAAgB,UAAY,CAClD,KAAK,SAAW,IAClB,EAEAA,EAAc,UAAU,gCAAkC,UAAY,CACpE,KAAK,2BAA6B,IACpC,EAEAA,EAAc,UAAU,YAAc,UAAY,CAChD,GAAI,KAAK,UAAY,KAAM,CAIzB,QAHIzB,EAAW,CAAC,EACZyE,EAAS,KAAK,UAAU,EACxBlC,EAAIkC,EAAO,OACNhE,EAAI,EAAGA,EAAIgE,EAAO,OAAQhE,IACjCT,EAAWA,EAAS,OAAOyE,EAAOhE,CAAC,EAAE,SAAS,CAAC,EAGjDT,EAAWA,EAAS,OAAO,KAAK,KAAK,EAErC,KAAK,SAAWA,CAClB,CACA,OAAO,KAAK,QACd,EAEAyB,EAAc,UAAU,8BAAgC,UAAY,CAClE,OAAO,KAAK,0BACd,EAEAA,EAAc,UAAU,8BAAgC,SAAU+C,EAAU,CAC1E,GAAI,KAAK,4BAA8B,KACrC,KAAM,gBAGR,KAAK,2BAA6BA,CACpC,EAEA/C,EAAc,UAAU,QAAU,UAAY,CAC5C,OAAO,KAAK,SACd,EAEAA,EAAc,UAAU,aAAe,SAAUjD,EAAO,CACtD,GAAIA,EAAM,gBAAgB,GAAK,KAC7B,KAAM,8BAGR,KAAK,UAAYA,EAEbA,EAAM,QAAU,OAClBA,EAAM,OAAS,KAAK,OAAO,QAAQ,WAAW,EAElD,EAEAiD,EAAc,UAAU,UAAY,UAAY,CAC9C,OAAO,KAAK,MACd,EAEAA,EAAc,UAAU,qBAAuB,SAAUiD,EAAWC,EAAY,CAC9E,GAAI,EAAED,GAAa,MAAQC,GAAc,MACvC,KAAM,gBAGR,GAAID,GAAaC,EACf,MAAO,GAGT,IAAIC,EAAaF,EAAU,SAAS,EAChCP,EAEJ,EAAG,CAGD,GAFAA,EAAaS,EAAW,UAAU,EAE9BT,GAAc,KAChB,MAGF,GAAIA,GAAcQ,EAChB,MAAO,GAIT,GADAC,EAAaT,EAAW,SAAS,EAC7BS,GAAc,KAChB,KAEJ,OAAS,IAETA,EAAaD,EAAW,SAAS,EAEjC,EAAG,CAGD,GAFAR,EAAaS,EAAW,UAAU,EAE9BT,GAAc,KAChB,MAGF,GAAIA,GAAcO,EAChB,MAAO,GAIT,GADAE,EAAaT,EAAW,SAAS,EAC7BS,GAAc,KAChB,KAEJ,OAAS,IAET,MAAO,EACT,EAEAnD,EAAc,UAAU,0BAA4B,UAAY,CAS9D,QARIxB,EACAgC,EACAC,EACA2C,EACAC,EAEAC,EAAQ,KAAK,YAAY,EACzBxC,EAAIwC,EAAM,OACLtE,EAAI,EAAGA,EAAI8B,EAAG9B,IAAK,CAS1B,GARAR,EAAO8E,EAAMtE,CAAC,EAEdwB,EAAahC,EAAK,OAClBiC,EAAajC,EAAK,OAClBA,EAAK,IAAM,KACXA,EAAK,YAAcgC,EACnBhC,EAAK,YAAciC,EAEfD,GAAcC,EAAY,CAC5BjC,EAAK,IAAMgC,EAAW,SAAS,EAC/B,QACF,CAIA,IAFA4C,EAAsB5C,EAAW,SAAS,EAEnChC,EAAK,KAAO,MAAM,CAIvB,IAHAA,EAAK,YAAciC,EACnB4C,EAAsB5C,EAAW,SAAS,EAEnCjC,EAAK,KAAO,MAAM,CACvB,GAAI6E,GAAuBD,EAAqB,CAC9C5E,EAAK,IAAM6E,EACX,KACF,CAEA,GAAIA,GAAuB,KAAK,UAC9B,MAGF,GAAI7E,EAAK,KAAO,KACd,KAAM,gBAERA,EAAK,YAAc6E,EAAoB,UAAU,EACjDA,EAAsB7E,EAAK,YAAY,SAAS,CAClD,CAEA,GAAI4E,GAAuB,KAAK,UAC9B,MAGE5E,EAAK,KAAO,OACdA,EAAK,YAAc4E,EAAoB,UAAU,EACjDA,EAAsB5E,EAAK,YAAY,SAAS,EAEpD,CAEA,GAAIA,EAAK,KAAO,KACd,KAAM,eAEV,CACF,EAEAwB,EAAc,UAAU,yBAA2B,SAAUiD,EAAWC,EAAY,CAClF,GAAID,GAAaC,EACf,OAAOD,EAAU,SAAS,EAE5B,IAAIM,EAAkBN,EAAU,SAAS,EAEzC,EAAG,CACD,GAAIM,GAAmB,KACrB,MAEF,IAAIC,EAAmBN,EAAW,SAAS,EAE3C,EAAG,CACD,GAAIM,GAAoB,KACtB,MAGF,GAAIA,GAAoBD,EACtB,OAAOC,EAETA,EAAmBA,EAAiB,UAAU,EAAE,SAAS,CAC3D,OAAS,IAETD,EAAkBA,EAAgB,UAAU,EAAE,SAAS,CACzD,OAAS,IAET,OAAOA,CACT,EAEAvD,EAAc,UAAU,wBAA0B,SAAUjD,EAAO0G,EAAO,CACpE1G,GAAS,MAAQ0G,GAAS,OAC5B1G,EAAQ,KAAK,UACb0G,EAAQ,GAMV,QAJI3G,EAEAiC,EAAQhC,EAAM,SAAS,EACvB+D,EAAI/B,EAAM,OACLC,EAAI,EAAGA,EAAI8B,EAAG9B,IACrBlC,EAAOiC,EAAMC,CAAC,EACdlC,EAAK,mBAAqB2G,EAEtB3G,EAAK,OAAS,MAChB,KAAK,wBAAwBA,EAAK,MAAO2G,EAAQ,CAAC,CAGxD,EAEAzD,EAAc,UAAU,oBAAsB,UAAY,CAIxD,QAHIxB,EAEAsC,EAAI,KAAK,MAAM,OACV9B,EAAI,EAAGA,EAAI8B,EAAG9B,IAGrB,GAFAR,EAAO,KAAK,MAAMQ,CAAC,EAEf,KAAK,qBAAqBR,EAAK,OAAQA,EAAK,MAAM,EACpD,MAAO,GAGX,MAAO,EACT,EAEAhD,EAAO,QAAUwE,CAEX,EAEC,SAASxE,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIO,EAAkBP,EAAoB,CAAC,EAE3C,SAAS4H,GAAoB,CAAC,CAArBjI,EAAAiI,EAAA,qBAGT,QAAS7G,KAAQR,EACfqH,EAAkB7G,CAAI,EAAIR,EAAgBQ,CAAI,EAGhD6G,EAAkB,eAAiB,KAEnCA,EAAkB,oBAAsB,GACxCA,EAAkB,wBAA0B,IAC5CA,EAAkB,2BAA6B,KAC/CA,EAAkB,yBAA2B,GAC7CA,EAAkB,kCAAoC,EACtDA,EAAkB,6BAA+B,IACjDA,EAAkB,sCAAwC,IAC1DA,EAAkB,gDAAkD,GACpEA,EAAkB,8CAAgD,GAClEA,EAAkB,mCAAqC,GACvDA,EAAkB,0BAA4B,IAC9CA,EAAkB,4BAA8B,IAChDA,EAAkB,4BAA8B,IAChDA,EAAkB,kCAAoC,IACtDA,EAAkB,sBAAwBA,EAAkB,kCAAoC,EAChGA,EAAkB,mBAAqBA,EAAkB,oBAAsB,GAC/EA,EAAkB,yBAA2B,IAC7CA,EAAkB,mCAAqC,GACvDA,EAAkB,gBAAkB,EACpCA,EAAkB,8BAAgC,GAElDlI,EAAO,QAAUkI,CAEX,EAEC,SAASlI,EAAQD,EAASO,EAAqB,CAEtD,aAUA,IAAImE,EAAQnE,EAAoB,EAAE,EAElC,SAASS,GAAY,CAAC,CAAbd,EAAAc,EAAA,aASTA,EAAU,qBAAuB,SAAUoH,EAAOC,EAAOC,EAAeC,EAAkB,CACxF,GAAI,CAACH,EAAM,WAAWC,CAAK,EACzB,KAAM,gBAGR,IAAIG,EAAa,IAAI,MAAM,CAAC,EAE5B,KAAK,oCAAoCJ,EAAOC,EAAOG,CAAU,EAEjEF,EAAc,CAAC,EAAI,KAAK,IAAIF,EAAM,SAAS,EAAGC,EAAM,SAAS,CAAC,EAAI,KAAK,IAAID,EAAM,EAAGC,EAAM,CAAC,EAC3FC,EAAc,CAAC,EAAI,KAAK,IAAIF,EAAM,UAAU,EAAGC,EAAM,UAAU,CAAC,EAAI,KAAK,IAAID,EAAM,EAAGC,EAAM,CAAC,EAGzFD,EAAM,KAAK,GAAKC,EAAM,KAAK,GAAKD,EAAM,SAAS,GAAKC,EAAM,SAAS,EAYrEC,EAAc,CAAC,GAAK,KAAK,IAAID,EAAM,KAAK,EAAID,EAAM,KAAK,EAAGA,EAAM,SAAS,EAAIC,EAAM,SAAS,CAAC,EACpFA,EAAM,KAAK,GAAKD,EAAM,KAAK,GAAKC,EAAM,SAAS,GAAKD,EAAM,SAAS,IAY5EE,EAAc,CAAC,GAAK,KAAK,IAAIF,EAAM,KAAK,EAAIC,EAAM,KAAK,EAAGA,EAAM,SAAS,EAAID,EAAM,SAAS,CAAC,GAE3FA,EAAM,KAAK,GAAKC,EAAM,KAAK,GAAKD,EAAM,UAAU,GAAKC,EAAM,UAAU,EAcvEC,EAAc,CAAC,GAAK,KAAK,IAAID,EAAM,KAAK,EAAID,EAAM,KAAK,EAAGA,EAAM,UAAU,EAAIC,EAAM,UAAU,CAAC,EACtFA,EAAM,KAAK,GAAKD,EAAM,KAAK,GAAKC,EAAM,UAAU,GAAKD,EAAM,UAAU,IAc9EE,EAAc,CAAC,GAAK,KAAK,IAAIF,EAAM,KAAK,EAAIC,EAAM,KAAK,EAAGA,EAAM,UAAU,EAAID,EAAM,UAAU,CAAC,GAIjG,IAAIK,EAAQ,KAAK,KAAKJ,EAAM,WAAW,EAAID,EAAM,WAAW,IAAMC,EAAM,WAAW,EAAID,EAAM,WAAW,EAAE,EAEtGC,EAAM,WAAW,IAAMD,EAAM,WAAW,GAAKC,EAAM,WAAW,IAAMD,EAAM,WAAW,IAEvFK,EAAQ,GAGV,IAAIC,EAAUD,EAAQH,EAAc,CAAC,EACjCK,EAAUL,EAAc,CAAC,EAAIG,EAC7BH,EAAc,CAAC,EAAIK,EACrBA,EAAUL,EAAc,CAAC,EAEzBI,EAAUJ,EAAc,CAAC,EAI3BA,EAAc,CAAC,EAAI,GAAKE,EAAW,CAAC,GAAKG,EAAU,EAAIJ,GACvDD,EAAc,CAAC,EAAI,GAAKE,EAAW,CAAC,GAAKE,EAAU,EAAIH,EACzD,EAUAvH,EAAU,oCAAsC,SAAUoH,EAAOC,EAAOG,EAAY,CAC9EJ,EAAM,WAAW,EAAIC,EAAM,WAAW,EACxCG,EAAW,CAAC,EAAI,GAEhBA,EAAW,CAAC,EAAI,EAGdJ,EAAM,WAAW,EAAIC,EAAM,WAAW,EACxCG,EAAW,CAAC,EAAI,GAEhBA,EAAW,CAAC,EAAI,CAEpB,EAQAxH,EAAU,iBAAmB,SAAUoH,EAAOC,EAAOO,EAAQ,CAE3D,IAAIC,EAAMT,EAAM,WAAW,EACvBU,EAAMV,EAAM,WAAW,EACvBW,EAAMV,EAAM,WAAW,EACvBW,EAAMX,EAAM,WAAW,EAG3B,GAAID,EAAM,WAAWC,CAAK,EACxB,OAAAO,EAAO,CAAC,EAAIC,EACZD,EAAO,CAAC,EAAIE,EACZF,EAAO,CAAC,EAAIG,EACZH,EAAO,CAAC,EAAII,EACL,GAGT,IAAIC,EAAYb,EAAM,KAAK,EACvBc,EAAYd,EAAM,KAAK,EACvBe,EAAaf,EAAM,SAAS,EAC5BgB,EAAehB,EAAM,KAAK,EAC1BiB,EAAejB,EAAM,UAAU,EAC/BkB,EAAgBlB,EAAM,SAAS,EAC/BmB,EAAanB,EAAM,aAAa,EAChCoB,EAAcpB,EAAM,cAAc,EAElCqB,EAAYpB,EAAM,KAAK,EACvBqB,EAAYrB,EAAM,KAAK,EACvBsB,EAAatB,EAAM,SAAS,EAC5BuB,EAAevB,EAAM,KAAK,EAC1BwB,EAAexB,EAAM,UAAU,EAC/ByB,EAAgBzB,EAAM,SAAS,EAC/B0B,EAAa1B,EAAM,aAAa,EAChC2B,EAAc3B,EAAM,cAAc,EAGlC4B,EAAkB,GAClBC,EAAkB,GAGtB,GAAIrB,IAAQE,EAAK,CACf,GAAID,EAAME,EACR,OAAAJ,EAAO,CAAC,EAAIC,EACZD,EAAO,CAAC,EAAIM,EACZN,EAAO,CAAC,EAAIG,EACZH,EAAO,CAAC,EAAIiB,EACL,GACF,GAAIf,EAAME,EACf,OAAAJ,EAAO,CAAC,EAAIC,EACZD,EAAO,CAAC,EAAIS,EACZT,EAAO,CAAC,EAAIG,EACZH,EAAO,CAAC,EAAIc,EACL,EAIX,SAESZ,IAAQE,EAAK,CAClB,GAAIH,EAAME,EACR,OAAAH,EAAO,CAAC,EAAIK,EACZL,EAAO,CAAC,EAAIE,EACZF,EAAO,CAAC,EAAIe,EACZf,EAAO,CAAC,EAAII,EACL,GACF,GAAIH,EAAME,EACf,OAAAH,EAAO,CAAC,EAAIO,EACZP,EAAO,CAAC,EAAIE,EACZF,EAAO,CAAC,EAAIa,EACZb,EAAO,CAAC,EAAII,EACL,EAIX,KAAO,CAEL,IAAImB,EAAS/B,EAAM,OAASA,EAAM,MAC9BgC,EAAS/B,EAAM,OAASA,EAAM,MAG9BgC,GAAcrB,EAAMF,IAAQC,EAAMF,GAClCyB,EAAqB,OACrBC,EAAqB,OACrBC,EAAc,OACdC,EAAc,OACdC,EAAc,OACdC,EAAc,OAiDlB,GA9CI,CAACR,IAAWE,EACVxB,EAAME,GACRH,EAAO,CAAC,EAAIQ,EACZR,EAAO,CAAC,EAAIS,EACZY,EAAkB,KAElBrB,EAAO,CAAC,EAAIO,EACZP,EAAO,CAAC,EAAIM,EACZe,EAAkB,IAEXE,IAAWE,IAChBxB,EAAME,GACRH,EAAO,CAAC,EAAIK,EACZL,EAAO,CAAC,EAAIM,EACZe,EAAkB,KAElBrB,EAAO,CAAC,EAAIU,EACZV,EAAO,CAAC,EAAIS,EACZY,EAAkB,KAKlB,CAACG,IAAWC,EACVtB,EAAMF,GACRD,EAAO,CAAC,EAAIgB,EACZhB,EAAO,CAAC,EAAIiB,EACZK,EAAkB,KAElBtB,EAAO,CAAC,EAAIe,EACZf,EAAO,CAAC,EAAIc,EACZQ,EAAkB,IAEXE,IAAWC,IAChBtB,EAAMF,GACRD,EAAO,CAAC,EAAIa,EACZb,EAAO,CAAC,EAAIc,EACZQ,EAAkB,KAElBtB,EAAO,CAAC,EAAIkB,EACZlB,EAAO,CAAC,EAAIiB,EACZK,EAAkB,KAKlBD,GAAmBC,EACrB,MAAO,GAsBT,GAlBIrB,EAAME,EACJD,EAAME,GACRsB,EAAqB,KAAK,qBAAqBH,EAAQE,EAAY,CAAC,EACpEE,EAAqB,KAAK,qBAAqBH,EAAQC,EAAY,CAAC,IAEpEC,EAAqB,KAAK,qBAAqB,CAACH,EAAQE,EAAY,CAAC,EACrEE,EAAqB,KAAK,qBAAqB,CAACH,EAAQC,EAAY,CAAC,GAGnEvB,EAAME,GACRsB,EAAqB,KAAK,qBAAqB,CAACH,EAAQE,EAAY,CAAC,EACrEE,EAAqB,KAAK,qBAAqB,CAACH,EAAQC,EAAY,CAAC,IAErEC,EAAqB,KAAK,qBAAqBH,EAAQE,EAAY,CAAC,EACpEE,EAAqB,KAAK,qBAAqBH,EAAQC,EAAY,CAAC,GAIpE,CAACJ,EACH,OAAQK,EAAoB,CAC1B,IAAK,GACHG,EAAcvB,EACdsB,EAAc3B,EAAM,CAACW,EAAca,EACnCzB,EAAO,CAAC,EAAI4B,EACZ5B,EAAO,CAAC,EAAI6B,EACZ,MACF,IAAK,GACHD,EAAclB,EACdmB,EAAc3B,EAAMS,EAAac,EACjCzB,EAAO,CAAC,EAAI4B,EACZ5B,EAAO,CAAC,EAAI6B,EACZ,MACF,IAAK,GACHA,EAAcpB,EACdmB,EAAc3B,EAAMW,EAAca,EAClCzB,EAAO,CAAC,EAAI4B,EACZ5B,EAAO,CAAC,EAAI6B,EACZ,MACF,IAAK,GACHD,EAAcpB,EACdqB,EAAc3B,EAAM,CAACS,EAAac,EAClCzB,EAAO,CAAC,EAAI4B,EACZ5B,EAAO,CAAC,EAAI6B,EACZ,KACJ,CAEF,GAAI,CAACP,EACH,OAAQK,EAAoB,CAC1B,IAAK,GACHI,EAAcjB,EACdgB,EAAc3B,EAAM,CAACiB,EAAcK,EACnCzB,EAAO,CAAC,EAAI8B,EACZ9B,EAAO,CAAC,EAAI+B,EACZ,MACF,IAAK,GACHD,EAAcZ,EACda,EAAc3B,EAAMe,EAAaM,EACjCzB,EAAO,CAAC,EAAI8B,EACZ9B,EAAO,CAAC,EAAI+B,EACZ,MACF,IAAK,GACHA,EAAcd,EACda,EAAc3B,EAAMiB,EAAcK,EAClCzB,EAAO,CAAC,EAAI8B,EACZ9B,EAAO,CAAC,EAAI+B,EACZ,MACF,IAAK,GACHD,EAAcd,EACde,EAAc3B,EAAM,CAACe,EAAaM,EAClCzB,EAAO,CAAC,EAAI8B,EACZ9B,EAAO,CAAC,EAAI+B,EACZ,KACJ,CAEJ,CACF,MAAO,EACT,EASA3J,EAAU,qBAAuB,SAAUyH,EAAO4B,EAAYO,EAAM,CAClE,OAAInC,EAAQ4B,EACHO,EAEA,EAAIA,EAAO,CAEtB,EAMA5J,EAAU,gBAAkB,SAAU6J,EAAIC,EAAIC,EAAIC,EAAI,CACpD,GAAIA,GAAM,KACR,OAAO,KAAK,iBAAiBH,EAAIC,EAAIC,CAAE,EAGzC,IAAIE,EAAKJ,EAAG,EACRK,EAAKL,EAAG,EACRM,EAAKL,EAAG,EACRM,EAAKN,EAAG,EACRO,EAAKN,EAAG,EACRO,EAAKP,EAAG,EACRQ,EAAKP,EAAG,EACRQ,EAAKR,EAAG,EACRrI,EAAI,OACJC,EAAI,OACJ6I,EAAK,OACLC,EAAK,OACLC,EAAK,OACLC,EAAK,OACLC,EAAK,OACLC,EAAK,OACLC,EAAQ,OAYZ,OAVAN,EAAKL,EAAKF,EACVS,EAAKV,EAAKE,EACVU,EAAKV,EAAKD,EAAKD,EAAKG,EAEpBM,EAAKF,EAAKF,EACVM,EAAKP,EAAKE,EACVO,EAAKP,EAAKD,EAAKD,EAAKG,EAEpBO,EAAQN,EAAKG,EAAKF,EAAKC,EAEnBI,IAAU,EACL,MAGTpJ,GAAKgJ,EAAKG,EAAKF,EAAKC,GAAME,EAC1BnJ,GAAK8I,EAAKG,EAAKJ,EAAKK,GAAMC,EAEnB,IAAIrH,EAAM/B,EAAGC,CAAC,EACvB,EAMA5B,EAAU,cAAgB,SAAUgL,EAAIC,EAAIC,EAAIC,EAAI,CAClD,IAAIC,EAAU,OAEd,OAAIJ,IAAOE,GACTE,EAAU,KAAK,MAAMD,EAAKF,IAAOC,EAAKF,EAAG,EAErCE,EAAKF,EACPI,GAAW,KAAK,GACPD,EAAKF,IACdG,GAAW,KAAK,SAETD,EAAKF,EACdG,EAAU,KAAK,gBAEfA,EAAU,KAAK,QAGVA,CACT,EAOApL,EAAU,YAAc,SAAUqL,EAAIC,EAAIC,EAAIC,EAAI,CAChD,IAAI,EAAIH,EAAG,EACPI,EAAIJ,EAAG,EACPK,EAAIJ,EAAG,EACPK,EAAIL,EAAG,EACPM,EAAIL,EAAG,EACPM,EAAIN,EAAG,EACPO,EAAIN,EAAG,EACPjH,EAAIiH,EAAG,EACPO,GAAOL,EAAI,IAAMnH,EAAIsH,IAAMC,EAAIF,IAAMD,EAAIF,GAE7C,GAAIM,IAAQ,EACV,MAAO,GAEP,IAAIC,IAAWzH,EAAIsH,IAAMC,EAAI,IAAMF,EAAIE,IAAMvH,EAAIkH,IAAMM,EACnDE,IAAUR,EAAIE,IAAMG,EAAI,IAAMJ,EAAI,IAAMnH,EAAIkH,IAAMM,EACtD,MAAO,GAAIC,GAAUA,EAAS,GAAK,EAAIC,GAASA,EAAQ,CAE5D,EAQAjM,EAAU,QAAU,GAAM,KAAK,GAC/BA,EAAU,gBAAkB,IAAM,KAAK,GACvCA,EAAU,OAAS,EAAM,KAAK,GAC9BA,EAAU,SAAW,EAAM,KAAK,GAEhCf,EAAO,QAAUe,CAEX,EAEC,SAASf,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASU,GAAQ,CAAC,CAATf,EAAAe,EAAA,SAKTA,EAAM,KAAO,SAAUR,EAAO,CAC5B,OAAIA,EAAQ,EACH,EACEA,EAAQ,EACV,GAEA,CAEX,EAEAQ,EAAM,MAAQ,SAAUR,EAAO,CAC7B,OAAOA,EAAQ,EAAI,KAAK,KAAKA,CAAK,EAAI,KAAK,MAAMA,CAAK,CACxD,EAEAQ,EAAM,KAAO,SAAUR,EAAO,CAC5B,OAAOA,EAAQ,EAAI,KAAK,MAAMA,CAAK,EAAI,KAAK,KAAKA,CAAK,CACxD,EAEAR,EAAO,QAAUgB,CAEX,EAEC,SAAShB,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASqB,GAAU,CAAC,CAAX1B,EAAA0B,EAAA,WAETA,EAAQ,UAAY,WACpBA,EAAQ,UAAY,YAEpB3B,EAAO,QAAU2B,CAEX,EAEC,SAAS3B,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAI2M,EAAe,UAAY,CAAE,SAASC,EAAiB/L,EAAQgM,EAAO,CAAE,QAAS3J,EAAI,EAAGA,EAAI2J,EAAM,OAAQ3J,IAAK,CAAE,IAAI4J,EAAaD,EAAM3J,CAAC,EAAG4J,EAAW,WAAaA,EAAW,YAAc,GAAOA,EAAW,aAAe,GAAU,UAAWA,IAAYA,EAAW,SAAW,IAAM,OAAO,eAAejM,EAAQiM,EAAW,IAAKA,CAAU,CAAG,CAAE,CAAlT,OAAAnN,EAAAiN,EAAA,oBAA2T,SAAUG,EAAaC,EAAYC,EAAa,CAAE,OAAID,GAAYJ,EAAiBG,EAAY,UAAWC,CAAU,EAAOC,GAAaL,EAAiBG,EAAaE,CAAW,EAAUF,CAAa,CAAG,EAAE,EAEljB,SAASG,EAAgBC,EAAUJ,EAAa,CAAE,GAAI,EAAEI,aAAoBJ,GAAgB,MAAM,IAAI,UAAU,mCAAmC,CAAK,CAA/IpN,EAAAuN,EAAA,mBAET,IAAIE,EAAWzN,EAAA,SAAkBO,EAAO,CACtC,MAAO,CAAE,MAAOA,EAAO,KAAM,KAAM,KAAM,IAAK,CAChD,EAFe,YAIXmN,EAAM1N,EAAA,SAAa2N,EAAMtM,EAAMuM,EAAMC,EAAM,CAC7C,OAAIF,IAAS,KACXA,EAAK,KAAOtM,EAEZwM,EAAK,KAAOxM,EAGVuM,IAAS,KACXA,EAAK,KAAOvM,EAEZwM,EAAK,KAAOxM,EAGdA,EAAK,KAAOsM,EACZtM,EAAK,KAAOuM,EAEZC,EAAK,SAEExM,CACT,EAnBU,OAqBNyM,EAAU9N,EAAA,SAAiBqB,EAAMwM,EAAM,CACzC,IAAIF,EAAOtM,EAAK,KACZuM,EAAOvM,EAAK,KAGhB,OAAIsM,IAAS,KACXA,EAAK,KAAOC,EAEZC,EAAK,KAAOD,EAGVA,IAAS,KACXA,EAAK,KAAOD,EAEZE,EAAK,KAAOF,EAGdtM,EAAK,KAAOA,EAAK,KAAO,KAExBwM,EAAK,SAEExM,CACT,EAtBc,WAwBVoD,EAAa,UAAY,CAC3B,SAASA,EAAWsJ,EAAM,CACxB,IAAIC,EAAQ,KAEZT,EAAgB,KAAM9I,CAAU,EAEhC,KAAK,OAAS,EACd,KAAK,KAAO,KACZ,KAAK,KAAO,KAGVsJ,GAAK,QAAQ,SAAUE,EAAG,CACxB,OAAOD,EAAM,KAAKC,CAAC,CACrB,CAAC,CAEL,CAdS,OAAAjO,EAAAyE,EAAA,cAgBTuI,EAAavI,EAAY,CAAC,CACxB,IAAK,OACL,MAAOzE,EAAA,UAAgB,CACrB,OAAO,KAAK,MACd,EAFO,OAGT,EAAG,CACD,IAAK,eACL,MAAOA,EAAA,SAAsBkO,EAAKC,EAAW,CAC3C,OAAOT,EAAIS,EAAU,KAAMV,EAASS,CAAG,EAAGC,EAAW,IAAI,CAC3D,EAFO,eAGT,EAAG,CACD,IAAK,cACL,MAAOnO,EAAA,SAAqBkO,EAAKC,EAAW,CAC1C,OAAOT,EAAIS,EAAWV,EAASS,CAAG,EAAGC,EAAU,KAAM,IAAI,CAC3D,EAFO,cAGT,EAAG,CACD,IAAK,mBACL,MAAOnO,EAAA,SAA0BiF,EAASkJ,EAAW,CACnD,OAAOT,EAAIS,EAAU,KAAMlJ,EAASkJ,EAAW,IAAI,CACrD,EAFO,mBAGT,EAAG,CACD,IAAK,kBACL,MAAOnO,EAAA,SAAyBiF,EAASkJ,EAAW,CAClD,OAAOT,EAAIS,EAAWlJ,EAASkJ,EAAU,KAAM,IAAI,CACrD,EAFO,kBAGT,EAAG,CACD,IAAK,OACL,MAAOnO,EAAA,SAAckO,EAAK,CACxB,OAAOR,EAAI,KAAK,KAAMD,EAASS,CAAG,EAAG,KAAM,IAAI,CACjD,EAFO,OAGT,EAAG,CACD,IAAK,UACL,MAAOlO,EAAA,SAAiBkO,EAAK,CAC3B,OAAOR,EAAI,KAAMD,EAASS,CAAG,EAAG,KAAK,KAAM,IAAI,CACjD,EAFO,UAGT,EAAG,CACD,IAAK,SACL,MAAOlO,EAAA,SAAgBqB,EAAM,CAC3B,OAAOyM,EAAQzM,EAAM,IAAI,CAC3B,EAFO,SAGT,EAAG,CACD,IAAK,MACL,MAAOrB,EAAA,UAAe,CACpB,OAAO8N,EAAQ,KAAK,KAAM,IAAI,EAAE,KAClC,EAFO,MAGT,EAAG,CACD,IAAK,UACL,MAAO9N,EAAA,UAAmB,CACxB,OAAO8N,EAAQ,KAAK,KAAM,IAAI,CAChC,EAFO,UAGT,EAAG,CACD,IAAK,QACL,MAAO9N,EAAA,UAAiB,CACtB,OAAO8N,EAAQ,KAAK,KAAM,IAAI,EAAE,KAClC,EAFO,QAGT,EAAG,CACD,IAAK,YACL,MAAO9N,EAAA,UAAqB,CAC1B,OAAO8N,EAAQ,KAAK,KAAM,IAAI,CAChC,EAFO,YAGT,EAAG,CACD,IAAK,gBACL,MAAO9N,EAAA,SAAuBsF,EAAO,CACnC,GAAIA,GAAS,KAAK,OAAO,EAAG,CAG1B,QAFI/B,EAAI,EACJ6K,EAAU,KAAK,KACZ7K,EAAI+B,GACT8I,EAAUA,EAAQ,KAClB7K,IAEF,OAAO6K,EAAQ,KACjB,CACF,EAVO,gBAWT,EAAG,CACD,IAAK,gBACL,MAAOpO,EAAA,SAAuBsF,EAAO/E,EAAO,CAC1C,GAAI+E,GAAS,KAAK,OAAO,EAAG,CAG1B,QAFI/B,EAAI,EACJ6K,EAAU,KAAK,KACZ7K,EAAI+B,GACT8I,EAAUA,EAAQ,KAClB7K,IAEF6K,EAAQ,MAAQ7N,CAClB,CACF,EAVO,gBAWT,CAAC,CAAC,EAEKkE,CACT,EAAE,EAEF1E,EAAO,QAAU0E,CAEX,EAEC,SAAS1E,EAAQD,EAASO,EAAqB,CAEtD,aAMA,SAASmE,EAAM/B,EAAGC,EAAGgK,EAAG,CACtB,KAAK,EAAI,KACT,KAAK,EAAI,KACLjK,GAAK,MAAQC,GAAK,MAAQgK,GAAK,MACjC,KAAK,EAAI,EACT,KAAK,EAAI,GACA,OAAOjK,GAAK,UAAY,OAAOC,GAAK,UAAYgK,GAAK,MAC9D,KAAK,EAAIjK,EACT,KAAK,EAAIC,GACAD,EAAE,YAAY,MAAQ,SAAWC,GAAK,MAAQgK,GAAK,OAC5DA,EAAIjK,EACJ,KAAK,EAAIiK,EAAE,EACX,KAAK,EAAIA,EAAE,EAEf,CAdS1M,EAAAwE,EAAA,SAgBTA,EAAM,UAAU,KAAO,UAAY,CACjC,OAAO,KAAK,CACd,EAEAA,EAAM,UAAU,KAAO,UAAY,CACjC,OAAO,KAAK,CACd,EAEAA,EAAM,UAAU,YAAc,UAAY,CACxC,OAAO,IAAIA,EAAM,KAAK,EAAG,KAAK,CAAC,CACjC,EAEAA,EAAM,UAAU,YAAc,SAAU/B,EAAGC,EAAGgK,EAAG,CAC3CjK,EAAE,YAAY,MAAQ,SAAWC,GAAK,MAAQgK,GAAK,MACrDA,EAAIjK,EACJ,KAAK,YAAYiK,EAAE,EAAGA,EAAE,CAAC,GAChB,OAAOjK,GAAK,UAAY,OAAOC,GAAK,UAAYgK,GAAK,OAE1D,SAASjK,CAAC,GAAKA,GAAK,SAASC,CAAC,GAAKA,EACrC,KAAK,KAAKD,EAAGC,CAAC,GAEd,KAAK,EAAI,KAAK,MAAMD,EAAI,EAAG,EAC3B,KAAK,EAAI,KAAK,MAAMC,EAAI,EAAG,GAGjC,EAEA8B,EAAM,UAAU,KAAO,SAAU/B,EAAGC,EAAG,CACrC,KAAK,EAAID,EACT,KAAK,EAAIC,CACX,EAEA8B,EAAM,UAAU,UAAY,SAAU7B,EAAIC,EAAI,CAC5C,KAAK,GAAKD,EACV,KAAK,GAAKC,CACZ,EAEA4B,EAAM,UAAU,OAAS,SAAUW,EAAK,CACtC,GAAIA,EAAI,YAAY,MAAQ,QAAS,CACnC,IAAId,EAAKc,EACT,OAAO,KAAK,GAAKd,EAAG,GAAK,KAAK,GAAKA,EAAG,CACxC,CACA,OAAO,MAAQc,CACjB,EAEAX,EAAM,UAAU,SAAW,UAAY,CACrC,OAAO,IAAIA,EAAM,EAAE,YAAY,KAAO,MAAQ,KAAK,EAAI,MAAQ,KAAK,EAAI,GAC1E,EAEAzE,EAAO,QAAUyE,CAEX,EAEC,SAASzE,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASsB,EAAWc,EAAGC,EAAGP,EAAOC,EAAQ,CACvC,KAAK,EAAI,EACT,KAAK,EAAI,EACT,KAAK,MAAQ,EACb,KAAK,OAAS,EAEVK,GAAK,MAAQC,GAAK,MAAQP,GAAS,MAAQC,GAAU,OACvD,KAAK,EAAIK,EACT,KAAK,EAAIC,EACT,KAAK,MAAQP,EACb,KAAK,OAASC,EAElB,CAZSpC,EAAA2B,EAAA,cAcTA,EAAW,UAAU,KAAO,UAAY,CACtC,OAAO,KAAK,CACd,EAEAA,EAAW,UAAU,KAAO,SAAUc,EAAG,CACvC,KAAK,EAAIA,CACX,EAEAd,EAAW,UAAU,KAAO,UAAY,CACtC,OAAO,KAAK,CACd,EAEAA,EAAW,UAAU,KAAO,SAAUe,EAAG,CACvC,KAAK,EAAIA,CACX,EAEAf,EAAW,UAAU,SAAW,UAAY,CAC1C,OAAO,KAAK,KACd,EAEAA,EAAW,UAAU,SAAW,SAAUQ,EAAO,CAC/C,KAAK,MAAQA,CACf,EAEAR,EAAW,UAAU,UAAY,UAAY,CAC3C,OAAO,KAAK,MACd,EAEAA,EAAW,UAAU,UAAY,SAAUS,EAAQ,CACjD,KAAK,OAASA,CAChB,EAEAT,EAAW,UAAU,SAAW,UAAY,CAC1C,OAAO,KAAK,EAAI,KAAK,KACvB,EAEAA,EAAW,UAAU,UAAY,UAAY,CAC3C,OAAO,KAAK,EAAI,KAAK,MACvB,EAEAA,EAAW,UAAU,WAAa,SAAU0M,EAAG,CAa7C,MAZI,OAAK,SAAS,EAAIA,EAAE,GAIpB,KAAK,UAAU,EAAIA,EAAE,GAIrBA,EAAE,SAAS,EAAI,KAAK,GAIpBA,EAAE,UAAU,EAAI,KAAK,EAK3B,EAEA1M,EAAW,UAAU,WAAa,UAAY,CAC5C,OAAO,KAAK,EAAI,KAAK,MAAQ,CAC/B,EAEAA,EAAW,UAAU,QAAU,UAAY,CACzC,OAAO,KAAK,KAAK,CACnB,EAEAA,EAAW,UAAU,QAAU,UAAY,CACzC,OAAO,KAAK,KAAK,EAAI,KAAK,KAC5B,EAEAA,EAAW,UAAU,WAAa,UAAY,CAC5C,OAAO,KAAK,EAAI,KAAK,OAAS,CAChC,EAEAA,EAAW,UAAU,QAAU,UAAY,CACzC,OAAO,KAAK,KAAK,CACnB,EAEAA,EAAW,UAAU,QAAU,UAAY,CACzC,OAAO,KAAK,KAAK,EAAI,KAAK,MAC5B,EAEAA,EAAW,UAAU,aAAe,UAAY,CAC9C,OAAO,KAAK,MAAQ,CACtB,EAEAA,EAAW,UAAU,cAAgB,UAAY,CAC/C,OAAO,KAAK,OAAS,CACvB,EAEA5B,EAAO,QAAU4B,CAEX,EAEC,SAAS5B,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIiO,EAAU,OAAO,QAAW,YAAc,OAAO,OAAO,UAAa,SAAW,SAAUnJ,EAAK,CAAE,OAAO,OAAOA,CAAK,EAAI,SAAUA,EAAK,CAAE,OAAOA,GAAO,OAAO,QAAW,YAAcA,EAAI,cAAgB,QAAUA,IAAQ,OAAO,UAAY,SAAW,OAAOA,CAAK,EAE3Q,SAASoJ,GAAoB,CAAC,CAArBvO,EAAAuO,EAAA,qBAETA,EAAkB,OAAS,EAE3BA,EAAkB,SAAW,SAAUpJ,EAAK,CAC1C,OAAIoJ,EAAkB,YAAYpJ,CAAG,EAC5BA,GAELA,EAAI,UAAY,OAGpBA,EAAI,SAAWoJ,EAAkB,UAAU,EAC3CA,EAAkB,UACXpJ,EAAI,SACb,EAEAoJ,EAAkB,UAAY,SAAUC,EAAI,CAC1C,OAAIA,GAAM,OAAMA,EAAKD,EAAkB,QAChC,UAAYC,CACrB,EAEAD,EAAkB,YAAc,SAAUE,EAAK,CAC7C,IAAIC,EAAO,OAAOD,EAAQ,IAAc,YAAcH,EAAQG,CAAG,EACjE,OAAOA,GAAO,MAAQC,GAAQ,UAAYA,GAAQ,UACpD,EAEA3O,EAAO,QAAUwO,CAEX,EAEC,SAASxO,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASsO,EAAmBC,EAAK,CAAE,GAAI,MAAM,QAAQA,CAAG,EAAG,CAAE,QAASrL,EAAI,EAAGsL,EAAO,MAAMD,EAAI,MAAM,EAAGrL,EAAIqL,EAAI,OAAQrL,IAAOsL,EAAKtL,CAAC,EAAIqL,EAAIrL,CAAC,EAAK,OAAOsL,CAAM,KAAS,QAAO,MAAM,KAAKD,CAAG,CAAK,CAAzL5O,EAAA2O,EAAA,sBAET,IAAI/N,EAAkBP,EAAoB,CAAC,EACvCkE,EAAgBlE,EAAoB,CAAC,EACrCyB,EAAQzB,EAAoB,CAAC,EAC7BW,EAAQX,EAAoB,CAAC,EAC7BqE,EAASrE,EAAoB,CAAC,EAC9BwB,EAASxB,EAAoB,CAAC,EAC9ByO,EAAYzO,EAAoB,EAAE,EAClC0O,EAAU1O,EAAoB,EAAE,EAEpC,SAAS2O,EAAOC,EAAa,CAC3BF,EAAQ,KAAK,IAAI,EAGjB,KAAK,cAAgBnO,EAAgB,QAErC,KAAK,oBAAsBA,EAAgB,+BAE3C,KAAK,YAAcA,EAAgB,oBAEnC,KAAK,kBAAoBA,EAAgB,4BAEzC,KAAK,sBAAwBA,EAAgB,gCAE7C,KAAK,gBAAkBA,EAAgB,yBAOvC,KAAK,qBAAuBA,EAAgB,gCAK5C,KAAK,iBAAmB,IAAI,IAC5B,KAAK,aAAe,IAAI2D,EAAc,IAAI,EAC1C,KAAK,iBAAmB,GACxB,KAAK,YAAc,GACnB,KAAK,YAAc,GAEf0K,GAAe,OACjB,KAAK,YAAcA,EAEvB,CAnCSjP,EAAAgP,EAAA,UAqCTA,EAAO,YAAc,EAErBA,EAAO,UAAY,OAAO,OAAOD,EAAQ,SAAS,EAElDC,EAAO,UAAU,gBAAkB,UAAY,CAC7C,OAAO,KAAK,YACd,EAEAA,EAAO,UAAU,YAAc,UAAY,CACzC,OAAO,KAAK,aAAa,YAAY,CACvC,EAEAA,EAAO,UAAU,YAAc,UAAY,CACzC,OAAO,KAAK,aAAa,YAAY,CACvC,EAEAA,EAAO,UAAU,8BAAgC,UAAY,CAC3D,OAAO,KAAK,aAAa,8BAA8B,CACzD,EAEAA,EAAO,UAAU,gBAAkB,UAAY,CAC7C,IAAIjN,EAAK,IAAIwC,EAAc,IAAI,EAC/B,YAAK,aAAexC,EACbA,CACT,EAEAiN,EAAO,UAAU,SAAW,SAAUnK,EAAQ,CAC5C,OAAO,IAAIH,EAAO,KAAM,KAAK,aAAcG,CAAM,CACnD,EAEAmK,EAAO,UAAU,QAAU,SAAU9M,EAAO,CAC1C,OAAO,IAAIJ,EAAM,KAAK,aAAcI,CAAK,CAC3C,EAEA8M,EAAO,UAAU,QAAU,SAAU7N,EAAO,CAC1C,OAAO,IAAIH,EAAM,KAAM,KAAMG,CAAK,CACpC,EAEA6N,EAAO,UAAU,mBAAqB,UAAY,CAChD,OAAO,KAAK,aAAa,QAAQ,GAAK,MAAQ,KAAK,aAAa,QAAQ,EAAE,SAAS,EAAE,QAAU,GAAK,KAAK,aAAa,oBAAoB,CAC5I,EAEAA,EAAO,UAAU,UAAY,UAAY,CACvC,KAAK,iBAAmB,GAEpB,KAAK,iBACP,KAAK,gBAAgB,EAGvB,KAAK,eAAe,EACpB,IAAIE,EAQJ,OANI,KAAK,mBAAmB,EAC1BA,EAAsB,GAEtBA,EAAsB,KAAK,OAAO,EAGhCtO,EAAgB,UAAY,SAGvB,IAGLsO,IACG,KAAK,aACR,KAAK,aAAa,GAIlB,KAAK,kBACP,KAAK,iBAAiB,EAGxB,KAAK,iBAAmB,GAEjBA,EACT,EAKAF,EAAO,UAAU,aAAe,UAAY,CAGrC,KAAK,aACR,KAAK,UAAU,EAEjB,KAAK,OAAO,CACd,EAMAA,EAAO,UAAU,QAAU,UAAY,CAWrC,GATI,KAAK,sBACP,KAAK,+BAA+B,EAGpC,KAAK,aAAa,cAAc,GAK9B,CAAC,KAAK,YAAa,CAIrB,QAFIjM,EACAoM,EAAW,KAAK,aAAa,YAAY,EACpC5L,EAAI,EAAGA,EAAI4L,EAAS,OAAQ5L,IACnCR,EAAOoM,EAAS5L,CAAC,EAOnB,QAFIlC,EACAiC,EAAQ,KAAK,aAAa,QAAQ,EAAE,SAAS,EACxCC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChClC,EAAOiC,EAAMC,CAAC,EAKhB,KAAK,OAAO,KAAK,aAAa,QAAQ,CAAC,CACzC,CACF,EAEAyL,EAAO,UAAU,OAAS,SAAU7J,EAAK,CACvC,GAAIA,GAAO,KACT,KAAK,QAAQ,UACJA,aAAerD,EAAO,CAC/B,IAAIT,EAAO8D,EACX,GAAI9D,EAAK,SAAS,GAAK,KAGrB,QADIiC,EAAQjC,EAAK,SAAS,EAAE,SAAS,EAC5BkC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChC,OAAOD,EAAMC,CAAC,CAAC,EAOnB,GAAIlC,EAAK,cAAgB,KAAM,CAE7B,IAAIa,EAAQb,EAAK,aAGjBa,EAAM,OAAOb,CAAI,CACnB,CACF,SAAW8D,aAAenE,EAAO,CAC/B,IAAI+B,EAAOoC,EAKX,GAAIpC,EAAK,cAAgB,KAAM,CAE7B,IAAI5B,EAAQ4B,EAAK,aAGjB5B,EAAM,OAAO4B,CAAI,CACnB,CACF,SAAWoC,aAAeT,EAAQ,CAChC,IAAIpD,EAAQ6D,EAKZ,GAAI7D,EAAM,cAAgB,KAAM,CAE9B,IAAIuD,EAASvD,EAAM,aAGnBuD,EAAO,OAAOvD,CAAK,CACrB,CACF,CACF,EAMA0N,EAAO,UAAU,eAAiB,UAAY,CACvC,KAAK,cACR,KAAK,cAAgBpO,EAAgB,QACrC,KAAK,sBAAwBA,EAAgB,gCAC7C,KAAK,gBAAkBA,EAAgB,yBACvC,KAAK,kBAAoBA,EAAgB,4BACzC,KAAK,YAAcA,EAAgB,oBACnC,KAAK,oBAAsBA,EAAgB,+BAC3C,KAAK,qBAAuBA,EAAgB,iCAG1C,KAAK,wBACP,KAAK,kBAAoB,GAE7B,EAEAoO,EAAO,UAAU,UAAY,SAAUI,EAAY,CACjD,GAAIA,GAAc,KAChB,KAAK,UAAU,IAAIvN,EAAO,EAAG,CAAC,CAAC,MAC1B,CAML,IAAImC,EAAQ,IAAI8K,EACZ3K,EAAU,KAAK,aAAa,QAAQ,EAAE,cAAc,EAExD,GAAIA,GAAW,KAAM,CACnBH,EAAM,aAAaoL,EAAW,CAAC,EAC/BpL,EAAM,aAAaoL,EAAW,CAAC,EAE/BpL,EAAM,cAAcG,EAAQ,CAAC,EAC7BH,EAAM,cAAcG,EAAQ,CAAC,EAK7B,QAHIb,EAAQ,KAAK,YAAY,EACzBjC,EAEKkC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChClC,EAAOiC,EAAMC,CAAC,EACdlC,EAAK,UAAU2C,CAAK,CAExB,CACF,CACF,EAEAgL,EAAO,UAAU,sBAAwB,SAAU1N,EAAO,CAExD,GAAIA,GAAS,KAEX,KAAK,sBAAsB,KAAK,gBAAgB,EAAE,QAAQ,CAAC,EAC3D,KAAK,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAI,MAMlD,SAJIsE,EACA7B,EAEAT,EAAQhC,EAAM,SAAS,EAClBiC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChCqC,EAAQtC,EAAMC,CAAC,EACfQ,EAAa6B,EAAM,SAAS,EAExB7B,GAAc,MAEPA,EAAW,SAAS,EAAE,QAAU,EADzC6B,EAAM,QAAQ,GAId,KAAK,sBAAsB7B,CAAU,EACrC6B,EAAM,aAAa,EAI3B,EAQAoJ,EAAO,UAAU,cAAgB,UAAY,CAW3C,QAVIK,EAAa,CAAC,EACdC,EAAW,GAIXC,EAAW,KAAK,aAAa,QAAQ,EAAE,SAAS,EAGhDC,EAAS,GAEJjM,EAAI,EAAGA,EAAIgM,EAAS,OAAQhM,IAC/BgM,EAAShM,CAAC,EAAE,SAAS,GAAK,OAC5BiM,EAAS,IAKb,GAAI,CAACA,EACH,OAAOH,EAKT,IAAIjJ,EAAU,IAAI,IACdqJ,EAAc,CAAC,EACfC,EAAU,IAAI,IACdC,EAAmB,CAAC,EAQxB,IANAA,EAAmBA,EAAiB,OAAOJ,CAAQ,EAM5CI,EAAiB,OAAS,GAAKL,GAAU,CAK9C,IAJAG,EAAY,KAAKE,EAAiB,CAAC,CAAC,EAI7BF,EAAY,OAAS,GAAKH,GAAU,CAEzC,IAAIjJ,EAAcoJ,EAAY,CAAC,EAC/BA,EAAY,OAAO,EAAG,CAAC,EACvBrJ,EAAQ,IAAIC,CAAW,EAKvB,QAFIC,EAAgBD,EAAY,SAAS,EAEhC9C,EAAI,EAAGA,EAAI+C,EAAc,OAAQ/C,IAAK,CAC7C,IAAIgD,EAAkBD,EAAc/C,CAAC,EAAE,YAAY8C,CAAW,EAG9D,GAAIqJ,EAAQ,IAAIrJ,CAAW,GAAKE,EAE9B,GAAI,CAACH,EAAQ,IAAIG,CAAe,EAC9BkJ,EAAY,KAAKlJ,CAAe,EAChCmJ,EAAQ,IAAInJ,EAAiBF,CAAW,MAMrC,CACDiJ,EAAW,GACX,KACF,CAEN,CACF,CAIA,GAAI,CAACA,EACHD,EAAa,CAAC,MAKX,CACD,IAAIO,EAAO,CAAC,EAAE,OAAOjB,EAAmBvI,CAAO,CAAC,EAChDiJ,EAAW,KAAKO,CAAI,EAGpB,QAASrM,EAAI,EAAGA,EAAIqM,EAAK,OAAQrM,IAAK,CACpC,IAAIhD,EAAQqP,EAAKrM,CAAC,EACd+B,EAAQqK,EAAiB,QAAQpP,CAAK,EACtC+E,EAAQ,IACVqK,EAAiB,OAAOrK,EAAO,CAAC,CAEpC,CACAc,EAAU,IAAI,IACdsJ,EAAU,IAAI,GAChB,CACJ,CAEA,OAAOL,CACT,EAOAL,EAAO,UAAU,8BAAgC,SAAUjM,EAAM,CAM/D,QALI8M,EAAa,CAAC,EACdlC,EAAO5K,EAAK,OAEZzB,EAAQ,KAAK,aAAa,yBAAyByB,EAAK,OAAQA,EAAK,MAAM,EAEtEQ,EAAI,EAAGA,EAAIR,EAAK,WAAW,OAAQQ,IAAK,CAE/C,IAAIuM,EAAY,KAAK,QAAQ,IAAI,EACjCA,EAAU,QAAQ,IAAI,MAAM,EAAG,CAAC,EAAG,IAAI,UAAU,EAAG,CAAC,CAAC,EAEtDxO,EAAM,IAAIwO,CAAS,EAGnB,IAAIC,EAAY,KAAK,QAAQ,IAAI,EACjC,KAAK,aAAa,IAAIA,EAAWpC,EAAMmC,CAAS,EAEhDD,EAAW,IAAIC,CAAS,EACxBnC,EAAOmC,CACT,CAEA,IAAIC,EAAY,KAAK,QAAQ,IAAI,EACjC,YAAK,aAAa,IAAIA,EAAWpC,EAAM5K,EAAK,MAAM,EAElD,KAAK,iBAAiB,IAAIA,EAAM8M,CAAU,EAGtC9M,EAAK,aAAa,EACpB,KAAK,aAAa,OAAOA,CAAI,EAI3BzB,EAAM,OAAOyB,CAAI,EAGd8M,CACT,EAMAb,EAAO,UAAU,+BAAiC,UAAY,CAC5D,IAAInH,EAAQ,CAAC,EACbA,EAAQA,EAAM,OAAO,KAAK,aAAa,YAAY,CAAC,EACpDA,EAAQ,CAAC,EAAE,OAAO8G,EAAmB,KAAK,iBAAiB,KAAK,CAAC,CAAC,EAAE,OAAO9G,CAAK,EAEhF,QAASmI,EAAI,EAAGA,EAAInI,EAAM,OAAQmI,IAAK,CACrC,IAAIC,EAAQpI,EAAMmI,CAAC,EAEnB,GAAIC,EAAM,WAAW,OAAS,EAAG,CAG/B,QAFIC,EAAO,KAAK,iBAAiB,IAAID,CAAK,EAEjC1M,EAAI,EAAGA,EAAI2M,EAAK,OAAQ3M,IAAK,CACpC,IAAIuM,EAAYI,EAAK3M,CAAC,EAClBmJ,EAAI,IAAI7K,EAAOiO,EAAU,WAAW,EAAGA,EAAU,WAAW,CAAC,EAG7DK,EAAMF,EAAM,WAAW,IAAI1M,CAAC,EAChC4M,EAAI,EAAIzD,EAAE,EACVyD,EAAI,EAAIzD,EAAE,EAIVoD,EAAU,SAAS,EAAE,OAAOA,CAAS,CACvC,CAGA,KAAK,aAAa,IAAIG,EAAOA,EAAM,OAAQA,EAAM,MAAM,CACzD,CACF,CACF,EAEAjB,EAAO,UAAY,SAAUoB,EAAaC,EAAcC,EAAQC,EAAQ,CACtE,GAAID,GAAU,MAAaC,GAAU,KAAW,CAC9C,IAAIhQ,EAAQ8P,EAEZ,GAAID,GAAe,GAAI,CACrB,IAAII,EAAWH,EAAeC,EAC9B/P,IAAU8P,EAAeG,GAAY,IAAM,GAAKJ,EAClD,KAAO,CACL,IAAIK,EAAWJ,EAAeE,EAC9BhQ,IAAUkQ,EAAWJ,GAAgB,IAAMD,EAAc,GAC3D,CAEA,OAAO7P,CACT,KAAO,CACL,IAAI8N,EAAG9B,EAEP,OAAI6D,GAAe,IACjB/B,EAAI,EAAMgC,EAAe,IACzB9D,EAAI8D,EAAe,KAEnBhC,EAAI,EAAMgC,EAAe,GACzB9D,EAAI,GAAK8D,GAGJhC,EAAI+B,EAAc7D,CAC3B,CACF,EAMAyC,EAAO,iBAAmB,SAAU1L,EAAO,CACzC,IAAIuK,EAAO,CAAC,EACZA,EAAOA,EAAK,OAAOvK,CAAK,EAExB,IAAIoN,EAAe,CAAC,EAChBC,EAAmB,IAAI,IACvBC,EAAc,GACdC,EAAa,MAEbhD,EAAK,QAAU,GAAKA,EAAK,QAAU,KACrC+C,EAAc,GACdC,EAAahD,EAAK,CAAC,GAGrB,QAAStK,EAAI,EAAGA,EAAIsK,EAAK,OAAQtK,IAAK,CACpC,IAAIlC,EAAOwM,EAAKtK,CAAC,EACbuN,EAASzP,EAAK,iBAAiB,EAAE,KACrCsP,EAAiB,IAAItP,EAAMA,EAAK,iBAAiB,EAAE,IAAI,EAEnDyP,GAAU,GACZJ,EAAa,KAAKrP,CAAI,CAE1B,CAEA,IAAI0P,EAAW,CAAC,EAGhB,IAFAA,EAAWA,EAAS,OAAOL,CAAY,EAEhC,CAACE,GAAa,CACnB,IAAII,EAAY,CAAC,EACjBA,EAAYA,EAAU,OAAOD,CAAQ,EACrCA,EAAW,CAAC,EAEZ,QAASxN,EAAI,EAAGA,EAAIsK,EAAK,OAAQtK,IAAK,CACpC,IAAIlC,EAAOwM,EAAKtK,CAAC,EAEb+B,EAAQuI,EAAK,QAAQxM,CAAI,EACzBiE,GAAS,GACXuI,EAAK,OAAOvI,EAAO,CAAC,EAGtB,IAAI2L,EAAa5P,EAAK,iBAAiB,EAEvC4P,EAAW,QAAQ,SAAUC,EAAW,CACtC,GAAIR,EAAa,QAAQQ,CAAS,EAAI,EAAG,CACvC,IAAIC,EAAcR,EAAiB,IAAIO,CAAS,EAC5CE,EAAYD,EAAc,EAE1BC,GAAa,GACfL,EAAS,KAAKG,CAAS,EAGzBP,EAAiB,IAAIO,EAAWE,CAAS,CAC3C,CACF,CAAC,CACH,CAEAV,EAAeA,EAAa,OAAOK,CAAQ,GAEvClD,EAAK,QAAU,GAAKA,EAAK,QAAU,KACrC+C,EAAc,GACdC,EAAahD,EAAK,CAAC,EAEvB,CAEA,OAAOgD,CACT,EAMA7B,EAAO,UAAU,gBAAkB,SAAUjN,EAAI,CAC/C,KAAK,aAAeA,CACtB,EAEAhC,EAAO,QAAUiP,CAEX,EAEC,SAASjP,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASuB,GAAa,CAAC,CAAd5B,EAAA4B,EAAA,cAETA,EAAW,KAAO,EAClBA,EAAW,EAAI,EAEfA,EAAW,WAAa,UAAY,CAClC,OAAAA,EAAW,EAAI,KAAK,IAAIA,EAAW,MAAM,EAAI,IACtCA,EAAW,EAAI,KAAK,MAAMA,EAAW,CAAC,CAC/C,EAEA7B,EAAO,QAAU6B,CAEX,EAEC,SAAS7B,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIwB,EAASxB,EAAoB,CAAC,EAElC,SAASyO,EAAUrM,EAAGC,EAAG,CACvB,KAAK,WAAa,EAClB,KAAK,WAAa,EAClB,KAAK,YAAc,EACnB,KAAK,YAAc,EACnB,KAAK,WAAa,EAClB,KAAK,WAAa,EAClB,KAAK,YAAc,EACnB,KAAK,YAAc,CACrB,CATS1C,EAAA8O,EAAA,aAWTA,EAAU,UAAU,aAAe,UAAY,CAC7C,OAAO,KAAK,UACd,EAEAA,EAAU,UAAU,aAAe,SAAUuC,EAAK,CAChD,KAAK,WAAaA,CACpB,EAEAvC,EAAU,UAAU,aAAe,UAAY,CAC7C,OAAO,KAAK,UACd,EAEAA,EAAU,UAAU,aAAe,SAAUwC,EAAK,CAChD,KAAK,WAAaA,CACpB,EAEAxC,EAAU,UAAU,aAAe,UAAY,CAC7C,OAAO,KAAK,UACd,EAEAA,EAAU,UAAU,aAAe,SAAUyC,EAAK,CAChD,KAAK,WAAaA,CACpB,EAEAzC,EAAU,UAAU,aAAe,UAAY,CAC7C,OAAO,KAAK,UACd,EAEAA,EAAU,UAAU,aAAe,SAAU0C,EAAK,CAChD,KAAK,WAAaA,CACpB,EAIA1C,EAAU,UAAU,cAAgB,UAAY,CAC9C,OAAO,KAAK,WACd,EAEAA,EAAU,UAAU,cAAgB,SAAU2C,EAAK,CACjD,KAAK,YAAcA,CACrB,EAEA3C,EAAU,UAAU,cAAgB,UAAY,CAC9C,OAAO,KAAK,WACd,EAEAA,EAAU,UAAU,cAAgB,SAAU4C,EAAK,CACjD,KAAK,YAAcA,CACrB,EAEA5C,EAAU,UAAU,cAAgB,UAAY,CAC9C,OAAO,KAAK,WACd,EAEAA,EAAU,UAAU,cAAgB,SAAU6C,EAAK,CACjD,KAAK,YAAcA,CACrB,EAEA7C,EAAU,UAAU,cAAgB,UAAY,CAC9C,OAAO,KAAK,WACd,EAEAA,EAAU,UAAU,cAAgB,SAAU8C,EAAK,CACjD,KAAK,YAAcA,CACrB,EAEA9C,EAAU,UAAU,WAAa,SAAUrM,EAAG,CAC5C,IAAIoP,EAAU,EACVC,EAAY,KAAK,WACrB,OAAIA,GAAa,IACfD,EAAU,KAAK,aAAepP,EAAI,KAAK,YAAc,KAAK,YAAcqP,GAGnED,CACT,EAEA/C,EAAU,UAAU,WAAa,SAAUpM,EAAG,CAC5C,IAAIqP,EAAU,EACVC,EAAY,KAAK,WACrB,OAAIA,GAAa,IACfD,EAAU,KAAK,aAAerP,EAAI,KAAK,YAAc,KAAK,YAAcsP,GAGnED,CACT,EAEAjD,EAAU,UAAU,kBAAoB,SAAUrM,EAAG,CACnD,IAAIwP,EAAS,EACTC,EAAa,KAAK,YACtB,OAAIA,GAAc,IAChBD,EAAS,KAAK,YAAcxP,EAAI,KAAK,aAAe,KAAK,WAAayP,GAGjED,CACT,EAEAnD,EAAU,UAAU,kBAAoB,SAAUpM,EAAG,CACnD,IAAIyP,EAAS,EACTC,EAAa,KAAK,YACtB,OAAIA,GAAc,IAChBD,EAAS,KAAK,YAAczP,EAAI,KAAK,aAAe,KAAK,WAAa0P,GAEjED,CACT,EAEArD,EAAU,UAAU,sBAAwB,SAAUuD,EAAS,CAC7D,IAAIC,EAAW,IAAIzQ,EAAO,KAAK,kBAAkBwQ,EAAQ,CAAC,EAAG,KAAK,kBAAkBA,EAAQ,CAAC,CAAC,EAC9F,OAAOC,CACT,EAEAvS,EAAO,QAAU+O,CAEX,EAEC,SAAS/O,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASsO,EAAmBC,EAAK,CAAE,GAAI,MAAM,QAAQA,CAAG,EAAG,CAAE,QAASrL,EAAI,EAAGsL,EAAO,MAAMD,EAAI,MAAM,EAAGrL,EAAIqL,EAAI,OAAQrL,IAAOsL,EAAKtL,CAAC,EAAIqL,EAAIrL,CAAC,EAAK,OAAOsL,CAAM,KAAS,QAAO,MAAM,KAAKD,CAAG,CAAK,CAAzL5O,EAAA2O,EAAA,sBAET,IAAIK,EAAS3O,EAAoB,EAAE,EAC/B4H,EAAoB5H,EAAoB,CAAC,EACzCO,EAAkBP,EAAoB,CAAC,EACvCS,EAAYT,EAAoB,CAAC,EACjCU,EAAQV,EAAoB,CAAC,EAEjC,SAASkS,GAAW,CAClBvD,EAAO,KAAK,IAAI,EAEhB,KAAK,mCAAqC/G,EAAkB,gDAC5D,KAAK,gBAAkBA,EAAkB,oBACzC,KAAK,eAAiBA,EAAkB,wBACxC,KAAK,kBAAoBA,EAAkB,2BAC3C,KAAK,gBAAkBA,EAAkB,yBACzC,KAAK,wBAA0BA,EAAkB,kCACjD,KAAK,mBAAqBA,EAAkB,6BAC5C,KAAK,2BAA6BA,EAAkB,sCACpD,KAAK,6BAA+B,EAAMA,EAAkB,oBAAsB,IAClF,KAAK,cAAgBA,EAAkB,mCACvC,KAAK,qBAAuBA,EAAkB,mCAC9C,KAAK,kBAAoB,EACzB,KAAK,qBAAuB,EAC5B,KAAK,cAAgBA,EAAkB,cACzC,CAjBSjI,EAAAuS,EAAA,YAmBTA,EAAS,UAAY,OAAO,OAAOvD,EAAO,SAAS,EAEnD,QAAS5N,KAAQ4N,EACfuD,EAASnR,CAAI,EAAI4N,EAAO5N,CAAI,EAG9BmR,EAAS,UAAU,eAAiB,UAAY,CAC9CvD,EAAO,UAAU,eAAe,KAAK,KAAM,SAAS,EAEpD,KAAK,gBAAkB,EACvB,KAAK,sBAAwB,EAE7B,KAAK,iBAAmB/G,EAAkB,8CAE1C,KAAK,KAAO,CAAC,CACf,EAEAsK,EAAS,UAAU,qBAAuB,UAAY,CASpD,QARIxP,EACAyP,EACAvR,EACAC,EACAuR,EACAC,EAEAvD,EAAW,KAAK,gBAAgB,EAAE,YAAY,EACzC5L,EAAI,EAAGA,EAAI4L,EAAS,OAAQ5L,IACnCR,EAAOoM,EAAS5L,CAAC,EAEjBR,EAAK,YAAc,KAAK,gBAEpBA,EAAK,eACP9B,EAAS8B,EAAK,UAAU,EACxB7B,EAAS6B,EAAK,UAAU,EAExB0P,EAAoB1P,EAAK,eAAe,EAAE,iBAAiB,EAC3D2P,EAAoB3P,EAAK,eAAe,EAAE,iBAAiB,EAEvD,KAAK,qCACPA,EAAK,aAAe0P,EAAoBC,EAAoB,EAAI9R,EAAgB,kBAGlF4R,EAAWzP,EAAK,OAAO,EAAE,sBAAsB,EAE/CA,EAAK,aAAekF,EAAkB,oBAAsBA,EAAkB,oCAAsChH,EAAO,sBAAsB,EAAIC,EAAO,sBAAsB,EAAI,EAAIsR,GAGhM,EAEAD,EAAS,UAAU,mBAAqB,UAAY,CAElD,IAAI,EAAI,KAAK,YAAY,EAAE,OACvB,KAAK,aACH,EAAItK,EAAkB,8BACxB,KAAK,cAAgB,KAAK,IAAI,KAAK,cAAgBA,EAAkB,0BAA2B,KAAK,eAAiB,EAAIA,EAAkB,8BAAgCA,EAAkB,4BAA8BA,EAAkB,6BAA+B,KAAK,eAAiB,EAAIA,EAAkB,0BAA0B,GAErV,KAAK,oBAAsBA,EAAkB,oCAEzC,EAAIA,EAAkB,4BACxB,KAAK,cAAgB,KAAK,IAAIA,EAAkB,0BAA2B,GAAO,EAAIA,EAAkB,8BAAgCA,EAAkB,4BAA8BA,EAAkB,8BAAgC,EAAIA,EAAkB,0BAA0B,EAE1R,KAAK,cAAgB,EAEvB,KAAK,qBAAuB,KAAK,cACjC,KAAK,oBAAsBA,EAAkB,uBAG/C,KAAK,cAAgB,KAAK,IAAI,KAAK,YAAY,EAAE,OAAS,EAAG,KAAK,aAAa,EAE/E,KAAK,2BAA6B,KAAK,6BAA+B,KAAK,YAAY,EAAE,OAEzF,KAAK,eAAiB,KAAK,mBAAmB,CAChD,EAEAsK,EAAS,UAAU,iBAAmB,UAAY,CAIhD,QAHII,EAAS,KAAK,YAAY,EAC1B5P,EAEKQ,EAAI,EAAGA,EAAIoP,EAAO,OAAQpP,IACjCR,EAAO4P,EAAOpP,CAAC,EAEf,KAAK,gBAAgBR,EAAMA,EAAK,WAAW,CAE/C,EAEAwP,EAAS,UAAU,oBAAsB,UAAY,CACnD,IAAIK,EAAoB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GACxFC,EAA+B,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAEnGtP,EAAGuP,EACHC,EAAOC,EACPC,EAAS,KAAK,YAAY,EAC1BC,EAEJ,GAAI,KAAK,iBAQP,IAPI,KAAK,gBAAkBjL,EAAkB,+BAAiC,GAAK2K,GACjF,KAAK,WAAW,EAGlBM,EAAmB,IAAI,IAGlB3P,EAAI,EAAGA,EAAI0P,EAAO,OAAQ1P,IAC7BwP,EAAQE,EAAO1P,CAAC,EAChB,KAAK,+BAA+BwP,EAAOG,EAAkBN,EAAmBC,CAA4B,EAC5GK,EAAiB,IAAIH,CAAK,MAG5B,KAAKxP,EAAI,EAAGA,EAAI0P,EAAO,OAAQ1P,IAG7B,IAFAwP,EAAQE,EAAO1P,CAAC,EAEXuP,EAAIvP,EAAI,EAAGuP,EAAIG,EAAO,OAAQH,IACjCE,EAAQC,EAAOH,CAAC,EAGZC,EAAM,SAAS,GAAKC,EAAM,SAAS,GAIvC,KAAK,mBAAmBD,EAAOC,CAAK,CAI5C,EAEAT,EAAS,UAAU,wBAA0B,UAAY,CAIvD,QAHIlR,EACA4R,EAAS,KAAK,8BAA8B,EAEvC1P,EAAI,EAAGA,EAAI0P,EAAO,OAAQ1P,IACjClC,EAAO4R,EAAO1P,CAAC,EACf,KAAK,uBAAuBlC,CAAI,CAEpC,EAEAkR,EAAS,UAAU,UAAY,UAAY,CAIzC,QAHIU,EAAS,KAAK,YAAY,EAC1B5R,EAEKkC,EAAI,EAAGA,EAAI0P,EAAO,OAAQ1P,IACjClC,EAAO4R,EAAO1P,CAAC,EACflC,EAAK,KAAK,CAEd,EAEAkR,EAAS,UAAU,gBAAkB,SAAUxP,EAAMoQ,EAAa,CAChE,IAAIpO,EAAahC,EAAK,UAAU,EAC5BiC,EAAajC,EAAK,UAAU,EAE5BqQ,EACAC,EACAC,EACAC,EAGJ,GAAI,KAAK,sBAAwBxO,EAAW,SAAS,GAAK,MAAQC,EAAW,SAAS,GAAK,KACzFjC,EAAK,mBAAmB,UAExBA,EAAK,aAAa,EAEdA,EAAK,4BACP,OAIJqQ,EAASrQ,EAAK,UAAU,EAEpBqQ,GAAU,IAGdC,EAAc,KAAK,gBAAkBD,EAASD,GAG9CG,EAAeD,GAAetQ,EAAK,QAAUqQ,GAC7CG,EAAeF,GAAetQ,EAAK,QAAUqQ,GAG7CrO,EAAW,cAAgBuO,EAC3BvO,EAAW,cAAgBwO,EAC3BvO,EAAW,cAAgBsO,EAC3BtO,EAAW,cAAgBuO,EAC7B,EAEAhB,EAAS,UAAU,mBAAqB,SAAUQ,EAAOC,EAAO,CAC9D,IAAI9K,EAAQ6K,EAAM,QAAQ,EACtB5K,EAAQ6K,EAAM,QAAQ,EACtB5K,EAAgB,IAAI,MAAM,CAAC,EAC3BoL,EAAa,IAAI,MAAM,CAAC,EACxBC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEJ,GAAI7L,EAAM,WAAWC,CAAK,EACxB,CAEErH,EAAU,qBAAqBoH,EAAOC,EAAOC,EAAeH,EAAkB,oBAAsB,CAAG,EAEvG6L,EAAkB,EAAI1L,EAAc,CAAC,EACrC2L,EAAkB,EAAI3L,EAAc,CAAC,EAErC,IAAI4L,EAAmBjB,EAAM,aAAeC,EAAM,cAAgBD,EAAM,aAAeC,EAAM,cAG7FD,EAAM,iBAAmBiB,EAAmBF,EAC5Cf,EAAM,iBAAmBiB,EAAmBD,EAC5Cf,EAAM,iBAAmBgB,EAAmBF,EAC5Cd,EAAM,iBAAmBgB,EAAmBD,CAC9C,MAIM,KAAK,sBAAwBhB,EAAM,SAAS,GAAK,MAAQC,EAAM,SAAS,GAAK,MAE7ES,EAAYtL,EAAM,WAAW,EAAID,EAAM,WAAW,EAClDwL,EAAYvL,EAAM,WAAW,EAAID,EAAM,WAAW,IAGlDpH,EAAU,gBAAgBoH,EAAOC,EAAOqL,CAAU,EAElDC,EAAYD,EAAW,CAAC,EAAIA,EAAW,CAAC,EACxCE,EAAYF,EAAW,CAAC,EAAIA,EAAW,CAAC,GAIxC,KAAK,IAAIC,CAAS,EAAIxL,EAAkB,qBAC1CwL,EAAY1S,EAAM,KAAK0S,CAAS,EAAIxL,EAAkB,oBAGpD,KAAK,IAAIyL,CAAS,EAAIzL,EAAkB,qBAC1CyL,EAAY3S,EAAM,KAAK2S,CAAS,EAAIzL,EAAkB,oBAGxD0L,EAAkBF,EAAYA,EAAYC,EAAYA,EACtDE,EAAW,KAAK,KAAKD,CAAe,EAEpCE,EAAiB,KAAK,kBAAoBd,EAAM,aAAeC,EAAM,aAAeW,EAGpFG,EAAkBD,EAAiBJ,EAAYG,EAC/CG,EAAkBF,EAAiBH,EAAYE,EAG/Cb,EAAM,iBAAmBe,EACzBf,EAAM,iBAAmBgB,EACzBf,EAAM,iBAAmBc,EACzBd,EAAM,iBAAmBe,CAE/B,EAEAxB,EAAS,UAAU,uBAAyB,SAAUlR,EAAM,CAC1D,IAAIqG,EACAuM,EACAC,EACAT,EACAC,EACAS,EACAC,EACAC,EACJ3M,EAAarG,EAAK,SAAS,EAE3B4S,GAAgBvM,EAAW,SAAS,EAAIA,EAAW,QAAQ,GAAK,EAChEwM,GAAgBxM,EAAW,OAAO,EAAIA,EAAW,UAAU,GAAK,EAChE+L,EAAYpS,EAAK,WAAW,EAAI4S,EAChCP,EAAYrS,EAAK,WAAW,EAAI6S,EAChCC,EAAe,KAAK,IAAIV,CAAS,EAAIpS,EAAK,SAAS,EAAI,EACvD+S,EAAe,KAAK,IAAIV,CAAS,EAAIrS,EAAK,UAAU,EAAI,EAEpDA,EAAK,SAAS,GAAK,KAAK,aAAa,QAAQ,GAE7CgT,EAAgB3M,EAAW,iBAAiB,EAAI,KAAK,oBAEjDyM,EAAeE,GAAiBD,EAAeC,KACjDhT,EAAK,kBAAoB,CAAC,KAAK,gBAAkBoS,EACjDpS,EAAK,kBAAoB,CAAC,KAAK,gBAAkBqS,KAInDW,EAAgB3M,EAAW,iBAAiB,EAAI,KAAK,4BAEjDyM,EAAeE,GAAiBD,EAAeC,KACjDhT,EAAK,kBAAoB,CAAC,KAAK,gBAAkBoS,EAAY,KAAK,wBAClEpS,EAAK,kBAAoB,CAAC,KAAK,gBAAkBqS,EAAY,KAAK,yBAG1E,EAEAnB,EAAS,UAAU,YAAc,UAAY,CAC3C,IAAI+B,EACAC,EAAa,GAEjB,OAAI,KAAK,gBAAkB,KAAK,cAAgB,IAC9CA,EAAa,KAAK,IAAI,KAAK,kBAAoB,KAAK,oBAAoB,EAAI,GAG9ED,EAAY,KAAK,kBAAoB,KAAK,2BAE1C,KAAK,qBAAuB,KAAK,kBAE1BA,GAAaC,CACtB,EAEAhC,EAAS,UAAU,QAAU,UAAY,CACnC,KAAK,uBAAyB,CAAC,KAAK,cAClC,KAAK,uBAAyB,KAAK,iBACrC,KAAK,OAAO,EACZ,KAAK,sBAAwB,GAE7B,KAAK,wBAGX,EAGAA,EAAS,UAAU,4BAA8B,UAAY,CAI3D,QAHIlR,EACAkO,EAAW,KAAK,aAAa,YAAY,EAEpChM,EAAI,EAAGA,EAAIgM,EAAS,OAAQhM,IACnClC,EAAOkO,EAAShM,CAAC,EACjBlC,EAAK,aAAeA,EAAK,gBAAgB,CAE7C,EAMAkR,EAAS,UAAU,SAAW,SAAUjR,EAAO,CAE7C,IAAIkT,EAAQ,EACRC,EAAQ,EAEZD,EAAQ,SAAS,KAAK,MAAMlT,EAAM,SAAS,EAAIA,EAAM,QAAQ,GAAK,KAAK,cAAc,CAAC,EACtFmT,EAAQ,SAAS,KAAK,MAAMnT,EAAM,UAAU,EAAIA,EAAM,OAAO,GAAK,KAAK,cAAc,CAAC,EAItF,QAFIoT,EAAO,IAAI,MAAMF,CAAK,EAEjBjR,EAAI,EAAGA,EAAIiR,EAAOjR,IACzBmR,EAAKnR,CAAC,EAAI,IAAI,MAAMkR,CAAK,EAG3B,QAASlR,EAAI,EAAGA,EAAIiR,EAAOjR,IACzB,QAASuP,EAAI,EAAGA,EAAI2B,EAAO3B,IACzB4B,EAAKnR,CAAC,EAAEuP,CAAC,EAAI,IAAI,MAIrB,OAAO4B,CACT,EAEAnC,EAAS,UAAU,cAAgB,SAAUtE,EAAGhK,EAAMC,EAAK,CAEzD,IAAIyQ,EAAS,EACTC,EAAU,EACVC,EAAS,EACTC,EAAU,EAEdH,EAAS,SAAS,KAAK,OAAO1G,EAAE,QAAQ,EAAE,EAAIhK,GAAQ,KAAK,cAAc,CAAC,EAC1E2Q,EAAU,SAAS,KAAK,OAAO3G,EAAE,QAAQ,EAAE,MAAQA,EAAE,QAAQ,EAAE,EAAIhK,GAAQ,KAAK,cAAc,CAAC,EAC/F4Q,EAAS,SAAS,KAAK,OAAO5G,EAAE,QAAQ,EAAE,EAAI/J,GAAO,KAAK,cAAc,CAAC,EACzE4Q,EAAU,SAAS,KAAK,OAAO7G,EAAE,QAAQ,EAAE,OAASA,EAAE,QAAQ,EAAE,EAAI/J,GAAO,KAAK,cAAc,CAAC,EAE/F,QAASX,EAAIoR,EAAQpR,GAAKqR,EAASrR,IACjC,QAASuP,EAAI+B,EAAQ/B,GAAKgC,EAAShC,IACjC,KAAK,KAAKvP,CAAC,EAAEuP,CAAC,EAAE,KAAK7E,CAAC,EACtBA,EAAE,mBAAmB0G,EAAQC,EAASC,EAAQC,CAAO,CAG3D,EAEAvC,EAAS,UAAU,WAAa,UAAY,CAC1C,IAAIhP,EACAwP,EACAE,EAAS,KAAK,YAAY,EAK9B,IAHA,KAAK,KAAO,KAAK,SAAS,KAAK,aAAa,QAAQ,CAAC,EAGhD1P,EAAI,EAAGA,EAAI0P,EAAO,OAAQ1P,IAC7BwP,EAAQE,EAAO1P,CAAC,EAChB,KAAK,cAAcwP,EAAO,KAAK,aAAa,QAAQ,EAAE,QAAQ,EAAG,KAAK,aAAa,QAAQ,EAAE,OAAO,CAAC,CAEzG,EAEAR,EAAS,UAAU,+BAAiC,SAAUQ,EAAOG,EAAkBN,EAAmBC,EAA8B,CAEtI,GAAI,KAAK,gBAAkB5K,EAAkB,+BAAiC,GAAK2K,GAAqBC,EAA8B,CACpI,IAAIkC,EAAc,IAAI,IACtBhC,EAAM,YAAc,IAAI,MAIxB,QAHIC,EACA0B,EAAO,KAAK,KAEPnR,EAAIwP,EAAM,OAAS,EAAGxP,EAAIwP,EAAM,QAAU,EAAGxP,IACpD,QAASuP,EAAIC,EAAM,OAAS,EAAGD,EAAIC,EAAM,QAAU,EAAGD,IACpD,GAAI,EAAEvP,EAAI,GAAKuP,EAAI,GAAKvP,GAAKmR,EAAK,QAAU5B,GAAK4B,EAAK,CAAC,EAAE,SACvD,QAAS1E,EAAI,EAAGA,EAAI0E,EAAKnR,CAAC,EAAEuP,CAAC,EAAE,OAAQ9C,IAKrC,GAJAgD,EAAQ0B,EAAKnR,CAAC,EAAEuP,CAAC,EAAE9C,CAAC,EAIhB,EAAA+C,EAAM,SAAS,GAAKC,EAAM,SAAS,GAAKD,GAASC,IAMjD,CAACE,EAAiB,IAAIF,CAAK,GAAK,CAAC+B,EAAY,IAAI/B,CAAK,EAAG,CAC3D,IAAIS,EAAY,KAAK,IAAIV,EAAM,WAAW,EAAIC,EAAM,WAAW,CAAC,GAAKD,EAAM,SAAS,EAAI,EAAIC,EAAM,SAAS,EAAI,GAC3GU,EAAY,KAAK,IAAIX,EAAM,WAAW,EAAIC,EAAM,WAAW,CAAC,GAAKD,EAAM,UAAU,EAAI,EAAIC,EAAM,UAAU,EAAI,GAI7GS,GAAa,KAAK,gBAAkBC,GAAa,KAAK,gBAExDqB,EAAY,IAAI/B,CAAK,CAEzB,EAMRD,EAAM,YAAc,CAAC,EAAE,OAAOpE,EAAmBoG,CAAW,CAAC,CAC/D,CACA,IAAKxR,EAAI,EAAGA,EAAIwP,EAAM,YAAY,OAAQxP,IACxC,KAAK,mBAAmBwP,EAAOA,EAAM,YAAYxP,CAAC,CAAC,CAEvD,EAEAgP,EAAS,UAAU,mBAAqB,UAAY,CAClD,MAAO,EACT,EAEAxS,EAAO,QAAUwS,CAEX,EAEC,SAASxS,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIW,EAAQX,EAAoB,CAAC,EAC7B4H,EAAoB5H,EAAoB,CAAC,EAE7C,SAAS2U,EAAa/T,EAAQC,EAAQC,EAAO,CAC3CH,EAAM,KAAK,KAAMC,EAAQC,EAAQC,CAAK,EACtC,KAAK,YAAc8G,EAAkB,mBACvC,CAHSjI,EAAAgV,EAAA,gBAKTA,EAAa,UAAY,OAAO,OAAOhU,EAAM,SAAS,EAEtD,QAASI,KAAQJ,EACfgU,EAAa5T,CAAI,EAAIJ,EAAMI,CAAI,EAGjCrB,EAAO,QAAUiV,CAEX,EAEC,SAASjV,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIyB,EAAQzB,EAAoB,CAAC,EAEjC,SAAS4U,EAAalT,EAAIC,EAAKC,EAAMC,EAAO,CAE1CJ,EAAM,KAAK,KAAMC,EAAIC,EAAKC,EAAMC,CAAK,EAErC,KAAK,aAAe,EACpB,KAAK,aAAe,EACpB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,EACvB,KAAK,kBAAoB,EACzB,KAAK,kBAAoB,EAEzB,KAAK,cAAgB,EACrB,KAAK,cAAgB,EAGrB,KAAK,OAAS,EACd,KAAK,QAAU,EACf,KAAK,OAAS,EACd,KAAK,QAAU,EAGf,KAAK,YAAc,CAAC,CACtB,CAtBSlC,EAAAiV,EAAA,gBAwBTA,EAAa,UAAY,OAAO,OAAOnT,EAAM,SAAS,EAEtD,QAASV,KAAQU,EACfmT,EAAa7T,CAAI,EAAIU,EAAMV,CAAI,EAGjC6T,EAAa,UAAU,mBAAqB,SAAUC,EAASC,EAAUC,EAASC,EAAU,CAC1F,KAAK,OAASH,EACd,KAAK,QAAUC,EACf,KAAK,OAASC,EACd,KAAK,QAAUC,CACjB,EAEAtV,EAAO,QAAUkV,CAEX,EAEC,SAASlV,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAASiV,EAAWnT,EAAOC,EAAQ,CACjC,KAAK,MAAQ,EACb,KAAK,OAAS,EACVD,IAAU,MAAQC,IAAW,OAC/B,KAAK,OAASA,EACd,KAAK,MAAQD,EAEjB,CAPSnC,EAAAsV,EAAA,cASTA,EAAW,UAAU,SAAW,UAAY,CAC1C,OAAO,KAAK,KACd,EAEAA,EAAW,UAAU,SAAW,SAAUnT,EAAO,CAC/C,KAAK,MAAQA,CACf,EAEAmT,EAAW,UAAU,UAAY,UAAY,CAC3C,OAAO,KAAK,MACd,EAEAA,EAAW,UAAU,UAAY,SAAUlT,EAAQ,CACjD,KAAK,OAASA,CAChB,EAEArC,EAAO,QAAUuV,CAEX,EAEC,SAASvV,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIkO,EAAoBlO,EAAoB,EAAE,EAE9C,SAASkV,GAAU,CACjB,KAAK,IAAM,CAAC,EACZ,KAAK,KAAO,CAAC,CACf,CAHSvV,EAAAuV,EAAA,WAKTA,EAAQ,UAAU,IAAM,SAAUC,EAAKjV,EAAO,CAC5C,IAAIkV,EAAQlH,EAAkB,SAASiH,CAAG,EACrC,KAAK,SAASC,CAAK,IACtB,KAAK,IAAIA,CAAK,EAAIlV,EAClB,KAAK,KAAK,KAAKiV,CAAG,EAEtB,EAEAD,EAAQ,UAAU,SAAW,SAAUC,EAAK,CAC1C,IAAIC,EAAQlH,EAAkB,SAASiH,CAAG,EAC1C,OAAO,KAAK,IAAIA,CAAG,GAAK,IAC1B,EAEAD,EAAQ,UAAU,IAAM,SAAUC,EAAK,CACrC,IAAIC,EAAQlH,EAAkB,SAASiH,CAAG,EAC1C,OAAO,KAAK,IAAIC,CAAK,CACvB,EAEAF,EAAQ,UAAU,OAAS,UAAY,CACrC,OAAO,KAAK,IACd,EAEAxV,EAAO,QAAUwV,CAEX,EAEC,SAASxV,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAIkO,EAAoBlO,EAAoB,EAAE,EAE9C,SAASqV,GAAU,CACjB,KAAK,IAAM,CAAC,CACd,CAFS1V,EAAA0V,EAAA,WAKTA,EAAQ,UAAU,IAAM,SAAUvQ,EAAK,CACrC,IAAIsQ,EAAQlH,EAAkB,SAASpJ,CAAG,EACrC,KAAK,SAASsQ,CAAK,IAAG,KAAK,IAAIA,CAAK,EAAItQ,EAC/C,EAEAuQ,EAAQ,UAAU,OAAS,SAAUvQ,EAAK,CACxC,OAAO,KAAK,IAAIoJ,EAAkB,SAASpJ,CAAG,CAAC,CACjD,EAEAuQ,EAAQ,UAAU,MAAQ,UAAY,CACpC,KAAK,IAAM,CAAC,CACd,EAEAA,EAAQ,UAAU,SAAW,SAAUvQ,EAAK,CAC1C,OAAO,KAAK,IAAIoJ,EAAkB,SAASpJ,CAAG,CAAC,GAAKA,CACtD,EAEAuQ,EAAQ,UAAU,QAAU,UAAY,CACtC,OAAO,KAAK,KAAK,IAAM,CACzB,EAEAA,EAAQ,UAAU,KAAO,UAAY,CACnC,OAAO,OAAO,KAAK,KAAK,GAAG,EAAE,MAC/B,EAGAA,EAAQ,UAAU,SAAW,SAAU7H,EAAM,CAG3C,QAFI8H,EAAO,OAAO,KAAK,KAAK,GAAG,EAC3BvC,EAASuC,EAAK,OACTpS,EAAI,EAAGA,EAAI6P,EAAQ7P,IAC1BsK,EAAK,KAAK,KAAK,IAAI8H,EAAKpS,CAAC,CAAC,CAAC,CAE/B,EAEAmS,EAAQ,UAAU,KAAO,UAAY,CACnC,OAAO,OAAO,KAAK,KAAK,GAAG,EAAE,MAC/B,EAEAA,EAAQ,UAAU,OAAS,SAAU7H,EAAM,CAEzC,QADIxI,EAAIwI,EAAK,OACJtK,EAAI,EAAGA,EAAI8B,EAAG9B,IAAK,CAC1B,IAAI0K,EAAIJ,EAAKtK,CAAC,EACd,KAAK,IAAI0K,CAAC,CACZ,CACF,EAEAlO,EAAO,QAAU2V,CAEX,EAEC,SAAS3V,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAI2M,EAAe,UAAY,CAAE,SAASC,EAAiB/L,EAAQgM,EAAO,CAAE,QAAS3J,EAAI,EAAGA,EAAI2J,EAAM,OAAQ3J,IAAK,CAAE,IAAI4J,EAAaD,EAAM3J,CAAC,EAAG4J,EAAW,WAAaA,EAAW,YAAc,GAAOA,EAAW,aAAe,GAAU,UAAWA,IAAYA,EAAW,SAAW,IAAM,OAAO,eAAejM,EAAQiM,EAAW,IAAKA,CAAU,CAAG,CAAE,CAAlT,OAAAnN,EAAAiN,EAAA,oBAA2T,SAAUG,EAAaC,EAAYC,EAAa,CAAE,OAAID,GAAYJ,EAAiBG,EAAY,UAAWC,CAAU,EAAOC,GAAaL,EAAiBG,EAAaE,CAAW,EAAUF,CAAa,CAAG,EAAE,EAEljB,SAASG,EAAgBC,EAAUJ,EAAa,CAAE,GAAI,EAAEI,aAAoBJ,GAAgB,MAAM,IAAI,UAAU,mCAAmC,CAAK,CAA/IpN,EAAAuN,EAAA,mBAST,IAAI9I,EAAapE,EAAoB,EAAE,EAEnCuV,EAAY,UAAY,CACxB,SAASA,EAAUC,EAAGC,EAAiB,CACnCvI,EAAgB,KAAMqI,CAAS,GAE3BE,IAAoB,MAAQA,IAAoB,UAAW,KAAK,gBAAkB,KAAK,yBAE3F,IAAI1C,EAAS,OACTyC,aAAapR,EAAY2O,EAASyC,EAAE,KAAK,EAAOzC,EAASyC,EAAE,OAE/D,KAAK,WAAWA,EAAG,EAAGzC,EAAS,CAAC,CACpC,CATS,OAAApT,EAAA4V,EAAA,aAWT5I,EAAa4I,EAAW,CAAC,CACrB,IAAK,aACL,MAAO5V,EAAA,SAAoB6V,EAAGnJ,EAAGE,EAAG,CAChC,GAAIF,EAAIE,EAAG,CACP,IAAID,EAAI,KAAK,WAAWkJ,EAAGnJ,EAAGE,CAAC,EAC/B,KAAK,WAAWiJ,EAAGnJ,EAAGC,CAAC,EACvB,KAAK,WAAWkJ,EAAGlJ,EAAI,EAAGC,CAAC,CAC/B,CACJ,EANO,aAOX,EAAG,CACC,IAAK,aACL,MAAO5M,EAAA,SAAoB6V,EAAGnJ,EAAGE,EAAG,CAIhC,QAHInK,EAAI,KAAK,KAAKoT,EAAGnJ,CAAC,EAClBnJ,EAAImJ,EACJoG,EAAIlG,IACK,CACT,KAAO,KAAK,gBAAgBnK,EAAG,KAAK,KAAKoT,EAAG/C,CAAC,CAAC,GAC1CA,IACH,KAAO,KAAK,gBAAgB,KAAK,KAAK+C,EAAGtS,CAAC,EAAGd,CAAC,GAC3Cc,IACH,GAAIA,EAAIuP,EACL,KAAK,MAAM+C,EAAGtS,EAAGuP,CAAC,EAClBvP,IACAuP,QACG,QAAOA,CAClB,CACJ,EAfO,aAgBX,EAAG,CACC,IAAK,OACL,MAAO9S,EAAA,SAAcU,EAAQ4E,EAAO,CAChC,OAAI5E,aAAkB+D,EAAmB/D,EAAO,cAAc4E,CAAK,EAAc5E,EAAO4E,CAAK,CACjG,EAFO,OAGX,EAAG,CACC,IAAK,OACL,MAAOtF,EAAA,SAAcU,EAAQ4E,EAAO/E,EAAO,CACnCG,aAAkB+D,EAAY/D,EAAO,cAAc4E,EAAO/E,CAAK,EAAOG,EAAO4E,CAAK,EAAI/E,CAC9F,EAFO,OAGX,EAAG,CACC,IAAK,QACL,MAAOP,EAAA,SAAe6V,EAAGtS,EAAGuP,EAAG,CAC3B,IAAIlD,EAAO,KAAK,KAAKiG,EAAGtS,CAAC,EACzB,KAAK,KAAKsS,EAAGtS,EAAG,KAAK,KAAKsS,EAAG/C,CAAC,CAAC,EAC/B,KAAK,KAAK+C,EAAG/C,EAAGlD,CAAI,CACxB,EAJO,QAKX,EAAG,CACC,IAAK,0BACL,MAAO5P,EAAA,SAAiC,EAAGuM,EAAG,CAC1C,OAAOA,EAAI,CACf,EAFO,0BAGX,CAAC,CAAC,EAEKqJ,CACX,EAAE,EAEF7V,EAAO,QAAU6V,CAEX,EAEC,SAAS7V,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAI2M,EAAe,UAAY,CAAE,SAASC,EAAiB/L,EAAQgM,EAAO,CAAE,QAAS3J,EAAI,EAAGA,EAAI2J,EAAM,OAAQ3J,IAAK,CAAE,IAAI4J,EAAaD,EAAM3J,CAAC,EAAG4J,EAAW,WAAaA,EAAW,YAAc,GAAOA,EAAW,aAAe,GAAU,UAAWA,IAAYA,EAAW,SAAW,IAAM,OAAO,eAAejM,EAAQiM,EAAW,IAAKA,CAAU,CAAG,CAAE,CAAlT,OAAAnN,EAAAiN,EAAA,oBAA2T,SAAUG,EAAaC,EAAYC,EAAa,CAAE,OAAID,GAAYJ,EAAiBG,EAAY,UAAWC,CAAU,EAAOC,GAAaL,EAAiBG,EAAaE,CAAW,EAAUF,CAAa,CAAG,EAAE,EAEljB,SAASG,EAAgBC,EAAUJ,EAAa,CAAE,GAAI,EAAEI,aAAoBJ,GAAgB,MAAM,IAAI,UAAU,mCAAmC,CAAK,CAA/IpN,EAAAuN,EAAA,mBAYT,IAAIwI,EAAkB,UAAY,CAC9B,SAASA,EAAgBC,EAAWC,EAAW,CAC3C,IAAIC,EAAc,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,EAClFC,EAAmB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GACvFC,EAAc,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAEtF7I,EAAgB,KAAMwI,CAAe,EAErC,KAAK,UAAYC,EACjB,KAAK,UAAYC,EACjB,KAAK,YAAcC,EACnB,KAAK,iBAAmBC,EACxB,KAAK,YAAcC,EAGnB,KAAK,KAAOJ,EAAU,OAAS,EAC/B,KAAK,KAAOC,EAAU,OAAS,EAG/B,KAAK,KAAO,IAAI,MAAM,KAAK,IAAI,EAC/B,QAAS1S,EAAI,EAAGA,EAAI,KAAK,KAAMA,IAAK,CAChC,KAAK,KAAKA,CAAC,EAAI,IAAI,MAAM,KAAK,IAAI,EAElC,QAASuP,EAAI,EAAGA,EAAI,KAAK,KAAMA,IAC3B,KAAK,KAAKvP,CAAC,EAAEuP,CAAC,EAAI,CAE1B,CAGA,KAAK,cAAgB,IAAI,MAAM,KAAK,IAAI,EACxC,QAASuD,EAAK,EAAGA,EAAK,KAAK,KAAMA,IAAM,CACnC,KAAK,cAAcA,CAAE,EAAI,IAAI,MAAM,KAAK,IAAI,EAE5C,QAASC,EAAK,EAAGA,EAAK,KAAK,KAAMA,IAC7B,KAAK,cAAcD,CAAE,EAAEC,CAAE,EAAI,CAAC,KAAM,KAAM,IAAI,CAEtD,CAGA,KAAK,WAAa,CAAC,EAGnB,KAAK,MAAQ,GAGb,KAAK,aAAa,CACtB,CA7CS,OAAAtW,EAAA+V,EAAA,mBA+CT/I,EAAa+I,EAAiB,CAAC,CAC3B,IAAK,WACL,MAAO/V,EAAA,UAAoB,CACvB,OAAO,KAAK,KAChB,EAFO,WAGX,EAAG,CACC,IAAK,gBACL,MAAOA,EAAA,UAAyB,CAC5B,OAAO,KAAK,UAChB,EAFO,gBAMX,EAAG,CACC,IAAK,eACL,MAAOA,EAAA,UAAwB,CAE3B,QAAS8S,EAAI,EAAGA,EAAI,KAAK,KAAMA,IAC3B,KAAK,KAAK,CAAC,EAAEA,CAAC,EAAI,KAAK,KAAK,CAAC,EAAEA,EAAI,CAAC,EAAI,KAAK,YAC7C,KAAK,cAAc,CAAC,EAAEA,CAAC,EAAI,CAAC,GAAO,GAAO,EAAI,EAIlD,QAASvP,EAAI,EAAGA,EAAI,KAAK,KAAMA,IAC3B,KAAK,KAAKA,CAAC,EAAE,CAAC,EAAI,KAAK,KAAKA,EAAI,CAAC,EAAE,CAAC,EAAI,KAAK,YAC7C,KAAK,cAAcA,CAAC,EAAE,CAAC,EAAI,CAAC,GAAO,GAAM,EAAK,EAIlD,QAASgT,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAC/B,QAASC,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAAO,CAEtC,IAAIC,EAAO,OACP,KAAK,UAAUF,EAAM,CAAC,IAAM,KAAK,UAAUC,EAAM,CAAC,EAAGC,EAAO,KAAK,KAAKF,EAAM,CAAC,EAAEC,EAAM,CAAC,EAAI,KAAK,YAAiBC,EAAO,KAAK,KAAKF,EAAM,CAAC,EAAEC,EAAM,CAAC,EAAI,KAAK,iBAE9J,IAAIE,EAAK,KAAK,KAAKH,EAAM,CAAC,EAAEC,CAAG,EAAI,KAAK,YACpCvS,EAAO,KAAK,KAAKsS,CAAG,EAAEC,EAAM,CAAC,EAAI,KAAK,YAGtCG,EAAQ,CAACF,EAAMC,EAAIzS,CAAI,EACvB2S,EAAU,KAAK,mBAAmBD,CAAK,EAG3C,KAAK,KAAKJ,CAAG,EAAEC,CAAG,EAAIG,EAAMC,EAAQ,CAAC,CAAC,EACtC,KAAK,cAAcL,CAAG,EAAEC,CAAG,EAAI,CAACI,EAAQ,SAAS,CAAC,EAAGA,EAAQ,SAAS,CAAC,EAAGA,EAAQ,SAAS,CAAC,CAAC,CACjG,CAIJ,KAAK,MAAQ,KAAK,KAAK,KAAK,KAAO,CAAC,EAAE,KAAK,KAAO,CAAC,CACvD,EAnCO,eAuCX,EAAG,CACC,IAAK,qBACL,MAAO5W,EAAA,UAA8B,CACjC,IAAI6W,EAAsB,CAAC,EAO3B,IALAA,EAAoB,KAAK,CAAE,IAAK,CAAC,KAAK,UAAU,OAAQ,KAAK,UAAU,MAAM,EACzE,KAAM,GACN,KAAM,EACV,CAAC,EAEMA,EAAoB,CAAC,GAAG,CAC3B,IAAIzI,EAAUyI,EAAoB,CAAC,EAC/BvO,EAAa,KAAK,cAAc8F,EAAQ,IAAI,CAAC,CAAC,EAAEA,EAAQ,IAAI,CAAC,CAAC,EAE9D9F,EAAW,CAAC,GACZuO,EAAoB,KAAK,CAAE,IAAK,CAACzI,EAAQ,IAAI,CAAC,EAAI,EAAGA,EAAQ,IAAI,CAAC,EAAI,CAAC,EACnE,KAAM,KAAK,UAAUA,EAAQ,IAAI,CAAC,EAAI,CAAC,EAAIA,EAAQ,KACnD,KAAM,KAAK,UAAUA,EAAQ,IAAI,CAAC,EAAI,CAAC,EAAIA,EAAQ,IACvD,CAAC,EAED9F,EAAW,CAAC,GACZuO,EAAoB,KAAK,CAAE,IAAK,CAACzI,EAAQ,IAAI,CAAC,EAAI,EAAGA,EAAQ,IAAI,CAAC,CAAC,EAC/D,KAAM,KAAK,UAAUA,EAAQ,IAAI,CAAC,EAAI,CAAC,EAAIA,EAAQ,KACnD,KAAM,IAAMA,EAAQ,IACxB,CAAC,EAED9F,EAAW,CAAC,GACZuO,EAAoB,KAAK,CAAE,IAAK,CAACzI,EAAQ,IAAI,CAAC,EAAGA,EAAQ,IAAI,CAAC,EAAI,CAAC,EAC/D,KAAM,IAAMA,EAAQ,KACpB,KAAM,KAAK,UAAUA,EAAQ,IAAI,CAAC,EAAI,CAAC,EAAIA,EAAQ,IACvD,CAAC,EAGDA,EAAQ,IAAI,CAAC,IAAM,GAAKA,EAAQ,IAAI,CAAC,IAAM,GAAG,KAAK,WAAW,KAAK,CAAE,UAAWA,EAAQ,KACxF,UAAWA,EAAQ,IACvB,CAAC,EAEDyI,EAAoB,MAAM,CAC9B,CAEA,OAAO,KAAK,UAChB,EAvCO,qBA2CX,EAAG,CACC,IAAK,gBACL,MAAO7W,EAAA,SAAuB4O,EAAKV,EAAK,CAGpC,QAFI4I,EAAU,CAAC,EACXvT,EAAI,IACAA,EAAIqL,EAAI,QAAQV,EAAK3K,EAAI,CAAC,KAAO,IACrCuT,EAAQ,KAAKvT,CAAC,EAElB,OAAOuT,CACX,EAPO,gBAQX,EAAG,CACC,IAAK,qBACL,MAAO9W,EAAA,SAA4B+W,EAAO,CACtC,OAAO,KAAK,cAAcA,EAAO,KAAK,IAAI,MAAM,KAAMA,CAAK,CAAC,CAChE,EAFO,qBAGX,CAAC,CAAC,EAEKhB,CACX,EAAE,EAEFhW,EAAO,QAAUgW,CAEX,EAEC,SAAShW,EAAQD,EAASO,EAAqB,CAEtD,aAGA,IAAI2W,EAAahX,EAAA,UAAsB,CAEvC,EAFiB,cAIjBgX,EAAW,SAAW3W,EAAoB,EAAE,EAC5C2W,EAAW,kBAAoB3W,EAAoB,CAAC,EACpD2W,EAAW,aAAe3W,EAAoB,EAAE,EAChD2W,EAAW,aAAe3W,EAAoB,EAAE,EAChD2W,EAAW,WAAa3W,EAAoB,EAAE,EAC9C2W,EAAW,QAAU3W,EAAoB,EAAE,EAC3C2W,EAAW,QAAU3W,EAAoB,EAAE,EAC3C2W,EAAW,UAAY3W,EAAoB,CAAC,EAC5C2W,EAAW,MAAQ3W,EAAoB,CAAC,EACxC2W,EAAW,QAAU3W,EAAoB,EAAE,EAC3C2W,EAAW,MAAQ3W,EAAoB,EAAE,EACzC2W,EAAW,OAAS3W,EAAoB,CAAC,EACzC2W,EAAW,WAAa3W,EAAoB,EAAE,EAC9C2W,EAAW,WAAa3W,EAAoB,EAAE,EAC9C2W,EAAW,UAAY3W,EAAoB,EAAE,EAC7C2W,EAAW,kBAAoB3W,EAAoB,EAAE,EACrD2W,EAAW,UAAY3W,EAAoB,EAAE,EAC7C2W,EAAW,WAAa3W,EAAoB,EAAE,EAC9C2W,EAAW,aAAe3W,EAAoB,CAAC,EAC/C2W,EAAW,OAAS3W,EAAoB,CAAC,EACzC2W,EAAW,MAAQ3W,EAAoB,CAAC,EACxC2W,EAAW,cAAgB3W,EAAoB,CAAC,EAChD2W,EAAW,MAAQ3W,EAAoB,CAAC,EACxC2W,EAAW,OAAS3W,EAAoB,EAAE,EAC1C2W,EAAW,gBAAkB3W,EAAoB,CAAC,EAClD2W,EAAW,gBAAkB3W,EAAoB,EAAE,EAEnDN,EAAO,QAAUiX,CAEX,EAEC,SAASjX,EAAQD,EAASO,EAAqB,CAEtD,aAGA,SAAS0O,GAAU,CACjB,KAAK,UAAY,CAAC,CACpB,CAFS/O,EAAA+O,EAAA,WAIT,IAAIrC,EAAIqC,EAAQ,UAEhBrC,EAAE,YAAc,SAAUuK,EAAOC,EAAU,CACzC,KAAK,UAAU,KAAK,CAClB,MAAOD,EACP,SAAUC,CACZ,CAAC,CACH,EAEAxK,EAAE,eAAiB,SAAUuK,EAAOC,EAAU,CAC5C,QAAS3T,EAAI,KAAK,UAAU,OAAQA,GAAK,EAAGA,IAAK,CAC/C,IAAI4T,EAAI,KAAK,UAAU5T,CAAC,EAEpB4T,EAAE,QAAUF,GAASE,EAAE,WAAaD,GACtC,KAAK,UAAU,OAAO3T,EAAG,CAAC,CAE9B,CACF,EAEAmJ,EAAE,KAAO,SAAUuK,EAAOG,EAAM,CAC9B,QAAS7T,EAAI,EAAGA,EAAI,KAAK,UAAU,OAAQA,IAAK,CAC9C,IAAI4T,EAAI,KAAK,UAAU5T,CAAC,EAEpB0T,IAAUE,EAAE,OACdA,EAAE,SAASC,CAAI,CAEnB,CACF,EAEArX,EAAO,QAAUgP,CAEX,CACG,CAAC,CACV,CAAC,IC7uID,IAAAsI,GAAAC,GAAA,CAAAC,GAAAC,KAAA,cAACC,EAAA,SAA0CC,EAAMC,EAAS,CACtD,OAAOJ,IAAY,UAAY,OAAOC,IAAW,SACnDA,GAAO,QAAUG,EAAQ,IAAsB,EACxC,OAAO,QAAW,YAAc,OAAO,IAC9C,OAAO,CAAC,aAAa,EAAGA,CAAO,EACxB,OAAOJ,IAAY,SAC1BA,GAAQ,SAAcI,EAAQ,IAAsB,EAEpDD,EAAK,SAAcC,EAAQD,EAAK,UAAa,CAC/C,EATC,oCASEH,GAAM,SAASK,EAA+B,CACjD,OAAiB,SAASC,EAAS,CAEzB,IAAIC,EAAmB,CAAC,EAGxB,SAASC,EAAoBC,EAAU,CAGtC,GAAGF,EAAiBE,CAAQ,EAC3B,OAAOF,EAAiBE,CAAQ,EAAE,QAGnC,IAAIR,EAASM,EAAiBE,CAAQ,EAAI,CACzC,EAAGA,EACH,EAAG,GACH,QAAS,CAAC,CACX,EAGA,OAAAH,EAAQG,CAAQ,EAAE,KAAKR,EAAO,QAASA,EAAQA,EAAO,QAASO,CAAmB,EAGlFP,EAAO,EAAI,GAGJA,EAAO,OACf,CArBS,OAAAC,EAAAM,EAAA,uBAyBTA,EAAoB,EAAIF,EAGxBE,EAAoB,EAAID,EAGxBC,EAAoB,EAAI,SAASE,EAAO,CAAE,OAAOA,CAAO,EAGxDF,EAAoB,EAAI,SAASR,EAASW,EAAMC,EAAQ,CACnDJ,EAAoB,EAAER,EAASW,CAAI,GACtC,OAAO,eAAeX,EAASW,EAAM,CACpC,aAAc,GACd,WAAY,GACZ,IAAKC,CACN,CAAC,CAEH,EAGAJ,EAAoB,EAAI,SAASP,EAAQ,CACxC,IAAIW,EAASX,GAAUA,EAAO,WAC7BC,EAAA,UAAsB,CAAE,OAAOD,EAAO,OAAY,EAAlD,cACAC,EAAA,UAA4B,CAAE,OAAOD,CAAQ,EAA7C,oBACD,OAAAO,EAAoB,EAAEI,EAAQ,IAAKA,CAAM,EAClCA,CACR,EAGAJ,EAAoB,EAAI,SAASK,EAAQC,EAAU,CAAE,OAAO,OAAO,UAAU,eAAe,KAAKD,EAAQC,CAAQ,CAAG,EAGpHN,EAAoB,EAAI,GAGjBA,EAAoBA,EAAoB,EAAI,CAAC,CACrD,EAEC,CAEH,SAASP,EAAQD,EAAS,CAEjCC,EAAO,QAAUI,CAEX,EAEC,SAASJ,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIO,EAAoBP,EAAoB,CAAC,EAAE,kBAE/C,SAASQ,GAAgB,CAAC,CAAjBd,EAAAc,EAAA,iBAGT,QAASC,KAAQF,EACfC,EAAcC,CAAI,EAAIF,EAAkBE,CAAI,EAG9CD,EAAc,gCAAkC,GAChDA,EAAc,0BAA4BD,EAAkB,oBAC5DC,EAAc,6BAA+B,GAC7CA,EAAc,KAAO,GACrBA,EAAc,wBAA0B,GACxCA,EAAc,0BAA4B,GAC1CA,EAAc,8BAAgC,GAE9Cf,EAAO,QAAUe,CAEX,EAEC,SAASf,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIU,EAAeV,EAAoB,CAAC,EAAE,aAE1C,SAASW,EAASC,EAAQC,EAAQC,EAAO,CACvCJ,EAAa,KAAK,KAAME,EAAQC,EAAQC,CAAK,CAC/C,CAFSpB,EAAAiB,EAAA,YAITA,EAAS,UAAY,OAAO,OAAOD,EAAa,SAAS,EACzD,QAASD,KAAQC,EACfC,EAASF,CAAI,EAAIC,EAAaD,CAAI,EAGpChB,EAAO,QAAUkB,CAEX,EAEC,SAASlB,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIe,EAASf,EAAoB,CAAC,EAAE,OAEpC,SAASgB,EAAUC,EAAQC,EAAUC,EAAQ,CAC3CJ,EAAO,KAAK,KAAME,EAAQC,EAAUC,CAAM,CAC5C,CAFSzB,EAAAsB,EAAA,aAITA,EAAU,UAAY,OAAO,OAAOD,EAAO,SAAS,EACpD,QAASN,KAAQM,EACfC,EAAUP,CAAI,EAAIM,EAAON,CAAI,EAG/BhB,EAAO,QAAUuB,CAEX,EAEC,SAASvB,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIoB,EAAgBpB,EAAoB,CAAC,EAAE,cAE3C,SAASqB,EAAiBC,EAAQ,CAChCF,EAAc,KAAK,KAAME,CAAM,CACjC,CAFS5B,EAAA2B,EAAA,oBAITA,EAAiB,UAAY,OAAO,OAAOD,EAAc,SAAS,EAClE,QAASX,KAAQW,EACfC,EAAiBZ,CAAI,EAAIW,EAAcX,CAAI,EAG7ChB,EAAO,QAAU4B,CAEX,EAEC,SAAS5B,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIuB,EAAevB,EAAoB,CAAC,EAAE,aACtCwB,EAAQxB,EAAoB,CAAC,EAAE,MAEnC,SAASyB,EAASC,EAAIC,EAAKC,EAAMC,EAAO,CACtCN,EAAa,KAAK,KAAMG,EAAIC,EAAKC,EAAMC,CAAK,CAC9C,CAFSnC,EAAA+B,EAAA,YAITA,EAAS,UAAY,OAAO,OAAOF,EAAa,SAAS,EACzD,QAASd,KAAQc,EACfE,EAAShB,CAAI,EAAIc,EAAad,CAAI,EAGpCgB,EAAS,UAAU,KAAO,UAAY,CACpC,IAAIH,EAAS,KAAK,aAAa,UAAU,EACzC,KAAK,cAAgBA,EAAO,eAAiB,KAAK,aAAe,KAAK,gBAAkB,KAAK,mBAAqB,KAAK,aACvH,KAAK,cAAgBA,EAAO,eAAiB,KAAK,aAAe,KAAK,gBAAkB,KAAK,mBAAqB,KAAK,aAEnH,KAAK,IAAI,KAAK,aAAa,EAAIA,EAAO,cAAgBA,EAAO,sBAC/D,KAAK,cAAgBA,EAAO,cAAgBA,EAAO,oBAAsBE,EAAM,KAAK,KAAK,aAAa,GAGpG,KAAK,IAAI,KAAK,aAAa,EAAIF,EAAO,cAAgBA,EAAO,sBAC/D,KAAK,cAAgBA,EAAO,cAAgBA,EAAO,oBAAsBE,EAAM,KAAK,KAAK,aAAa,GAIpG,KAAK,OAAS,KAChB,KAAK,OAAO,KAAK,cAAe,KAAK,aAAa,EAG3C,KAAK,MAAM,SAAS,EAAE,QAAU,EACrC,KAAK,OAAO,KAAK,cAAe,KAAK,aAAa,EAIhD,KAAK,gCAAgC,KAAK,cAAe,KAAK,aAAa,EAGjFF,EAAO,mBAAqB,KAAK,IAAI,KAAK,aAAa,EAAI,KAAK,IAAI,KAAK,aAAa,EAEtF,KAAK,aAAe,EACpB,KAAK,aAAe,EACpB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,EACvB,KAAK,kBAAoB,EACzB,KAAK,kBAAoB,EACzB,KAAK,cAAgB,EACrB,KAAK,cAAgB,CACvB,EAEAG,EAAS,UAAU,gCAAkC,SAAUK,EAAIC,EAAI,CAGrE,QAFIC,EAAQ,KAAK,SAAS,EAAE,SAAS,EACjCC,EACKC,EAAI,EAAGA,EAAIF,EAAM,OAAQE,IAChCD,EAAOD,EAAME,CAAC,EACVD,EAAK,SAAS,GAAK,MACrBA,EAAK,OAAOH,EAAIC,CAAE,EAClBE,EAAK,eAAiBH,EACtBG,EAAK,eAAiBF,GAEtBE,EAAK,gCAAgCH,EAAIC,CAAE,CAGjD,EAEAN,EAAS,UAAU,SAAW,SAAUU,EAAO,CAC7C,KAAK,MAAQA,CACf,EAEAV,EAAS,UAAU,SAAW,UAAY,CACxC,OAAO,KACT,EAEAA,EAAS,UAAU,SAAW,UAAY,CACxC,OAAO,KACT,EAEAA,EAAS,UAAU,QAAU,SAAUW,EAAM,CAC3C,KAAK,KAAOA,CACd,EAEAX,EAAS,UAAU,QAAU,UAAY,CACvC,OAAO,IACT,EAEAA,EAAS,UAAU,aAAe,SAAUY,EAAW,CACrD,KAAK,UAAYA,CACnB,EAEAZ,EAAS,UAAU,YAAc,UAAY,CAC3C,OAAO,SACT,EAEAhC,EAAO,QAAUgC,CAEX,EAEC,SAAShC,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIsC,EAAWtC,EAAoB,CAAC,EAAE,SAClCqB,EAAmBrB,EAAoB,CAAC,EACxCgB,EAAYhB,EAAoB,CAAC,EACjCyB,EAAWzB,EAAoB,CAAC,EAChCW,EAAWX,EAAoB,CAAC,EAChCQ,EAAgBR,EAAoB,CAAC,EACrCO,EAAoBP,EAAoB,CAAC,EAAE,kBAC3CuC,EAAkBvC,EAAoB,CAAC,EAAE,gBACzCwC,EAAQxC,EAAoB,CAAC,EAAE,MAC/ByC,EAASzC,EAAoB,CAAC,EAAE,OAChC0C,EAAS1C,EAAoB,CAAC,EAAE,OAChC2C,EAAU3C,EAAoB,CAAC,EAAE,QACjC4C,EAAY5C,EAAoB,CAAC,EAAE,UACnCe,EAASf,EAAoB,CAAC,EAAE,OAChC6C,EAAY7C,EAAoB,CAAC,EAAE,UAEvC,SAAS8C,GAAa,CACpBR,EAAS,KAAK,IAAI,EAElB,KAAK,UAAY,CAAC,CACpB,CAJS5C,EAAAoD,EAAA,cAMTA,EAAW,UAAY,OAAO,OAAOR,EAAS,SAAS,EAEvD,QAAS7B,KAAQ6B,EACfQ,EAAWrC,CAAI,EAAI6B,EAAS7B,CAAI,EAGlCqC,EAAW,UAAU,gBAAkB,UAAY,CACjD,IAAIpB,EAAK,IAAIL,EAAiB,IAAI,EAClC,YAAK,aAAeK,EACbA,CACT,EAEAoB,EAAW,UAAU,SAAW,SAAU3B,EAAQ,CAChD,OAAO,IAAIH,EAAU,KAAM,KAAK,aAAcG,CAAM,CACtD,EAEA2B,EAAW,UAAU,QAAU,SAAUjB,EAAO,CAC9C,OAAO,IAAIJ,EAAS,KAAK,aAAcI,CAAK,CAC9C,EAEAiB,EAAW,UAAU,QAAU,SAAUhC,EAAO,CAC9C,OAAO,IAAIH,EAAS,KAAM,KAAMG,CAAK,CACvC,EAEAgC,EAAW,UAAU,eAAiB,UAAY,CAChDR,EAAS,UAAU,eAAe,KAAK,KAAM,SAAS,EACjD,KAAK,cACJ9B,EAAc,oBAAsB,GACtC,KAAK,gBAAkB,GAEvB,KAAK,gBAAkBA,EAAc,oBAGvC,KAAK,mCAAqCA,EAAc,gDACxD,KAAK,eAAiBD,EAAkB,wBACxC,KAAK,kBAAoBA,EAAkB,2BAC3C,KAAK,gBAAkBA,EAAkB,yBACzC,KAAK,wBAA0BA,EAAkB,kCACjD,KAAK,mBAAqBA,EAAkB,6BAC5C,KAAK,2BAA6BA,EAAkB,sCAGpD,KAAK,eAAiB,CAAC,EACvB,KAAK,mBAAqB,EAC1B,KAAK,sBAAwB,EAC7B,KAAK,cAAgB,GACrB,KAAK,iBAAmB,GAGxB,KAAK,aAAe,EACpB,KAAK,gBAAkB,KAAK,cAAgBA,EAAkB,yBAC9D,KAAK,iBAAmBA,EAAkB,yBAA2B,KAAK,cAC1E,KAAK,gBAAkB,EAE3B,EAEAuC,EAAW,UAAU,OAAS,UAAY,CACxC,IAAIC,EAAsBR,EAAgB,+BAC1C,OAAIQ,IACF,KAAK,iBAAiB,EACtB,KAAK,aAAa,cAAc,GAGlC,KAAK,MAAQ,EACN,KAAK,cAAc,CAC5B,EAEAD,EAAW,UAAU,cAAgB,UAAY,CAS/C,GARA,KAAK,iBAAmB,KAAK,mCAAmC,EAChE,KAAK,aAAa,8BAA8B,KAAK,gBAAgB,EACrE,KAAK,4BAA4B,EACjC,KAAK,aAAa,0BAA0B,EAC5C,KAAK,aAAa,wBAAwB,EAC1C,KAAK,aAAa,QAAQ,EAAE,kBAAkB,EAC9C,KAAK,qBAAqB,EAErB,KAAK,aAsBR,GAAItC,EAAc,8BAA+B,CAE/C,KAAK,YAAY,EAEjB,KAAK,aAAa,gCAAgC,EAClD,IAAIwC,EAAW,IAAI,IAAI,KAAK,YAAY,CAAC,EACrCC,EAAe,KAAK,iBAAiB,OAAO,SAAUC,EAAG,CAC3D,OAAOF,EAAS,IAAIE,CAAC,CACvB,CAAC,EACD,KAAK,aAAa,8BAA8BD,CAAY,CAC9D,MAhCqB,CACrB,IAAIE,EAAS,KAAK,cAAc,EAGhC,GAAIA,EAAO,OAAS,EAClB,KAAK,sBAAsBA,CAAM,MAG9B,CAED,KAAK,YAAY,EAEjB,KAAK,aAAa,gCAAgC,EAClD,IAAIH,EAAW,IAAI,IAAI,KAAK,YAAY,CAAC,EACrCC,EAAe,KAAK,iBAAiB,OAAO,SAAUC,EAAG,CAC3D,OAAOF,EAAS,IAAIE,CAAC,CACvB,CAAC,EACD,KAAK,aAAa,8BAA8BD,CAAY,EAE5D,KAAK,sBAAsB,CAC7B,CACJ,CAcA,YAAK,mBAAmB,EACxB,KAAK,kBAAkB,EAEhB,EACT,EAEAH,EAAW,UAAU,KAAO,UAAY,CAGtC,GAFA,KAAK,kBAED,KAAK,kBAAoB,KAAK,eAAiB,CAAC,KAAK,eAAiB,CAAC,KAAK,iBAC9E,GAAI,KAAK,eAAe,OAAS,EAC/B,KAAK,cAAgB,OAErB,OAAO,GAIX,GAAI,KAAK,gBAAkBvC,EAAkB,0BAA4B,GAAK,CAAC,KAAK,eAAiB,CAAC,KAAK,iBAAkB,CAC3H,GAAI,KAAK,YAAY,EACnB,GAAI,KAAK,eAAe,OAAS,EAC/B,KAAK,cAAgB,OAErB,OAAO,GAIX,KAAK,eAED,KAAK,eAAiB,EAExB,KAAK,gBAAkB,KAAK,aACnB,KAAK,eAAiB,IAE/B,KAAK,gBAAkB,KAAK,aAAe,GAI7C,KAAK,cAAgB,KAAK,IAAI,KAAK,qBAAuB,KAAK,IAAI,KAAK,aAAc,KAAK,IAAI,KAAO,KAAK,qBAAuB,KAAK,iBAAiB,EAAI,KAAK,IAAI,KAAK,eAAe,CAAC,EAAI,IAAM,KAAK,gBAAiB,KAAK,gBAAgB,EAC/O,KAAK,gBAAkB,KAAK,KAAK,KAAK,uBAAyB,KAAK,KAAK,KAAK,aAAa,CAAC,CAC9F,CAEA,GAAI,KAAK,cAAe,CACtB,GAAI,KAAK,mBAAqB,IAAM,EAClC,GAAI,KAAK,eAAe,OAAS,EAAG,CAClC,KAAK,aAAa,aAAa,EAC/B,KAAK,WAAW,EAChB,KAAK,SAAS,KAAK,cAAc,EAEjC,KAAK,aAAa,gCAAgC,EAClD,IAAIyC,EAAW,IAAI,IAAI,KAAK,YAAY,CAAC,EACrCC,EAAe,KAAK,iBAAiB,OAAO,SAAUC,EAAG,CAC3D,OAAOF,EAAS,IAAIE,CAAC,CACvB,CAAC,EACD,KAAK,aAAa,8BAA8BD,CAAY,EAE5D,KAAK,aAAa,aAAa,EAC/B,KAAK,WAAW,EAChB,KAAK,cAAgB1C,EAAkB,kCACzC,MACE,KAAK,cAAgB,GACrB,KAAK,iBAAmB,GAG5B,KAAK,oBACP,CAEA,GAAI,KAAK,iBAAkB,CACzB,GAAI,KAAK,YAAY,EACnB,MAAO,GAEL,KAAK,sBAAwB,IAAM,IACrC,KAAK,aAAa,aAAa,EAC/B,KAAK,WAAW,GAElB,KAAK,cAAgBA,EAAkB,qCAAuC,IAAM,KAAK,uBAAyB,KAClH,KAAK,uBACP,CAEA,IAAI6C,EAAoB,CAAC,KAAK,eAAiB,CAAC,KAAK,iBACjDC,EAA+B,KAAK,mBAAqB,IAAM,GAAK,KAAK,eAAiB,KAAK,sBAAwB,IAAM,GAAK,KAAK,iBAE3I,YAAK,kBAAoB,EACzB,KAAK,aAAa,aAAa,EAC/B,KAAK,iBAAiB,EACtB,KAAK,oBAAoBD,EAAmBC,CAA4B,EACxE,KAAK,wBAAwB,EAC7B,KAAK,UAAU,EACf,KAAK,QAAQ,EAEN,EACT,EAEAP,EAAW,UAAU,iBAAmB,UAAY,CAGlD,QAFIE,EAAW,KAAK,aAAa,YAAY,EACzCM,EAAQ,CAAC,EACJpB,EAAI,EAAGA,EAAIc,EAAS,OAAQd,IAAK,CACxC,IAAIqB,EAAOP,EAASd,CAAC,EAAE,KACnBsB,EAAKR,EAASd,CAAC,EAAE,GACrBoB,EAAME,CAAE,EAAI,CACV,GAAIA,EACJ,EAAGD,EAAK,WAAW,EACnB,EAAGA,EAAK,WAAW,EACnB,EAAGA,EAAK,MACR,EAAGA,EAAK,MACV,CACF,CAEA,OAAOD,CACT,EAEAR,EAAW,UAAU,kBAAoB,UAAY,CACnD,KAAK,uBAAyB,GAC9B,KAAK,gBAAkB,KAAK,uBAC5B,IAAIW,EAAc,GAGlB,GAAIlD,EAAkB,UAAY,SAChC,KAAK,KAAK,eAAe,MACpB,CAEL,KAAO,CAACkD,GACNA,EAAc,KAAK,KAAK,EAG1B,KAAK,aAAa,aAAa,CACjC,CACF,EAEAX,EAAW,UAAU,mCAAqC,UAAY,CACpE,IAAIY,EAAW,CAAC,EACZC,EAEAC,EAAS,KAAK,aAAa,UAAU,EACrChC,EAAOgC,EAAO,OACd1B,EACJ,IAAKA,EAAI,EAAGA,EAAIN,EAAMM,IACpByB,EAAQC,EAAO1B,CAAC,EAEhByB,EAAM,gBAAgB,EAEjBA,EAAM,cACTD,EAAWA,EAAS,OAAOC,EAAM,SAAS,CAAC,GAI/C,OAAOD,CACT,EAEAZ,EAAW,UAAU,iBAAmB,UAAY,CAClD,IAAIe,EAAQ,CAAC,EACbA,EAAQA,EAAM,OAAO,KAAK,aAAa,YAAY,CAAC,EACpD,IAAIC,EAAU,IAAI,IACd5B,EACJ,IAAKA,EAAI,EAAGA,EAAI2B,EAAM,OAAQ3B,IAAK,CACjC,IAAI6B,EAAOF,EAAM3B,CAAC,EAElB,GAAI,CAAC4B,EAAQ,IAAIC,CAAI,EAAG,CACtB,IAAInD,EAASmD,EAAK,UAAU,EACxBlD,EAASkD,EAAK,UAAU,EAE5B,GAAInD,GAAUC,EACZkD,EAAK,cAAc,EAAE,KAAK,IAAItB,CAAQ,EACtCsB,EAAK,cAAc,EAAE,KAAK,IAAItB,CAAQ,EACtC,KAAK,8BAA8BsB,CAAI,EACvCD,EAAQ,IAAIC,CAAI,MACX,CACL,IAAIC,EAAW,CAAC,EAKhB,GAHAA,EAAWA,EAAS,OAAOpD,EAAO,kBAAkBC,CAAM,CAAC,EAC3DmD,EAAWA,EAAS,OAAOnD,EAAO,kBAAkBD,CAAM,CAAC,EAEvD,CAACkD,EAAQ,IAAIE,EAAS,CAAC,CAAC,EAAG,CAC7B,GAAIA,EAAS,OAAS,EAAG,CACvB,IAAIC,EACJ,IAAKA,EAAI,EAAGA,EAAID,EAAS,OAAQC,IAAK,CACpC,IAAIC,EAAYF,EAASC,CAAC,EAC1BC,EAAU,cAAc,EAAE,KAAK,IAAIzB,CAAQ,EAC3C,KAAK,8BAA8ByB,CAAS,CAC9C,CACF,CACAF,EAAS,QAAQ,SAAUD,EAAM,CAC/BD,EAAQ,IAAIC,CAAI,CAClB,CAAC,CACH,CACF,CACF,CAEA,GAAID,EAAQ,MAAQD,EAAM,OACxB,KAEJ,CACF,EAEAf,EAAW,UAAU,sBAAwB,SAAUK,EAAQ,CAS7D,QAPIgB,EAAuB,IAAI3B,EAAM,EAAG,CAAC,EACrC4B,EAAkB,KAAK,KAAK,KAAK,KAAKjB,EAAO,MAAM,CAAC,EACpDkB,EAAS,EACTC,EAAW,EACXC,EAAW,EACXC,EAAQ,IAAI/B,EAAO,EAAG,CAAC,EAElBP,EAAI,EAAGA,EAAIiB,EAAO,OAAQjB,IAAK,CAClCA,EAAIkC,GAAmB,IAGzBG,EAAW,EACXD,EAAWD,EAEPnC,GAAK,IACPoC,GAAY9D,EAAc,8BAG5B6D,EAAS,GAGX,IAAII,EAAOtB,EAAOjB,CAAC,EAGfwC,EAAahC,EAAO,iBAAiB+B,CAAI,EAG7CN,EAAqB,EAAII,EACzBJ,EAAqB,EAAIG,EAGzBE,EAAQ1B,EAAW,aAAa2B,EAAMC,EAAYP,CAAoB,EAElEK,EAAM,EAAIH,IACZA,EAAS,KAAK,MAAMG,EAAM,CAAC,GAG7BD,EAAW,KAAK,MAAMC,EAAM,EAAIhE,EAAc,4BAA4B,CAC5E,CAEA,KAAK,UAAU,IAAIiC,EAAOF,EAAgB,eAAiBiC,EAAM,EAAI,EAAGjC,EAAgB,eAAiBiC,EAAM,EAAI,CAAC,CAAC,CACvH,EAEA1B,EAAW,aAAe,SAAU2B,EAAMC,EAAYC,EAAe,CACnE,IAAIC,EAAY,KAAK,IAAI,KAAK,kBAAkBH,CAAI,EAAGjE,EAAc,yBAAyB,EAC9FsC,EAAW,mBAAmB4B,EAAY,KAAM,EAAG,IAAK,EAAGE,CAAS,EACpE,IAAIC,EAAS9D,EAAO,gBAAgB0D,CAAI,EAEpCK,EAAY,IAAIjC,EACpBiC,EAAU,cAAcD,EAAO,QAAQ,CAAC,EACxCC,EAAU,cAAcD,EAAO,QAAQ,CAAC,EACxCC,EAAU,aAAaH,EAAc,CAAC,EACtCG,EAAU,aAAaH,EAAc,CAAC,EAEtC,QAASzC,EAAI,EAAGA,EAAIuC,EAAK,OAAQvC,IAAK,CACpC,IAAID,EAAOwC,EAAKvC,CAAC,EACjBD,EAAK,UAAU6C,CAAS,CAC1B,CAEA,IAAIC,EAAc,IAAItC,EAAOoC,EAAO,QAAQ,EAAGA,EAAO,QAAQ,CAAC,EAE/D,OAAOC,EAAU,sBAAsBC,CAAW,CACpD,EAEAjC,EAAW,mBAAqB,SAAUb,EAAM+C,EAAcC,EAAYC,EAAUC,EAAUC,EAAkB,CAE9G,IAAIC,GAAgBH,EAAWD,EAAa,GAAK,EAE7CI,EAAe,IACjBA,GAAgB,KAGlB,IAAIC,GAAaD,EAAeJ,GAAc,IAC1CM,EAAOD,EAAY1C,EAAU,OAAS,IAGtC4C,EAAW,KAAK,IAAID,CAAI,EACxBE,EAAKN,EAAW,KAAK,IAAII,CAAI,EAC7BG,EAAKP,EAAW,KAAK,IAAII,CAAI,EAEjCtD,EAAK,UAAUwD,EAAIC,CAAE,EAIrB,IAAIC,EAAgB,CAAC,EACrBA,EAAgBA,EAAc,OAAO1D,EAAK,SAAS,CAAC,EACpD,IAAI2D,EAAaD,EAAc,OAE3BX,GAAgB,MAClBY,IAYF,QATIC,EAAc,EAEdC,EAAgBH,EAAc,OAC9BI,EAEAlC,EAAQ5B,EAAK,gBAAgB+C,CAAY,EAItCnB,EAAM,OAAS,GAAG,CAEvB,IAAImC,EAAOnC,EAAM,CAAC,EAClBA,EAAM,OAAO,EAAG,CAAC,EACjB,IAAIoC,EAAQN,EAAc,QAAQK,CAAI,EAClCC,GAAS,GACXN,EAAc,OAAOM,EAAO,CAAC,EAE/BH,IACAF,GACF,CAEIZ,GAAgB,KAElBe,GAAcJ,EAAc,QAAQ9B,EAAM,CAAC,CAAC,EAAI,GAAKiC,EAErDC,EAAa,EAKf,QAFIG,EAAY,KAAK,IAAIhB,EAAWD,CAAU,EAAIW,EAEzC1D,EAAI6D,EAAYF,GAAeD,EAAY1D,EAAI,EAAEA,EAAI4D,EAAe,CAC3E,IAAIK,EAAkBR,EAAczD,CAAC,EAAE,YAAYD,CAAI,EAGvD,GAAIkE,GAAmBnB,EAIvB,KAAIoB,GAAmBnB,EAAaY,EAAcK,GAAa,IAC3DG,GAAiBD,EAAkBF,GAAa,IAEpDpD,EAAW,mBAAmBqD,EAAiBlE,EAAMmE,EAAiBC,EAAelB,EAAWC,EAAkBA,CAAgB,EAElIS,IACF,CACF,EAEA/C,EAAW,kBAAoB,SAAU2B,EAAM,CAG7C,QAFI6B,EAAc3D,EAAQ,UAEjBT,EAAI,EAAGA,EAAIuC,EAAK,OAAQvC,IAAK,CACpC,IAAID,EAAOwC,EAAKvC,CAAC,EACbqE,EAAWtE,EAAK,YAAY,EAE5BsE,EAAWD,IACbA,EAAcC,EAElB,CAEA,OAAOD,CACT,EAEAxD,EAAW,UAAU,mBAAqB,UAAY,CAEpD,MAAO,IAAK,KAAK,MAAQ,GAAK,KAAK,eACrC,EAKAA,EAAW,UAAU,uBAAyB,UAAY,CACxD,IAAI0D,EAAO,KAEPC,EAAmB,CAAC,EACxB,KAAK,aAAe,CAAC,EACrB,KAAK,cAAgB,CAAC,EAMtB,QAJIC,EAAa,CAAC,EACd1D,EAAW,KAAK,aAAa,YAAY,EAGpCd,EAAI,EAAGA,EAAIc,EAAS,OAAQd,IAAK,CACxC,IAAID,EAAOe,EAASd,CAAC,EACjBjB,EAASgB,EAAK,UAAU,EAExB,KAAK,0BAA0BA,CAAI,IAAM,IAAMhB,EAAO,IAAM,MAAa,CAAC,KAAK,aAAaA,CAAM,IACpGyF,EAAW,KAAKzE,CAAI,CAExB,CAGA,QAASC,EAAI,EAAGA,EAAIwE,EAAW,OAAQxE,IAAK,CAC1C,IAAID,EAAOyE,EAAWxE,CAAC,EACnByE,EAAO1E,EAAK,UAAU,EAAE,GAExB,OAAOwE,EAAiBE,CAAI,EAAM,MAAaF,EAAiBE,CAAI,EAAI,CAAC,GAE7EF,EAAiBE,CAAI,EAAIF,EAAiBE,CAAI,EAAE,OAAO1E,CAAI,CAC7D,CAGA,OAAO,KAAKwE,CAAgB,EAAE,QAAQ,SAAUE,EAAM,CACpD,GAAIF,EAAiBE,CAAI,EAAE,OAAS,EAAG,CACrC,IAAIC,EAAkB,iBAAmBD,EACzCH,EAAK,aAAaI,CAAe,EAAIH,EAAiBE,CAAI,EAE1D,IAAI1F,EAASwF,EAAiBE,CAAI,EAAE,CAAC,EAAE,UAAU,EAG7CE,EAAgB,IAAIpF,EAAS+E,EAAK,YAAY,EAClDK,EAAc,GAAKD,EACnBC,EAAc,YAAc5F,EAAO,aAAe,EAClD4F,EAAc,aAAe5F,EAAO,cAAgB,EACpD4F,EAAc,cAAgB5F,EAAO,eAAiB,EACtD4F,EAAc,WAAa5F,EAAO,YAAc,EAEhDuF,EAAK,cAAcI,CAAe,EAAIC,EAEtC,IAAIC,EAAmBN,EAAK,gBAAgB,EAAE,IAAIA,EAAK,SAAS,EAAGK,CAAa,EAC5EE,EAAc9F,EAAO,SAAS,EAGlC8F,EAAY,IAAIF,CAAa,EAG7B,QAAS3E,EAAI,EAAGA,EAAIuE,EAAiBE,CAAI,EAAE,OAAQzE,IAAK,CACtD,IAAID,EAAOwE,EAAiBE,CAAI,EAAEzE,CAAC,EAEnC6E,EAAY,OAAO9E,CAAI,EACvB6E,EAAiB,IAAI7E,CAAI,CAC3B,CACF,CACF,CAAC,CACH,EAEAa,EAAW,UAAU,eAAiB,UAAY,CAChD,IAAIkE,EAAgB,CAAC,EACjBC,EAAW,CAAC,EAGhB,KAAK,sBAAsB,EAE3B,QAAS/E,EAAI,EAAGA,EAAI,KAAK,cAAc,OAAQA,IAE7C+E,EAAS,KAAK,cAAc/E,CAAC,EAAE,EAAE,EAAI,KAAK,cAAcA,CAAC,EACzD8E,EAAc,KAAK,cAAc9E,CAAC,EAAE,EAAE,EAAI,CAAC,EAAE,OAAO,KAAK,cAAcA,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,EAG/F,KAAK,aAAa,OAAO,KAAK,cAAcA,CAAC,EAAE,SAAS,CAAC,EACzD,KAAK,cAAcA,CAAC,EAAE,MAAQ,KAGhC,KAAK,aAAa,cAAc,EAGhC,KAAK,oBAAoB8E,EAAeC,CAAQ,CAClD,EAEAnE,EAAW,UAAU,uBAAyB,UAAY,CACxD,IAAI0D,EAAO,KACPU,EAAsB,KAAK,oBAAsB,CAAC,EAEtD,OAAO,KAAK,KAAK,YAAY,EAAE,QAAQ,SAAU1D,EAAI,CACnD,IAAI2D,EAAeX,EAAK,cAAchD,CAAE,EAExC0D,EAAoB1D,CAAE,EAAIgD,EAAK,UAAUA,EAAK,aAAahD,CAAE,EAAG2D,EAAa,YAAcA,EAAa,YAAY,EAGpHA,EAAa,KAAK,MAAQD,EAAoB1D,CAAE,EAAE,MAClD2D,EAAa,KAAK,OAASD,EAAoB1D,CAAE,EAAE,MACrD,CAAC,CACH,EAEAV,EAAW,UAAU,oBAAsB,UAAY,CACrD,QAASZ,EAAI,KAAK,cAAc,OAAS,EAAGA,GAAK,EAAGA,IAAK,CACvD,IAAIkF,EAAgB,KAAK,cAAclF,CAAC,EACpCsB,EAAK4D,EAAc,GACnBC,EAAmBD,EAAc,YACjCE,EAAiBF,EAAc,WAEnC,KAAK,gBAAgB,KAAK,gBAAgB5D,CAAE,EAAG4D,EAAc,KAAK,EAAGA,EAAc,KAAK,EAAGC,EAAkBC,CAAc,CAC7H,CACF,EAEAxE,EAAW,UAAU,4BAA8B,UAAY,CAC7D,IAAI0D,EAAO,KACPe,EAAY,KAAK,oBAErB,OAAO,KAAKA,CAAS,EAAE,QAAQ,SAAU/D,EAAI,CAC3C,IAAI2D,EAAeX,EAAK,cAAchD,CAAE,EACpC6D,EAAmBF,EAAa,YAChCG,EAAiBH,EAAa,WAGlCX,EAAK,gBAAgBe,EAAU/D,CAAE,EAAG2D,EAAa,KAAK,EAAGA,EAAa,KAAK,EAAGE,EAAkBC,CAAc,CAChH,CAAC,CACH,EAEAxE,EAAW,UAAU,aAAe,SAAUb,EAAM,CAClD,IAAIuB,EAAKvB,EAAK,GAEd,GAAI,KAAK,UAAUuB,CAAE,GAAK,KACxB,OAAO,KAAK,UAAUA,CAAE,EAI1B,IAAIgE,EAAavF,EAAK,SAAS,EAC/B,GAAIuF,GAAc,KAChB,YAAK,UAAUhE,CAAE,EAAI,GACd,GAMT,QAHIiE,EAAWD,EAAW,SAAS,EAG1BtF,EAAI,EAAGA,EAAIuF,EAAS,OAAQvF,IAAK,CACxC,IAAIwF,EAAWD,EAASvF,CAAC,EAEzB,GAAI,KAAK,cAAcwF,CAAQ,EAAI,EACjC,YAAK,UAAUlE,CAAE,EAAI,GACd,GAIT,GAAIkE,EAAS,SAAS,GAAK,KAAM,CAC/B,KAAK,UAAUA,EAAS,EAAE,EAAI,GAC9B,QACF,CAEA,GAAI,CAAC,KAAK,aAAaA,CAAQ,EAC7B,YAAK,UAAUlE,CAAE,EAAI,GACd,EAEX,CACA,YAAK,UAAUA,CAAE,EAAI,GACd,EACT,EAGAV,EAAW,UAAU,cAAgB,SAAUb,EAAM,CAMnD,QALIuB,EAAKvB,EAAK,GACV4B,EAAQ5B,EAAK,SAAS,EACtB0F,EAAS,EAGJzF,EAAI,EAAGA,EAAI2B,EAAM,OAAQ3B,IAAK,CACrC,IAAI6B,EAAOF,EAAM3B,CAAC,EACd6B,EAAK,UAAU,EAAE,KAAOA,EAAK,UAAU,EAAE,KAC3C4D,EAASA,EAAS,EAEtB,CACA,OAAOA,CACT,EAGA7E,EAAW,UAAU,0BAA4B,SAAUb,EAAM,CAC/D,IAAI0F,EAAS,KAAK,cAAc1F,CAAI,EACpC,GAAIA,EAAK,SAAS,GAAK,KACrB,OAAO0F,EAGT,QADIF,EAAWxF,EAAK,SAAS,EAAE,SAAS,EAC/BC,EAAI,EAAGA,EAAIuF,EAAS,OAAQvF,IAAK,CACxC,IAAI0F,EAAQH,EAASvF,CAAC,EACtByF,GAAU,KAAK,0BAA0BC,CAAK,CAChD,CACA,OAAOD,CACT,EAEA7E,EAAW,UAAU,sBAAwB,UAAY,CACvD,KAAK,cAAgB,CAAC,EACtB,KAAK,qBAAqB,KAAK,aAAa,QAAQ,EAAE,SAAS,CAAC,CAClE,EAEAA,EAAW,UAAU,qBAAuB,SAAU2E,EAAU,CAC9D,QAASvF,EAAI,EAAGA,EAAIuF,EAAS,OAAQvF,IAAK,CACxC,IAAI0F,EAAQH,EAASvF,CAAC,EAClB0F,EAAM,SAAS,GAAK,MACtB,KAAK,qBAAqBA,EAAM,SAAS,EAAE,SAAS,CAAC,EAEnD,KAAK,aAAaA,CAAK,GACzB,KAAK,cAAc,KAAKA,CAAK,CAEjC,CACF,EAKA9E,EAAW,UAAU,gBAAkB,SAAU+E,EAAc3E,EAAG4E,EAAGC,EAA0BC,EAAwB,CACrH9E,GAAK6E,EACLD,GAAKE,EAIL,QAFIC,EAAO/E,EAEFhB,EAAI,EAAGA,EAAI2F,EAAa,KAAK,OAAQ3F,IAAK,CACjD,IAAIgG,EAAML,EAAa,KAAK3F,CAAC,EAC7BgB,EAAI+E,EAGJ,QAFIE,EAAY,EAEPC,EAAI,EAAGA,EAAIF,EAAI,OAAQE,IAAK,CACnC,IAAIC,EAAQH,EAAIE,CAAC,EAEjBC,EAAM,KAAK,EAAInF,EACfmF,EAAM,KAAK,EAAIP,EAEf5E,GAAKmF,EAAM,KAAK,MAAQR,EAAa,kBAEjCQ,EAAM,KAAK,OAASF,IAAWA,EAAYE,EAAM,KAAK,OAC5D,CAEAP,GAAKK,EAAYN,EAAa,eAChC,CACF,EAEA/E,EAAW,UAAU,oBAAsB,SAAUkE,EAAeC,EAAU,CAC5E,IAAIT,EAAO,KACX,KAAK,gBAAkB,CAAC,EAExB,OAAO,KAAKQ,CAAa,EAAE,QAAQ,SAAUxD,EAAI,CAE/C,IAAI2D,EAAeF,EAASzD,CAAE,EAE9BgD,EAAK,gBAAgBhD,CAAE,EAAIgD,EAAK,UAAUQ,EAAcxD,CAAE,EAAG2D,EAAa,YAAcA,EAAa,YAAY,EAEjHA,EAAa,KAAK,MAAQX,EAAK,gBAAgBhD,CAAE,EAAE,MACnD2D,EAAa,KAAK,OAASX,EAAK,gBAAgBhD,CAAE,EAAE,MACtD,CAAC,CACH,EAEAV,EAAW,UAAU,UAAY,SAAUd,EAAOsG,EAAU,CAC1D,IAAIC,EAAkB/H,EAAc,wBAChCgI,EAAoBhI,EAAc,0BAClCqH,EAAe,CACjB,KAAM,CAAC,EACP,SAAU,CAAC,EACX,UAAW,CAAC,EACZ,MAAO,EACP,OAAQS,EACR,gBAAiBC,EACjB,kBAAmBC,CACrB,EAGAxG,EAAM,KAAK,SAAUyG,EAAIC,EAAI,CAC3B,OAAID,EAAG,KAAK,MAAQA,EAAG,KAAK,OAASC,EAAG,KAAK,MAAQA,EAAG,KAAK,OAAe,GACxED,EAAG,KAAK,MAAQA,EAAG,KAAK,OAASC,EAAG,KAAK,MAAQA,EAAG,KAAK,OAAe,EACrE,CACT,CAAC,EAGD,QAASxG,EAAI,EAAGA,EAAIF,EAAM,OAAQE,IAAK,CACrC,IAAIyG,EAAQ3G,EAAME,CAAC,EAEf2F,EAAa,KAAK,QAAU,EAC9B,KAAK,gBAAgBA,EAAcc,EAAO,EAAGL,CAAQ,EAC5C,KAAK,iBAAiBT,EAAcc,EAAM,KAAK,MAAOA,EAAM,KAAK,MAAM,EAChF,KAAK,gBAAgBd,EAAcc,EAAO,KAAK,oBAAoBd,CAAY,EAAGS,CAAQ,EAE1F,KAAK,gBAAgBT,EAAcc,EAAOd,EAAa,KAAK,OAAQS,CAAQ,EAG9E,KAAK,eAAeT,CAAY,CAClC,CAEA,OAAOA,CACT,EAEA/E,EAAW,UAAU,gBAAkB,SAAU+E,EAAc5F,EAAM2G,EAAUN,EAAU,CACvF,IAAIO,EAAkBP,EAGtB,GAAIM,GAAYf,EAAa,KAAK,OAAQ,CACxC,IAAIiB,EAAkB,CAAC,EAEvBjB,EAAa,KAAK,KAAKiB,CAAe,EACtCjB,EAAa,SAAS,KAAKgB,CAAe,EAC1ChB,EAAa,UAAU,KAAK,CAAC,CAC/B,CAGA,IAAIkB,EAAIlB,EAAa,SAASe,CAAQ,EAAI3G,EAAK,KAAK,MAEhD4F,EAAa,KAAKe,CAAQ,EAAE,OAAS,IACvCG,GAAKlB,EAAa,mBAGpBA,EAAa,SAASe,CAAQ,EAAIG,EAE9BlB,EAAa,MAAQkB,IACvBlB,EAAa,MAAQkB,GAIvB,IAAIC,EAAI/G,EAAK,KAAK,OACd2G,EAAW,IAAGI,GAAKnB,EAAa,iBAEpC,IAAIoB,EAAc,EACdD,EAAInB,EAAa,UAAUe,CAAQ,IACrCK,EAAcpB,EAAa,UAAUe,CAAQ,EAC7Cf,EAAa,UAAUe,CAAQ,EAAII,EACnCC,EAAcpB,EAAa,UAAUe,CAAQ,EAAIK,GAGnDpB,EAAa,QAAUoB,EAGvBpB,EAAa,KAAKe,CAAQ,EAAE,KAAK3G,CAAI,CACvC,EAGAa,EAAW,UAAU,oBAAsB,SAAU+E,EAAc,CAIjE,QAHIqB,EAAI,GACJC,EAAM,OAAO,UAERjH,EAAI,EAAGA,EAAI2F,EAAa,KAAK,OAAQ3F,IACxC2F,EAAa,SAAS3F,CAAC,EAAIiH,IAC7BD,EAAIhH,EACJiH,EAAMtB,EAAa,SAAS3F,CAAC,GAGjC,OAAOgH,CACT,EAGApG,EAAW,UAAU,mBAAqB,SAAU+E,EAAc,CAIhE,QAHIqB,EAAI,GACJE,EAAM,OAAO,UAERlH,EAAI,EAAGA,EAAI2F,EAAa,KAAK,OAAQ3F,IAExC2F,EAAa,SAAS3F,CAAC,EAAIkH,IAC7BF,EAAIhH,EACJkH,EAAMvB,EAAa,SAAS3F,CAAC,GAIjC,OAAOgH,CACT,EAMApG,EAAW,UAAU,iBAAmB,SAAU+E,EAAcwB,EAAYJ,EAAa,CAEvF,IAAIK,EAAM,KAAK,oBAAoBzB,CAAY,EAE/C,GAAIyB,EAAM,EACR,MAAO,GAGT,IAAIH,EAAMtB,EAAa,SAASyB,CAAG,EAEnC,GAAIH,EAAMtB,EAAa,kBAAoBwB,GAAcxB,EAAa,MAAO,MAAO,GAEpF,IAAI0B,EAAQ,EAGR1B,EAAa,UAAUyB,CAAG,EAAIL,GAC5BK,EAAM,IAAGC,EAAQN,EAAcpB,EAAa,gBAAkBA,EAAa,UAAUyB,CAAG,GAG9F,IAAIE,EACA3B,EAAa,MAAQsB,GAAOE,EAAaxB,EAAa,kBACxD2B,GAAoB3B,EAAa,OAAS0B,IAAUJ,EAAME,EAAaxB,EAAa,mBAEpF2B,GAAoB3B,EAAa,OAAS0B,GAAS1B,EAAa,MAIlE0B,EAAQN,EAAcpB,EAAa,gBACnC,IAAI4B,EACJ,OAAI5B,EAAa,MAAQwB,EACvBI,GAAqB5B,EAAa,OAAS0B,GAASF,EAEpDI,GAAqB5B,EAAa,OAAS0B,GAAS1B,EAAa,MAG/D4B,EAAoB,IAAGA,EAAoB,EAAIA,GAE/CD,EAAmB,IAAGA,EAAmB,EAAIA,GAE1CA,EAAmBC,CAC5B,EAIA3G,EAAW,UAAU,eAAiB,SAAU+E,EAAc,CAC5D,IAAI6B,EAAU,KAAK,mBAAmB7B,CAAY,EAC9C8B,EAAO9B,EAAa,SAAS,OAAS,EACtCK,EAAML,EAAa,KAAK6B,CAAO,EAC/BzH,EAAOiG,EAAIA,EAAI,OAAS,CAAC,EAEzB0B,EAAO3H,EAAK,MAAQ4F,EAAa,kBAGrC,GAAIA,EAAa,MAAQA,EAAa,SAAS8B,CAAI,EAAIC,GAAQF,GAAWC,EAAM,CAE9EzB,EAAI,OAAO,GAAI,CAAC,EAGhBL,EAAa,KAAK8B,CAAI,EAAE,KAAK1H,CAAI,EAEjC4F,EAAa,SAAS6B,CAAO,EAAI7B,EAAa,SAAS6B,CAAO,EAAIE,EAClE/B,EAAa,SAAS8B,CAAI,EAAI9B,EAAa,SAAS8B,CAAI,EAAIC,EAC5D/B,EAAa,MAAQA,EAAa,SAAS,SAAS,mBAAmBA,CAAY,CAAC,EAIpF,QADIM,EAAY,OAAO,UACdjG,EAAI,EAAGA,EAAIgG,EAAI,OAAQhG,IAC1BgG,EAAIhG,CAAC,EAAE,OAASiG,IAAWA,EAAYD,EAAIhG,CAAC,EAAE,QAEhDwH,EAAU,IAAGvB,GAAaN,EAAa,iBAE3C,IAAIgC,EAAYhC,EAAa,UAAU6B,CAAO,EAAI7B,EAAa,UAAU8B,CAAI,EAE7E9B,EAAa,UAAU6B,CAAO,EAAIvB,EAC9BN,EAAa,UAAU8B,CAAI,EAAI1H,EAAK,OAAS4F,EAAa,kBAAiBA,EAAa,UAAU8B,CAAI,EAAI1H,EAAK,OAAS4F,EAAa,iBAEzI,IAAIiC,EAAajC,EAAa,UAAU6B,CAAO,EAAI7B,EAAa,UAAU8B,CAAI,EAC9E9B,EAAa,QAAUiC,EAAaD,EAEpC,KAAK,eAAehC,CAAY,CAClC,CACF,EAEA/E,EAAW,UAAU,gBAAkB,UAAY,CAC7CtC,EAAc,OAEhB,KAAK,uBAAuB,EAE5B,KAAK,eAAe,EAEpB,KAAK,uBAAuB,EAEhC,EAEAsC,EAAW,UAAU,iBAAmB,UAAY,CAC9CtC,EAAc,OAChB,KAAK,4BAA4B,EACjC,KAAK,oBAAoB,EAE7B,EAMAsC,EAAW,UAAU,YAAc,UAAY,CAK7C,QAJIiH,EAAiB,CAAC,EAClBC,EAAe,GACf/H,EAEG+H,GAAc,CACnB,IAAIhH,EAAW,KAAK,aAAa,YAAY,EACzCiH,EAAwB,CAAC,EAC7BD,EAAe,GAEf,QAAS9H,EAAI,EAAGA,EAAIc,EAAS,OAAQd,IACnCD,EAAOe,EAASd,CAAC,EACbD,EAAK,SAAS,EAAE,QAAU,GAAK,CAACA,EAAK,SAAS,EAAE,CAAC,EAAE,cAAgBA,EAAK,SAAS,GAAK,OACxFgI,EAAsB,KAAK,CAAChI,EAAMA,EAAK,SAAS,EAAE,CAAC,EAAGA,EAAK,SAAS,CAAC,CAAC,EACtE+H,EAAe,IAGnB,GAAIA,GAAgB,GAAM,CAExB,QADIE,EAAoB,CAAC,EAChB9B,EAAI,EAAGA,EAAI6B,EAAsB,OAAQ7B,IAC5C6B,EAAsB7B,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,QAAU,IACnD8B,EAAkB,KAAKD,EAAsB7B,CAAC,CAAC,EAC/C6B,EAAsB7B,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO6B,EAAsB7B,CAAC,EAAE,CAAC,CAAC,GAG7E2B,EAAe,KAAKG,CAAiB,EACrC,KAAK,aAAa,cAAc,EAChC,KAAK,aAAa,cAAc,CAClC,CACF,CACA,KAAK,eAAiBH,CACxB,EAGAjH,EAAW,UAAU,SAAW,SAAUiH,EAAgB,CAKxD,QAJII,EAA4BJ,EAAe,OAC3CG,EAAoBH,EAAeI,EAA4B,CAAC,EAEhEC,EACKlI,EAAI,EAAGA,EAAIgI,EAAkB,OAAQhI,IAC5CkI,EAAWF,EAAkBhI,CAAC,EAE9B,KAAK,uBAAuBkI,CAAQ,EAEpCA,EAAS,CAAC,EAAE,IAAIA,EAAS,CAAC,CAAC,EAC3BA,EAAS,CAAC,EAAE,IAAIA,EAAS,CAAC,EAAGA,EAAS,CAAC,EAAE,OAAQA,EAAS,CAAC,EAAE,MAAM,EAGrEL,EAAe,OAAOA,EAAe,OAAS,EAAG,CAAC,EAClD,KAAK,aAAa,cAAc,EAChC,KAAK,aAAa,cAAc,CAClC,EAGAjH,EAAW,UAAU,uBAAyB,SAAUsH,EAAU,CAEhE,IAAIC,EACAC,EACAC,EAAaH,EAAS,CAAC,EACvBG,GAAcH,EAAS,CAAC,EAAE,OAC5BE,EAAgBF,EAAS,CAAC,EAAE,OAE5BE,EAAgBF,EAAS,CAAC,EAAE,OAE9B,IAAII,EAAaF,EAAc,OAC3BG,EAAcH,EAAc,QAC5BI,EAAaJ,EAAc,OAC3BK,EAAcL,EAAc,QAE5BM,EAAc,EACdC,EAAgB,EAChBC,EAAiB,EACjBC,EAAgB,EAChBC,EAAiB,CAACJ,EAAaE,EAAgBD,EAAeE,CAAa,EAE/E,GAAIL,EAAa,EACf,QAASxI,EAAIsI,EAAYtI,GAAKuI,EAAavI,IACzC8I,EAAe,CAAC,GAAK,KAAK,KAAK9I,CAAC,EAAEwI,EAAa,CAAC,EAAE,OAAS,KAAK,KAAKxI,CAAC,EAAEwI,CAAU,EAAE,OAAS,EAGjG,GAAID,EAAc,KAAK,KAAK,OAAS,EACnC,QAASvI,EAAIwI,EAAYxI,GAAKyI,EAAazI,IACzC8I,EAAe,CAAC,GAAK,KAAK,KAAKP,EAAc,CAAC,EAAEvI,CAAC,EAAE,OAAS,KAAK,KAAKuI,CAAW,EAAEvI,CAAC,EAAE,OAAS,EAGnG,GAAIyI,EAAc,KAAK,KAAK,CAAC,EAAE,OAAS,EACtC,QAASzI,EAAIsI,EAAYtI,GAAKuI,EAAavI,IACzC8I,EAAe,CAAC,GAAK,KAAK,KAAK9I,CAAC,EAAEyI,EAAc,CAAC,EAAE,OAAS,KAAK,KAAKzI,CAAC,EAAEyI,CAAW,EAAE,OAAS,EAGnG,GAAIH,EAAa,EACf,QAAStI,EAAIwI,EAAYxI,GAAKyI,EAAazI,IACzC8I,EAAe,CAAC,GAAK,KAAK,KAAKR,EAAa,CAAC,EAAEtI,CAAC,EAAE,OAAS,KAAK,KAAKsI,CAAU,EAAEtI,CAAC,EAAE,OAAS,EAMjG,QAHIiH,EAAMxG,EAAQ,UACdsI,EACAC,EACK9C,EAAI,EAAGA,EAAI4C,EAAe,OAAQ5C,IACrC4C,EAAe5C,CAAC,EAAIe,GACtBA,EAAM6B,EAAe5C,CAAC,EACtB6C,EAAW,EACXC,EAAW9C,GACF4C,EAAe5C,CAAC,GAAKe,GAC9B8B,IAIJ,GAAIA,GAAY,GAAK9B,GAAO,EACtB6B,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EAC3EX,EAAoB,EACXW,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EAClFX,EAAoB,EACXW,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EAClFX,EAAoB,EACXW,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,IAClFX,EAAoB,WAEbY,GAAY,GAAK9B,GAAO,EAAG,CACpC,IAAIgC,EAAS,KAAK,MAAM,KAAK,OAAO,EAAI,CAAC,EACrCH,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EAE7CG,GAAU,EACZd,EAAoB,EAEpBA,EAAoB,EAEbW,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EACpDG,GAAU,EACZd,EAAoB,EAEpBA,EAAoB,EAEbW,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EACpDG,GAAU,EACZd,EAAoB,EAEpBA,EAAoB,EAEbW,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EACpDG,GAAU,EACZd,EAAoB,EAEpBA,EAAoB,EAEbW,EAAe,CAAC,GAAK,GAAKA,EAAe,CAAC,GAAK,EACpDG,GAAU,EACZd,EAAoB,EAEpBA,EAAoB,EAGlBc,GAAU,EACZd,EAAoB,EAEpBA,EAAoB,CAG1B,SAAWY,GAAY,GAAK9B,GAAO,EAAG,CACpC,IAAIgC,EAAS,KAAK,MAAM,KAAK,OAAO,EAAI,CAAC,EACzCd,EAAoBc,CACtB,MACEd,EAAoBa,EAGlBb,GAAqB,EACvBE,EAAW,UAAUD,EAAc,WAAW,EAAGA,EAAc,WAAW,EAAIA,EAAc,UAAU,EAAI,EAAI/J,EAAkB,oBAAsBgK,EAAW,UAAU,EAAI,CAAC,EACvKF,GAAqB,EAC9BE,EAAW,UAAUD,EAAc,WAAW,EAAIA,EAAc,SAAS,EAAI,EAAI/J,EAAkB,oBAAsBgK,EAAW,SAAS,EAAI,EAAGD,EAAc,WAAW,CAAC,EACrKD,GAAqB,EAC9BE,EAAW,UAAUD,EAAc,WAAW,EAAGA,EAAc,WAAW,EAAIA,EAAc,UAAU,EAAI,EAAI/J,EAAkB,oBAAsBgK,EAAW,UAAU,EAAI,CAAC,EAEhLA,EAAW,UAAUD,EAAc,WAAW,EAAIA,EAAc,SAAS,EAAI,EAAI/J,EAAkB,oBAAsBgK,EAAW,SAAS,EAAI,EAAGD,EAAc,WAAW,CAAC,CAElL,EAEA7K,EAAO,QAAUqD,CAEX,EAEC,SAASrD,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIoL,EAAW,CAAC,EAEhBA,EAAS,WAAapL,EAAoB,CAAC,EAC3CoL,EAAS,cAAgBpL,EAAoB,CAAC,EAC9CoL,EAAS,SAAWpL,EAAoB,CAAC,EACzCoL,EAAS,UAAYpL,EAAoB,CAAC,EAC1CoL,EAAS,iBAAmBpL,EAAoB,CAAC,EACjDoL,EAAS,WAAapL,EAAoB,CAAC,EAC3CoL,EAAS,SAAWpL,EAAoB,CAAC,EAEzCP,EAAO,QAAU2L,CAEX,CACG,CAAC,CACV,CAAC,ICt6CD,IAAAC,GAAAC,GAAA,CAAAC,GAAAC,KAAA,cAACC,EAAA,SAA0CC,EAAMC,EAAS,CACtD,OAAOJ,IAAY,UAAY,OAAOC,IAAW,SACnDA,GAAO,QAAUG,EAAQ,IAAoB,EACtC,OAAO,QAAW,YAAc,OAAO,IAC9C,OAAO,CAAC,WAAW,EAAGA,CAAO,EACtB,OAAOJ,IAAY,SAC1BA,GAAQ,qBAA0BI,EAAQ,IAAoB,EAE9DD,EAAK,qBAA0BC,EAAQD,EAAK,QAAW,CACzD,EATC,oCASEH,GAAM,SAASK,EAA+B,CACjD,OAAiB,SAASC,EAAS,CAEzB,IAAIC,EAAmB,CAAC,EAGxB,SAASC,EAAoBC,EAAU,CAGtC,GAAGF,EAAiBE,CAAQ,EAC3B,OAAOF,EAAiBE,CAAQ,EAAE,QAGnC,IAAIR,EAASM,EAAiBE,CAAQ,EAAI,CACzC,EAAGA,EACH,EAAG,GACH,QAAS,CAAC,CACX,EAGA,OAAAH,EAAQG,CAAQ,EAAE,KAAKR,EAAO,QAASA,EAAQA,EAAO,QAASO,CAAmB,EAGlFP,EAAO,EAAI,GAGJA,EAAO,OACf,CArBS,OAAAC,EAAAM,EAAA,uBAyBTA,EAAoB,EAAIF,EAGxBE,EAAoB,EAAID,EAGxBC,EAAoB,EAAI,SAASE,EAAO,CAAE,OAAOA,CAAO,EAGxDF,EAAoB,EAAI,SAASR,EAASW,EAAMC,EAAQ,CACnDJ,EAAoB,EAAER,EAASW,CAAI,GACtC,OAAO,eAAeX,EAASW,EAAM,CACpC,aAAc,GACd,WAAY,GACZ,IAAKC,CACN,CAAC,CAEH,EAGAJ,EAAoB,EAAI,SAASP,EAAQ,CACxC,IAAIW,EAASX,GAAUA,EAAO,WAC7BC,EAAA,UAAsB,CAAE,OAAOD,EAAO,OAAY,EAAlD,cACAC,EAAA,UAA4B,CAAE,OAAOD,CAAQ,EAA7C,oBACD,OAAAO,EAAoB,EAAEI,EAAQ,IAAKA,CAAM,EAClCA,CACR,EAGAJ,EAAoB,EAAI,SAASK,EAAQC,EAAU,CAAE,OAAO,OAAO,UAAU,eAAe,KAAKD,EAAQC,CAAQ,CAAG,EAGpHN,EAAoB,EAAI,GAGjBA,EAAoBA,EAAoB,EAAI,CAAC,CACrD,EAEC,CAEH,SAASP,EAAQD,EAAS,CAEjCC,EAAO,QAAUI,CAEX,EAEC,SAASJ,EAAQD,EAASQ,EAAqB,CAEtD,aAGA,IAAIO,EAAkBP,EAAoB,CAAC,EAAE,WAAW,gBACpDQ,EAAoBR,EAAoB,CAAC,EAAE,WAAW,kBACtDS,EAAgBT,EAAoB,CAAC,EAAE,cACvCU,EAAaV,EAAoB,CAAC,EAAE,WACpCW,EAAWX,EAAoB,CAAC,EAAE,SAClCY,EAASZ,EAAoB,CAAC,EAAE,WAAW,OAC3Ca,EAAab,EAAoB,CAAC,EAAE,WAAW,WAE/Cc,EAAW,CAEb,MAAOpB,EAAA,UAAiB,CAAC,EAAlB,SAEP,KAAMA,EAAA,UAAgB,CAAC,EAAjB,QAKN,QAAS,UAET,4BAA6B,GAE7B,QAAS,GAET,IAAK,GAEL,QAAS,GAET,UAAW,GAEX,cAAe,KAEf,gBAAiB,GAEjB,eAAgB,IAEhB,cAAe,GAEf,QAAS,IAET,QAAS,KAET,KAAM,GAEN,QAAS,MAET,kBAAmB,IAEnB,sBAAuB,GAEvB,wBAAyB,GAEzB,qBAAsB,IAEtB,gBAAiB,EAEjB,aAAc,IAEd,2BAA4B,EAC9B,EAEA,SAASqB,EAAOD,EAAUE,EAAS,CACjC,IAAIC,EAAM,CAAC,EAEX,QAASC,KAAKJ,EACZG,EAAIC,CAAC,EAAIJ,EAASI,CAAC,EAGrB,QAASA,KAAKF,EACZC,EAAIC,CAAC,EAAIF,EAAQE,CAAC,EAGpB,OAAOD,CACT,CAZSvB,EAAAqB,EAAA,UAcT,SAASI,EAAYC,EAAU,CAC7B,KAAK,QAAUL,EAAOD,EAAUM,CAAQ,EACxCC,EAAe,KAAK,OAAO,CAC7B,CAHS3B,EAAAyB,EAAA,eAKT,IAAIE,EAAiB3B,EAAA,SAAwBsB,EAAS,CAChDA,EAAQ,eAAiB,OAAMP,EAAc,2BAA6BD,EAAkB,2BAA6BQ,EAAQ,eACjIA,EAAQ,iBAAmB,OAAMP,EAAc,oBAAsBD,EAAkB,oBAAsBQ,EAAQ,iBACrHA,EAAQ,gBAAkB,OAAMP,EAAc,wBAA0BD,EAAkB,wBAA0BQ,EAAQ,gBAC5HA,EAAQ,eAAiB,OAAMP,EAAc,mCAAqCD,EAAkB,mCAAqCQ,EAAQ,eACjJA,EAAQ,SAAW,OAAMP,EAAc,yBAA2BD,EAAkB,yBAA2BQ,EAAQ,SACvHA,EAAQ,SAAW,OAAMP,EAAc,eAAiBD,EAAkB,eAAiBQ,EAAQ,SACnGA,EAAQ,cAAgB,OAAMP,EAAc,6BAA+BD,EAAkB,6BAA+BQ,EAAQ,cACpIA,EAAQ,iBAAmB,OAAMP,EAAc,kCAAoCD,EAAkB,kCAAoCQ,EAAQ,iBACjJA,EAAQ,sBAAwB,OAAMP,EAAc,sCAAwCD,EAAkB,sCAAwCQ,EAAQ,sBAC9JA,EAAQ,4BAA8B,OAAMP,EAAc,mCAAqCD,EAAkB,mCAAqCQ,EAAQ,4BAE9JA,EAAQ,SAAW,QAAST,EAAgB,QAAU,EAAWS,EAAQ,SAAW,QAAST,EAAgB,QAAU,EAAOA,EAAgB,QAAU,EAE5JE,EAAc,+BAAiCD,EAAkB,+BAAiCD,EAAgB,+BAAiCS,EAAQ,4BAC3JP,EAAc,oBAAsBD,EAAkB,oBAAsBD,EAAgB,oBAAsB,CAACS,EAAQ,UAC3HP,EAAc,QAAUD,EAAkB,QAAUD,EAAgB,QAAUS,EAAQ,QACtFP,EAAc,KAAOO,EAAQ,KAC7BP,EAAc,wBAA0B,OAAOO,EAAQ,uBAA0B,WAAaA,EAAQ,sBAAsB,KAAK,EAAIA,EAAQ,sBAC7IP,EAAc,0BAA4B,OAAOO,EAAQ,yBAA4B,WAAaA,EAAQ,wBAAwB,KAAK,EAAIA,EAAQ,uBACrJ,EApBqB,kBAsBrBG,EAAY,UAAU,IAAM,UAAY,CACtC,IAAIG,EACAC,EACAP,EAAU,KAAK,QACfQ,EAAY,KAAK,UAAY,CAAC,EAC9BC,EAAS,KAAK,OAAS,IAAIf,EAC3BgB,EAAO,KAEXA,EAAK,QAAU,GAEf,KAAK,GAAK,KAAK,QAAQ,GAEvB,KAAK,GAAG,QAAQ,CAAE,KAAM,cAAe,OAAQ,IAAK,CAAC,EAErD,IAAIC,EAAKF,EAAO,gBAAgB,EAChC,KAAK,GAAKE,EAEV,IAAIC,EAAQ,KAAK,QAAQ,KAAK,MAAM,EAChCC,EAAQ,KAAK,QAAQ,KAAK,MAAM,EAEpC,KAAK,KAAOF,EAAG,QAAQ,EACvB,KAAK,oBAAoB,KAAK,KAAM,KAAK,gBAAgBC,CAAK,EAAGH,CAAM,EAEvE,QAASP,EAAI,EAAGA,EAAIW,EAAM,OAAQX,IAAK,CACrC,IAAIY,EAAOD,EAAMX,CAAC,EACda,EAAa,KAAK,UAAUD,EAAK,KAAK,QAAQ,CAAC,EAC/CE,EAAa,KAAK,UAAUF,EAAK,KAAK,QAAQ,CAAC,EACnD,GAAIC,IAAeC,GAAcD,EAAW,gBAAgBC,CAAU,EAAE,QAAU,EAAG,CACnF,IAAIC,EAAKN,EAAG,IAAIF,EAAO,QAAQ,EAAGM,EAAYC,CAAU,EACxDC,EAAG,GAAKH,EAAK,GAAG,CAClB,CACF,CAEA,IAAII,EAAexC,EAAA,SAAsByC,EAAKjB,EAAG,CAC3C,OAAOiB,GAAQ,WACjBA,EAAMjB,GAER,IAAIkB,EAAQD,EAAI,KAAK,IAAI,EACrBE,EAAQX,EAAK,UAAUU,CAAK,EAEhC,MAAO,CACL,EAAGC,EAAM,QAAQ,EAAE,WAAW,EAC9B,EAAGA,EAAM,QAAQ,EAAE,WAAW,CAChC,CACF,EAXmB,gBAgBfC,EAAkB5C,EAAA,SAAS4C,GAAkB,CAiB/C,QAfIC,EAAkB7C,EAAA,UAA2B,CAC3CsB,EAAQ,KACVA,EAAQ,GAAG,IAAIA,EAAQ,KAAMA,EAAQ,OAAO,EAGzCM,IACHA,EAAQ,GACRI,EAAK,GAAG,IAAI,cAAeV,EAAQ,KAAK,EACxCU,EAAK,GAAG,QAAQ,CAAE,KAAM,cAAe,OAAQA,CAAK,CAAC,EAEzD,EAVsB,mBAYlBc,EAAgBd,EAAK,QAAQ,QAC7Be,EAEKvB,EAAI,EAAGA,EAAIsB,GAAiB,CAACC,EAAQvB,IAC5CuB,EAASf,EAAK,SAAWA,EAAK,OAAO,KAAK,EAI5C,GAAIe,EAAQ,CAENhB,EAAO,mBAAmB,GAAK,CAACA,EAAO,aACzCA,EAAO,aAAa,EAIlBA,EAAO,kBACTA,EAAO,iBAAiB,EAG1BA,EAAO,iBAAmB,GAE1BC,EAAK,QAAQ,KAAK,MAAM,EAAE,UAAUQ,CAAY,EAEhDK,EAAgB,EAGhBb,EAAK,GAAG,IAAI,aAAcA,EAAK,QAAQ,IAAI,EAC3CA,EAAK,GAAG,QAAQ,CAAE,KAAM,aAAc,OAAQA,CAAK,CAAC,EAEhDH,GACF,qBAAqBA,CAAO,EAG9BD,EAAQ,GACR,MACF,CAEA,IAAIoB,EAAgBhB,EAAK,OAAO,iBAAiB,EAIjDV,EAAQ,KAAK,MAAM,EAAE,UAAU,SAAUmB,EAAKjB,EAAG,CAK/C,GAJI,OAAOiB,GAAQ,WACjBA,EAAMjB,GAGJ,CAACiB,EAAI,SAAS,EAAG,CAKnB,QAJIC,EAAQD,EAAI,GAAG,EACfQ,EAAQD,EAAcN,CAAK,EAC3BQ,EAAOT,EAEJQ,GAAS,OACdA,EAAQD,EAAcE,EAAK,KAAK,QAAQ,CAAC,GAAKF,EAAc,iBAAmBE,EAAK,KAAK,QAAQ,CAAC,EAClGF,EAAcN,CAAK,EAAIO,EACvBC,EAAOA,EAAK,OAAO,EAAE,CAAC,EAClBA,GAAQ,OAAZ,CAIF,OAAID,GAAS,KACJ,CACL,EAAGA,EAAM,EACT,EAAGA,EAAM,CACX,EAEO,CACL,EAAGR,EAAI,SAAS,GAAG,EACnB,EAAGA,EAAI,SAAS,GAAG,CACrB,CAEJ,CACF,CAAC,EAEDI,EAAgB,EAEhBhB,EAAU,sBAAsBe,CAAe,CACjD,EA1FsB,mBA+FtB,OAAAb,EAAO,YAAY,gBAAiB,UAAY,CAC1CC,EAAK,QAAQ,UAAY,WAC3BH,EAAU,sBAAsBe,CAAe,EAEnD,CAAC,EAEDb,EAAO,UAAU,EAKb,KAAK,QAAQ,UAAY,WAC3BC,EAAK,QAAQ,KAAK,MAAM,EAAE,IAAI,SAAS,EAAE,gBAAgBA,EAAMA,EAAK,QAASQ,CAAY,EACzFZ,EAAQ,IAGH,IACT,EAGAH,EAAY,UAAU,gBAAkB,SAAUS,EAAO,CAEvD,QADIiB,EAAW,CAAC,EACP3B,EAAI,EAAGA,EAAIU,EAAM,OAAQV,IAChC2B,EAASjB,EAAMV,CAAC,EAAE,GAAG,CAAC,EAAI,GAE5B,IAAI4B,EAAQlB,EAAM,OAAO,SAAUO,EAAKjB,EAAG,CACrC,OAAOiB,GAAQ,WACjBA,EAAMjB,GAGR,QADI6B,EAASZ,EAAI,OAAO,EAAE,CAAC,EACpBY,GAAU,MAAM,CACrB,GAAIF,EAASE,EAAO,GAAG,CAAC,EACtB,MAAO,GAETA,EAASA,EAAO,OAAO,EAAE,CAAC,CAC5B,CACA,MAAO,EACT,CAAC,EAED,OAAOD,CACT,EAEA3B,EAAY,UAAU,oBAAsB,SAAU4B,EAAQC,EAAUvB,EAAQ,CAE9E,QADIwB,EAAOD,EAAS,OACX9B,EAAI,EAAGA,EAAI+B,EAAM/B,IAAK,CAC7B,IAAIgC,EAAWF,EAAS9B,CAAC,EACrBiC,EAAuBD,EAAS,SAAS,EACzCE,EAEAC,EAAaH,EAAS,iBAAiB,CACzC,4BAA6B,KAAK,QAAQ,2BAC5C,CAAC,EAgBD,GAdIA,EAAS,WAAW,GAAK,MAAQA,EAAS,YAAY,GAAK,KAC7DE,EAAUL,EAAO,IAAI,IAAIpC,EAASc,EAAO,aAAc,IAAIb,EAAOsC,EAAS,SAAS,GAAG,EAAIG,EAAW,EAAI,EAAGH,EAAS,SAAS,GAAG,EAAIG,EAAW,EAAI,CAAC,EAAG,IAAIxC,EAAW,WAAWwC,EAAW,CAAC,EAAG,WAAWA,EAAW,CAAC,CAAC,CAAC,CAAC,EAE5ND,EAAUL,EAAO,IAAI,IAAIpC,EAAS,KAAK,YAAY,CAAC,EAGtDyC,EAAQ,GAAKF,EAAS,KAAK,IAAI,EAE/BE,EAAQ,YAAc,SAASF,EAAS,IAAI,SAAS,CAAC,EACtDE,EAAQ,WAAa,SAASF,EAAS,IAAI,SAAS,CAAC,EACrDE,EAAQ,aAAe,SAASF,EAAS,IAAI,SAAS,CAAC,EACvDE,EAAQ,cAAgB,SAASF,EAAS,IAAI,SAAS,CAAC,EAGpD,KAAK,QAAQ,6BACXA,EAAS,SAAS,EAAG,CACvB,IAAII,EAAaJ,EAAS,YAAY,CAAE,cAAe,GAAM,aAAc,EAAM,CAAC,EAAE,EAChFK,EAAcL,EAAS,YAAY,CAAE,cAAe,GAAM,aAAc,EAAM,CAAC,EAAE,EACjFM,EAAWN,EAAS,IAAI,aAAa,EACzCE,EAAQ,WAAaE,EACrBF,EAAQ,YAAcG,EACtBH,EAAQ,SAAWI,CACrB,CAcF,GAVA,KAAK,UAAUN,EAAS,KAAK,IAAI,CAAC,EAAIE,EAElC,MAAMA,EAAQ,KAAK,CAAC,IACtBA,EAAQ,KAAK,EAAI,GAGf,MAAMA,EAAQ,KAAK,CAAC,IACtBA,EAAQ,KAAK,EAAI,GAGfD,GAAwB,MAAQA,EAAqB,OAAS,EAAG,CACnE,IAAIM,EACJA,EAAchC,EAAO,gBAAgB,EAAE,IAAIA,EAAO,SAAS,EAAG2B,CAAO,EACrE,KAAK,oBAAoBK,EAAaN,EAAsB1B,CAAM,CACpE,CACF,CACF,EAKAN,EAAY,UAAU,KAAO,UAAY,CACvC,YAAK,QAAU,GAER,IACT,EAEA,IAAIuC,EAAWhE,EAAA,SAAkBiE,EAAW,CAG1CA,EAAU,SAAU,eAAgBxC,CAAW,CACjD,EAJe,YAOX,OAAO,UAAc,KACvBuC,EAAS,SAAS,EAGpBjE,EAAO,QAAUiE,CAEX,CACG,CAAC,CACV,CAAC,IChYD,IAAIE,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,EAAE,OAAOE,IAAIJ,EAAEE,EAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EACrRrB,EAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,QAAU,EAAE,WAAa,EAAE,UAAY,EAAE,GAAK,EAAE,QAAU,EAAE,SAAW,EAAE,KAAO,GAAG,IAAM,GAAG,UAAY,GAAG,UAAY,GAAG,KAAO,GAAG,KAAO,GAAG,MAAQ,GAAG,WAAa,GAAG,cAAgB,GAAG,YAAc,GAAG,WAAa,GAAG,UAAY,GAAG,QAAU,GAAG,QAAU,EAAE,KAAO,CAAC,EAC5S,WAAY,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,GAAG,MAAM,GAAG,YAAY,GAAG,OAAO,GAAG,QAAQ,GAAG,cAAc,GAAG,aAAa,GAAG,YAAY,GAAG,SAAS,EACjK,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EACpM,cAAeA,EAAA,SAAmBoB,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,EAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GAAG,IAAK,GACZ,OAAOD,EAER,IAAK,GACLA,EAAG,UAAU,EAAE,MAAM,UAAU,EAC/B,MACA,IAAK,GACLA,EAAG,UAAU,EAAE,MAAM,WAAW,EAChC,MACA,IAAK,IACLA,EAAG,UAAU,EAAE,MAAM,WAAW,EAChC,MACA,IAAK,IACLA,EAAG,UAAU,EAAE,MAAM,YAAY,EACjC,MACA,IAAK,IACJA,EAAG,UAAU,EAAE,KAAK,SAASE,EAAGE,CAAE,EAAE,EAAE,EAAEJ,EAAG,QAAQE,EAAGE,EAAG,CAAC,EAAE,OAAQF,EAAGE,CAAE,EAAE,GAAIF,EAAGE,CAAE,EAAE,MAAOF,EAAGE,CAAE,EAAE,IAAI,EACzG,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,SAASE,EAAGE,CAAE,CAAC,EAAEJ,EAAG,aAAa,CAAC,KAAME,EAAGE,CAAE,CAAC,CAAC,EACrE,MACA,IAAK,IAAI,IAAK,IACbJ,EAAG,aAAa,CAAC,MAAOE,EAAGE,CAAE,CAAC,CAAC,EAChC,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,WAAW,EACjC,MACA,IAAK,IACJA,EAAG,UAAU,EAAE,MAAM,SAASE,EAAGE,CAAE,EAAE,EAAE,EAAEJ,EAAG,QAAQ,EAAGE,EAAGE,CAAE,EAAE,GAAIF,EAAGE,CAAE,EAAE,MAAOF,EAAGE,CAAE,EAAE,IAAI,EAC5F,MACA,IAAK,IACJJ,EAAG,aAAa,CAAC,KAAME,EAAGE,CAAE,CAAC,CAAC,EAC/B,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,gBAAiBE,EAAGE,EAAG,CAAC,CAAC,EAAG,KAAK,EAAI,CAAE,GAAIF,EAAGE,EAAG,CAAC,EAAG,MAAOF,EAAGE,EAAG,CAAC,EAAG,KAAMJ,EAAG,QAAQE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,CAAE,EAC/H,MACA,IAAK,IACJ,KAAK,EAAI,CAAE,GAAIF,EAAGE,CAAE,EAAG,MAAOF,EAAGE,CAAE,EAAG,KAAMJ,EAAG,SAAS,OAAQ,EACjE,MACA,IAAK,IACJA,EAAG,UAAU,EAAE,MAAM,gBAAiBE,EAAGE,EAAG,CAAC,CAAC,EAAG,KAAK,EAAI,CAAE,GAAIF,EAAGE,EAAG,CAAC,EAAG,MAAOF,EAAGE,EAAG,CAAC,EAAG,KAAMJ,EAAG,QAAQE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,CAAE,EAC/H,KACA,CACA,EAhDe,aAiDf,MAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAEvB,CAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAG,EAAE,CAAC,EAAEC,EAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,CAAG,EAAEX,EAAEY,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAEN,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEL,EAAI,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEE,EAAI,EAAEC,EAAI,GAAG,GAAG,GAAGC,CAAG,EAAEf,EAAEgB,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAGN,EAAI,GAAGC,CAAG,CAAC,EAAEX,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAEH,EAAI,EAAEC,EAAI,GAAG,GAAG,GAAGC,CAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAET,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,CAAG,EAAEX,EAAEiB,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,EAAEC,EAAI,GAAGC,CAAG,CAAC,EAAEnB,EAAEoB,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEpB,EAAEoB,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEpB,EAAEoB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEiB,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,EAAEC,EAAI,GAAGC,CAAG,CAAC,EAAEnB,EAAEoB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpB,EAAEoB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEpB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEhB,EAAEgB,EAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAC54B,eAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAChC,WAAYf,EAAA,SAAqB4B,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAO9B,EAAA,SAAe+B,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOjB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGiB,EAAa,EAAGC,EAAS,EAAGC,EAAM,EAClKC,EAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAAS1C,KAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,CAAC,IAC/C0C,EAAY,GAAG1C,CAAC,EAAI,KAAK,GAAGA,CAAC,GAGrCyC,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,EAAQF,EAAM,OAClBN,EAAO,KAAKQ,CAAK,EACjB,IAAIC,EAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,EAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJS/C,EAAA8C,EAAA,YAKD,SAASE,GAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,EACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXajD,EAAAgD,EAAA,OAajB,QADIE,EAAQC,EAAgBC,EAAOC,EAAQC,GAAGC,GAAGC,EAAQ,CAAC,EAAGC,GAAGC,EAAKC,GAAUC,KAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,EAAI,GAEjBK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,GAAS,GACbD,GAAW,CAAC,EACZ,IAAKH,MAAKpB,EAAMe,CAAK,EACb,KAAK,WAAWK,EAAC,GAAKA,GAAIlB,GAC1BqB,GAAS,KAAK,IAAO,KAAK,WAAWH,EAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,GAAS,wBAA0BvC,EAAW,GAAK;AAAA,EAAQoB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,GAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0BvC,EAAW,GAAK,iBAAmB4B,GAAUV,EAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,GAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,EACL,SAAUgB,EACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,GASDD,EAASC,EACTA,EAAiB,OATjB9B,EAASqB,EAAM,OACftB,EAASsB,EAAM,OACfpB,EAAWoB,EAAM,SACjBE,EAAQF,EAAM,OACVJ,EAAa,GACbA,KAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,IACAW,EAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,GAAI,KAAK,cAAc,MAAMC,EAAO,CAChCpC,EACAC,EACAC,EACAqB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,CAAI,CAAC,EACV,OAAOc,GAAM,IACb,OAAOA,GAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,GAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,EAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAW1C,EAAA,SAAoB4B,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAAS5B,EAAA,SAAU+B,EAAOR,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAM/B,EAAA,UAAY,CACV,IAAI8D,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAM9D,EAAA,SAAU8D,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAK1D,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAU,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUA,EAAA,UAAY,CACd,IAAIiE,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAcjE,EAAA,UAAY,CAClB,IAAIkE,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAalE,EAAA,UAAY,CACjB,IAAImE,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAWpE,EAAA,SAASqE,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAAShD,KAAKsE,EACV,KAAKtE,CAAC,EAAIsE,EAAOtE,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIiD,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAIjD,EAAA,UAAgB,CACZ,IAAIuD,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAMvD,EAAA,SAAgB4E,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAAS5E,EAAA,UAAqB,CACtB,IAAI+C,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAc/C,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmB+C,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAU/C,EAAA,SAAoB4E,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAe5E,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBuB,EAAGsD,EAAIC,EAA0BC,EAAU,CAG7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GAAE,OAAAvD,EAAG,UAAU,EAAE,MAAM,gBAAgBsD,EAAI,MAAM,EAAU,EAChE,MACA,IAAK,GAAE,MAAO,GAEd,IAAK,GAAG,KAAK,MAAM,OAAO,EAC1B,MACA,IAAK,GAAG,YAAK,SAAS,EAAS,GAC/B,MACA,IAAK,GAAG,KAAK,SAAS,EACtB,MACA,IAAK,GAAGtD,EAAG,UAAU,EAAE,MAAM,YAAY,EAAE,KAAK,MAAM,MAAM,EAC5D,MACA,IAAK,GAAE,OAAAA,EAAG,UAAU,EAAE,MAAM,WAAW,EAAS,EAChD,MACA,IAAK,GAAE,MAAO,GAEd,IAAK,GAAG,MAAO,IAEf,IAAK,GAAEA,EAAG,UAAU,EAAE,MAAM,UAAU,EAAE,KAAK,SAAS,EACtD,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,gBAAgB,EAAG,KAAK,MAAM,MAAM,EAAS,GAC3E,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,OAAO,EAAG,KAAK,MAAM,MAAM,EAAS,GAClE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,gBAAgB,EAAG,KAAK,MAAM,MAAM,EAAS,GAC3E,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,YAAY,EAAG,KAAK,MAAM,MAAM,EAAS,GACvE,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAS,GACnC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAS,GACnC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAS,GACnC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAS,GACnC,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,KAAK,MAAM,OAAO,EAC3B,MACA,IAAK,IAAI,MAAO,aAEhB,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAIA,EAAG,UAAU,EAAE,MAAM,eAAe,EAAE,KAAK,MAAM,MAAM,EAChE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,eAAgBsD,EAAI,MAAM,EAAU,aAClE,MACA,IAAK,IAAG,KAAK,SAAS,EACtB,MACA,IAAK,IAAG,YAAK,SAAS,EAAEtD,EAAG,UAAU,EAAE,MAAM,aAAa,EAAS,YACnE,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,YAAY,EAAS,YAClE,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,eAAesD,EAAI,MAAM,EAAS,YAC/E,MACA,IAAK,IAAG,YAAK,SAAS,EAAEtD,EAAG,UAAU,EAAE,MAAM,aAAa,EAAS,YACnE,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,aAAa,EAAS,YACnE,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,aAAa,EAAS,YACnE,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,aAAa,EAAS,YACnE,MACA,IAAK,IAAG,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,aAAa,EAAS,YACnE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,oBAAqBsD,EAAI,MAAM,EAAY,GACzE,MACA,IAAK,IAAI,OAAAtD,EAAG,UAAU,EAAE,MAAM,oBAAqBsD,EAAI,MAAM,EAAY,GACzE,KACA,CACA,EAhFe,aAiFf,MAAO,CAAC,gBAAgB,kBAAkB,YAAY,WAAW,WAAW,iBAAiB,kBAAkB,cAAc,eAAe,WAAW,YAAY,YAAY,aAAa,WAAW,aAAa,aAAa,WAAW,WAAW,cAAc,yBAAyB,UAAU,eAAe,eAAe,eAAe,YAAY,cAAc,YAAY,eAAe,aAAa,aAAa,aAAa,YAAY,YAAY,aAAa,WAAW,qBAAqB,kBAAkB,EACxgB,WAAY,CAAC,MAAQ,CAAC,MAAQ,CAAC,EAAE,CAAC,EAAE,UAAY,EAAK,EAAE,KAAO,CAAC,MAAQ,CAAC,EAAE,CAAC,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,KAAO,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,KAAO,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CACnV,EACA,OAAOnC,CACP,EAAG,EACH5C,EAAO,MAAQ4C,EACf,SAASuC,GAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAAjF,EAAAiF,EAAA,UAGTA,EAAO,UAAYnF,EAAOA,EAAO,OAASmF,EACnC,IAAIA,CACX,EAAG,EACFnF,GAAO,OAASA,GAEhB,IAAOoF,GAAQC,GCtrBhB,IAAIC,EAAuB,CAAC,EACxBC,GAAM,EACNC,GAAsC,CAAC,EAErCC,GAAQC,EAAA,IAAM,CAClBJ,EAAQ,CAAC,EACTC,GAAM,EACNC,GAAW,CAAC,CACd,EAJc,SAMRG,GAAYD,EAAA,SAAUE,EAAe,CACzC,QAASC,EAAIP,EAAM,OAAS,EAAGO,GAAK,EAAGA,IACrC,GAAIP,EAAMO,CAAC,EAAE,MAAQD,EACnB,OAAON,EAAMO,CAAC,EAIlB,OAAO,IACT,EARkB,aAUZC,GAAaJ,EAAA,IACVJ,EAAM,OAAS,EAAIA,EAAM,CAAC,EAAI,KADpB,cAIbS,GAAUL,EAAA,CAACE,EAAeI,EAAYC,EAAeC,IAAiB,CAC1EC,EAAI,KAAK,UAAWP,EAAOI,EAAIC,EAAOC,CAAI,EAC1C,IAAME,EAAOC,GAAU,EACnBC,EAAkBF,EAAK,SAAS,SAAWG,EAAc,QAAQ,QACrE,OAAQL,EAAM,CACZ,KAAKM,EAAS,aACd,KAAKA,EAAS,KACd,KAAKA,EAAS,QACZF,GAAW,CACf,CAEA,IAAMG,EAAO,CACX,GAAIlB,KACJ,OAAQmB,GAAaV,EAAII,CAAI,EAC7B,MAAAR,EACA,MAAOc,GAAaT,EAAOG,CAAI,EAC/B,KAAAF,EACA,SAAU,CAAC,EACX,MAAOE,EAAK,SAAS,cAAgBG,EAAc,QAAQ,aAC3D,QAAAD,CACF,EAEMK,EAAShB,GAAUC,CAAK,EAC9B,GAAIe,EACFA,EAAO,SAAS,KAAKF,CAAI,EAEzBnB,EAAM,KAAKmB,CAAI,UAEXnB,EAAM,SAAW,EAEnBA,EAAM,KAAKmB,CAAI,MAGf,OAAM,IAAI,MACR,8DAAgEA,EAAK,MAAQ,IAC/E,CAGN,EAtCgB,WAwCVD,EAAW,CACf,QAAS,EACT,UAAW,EACX,aAAc,EACd,KAAM,EACN,OAAQ,EACR,MAAO,EACP,KAAM,EACN,QAAS,CACX,EAEMI,GAAUlB,EAAA,CAACmB,EAAkBC,IAA2B,CAE5D,OADAX,EAAI,MAAM,cAAeU,EAAUC,CAAM,EACjCD,EAAU,CAChB,IAAK,IACH,OAAOL,EAAS,KAClB,IAAK,IACH,OAAOM,IAAW,IAAMN,EAAS,aAAeA,EAAS,MAC3D,IAAK,KACH,OAAOA,EAAS,OAClB,IAAK,IACH,OAAOA,EAAS,MAClB,IAAK,KACH,OAAOA,EAAS,KAClB,IAAK,KACH,OAAOA,EAAS,QAClB,QACE,OAAOA,EAAS,OACpB,CACF,EAlBgB,WAoBVO,GAAkBrB,EAAA,CAACM,EAAYgB,IAAuB,CAC1DxB,GAASQ,CAAE,EAAIgB,CACjB,EAFwB,mBAIlBC,GAAevB,EAACwB,GAAmD,CACvE,GAAI,CAACA,EACH,OAEF,IAAMC,EAASd,GAAU,EACnBI,EAAOnB,EAAMA,EAAM,OAAS,CAAC,EAC/B4B,EAAW,OACbT,EAAK,KAAOC,GAAaQ,EAAW,KAAMC,CAAM,GAE9CD,EAAW,QACbT,EAAK,MAAQC,GAAaQ,EAAW,MAAOC,CAAM,EAEtD,EAZqB,gBAcfC,GAAW1B,EAACQ,GAAiB,CACjC,OAAQA,EAAM,CACZ,KAAKM,EAAS,QACZ,MAAO,YACT,KAAKA,EAAS,KACZ,MAAO,OACT,KAAKA,EAAS,aACZ,MAAO,eACT,KAAKA,EAAS,OACZ,MAAO,SACT,KAAKA,EAAS,MACZ,MAAO,QACT,KAAKA,EAAS,KACZ,MAAO,OACT,KAAKA,EAAS,QACZ,MAAO,SACT,QACE,MAAO,WACX,CACF,EAnBiB,YAsBXa,GAAY3B,EAAA,IAAMS,EAAN,aACZmB,GAAiB5B,EAACM,GAAeR,GAASQ,CAAE,EAA3B,kBAEjBuB,GAAK,CACT,MAAA9B,GACA,QAAAM,GACA,WAAAD,GACA,SAAAU,EACA,QAAAI,GACA,gBAAAG,GACA,aAAAE,GACA,SAAAG,GACA,UAAAC,GACA,eAAAC,EACF,EAEOE,GAAQD,GC5Jf,IAAAE,GAAwB,WCIxB,IAAMC,GAAe,GASfC,GAA4BC,EAAA,SAAUC,EAAIC,EAAMC,EAAMC,EAAS,CAEnEF,EACG,OAAO,MAAM,EACb,KAAK,KAAM,QAAUC,EAAK,EAAE,EAC5B,KAAK,QAAS,iBAAmBF,EAAG,SAASE,EAAK,IAAI,CAAC,EACvD,KACC,IACA,MAAMA,EAAK,OAAS,CAAE,KAAK,CAACA,EAAK,OAAS,EAAI,CAAE,gBAC9CA,EAAK,MAAQ,EAAI,CACnB,cAAcA,EAAK,OAAS,CAAE,OAChC,EAEFD,EACG,OAAO,MAAM,EACb,KAAK,QAAS,aAAeE,CAAO,EACpC,KAAK,KAAM,CAAC,EACZ,KAAK,KAAMD,EAAK,MAAM,EACtB,KAAK,KAAMA,EAAK,KAAK,EACrB,KAAK,KAAMA,EAAK,MAAM,CAC3B,EApBkC,cAsB5BE,GAAyBL,EAAA,SAAUC,EAAIC,EAAMC,EAAM,CACvDD,EACG,OAAO,MAAM,EACb,KAAK,KAAM,QAAUC,EAAK,EAAE,EAC5B,KAAK,QAAS,iBAAmBF,EAAG,SAASE,EAAK,IAAI,CAAC,EACvD,KAAK,SAAUA,EAAK,MAAM,EAC1B,KAAK,QAASA,EAAK,KAAK,CAC7B,EAP+B,WASzBG,GAA0BN,EAAA,SAAUC,EAAIC,EAAMC,EAAM,CACxD,IAAMI,EAAIJ,EAAK,MACTK,EAAIL,EAAK,OACTM,EAAK,IAAOF,EACZG,EAAK,IAAOH,EACZI,EAAK,IAAOJ,EACZK,EAAK,GAAML,EACjBL,EACG,OAAO,MAAM,EACb,KAAK,KAAM,QAAUC,EAAK,EAAE,EAC5B,KAAK,QAAS,iBAAmBF,EAAG,SAASE,EAAK,IAAI,CAAC,EACvD,KACC,IACA,SAASM,CAAE,IAAIA,CAAE,UAAUF,EAAI,GAAI,IAAI,GAAKA,EAAI,EAAG;AAAA,SAChDI,CAAE,IAAIA,CAAE,UAAUJ,EAAI,EAAG,IAAI,GAAKA,EAAI,EAAG;AAAA,SACzCG,CAAE,IAAIA,CAAE,UAAUH,EAAI,GAAI,IAAI,EAAIA,EAAI,EAAG;AAAA;AAAA,SAEzCE,CAAE,IAAIA,CAAE,UAAUF,EAAI,GAAI,IAAI,EAAIC,EAAI,GAAI;AAAA,SAC1CI,CAAE,IAAIA,CAAE,UAAU,GAAKL,EAAI,GAAI,IAAI,EAAIC,EAAI,GAAI;AAAA;AAAA,SAE/CE,CAAE,IAAID,CAAE,UAAU,GAAKF,EAAI,GAAI,IAAIA,EAAI,GAAI;AAAA,SAC3CI,CAAE,IAAIA,CAAE,UAAU,GAAKJ,EAAI,EAAG;AAAA,SAC9BE,CAAE,IAAIA,CAAE,UAAU,GAAKF,EAAI,GAAI,IAAI,GAAKA,EAAI,GAAI;AAAA;AAAA,SAEhDE,CAAE,IAAIA,CAAE,UAAU,GAAKF,EAAI,EAAG,IAAI,GAAKC,EAAI,GAAI;AAAA,SAC/CI,CAAE,IAAIA,CAAE,UAAUL,EAAI,EAAG,IAAI,GAAKC,EAAI,GAAI;AAAA;AAAA,YAG/C,CACJ,EA7BgC,YA+B1BK,GAAyBb,EAAA,SAAUC,EAAIC,EAAMC,EAAM,CACvD,IAAMI,EAAIJ,EAAK,MACTK,EAAIL,EAAK,OACTW,EAAI,IAAOP,EACjBL,EACG,OAAO,MAAM,EACb,KAAK,KAAM,QAAUC,EAAK,EAAE,EAC5B,KAAK,QAAS,iBAAmBF,EAAG,SAASE,EAAK,IAAI,CAAC,EACvD,KACC,IACA,SAASW,CAAC,IAAIA,CAAC,UAAUP,EAAI,GAAI,IAAI,GAAKC,EAAI,EAAG;AAAA,SAC9CM,CAAC,IAAIA,CAAC,UAAUP,EAAI,GAAI;AAAA,SACxBO,CAAC,IAAIA,CAAC,UAAUP,EAAI,GAAI;AAAA,SACxBO,CAAC,IAAIA,CAAC,UAAUP,EAAI,GAAI,IAAI,EAAIC,EAAI,EAAG;AAAA;AAAA,SAEvCM,CAAC,IAAIA,CAAC,UAAUP,EAAI,GAAI,IAAI,EAAIC,EAAI,GAAI;AAAA,SACxCM,EAAI,EAAG,IAAIA,EAAI,EAAG,YAAe,EAAIN,EAAI,GAAI;AAAA,SAC7CM,CAAC,IAAIA,CAAC,UAAU,GAAKP,EAAI,GAAI,IAAI,EAAIC,EAAI,GAAI;AAAA;AAAA,SAE7CM,CAAC,IAAIA,CAAC,UAAU,GAAKP,EAAI,GAAI,IAAIC,EAAI,GAAI;AAAA,SACzCM,CAAC,IAAIA,CAAC,UAAU,GAAKP,EAAI,GAAI;AAAA,SAC7BO,CAAC,IAAIA,CAAC,UAAU,GAAKP,EAAI,GAAI;AAAA,SAC7BO,CAAC,IAAIA,CAAC,UAAU,GAAKP,EAAI,GAAI,IAAI,GAAKC,EAAI,GAAI;AAAA;AAAA,SAE9CM,CAAC,IAAIA,CAAC,UAAU,GAAKP,EAAI,EAAG,IAAI,GAAKC,EAAI,GAAI;AAAA,SAC7CM,EAAI,EAAG,IAAIA,EAAI,EAAG,YAAe,GAAKN,EAAI,GAAI;AAAA,SAC9CM,CAAC,IAAIA,CAAC,UAAUP,EAAI,EAAG,IAAI,GAAKC,EAAI,GAAI;AAAA;AAAA,YAG7C,CACJ,EA9B+B,WAgCzBO,GAA2Bf,EAAA,SAAUC,EAAIC,EAAMC,EAAM,CACzDD,EACG,OAAO,QAAQ,EACf,KAAK,KAAM,QAAUC,EAAK,EAAE,EAC5B,KAAK,QAAS,iBAAmBF,EAAG,SAASE,EAAK,IAAI,CAAC,EACvD,KAAK,IAAKA,EAAK,MAAQ,CAAC,CAC7B,EANiC,aAQjC,SAASa,GACPC,EACAV,EACAC,EACAU,EACAf,EACA,CACA,OAAOc,EACJ,OAAO,UAAW,cAAc,EAChC,KACC,SACAC,EACG,IAAI,SAAUC,EAAG,CAChB,OAAOA,EAAE,EAAI,IAAMA,EAAE,CACvB,CAAC,EACA,KAAK,GAAG,CACb,EACC,KAAK,YAAa,cAAgBhB,EAAK,MAAQI,GAAK,EAAI,KAAOC,EAAI,GAAG,CAC3E,CAlBSR,EAAAgB,GAAA,sBAoBT,IAAMI,GAA4BpB,EAAA,SAChCqB,EACAnB,EACAC,EACA,CACA,IAAMK,EAAIL,EAAK,OAETmB,EAAId,EADA,EAEJD,EAAIJ,EAAK,MAAQA,EAAK,QAAU,EAAImB,EACpCJ,EAAkB,CACtB,CAAE,EAAGI,EAAG,EAAG,CAAE,EACb,CAAE,EAAGf,EAAIe,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGf,EAAG,EAAG,CAACC,EAAI,CAAE,EAClB,CAAE,EAAGD,EAAIe,EAAG,EAAG,CAACd,CAAE,EAClB,CAAE,EAAGc,EAAG,EAAG,CAACd,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,CACpB,EACAQ,GAAmBd,EAAMK,EAAGC,EAAGU,EAAQf,CAAI,CAC7C,EAlBkC,cAoB5BoB,GAAgCvB,EAAA,SAAUC,EAAIC,EAAMC,EAAM,CAC9DD,EACG,OAAO,MAAM,EACb,KAAK,KAAM,QAAUC,EAAK,EAAE,EAC5B,KAAK,QAAS,iBAAmBF,EAAG,SAASE,EAAK,IAAI,CAAC,EACvD,KAAK,SAAUA,EAAK,MAAM,EAC1B,KAAK,KAAMA,EAAK,OAAO,EACvB,KAAK,KAAMA,EAAK,OAAO,EACvB,KAAK,QAASA,EAAK,KAAK,CAC7B,EATsC,kBAmBzBqB,GAAWxB,EAAA,eACtBC,EACAC,EACAC,EACAsB,EACAC,EACiB,CACjB,IAAMC,EAAaD,EAAK,WAClBtB,EAAUqB,GAAe3B,GAAe,GACxC8B,EAAW1B,EAAK,OAAO,GAAG,EAChCC,EAAK,QAAUC,EACf,IAAIyB,EAAe,WAAazB,EAC5BA,EAAU,IACZyB,GAAgB,iBAElBD,EAAS,KAAK,SAAUzB,EAAK,MAAQA,EAAK,MAAQ,IAAM,IAAM,gBAAkB0B,CAAY,EAC5F,IAAMC,EAAUF,EAAS,OAAO,GAAG,EAG7BG,EAAWH,EAAS,OAAO,GAAG,EAC9BI,EAAc7B,EAAK,MAAM,QAAQ,aAAc;AAAA,CAAI,EACzD,MAAM8B,GACJF,EACAC,EACA,CACE,cAAeL,EACf,MAAOxB,EAAK,MACZ,QAAS,oBACX,EACAuB,CACF,EAEKC,GACHI,EACG,KAAK,KAAM,KAAK,EAChB,KAAK,qBAAsB,QAAQ,EACnC,KAAK,oBAAqB,QAAQ,EAClC,KAAK,cAAe,QAAQ,EAEjC,IAAMG,EAAOH,EAAS,KAAK,EAAE,QAAQ,EAC/B,CAACI,CAAQ,EAAIC,GAAcV,EAAK,QAAQ,EAG9C,GAFAvB,EAAK,OAAS+B,EAAK,OAASC,EAAY,IAAM,GAAMhC,EAAK,QACzDA,EAAK,MAAQ+B,EAAK,MAAQ,EAAI/B,EAAK,QAC/BA,EAAK,KACP,GAAIA,EAAK,OAASF,EAAG,SAAS,OAC5BE,EAAK,QAAU,GACfA,EAAK,OAAS,GACDyB,EACV,OAAO,eAAe,EACtB,KAAK,SAAU,MAAM,EACrB,KAAK,QAASzB,EAAK,KAAK,EACxB,KAAK,QAAS,qBAAqB,EAEnC,OAAO,KAAK,EACZ,KAAK,QAAS,gBAAgB,EAC9B,OAAO,GAAG,EACV,KAAK,QAAS,aAAeC,EAAU,IAAMD,EAAK,IAAI,EACzD4B,EAAS,KACP,YACA,aAAe5B,EAAK,MAAQ,EAAI,MAAQA,EAAK,OAAS,EAAI,IAAMA,EAAK,SAAW,GAClF,MACK,CACLA,EAAK,OAAS,GACd,IAAMkC,EAAYlC,EAAK,OACvBA,EAAK,OAAS,KAAK,IAAIkC,EAAW,EAAE,EACpC,IAAMC,EAAa,KAAK,IAAInC,EAAK,OAASkC,CAAS,EACtCT,EACV,OAAO,eAAe,EACtB,KAAK,QAAS,MAAM,EACpB,KAAK,SAAUzB,EAAK,MAAM,EAC1B,KAAK,QAAS,iCAAmCmC,EAAa,EAAI,KAAK,EAGvE,OAAO,KAAK,EACZ,KAAK,QAAS,gBAAgB,EAC9B,OAAO,GAAG,EACV,KAAK,QAAS,aAAelC,EAAU,IAAMD,EAAK,IAAI,EACzD4B,EAAS,KACP,YACA,cAAgB,GAAK5B,EAAK,MAAQ,GAAK,MAAQmC,EAAa,EAAInC,EAAK,QAAU,GAAK,GACtF,CACF,SAEKwB,EAKE,CACL,IAAMY,GAAMpC,EAAK,MAAQ+B,EAAK,OAAS,EACjCM,GAAMrC,EAAK,OAAS+B,EAAK,QAAU,EACzCH,EAAS,KAAK,YAAa,aAAeQ,EAAK,KAAOC,EAAK,GAAG,CAChE,KATiB,CACf,IAAMD,EAAKpC,EAAK,MAAQ,EAClBqC,EAAKrC,EAAK,QAAU,EAC1B4B,EAAS,KAAK,YAAa,aAAeQ,EAAK,KAAOC,EAAK,GAAG,CAEhE,CAOF,OAAQrC,EAAK,KAAM,CACjB,KAAKF,EAAG,SAAS,QACfF,GAAWE,EAAI6B,EAAS3B,EAAMC,CAAO,EACrC,MACF,KAAKH,EAAG,SAAS,aACfsB,GAAetB,EAAI6B,EAAS3B,EAAMC,CAAO,EACzC,MACF,KAAKH,EAAG,SAAS,KACfI,GAAQJ,EAAI6B,EAAS3B,EAAMC,CAAO,EAClC,MACF,KAAKH,EAAG,SAAS,OACf6B,EAAQ,KAAK,YAAa,aAAe3B,EAAK,MAAQ,EAAI,MAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EACvFY,GAAUd,EAAI6B,EAAS3B,EAAMC,CAAO,EACpC,MACF,KAAKH,EAAG,SAAS,MACfK,GAASL,EAAI6B,EAAS3B,EAAMC,CAAO,EACnC,MACF,KAAKH,EAAG,SAAS,KACfY,GAAQZ,EAAI6B,EAAS3B,EAAMC,CAAO,EAClC,MACF,KAAKH,EAAG,SAAS,QACfmB,GAAWnB,EAAI6B,EAAS3B,EAAMC,CAAO,EACrC,KACJ,CAEA,OAAAH,EAAG,gBAAgBE,EAAK,GAAIyB,CAAQ,EAC7BzB,EAAK,MACd,EA1HwB,YA4HXsC,GAAezC,EAAA,SAAUC,EAAeE,EAAyB,CAC5E,IAAMyB,EAAW3B,EAAG,eAAeE,EAAK,EAAE,EAEpCuC,EAAIvC,EAAK,GAAK,EACdwC,EAAIxC,EAAK,GAAK,EAEpByB,EAAS,KAAK,YAAa,aAAec,EAAI,IAAMC,EAAI,GAAG,CAC7D,EAP4B,gBD5R5BC,GAAU,IAAI,GAAAC,OAAW,EAEzB,eAAeC,GACbC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,MAAMC,GAASL,EAAIC,EAAKC,EAASC,EAASC,CAAI,EAC1CF,EAAQ,UACV,MAAM,QAAQ,IACZA,EAAQ,SAAS,IAAI,CAACI,EAAOC,IAC3BR,GAAUC,EAAIC,EAAKK,EAAOH,EAAU,EAAII,EAAQJ,EAASC,CAAI,CAC/D,CACF,CAEJ,CAfeI,EAAAT,GAAA,aAiCf,SAASU,GAAUC,EAAoBC,EAAoB,CACzDA,EAAG,MAAM,EAAE,IAAI,CAACC,EAAMC,IAAO,CAC3B,IAAMC,EAAOF,EAAK,KAAK,EACvB,GAAIA,EAAK,CAAC,EAAE,SAAS,WAAY,CAC/B,IAAMG,EAASH,EAAK,CAAC,EAAE,SAAS,SAChCI,EAAI,MAAM,SAAUH,EAAIC,CAAI,EAC5BJ,EACG,OAAO,MAAM,EACb,KACC,IACA,KAAKK,EAAO,MAAM,IAAIA,EAAO,MAAM,MAAMA,EAAO,IAAI,IAAIA,EAAO,IAAI,KAAKA,EAAO,IAAI,IAAIA,EAAO,IAAI,GACpG,EACC,KAAK,QAAS,qBAAuBD,EAAK,QAAU,eAAiBA,EAAK,KAAK,CACpF,CACF,CAAC,CACH,CAfSN,EAAAC,GAAA,aAiBT,SAASQ,GAASf,EAAsBS,EAAoBP,EAAqBc,EAAe,CAC9FP,EAAG,IAAI,CACL,MAAO,QACP,KAAM,CACJ,GAAIT,EAAQ,GAAG,SAAS,EACxB,UAAWA,EAAQ,MACnB,OAAQA,EAAQ,OAChB,MAAOA,EAAQ,MACf,MAAOgB,EACP,OAAQhB,EAAQ,GAChB,QAASA,EAAQ,QACjB,KAAMA,EAAQ,IAChB,EACA,SAAU,CACR,EAAGA,EAAQ,EACX,EAAGA,EAAQ,CACb,CACF,CAAC,EACGA,EAAQ,UACVA,EAAQ,SAAS,QAASI,GAAU,CAClCW,GAASX,EAAOK,EAAIP,EAAMc,EAAQ,CAAC,EACnCP,EAAG,IAAI,CACL,MAAO,QACP,KAAM,CACJ,GAAI,GAAGT,EAAQ,EAAE,IAAII,EAAM,EAAE,GAC7B,OAAQJ,EAAQ,GAChB,OAAQI,EAAM,GACd,MAAOY,EACP,QAASZ,EAAM,OACjB,CACF,CAAC,CACH,CAAC,CAEL,CAjCSE,EAAAS,GAAA,YAmCT,SAASE,GAAcC,EAAmBhB,EAA8C,CACtF,OAAO,IAAI,QAASiB,GAAY,CAE9B,IAAMC,EAAWC,GAAO,MAAM,EAAE,OAAO,KAAK,EAAE,KAAK,KAAM,IAAI,EAAE,KAAK,QAAS,cAAc,EACrFZ,EAAKd,GAAU,CACnB,UAAW,SAAS,eAAe,IAAI,EACvC,MAAO,CACL,CACE,SAAU,OACV,MAAO,CACL,cAAe,QACjB,CACF,CACF,CACF,CAAC,EAEDyB,EAAS,OAAO,EAChBL,GAASG,EAAMT,EAAIP,EAAM,CAAC,EAG1BO,EAAG,MAAM,EAAE,QAAQ,SAAUa,EAAG,CAC9BA,EAAE,iBAAmB,IAAM,CACzB,IAAMV,EAAOU,EAAE,KAAK,EACpB,MAAO,CAAE,EAAGV,EAAK,MAAO,EAAGA,EAAK,MAAO,CACzC,CACF,CAAC,EAEDH,EAAG,OAAO,CACR,KAAM,eAEN,QAAS,QACT,aAAc,GACd,QAAS,EACX,CAAC,EAAE,IAAI,EACPA,EAAG,MAAOc,GAAM,CACdT,EAAI,KAAK,QAASS,CAAC,EACnBJ,EAAQV,CAAE,CACZ,CAAC,CACH,CAAC,CACH,CAvCSH,EAAAW,GAAA,iBAyCT,SAASO,GAAc1B,EAAeW,EAAoB,CACxDA,EAAG,MAAM,EAAE,IAAI,CAACS,EAAMP,IAAO,CAC3B,IAAMC,EAAOM,EAAK,KAAK,EACvBN,EAAK,EAAIM,EAAK,SAAS,EAAE,EACzBN,EAAK,EAAIM,EAAK,SAAS,EAAE,EACzBO,GAAa3B,EAAIc,CAAI,EACrB,IAAMc,EAAK5B,EAAG,eAAec,EAAK,MAAM,EACxCE,EAAI,KAAK,MAAOH,EAAI,cAAeO,EAAK,SAAS,EAAE,EAAG,KAAMA,EAAK,SAAS,EAAE,EAAG,IAAKN,CAAI,EACxFc,EAAG,KACD,YACA,aAAaR,EAAK,SAAS,EAAE,EAAIN,EAAK,MAAQ,CAAC,KAAKM,EAAK,SAAS,EAAE,EAAIN,EAAK,OAAS,CAAC,GACzF,EACAc,EAAG,KAAK,OAAQ,OAAOf,CAAE,GAAG,CAC9B,CAAC,CACH,CAdSL,EAAAkB,GAAA,iBAgBF,IAAMG,GAAuBrB,EAAA,MAAOsB,EAAMjB,EAAIkB,EAAUC,IAAY,CACzEhB,EAAI,MAAM;AAAA,EAAgCc,CAAI,EAE9C,IAAM9B,EAAKgC,EAAQ,GACbC,EAAKjC,EAAG,WAAW,EACzB,GAAI,CAACiC,EACH,OAGF,IAAM7B,EAAO8B,GAAU,EACvB9B,EAAK,WAAa,GAElB,IAAMH,EAAMkC,GAAiBtB,CAAE,EAKzBuB,EAAYnC,EAAI,OAAO,GAAG,EAChCmC,EAAU,KAAK,QAAS,eAAe,EACvC,IAAMC,EAAYpC,EAAI,OAAO,GAAG,EAChCoC,EAAU,KAAK,QAAS,eAAe,EACvC,MAAMtC,GAAUC,EAAIqC,EAAWJ,EAAyB,GAAI7B,CAAI,EAIhE,IAAMO,EAAK,MAAMQ,GAAcc,EAAI7B,CAAI,EAGvCK,GAAU2B,EAAWzB,CAAE,EACvBe,GAAc1B,EAAIW,CAAE,EAGpB2B,GACE,OACArC,EACAG,EAAK,SAAS,SAAWmC,EAAc,QAAQ,QAC/CnC,EAAK,SAAS,aAAemC,EAAc,QAAQ,WACrD,CACF,EAtCoC,QAwC7BC,GAAQ,CACb,KAAAX,EACF,EEtMA,IAAMY,GAAqCC,EAACC,GAAY,CACtD,IAAIC,EAAW,GAEf,QAASC,EAAI,EAAGA,EAAIF,EAAQ,kBAAmBE,IAC7CF,EAAQ,YAAcE,CAAC,EAAIF,EAAQ,YAAcE,CAAC,GAAKF,EAAQ,YAAcE,CAAC,EAC1EC,GAAOH,EAAQ,YAAcE,CAAC,CAAC,EACjCF,EAAQ,YAAcE,CAAC,EAAIE,GAAQJ,EAAQ,YAAcE,CAAC,EAAG,EAAE,EAE/DF,EAAQ,YAAcE,CAAC,EAAIG,GAAOL,EAAQ,YAAcE,CAAC,EAAG,EAAE,EAIlE,QAASA,EAAI,EAAGA,EAAIF,EAAQ,kBAAmBE,IAAK,CAClD,IAAMI,EAAK,IAAM,GAAK,EAAIJ,GAC1BD,GAAY;AAAA,eACDC,EAAI,CAAC,mBAAmBA,EAAI,CAAC,mBAAmBA,EAAI,CAAC,qBAC9DA,EAAI,CACN,sBAAsBA,EAAI,CAAC;AAAA,cACjBF,EAAQ,SAAWE,CAAC,CAAC;AAAA;AAAA,eAEpBA,EAAI,CAAC;AAAA,aACPF,EAAQ,cAAgBE,CAAC,CAAC;AAAA;AAAA,iBAEtBA,EAAI,CAAC;AAAA;AAAA,eAEPF,EAAQ,cAAgBE,CAAC,CAAC;AAAA;AAAA,oBAErBA,EAAI,CAAC;AAAA,gBACTF,EAAQ,SAAWE,CAAC,CAAC;AAAA;AAAA,kBAEnBA,EAAI,CAAC;AAAA,sBACDI,CAAE;AAAA;AAAA,eAETJ,EAAI,CAAC;AAAA,gBACJF,EAAQ,YAAcE,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAWtC,CACA,OAAOD,CACT,EA/C2C,eAkDrCM,GAAmCR,EAACC,GACxC;AAAA;AAAA;AAAA;AAAA,IAIEF,GAAYE,CAAO,CAAC;AAAA;AAAA,YAEZA,EAAQ,IAAI;AAAA;AAAA;AAAA,YAGZA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAVM,aA6BlCQ,GAAQD,GC5ER,IAAME,GAA6B,CACxC,GAAAC,GACA,SAAAC,GACA,OAAAC,GACA,OAAAC,EACF", "names": ["require_layout_base", "__commonJSMin", "exports", "module", "__name", "root", "factory", "modules", "installedModules", "__webpack_require__", "moduleId", "value", "name", "getter", "object", "property", "LayoutConstants", "LGraphObject", "IGeometry", "IMath", "<PERSON><PERSON><PERSON>", "source", "target", "vEdge", "prop", "node", "graph", "otherEnd", "clipPointCoordinates", "vGraphObject", "Integer", "RectangleD", "RandomSeed", "PointD", "LNode", "gm", "loc", "size", "vNode", "width", "height", "upperLeft", "dimension", "cx", "cy", "x", "y", "dx", "dy", "to", "edgeList", "edge", "self", "other", "neighbors", "withNeighborsList", "childNode", "children", "nodes", "i", "noOf<PERSON><PERSON><PERSON><PERSON>", "randomCenterX", "randomCenterY", "minX", "maxX", "minY", "maxY", "childGraph", "trans", "left", "top", "leftTop", "vLeftTop", "pt", "dim", "LGraphManager", "Point", "LinkedList", "LGraph", "parent", "obj2", "vGraph", "obj1", "sourceNode", "targetNode", "newNode", "newEdge", "obj", "edgesToBeRemoved", "s", "index", "sourceIndex", "targetIndex", "nodeTop", "nodeLeft", "margin", "lNode", "recursive", "right", "bottom", "nodeRight", "nodeBottom", "boundingRect", "queue", "visited", "currentNode", "neighborEdges", "currentNeighbor", "childrenOfNode", "neighborEdge", "childrenOfNeighbor", "noOfVisitedInThisGraph", "visitedNode", "layout", "ngraph", "nnode", "newGraph", "parentNode", "sourceGraph", "targetGraph", "lObj", "nodesToBeRemoved", "nodeList", "graphs", "firstNode", "secondNode", "ownerGraph", "sourceAncestorGraph", "targetAncestorGraph", "edges", "firstOwnerGraph", "secondOwnerGraph", "depth", "FDLayoutConstants", "rectA", "rectB", "overlapAmount", "<PERSON><PERSON><PERSON><PERSON>", "directions", "slope", "moveByY", "moveByX", "result", "p1x", "p1y", "p2x", "p2y", "topLeftAx", "topLeftAy", "topRightAx", "bottomLeftAx", "bottomLeftAy", "bottomRightAx", "halfWidthA", "halfHeightA", "topLeftBx", "topLeftBy", "topRightBx", "bottomLeftBx", "bottomLeftBy", "bottomRightBx", "halfWidthB", "halfHeightB", "clipPointAFound", "clipPointBFound", "slopeA", "slopeB", "slopePrime", "cardinalDirectionA", "cardinalDirectionB", "tempPointAx", "tempPointAy", "tempPointBx", "tempPointBy", "line", "s1", "s2", "f1", "f2", "x1", "y1", "x2", "y2", "x3", "y3", "x4", "y4", "a1", "a2", "b1", "b2", "c1", "c2", "denom", "Cx", "Cy", "Nx", "Ny", "C_angle", "p1", "p2", "p3", "p4", "b", "c", "d", "p", "q", "r", "det", "lambda", "gamma", "_createClass", "defineProperties", "props", "descriptor", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_classCallCheck", "instance", "nodeFrom", "add", "prev", "next", "list", "_remove", "vals", "_this", "v", "val", "otherNode", "current", "a", "_typeof", "UniqueIDGeneretor", "id", "arg", "type", "_toConsumableArray", "arr", "arr2", "Transform", "Emitter", "Layout", "isRemoteUse", "isLayoutSuccessfull", "allEdges", "newLeftTop", "flatForest", "isForest", "allNodes", "is<PERSON><PERSON>", "toBeVisited", "parents", "unProcessedNodes", "temp", "dummyNodes", "dummy<PERSON>ode", "dummy<PERSON><PERSON>", "k", "lEdge", "path", "ebp", "slider<PERSON><PERSON><PERSON>", "defaultValue", "minDiv", "maxMul", "minValue", "maxValue", "removedNodes", "remainingDegrees", "foundCenter", "centerNode", "degree", "tempList", "tempList2", "neighbours", "neighbour", "otherDegree", "newDegree", "wox", "woy", "wex", "wey", "dox", "doy", "dex", "dey", "xDevice", "worldExtX", "yDevice", "worldExtY", "xWorld", "deviceExtX", "yWorld", "deviceExtY", "inPoint", "outPoint", "FDLayout", "lcaDepth", "sizeOfSourceInLca", "sizeOfTargetInLca", "l<PERSON><PERSON>", "gridUpdateAllowed", "forceToNodeSurroundingUpdate", "j", "nodeA", "nodeB", "lNodes", "processedNodeSet", "ideal<PERSON>ength", "length", "springForce", "springForceX", "springForceY", "clipPoints", "distanceX", "distanceY", "distanceSquared", "distance", "repulsionForce", "repulsionForceX", "repulsionForceY", "childrenConstant", "ownerCenterX", "ownerCenterY", "absDistanceX", "absDistanceY", "estimatedSize", "converged", "oscilating", "sizeX", "sizeY", "grid", "startX", "finishX", "startY", "finishY", "surrounding", "FDLayoutEdge", "FDLayoutNode", "_startX", "_finishX", "_startY", "_finishY", "DimensionD", "HashMap", "key", "theId", "HashSet", "keys", "Quicksort", "A", "compareFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sequence1", "sequence2", "match_score", "mismatch_penalty", "gap_penalty", "_i", "_j", "_i2", "_j2", "diag", "up", "maxOf", "indices", "inProcessAlignments", "indexes", "array", "layoutBase", "event", "callback", "l", "data", "require_cose_base", "__commonJSMin", "exports", "module", "__name", "root", "factory", "__WEBPACK_EXTERNAL_MODULE_0__", "modules", "installedModules", "__webpack_require__", "moduleId", "value", "name", "getter", "object", "property", "FDLayoutConstants", "CoSEConstants", "prop", "FDLayoutEdge", "CoSEEdge", "source", "target", "vEdge", "LGraph", "CoSEGraph", "parent", "graphMgr", "vGraph", "LGraphManager", "CoSEGraphManager", "layout", "FDLayoutNode", "IMath", "CoSENode", "gm", "loc", "size", "vNode", "dX", "dY", "nodes", "node", "i", "pred1", "next", "processed", "FDLayout", "LayoutConstants", "Point", "PointD", "Layout", "Integer", "IGeometry", "Transform", "CoSELayout", "createBendsAsNeeded", "allNodes", "intersection", "x", "forest", "gridUpdateAllowed", "forceToNodeSurroundingUpdate", "pData", "rect", "id", "layoutEnded", "nodeList", "graph", "graphs", "edges", "visited", "edge", "edgeList", "k", "multiEdge", "currentStartingPoint", "numberOfColumns", "height", "currentY", "currentX", "point", "tree", "centerNode", "startingPoint", "radialSep", "bounds", "transform", "bottomRight", "parentOfNode", "startAngle", "endAngle", "distance", "radialSeparation", "halfInterval", "nodeAngle", "teta", "cos_teta", "x_", "y_", "neighborEdges", "childCount", "branchCount", "incEdgesCount", "startIndex", "temp", "index", "stepAngle", "currentNeighbor", "childStartAngle", "childEndAngle", "maxDiagonal", "diagonal", "self", "tempMemberGroups", "zeroDegree", "p_id", "dummyCompoundId", "dummyCompound", "dummy<PERSON><PERSON>ntGraph", "parentGraph", "childGraphMap", "idToNode", "tiledZeroDegreePack", "compoundNode", "lCompoundNode", "<PERSON><PERSON><PERSON><PERSON>", "verticalMargin", "tiledPack", "childGraph", "children", "theChild", "degree", "child", "organization", "y", "compoundHorizontalMargin", "compoundVerticalMargin", "left", "row", "maxHeight", "j", "lnode", "min<PERSON><PERSON><PERSON>", "verticalPadding", "horizontalPadding", "n1", "n2", "lNode", "rowIndex", "minCompoundSize", "secondDimension", "w", "h", "extraHeight", "r", "min", "max", "extraWidth", "sri", "hDiff", "add_to_row_ratio", "add_new_row_ratio", "longest", "last", "diff", "prevTotal", "finalTotal", "prunedNodesAll", "<PERSON><PERSON><PERSON><PERSON>", "prunedNodesInStepTemp", "prunedNodesInStep", "lengthOfPrunedNodesInStep", "nodeData", "gridForPrunedNode", "nodeToConnect", "prunedNode", "startGridX", "finishGridX", "startGridY", "finishGridY", "upNodeCount", "downNodeCount", "rightNodeCount", "leftNodeCount", "controlRegions", "minCount", "minIndex", "random", "coseBase", "require_cytoscape_cose_bilkent", "__commonJSMin", "exports", "module", "__name", "root", "factory", "__WEBPACK_EXTERNAL_MODULE_0__", "modules", "installedModules", "__webpack_require__", "moduleId", "value", "name", "getter", "object", "property", "LayoutConstants", "FDLayoutConstants", "CoSEConstants", "CoSELayout", "CoSENode", "PointD", "DimensionD", "defaults", "extend", "options", "obj", "i", "_CoSELayout", "_options", "getUserOptions", "ready", "frameId", "idToLNode", "layout", "self", "gm", "nodes", "edges", "edge", "sourceNode", "targetNode", "e1", "getPositions", "ele", "theId", "lNode", "iterateAnimated", "afterReposition", "ticksPerFrame", "isDone", "animationData", "pNode", "temp", "nodesMap", "roots", "parent", "children", "size", "theChild", "children_of_children", "theNode", "dimensions", "labelWidth", "labelHeight", "labelPos", "theNewGraph", "register", "cytoscape", "parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "mindmap_default", "parser", "nodes", "cnt", "elements", "clear", "__name", "getParent", "level", "i", "getMindmap", "addNode", "id", "descr", "type", "log", "conf", "getConfig", "padding", "defaultConfig_default", "nodeType", "node", "sanitizeText", "parent", "getType", "startStr", "endStr", "setElementForId", "element", "decorateNode", "decoration", "config", "type2Str", "<PERSON><PERSON><PERSON><PERSON>", "getElementById", "db", "mindmapDb_default", "import_cytoscape_cose_bilkent", "MAX_SECTIONS", "defaultBkg", "__name", "db", "elem", "node", "section", "rectBkg", "cloudBkg", "w", "h", "r1", "r2", "r3", "r4", "bangBkg", "r", "circleBkg", "insertPolygonShape", "parent", "points", "d", "hexagonBkg", "_db", "m", "roundedRectBkg", "drawNode", "fullSection", "conf", "htmlLabels", "nodeElem", "sectionClass", "bkgElem", "textElem", "description", "createText", "bbox", "fontSize", "parseFontSize", "orgHeight", "heightDiff", "dx", "dy", "positionNode", "x", "y", "cytoscape", "coseBilkent", "drawNodes", "db", "svg", "mindmap", "section", "conf", "drawNode", "child", "index", "__name", "drawEdges", "edgesEl", "cy", "edge", "id", "data", "bounds", "log", "addNodes", "level", "layoutMindmap", "node", "resolve", "renderEl", "select_default", "n", "e", "positionNodes", "positionNode", "el", "draw", "text", "_version", "diagObj", "mm", "getConfig", "selectSvgElement", "edgesElem", "nodesElem", "setupGraphViewbox", "defaultConfig_default", "mindmapRenderer_default", "genSections", "__name", "options", "sections", "i", "is_dark_default", "lighten_default", "darken_default", "sw", "getStyles", "styles_default", "diagram", "mindmapDb_default", "mindmapRenderer_default", "mindmap_default", "styles_default"]}