import { IconifyIcon } from '@iconify/types';
import { IconCSSIconOptions, IconContentIconOptions } from './types.cjs';

/**
 * Get CSS for icon, rendered as background or mask
 */
declare function getIconCSS(icon: IconifyIcon, options?: IconCSSIconOptions): string;
/**
 * Get CSS for icon, rendered as content
 */
declare function getIconContentCSS(icon: IconifyIcon, options: IconContentIconOptions): string;

export { getIconCSS, getIconContentCSS };
