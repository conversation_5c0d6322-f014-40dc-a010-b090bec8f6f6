@echo off
chcp 65001
echo ========================================
echo iFlytek 职位管理系统开发服务器启动
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo 错误：Node.js未安装或未添加到PATH环境变量
    pause
    exit /b 1
)

echo.
echo 正在进入项目目录...
cd /d "%~dp0frontend"
echo 当前目录：%cd%

echo.
echo 正在检查package.json...
if not exist package.json (
    echo 错误：package.json文件不存在
    echo 请确认当前目录是否正确
    pause
    exit /b 1
)

echo.
echo 正在检查node_modules...
if not exist node_modules (
    echo node_modules不存在，正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误：依赖安装失败
        pause
        exit /b 1
    )
)

echo.
echo 正在启动开发服务器...
echo 服务器将在 http://localhost:5173 启动
echo 按 Ctrl+C 可以停止服务器
echo.

start "iFlytek开发服务器" cmd /k "npm run dev"

echo.
echo 开发服务器已在新窗口中启动
echo 请等待几秒钟，然后访问 http://localhost:5173
echo.
pause
