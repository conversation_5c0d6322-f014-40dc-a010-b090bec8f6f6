<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MultimodalAIShowcase 组件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }
        .test-title {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 12px;
        }
        .test-description {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.6;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-loading {
            background: #f39c12;
            animation: pulse 1.5s infinite;
        }
        .status-success {
            background: #27ae60;
        }
        .status-error {
            background: #e74c3c;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .component-frame {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            min-height: 400px;
            background: white;
            position: relative;
        }
        .loading-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #7f8c8d;
        }
        .test-info {
            margin-top: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .test-info h4 {
            margin: 0 0 12px 0;
            color: #2c3e50;
        }
        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }
        .test-info li {
            margin-bottom: 8px;
            color: #5a6c7d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">
                <span class="status-indicator status-loading" id="statusIndicator"></span>
                MultimodalAIShowcase 组件测试
            </h1>
            <p class="test-description">
                验证基于三个竞品网站优化后的多模态AI特色展示组件是否正常工作
            </p>
        </div>

        <div class="component-frame" id="componentFrame">
            <div class="loading-message">
                <div>正在加载组件...</div>
                <div style="margin-top: 8px; font-size: 14px;">请确保开发服务器已启动</div>
            </div>
        </div>

        <div class="test-info">
            <h4>🎯 优化内容验证清单</h4>
            <ul>
                <li><strong>功能特性增强</strong>：98%+ 准确率、毫秒级响应、12维度评估</li>
                <li><strong>技术性能指标</strong>：语音识别、AI响应、评估准确率对比</li>
                <li><strong>实际应用案例</strong>：科技企业和金融集团成功案例</li>
                <li><strong>动画效果优化</strong>：12条语音波形、实时数据更新、状态轮播</li>
                <li><strong>竞品优势融合</strong>：Offermore.cc + Hina.com + Dayee.com</li>
            </ul>
        </div>

        <div class="test-info">
            <h4>🔧 测试步骤</h4>
            <ul>
                <li>1. 确保开发服务器运行在 http://localhost:5173/</li>
                <li>2. 检查浏览器控制台是否有错误信息</li>
                <li>3. 验证组件是否正确显示所有优化内容</li>
                <li>4. 测试动画效果是否正常工作</li>
                <li>5. 确认响应式设计在不同设备上的表现</li>
            </ul>
        </div>
    </div>

    <script>
        // 检查开发服务器状态
        async function checkServerStatus() {
            const statusIndicator = document.getElementById('statusIndicator');
            const componentFrame = document.getElementById('componentFrame');
            
            try {
                const response = await fetch('http://localhost:5173/');
                if (response.ok) {
                    statusIndicator.className = 'status-indicator status-success';
                    loadComponent();
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                statusIndicator.className = 'status-indicator status-error';
                componentFrame.innerHTML = `
                    <div class="loading-message">
                        <div style="color: #e74c3c;">❌ 开发服务器未启动</div>
                        <div style="margin-top: 8px; font-size: 14px;">
                            请运行: cd frontend && npm run dev
                        </div>
                    </div>
                `;
            }
        }

        function loadComponent() {
            const componentFrame = document.getElementById('componentFrame');
            componentFrame.innerHTML = `
                <iframe 
                    src="http://localhost:5173/" 
                    width="100%" 
                    height="800" 
                    frameborder="0"
                    style="border-radius: 8px;"
                    onload="this.style.opacity=1"
                    style="opacity: 0; transition: opacity 0.3s ease;"
                ></iframe>
            `;
        }

        // 页面加载时检查服务器状态
        window.addEventListener('load', checkServerStatus);
        
        // 每30秒检查一次服务器状态
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html>
