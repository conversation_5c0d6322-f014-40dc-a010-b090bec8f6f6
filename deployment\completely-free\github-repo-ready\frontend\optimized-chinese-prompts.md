# 🎯 中文文字优化AI视频生成提示词

## 📋 优化重点
- ✅ 明确指定具体中文文字内容
- ✅ 强化视觉对比度描述
- ✅ 明确字体规格要求
- ✅ 防止乱码和模糊
- ✅ 优化界面布局描述
- ✅ 突出专业术语显示

---

## 🎬 视频1: demo-complete.mp4 (8分钟) - 系统完整演示

### 📝 优化提示词
```
专业AI面试系统界面演示，顶部居中显示大号白色清晰标题"科大讯飞Spark智能面试评估系统"，使用现代简洁字体无乱码显示，深蓝紫色渐变背景确保高对比度文字显示。界面包含四个主要功能按钮，从左到右清晰显示白色中等大小文字："开始面试"、"多模态分析"、"能力评估"、"生成报告"，每个按钮文字清晰可识别标准中文字符。左侧显示功能模块标签："语音识别"、"视频分析"、"文本理解"，右侧显示评估维度："技术能力"、"沟通表达"、"逻辑思维"、"学习能力"、"团队协作"、"创新思维"，所有文字使用企业级标准字体，白色清晰字体配深色背景，确保文字清晰可读无模糊，现代企业UI设计，专业商务演示风格
```

### 🎨 关键文字要求
- **主标题**: "科大讯飞Spark智能面试评估系统"
- **功能按钮**: "开始面试"、"多模态分析"、"能力评估"、"生成报告"
- **技术模块**: "语音识别"、"视频分析"、"文本理解"
- **评估维度**: "技术能力"、"沟通表达"、"逻辑思维"、"学习能力"、"团队协作"、"创新思维"

---

## 🤖 视频2: demo-ai-tech.mp4 (6分钟) - AI技术解析

### 📝 优化提示词
```
iFlytek Spark大语言模型技术架构深度展示界面，顶部居中显示大号白色清晰标题"iFlytek Spark AI技术架构"，使用现代简洁字体无乱码显示。界面左侧技术模块区域清晰显示白色中文标签："神经网络架构"、"多模态融合算法"、"自然语言处理"、"深度学习引擎"，右侧算法展示区域显示："实时语音识别"、"视频情感分析"、"文本语义理解"、"智能评分算法"。底部技术指标区域显示："处理速度"、"准确率"、"响应时间"、"并发能力"，所有文字使用企业级标准字体，白色清晰字体配深蓝色科技背景，确保文字清晰可读无模糊，高对比度文字显示，标准中文字符清晰可识别，专业技术界面设计
```

### 🎨 关键文字要求
- **主标题**: "iFlytek Spark AI技术架构"
- **技术模块**: "神经网络架构"、"多模态融合算法"、"自然语言处理"、"深度学习引擎"
- **算法功能**: "实时语音识别"、"视频情感分析"、"文本语义理解"、"智能评分算法"
- **技术指标**: "处理速度"、"准确率"、"响应时间"、"并发能力"

---

## 📊 视频3: demo-cases.mp4 (5分钟) - 案例分析

### 📝 优化提示词
```
智能面试案例分析专业界面，顶部居中显示大号白色清晰标题"面试案例深度分析"，使用现代简洁字体无乱码显示。界面采用三栏布局，左栏显示职位类别标签："AI算法工程师"、"大数据分析师"、"IoT系统开发者"，中栏显示评估过程："技能测试进行中"、"能力分析中"、"综合评分中"，右栏显示评估结果："技术深度85分"、"问题解决92分"、"沟通能力78分"。底部状态栏显示："当前案例：AI工程师面试"、"评估进度：75%完成"、"预计剩余时间：3分钟"，所有文字使用企业级标准字体，白色清晰字体配深色背景，确保文字清晰可读无模糊，高对比度文字显示，标准中文字符清晰可识别，专业面试分析界面设计
```

### 🎨 关键文字要求
- **主标题**: "面试案例深度分析"
- **职位类别**: "AI算法工程师"、"大数据分析师"、"IoT系统开发者"
- **评估过程**: "技能测试进行中"、"能力分析中"、"综合评分中"
- **评估结果**: "技术深度85分"、"问题解决92分"、"沟通能力78分"
- **状态信息**: "当前案例：AI工程师面试"、"评估进度：75%完成"

---

## 📈 视频4: demo-bigdata.mp4 (7分钟) - 大数据专题

### 📝 优化提示词
```
大数据技术能力评估专业界面，顶部居中显示大号白色清晰标题"大数据技术能力专项评估"，使用现代简洁字体无乱码显示。界面左上角显示技能模块："数据处理能力"、"机器学习算法"、"数据可视化"、"实时计算"，右上角显示工具掌握度："Hadoop生态系统"、"Spark计算引擎"、"Python数据分析"、"SQL查询优化"。中央数据仪表盘区域显示："数据处理速度"、"算法准确率"、"模型性能"、"系统吞吐量"，底部项目经验区域显示："推荐系统项目"、"用户画像分析"、"实时风控系统"、"数据挖掘项目"，所有文字使用企业级标准字体，白色清晰字体配深蓝色数据背景，确保文字清晰可读无模糊，高对比度文字显示，标准中文字符清晰可识别，专业数据分析界面设计
```

### 🎨 关键文字要求
- **主标题**: "大数据技术能力专项评估"
- **技能模块**: "数据处理能力"、"机器学习算法"、"数据可视化"、"实时计算"
- **工具掌握**: "Hadoop生态系统"、"Spark计算引擎"、"Python数据分析"、"SQL查询优化"
- **性能指标**: "数据处理速度"、"算法准确率"、"模型性能"、"系统吞吐量"
- **项目经验**: "推荐系统项目"、"用户画像分析"、"实时风控系统"、"数据挖掘项目"

---

## 🔌 视频5: demo-iot.mp4 (6分钟) - IoT专题

### 📝 优化提示词
```
物联网技术能力评估专业界面，顶部居中显示大号白色清晰标题"IoT物联网技术专项评估"，使用现代简洁字体无乱码显示。界面左侧硬件技能区域显示："嵌入式系统开发"、"传感器技术应用"、"微控制器编程"、"电路设计能力"，右侧软件技能区域显示："物联网协议栈"、"云端数据处理"、"移动应用开发"、"系统集成能力"。中央技术架构图显示："设备层"、"网络层"、"平台层"、"应用层"，底部项目实战区域显示："智能家居系统"、"工业物联网"、"智慧城市项目"、"车联网应用"，所有文字使用企业级标准字体，白色清晰字体配深绿色科技背景，确保文字清晰可读无模糊，高对比度文字显示，标准中文字符清晰可识别，专业IoT技术界面设计
```

### 🎨 关键文字要求
- **主标题**: "IoT物联网技术专项评估"
- **硬件技能**: "嵌入式系统开发"、"传感器技术应用"、"微控制器编程"、"电路设计能力"
- **软件技能**: "物联网协议栈"、"云端数据处理"、"移动应用开发"、"系统集成能力"
- **技术架构**: "设备层"、"网络层"、"平台层"、"应用层"
- **项目实战**: "智能家居系统"、"工业物联网"、"智慧城市项目"、"车联网应用"

---

## 🎨 通用文字优化规则

### 字体规格要求
- **主标题**: 大号字体 (36-48px)，白色，居中显示
- **功能按钮**: 中等字体 (24-32px)，白色，清晰边框
- **标签文字**: 标准字体 (18-24px)，白色或浅色
- **数据文字**: 等宽字体 (16-20px)，高对比度

### 对比度要求
- **背景色**: 深蓝色 (#1a329e)、深紫色 (#764ba2)、深绿色 (#2d5a27)
- **文字色**: 纯白色 (#ffffff)、浅灰色 (#f0f0f0)
- **对比度**: 至少7:1的对比度比例
- **边框**: 半透明白色边框增强可读性

### 防乱码措施
- **字符集**: 明确指定"标准中文字符"
- **编码**: 要求"UTF-8编码显示"
- **字体**: 指定"系统标准中文字体"
- **渲染**: 要求"清晰渲染无锯齿"

### 布局优化
- **层次分明**: 主标题 > 功能区 > 详细信息
- **对齐方式**: 标题居中，功能区左对齐，数据右对齐
- **间距合理**: 文字间距适中，避免拥挤
- **分组清晰**: 相关功能文字分组显示

---

## 🚀 使用建议

### 剪映使用技巧
1. **分段输入**: 将长提示词分成几段输入
2. **关键词突出**: 将重要的中文文字用引号标注
3. **多次尝试**: 如果文字不清晰，调整提示词重新生成
4. **后期编辑**: 生成后可以用剪映的文字功能补充

### 质量检查标准
- [ ] 主标题文字清晰可读
- [ ] 功能按钮文字无乱码
- [ ] 专业术语正确显示
- [ ] 对比度足够高
- [ ] 字体大小合适
- [ ] 布局整齐美观

### 优化迭代
如果首次生成效果不理想：
1. **简化提示词**: 减少文字数量，突出重点
2. **增强对比度**: 强调"高对比度"、"白色文字"
3. **明确字体**: 指定"大号字体"、"清晰字体"
4. **分步生成**: 先生成界面，再添加文字

---

**使用这些优化提示词，应该能显著改善AI生成视频中的中文文字显示效果！** 🎉
