# 即梦AI视频生成安全配置指南

## ⚠️ 重要安全提醒

**您刚才公开分享了API密钥，请立即采取以下安全措施：**

1. **立即登录即梦AI控制台**
2. **删除或重置当前密钥**
3. **生成新的访问密钥**
4. **检查账户使用记录**

## 🔐 安全配置步骤

### 第1步：重置API密钥
1. 访问即梦AI控制台
2. 进入API密钥管理
3. 删除当前密钥：`AKLTYTViOTdiMjM2ZDMyNGJjNmJlODZlNzk3MTE2NWU1MjA`
4. 生成新的密钥对

### 第2步：安全存储密钥
**Windows系统：**
```cmd
set JIMENG_ACCESS_KEY_ID=your_new_access_key_id
set JIMENG_SECRET_ACCESS_KEY=your_new_secret_access_key
```

**Linux/Mac系统：**
```bash
export JIMENG_ACCESS_KEY_ID="your_new_access_key_id"
export JIMENG_SECRET_ACCESS_KEY="your_new_secret_access_key"
```

**或者创建 .env 文件：**
```
JIMENG_ACCESS_KEY_ID=your_new_access_key_id
JIMENG_SECRET_ACCESS_KEY=your_new_secret_access_key
```

## 🚀 使用即梦AI生成视频

### 安装依赖
```bash
npm install node-fetch crypto
```

### 运行生成脚本
```bash
# 设置环境变量后运行
node jimeng-ai-video-generator.js

# 检查生成状态
node jimeng-ai-video-generator.js --check
```

## 🎯 针对多模态面试系统的优化提示词

### 1. demo-complete.mp4 (8分钟)
```
专业AI面试系统界面演示，科大讯飞Spark LLM技术平台，蓝紫色渐变UI设计，多模态输入功能展示，包含语音识别、视频分析、文本处理模块，六维能力评估仪表盘，清晰的中文界面标签，现代企业级设计风格，高清晰度界面文字，专业商务演示风格
```

### 2. demo-ai-tech.mp4 (6分钟)
```
iFlytek Spark大语言模型技术架构深度展示，神经网络结构图表可视化，AI算法处理流程演示，多模态融合技术原理图解，深蓝色科技风格界面，清晰的中文技术标签和说明，专业技术图表动画，现代科技感界面设计
```

### 3. demo-cases.mp4 (5分钟)
```
智能面试案例分析界面展示，AI工程师技能评估案例，大数据分析师能力测试案例，IoT开发者技术评估案例，分屏显示候选人分析过程，专业面试环境背景，清晰的中文评估标签，企业级界面设计风格
```

### 4. demo-bigdata.mp4 (7分钟)
```
大数据技术能力评估界面，数据处理能力可视化展示，机器学习算法理解测试界面，实时数据分析仪表盘，数据科学技能评估模块，蓝色数据主题界面设计，清晰的中文数据标签，专业数据分析图表展示
```

### 5. demo-iot.mp4 (6分钟)
```
物联网技术评估专业界面，嵌入式系统架构图展示，传感器网络拓扑可视化，IoT通信协议技术演示，硬件软件集成能力界面，科技蓝色主题设计，清晰的中文IoT技术标签，专业物联网技术图表
```

## 🎨 提示词优化技巧

### 解决文字显示问题
1. **明确指定文字内容**：在提示词中具体说明要显示的中文文字
2. **强调文字清晰度**：使用"清晰的中文标签"、"高清晰度文字"等描述
3. **指定字体风格**：要求"现代简洁字体"、"企业级字体设计"
4. **确保对比度**：明确"白色文字蓝色背景"、"高对比度显示"

### 提升专业度
1. **使用专业术语**：iFlytek Spark、多模态、六维评估等
2. **强调企业风格**：企业级、商务风格、专业设计
3. **指定色彩主题**：蓝紫色渐变、科技蓝色等
4. **要求界面规范**：现代UI设计、简洁布局等

## 📊 生成流程

### 自动化流程
1. **批量提交**：一次性提交5个视频生成任务
2. **状态监控**：定期检查生成进度
3. **自动下载**：完成后自动下载到指定目录
4. **文件管理**：自动重命名并放置到正确位置

### 手动流程（备选）
1. **即梦AI网页版**：访问即梦AI官网
2. **选择视频生成**：选择文本转视频功能
3. **输入提示词**：使用上述优化的中文提示词
4. **设置参数**：1920x1080分辨率，对应时长
5. **生成下载**：等待生成完成后下载

## 💡 成功提示

### API调用优势
- **批量处理**：一次性生成所有需要的视频
- **自动化**：无需手动操作，节省时间
- **一致性**：确保所有视频风格统一
- **可重复**：可以轻松调整参数重新生成

### 注意事项
- **API限制**：注意调用频率限制
- **费用控制**：监控API使用费用
- **质量检查**：生成后检查视频质量
- **备份保存**：保存好满意的提示词模板

## 🔧 故障排除

### 常见问题
1. **认证失败**：检查环境变量是否正确设置
2. **生成失败**：尝试简化提示词或调整参数
3. **下载失败**：检查网络连接和存储空间
4. **文字乱码**：在提示词中更明确地指定文字内容

### 联系支持
- 即梦AI官方文档
- 技术支持邮箱
- 用户社区论坛

---

**重要提醒：请务必先重置您的API密钥，确保账户安全！**
