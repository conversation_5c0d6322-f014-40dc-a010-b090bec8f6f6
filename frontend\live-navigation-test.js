// iFlytek 面试系统实时导航测试脚本
// 在主页控制台中运行此脚本进行实时测试

console.log('🚀 开始iFlytek面试系统实时导航测试...');

// 测试结果收集器
const testResults = {
    menuItems: [],
    buttons: [],
    errors: [],
    warnings: []
};

// 1. 测试导航菜单项
function testNavigationMenuItems() {
    console.log('\n📋 测试导航菜单项...');
    
    const menuItems = [
        { selector: '.el-menu-item', text: '首页', expectedPath: '/' },
        { selector: '.el-menu-item', text: '产品演示', expectedPath: '/demo' },
        { selector: '.el-menu-item', text: '开始面试', expectedPath: '/interview-selection' },
        { selector: '.el-menu-item', text: '面试报告', expectedPath: '/reports' },
        { selector: '.el-menu-item', text: '数据洞察', expectedPath: '/intelligent-dashboard' }
    ];
    
    const allMenuItems = document.querySelectorAll('.el-menu-item');
    console.log(`📝 找到 ${allMenuItems.length} 个菜单项`);
    
    allMenuItems.forEach((item, index) => {
        const text = item.textContent.trim();
        const result = {
            index: index + 1,
            text: text,
            element: item,
            hasClickHandler: false,
            hasVueBinding: false,
            clickable: false,
            testResult: 'pending'
        };
        
        // 检查点击处理器
        if (item.onclick || item.addEventListener) {
            result.hasClickHandler = true;
        }
        
        // 检查Vue绑定
        if (item.__vueParentComponent || item._vnode) {
            result.hasVueBinding = true;
        }
        
        // 检查是否可点击
        const style = window.getComputedStyle(item);
        if (style.pointerEvents !== 'none' && style.cursor === 'pointer') {
            result.clickable = true;
        }
        
        // 模拟点击测试（不实际导航）
        try {
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            // 监听点击事件
            let clickDetected = false;
            const clickListener = () => {
                clickDetected = true;
                result.testResult = 'success';
            };
            
            item.addEventListener('click', clickListener, { once: true });
            
            // 触发点击事件
            item.dispatchEvent(clickEvent);
            
            // 检查是否检测到点击
            setTimeout(() => {
                if (clickDetected) {
                    console.log(`✅ 菜单项 "${text}" 点击事件正常`);
                    result.testResult = 'success';
                } else {
                    console.warn(`⚠️ 菜单项 "${text}" 点击事件未响应`);
                    result.testResult = 'warning';
                }
                
                // 移除监听器
                item.removeEventListener('click', clickListener);
            }, 100);
            
        } catch (error) {
            console.error(`❌ 菜单项 "${text}" 测试失败:`, error);
            result.testResult = 'error';
            testResults.errors.push(`菜单项 "${text}": ${error.message}`);
        }
        
        testResults.menuItems.push(result);
    });
    
    return testResults.menuItems;
}

// 2. 测试行动按钮
function testActionButtons() {
    console.log('\n🔘 测试行动按钮...');
    
    const buttonSelectors = [
        { selector: '.primary-cta', name: '立即开始面试', expectedPath: '/interview-selection' },
        { selector: '.secondary-cta', name: '观看产品演示', expectedPath: '/demo' },
        { selector: '.cta-button', name: '企业版体验', expectedPath: '/enterprise-home' },
        { selector: '.secondary-btn', name: '候选人入口', expectedPath: '/candidate-portal' },
        { selector: '.start-btn', name: '开始AI面试', expectedPath: '/interview-selection' },
        { selector: '.demo-btn', name: '观看演示', expectedPath: '/demo' },
        { selector: '.report-btn', name: '查看报告', expectedPath: '/reports' }
    ];
    
    buttonSelectors.forEach(config => {
        const buttons = document.querySelectorAll(config.selector);
        
        buttons.forEach((button, index) => {
            const result = {
                selector: config.selector,
                name: config.name,
                index: index + 1,
                element: button,
                text: button.textContent.trim(),
                hasClickHandler: false,
                hasVueBinding: false,
                clickable: false,
                testResult: 'pending'
            };
            
            // 检查点击处理器
            if (button.onclick || button.addEventListener) {
                result.hasClickHandler = true;
            }
            
            // 检查Vue绑定
            if (button.__vueParentComponent || button._vnode) {
                result.hasVueBinding = true;
            }
            
            // 检查是否可点击
            const style = window.getComputedStyle(button);
            if (style.pointerEvents !== 'none' && !button.disabled) {
                result.clickable = true;
            }
            
            // 模拟点击测试
            try {
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                
                let clickDetected = false;
                const clickListener = () => {
                    clickDetected = true;
                    result.testResult = 'success';
                };
                
                button.addEventListener('click', clickListener, { once: true });
                button.dispatchEvent(clickEvent);
                
                setTimeout(() => {
                    if (clickDetected) {
                        console.log(`✅ 按钮 "${result.text}" 点击事件正常`);
                        result.testResult = 'success';
                    } else {
                        console.warn(`⚠️ 按钮 "${result.text}" 点击事件未响应`);
                        result.testResult = 'warning';
                    }
                    
                    button.removeEventListener('click', clickListener);
                }, 100);
                
            } catch (error) {
                console.error(`❌ 按钮 "${result.text}" 测试失败:`, error);
                result.testResult = 'error';
                testResults.errors.push(`按钮 "${result.text}": ${error.message}`);
            }
            
            testResults.buttons.push(result);
        });
    });
    
    return testResults.buttons;
}

// 3. 检查Vue应用状态
function checkVueApplicationStatus() {
    console.log('\n📱 检查Vue应用状态...');
    
    const app = document.getElementById('app');
    if (!app) {
        console.error('❌ #app 容器不存在');
        testResults.errors.push('Vue应用容器不存在');
        return false;
    }
    
    if (!app.__vue_app__) {
        console.error('❌ Vue应用实例未挂载');
        testResults.errors.push('Vue应用实例未挂载');
        return false;
    }
    
    console.log('✅ Vue应用状态正常');
    
    // 检查路由器
    try {
        const vueApp = app.__vue_app__;
        if (vueApp.config && vueApp.config.globalProperties) {
            console.log('✅ Vue全局属性可访问');
            
            // 尝试访问路由器
            if (vueApp.config.globalProperties.$router) {
                console.log('✅ Vue Router实例存在');
            } else {
                console.warn('⚠️ Vue Router实例未找到');
                testResults.warnings.push('Vue Router实例未找到');
            }
        }
    } catch (error) {
        console.error('❌ Vue应用检查失败:', error);
        testResults.errors.push(`Vue应用检查: ${error.message}`);
    }
    
    return true;
}

// 4. 检查Element Plus组件
function checkElementPlusComponents() {
    console.log('\n🎨 检查Element Plus组件...');
    
    // 检查菜单组件
    const menus = document.querySelectorAll('.el-menu');
    console.log(`📋 找到 ${menus.length} 个菜单组件`);
    
    // 检查按钮组件
    const buttons = document.querySelectorAll('.el-button');
    console.log(`🔘 找到 ${buttons.length} 个按钮组件`);
    
    // 检查图标组件
    const icons = document.querySelectorAll('.el-icon');
    console.log(`🎨 找到 ${icons.length} 个图标组件`);
    
    if (menus.length === 0) {
        testResults.warnings.push('未找到Element Plus菜单组件');
    }
    
    if (buttons.length === 0) {
        testResults.warnings.push('未找到Element Plus按钮组件');
    }
    
    return { menus: menus.length, buttons: buttons.length, icons: icons.length };
}

// 5. 生成测试报告
function generateTestReport() {
    console.log('\n📊 生成测试报告...');
    
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            totalMenuItems: testResults.menuItems.length,
            successfulMenuItems: testResults.menuItems.filter(item => item.testResult === 'success').length,
            totalButtons: testResults.buttons.length,
            successfulButtons: testResults.buttons.filter(btn => btn.testResult === 'success').length,
            totalErrors: testResults.errors.length,
            totalWarnings: testResults.warnings.length
        },
        details: testResults
    };
    
    console.log('📈 测试摘要:');
    console.log(`  菜单项: ${report.summary.successfulMenuItems}/${report.summary.totalMenuItems} 成功`);
    console.log(`  按钮: ${report.summary.successfulButtons}/${report.summary.totalButtons} 成功`);
    console.log(`  错误: ${report.summary.totalErrors} 个`);
    console.log(`  警告: ${report.summary.totalWarnings} 个`);
    
    if (report.summary.totalErrors > 0) {
        console.log('\n❌ 发现的错误:');
        testResults.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (report.summary.totalWarnings > 0) {
        console.log('\n⚠️ 发现的警告:');
        testResults.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    // 计算成功率
    const menuSuccessRate = report.summary.totalMenuItems > 0 ? 
        (report.summary.successfulMenuItems / report.summary.totalMenuItems * 100).toFixed(1) : 0;
    const buttonSuccessRate = report.summary.totalButtons > 0 ? 
        (report.summary.successfulButtons / report.summary.totalButtons * 100).toFixed(1) : 0;
    
    console.log(`\n🎯 成功率: 菜单 ${menuSuccessRate}%, 按钮 ${buttonSuccessRate}%`);
    
    return report;
}

// 6. 主测试函数
function runLiveNavigationTest() {
    console.log('🚀 开始实时导航测试...\n');
    
    // 重置测试结果
    testResults.menuItems = [];
    testResults.buttons = [];
    testResults.errors = [];
    testResults.warnings = [];
    
    // 执行测试
    checkVueApplicationStatus();
    checkElementPlusComponents();
    testNavigationMenuItems();
    testActionButtons();
    
    // 等待异步测试完成后生成报告
    setTimeout(() => {
        const report = generateTestReport();
        
        // 将报告保存到全局变量
        window.iflytekNavigationTestReport = report;
        
        console.log('\n✅ 实时导航测试完成!');
        console.log('💡 测试报告已保存到 window.iflytekNavigationTestReport');
        
        return report;
    }, 1000);
}

// 导出到全局作用域
window.iflytekLiveTest = {
    runLiveNavigationTest,
    testNavigationMenuItems,
    testActionButtons,
    checkVueApplicationStatus,
    checkElementPlusComponents,
    generateTestReport,
    getResults: () => testResults
};

console.log('✅ 实时导航测试脚本已加载');
console.log('💡 使用 iflytekLiveTest.runLiveNavigationTest() 开始测试');
console.log('💡 使用 window.iflytekNavigationTestReport 查看测试报告');
