<!DOCTYPE>

<html>

  <head>
    <title>cytoscape-fcose.js demo</title>

      <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1">
      
      <!-- Bootstrap, popper, jQuery and filesaver - for demo purpose only -->
      <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js"></script>
      <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>      
      <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">      

      <script src="https://unpkg.com/cytoscape/dist/cytoscape.min.js"></script>
      <script src="https://unpkg.com/layout-base/layout-base.js"></script>
      <script src="https://unpkg.com/cose-base/cose-base.js"></script>
      <script src="https://unpkg.com/cytoscape-layout-utilities/cytoscape-layout-utilities.js"></script>

      <!-- for testing with local version of cytoscape.js -->
      <!--<script src="../cytoscape.js/build/cytoscape.js"></script>-->

      <script src="../cytoscape-fcose.js"></script>

      <style>
        body {
          font-family: helvetica neue, helvetica, liberation sans, arial, sans-serif;
          font-size: 14px;
        }

        #cy {
          position: relative;
          width: auto;
          height: 90%;
          z-index: 999;
          margin-left: 290px;
          right: 0px;
        }

        h1 {
          opacity: 1;
          color: #555;
          font-size: 15px;
          font-weight: bold;
          padding-top: 5px;
        }        
        
        /* The sidepanel menu */
        .sidepanel {
          height: auto; /* Specify a height */
          width: 290px; /* 0 width - change this with JavaScript */
          position: absolute; /* Stay in place */
          z-index: 1000; /* Stay on top */
          float: left;
          top: auto;
          left: 0;
          background-color: #b7ffff; /* Black*/
          overflow-x: hidden; /* Disable horizontal scroll */
          padding-top: 10px; /* Place content 10px from the top */
          padding-bottom: 10px; /* Place content 10px from the bottom */
          transition: 0s; /* 0.5 second transition effect to slide in the sidepanel */
        }

        table{
          margin-left: 4px;
          margin-right: auto;
          table-layout: fixed;
        }       
        
        /* The sidepanel links */
        table td {
          padding: 0px 8px 8px 8px;
          text-decoration: none;
          font-size: 13px;
          color: #555;
          transition: 0.3s;
          vertical-align: middle;
        }
        
        /* Style the button that is used to open the sidepanel */
        .button {
          font-size: 15px;
          color: #555;
          cursor: pointer;
          background-color: #b7ffff;
          padding: 5px 10px;
          border: none;
          margin-bottom: 5px;
        }
        
        .textField {
          padding-left: 5px;
        }
        
        .checkbox {
          margin-left: 0px;
        }        

        .button:hover {
          background-color: #0f0;
        }
        
        .btn-group-sm>.btn, .btn-sm { 
          font-size: 13px;
          background-color: #7d8991;
          border-color: #7d8991;
        }
        
        .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open>.dropdown-toggle.btn-primary {
            color: #fff;
            background-color: #60717d;
            border-color: #60717d; /*set the color you want here*/
        }  
        
        .btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active, .btn-outline-primary.active, .open>.dropdown-toggle.btn-primary {
            color: #fff;
            background-color: #60717d;
            border-color: #60717d; /*set the color you want here*/
        }        
        
        .form-control-sm { 
          font-size: 13px
        }

        .custom-select-sm {
          font-size: 13px
        }

        .custom-control-label {
          padding-top: 2px;
        }
        
        .custom-control-input:checked~.custom-control-label::before {
            color: #fff;
            border-color: #7d8991;
            background-color: #7d8991;
        }         
      </style>

      <script>
        document.addEventListener('DOMContentLoaded', function(){

          var cy = window.cy = cytoscape({
            container: document.getElementById('cy'),
            
            ready: function(){
              let layoutUtilities = this.layoutUtilities({
                desiredAspectRatio: this.width()/this.height()
              });
              this.nodes().forEach(function(node){
                let size = Math.random()*120+30;
                node.css("width", size);
                node.css("height", size);
              });
              this.layout({name: 'fcose'}).run();
            },          

            style: [
              {
                selector: 'node',
                style: {
                  'background-color': '#2B65EC'
                }
              },

              {
                selector: 'edge',
                style: {
                  'width': 3,
                  'line-color': '#2B65EC'
                }
              },
                            
              {
                selector: 'node:selected',
                style: {
                  'background-color': '#F08080',
                  'border-color': 'red'
                }
              },
              
              {
                selector: 'edge:selected',
                style: {
                  'line-color': '#F08080'
                }
              }                 
            ], 

          elements: [{"data":{"id":"glyph9","position":{"x":1452.639173965406,"y":609.3619416544145},"group":"nodes"}},
            {"data":{"id":"glyph0","position":{"x":1351.3490293961959,"y":518.9529901384763},"group":"nodes"}},
            {"data":{"id":"glyph6","position":{"x":1358.2854747390154,"y":707.9866590968695},"group":"nodes"}},
            {"data":{"id":"glyph8","position":{"x":1322.9939787691299,"y":614.6878118623499},"group":"nodes"}},
            {"data":{"id":"glyph7","position":{"x":1239.4852011317887,"y":543.2369849876238},"group":"nodes"}},
            {"data":{"id":"glyph12","position":{"x":841.6855140740067,"y":765.0152660242113},"group":"nodes"}},
            {"data":{"id":"glyph13","position":{"x":1019.5908382748769,"y":841.6087025848726},"group":"nodes"}},
            {"data":{"id":"glyph1","position":{"x":1231.2768042260652,"y":673.2683218469676},"group":"nodes"}},
            {"data":{"id":"glyph2","position":{"x":1039.8995038336504,"y":730.180116446269},"group":"nodes"}},
            {"data":{"id":"glyph15","position":{"x":569.5498472077387,"y":506.89980858075364},"group":"nodes"}},
            {"data":{"id":"glyph3","position":{"x":903.0347368937041,"y":654.3308627056822},"group":"nodes"}},
            {"data":{"id":"glyph17","position":{"x":1195.6310733031135,"y":820.9504141631944},"group":"nodes"}},
            {"data":{"id":"glyph10","position":{"x":1141.2404374322139,"y":732.3190922346248},"group":"nodes"}},
            {"data":{"id":"glyph19","position":{"x":893.1427762830865,"y":856.2695126662625},"group":"nodes"}},
            {"data":{"id":"glyph11","position":{"x":939.3335184518824,"y":758.3699048922733},"group":"nodes"}},
            {"data":{"id":"glyph18","position":{"x":770.4114528170364,"y":659.2220219290564},"group":"nodes"}},
            {"data":{"id":"glyph16","position":{"x":818.0111009023315,"y":564.8072603606723},"group":"nodes"}},
            {"data":{"id":"glyph22","position":{"x":651.1292498357636,"y":314.1387423188818},"group":"nodes"}},
            {"data":{"id":"glyph4","position":{"x":792.0076145303351,"y":454.0225025614517},"group":"nodes"}},
            {"data":{"id":"glyph23","position":{"x":704.0937009722281,"y":398.0421081673902},"group":"nodes"}},
            {"data":{"id":"glyph24","position":{"x":809.2974819306742,"y":231.7141323534711},"group":"nodes"}},
            {"data":{"id":"glyph25","position":{"x":890.826951363933,"y":299.74915938409947},"group":"nodes"}},
            {"data":{"id":"glyph20","position":{"x":786.2625869125006,"y":331.67766378118495},"group":"nodes"}},
            {"data":{"id":"glyph26","position":{"x":879.2981049664311,"y":389.27232563593486},"group":"nodes"}},
            {"data":{"id":"glyph35","position":{"x":627.088268638501,"y":40.089848876876886},"group":"nodes"}},
            {"data":{"id":"glyph36","position":{"x":329.6761506918384,"y":187.20503497360494},"group":"nodes"}},
            {"data":{"id":"glyph37","position":{"x":155.12947729633356,"y":379.5263531900425},"group":"nodes"}},
            {"data":{"id":"glyph38","position":{"x":70.13952165372024,"y":581.2691021233562},"group":"nodes"}},
            {"data":{"id":"glyph21","position":{"x":713.4639263718316,"y":229.06355211274115},"group":"nodes"}},
            {"data":{"id":"glyph42","position":{"x":523.848994074475,"y":108.47701882803744},"group":"nodes"}},
            {"data":{"id":"glyph41","position":{"x":718.966532806447,"y":116.46683749236911},"group":"nodes"}},
            {"data":{"id":"glyph31","position":{"x":621.3138039842713,"y":145.7168752444793},"group":"nodes"}},
            {"data":{"id":"glyph27","position":{"x":525.2099120385327,"y":210.92542274858295},"group":"nodes"}},
            {"data":{"id":"glyph32","position":{"x":426.3492127437995,"y":257.85665030680025},"group":"nodes"}},
            {"data":{"id":"glyph28","position":{"x":346.30926488002945,"y":344.4562152937847},"group":"nodes"}},
            {"data":{"id":"glyph43","position":{"x":363.54724181648487,"y":486.5705174517715},"group":"nodes"}},
            {"data":{"id":"glyph33","position":{"x":269.87972487503066,"y":430.2423722580144},"group":"nodes"}},
            {"data":{"id":"glyph29","position":{"x":227.86139816113416,"y":531.824141876398},"group":"nodes"}},
            {"data":{"id":"glyph39","position":{"x":104.77693104995387,"y":691.8382969303054},"group":"nodes"}},
            {"data":{"id":"glyph40","position":{"x":292.039416141131,"y":643.4009391289965},"group":"nodes"}},
            {"data":{"id":"glyph34","position":{"x":193.8304385062596,"y":632.9540034207419},"group":"nodes"}},
            {"data":{"id":"glyph30","position":{"x":205.4745704273754,"y":733.5181650652648},"group":"nodes"}},
            {"data":{"id":"glyph14","position":{"x":695.1248473196924,"y":482.8828321494848},"group":"nodes"}},
            {"data":{"id":"glyph5","position":{"x":721.6687687330186,"y":570.3868893775194},"group":"nodes"}},
            {"data":{"id":"e22","source":"glyph9","target":"glyph8","group":"edges"}},
            {"data":{"id":"e23","source":"glyph0","target":"glyph8","group":"edges"}},
            {"data":{"id":"e24","source":"glyph8","target":"glyph1","group":"edges"}},
            {"data":{"id":"e25","source":"glyph6","target":"glyph8","group":"edges"}},
            {"data":{"id":"e26","source":"glyph8","target":"glyph7","group":"edges"}},
            {"data":{"id":"e27","source":"glyph11","target":"glyph12","group":"edges"}},
            {"data":{"id":"e28","source":"glyph13","target":"glyph11","group":"edges"}},
            {"data":{"id":"e29","source":"glyph1","target":"glyph10","group":"edges"}},
            {"data":{"id":"e30","source":"glyph10","target":"glyph2","group":"edges"}},
            {"data":{"id":"e31","source":"glyph2","target":"glyph11","group":"edges"}},
            //{"data":{"id":"e32","source":"glyph11","target":"glyph3","group":"edges"}},
            {"data":{"id":"e33","source":"glyph14","target":"glyph4","group":"edges"}},
            {"data":{"id":"e34","source":"glyph15","target":"glyph14","group":"edges"}},
            {"data":{"id":"e35","source":"glyph3","target":"glyph16","group":"edges"}},
            {"data":{"id":"e36","source":"glyph16","target":"glyph5","group":"edges"}},
            {"data":{"id":"e37","source":"glyph16","target":"glyph4","group":"edges"}},
            {"data":{"id":"e38","source":"glyph17","target":"glyph10","group":"edges"}},
            {"data":{"id":"e39","source":"glyph19","target":"glyph11","group":"edges"}},
            {"data":{"id":"e40","source":"glyph18","target":"glyph16","group":"edges"}},
            //{"data":{"id":"e41","source":"glyph22","target":"glyph20","group":"edges"}},
            {"data":{"id":"e42","source":"glyph4","target":"glyph20","group":"edges"}},
            {"data":{"id":"e43","source":"glyph20","target":"glyph21","group":"edges"}},
            {"data":{"id":"e44","source":"glyph23","target":"glyph20","group":"edges"}},
            {"data":{"id":"e45","source":"glyph24","target":"glyph20","group":"edges"}},
            {"data":{"id":"e46","source":"glyph20","target":"glyph25","group":"edges"}},
            {"data":{"id":"e47","source":"glyph20","target":"glyph26","group":"edges"}},
            {"data":{"id":"e48","source":"glyph35","target":"glyph31","group":"edges"}},
            {"data":{"id":"e49","source":"glyph36","target":"glyph32","group":"edges"}},
            //{"data":{"id":"e50","source":"glyph37","target":"glyph33","group":"edges"}},
            {"data":{"id":"e51","source":"glyph38","target":"glyph34","group":"edges"}},
            {"data":{"id":"e52","source":"glyph21","target":"glyph31","group":"edges"}},
            {"data":{"id":"e53","source":"glyph42","target":"glyph31","group":"edges"}},
            {"data":{"id":"e54","source":"glyph31","target":"glyph41","group":"edges"}},
            {"data":{"id":"e55","source":"glyph31","target":"glyph27","group":"edges"}},
            {"data":{"id":"e56","source":"glyph27","target":"glyph32","group":"edges"}},
            {"data":{"id":"e57","source":"glyph32","target":"glyph28","group":"edges"}},
            {"data":{"id":"e58","source":"glyph28","target":"glyph33","group":"edges"}},
            //{"data":{"id":"e59","source":"glyph33","target":"glyph43","group":"edges"}},
            //{"data":{"id":"e60","source":"glyph33","target":"glyph29","group":"edges"}},
            {"data":{"id":"e61","source":"glyph29","target":"glyph34","group":"edges"}},
            //{"data":{"id":"e62","source":"glyph39","target":"glyph34","group":"edges"}},
            {"data":{"id":"e63","source":"glyph34","target":"glyph40","group":"edges"}},
            {"data":{"id":"e64","source":"glyph34","target":"glyph30","group":"edges"}},
            {"data":{"id":"e65","source":"glyph14","target":"glyph5","group":"edges"}},
            {"data":{"id":"e66","source":"glyph33","target":"glyph35","group":"edges"}},
            {"data":{"id":"e67","source":"glyph13","target":"glyph22","group":"edges"}},
            {"data":{"id":"e68","source":"glyph17","target":"glyph6","group":"edges"}},
            {"data":{"id":"e69","source":"glyph25","target":"glyph27","group":"edges"}}]
          });          
          
          document.getElementById("randomizeButton").addEventListener("click", function(){
            var layout = cy.layout({
              name: 'random',
              animate: true,
              animationDuration: 1000
            });
            
            layout.run();        
          });          
          
          document.getElementById("fcoseButton").addEventListener("click", function(){
            let qualityItem = document.getElementById("quality");
            var layout = cy.layout({
              name: 'fcose',
              quality: qualityItem.options[qualityItem.selectedIndex].value,
              randomize: !(document.getElementById("randomize").checked),
              animate: document.getElementById("animate").checked,
              animationEasing: 'ease-out',
              fit: document.getElementById("fit").checked,
              uniformNodeDimensions: document.getElementById("uniformNodeDimensions").checked,
              packComponents: document.getElementById("packComponents").checked,
              tile: document.getElementById("tile").checked,
              nodeRepulsion: parseFloat(document.getElementById("nodeRepulsion").value),
              idealEdgeLength: parseFloat(document.getElementById("idealEdgeLength").value),
              edgeElasticity: parseFloat(document.getElementById("edgeElasticity").value),
              nestingFactor: parseFloat(document.getElementById("nestingFactor").value),
              gravity: parseFloat(document.getElementById("gravity").value),
              gravityRange: parseFloat(document.getElementById("gravityRange").value),
              gravityCompound: parseFloat(document.getElementById("gravityCompound").value),
              gravityRangeCompound: parseFloat(document.getElementById("gravityRangeCompound").value),
              numIter: parseFloat(document.getElementById("numIter").value),
              tilingPaddingVertical: parseFloat(document.getElementById("tilingPaddingVertical").value),
              tilingPaddingHorizontal: parseFloat(document.getElementById("tilingPaddingHorizontal").value),
              initialEnergyOnIncremental: document.getElementById("initialEnergyOnIncremental").value,
              step:"all"
            });
            
            layout.run();        
          });

        });
      </script>
  </head>

  <body>
          <h1 class="ml-2">cytoscape-fcose demo</h1>
          <div style='width: 300px; position: absolute;'>
            <button id="randomizeButton" class="btn btn-primary btn-sm mb-2 ml-2">Randomize</button>&nbsp
            <button id="fcoseButton" class="btn btn-primary btn-sm mb-2">fCoSE</button>&nbsp
          
            <div id="mySidepanel" class="sidepanel">
              <table>
                <tr>
                  <td><span class="add-on layout-text" title="Quality of the layout"> Quality </span></td>
                  <td>            
                    <select id="quality" class='custom-select custom-select-sm'>
                      <option value="draft">draft</option>
                      <option value="default" selected="">default</option>
                      <option value="proof">proof</option>
                    </select>
                  </td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Whether to enable incremental mode"> Incremental </span></td>
                  <td>        
                    <div class="custom-control custom-control-inline custom-checkbox">
                      <input type="checkbox" class="custom-control-input" id="randomize" name="incremental">
                      <label class="custom-control-label" for="randomize"></label>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Whether to perform animation after layout"> Animate </span></td>
                  <td>
                    <div class="custom-control custom-control-inline custom-checkbox">
                      <input type="checkbox" class="custom-control-input" id="animate" name="animate" checked>
                      <label class="custom-control-label" for="animate"></label>
                    </div>                  
                  </td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Whether to fit the viewport to the repositioned nodes"> Fit </span></td>
                  <td>
                    <div class="custom-control custom-control-inline custom-checkbox">
                      <input type="checkbox" class="custom-control-input" id="fit" name="fit" checked>
                      <label class="custom-control-label" for="fit"></label>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Whether nodes are of uniform dimensions"> Uniform Node Dimensions </span></td>
                  <td>
                    <div class="custom-control custom-control-inline custom-checkbox">
                      <input type="checkbox" class="custom-control-input" id="uniformNodeDimensions" name="uniformNodeDimensions">
                      <label class="custom-control-label" for="uniformNodeDimensions"></label>
                    </div> 
                  </td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Whether to pack components"> Pack Components to Window</span></td>
                  <td>
                    <div class="custom-control custom-control-inline custom-checkbox">
                      <input type="checkbox" class="custom-control-input" id="packComponents" name="packComponents" checked>
                      <label class="custom-control-label" for="packComponents"></label>
                    </div>                     
                  </td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Whether to tile disconnected nodes"> Tile Disconnected </span></td>
                  <td>
                    <div class="custom-control custom-control-inline custom-checkbox">
                      <input type="checkbox" class="custom-control-input" id="tile" name="tile" checked>
                      <label class="custom-control-label" for="tile"></label>
                    </div>                     
                  </td>
                </tr>              
                <tr>
                  <td><span class="add-on layout-text" title="Node repulsion (non overlapping) multiplier"> Node Repulsion </span></td>
                  <td><input id="nodeRepulsion" class="textField form-control form-control-sm" type="text" value="4500" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Ideal (intra-graph) edge length"> Ideal Edge Length </span></td>
                  <td><input id="idealEdgeLength" class="textField form-control form-control-sm" type="text" value="50" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Divisor to compute edge forces"> Edge Elasticity </span></td>
                  <td><input id="edgeElasticity" class="textField form-control form-control-sm" type="text" value="0.45" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Nesting factor (multiplier) to compute ideal edge length for inter-graph edges"> Nesting Factor </span></td>
                  <td><input id="nestingFactor" class="textField form-control form-control-sm" type="text" value="0.1" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Gravity force (constant)"> Gravity </span></td>
                  <td><input id="gravity" class="textField form-control form-control-sm" type="text" value="0.25" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Gravity range (constant)"> Gravity Range </span></td>
                  <td><input id="gravityRange" class="textField form-control form-control-sm" type="text" value="3.8" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Gravity force (constant) for compounds"> Compound Gravity </span></td>
                  <td><input id="gravityCompound" class="textField form-control form-control-sm" type="text" value="1" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Gravity range (constant) for compounds"> Compound Gravity Range </span></td>
                  <td><input id="gravityRangeCompound" class="textField form-control form-control-sm" type="text" value="1.5" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Maximum number of iterations to perform"> Number of Iterations </span></td>
                  <td><input id="numIter" class="textField form-control form-control-sm" type="text" value="2500" size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Amount of vertical space to put between degree zero nodes during tiling"> Tiling Vertical Padding </span></td>
                  <td><input id="tilingPaddingVertical" class="textField form-control form-control-sm" type="text" value="10" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Amount of horizontal space to put between degree zero nodes during tiling"> Tiling Horizontal Padding </span></td>
                  <td><input id="tilingPaddingHorizontal" class="textField form-control form-control-sm" type="text" value="10" maxlength=5 size="5"></td>
                </tr>
                <tr>
                  <td><span class="add-on layout-text" title="Initial cooling factor for incremental layout"> Incremental Cooling Factor </span></td>
                  <td><input id="initialEnergyOnIncremental" class="textField form-control form-control-sm" type="text" value="0.3" maxlength=5 size="5"></td>
                </tr>               
              </table>

            </div>
          </div>
          <div id="cy"></div>      

  </body>

</html>
