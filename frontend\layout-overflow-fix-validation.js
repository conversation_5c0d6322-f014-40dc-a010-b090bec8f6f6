/**
 * iFlytek 星火大模型智能面试系统 - 布局溢出修复验证
 * Layout Overflow Fix Validation for iFlytek Spark Interview System
 * 
 * 验证目标：
 * - 检查内容是否还会超出紫色背景区域
 * - 验证容器宽度是否正确设置
 * - 确认响应式布局是否正常工作
 * - 检查文字是否在可视区域内
 */

console.log('🔍 开始验证布局溢出修复效果...')
console.log('=' .repeat(60))

// 验证结果对象
const validationResults = {
  containerWidths: {
    passed: 0,
    failed: 0,
    issues: []
  },
  overflowChecks: {
    passed: 0,
    failed: 0,
    issues: []
  },
  responsiveLayout: {
    passed: 0,
    failed: 0,
    issues: []
  },
  textVisibility: {
    passed: 0,
    failed: 0,
    issues: []
  },
  summary: {
    totalChecks: 0,
    passedChecks: 0,
    failedChecks: 0,
    successRate: 0
  }
}

// 1. 检查容器宽度设置
function validateContainerWidths() {
  console.log('\n📐 1. 验证容器宽度设置')
  console.log('-'.repeat(40))
  
  const containerSelectors = [
    '.container',
    '.demo-container',
    '.optimized-container',
    '.optimized-container-narrow',
    '.optimized-container-wide'
  ]
  
  containerSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    
    if (elements.length === 0) {
      console.log(`⚠️  ${selector}: 未找到元素`)
      return
    }
    
    elements.forEach((element, index) => {
      const computedStyle = window.getComputedStyle(element)
      const maxWidth = computedStyle.maxWidth
      const width = computedStyle.width
      const boxSizing = computedStyle.boxSizing
      
      // 检查最大宽度是否合理
      const maxWidthValue = parseInt(maxWidth)
      const viewportWidth = window.innerWidth
      
      if (maxWidthValue > viewportWidth) {
        validationResults.containerWidths.failed++
        validationResults.containerWidths.issues.push(
          `${selector}[${index}]: 最大宽度 ${maxWidth} 超过视口宽度 ${viewportWidth}px`
        )
        console.log(`❌ ${selector}[${index}]: 最大宽度过大 (${maxWidth})`)
      } else {
        validationResults.containerWidths.passed++
        console.log(`✅ ${selector}[${index}]: 宽度设置正确 (${maxWidth})`)
      }
      
      // 检查box-sizing
      if (boxSizing !== 'border-box') {
        validationResults.containerWidths.failed++
        validationResults.containerWidths.issues.push(
          `${selector}[${index}]: box-sizing 应为 border-box，当前为 ${boxSizing}`
        )
        console.log(`❌ ${selector}[${index}]: box-sizing 不正确 (${boxSizing})`)
      } else {
        validationResults.containerWidths.passed++
        console.log(`✅ ${selector}[${index}]: box-sizing 正确`)
      }
    })
  })
}

// 2. 检查水平溢出
function validateOverflowChecks() {
  console.log('\n🌊 2. 验证水平溢出检查')
  console.log('-'.repeat(40))
  
  // 检查body和html的溢出设置
  const body = document.body
  const html = document.documentElement
  
  const bodyOverflowX = window.getComputedStyle(body).overflowX
  const htmlOverflowX = window.getComputedStyle(html).overflowX
  
  if (bodyOverflowX === 'hidden') {
    validationResults.overflowChecks.passed++
    console.log('✅ body overflow-x: hidden')
  } else {
    validationResults.overflowChecks.failed++
    validationResults.overflowChecks.issues.push(`body overflow-x 应为 hidden，当前为 ${bodyOverflowX}`)
    console.log(`❌ body overflow-x: ${bodyOverflowX}`)
  }
  
  if (htmlOverflowX === 'hidden') {
    validationResults.overflowChecks.passed++
    console.log('✅ html overflow-x: hidden')
  } else {
    validationResults.overflowChecks.failed++
    validationResults.overflowChecks.issues.push(`html overflow-x 应为 hidden，当前为 ${htmlOverflowX}`)
    console.log(`❌ html overflow-x: ${htmlOverflowX}`)
  }
  
  // 检查是否有水平滚动条
  const hasHorizontalScrollbar = document.body.scrollWidth > window.innerWidth
  
  if (!hasHorizontalScrollbar) {
    validationResults.overflowChecks.passed++
    console.log('✅ 无水平滚动条')
  } else {
    validationResults.overflowChecks.failed++
    validationResults.overflowChecks.issues.push(
      `检测到水平滚动条: scrollWidth(${document.body.scrollWidth}) > innerWidth(${window.innerWidth})`
    )
    console.log(`❌ 检测到水平滚动条: ${document.body.scrollWidth} > ${window.innerWidth}`)
  }
}

// 3. 检查响应式布局
function validateResponsiveLayout() {
  console.log('\n📱 3. 验证响应式布局')
  console.log('-'.repeat(40))
  
  const viewportWidth = window.innerWidth
  console.log(`当前视口宽度: ${viewportWidth}px`)
  
  // 检查网格布局
  const gridElements = document.querySelectorAll('.features-grid, .demo-features-container, .tech-grid')
  
  gridElements.forEach((element, index) => {
    const computedStyle = window.getComputedStyle(element)
    const gridTemplateColumns = computedStyle.gridTemplateColumns
    
    if (viewportWidth <= 768) {
      // 移动端应该是单列
      if (gridTemplateColumns.includes('1fr') && !gridTemplateColumns.includes('1fr 1fr')) {
        validationResults.responsiveLayout.passed++
        console.log(`✅ 网格元素[${index}]: 移动端单列布局正确`)
      } else {
        validationResults.responsiveLayout.failed++
        validationResults.responsiveLayout.issues.push(
          `网格元素[${index}]: 移动端应为单列，当前为 ${gridTemplateColumns}`
        )
        console.log(`❌ 网格元素[${index}]: 移动端布局不正确 (${gridTemplateColumns})`)
      }
    } else {
      // 桌面端应该是多列
      validationResults.responsiveLayout.passed++
      console.log(`✅ 网格元素[${index}]: 桌面端多列布局`)
    }
  })
  
  // 检查统计数据布局
  const statsElements = document.querySelectorAll('.stats-display, .stats-grid')
  
  statsElements.forEach((element, index) => {
    const computedStyle = window.getComputedStyle(element)
    const flexDirection = computedStyle.flexDirection
    
    if (viewportWidth <= 768) {
      if (flexDirection === 'column') {
        validationResults.responsiveLayout.passed++
        console.log(`✅ 统计元素[${index}]: 移动端垂直布局正确`)
      } else {
        validationResults.responsiveLayout.failed++
        validationResults.responsiveLayout.issues.push(
          `统计元素[${index}]: 移动端应为垂直布局，当前为 ${flexDirection}`
        )
        console.log(`❌ 统计元素[${index}]: 移动端布局不正确 (${flexDirection})`)
      }
    } else {
      validationResults.responsiveLayout.passed++
      console.log(`✅ 统计元素[${index}]: 桌面端水平布局`)
    }
  })
}

// 4. 检查文字可见性
function validateTextVisibility() {
  console.log('\n👁️  4. 验证文字可见性')
  console.log('-'.repeat(40))
  
  const textSelectors = [
    '.hero-title',
    '.demo-title',
    '.section-title',
    '.hero-subtitle',
    '.demo-subtitle',
    '.feature-title',
    '.feature-description'
  ]
  
  textSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    
    elements.forEach((element, index) => {
      const rect = element.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(element)
      
      // 检查元素是否在视口内
      const isVisible = rect.width > 0 && rect.height > 0
      const isInViewport = rect.left >= 0 && rect.right <= window.innerWidth
      
      if (isVisible && isInViewport) {
        validationResults.textVisibility.passed++
        console.log(`✅ ${selector}[${index}]: 文字可见且在视口内`)
      } else {
        validationResults.textVisibility.failed++
        validationResults.textVisibility.issues.push(
          `${selector}[${index}]: 文字不可见或超出视口 (left: ${rect.left}, right: ${rect.right}, width: ${rect.width})`
        )
        console.log(`❌ ${selector}[${index}]: 文字可见性问题`)
      }
      
      // 检查文字是否被截断
      const overflow = computedStyle.overflow
      const textOverflow = computedStyle.textOverflow
      
      if (textOverflow === 'ellipsis' && element.scrollWidth > element.clientWidth) {
        validationResults.textVisibility.failed++
        validationResults.textVisibility.issues.push(
          `${selector}[${index}]: 文字被截断 (scrollWidth: ${element.scrollWidth}, clientWidth: ${element.clientWidth})`
        )
        console.log(`⚠️  ${selector}[${index}]: 文字被截断`)
      }
    })
  })
}

// 5. 生成验证报告
function generateValidationReport() {
  console.log('\n📊 验证报告')
  console.log('=' .repeat(60))
  
  // 计算总体统计
  const totalPassed = validationResults.containerWidths.passed + 
                     validationResults.overflowChecks.passed + 
                     validationResults.responsiveLayout.passed + 
                     validationResults.textVisibility.passed
  
  const totalFailed = validationResults.containerWidths.failed + 
                     validationResults.overflowChecks.failed + 
                     validationResults.responsiveLayout.failed + 
                     validationResults.textVisibility.failed
  
  const totalChecks = totalPassed + totalFailed
  const successRate = totalChecks > 0 ? ((totalPassed / totalChecks) * 100).toFixed(1) : 0
  
  validationResults.summary = {
    totalChecks,
    passedChecks: totalPassed,
    failedChecks: totalFailed,
    successRate: parseFloat(successRate)
  }
  
  console.log(`📈 总体成功率: ${successRate}% (${totalPassed}/${totalChecks})`)
  console.log(`✅ 通过检查: ${totalPassed}`)
  console.log(`❌ 失败检查: ${totalFailed}`)
  
  // 详细报告
  console.log('\n📋 详细检查结果:')
  console.log(`🏗️  容器宽度: ${validationResults.containerWidths.passed}✅ ${validationResults.containerWidths.failed}❌`)
  console.log(`🌊 溢出检查: ${validationResults.overflowChecks.passed}✅ ${validationResults.overflowChecks.failed}❌`)
  console.log(`📱 响应式布局: ${validationResults.responsiveLayout.passed}✅ ${validationResults.responsiveLayout.failed}❌`)
  console.log(`👁️  文字可见性: ${validationResults.textVisibility.passed}✅ ${validationResults.textVisibility.failed}❌`)
  
  // 问题列表
  const allIssues = [
    ...validationResults.containerWidths.issues,
    ...validationResults.overflowChecks.issues,
    ...validationResults.responsiveLayout.issues,
    ...validationResults.textVisibility.issues
  ]
  
  if (allIssues.length > 0) {
    console.log('\n⚠️  发现的问题:')
    allIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`)
    })
  } else {
    console.log('\n🎉 未发现任何布局问题！')
  }
  
  return validationResults
}

// 执行验证
function runValidation() {
  try {
    validateContainerWidths()
    validateOverflowChecks()
    validateResponsiveLayout()
    validateTextVisibility()
    
    const results = generateValidationReport()
    
    console.log('\n🏁 布局溢出修复验证完成')
    console.log('=' .repeat(60))
    
    return results
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error)
    return null
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runValidation)
  } else {
    runValidation()
  }
  
  // 导出到全局作用域以便手动调用
  window.validateLayoutFix = runValidation
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runValidation }
}
