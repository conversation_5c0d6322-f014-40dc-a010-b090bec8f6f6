/**
 * AI分析功能测试脚本
 * 测试四个不同分析类型的差异化内容
 */

// 模拟导入服务
import enhancedIflytekSparkService from './src/services/enhancedIflytekSparkService.js'

// 测试数据
const testAnalysisRequest = {
  dataScope: 'position_management',
  currentData: {
    positions: [
      { id: 1, title: 'AI工程师', domain: 'AI', level: 'senior', status: 'active' },
      { id: 2, title: '大数据工程师', domain: '大数据', level: 'mid', status: 'active' },
      { id: 3, title: '物联网开发工程师', domain: '物联网', level: 'junior', status: 'urgent' }
    ],
    stats: {
      activePositions: 28,
      totalPositions: 156,
      urgentPositions: 5
    },
    filters: {
      domain: '',
      level: '',
      status: ''
    }
  }
}

// 测试函数
async function testAIAnalysis() {
  console.log('🧪 开始测试AI分析功能...')
  
  const analysisTypes = [
    'position_trends',
    'recruitment_optimization', 
    'candidate_matching',
    'market_insights'
  ]
  
  for (const type of analysisTypes) {
    console.log(`\n📊 测试分析类型: ${type}`)
    
    try {
      const request = {
        ...testAnalysisRequest,
        type: type
      }
      
      const result = await enhancedIflytekSparkService.generateDataDrivenInsights(request)
      
      console.log(`✅ ${type} 分析成功`)
      console.log(`📝 摘要: ${result.summary}`)
      console.log(`💡 洞察数量: ${result.insights?.length || 0}`)
      console.log(`🎯 推荐数量: ${result.recommendations?.length || 0}`)
      console.log(`📈 预测指标: ${Object.keys(result.predictions || {}).length}`)
      
      // 检查特定字段
      if (type === 'position_trends' && result.marketAnalysis) {
        console.log(`🔥 热门技能: ${result.marketAnalysis.hotSkills?.length || 0}`)
      }
      
      if (type === 'recruitment_optimization' && result.processOptimization) {
        console.log(`⚡ 流程瓶颈: ${result.processOptimization.currentBottlenecks?.length || 0}`)
      }
      
      if (type === 'candidate_matching' && result.matchingStrategies) {
        console.log(`🎯 匹配策略: ${result.matchingStrategies.length || 0}`)
      }
      
      if (type === 'market_insights' && result.industryInsights) {
        console.log(`🏭 热门行业: ${result.industryInsights.hotSectors?.length || 0}`)
      }
      
    } catch (error) {
      console.error(`❌ ${type} 分析失败:`, error.message)
    }
  }
  
  console.log('\n🎉 AI分析功能测试完成!')
}

// 运行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.testAIAnalysis = testAIAnalysis
  console.log('🌐 测试函数已添加到 window.testAIAnalysis，请在浏览器控制台中调用')
} else {
  // Node.js环境
  testAIAnalysis().catch(console.error)
}

export { testAIAnalysis }
