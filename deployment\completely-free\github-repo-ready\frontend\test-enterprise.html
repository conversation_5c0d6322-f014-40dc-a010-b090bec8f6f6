<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业管理界面测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #1890ff;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: #0066cc;
        }
        
        .status-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status-error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            color: #ff4d4f;
        }
        
        .code-block {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔧 企业管理界面访问测试</h1>
        
        <div class="test-section">
            <h3>📍 当前状态检查</h3>
            <div class="status-info">
                <strong>开发服务器：</strong> http://localhost:5173/<br>
                <strong>目标路由：</strong> /enterprise<br>
                <strong>组件文件：</strong> EnterpriseDashboard.vue
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚀 快速访问测试</h3>
            <p>点击以下按钮测试不同的访问方式：</p>
            
            <a href="http://localhost:5173/enterprise" class="test-button" target="_blank">
                直接访问 /enterprise
            </a>
            
            <a href="http://localhost:5173/#/enterprise" class="test-button" target="_blank">
                Hash路由 /#/enterprise
            </a>
            
            <button class="test-button" onclick="testAjaxRoute()">
                AJAX测试路由
            </button>
            
            <button class="test-button" onclick="checkServerStatus()">
                检查服务器状态
            </button>
        </div>
        
        <div class="test-section">
            <h3>🔍 问题排查步骤</h3>
            
            <div class="status-info">
                <strong>步骤1：检查开发服务器</strong><br>
                确认 Vite 开发服务器正在运行在 localhost:5173
            </div>
            
            <div class="status-info">
                <strong>步骤2：检查路由模式</strong><br>
                Vue Router 可能使用 history 模式或 hash 模式
            </div>
            
            <div class="status-info">
                <strong>步骤3：检查组件导入</strong><br>
                确认 EnterpriseDashboard.vue 文件没有语法错误
            </div>
        </div>
        
        <div class="test-section">
            <h3>🛠️ 手动验证代码</h3>
            <p>在浏览器控制台中运行以下代码：</p>
            
            <div class="code-block">
// 检查当前路由
console.log('当前URL:', window.location.href);

// 测试fetch请求
fetch('/enterprise')
  .then(response => {
    console.log('响应状态:', response.status);
    console.log('响应类型:', response.headers.get('content-type'));
    return response.text();
  })
  .then(html => {
    console.log('响应内容长度:', html.length);
    if (html.includes('EnterpriseDashboard')) {
      console.log('✅ 找到企业管理组件');
    } else {
      console.log('❌ 未找到企业管理组件');
    }
  })
  .catch(error => {
    console.error('请求失败:', error);
  });
            </div>
            
            <button class="test-button" onclick="copyCode()">复制代码</button>
        </div>
        
        <div class="test-section">
            <h3>⚡ 可能的解决方案</h3>
            
            <div class="status-error">
                <strong>如果出现404错误，可能的原因：</strong>
                <ul>
                    <li>Vue Router 配置问题</li>
                    <li>组件文件路径错误</li>
                    <li>开发服务器配置问题</li>
                    <li>浏览器缓存问题</li>
                </ul>
            </div>
            
            <div class="status-info">
                <strong>建议的解决步骤：</strong>
                <ol>
                    <li>清除浏览器缓存 (Ctrl+F5)</li>
                    <li>重启开发服务器</li>
                    <li>检查路由配置文件</li>
                    <li>验证组件文件是否存在</li>
                </ol>
            </div>
        </div>
        
        <div id="test-results" class="test-section" style="display: none;">
            <h3>📊 测试结果</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        function testAjaxRoute() {
            const resultsDiv = document.getElementById('test-results');
            const contentDiv = document.getElementById('results-content');
            
            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = '<p>正在测试路由...</p>';
            
            fetch('/enterprise')
                .then(response => {
                    const status = response.status;
                    const statusText = response.statusText;
                    
                    if (status === 200) {
                        contentDiv.innerHTML = `
                            <div class="status-info">
                                <strong>✅ 路由测试成功</strong><br>
                                状态码: ${status} ${statusText}<br>
                                路由可以正常访问
                            </div>
                        `;
                    } else {
                        contentDiv.innerHTML = `
                            <div class="status-error">
                                <strong>❌ 路由测试失败</strong><br>
                                状态码: ${status} ${statusText}<br>
                                可能存在路由配置问题
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    contentDiv.innerHTML = `
                        <div class="status-error">
                            <strong>❌ 请求失败</strong><br>
                            错误信息: ${error.message}<br>
                            可能是服务器连接问题
                        </div>
                    `;
                });
        }
        
        function checkServerStatus() {
            const resultsDiv = document.getElementById('test-results');
            const contentDiv = document.getElementById('results-content');
            
            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = '<p>正在检查服务器状态...</p>';
            
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        contentDiv.innerHTML = `
                            <div class="status-info">
                                <strong>✅ 服务器运行正常</strong><br>
                                主页可以正常访问<br>
                                问题可能在于特定路由配置
                            </div>
                        `;
                    } else {
                        contentDiv.innerHTML = `
                            <div class="status-error">
                                <strong>❌ 服务器响应异常</strong><br>
                                状态码: ${response.status}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    contentDiv.innerHTML = `
                        <div class="status-error">
                            <strong>❌ 无法连接服务器</strong><br>
                            请检查开发服务器是否正在运行
                        </div>
                    `;
                });
        }
        
        function copyCode() {
            const codeText = document.querySelector('.code-block').textContent;
            navigator.clipboard.writeText(codeText).then(function() {
                alert('代码已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
