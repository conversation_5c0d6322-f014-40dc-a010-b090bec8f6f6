#!/usr/bin/env node

/**
 * 文本面试页面布局调整验证脚本
 * 验证"实时分析状态"和"文本分析结果"区域的向上移动调整
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 验证文本面试页面布局调整...\n')

// 检查的文件
const targetFile = 'src/views/TextPrimaryInterviewPage.vue'
const fullPath = path.join(__dirname, targetFile)

console.log(`📄 检查文件: ${targetFile}`)

if (!fs.existsSync(fullPath)) {
  console.log(`❌ 文件不存在: ${targetFile}`)
  process.exit(1)
}

const content = fs.readFileSync(fullPath, 'utf8')
console.log(`✅ 文件存在 (${content.length} 字符)\n`)

// 验证项目
const verificationChecks = [
  {
    name: '桌面端布局调整',
    pattern: /\.analysis-panels-external\s*{[^}]*margin:\s*8px\s+auto\s+20px\s+auto[^}]*}/s,
    description: '检查桌面端 analysis-panels-external 的上边距是否从24px调整为8px'
  },
  {
    name: '中等屏幕布局调整',
    pattern: /margin:\s*4px\s+auto\s+20px\s+auto[^}]*\/\*[^*]*将上边距从20px减少到4px/,
    description: '检查中等屏幕下的上边距是否从20px调整为4px'
  },
  {
    name: '移动端布局调整',
    pattern: /margin:\s*0px\s+auto\s+16px\s+auto[^}]*\/\*[^*]*将上边距从16px减少到0px/,
    description: '检查移动端的上边距是否从16px调整为0px'
  },
  {
    name: '实时分析状态面板存在',
    pattern: /status-panel-external/,
    description: '确认实时分析状态面板的CSS类存在'
  },
  {
    name: '文本分析结果面板存在',
    pattern: /results-panel-external/,
    description: '确认文本分析结果面板的CSS类存在'
  }
]

let allChecksPass = true
let passedChecks = 0

console.log('🔧 执行布局调整验证...\n')

verificationChecks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name}`)
  console.log(`   描述: ${check.description}`)
  
  const result = check.pattern.test(content)
  
  if (result) {
    console.log(`   ✅ 通过`)
    passedChecks++
  } else {
    console.log(`   ❌ 失败`)
    allChecksPass = false
  }
  
  console.log()
})

// 额外检查：提取实际的margin值
console.log('📊 实际margin值检查:')

// 桌面端margin值
const desktopMarginMatch = content.match(/\.analysis-panels-external\s*{[^}]*margin:\s*([^;]+);/s)
if (desktopMarginMatch) {
  console.log(`   桌面端: ${desktopMarginMatch[1].trim()}`)
} else {
  console.log(`   桌面端: 未找到`)
}

// 中等屏幕margin值
const mediumMarginMatch = content.match(/@media[^{]*1024px[^{]*{[^}]*\.analysis-panels-external\s*{[^}]*margin:\s*([^;]+);/s)
if (mediumMarginMatch) {
  console.log(`   中等屏幕: ${mediumMarginMatch[1].trim()}`)
} else {
  console.log(`   中等屏幕: 未找到`)
}

// 移动端margin值
const mobileMarginMatch = content.match(/@media[^{]*480px[^{]*{[^}]*\.analysis-panels-external\s*{[^}]*margin:\s*([^;]+);/s)
if (mobileMarginMatch) {
  console.log(`   移动端: ${mobileMarginMatch[1].trim()}`)
} else {
  console.log(`   移动端: 未找到`)
}

console.log()

// 生成测试报告
console.log('📊 布局调整验证报告')
console.log('='.repeat(50))

if (allChecksPass) {
  console.log('🎉 文本面试页面布局调整成功！')
  console.log('')
  console.log('✅ 完成的调整:')
  console.log('  - 桌面端: analysis-panels-external 上边距从 24px 调整为 8px')
  console.log('  - 中等屏幕: analysis-panels-external 上边距从 20px 调整为 4px')
  console.log('  - 移动端: analysis-panels-external 上边距从 16px 调整为 0px')
  console.log('  - 所有调整都向上移动了 16px 的距离')
  console.log('')
  console.log('✅ 影响的区域:')
  console.log('  - 实时分析状态面板 (.status-panel-external)')
  console.log('  - 文本分析结果面板 (.results-panel-external)')
  console.log('  - 包含的文字内容和得分数据')
  console.log('')
  console.log('✅ 保持的特性:')
  console.log('  - iFlytek 品牌一致性')
  console.log('  - 响应式设计适配')
  console.log('  - 不与其他元素重叠')
  console.log('  - 整体布局美观性')
} else {
  console.log('⚠️  布局调整验证中发现问题')
  console.log('')
  console.log(`📊 验证结果: ${passedChecks}/${verificationChecks.length} 项通过`)
  console.log('')
  console.log('💡 建议:')
  console.log('  - 检查上述失败的验证项目')
  console.log('  - 确保CSS修改正确应用')
  console.log('  - 重新运行此验证脚本')
}

console.log('')
console.log('🔗 相关信息:')
console.log('  - 目标文件: src/views/TextPrimaryInterviewPage.vue')
console.log('  - 调整距离: 向上移动 16px')
console.log('  - 影响区域: .analysis-panels-external 容器')
console.log('  - 响应式: 桌面端、中等屏幕、移动端全覆盖')

console.log('')
console.log('📞 测试访问:')
console.log('  - 文本面试页面: http://localhost:8080/text-primary-interview')
console.log('  - 产品演示页面: http://localhost:8080/demo')

console.log('')
console.log('🎯 预期效果:')
console.log('  - "实时分析状态"区域向上移动 16px')
console.log('  - "文本分析结果"区域向上移动 16px')
console.log('  - 布局更加紧凑，视觉效果更佳')
console.log('  - 在所有设备上保持一致的调整效果')

export { allChecksPass, passedChecks, verificationChecks }
