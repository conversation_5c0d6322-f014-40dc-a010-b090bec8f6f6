/******************************************************************************
 * Copyright 2021 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/
import type { GeneratorNode } from './generator-node.js';
import type { TraceRegion } from './generator-tracing.js';
export declare function processGeneratorNode(node: GeneratorNode, defaultIndentation?: string | number): {
    text: string;
    trace: TraceRegion;
};
//# sourceMappingURL=node-processor.d.ts.map