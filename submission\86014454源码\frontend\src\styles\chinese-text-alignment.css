/**
 * iFlytek 星火大模型智能面试系统 - 中文文本对齐优化
 * Chinese Text Alignment Optimization for iFlytek Spark Interview System
 *
 * 版本: 1.0
 * 更新: 2025-07-20
 *
 * 功能特性:
 * - 中文字体垂直居中优化
 * - Microsoft YaHei字体基线调整
 * - 按钮和标题文本对齐
 * - 响应式中文显示优化
 */

/* ===== 中文字体基础对齐 ===== */

/* Microsoft YaHei 字体基线调整 */
body,
.el-button,
.el-menu-item,
.el-card__header,
h1, h2, h3, h4, h5, h6 {
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  /* 中文字体垂直居中微调 */
  line-height: 1.4;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== 按钮中文文本对齐 ===== */

.el-button {
  /* 确保中文文本在按钮中垂直居中 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding-top: 1px; /* 微调中文字体位置 */
}

.el-button span {
  line-height: 1.2;
  vertical-align: middle;
}

/* 大尺寸按钮中文优化 */
.el-button--large {
  padding-top: 2px;
  line-height: 1;
}

.el-button--large span {
  line-height: 1.3;
}

/* 小尺寸按钮中文优化 */
.el-button--small {
  padding-top: 0px;
  line-height: 1;
}

.el-button--small span {
  line-height: 1.1;
}

/* ===== 标题中文文本对齐 ===== */

h1, h2, h3, h4, h5, h6 {
  text-align: center;
  line-height: 1.3;
  margin: 0;
  padding: 0;
  /* 中文标题垂直居中优化 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
  line-height: 1.2;
  text-align: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  text-align: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  text-align: center;
  margin-bottom: 12px;
}

/* ===== 菜单项中文对齐 ===== */

.el-menu-item {
  display: flex;
  align-items: center;
  line-height: 1.2;
  padding-top: 1px; /* 微调中文字体位置 */
}

.el-menu-item span {
  line-height: 1.2;
  vertical-align: middle;
}

/* ===== 表格中文内容对齐 ===== */

.el-table .el-table__cell {
  text-align: center;
  vertical-align: middle;
  line-height: 1.4;
}

.el-table .el-table__cell .cell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  line-height: 1.3;
  padding: 8px;
}

/* 表头中文对齐 */
.el-table th .cell {
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

/* ===== 面试系统专用中文对齐 ===== */

/* 候选人信息中文对齐 */
.candidate-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  text-align: center;
  margin-bottom: 4px;
}

.candidate-position {
  font-size: 14px;
  color: #666;
  line-height: 1.3;
  text-align: center;
  margin-bottom: 8px;
}

/* 技能标签中文对齐 */
.skill-tag {
  font-size: 12px;
  line-height: 1.2;
  padding: 4px 8px;
  text-align: center;
  white-space: nowrap;
}

/* 状态文本中文对齐 */
.status-text {
  font-size: 14px;
  line-height: 1.2;
  color: #333;
  text-align: center;
}

/* 元数据标签中文对齐 */
.meta-label {
  font-size: 12px;
  line-height: 1.2;
  color: #666;
  text-align: left;
}

.meta-value {
  font-size: 14px;
  line-height: 1.2;
  color: #333;
  font-weight: 500;
  text-align: left;
}

/* ===== 响应式中文优化 ===== */

/* 平板端中文优化 */
@media (max-width: 768px) {
  .page-title {
    font-size: 24px;
    line-height: 1.2;
  }
  
  .section-title {
    font-size: 20px;
    line-height: 1.3;
  }
  
  .card-title {
    font-size: 16px;
    line-height: 1.3;
  }
  
  .el-button {
    padding-top: 0px;
  }
  
  .el-button--large {
    padding-top: 1px;
  }
}

/* 手机端中文优化 */
@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
    line-height: 1.2;
  }
  
  .section-title {
    font-size: 18px;
    line-height: 1.3;
  }
  
  .card-title {
    font-size: 14px;
    line-height: 1.3;
  }
  
  .el-button {
    padding-top: 0px;
    font-size: 12px;
  }
  
  .el-button span {
    line-height: 1.1;
  }
  
  .candidate-name {
    font-size: 16px;
  }
  
  .candidate-position {
    font-size: 12px;
  }
  
  .skill-tag {
    font-size: 11px;
    padding: 3px 6px;
  }
}

/* ===== 特殊场景中文对齐 ===== */

/* 加载状态文本对齐 */
.el-loading-text {
  font-size: 14px;
  line-height: 1.2;
  color: #666;
  text-align: center;
  margin-top: 8px;
}

/* 空状态文本对齐 */
.el-empty__description {
  font-size: 14px;
  line-height: 1.3;
  color: #999;
  text-align: center;
}

/* 提示文本对齐 */
.el-tooltip__popper {
  font-size: 12px;
  line-height: 1.3;
  text-align: center;
}

/* 消息通知文本对齐 */
.el-message {
  font-size: 14px;
  line-height: 1.3;
  text-align: left;
}

.el-notification__title {
  font-size: 16px;
  line-height: 1.2;
  font-weight: 600;
}

.el-notification__content {
  font-size: 14px;
  line-height: 1.4;
}
