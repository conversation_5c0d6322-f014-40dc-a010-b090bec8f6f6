<template>
  <div class="test-evaluation-page">
    <h1>✅ 测试页面 - AI智能评估</h1>
    <p>如果您看到这个页面，说明路由配置正确！</p>
    
    <el-card>
      <template #header>
        <h3>路由测试成功</h3>
      </template>
      
      <div>
        <p><strong>当前路径:</strong> /evaluation</p>
        <p><strong>组件名称:</strong> TestEvaluationPage.vue</p>
        <p><strong>测试时间:</strong> {{ new Date().toLocaleString() }}</p>
      </div>
      
      <div style="margin-top: 20px;">
        <el-button type="primary" @click="goBack">返回演示页面</el-button>
        <el-button type="success" @click="showSuccess">测试成功</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

const goBack = () => {
  router.push('/demo')
}

const showSuccess = () => {
  ElMessage.success('路由测试成功！404问题已解决！')
}
</script>

<style scoped>
.test-evaluation-page {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

h1 {
  color: #67c23a;
  margin-bottom: 20px;
}
</style>
