(function(e,r){typeof exports==="object"&&typeof module!=="undefined"?r(exports):typeof define==="function"&&define.amd?define(["exports"],r):(e=e||self,r(e.stylis={}))})(this,(function(e){"use strict";var r="-ms-";var a="-moz-";var c="-webkit-";var t="comm";var n="rule";var s="decl";var i="@page";var u="@media";var o="@import";var l="@charset";var f="@viewport";var p="@supports";var h="@document";var v="@namespace";var d="@keyframes";var b="@font-face";var w="@counter-style";var m="@font-feature-values";var g="@layer";var k="@scope";var $=Math.abs;var x=String.fromCharCode;var E=Object.assign;function y(e,r){return M(e,0)^45?(((r<<2^M(e,0))<<2^M(e,1))<<2^M(e,2))<<2^M(e,3):0}function O(e){return e.trim()}function T(e,r){return(e=r.exec(e))?e[0]:e}function A(e,r,a){return e.replace(r,a)}function C(e,r,a){return e.indexOf(r,a)}function M(e,r){return e.charCodeAt(r)|0}function S(e,r,a){return e.slice(r,a)}function R(e){return e.length}function P(e){return e.length}function z(e,r){return r.push(e),e}function N(e,r){return e.map(r).join("")}function j(e,r){return e.filter((function(e){return!T(e,r)}))}e.line=1;e.column=1;e.length=0;e.position=0;e.character=0;e.characters="";function U(r,a,c,t,n,s,i,u){return{value:r,root:a,parent:c,type:t,props:n,children:s,line:e.line,column:e.column,length:i,return:"",siblings:u}}function _(e,r){return E(U("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},r)}function F(e){while(e.root)e=_(e.root,{children:[e]});z(e,e.siblings)}function I(){return e.character}function L(){e.character=e.position>0?M(e.characters,--e.position):0;if(e.column--,e.character===10)e.column=1,e.line--;return e.character}function D(){e.character=e.position<e.length?M(e.characters,e.position++):0;if(e.column++,e.character===10)e.column=1,e.line++;return e.character}function Y(){return M(e.characters,e.position)}function K(){return e.position}function V(r,a){return S(e.characters,r,a)}function W(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function B(r){return e.line=e.column=1,e.length=R(e.characters=r),e.position=0,[]}function G(r){return e.characters="",r}function H(r){return O(V(e.position-1,X(r===91?r+2:r===40?r+1:r)))}function Z(e){return G(J(B(e)))}function q(r){while(e.character=Y())if(e.character<33)D();else break;return W(r)>2||W(e.character)>3?"":" "}function J(r){while(D())switch(W(e.character)){case 0:z(re(e.position-1),r);break;case 2:z(H(e.character),r);break;default:z(x(e.character),r)}return r}function Q(r,a){while(--a&&D())if(e.character<48||e.character>102||e.character>57&&e.character<65||e.character>70&&e.character<97)break;return V(r,K()+(a<6&&Y()==32&&D()==32))}function X(r){while(D())switch(e.character){case r:return e.position;case 34:case 39:if(r!==34&&r!==39)X(e.character);break;case 40:if(r===41)X(r);break;case 92:D();break}return e.position}function ee(r,a){while(D())if(r+e.character===47+10)break;else if(r+e.character===42+42&&Y()===47)break;return"/*"+V(a,e.position-1)+"*"+x(r===47?r:D())}function re(r){while(!W(Y()))D();return V(r,e.position)}function ae(e){return G(ce("",null,null,null,[""],e=B(e),0,[0],e))}function ce(e,r,a,c,t,n,s,i,u){var o=0;var l=0;var f=s;var p=0;var h=0;var v=0;var d=1;var b=1;var w=1;var m=0;var g="";var k=t;var E=n;var y=c;var O=g;while(b)switch(v=m,m=D()){case 40:if(v!=108&&M(O,f-1)==58){if(C(O+=A(H(m),"&","&\f"),"&\f",$(o?i[o-1]:0))!=-1)w=-1;break}case 34:case 39:case 91:O+=H(m);break;case 9:case 10:case 13:case 32:O+=q(v);break;case 92:O+=Q(K()-1,7);continue;case 47:switch(Y()){case 42:case 47:z(ne(ee(D(),K()),r,a,u),u);if((W(v||1)==5||W(Y()||1)==5)&&R(O)&&S(O,-1,void 0)!==" ")O+=" ";break;default:O+="/"}break;case 123*d:i[o++]=R(O)*w;case 125*d:case 59:case 0:switch(m){case 0:case 125:b=0;case 59+l:if(w==-1)O=A(O,/\f/g,"");if(h>0&&(R(O)-f||d===0&&v===47))z(h>32?se(O+";",c,a,f-1,u):se(A(O," ","")+";",c,a,f-2,u),u);break;case 59:O+=";";default:z(y=te(O,r,a,o,l,t,i,g,k=[],E=[],f,n),n);if(m===123)if(l===0)ce(O,r,y,y,k,n,f,i,E);else{switch(p){case 99:if(M(O,3)===110)break;case 108:if(M(O,2)===97)break;default:l=0;case 100:case 109:case 115:}if(l)ce(e,y,y,c&&z(te(e,y,y,0,0,t,i,g,t,k=[],f,E),E),t,E,f,i,c?k:E);else ce(O,y,y,y,[""],E,0,i,E)}}o=l=h=0,d=w=1,g=O="",f=s;break;case 58:f=1+R(O),h=v;default:if(d<1)if(m==123)--d;else if(m==125&&d++==0&&L()==125)continue;switch(O+=x(m),m*d){case 38:w=l>0?1:(O+="\f",-1);break;case 44:i[o++]=(R(O)-1)*w,w=1;break;case 64:if(Y()===45)O+=H(D());p=Y(),l=f=R(g=O+=re(K())),m++;break;case 45:if(v===45&&R(O)==2)d=0}}return n}function te(e,r,a,c,t,s,i,u,o,l,f,p){var h=t-1;var v=t===0?s:[""];var d=P(v);for(var b=0,w=0,m=0;b<c;++b)for(var g=0,k=S(e,h+1,h=$(w=i[b])),x=e;g<d;++g)if(x=O(w>0?v[g]+" "+k:A(k,/&\f/g,v[g])))o[m++]=x;return U(e,r,a,t===0?n:u,o,l,f,p)}function ne(e,r,a,c){return U(e,r,a,t,x(I()),S(e,2,-2),0,c)}function se(e,r,a,c,t){return U(e,r,a,s,S(e,0,c),S(e,c+1,-1),c,t)}function ie(e,t,n){switch(y(e,t)){case 5103:return c+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:case 6391:case 5879:case 5623:case 6135:case 4599:return c+e+e;case 4855:return c+e.replace("add","source-over").replace("substract","source-out").replace("intersect","source-in").replace("exclude","xor")+e;case 4789:return a+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return c+e+a+e+r+e+e;case 5936:switch(M(e,t+11)){case 114:return c+e+r+A(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return c+e+r+A(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return c+e+r+A(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return c+e+r+e+e;case 6165:return c+e+r+"flex-"+e+e;case 5187:return c+e+A(e,/(\w+).+(:[^]+)/,c+"box-$1$2"+r+"flex-$1$2")+e;case 5443:return c+e+r+"flex-item-"+A(e,/flex-|-self/g,"")+(!T(e,/flex-|baseline/)?r+"grid-row-"+A(e,/flex-|-self/g,""):"")+e;case 4675:return c+e+r+"flex-line-pack"+A(e,/align-content|flex-|-self/g,"")+e;case 5548:return c+e+r+A(e,"shrink","negative")+e;case 5292:return c+e+r+A(e,"basis","preferred-size")+e;case 6060:return c+"box-"+A(e,"-grow","")+c+e+r+A(e,"grow","positive")+e;case 4554:return c+A(e,/([^-])(transform)/g,"$1"+c+"$2")+e;case 6187:return A(A(A(e,/(zoom-|grab)/,c+"$1"),/(image-set)/,c+"$1"),e,"")+e;case 5495:case 3959:return A(e,/(image-set\([^]*)/,c+"$1"+"$`$1");case 4968:return A(A(e,/(.+:)(flex-)?(.*)/,c+"box-pack:$3"+r+"flex-pack:$3"),/space-between/,"justify")+c+e+e;case 4200:if(!T(e,/flex-|baseline/))return r+"grid-column-align"+S(e,t)+e;break;case 2592:case 3360:return r+A(e,"template-","")+e;case 4384:case 3616:if(n&&n.some((function(e,r){return t=r,T(e.props,/grid-\w+-end/)}))){return~C(e+(n=n[t].value),"span",0)?e:r+A(e,"-start","")+e+r+"grid-row-span:"+(~C(n,"span",0)?T(n,/\d+/):+T(n,/\d+/)-+T(e,/\d+/))+";"}return r+A(e,"-start","")+e;case 4896:case 4128:return n&&n.some((function(e){return T(e.props,/grid-\w+-start/)}))?e:r+A(A(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return A(e,/(.+)-inline(.+)/,c+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(R(e)-1-t>6)switch(M(e,t+1)){case 109:if(M(e,t+4)!==45)break;case 102:return A(e,/(.+:)(.+)-([^]+)/,"$1"+c+"$2-$3"+"$1"+a+(M(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~C(e,"stretch",0)?ie(A(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return A(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(a,c,t,n,s,i,u){return r+c+":"+t+u+(n?r+c+"-span:"+(s?i:+i-+t)+u:"")+e}));case 4949:if(M(e,t+6)===121)return A(e,":",":"+c)+e;break;case 6444:switch(M(e,M(e,14)===45?18:11)){case 120:return A(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+c+(M(e,14)===45?"inline-":"")+"box$3"+"$1"+c+"$2$3"+"$1"+r+"$2box$3")+e;case 100:return A(e,":",":"+r)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return A(e,"scroll-","scroll-snap-")+e}return e}function ue(e,r){var a="";for(var c=0;c<e.length;c++)a+=r(e[c],c,e,r)||"";return a}function oe(e,r,a,c){switch(e.type){case g:if(e.children.length)break;case o:case v:case s:return e.return=e.return||e.value;case t:return"";case d:return e.return=e.value+"{"+ue(e.children,c)+"}";case n:if(!R(e.value=e.props.join(",")))return""}return R(a=ue(e.children,c))?e.return=e.value+"{"+a+"}":""}function le(e){var r=P(e);return function(a,c,t,n){var s="";for(var i=0;i<r;i++)s+=e[i](a,c,t,n)||"";return s}}function fe(e){return function(r){if(!r.root)if(r=r.return)e(r)}}function pe(e,t,i,u){if(e.length>-1)if(!e.return)switch(e.type){case s:e.return=ie(e.value,e.length,i);return;case d:return ue([_(e,{value:A(e.value,"@","@"+c)})],u);case n:if(e.length)return N(i=e.props,(function(t){switch(T(t,u=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":F(_(e,{props:[A(t,/:(read-\w+)/,":"+a+"$1")]}));F(_(e,{props:[t]}));E(e,{props:j(i,u)});break;case"::placeholder":F(_(e,{props:[A(t,/:(plac\w+)/,":"+c+"input-$1")]}));F(_(e,{props:[A(t,/:(plac\w+)/,":"+a+"$1")]}));F(_(e,{props:[A(t,/:(plac\w+)/,r+"input-$1")]}));F(_(e,{props:[t]}));E(e,{props:j(i,u)});break}return""}))}}function he(e){switch(e.type){case n:e.props=e.props.map((function(r){return N(Z(r),(function(r,a,c){switch(M(r,0)){case 12:return S(r,1,R(r));case 0:case 40:case 43:case 62:case 126:return r;case 58:if(c[++a]==="global")c[a]="",c[++a]="\f"+S(c[a],a=1,-1);case 32:return a===1?"":r;default:switch(a){case 0:e=r;return P(c)>1?"":r;case a=P(c)-1:case 2:return a===2?r+e+e:r+e;default:return r}}}))}))}}e.CHARSET=l;e.COMMENT=t;e.COUNTER_STYLE=w;e.DECLARATION=s;e.DOCUMENT=h;e.FONT_FACE=b;e.FONT_FEATURE_VALUES=m;e.IMPORT=o;e.KEYFRAMES=d;e.LAYER=g;e.MEDIA=u;e.MOZ=a;e.MS=r;e.NAMESPACE=v;e.PAGE=i;e.RULESET=n;e.SCOPE=k;e.SUPPORTS=p;e.VIEWPORT=f;e.WEBKIT=c;e.abs=$;e.alloc=B;e.append=z;e.assign=E;e.caret=K;e.char=I;e.charat=M;e.combine=N;e.comment=ne;e.commenter=ee;e.compile=ae;e.copy=_;e.dealloc=G;e.declaration=se;e.delimit=H;e.delimiter=X;e.escaping=Q;e.filter=j;e.from=x;e.hash=y;e.identifier=re;e.indexof=C;e.lift=F;e.match=T;e.middleware=le;e.namespace=he;e.next=D;e.node=U;e.parse=ce;e.peek=Y;e.prefix=ie;e.prefixer=pe;e.prev=L;e.replace=A;e.ruleset=te;e.rulesheet=fe;e.serialize=ue;e.sizeof=P;e.slice=V;e.stringify=oe;e.strlen=R;e.substr=S;e.token=W;e.tokenize=Z;e.tokenizer=J;e.trim=O;e.whitespace=q;Object.defineProperty(e,"__esModule",{value:true})}));
//# sourceMappingURL=stylis.js.map
