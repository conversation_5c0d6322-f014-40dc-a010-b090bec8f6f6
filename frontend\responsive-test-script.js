/**
 * iFlytek 多模态面试系统 - 响应式设计测试脚本
 * 自动化测试各种设备尺寸下的响应式效果
 */

class ResponsiveTestSuite {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            details: []
        };

        // 测试设备尺寸
        this.deviceSizes = [
            { name: 'iPhone SE', width: 375, height: 667 },
            { name: 'iPhone 12', width: 390, height: 844 },
            { name: 'iPhone 12 Pro Max', width: 428, height: 926 },
            { name: 'iPad', width: 768, height: 1024 },
            { name: 'iPad Pro', width: 1024, height: 1366 },
            { name: 'Desktop Small', width: 1200, height: 800 },
            { name: 'Desktop Large', width: 1920, height: 1080 }
        ];

        // 测试页面路径
        this.testPages = [
            { name: '主页', path: '/' },
            { name: '企业仪表板', path: '/enterprise' },
            { name: '职位管理', path: '/position-management' },
            { name: '候选人管理', path: '/candidate-management' },
            { name: '面试页面', path: '/interviewing' }
        ];

        // 响应式断点
        this.breakpoints = {
            xs: 320,
            sm: 576,
            md: 768,
            lg: 992,
            xl: 1200,
            xxl: 1600
        };
    }

    /**
     * 运行完整的响应式测试套件
     */
    async runFullTestSuite() {
        console.log('🚀 开始iFlytek响应式设计测试...');
        
        // 测试CSS变量系统
        this.testCSSVariables();
        
        // 测试断点系统
        this.testBreakpoints();
        
        // 测试各设备尺寸
        for (const device of this.deviceSizes) {
            await this.testDeviceSize(device);
        }
        
        // 测试组件响应式
        this.testComponentResponsiveness();
        
        // 测试可访问性
        this.testAccessibility();
        
        // 输出测试结果
        this.outputResults();
    }

    /**
     * 测试CSS变量系统
     */
    testCSSVariables() {
        console.log('📝 测试CSS变量系统...');
        
        const root = document.documentElement;
        const computedStyle = getComputedStyle(root);
        
        const requiredVariables = [
            '--font-xs', '--font-sm', '--font-base', '--font-lg',
            '--icon-xs', '--icon-sm', '--icon-base', '--icon-lg',
            '--space-responsive-xs', '--space-responsive-sm',
            '--space-responsive-md', '--space-responsive-lg',
            '--btn-height-sm', '--btn-height-md', '--btn-height-lg'
        ];

        let passed = 0;
        requiredVariables.forEach(variable => {
            const value = computedStyle.getPropertyValue(variable);
            if (value && value.trim() !== '') {
                passed++;
                this.addResult('pass', `CSS变量 ${variable} 已定义: ${value.trim()}`);
            } else {
                this.addResult('fail', `CSS变量 ${variable} 未定义或为空`);
            }
        });

        console.log(`✅ CSS变量测试完成: ${passed}/${requiredVariables.length} 通过`);
    }

    /**
     * 测试断点系统
     */
    testBreakpoints() {
        console.log('📱 测试断点系统...');
        
        Object.entries(this.breakpoints).forEach(([name, width]) => {
            // 模拟窗口宽度
            const mediaQuery = `(max-width: ${width}px)`;
            const matches = window.matchMedia(mediaQuery).matches;
            
            this.addResult('info', `断点 ${name} (${width}px): ${matches ? '激活' : '未激活'}`);
        });
    }

    /**
     * 测试特定设备尺寸
     */
    async testDeviceSize(device) {
        console.log(`📱 测试设备: ${device.name} (${device.width}x${device.height})`);
        
        // 模拟设备尺寸
        if (window.chrome && window.chrome.runtime) {
            // 在Chrome DevTools中设置设备尺寸
            try {
                await this.setViewportSize(device.width, device.height);
                
                // 测试布局适配
                this.testLayoutAdaptation(device);
                
                // 测试字体大小
                this.testFontSizes(device);
                
                // 测试按钮尺寸
                this.testButtonSizes(device);
                
                // 测试导航适配
                this.testNavigationAdaptation(device);
                
            } catch (error) {
                this.addResult('warning', `设备 ${device.name} 测试失败: ${error.message}`);
            }
        } else {
            this.addResult('warning', `无法在当前环境中模拟设备 ${device.name}`);
        }
    }

    /**
     * 设置视口尺寸
     */
    async setViewportSize(width, height) {
        if (window.visualViewport) {
            // 使用Visual Viewport API
            document.documentElement.style.width = `${width}px`;
            document.documentElement.style.height = `${height}px`;
        }
        
        // 触发resize事件
        window.dispatchEvent(new Event('resize'));
        
        // 等待布局更新
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    /**
     * 测试布局适配
     */
    testLayoutAdaptation(device) {
        const containers = document.querySelectorAll('.responsive-container, .container, .ai-container');
        
        containers.forEach(container => {
            const rect = container.getBoundingClientRect();
            const isOverflowing = rect.width > device.width;
            
            if (isOverflowing) {
                this.addResult('fail', `容器在 ${device.name} 上溢出: ${rect.width}px > ${device.width}px`);
            } else {
                this.addResult('pass', `容器在 ${device.name} 上适配正常`);
            }
        });
    }

    /**
     * 测试字体大小
     */
    testFontSizes(device) {
        const textElements = document.querySelectorAll('h1, h2, h3, p, span, .text-responsive');
        
        textElements.forEach(element => {
            const fontSize = parseFloat(getComputedStyle(element).fontSize);
            
            // 检查字体大小是否在合理范围内
            if (device.width <= 480 && fontSize < 12) {
                this.addResult('warning', `字体过小在 ${device.name}: ${fontSize}px`);
            } else if (device.width <= 480 && fontSize > 24) {
                this.addResult('warning', `字体过大在 ${device.name}: ${fontSize}px`);
            } else {
                this.addResult('pass', `字体大小在 ${device.name} 上合适: ${fontSize}px`);
            }
        });
    }

    /**
     * 测试按钮尺寸
     */
    testButtonSizes(device) {
        const buttons = document.querySelectorAll('button, .btn, .el-button');
        
        buttons.forEach(button => {
            const rect = button.getBoundingClientRect();
            
            // 检查触摸目标大小（最小44px）
            if (device.width <= 768 && (rect.height < 44 || rect.width < 44)) {
                this.addResult('warning', `按钮触摸区域过小在 ${device.name}: ${rect.width}x${rect.height}`);
            } else {
                this.addResult('pass', `按钮尺寸在 ${device.name} 上合适`);
            }
        });
    }

    /**
     * 测试导航适配
     */
    testNavigationAdaptation(device) {
        const nav = document.querySelector('.ai-nav, .nav, .navigation');
        
        if (nav) {
            const isHidden = getComputedStyle(nav).display === 'none';
            const isMobile = device.width <= 768;
            
            if (isMobile && !isHidden) {
                this.addResult('warning', `导航在 ${device.name} 上应该隐藏或折叠`);
            } else {
                this.addResult('pass', `导航在 ${device.name} 上适配正常`);
            }
        }
    }

    /**
     * 测试组件响应式
     */
    testComponentResponsiveness() {
        console.log('🧩 测试组件响应式...');
        
        // 测试表格响应式
        const tables = document.querySelectorAll('.el-table, table');
        tables.forEach(table => {
            const wrapper = table.closest('.table-responsive-wrapper');
            if (wrapper) {
                this.addResult('pass', '表格有响应式包装器');
            } else {
                this.addResult('warning', '表格缺少响应式包装器');
            }
        });

        // 测试卡片响应式
        const cards = document.querySelectorAll('.card, .el-card, .stat-card');
        cards.forEach(card => {
            const hasResponsiveClass = card.classList.contains('card-responsive') || 
                                     card.classList.contains('responsive');
            if (hasResponsiveClass) {
                this.addResult('pass', '卡片有响应式类');
            } else {
                this.addResult('info', '卡片可能需要响应式优化');
            }
        });
    }

    /**
     * 测试可访问性
     */
    testAccessibility() {
        console.log('♿ 测试可访问性...');
        
        // 测试颜色对比度
        this.testColorContrast();
        
        // 测试键盘导航
        this.testKeyboardNavigation();
        
        // 测试语义化标签
        this.testSemanticHTML();
    }

    /**
     * 测试颜色对比度
     */
    testColorContrast() {
        const textElements = document.querySelectorAll('p, span, h1, h2, h3, h4, h5, h6');
        
        textElements.forEach(element => {
            const style = getComputedStyle(element);
            const color = style.color;
            const backgroundColor = style.backgroundColor;
            
            // 简单的对比度检查（实际应用中需要更复杂的算法）
            if (color && backgroundColor && color !== backgroundColor) {
                this.addResult('pass', '文本颜色对比度检查通过');
            }
        });
    }

    /**
     * 测试键盘导航
     */
    testKeyboardNavigation() {
        const focusableElements = document.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length > 0) {
            this.addResult('pass', `发现 ${focusableElements.length} 个可聚焦元素`);
        } else {
            this.addResult('warning', '未发现可聚焦元素');
        }
    }

    /**
     * 测试语义化HTML
     */
    testSemanticHTML() {
        const semanticElements = document.querySelectorAll(
            'header, nav, main, section, article, aside, footer'
        );
        
        if (semanticElements.length > 0) {
            this.addResult('pass', `使用了 ${semanticElements.length} 个语义化元素`);
        } else {
            this.addResult('warning', '建议使用更多语义化HTML元素');
        }
    }

    /**
     * 添加测试结果
     */
    addResult(type, message) {
        this.testResults.details.push({ type, message });
        
        switch (type) {
            case 'pass':
                this.testResults.passed++;
                break;
            case 'fail':
                this.testResults.failed++;
                break;
            case 'warning':
                this.testResults.warnings++;
                break;
        }
    }

    /**
     * 输出测试结果
     */
    outputResults() {
        console.log('\n📊 iFlytek响应式设计测试结果:');
        console.log(`✅ 通过: ${this.testResults.passed}`);
        console.log(`❌ 失败: ${this.testResults.failed}`);
        console.log(`⚠️  警告: ${this.testResults.warnings}`);
        
        console.log('\n📋 详细结果:');
        this.testResults.details.forEach(result => {
            const icon = result.type === 'pass' ? '✅' : 
                        result.type === 'fail' ? '❌' : '⚠️';
            console.log(`${icon} ${result.message}`);
        });

        // 计算总体评分
        const total = this.testResults.passed + this.testResults.failed + this.testResults.warnings;
        const score = total > 0 ? (this.testResults.passed / total * 100).toFixed(1) : 0;
        
        console.log(`\n🎯 总体评分: ${score}%`);
        
        if (score >= 90) {
            console.log('🎉 优秀！响应式设计质量很高');
        } else if (score >= 75) {
            console.log('👍 良好！响应式设计基本达标');
        } else {
            console.log('⚠️  需要改进响应式设计');
        }
    }
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            const testSuite = new ResponsiveTestSuite();
            testSuite.runFullTestSuite();
        });
    } else {
        const testSuite = new ResponsiveTestSuite();
        testSuite.runFullTestSuite();
    }
}

// 导出测试类（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResponsiveTestSuite;
}
