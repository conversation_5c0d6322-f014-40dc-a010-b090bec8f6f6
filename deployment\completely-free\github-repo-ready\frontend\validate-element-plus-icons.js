// Element Plus 图标验证脚本
console.log('🔍 验证 Element Plus 图标导入...');

// Element Plus 中实际可用的图标列表（常用图标）
const availableIcons = [
  // 基础图标
  'Plus', 'Minus', 'Close', 'Check', 'Delete', 'Edit', 'Search', 'Refresh',
  'Upload', 'Download', 'Setting', 'Star', 'Warning', 'InfoFilled',
  
  // 媒体图标
  'VideoPlay', 'VideoPause', 'VideoCamera', 'Microphone', 'Document',
  
  // 数据图标
  'DataBoard', 'TrendCharts', 'Monitor', 'DataAnalysis',
  
  // 技术图标
  'Cpu', 'Connection',
  
  // 其他常用图标
  'User', 'House', 'Timer', 'Loading', 'ArrowRight', 'View'
];

// 检查 IntelligentIcons.vue 中使用的图标
const usedIcons = [
  'Cpu', 'DataBoard', 'Connection', 'Microphone', 'VideoCamera', 'Document',
  'Setting', 'VideoPause', 'VideoPlay', 'Star', 'TrendCharts', 'Monitor',
  'DataAnalysis', 'Upload', 'Download', 'Search', 'Edit', 'Delete',
  'Plus', 'Minus', 'Refresh', 'Check', 'Close', 'Warning', 'InfoFilled'
];

console.log('📋 检查图标可用性:');
console.log('='.repeat(50));

let validCount = 0;
let invalidCount = 0;
const invalidIcons = [];

usedIcons.forEach(icon => {
  const isValid = availableIcons.includes(icon);
  const status = isValid ? '✅' : '❌';
  console.log(`${status} ${icon}`);
  
  if (isValid) {
    validCount++;
  } else {
    invalidCount++;
    invalidIcons.push(icon);
  }
});

console.log('\n📊 统计结果:');
console.log(`✅ 有效图标: ${validCount}`);
console.log(`❌ 无效图标: ${invalidCount}`);

if (invalidIcons.length > 0) {
  console.log('\n❌ 需要替换的图标:');
  
  // 图标替换建议
  const replacements = {
    'DataAnalysis': 'TrendCharts',
    'Monitor': 'DataBoard',
    'CloudUpload': 'Upload'
  };
  
  invalidIcons.forEach(icon => {
    if (replacements[icon]) {
      console.log(`   ${icon} → ${replacements[icon]}`);
    } else {
      console.log(`   ${icon} → 需要手动选择替换`);
    }
  });
  
  console.log('\n🔧 修复建议:');
  console.log('1. 更新 IntelligentIcons.vue 中的导入语句');
  console.log('2. 更新图标映射对象中的引用');
  console.log('3. 测试所有图标类型的显示效果');
} else {
  console.log('\n🎉 所有图标都是有效的！');
}

// 生成修复后的导入语句
console.log('\n📝 建议的导入语句:');
console.log('import {');
const validUsedIcons = usedIcons.filter(icon => availableIcons.includes(icon));
validUsedIcons.forEach((icon, index) => {
  const comma = index < validUsedIcons.length - 1 ? ',' : '';
  console.log(`  ${icon}${comma}`);
});
console.log("} from '@element-plus/icons-vue'");

// 检查浏览器中的实际可用图标
if (typeof window !== 'undefined') {
  console.log('\n🌐 检查浏览器中的 Element Plus 图标:');
  
  // 尝试动态导入 Element Plus 图标
  import('@element-plus/icons-vue').then(icons => {
    console.log('📦 Element Plus 图标模块加载成功');
    console.log(`📊 可用图标数量: ${Object.keys(icons).length}`);
    
    // 检查我们使用的图标是否真的存在
    const missingIcons = [];
    usedIcons.forEach(icon => {
      if (!icons[icon]) {
        missingIcons.push(icon);
      }
    });
    
    if (missingIcons.length > 0) {
      console.log('❌ 浏览器中缺失的图标:', missingIcons.join(', '));
    } else {
      console.log('✅ 所有图标在浏览器中都可用');
    }
    
    // 列出一些可用的替代图标
    console.log('\n💡 可用的替代图标:');
    const alternativeIcons = [
      'Upload', 'Download', 'CloudUpload', 'FolderOpened', 'Files',
      'Picture', 'VideoPlay', 'Headset', 'Microphone', 'Monitor',
      'Cpu', 'HardDrive', 'Wifi', 'Connection', 'DataBoard',
      'TrendCharts', 'PieChart', 'Histogram', 'ScaleToOriginal'
    ];
    
    alternativeIcons.forEach(icon => {
      if (icons[icon]) {
        console.log(`✅ ${icon} - 可用`);
      } else {
        console.log(`❌ ${icon} - 不可用`);
      }
    });
    
  }).catch(error => {
    console.log('❌ 无法加载 Element Plus 图标模块:', error.message);
  });
}

// 导出验证函数
if (typeof window !== 'undefined') {
  window.validateElementPlusIcons = () => {
    console.log('🔄 重新验证 Element Plus 图标...');
    location.reload();
  };
  
  console.log('\n💡 提示: 运行 validateElementPlusIcons() 重新验证');
}
