import { CustomIconLoader, InlineCollection, IconifyLoaderOptions } from './types.js';
import '@antfu/utils';
import '../customisations/defaults.js';
import '@iconify/types';

/**
 * Get custom icon from inline collection or using loader
 */
declare function getCustomIcon(custom: CustomIconLoader | InlineCollection, collection: string, icon: string, options?: IconifyLoaderOptions): Promise<string | undefined>;

export { getCustomIcon };
