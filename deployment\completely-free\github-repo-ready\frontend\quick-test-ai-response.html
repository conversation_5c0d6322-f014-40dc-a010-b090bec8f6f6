<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI响应快速测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-input {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .test-button.warning {
            background: #faad14;
        }
        .test-button.warning:hover {
            background: #ffc53d;
        }
        .result-container {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            border-left: 4px solid #1890ff;
        }
        .result-title {
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 10px;
        }
        .result-content {
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .log-container {
            background: #001529;
            color: #fff;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        .status.processing {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-header">🧪 AI响应功能快速测试</h1>
        
        <div>
            <label for="testInput">测试输入：</label>
            <textarea id="testInput" class="test-input" placeholder="输入测试文本，例如：我不知道">我不知道</textarea>
        </div>
        
        <div>
            <button class="test-button" onclick="testAIResponse()">🚀 测试AI回复</button>
            <button class="test-button warning" onclick="testAnalysis()">📊 测试分析功能</button>
            <button class="test-button" onclick="testMultipleScenarios()">🎯 测试多种场景</button>
            <button class="test-button" onclick="clearResults()">🗑️ 清空结果</button>
            <span id="status" class="status"></span>
        </div>
        
        <div id="results"></div>
        <div id="logs" class="log-container" style="display: none;"></div>
        <button class="test-button" onclick="toggleLogs()">📋 显示/隐藏日志</button>
    </div>

    <script>
        let testResults = [];
        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logContainer = document.getElementById('logs');
            logContainer.innerHTML = logs.join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(message);
        }

        function setStatus(message, type = 'processing') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function addResult(title, content, type = 'info') {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result-container';
            resultDiv.innerHTML = `
                <div class="result-title">${title}</div>
                <div class="result-content">${content}</div>
            `;
            document.getElementById('results').appendChild(resultDiv);
        }

        async function testAIResponse() {
            const input = document.getElementById('testInput').value.trim();
            if (!input) {
                alert('请输入测试文本');
                return;
            }

            setStatus('正在测试AI回复...', 'processing');
            log('🚀 开始测试AI回复功能');
            log(`📝 测试输入: ${input}`);

            try {
                // 模拟分析结果
                const mockAnalysis = await simulateAnalysis(input);
                log(`📊 模拟分析完成: 评分 ${mockAnalysis.overallScore}`);

                // 生成AI回复
                const aiResponse = generateAIResponse(mockAnalysis, input);
                log(`🤖 AI回复生成完成`);

                addResult('🤖 AI面试官回复', aiResponse);
                addResult('📊 分析结果', JSON.stringify(mockAnalysis, null, 2));

                setStatus('测试完成', 'success');
                log('✅ AI回复测试完成');

            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                setStatus('测试失败', 'error');
                addResult('❌ 错误信息', error.message);
            }
        }

        async function testAnalysis() {
            const input = document.getElementById('testInput').value.trim();
            if (!input) {
                alert('请输入测试文本');
                return;
            }

            setStatus('正在测试分析功能...', 'processing');
            log('🔍 开始测试分析功能');

            try {
                const analysis = await simulateAnalysis(input);
                log(`📊 分析完成: 评分 ${analysis.overallScore}`);

                addResult('📊 详细分析结果', `
评分: ${analysis.overallScore}
关键词: ${analysis.textAnalysis.keywords.join(', ')}
建议: ${analysis.recommendations.join('; ')}
优势: ${analysis.strengthAreas.join(', ')}
改进: ${analysis.improvementAreas.join(', ')}
                `);

                setStatus('分析完成', 'success');
                log('✅ 分析测试完成');

            } catch (error) {
                log(`❌ 分析失败: ${error.message}`);
                setStatus('分析失败', 'error');
            }
        }

        async function testMultipleScenarios() {
            setStatus('正在测试多种场景...', 'processing');
            log('🎯 开始多场景测试');

            const scenarios = [
                '我不知道',
                '我在机器学习项目中使用了TensorFlow框架，实现了一个深度神经网络模型。',
                '不清楚这个问题',
                '我有3年的Python开发经验，熟悉Django和Flask框架。',
                '没有相关经验'
            ];

            for (let i = 0; i < scenarios.length; i++) {
                const scenario = scenarios[i];
                log(`🧪 测试场景 ${i + 1}: ${scenario}`);

                try {
                    const analysis = await simulateAnalysis(scenario);
                    const aiResponse = generateAIResponse(analysis, scenario);

                    addResult(`场景 ${i + 1}: "${scenario}"`, `
评分: ${analysis.overallScore}
AI回复: ${aiResponse}
                    `);

                    log(`✅ 场景 ${i + 1} 测试完成`);
                } catch (error) {
                    log(`❌ 场景 ${i + 1} 测试失败: ${error.message}`);
                }

                // 添加延迟避免过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            setStatus('多场景测试完成', 'success');
            log('🎉 所有场景测试完成');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            logs = [];
            document.getElementById('logs').innerHTML = '';
            setStatus('', '');
            log('🗑️ 结果已清空');
        }

        function toggleLogs() {
            const logContainer = document.getElementById('logs');
            logContainer.style.display = logContainer.style.display === 'none' ? 'block' : 'none';
        }

        // 模拟分析功能
        async function simulateAnalysis(text) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟延迟

            const isUnknownAnswer = text.includes('不知道') || 
                                   text.includes('不清楚') || 
                                   text.includes('没有经验') ||
                                   text.includes('不会') ||
                                   text.trim().length < 10;

            const baseScore = isUnknownAnswer ? 35 : 75;
            const variation = Math.random() * 20 - 10;
            const finalScore = Math.max(20, Math.min(95, baseScore + variation));

            return {
                overallScore: Math.round(finalScore),
                textAnalysis: {
                    keywords: isUnknownAnswer ? [] : ['技术', '项目', '经验'],
                    sentiment: isUnknownAnswer ? 'uncertain' : 'positive'
                },
                recommendations: isUnknownAnswer ? [
                    '诚实表达不了解是好的态度，建议从基础概念开始学习',
                    '可以尝试从相关的基础知识点入手'
                ] : [
                    '继续保持清晰的表达逻辑',
                    '可以增加更多具体的技术细节'
                ],
                strengthAreas: isUnknownAnswer ? ['诚实态度', '学习意愿'] : ['逻辑清晰', '表达流畅'],
                improvementAreas: isUnknownAnswer ? ['基础知识', '学习深度'] : ['技术深度', '创新表达']
            };
        }

        // 生成AI回复
        function generateAIResponse(analysis, userAnswer) {
            const score = analysis.overallScore || 0;
            const isUnknownAnswer = userAnswer.includes('不知道') || 
                                   userAnswer.includes('不清楚') || 
                                   userAnswer.includes('没有经验') ||
                                   userAnswer.includes('不会') ||
                                   userAnswer.trim().length < 10;

            if (isUnknownAnswer) {
                return `没关系，这是一个很好的学习机会！诚实地表达不了解是很好的态度。

让我为您提供一些思路和指导：

📚 **基础概念**: 这个问题主要考察深度学习模型优化方面的知识
🔧 **技术要点**: 包括数据预处理、模型调参、性能优化等
💼 **实际应用**: 在实际项目中通常需要考虑模型效果和计算效率的平衡

💡 **小提示**: 您可以从以下角度思考：
• 数据质量对模型性能的影响
• 常见的优化策略有哪些
• 如何评估优化效果

要不我们换个角度：您在学习或工作中有没有接触过机器学习相关的内容？哪怕是很基础的概念也可以分享一下。`;
            } else if (score >= 85) {
                return `很棒的回答！您展现了扎实的技术功底。让我们深入探讨一下：您在实际项目中是如何应用这些技术的？遇到过什么挑战？`;
            } else if (score >= 70) {
                return `您的回答有一定的技术深度，不过还有提升空间。能否详细说明一下具体的技术实现细节？`;
            } else {
                return `感谢您的回答。我看到您对这个问题有一定的理解。您能否举个具体的例子来说明？`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 AI响应测试页面已加载');
            log('💡 可以开始测试AI响应功能');
        });
    </script>
</body>
</html>
