{"name": "cytoscape-fcose", "version": "2.2.0", "description": "The fCoSE layout for Cytoscape.js by Bilkent with fast compound node placement", "main": "cytoscape-fcose.js", "author": {"name": "iVis-at-Bilkent"}, "scripts": {"copyright": "update license", "lint": "eslint src", "build": "cross-env NODE_ENV=production webpack", "build:min": "cross-env NODE_ENV=production MIN=true webpack", "build:release": "run-s build copyright", "watch": "webpack --progress --watch", "dev": "webpack-dev-server --open", "test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/iVis-at-Bilkent/cytoscape.js-fcose.git"}, "keywords": ["cytoscape", "cytoscape-extension"], "license": "MIT", "bugs": {"url": "https://github.com/iVis-at-Bilkent/cytoscape.js-fcose/issues"}, "homepage": "https://github.com/iVis-at-Bilkent/cytoscape.js-fcose", "devDependencies": {"babel-core": "^6.24.1", "babel-loader": "^7.1.4", "babel-preset-env": "^1.5.1", "camelcase": "^6.2.0", "chai": "4.0.2", "cpy-cli": "^3.1.1", "cross-env": "^7.0.3", "eslint": "^7.26.0", "gh-pages": "^1.0.0", "mocha": "8.4.0", "npm-run-all": "^4.1.2", "rimraf": "^3.0.2", "update": "^0.7.4", "updater-license": "^1.0.0", "webpack": "^5.37.0", "webpack-cli": "^4.7.0", "webpack-dev-server": "^3.11.2"}, "peerDependencies": {"cytoscape": "^3.2.0"}, "dependencies": {"cose-base": "^2.2.0"}}